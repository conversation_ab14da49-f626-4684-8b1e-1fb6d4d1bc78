package com.ideas.tetris.pacman.services.fplos.batchupdate;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import static com.ideas.g3.test.AbstractG3JupiterTest.inject;
import static com.ideas.tetris.pacman.common.constants.Constants.FALSE;
import static com.ideas.tetris.pacman.common.constants.Constants.TRUE;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DecisionQualifiedFPLOSBatchUpdaterTest {

    public static final String TEST_TABLE_NAME = "testTableName";
    private static final String TEMP_TABLE_NAME = "temp_qualified_fplos_pid_5";
    private static final String AGILE_PRODUCT_TEMP_TABLE_NAME = "temp_qualified_agile_fplos_pid_5";
    DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater;
    DecisionQualifiedFPLOSBatchUpdaterBean decisionQualifiedFPLOSBatchUpdaterBean;

    Connection connection;
    PacmanWorkContextHelper pacmanWorkContextHelper;
    int propertyId = 5;
    int maxLOS_7 = 7;
    int maxLOS_3 = 3;
    int barMaxLos = maxLOS_3;
    boolean isSrpFplosAtTotalLevel = true;
    String tableName = "temp_qualified_fplos_pid_" + propertyId;
    private int lraEnabled = 0;
    private String NEW_LINE = "\n";
    private PreparedStatement statement;
    boolean isServicingCostByLosEnabledAndConfigured = false;

    @BeforeEach
    public void setUp() throws Exception {
        connection = mock(Connection.class);
        statement = mock(PreparedStatement.class);
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        pacmanWorkContextHelper = mock(PacmanWorkContextHelper.class);
        System.setProperty("use.window.function.for.FPLOSBatch.query.optimization", FALSE);
    }

    @Test
    public void testCreateEffectiveLRVTable() {
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveLRVTable(tableName, maxLOS_3);
        String expected = "IF OBJECT_ID('tempdb..#" + tableName + "') IS NOT NULL DROP TABLE #" + tableName + ";" + NEW_LINE
                + "CREATE TABLE #temp_qualified_fplos_pid_5([Arrival_DT] [date] NOT NULL," + NEW_LINE
                + "[Accom_Class_ID] [int] NOT NULL," + NEW_LINE
                + "[lrv1] [numeric](19,5) NOT NULL," + NEW_LINE
                + "[lrv2] [numeric](19,5) NOT NULL," + NEW_LINE
                + "[lrv3] [numeric](19,5) NOT NULL," + NEW_LINE
                + " CONSTRAINT [PK_" + tableName + "] PRIMARY KEY CLUSTERED " + NEW_LINE
                + "(" + NEW_LINE
                + "Accom_Class_ID,Arrival_DT ASC" + NEW_LINE
                + ") ) ON [PRIMARY];" + NEW_LINE;
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateEffectiveLRVTable() {
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateEffectiveLRVTable(tableName, maxLOS_3, false);
        String expected = "insert into #" + tableName + "\n" +
                "SELECT  cal.calendar_date, \n" +
                "      a.Accom_Class_ID, \n" +
                "      isnull(lrv1.LRV,0) as lrv1 \n" +
                ", isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  as lrv2\n" +
                " , isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  + isnull(lrv3.LRV,0)  as lrv3\n" +
                " \n" +
                "FROM calendar_dim cal \n" +
                "INNER JOIN Accom_Class a on a.Property_ID = :propertyId and a.System_Default != 1 \n" +
                " and a.Accom_Class_ID in \n" +
                "(SELECT DISTINCT Accom_Class_ID from Accom_Type \n" +
                "where Property_ID = :propertyId and Status_ID = 1 and System_Default != 1 ) \n" +
                "LEFT JOIN Decision_LRV lrv1 on lrv1.Accom_Class_ID = a.Accom_Class_ID and lrv1.Occupancy_DT = DATEADD(day,0,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv2 on lrv2.Accom_Class_ID = a.Accom_Class_ID and lrv2.Occupancy_DT = DATEADD(day,1,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv3 on lrv3.Accom_Class_ID = a.Accom_Class_ID and lrv3.Occupancy_DT = DATEADD(day,2,cal.calendar_date) \n" +
                "WHERE cal.calendar_date >= :startDate and cal.calendar_date <= :endDate\n" +
                "ORDER BY cal.calendar_date, a.Accom_Class_ID;\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateEffectiveRatesTable_isFplosSRP_true_isLV0_false() {
        String expected = createEffectiveRatesTableQuery();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTable(tableName, maxLOS_7, true, false);
        assertThat(actual, is(expected));
    }

    private String createEffectiveRatesTableQuery() {
        return "IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n" +
                "CREATE TABLE " + tableName + "_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate] [numeric](19,5) NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[Rate4] [numeric](19,5) NOT NULL,\n" +
                "[Rate5] [numeric](19,5) NOT NULL,\n" +
                "[Rate6] [numeric](19,5) NOT NULL,\n" +
                "[Rate7] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "[lrv4] [numeric](19,5) NOT NULL,\n" +
                "[lrv5] [numeric](19,5) NOT NULL,\n" +
                "[lrv6] [numeric](19,5) NOT NULL,\n" +
                "[lrv7] [numeric](19,5) NOT NULL,\n" +
                "[remCap1] [numeric](19,5) NOT NULL,\n" +
                "[remCap2] [numeric](19,5) NOT NULL,\n" +
                "[remCap3] [numeric](19,5) NOT NULL,\n" +
                "[remCap4] [numeric](19,5) NOT NULL,\n" +
                "[remCap5] [numeric](19,5) NOT NULL,\n" +
                "[remCap6] [numeric](19,5) NOT NULL,\n" +
                "[remCap7] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_" + tableName + "_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n";
    }

    private String createEffectiveRatesTableQuery_withoutRemainingCapacity() {
        return "IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n" +
                "CREATE TABLE " + tableName + "_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate] [numeric](19,5) NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[Rate4] [numeric](19,5) NOT NULL,\n" +
                "[Rate5] [numeric](19,5) NOT NULL,\n" +
                "[Rate6] [numeric](19,5) NOT NULL,\n" +
                "[Rate7] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "[lrv4] [numeric](19,5) NOT NULL,\n" +
                "[lrv5] [numeric](19,5) NOT NULL,\n" +
                "[lrv6] [numeric](19,5) NOT NULL,\n" +
                "[lrv7] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_" + tableName + "_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n";
    }

    @Test
    public void testCreateEffectiveRatesTable_isFplosSRP_true_isLV0_true() {
        String expected = createEffectiveRatesTableQuery_withoutRemainingCapacity();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTable(tableName, maxLOS_7, true, true);
        assertThat(actual, is(expected));
    }

    @Test
    public void testClosingStatement() throws SQLException {
        String expected = createEffectiveRatesTableQuery_withoutRemainingCapacity();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTable(tableName, maxLOS_7, true, true);
        decisionQualifiedFPLOSBatchUpdater.closeStatement();
        assertThat(actual, is(expected));
        Mockito.verify(statement, Mockito.times(1)).close();
    }

    @Test
    public void testPopulateEffectiveRatesTable_isFplosSRP_false_isLV0_false() {
        String expected = createEffectiveRatesTableQuery_withoutRemainingCapacity();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTable(tableName, maxLOS_7, false, false);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateEffectiveRatesTable_isFplosSRP_false_isLV0_true() {
        String expected = createEffectiveRatesTableQuery_withoutRemainingCapacity();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTable(tableName, maxLOS_7, false, false);
        assertThat(actual, is(expected));
    }

    @Test
    public void testUdateEffectiveRatesPerStayForIsDerivedRatePlanEnabledAndBarByLos() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String expected = (SystemConfig.isFPLOSWithNonTempTableEnabled() ? "update temp_qualified_fplos_pid_5_1 set\n" : "update #temp_qualified_fplos_pid_5_1 set\n") +
                "Rate1 = case\n" +
                "when Net_Value_Type_ID = 1 then Rate1 + Net_Value\n" +
                "when Net_Value_Type_ID = 2 then Rate1 + ((Rate1 * Net_Value) / 100.0)\n" +
                "when Net_Value_Type_ID = 3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate2 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate2\n" +
                "end\n" +
                ",\n" +
                "Rate3 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate3\n" +
                "end\n" +
                "from\n" +
                tableName + "_1 inner join Rate_Qualified_Adjustment\n" +
                "on " + tableName + "_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n" +
                "and " + tableName + "_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n" +
                "where Posting_Rule_ID = 1;\n";
        final String actual = decisionQualifiedFPLOSBatchUpdater.udateEffectiveRatesPerStay(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    @Test
    public void testUdateEffectiveRatesPerStay() {
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        final String expected = "update " + tableName + "_1 set\n" +
                "Rate1 = case\n" +
                "when Net_Value_Type_ID = 1 then Rate1 + Net_Value\n" +
                "when Net_Value_Type_ID = 2 then Rate1 + ((Rate1 * Net_Value) / 100.0)\n" +
                "when Net_Value_Type_ID = 3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate2 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate2\n" +
                "end\n" +
                ",\n" +
                "Rate3 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate3\n" +
                "end\n" +
                "from\n" +
                tableName + "_1 inner join Rate_Qualified_Adjustment\n" +
                "on " + tableName + "_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n" +
                "and " + tableName + "_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n" +
                "where Posting_Rule_ID = 1;\n";
        final String actual = decisionQualifiedFPLOSBatchUpdater.udateEffectiveRatesPerStay(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    @Test
    public void testUdateEffectiveRatesPerNightForYieldableCost() {
        String expected = createQueryUdateEffectiveRatesPerNightForAdjustmentType("YieldableCost");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateEffectiveRatesPerNightByArrivalDateRateAdjustmentForAdjustmentType(tableName, maxLOS_3, DecisionQualifiedFPLOSBatchUpdater.YIELDABLE_COST);
        assertThat(actual, is(expected));
    }

    private String createQueryUdateEffectiveRatesPerNightForAdjustmentType(final String adjustmentType) {
        return "update " + tableName + "_1\n" +
                "set Rate1= \n" +
                "case \n" +
                "	when Net_Value_Type_ID=1 then Rate1+Net_Value\n" +
                "	when Net_Value_Type_ID=2 then Rate1+((Rate*Net_Value)/100.0)\n" +
                "	when Net_Value_Type_ID=3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate2= \n" +
                "case \n" +
                "	when Net_Value_Type_ID=1 then Rate+Net_Value\n" +
                "	when Net_Value_Type_ID=2 then Rate2+((Rate*Net_Value)/100.0)\n" +
                "	when Net_Value_Type_ID=3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate3= \n" +
                "case \n" +
                "	when Net_Value_Type_ID=1 then Rate+Net_Value\n" +
                "	when Net_Value_Type_ID=2 then Rate3+((Rate*Net_Value)/100.0)\n" +
                "	when Net_Value_Type_ID=3 then Net_Value\n" +
                "end\n" +
                "from \n" +
                tableName + "_1 inner join Rate_Qualified_Adjustment\n" +
                "on " + tableName + "_1.Rate_Qualified_id=Rate_Qualified_Adjustment.Rate_Qualified_ID\n" +
                "and " + tableName + "_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n" +
                "where\n" +
                "Posting_Rule_ID=2 and Rate_Qualified_Adjustment.AdjustmentType='" + adjustmentType + "';\n";
    }

    @Test
    public void testUdateEffectiveRatesPerNightForYieldableValue() {
        String expected = createQueryUdateEffectiveRatesPerNightForAdjustmentType("YieldableValue");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateEffectiveRatesPerNightByArrivalDateRateAdjustmentForAdjustmentType(tableName, maxLOS_3, DecisionQualifiedFPLOSBatchUpdater.YIELDABLE_VALUE);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateTempWeightedRateTable() {
        String expected = "IF OBJECT_ID('tempdb..#" + tableName + "_1_wrate') IS NOT NULL DROP TABLE #" + tableName + "_1_wrate;\n" +
                "CREATE TABLE #" + tableName + "_1_wrate(\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[remCap1] [numeric] (19,5) NOT NULL,\n" +
                "[remCap2] [numeric] (19,5) NOT NULL,\n" +
                "[remCap3] [numeric] (19,5) NOT NULL,\n" +
                "[wRate1] [numeric](19,5) NOT NULL,\n" +
                "[wRate2] [numeric](19,5) NOT NULL,\n" +
                "[wRate3] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_" + tableName + "_1_wrate] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "	Arrival_DT,Accom_Type_ID,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n";
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempWeightedRateTable(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempWeightedRateTable() {
        String expected = "insert into #" + tableName + "_1_wrate\n" +
                "select Arrival_DT,Accom_Type_ID,Rate_Qualified_id\n" +
                "	,remCap1\n" +
                "	,remCap2\n" +
                "	,remCap3\n" +
                "	,Rate1*remCap1 as wrate1\n" +
                "	,Rate2*remCap2 as wrate2\n" +
                "	,Rate3*remCap3 as wrate3\n" +
                "	,[lrv1]\n" +
                "	,[lrv2]\n" +
                "	,[lrv3]\n" +
                " FROM " + tableName + "_1\n";
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempWeightedRateTable(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateTempWeightedRateFinalTable() {
        String expected = createTempWeightedRateQeury();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempWeightedRateFinalTable(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    private String createTempWeightedRateQeury() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_1_wrate_final') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_1_wrate_final;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_1_wrate_final(\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                ",[wRate1] [numeric](19,5) NOT NULL\n" +
                ",[wRate2] [numeric](19,5) NOT NULL\n" +
                ",[wRate3] [numeric](19,5) NOT NULL\n" +
                ",[lrv1] [numeric](19,5) NOT NULL\n" +
                ",[lrv2] [numeric](19,5) NOT NULL\n" +
                ",[lrv3] [numeric](19,5) NOT NULL\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_1_wrate_final] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n";
    }

    @Test
    public void testPopulateTempWeightedRateFinalTable() {
        String expected = createPopulateTempWeightedRateQeury();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempWeightedRateFinalTable(tableName, maxLOS_3);
        assertThat(actual, is(expected));
    }

    private String createPopulateTempWeightedRateQeury() {
        return "insert into #temp_qualified_fplos_pid_5_1_wrate_final\n" +
                "select Arrival_DT,Rate_Qualified_id\n" +
                "\t,wRate1= case(sum(remCap1)) when 0 then 0 else sum(Rate1*remCap1)/sum(remCap1) end\t\n" +
                "\t,wRate2= case(sum(remCap2)) when 0 then 0 else sum(Rate2*remCap2)/sum(remCap2) end\t\n" +
                "\t,wRate3= case(sum(remCap3)) when 0 then 0 else sum(Rate3*remCap3)/sum(remCap3) end\t\n" +
                "\t,avg(lrv1)\n" +
                "\t,avg(lrv2)\n" +
                "\t,avg(lrv3)\n" +
                " FROM temp_qualified_fplos_pid_5_1\n" +
                " group by Arrival_DT,Rate_Qualified_id\n";
    }

    @Test
    public void testCreateNonMasterRateCodeTable() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master') IS NOT NULL DROP TABLE #" + tableName + "_non_master;\n");
        stringBuffer.append("CREATE TABLE #" + tableName + "_non_master(\n");
        stringBuffer.append("	[Arrival_DT] [date] NOT NULL\n");
        stringBuffer.append("	,[Rate_Qualified_id] [int] NOT NULL\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuffer.append("\n");
        stringBuffer.append(" CONSTRAINT [PK_" + tableName + "_non_master] PRIMARY KEY CLUSTERED \n");
        stringBuffer.append("(\n");
        stringBuffer.append("	Arrival_DT,Rate_Qualified_id ASC\n");
        stringBuffer.append(")\n");
        stringBuffer.append(");\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.createNonMasterRateCodeTable(tableName, maxLOS_3), is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateNonMasterRateCodeTable() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_non_master \n");
        stringBuffer.append("select Arrival_DT,Rate_Qualified_id\n");
        for (int los = 1; los <= 3; los++) {
            stringBuffer.append(",Rate as Rate" + los + "\n");
        }
        stringBuffer.append("from \n");
        stringBuffer.append("( \n");
        stringBuffer.append("	select arrival_dt,Rate_Qualified_ID,max(rate) Rate \n");
        stringBuffer.append("	from \n");
        stringBuffer.append("	( \n");
        stringBuffer.append("		select arrival_dt,Rate_Qualified_ID,rate_q.Accom_Type_ID, \n");
        stringBuffer.append("		 Rate=case  \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 1 then Sunday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 2 then Monday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 3 then Tuesday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 4 then Wednesday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 5 then Thursday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 6 then Friday \n");
        stringBuffer.append("			 when DATEPART(weekday,Arrival_DT) = 7 then Saturday \n");
        stringBuffer.append("		end \n");
        stringBuffer.append("		from \n");
        stringBuffer.append("		( \n");
        stringBuffer.append("			select cast (calendar_date as date) arrival_dt from calendar_dim where calendar_date between :startDate and :endDate \n");
        stringBuffer.append("		) dt left join \n");
        stringBuffer.append("		( \n");
        stringBuffer.append("			select rqd.* from Rate_Qualified rq inner join Rate_Qualified_Details rqd on rq.Rate_Qualified_ID=rqd.Rate_Qualified_ID \n");
        stringBuffer.append("		where rq.Property_ID=:propertyId and rq.Rate_Qualified_ID!=:specialSrpId  and rq.Status_ID=1 and rq.Yieldable=1 \n");
        stringBuffer.append("		) rate_q on dt.arrival_dt between rate_q.Start_Date_DT and rate_q.End_Date_DT \n");
        stringBuffer.append("		inner join  \n");
        stringBuffer.append("		( \n");
        stringBuffer.append("			select AT.Accom_Type_ID from Accom_Type AT inner join Accom_Class AC on AT.Accom_Class_ID=AC.Accom_Class_ID  \n");
        stringBuffer.append("			where AT.System_Default=0 and AC.System_Default=0 and AT.Status_ID=1 and AC.Status_ID=1 \n");
        stringBuffer.append("			and AC.Master_Class!=1 and AT.Property_ID=:propertyId \n");
        stringBuffer.append("			and At.Accom_Type_Capacity>0 \n");
        stringBuffer.append("		) AT on rate_q.Accom_Type_ID=AT.Accom_Type_ID \n");
        stringBuffer.append("	)data group by arrival_dt,Rate_Qualified_ID \n");
        stringBuffer.append(")data2 \n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.populateNonMasterRateCodeTable(tableName, maxLOS_3), is(stringBuffer.toString()));
    }

    @Test
    public void testCreateNonMasterRateCodeTable_2() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master_2') IS NOT NULL DROP TABLE #" + tableName + "_non_master_2;\n");
        stringBuffer.append("CREATE TABLE #" + tableName + "_non_master_2(\n");
        stringBuffer.append("	[Arrival_DT] [date] NOT NULL\n");
        stringBuffer.append("	,[Rate_Qualified_id] [int] NOT NULL\n");
        for (int los = 1; los <= 3; los++) {
            stringBuffer.append("	,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuffer.append("\n");
        stringBuffer.append(" CONSTRAINT [PK_" + tableName + "_non_master_2] PRIMARY KEY CLUSTERED \n");
        stringBuffer.append("(\n");
        stringBuffer.append("	Arrival_DT,Rate_Qualified_id ASC\n");
        stringBuffer.append(")\n");
        stringBuffer.append(");\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(stringBuffer.toString(), is(decisionQualifiedFPLOSBatchUpdater.createNonMasterRateCodeTable_2(tableName, maxLOS_3)));
    }

    @Test
    public void testPopulateNonMasterRateCodeTable_2() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_non_master_2 \n");
        stringBuffer.append("select t1.* from #" + tableName + "_non_master t1  \n");
        stringBuffer.append("	left join #" + tableName + "_1_wrate_final t2 \n");
        stringBuffer.append("	on t1.Arrival_DT=t2.Arrival_DT and t1.Rate_Qualified_id=t2.Rate_Qualified_id \n");
        stringBuffer.append("	where t2.wRate1 is null \n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.populateNonMasterRateCodeTable_2(tableName), is(stringBuffer.toString()));
    }

    @Test
    public void testPopulatePaceQualifiedFPLOSTableNONisSRPbyFPLOS() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuffer.append("select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n");
        stringBuffer.append("where property_id=:propertyId\n");
        stringBuffer.append("and\n");
        stringBuffer.append("decision_id=:decisionId;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.populatePaceQualifiedFPLOSTable(false, false), is(stringBuffer.toString()));
    }

    @Test
    public void testPopulatePaceQualifiedFPLOSTableNONisSRPbyFPLOSLV0() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuffer.append("select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n");
        stringBuffer.append("where property_id=:propertyId\n");
        stringBuffer.append("and\n");
        stringBuffer.append("decision_id=:decisionId;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.populatePaceQualifiedFPLOSTable(false, true), is(stringBuffer.toString()));
    }

    @Test
    public void testPopulatePaceQualifiedFPLOSTableisSRPbyFPLOSLV0() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuffer.append("select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n");
        stringBuffer.append("where property_id=:propertyId\n");
        stringBuffer.append("and Rate_Qualified_ID=:specialSrpId  \n");
        stringBuffer.append("and\n");
        stringBuffer.append("decision_id=:decisionId;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populatePaceQualifiedFPLOSTable(true, true);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulatePaceQualifiedFPLOSTableisSRPbyFPLOSLV0NotFirstChunk() {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n")
                .append("select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n")
                .append("where property_id=:propertyId\n")
                .append("and Rate_Qualified_ID=:specialSrpId  \n")
                .append(" AND NOT EXISTS (SELECT 1 \n")
                .append("   FROM   pace_qualified_fplos \n")
                .append("   WHERE  dqf.decision_id = decision_id \n")
                .append("   AND dqf.property_id = property_id \n")
                .append("   AND dqf.accom_type_id = accom_type_id \n")
                .append("   AND dqf.rate_qualified_id = rate_qualified_id \n")
                .append("   AND dqf.arrival_dt = arrival_dt \n")
                .append("   AND dqf.fplos = fplos) \n")
                .append("and\n")
                .append("decision_id=:decisionId;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setFirstChunk(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populatePaceQualifiedFPLOSTable(true, true);
        assertThat(actual, is(sb.toString()));
    }

    @Test
    public void testPopulatePaceQualifiedFPLOSTableisSRPbyFPLOSNONLV0() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuffer.append("select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n");
        stringBuffer.append("where property_id=:propertyId\n");
        stringBuffer.append("and Rate_Qualified_ID!=:specialSrpId  \n");
        stringBuffer.append("and\n");
        stringBuffer.append("decision_id=:decisionId;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.populatePaceQualifiedFPLOSTable(true, false), is(stringBuffer.toString()));
    }

    @Test
    public void testCleanUpTempTablesLV0() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "') IS NOT NULL DROP TABLE #" + tableName + ";\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_1a') IS NOT NULL DROP TABLE #" + tableName + "_1a;\n");
        stringBuffer.append("IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_2') IS NOT NULL DROP TABLE #" + tableName + "_2;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA') IS NOT NULL DROP TABLE #" + tableName + "_LRA;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA_1') IS NOT NULL DROP TABLE #" + tableName + "_LRA_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_bar_value') IS NOT NULL DROP TABLE #" + tableName + "_bar_value;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustment') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustment;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustmentType') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustmentType;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.cleanUpTempTables(tableName, true, true), is(stringBuffer.toString()));
    }

    @Test
    public void testCleanUpTempTablesLV0WithRHB() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "') IS NOT NULL DROP TABLE #" + tableName + ";\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_1a') IS NOT NULL DROP TABLE #" + tableName + "_1a;\n");
        stringBuffer.append("IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_2') IS NOT NULL DROP TABLE #" + tableName + "_2;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA') IS NOT NULL DROP TABLE #" + tableName + "_LRA;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA_1') IS NOT NULL DROP TABLE #" + tableName + "_LRA_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_bar_value') IS NOT NULL DROP TABLE #" + tableName + "_bar_value;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustment') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustment;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustmentType') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustmentType;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RHB') IS NOT NULL DROP TABLE #" + tableName + "_RHB;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(true).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.cleanUpTempTables(tableName, true, true), is(stringBuffer.toString()));
    }

    @Test
    public void testCleanUpTempTablesnonLV0() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "') IS NOT NULL DROP TABLE #" + tableName + ";\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_1a') IS NOT NULL DROP TABLE #" + tableName + "_1a;\n");
        stringBuffer.append("IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_2') IS NOT NULL DROP TABLE #" + tableName + "_2;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA') IS NOT NULL DROP TABLE #" + tableName + "_LRA;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_LRA_1') IS NOT NULL DROP TABLE #" + tableName + "_LRA_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_bar_value') IS NOT NULL DROP TABLE #" + tableName + "_bar_value;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustment') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustment;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustmentType') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustmentType;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_1_wrate') IS NOT NULL DROP TABLE #" + tableName + "_1_wrate;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_1_wrate_final') IS NOT NULL DROP TABLE #" + tableName + "_1_wrate_final;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_wrate_cumulative') IS NOT NULL DROP TABLE #" + tableName + "_wrate_cumulative;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master') IS NOT NULL DROP TABLE #" + tableName + "_non_master;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master_1') IS NOT NULL DROP TABLE #" + tableName + "_non_master_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master_2') IS NOT NULL DROP TABLE #" + tableName + "_non_master_2;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + tableName + "a_non_master') IS NOT NULL DROP TABLE #" + tableName + "a_non_master;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.cleanUpTempTables(tableName, true, false), is(stringBuffer.toString()));
    }

    @Test
    public void testCleanUpTempTablesnonSRP() {
        StringBuilder stringBuffer = new StringBuilder();
        String curr_tablename = tableName;
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "') IS NOT NULL DROP TABLE #" + curr_tablename + ";\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_1a') IS NOT NULL DROP TABLE #" + curr_tablename + "_1a;\n");
        stringBuffer.append("IF OBJECT_ID('" + curr_tablename + "_1') IS NOT NULL DROP TABLE " + curr_tablename + "_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_2') IS NOT NULL DROP TABLE #" + curr_tablename + "_2;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_LRA') IS NOT NULL DROP TABLE #" + curr_tablename + "_LRA;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_LRA_1') IS NOT NULL DROP TABLE #" + curr_tablename + "_LRA_1;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_bar_value') IS NOT NULL DROP TABLE #" + curr_tablename + "_bar_value;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_RateAdjustment') IS NOT NULL DROP TABLE #" + curr_tablename + "_RateAdjustment;\n");
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "_RateAdjustmentType') IS NOT NULL DROP TABLE #" + curr_tablename + "_RateAdjustmentType;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        assertThat(decisionQualifiedFPLOSBatchUpdater.cleanUpTempTables(tableName, false, false), is(stringBuffer.toString()));
    }

    @Test
    public void verifyQueryToCleanUpTempTablesWhenHighestBARIsRestricted() {
        final String expectedQuery = getQueryToCleanUpTempTablesWhenHighestBARIsRestricted();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        final String queryToCleanUpTempTables = decisionQualifiedFPLOSBatchUpdater.cleanUpTempTables(tableName, false, true);
        assertEquals(queryToCleanUpTempTables, expectedQuery);
    }

    private String getQueryToCleanUpTempTablesWhenHighestBARIsRestricted() {
        return ("IF OBJECT_ID('tempdb..#" + tableName + "') IS NOT NULL DROP TABLE #" + tableName + ";\n") +
                "IF OBJECT_ID('tempdb..#" + tableName + "_1a') IS NOT NULL DROP TABLE #" + tableName + "_1a;\n" +
                "IF OBJECT_ID('" + tableName + "_1') IS NOT NULL DROP TABLE " + tableName + "_1;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_2') IS NOT NULL DROP TABLE #" + tableName + "_2;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_LRA') IS NOT NULL DROP TABLE #" + tableName + "_LRA;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_LRA_1') IS NOT NULL DROP TABLE #" + tableName + "_LRA_1;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_bar_value') IS NOT NULL DROP TABLE #" + tableName + "_bar_value;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustment') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustment;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustmentType') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustmentType;\n" +
                "IF OBJECT_ID('tempdb..#" + tableName + "_RHB') IS NOT NULL DROP TABLE #" + tableName + "_RHB;\n";
    }

    @Test
    public void testCreateTempEffectiveRatesWithDecisionReasonType() {
        String curr_tablename = tableName + "_LRA";
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "') IS NOT NULL DROP TABLE #" + curr_tablename + ";\n");
        stringBuffer.append("CREATE TABLE #" + curr_tablename + "(\n");
        stringBuffer.append("\n");
        stringBuffer.append("	[Arrival_DT] [date] NOT NULL,\n");
        stringBuffer.append("	[Accom_Type_ID] [int] NOT NULL,\n");
        stringBuffer.append("	[Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuffer.append("	[Rate] [numeric](19,5) NOT NULL,\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	[Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	[lrv" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        stringBuffer.append("	[LOS] [int] NULL,\n");
        stringBuffer.append("	[Decision_Reason_Type_ID] [int]\n");
        stringBuffer.append(")\n");
        stringBuffer.append(";\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempEffectiveRatesWithDecisionReasonType(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempEffectiveRatesWithDecisionReasonType() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_LRA" + "\n");
        stringBuffer.append("select " + tableName + "_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID FROM " + tableName + "_1 left join \n");
        stringBuffer.append("(\n");
        stringBuffer.append("	select Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:specialSrpId Rate_Qualified_ID,Decision_Reason_Type_ID from\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select * from decision_bar_output \n");
        stringBuffer.append("		where Rate_Unqualified_ID=:lv0UnqualifiedId and Accom_Class_ID=:masterClassId and Decision_Reason_Type_ID=6\n");
        stringBuffer.append("	) sub_optimal_LV0\n");
        stringBuffer.append("	inner join\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n");
        stringBuffer.append("		from Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n");
        stringBuffer.append("		where AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:masterClassId and AT.Property_ID=:propertyId\n");
        stringBuffer.append("	) MASTER_AC_AT\n");
        stringBuffer.append("	on sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n");
        stringBuffer.append(") LRA_IMPACT on " + tableName + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=" + tableName + "_1.Accom_Type_ID\n");
        stringBuffer.append("and " + tableName + "_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempEffectiveRatesWithDecisionReasonType(tableName);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempEffectiveRatesWithDecisionReasonType_lraEnabledImpactForAllBarRates() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_LRA" + "\n");
        stringBuffer.append("select " + tableName + "_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID FROM " + tableName + "_1 left join \n");
        stringBuffer.append("(\n");
        stringBuffer.append("	select Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:specialSrpId Rate_Qualified_ID,Decision_Reason_Type_ID from\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select * from decision_bar_output \n");
        stringBuffer.append("		where Accom_Class_ID=:masterClassId and Decision_Reason_Type_ID=6\n");
        stringBuffer.append("	) sub_optimal_LV0\n");
        stringBuffer.append("	inner join\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n");
        stringBuffer.append("		from Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n");
        stringBuffer.append("		where AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:masterClassId and AT.Property_ID=:propertyId\n");
        stringBuffer.append("	) MASTER_AC_AT\n");
        stringBuffer.append("	on sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n");
        stringBuffer.append(") LRA_IMPACT on " + tableName + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=" + tableName + "_1.Accom_Type_ID\n");
        stringBuffer.append("and " + tableName + "_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n");
        int lraEnabledValue = DecisionQualifiedFPLOSBatchUpdater.LRA_ENABLED_ALL_BAR_RATES_IMPACT;
        decisionQualifiedFPLOSBatchUpdaterBean = buildDecisionQualifiedFPLOSBatchUpdaterBean(connection, propertyId, maxLOS_7, isSrpFplosAtTotalLevel, lraEnabledValue, barMaxLos, isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempEffectiveRatesWithDecisionReasonType(tableName);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempEffectiveRatesWithDecisionReasonType_forNonLv0() {
        String curr_tablename = tableName;
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + curr_tablename + "_LRA" + "\n");
        stringBuffer.append("select " + curr_tablename + "_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID FROM " + curr_tablename + "_1 left join \n");
        stringBuffer.append("(\n");
        stringBuffer.append("	select Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:specialSrpId Rate_Qualified_ID,Decision_Reason_Type_ID from\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select * from decision_bar_output \n");
        stringBuffer.append("		where Rate_Unqualified_ID=:lv0UnqualifiedId and Accom_Class_ID=:masterClassId and Decision_Reason_Type_ID=6\n");
        stringBuffer.append("	) sub_optimal_LV0\n");
        stringBuffer.append("	inner join\n");
        stringBuffer.append("	(\n");
        stringBuffer.append("		select distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n");
        stringBuffer.append("		from Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n");
        stringBuffer.append("		where AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:masterClassId and AT.Property_ID=:propertyId\n");
        stringBuffer.append("	) MASTER_AC_AT\n");
        stringBuffer.append("	on sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n");
        stringBuffer.append(") LRA_IMPACT on " + curr_tablename + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=" + curr_tablename + "_1.Accom_Type_ID\n");
        stringBuffer.append("and " + curr_tablename + "_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempEffectiveRatesWithDecisionReasonType(tableName);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testCreateTempEffectiveRatesWithDecisionReasonTypeByLos_forLv0() {
        String curr_tablename = tableName + "_LRA_1";
        int maxLOS = maxLOS_3;
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "') IS NOT NULL DROP TABLE #" + curr_tablename + ";\n");
        stringBuffer.append("CREATE TABLE #" + curr_tablename + "(\n");
        stringBuffer.append("\n");
        stringBuffer.append("	[Arrival_DT] [date] NOT NULL\n");
        stringBuffer.append("	,[Accom_Type_ID] [int] NOT NULL\n");
        stringBuffer.append("	,[Rate_Qualified_id] [int] NOT NULL\n");
        stringBuffer.append("	,[Rate] [numeric](19,5) NOT NULL\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuffer.append("	,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuffer.append("	,[lrv" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuffer.append("	,[ReasonType_los" + los + "] [int]\n");
        }
        stringBuffer.append(")\n");
        stringBuffer.append(";\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempEffectiveRatesWithDecisionReasonTypeByLos(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testCreateTempEffectiveRatesWithDecisionReasonTypeByLos() {
        String curr_tablename = tableName + "_LRA_1";
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("IF OBJECT_ID('tempdb..#" + curr_tablename + "') IS NOT NULL DROP TABLE #" + curr_tablename + ";\n");
        stringBuffer.append("CREATE TABLE #" + curr_tablename + "(\n");
        stringBuffer.append("\n");
        stringBuffer.append("	[Arrival_DT] [date] NOT NULL\n");
        stringBuffer.append("	,[Accom_Type_ID] [int] NOT NULL\n");
        stringBuffer.append("	,[Rate_Qualified_id] [int] NOT NULL\n");
        stringBuffer.append("	,[Rate] [numeric](19,5) NOT NULL\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	,[lrv" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	,[ReasonType_los" + los + "] [int]\n");
        }
        stringBuffer.append(")\n");
        stringBuffer.append(";\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempEffectiveRatesWithDecisionReasonTypeByLos(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testUpdateTempTableForDecisionBarOutput() {
        String expected = "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then (temp_bar.BAR_Rate1 * 1 + temp_fplos.Rate1)\n" +
                " when rq.Rate_Qualified_type_ID=2 then (temp_bar.BAR_Rate1 * (1 + (temp_fplos.Rate1) / 100.0))\n" +
                " else temp_fplos.Rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then (temp_bar.BAR_Rate2 * 2 + temp_fplos.Rate1 + temp_fplos.Rate2)\n" +
                " when rq.Rate_Qualified_type_ID=2 then (temp_bar.BAR_Rate2 * (2 + (temp_fplos.Rate1 + temp_fplos.Rate2) / 100.0))\n" +
                " else temp_fplos.Rate1 + temp_fplos.Rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then (temp_bar.BAR_Rate3 * 3 + temp_fplos.Rate1 + temp_fplos.Rate2 + temp_fplos.Rate3)\n" +
                " when rq.Rate_Qualified_type_ID=2 then (temp_bar.BAR_Rate3 * (3 + (temp_fplos.Rate1 + temp_fplos.Rate2 + temp_fplos.Rate3) / 100.0))\n" +
                " else temp_fplos.Rate1 + temp_fplos.Rate2 + temp_fplos.Rate3 end\n" +
                " FROM " + tableName + "_1 temp_fplos\n" +
                " INNER JOIN #" + tableName + "_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n";
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(true).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableForDecisionBarOutput(tableName, maxLOS_3);
        assertThat(actual, is(expected.toString()));
    }

    @Test
    public void testPopulateTempEffectiveRatesWithDecisionReasonTypeByLOS() {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_LRA_1\n");
        stringBuffer.append(" select Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append(" ,Rate" + los + "\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append(" ,lrv" + los + "\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append(" ,max([" + los + "])  as reasonType_los" + los + "\n");
        }
        stringBuffer.append(" from\n");
        stringBuffer.append(" (\n");
        stringBuffer.append("	 select :propertyId propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	 ,Rate" + los + "\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	 ,lrv" + los + "\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append("	 ,(case los when " + los + " then  Decision_Reason_Type_ID end ) as [" + los + "]\n");
        }
        stringBuffer.append("	 from #" + tableName + "_LRA\n");
        stringBuffer.append(") as x group by propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append(",Rate" + los + "\n");
        }
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuffer.append(",lrv" + los + "\n");
        }
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempEffectiveRatesWithDecisionReasonTypeByLOS(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempFPLOSTableNonLV0() {
        String rate = "Rate";
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_2\n");
        stringBuffer.append("select :propertyId,LRA_1.Arrival_DT\n");
        stringBuffer.append(",LRA_1.Accom_Type_ID\n");
        stringBuffer.append(",LRA_1.Rate_Qualified_id, \n");
        stringBuffer.append("case when (" + this.lraEnabled + ">0 and ReasonType_los1=6) then 'N' else \n");
        stringBuffer.append("	case when Rate1>=lrv1 then 'Y' else 'N' end end\n");
        for (int i = 2; i <= maxLOS_3; i++) {
            if (maxLOS_3 == i) {
                stringBuffer.append("+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n");
                stringBuffer.append("	then 'Y' \n");
                stringBuffer.append("	else \n");
                stringBuffer.append("		case when " + this.lraEnabled + ">0 and ReasonType_los" + i + "=6 \n");
                stringBuffer.append("			then 'N' \n");
                stringBuffer.append("			else\n");
                stringBuffer.append("				case when " + rate + "1");
                for (int j = 2; j <= i; j++) {
                    stringBuffer.append("+" + rate + j);
                }
                stringBuffer.append(">=");
                stringBuffer.append("lrv" + i);
                stringBuffer.append(" \n");
                stringBuffer.append("					then 'Y' \n");
                stringBuffer.append("					else 'N' \n");
                stringBuffer.append("				end \n");
                stringBuffer.append("		end \n");
                stringBuffer.append("end \n");
            } else {
                stringBuffer.append("+case when (" + this.lraEnabled + ">0 and ReasonType_los" + i + "=6) then 'N' else \n");
                stringBuffer.append("	case when " + rate + "1");
                for (int j = 2; j <= i; j++) {
                    stringBuffer.append("+" + rate + j);
                }
                stringBuffer.append(">=");
                stringBuffer.append("lrv" + i);
                stringBuffer.append(" then 'Y' else 'N' end end\n");
            }
        }
        stringBuffer.append(" as FPLOS\n");
        stringBuffer.append("from #" + tableName + "_LRA_1 AS LRA_1 ;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempFPLOSTableLV0ForRestrictHighestBarEnabled() {
        String rate = "Rate";
        boolean isRestrictHighestBarEnabled = true;
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("insert into #" + tableName + "_2\n");
        stringBuffer.append("select :propertyId,LRA_1.Arrival_DT\n");
        stringBuffer.append(",LRA_1.Accom_Type_ID\n");
        stringBuffer.append(",LRA_1.Rate_Qualified_id, \n");
        boolean isLv0 = true;
        stringBuffer.append("case when (" + this.lraEnabled + ">0 and ReasonType_los1=6)");
        if (isLv0 && isRestrictHighestBarEnabled) {
            stringBuffer.append(" or restHighestBar.RHB_LOS1=1");
        }
        stringBuffer.append(" then 'N' else \n");
        stringBuffer.append("	case when Rate1>=lrv1 then 'Y' else 'N' end end\n");
        for (int i = 2; i <= maxLOS_3; i++) {
            if (maxLOS_3 == i) {
                stringBuffer.append("+");
                if (isLv0 && isRestrictHighestBarEnabled) {
                    stringBuffer.append("case when restHighestBar.RHB_LOS").append(i).append("=").append(i).append(" then 'N' else ").append("\n");
                }
                stringBuffer.append("case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n");
                stringBuffer.append("	then 'Y' \n");
                stringBuffer.append("	else \n");
                stringBuffer.append("		case when " + this.lraEnabled + ">0 and ReasonType_los" + i + "=6 \n");
                stringBuffer.append("			then 'N' \n");
                stringBuffer.append("			else\n");
                stringBuffer.append("				case when " + rate + "1");
                for (int j = 2; j <= i; j++) {
                    stringBuffer.append("+" + rate + j);
                }
                stringBuffer.append(">=");
                stringBuffer.append("lrv" + i);
                stringBuffer.append(" \n");
                stringBuffer.append("					then 'Y' \n");
                stringBuffer.append("					else 'N' \n");
                stringBuffer.append("				end \n");
                stringBuffer.append("		end \n");
                stringBuffer.append("end \n");
                if (isLv0 && isRestrictHighestBarEnabled) {
                    stringBuffer.append("end \n");
                }
            } else {
                stringBuffer.append("+case when (" + this.lraEnabled + ">0 and ReasonType_los" + i + "=6)");
                if (isLv0 && isRestrictHighestBarEnabled) {
                    stringBuffer.append(" or restHighestBar.RHB_LOS").append(i).append("=").append(i);
                }
                stringBuffer.append(" then 'N' else \n");
                stringBuffer.append("	case when " + rate + "1");
                for (int j = 2; j <= i; j++) {
                    stringBuffer.append("+" + rate + j);
                }
                stringBuffer.append(">=");
                stringBuffer.append("lrv" + i);
                stringBuffer.append(" then 'Y' else 'N' end end\n");
            }
        }
        stringBuffer.append(" as FPLOS\n");
        stringBuffer.append("from #" + tableName + "_LRA_1 AS LRA_1");
        if (isLv0 && isRestrictHighestBarEnabled) {
            stringBuffer.append(" LEFT OUTER JOIN #").append(tableName).append("_RHB AS restHighestBar ").append("\n");
            stringBuffer.append("ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT ").append("\n");
            stringBuffer.append("AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId ");
        }
        stringBuffer.append(" ;\n");
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(isRestrictHighestBarEnabled).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTableForLV0(tableName, maxLOS_3);
        assertThat(actual, is(stringBuffer.toString()));
    }

    @Test
    public void testPopulateTempFPLOSTableLV0WhenServicingCostDeductionIsEnabled() {
        boolean isRestrictHighestBarEnabled = false;
        String expectedQuery = "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2 - ISNULL(servicingCost_los2, 0) >=lrv2 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3 - ISNULL(servicingCost_los3, 0) >=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 " +
                "left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost \n" +
                "on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)  ;\n";
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(isRestrictHighestBarEnabled).setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTableForLV0(tableName, maxLOS_3);
        assertThat(actual, is(expectedQuery));
    }

    @Test
    public void printFullQueryNonTars() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP("temp_qualified_fplos_pid_11022", 8);
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void printFullQueryForNonTarsSrpTotalLevelDerivedBARByLOSTrue() {
        DecisionQualifiedFPLOSBatchUpdaterBean decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true)
                .setConnection(connection)
                .setPropertyId(propertyId)
                .setMaxLOS(7)
                .setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel)
                .setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(false)
                .setDerivedRatePlanEnabled(true)
                .setBarByLos(true)
                .setBarMaxLos(7)
                .setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP("temp_qualified_fplos_pid_11022", 7);
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void servicingCostQueriesShouldIncludeInFullQueryAtTotalLevelWhenServicingCostByLosIsEnabled() {
        isServicingCostByLosEnabledAndConfigured = true;
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true)
                .setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3)
                .setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true)
                .setBarByLos(true).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP("temp_qualified_fplos_pid_11022", 8);
        assertThat(query, containsString("tempdb..#temp_qualified_fplos_pid_11022_servicingCost"));
        assertThat(query, containsString("insert into #temp_qualified_fplos_pid_11022_servicingCost"));
        assertThat(query, containsString("DROP TABLE #temp_qualified_fplos_pid_11022_servicingCost"));
    }

    @Test
    public void testGenerateQueriesWhenDerivedRatePlanEnabledIsTrueExpectBarDecisionTablesGetingUsed() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(8).setSrpFplosAtTotalLevel(false).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(8).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueries(tableName, 8);
        assertThat(query, containsString("Decision_Bar_Output"));
        assertFalse(StringUtils.contains(query, "CP_Decision_Bar_Output"));
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void testGenerateQueriesWhenDerivedRatePlanEnabledIsTrueAndBarDecisionIsBarByLosExpectBarDecisionTablesGetingUsed() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(false).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(2).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueries(tableName, maxLOS_3);
        assertThat(query, containsString("[1] AS BAR_RATE1, [2] AS BAR_RATE2, [2] AS BAR_RATE3"));
        assertThat(query, containsString("Decision_Bar_Output"));
        assertFalse(StringUtils.contains(query, "CP_Decision_Bar_Output"));
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void shouldAddQueriesRelatedToDerivedRatesCalculationForTotalLevel() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(2).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP(tableName, maxLOS_3);
        assertThat(query, containsString("[1] AS BAR_RATE1, [2] AS BAR_RATE2, [2] AS BAR_RATE3"));
        assertThat(query, containsString("Decision_Bar_Output"));
        assertFalse(StringUtils.contains(query, "CP_Decision_Bar_Output"));
        assertDerivedRatePlanQueries(query);
    }

    private void assertDerivedRatePlanQueries(String query) {
        assertThat(query, containsString("CREATE TABLE #temp_qualified_fplos_pid_5_bar_value"));
        assertThat(query, containsString("insert into #temp_qualified_fplos_pid_5_bar_value"));

        assertThat(query, containsString("delete temp_qualified_fplos_pid_5_1\n" +
                " from temp_qualified_fplos_pid_5_1\n" +
                " inner join Rate_Qualified on temp_qualified_fplos_pid_5_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #temp_qualified_fplos_pid_5_bar_value on temp_qualified_fplos_pid_5_1.arrival_dt = #temp_qualified_fplos_pid_5_bar_value.arrival_dt and temp_qualified_fplos_pid_5_1.Accom_Type_ID = #temp_qualified_fplos_pid_5_bar_value.Accom_Type_ID\n" +
                " where ( #temp_qualified_fplos_pid_5_bar_value.BAR_Rate1 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate2 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate3 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)"));
        assertThat(query, containsString("update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end\n" +
                " from temp_qualified_fplos_pid_5_1 temp_fplos\n" +
                " INNER JOIN #temp_qualified_fplos_pid_5_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id"));

        assertThat(query, containsString("delete #temp_qualified_fplos_pid_5_non_master_1\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1\n" +
                " inner join Rate_Qualified on #temp_qualified_fplos_pid_5_non_master_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #temp_qualified_fplos_pid_5_bar_value on #temp_qualified_fplos_pid_5_non_master_1.arrival_dt = #temp_qualified_fplos_pid_5_bar_value.arrival_dt and #temp_qualified_fplos_pid_5_non_master_1.Accom_Type_ID = #temp_qualified_fplos_pid_5_bar_value.Accom_Type_ID\n" +
                " where ( #temp_qualified_fplos_pid_5_bar_value.BAR_Rate1 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate2 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate3 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)"));
        assertThat(query, containsString("update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1 temp_fplos\n" +
                " INNER JOIN #temp_qualified_fplos_pid_5_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id"));
    }

    @Test
    public void testGenerateQueriesWhenDerivedRatePlanEnabledIsFalseExpectBarDecisionTablesAreNotGetingUsed() {
        decisionQualifiedFPLOSBatchUpdaterBean = buildDecisionQualifiedFPLOSBatchUpdaterBean(connection, propertyId, 8, isSrpFplosAtTotalLevel, lraEnabled, 8, isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueries(tableName, 8);
        assertFalse(StringUtils.contains(query, "Decision_Bar_Output"));
        assertFalse(StringUtils.contains(query, "CP_Decision_Bar_Output"));
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void testGenerateQueriesWhenDerivedRatePlanEnabledIsTrueAndIsContinuousPricingProperty() {
        System.setProperty("pacman.recommendation.cp.fplos.lead.enabled", "false");
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueries(tableName, maxLOS_3);
        assertThat(query, containsString("CP_Decision_Bar_Output"));
        assertThat(query, containsString("SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,CP.Final_BAR AS BAR_Rate1"));
        assertThat(query, containsString(" where CP.Arrival_DT BETWEEN :startDate AND DATEADD(DAY," + maxLOS_3 + ", :endDate)  AND CP.LOS = -1 AND CP.Final_BAR IS NOT NULL AND CP.Product_ID=1;"));
        assertThat(query, not(containsString("servicingCost")));
    }

    @Test
    public void testCreateTempRateAdjustment() throws Exception {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempRateAdjustment(tableName, 2);
        String expected = "IF OBJECT_ID('tempdb..#" + tableName + "_RateAdjustment') IS NOT NULL DROP TABLE #" + tableName + "_RateAdjustment;\n" +
                "CREATE TABLE #" + tableName + "_RateAdjustment(\n" +
                "          [Arrival_DT] [date] NOT NULL,\n" +
                "          [Rate_Qualified_id] [int] NOT NULL,\n" +
                "          [AdjustmentType] [varchar](50) NOT NULL,\n" +
                "          [Net_Value1] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID1] [int],\n" +
                "          [Net_Value2] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID2] [int],\n" +
                " CONSTRAINT [PK_" + tableName + "_RateAdjustment] PRIMARY KEY CLUSTERED ([Arrival_DT],[Rate_Qualified_ID],[AdjustmentType] ASC )\n" +
                ");\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempRateAdjustment() {
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", FALSE);
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempRateAdjustment(tableName, 3, false);
        String expected = "insert into #" + tableName + "_RateAdjustment\n" +
                "select cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType\n" +
                ", adj1.Net_Value as Net_Value1, adj1.Net_Value_Type_ID as Net_Value_Type_ID1\n" +
                ", adj2.Net_Value as Net_Value2, adj2.Net_Value_Type_ID as Net_Value_Type_ID2\n" +
                ", adj3.Net_Value as Net_Value3, adj3.Net_Value_Type_ID as Net_Value_Type_ID3\n" +
                "from calendar_dim cal\n" +
                "cross join Rate_Qualified rq\n" +
                "cross join #" + tableName + "_RateAdjustmentType rat\n" +
                "left join Rate_Qualified_Adjustment adj1 on dateadd(day, 0, cal.calendar_date) between adj1.Start_Date_DT and adj1.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj1.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj1.AdjustmentType\n" +
                "and adj1.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj2 on dateadd(day, 1, cal.calendar_date) between adj2.Start_Date_DT and adj2.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj2.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj2.AdjustmentType\n" +
                "and adj2.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj3 on dateadd(day, 2, cal.calendar_date) between adj3.Start_Date_DT and adj3.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj3.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj3.AdjustmentType\n" +
                "and adj3.Posting_Rule_ID = 2\n" +
                "where calendar_date between :startDate and :endDate\n" +
                "and rq.Status_Id = 1\n" +
                "and coalesce(adj1.Rate_Qualified_ID, adj2.Rate_Qualified_ID, adj3.Rate_Qualified_ID) is not null;\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempRateAdjustmentWithOptimizedQuery() {
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", TRUE);
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempRateAdjustment(tableName, 3, false);
        String expected = "\ninsert into #" + tableName + "_RateAdjustment" +
                "\nselect Arrival_DT, Rate_Qualified_ID, AdjustmentType, " +
                "\n\t\tNet_Value1, Net_Value_Type_ID1," +
                "\n\t\tNet_Value2, Net_Value_Type_ID2," +
                "\n\t\tNet_Value3, Net_Value_Type_ID3" +
                "\nfrom (\n" +
                "\t\tselect cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType,\n" +
                "\t\t\tlead(adj.Net_Value,0) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value1 , lead(adj.Net_Value_Type_ID,0) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID1,\n" +
                "\t\t\tlead(adj.Net_Value,1) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value2 , lead(adj.Net_Value_Type_ID,1) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID2,\n" +
                "\t\t\tlead(adj.Net_Value,2) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value3 , lead(adj.Net_Value_Type_ID,2) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID3\n" +
                "\t\tfrom calendar_dim cal\n" +
                "\t\t\tcross join Rate_Qualified rq\n" +
                "cross join #" + tableName + "_RateAdjustmentType rat\n" +
                "\t\t\tleft join Rate_Qualified_Adjustment adj on cal.calendar_date between adj.Start_Date_DT and adj.End_Date_DT and rq.Rate_Qualified_ID = adj.Rate_Qualified_ID and rat.AdjustmentType = adj.AdjustmentType and adj.Posting_Rule_ID = 2 \n" +
                "\t\twhere rq.Rate_Qualified_ID is not null and rq.Status_Id = 1\n" +
                "\t\t\t\tand calendar_date between :startDate and dateadd(day," + 3 + ",:endDate)\n" +
                ") as final \n" +
                "where final.Arrival_DT between :startDate and :endDate \n" +
                "and coalesce(Net_Value1,Net_Value2,Net_Value3) is not null\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempRateAdjustmentWithOptimizedQueryLimitTotalSRPRatesApplicable() {
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", TRUE);
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3)
                .setSrpFplosAtTotalLevel(false).setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false)
                .setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured)
                .setLimitTotalSRPRatesEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempRateAdjustment(tableName, 3, false);
        String expected = "\ninsert into #" + tableName + "_RateAdjustment" +
                "\nselect Arrival_DT, Rate_Qualified_ID, AdjustmentType, " +
                "\n\t\tNet_Value1, Net_Value_Type_ID1," +
                "\n\t\tNet_Value2, Net_Value_Type_ID2," +
                "\n\t\tNet_Value3, Net_Value_Type_ID3" +
                "\nfrom (\n" +
                "\t\tselect cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType,\n" +
                "\t\t\tlead(adj.Net_Value,0) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value1 , lead(adj.Net_Value_Type_ID,0) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID1,\n" +
                "\t\t\tlead(adj.Net_Value,1) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value2 , lead(adj.Net_Value_Type_ID,1) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID2,\n" +
                "\t\t\tlead(adj.Net_Value,2) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value3 , lead(adj.Net_Value_Type_ID,2) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID3\n" +
                "\t\tfrom calendar_dim cal\n" +
                "\t\t\tcross join Rate_Qualified rq\n" +
                "cross join #" + tableName + "_RateAdjustmentType rat\n" +
                "\t\t\tleft join Rate_Qualified_Adjustment adj on cal.calendar_date between adj.Start_Date_DT and adj.End_Date_DT and rq.Rate_Qualified_ID = adj.Rate_Qualified_ID and rat.AdjustmentType = adj.AdjustmentType and adj.Posting_Rule_ID = 2  left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID " +
                "\n where lt.Limit_Total_Rate_Qualified_ID is null and " +
                "\n rq.Rate_Qualified_ID is not null and rq.Status_Id = 1\n" +
                "\t\t\t\tand calendar_date between :startDate and dateadd(day," + 3 + ",:endDate)\n" +
                ") as final \n" +
                "where final.Arrival_DT between :startDate and :endDate \n" +
                "and coalesce(Net_Value1,Net_Value2,Net_Value3) is not null\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempRateAdjustmentWithOptimizedQueryLimitTotalSRPRatesApplicableCalculatedAtTotalLevel() {
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", TRUE);
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3)
                .setSrpFplosAtTotalLevel(false).setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false)
                .setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured)
                .setLimitTotalSRPRatesEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempRateAdjustment(tableName, 3, true);
        String expected = "\ninsert into #" + tableName + "_RateAdjustment" +
                "\nselect Arrival_DT, Rate_Qualified_ID, AdjustmentType, " +
                "\n\t\tNet_Value1, Net_Value_Type_ID1," +
                "\n\t\tNet_Value2, Net_Value_Type_ID2," +
                "\n\t\tNet_Value3, Net_Value_Type_ID3" +
                "\nfrom (\n" +
                "\t\tselect cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType,\n" +
                "\t\t\tlead(adj.Net_Value,0) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value1 , lead(adj.Net_Value_Type_ID,0) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID1,\n" +
                "\t\t\tlead(adj.Net_Value,1) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value2 , lead(adj.Net_Value_Type_ID,1) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID2,\n" +
                "\t\t\tlead(adj.Net_Value,2) over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value3 , lead(adj.Net_Value_Type_ID,2) over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID3\n" +
                "\t\tfrom calendar_dim cal\n" +
                "\t\t\tcross join Rate_Qualified rq\n" +
                "cross join #" + tableName + "_RateAdjustmentType rat\n" +
                "\t\t\tleft join Rate_Qualified_Adjustment adj on cal.calendar_date between adj.Start_Date_DT and adj.End_Date_DT and rq.Rate_Qualified_ID = adj.Rate_Qualified_ID and rat.AdjustmentType = adj.AdjustmentType and adj.Posting_Rule_ID = 2  inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID " +
                "\n where  " +
                "rq.Rate_Qualified_ID is not null and rq.Status_Id = 1\n" +
                "\t\t\t\tand calendar_date between :startDate and dateadd(day," + 3 + ",:endDate)\n" +
                ") as final \n" +
                "where final.Arrival_DT between :startDate and :endDate \n" +
                "and coalesce(Net_Value1,Net_Value2,Net_Value3) is not null\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateEffectiveRatesTableBasedOnServicingCost() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(false).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateEffectiveRatesTablesIfServicingCostEnabled(tableName, maxLOS_3);
        StringBuilder expected = new StringBuilder("update #temp_qualified_fplos_pid_5_1\n")
                .append(" set Rate1 = t.Rate1 - ISNULL(servicingCost_los1, 0)\n")
                .append(", Rate2 = t.Rate2 - ISNULL(servicingCost_los2, 0)\n")
                .append(", Rate3 = t.Rate3 - ISNULL(servicingCost_los3, 0)\n")
                .append(" from #temp_qualified_fplos_pid_5_1 as t left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost  on (t.Rate_Qualified_id = servicingCost.Rate_Qualified_id and t.Accom_Type_ID = servicingCost.Accom_Type_ID)\n");
        assertEquals(actual, expected.toString());
    }

    @Test
    public void testPopulateEffectiveRatesTableForSRPTotalLevel() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_3).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateEffectiveRatesTableBasedOnDow(tableName, maxLOS_3, true, false);
        String expected = "insert into temp_qualified_fplos_pid_5_1\n" +
                " select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,t1.Rate\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t1.lrv1\n" +
                ", t1.lrv2\n" +
                ", t1.lrv3\n" +
                ", t1.remCap \n" +
                ", t2.remCap \n" +
                ", t3.remCap \n" +
                "from #temp_qualified_fplos_pid_5_1a t1 \n" +
                "inner join #temp_qualified_fplos_pid_5_1a t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5_1a t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID \n" +
                ";drop table #temp_qualified_fplos_pid_5_1a;\n" +
                "drop table #temp_qualified_fplos_pid_5;";
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateEffectiveRatesPerNightForYieldableAdjustmentTypeDerivedRatesFromRateOfDay() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(8).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(false).setBarMaxLos(8).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS_3, "YieldableCost");
        String expected = "update " + tableName + "_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM " + tableName + "_1\n" +
                "inner join #" + tableName + "_RateAdjustment ra\n" +
                "on " + tableName + "_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and " + tableName + "_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableCost';\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateEffectiveRatesPerNightForYieldableAdjustmentTypeForDerivedRatesFromBarByLos() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(8).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(8).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS_3, "YieldableCost");
        String expected = "update " + tableName + "_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM " + tableName + "_1\n" +
                "inner join #" + tableName + "_RateAdjustment ra\n" +
                "on " + tableName + "_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and " + tableName + "_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableCost';\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(2).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(2).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments(tableName, 2);
        String expected = "update temp_fplos set\n" +
                " temp_fplos.rate1 =\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 then (temp_bar.BAR_Rate1 * 1 + temp_fplos.Rate1)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * 1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + (temp_fplos.Rate1) / 100.0))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + (temp_fplos.Rate1) / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "  end\n" +
                " ,\n" +
                " temp_fplos.rate2 =\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 then (temp_bar.BAR_Rate2 * 2 + temp_fplos.Rate1 + temp_fplos.Rate2)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate2 * 2 + temp_fplos.Rate1 + temp_fplos.Rate2) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 then (temp_bar.BAR_Rate2 * (2 + (temp_fplos.Rate1 + temp_fplos.Rate2) / 100.0))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate1 / 100.0)) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate2 * (2 + (temp_fplos.Rate1 + temp_fplos.Rate2) / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "  end\n" +
                "FROM temp_qualified_fplos_pid_5_1 temp_fplos\n" +
                "INNER JOIN #temp_qualified_fplos_pid_5_bar_value temp_bar\n" +
                " on temp_bar.Arrival_DT = temp_fplos.arrival_DT\n" +
                " and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                "INNER JOIN Rate_Qualified rq\n" +
                " on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n" +
                "LEFT JOIN Rate_Qualified_Adjustment ra1\n" +
                " on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n" +
                " and ra1.Posting_Rule_ID = 1\n" +
                "LEFT JOIN #temp_qualified_fplos_pid_5_RateAdjustment ra2_cost\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n" +
                " and ra2_cost.AdjustmentType = 'YieldableCost'\n" +
                "LEFT JOIN #temp_qualified_fplos_pid_5_RateAdjustment ra2_value\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n" +
                " and ra2_value.AdjustmentType = 'YieldableValue'\n" +
                " where rq.Rate_Qualified_type_ID in (1, 2);\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustmentsForFixedQualifiedRates() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(2).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(2).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustmentsForFixedQualifiedRates(tableName, 2);
        String expected = "update temp_fplos set\n" +
                " temp_fplos.rate1 =\n" +
                "   temp_fplos.Rate1\n" +
                "   +\n" +
                "   case\n" +
                "    when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "    when ra1.Net_Value_Type_ID = 2 then temp_fplos.Rate1 * (ra1.Net_Value / 100.0)\n" +
                "    when ra1.Net_Value_Type_ID = 3 then - temp_fplos.Rate1 + ra1.Net_Value\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_cost.Net_Value1 / 100.0)\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_cost.Net_Value1\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_value.Net_Value1 / 100.0)\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_value.Net_Value1\n" +
                "    else 0\n" +
                "   end\n" +
                " ,\n" +
                " temp_fplos.rate2 =\n" +
                "   temp_fplos.Rate1\n" +
                "   +\n" +
                "   case\n" +
                "    when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "    when ra1.Net_Value_Type_ID = 2 then temp_fplos.Rate1 * (ra1.Net_Value / 100.0)\n" +
                "    when ra1.Net_Value_Type_ID = 3 then - temp_fplos.Rate1 + ra1.Net_Value\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_cost.Net_Value1 / 100.0)\n" +
                "    when ra2_cost.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_cost.Net_Value1\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_value.Net_Value1 / 100.0)\n" +
                "    when ra2_value.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_value.Net_Value1\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   temp_fplos.Rate2\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "    when ra2_cost.Net_Value_Type_ID2 = 2 then temp_fplos.Rate2 * (ra2_cost.Net_Value2 / 100.0)\n" +
                "    when ra2_cost.Net_Value_Type_ID2 = 3 then - temp_fplos.Rate2 + ra2_cost.Net_Value2\n" +
                "    else 0\n" +
                "   end\n" +
                "   +\n" +
                "   case\n" +
                "    when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "    when ra2_value.Net_Value_Type_ID2 = 2 then temp_fplos.Rate2 * (ra2_value.Net_Value2 / 100.0)\n" +
                "    when ra2_value.Net_Value_Type_ID2 = 3 then - temp_fplos.Rate2 + ra2_value.Net_Value2\n" +
                "    else 0\n" +
                "   end\n" +
                "FROM temp_qualified_fplos_pid_5_1 temp_fplos\n" +
                "INNER JOIN Rate_Qualified rq\n" +
                " on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n" +
                "LEFT JOIN Rate_Qualified_Adjustment ra1\n" +
                " on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n" +
                " and ra1.Posting_Rule_ID = 1\n" +
                "LEFT JOIN #temp_qualified_fplos_pid_5_RateAdjustment ra2_cost\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n" +
                " and ra2_cost.AdjustmentType = 'YieldableCost'\n" +
                "LEFT JOIN #temp_qualified_fplos_pid_5_RateAdjustment ra2_value\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n" +
                " and ra2_value.AdjustmentType = 'YieldableValue'\n" +
                " where rq.Rate_Qualified_type_ID = 3;\n";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateBarRatesForBarByLos() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(2).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(true).setBarByLos(true).setBarMaxLos(2).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateBarRatesForBarByLos(2, tableName);
        String expected = "insert into #temp_qualified_fplos_pid_5_bar_value\n" +
                "SELECT Arrival_DT, Accom_Type_ID\n" +
                ", [1] AS BAR_RATE1, [2] AS BAR_RATE2\n" +
                " FROM (SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,LOS ,CASE DATEPART(WEEKDAY, D.Arrival_DT) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate\n" +
                " FROM Decision_Bar_Output as D\n" +
                " INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n" +
                " INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                " WHERE D.Arrival_DT BETWEEN :startDate AND :endDate\n" +
                " ) as A\n" +
                " PIVOT ( MAX(BAR_RATE) FOR los in ([1] ,[2])) as piv\n" +
                "where [1] is not null and [2] is not null;";
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateNonMasterRateCodeTableForDOW() {
        decisionQualifiedFPLOSBatchUpdaterBean =
                buildDecisionQualifiedFPLOSBatchUpdaterBean(connection, propertyId, maxLOS_3, isSrpFplosAtTotalLevel, lraEnabled, barMaxLos, isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateNonMasterRateCodeTableForDOW(tableName, maxLOS_3);
        assertPopulateNonMasterRateForDOWQuery(actual);
    }

    private void assertPopulateNonMasterRateForDOWQuery(String actual) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into #" + tableName + "_non_master \n")
                .append(" select t1.Arrival_DT, t1.Rate_Qualified_id\n");
        for (int i = 1; i <= maxLOS_3; i++) {
            stringBuilder.append(", max(Rate").append(i).append(") as Rate").append(i).append("\n");
        }
        stringBuilder.append("from #" + tableName + "_non_master_1 t1\n");
        stringBuilder.append("group by t1.arrival_dt, t1.Rate_Qualified_ID;\n");
        String expected = stringBuilder.toString();
        assertThat(actual, is(expected));
    }

    private static DecisionQualifiedFPLOSBatchUpdaterBean buildDecisionQualifiedFPLOSBatchUpdaterBean(Connection connection, int propertyId, int maxLOS_7, boolean isSrpFplosAtTotalLevel, int lraEnabled, int barMaxLos, boolean isServicingCostByLosEnabledAndConfigured) {
        return new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(false).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
    }

    @Test
    public void testPopulateNonMasterRateCodeTableForDOWForServicingCost() {
        decisionQualifiedFPLOSBatchUpdaterBean = buildDecisionQualifiedFPLOSBatchUpdaterBean(connection, propertyId, maxLOS_3, isSrpFplosAtTotalLevel, lraEnabled, barMaxLos, true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateNonMasterRateCodeTableForDOW(tableName, maxLOS_3);
        assertPopulateNonMasterRateForDOWQuery(actual);
    }

    @Test
    public void testCreateEffectiveRHBTable() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_RHB') IS NOT NULL DROP TABLE #" + tableName + "_RHB;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_RHB(");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        for (int los = 1; los <= maxLOS_3; los++) {
            stringBuilder.append("[RHB_LOS" + los + "] [int] NULL,\n");
        }
        stringBuilder.append("[LV0QualifiedId] [int] NOT NULL,\n");
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_RHB] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("Accom_Type_ID,Arrival_DT ASC\n");
        stringBuilder.append(")\n");
        stringBuilder.append(");\n");

        decisionQualifiedFPLOSBatchUpdaterBean =
                buildDecisionQualifiedFPLOSBatchUpdaterBean((Connection) connection, (int) propertyId, (int) maxLOS_7, (boolean) isSrpFplosAtTotalLevel, (int) lraEnabled, (int) barMaxLos, (boolean) isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRHBTable(tableName, maxLOS_3);
        assertThat(actual, is(stringBuilder.toString()));
    }

    @Test
    public void testPopulateEffectiveRHBTable() {
        StringBuilder stringBuilder = new StringBuilder();
        int lv0QualifiedId = 0;
        stringBuilder.append("insert into #" + tableName + "_RHB \n")
                .append("SELECT Arrival_DT,Accom_Type_ID, [1] AS RHB_LOS1\n");
        for (int i = 2; i <= maxLOS_3; i++) {
            stringBuilder.append(", ").append("[").append(i < barMaxLos ? i : barMaxLos).append("] AS RHB_LOS").append(i);
        }
        stringBuilder.append(", " + lv0QualifiedId + " AS LV0QualifiedId");
        stringBuilder.append(" FROM (SELECT Arrival_DT, Accom_Type_ID, los from Decision_Restrict_Highest_Bar ) AS RHB \n")
                .append("PIVOT \n")
                .append("( MAX(Los) FOR Los IN ([1]");
        for (int i = 2; i <= barMaxLos; i++) {
            stringBuilder.append(", [").append(i).append("]");
        }
        stringBuilder.append(")) AS pivotJoin \n");

        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean().setFirstChunk(true).setConnection(connection).setPropertyId(propertyId).setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel).setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(false).setDerivedRatePlanEnabled(false).setBarByLos(false).setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(true).setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateEffectiveRHBTable(tableName, maxLOS_3);
        assertThat(actual, is(stringBuilder.toString()));
    }

    @Test
    public void verifyGeneratedQueriesWhenHighestBARIsRestrictedAndSRPAtFPLOSIsFalse() {
        final String TEMP_TABLE_NAME_FOR_RESTRICT_HIGHEST_BAR = "temp_qualified_fplos_pid_5_RHB";
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setSrpFplosAtTotalLevel(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueries(tableName, maxLOS_7);
        assertThat(query, containsString(TEMP_TABLE_NAME_FOR_RESTRICT_HIGHEST_BAR));
    }

    private DecisionQualifiedFPLOSBatchUpdaterBean createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean() {
        return new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true)
                .setConnection(connection)
                .setPropertyId(propertyId)
                .setMaxLOS(maxLOS_7)
                .setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel)
                .setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(false)
                .setDerivedRatePlanEnabled(false)
                .setBarByLos(false)
                .setBarMaxLos(barMaxLos)
                .setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabled() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabled();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_7);
        assertEquals(expected, actual);
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabled() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los3=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3>=lrv3 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los4=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4>=lrv4 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los5=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4+Rate5>=lrv5 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los6=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4+Rate5+Rate6>=lrv6 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los7=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3+Rate4+Rate5+Rate6+Rate7>=lrv7 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 ;\n";
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARRestrictedAndServicingCostByLOS() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARIsRestrictedAndServicingCostByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setSrpFplosAtTotalLevel(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_3);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledAndHighestBARRestrictedIsDisabledForBARByLOSPropertyAndServicingCostByLOS() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledAndHighestBARRestrictedIsDisabledForBARByLOSPropertyAndServicingCostByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setSrpFplosAtTotalLevel(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_3);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledForBARByLOSPropertyAndHighestBARRestrictedAndServicingCostByLOS() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledForBARByLOSPropertyAndHighestBARRestrictedAndServicingCostByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setSrpFplosAtTotalLevel(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_3);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestrictedDisabledForBARByLOSPropertyAndServicingCostByLOS() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestrictedDisabledForBARByLOSPropertyAndServicingCostByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setSrpFplosAtTotalLevel(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_3);
        assertEquals(expected, actual);
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestrictedDisabledForBARByLOSPropertyAndServicingCostByLOS() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2 - ISNULL(servicingCost_los2, 0)>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3 - ISNULL(servicingCost_los3, 0)>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost  on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)\n" +
                " ;\n";
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledForBARByLOSPropertyAndHighestBARRestrictedAndServicingCostByLOS() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) or restHighestBar.RHB_LOS1=1 then 'N' else \n" +
                "\tcase when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) or restHighestBar.RHB_LOS2=2 then 'N' else \n" +
                "\tcase when Rate1+Rate2 - ISNULL(servicingCost_los2, 0)>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when restHighestBar.RHB_LOS3=3 then 'N' else \n" +
                "case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3 - ISNULL(servicingCost_los3, 0)>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost  on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)\n" +
                " LEFT OUTER JOIN #temp_qualified_fplos_pid_5_RHB AS restHighestBar \n" +
                "ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT \n" +
                "AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId  ;\n";
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledAndHighestBARRestrictedIsDisabledForBARByLOSPropertyAndServicingCostByLOS() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate2 - ISNULL(servicingCost_los2, 0)>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate3 - ISNULL(servicingCost_los3, 0)>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost  on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)\n" +
                " ;\n";
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARIsRestrictedAndServicingCostByLOS() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) or restHighestBar.RHB_LOS1=1 then 'N' else \n" +
                "\tcase when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) or restHighestBar.RHB_LOS2=2 then 'N' else \n" +
                "\tcase when Rate2 - ISNULL(servicingCost_los2, 0)>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when restHighestBar.RHB_LOS3=3 then 'N' else \n" +
                "case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate3 - ISNULL(servicingCost_los3, 0)>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 left join #temp_qualified_fplos_pid_5_servicingCost as servicingCost  on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)\n" +
                " LEFT OUTER JOIN #temp_qualified_fplos_pid_5_RHB AS restHighestBar \n" +
                "ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT \n" +
                "AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId  ;\n";
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARRestricted() {
        String expected = getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARIsRestricted();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_7);
        assertEquals(expected, actual);
    }

    private String getExpectedQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSPropertyAndHighestBARIsRestricted() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) or restHighestBar.RHB_LOS1=1 then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) or restHighestBar.RHB_LOS2=2 then 'N' else \n" +
                "\tcase when Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los3=6) or restHighestBar.RHB_LOS3=3 then 'N' else \n" +
                "\tcase when Rate3>=lrv3 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los4=6) or restHighestBar.RHB_LOS4=4 then 'N' else \n" +
                "\tcase when Rate4>=lrv4 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los5=6) or restHighestBar.RHB_LOS5=5 then 'N' else \n" +
                "\tcase when Rate5>=lrv5 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los6=6) or restHighestBar.RHB_LOS6=6 then 'N' else \n" +
                "\tcase when Rate6>=lrv6 then 'Y' else 'N' end end\n" +
                "+case when restHighestBar.RHB_LOS7=7 then 'N' else \n" +
                "case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los7=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate7>=lrv7 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 LEFT OUTER JOIN #temp_qualified_fplos_pid_5_RHB AS restHighestBar \n" +
                "ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT \n" +
                "AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId  ;\n";
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestricted() {
        String expected = getQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestricted();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setRestrictHighestBarEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_7);
        assertEquals(expected, actual);
    }

    private String getQueryToPopulateTempFPLOSTableWhenDerivedRatePlanDisabledAndHighestBARRestricted() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) or restHighestBar.RHB_LOS1=1 then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) or restHighestBar.RHB_LOS2=2 then 'N' else \n" +
                "\tcase when Rate1+Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los3=6) or restHighestBar.RHB_LOS3=3 then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3>=lrv3 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los4=6) or restHighestBar.RHB_LOS4=4 then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4>=lrv4 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los5=6) or restHighestBar.RHB_LOS5=5 then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4+Rate5>=lrv5 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los6=6) or restHighestBar.RHB_LOS6=6 then 'N' else \n" +
                "\tcase when Rate1+Rate2+Rate3+Rate4+Rate5+Rate6>=lrv6 then 'Y' else 'N' end end\n" +
                "+case when restHighestBar.RHB_LOS7=7 then 'N' else \n" +
                "case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los7=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3+Rate4+Rate5+Rate6+Rate7>=lrv7 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 LEFT OUTER JOIN #temp_qualified_fplos_pid_5_RHB AS restHighestBar \n" +
                "ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT \n" +
                "AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId  ;\n";
    }

    @Test
    public void verifyQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSProperty() {
        String expected = getQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSProperty();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setDerivedRatePlanEnabled(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTable(tableName, maxLOS_7);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyQueryOfCreateTempTableForDecisionBarOutputByLOS() {
        String expected = getBarOutputByLosQuery();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean());
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempTableForDecisionBarOutputByLOS(TEST_TABLE_NAME, maxLOS_7);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyQueryOfPopulateBarRatesWithLOS() {
        String expected = getPopulateBarRatesWithLOSQuery();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setBarMaxLos(7));
        String actual = decisionQualifiedFPLOSBatchUpdater.populateBarRatesWithLOS(TEST_TABLE_NAME, maxLOS_7);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyMethodCallsWhenDerivedRatesFPLOSCorrectionEnabled() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_7)
                        .setDerivedRatesFPLOSCorrectionEnabled(true)
                        .setBarByLos(true)
                        .setDerivedRatePlanEnabled(true)));
        String actual = spy.generateQueries(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).createTempTableForDecisionBarOutputByLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).populateBarRatesWithLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).updateTempTableForDerivedRatesAndRateAdjustments(TEST_TABLE_NAME, maxLOS_7);

        verify(spy, never()).createTempTableForDecisionBarOutput(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).populateTempTableForDecisionBarOutput(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments(TEST_TABLE_NAME, maxLOS_7);
    }

    @Test
    public void verifyMethodCallsWhenDerivedRatesFPLOSCorrectionIsDisabled() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_7)
                        .setBarByLos(true)
                        .setDerivedRatePlanEnabled(true)));
        spy.generateQueries(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).createTempTableForDecisionBarOutputByLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).populateBarRatesWithLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).updateTempTableForDerivedRatesAndRateAdjustments(TEST_TABLE_NAME, maxLOS_7);

        verify(spy).createAndPopulateDecisionBarOutputTableIfEnabled(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments(TEST_TABLE_NAME, maxLOS_7);
    }

    @Test
    public void verifyQueryOfUpdateTempTableForDerivedRatesAndRateAdjustments() {
        String expected = getQueryForUpdateTempTableForDerivedRatesAndRateAdjustments();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setBarMaxLos(maxLOS_7));
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableForDerivedRatesAndRateAdjustments(TEST_TABLE_NAME, maxLOS_7);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyMethodCallsWhenGenerateAgileProductQualifiedFPLOSIsDisabled() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_7)
                        .setBarByLos(false)
                        .setSrpFplosAtTotalLevel(false)
                        .setLraEnabledValue(0)
                        .setContinuousPricingEnabled(true)
                        .setDerivedRatePlanEnabled(true)
                        .setGenerateAgileProductQualifiedFPLOS(false)));
        spy.generateQueries(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).generateAgileProductQualifiedFPLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSForRoomTypeLevel(TEST_TABLE_NAME);
        verify(spy).populateQualifiedFPLOSTable(TEST_TABLE_NAME, false, false);
        verify(spy).updateQualifiedFPLOSTable(TEST_TABLE_NAME, false, false);
    }

    @Test
    public void verifyMethodCallsWhenGenerateAgileProductQualifiedFPLOSIsEnabled() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_7)
                        .setBarByLos(false)
                        .setSrpFplosAtTotalLevel(false)
                        .setLraEnabledValue(0)
                        .setContinuousPricingEnabled(true)
                        .setDerivedRatePlanEnabled(true)
                        .setGenerateAgileProductQualifiedFPLOS(true)));
        spy.generateQueries(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).generateAgileProductQualifiedFPLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy).buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSForRoomTypeLevel(anyString());
        verify(spy).populateQualifiedFPLOSTable(TEST_TABLE_NAME, false, false);
        verify(spy).updateQualifiedFPLOSTable(TEST_TABLE_NAME, false, false);
    }

    @Test
    public void verifyGenerateQueriesForCPBarByDayDerivedRatesSRPFPLOSAtHotelLevelIsFalseAgileFPLOSGenerationEnabledIsFalse() {
        System.setProperty("pacman.recommendation.cp.fplos.lead.enabled", "false");
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", FALSE);
        String expected = getQueriesForCPBarByDayDerivedRatesNonSRPFPLOSAtHotelLevelAgileFPLOSGenerationNotEnabled();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_3)
                        .setBarByLos(false)
                        .setContinuousPricingEnabled(true)
                        .setDerivedRatePlanEnabled(true)
                        .setSrpFplosAtTotalLevel(false)
                        .setGenerateAgileProductQualifiedFPLOS(false));
        String actual = decisionQualifiedFPLOSBatchUpdater.generateQueries(TEST_TABLE_NAME, maxLOS_3);
        actual = decisionQualifiedFPLOSBatchUpdater.updateQueryForHospitalityRoomsClause(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void verifyGenerateQueriesForCPBarByDayDerivedRatesSRPFPLOSAtHotelLevelIsFalseAgileFPLOSGenerationIsEnabled() {
        System.setProperty("pacman.recommendation.cp.fplos.lead.enabled", "false");
        System.setProperty("use.window.function.for.FPLOSBatch.RateAdjustment.query.optimization", FALSE);
        String expected = getQueriesForCPBarByDayDerivedRatesNonSRPFPLOSAtHotelLevelAgileFPLOSGenerationIsEnabled();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_3)
                        .setBarByLos(false)
                        .setContinuousPricingEnabled(true)
                        .setDerivedRatePlanEnabled(true)
                        .setSrpFplosAtTotalLevel(false)
                        .setGenerateAgileProductQualifiedFPLOS(true));
        String actual = decisionQualifiedFPLOSBatchUpdater.generateQueries(TEST_TABLE_NAME, maxLOS_3);
        actual = decisionQualifiedFPLOSBatchUpdater.updateQueryForHospitalityRoomsClause(actual);
        assertEquals(expected, actual);
    }

    private String getQueriesForCPBarByDayDerivedRatesNonSRPFPLOSAtHotelLevelAgileFPLOSGenerationIsEnabled() {
        return "IF OBJECT_ID('tempdb..#testTableName') IS NOT NULL DROP TABLE #testTableName;\n" +
                "CREATE TABLE #testTableName([Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Class_ID] [int] NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_testTableName] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Class_ID,Arrival_DT ASC\n" +
                ") ) ON [PRIMARY];\n" +
                "insert into #testTableName\n" +
                "SELECT  cal.calendar_date, \n" +
                "      a.Accom_Class_ID, \n" +
                "      isnull(lrv1.LRV,0) as lrv1 \n" +
                ", isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  as lrv2\n" +
                " , isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  + isnull(lrv3.LRV,0)  as lrv3\n" +
                " \n" +
                "FROM calendar_dim cal \n" +
                "INNER JOIN Accom_Class a on a.Property_ID = :propertyId and a.System_Default != 1 \n" +
                " and a.Accom_Class_ID in \n" +
                "(SELECT DISTINCT Accom_Class_ID from Accom_Type \n" +
                "where Property_ID = :propertyId and Status_ID = 1 and System_Default != 1 ) \n" +
                "LEFT JOIN Decision_LRV lrv1 on lrv1.Accom_Class_ID = a.Accom_Class_ID and lrv1.Occupancy_DT = DATEADD(day,0,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv2 on lrv2.Accom_Class_ID = a.Accom_Class_ID and lrv2.Occupancy_DT = DATEADD(day,1,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv3 on lrv3.Accom_Class_ID = a.Accom_Class_ID and lrv3.Occupancy_DT = DATEADD(day,2,cal.calendar_date) \n" +
                "WHERE cal.calendar_date >= :startDate and cal.calendar_date <= :endDate\n" +
                "ORDER BY cal.calendar_date, a.Accom_Class_ID;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_bar_value') IS NOT NULL DROP TABLE #testTableName_bar_value;\n" +
                "CREATE TABLE #testTableName_bar_value(\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[BAR_Rate1] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate2] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate3] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_testTableName_bar_value] PRIMARY KEY CLUSTERED \n" +
                "(Arrival_DT,Accom_Type_ID ASC)\n" +
                ");\n" +
                "insert into #testTableName_bar_value\n" +
                " SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,CP.Final_BAR AS BAR_Rate1  ,CP2.Final_BAR AS BAR_Rate2 ,CP3.Final_BAR AS BAR_Rate3 from CP_Decision_Bar_Output AS CP  INNER JOIN CP_Decision_Bar_Output AS CP2 ON CP.Accom_Type_ID = CP2.Accom_Type_ID AND CP2.Arrival_DT = dateadd(DAY,1, CP.Arrival_DT)AND CP2.LOS = CP.LOS  AND CP2.Final_BAR IS NOT NULL AND CP.Product_ID=CP2.Product_ID INNER JOIN CP_Decision_Bar_Output AS CP3 ON CP.Accom_Type_ID = CP3.Accom_Type_ID AND CP3.Arrival_DT = dateadd(DAY,2, CP.Arrival_DT)AND CP3.LOS = CP.LOS  AND CP3.Final_BAR IS NOT NULL AND CP.Product_ID=CP3.Product_ID where CP.Arrival_DT BETWEEN :startDate AND DATEADD(DAY,3, :endDate)  AND CP.LOS = -1 AND CP.Final_BAR IS NOT NULL AND CP.Product_ID=1;IF OBJECT_ID('testTableName_1') IS NOT NULL DROP TABLE testTableName_1;\n" +
                "CREATE TABLE testTableName_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate] [numeric](19,5) NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "IF OBJECT_ID('tempdb..#testTableName_1a') IS NOT NULL DROP TABLE #testTableName_1a;\n" +
                "CREATE TABLE #testTableName_1a(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[Rate] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv1] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv2] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_1a] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tAccom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "insert into #testTableName_1a\n" +
                "select calendar_date as Arrival_DT,Rate_Qualified_Details.Accom_Type_ID,Rate_Qualified_Details.Rate_Qualified_ID,Rate=case\n" +
                " when DATEPART(weekday,calendar_date) = 1 then Sunday\n" +
                " when DATEPART(weekday,calendar_date) = 2 then Monday\n" +
                " when DATEPART(weekday,calendar_date) = 3 then Tuesday\n" +
                " when DATEPART(weekday,calendar_date) = 4 then Wednesday\n" +
                " when DATEPART(weekday,calendar_date) = 5 then Thursday\n" +
                " when DATEPART(weekday,calendar_date) = 6 then Friday\n" +
                " when DATEPART(weekday,calendar_date) = 7 then Saturday\n" +
                "end, lrv1\n" +
                ", lrv2\n" +
                ", lrv3\n" +
                "from  Rate_Qualified_Details\n" +
                " inner join Rate_Qualified on Rate_Qualified.Rate_Qualified_ID=Rate_Qualified_Details.Rate_Qualified_ID\n" +
                " inner join Accom_Type on Rate_Qualified_Details.Accom_Type_ID=Accom_Type.Accom_Type_ID\n" +
                " inner join calendar_dim on calendar_date between Rate_Qualified_Details.Start_Date_DT and Rate_Qualified_Details.End_Date_DT\n" +
                " inner join Accom_Activity on Accom_Type.Accom_Type_ID=Accom_Activity.Accom_Type_ID and calendar_date = Accom_Activity.Occupancy_DT\n" +
                " inner join #testTableName on #testTableName.Accom_Class_ID=Accom_Type.Accom_Class_ID and #testTableName.Arrival_DT = calendar_date\n" +
                " where  Rate_Qualified.Status_ID=1\n" +
                " and Rate_Qualified.Yieldable=1\n" +
                " and Accom_Type.Status_ID=1\n" +
                " and (Accom_Activity.Accom_Capacity > 0   )\n" +
                " and Rate_Qualified.Property_ID=:propertyId\n" +
                " and calendar_date between :startDate and :endDate\n" +
                " and not exists( select agileProd.Rate_Qualified_ID from Agile_Product_Restriction_Association agileProd where agileProd.Rate_Qualified_ID = Rate_Qualified.Rate_Qualified_id); \n" +
                "IF OBJECT_ID('tempdb..#temp_qualified_agile_fplos_pid_5_1a') IS NOT NULL DROP TABLE #temp_qualified_agile_fplos_pid_5_1a;\n" +
                "CREATE TABLE #temp_qualified_agile_fplos_pid_5_1a(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[Product_ID][bigint] NOT NULL,\n" +
                "\t[Rate] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv1] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv2] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_agile_fplos_pid_5_1a] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Accom_Type_ID,Rate_Qualified_id,Product_ID ASC )\n" +
                "); \n" +
                "insert into #temp_qualified_agile_fplos_pid_5_1a\n" +
                " select\n" +
                " cpBar.Arrival_DT, cpBar.Accom_Type_ID, agileProduct.Rate_Qualified_ID, cpBar.Product_ID, cpBar.Final_BAR as Rate\n" +
                " , lrv.lrv1, lrv.lrv2, lrv.lrv3\n" +
                " from\n" +
                " CP_Decision_Bar_Output as cpBar\n" +
                " inner join\n" +
                " Agile_Product_Restriction_Association as agileProduct\n" +
                " on cpBar.Product_ID = agileProduct.Product_ID\n" +
                " inner join\n" +
                " Product as pr\n" +
                " on pr.Product_ID = agileProduct.Product_ID\n" +
                " inner join\n" +
                " Rate_Qualified as rq\n" +
                " on rq.Rate_Qualified_ID = agileProduct.Rate_Qualified_ID and rq.Status_ID = 1 and rq.Yieldable = 1 and rq.Property_ID = :propertyId\n" +
                " inner join\n" +
                " Accom_Type as accomType\n" +
                " on accomType.Accom_Type_ID = cpBar.Accom_Type_ID \n" +
                " inner join #testTableName as lrv\n" +
                " on lrv.Arrival_DT = cpBar.Arrival_DT and lrv.Accom_Class_ID = accomType.Accom_Class_ID\n" +
                " where cpBar.Product_ID >= 5 and cpBar.Arrival_DT between :startDate and :endDate and cpBar.LOS = -1\n" +
                " and pr.Status_ID = 1 and cpBar.Property_ID = :propertyId\n" +
                " and cpBar.Final_BAR IS NOT NULL and (accomType.Accom_Type_Capacity > 0  ) ;\n" +
                "IF OBJECT_ID('temp_qualified_agile_fplos_pid_5_1') IS NOT NULL DROP TABLE temp_qualified_agile_fplos_pid_5_1;\n" +
                "CREATE TABLE temp_qualified_agile_fplos_pid_5_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_agile_fplos_pid_5_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                " Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC )\n" +
                "); \n" +
                "insert into temp_qualified_agile_fplos_pid_5_1\n" +
                "select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t1.lrv1\n" +
                ", t1.lrv2\n" +
                ", t1.lrv3\n" +
                " from #temp_qualified_agile_fplos_pid_5_1a t1 \n" +
                " inner join #temp_qualified_agile_fplos_pid_5_1a t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID\n" +
                " inner join #temp_qualified_agile_fplos_pid_5_1a t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID\n" +
                ";\n" +
                "IF OBJECT_ID('tempdb..#temp_qualified_agile_fplos_pid_5_2') IS NOT NULL DROP TABLE #temp_qualified_agile_fplos_pid_5_2;\n" +
                "CREATE TABLE #temp_qualified_agile_fplos_pid_5_2(\n" +
                " [Property_ID] [int] NOT NULL,\n" +
                " [Arrival_DT] [date] NOT NULL,\n" +
                " [Accom_Type_ID] [int] NOT NULL,\n" +
                " [Rate_Qualified_id] [int] NOT NULL,\n" +
                " [FPLOS] [varchar](21) NOT NULL,\n" +
                "\n" +
                "CONSTRAINT [PK_temp_qualified_agile_fplos_pid_5_2] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                " Property_ID,Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC )\n" +
                "); \n" +
                "insert into #temp_qualified_agile_fplos_pid_5_2\n" +
                " select :propertyId,agileRatesLrv.Arrival_DT,\n" +
                " agileRatesLrv.Accom_Type_ID,\n" +
                " agileRatesLrv.Rate_Qualified_id,\n" +
                " case when Rate1 >= lrv1 then 'Y' else 'N' end + \n" +
                " case when Rate1+Rate2 >= lrv2 then 'Y' else 'N' end + \n" +
                " case when Rate1+Rate2+Rate3 >= lrv3 then 'Y' else 'N' end \n" +
                " as FPLOS\n" +
                " from temp_qualified_agile_fplos_pid_5_1 AS agileRatesLrv;\n" +
                "drop table #temp_qualified_agile_fplos_pid_5_1a;\n" +
                "drop table temp_qualified_agile_fplos_pid_5_1;\n" +
                "insert into testTableName_1\n" +
                " select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,t1.Rate\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t1.lrv1\n" +
                ", t1.lrv2\n" +
                ", t1.lrv3\n" +
                "from #testTableName_1a t1 \n" +
                "inner join #testTableName_1a t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID \n" +
                "inner join #testTableName_1a t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID \n" +
                ";drop table #testTableName_1a;\n" +
                "drop table #testTableName;delete testTableName_1\n" +
                " FROM testTableName_1\n" +
                " inner join Rate_Qualified on testTableName_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #testTableName_bar_value on testTableName_1.arrival_dt = #testTableName_bar_value.arrival_dt and testTableName_1.Accom_Type_ID = #testTableName_bar_value.Accom_Type_ID\n" +
                " where  ( #testTableName_bar_value.BAR_Rate1 is null\n" +
                " or #testTableName_bar_value.BAR_Rate2 is null\n" +
                " or #testTableName_bar_value.BAR_Rate3 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)\n" +
                "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end\n" +
                " FROM testTableName_1 temp_fplos\n" +
                " INNER JOIN #testTableName_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustmentType') IS NOT NULL DROP TABLE #testTableName_RateAdjustmentType;\n" +
                "CREATE TABLE #testTableName_RateAdjustmentType(\n" +
                "[AdjustmentType] [varchar](20) NOT NULL);\n" +
                "insert into #testTableName_RateAdjustmentType\n" +
                "values ('YieldableValue'), ('YieldableCost');\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustment') IS NOT NULL DROP TABLE #testTableName_RateAdjustment;\n" +
                "CREATE TABLE #testTableName_RateAdjustment(\n" +
                "          [Arrival_DT] [date] NOT NULL,\n" +
                "          [Rate_Qualified_id] [int] NOT NULL,\n" +
                "          [AdjustmentType] [varchar](50) NOT NULL,\n" +
                "          [Net_Value1] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID1] [int],\n" +
                "          [Net_Value2] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID2] [int],\n" +
                "          [Net_Value3] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID3] [int],\n" +
                " CONSTRAINT [PK_testTableName_RateAdjustment] PRIMARY KEY CLUSTERED ([Arrival_DT],[Rate_Qualified_ID],[AdjustmentType] ASC )\n" +
                ");\n" +
                "insert into #testTableName_RateAdjustment\n" +
                "select cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType\n" +
                ", adj1.Net_Value as Net_Value1, adj1.Net_Value_Type_ID as Net_Value_Type_ID1\n" +
                ", adj2.Net_Value as Net_Value2, adj2.Net_Value_Type_ID as Net_Value_Type_ID2\n" +
                ", adj3.Net_Value as Net_Value3, adj3.Net_Value_Type_ID as Net_Value_Type_ID3\n" +
                "from calendar_dim cal\n" +
                "cross join Rate_Qualified rq\n" +
                "cross join #testTableName_RateAdjustmentType rat\n" +
                "left join Rate_Qualified_Adjustment adj1 on dateadd(day, 0, cal.calendar_date) between adj1.Start_Date_DT and adj1.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj1.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj1.AdjustmentType\n" +
                "and adj1.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj2 on dateadd(day, 1, cal.calendar_date) between adj2.Start_Date_DT and adj2.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj2.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj2.AdjustmentType\n" +
                "and adj2.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj3 on dateadd(day, 2, cal.calendar_date) between adj3.Start_Date_DT and adj3.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj3.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj3.AdjustmentType\n" +
                "and adj3.Posting_Rule_ID = 2\n" +
                "where calendar_date between :startDate and :endDate\n" +
                "and rq.Status_Id = 1\n" +
                "and coalesce(adj1.Rate_Qualified_ID, adj2.Rate_Qualified_ID, adj3.Rate_Qualified_ID) is not null;\n" +
                "update testTableName_1 set\n" +
                "Rate1 = case\n" +
                "when Net_Value_Type_ID = 1 then Rate1 + Net_Value\n" +
                "when Net_Value_Type_ID = 2 then Rate1 + ((Rate1 * Net_Value) / 100.0)\n" +
                "when Net_Value_Type_ID = 3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate2 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate2\n" +
                "end\n" +
                ",\n" +
                "Rate3 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate3\n" +
                "end\n" +
                "from\n" +
                "testTableName_1 inner join Rate_Qualified_Adjustment\n" +
                "on testTableName_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n" +
                "where Posting_Rule_ID = 1;\n" +
                "update testTableName_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM testTableName_1\n" +
                "inner join #testTableName_RateAdjustment ra\n" +
                "on testTableName_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableCost';\n" +
                "update testTableName_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM testTableName_1\n" +
                "inner join #testTableName_RateAdjustment ra\n" +
                "on testTableName_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableValue';\n" +
                "IF OBJECT_ID('tempdb..#testTableName_2') IS NOT NULL DROP TABLE #testTableName_2;\n" +
                "CREATE TABLE #testTableName_2(\n" +
                "\t[Property_ID] [int] NOT NULL,\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[FPLOS] [varchar](21) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_2] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tProperty_ID,Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA') IS NOT NULL DROP TABLE #testTableName_LRA;\n" +
                "CREATE TABLE #testTableName_LRA(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[Rate] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate1] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate2] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate3] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv1] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv2] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\t[LOS] [int] NULL,\n" +
                "\t[Decision_Reason_Type_ID] [int]\n" +
                ")\n" +
                ";\n" +
                "insert into #testTableName_LRA\n" +
                "select testTableName_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID FROM testTableName_1 left join \n" +
                "(\n" +
                "\tselect Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:specialSrpId Rate_Qualified_ID,Decision_Reason_Type_ID from\n" +
                "\t(\n" +
                "\t\tselect * from decision_bar_output \n" +
                "\t\twhere Rate_Unqualified_ID=:lv0UnqualifiedId and Accom_Class_ID=:masterClassId and Decision_Reason_Type_ID=6\n" +
                "\t) sub_optimal_LV0\n" +
                "\tinner join\n" +
                "\t(\n" +
                "\t\tselect distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n" +
                "\t\tfrom Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n" +
                "\t\twhere AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:masterClassId and AT.Property_ID=:propertyId\n" +
                "\t) MASTER_AC_AT\n" +
                "\ton sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n" +
                ") LRA_IMPACT on testTableName_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=testTableName_1.Accom_Type_ID\n" +
                "and testTableName_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA_1') IS NOT NULL DROP TABLE #testTableName_LRA_1;\n" +
                "CREATE TABLE #testTableName_LRA_1(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Accom_Type_ID] [int] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                "\t,[Rate] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate1] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate2] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate3] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv1] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv2] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv3] [numeric](19,5) NOT NULL\n" +
                "\t,[ReasonType_los1] [int]\n" +
                "\t,[ReasonType_los2] [int]\n" +
                "\t,[ReasonType_los3] [int]\n" +
                ")\n" +
                ";\n" +
                "insert into #testTableName_LRA_1\n" +
                " select Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                " ,Rate1\n" +
                " ,Rate2\n" +
                " ,Rate3\n" +
                " ,lrv1\n" +
                " ,lrv2\n" +
                " ,lrv3\n" +
                " ,max([1])  as reasonType_los1\n" +
                " ,max([2])  as reasonType_los2\n" +
                " ,max([3])  as reasonType_los3\n" +
                " from\n" +
                " (\n" +
                "\t select :propertyId propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                "\t ,Rate1\n" +
                "\t ,Rate2\n" +
                "\t ,Rate3\n" +
                "\t ,lrv1\n" +
                "\t ,lrv2\n" +
                "\t ,lrv3\n" +
                "\t ,(case los when 1 then  Decision_Reason_Type_ID end ) as [1]\n" +
                "\t ,(case los when 2 then  Decision_Reason_Type_ID end ) as [2]\n" +
                "\t ,(case los when 3 then  Decision_Reason_Type_ID end ) as [3]\n" +
                "\t from #testTableName_LRA\n" +
                ") as x group by propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                ",Rate1\n" +
                ",Rate2\n" +
                ",Rate3\n" +
                ",lrv1\n" +
                ",lrv2\n" +
                ",lrv3\n" +
                "insert into #testTableName_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #testTableName_LRA_1 AS LRA_1 ;\n" +
                "update #testTableName_2 \n" +
                "set FPLOS = agileFplos.FPLOS\n" +
                "from #temp_qualified_agile_fplos_pid_5_2 as agileFplos \n" +
                "left join #testTableName_2 as regularFplos\n" +
                "on agileFplos.Property_ID = regularFplos.Property_ID and \n" +
                "agileFplos.Arrival_DT = regularFplos.Arrival_DT and \n" +
                "agileFplos.Accom_Type_ID = regularFplos.Accom_Type_ID and \n" +
                "agileFplos.Rate_Qualified_id = regularFplos.Rate_Qualified_id\n" +
                "where agileFplos.FPLOS <> regularFplos.FPLOS;\n" +
                "insert into #testTableName_2 \n" +
                "(Property_ID, Arrival_DT, Accom_Type_ID, Rate_Qualified_id, FPLOS)\n" +
                "select agileFplos.* from #temp_qualified_agile_fplos_pid_5_2 as agileFplos\n" +
                "left join #testTableName_2 as regularFplos\n" +
                "on agileFplos.Property_ID = regularFplos.Property_ID and \n" +
                "agileFplos.Arrival_DT = regularFplos.Arrival_DT and \n" +
                "agileFplos.Accom_Type_ID = regularFplos.Accom_Type_ID and \n" +
                "agileFplos.Rate_Qualified_id = regularFplos.Rate_Qualified_id\n" +
                "where regularFplos.FPLOS is null;\n" +
                "drop table #temp_qualified_agile_fplos_pid_5_2;\n" +
                "insert into Decision_Qualified_FPLOS (Decision_ID,Property_ID,Accom_Type_ID,Rate_qualified_ID,Arrival_DT,FPLOS)\n" +
                "select :decisionId,:propertyId\n" +
                ",a.Accom_Type_ID\n" +
                ",a.Rate_qualified_ID,a.Arrival_DT,a.FPLOS \n" +
                "from #testTableName_2 a left join Decision_Qualified_FPLOS b\n" +
                "on\n" +
                "a.Accom_Type_ID=b.Accom_Type_ID\n" +
                "and\n" +
                "a.Arrival_DT=b.Arrival_DT\n" +
                "and\n" +
                "a.Rate_qualified_ID=b.Rate_qualified_ID\n" +
                "where \n" +
                "b.FPLOS is null;\n" +
                "update Decision_Qualified_FPLOS \n" +
                "set Decision_ID=:decisionId\n" +
                ",fplos = a.FPLOS\n" +
                "from #testTableName_2 a inner join Decision_Qualified_FPLOS b\n" +
                "on \n" +
                "a.Accom_Type_ID=b.Accom_Type_ID and \n" +
                "a.Arrival_DT=b.Arrival_DT and\n" +
                "a.Rate_qualified_ID=b.Rate_qualified_ID and\n" +
                "a.Property_ID=b.Property_ID\n" +
                "where a.FPLOS <> b.FPLOS\n" +
                ";\n" +
                "delete Decision_Qualified_FPLOS \n" +
                "from #testTableName_2 a right join Decision_Qualified_FPLOS b\n" +
                "on \n" +
                "a.Property_ID=b.Property_ID\n" +
                "and a.Accom_Type_ID=b.Accom_Type_ID\n" +
                "and\n" +
                "a.Arrival_DT=b.Arrival_DT\n" +
                "and\n" +
                "a.Rate_Qualified_ID=b.Rate_Qualified_ID\n" +
                "\n" +
                "where b.Property_ID=:propertyId\n" +
                "and\n" +
                "b.Arrival_DT between :startDate and DATEADD(DAY,-2,:endDate)\n" +
                "and\n" +
                "a.FPLOS is null;\n" +
                "insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n" +
                "select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n" +
                "where property_id=:propertyId\n" +
                "and\n" +
                "decision_id=:decisionId;\n" +
                "IF OBJECT_ID('tempdb..#testTableName') IS NOT NULL DROP TABLE #testTableName;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_1a') IS NOT NULL DROP TABLE #testTableName_1a;\n" +
                "IF OBJECT_ID('testTableName_1') IS NOT NULL DROP TABLE testTableName_1;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_2') IS NOT NULL DROP TABLE #testTableName_2;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA') IS NOT NULL DROP TABLE #testTableName_LRA;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA_1') IS NOT NULL DROP TABLE #testTableName_LRA_1;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_bar_value') IS NOT NULL DROP TABLE #testTableName_bar_value;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustment') IS NOT NULL DROP TABLE #testTableName_RateAdjustment;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustmentType') IS NOT NULL DROP TABLE #testTableName_RateAdjustmentType;\n";
    }

    private String getQueriesForCPBarByDayDerivedRatesNonSRPFPLOSAtHotelLevelAgileFPLOSGenerationNotEnabled() {
        return "IF OBJECT_ID('tempdb..#testTableName') IS NOT NULL DROP TABLE #testTableName;\n" +
                "CREATE TABLE #testTableName([Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Class_ID] [int] NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_testTableName] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Class_ID,Arrival_DT ASC\n" +
                ") ) ON [PRIMARY];\n" +
                "insert into #testTableName\n" +
                "SELECT  cal.calendar_date, \n" +
                "      a.Accom_Class_ID, \n" +
                "      isnull(lrv1.LRV,0) as lrv1 \n" +
                ", isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  as lrv2\n" +
                " , isnull(lrv1.LRV,0) + isnull(lrv2.LRV,0)  + isnull(lrv3.LRV,0)  as lrv3\n" +
                " \n" +
                "FROM calendar_dim cal \n" +
                "INNER JOIN Accom_Class a on a.Property_ID = :propertyId and a.System_Default != 1 \n" +
                " and a.Accom_Class_ID in \n" +
                "(SELECT DISTINCT Accom_Class_ID from Accom_Type \n" +
                "where Property_ID = :propertyId and Status_ID = 1 and System_Default != 1 ) \n" +
                "LEFT JOIN Decision_LRV lrv1 on lrv1.Accom_Class_ID = a.Accom_Class_ID and lrv1.Occupancy_DT = DATEADD(day,0,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv2 on lrv2.Accom_Class_ID = a.Accom_Class_ID and lrv2.Occupancy_DT = DATEADD(day,1,cal.calendar_date) \n" +
                "LEFT JOIN Decision_LRV lrv3 on lrv3.Accom_Class_ID = a.Accom_Class_ID and lrv3.Occupancy_DT = DATEADD(day,2,cal.calendar_date) \n" +
                "WHERE cal.calendar_date >= :startDate and cal.calendar_date <= :endDate\n" +
                "ORDER BY cal.calendar_date, a.Accom_Class_ID;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_bar_value') IS NOT NULL DROP TABLE #testTableName_bar_value;\n" +
                "CREATE TABLE #testTableName_bar_value(\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[BAR_Rate1] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate2] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate3] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_testTableName_bar_value] PRIMARY KEY CLUSTERED \n" +
                "(Arrival_DT,Accom_Type_ID ASC)\n" +
                ");\n" +
                "insert into #testTableName_bar_value\n" +
                " SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,CP.Final_BAR AS BAR_Rate1  ,CP2.Final_BAR AS BAR_Rate2 ,CP3.Final_BAR AS BAR_Rate3 from CP_Decision_Bar_Output AS CP  INNER JOIN CP_Decision_Bar_Output AS CP2 ON CP.Accom_Type_ID = CP2.Accom_Type_ID AND CP2.Arrival_DT = dateadd(DAY,1, CP.Arrival_DT)AND CP2.LOS = CP.LOS  AND CP2.Final_BAR IS NOT NULL AND CP.Product_ID=CP2.Product_ID INNER JOIN CP_Decision_Bar_Output AS CP3 ON CP.Accom_Type_ID = CP3.Accom_Type_ID AND CP3.Arrival_DT = dateadd(DAY,2, CP.Arrival_DT)AND CP3.LOS = CP.LOS  AND CP3.Final_BAR IS NOT NULL AND CP.Product_ID=CP3.Product_ID where CP.Arrival_DT BETWEEN :startDate AND DATEADD(DAY,3, :endDate)  AND CP.LOS = -1 AND CP.Final_BAR IS NOT NULL AND CP.Product_ID=1;IF OBJECT_ID('testTableName_1') IS NOT NULL DROP TABLE testTableName_1;\n" +
                "CREATE TABLE testTableName_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate] [numeric](19,5) NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "IF OBJECT_ID('tempdb..#testTableName_1a') IS NOT NULL DROP TABLE #testTableName_1a;\n" +
                "CREATE TABLE #testTableName_1a(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[Rate] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv1] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv2] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_1a] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tAccom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "insert into #testTableName_1a\n" +
                "select calendar_date as Arrival_DT,Rate_Qualified_Details.Accom_Type_ID,Rate_Qualified_Details.Rate_Qualified_ID,Rate=case\n" +
                " when DATEPART(weekday,calendar_date) = 1 then Sunday\n" +
                " when DATEPART(weekday,calendar_date) = 2 then Monday\n" +
                " when DATEPART(weekday,calendar_date) = 3 then Tuesday\n" +
                " when DATEPART(weekday,calendar_date) = 4 then Wednesday\n" +
                " when DATEPART(weekday,calendar_date) = 5 then Thursday\n" +
                " when DATEPART(weekday,calendar_date) = 6 then Friday\n" +
                " when DATEPART(weekday,calendar_date) = 7 then Saturday\n" +
                "end, lrv1\n" +
                ", lrv2\n" +
                ", lrv3\n" +
                "from  Rate_Qualified_Details\n" +
                " inner join Rate_Qualified on Rate_Qualified.Rate_Qualified_ID=Rate_Qualified_Details.Rate_Qualified_ID\n" +
                " inner join Accom_Type on Rate_Qualified_Details.Accom_Type_ID=Accom_Type.Accom_Type_ID\n" +
                " inner join calendar_dim on calendar_date between Rate_Qualified_Details.Start_Date_DT and Rate_Qualified_Details.End_Date_DT\n" +
                " inner join Accom_Activity on Accom_Type.Accom_Type_ID=Accom_Activity.Accom_Type_ID and calendar_date = Accom_Activity.Occupancy_DT\n" +
                " inner join #testTableName on #testTableName.Accom_Class_ID=Accom_Type.Accom_Class_ID and #testTableName.Arrival_DT = calendar_date\n" +
                " where  Rate_Qualified.Status_ID=1\n" +
                " and Rate_Qualified.Yieldable=1\n" +
                " and Accom_Type.Status_ID=1\n" +
                " and (Accom_Activity.Accom_Capacity > 0   )\n" +
                " and Rate_Qualified.Property_ID=:propertyId\n" +
                " and calendar_date between :startDate and :endDate\n" +
                "insert into testTableName_1\n" +
                " select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,t1.Rate\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t1.lrv1\n" +
                ", t1.lrv2\n" +
                ", t1.lrv3\n" +
                "from #testTableName_1a t1 \n" +
                "inner join #testTableName_1a t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID \n" +
                "inner join #testTableName_1a t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID \n" +
                ";drop table #testTableName_1a;\n" +
                "drop table #testTableName;delete testTableName_1\n" +
                " FROM testTableName_1\n" +
                " inner join Rate_Qualified on testTableName_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #testTableName_bar_value on testTableName_1.arrival_dt = #testTableName_bar_value.arrival_dt and testTableName_1.Accom_Type_ID = #testTableName_bar_value.Accom_Type_ID\n" +
                " where  ( #testTableName_bar_value.BAR_Rate1 is null\n" +
                " or #testTableName_bar_value.BAR_Rate2 is null\n" +
                " or #testTableName_bar_value.BAR_Rate3 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)\n" +
                "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end\n" +
                " FROM testTableName_1 temp_fplos\n" +
                " INNER JOIN #testTableName_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustmentType') IS NOT NULL DROP TABLE #testTableName_RateAdjustmentType;\n" +
                "CREATE TABLE #testTableName_RateAdjustmentType(\n" +
                "[AdjustmentType] [varchar](20) NOT NULL);\n" +
                "insert into #testTableName_RateAdjustmentType\n" +
                "values ('YieldableValue'), ('YieldableCost');\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustment') IS NOT NULL DROP TABLE #testTableName_RateAdjustment;\n" +
                "CREATE TABLE #testTableName_RateAdjustment(\n" +
                "          [Arrival_DT] [date] NOT NULL,\n" +
                "          [Rate_Qualified_id] [int] NOT NULL,\n" +
                "          [AdjustmentType] [varchar](50) NOT NULL,\n" +
                "          [Net_Value1] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID1] [int],\n" +
                "          [Net_Value2] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID2] [int],\n" +
                "          [Net_Value3] [numeric](19, 5),\n" +
                "          [Net_Value_Type_ID3] [int],\n" +
                " CONSTRAINT [PK_testTableName_RateAdjustment] PRIMARY KEY CLUSTERED ([Arrival_DT],[Rate_Qualified_ID],[AdjustmentType] ASC )\n" +
                ");\n" +
                "insert into #testTableName_RateAdjustment\n" +
                "select cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType\n" +
                ", adj1.Net_Value as Net_Value1, adj1.Net_Value_Type_ID as Net_Value_Type_ID1\n" +
                ", adj2.Net_Value as Net_Value2, adj2.Net_Value_Type_ID as Net_Value_Type_ID2\n" +
                ", adj3.Net_Value as Net_Value3, adj3.Net_Value_Type_ID as Net_Value_Type_ID3\n" +
                "from calendar_dim cal\n" +
                "cross join Rate_Qualified rq\n" +
                "cross join #testTableName_RateAdjustmentType rat\n" +
                "left join Rate_Qualified_Adjustment adj1 on dateadd(day, 0, cal.calendar_date) between adj1.Start_Date_DT and adj1.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj1.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj1.AdjustmentType\n" +
                "and adj1.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj2 on dateadd(day, 1, cal.calendar_date) between adj2.Start_Date_DT and adj2.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj2.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj2.AdjustmentType\n" +
                "and adj2.Posting_Rule_ID = 2\n" +
                "left join Rate_Qualified_Adjustment adj3 on dateadd(day, 2, cal.calendar_date) between adj3.Start_Date_DT and adj3.End_Date_DT\n" +
                "and rq.Rate_Qualified_ID = adj3.Rate_Qualified_ID\n" +
                "and rat.AdjustmentType = adj3.AdjustmentType\n" +
                "and adj3.Posting_Rule_ID = 2\n" +
                "where calendar_date between :startDate and :endDate\n" +
                "and rq.Status_Id = 1\n" +
                "and coalesce(adj1.Rate_Qualified_ID, adj2.Rate_Qualified_ID, adj3.Rate_Qualified_ID) is not null;\n" +
                "update testTableName_1 set\n" +
                "Rate1 = case\n" +
                "when Net_Value_Type_ID = 1 then Rate1 + Net_Value\n" +
                "when Net_Value_Type_ID = 2 then Rate1 + ((Rate1 * Net_Value) / 100.0)\n" +
                "when Net_Value_Type_ID = 3 then Net_Value\n" +
                "end\n" +
                ",\n" +
                "Rate2 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate2\n" +
                "end\n" +
                ",\n" +
                "Rate3 = case\n" +
                "when Net_Value_Type_ID = 3 then 0\n" +
                "else Rate3\n" +
                "end\n" +
                "from\n" +
                "testTableName_1 inner join Rate_Qualified_Adjustment\n" +
                "on testTableName_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n" +
                "where Posting_Rule_ID = 1;\n" +
                "update testTableName_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM testTableName_1\n" +
                "inner join #testTableName_RateAdjustment ra\n" +
                "on testTableName_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableCost';\n" +
                "update testTableName_1 set\n" +
                "Rate1 = \n" +
                "case when Net_Value_Type_ID1 = 1 then Rate1 + Net_Value1\n" +
                "when Net_Value_Type_ID1 = 2 then Rate1 + ((Rate1 * Net_Value1) / 100.0)\n" +
                "when Net_Value_Type_ID1 = 3 then Net_Value1\n" +
                "else Rate1\n" +
                "end,\n" +
                "Rate2 = \n" +
                "case when Net_Value_Type_ID2 = 1 then Rate2 + Net_Value2\n" +
                "when Net_Value_Type_ID2 = 2 then Rate2 + ((Rate2 * Net_Value2) / 100.0)\n" +
                "when Net_Value_Type_ID2 = 3 then Net_Value2\n" +
                "else Rate2\n" +
                "end,\n" +
                "Rate3 = \n" +
                "case when Net_Value_Type_ID3 = 1 then Rate3 + Net_Value3\n" +
                "when Net_Value_Type_ID3 = 2 then Rate3 + ((Rate3 * Net_Value3) / 100.0)\n" +
                "when Net_Value_Type_ID3 = 3 then Net_Value3\n" +
                "else Rate3\n" +
                "end\n" +
                "FROM testTableName_1\n" +
                "inner join #testTableName_RateAdjustment ra\n" +
                "on testTableName_1.Rate_Qualified_id=ra.Rate_Qualified_ID\n" +
                "and testTableName_1.Arrival_DT = ra.Arrival_DT\n" +
                "where ra.AdjustmentType='YieldableValue';\n" +
                "IF OBJECT_ID('tempdb..#testTableName_2') IS NOT NULL DROP TABLE #testTableName_2;\n" +
                "CREATE TABLE #testTableName_2(\n" +
                "\t[Property_ID] [int] NOT NULL,\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[FPLOS] [varchar](21) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_2] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tProperty_ID,Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA') IS NOT NULL DROP TABLE #testTableName_LRA;\n" +
                "CREATE TABLE #testTableName_LRA(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[Rate_Qualified_id] [int] NOT NULL,\n" +
                "\t[Rate] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate1] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate2] [numeric](19,5) NOT NULL,\n" +
                "\t[Rate3] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv1] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv2] [numeric](19,5) NOT NULL,\n" +
                "\t[lrv3] [numeric](19,5) NOT NULL,\n" +
                "\t[LOS] [int] NULL,\n" +
                "\t[Decision_Reason_Type_ID] [int]\n" +
                ")\n" +
                ";\n" +
                "insert into #testTableName_LRA\n" +
                "select testTableName_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID FROM testTableName_1 left join \n" +
                "(\n" +
                "\tselect Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:specialSrpId Rate_Qualified_ID,Decision_Reason_Type_ID from\n" +
                "\t(\n" +
                "\t\tselect * from decision_bar_output \n" +
                "\t\twhere Rate_Unqualified_ID=:lv0UnqualifiedId and Accom_Class_ID=:masterClassId and Decision_Reason_Type_ID=6\n" +
                "\t) sub_optimal_LV0\n" +
                "\tinner join\n" +
                "\t(\n" +
                "\t\tselect distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n" +
                "\t\tfrom Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n" +
                "\t\twhere AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:masterClassId and AT.Property_ID=:propertyId\n" +
                "\t) MASTER_AC_AT\n" +
                "\ton sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n" +
                ") LRA_IMPACT on testTableName_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=testTableName_1.Accom_Type_ID\n" +
                "and testTableName_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA_1') IS NOT NULL DROP TABLE #testTableName_LRA_1;\n" +
                "CREATE TABLE #testTableName_LRA_1(\n" +
                "\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Accom_Type_ID] [int] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                "\t,[Rate] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate1] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate2] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate3] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv1] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv2] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv3] [numeric](19,5) NOT NULL\n" +
                "\t,[ReasonType_los1] [int]\n" +
                "\t,[ReasonType_los2] [int]\n" +
                "\t,[ReasonType_los3] [int]\n" +
                ")\n" +
                ";\n" +
                "insert into #testTableName_LRA_1\n" +
                " select Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                " ,Rate1\n" +
                " ,Rate2\n" +
                " ,Rate3\n" +
                " ,lrv1\n" +
                " ,lrv2\n" +
                " ,lrv3\n" +
                " ,max([1])  as reasonType_los1\n" +
                " ,max([2])  as reasonType_los2\n" +
                " ,max([3])  as reasonType_los3\n" +
                " from\n" +
                " (\n" +
                "\t select :propertyId propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                "\t ,Rate1\n" +
                "\t ,Rate2\n" +
                "\t ,Rate3\n" +
                "\t ,lrv1\n" +
                "\t ,lrv2\n" +
                "\t ,lrv3\n" +
                "\t ,(case los when 1 then  Decision_Reason_Type_ID end ) as [1]\n" +
                "\t ,(case los when 2 then  Decision_Reason_Type_ID end ) as [2]\n" +
                "\t ,(case los when 3 then  Decision_Reason_Type_ID end ) as [3]\n" +
                "\t from #testTableName_LRA\n" +
                ") as x group by propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n" +
                ",Rate1\n" +
                ",Rate2\n" +
                ",Rate3\n" +
                ",lrv1\n" +
                ",lrv2\n" +
                ",lrv3\n" +
                "insert into #testTableName_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate1+Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los3=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate1+Rate2+Rate3>=lrv3 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #testTableName_LRA_1 AS LRA_1 ;\n" +
                "insert into Decision_Qualified_FPLOS (Decision_ID,Property_ID,Accom_Type_ID,Rate_qualified_ID,Arrival_DT,FPLOS)\n" +
                "select :decisionId,:propertyId\n" +
                ",a.Accom_Type_ID\n" +
                ",a.Rate_qualified_ID,a.Arrival_DT,a.FPLOS \n" +
                "from #testTableName_2 a left join Decision_Qualified_FPLOS b\n" +
                "on\n" +
                "a.Accom_Type_ID=b.Accom_Type_ID\n" +
                "and\n" +
                "a.Arrival_DT=b.Arrival_DT\n" +
                "and\n" +
                "a.Rate_qualified_ID=b.Rate_qualified_ID\n" +
                "where \n" +
                "b.FPLOS is null;\n" +
                "update Decision_Qualified_FPLOS \n" +
                "set Decision_ID=:decisionId\n" +
                ",fplos = a.FPLOS\n" +
                "from #testTableName_2 a inner join Decision_Qualified_FPLOS b\n" +
                "on \n" +
                "a.Accom_Type_ID=b.Accom_Type_ID and \n" +
                "a.Arrival_DT=b.Arrival_DT and\n" +
                "a.Rate_qualified_ID=b.Rate_qualified_ID and\n" +
                "a.Property_ID=b.Property_ID\n" +
                "where a.FPLOS <> b.FPLOS\n" +
                ";\n" +
                "delete Decision_Qualified_FPLOS \n" +
                "from #testTableName_2 a right join Decision_Qualified_FPLOS b\n" +
                "on \n" +
                "a.Property_ID=b.Property_ID\n" +
                "and a.Accom_Type_ID=b.Accom_Type_ID\n" +
                "and\n" +
                "a.Arrival_DT=b.Arrival_DT\n" +
                "and\n" +
                "a.Rate_Qualified_ID=b.Rate_Qualified_ID\n" +
                "\n" +
                "where b.Property_ID=:propertyId\n" +
                "and\n" +
                "b.Arrival_DT between :startDate and DATEADD(DAY,-2,:endDate)\n" +
                "and\n" +
                "a.FPLOS is null;\n" +
                "insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n" +
                "select Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS from Decision_Qualified_FPLOS dqf \n" +
                "where property_id=:propertyId\n" +
                "and\n" +
                "decision_id=:decisionId;\n" +
                "IF OBJECT_ID('tempdb..#testTableName') IS NOT NULL DROP TABLE #testTableName;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_1a') IS NOT NULL DROP TABLE #testTableName_1a;\n" +
                "IF OBJECT_ID('testTableName_1') IS NOT NULL DROP TABLE testTableName_1;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_2') IS NOT NULL DROP TABLE #testTableName_2;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA') IS NOT NULL DROP TABLE #testTableName_LRA;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_LRA_1') IS NOT NULL DROP TABLE #testTableName_LRA_1;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_bar_value') IS NOT NULL DROP TABLE #testTableName_bar_value;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustment') IS NOT NULL DROP TABLE #testTableName_RateAdjustment;\n" +
                "IF OBJECT_ID('tempdb..#testTableName_RateAdjustmentType') IS NOT NULL DROP TABLE #testTableName_RateAdjustmentType;\n";
    }

    private DecisionQualifiedFPLOSBatchUpdaterBean createBeanWithDerivedRatesAndBarByLos() {
        return new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true)
                .setConnection(connection)
                .setPropertyId(propertyId)
                .setMaxLOS(maxLOS_7)
                .setSrpFplosAtTotalLevel(isSrpFplosAtTotalLevel)
                .setLraEnabledValue(lraEnabled)
                .setContinuousPricingEnabled(false)
                .setDerivedRatePlanEnabled(true)
                .setBarByLos(true)
                .setDerivedRatesFPLOSCorrectionEnabled(true)
                .setBarMaxLos(maxLOS_7)
                .setRestrictHighestBarEnabled(false)
                .setServicingCostEnabledAndConfigured(isServicingCostByLosEnabledAndConfigured);
    }

    @Test
    public void verifyQueryOfupdateFromCTETableWithLOS10() {
        String expected = getUpdateFromCTEQeury();
        StringBuilder stringBuilder = new StringBuilder();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setBarMaxLos(7));
        decisionQualifiedFPLOSBatchUpdater.updateFromCTETable(10, stringBuilder);
        assertEquals(expected, stringBuilder.toString());
    }

    @Test
    public void test_AllTogglesTrue() {
        decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setFirstChunk(true).setConnection(connection).setPropertyId(propertyId)
                .setMaxLOS(maxLOS_7).setSrpFplosAtTotalLevel(true)
                .setLraEnabledValue(lraEnabled).setContinuousPricingEnabled(true)
                .setDerivedRatePlanEnabled(true).setBarByLos(true)
                .setBarMaxLos(barMaxLos).setRestrictHighestBarEnabled(true)
                .setServicingCostEnabledAndConfigured(true);

        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP("temp_qualified_fplos_pid_11022", 8);
        assertThat(query, not(containsString("[LOS] [int] NOT NULL")));
    }

    @Test
    public void verifyQueryForQualifiedLosIs10ButBarMaxLosIs7() {
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setBarMaxLos(maxLOS_7));
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableForDerivedRatesAndRateAdjustments(TEST_TABLE_NAME, 10);
        int maxLosCount = getCountOf("and LOS =7", actual);
        assertEquals(8, maxLosCount);
        assertThat(actual, not(containsString("and LOS =8")));
        assertThat(actual, not(containsString("and LOS =9")));
        assertThat(actual, not(containsString("and LOS =10")));
    }

    @Test
    public void verifyQueryForSRPAtTotalLevel() {
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                .setBarMaxLos(7)
                .setDerivedRatesFPLOSCorrectionEnabled(false)
                .setBarByLos(true)
                .setDerivedRatePlanEnabled(true));
        String actual = decisionQualifiedFPLOSBatchUpdater.generateQueriesFplosBySRP(TEST_TABLE_NAME, maxLOS_7);
        assertThat(actual, not(containsString("[LOS] [int] NOT NULL")));
    }

    @Test
    public void shouldGenerateAgileRateFplosQueriesWhenSrpFplosAtTotalIsTrueAndGenerateAgileProductQualifiedFPLOSToggleIsTrue() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean()
                        .setBarMaxLos(maxLOS_7)
                        .setBarByLos(false)
                        .setSrpFplosAtTotalLevel(true)
                        .setLraEnabledValue(1)
                        .setContinuousPricingEnabled(true)
                        .setDerivedRatePlanEnabled(true)
                        .setRestrictHighestBarEnabled(true)
                        .setDerivedRatesFPLOSCorrectionEnabled(true)
                        .setGenerateAgileProductQualifiedFPLOS(true))
        );
        AgileProductQualifiedFPLOSQueryBuilder agileProductQualifiedFPLOSQueryBuilder = mock(AgileProductQualifiedFPLOSQueryBuilder.class);
        inject(spy, "agileProductQualifiedFPLOSQueryBuilder", agileProductQualifiedFPLOSQueryBuilder);
        spy.generateQueriesFplosBySRP(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, times(1)).createEffectiveLRVTable(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, times(1)).createEffectiveLRVTable(TEST_TABLE_NAME + "_lv0", maxLOS_7);
        verify(spy, times(1)).populateEffectiveLRVTable(TEST_TABLE_NAME, maxLOS_7, true);
        verify(spy, times(1)).populateEffectiveLRVTable(TEST_TABLE_NAME + "_lv0", maxLOS_7, false);
        verify(agileProductQualifiedFPLOSQueryBuilder).buildQueriesToGenerateFPLOSForSrpFPLOSAtTotalLevel(TEST_TABLE_NAME, maxLOS_7);
        verify(agileProductQualifiedFPLOSQueryBuilder).buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSTempTableForSRPFPLOSAtTotalLevel(TEST_TABLE_NAME);
        verify(spy, times(1)).populateQualifiedFPLOSTable(TEST_TABLE_NAME, true, false);
        verify(spy, times(1)).populateQualifiedFPLOSTable(TEST_TABLE_NAME + "_lv0", true, true);
        verify(spy, times(1)).updateQualifiedFPLOSTable(TEST_TABLE_NAME, true, false);
        verify(spy, times(1)).updateQualifiedFPLOSTable(TEST_TABLE_NAME + "_lv0", true, true);
    }

    @Test
    public void shouldGenerateAgileRateFplosQueriesWhenSrpFplosAtTotalIsTrueAndGenerateAgileProductQualifiedFPLOSToggleIsFalse() {
        DecisionQualifiedFPLOSBatchUpdater spy = spy(new DecisionQualifiedFPLOSBatchUpdater(createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setBarMaxLos(maxLOS_7).setBarByLos(false).setSrpFplosAtTotalLevel(false).setLraEnabledValue(1).setContinuousPricingEnabled(true).setDerivedRatePlanEnabled(true).setRestrictHighestBarEnabled(true).setDerivedRatesFPLOSCorrectionEnabled(true).setGenerateAgileProductQualifiedFPLOS(false)));
        spy.generateQueriesFplosBySRP(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, times(1)).createEffectiveLRVTable(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, times(1)).createEffectiveLRVTable(TEST_TABLE_NAME + "_lv0", maxLOS_7);
        verify(spy, times(1)).populateEffectiveLRVTable(TEST_TABLE_NAME, maxLOS_7, true);
        verify(spy, never()).populateEffectiveLRVTable(TEST_TABLE_NAME, maxLOS_7, false);
        verify(spy, times(1)).populateEffectiveLRVTable(TEST_TABLE_NAME + "_lv0", maxLOS_7, false);
        verify(spy, never()).generateAgileProductQualifiedFPLOS(TEST_TABLE_NAME, maxLOS_7);
        verify(spy, never()).buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSForRoomTypeLevel("temp_qualified_agile_fplos_pid_" + propertyId);
        verify(spy, times(1)).populateQualifiedFPLOSTable(TEST_TABLE_NAME, true, false);
        verify(spy, times(1)).populateQualifiedFPLOSTable(TEST_TABLE_NAME + "_lv0", true, true);
        verify(spy, times(1)).updateQualifiedFPLOSTable(TEST_TABLE_NAME, true, false);
        verify(spy, times(1)).updateQualifiedFPLOSTable(TEST_TABLE_NAME + "_lv0", true, true);
    }

    @Test
    public void shouldSkipRestrictionRatesAssociatedWithAgileProductForNonMasterQueryInFPLOSAtTotalLevel() {
        DecisionQualifiedFPLOSBatchUpdaterBean decisionQualifiedFPLOSBatchUpdaterBean =
                createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean.setGenerateAgileProductQualifiedFPLOS(true);
        DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(
                decisionQualifiedFPLOSBatchUpdaterBean
        );
        String resultQuery = decisionQualifiedFPLOSBatchUpdater.populateIntermediateNonMasterRateCodeTable(TEMP_TABLE_NAME);
        String expectedQuery = " insert into #" + TEMP_TABLE_NAME + "a_non_master\n" +
                "select arrival_dt as Arrival_DT,rate_q.Accom_Type_ID,rate_q.Rate_Qualified_ID,Rate=case\n" +
                " when DATEPART(weekday,arrival_dt) = 1 then Sunday\n" +
                " when DATEPART(weekday,arrival_dt) = 2 then Monday\n" +
                " when DATEPART(weekday,arrival_dt) = 3 then Tuesday\n" +
                " when DATEPART(weekday,arrival_dt) = 4 then Wednesday\n" +
                " when DATEPART(weekday,arrival_dt) = 5 then Thursday\n" +
                " when DATEPART(weekday,arrival_dt) = 6 then Friday\n" +
                " when DATEPART(weekday,arrival_dt) = 7 then Saturday\n" +
                "end\n" +
                "from\n" +
                "( \n" +
                "select cast (calendar_date as date) arrival_dt from calendar_dim where calendar_date between :startDate and :endDate \n" +
                ") dt left join \n" +
                "( \n" +
                "select rqd.* from Rate_Qualified rq inner join Rate_Qualified_Details rqd on rq.Rate_Qualified_ID=rqd.Rate_Qualified_ID \n" +
                "where rq.Property_ID=:propertyId and rq.Rate_Qualified_ID!=:specialSrpId  and rq.Status_ID=1 and rq.Yieldable=1 \n" +
                ") rate_q on dt.arrival_dt between rate_q.Start_Date_DT and rate_q.End_Date_DT \n" +
                "inner join  \n" +
                "( \n" +
                "select AT.Accom_Type_ID from Accom_Type AT inner join Accom_Class AC on AT.Accom_Class_ID=AC.Accom_Class_ID  \n" +
                "where AT.System_Default=0 and AC.System_Default=0 and AT.Status_ID=1 and AC.Status_ID=1 \n" +
                "and AC.Master_Class!=1 and AT.Property_ID=:propertyId \n" +
                "and (At.Accom_Type_Capacity>0   )\n" +
                ") AT on rate_q.Accom_Type_ID=AT.Accom_Type_ID\n" +
                " and not exists( select agileProd.Rate_Qualified_ID from " +
                "Agile_Product_Restriction_Association agileProd " +
                "where agileProd.Rate_Qualified_ID = rate_q.Rate_Qualified_ID); \n";
        resultQuery = decisionQualifiedFPLOSBatchUpdater.updateQueryForHospitalityRoomsClause(resultQuery);
        assertEquals(expectedQuery, resultQuery);
    }

    @Test
    public void testCreateEffectiveRatesTableByLOS_SRPTrueIsLV0False() {
        String expected = createEffectiveRatesByLOSTableQuery();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTableByLOS(tableName, maxLOS_7, true, false);
        assertThat(actual, is(expected));
    }

    @Test
    public void verifyQueryForpopulateEffectiveRatesTableBasedOnDowByLOS() {
        String expected = createPopulateQueryForEffectiveRatesByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateEffectiveRatesTableBasedOnDowByLOS(TEST_TABLE_NAME, maxLOS_7, true, false);
        assertThat(actual, is(expected));
    }

    @Test
    public void verifyQueryForUpdateTempTableWithBarAndDerivedRates() {
        String expected = createQueryForUpdateTempTableWithBarAndDerivedRates();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableWithBarAndDerivedRates(TEST_TABLE_NAME, "_1", maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void verifyQueryForCreateTempWeightedRateTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForcreateTempWeightedRateTableByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempWeightedRateTable(TEST_TABLE_NAME, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempWeightedRateTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForPopulateTempWeightedTateTable();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempWeightedRateTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateTempWeightedRateFinalTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createTempWeightedRateQeuryForByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createTempWeightedRateFinalTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempWeightedRateFinalTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createPopulateTempWeightedRateByLOSQeury();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempWeightedRateFinalTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateNonMasterRateCodeTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQeuryForCreateNonMasterRateCodeTable();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createNonMasterRateCodeTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateEffectiveRatesTableNonMaster_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQeuryForEffectiveNonMasterRateCodeTable();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createEffectiveRatesTableNonMaster(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateEffectiveRatesTableNonMasterBasedOnDow_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQeuryForEffectiveNonMasterDowRates();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateEffectiveRatesTableNonMasterBasedOnDow(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateTempTableForDecisionIfDerivedRateEnabled_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQeuryForUpdateTempTableForDecisionIfDerivedRateEnabled();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableForDecisionIfDerivedRateEnabled(tableName, "_non_master_1", maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testUpdateTempTableForDecisionIfDerivedRateEnabled() {
        String expected = createQeuryForDerivedRatesWithRateOfDayON();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean().setDerivedRatePlanEnabled(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.updateTempTableForDecisionIfDerivedRateEnabled(tableName, "_non_master_1", maxLOS_3);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateNonMasterRateCodeTableForDOW_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForPopulateNonMasterRateCodes();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateNonMasterRateCodeTableForDOW(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateNonMasterRateCodeTable_2_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForNonMasterRateCode2();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createNonMasterRateCodeTable_2(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testCreateCumulativeWeightedRateTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForCreateCumulativeWeightedRateTable();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.createCumulativeWeightedRateTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateCumulativeWeightedRateTable_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForPopulateCumulativeWeightedRateTable();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateCumulativeWeightedRateTable(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempFPLOSTableForAtSRPNonLV0() {
        String expected = createQueryForPopulateFPLOSTableForAtSRPNonLV0();
        decisionQualifiedFPLOSBatchUpdaterBean = createWithDefaultValuesDecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTableNonLV0(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void testPopulateTempFPLOSTableForAtSRPNonLV0_DerivedRatesAndBarByLOSAreTrue() {
        String expected = createQueryForPopulateFPLOSTableForAtSRPNonLV0ByLOS();
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String actual = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTableNonLV0(tableName, maxLOS_7);
        assertThat(actual, is(expected));
    }

    @Test
    public void shouldPopulateTempFPLOSTableNonLV0WhenServicingCostEnabled() {
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setLimitTotalSRPRatesEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        decisionQualifiedFPLOSBatchUpdater.servicingCostQueryBuilder.servicingCostTableTempTableAtTotal = "temp_qualified_fplos_pid_5_ServicingCost_AtTotal";
        String resultQuery = decisionQualifiedFPLOSBatchUpdater.populateTempFPLOSTableNonLV0("temp_qualified_fplos_pid_5", 4);
        String expectedQuery = "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,Arrival_DT\n" +
                ",wrateFinal.Rate_Qualified_id, \n" +
                "case when wRate1 - ISNULL(servicingCost.servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end +\n" +
                "case when wRate1+wRate2 - ISNULL(servicingCost.servicingCost_los2, 0) >=lrv2 then 'Y' else 'N' end + \n" +
                "case when wRate1+wRate2+wRate3 - ISNULL(servicingCost.servicingCost_los3, 0) >=lrv3 then 'Y' else 'N' end + \n" +
                "case when :extendedStay=1 and wrateFinal.Rate_Qualified_id=isnull(:specialSrpId,-1) then 'Y' else\n" +
                "case when wRate1+wRate2+wRate3+wRate4 - ISNULL(servicingCost.servicingCost_los4, 0) >=lrv4 then 'Y' else 'N' end end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_1_wrate_final as wrateFinal \n" +
                " left join #temp_qualified_fplos_pid_5_ServicingCost_AtTotal as servicingCost \n" +
                " on wrateFinal.Rate_Qualified_id = servicingCost.Rate_Qualified_id ;\n";
        assertEquals(expectedQuery, resultQuery);
    }

    @Test
    void verifyQueryToPopulateBarRatesForRateOfTheDay(){
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setLimitTotalSRPRatesEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setUseOptimizedFplosQueryForBarRates(false);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.populateBarRatesForBarByDay(2, "temp_qualified_fplos_pid_5");
        String expectedQuery = "insert into #temp_qualified_fplos_pid_5_bar_value\n" +
                " SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,\n" +
                " CASE (DATEPART(WEEKDAY, D.Arrival_DT))\n" +
                " WHEN 1 THEN (rud.Sunday)\n" +
                " WHEN 2 THEN (rud.Monday)\n" +
                " WHEN 3 THEN (rud.Tuesday)\n" +
                " WHEN 4 THEN (rud.Wednesday)\n" +
                " WHEN 5 THEN (rud.Thursday)\n" +
                " WHEN 6 THEN (rud.Friday)\n" +
                " WHEN 7 THEN (rud.Saturday) END AS BAR_Rate1\n" +
                " ,CASE DATEPART(WEEKDAY, D2.Arrival_DT)\n" +
                " WHEN 1 THEN (rud2.Sunday)\n" +
                " WHEN 2 THEN (rud2.Monday)\n" +
                " WHEN 3 THEN (rud2.Tuesday)\n" +
                " WHEN 4 THEN (rud2.Wednesday)\n" +
                " WHEN 5 THEN (rud2.Thursday)\n" +
                " WHEN 6 THEN (rud2.Friday)\n" +
                " WHEN 7 THEN (rud2.Saturday) END AS BAR_Rate2\n" +
                " FROM Decision_Bar_Output as D\n" +
                " INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n" +
                " INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                " INNER JOIN Decision_Bar_Output as D2 ON D.Accom_Class_ID = D2.Accom_Class_ID and D2.Arrival_DT = dateadd(day, 1, D.Arrival_DT) and D.los = D2.los\n" +
                " INNER JOIN Rate_Unqualified_Details AS rud2 ON rud.Accom_Type_ID = rud2.Accom_Type_ID AND D2.Rate_Unqualified_ID = rud2.Rate_Unqualified_ID and dateadd(day,1,D.Arrival_DT) BETWEEN rud2.Start_Date_DT AND rud2.End_Date_DT\n" +
                " WHERE D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                "AND D.Arrival_DT BETWEEN :startDate AND DATEADD(DAY,2,:endDate) AND D.los = -1\n";
        assertEquals(expectedQuery, query);
    }

    @Test
    void verifyQueryToPopulateBarRatesForRateOfTheDayByOptimizingQuery(){
        decisionQualifiedFPLOSBatchUpdaterBean = createBeanWithDerivedRatesAndBarByLos();
        decisionQualifiedFPLOSBatchUpdaterBean.setBarByLos(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setLimitTotalSRPRatesEnabled(false);
        decisionQualifiedFPLOSBatchUpdaterBean.setServicingCostEnabledAndConfigured(true);
        decisionQualifiedFPLOSBatchUpdaterBean.setUseOptimizedFplosQueryForBarRates(true);
        decisionQualifiedFPLOSBatchUpdater = new DecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
        String query = decisionQualifiedFPLOSBatchUpdater.populateBarRatesForBarByDay(2, "temp_qualified_fplos_pid_5");
        String expectedQuery = "insert into #temp_qualified_fplos_pid_5_bar_value\n" +
                " SELECT * from \n" +
                " (\n" +
                " SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,\n" +
                " CASE (DATEPART(WEEKDAY, D.Arrival_DT))\n" +
                " WHEN 1 THEN (rud.Sunday)\n" +
                " WHEN 2 THEN (rud.Monday)\n" +
                " WHEN 3 THEN (rud.Tuesday)\n" +
                " WHEN 4 THEN (rud.Wednesday)\n" +
                " WHEN 5 THEN (rud.Thursday)\n" +
                " WHEN 6 THEN (rud.Friday)\n" +
                " WHEN 7 THEN (rud.Saturday) END AS BAR_Rate1\n" +
                " ,CASE DATEPART(WEEKDAY, DATEADD(DAY, 1, D.Arrival_DT))\n" +
                " WHEN 1 THEN (LEAD(rud.Sunday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 2 THEN (LEAD(rud.Monday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 3 THEN (LEAD(rud.Tuesday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 4 THEN (LEAD(rud.Wednesday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 5 THEN (LEAD(rud.Thursday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 6 THEN (LEAD(rud.Friday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT))\n" +
                " WHEN 7 THEN (LEAD(rud.Saturday, 1) OVER(PARTITION BY at.Accom_Type_ID ORDER BY D.Arrival_DT)) END AS BAR_Rate2\n" +
                " FROM Decision_Bar_Output as D\n" +
                " INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n" +
                " INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                " WHERE D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                "AND D.Arrival_DT BETWEEN :startDate AND DATEADD(DAY,4,:endDate) AND D.los = -1\n" +
                ") as t\n" +
                " WHERE t.Arrival_DT BETWEEN :startDate AND DATEADD(DAY,2,:endDate)\n" +
                " AND BAR_Rate2 IS NOT NULL \n";
        assertEquals(expectedQuery, query);
    }

    private String createQueryForPopulateFPLOSTableForAtSRPNonLV0ByLOS() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,Arrival_DT\n" +
                ",wrateFinal.Rate_Qualified_id, \n" +
                "case when cumulative_Rate1>=lrv1 then 'Y' else 'N' end +\n" +
                "case when cumulative_Rate2>=lrv2 then 'Y' else 'N' end + \n" +
                "case when cumulative_Rate3>=lrv3 then 'Y' else 'N' end + \n" +
                "case when cumulative_Rate4>=lrv4 then 'Y' else 'N' end + \n" +
                "case when cumulative_Rate5>=lrv5 then 'Y' else 'N' end + \n" +
                "case when cumulative_Rate6>=lrv6 then 'Y' else 'N' end + \n" +
                "case when :extendedStay=1 and wrateFinal.Rate_Qualified_id=isnull(:specialSrpId,-1) then 'Y' else\n" +
                "case when cumulative_Rate7>=lrv7 then 'Y' else 'N' end end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_wrate_cumulative as wrateFinal;\n";
    }

    private String createQueryForPopulateFPLOSTableForAtSRPNonLV0() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,Arrival_DT\n" +
                ",wrateFinal.Rate_Qualified_id, \n" +
                "case when wRate1>=lrv1 then 'Y' else 'N' end +\n" +
                "case when wRate1+wRate2>=lrv2 then 'Y' else 'N' end + \n" +
                "case when wRate1+wRate2+wRate3>=lrv3 then 'Y' else 'N' end + \n" +
                "case when wRate1+wRate2+wRate3+wRate4>=lrv4 then 'Y' else 'N' end + \n" +
                "case when wRate1+wRate2+wRate3+wRate4+wRate5>=lrv5 then 'Y' else 'N' end + \n" +
                "case when wRate1+wRate2+wRate3+wRate4+wRate5+wRate6>=lrv6 then 'Y' else 'N' end + \n" +
                "case when :extendedStay=1 and wrateFinal.Rate_Qualified_id=isnull(:specialSrpId,-1) then 'Y' else\n" +
                "case when wRate1+wRate2+wRate3+wRate4+wRate5+wRate6+wRate7>=lrv7 then 'Y' else 'N' end end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_1_wrate_final as wrateFinal;\n";
    }

    private String createQueryForPopulateCumulativeWeightedRateTable() {
        return "insert into #temp_qualified_fplos_pid_5_wrate_cumulative\n" +
                "SELECT Arrival_DT, Rate_Qualified_id\n" +
                ", [1] AS cumulative_Rate1, [2] AS cumulative_Rate2, [3] AS cumulative_Rate3, [4] AS cumulative_Rate4, [5] AS cumulative_Rate5, [6] AS cumulative_Rate6, [7] AS cumulative_Rate7\n" +
                ", lrv1, lrv2, lrv3, lrv4, lrv5, lrv6, lrv7\n" +
                " FROM ( select Arrival_DT,Rate_Qualified_id,LOS \n" +
                " ,case when LOS = 1 then wrate1\n" +
                " when LOS = 2 then wrate1 + wrate2\n" +
                " when LOS = 3 then wrate1 + wrate2 + wrate3\n" +
                " when LOS = 4 then wrate1 + wrate2 + wrate3 + wrate4\n" +
                " when LOS = 5 then wrate1 + wrate2 + wrate3 + wrate4 + wrate5\n" +
                " when LOS = 6 then wrate1 + wrate2 + wrate3 + wrate4 + wrate5 + wrate6\n" +
                " when LOS = 7 then wrate1 + wrate2 + wrate3 + wrate4 + wrate5 + wrate6 + wrate7\n" +
                "end as cumulative_Rate , lrv1, lrv2, lrv3, lrv4, lrv5, lrv6, lrv7\n" +
                "from #temp_qualified_fplos_pid_5_1_wrate_final ) as A \n" +
                " PIVOT ( MAX(cumulative_Rate) FOR los in ([1] ,[2] ,[3] ,[4] ,[5] ,[6] ,[7])) as piv\n" +
                "where [1] is not null and [2] is not null and [3] is not null and [4] is not null and [5] is not null and [6] is not null and [7] is not null;\n";
    }

    private String createQueryForCreateCumulativeWeightedRateTable() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_wrate_cumulative') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_wrate_cumulative;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_wrate_cumulative(\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                "\t,[cumulative_Rate1] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate2] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate3] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate4] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate5] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate6] [numeric](19,5) NOT NULL\n" +
                "\t,[cumulative_Rate7] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv1] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv2] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv3] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv4] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv5] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv6] [numeric](19,5) NOT NULL\n" +
                "\t,[lrv7] [numeric](19,5) NOT NULL\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_wrate_cumulative] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Rate_Qualified_id ASC\n" +
                ")\n" +
                ");\n";
    }

    private String createQueryForNonMasterRateCode2() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_non_master_2') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_non_master_2;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_non_master_2(\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                "\t,[LOS] [int] NOT NULL\n" +
                "\t,[Rate1] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate2] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate3] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate4] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate5] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate6] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate7] [numeric](19,5) NOT NULL\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_non_master_2] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Rate_Qualified_id,LOS ASC\n" +
                ")\n" +
                ");\n";
    }


    private String createQueryForPopulateNonMasterRateCodes() {
        return " insert into #temp_qualified_fplos_pid_5_non_master \n" +
                " select t1.Arrival_DT, t1.Rate_Qualified_id\n" +
                ", LOS\n" +
                ", max(Rate1) as Rate1\n" +
                ", max(Rate2) as Rate2\n" +
                ", max(Rate3) as Rate3\n" +
                ", max(Rate4) as Rate4\n" +
                ", max(Rate5) as Rate5\n" +
                ", max(Rate6) as Rate6\n" +
                ", max(Rate7) as Rate7\n" +
                "from #temp_qualified_fplos_pid_5_non_master_1 t1\n" +
                "group by t1.arrival_dt, t1.Rate_Qualified_ID ,LOS;\n";
    }

    private String createQeuryForDerivedRatesWithRateOfDayON() {
        return "delete #temp_qualified_fplos_pid_5_non_master_1\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1\n" +
                " inner join Rate_Qualified on #temp_qualified_fplos_pid_5_non_master_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #temp_qualified_fplos_pid_5_bar_value on #temp_qualified_fplos_pid_5_non_master_1.arrival_dt = #temp_qualified_fplos_pid_5_bar_value.arrival_dt and #temp_qualified_fplos_pid_5_non_master_1.Accom_Type_ID = #temp_qualified_fplos_pid_5_bar_value.Accom_Type_ID\n" +
                " where ( #temp_qualified_fplos_pid_5_bar_value.BAR_Rate1 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate2 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate3 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)\n" +
                "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1 temp_fplos\n" +
                " INNER JOIN #temp_qualified_fplos_pid_5_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id;\n";
    }

    private String createQeuryForUpdateTempTableForDecisionIfDerivedRateEnabled() {
        return "delete #temp_qualified_fplos_pid_5_non_master_1\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1\n" +
                " inner join Rate_Qualified on #temp_qualified_fplos_pid_5_non_master_1.Rate_Qualified_id = Rate_Qualified.Rate_Qualified_id\n" +
                " left join #temp_qualified_fplos_pid_5_bar_value on #temp_qualified_fplos_pid_5_non_master_1.arrival_dt = #temp_qualified_fplos_pid_5_bar_value.arrival_dt and #temp_qualified_fplos_pid_5_non_master_1.Accom_Type_ID = #temp_qualified_fplos_pid_5_bar_value.Accom_Type_ID\n" +
                " where ( #temp_qualified_fplos_pid_5_bar_value.BAR_Rate1 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate2 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate3 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate4 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate5 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate6 is null\n" +
                " or #temp_qualified_fplos_pid_5_bar_value.BAR_Rate7 is null\n" +
                ") and ( Rate_Qualified.Rate_Qualified_type_ID=1 or Rate_Qualified.Rate_Qualified_type_ID=2)\n" +
                "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end,\n" +
                " temp_fplos.rate4 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate4 + temp_fplos.Rate4\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate4 + ((temp_bar.BAR_Rate4 * temp_fplos.Rate4)/100)\n" +
                " else temp_fplos.rate4 end,\n" +
                " temp_fplos.rate5 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate5 + temp_fplos.Rate5\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate5 + ((temp_bar.BAR_Rate5 * temp_fplos.Rate5)/100)\n" +
                " else temp_fplos.rate5 end,\n" +
                " temp_fplos.rate6 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate6 + temp_fplos.Rate6\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate6 + ((temp_bar.BAR_Rate6 * temp_fplos.Rate6)/100)\n" +
                " else temp_fplos.rate6 end,\n" +
                " temp_fplos.rate7 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate7 + temp_fplos.Rate7\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate7 + ((temp_bar.BAR_Rate7 * temp_fplos.Rate7)/100)\n" +
                " else temp_fplos.rate7 end\n" +
                " from #temp_qualified_fplos_pid_5_non_master_1 temp_fplos\n" +
                " INNER JOIN #temp_qualified_fplos_pid_5_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID and temp_fplos.los = temp_bar.los \n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id;\n";
    }

    private String createQeuryForEffectiveNonMasterDowRates() {
        return "insert into #temp_qualified_fplos_pid_5_non_master_1\n" +
                " select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID\n" +
                ",LOS\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t4.Rate as Rate4\n" +
                ", t5.Rate as Rate5\n" +
                ", t6.Rate as Rate6\n" +
                ", t7.Rate as Rate7\n" +
                "from #temp_qualified_fplos_pid_5a_non_master t1 \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t4 on t4.Arrival_DT = dateadd(day,3,t1.Arrival_DT) and t1.Rate_Qualified_ID = t4.Rate_Qualified_id and t1.Accom_Type_ID = t4.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t5 on t5.Arrival_DT = dateadd(day,4,t1.Arrival_DT) and t1.Rate_Qualified_ID = t5.Rate_Qualified_id and t1.Accom_Type_ID = t5.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t6 on t6.Arrival_DT = dateadd(day,5,t1.Arrival_DT) and t1.Rate_Qualified_ID = t6.Rate_Qualified_id and t1.Accom_Type_ID = t6.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5a_non_master t7 on t7.Arrival_DT = dateadd(day,6,t1.Arrival_DT) and t1.Rate_Qualified_ID = t7.Rate_Qualified_id and t1.Accom_Type_ID = t7.Accom_Type_ID \n" +
                "inner join #temp_qualified_fplos_pid_5_bar_value temp_bar on temp_bar.Arrival_DT = t1.arrival_DT and temp_bar.Accom_Type_ID = t1.Accom_Type_ID;\n";
    }

    private String createQeuryForEffectiveNonMasterRateCodeTable() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_non_master_1') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_non_master_1;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_non_master_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[LOS] [int] NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[Rate4] [numeric](19,5) NOT NULL,\n" +
                "[Rate5] [numeric](19,5) NOT NULL,\n" +
                "[Rate6] [numeric](19,5) NOT NULL,\n" +
                "[Rate7] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_non_master_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id, LOS ASC\n" +
                ")\n" +
                ");\n";
    }

    private String createQeuryForCreateNonMasterRateCodeTable() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_non_master') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_non_master;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_non_master(\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                "\t,[LOS] [int] NOT NULL\n" +
                "\t,[Rate1] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate2] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate3] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate4] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate5] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate6] [numeric](19,5) NOT NULL\n" +
                "\t,[Rate7] [numeric](19,5) NOT NULL\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_non_master] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Rate_Qualified_id, LOS ASC\n" +
                ")\n" +
                ");\n";
    }


    private String createPopulateTempWeightedRateByLOSQeury() {
        return "insert into #temp_qualified_fplos_pid_5_1_wrate_final\n" +
                "select Arrival_DT,Rate_Qualified_id\n" +
                ",LOS\n" +
                "\t,wRate1= case(sum(remCap1)) when 0 then 0 else sum(Rate1*remCap1)/sum(remCap1) end\t\n" +
                "\t,wRate2= case(sum(remCap2)) when 0 then 0 else sum(Rate2*remCap2)/sum(remCap2) end\t\n" +
                "\t,wRate3= case(sum(remCap3)) when 0 then 0 else sum(Rate3*remCap3)/sum(remCap3) end\t\n" +
                "\t,wRate4= case(sum(remCap4)) when 0 then 0 else sum(Rate4*remCap4)/sum(remCap4) end\t\n" +
                "\t,wRate5= case(sum(remCap5)) when 0 then 0 else sum(Rate5*remCap5)/sum(remCap5) end\t\n" +
                "\t,wRate6= case(sum(remCap6)) when 0 then 0 else sum(Rate6*remCap6)/sum(remCap6) end\t\n" +
                "\t,wRate7= case(sum(remCap7)) when 0 then 0 else sum(Rate7*remCap7)/sum(remCap7) end\t\n" +
                "\t,avg(lrv1)\n" +
                "\t,avg(lrv2)\n" +
                "\t,avg(lrv3)\n" +
                "\t,avg(lrv4)\n" +
                "\t,avg(lrv5)\n" +
                "\t,avg(lrv6)\n" +
                "\t,avg(lrv7)\n" +
                " FROM temp_qualified_fplos_pid_5_1\n" +
                " group by Arrival_DT,Rate_Qualified_id\n" +
                ",LOS;\n";
    }

    private String createTempWeightedRateQeuryForByLOS() {
        return "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_1_wrate_final') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_1_wrate_final;\n" +
                "CREATE TABLE #temp_qualified_fplos_pid_5_1_wrate_final(\n" +
                "\t[Arrival_DT] [date] NOT NULL\n" +
                "\t,[Rate_Qualified_id] [int] NOT NULL\n" +
                ",[LOS] [int] NOT NULL\n" +
                ",[wRate1] [numeric](19,5) NOT NULL\n" +
                ",[wRate2] [numeric](19,5) NOT NULL\n" +
                ",[wRate3] [numeric](19,5) NOT NULL\n" +
                ",[wRate4] [numeric](19,5) NOT NULL\n" +
                ",[wRate5] [numeric](19,5) NOT NULL\n" +
                ",[wRate6] [numeric](19,5) NOT NULL\n" +
                ",[wRate7] [numeric](19,5) NOT NULL\n" +
                ",[lrv1] [numeric](19,5) NOT NULL\n" +
                ",[lrv2] [numeric](19,5) NOT NULL\n" +
                ",[lrv3] [numeric](19,5) NOT NULL\n" +
                ",[lrv4] [numeric](19,5) NOT NULL\n" +
                ",[lrv5] [numeric](19,5) NOT NULL\n" +
                ",[lrv6] [numeric](19,5) NOT NULL\n" +
                ",[lrv7] [numeric](19,5) NOT NULL\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_1_wrate_final] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Rate_Qualified_id, LOS ASC\n" +
                ")\n" +
                ");\n";
    }


    private String createQueryForPopulateTempWeightedTateTable() {
        return "insert into #temp_qualified_fplos_pid_5_1_wrate\n" +
                "select Arrival_DT,Accom_Type_ID,Rate_Qualified_id\n" +
                ",LOS\n" +
                "\t,remCap1\n" +
                "\t,remCap2\n" +
                "\t,remCap3\n" +
                "\t,remCap4\n" +
                "\t,remCap5\n" +
                "\t,remCap6\n" +
                "\t,remCap7\n" +
                "\t,Rate1*remCap1 as wrate1\n" +
                "\t,Rate2*remCap2 as wrate2\n" +
                "\t,Rate3*remCap3 as wrate3\n" +
                "\t,Rate4*remCap4 as wrate4\n" +
                "\t,Rate5*remCap5 as wrate5\n" +
                "\t,Rate6*remCap6 as wrate6\n" +
                "\t,Rate7*remCap7 as wrate7\n" +
                "\t,[lrv1]\n" +
                "\t,[lrv2]\n" +
                "\t,[lrv3]\n" +
                "\t,[lrv4]\n" +
                "\t,[lrv5]\n" +
                "\t,[lrv6]\n" +
                "\t,[lrv7]\n" +
                " FROM temp_qualified_fplos_pid_5_1\n";
    }

    private String createQueryForcreateTempWeightedRateTableByLOS() {
        return "IF OBJECT_ID('tempdb..#testTableName_1_wrate') IS NOT NULL DROP TABLE #testTableName_1_wrate;\n" +
                "CREATE TABLE #testTableName_1_wrate(\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[LOS] [int] NOT NULL,\n" +
                "[remCap1] [numeric] (19,5) NOT NULL,\n" +
                "[remCap2] [numeric] (19,5) NOT NULL,\n" +
                "[remCap3] [numeric] (19,5) NOT NULL,\n" +
                "[remCap4] [numeric] (19,5) NOT NULL,\n" +
                "[remCap5] [numeric] (19,5) NOT NULL,\n" +
                "[remCap6] [numeric] (19,5) NOT NULL,\n" +
                "[remCap7] [numeric] (19,5) NOT NULL,\n" +
                "[wRate1] [numeric](19,5) NOT NULL,\n" +
                "[wRate2] [numeric](19,5) NOT NULL,\n" +
                "[wRate3] [numeric](19,5) NOT NULL,\n" +
                "[wRate4] [numeric](19,5) NOT NULL,\n" +
                "[wRate5] [numeric](19,5) NOT NULL,\n" +
                "[wRate6] [numeric](19,5) NOT NULL,\n" +
                "[wRate7] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "[lrv4] [numeric](19,5) NOT NULL,\n" +
                "[lrv5] [numeric](19,5) NOT NULL,\n" +
                "[lrv6] [numeric](19,5) NOT NULL,\n" +
                "[lrv7] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_testTableName_1_wrate] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "\tArrival_DT,Accom_Type_ID,Rate_Qualified_id, LOS ASC\n" +
                ")\n" +
                ");\n";
    }


    private String createQueryForUpdateTempTableWithBarAndDerivedRates() {
        return "update temp_fplos set \n" +
                " temp_fplos.rate1 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate1 + temp_fplos.Rate1\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate1 + ((temp_bar.BAR_Rate1 * temp_fplos.Rate1)/100)\n" +
                " else temp_fplos.rate1 end,\n" +
                " temp_fplos.rate2 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate2 + temp_fplos.Rate2\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate2 + ((temp_bar.BAR_Rate2 * temp_fplos.Rate2)/100)\n" +
                " else temp_fplos.rate2 end,\n" +
                " temp_fplos.rate3 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate3 + temp_fplos.Rate3\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate3 + ((temp_bar.BAR_Rate3 * temp_fplos.Rate3)/100)\n" +
                " else temp_fplos.rate3 end,\n" +
                " temp_fplos.rate4 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate4 + temp_fplos.Rate4\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate4 + ((temp_bar.BAR_Rate4 * temp_fplos.Rate4)/100)\n" +
                " else temp_fplos.rate4 end,\n" +
                " temp_fplos.rate5 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate5 + temp_fplos.Rate5\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate5 + ((temp_bar.BAR_Rate5 * temp_fplos.Rate5)/100)\n" +
                " else temp_fplos.rate5 end,\n" +
                " temp_fplos.rate6 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate6 + temp_fplos.Rate6\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate6 + ((temp_bar.BAR_Rate6 * temp_fplos.Rate6)/100)\n" +
                " else temp_fplos.rate6 end,\n" +
                " temp_fplos.rate7 = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate7 + temp_fplos.Rate7\n" +
                " when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate7 + ((temp_bar.BAR_Rate7 * temp_fplos.Rate7)/100)\n" +
                " else temp_fplos.rate7 end\n" +
                " from testTableName_1 temp_fplos\n" +
                " INNER JOIN #testTableName_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID and temp_fplos.los = temp_bar.los \n" +
                " INNER JOIN Rate_Qualified rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id;\n";
    }

    private String createPopulateQueryForEffectiveRatesByLOS() {
        return "insert into testTableName_1\n" +
                " select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,temp_bar.LOS\n" +
                ", t1.Rate as Rate1\n" +
                ", t2.Rate as Rate2\n" +
                ", t3.Rate as Rate3\n" +
                ", t4.Rate as Rate4\n" +
                ", t5.Rate as Rate5\n" +
                ", t6.Rate as Rate6\n" +
                ", t7.Rate as Rate7\n" +
                ", t1.lrv1\n" +
                ", t1.lrv2\n" +
                ", t1.lrv3\n" +
                ", t1.lrv4\n" +
                ", t1.lrv5\n" +
                ", t1.lrv6\n" +
                ", t1.lrv7\n" +
                ", t1.remCap \n" +
                ", t2.remCap \n" +
                ", t3.remCap \n" +
                ", t4.remCap \n" +
                ", t5.remCap \n" +
                ", t6.remCap \n" +
                ", t7.remCap \n" +
                "from #testTableName_1a t1 \n" +
                "inner join #testTableName_1a t2 on t2.Arrival_DT = dateadd(day,1,t1.Arrival_DT) and t1.Rate_Qualified_ID = t2.Rate_Qualified_id and t1.Accom_Type_ID = t2.Accom_Type_ID \n" +
                "inner join #testTableName_1a t3 on t3.Arrival_DT = dateadd(day,2,t1.Arrival_DT) and t1.Rate_Qualified_ID = t3.Rate_Qualified_id and t1.Accom_Type_ID = t3.Accom_Type_ID \n" +
                "inner join #testTableName_1a t4 on t4.Arrival_DT = dateadd(day,3,t1.Arrival_DT) and t1.Rate_Qualified_ID = t4.Rate_Qualified_id and t1.Accom_Type_ID = t4.Accom_Type_ID \n" +
                "inner join #testTableName_1a t5 on t5.Arrival_DT = dateadd(day,4,t1.Arrival_DT) and t1.Rate_Qualified_ID = t5.Rate_Qualified_id and t1.Accom_Type_ID = t5.Accom_Type_ID \n" +
                "inner join #testTableName_1a t6 on t6.Arrival_DT = dateadd(day,5,t1.Arrival_DT) and t1.Rate_Qualified_ID = t6.Rate_Qualified_id and t1.Accom_Type_ID = t6.Accom_Type_ID \n" +
                "inner join #testTableName_1a t7 on t7.Arrival_DT = dateadd(day,6,t1.Arrival_DT) and t1.Rate_Qualified_ID = t7.Rate_Qualified_id and t1.Accom_Type_ID = t7.Accom_Type_ID \n" +
                "inner join #testTableName_bar_value temp_bar on temp_bar.Arrival_DT = t1.arrival_DT and temp_bar.Accom_Type_ID = t1.Accom_Type_ID;\n" +
                "drop table #testTableName_1a;\n" +
                "drop table #testTableName;";
    }

    private int getCountOf(String s, String actual) {
        int count = 0, fromIndex = 0;
        while ((fromIndex = actual.indexOf(s, fromIndex)) != -1) {
            count++;
            fromIndex++;
        }
        return count;
    }

    private String createEffectiveRatesByLOSTableQuery() {
        return "IF OBJECT_ID('temp_qualified_fplos_pid_5_1') IS NOT NULL DROP TABLE temp_qualified_fplos_pid_5_1;\n" +
                "CREATE TABLE temp_qualified_fplos_pid_5_1(\n" +
                "\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[LOS] [int] NOT NULL,\n" +
                "[Rate1] [numeric](19,5) NOT NULL,\n" +
                "[Rate2] [numeric](19,5) NOT NULL,\n" +
                "[Rate3] [numeric](19,5) NOT NULL,\n" +
                "[Rate4] [numeric](19,5) NOT NULL,\n" +
                "[Rate5] [numeric](19,5) NOT NULL,\n" +
                "[Rate6] [numeric](19,5) NOT NULL,\n" +
                "[Rate7] [numeric](19,5) NOT NULL,\n" +
                "[lrv1] [numeric](19,5) NOT NULL,\n" +
                "[lrv2] [numeric](19,5) NOT NULL,\n" +
                "[lrv3] [numeric](19,5) NOT NULL,\n" +
                "[lrv4] [numeric](19,5) NOT NULL,\n" +
                "[lrv5] [numeric](19,5) NOT NULL,\n" +
                "[lrv6] [numeric](19,5) NOT NULL,\n" +
                "[lrv7] [numeric](19,5) NOT NULL,\n" +
                "[remCap1] [numeric](19,5) NOT NULL,\n" +
                "[remCap2] [numeric](19,5) NOT NULL,\n" +
                "[remCap3] [numeric](19,5) NOT NULL,\n" +
                "[remCap4] [numeric](19,5) NOT NULL,\n" +
                "[remCap5] [numeric](19,5) NOT NULL,\n" +
                "[remCap6] [numeric](19,5) NOT NULL,\n" +
                "[remCap7] [numeric](19,5) NOT NULL,\n" +
                "\n" +
                " CONSTRAINT [PK_temp_qualified_fplos_pid_5_1] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Accom_Type_ID,Arrival_DT,Rate_Qualified_id, LOS ASC\n" +
                ")\n" +
                ");\n";
    }


    private String getPopulateBarRatesWithLOSQuery() {
        return "insert into #testTableName_bar_value\n" +
                "SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,LOS \n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,0,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate1\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,1,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate2\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,2,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate3\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,3,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate4\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,4,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate5\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,5,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate6\n" +
                " ,CASE DATEPART(WEEKDAY, dateadd(day,6,D.Arrival_DT)) WHEN 1 THEN (rud.Sunday) WHEN 2 THEN (rud.Monday) WHEN 3 THEN (rud.Tuesday) WHEN 4 THEN (rud.Wednesday) WHEN 5 THEN (rud.Thursday) WHEN 6 THEN (rud.Friday)WHEN 7 THEN (rud.Saturday) END AS BAR_Rate7\n" +
                " FROM Decision_Bar_Output as D\n" +
                " INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n" +
                " INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n" +
                " WHERE LOS > 0 AND D.Arrival_DT BETWEEN :startDate AND :endDate;";
    }

    private String getUpdateFromCTEQeury() {
        return "update temp_fplos2 set \n" +
                "temp_fplos2.rate1 = byLosCTE.rate1,\n" +
                "temp_fplos2.rate2 = byLosCTE.rate2,\n" +
                "temp_fplos2.rate3 = byLosCTE.rate3,\n" +
                "temp_fplos2.rate4 = byLosCTE.rate4,\n" +
                "temp_fplos2.rate5 = byLosCTE.rate5,\n" +
                "temp_fplos2.rate6 = byLosCTE.rate6,\n" +
                "temp_fplos2.rate7 = byLosCTE.rate7,\n" +
                "temp_fplos2.rate8 = byLosCTE.rate8,\n" +
                "temp_fplos2.rate9 = byLosCTE.rate9,\n" +
                "temp_fplos2.rate10 = byLosCTE.rate10";
    }

    private String getBarOutputByLosQuery() {
        return "IF OBJECT_ID('tempdb..#testTableName_bar_value') IS NOT NULL DROP TABLE #testTableName_bar_value;\n" +
                "CREATE TABLE #testTableName_bar_value(\n" +
                "\t[Arrival_DT] [date] NOT NULL,\n" +
                "\t[Accom_Type_ID] [int] NOT NULL,\n" +
                "\t[LOS] [int] NOT NULL,\n" +
                "\t[BAR_Rate1] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate2] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate3] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate4] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate5] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate6] [numeric](19,5) NOT NULL,\n" +
                "\t[BAR_Rate7] [numeric](19,5) NOT NULL,\n" +
                " CONSTRAINT [PK_testTableName_bar_value] PRIMARY KEY CLUSTERED \n" +
                "(Arrival_DT,Accom_Type_ID, LOS ASC)\n" +
                ");\n";
    }


    private String getQueryToPopulateTempFPLOSTableWhenDerivedRatePlanEnabledForBARByLOSProperty() {
        return "insert into #temp_qualified_fplos_pid_5_2\n" +
                "select :propertyId,LRA_1.Arrival_DT\n" +
                ",LRA_1.Accom_Type_ID\n" +
                ",LRA_1.Rate_Qualified_id, \n" +
                "case when (0>0 and ReasonType_los1=6) then 'N' else \n" +
                "\tcase when Rate1>=lrv1 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los2=6) then 'N' else \n" +
                "\tcase when Rate2>=lrv2 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los3=6) then 'N' else \n" +
                "\tcase when Rate3>=lrv3 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los4=6) then 'N' else \n" +
                "\tcase when Rate4>=lrv4 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los5=6) then 'N' else \n" +
                "\tcase when Rate5>=lrv5 then 'Y' else 'N' end end\n" +
                "+case when (0>0 and ReasonType_los6=6) then 'N' else \n" +
                "\tcase when Rate6>=lrv6 then 'Y' else 'N' end end\n" +
                "+case when :extendedStay=1 and LRA_1.Rate_Qualified_id=isnull(:specialSrpId,-1) \n" +
                "\tthen 'Y' \n" +
                "\telse \n" +
                "\t\tcase when 0>0 and ReasonType_los7=6 \n" +
                "\t\t\tthen 'N' \n" +
                "\t\t\telse\n" +
                "\t\t\t\tcase when Rate7>=lrv7 \n" +
                "\t\t\t\t\tthen 'Y' \n" +
                "\t\t\t\t\telse 'N' \n" +
                "\t\t\t\tend \n" +
                "\t\tend \n" +
                "end \n" +
                " as FPLOS\n" +
                "from #temp_qualified_fplos_pid_5_LRA_1 AS LRA_1 ;\n";
    }

    private String getQueryForUpdateTempTableForDerivedRatesAndRateAdjustments() {
        return ";WITH Bar_By_LOS_CTE AS (\n" +
                "  SELECT temp_fplos.arrival_DT,\n" +
                " temp_fplos.Accom_Type_ID,\n" +
                " temp_fplos.Rate_Qualified_id,\n" +
                " rate1 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =1 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =1 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate2 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =2 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =2 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate3 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =3 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2 + temp_bar.BAR_Rate3 + temp_fplos.Rate3)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =3 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate4 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =4 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2 + temp_bar.BAR_Rate3 + temp_fplos.Rate3 + temp_bar.BAR_Rate4 + temp_fplos.Rate4)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =4 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate5 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =5 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2 + temp_bar.BAR_Rate3 + temp_fplos.Rate3 + temp_bar.BAR_Rate4 + temp_fplos.Rate4 + temp_bar.BAR_Rate5 + temp_fplos.Rate5)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =5 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate6 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =6 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2 + temp_bar.BAR_Rate3 + temp_fplos.Rate3 + temp_bar.BAR_Rate4 + temp_fplos.Rate4 + temp_bar.BAR_Rate5 + temp_fplos.Rate5 + temp_bar.BAR_Rate6 + temp_fplos.Rate6)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 1 then ra2_cost.Net_Value6\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 + temp_fplos.Rate6) * (ra2_cost.Net_Value6 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 + temp_fplos.Rate6) + ra2_cost.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 1 then ra2_value.Net_Value6\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 + temp_fplos.Rate6) * (ra2_value.Net_Value6 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 + temp_fplos.Rate6) + ra2_value.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =6 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 1 then ra2_cost.Net_Value6\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) * (ra2_cost.Net_Value6 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) + ra2_cost.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 1 then ra2_value.Net_Value6\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) * (ra2_value.Net_Value6 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) + ra2_value.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                " ,\n" +
                " rate7 = max(\n" +
                "  case\n" +
                "   when rq.Rate_Qualified_type_ID = 1 and LOS =7 then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1 + temp_bar.BAR_Rate2 + temp_fplos.Rate2 + temp_bar.BAR_Rate3 + temp_fplos.Rate3 + temp_bar.BAR_Rate4 + temp_fplos.Rate4 + temp_bar.BAR_Rate5 + temp_fplos.Rate5 + temp_bar.BAR_Rate6 + temp_fplos.Rate6 + temp_bar.BAR_Rate7 + temp_fplos.Rate7)\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 1 then ra2_cost.Net_Value6\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 + temp_fplos.Rate6) * (ra2_cost.Net_Value6 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 + temp_fplos.Rate6) + ra2_cost.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 1 then ra2_cost.Net_Value7\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 2 then (temp_bar.BAR_Rate7 + temp_fplos.Rate7) * (ra2_cost.Net_Value7 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 3 then - (temp_bar.BAR_Rate7 + temp_fplos.Rate7) + ra2_cost.Net_Value7\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 + temp_fplos.Rate2) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 + temp_fplos.Rate2) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 + temp_fplos.Rate3) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 + temp_fplos.Rate3) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 + temp_fplos.Rate4) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 + temp_fplos.Rate4) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 + temp_fplos.Rate5) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 + temp_fplos.Rate5) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 1 then ra2_value.Net_Value6\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 + temp_fplos.Rate6) * (ra2_value.Net_Value6 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 + temp_fplos.Rate6) + ra2_value.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 1 then ra2_value.Net_Value7\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 2 then (temp_bar.BAR_Rate7 + temp_fplos.Rate7) * (ra2_value.Net_Value7 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 3 then - (temp_bar.BAR_Rate7 + temp_fplos.Rate7) + ra2_value.Net_Value7\n" +
                "     else 0\n" +
                "    end\n" +
                "   when rq.Rate_Qualified_type_ID = 2 and LOS =7 then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) + (temp_bar.BAR_Rate7 * (1 + temp_fplos.Rate7 / 100.0)))\n" +
                "    +\n" +
                "    case\n" +
                "     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n" +
                "     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n" +
                "     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_cost.Net_Value1 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_cost.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 1 then ra2_cost.Net_Value2\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_cost.Net_Value2 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_cost.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 1 then ra2_cost.Net_Value3\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_cost.Net_Value3 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_cost.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 1 then ra2_cost.Net_Value4\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_cost.Net_Value4 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_cost.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 1 then ra2_cost.Net_Value5\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_cost.Net_Value5 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_cost.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 1 then ra2_cost.Net_Value6\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) * (ra2_cost.Net_Value6 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) + ra2_cost.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 1 then ra2_cost.Net_Value7\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 2 then (temp_bar.BAR_Rate7 * (1 + temp_fplos.Rate7 / 100.0)) * (ra2_cost.Net_Value7 / 100.0)\n" +
                "     when ra2_cost.Net_Value_Type_ID7 = 3 then - (temp_bar.BAR_Rate7 * (1 + temp_fplos.Rate7 / 100.0)) + ra2_cost.Net_Value7\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) * (ra2_value.Net_Value1 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID1 = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra2_value.Net_Value1\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 1 then ra2_value.Net_Value2\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 2 then (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) * (ra2_value.Net_Value2 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID2 = 3 then - (temp_bar.BAR_Rate2 * (1 + temp_fplos.Rate2 / 100.0)) + ra2_value.Net_Value2\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 1 then ra2_value.Net_Value3\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 2 then (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) * (ra2_value.Net_Value3 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID3 = 3 then - (temp_bar.BAR_Rate3 * (1 + temp_fplos.Rate3 / 100.0)) + ra2_value.Net_Value3\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 1 then ra2_value.Net_Value4\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 2 then (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) * (ra2_value.Net_Value4 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID4 = 3 then - (temp_bar.BAR_Rate4 * (1 + temp_fplos.Rate4 / 100.0)) + ra2_value.Net_Value4\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 1 then ra2_value.Net_Value5\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 2 then (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) * (ra2_value.Net_Value5 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID5 = 3 then - (temp_bar.BAR_Rate5 * (1 + temp_fplos.Rate5 / 100.0)) + ra2_value.Net_Value5\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 1 then ra2_value.Net_Value6\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 2 then (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) * (ra2_value.Net_Value6 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID6 = 3 then - (temp_bar.BAR_Rate6 * (1 + temp_fplos.Rate6 / 100.0)) + ra2_value.Net_Value6\n" +
                "     else 0\n" +
                "    end\n" +
                "    +\n" +
                "    case\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 1 then ra2_value.Net_Value7\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 2 then (temp_bar.BAR_Rate7 * (1 + temp_fplos.Rate7 / 100.0)) * (ra2_value.Net_Value7 / 100.0)\n" +
                "     when ra2_value.Net_Value_Type_ID7 = 3 then - (temp_bar.BAR_Rate7 * (1 + temp_fplos.Rate7 / 100.0)) + ra2_value.Net_Value7\n" +
                "     else 0\n" +
                "    end\n" +
                "  end)\n" +
                "FROM testTableName_1 temp_fplos\n" +
                "INNER JOIN #testTableName_bar_value temp_bar\n" +
                " on temp_bar.Arrival_DT = temp_fplos.arrival_DT\n" +
                " and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n" +
                "INNER JOIN Rate_Qualified rq\n" +
                " on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n" +
                "LEFT JOIN Rate_Qualified_Adjustment ra1\n" +
                " on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n" +
                " and ra1.Posting_Rule_ID = 1\n" +
                "LEFT JOIN #testTableName_RateAdjustment ra2_cost\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n" +
                " and ra2_cost.AdjustmentType = 'YieldableCost'\n" +
                "LEFT JOIN #testTableName_RateAdjustment ra2_value\n" +
                " on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n" +
                " and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n" +
                " and ra2_value.AdjustmentType = 'YieldableValue'\n" +
                " where rq.Rate_Qualified_type_ID in (1, 2)\n" +
                " group by temp_fplos.arrival_DT,\n" +
                " temp_fplos.Accom_Type_ID,\n" +
                " temp_fplos.Rate_Qualified_id)\n" +
                "update temp_fplos2 set \n" +
                "temp_fplos2.rate1 = byLosCTE.rate1,\n" +
                "temp_fplos2.rate2 = byLosCTE.rate2,\n" +
                "temp_fplos2.rate3 = byLosCTE.rate3,\n" +
                "temp_fplos2.rate4 = byLosCTE.rate4,\n" +
                "temp_fplos2.rate5 = byLosCTE.rate5,\n" +
                "temp_fplos2.rate6 = byLosCTE.rate6,\n" +
                "temp_fplos2.rate7 = byLosCTE.rate7 FROM testTableName_1 temp_fplos2 \n" +
                "INNER JOIN Bar_By_LOS_CTE byLosCTE on \n" +
                "temp_fplos2.arrival_DT = byLosCTE.arrival_DT and \n" +
                "temp_fplos2.Rate_Qualified_id = byLosCTE.Rate_Qualified_id and \n" +
                "temp_fplos2.Accom_Type_ID = byLosCTE.Accom_Type_ID; \n";
    }
}
