package com.ideas.tetris.pacman.services.marketsegment.service;

import com.google.common.collect.ImmutableMap;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.independentproducts.repository.IndependentProductsRepository;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentCasinoCategoryType;
import com.ideas.tetris.pacman.services.marketsegment.MktSegChangeType;
import com.ideas.tetris.pacman.services.marketsegment.entity.AMSCompositionChange;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentProductMapping;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.marketsegment.entity.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldType;
import com.ideas.tetris.pacman.services.marketsegment.repository.MarketSegmentRepository;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.product.ProductType;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.AdditionalAnswers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.product.Product.INDEPENDENT_PRODUCT_CODE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.GROUP;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.UNFENCED_NON_PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService.MAPPED_MARKET_CODE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService.SPECIAL_IPP_MARKET_CODES;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyCollection;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.nullable;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class AnalyticalMarketSegmentServiceTest extends AbstractG3JupiterTest {

    private static final Integer ONE = 1;
    private static final Integer BOOKING_BLOCK_PCT_DEFAULT = 0;
    private static final Integer BOOKING_BLOCK_PCT_IS_BLOCK = 100;

    private static final String CANCELLED = "CANCELLED";
    private static final String CHECKED_OUT = "CHECKED OUT";
    private static final String CHECKED_IN = "CHECKED IN";

    private static final String MARKET_SEGMENT_1 = "BC";
    private static final String MARKET_SEGMENT_2 = "CC";
    private static final String MARKET_SEGMENT_3 = "CF";
    private static final String MARKET_SEGMENT_4 = "CG";
    private static final String MARKET_SEGMENT_5 = "FD";
    private static final String MARKET_SEGMENT_5_DEF = "FD_DEF";
    private static final String MARKET_SEGMENT_6 = "HR";
    private static final String MARKET_SEGMENT_7 = "SD";
    private static final String MARKET_SEGMENT_7_DEF = "SD_DEF";
    private static final String MARKET_SEGMENT_8_NEW = "_ZZ_";
    private static final String MARKET_SEGMENT_9_TMP = "TMP";


    private static final String RATE_CODE_1 = "BA";
    private static final String RATE_CODE_2 = "BC1";
    private static final String RATE_CODE_3 = "RC1";
    private static final String RATE_CODE_4 = "RC4";
    private static final String RATE_CODE_5 = "RC5";
    private static final String RATE_CODE_6 = "RC6";
    private static final String RATE_CODE_7 = "RC7";
    private static final String RATE_CODE_8 = "RC8";
    private static final String RATE_CODE_9 = "RC9";
    private static final String RATE_CODE_DASH_1 = "RC-1";
    private static final String RATE_CODE_DASH_2 = "RC-2";
    private static final String CONV = "CONV";
    private static final String MKT_SEG_CODE_CONV_UF = "CONV_UF";
    private static final String UNQUALIFIED_FENCED_SUFFIX = "_UF";
    private static final String UNQUALIFIED_EQUAL_TO_BAR_SUFFIX = "_USB";
    private static final String AMS_NEW = "AMS_NEW";
    private static final String AMS_OLD = "AMS_OLD";
    private static final String AMS_NEW_ETB = "AMS_NEW_ETB";
    private static final String AMS_NEW_UF = "AMS_NEW_UF";
    private static final String AMS_NEW_1_UF = "AMS_NEW_1_UF";
    private static final String AMS_NEW_DEF = "AMS_NEW_DEF";
    private static final int RANK_10 = 10;
    public static final List<String> MODIFIED_HOTEL_MKT_SEGS = List.of("CNR", "DISC", "LNR", "IT", "MKT");
    public static final List<String> SPLIT_AMS = List.of("CNR_U", "CNR_QS", "CNR_QSL");
    private static final String BAR = "BAR";
    private static final String TIER_1 = "Tier1";
    private static final String TIER_2 = "Tier2";
    private static final String TIER_3 = "Tier3";
    private static final String LOYALTY = "LOYALTY";
    private static final String GROUP_MS = "Group";
    private static final String STRAIGHT_MS = "SMS";
    private static final String TRANSIENT_BLOCK = "TB";
    private static final String DEFAULT_MS_AFFIXED = "IT_DEF";
    private static final String DEF_FUT_MS_AFFIXED = "IT_DEF_FUT";
    private static final String DEF_FUT_MS = "IT";
    private static final String MKT_SEG_1 = "LNR";
    private static final String MKT_SEG_1_AFFIXED = "LNR_QYL";
    private static final String MKT_SEG_2 = "CNR";
    private static final String MKT_SEG_2_AFFIXED = "CNR_QY";
    private static final String MKT_SEG_3 = "CMP";
    private static final String MKT_SEG_4 = "IT";
    private static final String TRUNCATE_TABLE_MKT_SEG_MASTER = "TRUNCATE TABLE Mkt_Seg_Master";
    private static final String TRUNCATE_TABLE_ANALYTICAL_MKT_SEG = "TRUNCATE TABLE Analytical_Mkt_Seg";
    private static final String TRUNCATE_TABLE_PRODUCT_RATE_CODE = "TRUNCATE TABLE Product_Rate_Code";
    private static final String TRUNCATE_TABLE_MKT_SEG_PRODUCT_MAPPING = "TRUNCATE TABLE Mkt_Seg_Product_Mapping";
    private static final String TRUNCATE_TABLE_MKT_SEG_DETAILS = "TRUNCATE TABLE Mkt_Seg_Details";
    private static final String TRUNCATE_TABLE_MKT_SEG_DETAILS_PROPOSED = "TRUNCATE TABLE Mkt_Seg_Details_Proposed";

    @InjectMocks
    AnalyticalMarketSegmentService service;
    private final int BusinessTypeGroup = 1;
    private final int BusinessTypeTransient = 2;
    private final int Yieldable = 1;
    private final int SemiYieldable = 2;
    private final int NonYieldable = 3;
    private final int ForecastActivityTypeForDemandAndWash = 1;
    private final int Qualified = 1;
    private final int Unqualified = 0;
    private final int Fenced = 1;
    private final int NonFenced = 0;
    private final int Package = 1;
    private final int NonPackage = 0;
    private final int Link = 1;
    private final int NonLink = 0;
    private final int PriceByBar = 1;
    private final int NonPriceByBar = 0;
    private final int IsEditable = 1;
    private final int processStatusIdCommitted = 2;
    private final int processStatusUpdated = 10;
    private final int statusId = 1;
    private final String attEqualsToBar = "EQUAL_TO_BAR";
    private final String attFenced = "FENCED";
    private final String attPackaged = "PACKAGED";
    private final String attFencedAndPackaged = "FENCED_AND_PACKAGED";
    private final String attUnfencedNonPackaged = "UNFENCED_NON_PACKAGED";
    private final String attQualifiedNonBlockLinkedYieldable = "QUALIFIED_NONBLOCK_LINKED_YIELDABLE";
    private final String attQualifiedNonBlockLinkedSemiYieldable = "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE";
    private final String attQualifiedNonBlockNonLinkedYieldable = "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE";
    private final String attQualifiedNonBlockNonLinkedSemiYieldable = "QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE";
    private final String attQualifiedNonBlockNonLinkedNonYieldable = "QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE";
    private final String attTransientBlockNonLinked = "TRANSIENT_BLOCK_NON_LINKED";
    private final String attGroup = "GROUP";
    private final String AttributeDescriptionForGroup = "group";
    private final String AttributeDescriptionForFenced = "transient.unqualified.fenced";
    private final String AttributeDescriptionForPriceByBAR = "equal.to.bar";
    private final String AttributeDescriptionForPackage = "transient.unqualified.package";
    private final String AttributeDescriptionForFencedAndPackage = "fenced.and.packaged";
    private final String AttributeDescriptionForUnFencedAndNonPackage = "unfenced.non.packaged";
    private final String AttributeDescriptionForQualifiedLinkedYieldable = "qualified.nonblock.linked.yieldable";
    private final String AttributeDescriptionForQualifiedLinkedSemiYieldable = "qualified.nonblock.linked.semiyieldable";
    private final String AttributeDescriptionForQualifiedNonLinkedYieldable = "qualified.nonblock.notlinked.yieldable";
    private final String AttributeDescriptionForQualifiedNonLinkedSemiYieldable = "qualified.nonblock.notlinked" +
            ".semiyieldable";
    private final String AttributeDescriptionForQualifiedNonLinkedNotYieldable = "qualified.nonblock.notlinked.nonyieldable";
    private final String AttributeDescriptionForTransientBlock = "transient.block.non.linked";
    private final Integer EDITABLE = 1;
    private final Integer NOT_EDITABLE = 0;

    private final Integer PROPERTY_ID = 5;

    @Mock
    @TenantCrudServiceBean.Qualifier
    CrudService tenantCrudService;

    private CrudService crudService;
    @Mock
    private AnalyticalMarketSegmentRepository amsRepository;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsServiceLocal;
    @Mock
    private PropertyService propertyServiceLocal;
    @Mock
    private MarketSegmentPopulationResource marketSegmentPopulationResource;
    @Mock
    private RestClient restClient;
    @Mock
    private DateService dateService;
    @Mock
    private MarketSegmentRepository marketSegmentRepository;
    @Mock
    private MarketSegmentService marketSegmentService;
    @Mock
    private IndependentProductsService independentProductsService;
    @Mock
    IndependentProductsRepository independentProductsRepository;
    @Mock
    VirtualPropertyMappingService virtualPropertyMappingService;
    @Mock
    BusinessGroupService businessGroupService;


    private int forecastActivityTypeId;
    private ForecastActivityType forecastActivityType;

    @BeforeEach
    public void setup() {
        crudService = tenantCrudService();
        service.crudService = crudService;

        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        crudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.DELETE_ALL);

        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern("MM-dd-yyyy");
        when(dateService.getCaughtUpDate()).thenReturn(dateTimeFormat.parseDateTime("03-30-2017").toDate());
        Date occupancyDate = new Date();

        List<AmsOccupancySummary> summaries = new ArrayList<>();
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_1, RATE_CODE_1, CHECKED_IN, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_1, RATE_CODE_2, CHECKED_IN, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_1, RATE_CODE_3, CHECKED_IN, false));

        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_2, RATE_CODE_1, CHECKED_OUT, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_2, RATE_CODE_2, CHECKED_OUT, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_2, RATE_CODE_3, CHECKED_OUT, false));

        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_3, RATE_CODE_2, CANCELLED, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_3, RATE_CODE_3, CANCELLED, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_4, RATE_CODE_1, CHECKED_IN, false));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_5, RATE_CODE_2, CHECKED_OUT, false));

        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_1, null, "GROUP1", true));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_2, null, "GROUP2", true));
        summaries.add(createSummary(occupancyDate, MARKET_SEGMENT_6, null, "GROUP3", true));
        crudService.save(summaries);
        crudService.flush();
        forecastActivityTypeId = 1;
        forecastActivityType = new ForecastActivityType();
        forecastActivityType.setId(forecastActivityTypeId);
        List<ForecastActivityType> forecastActivities = crudService.findByNamedQuery(ForecastActivityType.BY_NAME,
                QueryParameter.with("name", "Demand and Wash").parameters());
        forecastActivityType = forecastActivities.get(0);
    }

    @Test
    void analyticalMarketSegmentMappingComplete_ParamName() {
        assertEquals("pacman.population.analyticalMarketSegmentMappingComplete", AnalyticalMarketSegmentService.ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE);
    }

    @Test
    void saveAndDeleteTransactional() {
        doReturn(List.of()).when(amsRepository).saveTransactional(anyCollection());
        doNothing().when(amsRepository).deleteTransactional(anyCollection());
        service.saveAndDelete(List.of(new AnalyticalMarketSegment()));
        verify(amsRepository).deleteTransactional(anyCollection());
    }

    @Test
    public void previewForAttributeAsGroupDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 8, 4, 0, 40, 356.29, "CHECKED OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 15, 5, 0, 54, 131.42, "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", "", 15, 5, 0, 54, 131.42, "CHECKED IN");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[6];
            object[0] = result.getMappedMarketCode();
            object[1] = result.getAttribute();
            object[2] = result.getRoomsSold();
            object[3] = result.getRoomRevenue();
            object[4] = result.getAdr();
            object[5] = result.getHotelPercent();
            objects.add(object);
        }
        crudService.flushAndClear();
        assertOnPreview(objects, MARKET_SEGMENT_5, null, 139, 730.89, 5.26, 72.02);
    }

    @Test
    public void previewForAttributeAsFencedAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 8, 4, 0, 40, 356.29, "CHECKED OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, "", 15, 5, 0, 54, 131.42, "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", "", 15, 5, 0, 12, 131.42, "CHECKED IN");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[6];
            object[0] = result.getMappedMarketCode();
            object[1] = result.getAttribute();
            object[2] = result.getRoomsSold();
            object[3] = result.getRoomRevenue();
            object[4] = result.getAdr();
            object[5] = result.getHotelPercent();
            objects.add(object);
        }
        crudService.flushAndClear();
        assertOnPreview(objects, MARKET_SEGMENT_5, null, 139, 730.8900000000001, 5.258201438848921, 92.05298013245033);
    }

    @Test
    public void previewForAttributeAsPriceBYBarAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 54, 131.42,
                "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 65, 131.42,
                "PROSPECT");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", RATE_CODE_2, 15, 5, 0, 2, 131.42, "PROSPECT");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        crudService.flushAndClear();
        //AssertOnPreview will find the record by mappedCode in the list to do the compare
        assertOnPreview(results, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, null, 85, 599.47,
                7.052588235294118, 41.262135922330096);
        assertOnPreview(results, MARKET_SEGMENT_5 + "_DEF", null, 119, 262.84, 2.2087394957983193, 57.76699029126213);
    }

    @Test
    @Disabled("slow")
    public void previewForAttributeAsUnfencedNonPackagedAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                UNFENCED_NON_PACKAGED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 54, 131.42,
                "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 65, 131.42,
                "PROSPECT");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", RATE_CODE_2, 15, 5, 0, 2, 131.42, "PROSPECT");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        crudService.flushAndClear();
        //AssertOnPreview will find the record by mappedCode in the list to do the compare
        assertOnPreview(results, MARKET_SEGMENT_5 + "_U", null, 85, 599.47, 7.052588235294118, 41.262135922330096);
        assertOnPreview(results, MARKET_SEGMENT_5 + "_DEF", null, 119, 262.84, 2.2087394957983193, 57.76699029126213);
    }

    @Test
    public void previewForAttributeAsQualifiedNonBlockNotLinkedSemiyieldableAtRateCodeLevelWithRateCodeTypeEnumAsStartWithDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("R");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_5, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_6, 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 15, 5, 0, 54, 131.42,
                "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 65, 131.42,
                "PROSPECT");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", RATE_CODE_2, 15, 5, 0, 2, 131.42, "PROSPECT");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[6];
            object[0] = result.getMappedMarketCode();
            object[1] = result.getAttribute();
            object[2] = result.getRoomsSold();
            object[3] = result.getRoomRevenue();
            object[4] = result.getAdr();
            object[5] = result.getHotelPercent();
            objects.add(object);
        }
        crudService.flushAndClear();
        assertOnPreview(objects, MARKET_SEGMENT_5 + "_QS", null, 85, 599.47, 7.052588235294118, 41.262135922330096);
        assertOnPreview(objects, MARKET_SEGMENT_5 + "_DEF", null, 119, 262.84, 2.2087394957983193, 57.76699029126213);
    }

    @Test
    public void previewForAttributeAsQualifiedNonBlockNotLinkedYieldableAtRateCodeLevelWithRateCodeTypeEnumAsEndWithDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("1");
        assignment.setRateCodeType(RateCodeTypeEnum.ENDS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_5, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_6, 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 15, 5, 0, 54, 131.42,
                "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 15, 5, 0, 65, 131.42,
                "PROSPECT");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", RATE_CODE_2, 15, 5, 0, 2, 131.42, "PROSPECT");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[6];
            object[0] = result.getMappedMarketCode();
            object[1] = result.getAttribute();
            object[2] = result.getRoomsSold();
            object[3] = result.getRoomRevenue();
            object[4] = result.getAdr();
            object[5] = result.getHotelPercent();
            objects.add(object);
        }
        crudService.flushAndClear();
        assertOnPreview(objects, MARKET_SEGMENT_5 + "_QY", null, 65, 131.42, 2.0218461538461536, 31.55339805825243);
        assertOnPreview(objects, MARKET_SEGMENT_5 + "_DEF", null, 139, 730.8900000000001, 5.258201438848921,
                67.47572815533981);
    }

    @Test
    public void previewForAttributeAsFencedAtRateCodeLevelWithRateCodeTypeEnumAsContainsDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("B");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment), AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 9, 5, 0, 45, 243.18, "NO SHOW");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_5, 15, 5, 0, 54, 131.42,
                "CHECKED IN");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_6, 15, 5, 0, 65, 131.42,
                "PROSPECT");
        populateAmsOccupancySummary(new StringBuilder(), "Invalid MS", RATE_CODE_2, 15, 5, 0, 2, 131.42, "PROSPECT");
        crudService.flushAndClear();
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[6];
            object[0] = result.getMappedMarketCode();
            object[1] = result.getAttribute();
            object[2] = result.getRoomsSold();
            object[3] = result.getRoomRevenue();
            object[4] = result.getAdr();
            object[5] = result.getHotelPercent();
            objects.add(object);
        }
        crudService.flushAndClear();
        assertOnPreview(objects, MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, null, 85, 599.47, 7.052588235294118,
                41.262135922330096);
        assertOnPreview(objects, MARKET_SEGMENT_5 + "_DEF", null, 119, 262.84, 2.2087394957983193, 57.76699029126213);
    }

    @Test
    public void previewToValidateAttributeAtGroupLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForGroup);
    }

    @Test
    public void test_unassignMarketSegments_createCompositionChange_StageNotOneOrTwoWay() {
        setWorkContextProperty(TestProperty.H1);
        when(propertyServiceLocal.isStageAtLeast(Stage.ONE_WAY, TestProperty.H1.getId())).thenReturn(false);
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        createMktSegIfNeeded(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_1);
        assertEquals(0, crudService.findAll(AMSCompositionChange.class).size());
        service.unassignMarketSegments(Collections.singletonList(assignedMktSeg.get(0)), true);
        // Population is independent of the stage
        assertEquals(1, crudService.findAll(AMSCompositionChange.class).size());
        assertNotNull(crudService.findByNamedQuerySingleResult(MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME,
                QueryParameter.with("mktSegName", assignedMktSeg.get(0).getMarketCode()).parameters()));
    }

    @Test
    public void test_unassignMarketSegments_createCompositionChange_happyPath() {
        setWorkContextProperty(TestProperty.H1);
        when(propertyServiceLocal.isStageAtLeast(Stage.ONE_WAY, TestProperty.H1.getId())).thenReturn(true);
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        createMktSegIfNeeded(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_1);
        assertEquals(0, crudService.findAll(AMSCompositionChange.class).size());
        service.unassignMarketSegments(Collections.singletonList(assignedMktSeg.get(0)), true);
        assertEquals(1, crudService.findAll(AMSCompositionChange.class).size());
        List<AMSCompositionChange> entities = crudService.findAll(AMSCompositionChange.class);
        assertEquals(3, entities.get(0).getAMSRoomsSold().intValue());
        assertEquals(new BigDecimal("37.50"), entities.get(0).getHotelPercent());
        assertNotNull(crudService.findByNamedQuerySingleResult(MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME,
                QueryParameter.with("mktSegName", assignedMktSeg.get(0).getMarketCode()).parameters()));
    }

    @Test
    public void test_clearAMSCompositionChanges() {
        AMSCompositionChange entity = new AMSCompositionChange();
        entity.setAMSId(1);
        entity.setMarketSegmentId(1);
        entity.setChangeDate(LocalDateTime.now());
        entity.setAMSRoomsSold(10);
        crudService.save(entity);
        assertEquals(1, crudService.findAll(AMSCompositionChange.class).size());
        assertEquals(1, service.clearAMSCompositionChanges());
        assertEquals(0, crudService.findAll(AMSCompositionChange.class).size());
    }

    @Test
    public void test_computeCompositionChangeImpact() {
        Map<Integer, List<String>> mktIdMarketCodes = ImmutableMap.of(1, Collections.singletonList("TEST"), 2, Collections.singletonList("TEST2"));
        when(marketSegmentService.findMktSegIdHotelMarketCodeMap()).thenReturn(mktIdMarketCodes);
        AMSCompositionChange entity1 = new AMSCompositionChange();
        entity1.setAMSId(1);
        entity1.setMarketSegmentId(1);
        entity1.setChangeDate(LocalDateTime.now());
        entity1.setAMSRoomsSold(10);
        entity1.setHotelPercent(new BigDecimal("2.0"));
        AMSCompositionChange entity2 = new AMSCompositionChange();
        entity2.setAMSId(1);
        entity2.setMarketSegmentId(1);
        entity2.setChangeDate(LocalDateTime.now());
        entity2.setAMSRoomsSold(10);
        entity2.setHotelPercent(new BigDecimal("5.0"));
        AMSCompositionChange entity3 = new AMSCompositionChange();
        entity3.setAMSId(2);
        entity3.setMarketSegmentId(1);
        entity3.setChangeDate(LocalDateTime.now());
        entity3.setAMSRoomsSold(10);
        entity3.setHotelPercent(new BigDecimal("9.0"));
        AMSCompositionChange entity4 = new AMSCompositionChange();
        entity4.setAMSId(2);
        entity4.setMarketSegmentId(1);
        entity4.setChangeDate(LocalDateTime.now());
        entity4.setAMSRoomsSold(10);
        entity4.setHotelPercent(new BigDecimal("7.0"));
        crudService.save(Arrays.asList(entity1, entity2, entity3, entity4));
        BigDecimal result = service.computeCompositionChangeImpact(true);
        assertEquals(new BigDecimal("14.0"), result); // should be largest percent from AMS1 + largest percent from AMS2
    }

    @Test
    public void test_computeCompositionChangeImpact_someApplyHistoryPresent() {
        Map<Integer, List<String>> mktIdMarketCodes = ImmutableMap.of(1, Collections.singletonList("TEST"), 2, Collections.singletonList("TEST2"));
        when(marketSegmentService.findMktSegIdHotelMarketCodeMap()).thenReturn(mktIdMarketCodes);
        AMSCompositionChange entity1 = new AMSCompositionChange();
        entity1.setAMSId(1);
        entity1.setMarketSegmentId(1);
        entity1.setChangeDate(LocalDateTime.now());
        entity1.setAMSRoomsSold(10);
        entity1.setHotelPercent(new BigDecimal("2.0"));
        AMSCompositionChange entity2 = new AMSCompositionChange();
        entity2.setAMSId(1);
        entity2.setMarketSegmentId(1);
        entity2.setChangeDate(LocalDateTime.now());
        entity2.setAMSRoomsSold(10);
        entity2.setHotelPercent(new BigDecimal("5.0"));
        AMSCompositionChange entity3 = new AMSCompositionChange();
        entity3.setAMSId(2);
        entity3.setMarketSegmentId(3);
        entity3.setChangeDate(LocalDateTime.now());
        entity3.setAMSRoomsSold(10);
        entity3.setHotelPercent(new BigDecimal("9.0"));
        AMSCompositionChange entity4 = new AMSCompositionChange();
        entity4.setAMSId(2);
        entity4.setMarketSegmentId(2);
        entity4.setChangeDate(LocalDateTime.now());
        entity4.setAMSRoomsSold(10);
        entity4.setHotelPercent(new BigDecimal("7.0"));
        entity4.setApplyToHistory(true);
        crudService.save(Arrays.asList(entity1, entity2, entity3, entity4));
        BigDecimal result = service.computeCompositionChangeImpact(true);
        assertEquals(new BigDecimal("5.0"), result); // should be largest percent from AMS1 + largest percent from AMS2
    }

    @Test
    public void unassignNonDefaultMarketSegments() {
        //when
        MktSeg mktSeg = createMktSeg("FD_QYL");
        MktSeg mktSegDef = createMktSeg("FD_DEF");
        crudService.save(mktSeg);
        crudService.save(mktSegDef);

        Date occupancyDate = new Date();
        crudService.save(createSummary(occupancyDate, MARKET_SEGMENT_5_DEF, RATE_CODE_3, CHECKED_OUT, false));

        service.clearAssignments(TestProperty.H1.getPaddedId());

        // assign and verify
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);

        AnalyticalMarketSegmentSummary assignment2 = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5_DEF);

        assignment.setMarketCode(MARKET_SEGMENT_5);
        assignment.setRateCode("ABC");
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        assignment2.setMarketCode(MARKET_SEGMENT_5);
        assignment2.setRateCode("RC1");
        service.assignMarketSegments(Collections.singletonList(assignment2),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assigned =
                new ArrayList<>(crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODE, QueryParameter.with(
                        "marketCode", MARKET_SEGMENT_5).parameters()));
        assertEquals(2, assigned.size());

        AnalyticalMarketSegment ams = crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.BY_RATE_CODE,
                QueryParameter.with("rateCode", assignment.getRateCode()).parameters());
        service.unassignMarketSegments(Collections.singletonList(ams), false);

        assigned = new ArrayList<>(crudService.findByNamedQuery(AnalyticalMarketSegment.BY_RATE_CODE,
                QueryParameter.with("rateCode", assignment.getRateCode()).parameters()));
        assertEquals(0, assigned.size());
    }

    @Test
    public void previewToValidateAttributeAsFencedAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForFenced);
    }

    @Test
    public void previewToValidateAttributeAsPriceByBARAtMarketSegmentLevelDataTest() {
        AnalyticalMarketSegmentService spyService = spy(service);
        setWorkContextProperty(TestProperty.H2);
        spyService.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        spyService.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,
                forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForPriceByBAR);
    }

    @Test
    public void previewToValidateAttributeAsPackageAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.PACKAGED, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForPackage);
    }

    @Test
    public void previewToValidateAttributeAsFencedAndPackageAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForFencedAndPackage);
    }

    @Test
    public void previewToValidateAttributeAsUnFencedAndNonPackageAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, UNFENCED_NON_PACKAGED,
                forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForUnFencedAndNonPackage);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedLinkedYieldableAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE
                , forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForQualifiedLinkedYieldable);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedLinkedSemiYieldableAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForQualifiedLinkedSemiYieldable);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedYieldableAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForQualifiedNonLinkedYieldable);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedSemiYieldableAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForQualifiedNonLinkedSemiYieldable);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedNotYieldableAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForQualifiedNonLinkedNotYieldable);
    }

    @Test
    public void previewToValidateAttributeAsTransientBlockAtMarketSegmentLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                forecastActivityTypeId);
        assertOnPreviewForAttributeDescription(AttributeDescriptionForTransientBlock);
    }

    @Test
    public void previewToValidateAttributeAsPriceByBARAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForPriceByBAR,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF",
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
    }

    @Test
    public void previewToValidateAttributeAsFencedAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment), AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForFenced,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX);
    }

    @Test
    public void previewToValidateAttributeAsPackagedAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment), AnalyticalMarketSegmentAttribute.PACKAGED
                , forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForPackage,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_UP");
    }

    @Test
    public void previewToValidateAttributeAsFencedAndPackageAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForFencedAndPackage,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_UFP");
    }

    @Test
    public void previewToValidateAttributeAsUnFencedAndNonPackageAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                UNFENCED_NON_PACKAGED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForUnFencedAndNonPackage,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_U");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedLinkedYieldableAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedLinkedYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QYL");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedLinkedSemiYieldableAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedLinkedSemiYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QSL");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedYieldableAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedNonLinkedYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QY");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedSemiYieldableAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedNonLinkedSemiYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QS");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedNotYieldableAtRateCodeLevelDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedNonLinkedNotYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QN");
    }

    @Test
    public void previewToValidateAttributeAsPriceByBARAtRateCodeLevelWithRateCodeTypeEnumAsStartDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("B");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForPriceByBAR,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF",
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
    }

    @Test
    public void previewToValidateAttributeAsQualifiedNonLinkedSemiYieldableAtRateCodeLevelWithRateCodeTypeEnumAsContainsDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("B");
        assignment.setRateCodeType(RateCodeTypeEnum.CONTAINS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedNonLinkedSemiYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QS");
    }

    @Test
    public void previewToValidateAttributeAsQualifiedLinkedYieldableAtRateCodeLevelWithRateCodeTypeEnumAsEndsWithDataTest() {
        setWorkContextProperty(TestProperty.H2);
        service.clearAssignments(TestProperty.H2.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("1");
        assignment.setRateCodeType(RateCodeTypeEnum.ENDS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        updateAMSOccupancySummaryForPreviewForMS5();
        assertOnPreviewForAttributeDescriptionAtRateCodeLevel(AttributeDescriptionForQualifiedLinkedYieldable,
                AttributeDescriptionForPriceByBAR, MARKET_SEGMENT_5 + "_DEF", MARKET_SEGMENT_5 + "_QYL");
    }

    @Test
    public void validateYCByRuleForExpandWithRateCodeTypeEnumAsStartWith() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        DataLoadMetadata dataLoadMetaData = createDataLoadMetaData("PTRANS");
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_5);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_6);

        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("R");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        service.populateYieldCategoryByRule();
        crudService.flushAndClear();
        assertOnYieldCategoryByRuleRowCount(3);
        assertOnYieldCategoryByRule(MARKET_SEGMENT_5, RATE_CODE_6, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
        assertOnYieldCategoryByRule(MARKET_SEGMENT_5, RATE_CODE_5, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
    }

    @Test
    public void validateYCByRuleForExpandWithRateCodeTypeEnumAsContains() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        DataLoadMetadata dataLoadMetaData = createDataLoadMetaData("PTRANS");
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_5);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_6);

        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("B");
        assignment.setRateCodeType(RateCodeTypeEnum.CONTAINS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        service.populateYieldCategoryByRule();
        crudService.flushAndClear();
        assertOnYieldCategoryByRuleRowCount(3);
        assertOnYieldCategoryByRule(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
        assertOnYieldCategoryByRule(MARKET_SEGMENT_5, RATE_CODE_2, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
    }

    @Test
    public void validateYCByRuleForExpandWithRateCodeTypeEnumAsEndsWith() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, MARKET_SEGMENT_5, RATE_CODE_1, 0, 0, 0, 1, 1.29, "CHECKED OUT");
        populateAmsOccupancySummary(insertQuery, MARKET_SEGMENT_5, RATE_CODE_2, 0, 0, 0, 1, 1.29, "CHECKED OUT");
        populateAmsOccupancySummary(insertQuery, MARKET_SEGMENT_5, RATE_CODE_5, 0, 0, 0, 1, 1.29, "CHECKED OUT");
        populateAmsOccupancySummary(insertQuery, MARKET_SEGMENT_5, RATE_CODE_6, 0, 0, 0, 1, 1.29, "CHECKED OUT");

        DataLoadMetadata dataLoadMetaData = createDataLoadMetaData("PTRANS");
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_2);

        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("1");
        assignment.setRateCodeType(RateCodeTypeEnum.ENDS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        service.populateYieldCategoryByRule();
        crudService.flushAndClear();
        assertOnYieldCategoryByRuleRowCount(2);
        assertOnYieldCategoryByRule(MARKET_SEGMENT_5, RATE_CODE_2, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
    }

    @Test
    public void getUnassignedGroupMarketSegments() {
        List<AnalyticalMarketSegmentSummary> results = service.getUnassignedGroupMarketSegments();
        assertNotNull(results);

        for (AnalyticalMarketSegmentSummary summary : results) {
            System.out.println(summary);
            assertTrue(summary.isGroupMs());
            assertEquals(new Integer(1), summary.getBlock());
            assertEquals(new Integer(1), summary.getPickup());
        }

        assertEquals(3, results.size());
    }

    @Test
    public void updateAttributeFromPriceByBARToFenced() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);


        crudService.flushAndClear();
        int BookingBlockPercentage1 = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, PriceByBar,
                BookingBlockPercentage1, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, attEqualsToBar,
                RateCodeTypeEnum.EQUALS);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED, (Product) null);
        crudService.flushAndClear();

        int BookingBlockPercentage2 = 0;
        assertOnMarketSegmentMasterAfterUpdate(MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, Fenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage2, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevelAfterUpdate(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, attFenced,
                RateCodeTypeEnum.EQUALS);
        crudService.flushAndClear();
        assertOnMarketSegmentDetailsProposedRowCount(0);
    }

    @Test
    public void updateAttributeFromPriceByBARToFencedOnExistingMktSeg() {
        // Clear any Assignments on H1
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // Create an existing AnalyticalMarketSegmentSummary record
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();

        // Create an old MktSeg that will be directly updated instead of creating a new MktSeg
        MktSeg oldMktSeg = insertMktSeg(TestProperty.H1.getId(), MARKET_SEGMENT_8_NEW, MARKET_SEGMENT_8_NEW,
                MARKET_SEGMENT_8_NEW);
        crudService.flushAndClear();

        // Get the previously created AnalyticalMarketSegment - and update the mappedMarketCode to represent the old
        // mapping
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        assignedMktSeg.get(0).setMappedMarketCode(MARKET_SEGMENT_8_NEW);
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED, (Product) null);
        crudService.flushAndClear();

        // Verify old MktSeg was updated
        MktSeg mktSegWithOldCode = findMktSegByCode(MARKET_SEGMENT_8_NEW);
        assertNull(mktSegWithOldCode);

        // Verify the MktSeg was updated for the new code
        String newMktCode = MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX;
        MktSeg mktSegWithNewCode = findMktSegByCode(newMktCode);
        assertEquals(oldMktSeg.getId(), mktSegWithNewCode.getId());
        assertEquals(oldMktSeg.getEditable(), EDITABLE);
        assertEquals(ONE, mktSegWithNewCode.getEditable());
        assertEquals(newMktCode, mktSegWithNewCode.getCode());
        assertEquals(newMktCode, mktSegWithNewCode.getName());
        assertEquals(newMktCode, mktSegWithNewCode.getDescription());
        assertNotNull(mktSegWithNewCode.getMktSegDetailsProposed());
    }


    @Test
    public void deleteAttributePriceByBAR() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        List<AnalyticalMarketSegmentSummary> summaries = new ArrayList<>();
        summaries.add(assignment);
        summaries.add(createDefaultMarketSegment(MARKET_SEGMENT_5));
        service.assignMarketSegments(summaries, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        crudService.flushAndClear();
        int BookingBlockPercentage1 = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, PriceByBar,
                BookingBlockPercentage1, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, attEqualsToBar, RateCodeTypeEnum.EQUALS);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(findAnalyticalMarketSegmentByMappedMarketCode(assignedMktSeg, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX)), false);
        crudService.flushAndClear();

        assertOnAnalyticalMarketSegmentRowCount(0);
        // There should be no remaining market segment master entries for this market code.
        assertOnMarketSegmentMasterRowCount(0);
    }

    @Test
    public void updateAttributeFromPriceByBARToFencedWhenEntryPresentUnderMktSegmentDetails() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();

        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);

        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);


        assignment.setMappedCode(null);
        assignment.setRateCode(RATE_CODE_2);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        crudService.flushAndClear();

        int BookingBlockPercentage1 = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, PriceByBar,
                BookingBlockPercentage1, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, attEqualsToBar,
                RateCodeTypeEnum.EQUALS);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(1).getMappedMarketCode());
        String mktId = getMktId(assignedMktSeg.get(1).getMappedMarketCode());
        populateMarketSegmentDetails(insertQuery2, mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, Fenced, NonPackage, NonLink, NonPriceByBar, processStatusIdCommitted, statusId);
        crudService.flushAndClear();

        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED, (Product) null);
        crudService.flushAndClear();

        // Because there is a mkt_seg with a code matching the mappedCode of the AnalyticalMarketSegment, we will have
        // added a MktSegDetailsProposed for it.
        assertOnMarketSegmentDetailsProposedRowCount(1);

    }

    @Test
    public void updateAttributeFromQualifiedNonBlockLinkedYieldableToQualifiedNonBlockNotLinkedNonYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMasterAfterUpdate(MARKET_SEGMENT_5 + "_QN", BusinessTypeTransient, NonYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevelAfterUpdate(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 +
                        "_QN", attQualifiedNonBlockNonLinkedNonYieldable,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentDetailsProposedRowCount(0);
    }

    @Test
    public void updateFencedAndPackageToGroup() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        String mktCode = assignedMktSeg.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeGroup, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attGroup, RateCodeTypeEnum.ALL);
    }

    @Test
    public void deleteFencedAndPackage() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);

        AnalyticalMarketSegmentService spyService = spy(service);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        spyService.unassignMarketSegments(Collections.singletonList(assignedMktSeg.get(0)), false);

        crudService.flushAndClear();
        assertOnAnalyticalMarketSegmentRowCount(0);
        assertOnMarketSegmentMasterRowCount(0);
    }

    @Test
    public void updateFencedAndPackageToGroupWhenEntryPresentUnderMktSegmentDetails() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetails(insertQuery2, mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, Fenced, Package, NonLink, NonPriceByBar, processStatusIdCommitted, statusId);

        AnalyticalMarketSegmentService spyService = spy(service);
        spyService.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, (Product) null);

        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeGroup, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, NonPriceByBar);
    }

    @Test
    public void updateFencedAndPackageToGroupWhenEntryNotPresentUnderMktSegmentDetailsButPresentInMktSegmentDetailsProposed() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetailsProposed(insertQuery2, mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, Fenced, Package, NonLink, NonPriceByBar, processStatusUpdated, statusId);

        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeGroup, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, NonPriceByBar);
    }

    @Test
    public void updateGroupToPriceByBar() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);
        crudService.flushAndClear();
        String mktCode = assignedMktSeg.get(0).getMarketCode();
        crudService.flushAndClear();
        int BookingBlockPercentage1 = 0;
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Unqualified, NonFenced, NonPackage, NonLink, PriceByBar, BookingBlockPercentage1, IsEditable);
        assertOnAnalyticalMarketSegment(attEqualsToBar, RateCodeTypeEnum.ALL);
        assertOnMarketSegmentDetailsProposedRowCount(0);
    }


    @Test
    public void deleteGroupMS() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(assignedMktSeg.get(0)), false);

        crudService.flushAndClear();
        assertOnAnalyticalMarketSegmentRowCount(0);
        assertOnMarketSegmentMasterRowCount(0);
    }

    @Test
    public void updateGroupToPriceByBarWhenEntryPresentUnderMktSegmentDetails() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetails(insertQuery2, mktId, BusinessTypeGroup, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, processStatusIdCommitted, statusId);

        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, PriceByBar);
    }

    @Test
    public void updateGroupToPriceByBarWhenEntryNotPresentUnderMktSegmentDetailsButPresentInMktSegmentDetailsProposed() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetailsProposed(insertQuery2, mktId, BusinessTypeGroup, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, processStatusUpdated, statusId);

        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, PriceByBar);
    }

    @Test
    public void updateTransientBlockLinkedToTransientBlockNonLinked() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        setStageAsOneWayToH1();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_LINKED, forecastActivityTypeId);
        crudService.flushAndClear();

        String mktCode = unassigned.get(0).getMarketCode();

        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, Link, NonPriceByBar, BOOKING_BLOCK_PCT_IS_BLOCK, IsEditable);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        assignedMktSeg.get(0).setBlockPercent(0);
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, (Product) null);
        crudService.flushAndClear();

        mktCode = assignedMktSeg.get(0).getMarketCode();

        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, BOOKING_BLOCK_PCT_IS_BLOCK, IsEditable);
        assertOnAnalyticalMarketSegment(attTransientBlockNonLinked, RateCodeTypeEnum.ALL);
        assertOnMarketSegmentDetailsProposedRowCount(0);
    }

    @Test
    public void updateTransientBlockLinkedToTransientBlockNonLinkedWhenEntryPresentUnderMktSegmentDetails() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_LINKED, forecastActivityTypeId);
        unassigned.get(0).setBlockPercent(60);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        assignedMktSeg.get(0).setBlockPercent(0);
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetails(insertQuery2, mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, Link, NonPriceByBar, processStatusIdCommitted, statusId);
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, NonPriceByBar);
    }

    @Test
    public void updateTransientBlockLinkedToTransientBlockNonLinkedWhenEntryNotPresentUnderMktSegmentDetailsButPresentInMktSegmentDetailsProposed() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
//        setStageAsOneWayToH1();
        StringBuilder insertQuery1 = new StringBuilder();
        StringBuilder insertQuery2 = new StringBuilder();
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(Collections.singletonList(unassigned.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_LINKED, forecastActivityTypeId);
        unassigned.get(0).setBlockPercent(0);
        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        assignedMktSeg.get(0).setBlockPercent(0);
        crudService.flushAndClear();
        populateMarketSegment(insertQuery1, assignedMktSeg.get(0).getMarketCode());
        String mktId = getMktId(assignedMktSeg.get(0).getMarketCode());
        populateMarketSegmentDetailsProposed(insertQuery2, mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, Link, NonPriceByBar, processStatusUpdated, statusId);
        service.updateAssignedMarketSegments(Collections.singletonList(assignedMktSeg.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, (Product) null);
        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        assertOnMarketSegmentDetailsProposed(mktId, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified,
                BookingBlockPercentage, NonFenced, NonPackage, NonLink, processStatusUpdated, statusId, NonPriceByBar);
    }

    @Test
    public void getUnassignedIndividualMarketSegments() {
        List<AnalyticalMarketSegmentSummary> results = service.getUnassignedIndividualMarketSegments();
        assertNotNull(results);

        for (AnalyticalMarketSegmentSummary summary : results) {
            System.out.println(summary);
            assertFalse(summary.isGroupMs());

            switch (summary.getMarketCode()) {
                case MARKET_SEGMENT_1:
                case MARKET_SEGMENT_2:
                    assertEquals(new Double(360), summary.getRoomRevenue());
                    assertEquals(new Integer(3), summary.getRoomsSold());
                    assertEquals(new Double(120), summary.getAverageDailyRate());
                    assertEquals(new Double(0.375), summary.getHotelPercent());
                    break;
                case MARKET_SEGMENT_4:
                case MARKET_SEGMENT_5:
                    assertEquals(new Double(120), summary.getRoomRevenue());
                    assertEquals(new Integer(1), summary.getRoomsSold());
                    assertEquals(new Double(120), summary.getAverageDailyRate());
                    assertEquals(new Double(0.125), summary.getHotelPercent());
                    break;
                default:
                    assertEquals(new Double(0), summary.getRoomRevenue());
                    assertEquals(new Integer(0), summary.getRoomsSold());
                    assertEquals(null, summary.getAverageDailyRate());
                    assertEquals(new Double(0.0), summary.getHotelPercent());
                    break;
            }
        }

        assertEquals(5, results.size());
    }

    @Test
    public void getUnassignedSharedMarketSegments() {
        List<AnalyticalMarketSegmentSummary> results = service.getUnassignedSharedMarketSegments();
        assertNotNull(results);

        for (AnalyticalMarketSegmentSummary summary : results) {
            assertFalse(summary.isGroupMs());

            switch (summary.getMarketCode()) {
                case MARKET_SEGMENT_1:
                case MARKET_SEGMENT_2:
                    assertEquals(new Double(360), summary.getRoomRevenue());
                    assertEquals(new Integer(3), summary.getRoomsSold());
                    assertEquals(new Double(120), summary.getAverageDailyRate());
                    assertEquals(new Double(0.375), summary.getHotelPercent());
                    break;
                case MARKET_SEGMENT_4:
                case MARKET_SEGMENT_5:
                    assertEquals(new Double(120), summary.getRoomRevenue());
                    assertEquals(new Integer(1), summary.getRoomsSold());
                    assertEquals(new Double(120), summary.getAverageDailyRate());
                    assertEquals(new Double(0.125), summary.getHotelPercent());
                    break;
                default:
                    assertEquals(new Double(0), summary.getRoomRevenue());
                    assertEquals(new Integer(0), summary.getRoomsSold());
                    assertNull(summary.getAverageDailyRate());
                    assertEquals(new Double(0.0), summary.getHotelPercent());
                    break;
            }
        }

        assertEquals(5, results.size());
    }

    @Test
    public void getUnassignedSharedMarketSegments_NO_ROOMS_SOLD() {
        tenantCrudService().executeUpdateByNativeQuery("update Ams_Occupancy_Summary set ROOMS_SOLD=0");
        List<AnalyticalMarketSegmentSummary> results = service.getUnassignedSharedMarketSegments();
        assertNotNull(results);

        for (AnalyticalMarketSegmentSummary summary : results) {
            assertFalse(summary.isGroupMs());

            switch (summary.getMarketCode()) {
                case MARKET_SEGMENT_1:
                case MARKET_SEGMENT_2:
                    assertEquals(new Double(360), summary.getRoomRevenue());
                    assertEquals(new Integer(0), summary.getRoomsSold());
                    assertNull(summary.getAverageDailyRate());
                    assertEquals(new Double(0.0), summary.getHotelPercent());
                    break;
                case MARKET_SEGMENT_4:
                case MARKET_SEGMENT_5:
                    assertEquals(new Double(120), summary.getRoomRevenue());
                    assertEquals(new Integer(0), summary.getRoomsSold());
                    assertNull(summary.getAverageDailyRate());
                    assertEquals(new Double(0.0), summary.getHotelPercent());
                    break;
                default:
                    assertEquals(new Double(0), summary.getRoomRevenue());
                    assertEquals(new Integer(0), summary.getRoomsSold());
                    assertNull(summary.getAverageDailyRate());
                    assertEquals(new Double(0.0), summary.getHotelPercent());
                    break;
            }
        }

        assertEquals(5, results.size());
    }

    @Test
    public void rebuild() {
        when(marketSegmentPopulationResource.startDataLoad("5")).thenReturn(5L);
        assertEquals("Starting data load", service.rebuild());
        verify(marketSegmentPopulationResource).startDataLoad("5");
    }

    @Test
    public void assignmentLifecycle() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // get all unassigned
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedGroupMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        // assign all of them to group and verify
        unassigned.get(0).setMappedCode("SOME_CODE");
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId, false, null);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedGroupMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.GROUP, summary.getAttribute());
            MarketSegmentMaster msm = getMasterByMappedCode(summary.getMappedMarketCode());
            assertNotNull(msm);
            assertEquals(new Integer(100), msm.getBookingBlockPc());
            assertEquals(ONE, msm.getIsEditable());
        }

        // remove 1 assignment and verify
        service.unassignMarketSegments(Collections.singletonList(assigned.get(0)), false);
        assertEquals(1, service.getUnassignedGroupMarketSegments().size());
        assertEquals(assigned.size() - 1, service.getAssignedMarketSegments().size());

        // remove the rest of the assignments
        service.clearAssignments(TestProperty.H1.getPaddedId());
        assertEquals(unassigned.size(), service.getUnassignedGroupMarketSegments().size());
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));
    }

    @Test
    public void assignAttributeAsGroup() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId, false, null);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.GROUP, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeForGroupDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 100;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeGroup, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attGroup, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
        service.populateYieldCategoryByRule();
        assertOnYieldCategoryByRuleRowCount(0);
    }

    @Test
    public void assignAttributeAsPricedByBar() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();

        service.unassignMarketSegments(Collections.singletonList(assigned.get(0)), false);
        assertEquals(1, service.getUnassignedIndividualMarketSegments().size());
        assertEquals(assigned.size() - 1, service.getAssignedMarketSegments().size());

        service.clearAssignments(TestProperty.H1.getPaddedId());
        assertEquals(unassigned.size(), service.getUnassignedIndividualMarketSegments().size());
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));
    }

    @Test
    public void assignAttributeAsPricedByBarForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Unqualified, NonFenced, NonPackage, NonLink, PriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attEqualsToBar, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }


    @Test
    public void assignAttributeAsPricedByBarForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        MktSeg newMktSeg = UniqueMktSegCreator.createUniqueMktSeg(MARKET_SEGMENT_5, TestProperty.H1.getId());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId, true, null);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5_DEF)),
                UNFENCED_NON_PACKAGED, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, PriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, attEqualsToBar,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        assertTrue(crudService.find(MktSeg.class, newMktSeg.getId()).isComplimentary());
    }

    @Test
    public void assignAttributeAsUnFencedNonPackagedForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                UNFENCED_NON_PACKAGED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_U", BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_U",
                attUnfencedNonPackaged,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    /**
     * This test creates multiple rate codes and a default AMS that also gets created in the Market Segment Master
     * when all of the rate codes are deleted, we need to delete the default and the market segment master records
     * (including default)
     */
    @Test
    public void defaultMktSegForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // Create a MktSeg with the correct code for the MktSegDetailsProposed later
        createMktSegIfNeeded(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_5);

        // Look for the master by market code which should not exist
        MarketSegmentMaster marketSegmentMaster = getMasterByMappedCode(MARKET_SEGMENT_5);
        assertNull(marketSegmentMaster);

        AnalyticalMarketSegmentSummary assignment1 =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment1 != null;
        assignment1.setRateCode(RATE_CODE_1);

        AnalyticalMarketSegmentSummary assignment2 =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment2 != null;
        assignment2.setRateCode(RATE_CODE_2);

        AnalyticalMarketSegmentSummary assignment3 =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment3 != null;
        assignment3.setRateCode(RATE_CODE_3);

        AnalyticalMarketSegmentSummary defaultMarketSegment = createDefaultMarketSegment(MARKET_SEGMENT_5);

        service.assignMarketSegments(Collections.singletonList(assignment1),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(assignment2),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(assignment3),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        service.assignMarketSegments(Collections.singletonList(defaultMarketSegment),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(findAnalyticalMarketSegmentByMappedMarketCode(assignedMktSeg, assignment1.getMappedCode())), false);
        crudService.flushAndClear();
        assertOnDefaultMarketSegmentCount(1);
        assertOnDefaultMarketSegmentCountInAnalyticalMS(1);

        assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(findAnalyticalMarketSegmentByMappedMarketCode(assignedMktSeg, assignment2.getMappedCode())), false);
        crudService.flushAndClear();
        assertOnDefaultMarketSegmentCount(1);
        assertOnDefaultMarketSegmentCountInAnalyticalMS(1);

        assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(findAnalyticalMarketSegmentByMappedMarketCode(assignedMktSeg, assignment3.getMappedCode())), false);
        crudService.flushAndClear();
        assertOnDefaultMarketSegmentCount(0);
        assertOnDefaultMarketSegmentCountInAnalyticalMS(0);

        //We should find no market segment master entriest for this market code.
        marketSegmentMaster = getMasterByMappedCode(MARKET_SEGMENT_5);
        assertNull(marketSegmentMaster, "MarketSegmentMaster was not deleted");

        // Assert that the SegmentDetailsProposed was deleted.
        MktSeg mktSegment = getMktSegByCode(MARKET_SEGMENT_5);
        assertNotNull(mktSegment, "MktSeg was not found");
        MktSegDetailsProposed mktSegDetailsProposed = this.getMktSegDetailsProposedByMktSeg(mktSegment);
        assertNull(mktSegDetailsProposed, "MktSegDetailsProposed should not exist for MktSeg");

        // Create a new MktSeg without MktSegDetails or MktSegDetailsProposed
        MktSeg mktSegNew = insertMktSeg(TestProperty.H1.getId(), MARKET_SEGMENT_8_NEW, MARKET_SEGMENT_8_NEW,
                MARKET_SEGMENT_8_NEW);

        Map<String, Object> parameters = QueryParameter.with("id", mktSegNew.getId()).and("propertyId",
                TestProperty.H1.getId()).parameters();

        mktSegDetailsProposed = crudService.findByNamedQuerySingleResult(MktSegDetailsProposed.BY_MKT_SEG_ID,
                parameters);
        assertNull(mktSegDetailsProposed, "MktSegDetailsProposed should not yet exist");

        defaultMarketSegment = createDefaultMarketSegment(MARKET_SEGMENT_8_NEW);

        // assignMargetSegments should create the MktSegDetailsProposed for the mktSegNew
        service.assignMarketSegments(Collections.singletonList(defaultMarketSegment),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        parameters =
                QueryParameter.with("id", mktSegNew.getId()).and("propertyId", TestProperty.H1.getId()).parameters();
        mktSegDetailsProposed = crudService.findByNamedQuerySingleResult(MktSegDetailsProposed.BY_MKT_SEG_ID,
                parameters);

        assertNotNull(mktSegDetailsProposed, "MktSegDetailsProposed not found");
    }

    /**
     * Test that attributing a market segment in AMS causes a marget segment detail proposed for an existing market
     * segment in the market segment screen which did not have any market segment details .
     */
    @Test
    public void assignMktSegDetailsProposedForAmsAssignment() {

        service.clearAssignments(TestProperty.H1.getPaddedId());

        // Create a MktSeg with the correct code for the MktSegDetailsProposed later
        insertMktSeg(TestProperty.H1.getId(), MARKET_SEGMENT_5, MARKET_SEGMENT_5, MARKET_SEGMENT_5);

        crudService.flushAndClear();

        // Look for the master by market code which should not exist
        MarketSegmentMaster marketSegmentMaster = getMasterByMappedCode(MARKET_SEGMENT_5);
        assertNull(marketSegmentMaster);

        AnalyticalMarketSegmentSummary assignment1 =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment1 != null;

        MktSeg mktSeg = findMktSegByCode(assignment1.getMarketCode());
        assertNotNull(mktSeg, "Market Segment not found");

        assertNull(mktSeg.getMktSegDetails(), "Market Segment should not have details");
        assertNull(mktSeg.getMktSegDetailsProposed(), "Market Segment should not have details proposed");

        // assignMargetSegments should create the MktSegDetailsProposed for the mktSeg
        service.assignMarketSegments(Collections.singletonList(assignment1),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        crudService.flushAndClear();

        mktSeg = findMktSegByCode(assignment1.getMarketCode());

        assertNull(mktSeg.getMktSegDetails(), "Market Segment details should not have details");
        assertNotNull(mktSeg.getMktSegDetailsProposed(), "Market Segment details proposed should have details " +
                "proposed");
    }

    /**
     * Test that updating a market segment in AMS causes a marget segment detail proposed to be created or updated for
     * an existing market segment
     */
    @Test
    public void updateMktSegWhenUpdatingAms() {

        // Clear any existing AnalyticalMarketSegments and MarketSegmentMasters
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // Create a straight MktSeg with the MARKET_SEGMENT_5 code
        insertMktSeg(TestProperty.H1.getId(), MARKET_SEGMENT_5, MARKET_SEGMENT_5, MARKET_SEGMENT_5);
        crudService.flushAndClear();

        // Look for the MarketSegmentMaster by market code which should not yet exist
        MarketSegmentMaster marketSegmentMaster = getMasterByMappedCode(MARKET_SEGMENT_5);
        assertNull(marketSegmentMaster);

        AnalyticalMarketSegmentSummary amsMktSegSummary5 =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert amsMktSegSummary5 != null;

        // Find the MktSeg for MARKET_SEGMENT_5 which we inserted previously
        MktSeg mktSeg5 = findMktSegByCode(amsMktSegSummary5.getMarketCode());
        assertNotNull(mktSeg5, "Market Segment not found");
        assertNull(mktSeg5.getMktSegDetails(), "Market Segment should not have details");
        assertNull(mktSeg5.getMktSegDetailsProposed(), "Market Segment should not have details proposed");

        // assignMargetSegments should create the MktSegDetailsProposed for the mktSeg5
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary5),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();

        // Find the MktSeg for MARKET_SEGMENT_5 which should have been updated during assignMarketSegments
        mktSeg5 = findMktSegByCode(MARKET_SEGMENT_5);
        assertNull(mktSeg5.getMktSegDetails(), "Market Segment details should not have details");
        assertNotNull(mktSeg5.getMktSegDetailsProposed(),
                "Market Segment details proposed should have details proposed");
        assertTrue(mktSeg5.getMktSegDetailsProposed().getPriceByBar() == 1,
                "market seg details proposed should be pricedByBar");

        // Clear all AnalyticalMarketSegments and MarketSegmentMasters (NOTE: this leave MktSegDetailsProposed in place)
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // Create a straight MktSeg with the _DEF code
        insertMktSeg(TestProperty.H1.getId(), MARKET_SEGMENT_5_DEF, MARKET_SEGMENT_5_DEF, MARKET_SEGMENT_5_DEF);
        crudService.flushAndClear();

        // Look for the MarketSegmentMaster by market code which should not exist
        marketSegmentMaster = getMasterByMappedCode(MARKET_SEGMENT_5);
        assertNull(marketSegmentMaster);

        // Look for the MarketSegmentMaster by market _DEF code which should not exist
        MarketSegmentMaster marketSegmentMasterDef = getMasterByMappedCode(MARKET_SEGMENT_5_DEF);
        assertNull(marketSegmentMasterDef);

        amsMktSegSummary5 = findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert amsMktSegSummary5 != null;

        // Create the _DEF amsSummary
        AnalyticalMarketSegmentSummary amsMktSegSummary5Def = new AnalyticalMarketSegmentSummary();
        amsMktSegSummary5Def.setMarketCode(MARKET_SEGMENT_5);
        amsMktSegSummary5Def.setMappedCode(MARKET_SEGMENT_5_DEF);
        amsMktSegSummary5Def.setRateCodeType(RateCodeTypeEnum.DEFAULT.name());

        // Look for the straight MktSeg (_DEF) by the mappedCode
        MktSeg mktSegDef = findMktSegByCode(amsMktSegSummary5Def.getMappedCode());
        // The straight MktSeg should be found
        assertNotNull(mktSegDef, "Market Segment not found");
        assertNull(mktSegDef.getMktSegDetails(), "Market Segment should not have details");
        assertNull(mktSegDef.getMktSegDetailsProposed(), "Market Segment should not have details proposed");

        // assignMargetSegments for mktSeg5Def should also create the MktSegDetailsProposed for the mktSeg5
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary5Def),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        crudService.flushAndClear();

        mktSeg5 = findMktSegByCode(amsMktSegSummary5.getMarketCode());
        assertNull(mktSeg5.getMktSegDetails(), "Market Segment details should not have details");
        assertNotNull(mktSeg5.getMktSegDetailsProposed(), "Market Segment details proposed should have details " +
                "proposed");
        assertTrue(mktSeg5.getMktSegDetailsProposed().getFenced() == 1, "market seg details proposed should be fenced");

        MktSeg mktSeg5Def = findMktSegByCode(amsMktSegSummary5Def.getMappedCode());
        assertNull(mktSeg5Def.getMktSegDetails(), "Market Segment details should not have details");
        assertNotNull(mktSeg5Def.getMktSegDetailsProposed(), "Market Segment details proposed should have details " +
                "proposed");
        assertTrue(mktSeg5Def.getMktSegDetailsProposed().getPriceByBar() == 0,
                "market seg details proposed should not be pricedByBar");
        assertTrue(mktSeg5Def.getMktSegDetailsProposed().getFenced() == 1,
                "market seg details proposed should be fenced");
    }

    private void createMktSegIfNeeded(AnalyticalMarketSegmentAttribute analyticalMarketSegmentAttribute,
                                      String codeIn) {
        MktSeg mktSeg = getMktSegByCode(codeIn);

        if (mktSeg == null) {
            this.addMktSegWithDetail(analyticalMarketSegmentAttribute, codeIn, 40);
        }
    }

    private AnalyticalMarketSegment findAnalyticalMarketSegmentByMappedMarketCode(List<AnalyticalMarketSegment> analyticalMarketSegments, String mappedMarketCode) {
        for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegments) {
            if (mappedMarketCode.equals(analyticalMarketSegment.getMappedMarketCode())) {
                return analyticalMarketSegment;
            }
        }
        return null;
    }

    @Test
    public void assignAttributeAsFenced() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.FENCED, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsFencedForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Unqualified, Fenced, NonPackage, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attFenced, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
        service.populateYieldCategoryByRule();
        assertOnYieldCategoryByRuleRowCount(0);
    }

    @Test
    public void assignAttributeAsFencedForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);

        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment), AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified, Fenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, attFenced, RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        service.populateYieldCategoryByRule();
        assertOnYieldCategoryByRuleRowCount(2);
    }

    @Test
    public void assignAttributeAsPackaged() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.PACKAGED, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.PACKAGED, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void getSharedRateCodesInThisMarketSegment() {
        int count = service.getSharedRateCodesInThisMarketSegment(MARKET_SEGMENT_1);
        assertEquals(3, count);
    }

    @Test
    public void getSharedRateCodesInThisMarketSegment_NO_MS() {
        int count = service.getSharedRateCodesInThisMarketSegment("NO_SUCH_MS");
        assertEquals(0, count);
    }

    @Test
    public void assignAttributeAsPackageForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.PACKAGED, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Unqualified, NonFenced, Package, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attPackaged, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsPackagedAndFenced() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsPackageAndFencedForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Unqualified, Fenced, Package, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attFencedAndPackaged, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsPackageAndFencedForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_UFP", BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified, Fenced, Package, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_UFP",
                attFencedAndPackaged,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsUnFencedAndNonPackaged() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, UNFENCED_NON_PACKAGED,
                forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(UNFENCED_NON_PACKAGED, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE
                , forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedYieldableForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE
                , forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, Link, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attQualifiedNonBlockLinkedYieldable, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedYieldableForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QYL", BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, Link, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QYL",
                attQualifiedNonBlockLinkedYieldable,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedSemiYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE,
                    summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedSemiYieldableForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, SemiYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, Link, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attQualifiedNonBlockLinkedSemiYieldable, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockLinkedSemiYieldableForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QSL", BusinessTypeTransient, SemiYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, Link, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QSL",
                attQualifiedNonBlockLinkedSemiYieldable,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE,
                    summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedYieldableForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attQualifiedNonBlockNonLinkedYieldable, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedYieldableForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QY", BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QY",
                attQualifiedNonBlockNonLinkedYieldable,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedSemiYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE,
                    summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedSemiYieldableForMarketSegmentLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, SemiYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attQualifiedNonBlockNonLinkedSemiYieldable, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedSemiYieldableForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QS", BusinessTypeTransient, SemiYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QS",
                attQualifiedNonBlockNonLinkedSemiYieldable, RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedNonYieldable() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));

        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE,
                    summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedNonYieldableForMarketSegmentLevelDataTest() {

        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassigned,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        String mktCode = unassigned.get(0).getMarketCode();
        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, NonYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegment(attQualifiedNonBlockNonLinkedNonYieldable, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void assignAttributeAsQualifiedNonBlockNonLinkedNonYieldableForRateCodeLevelDataTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QN", BusinessTypeTransient, NonYieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QN",
                attQualifiedNonBlockNonLinkedNonYieldable, RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
    }

    @Test
    public void assignAttributeAsTransientBlockNonLinked() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));
        for (AnalyticalMarketSegmentSummary summary : unassigned) {
            summary.setBlockPercent(50);
        }

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(RateCodeTypeEnum.ALL, summary.getRateCodeType());
            assertEquals(RateCodeTypeEnum.ALL.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @Test
    public void assignAttributeAsTransientBlockNonLinkedForMarketSegmentLevelDataTest() {

        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        for (AnalyticalMarketSegmentSummary summary : unassigned) {
            summary.setBlockPercent(0);
        }
        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                forecastActivityTypeId);
        crudService.flushAndClear();
        String mktCode = unassigned.get(0).getMarketCode();

        assertOnMarketSegmentMaster(mktCode, BusinessTypeTransient, Yieldable, ForecastActivityTypeForDemandAndWash,
                Qualified, NonFenced, NonPackage, NonLink, NonPriceByBar, BOOKING_BLOCK_PCT_IS_BLOCK, IsEditable);
        assertOnAnalyticalMarketSegment(attTransientBlockNonLinked, RateCodeTypeEnum.ALL);
        assertOnDefaultMarketSegmentCount(0);
    }

    @Test
    public void advancedMappingEquals() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        // assign and verify
        AnalyticalMarketSegmentSummary assignment = service.getUnassignedIndividualMarketSegments().get(0);
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        crudService.flush();

        String marketCode = assignment.getMarketCode();

        List<AnalyticalMarketSegmentSummary> group = service.getUnassignedGroupMarketSegments();
        assertFalse(contains(group, marketCode));

        List<AnalyticalMarketSegmentSummary> individual = service.getUnassignedIndividualMarketSegments();
        assertFalse(contains(individual, marketCode));

        List<AnalyticalMarketSegmentSummary> shared = service.getUnassignedSharedMarketSegments();
        assertTrue(contains(shared, marketCode));

        assertEquals(new Double(120.0), shared.get(0).getAverageDailyRate());
        assertEquals(new Double(120.0), shared.get(1).getAverageDailyRate());
        assertEquals(new Double(120.0), shared.get(2).getAverageDailyRate());
        assertEquals(new Double(120.0), shared.get(3).getAverageDailyRate());
        assertEquals(null, shared.get(4).getAverageDailyRate());
    }

    @Test
    public void advancedMappingStartsWith() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();

        // assign and verify
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4);
        assert assignment != null;
        assignment.setRateCode("R");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_4)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4));
        assertTrue(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_4));

        List<AnalyticalMarketSegment> found = service.getAssignedMarketSegments();
        assertEquals(2, found.size());
        assertEquals(RateCodeTypeEnum.STARTS_WITH.getRank(), found.get(0).getRank().intValue());
        assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, found.get(0).getAttribute());

        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();
        assignment.setRateCode("B");
        assignment.setRateCodeType(RateCodeTypeEnum.STARTS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_4));
    }

    @Test
    public void findExactChangeInAllMktSegTest() {
        List<MktSegDetails> mktSegDetailsList = crudService.findByNamedQuery(MktSegDetails.GET_ALL);

        MktSegChangeType exactChangeInMktSeg = service.findExactChangeInAllMktSeg();
        assertEquals(MktSegChangeType.NO_CHANGE, exactChangeInMktSeg);

        MktSegDetails mktSegDetails = mktSegDetailsList.get(0);
        MktSegDetailsProposed mktSegDetailsProposed = createMktSegDetailsProposed(mktSegDetails);

        mktSegDetailsProposed.setLink(2);
        crudService.save(mktSegDetailsProposed);
        crudService.flushAndClear();
        exactChangeInMktSeg = service.findExactChangeInAllMktSeg();
        assertEquals(MktSegChangeType.SECONDARY_ATTRIBUTE, exactChangeInMktSeg);

        mktSegDetailsProposed.setBusinessType(mktSegDetailsList.get(8).getBusinessType());
        crudService.save(mktSegDetailsProposed);
        crudService.flushAndClear();
        exactChangeInMktSeg = service.findExactChangeInAllMktSeg();
        assertEquals(MktSegChangeType.BUSINESSTYPE, exactChangeInMktSeg);
    }

    @Test
    public void findExactChangeInAllMktSegTest_forecastActivityTypeChange() {
        List<MktSegDetails> mktSegDetailsList = crudService.findByNamedQuery(MktSegDetails.GET_ALL);

        MktSegChangeType exactChangeInMktSeg = service.findExactChangeInAllMktSeg();
        assertEquals(MktSegChangeType.NO_CHANGE, exactChangeInMktSeg);

        MktSegDetails mktSegDetails = mktSegDetailsList.get(0);
        MktSegDetailsProposed mktSegDetailsProposed = createMktSegDetailsProposed(mktSegDetails);
        ForecastActivityType demandAndWash = crudService.findByNamedQuerySingleResult(ForecastActivityType.BY_ID,
                QueryParameter.with("id", ForecastActivityType.DEMAND_AND_WASH).parameters());

        assertNotNull(demandAndWash);
        assertFalse(demandAndWash.equals(mktSegDetails.getForecastActivityType()));
        mktSegDetailsProposed.setForecastActivityType(demandAndWash);
        crudService.save(mktSegDetailsProposed);
        crudService.flushAndClear();
        exactChangeInMktSeg = service.findExactChangeInAllMktSeg();
        assertEquals(MktSegChangeType.SECONDARY_ATTRIBUTE, exactChangeInMktSeg);
    }

    @Test
    public void findExactChangeInMktSegTest() {
        List<MktSegDetails> mktSegDetailsList = crudService.findByNamedQuery(MktSegDetails.GET_ALL);

        //new mktSeg
        MktSegChangeType exactChangeInMktSeg = service.findExactChangeInGivenMktSeg("COMP");
        assertEquals(MktSegChangeType.NO_CHANGE, exactChangeInMktSeg);
        MktSegDetails mktSegDetails1 = mktSegDetailsList.get(0);
        MktSegDetails mktSegDetails = createMktSegDetails(mktSegDetails1);
        MktSegDetailsProposed mktSegDetailsProposed = createMktSegDetailsProposed(mktSegDetails);
        mktSegDetailsProposed.setLink(2);
        crudService.save(mktSegDetailsProposed);
        crudService.flushAndClear();
        exactChangeInMktSeg = service.findExactChangeInGivenMktSeg(mktSegDetails.getMktSeg().getCode());
        assertEquals(MktSegChangeType.SECONDARY_ATTRIBUTE, exactChangeInMktSeg);
    }

    private MktSegDetails createMktSegDetails(MktSegDetails mktSegDetails1) {
        MktSegDetails mktSegDetails = new MktSegDetails();
        mktSegDetails.setBusinessType(mktSegDetails1.getBusinessType());
        mktSegDetails.setYieldType(mktSegDetails1.getYieldType());
        mktSegDetails.setForecastActivityType(mktSegDetails1.getForecastActivityType());
        mktSegDetails.setQualified(mktSegDetails1.getQualified());
        mktSegDetails.setBookingBlockPc(mktSegDetails1.getBookingBlockPc());
        mktSegDetails.setFenced(mktSegDetails1.getFenced());
        mktSegDetails.setPackageValue(mktSegDetails1.getPackageValue());
        mktSegDetails.setTemplateId(mktSegDetails1.getTemplateId());
        mktSegDetails.setTemplateDefault(mktSegDetails1.getTemplateDefault());
        mktSegDetails.setProcessStatus(mktSegDetails1.getProcessStatus());
        mktSegDetails.setStatusId(mktSegDetails1.getStatusId());
        mktSegDetails.setLink(mktSegDetails1.getLink());
        mktSegDetails.setOffsetType(mktSegDetails1.getOffsetType());
        mktSegDetails.setOffsetValue(mktSegDetails1.getOffsetValue());
        mktSegDetails.setPriceByBar(mktSegDetails1.getPriceByBar());
        mktSegDetails.setCreateDate(new Timestamp(new Date().getTime()));
        mktSegDetails.setCreateDate(new Date());
        mktSegDetails.setCreatedByUserId(0);
        mktSegDetails.setLastUpdatedDate(new Date());
        mktSegDetails.setLastUpdatedDate(new Date());
        mktSegDetails.setLastUpdatedByUserId(0);
        mktSegDetails.setMktSeg(createMktSeg("Test"));
        return mktSegDetails;
    }

    private MktSeg createMktSeg(String mktSegCode) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setCode(mktSegCode);
        mktSeg.setEditable(1);
        mktSeg.setDescription(mktSegCode);
        mktSeg.setName(mktSegCode);
        mktSeg.setStatusId(1);
        mktSeg.setPropertyId(5);
        return mktSeg;
    }

    private MktSegDetailsProposed createMktSegDetailsProposed(MktSegDetails mktSegDetails) {
        MktSegDetailsProposed mktSegDetailsProposed = new MktSegDetailsProposed();
        mktSegDetailsProposed.setMktSeg(mktSegDetails.getMktSeg());
        mktSegDetailsProposed.setBusinessType(mktSegDetails.getBusinessType());
        mktSegDetailsProposed.setYieldType(mktSegDetails.getYieldType());
        mktSegDetailsProposed.setForecastActivityType(mktSegDetails.getForecastActivityType());
        mktSegDetailsProposed.setQualified(mktSegDetails.getQualified());
        mktSegDetailsProposed.setBookingBlockPc(mktSegDetails.getBookingBlockPc());
        mktSegDetailsProposed.setFenced(mktSegDetails.getFenced());
        mktSegDetailsProposed.setPackageValue(mktSegDetails.getPackageValue());
        mktSegDetailsProposed.setLink(mktSegDetails.getLink());
        mktSegDetailsProposed.setPriceByBar(mktSegDetails.getPriceByBar());
        mktSegDetailsProposed.setTemplateId(mktSegDetails.getTemplateId());
        mktSegDetailsProposed.setTemplateDefault(mktSegDetails.getTemplateDefault());
        mktSegDetailsProposed.setProcessStatus(new ProcessStatus());
        mktSegDetailsProposed.setStatusId(mktSegDetails.getStatusId());
        mktSegDetailsProposed.setOffsetType(mktSegDetails.getOffsetType());
        mktSegDetailsProposed.setOffsetValue(mktSegDetails.getOffsetValue());
        mktSegDetailsProposed.setCreateDate(new Timestamp(new Date().getTime()));
        mktSegDetailsProposed.setCreateDate(new Date());
        mktSegDetailsProposed.setCreatedByUserId(0);
        mktSegDetailsProposed.setLastUpdatedDate(new Timestamp(new Date().getTime()));
        mktSegDetailsProposed.setLastUpdatedDate(new Date());
        mktSegDetailsProposed.setLastUpdatedByUserId(0);
        mktSegDetailsProposed.setProcessStatus(mktSegDetails.getProcessStatus());
        return mktSegDetailsProposed;
    }

    @Test
    public void advancedMappingEndsWith() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();

        // assign and verify
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4);
        assert assignment != null;
        assignment.setRateCode("1");
        assignment.setRateCodeType(RateCodeTypeEnum.ENDS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_4)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4));
        assertTrue(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_4));

        List<AnalyticalMarketSegment> found = service.getAssignedMarketSegments();
        assertEquals(2, found.size());
        assertEquals(RateCodeTypeEnum.ENDS_WITH.getRank(), found.get(0).getRank().intValue());
        assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, found.get(0).getAttribute());

        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();
        assignment.setRateCode("A");
        assignment.setRateCodeType(RateCodeTypeEnum.ENDS_WITH.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_4));
        assertFalse(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_4));
    }

    @Test
    public void advancedMappingContains() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();

        // assign and verify
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode("A");
        assignment.setRateCodeType(RateCodeTypeEnum.CONTAINS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_5));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5));
        assertTrue(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_5));

        List<AnalyticalMarketSegment> found = service.getAssignedMarketSegments();
        assertEquals(2, found.size());
        assertEquals(RateCodeTypeEnum.CONTAINS.getRank(), found.get(0).getRank().intValue());
        assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, found.get(0).getAttribute());

        service.clearAssignments(TestProperty.H1.getPaddedId());
        crudService.clear();
        assignment.setRateCode("C");
        assignment.setRateCodeType(RateCodeTypeEnum.CONTAINS.toString());
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);

        assertFalse(contains(service.getUnassignedGroupMarketSegments(), MARKET_SEGMENT_5));
        assertFalse(contains(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5));
        assertFalse(contains(service.getUnassignedSharedMarketSegments(), MARKET_SEGMENT_5));
    }

    @Test
    public void assignAttributeFindByMappedMarketCodeLike() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);

        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment), AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Unqualified, Fenced, NonPackage, NonLink, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX, attFenced, RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);

        List<AnalyticalMarketSegment> segments = service.getAssignedMarketSegmentsByMappedCodeLike("FD%");
        assertEquals(2, segments.size());
        assertOnDefaultMarketSegmentCount(1);
        assertNotNull(findAnalyticalMarketSegmentByMappedMarketCode(segments, "FD_UF"));
        assertNotNull(findAnalyticalMarketSegmentByMappedMarketCode(segments, "FD_DEF"));

        segments = service.getAssignedMarketSegmentsByMappedCodeLike("%_DEF");
        assertEquals(1, segments.size());
        assertOnDefaultMarketSegmentCount(1);

        assertNull(findAnalyticalMarketSegmentByMappedMarketCode(segments, "FD_UF"));
        assertNotNull(findAnalyticalMarketSegmentByMappedMarketCode(segments, "FD_DEF"));
    }


    @Test
    public void getRateCodes() {
        List<RateCodeSummary> rateCodesMS1 = service.getRateCodes(MARKET_SEGMENT_1);
        assertEquals(3, rateCodesMS1.size());
        assertEquals(RATE_CODE_1, rateCodesMS1.get(0).getRateCode());
        assertTrue(rateCodesMS1.get(0).getMarketSegments().contains(MARKET_SEGMENT_1));
        assertTrue(rateCodesMS1.get(0).getMarketSegments().contains(MARKET_SEGMENT_2));
        assertTrue(rateCodesMS1.get(0).getMarketSegments().contains(MARKET_SEGMENT_4));
        assertEquals(RATE_CODE_2, rateCodesMS1.get(1).getRateCode());
        assertTrue(rateCodesMS1.get(1).getMarketSegments().contains(MARKET_SEGMENT_1));
        assertTrue(rateCodesMS1.get(1).getMarketSegments().contains(MARKET_SEGMENT_2));
        assertTrue(rateCodesMS1.get(1).getMarketSegments().contains(MARKET_SEGMENT_5));
        assertEquals(RATE_CODE_3, rateCodesMS1.get(2).getRateCode());
        assertTrue(rateCodesMS1.get(2).getMarketSegments().contains(MARKET_SEGMENT_1));
        assertTrue(rateCodesMS1.get(2).getMarketSegments().contains(MARKET_SEGMENT_2));

        for (RateCodeSummary rateCodeSummary : rateCodesMS1) {
            assertEquals(new Integer(1), rateCodeSummary.getRoomsSold());
            assertEquals(new Double(120), rateCodeSummary.getRoomRevenue());
            assertEquals(new Double(120), rateCodeSummary.getAverageDailyRate());
        }

        assertEquals(new Integer(3), rateCodesMS1.get(0).getMarketSegmentCount());
        assertEquals(new Integer(4), rateCodesMS1.get(1).getMarketSegmentCount());
        assertEquals(new Integer(3), rateCodesMS1.get(2).getMarketSegmentCount());

        List<RateCodeSummary> rateCodesMS2 = service.getRateCodes(MARKET_SEGMENT_2);
        assertEquals(3, rateCodesMS2.size());
        assertEquals(RATE_CODE_1, rateCodesMS2.get(0).getRateCode());
        assertEquals(RATE_CODE_2, rateCodesMS2.get(1).getRateCode());
        assertEquals(RATE_CODE_3, rateCodesMS2.get(2).getRateCode());

        for (RateCodeSummary rateCodeSummary : rateCodesMS2) {
            assertEquals(new Integer(1), rateCodeSummary.getRoomsSold());
            assertEquals(new Double(120), rateCodeSummary.getRoomRevenue());
            assertEquals(new Double(120), rateCodeSummary.getAverageDailyRate());
            assertEquals(new Double(.125), rateCodeSummary.getPercent());
        }

        assertEquals(new Integer(3), rateCodesMS2.get(0).getMarketSegmentCount());
        assertEquals(new Integer(4), rateCodesMS2.get(1).getMarketSegmentCount());
        assertEquals(new Integer(3), rateCodesMS2.get(2).getMarketSegmentCount());
    }


    @Test
    public void getRateCodesWhenCancelled() {
        List<RateCodeSummary> rateCodes = service.getRateCodes(MARKET_SEGMENT_3);
        assertEquals(2, rateCodes.size());
        assertEquals(RATE_CODE_2, rateCodes.get(0).getRateCode());
        assertEquals(new Integer(0), rateCodes.get(0).getRoomsSold());
        assertEquals(new Double(0), rateCodes.get(0).getRoomRevenue());
        assertTrue(rateCodes.get(0).getMarketSegments().contains(MARKET_SEGMENT_1));
        assertTrue(rateCodes.get(0).getMarketSegments().contains(MARKET_SEGMENT_2));
        assertTrue(rateCodes.get(0).getMarketSegments().contains(MARKET_SEGMENT_3));
        assertTrue(rateCodes.get(0).getMarketSegments().contains(MARKET_SEGMENT_5));
        assertEquals(RATE_CODE_3, rateCodes.get(1).getRateCode());
        assertEquals(new Integer(0), rateCodes.get(1).getRoomsSold());
        assertEquals(new Double(0), rateCodes.get(1).getRoomRevenue());
        assertTrue(rateCodes.get(1).getMarketSegments().contains(MARKET_SEGMENT_1));
        assertTrue(rateCodes.get(1).getMarketSegments().contains(MARKET_SEGMENT_2));
        assertTrue(rateCodes.get(1).getMarketSegments().contains(MARKET_SEGMENT_3));
    }

    @Test
    public void accept() {
        service.accept();
        verify(service.configParamsServiceLocal).addParameterValue(AnalyticalMarketSegmentService.ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE, "true");
    }

    @Test
    public void isAcceptedTrue() {
        assertFalse(service.isAccepted());
        when(service.configParamsServiceLocal.getBooleanParameterValue(AnalyticalMarketSegmentService.ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE)).thenReturn(true);
        assertTrue(service.isAccepted());
    }

    @Test
    public void hasAllGroupAndIndividualMarketSegmentsMapped() {

        // delete all AmsOccupancySummarys
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        // delete all AnalyticalMarketSegments
        crudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.DELETE_ALL);
        crudService.clear();

        assertFalse(service.isAccepted());
        when(service.configParamsServiceLocal.getBooleanParameterValue(AnalyticalMarketSegmentService.ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE)).thenReturn(true);
        // should show as accepted
        assertTrue(service.isAccepted());
        // should show as complete
        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        Date occupancyDate = new Date();

        // Create a group level AmsOccupancySummary
        AmsOccupancySummary amsOccupancySummary = createSummary(occupancyDate, MARKET_SEGMENT_9_TMP, null, "GROUP_T",
                true);
        crudService.save(amsOccupancySummary);
        crudService.flushAndClear();
        assertFalse(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        crudService.delete(amsOccupancySummary);
        crudService.flushAndClear();
        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        // Create a market segment (non-group) level AmsOccupancySummary
        amsOccupancySummary = createSummary(occupancyDate, MARKET_SEGMENT_9_TMP, null, "INDIV_1", false);
        crudService.save(amsOccupancySummary);
        crudService.flushAndClear();
        assertFalse(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        crudService.delete(amsOccupancySummary);
        crudService.flushAndClear();
        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());
    }

    @Test
    public void updateAttribute() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        AnalyticalMarketSegmentSummary unassignedIndividualMarketSegments =
                service.getUnassignedIndividualMarketSegments().get(0);
        assertNotNull(unassignedIndividualMarketSegments);

        String marketCode = unassignedIndividualMarketSegments.getMarketCode();
        String mappedCodeFenced = marketCode + "_" + AnalyticalMarketSegmentAttribute.FENCED.getSuffix();
        unassignedIndividualMarketSegments.setRateCode(RATE_CODE_1);
        //Assign MS1 and RC1 to Fenced and verify
        service.assignMarketSegments(Collections.singletonList(unassignedIndividualMarketSegments),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(marketCode)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        List<AnalyticalMarketSegment> assignedMarketSegments = service.getAssignedMarketSegments();
        assertEquals(2, assignedMarketSegments.size());
        AnalyticalMarketSegment analyticalMarketSegment = assignedMarketSegments.get(0);
        assertEquals(marketCode, analyticalMarketSegment.getMarketCode());
        assertEquals(mappedCodeFenced, analyticalMarketSegment.getMappedMarketCode());
        assertEquals(RATE_CODE_1, analyticalMarketSegment.getRateCode());

        //verify mapped codes exist in market segment master
        MarketSegmentMaster marketSegmentMaster = new MarketSegmentMaster();
        marketSegmentMaster.setCode(mappedCodeFenced);
        assertEquals(1, crudService.findByExample(marketSegmentMaster).size());

        //update MS1 and RC1 to FENCED_AND_PACKAGED and verify
        service.updateAssignedMarketSegments(Collections.singletonList(analyticalMarketSegment),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, (Product) null);
        assignedMarketSegments = service.getAssignedMarketSegments();
        assertEquals(2, assignedMarketSegments.size());
        analyticalMarketSegment = assignedMarketSegments.get(0);
        assertEquals(marketCode, analyticalMarketSegment.getMarketCode());
        assertEquals(marketCode + "_" + AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED.getSuffix(),
                analyticalMarketSegment.getMappedMarketCode());
        assertEquals(RATE_CODE_1, analyticalMarketSegment.getRateCode());

        //verify old mapped codes is removed from market segment master
        marketSegmentMaster = new MarketSegmentMaster();
        marketSegmentMaster.setCode(mappedCodeFenced);
        assertEquals(0, crudService.findByExample(marketSegmentMaster).size());
    }

    @Test
    public void assignmentBlockWithRateCodes() {
        Date occupancyDate = new Date();

        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AmsOccupancySummary> list = new ArrayList<>();
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_4, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_5, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_6, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_7, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_8, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_9, CHECKED_IN, false));
        crudService.save(list);

        service.assignMarketSegments(createAnalyticalMarketSegment(false, RateCodeTypeEnum.EQUALS, list.get(0)),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, forecastActivityTypeId);

        // Verify that the block_pct was assigned
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        AnalyticalMarketSegment ams = findAnalyticalMarketSegmentByMappedMarketCode(assigned, MARKET_SEGMENT_7);
        assertNotNull(ams);
        assertEquals(BOOKING_BLOCK_PCT_IS_BLOCK, ams.getBlockPercent());

        // Verify that the block_pct was assigned
        MarketSegmentMaster msm = getMasterByMappedCode(ams.getMappedMarketCode());
        assertNotNull(msm);
        assertEquals(BOOKING_BLOCK_PCT_IS_BLOCK, msm.getBookingBlockPc());

        // Update the assignments to being transient - equal to bar (removes the block pct)
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_7)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        assertTrue(CollectionUtils.isEmpty(service.getRateCodes(MARKET_SEGMENT_7)));
        AnalyticalMarketSegmentSummary after =
                (AnalyticalMarketSegmentSummary) CollectionUtils.find(service.getUnassignedSharedMarketSegments(),
                        new MarketCodePredicate(MARKET_SEGMENT_7));
        assertNull(after);

        assigned = service.getAssignedMarketSegments();
        assertEquals(2, assigned.size());
        ams = findAnalyticalMarketSegmentByMappedMarketCode(assigned, MARKET_SEGMENT_7);
        assertNotNull(ams);
        assertEquals(BOOKING_BLOCK_PCT_DEFAULT, ams.getBlockPercent());

        msm = getMasterByMappedCode(ams.getMappedMarketCode());
        assertNotNull(msm);
        assertEquals(BOOKING_BLOCK_PCT_DEFAULT, msm.getBookingBlockPc());
        assertEquals(ONE, msm.getIsEditable());
        assertNotNull(getAssignmentByMappedCode(MARKET_SEGMENT_7_DEF));

        service.unassignMarketSegments(assigned, false);
    }

    @Test
    public void assignmentNonBlockWithRateCodes() {
        Date occupancyDate = new Date();
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AmsOccupancySummary> list = new ArrayList<>();
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_4, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_5, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_6, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_7, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_8, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_9, CHECKED_IN, false));
        crudService.save(list);

        AnalyticalMarketSegmentSummary before = (AnalyticalMarketSegmentSummary) CollectionUtils.find(
                service.getUnassignedSharedMarketSegments(), new MarketCodePredicate(MARKET_SEGMENT_7));

        service.assignMarketSegments(createAnalyticalMarketSegment(true, RateCodeTypeEnum.EQUALS, list.get(0),
                        list.get(1), list.get(2), list.get(3)),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_7)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.clear();

        List<RateCodeSummary> rateCodes = service.getRateCodes(MARKET_SEGMENT_7);
        for (RateCodeSummary rateCodeSummary : rateCodes) {
            assertFalse(rateCodeSummary.getRateCode().equalsIgnoreCase(RATE_CODE_4));
            assertFalse(rateCodeSummary.getRateCode().equalsIgnoreCase(RATE_CODE_5));
            assertFalse(rateCodeSummary.getRateCode().equalsIgnoreCase(RATE_CODE_6));
            assertFalse(rateCodeSummary.getRateCode().equalsIgnoreCase(RATE_CODE_7));
        }

        AnalyticalMarketSegmentSummary after =
                (AnalyticalMarketSegmentSummary) CollectionUtils.find(service.getUnassignedSharedMarketSegments(),
                        new MarketCodePredicate(MARKET_SEGMENT_7));
        assertNotNull(after);
        assertEquals(MARKET_SEGMENT_7, after.getMarketCode());
        assertEquals(before.getRoomsSold(), after.getRoomsSold());
        assertEquals(before.getRoomRevenue(), after.getRoomRevenue());

        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        for (AnalyticalMarketSegment ams : assigned) {
            MarketSegmentMaster msm = getMasterByMappedCode(ams.getMappedMarketCode());
            assertNotNull(msm);
        }

        AnalyticalMarketSegment found = getAssignmentByMappedCode(MARKET_SEGMENT_7_DEF);
        assertNotNull(found);
        assertEquals(MARKET_SEGMENT_7, found.getMarketCode());
        assertEquals(MARKET_SEGMENT_7_DEF, found.getMappedMarketCode());
        assertEquals(RateCodeTypeEnum.DEFAULT, found.getRateCodeType());
        assertEquals(RateCodeTypeEnum.DEFAULT.getRank(), found.getRank().intValue());
        assertEquals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, found.getAttribute());
        MarketSegmentMaster msmDef = getMasterByMappedCode(MARKET_SEGMENT_7_DEF);
        assertNotNull(msmDef);
        assertEquals(ONE, msmDef.getIsEditable());

        service.unassignMarketSegments(assigned, false);
        assertNull(getAssignmentByMappedCode(MARKET_SEGMENT_7_DEF));
    }

    @Test
    public void assignAttributeAsGroupWithStartsWithRateCodeType() {
        assignAttributeAsGroupWithRateCodeType(RateCodeTypeEnum.STARTS_WITH);
    }

    @Test
    public void assignAttributeAsGroupWithEqualsRateCodeType() {
        assignAttributeAsGroupWithRateCodeType(RateCodeTypeEnum.EQUALS);
    }

    @Test
    public void assignAttributeAsGroupWithEndsWithRateCodeType() {
        assignAttributeAsGroupWithRateCodeType(RateCodeTypeEnum.ENDS_WITH);
    }

    @Test
    public void assignAttributeAsGroupWithContainsRateCodeType() {
        assignAttributeAsGroupWithRateCodeType(RateCodeTypeEnum.CONTAINS);
    }

    @Test
    public void assignAttributeCreateProposedTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<MktSegDetailsProposed> originalProposed = findDetailsProposedByMktCode(MARKET_SEGMENT_5);
        int origProposedSize = originalProposed.size();
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        //Add in a detail and segment
        addMktSegWithDetail(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_5, 0);
        crudService.flushAndClear();

        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.ALL.name());
        assignment.setMappedCode(MARKET_SEGMENT_5);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, forecastActivityTypeId);
        crudService.flushAndClear();
        List<MktSegDetailsProposed> proposed = findDetailsProposedByMktCode(MARKET_SEGMENT_5);
        int proposedSize = proposed.size();
        assertTrue(origProposedSize < proposedSize, "originalProposedSize < new proposed Size");
    }

    @Test
    public void assignAttributeUpdateProposedTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<MktSegDetailsProposed> originalProposed = findDetailsProposedByMktCode(MARKET_SEGMENT_5);
        int origProposedSize = originalProposed.size();
        AnalyticalMarketSegmentSummary assignment =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        //Add in a detail and segment
        addMktSegWithDetail(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_5, 0);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        assignment.setRateCodeType(RateCodeTypeEnum.ALL.name());
        assignment.setMappedCode(MARKET_SEGMENT_5);
        assignment.setBlockPercent(0);

        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, forecastActivityTypeId);
        crudService.flushAndClear();
        List<MktSegDetailsProposed> newProposed = findDetailsProposedByMktCode(MARKET_SEGMENT_5);
        int proposedSize = newProposed.size();

        // This has a Transient Block non-linked attribute, so block percent should be set to 100 during the assignment
        assertEquals(Integer.valueOf(100), (Object) newProposed.get(0).getBookingBlockPc(),
                "Should be setting the block percent to 100 during assignMarketSegments");

        assignment.setRateCode(RATE_CODE_2);
        assignment.setRateCodeType(RateCodeTypeEnum.ALL.name());
        assignment.setMappedCode(MARKET_SEGMENT_5);
        assignment.setBlockPercent(0);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, forecastActivityTypeId);
//        List<MktSegDetailsProposed> updatedProposed = findDetailsProposedByMktCode(MARKET_SEGMENT_5);

        // This has a Transient Block non-linked attribute, so block percent should be set to 100 during the assignment
        assertEquals(Integer.valueOf(100), (Object) newProposed.get(0).getBookingBlockPc(),
                "Should be setting the block percent to 100 during assignMarketSegments");
        // We've added MktSegDetailsProposed so the original count should be less than the current count
        assertTrue(origProposedSize < proposedSize, "OriginalProposedSize is not less than new proposed Size");
    }

    private void addMktSegWithDetail(AnalyticalMarketSegmentAttribute attribute, String mappedCode, int blockPercent) {
        MktSeg segment = new MktSeg();
        segment.setPropertyId(PacmanWorkContextHelper.getWorkContext().getPropertyId());
        segment.setStatusId(Constants.ACTIVE_STATUS_ID);
        segment.setCode(mappedCode);
        segment.setDescription(mappedCode);
        segment.setName(mappedCode);
        segment.setEditable(1);

        MktSegDetails segDetails = new MktSegDetails();
        segment.setMktSegDetails(segDetails);
        if (attribute.equals(AnalyticalMarketSegmentAttribute.GROUP)) {
            segDetails.setBookingBlockPc(100);
        } else {
            segDetails.setBookingBlockPc(blockPercent);
        }
        segDetails.setBusinessType(service.lookupBusinessType(attribute.getBusinessType()));
        segDetails.setYieldType(service.lookupYieldType(attribute.getYieldType()));
        segDetails.setFenced(attribute.getFenced() ? 1 : 0);
        segDetails.setLink(attribute.getLinkType().getId());
        segDetails.setPackageValue(attribute.getPackaged() ? 1 : 0);
        segDetails.setPriceByBar(attribute.getPricedByBar() ? 1 : 0);
        segDetails.setQualified(attribute.getQualified() ? 1 : 0);

        //Default these for newly created proposed details
        segDetails.setProcessStatus(service.lookupProcessStatusById(ProcessStatus.NOT_APPROVED));
        segDetails.setStatusId(Constants.ACTIVE_STATUS_ID);
        segDetails.setTemplateDefault(0);
        segDetails.setTemplateId(0);
        segDetails.setForecastActivityType(forecastActivityType);

        crudService.save(segment);
        segDetails.setMktSeg(segment);
        crudService.save(segDetails);
        crudService.flushAndClear();
    }

    private List<MktSegDetailsProposed> findDetailsProposedByMktCode(String mktCode) {
        MktSegDetailsProposed detailsProposed = new MktSegDetailsProposed();
        MktSeg segment = new MktSeg();
        detailsProposed.setMktSeg(segment);
        segment.setCode(mktCode);
        return (List<MktSegDetailsProposed>) crudService.findByExample(detailsProposed);
    }

    private void assignAttributeAsGroupWithRateCodeType(RateCodeTypeEnum rateCodeType) {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        assertTrue(CollectionUtils.isEmpty(service.getAssignedMarketSegments()));
        for (AnalyticalMarketSegmentSummary summary : unassigned) {
            summary.setRateCodeType(rateCodeType.name());
        }

        service.assignMarketSegments(unassigned, AnalyticalMarketSegmentAttribute.GROUP, forecastActivityTypeId);
        assertTrue(CollectionUtils.isEmpty(service.getUnassignedIndividualMarketSegments()));
        List<AnalyticalMarketSegment> assigned = service.getAssignedMarketSegments();
        assertEquals(unassigned.size(), assigned.size());

        for (AnalyticalMarketSegment summary : assigned) {
            assertEquals(AnalyticalMarketSegmentAttribute.GROUP, summary.getAttribute());
            assertEquals(summary.getMarketCode(), summary.getMappedMarketCode());
            assertEquals(rateCodeType, summary.getRateCodeType());
            assertEquals(rateCodeType.getRank(), summary.getRateCodeType().getRank());
        }
        crudService.flushAndClear();
    }

    @SuppressWarnings("unchecked")
    @Test
    @Tag("analyticMarketSegment-flaky")
    @Disabled("Flaky")
    public void populateYieldCategoryByRule_Simple() {
        Date occupancyDate = new Date();
        service.clearAssignments(TestProperty.H1.getPaddedId());

        DataLoadMetadata dataLoadMetaData = createDataLoadMetaData("PTRANS");
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_4);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_5);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_6);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_7);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_8);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_7, RATE_CODE_9);

        List<AmsOccupancySummary> list = new ArrayList<>();
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_4, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_5, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_6, CHECKED_IN, false));
        list.add(createSummary(occupancyDate, MARKET_SEGMENT_7, RATE_CODE_7, CHECKED_IN, false));

        service.assignMarketSegments(createAnalyticalMarketSegment(true, RateCodeTypeEnum.EQUALS, list.get(0),
                        list.get(1), list.get(2), list.get(3)),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_7)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        List<AnalyticalMarketSegment> assignments = service.getAssignedMarketSegments();
        assertEquals(5, assignments.size());

        // insert a rule that should be removed
        YieldCategoryRule defunctRule = insertYieldCategoryRule("MC0", "RC0", "MC0_RC0", 0);

        assertEquals(5, service.populateYieldCategoryByRule()); // 4 + the _DEF rule

        // make sure old rules are removed
        List<YieldCategoryRule> defunctYcbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_MARKET_RATE_CODE,
                QueryParameter.with("marketCode", defunctRule.getMarketCode())
                        .and("rateCode", defunctRule.getRateCode()).parameters());
        assertTrue(defunctYcbrList.isEmpty());

        // verify that new rules are created
        for (AnalyticalMarketSegment ams : assignments) {
            List<YieldCategoryRule> ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_MARKET_RATE_CODE,
                    QueryParameter.with("marketCode", ams.getMarketCode()).and("rateCode", ams.getRateCode()).parameters());
            if (!ams.getMappedMarketCode().endsWith("_DEF")) {
                assertEquals(1, ycbrList.size());
                YieldCategoryRule ycbr = ycbrList.get(0);
                assertEquals(ams.getMarketCode(), ycbr.getMarketCode());
                assertEquals(ams.getRateCode(), ycbr.getRateCode());
                assertEquals(ams.getMappedMarketCode(), ycbr.getAnalyticalMarketCode());
                assertEquals(new LocalDate("1800-01-01"), ycbr.getBookingStartDate());
                assertEquals(new LocalDate("2999-12-31"), ycbr.getBookingEndDate());
                assertEquals(ams.getRank(), ycbr.getRank());
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void populateYieldCategoryByRule_Advanced() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.ALL, null, AnalyticalMarketSegmentAttribute.GROUP,
                MARKET_SEGMENT_1 + "_GROUP");

        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.EQUALS, RATE_CODE_1,
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE,
                MARKET_SEGMENT_1 + "_QNLY");
        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.STARTS_WITH, "B",
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                MARKET_SEGMENT_1 + "_TBL");
        createAssignment(MARKET_SEGMENT_2, RateCodeTypeEnum.CONTAINS, "B",
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                MARKET_SEGMENT_2 + "_FAP");
        createAssignment(MARKET_SEGMENT_3, RateCodeTypeEnum.CONTAINS, "-",
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                MARKET_SEGMENT_3 + "_FAP");

        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_1)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_2)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_3)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);

        DataLoadMetadata dataLoadMetaData = createDataLoadMetaData("PTRANS");
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_1, RATE_CODE_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_1, RATE_CODE_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_1, RATE_CODE_3);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_2, RATE_CODE_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_2, RATE_CODE_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_2, RATE_CODE_3);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_3, RATE_CODE_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_3, RATE_CODE_3);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_3, RATE_CODE_DASH_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_3, RATE_CODE_DASH_2);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_4, RATE_CODE_1);
        createRawTrans(dataLoadMetaData, MARKET_SEGMENT_5, RATE_CODE_2);

        service.getAssignedMarketSegments();

        assertEquals(9, service.populateYieldCategoryByRule());


        // MS level assignments not copied
        List<YieldCategoryRule> ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_1 + "_GROUP").parameters());
        assertTrue(ycbrList.isEmpty());

        // Rate level
        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_1 + "_QNLY").parameters());
        assertEquals(1, ycbrList.size());
        assertEquals(MARKET_SEGMENT_1, ycbrList.get(0).getMarketCode());
        assertEquals(RATE_CODE_1, ycbrList.get(0).getRateCode());
        assertEquals(MARKET_SEGMENT_1 + "_QNLY", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.EQUALS.getRank(), ycbrList.get(0).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_1 + "_TBL").parameters());
        assertEquals(1, ycbrList.size());
        assertEquals(MARKET_SEGMENT_1, ycbrList.get(0).getMarketCode());
        assertEquals(RATE_CODE_2, ycbrList.get(0).getRateCode());
        assertEquals(MARKET_SEGMENT_1 + "_TBL", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.STARTS_WITH.getRank(), ycbrList.get(0).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_2 + "_FAP").parameters());
        assertEquals(2, ycbrList.size());
        assertEquals(MARKET_SEGMENT_2, ycbrList.get(1).getMarketCode());
        assertEquals(RATE_CODE_1, ycbrList.get(1).getRateCode());
        assertEquals(MARKET_SEGMENT_2 + "_FAP", ycbrList.get(1).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(1).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(1).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.CONTAINS.getRank(), ycbrList.get(1).getRank().intValue());

        assertEquals(MARKET_SEGMENT_2, ycbrList.get(0).getMarketCode());
        assertEquals(RATE_CODE_2, ycbrList.get(0).getRateCode());
        assertEquals(MARKET_SEGMENT_2 + "_FAP", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.CONTAINS.getRank(), ycbrList.get(0).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_3 + "_FAP").parameters());
        assertEquals(2, ycbrList.size());
        assertEquals(MARKET_SEGMENT_3, ycbrList.get(0).getMarketCode());
        assertEquals(RATE_CODE_DASH_1, ycbrList.get(0).getRateCode());
        assertEquals(MARKET_SEGMENT_3 + "_FAP", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.CONTAINS.getRank(), ycbrList.get(0).getRank().intValue());
        assertEquals(MARKET_SEGMENT_3, ycbrList.get(1).getMarketCode());
        assertEquals(RATE_CODE_DASH_2, ycbrList.get(1).getRateCode());
        assertEquals(MARKET_SEGMENT_3 + "_FAP", ycbrList.get(1).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(1).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(1).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.CONTAINS.getRank(), ycbrList.get(1).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_1 + "_DEF").parameters());
        assertEquals(1, ycbrList.size());
        assertEquals(MARKET_SEGMENT_1, ycbrList.get(0).getMarketCode());
        assertNull(ycbrList.get(0).getRateCode());
        assertEquals(MARKET_SEGMENT_1 + "_DEF", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.DEFAULT.getRank(), ycbrList.get(0).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_2 + "_DEF").parameters());
        assertEquals(1, ycbrList.size());
        assertEquals(MARKET_SEGMENT_2, ycbrList.get(0).getMarketCode());
        assertNull(ycbrList.get(0).getRateCode(), RATE_CODE_3);
        assertEquals(MARKET_SEGMENT_2 + "_DEF", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.DEFAULT.getRank(), ycbrList.get(0).getRank().intValue());

        ycbrList = crudService.findByNamedQuery(YieldCategoryRule.BY_ANALYTICAL_MARKET_CODE,
                QueryParameter.with("analyticalMarketCode", MARKET_SEGMENT_3 + "_DEF").parameters());
        assertEquals(1, ycbrList.size());
        assertEquals(MARKET_SEGMENT_3, ycbrList.get(0).getMarketCode());
        assertNull(ycbrList.get(0).getRateCode(), RATE_CODE_3);
        assertEquals(MARKET_SEGMENT_3 + "_DEF", ycbrList.get(0).getAnalyticalMarketCode());
        assertEquals(new LocalDate("1800-01-01"), ycbrList.get(0).getBookingStartDate());
        assertEquals(new LocalDate("2999-12-31"), ycbrList.get(0).getBookingEndDate());
        assertEquals(RateCodeTypeEnum.DEFAULT.getRank(), ycbrList.get(0).getRank().intValue());

    }

    @Test
    public void previewHappyPath() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.ALL, null, AnalyticalMarketSegmentAttribute.GROUP,
                MARKET_SEGMENT_1 + "_GROUP");

        createAssignment(MARKET_SEGMENT_2, RateCodeTypeEnum.STARTS_WITH, "B",
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                MARKET_SEGMENT_2 + "_TBL");
        createAssignment(MARKET_SEGMENT_2, RateCodeTypeEnum.CONTAINS, "C",
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                MARKET_SEGMENT_2 + "_FAP");

        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_2)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_4)),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        List<PreviewMarketSegment> results = service.preview();

        assertNotNull(results);
        double totalRooms = 8.0;

        // group
        assertPreview(results, MARKET_SEGMENT_1 + "_GROUP", 3, 360.0, 120.0, 3.0 / totalRooms * 100.0);

        // others
        assertPreview(results, MARKET_SEGMENT_2 + "_TBL", 2, 240.0, 120.0, 2.0 / totalRooms * 100.0);
        assertPreview(results, MARKET_SEGMENT_2 + "_FAP", 1, 120.0, 120.0, 1.0 / totalRooms * 100.0);

        // default to straight map
        assertPreview(results, MARKET_SEGMENT_3, 0, 0.0, 0.0, 0.0);
        assertPreview(results, MARKET_SEGMENT_5, 1, 120.0, 120.0, 1.0 / totalRooms * 100.0);

        // no additional previews
        assertEquals(7, results.size());
    }

    @Test
    public void unassignedGroupPercentages() {
        Map<String, AnalyticalMarketSegmentSummary> unassignedMarket = service.getUnassignedIndividualMarketSegments().stream().collect(Collectors.toMap(AnalyticalMarketSegmentSummary::getMarketCode, value -> value));

        List<AnalyticalMarketSegmentSummary> unassignedGroup = service.getUnassignedGroupMarketSegments();
        assertEquals(3, unassignedGroup.size());

        for (AnalyticalMarketSegmentSummary groupSummary : unassignedGroup) {
            AnalyticalMarketSegmentSummary marketSegment = unassignedMarket.get(groupSummary.getMarketCode());

            double expected = (marketSegment == null) ? 1.0 :
                    (double) groupSummary.getPickup() / (double) marketSegment.getRoomsSold();
            assertTrue(Math.abs(expected - groupSummary.getHotelPercent()) < 0.001, "Expected:" + expected + " Actual" +
                    ": " + groupSummary.getHotelPercent());
        }
    }

    @Test
    public void getMarketSegmentSummary_ExistingMarketSegment() {
        MarketSegmentSummary summary = service.findOrCreateMarketSegmentSummary("COMP");
        assertNotNull(summary);
        assertEquals("COMP", summary.getCode());
    }

    @Test
    public void getMarketSegmentSummary_CreateNewMarketSegment() {
        MarketSegmentSummary summary = service.findOrCreateMarketSegmentSummary("ABC");
        assertNotNull(summary);
        assertEquals("ABC", summary.getCode());
        assertEquals("ABC", summary.getDescription());
        assertEquals("ABC", summary.getName());
        assertEquals(Integer.valueOf(1), summary.getEditable());
        assertNotNull(summary.getId());
    }

    @Test
    public void getStraightOrDefaultMarketSegmentSummary_ExistingSplitMarketSegment() {
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(RateCodeTypeEnum.DEFAULT.getRank(), "ALL",
                RateCodeTypeEnum.DEFAULT, "COMP");
        tenantCrudService().save(ams);
        MarketSegmentSummary summary = service.findOrCreateStraightOrDefaultMarketSegmentSummary("COMP");
        assertNotNull(summary);
        assertEquals("COMP", summary.getCode());
    }

    @Test
    public void getStraightOrDefaultMarketSegmentSummary_ExistingMarketSegment() {
        MarketSegmentSummary summary = service.findOrCreateStraightOrDefaultMarketSegmentSummary("COMP");
        assertNotNull(summary);
        assertEquals("COMP", summary.getCode());
    }

    @Test
    public void getStraightOrDefaultMarketSegmentSummary_CreateNewStraightMarketSegment() {
        MarketSegmentSummary summary = service.findOrCreateStraightOrDefaultMarketSegmentSummary("ABC");
        assertNotNull(summary);
        assertEquals("ABC", summary.getCode());
        assertEquals("ABC", summary.getDescription());
        assertEquals("ABC", summary.getName());
        assertEquals(Integer.valueOf(1), summary.getEditable());
        assertNotNull(summary.getId());
    }


    @Test
    public void getStraightOrDefaultMarketSegmentSummary_CreateNewDefaultMarketSegment() {
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(RateCodeTypeEnum.DEFAULT.getRank(), "ALL",
                RateCodeTypeEnum.DEFAULT, "ABC");
        tenantCrudService().save(ams);
        MarketSegmentSummary summary = service.findOrCreateStraightOrDefaultMarketSegmentSummary("ABC");
        assertNotNull(summary);
        assertEquals("ABC_DEF", summary.getCode());
        assertEquals("ABC_DEF", summary.getDescription());
        assertEquals("ABC_DEF", summary.getName());
        assertEquals(Integer.valueOf(1), summary.getEditable());
        assertNotNull(summary.getId());
    }

    @Test
    public void createMissingMarketSegmentFromHotelMarketSummary() {
        AnalyticalMarketSegmentRepository amsRepository = new AnalyticalMarketSegmentRepository();
        inject(amsRepository, "crudService", crudService);
        inject(service, "amsRepository", amsRepository);

        buildMktSegAccomActivity("ABC");
        buildMktSegAccomActivity("ABC1");
        service.createMissingMarketSegmentFromHotelMarketSummary();
        verifyMktCodeSummary("ABC");
        verifyMktCodeSummary("ABC1");
    }

    private void verifyMktCodeSummary(final String mktCode) {
        MarketSegmentSummary summary = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentSummary.BY_CODE,
                QueryParameter.with("code", mktCode).parameters());
        assertNotNull(summary);
        assertEquals(mktCode, summary.getCode());
        assertEquals(mktCode, summary.getDescription());
        assertEquals(mktCode, summary.getName());
        assertEquals(Integer.valueOf(1), summary.getEditable());
        assertNotNull(summary.getId());
    }

    private HotelMktSegAccomActivity buildMktSegAccomActivity(String mktSegCode) {
        HotelMktSegAccomActivity mktSegAccomActivity = new HotelMktSegAccomActivity();
        mktSegAccomActivity.setId(1);
        mktSegAccomActivity.setPropertyId(5);
        AccomType accomType = tenantCrudService().findOne(AccomType.class);
        mktSegAccomActivity.setAccomTypeCode(accomType.getAccomTypeCode());
        mktSegAccomActivity.setMktSegCode(mktSegCode);
        mktSegAccomActivity.setFileMetadataId(2);
        mktSegAccomActivity.setSnapShotDate(new Date());
        mktSegAccomActivity.setOccupancyDate(new Date());
        mktSegAccomActivity.setArrivals(BigDecimal.ONE);
        mktSegAccomActivity.setCancellations(new BigDecimal(2));
        mktSegAccomActivity.setDepartures(new BigDecimal(3));
        mktSegAccomActivity.setFoodRevenue(new BigDecimal(4));
        mktSegAccomActivity.setNoShows(new BigDecimal(5));
        mktSegAccomActivity.setRoomRevenue(new BigDecimal(6));
        mktSegAccomActivity.setRoomsSold(new BigDecimal(9));
        mktSegAccomActivity.setTotalRevenue(new BigDecimal(11));
        return tenantCrudService().save(mktSegAccomActivity);
    }

    @Test
    public void getMarketSegmentSummary_CreateNewMarketSegmentWithNotEditable() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        MarketSegmentSummary summary = service.findOrCreateMarketSegmentSummary("ABC");
        assertNotNull(summary);
        assertEquals("ABC", summary.getCode());
        assertEquals("ABC", summary.getDescription());
        assertEquals("ABC", summary.getName());
        assertNotNull(summary.getId());
        assertEquals(ONE, summary.getEditable());
    }

    @Test
    public void isAnalyticalMarketSegmentChanged() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        MktSeg bart = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "BART").parameters());
        createMktSegDetailsProposed(bart);
        crudService.save(createAnalyticalMarketSegment(0, "ALL", RateCodeTypeEnum.ALL, "BART"));
        assertTrue(service.isAnalyticalMarketSegmentChanged());
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());
    }

    @Test
    public void isAnalyticalMarketSegmentStraightMarketSegment_no() {
        MktSeg bart = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "BART").parameters());
        createMktSegDetailsProposed(bart);
        crudService.save(createAnalyticalMarketSegment(0, "ALL", RateCodeTypeEnum.ALL, "BART"));
        assertFalse(service.isAnalyticalMarketSegmentStraightMarketSegment(bart.getCode()));
    }

    @Test
    public void isAnalyticalMarketSegmentStraightMarketSegment() {
        MktSeg bart = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "BART").parameters());
        createMktSegDetailsProposed(bart);
        crudService.save(createAnalyticalMarketSegment(1, "ALL", RateCodeTypeEnum.ALL, "BART"));
        assertTrue(service.isAnalyticalMarketSegmentStraightMarketSegment(bart.getCode()));
    }


    @Test
    public void isAnalyticalMarketSegmentChanged_whereMatchIsNotTheFirstProposed() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        MktSeg bart = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "BART").parameters());
        MktSeg comp = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "COMP").parameters());
        createMktSegDetailsProposed(bart);
        createMktSegDetailsProposed(comp);
        crudService.save(createAnalyticalMarketSegment(0, "ALL", RateCodeTypeEnum.ALL, "COMP"));
        assertTrue(service.isAnalyticalMarketSegmentChanged());
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());
    }

    @Test
    public void isAnalyticalMarketSegmentChanged_no() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        MktSeg bart = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", "BART").parameters());
        createMktSegDetailsProposed(bart);
        crudService.save(createAnalyticalMarketSegment(1, "ALL", RateCodeTypeEnum.ALL, "BART"));
        assertFalse(service.isAnalyticalMarketSegmentChanged());
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());
    }

    @Test
    public void isAnalyticalMarketSegmentChanged_AMS_Not_Enabled() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        assertFalse(service.isAnalyticalMarketSegmentChanged());
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());
    }

    @Test
    public void updateExistingMarketSegmentTest() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.ALL, null, AnalyticalMarketSegmentAttribute.GROUP,
                MARKET_SEGMENT_1 + "_GROUP");
        createAssignment(MARKET_SEGMENT_2, RateCodeTypeEnum.STARTS_WITH, "B",
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                MARKET_SEGMENT_2 + "_TBL");
        createAssignment(MARKET_SEGMENT_2, RateCodeTypeEnum.CONTAINS, "C",
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED,
                MARKET_SEGMENT_2 + "_FAP");

        int marketSegmentCountBeforeUpdate = crudService.findAll(MktSeg.class).size();

        List<AnalyticalMarketSegment> updatedMarketSegment = new ArrayList<>();
        AnalyticalMarketSegment newMarketSegment = new AnalyticalMarketSegment();
        newMarketSegment.setMappedMarketCode(MARKET_SEGMENT_2 + "_NEW");
        newMarketSegment.setAttribute(AnalyticalMarketSegmentAttribute.FENCED);
        newMarketSegment.setMarketCode(MARKET_SEGMENT_2);
        ForecastActivityType forecastActivityType = new ForecastActivityType();
        forecastActivityType.setId(ForecastActivityType.WASH);
        newMarketSegment.setForecastActivityType(forecastActivityType);
        updatedMarketSegment.add(newMarketSegment);

        service.updateAssignedMarketSegments(updatedMarketSegment,
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, (Product) null);

        int marketSegmentCountAfterUpdate = crudService.findAll(MktSeg.class).size();

        assertEquals(marketSegmentCountBeforeUpdate, marketSegmentCountAfterUpdate);
    }

    @Test
    public void updateExistingMarketSegmentTest_defaultWithChildRateCode() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.DEFAULT, null, AnalyticalMarketSegmentAttribute.GROUP,
                MARKET_SEGMENT_1 + "_DEF");
        createAssignment(MARKET_SEGMENT_1, RateCodeTypeEnum.EQUALS, "BC1", AnalyticalMarketSegmentAttribute.GROUP,
                MARKET_SEGMENT_1 + "_DEF");

        List<AnalyticalMarketSegment> analyticalMarketSegments = crudService.findAll(AnalyticalMarketSegment.class);

        service.updateAssignedMarketSegments(analyticalMarketSegments, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);

        analyticalMarketSegments = crudService.findAll(AnalyticalMarketSegment.class);

        assertEquals(2, analyticalMarketSegments.size());
        assertEquals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, analyticalMarketSegments.get(0).getAttribute());
        assertEquals(MARKET_SEGMENT_1 + "_DEF", analyticalMarketSegments.get(0).getMappedMarketCode());

        assertEquals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, analyticalMarketSegments.get(1).getAttribute());
        assertEquals(MARKET_SEGMENT_1 + "_DEF", analyticalMarketSegments.get(1).getMappedMarketCode());
    }

    private AnalyticalMarketSegment createAnalyticalMarketSegment(Integer rank, String rateCode,
                                                                  RateCodeTypeEnum rateCodeType,
                                                                  String marketSegmentCode) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setRank(rank);
        analyticalMarketSegment.setRateCode(rateCode);
        analyticalMarketSegment.setRateCodeType(rateCodeType);
        analyticalMarketSegment.setMarketCode(marketSegmentCode);
        final String mappedMarketCode = rateCodeType.equals(RateCodeTypeEnum.DEFAULT) ? marketSegmentCode + "_DEF" :
                marketSegmentCode;
        analyticalMarketSegment.setMappedMarketCode(mappedMarketCode);
        return analyticalMarketSegment;
    }

    private MktSegDetailsProposed createMktSegDetailsProposed(MktSeg mktSeg) {
        MktSegDetailsProposed mktSegDetailsProposed = new MktSegDetailsProposed();
        mktSegDetailsProposed.setMktSeg(mktSeg);
        mktSegDetailsProposed.setBookingBlockPc(1);
        List<BusinessType> businessTypes = crudService.findByNamedQuery(BusinessType.BY_NAME,
                QueryParameter.with("name", "Transient").parameters());
        mktSegDetailsProposed.setBusinessType(businessTypes.get(0));
        mktSegDetailsProposed.setFenced(0);
        mktSegDetailsProposed.setLink(0);
        mktSegDetailsProposed.setQualified(1);
        mktSegDetailsProposed.setTemplateDefault(1);
        mktSegDetailsProposed.setStatusId(1);
        mktSegDetailsProposed.setProcessStatus(service.lookupProcessStatusById(ProcessStatus.NOT_APPROVED));
        mktSegDetailsProposed.setPackageValue(1);
        mktSegDetailsProposed.setForecastActivityType(forecastActivityType);
        mktSegDetailsProposed.setYieldType(service.lookupYieldType(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE));
        crudService.save(mktSegDetailsProposed);
        return mktSegDetailsProposed;
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAssignedMarketSegmentsFillDefaultAttributesIfNull() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary assignment = findFirstMarketSegment(
                service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        assert assignment != null;
        assignment.setRateCode(RATE_CODE_1);
        service.assignMarketSegments(Collections.singletonList(assignment),
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, forecastActivityTypeId);
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId);
        crudService.flushAndClear();
        int BookingBlockPercentage = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + "_QYL", BusinessTypeTransient, Yieldable,
                ForecastActivityTypeForDemandAndWash, Qualified, NonFenced, NonPackage, Link, NonPriceByBar,
                BookingBlockPercentage, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1, MARKET_SEGMENT_5 + "_QYL",
                attQualifiedNonBlockLinkedYieldable,
                RateCodeTypeEnum.EQUALS);
        assertOnMarketSegmentMasterForDefaultMarketSegment(MARKET_SEGMENT_5, IsEditable);
        assertOnAnalyticalMarketSegmentForDefaultMarketSegment(MARKET_SEGMENT_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);

        tenantCrudService().executeUpdateByNativeQuery("update Analytical_Mkt_Seg set Attribute = NULL where " +
                "Mapped_Market_Code = '" + MARKET_SEGMENT_5 + "_DEF'");
        List<Object[]> analyticalMktSegData = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg" +
                " where Mapped_Market_Code='" + MARKET_SEGMENT_5 + "_DEF' ");
        assertNull(analyticalMktSegData.get(0)[4]);
        crudService.flushAndClear();
        List<AnalyticalMarketSegment> segments = service.getAssignedMarketSegments();
        for (AnalyticalMarketSegment segment : segments) {
            if (segment.getMappedMarketCode().equalsIgnoreCase(MARKET_SEGMENT_5_DEF)) {
                assertNotNull(segment.getAttribute());
                assertEquals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, segment.getAttribute());
                assertEquals(ForecastActivityType.DEMAND_AND_WASH,
                        segment.getForecastActivityType().getId().longValue());
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testNGINeedsInitialRefresh() {
        setWorkContextProperty(TestProperty.H1);
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        when(pacmanConfigParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(Constants.NGI);
        Property property = new Property();
        property.setCode("NGI02");
        Client client = new Client();
        client.setCode("SandBox");
        property.setClient(client);
        when(propertyServiceLocal.getPropertyById(TestProperty.H1.getId())).thenReturn(property);
        when(restClient.getDataFromEndpoint(eq(RestEndpoints.FETCH_ONE_OF_NGI_OCCUPANCY_SUMMARIES), anyMap())).thenAnswer(new Answer<List<JSONObject>>() {
            private int count = 0;

            public List<JSONObject> answer(InvocationOnMock invocation) {
                if (count == 0) {
                    count++;
                    List<JSONObject> jsonObjects = new ArrayList<JSONObject>();
                    jsonObjects.add(new JSONObject());
                    return jsonObjects;
                }
                return new ArrayList<JSONObject>();
            }
        });

        assertTrue(service.needsInitialRefresh());
        assertFalse(service.needsInitialRefresh());
    }


    @Test
    public void verifyThatInitialRefreshIsRequiredOnlyWhenAmsOccupancySummaryIsEmptyAndHistoryTransactionTableHasSomeRecords() {
        setWorkContextProperty(TestProperty.H1);
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        insertHistoryTransaction();

        assertTrue(service.needsInitialRefresh());
    }

    @Test
    public void verifyThatInitialRefreshIsNotRequiredWhenBothAmsOccupancySummaryAndHistoryTransactionTablesAreEmpty() {
        setWorkContextProperty(TestProperty.H1);
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        when(pacmanConfigParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn("blah");

        assertFalse(service.needsInitialRefresh());
    }

    @Test
    public void verifyThatInitialRefreshIsNotRequiredWhenAmsOccupancySummaryTableIsNotEmpty() {
        setWorkContextProperty(TestProperty.H1);
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        List<AmsOccupancySummary> summaries = new ArrayList<>();
        summaries.add(createSummary(new Date(), MARKET_SEGMENT_1, RATE_CODE_1, CHECKED_IN, false));
        crudService.save(summaries);
        crudService.flush();

        assertFalse(service.needsInitialRefresh());
    }

    @Test
    public void testLoadAMSOccupancySummariesFromNGI() {
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        crudService.clear();
        List<Map<String, Object>> summaryList = new ArrayList<>(3);
        when(pacmanConfigParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        String fiscalDateValue = "2015-12-25";
        summaryList.add(createNGIOccupancySummaryMap("MS1", "RC1", 5, 100.0, 120.0, true, fiscalDateValue));
        summaryList.add(createNGIOccupancySummaryMap("MS2", "RC2", null, 100.0, 120.0, false, fiscalDateValue));
        summaryList.add(createNGIOccupancySummaryMap("MS3", "RC3", 5, null, null, false, fiscalDateValue));
        service.loadDataFromNGI(summaryList);
        List<AnalyticalMarketSegmentSummary> groupMarketSegments = service.getUnassignedGroupMarketSegments();
        assertTrue(groupMarketSegments.size() == 1);
        assertEquals("MS1", groupMarketSegments.get(0).getMarketCode());

        List<AnalyticalMarketSegmentSummary> individualMarketSegments = service.getUnassignedIndividualMarketSegments();
        assertTrue(individualMarketSegments.size() == 2);
        //Because the unassignedIndiviualMarketSegments are sorted we get MS3 before MS2 though they should have been
        // inserted correctly.
        assertEquals("MS3", individualMarketSegments.get(0).getMarketCode());
        assertEquals(new Integer(5), individualMarketSegments.get(0).getRoomsSold());
        assertEquals(new Double(0.0), individualMarketSegments.get(0).getRoomRevenue());
        assertEquals("MS2", individualMarketSegments.get(1).getMarketCode());
        assertEquals(new Integer(0), individualMarketSegments.get(1).getRoomsSold());
        assertEquals(new Double(100.0), individualMarketSegments.get(1).getRoomRevenue());
    }

    @Test
    public void testLoadAMSOccupancySummariesFromNGI_TotalRateEnabled() {
        crudService.executeUpdateByNamedQuery(AmsOccupancySummary.DELETE_ALL);
        crudService.clear();
        List<Map<String, Object>> summaryList = new ArrayList<>(3);
        when(pacmanConfigParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(1);
        String fiscalDateValue = "2015-12-25";
        summaryList.add(createNGIOccupancySummaryMap("MS1", "RC1", 5, 100.0, 120.0, true, fiscalDateValue));
        summaryList.add(createNGIOccupancySummaryMap("MS2", "RC2", null, 100.0, 120.0, false, fiscalDateValue));
        summaryList.add(createNGIOccupancySummaryMap("MS3", "RC3", 5, null, null, false, fiscalDateValue));
        service.loadDataFromNGI(summaryList);
        List<AnalyticalMarketSegmentSummary> groupMarketSegments = service.getUnassignedGroupMarketSegments();
        assertTrue(groupMarketSegments.size() == 1);
        assertEquals("MS1", groupMarketSegments.get(0).getMarketCode());

        List<AnalyticalMarketSegmentSummary> individualMarketSegments = service.getUnassignedIndividualMarketSegments();
        assertTrue(individualMarketSegments.size() == 2);
        //Because the unassignedIndiviualMarketSegments are sorted we get MS3 before MS2 though they should have been
        // inserted correctly.
        assertEquals("MS3", individualMarketSegments.get(0).getMarketCode());
        assertEquals(new Integer(5), individualMarketSegments.get(0).getRoomsSold());
        assertEquals(new Double(0.0), individualMarketSegments.get(0).getRoomRevenue());
        assertEquals("MS2", individualMarketSegments.get(1).getMarketCode());
        assertEquals(new Integer(0), individualMarketSegments.get(1).getRoomsSold());
        assertEquals(new Double(120.0), individualMarketSegments.get(1).getRoomRevenue());
    }

    @Test
    public void assignRateCodeToDefaultTest() {
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode("TEST");
        ams.setMappedMarketCode("TEST_DEF");
        ams.setRateCodeType(RateCodeTypeEnum.DEFAULT);
        ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        crudService.save(ams);

        MarketSegmentMaster defaultMaster = new MarketSegmentMaster();
        defaultMaster.setForecastActivityTypeId(1);
        defaultMaster.setCode("TEST_DEF");
        defaultMaster.setIsEditable(1);
        crudService.save(defaultMaster);

        ForecastActivityType demandAndWash = new ForecastActivityType();
        demandAndWash.setId(ForecastActivityType.DEMAND_AND_WASH);

        service.assignRateCodeToDefault("ABC", "TEST", ams, demandAndWash);

        Map<String, Object> params = QueryParameter.with("mappedMarketCode", "TEST_DEF").parameters();
        List<AnalyticalMarketSegment> amsList =
                crudService.findByNamedQuery(AnalyticalMarketSegment.LIKE_MAPPED_MARKET_CODE, params);

        assertEquals(2, amsList.size());
        assertEquals("TEST_DEF", amsList.get(0).getMappedMarketCode());
        assertEquals("TEST", amsList.get(0).getMarketCode());
        assertNull(amsList.get(0).getRateCode());
        assertTrue(amsList.get(0).isComplimentary());

        assertEquals("TEST_DEF", amsList.get(1).getMappedMarketCode());
        assertEquals("TEST", amsList.get(1).getMarketCode());
        assertEquals("ABC", amsList.get(1).getRateCode());
        assertTrue(amsList.get(1).isComplimentary());
    }

    @Test
    public void assignMarketSegmentWiseRateCodesToDefaultTest() {
        // this is a straight ms, must be ignored by the api
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode("TEST2");
        ams.setMappedMarketCode("TEST2");
        ams.setRateCodeType(RateCodeTypeEnum.ALL);
        ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        crudService.save(ams);

        ams = new AnalyticalMarketSegment();
        ams.setMarketCode("TEST");
        ams.setMappedMarketCode("TEST_DEF");
        ams.setRateCodeType(RateCodeTypeEnum.DEFAULT);
        ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        crudService.save(ams);

        MarketSegmentMaster defaultMaster = new MarketSegmentMaster();
        defaultMaster.setForecastActivityTypeId(1);
        defaultMaster.setCode("TEST_DEF");
        defaultMaster.setIsEditable(1);
        crudService.save(defaultMaster);

        ForecastActivityType demandAndWash = new ForecastActivityType();
        demandAndWash.setId(ForecastActivityType.DEMAND_AND_WASH);

        Map<String, Set<String>> mktSegWiseRateCodes = new HashMap<>();
        mktSegWiseRateCodes.put("TEST", Collections.singleton("ABC"));
        mktSegWiseRateCodes.put("TEST2", Collections.singleton("ABC2"));
        service.assignMarketSegmentWiseRateCodesToDefault(mktSegWiseRateCodes);

        Map<String, Object> params = QueryParameter.with("mappedMarketCode", "TEST_DEF").parameters();
        List<AnalyticalMarketSegment> amsList =
                crudService.findByNamedQuery(AnalyticalMarketSegment.LIKE_MAPPED_MARKET_CODE, params);

        amsList.sort(Comparator.comparing(AnalyticalMarketSegment::getRateCodeType));
        assertEquals(2, amsList.size());

        assertEquals("TEST_DEF", amsList.get(0).getMappedMarketCode());
        assertEquals("TEST", amsList.get(0).getMarketCode());
        assertEquals("ABC", amsList.get(0).getRateCode());
        assertEquals(RateCodeTypeEnum.EQUALS, amsList.get(0).getRateCodeType());
        assertTrue(amsList.get(0).isComplimentary());

        assertEquals("TEST_DEF", amsList.get(1).getMappedMarketCode());
        assertEquals("TEST", amsList.get(1).getMarketCode());
        assertNull(amsList.get(1).getRateCode());
        assertEquals(RateCodeTypeEnum.DEFAULT, amsList.get(1).getRateCodeType());
        assertTrue(amsList.get(1).isComplimentary());
    }

    @Test
    public void assignMarketSegmentWiseRateCodesToDefaultUsingProduct() {
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode("TEST2");
        ams.setMappedMarketCode("TEST2");
        ams.setRateCodeType(RateCodeTypeEnum.ALL);
        ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        crudService.save(ams);
        ams = new AnalyticalMarketSegment();
        ams.setMarketCode("TEST");
        ams.setMappedMarketCode("TEST_DEF");
        ams.setRateCodeType(RateCodeTypeEnum.DEFAULT);
        ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        crudService.save(ams);
        MarketSegmentMaster defaultMaster = new MarketSegmentMaster();
        defaultMaster.setForecastActivityTypeId(1);
        defaultMaster.setCode("TEST_DEF");
        defaultMaster.setIsEditable(1);
        crudService.save(defaultMaster);
        ForecastActivityType demandAndWash = new ForecastActivityType();
        demandAndWash.setId(ForecastActivityType.DEMAND_AND_WASH);
        Map<String, Set<String>> mktSegWiseRateCodes = new HashMap<>();
        mktSegWiseRateCodes.put("TEST", Collections.singleton("ABC"));
        mktSegWiseRateCodes.put("TEST2", Collections.singleton("ABC2"));
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.assignMarketSegmentWiseRateCodesToDefault(mktSegWiseRateCodes);

        Map<String, Object> params = QueryParameter.with("mappedMarketCode", "TEST_DEF").parameters();
        List<AnalyticalMarketSegment> amsList =
                crudService.findByNamedQuery(AnalyticalMarketSegment.LIKE_MAPPED_MARKET_CODE, params);
        amsList.sort(Comparator.comparing(AnalyticalMarketSegment::getRateCodeType));
        assertEquals(2, amsList.size());
        assertEquals("TEST_DEF", amsList.get(0).getMappedMarketCode());
        assertEquals("TEST", amsList.get(0).getMarketCode());
        assertEquals("ABC", amsList.get(0).getRateCode());
        assertEquals(RateCodeTypeEnum.EQUALS, amsList.get(0).getRateCodeType());
        assertTrue(amsList.get(0).isComplimentary());
        assertEquals("TEST_DEF", amsList.get(1).getMappedMarketCode());
        assertEquals("TEST", amsList.get(1).getMarketCode());
        assertNull(amsList.get(1).getRateCode());
        assertEquals(RateCodeTypeEnum.DEFAULT, amsList.get(1).getRateCodeType());
        assertTrue(amsList.get(1).isComplimentary());
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        ArgumentCaptor<String> marketCodesCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).assignRateCodesFromMarketSegmentToTheMappedIndependentProduct(marketCodesCaptor.capture(), rateCodesCaptor.capture());
        List<String> marketCodes = new ArrayList<>(marketCodesCaptor.getAllValues());
        marketCodes.sort(String::compareTo);
        assertEquals("TEST_DEF", marketCodes.get(0));
        assertEquals(1, rateCodesCaptor.getAllValues().get(0).size());
    }

    @Test
    public void getUnassignedSharedMarketSegmentsTest_nonEmptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedSharedMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "TEST_MS", "TEST_RC", 0, 0, 0, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedSharedMarketSegments();

        assertEquals(5, beforeList.size());
        assertEquals(6, afterList.size());
    }

    @Test
    public void getUnassignedSharedMarketSegmentsTest_emptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedSharedMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "", "TEST_RC", 0, 0, 0, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedSharedMarketSegments();

        assertEquals(beforeList.size(), afterList.size());
    }

    @Test
    public void getUnassignedIndividualMarketSegmentsTest_nonEmptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedIndividualMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "TEST_MS", "TEST_RC", 0, 0, 0, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedIndividualMarketSegments();

        assertEquals(5, beforeList.size());
        assertEquals(6, afterList.size());
    }

    @Test
    public void getUnassignedIndividualMarketSegmentsTest_emptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedIndividualMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "", "TEST_RC", 0, 0, 0, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedIndividualMarketSegments();

        assertEquals(beforeList.size(), afterList.size());
    }

    @Test
    public void getUnassignedGroupMarketSegmentsTest_nonEmptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedGroupMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "TEST_MS", "TEST_RC", 0, 0, 1, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedGroupMarketSegments();

        assertEquals(3, beforeList.size());
        assertEquals(4, afterList.size());
    }

    @Test
    public void getUnassignedGroupMarketSegmentsTest_emptyMS() {
        List<AnalyticalMarketSegmentSummary> beforeList = service.getUnassignedGroupMarketSegments();

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "", "TEST_RC", 0, 0, 1, 1, 1.29, "CHECKED OUT");

        List<AnalyticalMarketSegmentSummary> afterList = service.getUnassignedGroupMarketSegments();

        assertEquals(beforeList.size(), afterList.size());
    }

    @Test
    public void hasAllGroupAndIndividualMarketSegmentsMapped_nonEmptyMS() {
        assertFalse(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        List<AnalyticalMarketSegmentSummary> unassignedIndividual = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassignedIndividual, AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);

        List<AnalyticalMarketSegmentSummary> unassignedShared = service.getUnassignedSharedMarketSegments();
        service.assignMarketSegments(unassignedShared, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        List<AnalyticalMarketSegmentSummary> unassignedGroup = service.getUnassignedGroupMarketSegments();
        service.assignMarketSegments(unassignedGroup, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "TEST_MS", "TEST_RC", 0, 0, 1, 1, 1.29, "CHECKED OUT");

        assertFalse(service.hasAllGroupAndIndividualMarketSegmentsMapped());
    }

    @Test
    public void hasAllGroupAndIndividualMarketSegmentsMapped_emptyMS() {
        assertFalse(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        List<AnalyticalMarketSegmentSummary> unassignedIndividual = service.getUnassignedIndividualMarketSegments();
        service.assignMarketSegments(unassignedIndividual, AnalyticalMarketSegmentAttribute.FENCED,
                forecastActivityTypeId);

        List<AnalyticalMarketSegmentSummary> unassignedShared = service.getUnassignedSharedMarketSegments();
        service.assignMarketSegments(unassignedShared, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        List<AnalyticalMarketSegmentSummary> unassignedGroup = service.getUnassignedGroupMarketSegments();
        service.assignMarketSegments(unassignedGroup, AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId);

        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());

        StringBuilder insertQuery = new StringBuilder();
        populateAmsOccupancySummary(insertQuery, "", "TEST_RC", 0, 0, 1, 1, 1.29, "CHECKED OUT");

        assertTrue(service.hasAllGroupAndIndividualMarketSegmentsMapped());
    }

    private Map<String, Object> createNGIOccupancySummaryMap(String marketSegmentCode, String rateCode,
                                                             Integer roomsSold, Double roomRevenue, Double roomRate, boolean group, String occupancyDate) {
        Map<String, Object> summaryMap = new HashMap<>();
        summaryMap.put("marketSegmentCode", marketSegmentCode);
        summaryMap.put("rateCode", rateCode);
        summaryMap.put("roomsSold", roomsSold);
        summaryMap.put("roomRevenue", roomRevenue);
        summaryMap.put("roomRate", roomRate);
        summaryMap.put("groupOccupancy", group);
        summaryMap.put("occupancyDate", occupancyDate);
        return summaryMap;
    }

    private MktSeg insertMktSeg(int propertyId, String segmentCode, String segmentName, String segmentDescription) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setPropertyId(propertyId);
        mktSeg.setCode(segmentCode);
        mktSeg.setName(segmentName);
        mktSeg.setDescription(segmentDescription);
        mktSeg.setStatusId(1);
        crudService.save(mktSeg);
        return mktSeg;
    }

    private void insertHistoryTransaction() {
        String historyInsert = "INSERT INTO opera.History_Transaction (Confirmation_Number,Reservation_Status," +
                "Rate_Code,Market_Code,Data_Load_Metadata_ID) " +
                " VALUES ('xbc1234', 'RESERVED', 'RC1', 'MS1',(select Data_Load_Metadata_ID from opera" +
                ".Data_Load_Metadata where Correlation_ID = 'xxx1234'))";
        String dataLoadMetatdataInsert = "INSERT INTO opera.Data_Load_Metadata (Correlation_ID," +
                "Incoming_File_Type_Code,Create_DT) " +
                " VALUES ('xxx1234', 'junk', GETDATE())";

        crudService.executeUpdateByNativeQuery(dataLoadMetatdataInsert);
        crudService.executeUpdateByNativeQuery(historyInsert);
    }


    @SuppressWarnings("unchecked")
    private void assertPreview(List<PreviewMarketSegment> list, final String mappedCode,
                               Integer roomsSold, Double roomRevenue, Double avgDailyRate, Double hotelPct) {
        Collection<PreviewMarketSegment> found = CollectionUtils.select(list,
                arg0 -> ((PreviewMarketSegment) arg0).getMappedMarketCode().equals(mappedCode));

        assertEquals(1, found.size());
        PreviewMarketSegment segment = found.iterator().next();
        assertEquals(mappedCode, segment.getMappedMarketCode());
        assertEquals(roomsSold, segment.getRoomsSold());
        assertEquals(new BigDecimal(roomRevenue).setScale(2, RoundingMode.HALF_UP), segment.getRoomRevenue());
        assertEquals(new BigDecimal(avgDailyRate).setScale(2, RoundingMode.HALF_UP), segment.getAdr());
        assertEquals(new BigDecimal(hotelPct).setScale(2, RoundingMode.HALF_UP), segment.getHotelPercent());
        assertNull(segment.getAttribute());
        assertFalse(segment.isComplimentary());
    }

    private AnalyticalMarketSegmentSummary createAssignment(String marketCode, RateCodeTypeEnum rateCodeType,
                                                            String rateCode,
                                                            AnalyticalMarketSegmentAttribute attribute,
                                                            String mappedCode) {
        AnalyticalMarketSegmentSummary rule = new AnalyticalMarketSegmentSummary();
        rule.setMarketCode(marketCode);
        rule.setRateCode(rateCode);
        rule.setMappedCode(mappedCode);
        rule.setRateCodeType(rateCodeType.toString());
        service.assignMarketSegments(Collections.singletonList(rule), attribute, forecastActivityTypeId);
        return rule;
    }

    @SuppressWarnings("rawtypes")
    private String getMktId(String mktSegmentName) {
        List mktIdList =
                crudService.findByNativeQuery("select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code='" + mktSegmentName + "' ");
        return mktIdList.get(0).toString();
    }

    private AnalyticalMarketSegment getAssignmentByMappedCode(String mappedCode) {
        try {
            TypedQuery<AnalyticalMarketSegment> query = crudService.getEntityManager().createQuery(
                    " from AnalyticalMarketSegment ams where ams.mappedMarketCode = :mappedCode",
                    AnalyticalMarketSegment.class);
            query.setParameter("mappedCode", mappedCode);
            return query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    private MktSeg getMktSegByCode(String codeIn) {
        try {
            TypedQuery<MktSeg> query = crudService.getEntityManager().createQuery(
                    " from MktSeg ms where ms.code = :code",
                    MktSeg.class);
            query.setParameter("code", codeIn);
            return query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    private MktSeg findMktSegByCode(String mktSegCodeIn) {
        MktSeg mktSeg;

        // We need to find the MktSeg so we can assign it to the MktSegDetailsProposed we are creating.
        Map<String, Object> mktSegParameters = QueryParameter.with("code", mktSegCodeIn).parameters();
        mktSeg = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE, mktSegParameters);
        return mktSeg;
    }

    private MarketSegmentMaster getMasterByMappedCode(String mappedCode) {
        try {
            TypedQuery<MarketSegmentMaster> query = crudService.getEntityManager().createQuery(
                    " from MarketSegmentMaster msm where msm.code = :mappedCode",
                    MarketSegmentMaster.class);
            query.setParameter("mappedCode", mappedCode);
            return query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    private MktSegDetailsProposed getMktSegDetailsProposedByMktSeg(MktSeg mktSegIn) {
        try {
            TypedQuery<MktSegDetailsProposed> query = crudService.getEntityManager().createQuery(
                    " from MktSegDetailsProposed msdp where msdp.mktSeg = :mktSeg",
                    MktSegDetailsProposed.class);
            query.setParameter("mktSeg", mktSegIn);
            return query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    private List<AnalyticalMarketSegmentSummary> createAnalyticalMarketSegment(boolean rateLevelAssignment,
                                                                               RateCodeTypeEnum rateCodeType, AmsOccupancySummary... list) {
        List<AnalyticalMarketSegmentSummary> results = new ArrayList<>();

        for (AmsOccupancySummary amsOccupancySummary : list) {
            AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
            summary.setMarketCode(amsOccupancySummary.getMarketCode());
            if (rateLevelAssignment) {
                summary.setRateCode(amsOccupancySummary.getRateCode().toLowerCase());
                summary.setRateCodeType(rateCodeType.toString());
            }
            summary.setBlockPercent(BOOKING_BLOCK_PCT_DEFAULT);
            results.add(summary);
        }
        return results;
    }

    private AmsOccupancySummary createSummary(Date occupancyDate, String marketCode, String rateCode, String status,
                                              boolean isGroup) {
        AmsOccupancySummary summary = new AmsOccupancySummary();
        summary.setDataLoadMetadataID(1);
        summary.setOccupancyDate(occupancyDate);
        summary.setMarketCode(marketCode);
        summary.setRateCode(rateCode);
        summary.setReservationStatus(status);
        summary.setGroup(isGroup);

        if (isGroup) {
            summary.setBlock(1);
            summary.setPickup(1);
        } else {
            if (status.equals(CANCELLED)) {
                summary.setRoomsSold(0);
                summary.setRoomRevenue(0.0);
            } else {
                summary.setRoomsSold(1);
                summary.setRoomRevenue(120.0);
            }
        }

        return summary;
    }

    private boolean contains(List<AnalyticalMarketSegmentSummary> list, String marketCode) {
        for (AnalyticalMarketSegmentSummary summary : list) {
            if (marketCode.equals(summary.getMarketCode())) {
                return true;
            }
        }
        return false;
    }

    private AnalyticalMarketSegmentSummary findFirstMarketSegment(
            List<AnalyticalMarketSegmentSummary> list, String marketCode) {

        for (AnalyticalMarketSegmentSummary summary : list) {
            if (marketCode.equals(summary.getMarketCode())) {
                return summary;
            }
        }

        return null;
    }

    private AnalyticalMarketSegmentSummary createDefaultMarketSegment(String marketSegment) {
        AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
        summary.setMarketCode(marketSegment);
        summary.setMappedCode(marketSegment + "_DEF");
        summary.setRateCodeType(RateCodeTypeEnum.DEFAULT.toString());
        return summary;
    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentMaster(String mktCode, int BusinessType, int Yield, int ForecastActivityType,
                                             int Qualified,
                                             int Fenced, int Package, int Link, int PriceByBar,
                                             int BookingBlockPercentage, int Editable) {

        List<Object[]> mktSegMasterData = tenantCrudService().findByNativeQuery("select * from Mkt_Seg_Master ");
        assertEquals(mktCode, mktSegMasterData.get(0)[1], "Market Segment Code - ");
        assertEquals(mktCode, mktSegMasterData.get(0)[2], "Market Segment Name - ");
        assertEquals(BusinessType, mktSegMasterData.get(0)[4], "Business Type - ");
        assertEquals(Yield, mktSegMasterData.get(0)[5], "Yield Type - ");
        assertEquals(ForecastActivityType, mktSegMasterData.get(0)[6], "Forecast Activity Type - ");
        assertEquals(Qualified, mktSegMasterData.get(0)[7], "Qualified - ");
        assertEquals(Fenced, mktSegMasterData.get(0)[8], "Fenced - ");
        assertEquals(Package, mktSegMasterData.get(0)[9], "Package - ");
        assertEquals(Link, mktSegMasterData.get(0)[10], "Link - ");
        assertEquals(PriceByBar, mktSegMasterData.get(0)[11], "Price By BAR - ");
        assertEquals(BookingBlockPercentage, mktSegMasterData.get(0)[12], "Booking Block Percentage - ");
        assertEquals(Editable, mktSegMasterData.get(0)[13], "Is Editable - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentMasterAfterUpdate(String mktCode, int BusinessType, int Yield,
                                                        int ForecastActivityType, int Qualified,
                                                        int Fenced, int Package, int Link, int PriceByBar,
                                                        int BookingBlockPercentage, int Editable) {

        List<Object[]> mktSegMasterData = tenantCrudService().findByNativeQuery("select * from Mkt_Seg_Master where " +
                "Mkt_Seg_Code='" + mktCode + "' ");
        assertEquals(mktCode, mktSegMasterData.get(0)[1], "Market Segment Code - ");

        assertEquals(mktCode, mktSegMasterData.get(0)[2], "Market Segment Name - ");

        assertEquals(BusinessType, mktSegMasterData.get(0)[4], "Business Type - ");

        assertEquals(Yield, mktSegMasterData.get(0)[5], "Yield Type - ");

        assertEquals(ForecastActivityType, mktSegMasterData.get(0)[6], "Forecast Activity Type - ");

        assertEquals(Qualified, mktSegMasterData.get(0)[7], "Qualified - ");

        assertEquals(Fenced, mktSegMasterData.get(0)[8], "Fenced - ");

        assertEquals(Package, mktSegMasterData.get(0)[9], "Package - ");

        assertEquals(Link, mktSegMasterData.get(0)[10], "Link - ");

        assertEquals(PriceByBar, mktSegMasterData.get(0)[11], "Price By BAR - ");

        assertEquals(BookingBlockPercentage, mktSegMasterData.get(0)[12], "Booking Block Percentage - ");

        assertEquals(Editable, mktSegMasterData.get(0)[13], "Is Editable - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentMasterForDefaultMarketSegment(String mktCode, int Editable) {

        List<Object[]> mktSegMasterData = tenantCrudService().findByNativeQuery("select * from Mkt_Seg_Master where " +
                "Mkt_Seg_Code='" + mktCode + "_DEF' ");
        assertEquals(mktCode + "_DEF", mktSegMasterData.get(0)[1], "Market Segment Code - ");

        assertEquals(mktCode + "_DEF", mktSegMasterData.get(0)[2], "Market Segment Name - ");

        //        assertEquals("Business Type - ", null,mktSegMasterData.get(0)[4]);
//        assertEquals("Yield Type - ",null,mktSegMasterData.get(0)[5]);
//        assertEquals("Forecast Activity Type - ",null,mktSegMasterData.get(0)[6]);
//        assertEquals("Qualified - ",null,mktSegMasterData.get(0)[7]);
//        assertEquals("Fenced - ",null,mktSegMasterData.get(0)[8]);
//        assertEquals("Package - ",null,mktSegMasterData.get(0)[9]);
//        assertEquals("Link - ",null,mktSegMasterData.get(0)[10]);
//        assertEquals("Price By BAR - ",0,mktSegMasterData.get(0)[11]);
//        assertEquals("Booking Block Percentage - ",null,mktSegMasterData.get(0)[12]);
        assertEquals(Editable, mktSegMasterData.get(0)[13], "Is Editable - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnDefaultMarketSegmentCountInAnalyticalMS(Integer defaultCount) {
        List<Integer> AnalyticalMktSegData = tenantCrudService().findByNativeQuery(
                "select count(*) from Analytical_Mkt_Seg where Mapped_Market_Code like '%DEF%' ");
        assertEquals(defaultCount, (Object) AnalyticalMktSegData.get(0), "Count for Default Market Segment in " +
                "Analytical_Mkt_Seg table - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnAnalyticalMarketSegmentForDefaultMarketSegment(String mktCode,
                                                                        AnalyticalMarketSegmentAttribute attribute) {
        List<Object[]> AnalyticalMktSegData = tenantCrudService().findByNativeQuery(
                "select * from Analytical_Mkt_Seg where Mapped_Market_Code='" + mktCode + "_DEF' ");
        assertEquals(mktCode, AnalyticalMktSegData.get(0)[1], "Market Segment Code - ");

        assertEquals(null, AnalyticalMktSegData.get(0)[2], "Rate Code - ");

        assertEquals(attribute.toString(), AnalyticalMktSegData.get(0)[4], "Attribute - ");

        assertEquals(RateCodeTypeEnum.DEFAULT.toString(), AnalyticalMktSegData.get(0)[9], "Rate Code Type  - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnDefaultMarketSegmentCount(Integer defaultCount) {
        List<Integer> mktSegMasterData = tenantCrudService().findByNativeQuery(
                "select count(*) from Mkt_Seg_Master where mkt_seg_code like '%DEF%' or Mkt_Seg_Name like '%DEF%' or " +
                        "Mkt_Seg_Description like '%DEF%'");
        assertEquals(defaultCount, (Object) mktSegMasterData.get(0), "Count for Default Market Segment - ");
    }

    @SuppressWarnings("unchecked")
    private void assertOnAnalyticalMarketSegment(String attribute, RateCodeTypeEnum rateCodeType) {

        List<Object[]> analyticalAktSegData = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg" +
                " ");
        assertEquals(analyticalAktSegData.get(0)[1], analyticalAktSegData.get(0)[3], "Market Code - ");
        assertEquals(null, analyticalAktSegData.get(0)[2], "Rate Code - ");
        assertEquals(attribute, analyticalAktSegData.get(0)[4], "Attribute - ");
        assertEquals(rateCodeType.toString(), analyticalAktSegData.get(0)[9], "Rate Code Type - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnAnalyticalMarketSegmentRowCount(Integer expectedRowCount) {
        List<Integer> analyticalMktSegData = tenantCrudService().findByNativeQuery("select count(*) from " +
                "Analytical_Mkt_Seg ");
        assertEquals(expectedRowCount, (Object) analyticalMktSegData.get(0), "Count for Analytical Market Segment - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentMasterRowCount(Integer expectedRowCount) {
        List<Integer> mktSegMasterData = tenantCrudService().findByNativeQuery("select count(*) from Mkt_Seg_Master ");
        assertEquals(expectedRowCount, (Object) mktSegMasterData.get(0), "Count for Market Segment Master - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentDetailsProposedRowCount(Integer expectedRowCount) {
        List<Integer> mktSegDetailsProposedData = tenantCrudService().findByNativeQuery("select count(*) from " +
                "Mkt_Seg_Details_Proposed ");
        assertEquals(expectedRowCount, (Object) mktSegDetailsProposedData.get(0), "Count for Market Segment Details " +
                "Proposed - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnYieldCategoryByRuleRowCount(Integer expectedRowCount) {
        List<Integer> yieldCategoryByRuleData = tenantCrudService().findByNativeQuery("select count(*) from opera" +
                ".Yield_Category_Rule ");
        assertEquals(expectedRowCount, (Object) yieldCategoryByRuleData.get(0), "Rules Created under Yield Category " +
                "By Rule - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnYieldCategoryByRule(String marketCode, String rateCode, String analyticMarketCode) {
        List<Object[]> yieldCategoryByRuleData = tenantCrudService().findByNativeQuery("select * from opera" +
                        ".Yield_Category_Rule where Market_Code=:marketCode and Rate_Code=:rateCode and " +
                        "Analytical_Market_Code=:analyticMarketCode ",
                QueryParameter.with("marketCode", marketCode).and("rateCode", rateCode).and("analyticMarketCode",
                        analyticMarketCode).parameters());
        assertEquals(marketCode, yieldCategoryByRuleData.get(0)[1], "Market Code  ");
        assertEquals(rateCode, yieldCategoryByRuleData.get(0)[2], "Rate Code  ");
        assertEquals(analyticMarketCode, yieldCategoryByRuleData.get(0)[3], "Analytical Market Code  ");

    }

    private void assertOnPreview(List<PreviewMarketSegment> results, String mappedmarketCode,
                                 AnalyticalMarketSegmentAttribute attribute, int roomSold, double revenue, double adr, double hotelPercentage) {
        PreviewMarketSegment previewMarketSegment = null;
        for (PreviewMarketSegment result : results) {
            if (result.getMappedMarketCode().equals(mappedmarketCode)) {
                previewMarketSegment = result;
                break;
            }
        }
        assertNotNull(previewMarketSegment, "No record found with key: " + mappedmarketCode);
        assertEquals(mappedmarketCode, previewMarketSegment.getMappedMarketCode(), "Mapped Market Code  ");
        assertEquals(attribute, previewMarketSegment.getAttribute(), "Attribute  ");
        assertEquals(roomSold, (Object) (int) previewMarketSegment.getRoomsSold(), "Room Sold  ");
        assertEquals(new BigDecimal(revenue).setScale(2, RoundingMode.HALF_UP), previewMarketSegment.getRoomRevenue()
                , "Revenue  ");
        assertEquals(new BigDecimal(adr).setScale(2, RoundingMode.HALF_UP), previewMarketSegment.getAdr(), "ADR  ");
        assertEquals(new BigDecimal(hotelPercentage).setScale(2, RoundingMode.HALF_UP),
                previewMarketSegment.getHotelPercent(), "Hotel Percentage  ");

    }

    private void assertOnPreview(List<Object[]> results, String mappedmarketCode, String attribute, int roomSold,
                                 double revenue, double adr, double hotelPercentage) {
        Object[] obj = null;
        for (Object[] result : results) {
            if (mappedmarketCode.equals(result[0])) {
                obj = result;
            }
        }
        if (obj == null) {
            fail("No result found with mappedMarketCode of " + mappedmarketCode);
        }
        assertEquals(mappedmarketCode, obj[0], "Mapped Market Code  ");
        assertEquals(attribute, obj[1], "Attribute  ");
        assertEquals(roomSold, obj[2], "Room Sold  ");
        assertEquals(new BigDecimal(revenue).setScale(2, RoundingMode.HALF_UP), obj[3], "Revenue  ");
        assertEquals(new BigDecimal(adr).setScale(2, RoundingMode.HALF_UP), obj[4], "ADR  ");
        assertEquals(new BigDecimal(hotelPercentage).setScale(2, RoundingMode.HALF_UP), obj[5], "Hotel Percentage  ");

    }

    private void assertOnPreviewToValidateAttribute(List<Object[]> result, int recordNumber,
                                                    String attributeDescription) {
        assertEquals(attributeDescription, result.get(recordNumber)[0], "Attribute Description  ");

    }

    private void assertOnPreviewToValidateAttribute(List<Object[]> results, String attributeDescription,
                                                    String mappedCode) {
        for (Object[] result : results) {
            if (mappedCode.equalsIgnoreCase((String) result[1])) {
                assertEquals(attributeDescription, result[0], "Attribute Description  ");

                return;
            }
        }
        fail("No values found with expected mapped code " + mappedCode);
    }

    @SuppressWarnings("unchecked")
    private void assertOnAnalyticalMarketSegmentForRateCodeLevel(String MarketCode, String RateCode,
                                                                 String MappedMarketCode, String Attribute,
                                                                 RateCodeTypeEnum rateCodeType) {
        List<Object[]> analyticalAktSegData = tenantCrudService().findByNativeQuery(
                "select * from Analytical_Mkt_Seg where Mapped_Market_Code = '" + MappedMarketCode + "'");
        assertEquals(MarketCode, analyticalAktSegData.get(0)[1], "Market Code - ");
        assertEquals(RateCode, analyticalAktSegData.get(0)[2], "Rate Code - ");
        assertEquals(MappedMarketCode, analyticalAktSegData.get(0)[3], "Mapped Market Code - ");
        assertEquals(Attribute, analyticalAktSegData.get(0)[4], "Attribute - ");
        assertEquals(rateCodeType.toString(), analyticalAktSegData.get(0)[9], "Rate Code Type - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnAnalyticalMarketSegmentForRateCodeLevelAfterUpdate(String MarketCode, String RateCode,
                                                                            String MappedMarketCode, String Attribute,
                                                                            RateCodeTypeEnum rateCodeType) {
        List<Object[]> analyticalAktSegData = tenantCrudService().findByNativeQuery(
                "select * from Analytical_Mkt_Seg where Mapped_Market_Code='" + MappedMarketCode + "' ");
        assertEquals(MarketCode, analyticalAktSegData.get(0)[1], "Market Code - ");
        assertEquals(RateCode, analyticalAktSegData.get(0)[2], "Rate Code - ");
        assertEquals(MappedMarketCode, analyticalAktSegData.get(0)[3], "Mapped Market Code - ");
        assertEquals(Attribute, analyticalAktSegData.get(0)[4], "Attribute - ");
        assertEquals(rateCodeType.toString(), analyticalAktSegData.get(0)[9], "Rate Code Type - ");

    }

    @SuppressWarnings("unchecked")
    private void assertOnMarketSegmentDetailsProposed(String mktID, int bizTypeId, int yieldTypeId,
                                                      int forecastActivityTypeId, int isQualifiedValue, int blockPercentageValue,
                                                      int fencedValue, int packageValue, int linkValue,
                                                      int processStatusID, int statusId, int priceByBarValue) {
        List<Object[]> MarketSegmentDetailsProposedData = tenantCrudService().findByNativeQuery(
                "select * from Mkt_Seg_Details_Proposed ");
        assertEquals(mktID, MarketSegmentDetailsProposedData.get(0)[1].toString(), "Market Segment ID - ");
        assertEquals(bizTypeId, MarketSegmentDetailsProposedData.get(0)[2], "Business Type ID - ");
        assertEquals(yieldTypeId, MarketSegmentDetailsProposedData.get(0)[3], "Yield Type ID - ");
        assertEquals(forecastActivityTypeId, MarketSegmentDetailsProposedData.get(0)[4], "Forecast Activity Type ID -" +
                " ");
        assertEquals(isQualifiedValue, MarketSegmentDetailsProposedData.get(0)[5], "Qualified - ");
        assertEquals(blockPercentageValue, MarketSegmentDetailsProposedData.get(0)[6], "Booking Block Percentage - ");
        assertEquals(fencedValue, MarketSegmentDetailsProposedData.get(0)[7], "Fenced - ");
        assertEquals(packageValue, MarketSegmentDetailsProposedData.get(0)[8], "Package - ");
        assertEquals(linkValue, MarketSegmentDetailsProposedData.get(0)[9], "Link - ");
        assertEquals(processStatusID, MarketSegmentDetailsProposedData.get(0)[12], "Process Status ID - ");
        assertEquals(statusId, MarketSegmentDetailsProposedData.get(0)[15], "Status ID - ");
        assertEquals(priceByBarValue, MarketSegmentDetailsProposedData.get(0)[17], "Price By BAR - ");

    }

    private void setStageAsOneWayToH1() {
        globalCrudService().executeUpdateByNativeQuery("update property set Stage='ONE_WAY' where property_code='H1'");
    }

    private void populateMarketSegmentDetails(StringBuilder insertQuery2, String mktId, int bizTypeValue,
                                              int yieldableValue,
                                              int forecastTypeValue, int qualifiedValue, int fencedValue,
                                              int packageValue, int linkValue, int priceByBarValue,
                                              int processStatusIDValue, int statusIdValue) {
        insertQuery2.append("INSERT INTO [Mkt_Seg_Details] ");
        insertQuery2.append("([Mkt_Seg_ID],[Business_Type_ID],[Yield_Type_ID],[Forecast_Activity_Type_ID],[Qualified]" +
                " ");
        insertQuery2.append(",[Booking_Block_Pc],[Fenced],[Package],[Link],[Template_ID],[Template_Default]," +
                "[Process_Status_ID] ");
        insertQuery2.append(",[Offset_Type_ID],[Offset_Value],[Status_ID],[Last_Updated_DTTM],[Priced_By_BAR]," +
                "[Created_By_User_ID] ");
        insertQuery2.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery2.append("VALUES (").append(mktId).append(",").append(bizTypeValue).append(",").append(yieldableValue).append(",").append(forecastTypeValue).append(",").append(qualifiedValue).append(",0 ");
        insertQuery2.append(",").append(fencedValue).append(",").append(packageValue).append(",").append(linkValue).append(",null,0,").append(processStatusIDValue).append(" ");
        insertQuery2.append(",2,0 ");
        insertQuery2.append(",").append(statusIdValue).append(",GETDATE() ");
        insertQuery2.append(",").append(priceByBarValue).append(",1 ");
        insertQuery2.append(",GETDATE(),1) ");
        crudService.executeUpdateByNativeQuery(insertQuery2.toString());
    }

    private void populateMarketSegmentDetailsProposed(StringBuilder insertQuery2, String mktId, int bizTypeValue,
                                                      int yieldableValue,
                                                      int forecastTypeValue, int qualifiedValue, int fencedValue,
                                                      int packageValue, int linkValue, int priceByBarValue,
                                                      int processStatusIDValue, int statusIdValue) {
        insertQuery2.append("INSERT INTO [Mkt_Seg_Details_Proposed] ");
        insertQuery2.append("([Mkt_Seg_ID],[Business_Type_ID],[Yield_Type_ID],[Forecast_Activity_Type_ID],[Qualified]" +
                " ");
        insertQuery2.append(",[Booking_Block_Pc],[Fenced],[Package],[Link],[Template_ID],[Template_Default]," +
                "[Process_Status_ID] ");
        insertQuery2.append(",[Offset_Type_ID],[Offset_Value],[Status_ID],[Last_Updated_DTTM],[Priced_By_BAR]," +
                "[Created_By_User_ID] ");
        insertQuery2.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery2.append("VALUES (").append(mktId).append(",").append(bizTypeValue).append(",").append(yieldableValue).append(",").append(forecastTypeValue).append(",").append(qualifiedValue).append(",0 ");
        insertQuery2.append(",").append(fencedValue).append(",").append(packageValue).append(",").append(linkValue).append(",null,0,").append(processStatusIDValue).append(" ");
        insertQuery2.append(",2,0 ");
        insertQuery2.append(",").append(statusIdValue).append(",GETDATE() ");
        insertQuery2.append(",").append(priceByBarValue).append(",1 ");
        insertQuery2.append(",GETDATE(),1) ");
        crudService.executeUpdateByNativeQuery(insertQuery2.toString());
    }

    private void deleteAMSOccupancySummary() {
        crudService.executeUpdateByNativeQuery("delete from Ams_Occupancy_Summary");
    }

    private void populateMarketSegment(StringBuilder insertQuery1, String mktSegmentName) {
        insertQuery1.append(" INSERT INTO [dbo].[Mkt_Seg] ");
        insertQuery1.append("([Property_ID],[Mkt_Seg_Code],[Mkt_Seg_Name],[Mkt_Seg_Description] ");
        insertQuery1.append(",[Status_ID],[Last_Updated_DTTM],[Is_Editable],[Created_By_User_ID]  ");
        insertQuery1.append(",[Created_DTTM],[Last_Updated_By_User_ID]) ");
        insertQuery1.append("VALUES (5,'").append(mktSegmentName).append("','").append(mktSegmentName).append("','").append(mktSegmentName).append("',1,GETDATE(),1,1,GETDATE(),1) ");
        crudService.executeUpdateByNativeQuery(insertQuery1.toString());
    }

    private void populateAmsOccupancySummary(StringBuilder insertQuery1, String mktSegmentName, String rateCodeName,
                                             int blocks, int pickUp, int IsGroup, int roomsSold, double roomRevenue, String reservationStatus) {
        insertQuery1.append("INSERT INTO Ams_Occupancy_Summary ( Data_Load_Metadata_ID, Occupancy_DT, Market_Code, ");
        insertQuery1.append("Rate_Code, Block, Pickup, Is_Group, Rooms_Sold, Room_Revenue, Reservation_Status, ");
        insertQuery1.append("Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM) ");
        insertQuery1.append("VALUES (12,'2012-12-31 12:22:12.317','").append(mktSegmentName).append("','").append(rateCodeName).append("', ");
        insertQuery1.append("").append(blocks).append(",").append(pickUp).append(",").append(IsGroup).append(",").append(roomsSold).append(",").append(roomRevenue).append(",'").append(reservationStatus).append("',1,GETDATE(),1,GETDATE()) ");
        crudService.executeUpdateByNativeQuery(insertQuery1.toString());
    }

    private YieldCategoryRule insertYieldCategoryRule(String marketCode, String rateCode, String mappedCode, int rank) {
        YieldCategoryRule ycbr = new YieldCategoryRule();
        ycbr.setMarketCode(marketCode);
        ycbr.setRateCode(rateCode);
        ycbr.setAnalyticalMarketCode(mappedCode);
        ycbr.setBookingStartDate(new LocalDate("1800-01-01"));
        ycbr.setBookingEndDate(new LocalDate("2999-12-31"));
        ycbr.setRank(rank);
        return crudService.save(ycbr);
    }

    private DataLoadMetadata createDataLoadMetaData(String incomingFileTypeCode) {
        DataLoadMetadata dlm = new DataLoadMetadata();
        dlm.setCorrelationId(UUID.randomUUID().toString());
        dlm.setIncomingFileTypeCode(incomingFileTypeCode);
        return crudService.save(dlm);
    }

    private void createRawTrans(DataLoadMetadata dlm, String marketSegment, String rateCode) {
        String sql = "INSERT INTO opera.Raw_Transaction(Market_Code, Rate_Code, Data_Load_Metadata_ID) "
                + "values(:marketCode, :rateCode, :dlmID)";
        crudService.executeUpdateByNativeQuery(sql,
                QueryParameter.with("marketCode", marketSegment).and("rateCode", rateCode).and("dlmID", dlm.getId())
                        .parameters());
    }

    private List<Object[]> getPreviewAttributeDescriptionObjects() {
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[1];
            object[0] = result.getAttributeDescription();
            objects.add(object);
        }
        return objects;
    }

    private void assertOnPreviewForAttributeDescription(String AttributeDescription) {
        List<Object[]> objects = getPreviewAttributeDescriptionObjects();
        assertOnPreviewToValidateAttribute(objects, 0, AttributeDescription);
    }

    private List<Object[]> getPreviewAttributeDescriptionAtRateCodeLevelObjects() {
        List<PreviewMarketSegment> results = service.preview();
        List<Object[]> objects = new ArrayList<>();
        for (PreviewMarketSegment result : results) {
            Object[] object = new Object[2];
            object[0] = result.getAttributeDescription();
            object[1] = result.getMappedMarketCode();
            objects.add(object);
        }
        return objects;
    }

    private void assertOnPreviewForAttributeDescriptionAtRateCodeLevel(String AttributeDescription,
                                                                       String AttributeAgainstDef, String defaultMappedCode, String mappedCode) {
        List<Object[]> objects = getPreviewAttributeDescriptionAtRateCodeLevelObjects();
        assertOnPreviewToValidateAttribute(objects, AttributeAgainstDef, defaultMappedCode);
        assertOnPreviewToValidateAttribute(objects, AttributeDescription, mappedCode);
    }

    private void updateAMSOccupancySummaryForPreviewForMS5() {
        deleteAMSOccupancySummary();
        crudService.flushAndClear();
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_1, 8, 4, 0, 40, 356.29, "CHECKED" +
                " OUT");
        populateAmsOccupancySummary(new StringBuilder(), MARKET_SEGMENT_5, RATE_CODE_2, 9, 5, 0, 45, 243.18, "NO SHOW");
        crudService.flushAndClear();
    }

    public class MarketCodePredicate implements Predicate {
        private String marketCode;

        public MarketCodePredicate(String marketCode) {
            this.marketCode = marketCode;
        }

        @Override
        public boolean evaluate(Object object) {
            AnalyticalMarketSegmentSummary summary = (AnalyticalMarketSegmentSummary) object;
            return summary.getMarketCode().equals(marketCode);
        }
    }

    private void verifyAttributesMatch(MarketSegmentMaster newMarketSegmentMaster,
                                       MarketSegmentMaster oldMarketSegmentMaster) {
        //except for id everything else should match
        newMarketSegmentMaster.setId(oldMarketSegmentMaster.getId());
        assertEquals(oldMarketSegmentMaster, newMarketSegmentMaster);
    }

    @Test
    public void mappingRateCodeToExistingMarketSegmentShouldNotAddEntryIntoMktSegDetailsProposed_masterDoesNotHaveRateCode() throws Exception {
        AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
        summary.setRateCode("RateCode");
        summary.setMarketCode("MarketCode");

        StringBuilder insertQuery1 = new StringBuilder();
        populateMarketSegment(insertQuery1, "MarketCode_UF");

        AnalyticalMarketSegmentService spy = Mockito.spy(service);
        spy.assignMarketSegments(summary, AnalyticalMarketSegmentAttribute.FENCED,
                ForecastActivityType.DEMAND_AND_WASH, false, false, StringUtils.EMPTY);

        Mockito.verify(spy).updateMktSegDetailsProposed(Mockito.any(AnalyticalMarketSegment.class),
                Mockito.any(AnalyticalMarketSegmentAttribute.class), Mockito.any(MktSeg.class), nullable(Product.class));
    }

    @Test
    public void mappingRateCodeToExistingMarketSegmentShouldNotAddEntryIntoMktSegDetailsProposed() throws Exception {
        AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
        summary.setRateCode("RateCode");
        summary.setMarketCode("MarketCode");

        StringBuilder insertQuery1 = new StringBuilder();
        populateMarketSegment(insertQuery1, "MarketCode_UF");

        CrudService crudSpy = Mockito.spy(service.crudService);
        MarketSegmentMaster master = new MarketSegmentMaster();
        master.setCode("MarketCode_UF");
        Mockito.doReturn(master).when(crudSpy).findByExampleSingleResult(Mockito.any(MarketSegmentMaster.class));

        AnalyticalMarketSegmentService spy = Mockito.spy(service);
        spy.crudService = crudSpy;
        spy.assignMarketSegments(summary, AnalyticalMarketSegmentAttribute.FENCED,
                ForecastActivityType.DEMAND_AND_WASH, false, false, StringUtils.EMPTY);

        Mockito.verify(spy, Mockito.never()).updateMktSegDetailsProposed(Mockito.any(AnalyticalMarketSegment.class),
                Mockito.any(AnalyticalMarketSegmentAttribute.class), Mockito.any(MktSeg.class), nullable(Product.class));

    }

    @Test
    public void testShouldGetMsByOriginalMktSegAndAttribute() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_2, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams2 = createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        tenantCrudService().save(Arrays.asList(ams, ams1, ams2));
        final Collection<String> amsWithAttribute =
                service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_1, marketSegmentAttribute);
        assertNotNull(amsWithAttribute);
        assertEquals(1, amsWithAttribute.size());
        assertTrue(amsWithAttribute.contains(buildMappedMarketCode(MARKET_SEGMENT_1, marketSegmentAttribute, offset)));
        assertTrue(service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_3,
                marketSegmentAttribute).isEmpty());
    }

    @Test
    public void testShouldGetMsByOriginalMktSegAndAttributeIgnoringComplimentaryAttribute() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        ams.setComplimentary(true);
        tenantCrudService().save(Arrays.asList(ams));

        final Collection<String> amsWithAttribute =
                service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_1, marketSegmentAttribute);

        assertNotNull(amsWithAttribute);
        assertEquals(1, amsWithAttribute.size());
        assertTrue(amsWithAttribute.contains(buildMappedMarketCode(MARKET_SEGMENT_1, marketSegmentAttribute, offset)));
        assertTrue(service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_3,
                marketSegmentAttribute).isEmpty());
    }

    @Test
    public void testShouldGetMsByOriginalMktSegAndAttribute_MultipleIdenticalAMS() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_2, marketSegmentAttribute, offset++);
        AnalyticalMarketSegment ams2 = createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        tenantCrudService().save(Arrays.asList(ams, ams1, ams2));
        final Collection<String> amsWithAttribute =
                service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_1,
                        marketSegmentAttribute);
        assertNotNull(amsWithAttribute);
        assertEquals(2, amsWithAttribute.size());
        for (int i = 0; i < amsWithAttribute.size(); i++) {
            final String mappedMarketCode = buildMappedMarketCode(MARKET_SEGMENT_1, marketSegmentAttribute, i);
            assertTrue(amsWithAttribute.contains(mappedMarketCode), mappedMarketCode + " should have been present");
        }
    }

    @Test
    public void testShouldGetMsByOriginalMktSegAndAttribute_shouldNotReturnDefaults() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_2, marketSegmentAttribute, offset++);
        AnalyticalMarketSegment ams2 = createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams3 = createAnalyticalMarketSegment(RateCodeTypeEnum.DEFAULT.getRank(), null,
                RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        tenantCrudService().save(Arrays.asList(ams, ams1, ams2, ams3));
        final Collection<String> amsWithAttribute =
                service.getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(MARKET_SEGMENT_1,
                        marketSegmentAttribute);
        assertNotNull(amsWithAttribute);
        assertEquals(2, amsWithAttribute.size());
        for (int i = 0; i < amsWithAttribute.size(); i++) {
            final String mappedMarketCode = buildMappedMarketCode(MARKET_SEGMENT_1, marketSegmentAttribute, i);
            assertTrue(amsWithAttribute.contains(mappedMarketCode), mappedMarketCode + " should have been present");
        }
    }

    @Test
    public void testGetNewMappedMarketCode() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_2, marketSegmentAttribute, offset++);
        AnalyticalMarketSegment ams2 = createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams3 = createAnalyticalMarketSegment(RateCodeTypeEnum.DEFAULT.getRank(), null,
                RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        tenantCrudService().save(Arrays.asList(ams, ams1, ams2, ams3));
        final String newMappedMarketCode =
                service.getNewMappedMarketCode(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, MARKET_SEGMENT_1
                        , MarketSegmentCasinoCategoryType.NONE, null);
        assertEquals(newMappedMarketCode, buildMappedMarketCode(MARKET_SEGMENT_1, marketSegmentAttribute, offset + 1));
    }

    @Test
    public void testGetNewMappedMarketCode_whenIdenticalMSAttributeEnabled() {
        service.crudService = tenantCrudService();
        int offset = 0;
        final AnalyticalMarketSegmentAttribute marketSegmentAttribute =
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_2, marketSegmentAttribute, offset + 1);
        AnalyticalMarketSegment ams2 = createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS,
                MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        AnalyticalMarketSegment ams3 = createAnalyticalMarketSegment(RateCodeTypeEnum.DEFAULT.getRank(), null,
                RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_1, marketSegmentAttribute, offset);
        tenantCrudService().save(Arrays.asList(ams, ams1, ams2, ams3));
        setSeparableIdenticalMSForIP();
        Product product = createPersistedIndependentProduct("IP1");
        when(independentProductsService.createProductIfNotPersisted(any())).thenReturn(product);
        String newMarketCodeForMS1 = service.getNewMappedMarketCode(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, MARKET_SEGMENT_1,
                MarketSegmentCasinoCategoryType.NONE, product);
        String newMarketCodeForMS2 = service.getNewMappedMarketCode(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, MARKET_SEGMENT_2,
                MarketSegmentCasinoCategoryType.NONE, product);
        assertEquals(newMarketCodeForMS1, buildMappedMarketCodeForIP(MARKET_SEGMENT_1, marketSegmentAttribute, offset + 1, product));
        assertEquals(newMarketCodeForMS2, buildMappedMarketCodeForIP(MARKET_SEGMENT_2, marketSegmentAttribute, offset + 1, product));
    }

    private String buildMappedMarketCodeForIP(String marketSegment, AnalyticalMarketSegmentAttribute attribute, int offset, Product product) {
        if (product != null && product.isIndependentProduct()) {
            return marketSegment + "_" + offset + "_" + "IP" + product.getId() + "_" + attribute.getSuffix();
        }
        return marketSegment + "_" + offset + "_" + attribute.getSuffix();

    }

    private AnalyticalMarketSegment createAnalyticalMarketSegment(final Integer rank, final String rateCode,
                                                                  final RateCodeTypeEnum rateCodeTypeEnum, final String marketSegmentCode,
                                                                  final AnalyticalMarketSegmentAttribute analyticalMarketSegmentAttribute, final int offset) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setRank(rank);
        analyticalMarketSegment.setRateCode(rateCode);
        analyticalMarketSegment.setRateCodeType(rateCodeTypeEnum);
        analyticalMarketSegment.setMarketCode(marketSegmentCode);
        analyticalMarketSegment.setAttribute(analyticalMarketSegmentAttribute);
        analyticalMarketSegment.setMappedMarketCode(buildMappedMarketCode(marketSegmentCode,
                analyticalMarketSegmentAttribute, offset));
        analyticalMarketSegment.setForecastActivityType(tenantCrudService().find(ForecastActivityType.class,
                ForecastActivityType.DEMAND_AND_WASH));
        return analyticalMarketSegment;
    }

    private String buildMappedMarketCode(final String marketSegmentCode,
                                         final AnalyticalMarketSegmentAttribute analyticalMarketSegmentAttribute,
                                         final int offset) {
        String offsetStr = offset == 0 ? "" : "_" + String.valueOf(offset);
        return marketSegmentCode + offsetStr + "_" + analyticalMarketSegmentAttribute.getSuffix();
    }

    @Test
    public void getAMSMarketSegmentCodes() {
        service.crudService = tenantCrudService;
        List<Pair<String, String>> amsList = new ArrayList<>();
        amsList.add(new Pair<>(MARKET_SEGMENT_5_DEF, MARKET_SEGMENT_5));
        amsList.add(new Pair<>(MARKET_SEGMENT_7_DEF, MARKET_SEGMENT_7));
        when(tenantCrudService.findByNativeQuery(eq(MktSeg.GET_AMS_TO_PMS_MS_MAPPINGS_QUERY), anyMap(),
                any(RowMapper.class))).thenReturn(amsList);
        List<String> amsMarketSegmentCodes = service.getAMSMarketSegmentCodes();
        assertEquals(2, amsMarketSegmentCodes.size());
        assertTrue(amsMarketSegmentCodes.containsAll(Arrays.asList(MARKET_SEGMENT_5_DEF, MARKET_SEGMENT_7_DEF)));
        verify(tenantCrudService).findByNativeQuery(eq(MktSeg.GET_AMS_TO_PMS_MS_MAPPINGS_QUERY), anyMap(),
                any(RowMapper.class));
    }

    @Test
    public void createDefaultAms() {
        AnalyticalMarketSegment ams = getAnalyticalMarketSegment(MARKET_SEGMENT_2,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams = tenantCrudService().save(ams);
        assertNull(findMktSegForAms(ams));

        service.createDefaultOrStraightMSForAMS(ams);

        verifyMarketSegmentAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ams, false);
    }

    private void verifyMarketSegmentAttribute(AnalyticalMarketSegmentAttribute marketSegmentAttribute,
                                              AnalyticalMarketSegment ams, boolean complimentary) {
        final MktSeg result = findMktSegForAms(ams);
        assertNotNull(result);
        assertNotNull(result.getMktSegDetailsProposed());
        assertEquals(MARKET_SEGMENT_2 + "_" + marketSegmentAttribute.getSuffix(), result.getCode());
        assertEquals(complimentary, result.isComplimentary());
    }

    private AnalyticalMarketSegment getAnalyticalMarketSegment(String marketSegment,
                                                               AnalyticalMarketSegmentAttribute marketSegmentAttribute) {
        final RateCodeTypeEnum rateCodeType = RateCodeTypeEnum.DEFAULT;
        return createAnalyticalMarketSegment(rateCodeType.getRank(), RATE_CODE_1, rateCodeType
                , marketSegment, marketSegmentAttribute, 0);
    }

    @Test
    public void createDefaultAmsWithComplimentarySetToTrue() {
        AnalyticalMarketSegment ams = getAnalyticalMarketSegment(MARKET_SEGMENT_2,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ams.setComplimentary(true);
        ams = tenantCrudService().save(ams);
        assertNull(findMktSegForAms(ams));

        service.createDefaultOrStraightMSForAMS(ams);

        verifyMarketSegmentAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ams, true);
    }


    private MktSeg findMktSegForAms(final AnalyticalMarketSegment ams) {
        final Map<String, Object> params = QueryParameter.with("code", ams.getMappedMarketCode()).parameters();
        return tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE,
                params);
    }

    @Test
    public void updateComplimentaryAttribute() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        List<AnalyticalMarketSegmentSummary> unassigned = service.getUnassignedIndividualMarketSegments();
        AnalyticalMarketSegmentSummary marketSegmentSummary = unassigned.get(0);
        service.assignMarketSegments(Collections.singletonList(marketSegmentSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);
        AnalyticalMarketSegment persistedAms = getPersistedAnalyticalMarketSegment();
        verifyComplimentaryStatus(true, persistedAms);

        persistedAms.setComplimentary(false);
        service.updateAssignedMarketSegments(Collections.singletonList(persistedAms),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);
        AnalyticalMarketSegment updatedAms = getPersistedAnalyticalMarketSegment();
        verifyComplimentaryStatus(false, updatedAms);
    }


    private void verifyComplimentaryStatus(boolean expected, AnalyticalMarketSegment persistedAms) {
        assertEquals(expected, persistedAms.isComplimentary());
    }

    private AnalyticalMarketSegment getPersistedAnalyticalMarketSegment() {
        crudService.flushAndClear();
        return service.getAssignedMarketSegments().get(0);
    }

    @Test
    public void persistComplimentaryStatusForMktSeg() {
        service.clearAssignments(TestProperty.H1.getPaddedId());

        verifyComplimentaryAttributeForNewlyCreatedAMS();

        verifyComplimentaryAttributeForUpdatedAMS();

        verifyComplimentaryAttributeForUpdatedAMSWithMappedCode();

    }

    private void verifyComplimentaryAttributeForUpdatedAMS() {
        AnalyticalMarketSegment analyticalMarketSegment = getPersistedAnalyticalMarketSegment();
        analyticalMarketSegment.setComplimentary(false);

        service.updateAssignedMarketSegments(Collections.singletonList(analyticalMarketSegment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, (Product) null);

        MktSeg mktSegForAms = findMktSegForAms(analyticalMarketSegment);
        assertFalse(mktSegForAms.isComplimentary());
    }

    private void verifyComplimentaryAttributeForUpdatedAMSWithMappedCode() {
        AnalyticalMarketSegment analyticalMarketSegment = getPersistedAnalyticalMarketSegment();
        analyticalMarketSegment.setComplimentary(true);

        service.updateAssignedMarketSegments(Collections.singletonList(analyticalMarketSegment),
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, CONV, null);

        MktSeg mktSegForAms = findMktSegForAms(analyticalMarketSegment);
        assertTrue(mktSegForAms.isComplimentary());
    }

    private void verifyComplimentaryAttributeForNewlyCreatedAMS() {
        AnalyticalMarketSegmentSummary marketSegmentSummary = new AnalyticalMarketSegmentSummary();
        marketSegmentSummary.setMarketCode(CONV);

        service.assignMarketSegments(Collections.singletonList(marketSegmentSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);

        AnalyticalMarketSegment analyticalMarketSegment = getPersistedAnalyticalMarketSegment();
        MktSeg mktSegForAms = findMktSegForAms(analyticalMarketSegment);
        assertTrue(mktSegForAms.isComplimentary());
    }

    @Test
    public void getAnalyticalMarketSegmentByMappedMarketCodes() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary marketSegmentSummary = new AnalyticalMarketSegmentSummary();
        marketSegmentSummary.setMarketCode(CONV);
        service.assignMarketSegments(Collections.singletonList(marketSegmentSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);

        List<AnalyticalMarketSegment> analyticalMarketSegments =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singleton(CONV));

        assertFalse(analyticalMarketSegments.isEmpty());
        assertEquals(CONV, analyticalMarketSegments.get(0).getMappedMarketCode());
        assertTrue(analyticalMarketSegments.get(0).isComplimentary());
    }


    @Test
    public void updateLatestComplimentaryAttributeForAllRateCodesOfSameMSWhileCreatingNewAMSRateCodeAttributeMapping() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        createAnalyticalMarketSegment(RATE_CODE_1, true);
        createAnalyticalMarketSegment(RATE_CODE_2, false);

        List<AnalyticalMarketSegment> analyticalMarketSegments =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singleton(MKT_SEG_CODE_CONV_UF));

        assertEquals(2, analyticalMarketSegments.size());
        assertTrue(everyAMSRecordsHasComplimentaryStatusSetTo(analyticalMarketSegments, Boolean.FALSE));

        createAnalyticalMarketSegment(RATE_CODE_3, true);

        analyticalMarketSegments =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singleton(MKT_SEG_CODE_CONV_UF));

        assertEquals(3, analyticalMarketSegments.size());
        assertTrue(everyAMSRecordsHasComplimentaryStatusSetTo(analyticalMarketSegments, Boolean.TRUE));
    }

    private boolean everyAMSRecordsHasComplimentaryStatusSetTo(List<AnalyticalMarketSegment> analyticalMarketSegments
            , Boolean expected) {
        return analyticalMarketSegments.stream().map(AnalyticalMarketSegment::isComplimentary).allMatch(expected::equals);
    }

    private void createAnalyticalMarketSegment(String rateCode, boolean complimentary1) {
        AnalyticalMarketSegmentSummary marketSegmentSummary = new AnalyticalMarketSegmentSummary();
        marketSegmentSummary.setMarketCode(CONV);
        marketSegmentSummary.setRateCode(rateCode);
        service.assignMarketSegments(Collections.singletonList(marketSegmentSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, complimentary1, null);
    }

    @Test
    public void updateLatestComplimentaryAttributeForAllRateCodesOfSameMSWhileUpdatingAMSAttributes() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        createAnalyticalMarketSegment(RATE_CODE_1, true);
        createAnalyticalMarketSegment(RATE_CODE_2, false);
        createAnalyticalMarketSegment(RATE_CODE_3, false);
        List<AnalyticalMarketSegment> assignedMarketSegments = service.getAssignedMarketSegments();
        AnalyticalMarketSegment amsForRateCode3 =
                assignedMarketSegments.stream().filter(ams -> RATE_CODE_3.equals(ams.getRateCode())).findFirst().orElse(null);
        amsForRateCode3.setComplimentary(true);

        service.updateAssignedMarketSegments(Collections.singletonList(amsForRateCode3),
                AnalyticalMarketSegmentAttribute.FENCED, (Product) null);

        List<AnalyticalMarketSegment> analyticalMarketSegments =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singleton(MKT_SEG_CODE_CONV_UF));
        assertEquals(3, analyticalMarketSegments.size());
        assertTrue(everyAMSRecordsHasComplimentaryStatusSetTo(analyticalMarketSegments, Boolean.TRUE));
    }

    @Test
    public void updateComplimentaryAttributeForNewlyCreatedSplitMSUsingAMSAttributes() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(LocalDate.now().minusDays(1));
        MktSeg newlyCreatedMs = createMktSegAndAMSRecords(LocalDate.now());

        service.syncComplimentaryAttributeForNewlyCreatedMs();

        verifyThatComplimentaryIsSetTo(Boolean.TRUE, newlyCreatedMs);
    }

    private void verifyThatComplimentaryIsSetTo(Boolean expectedValue, MktSeg newlyCreatedMs) {
        crudService.flushAndClear();
        MktSeg updatedMs = crudService.find(MktSeg.class, newlyCreatedMs.getId());
        assertEquals(expectedValue, updatedMs.isComplimentary());
    }


    @Test
    public void shouldNotUpdateComplimentaryAttributeForRecordsThatWereCreatedOnOrBeforeCaughtUpDate() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(LocalDate.now().minusDays(1));
        MktSeg newlyCreatedMs = createMktSegAndAMSRecords(LocalDate.now().minusDays(1));

        service.syncComplimentaryAttributeForNewlyCreatedMs();

        verifyThatComplimentaryIsSetTo(Boolean.FALSE, newlyCreatedMs);
    }

    private MktSeg createMktSegAndAMSRecords(LocalDate localDate) {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        createAnalyticalMarketSegment(RATE_CODE_1, true);
        createAnalyticalMarketSegment(RATE_CODE_2, true);
        MktSeg mktSeg = createMktSeg(MKT_SEG_CODE_CONV_UF);
        mktSeg.setCreateDate(localDate.toDate());
        return crudService.save(mktSeg);
    }

    @Test
    public void shouldUpdateComplimentaryAttributeForRecordsForCatchUpJob() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(LocalDate.now());
        MktSeg newlyCreatedMs = createMktSegAndAMSRecords(LocalDate.now().minusDays(1));

        service.syncComplimentaryAttributeForNewlyCreatedMs();

        verifyThatComplimentaryIsSetTo(Boolean.FALSE, newlyCreatedMs);
    }

    @Test
    public void loadsOccupancyDataFromReservationNightAndGroupBlock() {
        crudService.executeUpdateByNativeQuery("truncate table ams_occupancy_summary");
        crudService.executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code='COMP_G' where mkt_seg_code='COMP'");
        service.loadOccupancyDataFromReservationNightAndGroupBlock(new Date());

        Integer compSummariesCount = crudService.findByNativeQuerySingleResult("select count(*) from " +
                "ams_occupancy_summary where market_code='COMP_G'", null);
        assertEquals(Integer.valueOf(1), compSummariesCount);
    }

    @Test
    void loadsOccupancyDataFromReservationNight() {
        crudService.executeUpdateByNativeQuery("truncate table ams_occupancy_summary");
        crudService.executeUpdateByNativeQuery("insert into Reservation_Night(Property_Id,Reservation_Identifier,Occupancy_DT,File_Metadata_ID,Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Booked_Accom_Type_Code,Accom_Type_ID,Mkt_Seg_ID,Room_Revenue,Total_Revenue,Source_Booking,Rate_Code,Market_Code,Persistent_Key)" +
                "values (5,1234567890,'2017-09-10',3,'RESERVED','2017-09-10','2017-09-11','2017-09-10','KNG',8,2,500,1000,'EMAIL','label','COMP_DEF','1234567890,2017-10-10')");
        crudService.executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg(Market_Code,Mapped_Market_Code) values('COMP','COMP_DEF')");
        when(pacmanConfigParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        service.loadOccupancyDataFromReservationNightAndGroupBlock(new Date());
        AmsOccupancySummary mock = new AmsOccupancySummary();
        mock.setRateCode("label");
        Collection<AmsOccupancySummary> summary = crudService.findByExample(mock);
        assertNotNull(summary);
        assertFalse(summary.isEmpty());
        crudService.executeUpdateByNativeQuery("delete from reservation_night where rate_code='label'");
        crudService.executeUpdateByNativeQuery("delete from Analytical_mkt_seg where Mapped_Market_Code='COMP_DEF'");
    }

    @Test
    void useNightChangeForAmsOccupancySummaryG3() {
        when(pacmanConfigParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        Date businessDate = new Date();
        service.crudService = tenantCrudService;
        service.loadOccupancyDataFromReservationNightAndGroupBlock(businessDate);
        verify(tenantCrudService).executeUpdateByNamedQuery(AmsOccupancySummary.INSERT_AMS_OCCUPANCY_DATA_FROM_RES_NIGHT_AND_NIGHT_CHANGE, QueryParameter.with("occupancyDate", businessDate).and("totalRateEnabled", 0).parameters());
    }

    @Test
    void loadsOccupancyDataFromReservationNightAndChange() {
        crudService.executeUpdateByNativeQuery("truncate table ams_occupancy_summary");
        crudService.executeUpdateByNativeQuery("insert into Reservation_Night_Change(Reservation_Identifier,Occupancy_DT,File_Metadata_ID,Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Booked_Accom_Type_Code,Accom_Type_ID,Mkt_Seg_ID,Room_Revenue,Total_Revenue,Source_Booking,Rate_Code,Market_Code,Persistent_Key,Change_Type,Change_DTTM)" +
                "values (1234567890,'2017-09-10',8903,'RESERVED','2017-09-10','2017-09-11','2017-09-10','KNG',8,2,500,1000,'EMAIL','label','COMP_DEF','1234567890,2017-10-10',1,'2017-10-10 00:00:00')");
        crudService.executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg(Market_Code,Mapped_Market_Code) values('COMP','COMP_DEF')");
        service.loadOccupancyDataFromReservationNightAndGroupBlock(new Date());
        AmsOccupancySummary mock = new AmsOccupancySummary();
        mock.setRateCode("label");
        Collection<AmsOccupancySummary> summary = crudService.findByExample(mock);
        assertNotNull(summary);
        assertFalse(summary.isEmpty());
        crudService.executeUpdateByNativeQuery("delete from reservation_night_change where rate_code='label'");
        crudService.executeUpdateByNativeQuery("delete from Analytical_mkt_seg where Mapped_Market_Code='COMP_DEF'");
    }

    @Test
    public void findMarketSegmentMaster() {
        service.clearAssignments(TestProperty.H2.getPaddedId());
        service.assignMarketSegments(Collections.singletonList(createDefaultMarketSegment(MARKET_SEGMENT_5)),
                AnalyticalMarketSegmentAttribute.FENCED, 2);

        MarketSegmentMaster defaultMarketSegMaster = service.findMarketSegmentMaster("FD_DEF");

        assertEquals(Integer.valueOf(2), defaultMarketSegMaster.getForecastActivityTypeId());
        assertEquals(Integer.valueOf(1), defaultMarketSegMaster.getFenced());
        assertEquals(Integer.valueOf(2), defaultMarketSegMaster.getBusinessTypeId());
        assertEquals(Integer.valueOf(0), defaultMarketSegMaster.getQualified());
        assertEquals(Integer.valueOf(0), defaultMarketSegMaster.getPriceByBar());
        assertEquals(Integer.valueOf(0), defaultMarketSegMaster.getBookingBlockPc());
        assertEquals(Integer.valueOf(0), defaultMarketSegMaster.getLink());
        assertEquals(Integer.valueOf(1), defaultMarketSegMaster.getYieldTypeId());
        assertEquals(Integer.valueOf(1), defaultMarketSegMaster.getIsEditable());

        defaultMarketSegMaster = service.findMarketSegmentMaster("INVALID");
        assertNull(defaultMarketSegMaster);
    }

    @Test
    public void deleteARateCodeFromMarketSegmentAttributedAsPriceByBAR() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary summary =
                findFirstMarketSegment(service.getUnassignedIndividualMarketSegments(), MARKET_SEGMENT_5);
        summary.setRateCode(RATE_CODE_1);
        summary.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        AnalyticalMarketSegmentSummary summary2 = new AnalyticalMarketSegmentSummary();
        summary2.setMarketCode(MARKET_SEGMENT_5);
        summary2.setRateCode(RATE_CODE_2);
        summary2.setRateCodeType(RateCodeTypeEnum.EQUALS.toString());
        List<AnalyticalMarketSegmentSummary> summaries = new ArrayList<>();
        summaries.add(summary);
        summaries.add(summary2);
        summaries.add(createDefaultMarketSegment(MARKET_SEGMENT_5));
        service.assignMarketSegments(summaries, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, forecastActivityTypeId
                , true, null);
        crudService.flushAndClear();
        int BookingBlockPercentage1 = 0;
        assertOnMarketSegmentMaster(MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, BusinessTypeTransient,
                Yieldable, ForecastActivityTypeForDemandAndWash, Unqualified, NonFenced, NonPackage, NonLink, PriceByBar,
                BookingBlockPercentage1, IsEditable);
        assertOnAnalyticalMarketSegmentForRateCodeLevel(MARKET_SEGMENT_5, RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX, attEqualsToBar, RateCodeTypeEnum.EQUALS);

        List<AnalyticalMarketSegment> assignedMktSeg = service.getAssignedMarketSegments();
        service.unassignMarketSegments(Collections.singletonList(findAnalyticalMarketSegmentByMappedMarketCode(assignedMktSeg, MARKET_SEGMENT_5 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX)), false);
        crudService.flushAndClear();

        assertOnAnalyticalMarketSegmentRowCount(2);
        //it should have records for 3 MS FD_USB , FD , and FD_DEF
        assertOnMarketSegmentMasterRowCount(3);
    }

    @Test
    public void testForecastActivityTypes() {
        Map<Integer, ForecastActivityType> forecastActivityTypesById = service.getForecastActivityTypesById();

        assertEquals(3, forecastActivityTypesById.size());
        assertEquals("Demand and Wash", forecastActivityTypesById.get(1).getName());
        assertEquals("Wash", forecastActivityTypesById.get(2).getName());
        assertEquals("None", forecastActivityTypesById.get(3).getName());
    }

    @Test
    public void shouldEnableCCFGWhenAddingARateCodeWithDifferentComplimentaryValue() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        String mappedCode = MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX;
        AnalyticalMarketSegmentSummary amsMktSegSummary = getAnalyticalMarketSegmentSummary(RATE_CODE_1, mappedCode);
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);
        MktSeg mktSeg = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code",
                mappedCode).parameters());
        assertNull(mktSeg);
        mktSeg = createMktSeg(mappedCode);
        crudService.save(mktSeg);

        verifyWhenComplimentaryIsSameThenCCFGIsNotEnabled(mappedCode);

        verifyWhenComplimentaryIsDifferentThenCCFGIsEnabled(mappedCode);
    }

    @Test
    public void getComplimentaryValuesByMappedMarketCodes() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegmentSummary amsMktSegSummary = getAnalyticalMarketSegmentSummary(RATE_CODE_1,
                MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX);
        AnalyticalMarketSegmentSummary amsMktSegSummary2 = getAnalyticalMarketSegmentSummary(RATE_CODE_2,
                MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX);
        AnalyticalMarketSegmentSummary amsMktSegSummary3 = getAnalyticalMarketSegmentSummary(RATE_CODE_2,
                MARKET_SEGMENT_1 + UNQUALIFIED_EQUAL_TO_BAR_SUFFIX);
        service.assignMarketSegments(Arrays.asList(amsMktSegSummary, amsMktSegSummary2, amsMktSegSummary3),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);
        AnalyticalMarketSegmentSummary amsMktSegSummary4 = getAnalyticalMarketSegmentSummary(RATE_CODE_2,
                MARKET_SEGMENT_1 + UNQUALIFIED_FENCED_SUFFIX);
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary4),
                AnalyticalMarketSegmentAttribute.PACKAGED, forecastActivityTypeId, false, null);

        Map<String, Set<Boolean>> complimentaryValuesByMappedMarketCodes =
                service.getComplimentaryValuesByMappedMarketCodes(Arrays.asList(MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX
                        , MARKET_SEGMENT_1 + UNQUALIFIED_FENCED_SUFFIX));

        assertTrue(complimentaryValuesByMappedMarketCodes.get(MARKET_SEGMENT_5 + UNQUALIFIED_FENCED_SUFFIX).stream().allMatch(Boolean.TRUE::equals));
        assertTrue(complimentaryValuesByMappedMarketCodes.get(MARKET_SEGMENT_1 + UNQUALIFIED_FENCED_SUFFIX).stream().allMatch(Boolean.FALSE::equals));
    }


    @Test
    public void testCreateAmsFromReservationNight() {
        Collection mktSegs = new ArrayList();
        service.crudService = tenantCrudService;
        List<String> marketCodes = Arrays.asList("TEST");
        final Map<String, Object> params = QueryParameter.with("marketCodes", marketCodes).parameters();
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_NEW_AMS_TO_BE_CREATED, params)).thenReturn(Arrays.asList("TEST1_USB", "TEST2"));
        when(marketSegmentRepository.createMarketSegments(any(Set.class))).thenReturn(mktSegs);
        when(marketSegmentService.createDetailsProposedFromMaster()).thenReturn(2);
        service.createNewMarketSegmentsFromReservationNights(marketCodes);
        verify(tenantCrudService).findByNativeQuery(eq(ReservationNight.GET_NEW_AMS_TO_BE_CREATED), eq(params));
        verify(marketSegmentRepository).createMarketSegments(any(Set.class));
        verify(marketSegmentService).createDetailsProposedFromMaster();
    }

    @Test
    public void deletesNonDefaultAMSRulesWithTheGivenMktSegCodes() {
        setUpAMS();
        List<String> mktSegCodes = Collections.singletonList("MS1");

        int rowsDeleted = service.deleteNonDefaultAMSRulesWithMktSegsCodes(mktSegCodes);

        List<AnalyticalMarketSegment> amsRulesFromDB = crudService.findAll(AnalyticalMarketSegment.class);
        assertEquals(3, amsRulesFromDB.size());
        assertEquals(2, rowsDeleted);
        assertTrue(amsRulesFromDB.stream().noneMatch(amsRule -> amsRule.getMarketCode().equalsIgnoreCase("MS1") && amsRule.getRank() != 999));
        assertEquals(1, amsRulesFromDB.stream().filter(ams -> ams.getMarketCode().equalsIgnoreCase("MS1")).count());
        assertTrue(amsRulesFromDB.stream().anyMatch(amsRule -> amsRule.getMarketCode().equalsIgnoreCase("MS1") && amsRule.getRank() == 999));
    }

    @Test
    public void deletesAllAMSRulesWithTheGivenMktSegCodes() {
        setUpAMS();
        List<String> mktSegCodes = Collections.singletonList("MS1");

        int rowsDeleted = service.deleteAMSRulesWithMktCodes(mktSegCodes);

        List<AnalyticalMarketSegment> amsRulesFromDB = crudService.findAll(AnalyticalMarketSegment.class);
        assertEquals(2, amsRulesFromDB.size());
        assertEquals(3, rowsDeleted);
        assertTrue(amsRulesFromDB.stream().noneMatch(amsRule -> amsRule.getMarketCode().equalsIgnoreCase("MS1")));
        assertEquals(0, amsRulesFromDB.stream().filter(ams -> ams.getMarketCode().equalsIgnoreCase("MS1")).count());
    }

    private void setUpAMS() {
        crudService.executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg ([Market_Code],[Rate_Code] ," +
                "[Mapped_Market_Code], [Attribute], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], " +
                "[Last_Updated_DTTM], [Rate_Code_Type], [Rank], [Complimentary]) " +
                "values('MS1', 'RC1', 'MS1_QYL', 'EQUAL_TO_BAR', '1', GETDATE(), 1, GETDATE(), 'CONTAINS', 20, 0)," +
                "('MS1', null, 'MS1_DEF', 'EQUAL_TO_BAR', '1', GETDATE(), 1, GETDATE(), 'DEFAULT', 999, 0)," +
                "('MS1', 'RC4', 'MS1_QYL', 'EQUAL_TO_BAR', '1', GETDATE(), 1, GETDATE(), 'EQUALS', 10, 0)," +
                "('MS2', 'RC2', 'MS2_QYL', 'EQUAL_TO_BAR', '1', GETDATE(), 1, GETDATE(), 'EQUALS', 10, 0)," +
                "('MS2', null, 'MS2_DEF', 'EQUAL_TO_BAR', '1', GETDATE(), 1, GETDATE(), 'DEFAULT', 999, 0)");
    }

    @Test
    public void returnsAllMktSegCodesForTheGivenRanksAndMarketCodes() {
        setUpAMS();
        List<String> mktSegCodes = Collections.singletonList("MS1");
        List<Integer> ranks = Collections.singletonList(20);

        List<String> result = service.getMarketCodes(ranks, mktSegCodes);

        assertEquals(1, result.size());
        assertTrue(result.get(0).equalsIgnoreCase("MS1"));
    }

    @Test
    public void updateAssignedMarketSegmentsWithPreservedAMS() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());
        final AnalyticalMarketSegment ams1 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_ETB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams2 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_ETB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);

        final AnalyticalMarketSegment ams3 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_ETB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, true);
        final AnalyticalMarketSegment ams4 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_ETB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, true);

        final List<AnalyticalMarketSegment> analyticalMarketSegmentsBeforeUpdate = Arrays.asList(ams1, ams2, ams3,
                ams4);
        crudService.save(analyticalMarketSegmentsBeforeUpdate);

        //When
        service.updateAssignedMarketSegments(analyticalMarketSegmentsBeforeUpdate,
                AnalyticalMarketSegmentAttribute.FENCED, (Product) null);

        //Then
        final List<AnalyticalMarketSegment> analyticalMarketSegmentsAfterUpdate =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));

        assertFalse(analyticalMarketSegmentsAfterUpdate.isEmpty());
        assertEquals(4, analyticalMarketSegmentsAfterUpdate.size());

        assertAnalyticalMarketSegment(analyticalMarketSegmentsAfterUpdate.get(0), AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        assertAnalyticalMarketSegment(analyticalMarketSegmentsAfterUpdate.get(1), AMS_NEW, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        assertAnalyticalMarketSegment(analyticalMarketSegmentsAfterUpdate.get(2), AMS_OLD, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);
        assertAnalyticalMarketSegment(analyticalMarketSegmentsAfterUpdate.get(3), AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);

    }

    @Test
    public void updateAssignedMarketSegmentsWithPreservedAMS_SeparableIdenticalMsAttribute() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment ams1 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams2 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);

        final AnalyticalMarketSegment ams3 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_ETB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams4 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_ETB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, true);

        crudService.save(Arrays.asList(ams1, ams2, ams3, ams4));

        //When
        service.updateAssignedMarketSegments(Arrays.asList(ams3, ams4), AnalyticalMarketSegmentAttribute.FENCED, "", null);

        //Then
        final List<AnalyticalMarketSegment> AMS_NEW_UF_List =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));

        assertFalse(AMS_NEW_UF_List.isEmpty());
        assertEquals(2, AMS_NEW_UF_List.size());
        assertAnalyticalMarketSegment(AMS_NEW_UF_List.get(0), AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        assertAnalyticalMarketSegment(AMS_NEW_UF_List.get(1), AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);


        final List<AnalyticalMarketSegment> AMS_NEW_1_UF_List =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_1_UF));

        assertFalse(AMS_NEW_1_UF_List.isEmpty());
        assertEquals(2, AMS_NEW_1_UF_List.size());
        assertAnalyticalMarketSegment(AMS_NEW_1_UF_List.get(0), AMS_NEW, AMS_NEW_1_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        assertAnalyticalMarketSegment(AMS_NEW_1_UF_List.get(1), AMS_OLD, AMS_NEW_1_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);

    }

    @Test
    public void unassignMarketSegmentsShouldUnassignDefaultAMSIncludingPreservedWhenItIsTheLastAssignment() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment ams1 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams2 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);

        final AnalyticalMarketSegment ams3 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.DEFAULT, 999, false);
        final AnalyticalMarketSegment ams4 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_DEF, RATE_CODE_1,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);

        final AnalyticalMarketSegment ams5 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.DEFAULT, 999, true);
        final AnalyticalMarketSegment ams6 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_DEF, RATE_CODE_1,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, true);

        crudService.save(Arrays.asList(ams1, ams2, ams3, ams4, ams5, ams6));

        //When
        service.unassignMarketSegments(Arrays.asList(ams1, ams2), false);

        //Then
        final List<Object> actual_AMS_NEW_UF_list =
                crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                        QueryParameter.with(MAPPED_MARKET_CODE, AMS_NEW_UF).parameters());
        assertTrue(actual_AMS_NEW_UF_list.isEmpty());

        final List<Object> actual_AMS_NEW_DEF_list =
                crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                        QueryParameter.with(MAPPED_MARKET_CODE, AMS_NEW_DEF).parameters());
        assertTrue(actual_AMS_NEW_DEF_list.isEmpty());

    }

    @Test
    public void updateAnalyticalMarketSegmentNonDefault() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);

        //When
        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.empty());
        //Then
        final List<AnalyticalMarketSegment> amsNewUF =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        final List<AnalyticalMarketSegment> amsNewUSB =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_USB"));
        assertFalse(amsNewUSB.isEmpty());
        assertAnalyticalMarketSegment(amsNewUSB.get(0), AMS_NEW, AMS_NEW + "_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);

    }

    @Test
    public void shouldUseIPProductIdToBuildMappedMarketCode() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product product = createIndependentProduct("IP1");
        product.setId(2);

        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.of(product));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
    }

    @Test
    public void shouldUseIPProductIdToBuildMappedMarketCodeWhenIPIsEnabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.empty());
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.of(product));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(null), eq(product), rateCodesCaptor.capture());
        assertEquals(RATE_CODE_4, (String) rateCodesCaptor.getValue().iterator().next());
    }

    @Test
    public void shouldTransferRateCodesToNewProductWhenIndependentProductsIsEnabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product originallyMappedProduct = new Product();
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.of(originallyMappedProduct));
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.of(product));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(originallyMappedProduct), eq(product), rateCodesCaptor.capture());
        assertEquals(RATE_CODE_4, rateCodesCaptor.getValue().iterator().next());
    }

    @Test
    public void shouldCreateProductRateCodeMappingUsingNonDefaultAMSRateCodeValueWhenRateCodesAreNotProvidedAndIndependentProductsIsEnabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product originallyMappedProduct = new Product();
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.of(originallyMappedProduct));
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAssignedMarketSegments(List.of(nonDefaultAMS), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,
                true, product, "", new HashMap<>());

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(originallyMappedProduct), eq(product), rateCodesCaptor.capture());
        assertEquals(RATE_CODE_4, rateCodesCaptor.getValue().iterator().next());
    }

    @Test
    public void shouldCreateProductRateCodeMappingUsingNonDefaultAMSRateCodeValueWhenRateCodesMapIsNullAndIndependentProductsIsEnabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product originallyMappedProduct = new Product();
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.of(originallyMappedProduct));
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAssignedMarketSegments(List.of(nonDefaultAMS), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,
                true, product, "", null);

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(originallyMappedProduct), eq(product), rateCodesCaptor.capture());
        assertEquals(RATE_CODE_4, rateCodesCaptor.getValue().iterator().next());
    }

    @Test
    public void shouldCreateProductRateCodeMappingWhenRateCodesAreProvidedIndependentProductsIsEnabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product originallyMappedProduct = new Product();
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.of(originallyMappedProduct));
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAssignedMarketSegments(List.of(nonDefaultAMS), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,
                true, product, "", Map.of(nonDefaultAMS, List.of(RATE_CODE_5)));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(originallyMappedProduct), eq(product), rateCodesCaptor.capture());
        assertEquals(1, rateCodesCaptor.getValue().size());
        assertEquals(RATE_CODE_5, rateCodesCaptor.getValue().iterator().next());
    }

    @Test
    public void shouldNotCreateProductRateCodeMappingWhenEmptyRateCodesAreProvided() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product originallyMappedProduct = new Product();
        Product product = createIndependentProduct("IP1");
        product.setId(2);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(independentProductsService.getProductMappedToMarketSegment(nonDefaultAMS.getMappedMarketCode())).thenReturn(Optional.of(originallyMappedProduct));
        when(independentProductsService.getOrCreateProduct(product)).thenReturn(product);

        service.updateAssignedMarketSegments(List.of(nonDefaultAMS), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,
                true, product, "", Map.of(nonDefaultAMS, List.of()));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_IP2_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_IP2_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(pacmanConfigParamsServiceLocal, times(2)).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService).getProductMappedToMarketSegment("AMS_NEW_UF");
        verify(independentProductsService).getOrCreateProduct(product);
        verify(independentProductsService).updateMarketSegmentProductMapping(AMS_NEW + "_IP2_USB", product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        ArgumentCaptor<Set> rateCodesCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(eq(originallyMappedProduct), eq(product), rateCodesCaptor.capture());
        assertEquals(0, rateCodesCaptor.getValue().size());
    }

    @Test
    public void shouldNotTransferRateCodesToNewProductWhenIndependentProductsIsEnabledButMSRecodingSupportForIPIsDisabled() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(false);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.empty());

        verify(pacmanConfigParamsServiceLocal).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsServiceLocal, never()).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        verify(independentProductsService, never()).getProductMappedToMarketSegment(any());
        verify(independentProductsService, never()).transferRateCodes(any(), any(), any());
    }

    @Test
    public void shouldNotUseIPAbbrToBuildMappedMarketCodeWhenPrimaryProductIsUsed() {
        service.clearAssignments(TestProperty.H1.getPaddedId());
        AnalyticalMarketSegment nonDefaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        nonDefaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(nonDefaultAMS);
        Product product = getPrimaryProduct("BAR");
        product.setId(2);

        service.updateAnalyticalMarketSegment(nonDefaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, false,
                true, Optional.of(product));

        List<AnalyticalMarketSegment> amsNewUF = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_UF));
        assertTrue(amsNewUF.isEmpty());
        List<AnalyticalMarketSegment> newAms = service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW + "_USB"));
        assertFalse(newAms.isEmpty());
        assertAnalyticalMarketSegment(newAms.get(0), AMS_NEW, AMS_NEW + "_USB", RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        verify(independentProductsService, never()).getProductMappedToMarketSegment(any());
    }

    @Test
    public void updateAnalyticalMarketSegmentDefault() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment defaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.DEFAULT, 999, false);
        defaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(defaultAMS);
        //When
        service.updateAnalyticalMarketSegment(defaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, true, true, Optional.empty());
        //Then
        final List<AnalyticalMarketSegment> amsNewDEF =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_DEF));
        assertFalse(amsNewDEF.isEmpty());
        assertAnalyticalMarketSegment(amsNewDEF.get(0), AMS_NEW, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.DEFAULT, 999, false);
    }

    @Test
    public void updateAnalyticalMarketSegmentDefault_separableIdenticalMSAttribute() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment defaultAMS = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.DEFAULT, 999, false);
        defaultAMS.setForecastActivityType(getForecastActivityType(ForecastActivityType.DEMAND_AND_WASH));
        crudService.save(defaultAMS);
        //When
        service.updateAnalyticalMarketSegment(defaultAMS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, true, true, Optional.empty());
        //Then
        final List<AnalyticalMarketSegment> amsNewDEF =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(AMS_NEW_DEF));
        assertFalse(amsNewDEF.isEmpty());
        assertAnalyticalMarketSegment(amsNewDEF.get(0), AMS_NEW, AMS_NEW_DEF, null,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.DEFAULT, 999, false);

    }

    @Test
    public void updateAssignedMarketSegments_WhenSeparableIdenticalMsAttribute_isTrue() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment ams1 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams2 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.FENCED, RateCodeTypeEnum.EQUALS, RANK_10, false);

        final AnalyticalMarketSegment ams3 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_ETB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams4 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_ETB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);

        crudService.save(Arrays.asList(ams1, ams2, ams3, ams4));

        //When
        setSeparableIdenticalMSForIP();
        Product savedProduct = createPersistedIndependentProduct("IP1");
        Product product = createIndependentProduct("IP1");
        when(independentProductsService.getOrCreateProduct(any())).thenReturn(savedProduct);
        service.updateAssignedMarketSegments(Arrays.asList(ams3, ams4), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, "", product);

        //Then
        String AMS_NEW_1_IP25_USB = "AMS_NEW_1_IP25_USB";
        String AMS_OLD_1_IP25_USB = "AMS_OLD_1_IP25_USB";
        final List<AnalyticalMarketSegment> AMS_NEW_UF_List =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(List.of(AMS_NEW_1_IP25_USB, AMS_OLD_1_IP25_USB));

        assertFalse(AMS_NEW_UF_List.isEmpty());
        assertEquals(2, AMS_NEW_UF_List.size());
        assertAnalyticalMarketSegment(AMS_NEW_UF_List.get(0), AMS_NEW, AMS_NEW_1_IP25_USB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        assertAnalyticalMarketSegment(AMS_NEW_UF_List.get(1), AMS_OLD, AMS_OLD_1_IP25_USB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
    }

    @Test
    public void updateAssignedMarketSegments_WhenSeparableIdenticalMsAttribute_isFalse() {
        //Given
        service.clearAssignments(TestProperty.H1.getPaddedId());

        final AnalyticalMarketSegment ams1 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_UF, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams2 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_UF, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);

        final AnalyticalMarketSegment ams3 = getAnalyticalMarketSegment(AMS_NEW, AMS_NEW_ETB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ams4 = getAnalyticalMarketSegment(AMS_OLD, AMS_NEW_ETB, RATE_CODE_5,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, true);

        crudService.save(Arrays.asList(ams1, ams2, ams3, ams4));

        //When
        Product product = getPrimaryProduct("BAR");
        service.updateAssignedMarketSegments(Arrays.asList(ams3, ams4), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, "", product);

        //Then
        String AMS_NEW_1_USB = "AMS_NEW_1_USB";
        final List<AnalyticalMarketSegment> AMS_NEW_UF_List =
                service.getAnalyticalMarketSegmentByMappedMarketCodes(List.of(AMS_NEW_1_USB));

        assertFalse(AMS_NEW_UF_List.isEmpty());
        assertEquals(2, AMS_NEW_UF_List.size());
        assertAnalyticalMarketSegment(AMS_NEW_UF_List.get(0), AMS_NEW, AMS_NEW_1_USB, RATE_CODE_4,
                AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);
    }

    private Product getPrimaryProduct(String name) {
        Product product = new Product();
        product.setId(1);
        product.setName(name);
        product.setSystemDefault(true);
        product.setCode(Product.BAR);
        return product;
    }

    private Product createIndependentProduct(String name) {
        Product product = new Product();
        product.setName(name);
        product.setSystemDefault(false);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        return product;
    }

    private void setSeparableIdenticalMSForIP() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(GUIConfigParamName.SEPARABLE_IDENTICAL_MS_ATTRIB)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IDENTICAL_MS_ATTRIBUTE_IP)).thenReturn(true);
    }

    private Product createPersistedIndependentProduct(String name) {
        Product product = new Product();
        product.setId(25);
        product.setName(name);
        product.setSystemDefault(false);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        return product;
    }

    private ForecastActivityType getForecastActivityType(int forecastActivityTypeId) {
        return crudService.find(ForecastActivityType.class, forecastActivityTypeId);
    }

    public void assertAnalyticalMarketSegment(AnalyticalMarketSegment actualAnalyticalMarketSegment,
                                              String expectedMarketCode, String expectedMappedMarketCode, String expectedRateCode,
                                              AnalyticalMarketSegmentAttribute expectedAttribute, RateCodeTypeEnum expectedRateCodeType, Integer expectedRank
            , boolean expectedPreserved) {
        assertEquals(expectedMarketCode, actualAnalyticalMarketSegment.getMarketCode());
        assertEquals(expectedMappedMarketCode, actualAnalyticalMarketSegment.getMappedMarketCode());
        assertEquals(expectedRateCode, actualAnalyticalMarketSegment.getRateCode());
        assertEquals(expectedAttribute, actualAnalyticalMarketSegment.getAttribute());
        assertEquals(expectedRateCodeType, actualAnalyticalMarketSegment.getRateCodeType());
        assertEquals(expectedRank, actualAnalyticalMarketSegment.getRank());
        assertEquals(expectedPreserved, actualAnalyticalMarketSegment.isPreserved());
    }

    private AnalyticalMarketSegment getAnalyticalMarketSegment(String marketCode, String mappedMarketCode,
                                                               String rateCode, AnalyticalMarketSegmentAttribute attribute, RateCodeTypeEnum rateCodeTypeEnum, int rank,
                                                               boolean preserved) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setMarketCode(marketCode);
        analyticalMarketSegment.setMappedMarketCode(mappedMarketCode);
        analyticalMarketSegment.setRateCode(rateCode);
        analyticalMarketSegment.setRateCodeType(rateCodeTypeEnum);
        analyticalMarketSegment.setAttribute(attribute);
        analyticalMarketSegment.setRank(rank);
        analyticalMarketSegment.setPreserved(preserved);
        return analyticalMarketSegment;
    }

    @Test
    public void returnsAllAMSRulesWithGivenRateAndMktCodes() {
        List<AnalyticalMarketSegment> amsRules = new ArrayList<>();
        amsRules.add(createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL, MARKET_SEGMENT_1));
        amsRules.add(createAnalyticalMarketSegment(10, RATE_CODE_2, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_2));
        amsRules.add(createAnalyticalMarketSegment(999, null, RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_2));
        amsRules.add(createAnalyticalMarketSegment(999, null, RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_3));
        amsRules.add(createAnalyticalMarketSegment(10, RATE_CODE_3, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_3));
        amsRules.add(createAnalyticalMarketSegment(10, RATE_CODE_1, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_3));
        crudService.save(amsRules);

        List<AnalyticalMarketSegment> amsList = service.getAMSRulesWith(Arrays.asList(MARKET_SEGMENT_2,
                MARKET_SEGMENT_3), new HashSet<>(Arrays.asList(RATE_CODE_2, RATE_CODE_1)));

        assertEquals(2, amsList.size());
        assertTrue(amsList.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(MARKET_SEGMENT_2) && ams.getRateCode().equalsIgnoreCase(RATE_CODE_2)));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(MARKET_SEGMENT_3) && ams.getRateCode().equalsIgnoreCase(RATE_CODE_1)));
    }

    @Test
    public void returnsAllAMSRulesForGivenMktCodes() {
        List<AnalyticalMarketSegment> amsRules = new ArrayList<>();
        amsRules.add(createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL, MARKET_SEGMENT_1));
        amsRules.add(createAnalyticalMarketSegment(10, RATE_CODE_2, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_2));
        amsRules.add(createAnalyticalMarketSegment(999, null, RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_2));
        amsRules.add(createAnalyticalMarketSegment(999, null, RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_3));
        amsRules.add(createAnalyticalMarketSegment(10, RATE_CODE_3, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_3));
        amsRules.add(createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL, MARKET_SEGMENT_4));
        amsRules.add(createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL, MARKET_SEGMENT_5));
        crudService.save(amsRules);

        List<AnalyticalMarketSegment> ams =
                service.getAMSRulesWithMarketCodes(new HashSet<>(Arrays.asList(MARKET_SEGMENT_1, MARKET_SEGMENT_2)));

        assertEquals(3, ams.size());
        assertTrue(ams.stream().anyMatch(rule -> rule.getMarketCode().equalsIgnoreCase(MARKET_SEGMENT_1)));
        assertTrue(ams.stream().anyMatch(rule -> rule.getMarketCode().equalsIgnoreCase(MARKET_SEGMENT_2)));
    }

    @Test
    public void retrievesAllDistinctMappedMarketForGivenMarketCode() {
        List<AnalyticalMarketSegment> amsRules = new ArrayList<>();
        amsRules.add(createAnalyticalMarketSegment(1, RATE_CODE_2, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_1));
        amsRules.add(createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_1));
        amsRules.add(createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.DEFAULT, MARKET_SEGMENT_1));
        amsRules.add(createAnalyticalMarketSegment(1, RATE_CODE_3, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_4));
        amsRules.add(createAnalyticalMarketSegment(1, RATE_CODE_4, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_4));
        amsRules.add(createAnalyticalMarketSegment(1, RATE_CODE_6, RateCodeTypeEnum.EQUALS, MARKET_SEGMENT_5));
        crudService.save(amsRules);

        List<String> distinctMappedMarketCodes = service.getDistinctMappedMarketCodesOf(MARKET_SEGMENT_1);

        assertEquals(2, distinctMappedMarketCodes.size());
        assertEquals(MARKET_SEGMENT_1, distinctMappedMarketCodes.get(0));
        assertEquals(MARKET_SEGMENT_1 + "_DEF", distinctMappedMarketCodes.get(1));
    }

    @Test
    public void applyHistoryTruePresent() {
        when(marketSegmentService.retrieveModifiedHotelMarketSegmentCodes()).thenReturn(Collections.EMPTY_LIST);
        assertFalse(service.amsChangeApplyToHistoryPresent());
        when(marketSegmentService.retrieveModifiedHotelMarketSegmentCodes()).thenReturn(Collections.singletonList("TEST"));
        assertTrue(service.amsChangeApplyToHistoryPresent());
    }


    private void verifyWhenComplimentaryIsDifferentThenCCFGIsEnabled(String mappedCode) {
        AnalyticalMarketSegmentSummary amsMktSegSummary;
        MktSeg mktSeg;
        amsMktSegSummary = getAnalyticalMarketSegmentSummary(RATE_CODE_3, mappedCode);
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, false, null);
        mktSeg = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", mappedCode).parameters());
        assertNotNull(mktSeg.getMktSegDetailsProposed());
    }

    private void verifyWhenComplimentaryIsSameThenCCFGIsNotEnabled(String mappedCode) {
        AnalyticalMarketSegmentSummary amsMktSegSummary;
        MktSeg mktSeg;
        amsMktSegSummary = getAnalyticalMarketSegmentSummary(RATE_CODE_2, mappedCode);
        service.assignMarketSegments(Collections.singletonList(amsMktSegSummary),
                AnalyticalMarketSegmentAttribute.FENCED, forecastActivityTypeId, true, null);
        mktSeg = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE,
                QueryParameter.with("code", mappedCode).parameters());
        assertNull(mktSeg.getMktSegDetailsProposed());
    }

    private AnalyticalMarketSegmentSummary getAnalyticalMarketSegmentSummary(String rateCode, String mappedCode) {
        AnalyticalMarketSegmentSummary amsMktSegSummary = new AnalyticalMarketSegmentSummary();
        amsMktSegSummary.setMarketCode(MARKET_SEGMENT_5);
        amsMktSegSummary.setMappedCode(mappedCode);
        amsMktSegSummary.setRateCodeType(RateCodeTypeEnum.EQUALS.name());
        amsMktSegSummary.setRateCode(rateCode);
        return amsMktSegSummary;
    }

    @Test
    public void testApplyHistoryShouldReturnFalse() {
        final List<AMSCompositionChange> amsCompositionList = createAmsCompositionList();
        crudService.save(amsCompositionList);
        final Boolean applyHistoryStatus = service.applyHistoryChangeImpact(1);
        assertFalse(applyHistoryStatus);
    }

    @Test
    public void testApplyHistoryShouldReturnTrue() {
        final List<AMSCompositionChange> amsCompositionList = createAmsCompositionList();
        crudService.save(amsCompositionList);
        final Boolean applyHistoryStatus = service.applyHistoryChangeImpact(2);
        assertTrue(applyHistoryStatus);
    }

    @Test
    public void testApplyHistoryShouldReturnNull() {
        final List<AMSCompositionChange> amsCompositionList = createAmsCompositionList();
        crudService.save(amsCompositionList);
        final Boolean applyHistoryStatus = service.applyHistoryChangeImpact(3);
        assertNull(applyHistoryStatus);
    }

    @Test
    void deleteDiscontinuedMs() {
        final List<Integer> discontinuedMarketSegments = service.findDiscontinuedMarketSegments(Arrays.asList("BART",
                "CORP"));
        assertEquals(2, discontinuedMarketSegments.size());
        assertTrue(Arrays.asList(1, 4).containsAll(discontinuedMarketSegments));
    }

    @Test
    void populateMissingAmsEntries() {
        service.crudService = tenantCrudService;
        List<MktSeg> mktSegs = Stream.of("A", "C", "F").map(this::toMktSeg).collect(Collectors.toList());
        doReturn(mktSegs).when(tenantCrudService).findByNamedQuery(MktSeg.BY_PROPERTY_ID, (QueryParameter.with("propertyId", 23).parameters()));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MAPPED_MARKET_CODES))
                .thenReturn(Arrays.asList("A", "B"));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES)).thenReturn(Arrays.asList("E", "B", "C"));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MARKET_CODES)).thenReturn(Arrays.asList("E", "B", "C"));
        when(tenantCrudService.save(anyList())).thenAnswer(AdditionalAnswers.returnsFirstArg());
        assertEquals(1, service.populateMissingAmsEntries(23));
        verify(tenantCrudService).save(anyList());
    }

    @Test
    void populateMissingAmsEntries_NoNewEntries() {
        service.crudService = tenantCrudService;
        List<MktSeg> mktSegs = Stream.of("A", "B", "C").map(this::toMktSeg).collect(Collectors.toList());
        doReturn(mktSegs).when(tenantCrudService).findByNamedQuery(MktSeg.BY_PROPERTY_ID, (QueryParameter.with("propertyId", 23).parameters()));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MAPPED_MARKET_CODES))
                .thenReturn(Arrays.asList("A", "B"));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES)).thenReturn(Arrays.asList("E", "B", "C"));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MARKET_CODES)).thenReturn(Arrays.asList("E", "B", "C"));
        assertEquals(0, service.populateMissingAmsEntries(23));
        verify(tenantCrudService, times(0)).save(anyList());
    }

    private MktSeg toMktSeg(String s) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setCode(s);
        MktSegDetailsProposed mktSegDetailsProposed = new MktSegDetailsProposed();
        mktSegDetailsProposed.setMktSeg(mktSeg);
        mktSegDetailsProposed.setBookingBlockPc(1);
        List<BusinessType> businessTypes = crudService.findByNamedQuery(BusinessType.BY_NAME,
                QueryParameter.with("name", "Transient").parameters());
        mktSegDetailsProposed.setBusinessType(businessTypes.get(0));
        mktSegDetailsProposed.setFenced(0);
        mktSegDetailsProposed.setLink(0);
        mktSegDetailsProposed.setQualified(0);
        mktSegDetailsProposed.setTemplateDefault(0);
        mktSegDetailsProposed.setStatusId(1);
        mktSegDetailsProposed.setProcessStatus(new ProcessStatus());
        mktSegDetailsProposed.setPackageValue(0);
        mktSegDetailsProposed.setForecastActivityType(forecastActivityType);
        mktSegDetailsProposed.setPriceByBar(1);
        YieldType yieldType = new YieldType();
        yieldType.setId(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE.getId());
        mktSegDetailsProposed.setYieldType(yieldType);
        mktSeg.setMktSegDetailsProposed(mktSegDetailsProposed);
        return mktSeg;
    }

    private List<AMSCompositionChange> createAmsCompositionList() {
        List<AMSCompositionChange> amsList = new ArrayList<AMSCompositionChange>();
        AMSCompositionChange ams1 = new AMSCompositionChange();
        ams1.setAMSId(1);
        ams1.setMarketSegmentId(1);
        ams1.setAMSRoomsSold(82620);
        ams1.setChangeDate(LocalDateTime.now());
        ams1.setHotelPercent(new BigDecimal("26.96000"));
        ams1.setApplyToHistory(false);
        amsList.add(ams1);

        AMSCompositionChange ams2 = new AMSCompositionChange();
        ams2.setAMSId(1);
        ams2.setMarketSegmentId(1);
        ams2.setAMSRoomsSold(28543);
        ams2.setChangeDate(LocalDateTime.now());
        ams2.setHotelPercent(new BigDecimal("09.96000"));
        ams2.setApplyToHistory(false);
        amsList.add(ams2);

        AMSCompositionChange ams3 = new AMSCompositionChange();
        ams3.setAMSId(2);
        ams3.setMarketSegmentId(2);
        ams3.setAMSRoomsSold(27077);
        ams3.setChangeDate(LocalDateTime.now());
        ams3.setHotelPercent(new BigDecimal("16.96000"));
        ams3.setApplyToHistory(true);
        amsList.add(ams3);

        AMSCompositionChange ams4 = new AMSCompositionChange();
        ams4.setAMSId(2);
        ams4.setMarketSegmentId(2);
        ams4.setAMSRoomsSold(27057);
        ams4.setChangeDate(LocalDateTime.now());
        ams4.setHotelPercent(new BigDecimal("14.96000"));
        ams4.setApplyToHistory(false);
        amsList.add(ams4);

        return amsList;
    }

    @Test
    void groupChangesInsignificant() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuerySingleResult(AMSCompositionChange.GET_MODIFIED_GROUP_COUNT)).thenReturn(1);
        when(tenantCrudService.findByNamedQuerySingleResult(GroupBlockMaster.COUNT_AVAILABLE_GROUP_PAST_DAYS)).thenReturn(365);
        when(tenantCrudService.findAll(AMSCompositionChange.class)).thenReturn(Collections.EMPTY_LIST);
        assertTrue(service.groupChangesHaveSufficientData());
        verify(tenantCrudService, times(2)).findByNamedQuerySingleResult(anyString());
    }

    @Test
    void groupChangesInsignificant_NoGroups() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuerySingleResult(AMSCompositionChange.GET_MODIFIED_GROUP_COUNT)).thenReturn(0);
        when(tenantCrudService.findByNamedQuerySingleResult(GroupBlockMaster.COUNT_AVAILABLE_GROUP_PAST_DAYS)).thenReturn(365);
        assertTrue(service.groupChangesHaveSufficientData());
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(anyString());
    }

    @Test
    void groupChangesInsignificant_InsufficientData() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuerySingleResult(AMSCompositionChange.GET_MODIFIED_GROUP_COUNT)).thenReturn(1);
        when(tenantCrudService.findByNamedQuerySingleResult(GroupBlockMaster.COUNT_AVAILABLE_GROUP_PAST_DAYS)).thenReturn(DateUtil.ONE_YEAR - 10);
        when(tenantCrudService.findByNamedQuery(AMSCompositionChange.DISTINCT_GROUP_CHANGE)).thenReturn(Collections.singletonList(1));
        when(tenantCrudService.findAll(AMSCompositionChange.class)).thenReturn(Collections.singletonList(new AMSCompositionChange(1, 1, 1, 1, LocalDateTime.now(), BigDecimal.valueOf(23), false)));
        assertFalse(service.groupChangesHaveSufficientData());
        verify(tenantCrudService, times(2)).findByNamedQuerySingleResult(anyString());
    }

    @Test
    void splitChangesPresent() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_NON_STRAIGHT_AMS_RULES)).thenReturn(Arrays.asList(new AnalyticalMarketSegment()));
        assertTrue(service.splitAmsRulesPresent());
    }

    @Test
    void splitChangesNotPresent() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_NON_STRAIGHT_AMS_RULES)).thenReturn(Collections.EMPTY_LIST);
        assertFalse(service.splitAmsRulesPresent());
    }

    @Test
    public void populateYieldCategoryByRuleForStraightAndGroupMS() {
        //GIVEN
        crudService.executeUpdateByNativeQuery("TRUNCATE TABLE opera.Yield_Category_Rule ");
        createAMSRule("CORP", "CORPNew", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "1");
        createAMSRule("CORPNew", "CORPNew", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "0");
        createAMSRule("GORV", "GORV", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "0");
        //WHEN
        service.populateYieldCategoryByRuleForStraightAndGroupMS();
        //THEN
        List<YieldCategoryRule> ycbrRules = tenantCrudService().findAll(YieldCategoryRule.class);
        assertEquals(2, ycbrRules.size());
        assertEquals("CORP", ycbrRules.get(0).getMarketCode());
        assertEquals("CORPNew", ycbrRules.get(0).getAnalyticalMarketCode());
        assertEquals(1, ycbrRules.get(0).getRank());
        assertEquals("CORPNew", ycbrRules.get(1).getMarketCode());
        assertEquals("CORPNew", ycbrRules.get(1).getAnalyticalMarketCode());
        assertEquals(1, ycbrRules.get(1).getRank());
    }

    @Test
    public void isStraightPreservedEntriesAvailableInAMS() {
        createAMSRule("CORP", "CORPNew", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "1");
        createAMSRule("CORPNew", "CORPNew", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "0");
        boolean straightPreservedEntriesAvailableInAMS = service.isStraightPreservedEntriesAvailableInAMS();
        assertTrue(straightPreservedEntriesAvailableInAMS);
    }

    @Test
    public void shouldReturnFalseWhenThereAreNoStraightPreservedEntriesAvailableInAMS() {
        createAMSRule("CORP", "CORP", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "0");
        boolean straightPreservedEntriesAvailableInAMS = service.isStraightPreservedEntriesAvailableInAMS();
        assertFalse(straightPreservedEntriesAvailableInAMS);
    }

    protected void createAMSRule(final String marketCode,
                                 final String mappedMarketCode,
                                 final String attribute,
                                 final String rateCodeType, final String rank, final String preserved) {
        createAms(marketCode, "null", mappedMarketCode, attribute, rateCodeType, rank, preserved);
    }

    private void createAms(String marketCode, String rateCode, String mappedMarketCode, String attribute, String rateCodeType, String rank, String preserved) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg values('" + marketCode + "', " + rateCode + ", " +
                "'" + mappedMarketCode + "', '" + attribute + "', 1, getdate(), 1, getdate(), '" + rateCodeType + "', " + rank + ", 0, "
                + preserved + ")");
    }

    @Test
    public void shouldGetAllAmsRulesAlongWithForecastActivityType() {
        createAms("BAR", "null", "BAR", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", "0");
        List<AnalyticalMarketSegment> allAMSWithForecastActivityType = service.getAllAMSWithForecastActivityType();
        assertEquals("BAR", allAMSWithForecastActivityType.get(0).getMarketCode());
        assertEquals("BAR", allAMSWithForecastActivityType.get(0).getMappedMarketCode());
        assertEquals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, allAMSWithForecastActivityType.get(0).getAttribute());
        assertEquals("Demand and Wash", allAMSWithForecastActivityType.get(0).getForecastActivityType().getName());
    }

    @Test
    void getAmsRatesByRateCode() {
        var rateCodes = Set.of("Test");
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.BY_RATE_CODES, Map.of("rateCodes", rateCodes)))
                .thenReturn(List.of(new AnalyticalMarketSegment()));
        var result = service.getAmsByRateCodes(rateCodes);
        assertEquals(1, result.size());
    }

    @Test
    void getAmsRatesByRateCodeEmptyInput() {
        var result = service.getAmsByRateCodes(Set.of());
        assertEquals(0, result.size());
    }

    @Test
    void getAMSAuditReportWithIPOn() {
        service.crudService = tenantCrudService;
        service.getAMSAuditReportWithIPOn();
        verify(tenantCrudService).findByNamedQuery(AnalyticalMarketSegmentAuditMapping.FIND_ALL_AUDITS_WITH_PRODUCT);
    }

    @Test
    void createNewMarketSegmentsFromReservationNights() {
        service.crudService = tenantCrudService;
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED)).thenReturn(true);
        when(tenantCrudService.<String>findByNativeQuery(ReservationNight.GET_NEW_AMS_TO_BE_CREATED_INCLUDE_GROUP
                , QueryParameter.with("marketCodes", MODIFIED_HOTEL_MKT_SEGS).parameters())).thenReturn(SPLIT_AMS);
        when(tenantCrudService.<String>findByNativeQuery(ReservationNight.GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED_INCLUDE_GROUPS
                , QueryParameter.with("marketCodes", MODIFIED_HOTEL_MKT_SEGS).parameters())).thenReturn(List.of());
        when(marketSegmentRepository.createMarketSegments(new HashSet<>(SPLIT_AMS))).thenReturn(List.of());
        service.createNewMarketSegmentsFromReservationNights(MODIFIED_HOTEL_MKT_SEGS);
        verify(tenantCrudService).findByNativeQuery(ReservationNight.GET_NEW_AMS_TO_BE_CREATED_INCLUDE_GROUP
                , QueryParameter.with("marketCodes", MODIFIED_HOTEL_MKT_SEGS).parameters());
        verify(tenantCrudService).findByNativeQuery(eq(ReservationNight.GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED_INCLUDE_GROUPS), anyMap());
        verify(marketSegmentRepository).createMarketSegments(new HashSet<>(SPLIT_AMS));
        verify(marketSegmentService).createDetailsProposedFromMaster();
    }

    @Test
    void shouldDeleteProductRateCodesWhenProductAndMarketSegmentIsProvided() {
        service.crudService = tenantCrudService;
        Product product = new Product();
        product.setCode("newProduct");
        product.setId(1);
        when(marketSegmentRepository.getRateCodesForMarketSegments(anyList())).thenReturn(List.of());

        service.deleteProductRateCodesFor(MARKET_SEGMENT_1, product);

        verify(independentProductsRepository).removeRateCodesFromProduct(eq(product), anyList(), anyList());
    }

    @Test
    void shouldDeleteProductRateCodesWhenMarketSegmentIsProvidedAndBaseProductIsNotAssigned() {
        service.crudService = tenantCrudService;
        when(marketSegmentRepository.getRateCodesForMarketSegments(anyList())).thenReturn(List.of());

        service.deleteProductRateCodesFor(MARKET_SEGMENT_1, null);

        verify(independentProductsRepository, times(0)).removeRateCodesFromProduct(eq(null), anyList(), anyList());
    }

    @Test
    void testsplitAmsForIndependentProducts() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of(createProduct(1, "LV1")));
        when(tenantCrudService.findByNamedQuery(MktSegDetails.GET_ALL)).thenReturn(new ArrayList<>());
        spyService.splitAmsBasedOnIndependentProducts();
        verify(tenantCrudService).findByNamedQuery(MktSegDetails.GET_ALL);
        verify(tenantCrudService).findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS);
    }

    @Test
    void testInsertMktSegProductMapping() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        Product lv1 = (createProduct(1, "LV1"));
        AnalyticalMarketSegment ams = getMockTierAnalyticalMarketSegment();

        when(tenantCrudService.findByNamedQuerySingleResult(MarketSegmentProductMapping.BY_MS,
                QueryParameter.with("marketSegmentCode", "Tier1").parameters())).thenReturn(null);
        spyService.upsertMktSegProductMappingForSpecialESMktSeg(lv1, ams);
        ArgumentCaptor<MarketSegmentProductMapping> argumentCaptor = ArgumentCaptor.forClass(MarketSegmentProductMapping.class);
        verify(tenantCrudService).save(argumentCaptor.capture());
        MarketSegmentProductMapping savedMapping = argumentCaptor.getValue();
        assertNull(savedMapping.getId());
        assertNotNull(savedMapping);
        assertEquals(lv1, savedMapping.getProduct());
        assertEquals("Tier1", savedMapping.getMarketSegmentCode());
        assertTrue(savedMapping.isIp());
    }

    @Test
    void testUpdateMktSegProductMapping() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        Product bar = (createProduct(1, "BAR"));
        Product lv1 = (createProduct(2, "LV1"));
        AnalyticalMarketSegment ams = getMockTierAnalyticalMarketSegment();

        MarketSegmentProductMapping mktSegProductMapping = getMockTierMktSegProductMapping(bar);
        when(tenantCrudService.findByNamedQuerySingleResult(MarketSegmentProductMapping.BY_MS,
                QueryParameter.with("marketSegmentCode", "Tier1").parameters())).thenReturn(mktSegProductMapping);
        spyService.upsertMktSegProductMappingForSpecialESMktSeg(lv1, ams);
        ArgumentCaptor<MarketSegmentProductMapping> argumentCaptor = ArgumentCaptor.forClass(MarketSegmentProductMapping.class);
        verify(tenantCrudService).save(argumentCaptor.capture());
        MarketSegmentProductMapping savedMapping = argumentCaptor.getValue();
        assertEquals(1, savedMapping.getId());
        assertNotNull(savedMapping);
        assertEquals(lv1, savedMapping.getProduct());
        assertEquals("Tier1", savedMapping.getMarketSegmentCode());
        assertTrue(savedMapping.isIp());
    }

    private MarketSegmentProductMapping getMockTierMktSegProductMapping(Product bar) {
        MarketSegmentProductMapping marketSegmentProductMapping = new MarketSegmentProductMapping();
        marketSegmentProductMapping.setId(1);
        marketSegmentProductMapping.setProduct(bar);
        marketSegmentProductMapping.setIp(false);
        marketSegmentProductMapping.setMarketSegmentCode("Tier1");
        return marketSegmentProductMapping;
    }

    private AnalyticalMarketSegment getMockTierAnalyticalMarketSegment() {
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL,
                "Tier1", EQUAL_TO_BAR, 0);
        ams.setMappedMarketCode("Tier1");
        return ams;
    }

    @Test
    void testGetTierMktSegProductMapForNonOperaProperty() {
        setWorkContextProperty(TestProperty.H1);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), "BSTN", "H1")).thenReturn(false);
        Map<String, String> result = service.getTierMktSegProductMap();
        assertEquals("LV1", result.get("Tier1"));
        assertEquals("LV2", result.get("Tier2"));
        assertEquals("LV3", result.get("Tier3"));
    }

    @Test
    void testGetTierMktSegProductMapForOperaProperty() {
        setWorkContextProperty(TestProperty.H1);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), "BSTN", "H1")).thenReturn(true);
        Map<String, String> result = service.getTierMktSegProductMap();
        assertEquals("LV2", result.get("Tier1"));
        assertEquals("LV3", result.get("Tier2"));
        assertEquals("LV4", result.get("Tier3"));
    }

    @Test
    void testGetTierMktSegProductMapForNonOperaPropertyForVP() {
        setWorkContextProperty(TestProperty.H1);
        VirtualPropertyMapping vpm = new VirtualPropertyMapping();
        vpm.setOperaConfiguration(false);
        Map<String, String> result = service.getTierMktSegProductMapForVP(vpm);
        assertEquals("LV1", result.get("Tier1"));
        assertEquals("LV2", result.get("Tier2"));
        assertEquals("LV3", result.get("Tier3"));
    }

    @Test
    void testGetTierMktSegProductMapForOperaPropertyForVP() {
        setWorkContextProperty(TestProperty.H1);
        VirtualPropertyMapping vpm = new VirtualPropertyMapping();
        vpm.setOperaConfiguration(true);
        Map<String, String> result = service.getTierMktSegProductMapForVP(vpm);
        assertEquals("LV2", result.get("Tier1"));
        assertEquals("LV3", result.get("Tier2"));
        assertEquals("LV4", result.get("Tier3"));
    }

    @Test
    public void testCreateProductRateCode() {
        Product product = new Product();
        List<ProductRateCode> productRateCodeList = new ArrayList<>();
        service.createProductRateCode(productRateCodeList, product, "COR18");
        assertEquals(1, productRateCodeList.size());
        ProductRateCode addedProductRateCode = productRateCodeList.get(0);
        assertEquals(product, addedProductRateCode.getProduct());
        assertEquals("COR18", addedProductRateCode.getRateCode());
    }

    @Test
    public void testCreateMarketSegmentProductMapping() {
        Product product = (createProduct(1, "LV1"));
        List<MarketSegmentProductMapping> marketSegmentProductMapping = new ArrayList<>();
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                "MS1", AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE, 0);
        service.createMarketSegmentProductMapping(marketSegmentProductMapping, product, ams);
        assertEquals(1, marketSegmentProductMapping.size());
        MarketSegmentProductMapping addedProductMapping = marketSegmentProductMapping.get(0);
        assertEquals(product, addedProductMapping.getProduct());
        assertEquals("MS1_LV1_QNYL", addedProductMapping.getMarketSegmentCode());
    }

    @Test
    public void testCreateAmsSummary() {
        Product product = (createProduct(1, "LV1"));
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(10, RATE_CODE_1, RateCodeTypeEnum.EQUALS,
                "MS1", AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE, 0);
        AnalyticalMarketSegmentSummary amsSummary = service.createAmsSummary(product, ams);
        assertEquals("MS1", amsSummary.getMarketCode());
        assertEquals("MS1_LV1_QNYL", amsSummary.getMappedCode());
        assertEquals(RATE_CODE_1 + "_LV1", amsSummary.getRateCode());
        assertEquals("EQUALS", amsSummary.getRateCodeType());
    }

    @Test
    public void testCreateAmsSummaryForStraightAms() {
        Product product = (createProduct(1, "LV1"));
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.ALL,
                "MS", AnalyticalMarketSegmentAttribute.FENCED, 0);
        ams.setMappedMarketCode("MS");
        AnalyticalMarketSegmentSummary amsSummary = service.createAmsSummary(product, ams);
        assertEquals("MS_LV1", amsSummary.getMarketCode());
        assertEquals("MS_LV1", amsSummary.getMappedCode());
        assertEquals(null, amsSummary.getRateCode());
        assertEquals("ALL", amsSummary.getRateCodeType());
    }

    @Test
    public void testCreateAmsSummaryForDefAms() {
        Product product = (createProduct(1, "LV1"));
        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(999, null, RateCodeTypeEnum.DEFAULT,
                "MS", AnalyticalMarketSegmentAttribute.FENCED, 0);
        ams.setMappedMarketCode("MS_DEF");
        AnalyticalMarketSegmentSummary amsSummary = service.createAmsSummary(product, ams);
        assertEquals("MS", amsSummary.getMarketCode());
        assertEquals("MS_LV1_DEF", amsSummary.getMappedCode());
        assertEquals(null, amsSummary.getRateCode());
        assertEquals("DEFAULT", amsSummary.getRateCodeType());
    }


    Product createProduct(int id, String name) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setCode("INDEPENDENT");
        product.setType("INDEPENDENTLY");
        return product;
    }

    @Test
    void testMappedMarketCodesToAmsIdWithoutCodes() {
        createAms("MS1", "null", "MS1", UNFENCED_NON_PACKAGED.toString(), "ALL", "1", "0");
        createAms("MS2", "null", "MS2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE.toString(), "ALL", "1", "0");
        createAms("DISC", "'RC1'", "DISC_QNYL", QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE.toString(), "EQUALS", "10", "0");
        createAms("CMTG", "null", "CMTG", GROUP.toString(), "ALL", "1", "0");
        createAms("Tier1", "null", "Tier1", UNFENCED_NON_PACKAGED.toString(), "ALL", "1", "0");

        createMktSegMaster("MS1", UNFENCED_NON_PACKAGED);
        createMktSegMaster("MS2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createMktSegMaster("DISC_QNYL", QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE);

        Map<String, Integer> mappedCodeToIdMap = service.mappedMarketCodesToAmsIdWithoutCodes(SPECIAL_IPP_MARKET_CODES);

        assertEquals(2, mappedCodeToIdMap.size());
    }

    @Test
    void shouldGetAmsByMarketCodes() {
        createAms("MS1", "null", "MS1", UNFENCED_NON_PACKAGED.toString(), "ALL", "1", "0");
        createAms("BAR", "null", "BAR", QUALIFIED_NONBLOCK_LINKED_YIELDABLE.toString(), "ALL", "1", "0");
        createAms("DISC", "'RC1'", "DISC_QNYL", QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE.toString(), "EQUALS", "10", "0");
        createAms("CMTG", "null", "CMTG", GROUP.toString(), "ALL", "1", "0");
        createAms("Tier1", "null", "Tier1", UNFENCED_NON_PACKAGED.toString(), "ALL", "1", "0");

        List<AnalyticalMarketSegment> analyticalMarketSegments = service.getAmsByMarketCodes(Set.of("MS1", "BAR", "DISC", "CMTG"));

        analyticalMarketSegments = new ArrayList<>(analyticalMarketSegments);
        analyticalMarketSegments.sort(Comparator.comparing(analyticalMarketSegment -> analyticalMarketSegment.getMarketCode()));
        assertEquals(4, analyticalMarketSegments.size());
        assertEquals("BAR", analyticalMarketSegments.get(0).getMarketCode());
        assertEquals("BAR", analyticalMarketSegments.get(0).getMappedMarketCode());
        assertEquals("CMTG", analyticalMarketSegments.get(1).getMarketCode());
        assertEquals("CMTG", analyticalMarketSegments.get(1).getMappedMarketCode());
        assertEquals("DISC", analyticalMarketSegments.get(2).getMarketCode());
        assertEquals("DISC_QNYL", analyticalMarketSegments.get(2).getMappedMarketCode());
        assertEquals("MS1", analyticalMarketSegments.get(3).getMarketCode());
        assertEquals("MS1", analyticalMarketSegments.get(3).getMappedMarketCode());
    }

    @Test
    void shouldReturnEmptyListWhenMarketCodesAreEmpty() {
        List<AnalyticalMarketSegment> analyticalMarketSegments = service.getAmsByMarketCodes(null);

        assertEquals(0, analyticalMarketSegments.size());
    }

    private void createMktSegMaster(String mappedMktCode, AnalyticalMarketSegmentAttribute attribute) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg_Master values('" + mappedMktCode + "', '" + mappedMktCode + "', '" + mappedMktCode
                + "', " + attribute.getBusinessType().getId() + ", " + attribute.getYieldType().getId() + ",1," + booleanToInt(attribute.getQualified())
                + ", " + booleanToInt(attribute.getFenced()) + ", " + booleanToInt(attribute.getPackaged()) + ", " + attribute.getLinkType().getId()
                + ", " + booleanToInt(attribute.getPricedByBar()) + ", " + attribute.getBlockPercent() + ", 1, NULL)");
    }

    private int booleanToInt(Boolean val) {
        return Boolean.TRUE.equals(val) ? 1 : 0;
    }


    @Test
    void testSplitAmsForIndependentProducts_whenIndependentProductsListIsEmpty() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of());
        doReturn(List.of(MARKET_SEGMENT_1)).when(spyService).getMktSegCodesToSplit();
        spyService.splitAmsBasedOnIndependentProducts();
        verify(spyService, times(0)).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        verify(spyService, times(0)).updateProductRateCodeForStraightAms(anyList(), anyList());
        verify(spyService, times(0)).updateTierMarketSegments();
    }

    @Test
    void testSplitAmsForIndependentProducts_whenMarketCodesToSplitIsEmpty() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of(createProduct(1, "LV1"), createProduct(2, "LV2"), createProduct(3, "LV3")));
        when(spyService.getMktSegCodesToSplit()).thenReturn(List.of());
        spyService.splitAmsBasedOnIndependentProducts();
        verify(spyService, times(0)).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        verify(spyService, times(0)).updateProductRateCodeForStraightAms(anyList(), anyList());
        verify(spyService, times(0)).updateTierMarketSegments();
    }

    @Test
    void testSplitAmsForIndependentProducts_whenAmsToSplitIsEmpty() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of(createProduct(1, "LV1"), createProduct(2, "LV2"), createProduct(3, "LV3")));
        doReturn(List.of(MARKET_SEGMENT_1)).when(spyService).getMktSegCodesToSplit();
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES, Map.of("mappedMarketCodes", List.of(MARKET_SEGMENT_1)))).thenReturn(List.of());
        spyService.splitAmsBasedOnIndependentProducts();
        verify(spyService, times(0)).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        verify(spyService, times(0)).updateProductRateCodeForStraightAms(anyList(), anyList());
        verify(spyService, times(0)).updateTierMarketSegments();
    }

    @Test
    void testSplitAmsForIndependentProducts_whenAllConditionsMet() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of(createProduct(1, "LV1"), createProduct(2, "LV2"), createProduct(3, "LV3")));
        doReturn(List.of(MARKET_SEGMENT_1)).when(spyService).getMktSegCodesToSplit();
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES, Map.of("mappedMarketCodes", List.of(MARKET_SEGMENT_1)))).thenReturn(List.of(new AnalyticalMarketSegment()));
        when(tenantCrudService.findByNamedQuery(MktSeg.ID_BY_CODES, Map.of("marketCodes", List.of(MARKET_SEGMENT_7)))).thenReturn(List.of(new AnalyticalMarketSegment()));
        doAnswer(invocation -> {
            List<String> mktSegIdOfStraightMS = invocation.getArgument(2);
            mktSegIdOfStraightMS.add(MARKET_SEGMENT_7);
            return null;
        }).when(spyService).splitAmsBasedOnIpp(anyList(), anyList(), anyList());

        doNothing().when(spyService).updateProductRateCodeForStraightAms(anyList(), anyList());
        doNothing().when(spyService).updateTierMarketSegments();
        spyService.splitAmsBasedOnIndependentProducts();
        verify(spyService, times(1)).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        verify(spyService, times(1)).updateProductRateCodeForStraightAms(anyList(), anyList());
    }

    @Test
    void testSplitAmsForIndependentProducts_whenAllConditionsMetAndNoStraightMsIdFound() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        when(tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS)).thenReturn(List.of(createProduct(1, "LV1"), createProduct(2, "LV2"), createProduct(3, "LV3")));
        doReturn(List.of(MARKET_SEGMENT_1)).when(spyService).getMktSegCodesToSplit();
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES, Map.of("mappedMarketCodes", List.of(MARKET_SEGMENT_1)))).thenReturn(List.of(new AnalyticalMarketSegment()));
        doNothing().when(spyService).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        doNothing().when(spyService).updateProductRateCodeForStraightAms(anyList(), anyList());
        doNothing().when(spyService).updateTierMarketSegments();
        spyService.splitAmsBasedOnIndependentProducts();
        verify(spyService, times(1)).splitAmsBasedOnIpp(anyList(), anyList(), anyList());
        verify(spyService, times(0)).updateProductRateCodeForStraightAms(anyList(), anyList());
    }

    @Test
    void doNotCreateParentMktSegIfExists() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        MktSeg mktSeg1 = createMktSeg(MKT_SEG_1_AFFIXED);
        MktSeg mktSeg2 = createMktSeg(MKT_SEG_2_AFFIXED);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(10, "RC1", RateCodeTypeEnum.ALL, MKT_SEG_1_AFFIXED);
        Map<String, MktSeg> mktSegMap = Map.of(
                MKT_SEG_1_AFFIXED, mktSeg1,
                MKT_SEG_2_AFFIXED, mktSeg2);

        spyService.createParentMktSegIfNotExists(mktSegMap, ams1);

        verify(spyService, never()).updateMktSeg(anyString(), any(), anyBoolean());
    }

    @Test
    void createParentMktSegIfNotExists() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        MktSeg mktSeg1 = createMktSeg(MKT_SEG_1_AFFIXED);
        MktSeg mktSeg2 = createMktSeg(MKT_SEG_2_AFFIXED);
        AnalyticalMarketSegment ams1 = createAnalyticalMarketSegment(10, "RC1", RateCodeTypeEnum.ALL, MARKET_SEGMENT_3);
        Map<String, MktSeg> mktSegMap = Map.of(
                MKT_SEG_1_AFFIXED, mktSeg1,
                MKT_SEG_2_AFFIXED, mktSeg2);

        spyService.createParentMktSegIfNotExists(mktSegMap, ams1);

        verify(spyService, times(1)).updateMktSeg(MARKET_SEGMENT_3, ams1, true);
    }

    @Test
    void shouldProcessForIpp() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        boolean result = service.shouldProcessForIpp();
        assertTrue(result);
    }

    @Test
    void shouldNotProcessForIppWhenIppToggleDisabled() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        boolean result = service.shouldProcessForIpp();
        assertFalse(result);
    }

    @Test
    void shouldNotProcessForIppWhenLdbToggleEnabled() {
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        boolean result = service.shouldProcessForIpp();
        assertFalse(result);
    }

    @Test
    void testCheckNewRateCodesFOrStraightAms(){
        List<Product> products = mockIndependentProducts();
        String parentMs = "CMP";
        List<AnalyticalMarketSegment> existingAmsList = mockAmsForUpdateAmsTest().stream()
                .filter(ams -> ams.getRank()==1 && ams.getMarketCode().equals(parentMs)).collect(Collectors.toList());
        service.crudService = tenantCrudService;
        AnalyticalMarketSegmentService spyService = spy(service);
        List<String> parentAmsCodesList = List.of(parentMs);
        List<Integer> msIds = List.of(2);
        when(tenantCrudService.<Integer>findByNamedQuery(MktSeg.ID_BY_CODES,  Map.of("marketCodes", parentAmsCodesList))).thenReturn(msIds);
        when(tenantCrudService.<AnalyticalMarketSegment>findByNamedQuery(AnalyticalMarketSegment.BY_RANK_AND_MARKET_CODES,
                QueryParameter.with("rank",1).and("marketCodes", parentAmsCodesList).parameters())).thenReturn(existingAmsList);
        when(spyService.getIndependentProducts()).thenReturn(products);
        doNothing().when(spyService).updateProductRateCodeForStraightAms(products, msIds);

        spyService.checkForNewRateCodeForStraightAMS(parentAmsCodesList);

        verify(spyService, times(1)).updateProductRateCodeForStraightAms(products, msIds);
    }

    @Test
    @Transactional
    void testUpdateSplitAmsForIpp() {
        truncateAllRelatedTables();
        mockIndependentProducts();
        List<Product> products = service.getIndependentProducts();
        mockAmsForUpdateAmsTest();
        List<String> splitAmsCodes = List.of("LNR_UF", "CNR_QSL", "CMP", "IT_DEF_FUT", "MKT_SEG_3");
        List<AnalyticalMarketSegment> existingAmsList = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);

        AnalyticalMarketSegment newAms1 = createAms(MKT_SEG_1, RATE_CODE_1, "LNR_UF", RateCodeTypeEnum.EQUALS, 10, FENCED);
        AnalyticalMarketSegment newAms2 = createAms(MKT_SEG_2, RATE_CODE_2, "CNR_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment newAms3 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, PACKAGED);
        AnalyticalMarketSegment newAms4 = createAms(MKT_SEG_1, RATE_CODE_3, "LNR_UF", RateCodeTypeEnum.EQUALS, 10, FENCED);
        AnalyticalMarketSegment newAms5 = createAms(MKT_SEG_4, null, "IT_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment newAms6 = createAms(MKT_SEG_4, RATE_CODE_4, "IT_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        service.updateSplitAmsForIpp(products, List.of(newAms1, newAms2, newAms3, newAms4, newAms5, newAms6), splitAmsCodes, existingAmsList);
        List<AnalyticalMarketSegment> result = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);
        assertEquals(23, result.size());
        Map<String, AnalyticalMarketSegment> resultMap = result.stream()
                .collect(Collectors.toMap(ams -> ams.getMappedMarketCode() + ams.getRateCode(), ams -> ams));
        assertEquals(AnalyticalMarketSegmentAttribute.FENCED ,resultMap.get("LNR_LV3_UFBA_LV3").getAttribute());
        assertEquals(AnalyticalMarketSegmentAttribute.FENCED ,resultMap.get("LNR_LV3_UFRC1_LV3").getAttribute());
        assertEquals(AnalyticalMarketSegmentAttribute.PACKAGED ,resultMap.get("CMP_LV2null").getAttribute());
        assertEquals(QUALIFIED_NONBLOCK_LINKED_YIELDABLE ,resultMap.get("IT_LV2_DEF_FUTnull").getAttribute());
        assertEquals(QUALIFIED_NONBLOCK_LINKED_YIELDABLE ,resultMap.get("IT_LV3_DEF_FUTRC4_LV3").getAttribute());
        assertEquals("RC1_LV1" ,resultMap.get("LNR_LV1_UFRC1_LV1").getRateCode());
        assertEquals(null ,resultMap.get("CMP_LV2null").getRateCode());
        assertEquals(null ,resultMap.get("IT_LV2_DEF_FUTnull").getRateCode());
        assertEquals("RC4_LV3" ,resultMap.get("IT_LV3_DEF_FUTRC4_LV3").getRateCode());
    }

    @Test
    @Transactional
    void testUpdateSplitAmsForIppForVP(){
        truncateAllRelatedTables();
        mockIndependentProductsForVirtualProperty();
        List<Product> products = service.getIndependentProducts();
        mockAmsForUpdateAmsTestForVP();
        List<String> splitAmsCodes = List.of("LNR_UF", "CNR_QSL", "CMP", "IT_DEF_FUT", "MKT_SEG_3");
        List<AnalyticalMarketSegment> existingAmsList = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);

        AnalyticalMarketSegment newAms1 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_1, "LNR_UF", RateCodeTypeEnum.EQUALS, 10, FENCED);
        AnalyticalMarketSegment newAms2 = createAms(MKT_SEG_2, "VP1_" +RATE_CODE_2, "CNR_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment newAms3 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, PACKAGED);
        AnalyticalMarketSegment newAms4 = createAms(MKT_SEG_1, "VP2_" + RATE_CODE_3, "LNR_UF", RateCodeTypeEnum.EQUALS, 10, FENCED);
        AnalyticalMarketSegment newAms5 = createAms(MKT_SEG_4, null, "IT_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment newAms6 = createAms(MKT_SEG_4, "VP1_" + RATE_CODE_4, "IT_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        service.updateSplitAmsForIppForVP(products, List.of(newAms1, newAms2, newAms3, newAms4, newAms5, newAms6), splitAmsCodes, existingAmsList, List.of("VP1", "VP2"));
        List<AnalyticalMarketSegment> result = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);
        assertEquals(44, result.size());
        Map<String, AnalyticalMarketSegment> resultMap = result.stream()
                .collect(Collectors.toMap(ams -> ams.getMappedMarketCode() + ams.getRateCode(), ams -> ams));
        assertEquals(AnalyticalMarketSegmentAttribute.FENCED ,resultMap.get("LNR_VP1_LV3_UFVP1_BA_VP1_LV3").getAttribute());
        assertEquals(AnalyticalMarketSegmentAttribute.FENCED ,resultMap.get("LNR_VP2_LV3_UFVP2_RC1_VP2_LV3").getAttribute());
        assertEquals(AnalyticalMarketSegmentAttribute.PACKAGED ,resultMap.get("CMP_VP1_LV2null").getAttribute());
        assertEquals(AnalyticalMarketSegmentAttribute.PACKAGED ,resultMap.get("CMP_VP2_LV2null").getAttribute());
        assertEquals(QUALIFIED_NONBLOCK_LINKED_YIELDABLE ,resultMap.get("IT_VP1_LV2_DEF_FUTnull").getAttribute());
        assertEquals(QUALIFIED_NONBLOCK_LINKED_YIELDABLE ,resultMap.get("IT_VP1_LV3_DEF_FUTVP1_RC4_VP1_LV3").getAttribute());
        assertEquals("VP1_RC4_VP1_LV3" ,resultMap.get("IT_VP1_LV3_DEF_FUTVP1_RC4_VP1_LV3").getRateCode());
    }

    private List<AnalyticalMarketSegment> mockAmsForUpdateAmsTest() {
        AnalyticalMarketSegment ams1 = createAms(MKT_SEG_1, RATE_CODE_1, "LNR_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams2 = createAms(MKT_SEG_1, RATE_CODE_1 + "_LV1", "LNR_LV1_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams3 = createAms(MKT_SEG_1, RATE_CODE_1 + "_LV2", "LNR_LV2_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams4 = createAms(MKT_SEG_1, RATE_CODE_1 + "_LV3", "LNR_LV3_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams5 = createAms(MKT_SEG_2, RATE_CODE_2, "CNR_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams6 = createAms(MKT_SEG_2, RATE_CODE_2 + "_LV1", "CNR_LV1_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams7 = createAms(MKT_SEG_2, RATE_CODE_2 + "_LV2", "CNR_LV2_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams8 = createAms(MKT_SEG_2, RATE_CODE_2 + "_LV3", "CNR_LV3_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams9 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams10 = createAms(MKT_SEG_3 + "_LV1", null, MKT_SEG_3 + "_LV1", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams11 = createAms(MKT_SEG_3 + "_LV2", null, MKT_SEG_3 + "_LV2", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams12 = createAms(MKT_SEG_3 + "_LV3", null, MKT_SEG_3 + "_LV3", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams13 = createAms(MKT_SEG_4, null, "IT_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams14 = createAms(MKT_SEG_4, null, "IT_LV1_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams15 = createAms(MKT_SEG_4, null, "IT_LV2_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams16 = createAms(MKT_SEG_4, null, "IT_LV3_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams17 = createAms(MKT_SEG_4, RATE_CODE_4, "IT_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams18 = createAms(MKT_SEG_4, RATE_CODE_4 + "_LV1", "IT_LV1_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams19 = createAms(MKT_SEG_4, RATE_CODE_4 + "_LV2", "IT_LV2_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams20 = createAms(MKT_SEG_4, RATE_CODE_4 + "_LV3", "IT_LV3_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);

        List<AnalyticalMarketSegment> amsList = List.of(ams1, ams2, ams3, ams4, ams5, ams6, ams7, ams8, ams9, ams10, ams11, ams12, ams13, ams14, ams15, ams16, ams17, ams18, ams19, ams20);
        crudService.save(amsList);
        return amsList;
    }

    private List<AnalyticalMarketSegment> mockAmsForUpdateAmsTestForVP() {
        AnalyticalMarketSegment ams1 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_1, "LNR_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams2 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_1 + "_LV1", "LNR_VP1_LV1_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams3 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_1 + "_LV2", "LNR_VP1_LV2_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams4 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_1 + "_LV3", "LNR_VP1_LV3_QYL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams5 = createAms(MKT_SEG_2, "VP2_" + RATE_CODE_2, "CNR_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams6 = createAms(MKT_SEG_2, "VP2_" + RATE_CODE_2 + "_LV1", "CNR_VP1_LV1_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams7 = createAms(MKT_SEG_2, "VP2_" + RATE_CODE_2 + "_LV2", "CNR_VP1_LV2_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams8 = createAms(MKT_SEG_2, "VP2_" + RATE_CODE_2 + "_LV3", "CNR_VP1_LV3_QSL", RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams9 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams10 = createAms(MKT_SEG_3 + "_LV1", null, MKT_SEG_3 + "_LV1", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams11 = createAms(MKT_SEG_3 + "_LV2", null, MKT_SEG_3 + "_LV2", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams12 = createAms(MKT_SEG_3 + "_LV3", null, MKT_SEG_3 + "_LV3", RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams13 = createAms(MKT_SEG_4, null, "IT_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams14 = createAms(MKT_SEG_4, null, "IT_LV1_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams15 = createAms(MKT_SEG_4, null, "IT_LV2_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams16 = createAms(MKT_SEG_4, null, "IT_LV3_DEF_FUT", RateCodeTypeEnum.DEFAULT, 999, FENCED_AND_PACKAGED);
        AnalyticalMarketSegment ams17 = createAms(MKT_SEG_4, "VP1_" + RATE_CODE_4, "IT_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams18 = createAms(MKT_SEG_4, "VP1_" + RATE_CODE_4 + "_LV1", "IT_VP1_LV1_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams19 = createAms(MKT_SEG_4, "VP1_" + RATE_CODE_4 + "_LV2", "IT_VP1_LV2_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);
        AnalyticalMarketSegment ams20 = createAms(MKT_SEG_4, "VP1_" + RATE_CODE_4 + "_LV3", "IT_VP1_LV3_DEF_FUT", RateCodeTypeEnum.EQUALS, 10, PACKAGED);

        List<AnalyticalMarketSegment> amsList = List.of(ams1, ams2, ams3, ams4, ams5, ams6, ams7, ams8, ams9, ams10, ams11, ams12, ams13, ams14, ams15, ams16, ams17, ams18, ams19, ams20);
        crudService.save(amsList);
        return amsList;
    }

    @Test
    @Transactional
    void amsSplitForHiltonIppDataTest() {
        truncateAllRelatedTables();
        mockIndependentProducts();
        mockMktSeg();
        mockAms();
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), "BLKSTN", "H1")).thenReturn(false);

        service.splitAmsBasedOnIndependentProducts();

        runAssertionsForAnalyticalMktSeg();
        runAssertionsForMktSegProductMapping();
        runAssertionsForProductRateCode();
        runAssertionsForMktSegMasterAndMktSegDetailsProposed();
    }

    @Test
    @Transactional
    void amsSplitForHiltonIppDataTestForVirtualProperty() {
        truncateAllRelatedTables();
        mockVirtualPropertyMappings();
        mockIndependentProductsForVirtualProperty();
        mockMktSeg();
        mockAmsForVirtualProperty();
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), "BLKSTN", "H1")).thenReturn(false);

        service.splitAmsBasedOnIndependentProductsForVirtualProperty(PROPERTY_ID);

        runAssertionsForAnalyticalMktSegForVP();
        runAssertionsForMktSegProductMappingForVP();
        runAssertionsForProductRateCodeForVP();
        runAssertionsForMktSegMasterAndMktSegDetailsProposedForVP();
    }

    private void mockVirtualPropertyMappings() {
        VirtualPropertyMapping vpm1 = new VirtualPropertyMapping();
        vpm1.setExtendedStay(true);
        vpm1.setPhysicalPropertyCode("VP1");

        VirtualPropertyMapping vpm2 = new VirtualPropertyMapping();
        vpm2.setExtendedStay(true);
        vpm2.setPhysicalPropertyCode("VP2");

        VirtualPropertyMapping vpm3 = new VirtualPropertyMapping();
        vpm3.setExtendedStay(false);
        vpm3.setPhysicalPropertyCode("VP3");

        when(virtualPropertyMappingService.getMappingsForVirtualProperty(PROPERTY_ID)).thenReturn(List.of(vpm1, vpm2, vpm3));
    }

    @Test
    void testFetchRateCodesForMktSegIdInFromReservationNight() {
        service.crudService = tenantCrudService;
        when(tenantCrudService.findByNamedQuery(ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN, Map.of("marketSegIds", List.of(1, 2, 3)))).thenReturn(List.of("RC1", "RC2", "RC3", "RC4"));
        List<String> result = service.fetchRateCodesForMktSegIdInFromReservationNight(List.of(1, 2, 3));
        assertEquals(List.of("RC1", "RC2", "RC3", "RC4"), result);
    }

    @Test
    void testSplitAmsForIpp() {
        AnalyticalMarketSegmentService spyService = spy(service);
        Property p = new Property();
        p.setVirtualProperty(false);
        when(propertyServiceLocal.getPropertyById(PROPERTY_ID)).thenReturn(p);
        doNothing().when(spyService).splitAmsBasedOnIndependentProducts();
        spyService.splitAmsForIpp(PROPERTY_ID);
        verify(spyService, times(0)).splitAmsBasedOnIndependentProductsForVirtualProperty(PROPERTY_ID);
        verify(spyService, times(1)).splitAmsBasedOnIndependentProducts();
    }

    @Test
    void testSplitAmsForIppForVirtualProperty() {
        AnalyticalMarketSegmentService spyService = spy(service);
        Property p = new Property();
        p.setVirtualProperty(true);
        when(propertyServiceLocal.getPropertyById(PROPERTY_ID)).thenReturn(p);
        doNothing().when(spyService).splitAmsBasedOnIndependentProductsForVirtualProperty(PROPERTY_ID);
        spyService.splitAmsForIpp(PROPERTY_ID);
        verify(spyService, times(1)).splitAmsBasedOnIndependentProductsForVirtualProperty(PROPERTY_ID);
        verify(spyService, times(0)).splitAmsBasedOnIndependentProducts();
    }

    @Test
    void testSetForecastActivityTypeForAms() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegment ams = createAms("TIER", null, "TIER", RateCodeTypeEnum.ALL, 1, EQUAL_TO_BAR);
        MktSegDetails msd = new MktSegDetails();
        msd.setForecastActivityType(ForecastActivityType.WASH_FAT);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "TIER").parameters())).thenReturn(msd);

        service.setForecastActivityTypeForAms("TIER", ams);

        assertEquals(ForecastActivityType.WASH_FAT, ams.getForecastActivityType());
    }

    @Test
    void testSetForecastActivityTypeForAmsWhenMktSegDetailsNotPresent() {
        service.crudService = tenantCrudService;
        AnalyticalMarketSegment ams = createAms("TIER", null, "TIER", RateCodeTypeEnum.ALL, 1, EQUAL_TO_BAR);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "TIER").parameters())).thenReturn(null);

        service.setForecastActivityTypeForAms("TIER", ams);

        assertEquals(ForecastActivityType.DEMAND_AND_WASH_FAT, ams.getForecastActivityType());
    }

    @Test
    void testAssignTierAmsForIpp() {
        AnalyticalMarketSegmentService spyService = spy(service);
        Property p = new Property();
        p.setVirtualProperty(false);
        when(propertyServiceLocal.getPropertyById(PROPERTY_ID)).thenReturn(p);
        spyService.assignTiersToIpp(PROPERTY_ID);
        verify(spyService, times(0)).getESPropertyCodesFromVirtualProperty(PROPERTY_ID);
        verify(spyService, times(1)).updateTierMarketSegments();
        verify(spyService, times(0)).updateTierMarketSegmentsForVP(any());
    }

    @Test
    void testAssignTierAmsForIppForVirtualProperty() {
        AnalyticalMarketSegmentService spyService = spy(service);
        Property p = new Property();
        p.setVirtualProperty(true);
        VirtualPropertyMapping vpm1 = createVpm("ABC");
        VirtualPropertyMapping vpm2 = createVpm("DEF");
        when(propertyServiceLocal.getPropertyById(PROPERTY_ID)).thenReturn(p);
        when(service.virtualPropertyMappingService.getMappingsForVirtualProperty(PROPERTY_ID))
                .thenReturn(List.of(vpm1, vpm2));
        spyService.assignTiersToIpp(PROPERTY_ID);
        verify(spyService, times(1)).updateTierMarketSegmentsForVP(vpm1);
        verify(spyService, times(1)).updateTierMarketSegmentsForVP(vpm2);
        verify(spyService, times(0)).updateTierMarketSegments();
    }

    private VirtualPropertyMapping createVpm(String propertyCode) {
        VirtualPropertyMapping vpm = new VirtualPropertyMapping();
        vpm.setPhysicalPropertyCode(propertyCode);
        vpm.setExtendedStay(true);
        return vpm;
    }

    @Test
    public void save() {
        MktSeg mktSeg = new MktSeg();
        when(marketSegmentService.findByCodeAndProperty(any(),any())).thenReturn(mktSeg);
        service.saveNonAMS(mktSeg,null, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR,1);
        ArgumentCaptor<List> captor = ArgumentCaptor.forClass(List.class);
        verify(marketSegmentService).persistMktSegDetails(captor.capture());
        List allValues = captor.getAllValues().get(0);
        assertEquals(1, allValues.size());
        assertNull(mktSeg.getMktSegDetailsProposed().getId());
        assertEquals(1, mktSeg.getMktSegDetailsProposed().getPriceByBar());
    }

    @Test
    public void saveMarketSegmentWithIndependentProduct() {
        when(marketSegmentService.getMktSegByPropertyId()).thenReturn(new ArrayList<>());
        when(independentProductsService.createNewMapping(any(),any(), Mockito.eq(true))).thenReturn(new MarketSegmentProductMapping());
        MktSeg mktSeg = new MktSeg();
        mktSeg.setName("mkt");
        mktSeg.setCode("mkt");
        Product product = new Product();
        product.setSystemDefault(false);
        product.setCode(INDEPENDENT_PRODUCT_CODE);
        product.setName("IP1");
        service.saveNonAMS(mktSeg,"IP1", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, 1);
        ArgumentCaptor<List> captor = ArgumentCaptor.forClass(List.class);
        verify(marketSegmentService).persistMktSegDetails(captor.capture());
        List allValues = captor.getAllValues().get(0);
        assertEquals(1, allValues.size());
        assertNull(mktSeg.getMktSegDetailsProposed().getId());
        assertEquals(1, mktSeg.getMktSegDetailsProposed().getPriceByBar());
    }

    @Test
    public void saveMarketSegmentWithNewIndependentProduct() {
        Product product = new Product();
        product.setSystemDefault(false);
        product.setCode(INDEPENDENT_PRODUCT_CODE);
        product.setId(10);
        product.setName("IP1");
        String mktName = "mktName";
        MktSeg mktSeg = new MktSeg();
        mktSeg.setName(mktName);
        mktSeg.setCode(mktName);

        MarketSegmentProductMapping productMapping = new MarketSegmentProductMapping();
        productMapping.setProduct(product);
        productMapping.setMarketSegmentCode(mktName);

        when(marketSegmentService.getMktSegByPropertyId()).thenReturn(new ArrayList<>());
        when(independentProductsService.createProductIfNotPersisted(any())).thenReturn(product);
        when(independentProductsService.createNewMapping(any(),any(), Mockito.eq(true))).thenReturn(productMapping);
        when(pacmanConfigParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        service.saveNonAMS(mktSeg, "IP1",AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, 1);

        ArgumentCaptor<Product> productArgumentCaptor = ArgumentCaptor.forClass(Product.class);
        verify(independentProductsService).createProductIfNotPersisted(productArgumentCaptor.capture());
        Product newIProductSaved = productArgumentCaptor.getValue();
        assertEquals(INDEPENDENT_PRODUCT_CODE, newIProductSaved.getCode());

        ArgumentCaptor<Set<MarketSegmentProductMapping>> productMappingsArgCapture = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).saveMappings(productMappingsArgCapture.capture());
        Optional<MarketSegmentProductMapping> mapping = productMappingsArgCapture.getValue().stream().findFirst();
        assertTrue(mapping.isPresent());
        MarketSegmentProductMapping marketSegmentProductMapping = mapping.get();
        assertTrue(marketSegmentProductMapping.isIp());
        assertEquals(product, marketSegmentProductMapping.getProduct());
        assertEquals(mktName, marketSegmentProductMapping.getMarketSegmentCode());
    }

    @Test
    void testSaveMktSegBusinessGroups() {
        MktSeg mkt1 = createMktSeg(MKT_SEG_1_AFFIXED);
        MktSeg mkt2 = createMktSeg(MKT_SEG_2_AFFIXED);
        MktSeg mkt3 = createMktSeg(MKT_SEG_2_AFFIXED);
        BusinessGroup bg1 = createBusinessGroup("BG1");
        Set<MktSegBusinessGroup> msbgSet = new HashSet<>();
        MktSegBusinessGroup msbg = new MktSegBusinessGroup();
        msbg.setMktSeg(mkt3);
        msbg.setBusinessGroup(bg1);
        msbgSet.add(msbg);
        bg1.setMktSegBusinessGroups(msbgSet);
        Map<BusinessGroup, Set<MktSeg>> mktSegBusinessGroupsMap = new HashMap<>();
        mktSegBusinessGroupsMap.put(bg1, Set.of(mkt1, mkt2));
        doNothing().when(businessGroupService).save(any());

        service.saveMktSegBusinessGroups(mktSegBusinessGroupsMap);

        ArgumentCaptor<List<BusinessGroup>> bgCaptor = ArgumentCaptor.forClass(List.class);
        verify(businessGroupService, times(1)).save(bgCaptor.capture());
        assertEquals("BG1", bgCaptor.getValue().get(0).getName());
        assertEquals(3, bgCaptor.getValue().get(0).getMktSegBusinessGroups().size());
        List<String> result = bgCaptor.getValue().get(0).getMktSegBusinessGroups().stream()
                .map(group -> group.getMktSeg().getCode()) // Assuming getMktSeg returns an object that has getCode method
                .collect(Collectors.toList());
        assertTrue(result.contains(mkt1.getCode()));
        assertTrue(result.contains(mkt2.getCode()));
        assertTrue(result.contains(mkt3.getCode()));
    }

    @Test
    void testCreateMktSegBusinessGroup(){
        MktSeg mkt1 = createMktSeg(MKT_SEG_1_AFFIXED);
        MktSeg mkt2 = createMktSeg(MKT_SEG_2_AFFIXED);
        MktSeg mkt3 = createMktSeg(MKT_SEG_3);
        BusinessGroup bg1 = createBusinessGroup("BG1");
        BusinessGroup bg2 = createBusinessGroup("BG2");
        Map<BusinessGroup, Set<MktSeg>> mktSegBusinessGroupsMap = new HashMap<>();

        service.createMktSegBusinessGroup(mktSegBusinessGroupsMap, bg1, mkt1);
        assertEquals(1, mktSegBusinessGroupsMap.size());
        service.createMktSegBusinessGroup(mktSegBusinessGroupsMap, bg2, mkt2);
        assertEquals(2, mktSegBusinessGroupsMap.size());
        service.createMktSegBusinessGroup(mktSegBusinessGroupsMap, bg2, mkt3);
        assertEquals(2, mktSegBusinessGroupsMap.size());
        assertEquals(1, mktSegBusinessGroupsMap.get(bg1).size());
        assertEquals(2, mktSegBusinessGroupsMap.get(bg2).size());
        assertTrue(mktSegBusinessGroupsMap.get(bg1).contains(mkt1));
        assertTrue(mktSegBusinessGroupsMap.get(bg2).containsAll(Set.of(mkt2, mkt3)));
    }

    @Test
    void shouldUpdateCompositionChangeForStraightToSplitMS() {
        String mktSegCode = "TEST";
        // Saving Straight Market Segment in Market Segment Table
        MktSeg mktSeg = insertMktSeg(1, mktSegCode, mktSegCode, "Test market segment");
        AnalyticalMarketSegment defaultAms = createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.DEFAULT, mktSegCode);
        AnalyticalMarketSegment ufAms1 = createAnalyticalMarketSegment(2, "RateCode1", RateCodeTypeEnum.EQUALS,
                mktSegCode, FENCED, 0);
        AnalyticalMarketSegment ufAms2 = createAnalyticalMarketSegment(3, "RateCode2", RateCodeTypeEnum.EQUALS,
                mktSegCode, FENCED, 0);
        crudService.save(List.of(defaultAms, ufAms1, ufAms2)); // Saving Split Market Segment in AMS table

        service.updateDataInAMSCompositionChange(Optional.of(1000), Optional.of(BigDecimalUtil.valueOf(25.0)));

        List<AMSCompositionChange> amsCompositionChangeRecords = crudService.findAll(AMSCompositionChange.class);
        assertEquals(3, amsCompositionChangeRecords.size());
        amsCompositionChangeRecords.forEach(amsCompositionChange -> {
            assertEquals(1000, amsCompositionChange.getAMSRoomsSold());
            assertEquals(BigDecimalUtil.valueOf(25.0), amsCompositionChange.getHotelPercent());
            assertTrue(amsCompositionChange.isApplyToHistory());
            MktSeg mktSegEntry = crudService.find(MktSeg.class, amsCompositionChange.getMarketSegmentId());
            assertEquals(mktSeg.getCode(), mktSegEntry.getCode());
        });
        List<AnalyticalMarketSegment> amsList = crudService.findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.parallelStream()
                .map(AnalyticalMarketSegment::getId)
                .collect(Collectors.toList())
                .containsAll(amsCompositionChangeRecords.parallelStream()
                        .map(AMSCompositionChange::getAMSId)
                        .collect(Collectors.toList())));
    }

    @Test
    void shouldUpdateCompositionChangeForSplitToStraightMS() {
        MktSeg mktSegDEF = createMktSeg("TEST_DEF");
        MktSeg mktSegUF1 = createMktSeg("TEST_UF");
        List<MktSeg> mktSegList = List.of(mktSegDEF, mktSegUF1);
        crudService.save(mktSegList); // Saving Split Market Segment in Market Segment table
        String mktSegCode = "TEST";
        AnalyticalMarketSegment defaultAms = createAnalyticalMarketSegment(1, null, RateCodeTypeEnum.EQUALS, mktSegCode);
        crudService.save(defaultAms); // Saving Straight Market Segment in AMS table

        service.updateDataInAMSCompositionChange(Optional.of(1000), Optional.of(BigDecimalUtil.valueOf(25.0)));

        List<AMSCompositionChange> amsCompositionChangeRecords = crudService.findAll(AMSCompositionChange.class);
        assertEquals(2, amsCompositionChangeRecords.size());
        amsCompositionChangeRecords.forEach(amsCompositionChange -> {
            assertEquals(1000, amsCompositionChange.getAMSRoomsSold());
            assertEquals(BigDecimalUtil.valueOf(25.0), amsCompositionChange.getHotelPercent());
            assertTrue(amsCompositionChange.isApplyToHistory());
            AnalyticalMarketSegment amsEntry = crudService.find(AnalyticalMarketSegment.class, amsCompositionChange.getAMSId());
            assertEquals(defaultAms.getMarketCode(), amsEntry.getMarketCode());
            assertEquals(defaultAms.getMappedMarketCode(), amsEntry.getMappedMarketCode());
        });
        assertTrue(mktSegList.parallelStream()
                .map(MktSeg::getId)
                .collect(Collectors.toList())
                .containsAll(amsCompositionChangeRecords.parallelStream()
                        .map(AMSCompositionChange::getMarketSegmentId)
                        .collect(Collectors.toList())));
    }

    private BusinessGroup createBusinessGroup(String groupName) {
        BusinessGroup bg = new BusinessGroup();
        bg.setName(groupName);
        return bg;
    }

    private MktSegBusinessGroup createMktSegBusinessGroup(MktSeg mkt1, BusinessGroup bg) {
        MktSegBusinessGroup msbg = new MktSegBusinessGroup();
        msbg.setMktSeg(mkt1);
        msbg.setBusinessGroup(bg);
        return msbg;
    }

    private void runAssertionsForMktSegMasterAndMktSegDetailsProposed() {
        List<MarketSegmentMaster> mktSegMasterList = crudService.findByNamedQuery(MarketSegmentMaster.FIND_ALL);
        assertEquals(12, mktSegMasterList.size());
        List<MktSegDetailsProposed> detailsProposedList = crudService.findByNamedQuery(MktSegDetailsProposed.BY_PROPERTYID, QueryParameter.with("propertyid", 5).parameters());
        assertEquals(12, detailsProposedList.size());
    }

    private void runAssertionsForMktSegMasterAndMktSegDetailsProposedForVP() {
        List<MarketSegmentMaster> mktSegMasterList = crudService.findByNamedQuery(MarketSegmentMaster.FIND_ALL);
        assertEquals(24, mktSegMasterList.size());
        List<MktSegDetailsProposed> detailsProposedList = crudService.findByNamedQuery(MktSegDetailsProposed.BY_PROPERTYID, QueryParameter.with("propertyid", 5).parameters());
        assertEquals(24, detailsProposedList.size());
    }

    private void runAssertionsForAnalyticalMktSeg() {
        List<AnalyticalMarketSegment> amsList = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);
        assertEquals(29, amsList.size());
        List<String> mappedCodeList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES);
        assertEquals(25, mappedCodeList.size());
        List<String> mktCodeList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MARKET_CODES);
        assertEquals(15, mktCodeList.size());
        List<String> amsListAgainstMktCode = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE,
                QueryParameter.with("marketCode", "LNR").parameters());
        assertEquals(4, amsListAgainstMktCode.size());
        List<AnalyticalMarketSegment> amsListAgainstStraightMktCode = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE,
                QueryParameter.with("marketCode", "SMS_LV1").parameters());
        assertEquals(1, amsListAgainstStraightMktCode.size());
    }

    private void runAssertionsForAnalyticalMktSegForVP() {
        List<AnalyticalMarketSegment> amsList = crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);
        assertEquals(47, amsList.size());
        List<String> mappedCodeList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES);
        assertEquals(37, mappedCodeList.size());
        List<String> mktCodeList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MARKET_CODES);
        assertEquals(18, mktCodeList.size());
        List<String> amsListAgainstMktCode = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE,
                QueryParameter.with("marketCode", "LNR").parameters());
        assertEquals(7, amsListAgainstMktCode.size());
        List<AnalyticalMarketSegment> amsListAgainstStraightMktCode = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE,
                QueryParameter.with("marketCode", "SMS_VP1_LV1").parameters());
        assertEquals(1, amsListAgainstStraightMktCode.size());
    }

    private void runAssertionsForProductRateCode() {
        List<String> prcList = crudService.findByNamedQuery(ProductRateCode.BY_DISTINCT_RATE_CODES);
        assertEquals(6, prcList.size());
        assertTrue(prcList.containsAll(List.of("BA_LV1", "BA_LV2", "BA_LV3", "BC1_LV1", "BC1_LV2", "BC1_LV3")));
        List<ProductRateCode> prc = crudService.findByNamedQuery(ProductRateCode.GET_PRODUCT_RATE_CODES_MATCHING_GIVEN_RATE_CODES,
                QueryParameter.with("rateCodes", List.of("BA_LV3")).parameters());
        assertEquals("LV3", prc.get(0).getProduct().getName());
    }

    private void runAssertionsForProductRateCodeForVP() {
        List<String> prcList = crudService.findByNamedQuery(ProductRateCode.BY_DISTINCT_RATE_CODES);
        assertEquals(12, prcList.size());
        assertTrue(prcList.containsAll(List.of("VP1_BA_VP1_LV1", "VP2_BA_VP2_LV2", "VP1_BC1_VP1_LV1", "VP2_BC1_VP2_LV2")));
        List<ProductRateCode> prc = crudService.findByNamedQuery(ProductRateCode.GET_PRODUCT_RATE_CODES_MATCHING_GIVEN_RATE_CODES,
                QueryParameter.with("rateCodes", List.of("VP1_BA_VP1_LV3")).parameters());
        assertEquals("VP1_LV3", prc.get(0).getProduct().getName());
    }

    private void runAssertionsForMktSegProductMapping() {
        List<String> result = crudService.findByNamedQuery(MarketSegmentProductMapping.GET_DISTINCT_MARKET_CODES);
        assertEquals(12, result.size());
        assertTrue(result.containsAll(List.of("IT_LV1_DEF", "IT_LV2_DEF", "IT_LV3_DEF", "SMS_LV1", "LNR_LV1_QYL")));
    }

    private void runAssertionsForMktSegProductMappingForVP() {
        List<String> result = crudService.findByNamedQuery(MarketSegmentProductMapping.GET_DISTINCT_MARKET_CODES);
        assertEquals(24, result.size());
    }

    private List<Product> mockIndependentProducts(){
        List<Product> products = crudService.findByNamedQuery(Product.GET_BY_TYPE, QueryParameter.with("type", ProductType.EXTENDEDSTAY.getTypeDescription()).parameters());
        IntStream.range(0, products.size())
                .forEach(i -> {
                    Product product = products.get(i);
                    product.setName("LV" + (i + 1));
                    product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
                    product.setType(Product.INDEPENDENT_PRODUCT_TYPE);
                    product.setStatus(TenantStatusEnum.ACTIVE);
                });
        return (List<Product>) crudService.save(products);
    }

    private void mockIndependentProductsForVirtualProperty() {
        List<Product> agileProducts = crudService.findByNamedQuery(Product.GET_BY_TYPE, QueryParameter.with("type", ProductType.EXTENDEDSTAY.getTypeDescription()).parameters());
        ProductCode pc = new ProductCode();
        pc.setId(1);
        Product p = agileProducts.get(0);
        List<Product> products = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            for (int j = 1; j <= 3; j++) {
                Product product = new Product(p);
                product.setName("VP" + i + "_LV" + j);
                product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
                product.setType(Product.INDEPENDENT_PRODUCT_TYPE);
                product.setStatus(TenantStatusEnum.ACTIVE);
                product.setProductCode(pc);
                products.add(product);
            }
        }
        crudService.save(products);
    }


    private void mockMktSeg() {
        MktSeg ms1 = crudService.save(createMktSeg(BAR));
        MktSegDetails msd1 = createMktSegDetails(ms1, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 1, YieldType.YIELDABLE, 0);

        MktSeg ms2 = crudService.save(createMktSeg(TIER_1));
        MktSegDetails msd2 = createMktSegDetails(ms2, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 1, 0, YieldType.YIELDABLE, 0);


        MktSeg ms3 = crudService.save(createMktSeg(TIER_2));
        MktSegDetails msd3 = createMktSegDetails(ms3, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 1, 0, YieldType.YIELDABLE, 0);

        MktSeg ms4 = crudService.save(createMktSeg(TIER_3));
        MktSegDetails msd4 = createMktSegDetails(ms4, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 1, 0, YieldType.YIELDABLE, 0);

        MktSeg ms5 = crudService.save(createMktSeg(LOYALTY));
        MktSegDetails msd5 = createMktSegDetails(ms5, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 1, YieldType.SEMI_YIELDABLE, 0);

        MktSeg ms6 = crudService.save(createMktSeg(GROUP_MS));
        MktSegDetails msd6 = createMktSegDetails(ms6, BusinessType.GROUP, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 1, YieldType.YIELDABLE, 0);

        MktSeg ms7 = crudService.save(createMktSeg(STRAIGHT_MS));
        MktSegDetails msd7 = createMktSegDetails(ms7, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 0, YieldType.YIELDABLE, 0);

        MktSeg ms8 = crudService.save(createMktSeg(TRANSIENT_BLOCK));
        MktSegDetails msd8 = createMktSegDetails(ms8, BusinessType.TRANSIENT, 100, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 1, YieldType.YIELDABLE, 0);

        MktSeg ms9 = crudService.save(createMktSeg(DEFAULT_MS_AFFIXED));
        MktSegDetails msd9 = createMktSegDetails(ms9, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 1, 0, YieldType.YIELDABLE, 0);

        MktSeg ms10 = crudService.save(createMktSeg(DEF_FUT_MS_AFFIXED));
        MktSegDetails msd10 = createMktSegDetails(ms10, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 1, 0, YieldType.YIELDABLE, 0);

        MktSeg ms11 = crudService.save(createMktSeg(MKT_SEG_1_AFFIXED));
        MktSegDetails msd11 = createMktSegDetails(ms11, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 1, 0, 1, YieldType.YIELDABLE, 0);

        MktSeg ms12 = crudService.save(createMktSeg(MKT_SEG_2_AFFIXED));
        MktSegDetails msd12 = createMktSegDetails(ms12, BusinessType.TRANSIENT, 0, ForecastActivityType.DEMAND_AND_WASH_FAT, 0, 0, 1, YieldType.YIELDABLE, 0);

        MktSeg ms13 = crudService.save(createMktSeg(MKT_SEG_3));
        MktSegDetails msd13 = createMktSegDetails(ms13, BusinessType.TRANSIENT, 0, ForecastActivityType.NONE_FAT, 0, 0, 1, YieldType.NON_YIELDABLE, 0);

        crudService.save(List.of(msd1, msd2, msd3, msd4, msd5, msd6, msd7, msd8, msd9, msd10, msd11, msd12, msd13));
    }

    private MktSegDetails createMktSegDetails(MktSeg mktSeg, BusinessType bt, Integer bookingpc, ForecastActivityType fat, Integer link, Integer fenced, Integer qualified, YieldType yieldType, Integer pckg) {
        MktSegDetails msd = new MktSegDetails();
        ProcessStatus ps = new ProcessStatus();
        ps.setId(13);
        msd.setMktSeg(mktSeg);
        msd.setBusinessType(bt);
        msd.setBookingBlockPc(bookingpc);
        msd.setForecastActivityType(fat);
        msd.setLink(link);
        msd.setFenced(fenced);
        msd.setQualified(qualified);
        msd.setYieldType(yieldType);
        msd.setPackageValue(pckg);
        msd.setStatusId(Constants.ACTIVE_STATUS_ID);
        msd.setProcessStatus(ps);
        msd.setTemplateDefault(0);
        return msd;
    }

    private void mockAms() {
        AnalyticalMarketSegment ams1 = createAms(BAR, null, BAR, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams2 = createAms(TIER_1, null, TIER_1, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams3 = createAms(TIER_2, null, TIER_2, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams4 = createAms(TIER_3, null, TIER_3, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams5 = createAms(LOYALTY, null, LOYALTY, RateCodeTypeEnum.ALL, 1, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams6 = createAms(GROUP_MS, null, GROUP_MS, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams7 = createAms(STRAIGHT_MS, null, STRAIGHT_MS, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams8 = createAms(TRANSIENT_BLOCK, null, TRANSIENT_BLOCK, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams9 = createAms(DEF_FUT_MS, null, DEFAULT_MS_AFFIXED, RateCodeTypeEnum.DEFAULT, 999, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams10 = createAms(DEF_FUT_MS, null, DEF_FUT_MS_AFFIXED, RateCodeTypeEnum.DEFAULT, 999, FENCED);
        AnalyticalMarketSegment ams11 = createAms(DEF_FUT_MS, RATE_CODE_1, DEF_FUT_MS_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams12 = createAms(MKT_SEG_1, RATE_CODE_2, MKT_SEG_1_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams13 = createAms(MKT_SEG_2, RATE_CODE_3, MKT_SEG_2_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        AnalyticalMarketSegment ams14 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, FENCED);
        crudService.save(List.of(ams1, ams2, ams3, ams4, ams5, ams6, ams7, ams8, ams9, ams10, ams11, ams12, ams13, ams14));
    }

    private void mockAmsForVirtualProperty() {
        AnalyticalMarketSegment ams1 = createAms(BAR, null, BAR, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams2 = createAms(TIER_1, null, TIER_1, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams3 = createAms(TIER_2, null, TIER_2, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams4 = createAms(TIER_3, null, TIER_3, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams5 = createAms(LOYALTY, null, LOYALTY, RateCodeTypeEnum.ALL, 1, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams6 = createAms(GROUP_MS, null, GROUP_MS, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams7 = createAms(STRAIGHT_MS, null, STRAIGHT_MS, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams8 = createAms(TRANSIENT_BLOCK, null, TRANSIENT_BLOCK, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams9 = createAms(DEF_FUT_MS, null, DEFAULT_MS_AFFIXED, RateCodeTypeEnum.DEFAULT, 999, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams10 = createAms(DEF_FUT_MS, null, DEF_FUT_MS_AFFIXED, RateCodeTypeEnum.DEFAULT, 999, FENCED);
        AnalyticalMarketSegment ams11 = createAms(DEF_FUT_MS, "VP1_" + RATE_CODE_1, DEF_FUT_MS_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams12 = createAms(MKT_SEG_1, "VP1_" + RATE_CODE_2, MKT_SEG_1_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams13 = createAms(MKT_SEG_2, "VP1_" + RATE_CODE_3, MKT_SEG_2_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        AnalyticalMarketSegment ams14 = createAms(MKT_SEG_3, null, MKT_SEG_3, RateCodeTypeEnum.ALL, 1, FENCED);
        AnalyticalMarketSegment ams15 = createAms(DEF_FUT_MS, "VP2_" + RATE_CODE_1, DEF_FUT_MS_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        AnalyticalMarketSegment ams16 = createAms(MKT_SEG_1, "VP2_" + RATE_CODE_2, MKT_SEG_1_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        AnalyticalMarketSegment ams17 = createAms(MKT_SEG_2, "VP2_" + RATE_CODE_3, MKT_SEG_2_AFFIXED, RateCodeTypeEnum.EQUALS, 10, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        crudService.save(List.of(ams1, ams2, ams3, ams4, ams5, ams6, ams7, ams8, ams9, ams10, ams11, ams12, ams13, ams14, ams15, ams16, ams17));
    }

    private void truncateAllRelatedTables() {
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_MKT_SEG_MASTER);
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_ANALYTICAL_MKT_SEG);
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_PRODUCT_RATE_CODE);
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_MKT_SEG_PRODUCT_MAPPING);
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_MKT_SEG_DETAILS);
        crudService.executeUpdateByNativeQuery(TRUNCATE_TABLE_MKT_SEG_DETAILS_PROPOSED);
    }

    private AnalyticalMarketSegment createAms(String mktCode, String rateCode, String mappedCode, RateCodeTypeEnum rateCodeType, Integer rank, AnalyticalMarketSegmentAttribute attribute) {
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode(mktCode);
        ams.setRateCode(rateCode);
        ams.setMappedMarketCode(mappedCode);
        ams.setAttribute(attribute);
        ams.setRateCodeType(rateCodeType);
        ams.setRank(rank);
        return ams;
    }
}
