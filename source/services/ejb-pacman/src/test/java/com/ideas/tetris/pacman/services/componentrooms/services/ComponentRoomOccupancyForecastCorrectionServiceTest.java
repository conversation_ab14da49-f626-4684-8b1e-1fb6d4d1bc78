package com.ideas.tetris.pacman.services.componentrooms.services;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.PaceAccomOccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMarketOccupancyForecast;
import com.ideas.tetris.pacman.services.componentrooms.dto.ComponentRoomsConfiguration;
import com.ideas.tetris.pacman.services.componentrooms.dto.OccupancyFcstBatchDto;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

public class ComponentRoomOccupancyForecastCorrectionServiceTest {

    @Mock
    private CrudService tenantCrudService;
    @Mock
    private ComponentRoomService componentRoomService;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @InjectMocks
    private ComponentRoomOccupancyForecastCorrectionService componentRoomOccupancyForecastCorrectionService;

    private Date startDate = LocalDate.now().toDate();
    private Date endDate = LocalDate.now().toDate();
    private List<ComponentRoomsConfiguration> componentRoomsConfigurations = Arrays.asList(new ComponentRoomsConfiguration());
    private Random random = new Random();
    private double DELTA = 0.01;
    private Integer someDecisionId;
    private List<Integer> accomTypeIdsInvolved = new ArrayList<>();
    private Integer propertyId;
    private List minMaxDatesList = new ArrayList<>();

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        minMaxDatesList = new ArrayList<>();
        Object[] minMaxDate = new Object[2];
        minMaxDate[0] = startDate;
        minMaxDate[1] = endDate;
        minMaxDatesList.add(minMaxDate);
        someDecisionId = random.nextInt();
        when(tenantCrudService.findByNamedQuerySingleResult(OccupancyForecast.MAX_DECISION)).thenReturn(someDecisionId);
        Map<String, Object> parameters = QueryParameter.with("decisionID", someDecisionId).parameters();
        when(tenantCrudService.findByNamedQuery(OccupancyForecast.MIN_AND_MAX_OCCUPANCY_DATE_FOR_DECISION, parameters)).thenReturn(minMaxDatesList);
        when(componentRoomService.getComponentRoomsConfigurations()).thenReturn(componentRoomsConfigurations);
        when(componentRoomService.getAccomTypeIdsInvolved()).thenReturn(accomTypeIdsInvolved);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(true);
        //System.setProperty("use.batch.update.for.cr.occupancy.fcst.correction", "false");
    }

    @Test
    public void shouldDoNothingWhenMaxDecisionIdNotPresent() throws Exception {
        when(tenantCrudService.findByNamedQuerySingleResult(OccupancyForecast.MAX_DECISION)).thenReturn(null);
        verify(componentRoomService, never()).getComponentRoomsConfigurations();
    }

    @Disabled
    @Test
    public void shouldInvokeMethodsToFetchAllOccupancyForecastsForDateRange() throws Exception {
        componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startDate, endDate);
        verify(tenantCrudService).findByNamedQuery(OccupancyForecast.FOR_DATE_RANGE_ACCOMTYPE_IDS, QueryParameter
                .with("startDate", startDate).and("endDate", endDate).and("propertyId", propertyId).and("accomTypeIds", accomTypeIdsInvolved).parameters());
    }

    @Disabled
    @Test
    public void shouldSplitOccupancyForecastRevenueForOneCROnePhysicalTwoQuantities() throws Exception {
        Double componentRevenue = random.nextDouble();
        Double physicalRevenue = random.nextDouble();
        Double physicalProfit = random.nextDouble();
        Double componentProfit = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithRevenue(componentRevenue, componentProfit, occupancyDate);
        OccupancyForecast occupancyForecastP = createOccupancyForecastWithRevenue(physicalRevenue, physicalProfit, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP)), Arrays.asList(Arrays.asList(2)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue + physicalRevenue, occupancyForecastP.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit + physicalProfit, occupancyForecastP.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldNotSplitProfitIfToggleIsOff() throws Exception {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);
        Double componentRevenue = random.nextDouble();
        Double physicalRevenue = random.nextDouble();
        Double physicalProfit = random.nextDouble();
        Double componentProfit = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithRevenue(componentRevenue, componentProfit, occupancyDate);
        OccupancyForecast occupancyForecastP = createOccupancyForecastWithRevenue(physicalRevenue, physicalProfit, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP)), Arrays.asList(Arrays.asList(2)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue + physicalRevenue, occupancyForecastP.getRevenue().doubleValue(), DELTA);
        assertEquals(physicalProfit, occupancyForecastP.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Test
    void occupancyForecastCorrectionFull() {
        componentRoomOccupancyForecastCorrectionService = spy(componentRoomOccupancyForecastCorrectionService);
        Object[] dates = (Object[]) minMaxDatesList.get(0);
        componentRoomOccupancyForecastCorrectionService.occupancyForecastCorrectionFull();
        verify(componentRoomOccupancyForecastCorrectionService).processForChunk((Date) dates[0], (Date) dates[1]);
    }

    @Disabled
    @Test
    public void shouldSplitOccupancyForecastSoldsForOneCROnePhysicalTwoQuantities() throws Exception {
        Double componentSolds = random.nextDouble();
        Double physicalSolds = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithOccupancyNumber(componentSolds, occupancyDate);
        OccupancyForecast occupancyForecastP = createOccupancyForecastWithOccupancyNumber(physicalSolds, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP)), Arrays.asList(Arrays.asList(2)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds * 2 + physicalSolds, occupancyForecastP.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenOneCRWithTwoPhysicalOneQuantity() throws Exception {
        Double componentRevenue = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double componentProfit = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithRevenue(componentRevenue, componentProfit, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(1, 1)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue / 2 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue / 2 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit / 2 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit / 2 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenOneCRWithTwoPhysicalOneQuantity() throws Exception {
        Double componentSolds = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithOccupancyNumber(componentSolds, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(1, 1)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenOneCRWithTwoPhysicalTwoQuantities() throws Exception {
        Double componentRevenue = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double componentProfit = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithRevenue(componentRevenue, componentProfit, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(2, 2)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue / 2 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue / 2 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit / 2 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit / 2 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenOneCRWithTwoPhysicalTwoQuantities() throws Exception {
        Double componentSolds = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithOccupancyNumber(componentSolds, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(2, 2)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds * 2 + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds * 2 + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenOneCRWithTwoPhysicalDifferentQuantities() throws Exception {
        Double componentRevenue = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double componentProfit = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithRevenue(componentRevenue, componentProfit, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(1, 3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue * 0.25 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue * 0.75 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit * 0.25 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit * 0.75 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenOneCRWithTwoPhysicalDifferentQuantities() throws Exception {
        Double componentSolds = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC = createOccupancyForecastWithOccupancyNumber(componentSolds, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate);
        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC), Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2)), Arrays.asList(Arrays.asList(1, 3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC, occupancyForecastP1, occupancyForecastP2), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds * 3 + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenTwoDifferentCRWithPhysicalAndDifferentQuantities() throws Exception {
        Double componentRevenue1 = random.nextDouble();
        Double componentRevenue2 = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double physicalRevenue3 = random.nextDouble();
        Double componentProfit1 = random.nextDouble();
        Double componentProfit2 = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Double physicalProfit3 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithRevenue(componentRevenue1, componentProfit1, occupancyDate);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithRevenue(componentRevenue2, componentProfit2, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithRevenue(physicalRevenue3, physicalProfit3, occupancyDate);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP3)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue1 * 0.33 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue1 * 0.66 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue2 + physicalRevenue3, occupancyForecastP3.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.33 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.66 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit2 + physicalProfit3, occupancyForecastP3.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenTwoDifferentCRWithPhysicalAndDifferentQuantities() throws Exception {
        Double componentSolds1 = random.nextDouble();
        Double componentSolds2 = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Double physicalSolds3 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithOccupancyNumber(componentSolds1, occupancyDate);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithOccupancyNumber(componentSolds2, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithOccupancyNumber(physicalSolds3, occupancyDate);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP3)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds1 + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds1 * 2 + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds2 * 3 + physicalSolds3, occupancyForecastP3.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenTwoDifferentCRSharingCommonCR() throws Exception {
        Double componentRevenue1 = random.nextDouble();
        Double componentRevenue2 = random.nextDouble();
        Double componentRevenue3 = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double physicalRevenue3 = random.nextDouble();
        Double physicalRevenue4 = random.nextDouble();
        Double componentProfit1 = random.nextDouble();
        Double componentProfit2 = random.nextDouble();
        Double componentProfit3 = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Double physicalProfit3 = random.nextDouble();
        Double physicalProfit4 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithRevenue(componentRevenue1, componentProfit1, occupancyDate);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithRevenue(componentRevenue2, componentProfit2, occupancyDate);
        OccupancyForecast occupancyForecastC3 = createOccupancyForecastWithRevenue(componentRevenue3, componentProfit3, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithRevenue(physicalRevenue3, physicalProfit3, occupancyDate);
        OccupancyForecast occupancyForecastP4 = createOccupancyForecastWithRevenue(physicalRevenue4, physicalProfit4, occupancyDate);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastC3),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), Arrays.asList(occupancyForecastP1, occupancyForecastP2, occupancyForecastP4)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(1, 2, 2), Arrays.asList(2, 4, 1)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastC3, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3, occupancyForecastP4), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue1 * 0.33 + componentRevenue2 * 0.2 + componentRevenue3 * 0.29 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue1 * 0.66 + componentRevenue2 * 0.4 + componentRevenue3 * 0.57 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue2 * 0.4 + physicalRevenue3, occupancyForecastP3.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue3 * 0.15 + physicalRevenue4, occupancyForecastP4.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.33 + componentProfit2 * 0.2 + componentProfit3 * 0.29 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.66 + componentProfit2 * 0.4 + componentProfit3 * 0.57 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit2 * 0.4 + physicalProfit3, occupancyForecastP3.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit3 * 0.15 + physicalProfit4, occupancyForecastP4.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenTwoDifferentCRSharingCommonCR() throws Exception {
        Double componentSolds1 = random.nextDouble();
        Double componentSolds2 = random.nextDouble();
        Double componentSolds3 = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Double physicalSolds3 = random.nextDouble();
        Double physicalSolds4 = random.nextDouble();
        Date occupancyDate = new Date();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithOccupancyNumber(componentSolds1, occupancyDate);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithOccupancyNumber(componentSolds2, occupancyDate);
        OccupancyForecast occupancyForecastC3 = createOccupancyForecastWithOccupancyNumber(componentSolds3, occupancyDate);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithOccupancyNumber(physicalSolds3, occupancyDate);
        OccupancyForecast occupancyForecastP4 = createOccupancyForecastWithOccupancyNumber(physicalSolds4, occupancyDate);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastC3),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), Arrays.asList(occupancyForecastP1, occupancyForecastP2, occupancyForecastP4)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(1, 2, 2), Arrays.asList(2, 4, 1)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastC3, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3, occupancyForecastP4), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds1 + componentSolds2 + componentSolds3 * 2 + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds1 * 2 + componentSolds2 * 2 + componentSolds3 * 4 + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds2 * 2 + physicalSolds3, occupancyForecastP3.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds3 + physicalSolds4, occupancyForecastP4.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitRevenueWhenTwoDifferentCRWithPhysicalAndDifferentQuantitiesAtDifferentOccupancyDates() throws Exception {
        Double componentRevenue1 = random.nextDouble();
        Double componentRevenue2 = random.nextDouble();
        Double physicalRevenue1 = random.nextDouble();
        Double physicalRevenue2 = random.nextDouble();
        Double physicalRevenue3 = random.nextDouble();
        Double componentProfit1 = random.nextDouble();
        Double componentProfit2 = random.nextDouble();
        Double physicalProfit1 = random.nextDouble();
        Double physicalProfit2 = random.nextDouble();
        Double physicalProfit3 = random.nextDouble();
        Date occupancyDate1 = LocalDate.now().toDate();
        Date occupancyDate2 = LocalDate.now().plusDays(1).toDate();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithRevenue(componentRevenue1, componentProfit1, occupancyDate1);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithRevenue(componentRevenue2, componentProfit2, occupancyDate2);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithRevenue(physicalRevenue1, physicalProfit1, occupancyDate1);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithRevenue(physicalRevenue2, physicalProfit2, occupancyDate1);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithRevenue(physicalRevenue3, physicalProfit3, occupancyDate2);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP3)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentRevenue1 * 0.33 + physicalRevenue1, occupancyForecastP1.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue1 * 0.66 + physicalRevenue2, occupancyForecastP2.getRevenue().doubleValue(), DELTA);
        assertEquals(componentRevenue2 + physicalRevenue3, occupancyForecastP3.getRevenue().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.33 + physicalProfit1, occupancyForecastP1.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit1 * 0.66 + physicalProfit2, occupancyForecastP2.getProfit().doubleValue(), DELTA);
        assertEquals(componentProfit2 + physicalProfit3, occupancyForecastP3.getProfit().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Disabled
    @Test
    public void shouldSplitSoldsWhenTwoDifferentCRWithPhysicalAndDifferentQuantitiesAtDifferentOccupancyDates() throws Exception {
        Double componentSolds1 = random.nextDouble();
        Double componentSolds2 = random.nextDouble();
        Double physicalSolds1 = random.nextDouble();
        Double physicalSolds2 = random.nextDouble();
        Double physicalSolds3 = random.nextDouble();
        Date occupancyDate1 = LocalDate.now().toDate();
        Date occupancyDate2 = LocalDate.now().plusDays(1).toDate();
        OccupancyForecast occupancyForecastC1 = createOccupancyForecastWithOccupancyNumber(componentSolds1, occupancyDate1);
        OccupancyForecast occupancyForecastC2 = createOccupancyForecastWithOccupancyNumber(componentSolds2, occupancyDate2);
        OccupancyForecast occupancyForecastP1 = createOccupancyForecastWithOccupancyNumber(physicalSolds1, occupancyDate1);
        OccupancyForecast occupancyForecastP2 = createOccupancyForecastWithOccupancyNumber(physicalSolds2, occupancyDate1);
        OccupancyForecast occupancyForecastP3 = createOccupancyForecastWithOccupancyNumber(physicalSolds3, occupancyDate2);

        createAndMockComponentRoomMultiplierForAccomActivity(Arrays.asList(occupancyForecastC1, occupancyForecastC2),
                Arrays.asList(Arrays.asList(occupancyForecastP1, occupancyForecastP2), Arrays.asList(occupancyForecastP3)),
                Arrays.asList(Arrays.asList(1, 2), Arrays.asList(3)));
        mockOccupancyForecastList(Arrays.asList(occupancyForecastC1, occupancyForecastC2, occupancyForecastP1, occupancyForecastP2, occupancyForecastP3), accomTypeIdsInvolved);

        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));

        assertEquals(componentSolds1 + physicalSolds1, occupancyForecastP1.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds1 * 2 + physicalSolds2, occupancyForecastP2.getOccupancyNumber().doubleValue(), DELTA);
        assertEquals(componentSolds2 * 3 + physicalSolds3, occupancyForecastP3.getOccupancyNumber().doubleValue(), DELTA);
        verify(tenantCrudService).execute(eq(OccupancyFcstBatchDto.USP_OCCUPANCY_FCST_MERGE), anyList());
    }

    @Test
    public void shouldInvokeMethodsToAddPaceRecords() throws Exception {
        componentRoomOccupancyForecastCorrectionService.populatePaceTables();
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(eq(PaceMarketOccupancyForecast.UPDATE_MKT_PACE_FORECAST), any());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(eq(PaceAccomOccupancyForecast.UPDATE_ACCOM_PACE_FORECAST), any());
    }

    @Test
    public void shouldNotInvokeMethodsToAddPaceRecords() throws Exception {
        Map<String, Date> startEndDates = componentRoomOccupancyForecastCorrectionService.getOccupancyForecastsMappedByOccupancyDate();
        componentRoomOccupancyForecastCorrectionService.processForChunk(startEndDates.get(Constants.START_DATE), startEndDates.get(Constants.END_DATE));
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(eq(PaceMarketOccupancyForecast.UPDATE_MKT_PACE_FORECAST), any());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(eq(PaceAccomOccupancyForecast.UPDATE_ACCOM_PACE_FORECAST), any());
    }

    private void mockOccupancyForecastList(List occupancyForecasts, List<Integer> accomTypeIdsInvolved) {
        when(tenantCrudService.findByNamedQuery(OccupancyForecast.FOR_DATE_RANGE_ACCOMTYPE_IDS, QueryParameter.
                with("startDate", startDate).and("endDate", endDate).and("propertyId", propertyId).and("accomTypeIds", accomTypeIdsInvolved).parameters())).thenReturn(occupancyForecasts);
    }

    private OccupancyForecast createOccupancyForecastWithRevenue(Double revenue, Double profit, Date occupancyDate) {
        OccupancyForecast occupancyForecast = new OccupancyForecast();
        occupancyForecast.setAccomTypeID(random.nextInt());
        occupancyForecast.setRevenue(BigDecimal.valueOf(revenue));
        occupancyForecast.setOccupancyNumber(BigDecimal.valueOf(random.nextDouble()));
        occupancyForecast.setProfit(BigDecimal.valueOf(profit));
        occupancyForecast.setOccupancyDate(occupancyDate);
        return occupancyForecast;
    }

    private OccupancyForecast createOccupancyForecastWithOccupancyNumber(Double occupancyNumber, Date occupancyDate) {
        OccupancyForecast occupancyForecast = new OccupancyForecast();
        occupancyForecast.setAccomTypeID(random.nextInt());
        occupancyForecast.setRevenue(BigDecimal.valueOf(random.nextDouble()));
        occupancyForecast.setOccupancyNumber(BigDecimal.valueOf(occupancyNumber));
        occupancyForecast.setProfit(BigDecimal.valueOf(random.nextDouble()));
        occupancyForecast.setOccupancyDate(occupancyDate);
        return occupancyForecast;
    }

    private void createAndMockComponentRoomMultiplierForAccomActivity(List<OccupancyForecast> components, List<List<OccupancyForecast>> physicalLists, List<List<Integer>> physicalCounts) {
        Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers = new HashMap<>();
        for (int i = 0; i < components.size(); i++) {
            OccupancyForecast component = components.get(i);

            List<OccupancyForecast> physicalList = physicalLists.get(i);
            List<Integer> physicalCount = physicalCounts.get(i);
            Map<Integer, Integer> pCounts = new HashMap<>();
            for (int j = 0; j < physicalList.size(); j++) {
                pCounts.put(physicalList.get(j).getAccomTypeID(), physicalCount.get(j));
            }

            ComponentRoomMultiplier componentRoomMultiplier = new ComponentRoomMultiplier(pCounts);
            componentRoomMultipliers.put(component.getAccomTypeID(), componentRoomMultiplier);

        }
        when(componentRoomService.computeComponentRoomMultiplier(componentRoomsConfigurations)).thenReturn(componentRoomMultipliers);
    }

    @Test
    public void getAccomTypeIdToOccupancyForecastMapTest() {
        List<OccupancyForecast> list = getListOfOccupancyForecasatForSingleOccupancyDate();
        Map<Integer, List<OccupancyForecast>> map =
                componentRoomOccupancyForecastCorrectionService.getAccomTypeIdToOccupancyForecastMap(list);
        assertEquals(3, map.size());
        assertEquals(2, map.get(1).size());
        assertEquals(1, map.get(2).size());
        assertEquals(1, map.get(3).size());
        List<OccupancyForecast> retList = map.get(1);
        assertTrue(retList.get(0).getMarketSegmentID() == 1);
        assertTrue(retList.get(1).getMarketSegmentID() == 4);
    }

    private List<OccupancyForecast> getListOfOccupancyForecasatForSingleOccupancyDate() {
        List<OccupancyForecast> list = new ArrayList<>();
        list.add(getOccupancyForecast(new BigInteger("1"), 1, 1, new Date(117, 7, 9)));
        list.add(getOccupancyForecast(new BigInteger("2"), 2, 2, new Date(117, 7, 9)));
        list.add(getOccupancyForecast(new BigInteger("3"), 3, 3, new Date(117, 7, 9)));
        list.add(getOccupancyForecast(new BigInteger("4"), 1, 4, new Date(117, 7, 9)));
        return list;
    }

    private OccupancyForecast getOccupancyForecast(BigInteger id, int accomTypeId, int mktSegId, Date occDate) {
        OccupancyForecast occfcst = new OccupancyForecast();
        occfcst.setId(id);
        occfcst.setAccomTypeID(accomTypeId);
        occfcst.setMarketSegmentID(mktSegId);
        occfcst.setOccupancyDate(occDate);
        return occfcst;

    }
}
