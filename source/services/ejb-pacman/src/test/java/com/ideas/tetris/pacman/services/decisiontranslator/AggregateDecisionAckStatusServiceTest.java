package com.ideas.tetris.pacman.services.decisiontranslator;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.decisiontranslator.entity.AggregateDecisionAckStatus;
import com.ideas.tetris.pacman.services.decisiontranslator.entity.DecisionAckStatus;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class AggregateDecisionAckStatusServiceTest {
    private static final Logger LOGGER = Logger.getLogger(AggregateDecisionAckStatusServiceTest.class);

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private FileMetadataService fileMetadataService;

    @Mock
    protected PacmanConfigParamsService configService;

    @InjectMocks
    private AggregateDecisionAckStatusService aggregateDecisionAckStatusService;

    private Date baseDate;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        baseDate = new Date();
        when(fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class))).thenReturn(getFileMetaData());
    }

    private List<FileMetadata> getFileMetaData() {
        return Arrays.asList(
                getFileMetaData("SandBox_RATVDW_20110227_0312_T2SNAP.psv"),
                getFileMetaData("SandBox_RATVDW_20120287_0312_T2SNAP.psv"));
    }

    @Test
    public void getLatestSuccessfulDate() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(getAggregateDecisionAckStatuses());
        assertEquals(DateUtils.addHours(getThresholdDate(), -7), aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_pendingOlderRecord() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total and pending records and is older than the pending threshold
                        new AggregateDecisionAckStatus("acceptedRecord",
                                DateUtils.addHours(getThresholdDate(), -1),
                                1, 0, 1)));
        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_pendingNewerRecord() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total and pending records and is older than the pending threshold
                        new AggregateDecisionAckStatus("acceptedRecord",
                                DateUtils.addHours(getThresholdDate(), 1),
                                1, 0, 1)));
        assertEquals(DateUtils.addHours(getThresholdDate(), 1), aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_filteredPendingOlderThanPendingThreshold() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total and pending records and is more recent than the pending threshold
                        new AggregateDecisionAckStatus("filteredWithinTwoHours",
                                DateUtils.addHours(getThresholdDate(), -1),
                                1, 0, 1)));
        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_filteredExceedsErrorThreshold() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total and error records and is older than the pending threshold
                        new AggregateDecisionAckStatus("filteredExceedsErrorThreshold",
                                DateUtils.addHours(getThresholdDate(), -1),
                                5, 2, 0)));
        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_noFiles() {
        reset(fileMetadataService);
        when(fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class))).thenReturn(new ArrayList());

        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verify(fileMetadataService).getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class));
        verifyNoMoreInteractions(tenantCrudService);
    }

    @Test
    public void getLatestSuccessfulDate_filteredZeroTotalRecords() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total and error records and is older than the pending threshold
                        new AggregateDecisionAckStatus("filteredZeroTotalRecords",
                                DateUtils.addHours(getThresholdDate(), -1),
                                0, 0, 0)));
        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_acceptedNoPendingOlderThanThreshold() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), -1),
                                1, 0, 0)));
        assertEquals(DateUtils.addHours(getThresholdDate(), -1), aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_acceptedNoPendingNewerThanThreshold() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), 1),
                                1, 0, 0)));
        assertEquals(DateUtils.addHours(getThresholdDate(), 1), aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verifyTenantInteractions();
    }

    @Test
    public void getLatestSuccessfulDate_noResults() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(new ArrayList<>());
        assertNull(aggregateDecisionAckStatusService.getLatestSuccessfulDate());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_noDecisionAckStatuses() {
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(new ArrayList<>());
        assertFalse(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_noFileMetadataRecords() {
        reset(fileMetadataService);
        when(fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class))).thenReturn(new ArrayList<>());

        assertFalse(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verifyNoMoreInteractions(tenantCrudService);
    }

    @Test
    public void shouldContinueEvaluation_latestIsWithinWindowButSuccessful() {
        //within the window nothing has failed or is pending... so successful
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), 1),
                                1, 0, 0)));
        assertFalse(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_latestIsWithinWindowButHasPending() {
        //within the window nothing has failed or is pending... so successful
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), 1),
                                1, 0, 1)));
        assertFalse(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_latestIsBeforeWindowButSuccessful() {
        //within the window nothing has failed or is pending... so successful
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), -1),
                                1, 0, 0)));
        assertFalse(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_latestIsBeforeWindowButStillHasPending() {
        //within the window nothing has failed or is pending... so successful
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), -1),
                                1, 0, 1)));
        assertTrue(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void shouldContinueEvaluation_latestIsBeforeWindowButExceedsErrorAmount() {
        //within the window nothing has failed or is pending... so successful
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(Collections.singletonList(
                        // a record that has total records without error or pending and is newer than the pending threshold
                        new AggregateDecisionAckStatus("acceptedWithinTwoHoursComplete",
                                DateUtils.addHours(getThresholdDate(), -1),
                                2, 1, 0)));
        assertTrue(aggregateDecisionAckStatusService.shouldContinueEvaluation());
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }

    @Test
    public void getRecommendationFileNames() {
        when(fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class))).thenReturn(Arrays.asList(
                getFileMetaData("PopulateMissingSnapshotPacePoint"),
                getFileMetaData("LDB_ADD_MS"),
                getFileMetaData("SandBox_RATVDW_20110227_0312_T2SNAP.psv"),
                getFileMetaData("SandBox_RATVDW_20120287_0312_T2SNAP.psv")));
        List<String> recommendationFileNames = aggregateDecisionAckStatusService.getRecommendationFileNames();
        assertEquals(2, recommendationFileNames.size());
        assertEquals("RATVDW.20110227.031200.recom", recommendationFileNames.get(0));
        assertEquals("RATVDW.20120287.031200.recom", recommendationFileNames.get(1));
    }

    @Test
    public void testContainsErrors_filteredExceedsErrorThreshold() {
        final List<AggregateDecisionAckStatus> decisionAckStatuses = Collections.singletonList(
                // a record that has total and error records and is older than the pending threshold
                new AggregateDecisionAckStatus("filteredExceedsErrorThreshold",
                        DateUtils.addHours(getThresholdDate(), 2),
                        5, 2, 0));
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(decisionAckStatuses);
        assertTrue(aggregateDecisionAckStatusService.containsErrors(decisionAckStatuses, org.joda.time.LocalDateTime.now().minusHours(2).toDate()));
    }

    @Test
    public void testContainsErrors_NoErrorExceedingThreshold() {
        final List<AggregateDecisionAckStatus> decisionAckStatuses = Collections.singletonList(
                // a record that has total and error records and is older than the pending threshold
                new AggregateDecisionAckStatus("filteredExceedsErrorThreshold",
                        DateUtils.addHours(getThresholdDate(), 2),
                        50, 2, 0));
        when(tenantCrudService.<AggregateDecisionAckStatus>findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap()))
                .thenReturn(decisionAckStatuses);
        assertFalse(aggregateDecisionAckStatusService.containsErrors(decisionAckStatuses, org.joda.time.LocalDateTime.now().minusHours(2).toDate()));
    }

    private Date getThresholdDate() {
        return DateUtils.addHours(baseDate, SystemConfig.getHiltonDecisionCarryForwardHoursToFilterPending());
    }

    private List<AggregateDecisionAckStatus> getAggregateDecisionAckStatuses() {
        return Arrays.asList(
                // should be filtered : it has pending and is older than the allowed threshold
                new AggregateDecisionAckStatus("filteredStillPendingOlderThanThreshold",
                        DateUtils.addHours(getThresholdDate(), -1),
                        1, 0, 1),
                // should be filtered : it has no records
                new AggregateDecisionAckStatus("filteredZeroRecords",
                        DateUtils.addHours(getThresholdDate(), -3),
                        0, 0, 0),
                // should be filtered : the error ratio is too high
                new AggregateDecisionAckStatus("filteredBecauseOfErrorPercent",
                        DateUtils.addHours(getThresholdDate(), -6),
                        6, 4, 0),
                // This is the record that should be selected
                new AggregateDecisionAckStatus("firstMatchingRecord",
                        DateUtils.addHours(getThresholdDate(), -7),
                        100, 2, 0),
                // This would be selected if it weren't for the previous record
                new AggregateDecisionAckStatus("secondMatchingRecordButWeFoundTheOneWeWereLookingFor",
                        DateUtils.addHours(getThresholdDate(), -8),
                        80, 0, 0)
        );
    }

    private FileMetadata getFileMetaData(String name) {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setFileName(name);
        return fileMetadata;
    }

    private void verifyTenantInteractions() {
        verify(fileMetadataService).getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(
                eq(ProcessStatus.SUCCESSFUL),
                eq(RecordType.T2SNAP_RECORD_TYPE_ID),
                any(Date.class));
        verify(tenantCrudService).findByNamedQuery(eq(DecisionAckStatus.GET_AGGREGATE_STATS), anyMap());
    }
}