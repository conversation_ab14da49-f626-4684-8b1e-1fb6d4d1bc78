package com.ideas.tetris.pacman.services.mktsegrecoding.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.service.RoomTypeMarketSegmentActivityService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.independentproducts.repository.IndependentProductsRepository;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.YieldCategoryRuleService;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegProductDetailsDTO;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegRecodingConfigDto;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.ProductRateCodeAmsRule;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.RateCodeAssociation;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingBusinessTypeShift;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociation;
import com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState;
import com.ideas.tetris.pacman.services.opera.DataLoadSummaryService;
import com.ideas.tetris.pacman.services.opera.OperaTransactionLoadService;
import com.ideas.tetris.pacman.services.pacebackfill.BackFillService;
import com.ideas.tetris.pacman.services.pmsmigration.alert.PMSMigrationAlertDetailsBuilder;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.IgnoredRateCode;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.NewAMSRule;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.PMSRevampAMSDataService;
import com.ideas.tetris.pacman.services.pmsmigration.dto.PMSMigrationMappingImportDTO;
import com.ideas.tetris.pacman.services.pmsmigration.dto.PMSMigrationRateCodeMktSegMappingDTO;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationMapping;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationMappingService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSRevampNonPaceUpdationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.reservationnight.ReservationNightService;
import com.ideas.tetris.pacman.services.saspopulation.service.OperaPopulationService;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_FAILED_JOB_EXECUTION_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.UNMAPPED_RATE_CODES_ALERT_METADATA;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.CONTAINS;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.ENDS_WITH;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.STARTS_WITH;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_COMMIT_FG_DONE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_COMMIT_FG_REQUIRED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_COMPLETED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_REQUIRED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_READY;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService.MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService.MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService.MKT_SEG_RECODING_NEW_AMS_ATTR_MAPPING_NOT_PROVIDED_ERROR;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService.MKT_SEG_RECODING_NEW_AMS_ATTR_MISSING_ERROR;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_PROPERTY_CODE_PUNE;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_PROPERTY_ID_PUNE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyListOf;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.nullable;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class MktSegRecodingServiceTest extends AbstractG3JupiterTest {

    public static final Long JOB_EXECUTION_ID = 1L;
    public static final String DESCRIPTION = PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + JOB_EXECUTION_ID;
    private final Date caughtUpDate = new Date();
    private final Date dateAfterCaughtUpDate = DateUtil.addDaysToDate(caughtUpDate, 1);
    private final Date dateBeforeCaughtUpDate = DateUtil.addDaysToDate(caughtUpDate, -1);
    private final Integer fileMetadataId = 1;
    private static final int MARKET_SEGMENT_ID = 913;
    private static final String MARKET_SEGMENT_CODE = "CONS";

    @Mock
    OperaPopulationService operaPopulationService;
    @Mock
    OperaTransactionLoadService operaTransactionLoadService;
    private CrudService globalCrudService;
    @Mock
    private PropertyStageChangeService propertyStageChangeService;
    @Mock
    private DateService dateService;
    @Mock
    private JobServiceLocal jobService;
    @Spy
    @InjectMocks
    private MktSegRecodingService service;
    @Mock
    private RestClient restClient;
    @Mock
    private PMSMigrationService pmsMigrationService;
    @Mock
    private PMSRevampAMSDataService pmsRevampAMSDataService;
    @Mock
    private PMSRevampNonPaceUpdationService pmsRevampNonPaceUpdationService;
    @Mock
    private RoomTypeMarketSegmentActivityService roomTypeMarketSegmentActivityService;
    @Mock
    private FileMetadataService fileMetadataService;
    @Mock
    private MarketSegmentComponent marketSegmentComponent;
    @Mock
    private MktSegRecodingBackupService mktSegRecodingBackupService;
    @Mock
    private DataLoadSummaryService dataLoadSummaryService;
    @Mock
    private CrudService tenantCrudService;
    @Mock
    private AlertService alertService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private PMSMigrationMappingService pmsMigrationMappingService;
    @Mock
    private BackFillService backFillService;
    @Mock
    private ReservationNightService reservationNightService;
    @Mock
    private AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Mock
    private YieldCategoryRuleService yieldCategoryRuleService;
    private Date date = new Date();
    private FileMetadata fileMetadata;
    @Mock
    private PMSMigrationAlertDetailsBuilder pmsMigrationAlertDetailsBuilder;

    @Mock
    private IndependentProductsRepository independentProductsRepository;

    @Mock
    private IndependentProductsService independentProductsService;

    private AnalyticalMarketSegmentCreationHelper analyticalMarketSegmentCreationHelper;

    @BeforeEach
    public void setUp() throws Exception {
        globalCrudService = globalCrudService();
        inject(service, "globalCrudService", globalCrudService);
        inject(service, "tenantCrudService", tenantCrudService);
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        fileMetadata = new FileMetadata();
        fileMetadata.setId(fileMetadataId);
        analyticalMarketSegmentCreationHelper = new AnalyticalMarketSegmentCreationHelper(tenantCrudService());
    }

    @Test
    public void delegatesWithMktSegAMSServiceInHandlingRenamedMSMappings() {
        when(tenantCrudService.findByNativeQuerySingleResult(MktSegRecodingBusinessTypeShift.TABLE_EXISTS, Collections.emptyMap())).thenReturn(0);
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        assertEquals(Collections.emptyList(), mktSegIds);
        verify(pmsRevampAMSDataService).assignOrUpdateMarketSegmentsForNewAMSRules();
        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.INVALIDATE_DISCONTINUED_MKT_SEG_PROPERTY_ID);
    }

    @Test
    public void handleRenamedRateCodesWithNoMarketCodeOrAttributeChangeFlowVerifyTest() {
        when(tenantCrudService.findByNativeQuerySingleResult(MktSegRecodingBusinessTypeShift.TABLE_EXISTS, Collections.emptyMap())).thenReturn(0);
        service.handleRenamedRateCodesWithNoMarketCodeOrAttributeChange();

        verify(pmsMigrationMappingService).getRenamedNewRateCodeByCurrentRateCode();
        verify(pmsMigrationMappingService).getUnchangedNonGroupMktCodes();
        verify(pmsRevampAMSDataService).getNewAMSRulesWith(any(), any());
    }

    @Test
    public void skipsCheckingForAssignmentAttributeRulesIfNoCodesAreChanged() {
        when(pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes()).thenReturn(new ArrayList<>());
        when(pmsMigrationMappingService.getChangedRateCodes()).thenReturn(new ArrayList<>());

        String message = service.ycbrToAMS();

        assertEquals("No market codes or Rate Codes are changed. So skipping", message);
        verify(pmsMigrationMappingService).getChangedCurrentNonGroupMktSegCodes();
        verify(pmsMigrationMappingService).getChangedRateCodes();
        verifyZeroInteractions(analyticalMarketSegmentService);
        verifyZeroInteractions(yieldCategoryRuleService);
    }

    @Test
    public void skipsExplodingAMSRulesIfNoAttributeAssignmentRulesAreFoundForChangedMktCodes() {
        List<String> changedMktCodes = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        List<String> changedRateCodes = new ArrayList<>(Arrays.asList("RC1", "RC2"));
        List<String> allChangedMktCodesFromYCBR = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        List<Integer> ranksOfAssignmentRules = Arrays.asList(CONTAINS.getRank(), STARTS_WITH.getRank(), ENDS_WITH.getRank());
        when(pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes()).thenReturn(changedMktCodes);
        when(pmsMigrationMappingService.getChangedRateCodes()).thenReturn(changedRateCodes);
        when(yieldCategoryRuleService.getMktSegCodes(anyListOf(String.class), anyListOf(String.class))).thenReturn(allChangedMktCodesFromYCBR);
        when(analyticalMarketSegmentService.getMarketCodes(eq(ranksOfAssignmentRules), anyListOf(String.class))).thenReturn(new ArrayList<>());

        String message = service.ycbrToAMS();

        assertEquals("No AMS Rules currently exists with Attribute Assignment Rules. Skipping.", message);
        verify(pmsMigrationMappingService).getChangedCurrentNonGroupMktSegCodes();
        verify(pmsMigrationMappingService).getChangedRateCodes();
        verify(yieldCategoryRuleService).getMktSegCodes(changedMktCodes, changedRateCodes);
        verify(analyticalMarketSegmentService).getMarketCodes(ranksOfAssignmentRules, allChangedMktCodesFromYCBR);
        verify(analyticalMarketSegmentService, times(0)).deleteNonDefaultAMSRulesWithMktSegsCodes(anyListOf(String.class));
        verify(yieldCategoryRuleService, times(0)).getYCBRRules(anyListOf(String.class));
        verify(tenantCrudService, times(0)).save(anyListOf(AnalyticalMarketSegment.class));
    }

    @Test
    public void explodesAMSRulesOfMktSegWithAttributeAssignmentRulesThatAreUndergoingRecoding() {
        List<String> changedMktCodes = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        List<String> changedRateCodes = new ArrayList<>(Arrays.asList("RC1", "RC2"));
        List<String> allChangedMktCodesFromYCBR = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        List<String> allChangedMktCodesWithAttributeAssignmentRules = new ArrayList<>(Collections.singletonList("MS2"));
        List<Integer> ranksOfAssignmentRules = Arrays.asList(CONTAINS.getRank(), STARTS_WITH.getRank(), ENDS_WITH.getRank());
        when(pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes()).thenReturn(changedMktCodes);
        when(pmsMigrationMappingService.getChangedRateCodes()).thenReturn(changedRateCodes);
        when(yieldCategoryRuleService.getMktSegCodes(anyListOf(String.class), anyListOf(String.class))).thenReturn(allChangedMktCodesFromYCBR);
        when(analyticalMarketSegmentService.getMarketCodes(eq(ranksOfAssignmentRules), anyListOf(String.class))).thenReturn(allChangedMktCodesWithAttributeAssignmentRules);
        when(analyticalMarketSegmentService.deleteNonDefaultAMSRulesWithMktSegsCodes(anyListOf(String.class))).thenReturn(5);
        when(yieldCategoryRuleService.getYCBRRules(anyListOf(String.class))).thenReturn(getChangedMktSegYCBRs());

        String message = service.ycbrToAMS();

        assertEquals("Market codes: [MS2] deleted. No of rows deleted = 5", message);
        verify(pmsMigrationMappingService).getChangedCurrentNonGroupMktSegCodes();
        verify(pmsMigrationMappingService).getChangedRateCodes();
        verify(yieldCategoryRuleService).getMktSegCodes(changedMktCodes, changedRateCodes);
        verify(analyticalMarketSegmentService).getMarketCodes(ranksOfAssignmentRules, allChangedMktCodesFromYCBR);
        verify(analyticalMarketSegmentService).deleteNonDefaultAMSRulesWithMktSegsCodes(allChangedMktCodesWithAttributeAssignmentRules);
        verify(yieldCategoryRuleService).getYCBRRules(allChangedMktCodesWithAttributeAssignmentRules);
        verify(tenantCrudService).save(anyListOf(AnalyticalMarketSegment.class));
    }

    @Test
    public void getsRelevantChangedMktSegCodesFromYCBREvenWhenOnlyRateCodesAreChanged() {
        ArrayList<String> changedRateCodes = new ArrayList<>(Arrays.asList("RC1", "RC2"));
        ArrayList<String> allChangedMktCodesFromYCBR = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        List<Integer> ranksOfAssignmentRules = Arrays.asList(CONTAINS.getRank(), STARTS_WITH.getRank(), ENDS_WITH.getRank());
        when(pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes()).thenReturn(new ArrayList<>());
        when(pmsMigrationMappingService.getChangedRateCodes()).thenReturn(changedRateCodes);
        when(yieldCategoryRuleService.getMktSegCodes(anyListOf(String.class), anyListOf(String.class))).thenReturn(allChangedMktCodesFromYCBR);
        when(analyticalMarketSegmentService.getMarketCodes(eq(ranksOfAssignmentRules), anyListOf(String.class))).thenReturn(allChangedMktCodesFromYCBR);
        when(analyticalMarketSegmentService.deleteNonDefaultAMSRulesWithMktSegsCodes(anyListOf(String.class))).thenReturn(5);
        when(yieldCategoryRuleService.getYCBRRules(anyListOf(String.class))).thenReturn(getChangedMktSegYCBRs());

        String message = service.ycbrToAMS();

        assertEquals("Market codes: [MS1, MS2] deleted. No of rows deleted = 5", message);
        verify(pmsMigrationMappingService).getChangedCurrentNonGroupMktSegCodes();
        verify(pmsMigrationMappingService).getChangedRateCodes();
        verify(yieldCategoryRuleService).getMktSegCodes(new ArrayList<>(), changedRateCodes);
        verify(analyticalMarketSegmentService).getMarketCodes(ranksOfAssignmentRules, allChangedMktCodesFromYCBR);
        verify(analyticalMarketSegmentService).deleteNonDefaultAMSRulesWithMktSegsCodes(allChangedMktCodesFromYCBR);
        verify(yieldCategoryRuleService).getYCBRRules(allChangedMktCodesFromYCBR);
        verify(tenantCrudService).save(anyListOf(AnalyticalMarketSegment.class));
    }

    @Test
    public void getsAMSRulesFromTheMktSegsFromYCBR() {
        ArrayList<String> changedMktSegs = new ArrayList<>(Arrays.asList("MS1", "MS2"));
        ArrayList<String> changedRateCodes = new ArrayList<>(Arrays.asList("RC1", "RC2"));
        List<Integer> ranksOfAssignmentRules = Arrays.asList(CONTAINS.getRank(), STARTS_WITH.getRank(), ENDS_WITH.getRank());
        when(pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes()).thenReturn(changedMktSegs);
        when(pmsMigrationMappingService.getChangedRateCodes()).thenReturn(changedRateCodes);
        when(yieldCategoryRuleService.getMktSegCodes(anyListOf(String.class), anyListOf(String.class))).thenReturn(changedMktSegs);
        when(analyticalMarketSegmentService.getMarketCodes(eq(ranksOfAssignmentRules), anyListOf(String.class))).thenReturn(changedMktSegs);
        when(analyticalMarketSegmentService.deleteNonDefaultAMSRulesWithMktSegsCodes(anyListOf(String.class))).thenReturn(5);
        when(yieldCategoryRuleService.getYCBRRules(anyListOf(String.class))).thenReturn(getChangedMktSegYCBRs());

        String message = service.ycbrToAMS();

        assertEquals("Market codes: [MS1, MS2] deleted. No of rows deleted = 5", message);
        verify(pmsMigrationMappingService).getChangedCurrentNonGroupMktSegCodes();
        verify(pmsMigrationMappingService).getChangedRateCodes();
        verify(yieldCategoryRuleService).getMktSegCodes(changedMktSegs, changedRateCodes);
        verify(analyticalMarketSegmentService).getMarketCodes(ranksOfAssignmentRules, changedMktSegs);
        verify(analyticalMarketSegmentService).deleteNonDefaultAMSRulesWithMktSegsCodes(changedMktSegs);
        verify(yieldCategoryRuleService).getYCBRRules(changedMktSegs);
        verify(tenantCrudService).save(anyListOf(AnalyticalMarketSegment.class));
    }

    @Test
    public void testGetByClientIdAndPropertyIdAndRecodingStateWhenNoRecordFoundInDB() {
        final String result = service.activateMktSegRecodingConfiguration();
        assertEquals("No market segment recoding configuration found to activate for " + WC_PROPERTY_CODE_PUNE, result);
    }

    @Test
    public void shouldActivateMktSegRecodingConfiguration() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfig();
        final MktSegRecodingState mktSegRecodingStateBeforeUpdate = mktSegRecodingConfig.getMktSegRecodingState();
        final String result = service.activateMktSegRecodingConfiguration();
        assertEquals("Market segment recoding configuration activated for " + WC_PROPERTY_CODE_PUNE, result);
        assertNotSame(mktSegRecodingStateBeforeUpdate, mktSegRecodingConfig.getMktSegRecodingState());
    }

    @Test
    public void testGetMktSegRecodingConfigurationWhenNoRecordFoundInDB() {
        assertNull(service.getMktSegRecodingConfiguration(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED));
    }

    @Test
    public void testGetMktSegRecodingConfigurationWhenRecordFoundInDB() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfig();
        final MktSegRecodingConfig mktSegRecodingConfigurationInDB = service.getMktSegRecodingConfiguration(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        assertNotNull(mktSegRecodingConfigurationInDB.getId());
        assertMktSegRecodingConfig(mktSegRecodingConfig, mktSegRecodingConfigurationInDB);
    }

    @Test
    public void testIsValidMktSegRecodingConfigurationWhenCaughtUpDateIsAfterOldSysDate() {
        createMktSegRecodingConfig();
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.addDaysToDate(date, 1));
        assertTrue(service.isValidMktSegRecodingConfigurationAvailable());
    }

    @Test
    public void testIsValidMktSegRecodingConfigurationWhenCaughtUpDateIsBeforeOldSysDate() {
        createMktSegRecodingConfig();
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.addDaysToDate(date, -1));
        assertFalse(service.isValidMktSegRecodingConfigurationAvailable());
    }

    @Test
    public void testIsValidMktSegRecodingConfigurationForNullMktSegRecodingConfiguration() {
        assertFalse(service.isValidMktSegRecodingConfigurationAvailable());
        verifyZeroInteractions(dateService);
    }

    @Test
    public void shouldBeAbleToSaveMktSegRecodingConfig() {
        //GIVEN
        MktSegRecodingConfigDto bean = new MktSegRecodingConfigDto();
        bean.setProperty(globalCrudService().find(Property.class, 5));
        bean.setClient(globalCrudService().find(Client.class, 2));
        LocalDate now = LocalDate.now();
        bean.setLastExtractDate(now);
        //WHEN
        service.saveMktSegRecodingConfig(bean);
        //THEN
        List<MktSegRecodingConfig> allMktSegRecodingConfig = globalCrudService().findAll(MktSegRecodingConfig.class);
        assertEquals(5, allMktSegRecodingConfig.get(0).getProperty().getId().intValue());
        assertEquals(2, allMktSegRecodingConfig.get(0).getClient().getId().intValue());
        assertEquals(org.joda.time.LocalDate.now().toDate(), allMktSegRecodingConfig.get(0).getOldSysDate());
        assertEquals(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED, allMktSegRecodingConfig.get(0).getMktSegRecodingState());
    }

    @Test
    public void checksIfCaughtupDateHasPassedOldSysDateOf() {
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        mktSegRecodingConfig.setOldSysDate(dateBeforeCaughtUpDate);

        assertTrue(service.hasCaughtupDatePassedOldSysDateOf(mktSegRecodingConfig));

        mktSegRecodingConfig.setOldSysDate(dateAfterCaughtUpDate);

        assertFalse(service.hasCaughtupDatePassedOldSysDateOf(mktSegRecodingConfig));
    }

    @Test
    public void startsMktSegRecodingJob() {
        service.startMktSegRecodingJob();

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.MktSegRecodingJob), jobParamsCaptor.capture());
        assertEquals(2, jobParamsCaptor.getValue().size());
        assertNotNull(jobParamsCaptor.getValue().get(JobParameterKey.TIMESTAMP));
        assertEquals(WC_PROPERTY_ID_PUNE, jobParamsCaptor.getValue().get(PROPERTY_ID));
    }

    @Test
    public void testChangePropertyStageToDormant() {
        service.changePropertyStageToDormant(WC_PROPERTY_ID_PUNE);
        verify(propertyStageChangeService).changeStage(eq(WC_PROPERTY_ID_PUNE), eq(Stage.DORMANT), nullable(Boolean.class), eq("Changing stage to Dormant as part of Market Segment Recoding start step."));
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldReturnFalseForInactiveConfiguration() {
        createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        assertFalse(service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID));
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldCreateAnAlertAndThrowExceptionIfFirstSheetOfMappingConfigurationNotSuppliedByUser() {
        assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationInconsistentMappingsValidation, DESCRIPTION, MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR);
            when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.emptyList());
            service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldThrowAnAlertAndThrowExceptionIfNewAMSRulesSheetNotSuppliedByUser() {
        assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationInconsistentMappingsValidation, DESCRIPTION, MKT_SEG_RECODING_NEW_AMS_ATTR_MAPPING_NOT_PROVIDED_ERROR);
            when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.singletonList(new PMSMigrationMappingImportDTO()));
            when(pmsMigrationService.getAllRateCodeMktSegMappings()).thenReturn(Collections.singletonList(new PMSMigrationRateCodeMktSegMappingDTO()));
            when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.emptyList());
            service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldThrowAnAlertIfThereIsMissingOrExtraMappingsForMktSeg() {
        assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationInconsistentMappingsValidation, DESCRIPTION, MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR);
            when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.singletonList(new NewAMSRule()));
            when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.singletonList(new PMSMigrationMappingImportDTO()));
            Map<String, Map<String, Integer>> errorMap = new HashMap<>();
            errorMap.put("A", new HashMap<>());
            when(pmsMigrationService.validateExtraMappingsForMSRecoding(anyListOf(PMSMigrationMappingImportDTO.class))).thenReturn(errorMap);
            service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldThrowAnAlertIfAMSAttributionIsNotPresentForAllNewMSAndRCInMSRecoding() {
            assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationInconsistentMappingsValidation, DESCRIPTION, MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR);
            when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.singletonList(new NewAMSRule()));
            when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.singletonList(new PMSMigrationMappingImportDTO()));
            when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.singletonList(new NewAMSRule()));
            when(pmsMigrationService.validateExtraMappingsForMSRecoding(anyListOf(PMSMigrationMappingImportDTO.class))).thenReturn(new HashMap<>());
            doReturn(Boolean.FALSE).when(service).isAMSAttributionPresentForAllNewMSAndRCInMSRecoding();
            service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationShouldThrowAnAlertIfAmsAttributionIsNotPresentForAllNewMSAndRC() {
        assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationInconsistentMappingsValidation, DESCRIPTION, MKT_SEG_RECODING_NEW_AMS_ATTR_MISSING_ERROR);
            when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.singletonList(new NewAMSRule()));
            when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.singletonList(new PMSMigrationMappingImportDTO()));
            final HashMap<String, Map<String, Integer>> map = new HashMap<>();
            when(pmsMigrationService.validateMissingOrExtraMappingsForMktSeg(anyListOf(PMSMigrationMappingImportDTO.class))).thenReturn(map);
            when(pmsMigrationService.getAllRateCodeMktSegMappings()).thenReturn(Collections.singletonList(new PMSMigrationRateCodeMktSegMappingDTO()));
            when(pmsMigrationService.validateMissingOrExtraMappingsForSecondSheetForMktSeg(anyListOf(PMSMigrationRateCodeMktSegMappingDTO.class))).thenReturn(map);
            doReturn(Boolean.FALSE).when(service).isAMSAttributionPresentForAllNewMSAndRCInMSRecoding();
            service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void validateRequiredMktSegRecodingConfigurationHappyFlow() {
        final Map<String, Map<String, Integer>> map = new HashMap<>();
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
        when(pmsMigrationService.getAllExistingMappings()).thenReturn(Collections.singletonList(new PMSMigrationMappingImportDTO()));
        when(pmsRevampAMSDataService.getNewAMSRules()).thenReturn(Collections.singletonList(new NewAMSRule()));
        when(pmsMigrationService.validateExtraMappingsForMSRecoding(anyListOf(PMSMigrationMappingImportDTO.class))).thenReturn(map);
        when(pmsRevampAMSDataService.getUserUploadedAMSRules()).thenReturn(new HashMap<>());
        when(pmsRevampAMSDataService.getMktSegWiseIgnoredRateCodesOfMismatchType(IgnoredRateCode.MismatchType.MISSING)).thenReturn(new HashMap<>());
        when(tenantCrudService.findByNativeQuerySingleResult(RateCodeShiftAssociation.TABLE_EXISTS, Collections.emptyMap())).thenReturn(0);
        assertTrue(service.validateRequiredMktSegRecodingConfiguration(JOB_EXECUTION_ID));
        assertEquals(MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE, mktSegRecodingConfig.getMktSegRecodingState());
        verifyZeroInteractions(alertService);
    }

    @Test
    public void updatesInprogressRecodingConfigIsBackedupField() {
        createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
        String result = service.setInProgressConfigIsBackedUpAs(Boolean.TRUE);

        assertEquals("In progress market segment recoding configuration isBackedUp is set as true", result);
        MktSegRecodingConfig ongoingRecodingConfig = service.fetchInProgressRecodingConfiguration();
        assertTrue(ongoingRecodingConfig.isBackupCreated());

        result = service.setInProgressConfigIsBackedUpAs(Boolean.FALSE);

        assertEquals("In progress market segment recoding configuration isBackedUp is set as false", result);
        ongoingRecodingConfig = service.fetchInProgressRecodingConfiguration();
        assertFalse(ongoingRecodingConfig.isBackupCreated());
    }

    @Test
    public void returnsAppropriateMessageWhenNoInprogressRecodingConfigIsFoundInDb() {
        String result = service.setInProgressConfigIsBackedUpAs(Boolean.TRUE);
        assertEquals("No market segment recoding configuration found for " + WC_PROPERTY_CODE_PUNE, result);
    }

    @Test
    public void testUpdateLatestFileMetadataAndGet() {
        when(pmsRevampNonPaceUpdationService.updateLatestFileMetadataAndGet()).thenReturn(fileMetadata);
        final FileMetadata updatedFileMetadata = service.updateLatestFileMetadataAndGet();
        assertEquals(fileMetadata.getId(), updatedFileMetadata.getId());
        verify(pmsRevampNonPaceUpdationService).updateLatestFileMetadataAndGet();
    }

    @Test
    public void testPopulateNonPaceAndLastPacePointFromMktAccomActivity() {
        when(fileMetadataService.getFileMetadataById(anyInt())).thenReturn(fileMetadata);
        service.populateNonPaceAndLastPacePointFromMktAccomActivity(123);
        verify(pmsRevampNonPaceUpdationService).populateNonPaceAndLastPacePointFromMktAccomActivity(fileMetadata);
    }

    @Test
    public void testUpdateLastPacePointFromMktAccomActivity() throws ParseException {
        final Date snapshotDt = DateUtil.parseDate("2019-12-26", DateUtil.DEFAULT_DATE_FORMAT);
        final Date minOccupancyDate = DateUtil.addDaysToDate(snapshotDt, -2);
        final Date maxOccupancyDate = DateUtil.addDaysToDate(snapshotDt, 2);
        fileMetadata.setSnapshotDt(snapshotDt);
        fileMetadata.setPastWindowSize(2);
        fileMetadata.setFutureWindowSize(2);
        when(fileMetadataService.getFileMetadataById(1)).thenReturn(fileMetadata);
        when(roomTypeMarketSegmentActivityService.updatePace(anyString(), eq(minOccupancyDate), eq(maxOccupancyDate), eq(fileMetadata))).thenReturn(20);
        final int updatedActivityCount = service.updateLastPacePointFromMktAccomActivity(fileMetadataId);
        assertEquals(20, updatedActivityCount);
    }

    @Test
    public void testSyncSASDataSetsWithMktAccomActivityDataForBDE() {
        when(fileMetadataService.getFileMetadataById(1)).thenReturn(fileMetadata);
        int overrideMaxHistoryLosOffset = 0;
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.AMS_REBUILD_SUMMARY_FOR_ALL_DATA.getParameterName()))
                .thenReturn(overrideMaxHistoryLosOffset);
        when(operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata)).thenReturn(10);
        doNothing().when(operaPopulationService).executeWithOverrideLosOffset(fileMetadataId, Boolean.FALSE, overrideMaxHistoryLosOffset);
        final int records = service.syncSASDataSetsWithMktAccomActivityData(fileMetadataId);
        assertEquals(10, records);
        verify(operaPopulationService).executeWithOverrideLosOffset(fileMetadataId, Boolean.FALSE, overrideMaxHistoryLosOffset);
    }

    @Test
    public void testCreateBackup() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);
        final String resultString = "MS Recoding - Completed backup";
        when(mktSegRecodingBackupService.createBackup()).thenReturn(resultString);
        final String backupResult = service.createBackup();
        assertTrue(mktSegRecodingConfig.isBackupCreated());
        assertEquals(resultString, backupResult);
    }

    @Test
    public void shouldGetMSRecodingConfigForClientAndProperty() {
        //GIVEN
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        mktSegRecodingConfig.setClient(globalCrudService.find(Client.class, 2));
        mktSegRecodingConfig.setProperty(globalCrudService.find(Property.class, 5));
        Date oldSysDate = org.joda.time.LocalDate.now().toDate();
        mktSegRecodingConfig.setOldSysDate(oldSysDate);
        mktSegRecodingConfig.setMktSegRecodingState(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        mktSegRecodingConfig.setBackupCreated(false);
        globalCrudService.save(mktSegRecodingConfig);
        //WHEN
        MktSegRecodingConfig msRecodingConfig = service.getMSRecodingConfigForClientAndProperty(2, 5);
        //THEN
        assertEquals(2, msRecodingConfig.getClient().getId().intValue());
        assertEquals(5, msRecodingConfig.getProperty().getId().intValue());
        assertEquals(oldSysDate, msRecodingConfig.getOldSysDate());
        assertEquals(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED, msRecodingConfig.getMktSegRecodingState());
        assertFalse(msRecodingConfig.isBackupCreated());
    }

    @Test
    public void returnsMktSegCodesInvalidated_MSMergeScenario() {
        List<String> changedMktSegs = new ArrayList<>(Arrays.asList("BGO", "BMO"));
        List<String> distinctEquivalentMktSegCodes = new ArrayList<>(Collections.singletonList("BR"));
        when(pmsMigrationMappingService.getChangedCurrentMktSegCodes()).thenReturn(changedMktSegs);
        when(pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes()).thenReturn(distinctEquivalentMktSegCodes);

        String message = service.invalidateRedundantMktSeg();

        Map<String, Object> queryParam = getQueryParamMap(changedMktSegs, distinctEquivalentMktSegCodes);
        verify(pmsMigrationMappingService).getChangedCurrentMktSegCodes();
        verify(pmsMigrationMappingService).getDistinctNewEquivalentMktSegCodes();
        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.INVALIDATE_PROPERTY_ID_BY_MKT_SEG, queryParam);
        assertEquals("[BGO, BMO] MktSegs invalidated", message);
    }

    @Test
    public void returnsMktSegCodesInvalidated_MSSplitScenario() {
        List<String> changedMktSegs = new ArrayList<>(Collections.singletonList("BGO"));
        List<String> distinctEquivalentMktSegCodes = new ArrayList<>(Arrays.asList("BR", "BM"));
        when(pmsMigrationMappingService.getChangedCurrentMktSegCodes()).thenReturn(changedMktSegs);
        when(pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes()).thenReturn(distinctEquivalentMktSegCodes);

        String message = service.invalidateRedundantMktSeg();

        Map<String, Object> queryParam = getQueryParamMap(changedMktSegs, distinctEquivalentMktSegCodes);
        verify(pmsMigrationMappingService).getChangedCurrentMktSegCodes();
        verify(pmsMigrationMappingService).getDistinctNewEquivalentMktSegCodes();
        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.INVALIDATE_PROPERTY_ID_BY_MKT_SEG, queryParam);
        assertEquals("[BGO] MktSegs invalidated", message);
    }

    @Test
    public void returnsMktSegCodesInvalidated_MSSemiSplitScenario() {
        List<String> changedMktSegs = new ArrayList<>(Collections.singletonList("BGO"));
        List<String> distinctEquivalentMktSegCodes = new ArrayList<>(Arrays.asList("BR", "BGO"));
        when(pmsMigrationMappingService.getChangedCurrentMktSegCodes()).thenReturn(changedMktSegs);
        when(pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes()).thenReturn(distinctEquivalentMktSegCodes);

        String message = service.invalidateRedundantMktSeg();

        verify(pmsMigrationMappingService).getChangedCurrentMktSegCodes();
        verify(pmsMigrationMappingService).getDistinctNewEquivalentMktSegCodes();
        verifyZeroInteractions(tenantCrudService);
        assertEquals("[] MktSegs invalidated", message);
    }

    @Test
    public void returnsMktSegCodesInvalidated_MSRenameScenario() {
        List<String> changedMktSegs = new ArrayList<>(Collections.singletonList("BGO"));
        List<String> distinctEquivalentMktSegCodes = new ArrayList<>(Collections.singletonList("BR"));
        when(pmsMigrationMappingService.getChangedCurrentMktSegCodes()).thenReturn(changedMktSegs);
        when(pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes()).thenReturn(distinctEquivalentMktSegCodes);

        String message = service.invalidateRedundantMktSeg();

        Map<String, Object> queryParam = getQueryParamMap(changedMktSegs, distinctEquivalentMktSegCodes);
        verify(pmsMigrationMappingService).getChangedCurrentMktSegCodes();
        verify(pmsMigrationMappingService).getDistinctNewEquivalentMktSegCodes();
        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.INVALIDATE_PROPERTY_ID_BY_MKT_SEG, queryParam);
        assertEquals("[BGO] MktSegs invalidated", message);
    }

    @Test
    public void shouldNotUpdateRecodingState() {
        final MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        globalCrudService.save(mktSegRecodingConfig);
        final MktSegRecodingState recodingStateBeforeUpdate = mktSegRecodingConfig.getMktSegRecodingState();
        service.updateMktSegRecodingStateTo(MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
        globalCrudService.flushAndClear();
        MktSegRecodingConfig mktSegRecodingConfigAfterUpdate = globalCrudService.find(MktSegRecodingConfig.class, mktSegRecodingConfig.getId());
        final MktSegRecodingState recodingStateAfterUpdate = mktSegRecodingConfigAfterUpdate.getMktSegRecodingState();
        assertEquals(recodingStateBeforeUpdate, recodingStateAfterUpdate);
    }

    @Test
    public void shouldUpdateRecodingState() {
        final MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MktSegRecodingState.MKT_SEG_RECODING_READY);
        globalCrudService.save(mktSegRecodingConfig);
        final MktSegRecodingState recodingStateBeforeUpdate = mktSegRecodingConfig.getMktSegRecodingState();
        service.updateMktSegRecodingStateTo(MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
        globalCrudService.flushAndClear();
        MktSegRecodingConfig mktSegRecodingConfigAfterUpdate = globalCrudService.find(MktSegRecodingConfig.class, mktSegRecodingConfig.getId());
        final MktSegRecodingState recodingStateAfterUpdate = mktSegRecodingConfigAfterUpdate.getMktSegRecodingState();
        assertNotEquals(recodingStateBeforeUpdate, recodingStateAfterUpdate);
        assertEquals(MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE, recodingStateAfterUpdate);
    }

    @Test
    public void returnsAppropriateMessageIfNoConfigExists() {
        assertEquals("No config found, with recoding state >= Post Backfill.", service.validateCCFGProcessCompletion(JOB_EXECUTION_ID));
    }

    @Test
    public void returnsAppropriateMessageIfNoConfigWithStateAfterValidationsDoneFound() {
        createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_READY);

        assertEquals("No config found, with recoding state >= Post Backfill.", service.validateCCFGProcessCompletion(JOB_EXECUTION_ID));
    }

    @Test
    public void generatesAlertAndThrowsExceptionToHaltFurtherProcessingIfCCFGProcessIsNotCompleted() {
        assertThrows(TetrisException.class, () -> {
            createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_DONE);
            InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
            mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationCCFGValidation, null, MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR);
            when(pmsMigrationService.isCCFGProcessCompleted(any())).thenReturn(false);
            service.validateCCFGProcessCompletion(JOB_EXECUTION_ID);
        });
    }

    @Test
    public void updatesRecodingStateIfCCFGIsCompleted() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState.MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_DONE);
        InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
        mockAlertServiceForAlert(alertTypeEntityMock, AlertType.PMSMigrationCCFGValidation, null, MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR);
        when(pmsMigrationService.isCCFGProcessCompleted(any())).thenReturn(true);

        String result = service.validateCCFGProcessCompletion(JOB_EXECUTION_ID);

        assertEquals("CCFG Completed", result);
        assertEquals(MKT_SEG_RECODING_COMMIT_FG_DONE, mktSegRecodingConfig.getMktSegRecodingState());
        MktSegRecodingConfig mktSegRecodingConfigFromDb = service.getMktSegRecodingConfiguration(MktSegRecodingState.MKT_SEG_RECODING_COMMIT_FG_DONE);
        assertMktSegRecodingConfig(mktSegRecodingConfig, mktSegRecodingConfigFromDb);
    }

    @Test
    public void shouldExecuteAndSyncTenantMS() {
        final List<Integer> newMktSegIds = Arrays.asList(1, 2);
        service.executeAndSyncTenantMS(newMktSegIds);
        verify(backFillService).updateFilenameForCorrectionBackfill();
        verify(backFillService).executeAndSyncTenantMS(newMktSegIds);
    }

    @Test
    public void handleRenamedMappedMktCode_shouldReturnWithoutProcessingWhenEitherCodeIsNull() {
        service.handleRenamedMappedMarketCode(null, "COMP");
        verifyNoInteractions(tenantCrudService);

        service.handleRenamedMappedMarketCode("COMP", null);
        verifyNoInteractions(tenantCrudService);

        service.handleRenamedMappedMarketCode(null, null);
        verifyNoInteractions(tenantCrudService);
    }

    @Test
    public void handleRenamedMappedMktCode_verifyThatItWillUpdateMktSegAndMktSegMaster() {
        when(tenantCrudService.executeUpdateByNamedQuery(anyString(), anyMap())).thenReturn(1);
        service.handleRenamedMappedMarketCode("COMP", "COMP_NEW");

        final Map<String, Object> parameters = QueryParameter.with("oldCode", "COMP").and("newCode", "COMP_NEW").parameters();
        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.UPDATE_MKT_SEG, parameters);
        verify(tenantCrudService).executeUpdateByNamedQuery(MarketSegmentMaster.UPDATE_CODE, parameters);
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(eq(MarketSegmentMaster.DELETE_BY_CODE), anyMap());
    }

    @Test
    public void handleRenamedMappedMktCode_verifyThatItWillDeleteMktSegMasterWhenMktSegNotExists() {
        final Map<String, Object> parameters = QueryParameter.with("oldCode", "COMP").and("newCode", "COMP_NEW").parameters();
        when(tenantCrudService.executeUpdateByNamedQuery(MktSeg.UPDATE_MKT_SEG, parameters)).thenReturn(0);
        service.handleRenamedMappedMarketCode("COMP", "COMP_NEW");

        verify(tenantCrudService).executeUpdateByNamedQuery(MktSeg.UPDATE_MKT_SEG, parameters);
        verify(tenantCrudService).executeUpdateByNamedQuery(eq(MarketSegmentMaster.DELETE_BY_CODE), anyMap());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(MarketSegmentMaster.UPDATE_CODE, parameters);
    }


    private List<YieldCategoryRule> getChangedMktSegYCBRs() {
        List<YieldCategoryRule> yieldCategoryRules = new ArrayList<>();
        yieldCategoryRules.add(createYCBR("MS1", "RC1", "MS1_QYL", 100));
        yieldCategoryRules.add(createYCBR("MS1", null, "MS1_DEF", 999));
        yieldCategoryRules.add(createYCBR("MS2", "RC2", "MS2_QYL", 100));
        yieldCategoryRules.add(createYCBR("MS2", null, "MS2_DEF", 999));
        return yieldCategoryRules;
    }

    private YieldCategoryRule createYCBR(String marketCode, String rateCode, String mappedMarketCode, int rank) {
        YieldCategoryRule ycr = new YieldCategoryRule();
        ycr.setMarketCode(marketCode);
        ycr.setRateCode(rateCode);
        ycr.setAnalyticalMarketCode(mappedMarketCode);
        ycr.setRank(rank);
        ycr.setBookingStartDate(org.joda.time.LocalDate.now());
        ycr.setBookingEndDate(org.joda.time.LocalDate.now());
        return ycr;
    }

    private Map<String, Object> getQueryParamMap(List<String> changedMktSegs, List<String> distinctEquivalentMktSegCodes) {
        changedMktSegs.removeAll(distinctEquivalentMktSegCodes);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("mktSegCodes", changedMktSegs);
        return queryParam;
    }

    @Test
    public void calculateMissingRateCodesInCaseOfSplit() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-01", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-02", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-03", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-04", "MS", "RC0", 1);

        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS", "RC3", 1);

        addReservationNightEntry("123459999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC4", 1);
        addReservationNightEntry("123459999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC4", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MS1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MS2", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC0", "rc0", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS1", new HashSet<>(Collections.singletonList("RC1")));
            put("MS2", new HashSet<>(Collections.singletonList("RC2")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS1", List.of("RC3", "rc0"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldCalculateMissingRateCodesInCaseOfSplitWhenRatesAreShared() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-01", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-02", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-03", "MS", "RC0", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-05", "2019-01-04", "MS", "RC0", 1);

        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS", "RC3", 1);

        addReservationNightEntry("123459999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC1", 1);
        addReservationNightEntry("123459999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC2", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MS1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MS2", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS34", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC0", "rc0", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS1", new HashSet<>(Collections.singletonList("RC1")));
            put("MS2", new HashSet<>(Collections.singletonList("RC2")));
            put("MS34", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS1", List.of("RC3", "rc0"));
            put("MS34", List.of("RC2"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesInCaseOfMerge() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS", Arrays.asList("RC2", "RC3"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesInCaseOfAMSToStraightMSRename() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>());
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap<>();
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesWhenOneRateCodeIsSharedBetweenTwoMarketCodes() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC2", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC2", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS", Collections.singletonList("RC2"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesWhenOneRateCodeIsSharedBetweenTwoMarketCodesAndRateCodeIsRenamed() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2New", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2New", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "EQUALS", "10");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2New", new HashSet<>(Collections.singletonList("RC2New")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS1", Collections.singletonList("RC2New"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesWhenOneRateCodeIsSharedBetweenTwoMarketCodesAndRateCodeIsRenamedButMarketCodeIsNot() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2New", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "EQUALS", "10");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet<>(Collections.singletonList("RC2New")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS1", Arrays.asList("RC2New"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldNotConsiderDiscontinuedMarketCodeWhileCalculatingMissingRateCodes_WhenOneRateCodeIsSharedBetweenTwoMarketCodesAndOneMarketCodeIsDiscontinued() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "1");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2New", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "EQUALS", "10");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS1", new HashSet<>(Collections.singletonList("RC2New")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap();
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldConsiderEmptyMarketCodeWhileCalculatingMissingRateCodes() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        Integer MS3_ID = addMarketSegment("MS3");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", null, "RC3", MS3_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", null, "RC3", MS3_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS", Arrays.asList("RC2", "RC3"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldConsiderMarketCodeFromAnalyticalMarketSegmentWhenNotFoundInReservationsWhileCalculatingMissingRateCodes() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");

        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS", Arrays.asList("RC2", "RC3"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldIgnoreWildCardRuleFromAnalyticalMarketSegmentWhileCalculatingMissingRateCodes() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        Integer MS3_ID = addMarketSegment("MS3");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", MS3_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", MS3_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");

        addAnalyticalMarketSegmentEntry("MS1", "RC", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "STARTS_WITH", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "STARTS_WITH", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "STARTS_WITH", "10");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Collections.singletonList("RC1")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS", Arrays.asList("RC2", "RC3"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesWhenStraightMSConvertedToAMSWithNoChangeInMarketCodeAndRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer MS1_ID = addMarketSegment("LNG");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", MS1_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", MS1_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", MS1_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG", new HashSet<>(Collections.singletonList("COMP")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("LNG", Arrays.asList("ROOM"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void calculateMissingRateCodesWhenStraightMSConvertedToAMSWithChangeInMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNGNew", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNGNew", new HashSet<>(Collections.singletonList("COMP")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("LNGNew", Arrays.asList("ROOM"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldNotMarkedSuchStraightMSRateCodesAsMissingIfNotConvertedIntoAMSWhenNoChangeInMarketCodeAndRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        Integer FAM_ID = addMarketSegment("FAM");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "FAM", "RC3", FAM_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "FAM", "RC3", FAM_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        addAnalyticalMarketSegmentEntry("FAM", null, "FAM", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG", new HashSet<>(Arrays.asList("COMP", "ROOM")));
            put("FAM", new HashSet<>());
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(new HashMap<>(), missingRateCodes);
    }

    @Test
    public void shouldNotMarkedSuchStraightMSRateCodesAsMissingIfNotConvertedIntoAMSWhenChangeInMarketCodeAndRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        Integer FAM_ID = addMarketSegment("FAM");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "FAM", "RC3", FAM_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "FAM", "RC3", FAM_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNGNEW", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        addAnalyticalMarketSegmentEntry("FAM", null, "FAM", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNGNEW", new HashSet<>(Arrays.asList("COMP", "ROOM")));
            put("FAM", new HashSet<>());
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(new HashMap<>(), missingRateCodes);
    }

    @Test
    public void givenForStraightMS_WhenSplitsIntoTwoMSAndSpecifyAllRateCodesInCombineForTwoMS_ThenItShouldNotCalculateMissingRateCodesForEachOther() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG2", "0", "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG1", new HashSet<>(Collections.singletonList("COMP")));
            put("LNG2", new HashSet<>(Collections.singletonList("ROOM")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(Collections.EMPTY_MAP, missingRateCodes);
    }

    @Test
    public void givenForStraightMS_WhenSplitsIntoTwoMSAndSpecifyFewRateCodesInCombineForTwoMS_ThenItShouldFetchMissingRateCodesForPrimary() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        Integer FAM_ID = addMarketSegment("FAM");
        Integer TAM_ID = addMarketSegment("TAM");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addReservationNightEntry("123456999", "2019-01-05", "2019-01-07", "2019-01-05", "LNG", "DAILY", LNG_ID);
        addReservationNightEntry("123456999", "2019-01-05", "2019-01-07", "2019-01-06", "LNG", "DAILY", LNG_ID);

        addReservationNightEntry("123459999", "2019-01-05", "2019-01-07", "2019-01-05", "FAM", "DAY", FAM_ID);
        addReservationNightEntry("123459999", "2019-01-05", "2019-01-07", "2019-01-06", "FAM", "DAY", FAM_ID);

        addReservationNightEntry("123499999", "2019-01-05", "2019-01-07", "2019-01-05", "FAM", "GORV", FAM_ID);
        addReservationNightEntry("123499999", "2019-01-05", "2019-01-07", "2019-01-06", "FAM", "GORV", FAM_ID);

        addReservationNightEntry("123999999", "2019-01-05", "2019-01-07", "2019-01-05", "FAM", "ADV", FAM_ID);
        addReservationNightEntry("123999999", "2019-01-05", "2019-01-07", "2019-01-06", "FAM", "ADV", FAM_ID);

        addReservationNightEntry("123488889", "2019-01-05", "2019-01-07", "2019-01-05", "TAM", "TORV", TAM_ID);
        addReservationNightEntry("123488889", "2019-01-05", "2019-01-07", "2019-01-06", "TAM", "TORV", TAM_ID);

        addReservationNightEntry("123988899", "2019-01-05", "2019-01-07", "2019-01-05", "TAM", "TADV", TAM_ID);
        addReservationNightEntry("123988899", "2019-01-05", "2019-01-07", "2019-01-06", "TAM", "TADV", TAM_ID);

        addReservationNightEntry("123988999", "2019-01-05", "2019-01-07", "2019-01-05", "TAM", "TDAY", TAM_ID);
        addReservationNightEntry("123988999", "2019-01-05", "2019-01-07", "2019-01-06", "TAM", "TDAY", TAM_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG2", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM2", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "TAM", "tam1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "TAM", "tam2", "0", "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "DAILY", "DAILY", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "DAY", "DAY", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "GORV", "GORV", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ADV", "ADV", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "TORV", "torv", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "TADV", "Tadv", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "TDAY", "tday", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        addAnalyticalMarketSegmentEntry("FAM", null, "FAM", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        addAnalyticalMarketSegmentEntry("TAM", null, "TAM", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG1", new HashSet<>(Collections.singletonList("COMP")));
            put("LNG2", new HashSet<>(Collections.singletonList("ROOM")));
            put("FAM1", new HashSet<>(Collections.singletonList("DAY")));
            put("FAM2", new HashSet<>(Collections.singletonList("GORV")));
            put("tam1", new HashSet<>(Collections.singletonList("torv")));
            put("tam2", new HashSet<>(Collections.singletonList("Tadv")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("LNG1", Arrays.asList("DAILY"));
            put("FAM1", Arrays.asList("ADV"));
            put("tam1", Arrays.asList("tday"));
        }};
        assertEquals(expected, missingRateCodes);
    }


    @Test
    public void givenForStraightMS_WhenSplitsIntoTwoMSAndStillKeptAsStraight_ThenItShouldNotCalculateMissingRateCodes() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        Integer LNG_ID = addMarketSegment("LNG");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "LNG", "COMP", LNG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "LNG", "COMP", LNG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "LNG", "LNG2", "0", "0");
        addPmsMigrationMappingEntry("RATE_CODE", "COMP", "COMP", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ROOM", "ROOM", null, "0");

        addAnalyticalMarketSegmentEntry("LNG", null, "LNG", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG1", new HashSet<>());
            put("LNG2", new HashSet<>());
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(Collections.EMPTY_MAP, missingRateCodes);
    }

    @Test
    public void shouldExcludeNullValuesWhileFilteringStraightMSFromUserUploadedRules() {
        Set rateCodeWithNullValues = new HashSet<>();
        rateCodeWithNullValues.add(null);
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("LNG1", rateCodeWithNullValues);
            put("LNG2", new HashSet<>());
            put("LNG3", Arrays.asList("DAILY"));
        }};
        Map resultMap = service.filterStraightMSFromUserUploadedAMSRule(mktSegWiseRateCodesFromUpload);
        HashMap<String, List<String>> expected = new HashMap() {{
            put("LNG3", Arrays.asList("DAILY"));
        }};
        assertEquals(expected, resultMap);
    }

    @Test
    public void shouldFindIfRateCodeIsShiftedToOtherMarketSegmentWhenNoChangeInMarketCodeAndRateCodeName() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC1")));
            put("MS1", new HashSet(Arrays.asList("RC2")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC1", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldReturnEmptyListWhenUserUploadedRateCodesNotFoundWhileFindingRateCodeIsShiftedToOtherMarketSegment() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2", "GROUP", "ALL", "1");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet());
            put("MS1", new HashSet());
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertTrue(rateCodeAssociations.isEmpty());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegmentWhenRateCodeRenamedAndDefinedRuleWithOtherMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC1NEW")));
            put("MS1", new HashSet(Arrays.asList("RC2")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC1NEW", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegmentWhenMarketCodeRenamedAndItsRateCodeDefinesRuleWithOtherMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1NEW", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC1")));
            put("MS1NEW", new HashSet(Arrays.asList("RC2")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC1", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1NEW", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegmentWhenRateCodeDefinesRuleWithOtherMarketCodeWhichIsRenamed() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2NEW", new HashSet(Arrays.asList("RC1", "RC3", "RC4")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC1", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2NEW", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldNotFindRateCodeIsShiftedToOtherMarketSegmentWhenMarketCodeMergesAndDefinesWithRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MSNEW", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MSNEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MSNEW", new HashSet(Arrays.asList("RC1", "RC2", "RC3", "RC4")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertTrue(rateCodeAssociations.isEmpty());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegment_WhenPreviousMarketCodeMergesButDefinesWithRateCodeWithThirdOtherRenamedMarketSegment() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        Integer MS3_ID = addMarketSegment("MS3");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC4", MS3_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC4", MS3_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MSNEW", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MSNEW", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS3", "RC4", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MSNEW", new HashSet(Arrays.asList("RC1", "RC3")));
            put("MS3NEW", new HashSet(Arrays.asList("RC4", "RC2NEW")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC2NEW", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MSNEW", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS3NEW", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldNotFindRateCodeIsShiftedToOtherMarketSegmentWhenMarketCodeSplitsAndDefinesWithRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MSNEW1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MSNEW2", "0", "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS", "RC1", "MS_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS", "RC2", "MS_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS", "RC3", "MS_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS", "RC4", "MS_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS", null, "MS_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MSNEW1", new HashSet(Arrays.asList("RC1", "RC2")));
            put("MSNEW2", new HashSet(Arrays.asList("RC3", "RC4")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertTrue(rateCodeAssociations.isEmpty());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegmentWhenRateCodeIsSharedBetweenMarketCodesButOnlyOneDefinesWithRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC2", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC2")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC2", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldNotFindRateCodeIsShiftedToOtherMarketSegmentWhenRateCodeIsSharedBetweenMarketCodesAndBothDefinesWithRateCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC2", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC2")));
            put("MS1", new HashSet(Arrays.asList("RC2")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertTrue(rateCodeAssociations.isEmpty());
    }

    @Test
    public void shouldFindRateCodeIsShiftedToOtherMarketSegmentGivenRateCodeIsSharedBetweenMarketCodesWhenRateCodeRenamedAndDefinesWithOneMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC2", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet(Arrays.asList("RC2NEW")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals("RC2NEW", rateCodeAssociations.get(0).getRateCodeName());
        assertEquals("MS1", rateCodeAssociations.get(0).getPreviousAssociatedMarketSegment());
        assertEquals("MS2", rateCodeAssociations.get(0).getNewAssociatedMarketSegment());
    }

    @Test
    public void shouldBeAbleSaveRateCodeShiftAssociation() {
        //GIVEN
        List<RateCodeAssociation> rateCodeAssociations = new ArrayList<>();
        rateCodeAssociations.add(new RateCodeAssociation("RC1", "MS1", "MS2"));
        rateCodeAssociations.add(new RateCodeAssociation("RC2", "MS3", "MS4"));
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //WHEN
        service.saveRateCodeShiftAssociations(rateCodeAssociations);
        //THEN
        List<RateCodeShiftAssociation> rateCodeShiftAssociations =
                tenantCrudService().findByNamedQuery(RateCodeShiftAssociation.ALL);
        assertEquals(2, rateCodeShiftAssociations.size());
        assertEquals("RC1", rateCodeShiftAssociations.get(0).getRateCode());
        assertEquals("MS1", rateCodeShiftAssociations.get(0).getPreviousMarketSegment());
        assertEquals("MS2", rateCodeShiftAssociations.get(0).getNewMarketSegment());
        assertEquals("RC2", rateCodeShiftAssociations.get(1).getRateCode());
        assertEquals("MS3", rateCodeShiftAssociations.get(1).getPreviousMarketSegment());
        assertEquals("MS4", rateCodeShiftAssociations.get(1).getNewMarketSegment());
    }

    @Test
    public void shouldFilterRateCodeShiftAssociationWhileCalculatingMissingRateCodesWhenRateCodeIsRenamedAndDefinedForOtherMarketSegment() {
        //GIVEN
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC4", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC4", MS2_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC4", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS2", new HashSet<>(Collections.singletonList("RC1NEW")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        service.saveRateCodeShiftAssociations(rateCodeAssociations);
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(Collections.EMPTY_MAP, missingRateCodes);
    }

    @Test
    public void shouldBeAbleToCalculateMissingRateCodesWhenSharedRateCodeIsRenamedAndDefinesWithOnlyOneMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        Integer MS3_ID = addMarketSegment("MS3");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC2", MS2_ID);

        addReservationNightEntry("123457999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC4", MS3_ID);
        addReservationNightEntry("123457999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC4", MS3_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1New", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS3", "RC4", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS1New", new HashSet(Arrays.asList("RC1")));
            put("MS2", new HashSet(Arrays.asList("RC2NEW", "RC3")));
        }};
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        HashMap<String, List<String>> expected = new HashMap() {{
            put("MS1New", Arrays.asList("RC2NEW"));
        }};
        assertEquals(expected, missingRateCodes);
    }

    @Test
    public void shouldSkipFromMissingRateCodeIfGivenInRateCodeShiftWhenSharedRateCodeIsRenamedAndDefinesWithOnlyOneMarketCode() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer MS1_ID = addMarketSegment("MS1");
        Integer MS2_ID = addMarketSegment("MS2");
        Integer MS3_ID = addMarketSegment("MS3");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", MS1_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", MS1_ID);

        addReservationNightEntry("123456989", "2019-01-01", "2019-01-02", "2019-01-01", "MS1", "RC2", MS1_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC3", MS2_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC3", MS2_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS2", "RC2", MS2_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS2", "RC2", MS2_ID);

        addReservationNightEntry("123457999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC4", MS3_ID);
        addReservationNightEntry("123457999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC4", MS3_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1New", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2NEW", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS3", "RC4", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", null, "MS3_DEF", "FENCED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS1New", new HashSet(Arrays.asList("RC1")));
            put("MS2", new HashSet(Arrays.asList("RC2NEW", "RC3")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        service.saveRateCodeShiftAssociations(rateCodeAssociations);
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(Collections.EMPTY_MAP, missingRateCodes);
    }

    @Test
    public void shouldSkipFromMissingRateCodeIfAlreadyPresentInAMSRules() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer GSRM_ID = addMarketSegment("GSRM");
        Integer CITY_USB_ID = addMarketSegment("CITY_USB");
        Integer CITY_QY_ID = addMarketSegment("CITY_QY");
        Integer CITY_DEF_ID = addMarketSegment("CITY_DEF");
        Integer DISC_QYL_ID = addMarketSegment("DISC_QYL");
        Integer DISC_QY_ID = addMarketSegment("DISC_QY");
        Integer DISC_DEF_ID = addMarketSegment("DISC_DEF");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "GSRM", "HSDE00", GSRM_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "GSRM", "GRFCBDAYUSE", GSRM_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "GSRM", "GRFCB", GSRM_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "GSRM", "", GSRM_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "CITY", "RRODEX", CITY_USB_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "CITY", "HSDE00", CITY_QY_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "CITY", "", CITY_DEF_ID);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-04", "2019-01-01", "DISC", "ADVP21", DISC_QYL_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-04", "2019-01-02", "DISC", "HSDE00", DISC_QY_ID);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-04", "2019-01-03", "DISC", "", DISC_DEF_ID);

        addPmsMigrationMappingEntry("MARKET_SEGMENT", "GSRM", "CITY", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT", "GSRM", "DISC", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "CITY", "CITY", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "DISC", "DISC", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "GRFCBDAYUSE", "GRFCBDAYUSE", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "GRFCB", "GRFCB", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "HSDE00", "HSDE00", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RRODEX", "RRODEX", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ADVP21", "ADVP21", null, "0");

        addAnalyticalMarketSegmentEntry("GSRM", null, "GSRM", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentEntry("DISC", "ADVP21", "DISC_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("DISC", "GRFCB", "DISC_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("DISC", "HSDE00", "DISC_QY", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("DISC", null, "DISC_DEF", "PACKAGED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("CITY", "RRODEX", "CITY_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("CITY", "GRFCBDAYUSE", "CITY_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("CITY", "HSDE00", "CITY_QY", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("CITY", null, "CITY_DEF", "PACKAGED", "DEFAULT", "999");
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //WHEN
        HashMap mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("CITY", new HashSet(Arrays.asList("GRFCBDAYUSE")));
            put("DISC", new HashSet(Arrays.asList("GRFCB")));
        }};
        List<RateCodeAssociation> rateCodeAssociations = service.findRateCodeIsShiftedFromPreviousMarketSegmentToOther(mktSegWiseRateCodesFromUpload);
        service.saveRateCodeShiftAssociations(rateCodeAssociations);
        Map<String, List<String>> missingRateCodes = service.calculateMissingRateCodes(mktSegWiseRateCodesFromUpload);
        //THEN
        assertEquals(Collections.EMPTY_MAP, missingRateCodes);
    }

    private int addAnalyticalMarketSegmentEntry(final String marketCode, final String rateCode, final String mappedMarketCode,
                                                final String attribute, final String rateCodeType, final String rank) {
        return tenantCrudService().executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg ([Market_Code],[Rate_Code] ,[Mapped_Market_Code], [Attribute], " +
                "[Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Code_Type], [Rank], [Complimentary]) values('"
                + marketCode + "', " + (null == rateCode ? "null" : "'" + rateCode + "'") + ", '" + mappedMarketCode + "', '" + attribute + "', 1, getdate(), 1, getdate(), '"
                + rateCodeType + "', " + rank + ", 0)");
    }

    private int insertIntoAnalyticalMktSegWithPreservedEntry(String marketCode, String rateCode, String mappedMarketCode,
                                                             String attribute, String rateCodeType, String rank, int preserved) {
        return analyticalMarketSegmentCreationHelper.addAnalyticalMarketSegmentEntry(marketCode, rateCode, mappedMarketCode, attribute, rateCodeType, rank, 0, preserved);
    }

    private Integer addMarketSegment(final String code) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg values(5, '" + code + "', '" + code + "', '" + code + "', 1, getdate(), 1, 1, getdate(), 1, 0, 0)");
        return tenantCrudService().findByNativeQuerySingleResult("select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = :code",
                QueryParameter.with("code", code).parameters());
    }

    @Test
    public void returnsTrueIfConfigStateIsAfterValidationsDone() {
        MktSegRecodingConfig config = new MktSegRecodingConfig();
        config.setMktSegRecodingState(MKT_SEG_RECODING_COMMIT_FG_REQUIRED);

        assertTrue(service.isConfigStateAfterValidationsDone(config));
    }

    @Test
    public void returnsTrueIfConfigStateIsValidationsDone() {
        MktSegRecodingConfig config = new MktSegRecodingConfig();
        config.setMktSegRecodingState(MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);

        assertTrue(service.isConfigStateAfterValidationsDone(config));
    }

    @Test
    public void returnsFalseIfConfigStateIsBeforeValidationsDone() {
        MktSegRecodingConfig config = new MktSegRecodingConfig();
        config.setMktSegRecodingState(MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED);

        assertFalse(service.isConfigStateAfterValidationsDone(config));
    }

    @Test
    public void isAMSAttributionPresentForAllNewMSAndRCInMSRecoding() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        //consider this entry comes in extract after user has configured and updated mappings
        addReservationNightEntry("123459999", "2019-01-01", "2019-01-02", "2019-01-01", "MS3", "RC4", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Arrays.asList("RC1", "RC2", "RC3")));
        }};
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(mktSegWiseRateCodesFromUpload);
        assertFalse(service.isAMSAttributionPresentForAllNewMSAndRCInMSRecoding());
    }


    @Test
    public void isAMSAttributionPresentForAllNewMSAndRCInMSRecoding_ValidationShouldFailWhenMissingRateCodeOtherThanUserApprovedFoundInNextExtract() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        //consider following entries comes in extract after user has configured and updated mappings
        addReservationNightEntry("123499999", "2019-01-05", "2019-01-06", "2019-01-05", "MS3", "RC5", 1);
        //User marked this entry as ignore missing rate codes
        addReservationNightEntry("123459999", "2019-01-01", "2019-01-02", "2019-01-01", "MS3", "RC4", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Arrays.asList("RC1", "RC2", "RC3")));
        }};
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(mktSegWiseRateCodesFromUpload);
        Map<String, Set<String>> ignoreMissingRateCodes = new HashMap() {{
            put("MS", new HashSet<>(Arrays.asList("RC4")));
        }};
        when(pmsRevampAMSDataService.getMktSegWiseIgnoredRateCodesOfMismatchType(IgnoredRateCode.MismatchType.MISSING)).thenReturn(ignoreMissingRateCodes);
        assertFalse(service.isAMSAttributionPresentForAllNewMSAndRCInMSRecoding());
    }

    @Test
    public void isAMSAttributionPresentForAllNewMSAndRCInMSRecoding_ValidationShouldSucceededWhenUserMarkedMissingRateCodeFoundInNextExtract() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "MS1", "RC1", 1);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "MS1", "RC1", 1);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "MS2", "RC2", 1);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "MS2", "RC2", 1);

        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-01", "MS3", "RC3", 1);
        addReservationNightEntry("123456999", "2019-01-01", "2019-01-03", "2019-01-02", "MS3", "RC3", 1);

        //consider following entry comes in extract after user has configured and updated mappings
        //User marked this entry as ignore missing rate codes
        addReservationNightEntry("123459999", "2019-01-01", "2019-01-02", "2019-01-01", "MS3", "RC4", 1);

        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        inject(service, "tenantCrudService", tenantCrudService());
        //WHEN
        Map<String, Set<String>> mktSegWiseRateCodesFromUpload = new HashMap() {{
            put("MS", new HashSet<>(Arrays.asList("RC1", "RC2", "RC3")));
        }};
        when(pmsRevampAMSDataService.getUserUploadedAMSRules()).thenReturn(mktSegWiseRateCodesFromUpload);
        Map<String, Set<String>> ignoreMissingRateCodes = new HashMap() {{
            put("MS", new HashSet<>(Arrays.asList("RC4")));
        }};
        when(pmsRevampAMSDataService.getMktSegWiseIgnoredRateCodesOfMismatchType(IgnoredRateCode.MismatchType.MISSING)).thenReturn(ignoreMissingRateCodes);
        assertTrue(service.isAMSAttributionPresentForAllNewMSAndRCInMSRecoding());
    }

    @Test
    public void shouldNotTriggerAlertAfterPostBackFillDataValidation() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfigurationWithRecodingStatus(MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
        when(pmsMigrationService.isActivityDeltaExceedsOverallThreshold()).thenReturn(Boolean.FALSE);
        service.validatePostBackFillData(mktSegRecodingConfig, JOB_EXECUTION_ID);
        verifyZeroInteractions(pmsMigrationAlertDetailsBuilder);
        verifyZeroInteractions(alertService);
    }

    @Test
    public void shouldTriggerAlertAfterPostBackFillDataValidation() {
        final MktSegRecodingConfig mktSegRecodingConfig = createMktSegRecodingConfigurationWithRecodingStatus(MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
        when(pmsMigrationService.isActivityDeltaExceedsOverallThreshold()).thenReturn(Boolean.TRUE);
        InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
        Alert alertMock = mock(Alert.class);
        when(alertService.getAlertType(AlertType.PMSMigrationPostBackFillDataValidation.getName())).thenReturn(alertTypeEntityMock);
        when(alertTypeEntityMock.isEnabled()).thenReturn(Boolean.TRUE);
        when(alertService.generateAlertAndUpdateRecodingState(any(WorkContextType.class), eq(alertTypeEntityMock), anyString(), anyString(), eq(AlertType.PMSMigrationPostBackFillDataValidation), eq(mktSegRecodingConfig))).thenReturn(alertMock);
        try {
            service.validatePostBackFillData(mktSegRecodingConfig, JOB_EXECUTION_ID);
            fail("Should not execute this line.");
        } catch (TetrisException te) {
            assertEquals(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, te.getErrorCode());
        }
    }

    @Test
    public void shouldSkipPreservedEntriesWhileGettingOriginalMktSegNameAndId() {
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery("delete from Analytical_Mkt_Seg");
        Integer GSRM_ID = addMarketSegment("GSRM");
        Integer CITY_USB_ID = addMarketSegment("CITY_USB");
        Integer CITY_QY_ID = addMarketSegment("CITY_QY");
        Integer CITY_DEF_ID = addMarketSegment("CITY_DEF");
        Integer DISC_QYL_ID = addMarketSegment("DISC_QYL");
        Integer DISC_QY_ID = addMarketSegment("DISC_QY");
        Integer DISC_DEF_ID = addMarketSegment("DISC_DEF");
        addPmsMigrationMappingEntry("MARKET_SEGMENT", "GSRM", "CITY", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT", "GSRM", "DISC", "0", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "CITY", "CITY", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "DISC", "DISC", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "GRFCBDAYUSE", "GRFCBDAYUSE", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "GRFCB", "GRFCB", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "HSDE00", "HSDE00", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RRODEX", "RRODEX", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "ADVP21", "ADVP21", null, "0");

        addAnalyticalMarketSegmentEntry("GSRM", null, "GSRM", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentEntry("DISC", "ADVP21", "DISC_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        insertIntoAnalyticalMktSegWithPreservedEntry("GSRM", "GRFCBDAYUSE", "CITY_USB", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("GSRM", "GRFCB", "DISC_QYL", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("GSRM", "HSDE00", "CITY_USB", "EQUAL_TO_BAR", "EQUALS", "10", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("GSRM", null, "CITY_DEF", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "DEFAULT", "999", 1);
        //New AMS - Rate Code HSDE00 is already present in AMS with CITY and DISC
        insertIntoAnalyticalMktSegWithPreservedEntry("DISC", "ADVP21", "DISC_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("DISC", "GRFCB", "DISC_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("DISC", "HSDE00", "DISC_QY", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("DISC", null, "DISC_DEF", "PACKAGED", "DEFAULT", "999", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("CITY", "RRODEX", "CITY_USB", "EQUAL_TO_BAR", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("CITY", "GRFCBDAYUSE", "CITY_USB", "EQUAL_TO_BAR", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("CITY", "HSDE00", "CITY_QY", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "EQUALS", "10", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("CITY", null, "CITY_DEF", "PACKAGED", "DEFAULT", "999", 0);

        List<Object[]> rows = tenantCrudService().findByNativeQuery("select map.ana_mkt_id, map.ori_mkt_code from " +
                        "dbo.ufn_get_generic_analyticalmarketsegid_originalmarketsegment_mapping() map " +
                        "where map.ori_mkt_code in :mktSegCodes",
                QueryParameter.with("mktSegCodes", Arrays.asList("GSRM")).parameters());
        assertEquals(1, rows.size());
        assertEquals(GSRM_ID, rows.get(0)[0]);
        assertEquals("GSRM", rows.get(0)[1]);
    }

    private void addPmsMigrationMappingEntry(final String codeType, final String currentCode, final String newCode, final String isPrimary, final String discontinued) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Migration_Mapping values('H1', '" + codeType + "', '" + currentCode + "', '" + newCode + "', " + discontinued + ", " + isPrimary + ")");
    }

    private void addReservationNightEntry(final String reservationId, final String arrivalDt, final String departureDt, final String occupancyDt,
                                          final String marketCode, final String rateCode, Integer mktSegId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night (File_Metadata_ID,Property_ID,Reservation_Identifier," +
                "Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Cancellation_DT," +
                "Booked_Accom_Type_Code,Accom_Type_ID,Mkt_Seg_ID,Room_Revenue,Food_Revenue,Beverage_Revenue," +
                "Telecom_Revenue,Other_Revenue,Total_Revenue,Source_Booking,Nationality,Rate_Code,Rate_Value,Room_Number," +
                "Booking_type,Number_Children,Number_Adults,CreateDate_DTTM,Confirmation_No,Channel,Booking_TM," +
                "Occupancy_DT, Persistent_Key, Analytics_Booking_Dt, Inv_Block_Code, Market_Code) " +
                "values(1, 5, " + reservationId + ", 'SS', '" + arrivalDt + "', '" + departureDt + "', '2018-12-24', '1900-01-01', " +
                "'SXBL', 4, " + mktSegId + ", '100.00000', '50.00000', '10.00000', " +
                "'3.00000', '0.00000', '163.00000', '', '', '" + rateCode + "', '30.00000', 127" +
                ", 'IN', 0, 2, getdate(), null, null, null, '"
                + occupancyDt + "', '123456789-2019-01-01', null,null," + (null == marketCode ? "null" : "'" + marketCode + "'") + ")");
    }

    private void mockAlertServiceForAlert(InfoMgrTypeEntity alertTypeEntityMock, AlertType pmsMigrationCCFGValidation, String description, String mktSegRecodingMktSegMappingNotProvidedError) {
        when(alertService.getAlertType(pmsMigrationCCFGValidation.getName())).thenReturn(alertTypeEntityMock);
        when(alertTypeEntityMock.isEnabled()).thenReturn(Boolean.TRUE);
    }

    private MktSegRecodingConfig createMktSegRecodingConfigurationWithRecodingStatus(MktSegRecodingState mktSegRecodingState) {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(mktSegRecodingState);
        mktSegRecodingConfig = globalCrudService.save(mktSegRecodingConfig);
        return mktSegRecodingConfig;
    }

    private void assertMktSegRecodingConfig(MktSegRecodingConfig mktSegRecodingConfig, MktSegRecodingConfig mktSegRecodingConfigFromDB) {
        assertEquals(mktSegRecodingConfig.getMktSegRecodingState(), mktSegRecodingConfigFromDB.getMktSegRecodingState());
        assertEquals(mktSegRecodingConfig.getProperty().getId(), mktSegRecodingConfigFromDB.getProperty().getId());
        assertEquals(mktSegRecodingConfig.getClient().getId(), mktSegRecodingConfigFromDB.getClient().getId());
        assertEquals(mktSegRecodingConfig.getCreatedByUserId(), mktSegRecodingConfigFromDB.getCreatedByUserId());
        assertEquals(mktSegRecodingConfig.getLastUpdatedByUserId(), mktSegRecodingConfigFromDB.getLastUpdatedByUserId());
        assertEquals(mktSegRecodingConfig.getCreateDate(), mktSegRecodingConfigFromDB.getCreateDate());
        assertEquals(mktSegRecodingConfig.getLastUpdatedDate(), mktSegRecodingConfigFromDB.getLastUpdatedDate());
    }

    private MktSegRecodingConfig createMktSegRecodingConfig() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        return globalCrudService.save(mktSegRecodingConfig);
    }

    private MktSegRecodingConfig buildMktSegRecodingConfig(MktSegRecodingState mktSegRecodingNotStarted) {
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        mktSegRecodingConfig.setClient(globalCrudService.find(Client.class, WC_CLIENT_ID_BLACKSTONE));
        mktSegRecodingConfig.setOldSysDate(date);
        mktSegRecodingConfig.setProperty(globalCrudService.find(Property.class, WC_PROPERTY_ID_PUNE));
        mktSegRecodingConfig.setMktSegRecodingState(mktSegRecodingNotStarted);
        mktSegRecodingConfig.setCreateDate(date);
        mktSegRecodingConfig.setLastUpdatedDate(date);
        mktSegRecodingConfig.setCreatedByUserId(Constants.SYSTEM_USER_ID);
        mktSegRecodingConfig.setLastUpdatedByUserId(Constants.SYSTEM_USER_ID);
        return mktSegRecodingConfig;
    }

    @Test
    public void shouldBeAbleToSaveBusinessTypeShiftAssociations() {
        //GIVEN
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        List<MktSegRecodingBusinessTypeShift> businessTypeAssociations = new ArrayList<>();
        businessTypeAssociations.add(new MktSegRecodingBusinessTypeShift("CONV",
                Constants.BUSINESS_TYPE.GROUP.getCode(),
                Constants.BUSINESS_TYPE.TRANSIENT.getCode()));
        //WHEN
        service.saveBusinessTypeShiftAssociations(businessTypeAssociations);
        //THEN
        List<MktSegRecodingBusinessTypeShift> mktSegRecodingBusinessTypeShifts =
                service.getBusinessTypeShiftAssociationsForGroupToTransient();
        assertEquals("CONV", mktSegRecodingBusinessTypeShifts.get(0).getMarketSegmentName());
        assertEquals(Constants.BUSINESS_TYPE.GROUP.getCode(), mktSegRecodingBusinessTypeShifts.get(0).getCurrentBusiness());
        assertEquals(Constants.BUSINESS_TYPE.TRANSIENT.getCode(), mktSegRecodingBusinessTypeShifts.get(0).getAttributionInAms());
    }

    @Test
    public void shouldBeAbleToGetBusinessTypeShiftAssociationFromGroupToTransient() {
        //GIVEN
        inject(service, "tenantCrudService", tenantCrudService());
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        List<MktSegRecodingBusinessTypeShift> businessTypeAssociations = new ArrayList<>();
        businessTypeAssociations.add(new MktSegRecodingBusinessTypeShift("CONV",
                Constants.BUSINESS_TYPE.GROUP.getCode(),
                Constants.BUSINESS_TYPE.TRANSIENT.getCode()));
        businessTypeAssociations.add(new MktSegRecodingBusinessTypeShift("COMP",
                Constants.BUSINESS_TYPE.TRANSIENT.getCode(),
                Constants.BUSINESS_TYPE.GROUP.getCode()));
        service.saveBusinessTypeShiftAssociations(businessTypeAssociations);
        //WHEN
        List<MktSegRecodingBusinessTypeShift> mktSegRecodingBusinessTypeShifts =
                service.getBusinessTypeShiftAssociationsForGroupToTransient();
        //THEN
        assertEquals(1, mktSegRecodingBusinessTypeShifts.size());
        assertEquals("CONV", mktSegRecodingBusinessTypeShifts.get(0).getMarketSegmentName());
        assertEquals(Constants.BUSINESS_TYPE.GROUP.getCode(), mktSegRecodingBusinessTypeShifts.get(0).getCurrentBusiness());
        assertEquals(Constants.BUSINESS_TYPE.TRANSIENT.getCode(), mktSegRecodingBusinessTypeShifts.get(0).getAttributionInAms());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_READY() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_READY);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_REQUIRED() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_REQUIRED);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_DONE() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_POST_BACKFILL_DATA_VALIDATION_DONE);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_COMMIT_FG_REQUIRED() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_COMMIT_FG_REQUIRED);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigInProgressWhenConfigStatusIs_MKT_SEG_RECODING_COMMIT_FG_DONE() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_COMMIT_FG_DONE);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertTrue(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigIsNotInProgressWhenConfigStatusIs_MKT_SEG_RECODING_NOT_STARTED() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_NOT_STARTED);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertFalse(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    public void mktSegRecodingConfigIsNotInProgressWhenConfigStatusIs_MKT_SEG_RECODING_COMPLETED() {
        MktSegRecodingConfig mktSegRecodingConfig = buildMktSegRecodingConfig(MKT_SEG_RECODING_COMPLETED);
        globalCrudService.save(mktSegRecodingConfig);
        globalCrudService.flushAndClear();
        assertFalse(service.isMktSegRecodingConfigInProgress());
    }

    @Test
    void getUnAttributedMarketSegmentsShouldReturnUnassignedGroupMarketSegmentsCount() {
        when(analyticalMarketSegmentService.getUnassignedGroupMarketSegments()).thenReturn(Collections.singletonList(new AnalyticalMarketSegmentSummary()));
        when(analyticalMarketSegmentService.getUnassignedIndividualMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.emptyList());
        assertEquals(1, service.getUnAttributedMarketSegments());
    }

    @Test
    void hasUnAttributedMarketSegmentsShouldReturnUnassignedIndividualMarketSegmentsCount() {
        when(analyticalMarketSegmentService.getUnassignedGroupMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedIndividualMarketSegments()).thenReturn(Collections.singletonList(new AnalyticalMarketSegmentSummary()));
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.emptyList());
        assertEquals(1, service.getUnAttributedMarketSegments());
    }

    @Test
    void hasUnAttributedMarketSegmentsShouldReturnUnassignedSharedMarketSegmentsCount() {
        when(analyticalMarketSegmentService.getUnassignedGroupMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.singletonList(new AnalyticalMarketSegmentSummary()));
        assertEquals(1, service.getUnAttributedMarketSegments());
    }

    @Test
    void hasUnAttributedMarketSegmentsHappyFlow() {
        when(analyticalMarketSegmentService.getUnassignedGroupMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.emptyList());
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.emptyList());
        assertEquals(0, service.getUnAttributedMarketSegments());
    }

    @Test
    public void shouldReturnTrueWhenMappedCodeInvolvedInMSRecoding() {
        doReturn(true).when(pmsMigrationService).isMsRecodingEnabled();
        inject(service, "tenantCrudService", tenantCrudService());
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", null, "MS3", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        boolean isInvolvedInRecoding = service.isInvolvedInMsRecoding("MS2");
        assertTrue(isInvolvedInRecoding);
    }

    @Test
    public void shouldReturnTrueWhenMappedCodeInvolvedInMSRecodingForSplitMS() {
        doReturn(true).when(pmsMigrationService).isMsRecodingEnabled();
        inject(service, "tenantCrudService", tenantCrudService());
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", "RC1", "MS2_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", null, "MS2_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", "RC2", "MS2_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", null, "MS2_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", "RC3", "MS3_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", null, "MS3_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        boolean isInvolvedInRecoding = service.isInvolvedInMsRecoding("MS2_USB");
        assertTrue(isInvolvedInRecoding);
    }

    @Test
    public void shouldReturnFalseWhenMappedCodeNotInvolvedInMSRecoding() {
        doReturn(true).when(pmsMigrationService).isMsRecodingEnabled();
        inject(service, "tenantCrudService", tenantCrudService());
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", null, "MS3", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        boolean isInvolvedInRecoding = service.isInvolvedInMsRecoding("MS3");
        assertFalse(isInvolvedInRecoding);
    }

    @Test
    public void shouldReturnFalseWhenMappedCodeNotInvolvedInMSRecodingForSplitMS() {
        doReturn(true).when(pmsMigrationService).isMsRecodingEnabled();
        inject(service, "tenantCrudService", tenantCrudService());
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", "RC1", "MS2_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", null, "MS2_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 1);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", "RC2", "MS2_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", null, "MS2_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", "RC3", "MS3_USB", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", null, "MS3_DEF", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        assertFalse(service.isInvolvedInMsRecoding("MS3_USB"));
        assertFalse(service.isInvolvedInMsRecoding("MS3_DEF"));
    }

    @Test
    public void shouldReturnFalseWhenMSRecodingToggleIsOFF() {
        doReturn(false).when(pmsMigrationService).isMsRecodingEnabled();
        inject(service, "tenantCrudService", tenantCrudService());
        insertIntoAnalyticalMktSegWithPreservedEntry("MS1", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS2", null, "MS2", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        insertIntoAnalyticalMktSegWithPreservedEntry("MS3", null, "MS3", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "ALL", "1", 0);
        boolean isInvolvedInRecoding = service.isInvolvedInMsRecoding("MS3");
        assertFalse(isInvolvedInRecoding);
        verify(tenantCrudService, never()).findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_INVOLVED_IN_MS_RECODING);
    }

    @Test
    public void findUnmappedRateCodes() {
        Map<String, String> map = new HashMap<>();
        List<String> unmappedRateCodes = Arrays.asList("RC1", "RC2");
        when(restClient.getJsonFromEndpoint(RestEndpoints.GET_UNMAPPED_RATE_CODES_V2, map, 0)).thenReturn("[\"RC1\",\"RC2\"]");
        List<String> result = service.findUnmappedRateCodes(map);
        verify(restClient).getJsonFromEndpoint(RestEndpoints.GET_UNMAPPED_RATE_CODES_V2, map, 0);
        assertEquals(unmappedRateCodes, result);
    }

    @Test
    public void generateAlertAndHaltProcessing() {
        AlertType alertType = AlertType.UnmappedRateCodesFound;
        InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
        when(alertService.getAlertType(alertType.getName())).thenReturn(alertTypeEntityMock);
        when(alertTypeEntityMock.isEnabled()).thenReturn(true);
        String statsCorrelationId = "dummyStats";
        Long jobExecutionId = 1234L;
        List<String> unmappedRateCodes = Arrays.asList("RC1", "RC2");
        String description = statsCorrelationId + "##" + UNMAPPED_RATE_CODES_ALERT_METADATA + ":" + jobExecutionId;
        String details = String.join(", ", unmappedRateCodes);
        assertThrows(TetrisException.class, () -> service.generateAlertAndHaltProcessing(unmappedRateCodes, jobExecutionId, statsCorrelationId));
        verify(alertService).createAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(), alertTypeEntityMock, description, details, alertType);
    }

    @Test
    public void generateAlertAndHaltProcessingWithAlertDisabled() {
        AlertType alertType = AlertType.UnmappedRateCodesFound;
        InfoMgrTypeEntity alertTypeEntityMock = mock(InfoMgrTypeEntity.class);
        when(alertService.getAlertType(alertType.getName())).thenReturn(alertTypeEntityMock);
        when(alertTypeEntityMock.isEnabled()).thenReturn(false);
        String statsCorrelationId = "dummyStats";
        Long jobExecutionId = 1234L;
        List<String> unmappedRateCodes = Arrays.asList("RC1", "RC2");
        service.generateAlertAndHaltProcessing(unmappedRateCodes, jobExecutionId, statsCorrelationId);
        verify(alertService).getAlertType(alertType.getName());
        verifyNoMoreInteractions(alertService);
    }

    @Test
    public void resolveAlertAndResumeFailedStepWhenMappingProvided() {
        InfoMgrInstanceEntity alertInstance = mock(InfoMgrInstanceEntity.class);
        when(alertService.findExistingAlert(any(Alert.class))).thenReturn(alertInstance);
        String description = "dummyStats##" + UNMAPPED_RATE_CODES_ALERT_METADATA + ":123L";
        when(alertInstance.getDescription()).thenReturn(description);
        String unmappedRateCodesStr = "[]";
        when(restClient.getJsonFromEndpoint(eq(RestEndpoints.GET_UNMAPPED_RATE_CODES_V2), anyMap(), eq(0))).thenReturn(unmappedRateCodesStr);
        service.resolveAlertAndResumeFailedStep();
        verify(alertService).resolveAlert(anyInt(), anyInt());
        verify(alertService).resumeJobFailedByUnmappedRateCodesAlert(description);
    }

    @Test
    public void resolveAlertAndResumeFailedStepWhenMappingNotProvided() {
        InfoMgrInstanceEntity alertInstance = mock(InfoMgrInstanceEntity.class);
        when(alertService.findExistingAlert(any(Alert.class))).thenReturn(alertInstance);
        String description = "dummyStats##" + UNMAPPED_RATE_CODES_ALERT_METADATA + ":123L";
        when(alertInstance.getDescription()).thenReturn(description);
        String unmappedRateCodesStr = "[\"RC1\",\"RC2\"]";
        when(restClient.getJsonFromEndpoint(eq(RestEndpoints.GET_UNMAPPED_RATE_CODES_V2), anyMap(), eq(0))).thenReturn(unmappedRateCodesStr);
        assertThrows(TetrisException.class, () -> service.resolveAlertAndResumeFailedStep());
        verify(alertService).findExistingAlert(any(Alert.class));
        verifyNoMoreInteractions(alertService);
    }

    @Test
    void shouldBeAbleToIdentifyCodesHavingSimilarSpellingWithDifferentCases() {
        inject(service, "tenantCrudService", tenantCrudService());
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "CORP", "CORP", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "GOV", "gov_n", null, null);
        List<PMSMigrationMapping> pmsMigrationMappings = service.identifyCodesHavingSimilarSpellingWithDifferentCases();
        assertEquals(1, pmsMigrationMappings.size());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
    }

    @Test
    void shouldBeAbleToCodesHavingSimilarSpellingWithDifferentCasesPresentInMappings() {
        inject(service, "tenantCrudService", tenantCrudService());
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "CORP", "CORP", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "GOV", "gov_n", null, null);
        assertTrue(service.codesHavingSimilarSpellingWithDifferentCasesPresentInMappings());
    }

    @Test
    void shouldGetMktSegProductDetailsWhenEqualToBarAttributeAMSPresentForGivenProducts() {
        inject(service, "tenantCrudService", tenantCrudService());
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS4", "MS4", null, null);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_IP2_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS4", "RC4", "MS4_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip3 = addProduct("IP3", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer bar = getProductId("BAR");
        createMktSegProductMappingEntry("MS1_IP1_USB", ip1);
        createMktSegProductMappingEntry("MS2_IP2_USB", ip2);
        createMktSegProductMappingEntry("MS2_IP3_QYL", ip3);
        createMktSegProductMappingEntry("MS3_USB", bar);

        List<MktSegProductDetailsDTO> mktSegProductDetailsWithEqualToBarAttribute = service.getMktSegProductDetailsWithEqualToBarAttribute(List.of("BAR", "IP1", "IP2"));

        assertFalse(mktSegProductDetailsWithEqualToBarAttribute.isEmpty());
        assertEquals(3, mktSegProductDetailsWithEqualToBarAttribute.size());
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(0), "BAR", "MS3", "MS3_USB", "MS3", false);
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(1), "IP1", "MS1", "MS1_IP1_USB", "MS1", false);
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(2), "IP2", "MS2", "MS2_IP2_USB", "MS2", false);

    }

    @Test
    void shouldGetMktSegProductDetailsWithDiscontinuedMSWhenEqualToBarAttributeAMSPresentForGivenProducts() {
        inject(service, "tenantCrudService", tenantCrudService());
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, "1");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS4", "MS4", null, "1");
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_IP2_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS4", "RC4", "MS4_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip3 = addProduct("IP3", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer bar = getProductId("BAR");
        createMktSegProductMappingEntry("MS1_IP1_USB", ip1);
        createMktSegProductMappingEntry("MS2_IP2_USB", ip2);
        createMktSegProductMappingEntry("MS2_IP3_QYL", ip3);
        createMktSegProductMappingEntry("MS3_USB", bar);

        List<MktSegProductDetailsDTO> mktSegProductDetailsWithEqualToBarAttribute = service.getMktSegProductDetailsWithEqualToBarAttribute(List.of("BAR", "IP1", "IP2"));

        assertFalse(mktSegProductDetailsWithEqualToBarAttribute.isEmpty());
        assertEquals(3, mktSegProductDetailsWithEqualToBarAttribute.size());
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(0), "BAR", "MS3", "MS3_USB", "MS3", false);
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(1), "IP1", "MS1", "MS1_IP1_USB", "MS1", true);
        assertMktSegDetailsDTO(mktSegProductDetailsWithEqualToBarAttribute.get(2), "IP2", "MS2", "MS2_IP2_USB", "MS2", false);

    }

    @Test
    void shouldNotGetMktSegProductDetailsWhenNoEqualToBarAttributeAMSNotPresentForGivenProducts() {
        inject(service, "tenantCrudService", tenantCrudService());
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS1", "MS1", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS3", "MS3", null, null);
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS4", "MS4", null, null);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC2", "MS2_IP2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS3", "RC3", "MS3_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS4", "RC4", "MS4_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip3 = addProduct("IP3", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer bar = getProductId("BAR");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        createMktSegProductMappingEntry("MS2_IP2_QYL", ip2);
        createMktSegProductMappingEntry("MS2_IP3_QYL", ip3);
        createMktSegProductMappingEntry("MS3_QYL", bar);

        List<MktSegProductDetailsDTO> mktSegProductDetailsWithEqualToBarAttribute = service.getMktSegProductDetailsWithEqualToBarAttribute(List.of("BAR", "IP1", "IP2"));

        assertTrue(mktSegProductDetailsWithEqualToBarAttribute.isEmpty());

    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromAms() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2"));

        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromAmsOnlyForIndependentProducts() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer linkedProduct = addProduct("LinkedProduct", "AGILE_RATES", "UNFENCED_AND_NO_PACKAGED", "null");
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(linkedProduct, "RC3");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "DEFAULT", "999");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "DEFAULT", "999");
        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2"));

        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromReservationNight() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-01", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-02", "StraightMS", "RC3", 1);

        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2", "RC3"));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("StraightMS", "RC3", "IP2")));
    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromReservationNightForLinkedToProduct() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip1LinkedProduct = addProduct("IP1linkedProduct", "AGILE_RATES", "UNFENCED_AND_NO_PACKAGED", ip1.toString());
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        addProductRateCodeEntry(ip1LinkedProduct, "RC1");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-01", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-02", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-01", "IPLinkedMS", "RC1", 1);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-02", "IPLinkedMS", "RC1", 1);

        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2", "RC3", "RC4"));

        assertEquals(4, existingProductRateCodeRulesForRateCodes.size());
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("StraightMS", "RC3", "IP2")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("IPLinkedMS", "RC1", "IP1")));
    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromReservationNightForLinkedToBarProduct() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer barLinkedProduct = addProduct("BARlinkedProduct", "AGILE_RATES", "UNFENCED_AND_NO_PACKAGED", "1");
        when(independentProductsService.getAllProducts()).thenReturn(tenantCrudService().findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE));
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        addProductRateCodeEntry(barLinkedProduct, "RC4");
        Integer MS1Id = addMarketSegment("MS1");
        Integer striaghtMSId = addMarketSegment("StraightMS");
        Integer barLinkedMsId = addMarketSegment("BARLinkedMS");
        Integer barMsId = addMarketSegment("BARMS");
        createMarketSegmentDetails(barLinkedMsId, 2, "1", "1");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-01", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-02", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-01", "BARLinkedMS", "RC4", barLinkedMsId);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-02", "BARLinkedMS", "RC4", barLinkedMsId);
        addReservationNightEntry("123476999", "2019-01-01", "2019-01-03", "2019-01-01", "BARMS", "RC4", barMsId);
        addReservationNightEntry("123476999", "2019-01-01", "2019-01-03", "2019-01-02", "BARMS", "RC4", barMsId);

        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2", "RC3", "RC4"));

        assertEquals(5, existingProductRateCodeRulesForRateCodes.size());
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("BARLinkedMS", "RC4", "BAR")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("BARMS", "RC4", "BAR")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("StraightMS", "RC3", "IP2")));
    }

    @Test
    public void shouldBeAbleToGetExistingProductRateCodeRulesForRateCodesFromReservationNightForBarProduct() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer barProductId = 1;
        when(independentProductsService.getAllProducts()).thenReturn(tenantCrudService().findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE));
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        Integer MS1Id = addMarketSegment("MS1");
        Integer striaghtMSId = addMarketSegment("StraightMS");
        Integer barMs = addMarketSegment("BARMS_QYL");
        createMarketSegmentDetails(barMs, 2, "1", "1");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        createMktSegProductMappingEntry("BARMS_QYL", barProductId);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-01", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-03", "2019-01-02", "StraightMS", "RC3", 1);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-01", "BARMS", "RC4", barMs);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-02", "BARMS", "RC4", barMs);

        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2", "RC3", "RC4"));

        assertEquals(4, existingProductRateCodeRulesForRateCodes.size());
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("BARMS", "RC4", "BAR")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("StraightMS", "RC3", "IP2")));
    }

    @Test
    public void shouldBeAbleToSkipLowVolumeReservationsDecidedByPercentageWhileGettingExistingProductRateCodeRulesForRateCodesFromReservation() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer barProductId = 1;
        when(independentProductsService.getAllProducts()).thenReturn(tenantCrudService().findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE));
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.PERCENTAGE_TO_SKIP_RESERVATIONS_HAVING_LESS_VOLUME_IN_PRODUCT_RATE_VALIDATION_IN_MS_RECODING.getParameterName())).thenReturn(10);
        Integer MS1Id = addMarketSegment("MS1");
        Integer mSWithSharedRateCodeId = addMarketSegment("MSWithSharedRateCode");
        Integer barMs = addMarketSegment("BARMS_QYL");
        Integer ms2Ip2QylId = addMarketSegment("MS2_IP2_QYL");
        createMarketSegmentDetails(barMs, 2, "1", "1");
        createMktSegProductMappingEntry("MS1_IP1_QYL", ip1);
        createMktSegProductMappingEntry("MS2_IP2_QYL", ip2);
        createMktSegProductMappingEntry("BARMS_QYL", barProductId);
        addAnalyticalMarketSegmentEntry("MS1", "RC1", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", "RC2", "MS1_IP1_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS1", null, "MS1_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", "RC3", "MS2_IP2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MS2", null, "MS2_DEF", "FENCED", "EQUALS", "10");
        addAnalyticalMarketSegmentEntry("MSWithSharedRateCode", "RC3", "MS2_IP2_QYL", "QUALIFIED_NONBLOCK_LINKED_YIELDABLE", "EQUALS", "10");
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-01", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-02", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-03", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-04", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-05", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476789", "2019-01-01", "2019-01-07", "2019-01-06", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123479999", "2019-01-01", "2019-01-05", "2019-01-01", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123479999", "2019-01-01", "2019-01-05", "2019-01-02", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123479999", "2019-01-01", "2019-01-05", "2019-01-03", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123479999", "2019-01-01", "2019-01-05", "2019-01-04", "MSWithSharedRateCode", "RC3", mSWithSharedRateCodeId);
        addReservationNightEntry("123476999", "2019-01-01", "2019-01-02", "2019-01-01", "MS2", "RC3", ms2Ip2QylId);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-01", "BARMS", "RC4", barMs);
        addReservationNightEntry("123476799", "2019-01-01", "2019-01-03", "2019-01-02", "BARMS", "RC4", barMs);

        List<ProductRateCodeAmsRule> existingProductRateCodeRulesForRateCodes = service.getExistingProductRateCodeRulesForRateCodes(Set.of("RC1", "RC2", "RC3", "RC4"));

        assertEquals(4, existingProductRateCodeRulesForRateCodes.size());
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("BARMS", "RC4", "BAR")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC1", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MS1", "RC2", "IP1")));
        assertTrue(existingProductRateCodeRulesForRateCodes.contains(new ProductRateCodeAmsRule("MSWithSharedRateCode", "RC3", "IP2")));
    }


    private void addProductRateCodeEntry(Integer ip1, String rateCode) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Product_Rate_Code values(" + ip1 + ", '" + rateCode + "', 1, getdate(), 1, getdate(), null)");
    }

    @Test
    void shouldDeleteProductMarketCodeMappings() {
        doNothing().when(independentProductsRepository).deleteOrphanMarketSegmentProductMappings();

        service.deleteIPProductMappings();

        verify(independentProductsRepository).deleteOrphanMarketSegmentProductMappings();
    }

    @Test
    void shouldDeleteProductRateCodeMappings() {
        Product product = ProductBuilder.createIndependentProductProduct("IP1");
        when(independentProductsRepository.getOrphanProductRateCodesForIPProducts()).thenReturn(List.of(
                createProductRateCode("RC1", product),
                createProductRateCode("RC2", product)
        ));

        service.deleteIPProductMappings();

        verify(independentProductsRepository).getOrphanProductRateCodesForIPProducts();
        ArgumentCaptor<Product> productArgumentCaptor = ArgumentCaptor.forClass(Product.class);
        ArgumentCaptor<Set> rateCodesArgumentCaptor = ArgumentCaptor.forClass(Set.class);
        verify(independentProductsService).transferRateCodes(productArgumentCaptor.capture(), eq(null), rateCodesArgumentCaptor.capture());
        assertEquals(product, productArgumentCaptor.getValue());
        List<String> rateCodes = new ArrayList<>(rateCodesArgumentCaptor.getValue());
        rateCodes.sort(String::compareTo);
        assertEquals("RC1", rateCodes.get(0));
        assertEquals("RC2", rateCodes.get(1));
    }

    @Test
    public void shouldGetRateCodesForStraightMarketCodes() {
        inject(service, "tenantCrudService", tenantCrudService());
        Set<String> marketCodes = Set.of("MS1", "MS2", "MS3", "MSP1", "MSP2");
        addAMSOccupancySummaryEntry("MS", "RC1");
        addAMSOccupancySummaryEntry("MS", "RC2");
        addAMSOccupancySummaryEntry("MS2", "RC3");
        addAMSOccupancySummaryEntry("MS2", "RC4");
        addAMSOccupancySummaryEntry("MS3", "RC5");
        addAMSOccupancySummaryEntry("MSP", "RC6");
        addAMSOccupancySummaryEntry("MSP", "RC7");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS", "MS1", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MS2", "MS2", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT", "MS3", "MS3", null, "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MSP", "MSP1", "1", "0");
        addPmsMigrationMappingEntry("MARKET_SEGMENT_NON_GROUP", "MSP", "MSP2", "0", "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC1", "RC1", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC2", "RC2", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC3", "RC3", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC4", "RC4", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC5", "RC5", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC6", "RC6", null, "0");
        addPmsMigrationMappingEntry("RATE_CODE", "RC7", "RC7", null, "0");
        Map<String, Set<String>> newMarketCodeRateCodeForStraightMS = service.getNewMarketCodeRateCodeForStraightMS(marketCodes);
        assertEquals(Set.of("RC1", "RC2"), newMarketCodeRateCodeForStraightMS.get("MS1"));
        assertEquals(Set.of("RC3", "RC4"), newMarketCodeRateCodeForStraightMS.get("MS2"));
        assertEquals(Set.of("RC5"), newMarketCodeRateCodeForStraightMS.get("MS3"));
        assertEquals(Set.of("RC6", "RC7"), newMarketCodeRateCodeForStraightMS.get("MSP1"));

    }

    @Test
    public void shouldBeAbleToGetSharedRateCodesWithMultipleProductsInProductRateCode() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer barLinkedProduct = addProduct("BARlinkedProduct", "AGILE_RATES", "UNFENCED_AND_NO_PACKAGED", "1");
        when(independentProductsService.getAllProducts()).thenReturn(tenantCrudService().findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE));
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC_Shared");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        addProductRateCodeEntry(ip2, "RC_Shared");
        addProductRateCodeEntry(barLinkedProduct, "RC4");
        addProductRateCodeEntry(barLinkedProduct, "RC_Shared");

        List<String> sharedRateCodesWithMultipleProductsInProductRateCode = service.getSharedRateCodesWithMultipleProductsInProductRateCode();

        assertEquals(1, sharedRateCodesWithMultipleProductsInProductRateCode.size());
        assertEquals("RC_Shared", sharedRateCodesWithMultipleProductsInProductRateCode.get(0));
    }

    @Test
    public void shouldBeAbleToGetSharedRateCodesWithMultipleProductsInProductRateCodeWhenBarLinkedProductIsNotAvailable() {
        inject(service, "tenantCrudService", tenantCrudService());
        when(independentProductsService.getPrimaryPricedProduct()).thenReturn(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        Integer ip1 = addProduct("IP1", "INDEPENDENT", "INDEPENDENTLY", "null");
        Integer ip2 = addProduct("IP2", "INDEPENDENT", "INDEPENDENTLY", "null");
        when(independentProductsService.getAllProducts()).thenReturn(tenantCrudService().findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE));
        addProductRateCodeEntry(ip1, "RC1");
        addProductRateCodeEntry(ip1, "RC_Shared");
        addProductRateCodeEntry(ip1, "RC2");
        addProductRateCodeEntry(ip2, "RC3");
        addProductRateCodeEntry(ip2, "RC_Shared");

        List<String> sharedRateCodesWithMultipleProductsInProductRateCode = service.getSharedRateCodesWithMultipleProductsInProductRateCode();

        assertEquals(1, sharedRateCodesWithMultipleProductsInProductRateCode.size());
        assertEquals("RC_Shared", sharedRateCodesWithMultipleProductsInProductRateCode.get(0));
    }

    private ProductRateCode createProductRateCode(String rateCode, Product product) {
        ProductRateCode productRateCode = new ProductRateCode();
        productRateCode.setRateCode(rateCode);
        productRateCode.setProduct(product);
        return productRateCode;
    }

    private static void assertMktSegDetailsDTO(MktSegProductDetailsDTO mktSegProductDetailsDTO, String productName, String marketCode, String mappedMarketCode, String newMarketCode, boolean isDiscontinued) {
        assertEquals(productName, mktSegProductDetailsDTO.getProductName());
        assertEquals(marketCode, mktSegProductDetailsDTO.getMarketCode());
        assertEquals(mappedMarketCode, mktSegProductDetailsDTO.getMappedMarketCode());
        assertEquals(newMarketCode, mktSegProductDetailsDTO.getNewMarketCode());
        assertEquals(isDiscontinued, mktSegProductDetailsDTO.isDiscontinued());
    }

    private void createMktSegProductMappingEntry(String MS1_IP1_USB, Integer IP1) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg_Product_Mapping values('" + MS1_IP1_USB + "'," + IP1 + ",0,0,0,1,null,1,null)");
    }

    public void createMarketSegmentDetails(Integer mktSegId, final int businessType, String qualified, String link) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg_Details values(" + mktSegId + ", " + businessType + ", 3, 2, " + qualified + ", 0, 0, 0, " + link + ", null, 0, 2, 2, 0, 1, '2010-07-19 14:55:55.067', 0, 1, '2010-07-19 14:55:55.067', 1)");
    }

    public Integer addProduct(String product, String code, String type, String dependentProductId) {
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Product VALUES('" + product + "',0,11403,GETDATE(),11403,GETDATE(),'" + code + "','" + type + "'," + dependentProductId + ",'" + product + "',0,null,8,12,0,0,0,0,0,0,1,0,null,1,null,1,1,null,null,1,0,0,0,1,1,null,-1,-1,1,1,-1,-1,0,0,0,0)");
        return getProductId(product);
    }

    private Integer getProductId(String product) {
        BigInteger productId = tenantCrudService().findByNativeQuerySingleResult("select Product_ID from Product where Name = '" + product + "'", new HashMap<>());
        return productId.intValue();
    }

    private int addAMSOccupancySummaryEntry(final String marketCode, final String rateCode) {
        return tenantCrudService().executeUpdateByNativeQuery("insert into Ams_Occupancy_Summary ([Data_Load_Metadata_ID],[Occupancy_DT] ,[Market_Code], [Rate_Code], " +
                "[Block], [Pickup], [Is_Group], [Rooms_Sold], [Room_Revenue], [Reservation_Status], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM]) values(0,'2024-04-28','"
                + marketCode + "', '" + rateCode + "', 0, 0, 0, 10, 100, null, 1, getdate(), 1, getdate())");
    }


}