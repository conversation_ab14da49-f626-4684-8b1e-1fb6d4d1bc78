package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reservation.service.ReservationDataService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@Disabled("slow")
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaTransactionLoadServiceTest extends OperaDataLoadServiceIntegrationTest {

    public static final int PAST_WINDOW_SIZE = 51;
    public static final int FUTURE_WINDOW_SIZE = 365;
    static boolean dataLoadComplete = false;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    PropertyService propertyService;
    @InjectMocks
    @Spy
    OperaTransactionLoadService operaTransactionLoadService = new OperaTransactionLoadService();
    @Spy
    SasDbToolService sasDbToolService;

    @AfterAll
    public static void afterclass() {
        begin();
        deleteExistingData();
        commit();
    }

    private static void commit() {
        CrudServiceBeanExtension.getTenantCrudService().getEntityManager().getTransaction().commit();
    }

    private static void begin() {
        CrudServiceBeanExtension.getTenantCrudService().getEntityManager().getTransaction().begin();
    }

    @Override
    @BeforeEach
    public void setUp() throws Exception {
        initialize(TestProperty.H1);
        if (!dataLoadComplete) {
            dataLoadComplete = true;
            try {
                loadStagingTablesCompleteETL(DEFAULT_CORRELATION_ID);
                preProcessPseudoRoomTransactionsService.operaFilterStageDataService = operaFilterStageDataService;
                commitTransaction(tenantCrudService());
                setUp();
            } catch (Exception e) {
                e.printStackTrace();
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Could not load staging data.", e);
            }
        }
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPERA_POPULATE_DIFFERENTIAL_TRANSACTIONS)).thenReturn(false);
    }

    @Override
    public void initialize(TestProperty property) throws Exception {
        super.initialize(property);
        try {
            setField(operaTransactionLoadService, OperaTransactionLoadService.class.getDeclaredField("crudService"), tenantCrudService());
            setField(operaTransactionLoadService, OperaTransactionLoadService.class.getDeclaredField("pacmanConfigParamsService"), pacmanConfigParamsService);
            setField(operaTransactionLoadService, OperaTransactionLoadService.class.getDeclaredField("propertyService"), propertyService);
            setField(preProcessPseudoRoomTransactionsService, PreProcessPseudoRoomTransactionsService.class.getDeclaredField("crudService"), tenantCrudService());
            setField(preProcessPseudoRoomTransactionsService, PreProcessPseudoRoomTransactionsService.class.getDeclaredField("pacmanConfigParamsService"), pacmanConfigParamsService);
        } catch (Exception e) {
            e.printStackTrace();
            throw new TetrisException("Could not set field");
        }
        when(dateService.getCaughtUpDate()).thenReturn(new LocalDate("2008-09-05").toDate());
    }

    @Test
    public void test_ReservationDataRollUp_WhenPostDepartureRevenueAdjustmentIsFalse() throws Exception {
        operaFilterStageDataService.filterStaging(DEFAULT_CORRELATION_ID);
        operaTransformTransactionDataService.splitReservationsOnMSAndRT(DEFAULT_CORRELATION_ID);
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());

        assertTransactionEquals("21409", new LocalDate(2008, 9, 7), new LocalDate(2008, 9, 10), null, new LocalDate(2008, 6, 15), 1025.00001, 0.00000, 1025.00001, 0, 1);
    }

    @Test
    public void test_ReservationDataRollUp_WhenPostDepartureRevenueAdjustmentIsTrue() throws Exception {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);
        operaFilterStageDataService.filterStaging(DEFAULT_CORRELATION_ID);
        operaTransformTransactionDataService.splitReservationsOnMSAndRT(DEFAULT_CORRELATION_ID);
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());

        assertTransactionEquals("21409", new LocalDate(2008, 9, 7), new LocalDate(2008, 9, 10), null, new LocalDate(2008, 6, 15), 1125, 0.00000, 1125, 0, 1);
    }

    @Test
    public void test_loadTenantDataAmsRebuildDisabled_NoRoomStays() {
        IndividualTransactions exampleTrans = new IndividualTransactions();
        exampleTrans.setReservationIdentifier(String.valueOf(83726));
        final Collection<IndividualTransactions> transactions = tenantCrudService().findByExample(exampleTrans);
        assertEquals(0, transactions.size());
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        assertTransactionEquals("35400", new LocalDate(2008, 9, 1), new LocalDate(2008, 9, 2), null, new LocalDate(2008, 9, 19), 550, 0, 550, 0, 1);
        assertTransactionEquals("27157", new LocalDate(2008, 9, 6), new LocalDate(2008, 9, 8), null, new LocalDate(2008, 6, 6), 336, 0, 336, 0, 1);
    }

    @Test
    public void test_loadAnalyticsMktAccomLosInvData() {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        operaTransactionLoadService.loadOperaAnalyticsTransData(testFileMetadata);
        final int count = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Analytic_Mkt_Accom_Los_Inv", null);
        assertEquals(94, count);
        final List<Object> objects = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv where Arrival_Dt between '2008-09-07' and '2008-09-08' and mkt_seg_id = (select mkt_seg_id from mkt_seg where mkt_seg_code = 'PK')");
        verifyAnalyticMktAccomLosInvResults(objects);
    }

    @Test
    void test_loadAnalyticsMktAccomLosInvDataOptimized() {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        operaTransactionLoadService.loadOperaAnalyticsTransData(testFileMetadata);
        final int count = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Analytic_Mkt_Accom_Los_Inv", null);
        assertEquals(94, count);
        final List<Object> objects = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv where Arrival_Dt between '2008-09-07' and '2008-09-08' and mkt_seg_id = (select mkt_seg_id from mkt_seg where mkt_seg_code = 'PK')");
        verifyAnalyticMktAccomLosInvResults(objects);
    }

    @Test
    public void test_loadAnalyticsMktAccomLosAllPseudoRTData() {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6");
        operaTransactionLoadService.loadOperaAnalyticsTransData(testFileMetadata);
        final int count = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Analytic_Mkt_Accom_Los_Inv", null);
        assertEquals(0, count);
    }

    @Test
    public void test_loadAnalyticsMktAccomLosPseudoRTData() {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        List<Object> accomTypeIDList = tenantCrudService().findByNativeQuery("select DISTINCT TOP(3) ACCOM_TYPE_ID from reservation_night");
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6 where accom_type_id in (:accomTypeIDList)", QueryParameter.with("accomTypeIDList", accomTypeIDList).parameters());
        operaTransactionLoadService.loadOperaAnalyticsTransData(testFileMetadata);
        final int pseudoRTrecords = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Analytic_Mkt_Accom_Los_Inv where accom_type_id in (:accomTypeIDList)", QueryParameter.with("accomTypeIDList", accomTypeIDList).parameters());
        assertEquals(0, pseudoRTrecords);
        assertNotNull(tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv where accom_type_id not in (:accomTypeIDList)", QueryParameter.with("accomTypeIDList", accomTypeIDList).parameters()));
    }

    @Test
    public void test_loadAnalyticsMktAccomLosInvData_OptimizedLoadDisabled() {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        operaTransactionLoadService.loadOperaAnalyticsTransData(testFileMetadata);
        final int count = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Analytic_Mkt_Accom_Los_Inv", null);
        assertEquals(94, count);
        final List<Object> objects = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv where Arrival_Dt between '2008-09-07' and '2008-09-08' and mkt_seg_id = (select mkt_seg_id from mkt_seg where mkt_seg_code = 'PK')");
        verifyAnalyticMktAccomLosInvResults(objects);
    }

    private void verifyAnalyticMktAccomLosInvResults(List<Object> objects) {
        assertEquals(1, objects.size());
        Object[] objs = (Object[]) objects.get(0);
        String expectedResult = "ID_2008-09-08_1_%msId_%atId,2008-09-08,1,%msId,%atId,350.00000,130.00000,0,0,1";
        Integer msId = tenantCrudService().findByNativeQuerySingleResult("select mkt_seg_id from mkt_seg where mkt_seg_code = 'PK'", null);
        Integer atId = tenantCrudService().findByNativeQuerySingleResult("select accom_type_id from accom_type where accom_type_code = 'DLXK'", null);
        final String[] expectedValues = expectedResult.replace("%msId", msId.toString()).replace("%atId", atId.toString()).split(",");
        for (int i = 0; i < objs.length; i++) {
            assertEquals(expectedValues[i], objs[i].toString());
        }
    }

    @Test
    public void test_BookingTime_NULL_Then_NotNull() throws Exception {

        operaFilterStageDataService.filterStaging(DEFAULT_CORRELATION_ID);
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        int numRowsUpdated = operaTransformTransactionDataService.splitReservationsOnMSAndRT(DEFAULT_CORRELATION_ID);
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());

        Time bookingTime = tenantCrudService().findByNativeQuerySingleResult(
                "select distinct Booking_TM from dbo.Individual_Trans where confirmation_no <> '35900'", null, new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(null, bookingTime, "Default Booking Time in Pacman is not NULL");

        String secondCorrelationId = "SECOND_CORRELATION_ID";
        loadFiles("SECOND_CORRELATION_ID");
        tenantCrudService()
                .executeUpdateByNativeQuery(
                        "update opera.History_Transaction set Booking_TM = '01:23:45' where Data_Load_Metadata_ID in (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where Correlation_ID = 'SECOND_CORRELATION_ID')");
        operaLoadRawDataService.loadRawTables(secondCorrelationId, false);
//        tenantCrudService().executeUpdateByNativeQuery("truncate table opera.stage_transaction");
        operaLoadStageDataService.loadStagingTables(secondCorrelationId);
        operaFilterStageDataService.filterStaging(secondCorrelationId);
        operaPreProcessStageDataService.preProcessStageData(secondCorrelationId, true);
        operaTransformTransactionDataService.adjustSharesInTransaction(secondCorrelationId);
        List<Integer> resIdList = tenantCrudService().findByNativeQuery(" select t.Reservation_Name_ID  " +
                "    from " +
                "  ( " +
                "  	select distinct Reservation_Name_ID ,transaction_DT " +
                "  	 from [opera].[Stage_Transaction]  " +
                "  	 where transaction_dt < departure_dt " +
                "  ) as t " +
                "  group by Reservation_Name_ID " +
                "  having COUNT(*) > 1 ");
        tenantCrudService().executeUpdateByNativeQuery("delete from opera.stage_transaction where Reservation_Name_ID in :resIDlist"
                , QueryParameter.with("resIDlist", resIdList).parameters());
        tenantCrudService().executeUpdateByNativeQuery(OperaTransaction.UPDATE_FOREIGN_KEYS_SQL);
        operaBuildRTMSSummaryDataService.buildRTMSSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.transformSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.zeroFillAdjustments(secondCorrelationId);
        operaTransformGroupDataService.transformGroupData(secondCorrelationId);
        tenantCrudService().flushAndClear();
        final FileMetadata testFileMetadata = createTestFileMetadata();
        operaTransactionLoadService.loadTenantDatabase(testFileMetadata);
        bookingTime = tenantCrudService().findByNativeQuerySingleResult("select distinct Booking_TM from dbo.Individual_Trans", null, new RowMapper<Time>() {
            @Override
            public Time mapRow(Object[] row) {
                return (Time) row[0];
            }
        });
        assertEquals(Time.valueOf("01:23:45"), bookingTime, "Booking Time in Pacman is not corrected with second feed");
    }

    // when new extract feed does contain booking time for the reservations
    // then all reservations received in that extract should be updated with corresponding booking time in individual trans table
    @Test
    public void test_BookingTime_MS_RT_Split() throws Exception {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());
        Time bookingTime = tenantCrudService().findByNativeQuerySingleResult(
                "select distinct Booking_TM from dbo.Individual_Trans where confirmation_no <> '35900'", null, new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(null, bookingTime, "Default Booking Time in Pacman is not NULL");

        String secondCorrelationId = "SECOND_CORRELATION_ID";
        loadFiles("SECOND_CORRELATION_ID");
        tenantCrudService()
                .executeUpdateByNativeQuery(
                        "update opera.History_Transaction set Booking_TM = '01:00:59' where confirmation_number = '10665' and Data_Load_Metadata_ID in (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where Correlation_ID = 'SECOND_CORRELATION_ID')");

        tenantCrudService().executeUpdateByNativeQuery("truncate table opera.raw_transaction");
        operaLoadRawDataService.loadRawTables(secondCorrelationId, false);
        operaLoadStageDataService.loadStagingTables(secondCorrelationId);
        tenantCrudService().executeUpdateByNativeQuery("update opera.stage_transaction set cancellation_dt = '0001-01-01' , booking_dt = '1908-09-19' where reservation_name_id = 35400");
        operaFilterStageDataService.filterStaging(secondCorrelationId);
        operaPreProcessStageDataService.preProcessStageData(secondCorrelationId, true);

        bookingTime = tenantCrudService().findByNativeQuerySingleResult("select distinct Booking_TM from opera.stage_transaction where confirmation_number = '10665'",
                null, new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(Time.valueOf("01:00:59"), bookingTime,
                "Booking Time in Pacman is not corrected with second feed for MS RT split");

        operaTransformTransactionDataService.transformTransactionStageData(secondCorrelationId);
        tenantCrudService().flushAndClear();
        tenantCrudService().executeUpdateByNativeQuery(OperaTransaction.UPDATE_FOREIGN_KEYS_SQL);
        operaBuildRTMSSummaryDataService.buildRTMSSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.transformSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.zeroFillAdjustments(secondCorrelationId);
        operaTransformGroupDataService.transformGroupData(secondCorrelationId);

        tenantCrudService().flushAndClear();
        operaTransactionLoadService.loadTenantDatabase(operaLoadTenantDataService.createFileMetadata(secondCorrelationId));
        assertTransactionEquals("35400", new LocalDate(2008, 9, 1), new LocalDate(2008, 9, 2), new LocalDate(2008, 9, 1), new LocalDate(2008, 9, 1), 550, 0, 550, 0, 1);
        bookingTime = tenantCrudService().findByNativeQuerySingleResult(
                "select distinct Booking_TM from dbo.Individual_Trans where confirmation_no <> '10665' and confirmation_no <> '35900'", null,
                new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(null, bookingTime, "Booking Time in Pacman is not corrected with second feed");
    }

    // booking time with split shares
    @Test
    public void test_BookingTimeNotNullSplitShare() throws Exception {
        when(propertyService.getReservationDataVersion()).thenReturn(ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_TABLE_CREATED);
        operaFilterStageDataService.filterStaging(DEFAULT_CORRELATION_ID);
        operaTransformTransactionDataService.splitReservationsOnMSAndRT(DEFAULT_CORRELATION_ID);
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());
        String secondCorrelationId = "SECOND_CORRELATION_ID";
        loadFiles(secondCorrelationId);
        tenantCrudService().executeUpdateByNativeQuery("truncate table opera.raw_transaction");
        tenantCrudService()
                .executeUpdateByNativeQuery(
                        "update opera.History_Transaction set Booking_TM = '01:23:45' where confirmation_number = '71092942' and Data_Load_Metadata_ID in (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where Correlation_ID = 'SECOND_CORRELATION_ID')");
        operaLoadRawDataService.loadRawTables(secondCorrelationId, false);
        operaLoadStageDataService.loadStagingTables(secondCorrelationId);
        operaFilterStageDataService.filterStaging(secondCorrelationId);
        operaPreProcessStageDataService.preProcessStageData(secondCorrelationId, true);
        operaTransformTransactionDataService.transformTransactionStageData(secondCorrelationId);
        tenantCrudService().executeUpdateByNativeQuery(OperaTransaction.UPDATE_FOREIGN_KEYS_SQL);
        operaBuildRTMSSummaryDataService.buildRTMSSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.transformSummaryData(secondCorrelationId);
        operaTransformSummaryDataService.zeroFillAdjustments(secondCorrelationId);
        operaTransformGroupDataService.transformGroupData(secondCorrelationId);
        tenantCrudService().flushAndClear();
        operaTransactionLoadService.loadTenantDatabase(createTestFileMetadata());
        Time bookingTime = tenantCrudService().findByNativeQuerySingleResult(
                "select distinct Booking_TM from dbo.Individual_Trans where confirmation_no = '71092942'", null, new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(Time.valueOf("01:23:45"), bookingTime, "Shares split with incorrect booking time");
        bookingTime = tenantCrudService().findByNativeQuerySingleResult(
                "select distinct Booking_TM from dbo.Individual_Trans where confirmation_no = '71092940'", null, new RowMapper<Time>() {
                    @Override
                    public Time mapRow(Object[] row) {
                        return (Time) row[0];
                    }
                });
        assertEquals(null, bookingTime, "Shares split with incorrect booking time");
    }

    @Test
    void testNoNewTransactions() {
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(createTestFileMetadata());
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        assertEquals(4, number,
                "NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP return 4 as result -> 1(drops #reservation_night) + 1(inserts in #reservation_night) + 1(deletion from #reservation_night) + 1(actual data entry in Analytic_Mkt_Accom_Los_Inv)");
    }
    @Test
    public void testNoNewTransactionsLDBNotEnabled() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        FileMetadata fileMetadata = createMockFileMetadata(lastDayOfLastMonth);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(2);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).and("totalRateEnabled", 2).parameters())).thenReturn(1);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(lastDayOfLastMonth);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(0)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).and("totalRateEnabled", 2).parameters());
        assertEquals(1, number);
    }

    @Test
    public void testNoNewTransactionsLDBNotEnabledOptimizeOperaLoadSasSQLEnabled() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        FileMetadata fileMetadata = createMockFileMetadata(lastDayOfLastMonth);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(2);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).
                        and("totalRateEnabled", 2).parameters())).thenReturn(1);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(lastDayOfLastMonth);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(0)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).
                        and("totalRateEnabled", 2).parameters());
        assertEquals(1, number);
    }

    @Test
    void testNoNewTransactionsLDBNotEnabledOptimizePopulateAnalyticMktLosInvEnabled() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        FileMetadata fileMetadata = createMockFileMetadata(lastDayOfLastMonth);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).
                        and("totalRateEnabled", 0).parameters())).thenReturn(1);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(lastDayOfLastMonth);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(0)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).
                        and("totalRateEnabled", 0).parameters());
        assertEquals(1, number);
    }


    @Test
    public void testLoadTransLDBEnabledFirstSnapshotDtAfterEarliestArrivalDate() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        Date firstSnapshotDate = lastDayOfLastMonth;
        FileMetadata fileMetadata = createMockFileMetadata(firstSnapshotDate);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.TRUE);
        CrudService mockCrudService = mock(CrudService.class);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(2);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", firstSnapshotDate).and("totalRateEnabled", 2).parameters())).thenReturn(1);
        when(mockCrudService.findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB)).thenReturn(firstSnapshotDate);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(earliestArrivalDate);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(1)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", firstSnapshotDate).and("totalRateEnabled", 2).parameters());
        assertEquals(1, number);
    }

    @Test
    public void testLoadTransLDBEnabledFirstSnapshotDtBeforeEarliestArrivalDate() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        Date firstSnapshotDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -(PAST_WINDOW_SIZE * 2));
        FileMetadata fileMetadata = createMockFileMetadata(lastDayOfLastMonth);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.TRUE);
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(2);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(earliestArrivalDate);
        when(mockCrudService.findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB)).thenReturn(firstSnapshotDate);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).and("totalRateEnabled", 2).parameters())).thenReturn(1);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(1)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestArrivalDate).and("totalRateEnabled", 2).parameters());
        assertEquals(1, number);
    }

    @Test
    public void testLoadTransLDBEnabledEarliestOccDateBeforeEarliestArrivalDate() {
        Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth();
        Date earliestArrivalDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -PAST_WINDOW_SIZE);
        Date earliestOccDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -(PAST_WINDOW_SIZE + 5));
        Date firstSnapshotDate = DateUtil.addDaysToDate(lastDayOfLastMonth, -(PAST_WINDOW_SIZE * 2));
        FileMetadata fileMetadata = createMockFileMetadata(lastDayOfLastMonth);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.TRUE);
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.executeUpdateByNativeQuery(anyString())).thenReturn(5);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(2);
        when(mockCrudService.findByNativeQuerySingleResult(FIND_MIN_ARRIVAL_DATE_FOR_FILE_METADATA,
                QueryParameter.with("fileMetadataId", fileMetadata.getId()).parameters())).thenReturn(earliestOccDate);
        when(mockCrudService.findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB)).thenReturn(firstSnapshotDate);
        when(mockCrudService.executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestOccDate).and("totalRateEnabled", 2).parameters())).thenReturn(1);
        operaTransactionLoadService.crudService = mockCrudService;
        int number = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED);
        verify(mockCrudService, times(1)).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(NEW_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", earliestOccDate).and("totalRateEnabled", 2).parameters());
        assertEquals(1, number);
    }

    // Utility Methods
    protected IndividualTransactions findExistingTransaction(String reservationIdentifier, LocalDate arrivalDate) {
        IndividualTransactions existingIndividualTransaction = tenantCrudService().findByNamedQuerySingleResult(
                IndividualTransactions.FIND_BY_PROPERTY_ARRIVAL_AND_RESERVATION,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("arrivalDate", arrivalDate.toDate())
                        .and("reservationIdentifier", reservationIdentifier).parameters());
        return existingIndividualTransaction;
    }

    public String getAllColumnsintoSingleLine(Object[] dataRow) {
        String resultsRow = dataRow[0] + "," + dataRow[1] + "," + dataRow[2] + "," + dataRow[3] + "," + dataRow[4] + "," + dataRow[5] + "," + dataRow[6] + ","
                + dataRow[7] + "," + dataRow[8] + "," + dataRow[9] + "," + dataRow[10] + "," + dataRow[11] + "," + dataRow[12] + dataRow[13] + ","
                + dataRow[14] + "," + dataRow[15] + "," + dataRow[16] + "," + dataRow[17] + "," + dataRow[18] + "," + dataRow[19] + "," + dataRow[20] + ","
                + dataRow[21] + "," + dataRow[22] + "," + dataRow[23] + "," + dataRow[24] + "," + dataRow[25] + "," + dataRow[26];
        return resultsRow;
    }

    /*
     * Asserts that the individual trans record with the given reservation ID and arrival date has
     * the same values for the parameters passed in.
     */
    protected void assertTransactionEquals(String reservationIdentifier, LocalDate arrivalDate, LocalDate expectedDepartureDate,
                                           LocalDate expectedCancellationDt, LocalDate expectedBookingDt, double expectedRoomRevenue,
                                           double expectedFoodRevenue, double expectedTotalRevenue, int expectedNumberOfChildren, int expectedNumberOfAdults) {

        IndividualTransactions existingTransaction = findExistingTransaction(reservationIdentifier, arrivalDate);
        assertNotNull(existingTransaction);
        assertEquals(expectedDepartureDate.toDate().getTime(), existingTransaction.getDepartureDate().getTime());
        if (null != expectedCancellationDt) {
            assertEquals(expectedCancellationDt.toDate().getTime(), existingTransaction.getCancellationDate().getTime());
        }
        assertEquals(expectedBookingDt.toDate().getTime(), existingTransaction.getBookingDate().getTime());
        assertEquals(expectedRoomRevenue, existingTransaction.getRoomRevenue().doubleValue(), 0.00001);
        assertEquals(expectedFoodRevenue, existingTransaction.getFoodRevenue().doubleValue(), 0.00001);
        assertEquals(expectedTotalRevenue, existingTransaction.getTotalRevenue().doubleValue(), 0.00001);
        assertEquals(expectedNumberOfChildren, existingTransaction.getNumberChildren().intValue());
        assertEquals(expectedNumberOfAdults, existingTransaction.getNumberAdults().intValue());
    }

    protected FileMetadata createMockFileMetadata(Date snapshotDate) {
        FileMetadata fileMetadata = Mockito.mock(FileMetadata.class);
        when(fileMetadata.getFileName()).thenReturn("OperaDataLoad");
        when(fileMetadata.getRecordTypeId()).thenReturn(RecordType.T2SNAP_RECORD_TYPE_ID);
        when(fileMetadata.getSnapshotDt()).thenReturn(snapshotDate);
        when(fileMetadata.getSnapshotTm()).thenReturn(snapshotDate);
        when(fileMetadata.getPreparedDt()).thenReturn(snapshotDate);
        when(fileMetadata.getPreparedTm()).thenReturn(snapshotDate);
        when(fileMetadata.getFileLocation()).thenReturn("localhost");
        when(fileMetadata.getTenantPropertyId()).thenReturn(PacmanWorkContextHelper.getPropertyId());
        when(fileMetadata.getProcessStatusId()).thenReturn(ProcessStatus.SUCCESSFUL);
        when(fileMetadata.getPastWindowSize()).thenReturn(PAST_WINDOW_SIZE);
        when(fileMetadata.getFutureWindowSize()).thenReturn(FUTURE_WINDOW_SIZE);
        return fileMetadata;
    }

    @Test
    public void testMarketCode() {

        String dataLoadMetadata = "" +
                "INSERT INTO [opera].[Data_Load_Metadata]\n" +
                "           ([Correlation_ID]\n" +
                "           ,[Incoming_File_Type_Code]\n" +
                "           ,[Create_DT])\n" +
                "     VALUES\n" +
                "           ('777'\n" +
                "           ,'777'\n" +
                "           ,GETDATE())\n";

        String stageTransaction = "" +
                "\n" +
                "INSERT INTO [opera].[Stage_Transaction]\n" +
                "           ([Confirmation_Number]\n" +
                "           ,[Reservation_Status]\n" +
                "           ,[Is_Shared]\n" +
                "           ,[Sharers]\n" +
                "           ,[Transaction_DT]\n" +
                "           ,[Arrival_DT]\n" +
                "           ,[Departure_DT]\n" +
                "           ,[Checkout_DT]\n" +
                "           ,[Cancellation_DT]\n" +
                "           ,[Booking_DT]\n" +
                "           ,[Rate_Code]\n" +
                "           ,[Rate_Amount]\n" +
                "           ,[Market_Code]\n" +
                "           ,[Room]\n" +
                "           ,[Room_Revenue]\n" +
                "           ,[Food_Beverage_Revenue]\n" +
                "           ,[Other_Revenue]\n" +
                "           ,[Total_Revenue]\n" +
                "           ,[Room_Type]\n" +
                "           ,[Source_Code]\n" +
                "           ,[Channel]\n" +
                "           ,[Booked_Room_Type]\n" +
                "           ,[Nationality]\n" +
                "           ,[Reservation_Type]\n" +
                "           ,[Number_Children]\n" +
                "           ,[Number_Adults]\n" +
                "           ,[Reservation_Name_ID]\n" +
                "           ,[Mkt_Seg_ID]\n" +
                "           ,[Accom_Type_ID]\n" +
                "           ,[Booked_Accom_Type_ID]\n" +
                "           ,[Data_Load_Metadata_ID]\n" +
                "           ,[Booking_TM]\n" +
                "           ,[Rate_Category])\n" +
                "     VALUES\n" +
                "           ('777'\n" +
                "           ,'XX'\n" +
                "           ,1\n" +
                "           ,'1'\n" +
                "           ,DATEADD(MONTH, -1, GETDATE())\n" +
                "           ,GETDATE()\n" +
                "           ,GETDATE()\n" +
                "           ,GETDATE()\n" +
                "           ,GETDATE()\n" +
                "           ,GETDATE()\n" +
                "           ,'BAR'\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,'AAA_QY'\n" +
                "           ,'room'\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,'room_type'\n" +
                "           ,'source_type'\n" +
                "           ,'channel'\n" +
                "           ,'booked_room_type'\n" +
                "           ,'CA'\n" +
                "           ,'reservation_type'\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,CAST('100.00000' AS DECIMAL(19,5))\n" +
                "           ,1\n" +
                "           ,1\n" +
                "           ,(select max(Accom_Type_Id) from [dbo].[Accom_Type])\n" +
                "           ,(select max(Accom_Type_Id) from [dbo].[Accom_Type])\n" +
                "           ,(select max(Data_Load_Metadata_ID) from [opera].[Data_Load_Metadata])\n" +
                "           ,GETDATE()\n" +
                "           ,'Rate_Category')\n" +
                "\n";

        String analyticalMktSeg = ""
                + "INSERT INTO [dbo].[Analytical_Mkt_Seg] "
                + "           ([Market_Code] "
                + "           ,[Mapped_Market_Code] "
                + "           ,[Attribute] "
                + "           ,[Created_By_User_ID] "
                + "           ,[Created_DTTM] "
                + "           ,[Last_Updated_By_User_ID] "
                + "           ,[Last_Updated_DTTM] "
                + "           ,[Rate_Code_Type] "
                + "           ,[Rank]) "
                + "     VALUES "
                + "           ('AAA' "
                + "           ,'AAA_QY' "
                + "           ,'QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE' "
                + "           ,16776 "
                + "           ,GETDATE() "
                + "           ,16776 "
                + "           ,GETDATE() "
                + "           ,'DEFAULT' "
                + "           ,10)";

        tenantCrudService().executeUpdateByNativeQuery(dataLoadMetadata);
        tenantCrudService().executeUpdateByNativeQuery(stageTransaction);

        QueryParameter params = QueryParameter.with("propertyId", 5)
                .and("fileMetaDataId", 3)
                .and("businessStartDate", LocalDate.now().minusDays(900).toDate());

        tenantCrudService().executeUpdateByNativeQuery(INSERT_RESERVATION_NIGHTS_IN_WINDOW, params.parameters());

        List<Object> o = tenantCrudService().findByNativeQuery("select * from dbo.reservation_night where [confirmation_no] = '777'");
        assertEquals("AAA_QY", ((Object[]) o.get(0))[34]);

        tenantCrudService().executeUpdateByNativeQuery(analyticalMktSeg);

        params = QueryParameter.with("propertyId", 5)
                .and("fileMetaDataId", 3)
                .and("businessStartDate", LocalDate.now().minusDays(900).toDate());

        tenantCrudService().executeUpdateByNativeQuery("delete from dbo.reservation_night");
        tenantCrudService().executeUpdateByNativeQuery(INSERT_RESERVATION_NIGHTS_IN_WINDOW, params.parameters());

        o = tenantCrudService().findByNativeQuery("select * from dbo.reservation_night where [confirmation_no] = '777'");
        assertEquals("AAA", ((Object[]) o.get(0))[34]);

        analyticalMktSeg = ""
                + "INSERT INTO [dbo].[Analytical_Mkt_Seg] "
                + "           ([Market_Code] "
                + "           ,[Rate_Code] "
                + "           ,[Mapped_Market_Code] "
                + "           ,[Attribute] "
                + "           ,[Created_By_User_ID] "
                + "           ,[Created_DTTM] "
                + "           ,[Last_Updated_By_User_ID] "
                + "           ,[Last_Updated_DTTM] "
                + "           ,[Rate_Code_Type] "
                + "           ,[Rank]) "
                + "     VALUES "
                + "           ('AAA' "
                + "           ,'BAR' "
                + "           ,'AAA_QY' "
                + "           ,'QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE' "
                + "           ,16776 "
                + "           ,GETDATE() "
                + "           ,16776 "
                + "           ,GETDATE() "
                + "           ,'EQUALS' "
                + "           ,10)";

        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Analytical_Mkt_Seg]");
        tenantCrudService().executeUpdateByNativeQuery(analyticalMktSeg);

        tenantCrudService().executeUpdateByNativeQuery("delete from dbo.reservation_night");
        tenantCrudService().executeUpdateByNativeQuery(INSERT_RESERVATION_NIGHTS_IN_WINDOW, params.parameters());

        o = tenantCrudService().findByNativeQuery("select * from dbo.reservation_night where [confirmation_no] = '777'");
        assertEquals("AAA", ((Object[]) o.get(0))[34]);

    }

    @Test
    public void test_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP_when_AllInclusiveRatesEnabled() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from Reservation_Night where individual_trans_id = 1", null);
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 1).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        assertEquals(date, result.get(0)[1]);
        assertEquals(2, result.get(0)[2]);
        assertEquals(1, result.get(0)[3]);
        assertEquals(4, result.get(0)[4]);
        assertEquals(new BigDecimal(90).setScale(5), result.get(0)[5]);
        assertEquals(new BigDecimal(60).setScale(5), result.get(0)[6]);
        assertEquals(1, result.get(0)[7]);
        assertEquals(0, result.get(0)[8]);
        assertEquals(1, result.get(0)[9]);
    }

    @Test
    public void test_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from Reservation_Night where individual_trans_id = 1", null);
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        assertEquals(date, result.get(0)[1]);
        assertEquals(2, result.get(0)[2]);
        assertEquals(1, result.get(0)[3]);
        assertEquals(4, result.get(0)[4]);
        assertEquals(new BigDecimal(90).setScale(5), result.get(0)[5]);
        assertEquals(new BigDecimal(70).setScale(5), result.get(0)[6]);
        assertEquals(1, result.get(0)[7]);
        assertEquals(0, result.get(0)[8]);
        assertEquals(1, result.get(0)[9]);
    }

    @Test
    public void test_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP_With_NS() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from Reservation_Night where individual_trans_id = 1", null);
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set individual_status = 'NS' where individual_trans_id in (1,2)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(90).setScale(5), object1[5]);
        assertEquals(new BigDecimal(70).setScale(5), object1[6]);
        assertEquals(0, object1[7]);
        assertEquals(1, object1[8]);
        assertEquals(1, object1[9]);
    }

    @Test
    public void test_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP_With_NS_XX() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from Reservation_Night where individual_trans_id = 1", null);
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set individual_status = 'NS' where individual_trans_id in (3,4)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(0).setScale(5), object1[5]);
        assertEquals(new BigDecimal(0).setScale(5), object1[6]);
        assertEquals(1, object1[7]);
        assertEquals(1, object1[8]);
        assertEquals(0, object1[9]);
    }

    @Test
    public void test_LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP_With_CO_XX() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from Reservation_Night where individual_trans_id = 1", null);
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set mkt_seg_id = 2 where individual_trans_id in (1,2)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(2, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(90).setScale(5), result.get(0)[5]);
        assertEquals(new BigDecimal(70).setScale(5), result.get(0)[6]);
        assertEquals(0, object1[7]);
        assertEquals(0, object1[8]);
        assertEquals(1, object1[9]);

        Object[] object2 = result.get(1);
        assertEquals(date, object2[1]);
        assertEquals(2, object2[2]);
        assertEquals(2, object2[3]);
        assertEquals(4, object2[4]);
        assertEquals(new BigDecimal(0).setScale(5), object2[5]);
        assertEquals(new BigDecimal(0).setScale(5), object2[6]);
        assertEquals(1, object2[7]);
        assertEquals(0, object2[8]);
        assertEquals(0, object2[9]);
    }

    @Test
    public void test_FIND_IN_DATE_RANGE_INSERT_INTO_TABLE_QUERY() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from individual_trans where individual_trans_id = 2", null);
        tenantCrudService().executeUpdateByNativeQuery(FIND_IN_DATE_RANGE_INSERT_INTO_TABLE_QUERY,
                QueryParameter.with("minimumDate", date).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        assertEquals(date, result.get(0)[1]);
        assertEquals(2, result.get(0)[2]);
        assertEquals(1, result.get(0)[3]);
        assertEquals(4, result.get(0)[4]);
        assertEquals(new BigDecimal(90).setScale(5), result.get(0)[5]);
        assertEquals(new BigDecimal(70).setScale(5), result.get(0)[6]);
        assertEquals(1, result.get(0)[7]);
        assertEquals(0, result.get(0)[8]);
        assertEquals(1, result.get(0)[9]);
    }

    @Test
    public void test_FIND_IN_DATE_RANGE_INSERT_INTO_TABLE_QUERY_With_NS() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from individual_trans where individual_trans_id = 2", null);
        tenantCrudService().executeUpdateByNativeQuery("update individual_trans set individual_status = 'NS' where individual_trans_id in (2)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(90).setScale(5), object1[5]);
        assertEquals(new BigDecimal(70).setScale(5), object1[6]);
        assertEquals(0, object1[7]);
        assertEquals(1, object1[8]);
        assertEquals(1, object1[9]);
    }

    @Test
    public void test_FIND_IN_DATE_RANGE_INSERT_INTO_TABLE_QUERY_With_NS_XX() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from individual_trans where individual_trans_id = 2", null);
        tenantCrudService().executeUpdateByNativeQuery("update individual_trans set individual_status = 'NS' where individual_trans_id in (4)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(1, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(0).setScale(5), object1[5]);
        assertEquals(new BigDecimal(0).setScale(5), object1[6]);
        assertEquals(1, object1[7]);
        assertEquals(1, object1[8]);
        assertEquals(0, object1[9]);
    }

    @Test
    public void test_FIND_IN_DATE_RANGE_INSERT_INTO_TABLE_QUERY_With_CO_XX() {
        operaTransactionLoadService.crudService = tenantCrudService();
        Date date = tenantCrudService().findByNativeQuerySingleResult("select arrival_DT from individual_trans where individual_trans_id = 2", null);
        tenantCrudService().executeUpdateByNativeQuery("update individual_trans set mkt_seg_id = 2 where individual_trans_id in (2)");
        tenantCrudService().executeUpdateByNativeQuery(LOAD_ANALYTIC_MKT_ACCOM_LOS_WITH_SP,
                QueryParameter.with("minimumDate", date).
                        and("totalRateEnabled", 0).parameters());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from Analytic_Mkt_Accom_Los_Inv");
        assertEquals(2, result.size());
        Object[] object1 = result.get(0);
        assertEquals(date, object1[1]);
        assertEquals(2, object1[2]);
        assertEquals(1, object1[3]);
        assertEquals(4, object1[4]);
        assertEquals(new BigDecimal(90).setScale(5), result.get(0)[5]);
        assertEquals(new BigDecimal(70).setScale(5), result.get(0)[6]);
        assertEquals(0, object1[7]);
        assertEquals(0, object1[8]);
        assertEquals(1, object1[9]);

        Object[] object2 = result.get(1);
        assertEquals(date, object2[1]);
        assertEquals(2, object2[2]);
        assertEquals(2, object2[3]);
        assertEquals(4, object2[4]);
        assertEquals(new BigDecimal(0).setScale(5), object2[5]);
        assertEquals(new BigDecimal(0).setScale(5), object2[6]);
        assertEquals(1, object2[7]);
        assertEquals(0, object2[8]);
        assertEquals(0, object2[9]);
    }

}
