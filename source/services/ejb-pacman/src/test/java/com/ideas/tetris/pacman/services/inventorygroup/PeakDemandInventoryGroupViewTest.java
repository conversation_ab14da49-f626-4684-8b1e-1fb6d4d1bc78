package com.ideas.tetris.pacman.services.inventorygroup;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupCreator;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupDetailsCreator;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class PeakDemandInventoryGroupViewTest extends AbstractG3JupiterTest {

    @Test
    public void shouldBeAbleToGetTotalPeakDemandByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "0.0", "5.00", "0.00");
        setAccomActivityBy(today, "150", "5", "5", "15", "900.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "0.0", "3.00", "0.00");
        setAccomActivityBy(today.plusDays(1), "150", "5", "5", "20", "900.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("70"), todaysData[2]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("75"), nextDayData[2]);
    }

    @Test
    public void shouldBeAbleToGetTotalRemainingDemandByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "0.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "3.0", "3.00", "0.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("25.00"), todaysData[3]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("15.00"), nextDayData[3]);
    }

    @Test
    public void shouldBeAbleToGetTotalUserRemainingDemandByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "3.0", "3.00", "4.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("30.00"), todaysData[4]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("20.00"), nextDayData[4]);
    }

    @Test
    public void shouldBeAbleToGetPeakDemandPercentageByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setAccomActivityBy(today, "100", "5", "5", "15", "900.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "3.0", "3.00", "4.00");
        setAccomActivityBy(today.plusDays(1), "150", "5", "5", "20", "900.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("23.333300"), todaysData[5]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("16.666600"), nextDayData[5]);
    }

    @Test
    public void shouldBeAbleToGetUserPeakDemandPercentageByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setAccomActivityBy(today, "100", "5", "5", "15", "900.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "3.0", "3.00", "4.00");
        setAccomActivityBy(today.plusDays(1), "150", "5", "5", "20", "900.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("25.000000"), todaysData[6]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("17.777700"), nextDayData[6]);
    }

    @Test
    public void shouldBeAbleToGetUserPeakDemandPercentageByActualCapacityByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setAccomActivityBy(today, "100", "5", "5", "15", "900.00");
        setOccupancyDemandFcstBy(today.plusDays(1), "3.0", "3.00", "4.00");
        setAccomActivityBy(today.plusDays(1), "150", "5", "5", "20", "900.00");
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from Vw_Peak_Demand_Inventory_Group where Inventory_Group_Id = " + inventoryGroup.getId());
        //THEN
        Object[] todaysData = findDataFor(today, results);
        Assertions.assertEquals(today, LocalDate.fromDateFields((Date) todaysData[1]));
        Assertions.assertEquals(new BigDecimal("27.777700"), todaysData[7]);
        Object[] nextDayData = findDataFor(today.plusDays(1), results);
        Assertions.assertEquals(today.plusDays(1), LocalDate.fromDateFields((Date) nextDayData[1]));
        Assertions.assertEquals(new BigDecimal("19.047600"), nextDayData[7]);
    }

    private Object[] findDataFor(LocalDate today, List<Object[]> results) {
        return results.stream().filter(result -> LocalDate.fromDateFields((Date) result[1]).compareTo(today) == 0).findFirst().get();
    }

    private void setOccupancyDemandFcstBy(LocalDate occupancyDate, String remainingDemand, String peakDemand, String userRemainingDemand) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_Demand_FCST set Peak_Demand = '" + peakDemand
                + "', Remaining_Demand = '" + remainingDemand + "', User_Remaining_Demand = '" + userRemainingDemand + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setAccomActivityBy(LocalDate occupancyDate, String capacity, String roomsNotAvailMaint, String roomsNotAvailOther, String roomsSolds, String roomRevenue) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Activity set Accom_Capacity = " + capacity + ", Rooms_Sold = " + roomsSolds +
                ", Rooms_Not_Avail_Maint = " + roomsNotAvailMaint + ", Rooms_Not_Avail_Other = " + roomsNotAvailOther + ", Room_Revenue = '" + roomRevenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

}
