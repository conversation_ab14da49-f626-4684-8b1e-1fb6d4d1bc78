package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.MeetingPackageBaseProductPricingOverridesDTO;
import com.ideas.tetris.pacman.services.datafeed.entity.MeetingPackageBaseProductPricingOverrides;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.SystemDates;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MeetingPackageBaseProductPricingOverridesServiceTest {

    private static final DatafeedRequest REQUEST = new DatafeedRequest();

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private DateService dateService;

    @InjectMocks
    private MeetingPackageBaseProductPricingOverridesService service;

    @BeforeEach
    public void setUp() {
        TimeZone mockTimeZone = TimeZone.getTimeZone("CDT");
        SystemDates mockSystemDate = mock(SystemDates.class);
        when(mockSystemDate.getTimeZone()).thenReturn(mockTimeZone);
        when(dateService.getSystemDates()).thenReturn(mockSystemDate);
    }

    @Test
    void getMeetingPackageBaseProductPricingOverrides_whenFloorAndCeilOverride() {
        MeetingPackageBaseProductPricingOverrides entity = createOverride("FLOORANDCEIL", "100.0,200.0", "50.0");

        when(tenantCrudService.findByNamedQuery(any(), anyMap(), anyInt(), anyInt())).thenReturn(Collections.singletonList(entity));

        List<MeetingPackageBaseProductPricingOverridesDTO> result = service.getMeetingPackagePricingOverrideDto(REQUEST);

        assertEquals(2, result.size());
        assertEquals("Floor", result.get(0).getOverrideType());
        assertEquals(100.00, result.get(0).getOverrideRoomRental());
        assertEquals(150.0, result.get(0).getTotalPackageOverridePrice());
        assertEquals("Ceiling", result.get(1).getOverrideType());
        assertEquals(200.00, result.get(1).getOverrideRoomRental());
        assertEquals(250.0, result.get(1).getTotalPackageOverridePrice());
    }

    @Test
    void getMeetingPackageBaseProductPricingOverrides_whenFloorOverride() {
        MeetingPackageBaseProductPricingOverrides entity = createOverride("FLOOR", "100.0", "30.0");

        when(tenantCrudService.findByNamedQuery(any(), anyMap(), anyInt(), anyInt())).thenReturn(Collections.singletonList(entity));

        List<MeetingPackageBaseProductPricingOverridesDTO> result = service.getMeetingPackagePricingOverrideDto(REQUEST);
        assertEquals(1, result.size());
        assertEquals("Floor", result.get(0).getOverrideType());
        assertEquals(100.00, result.get(0).getOverrideRoomRental());
        assertEquals(130.0, result.get(0).getTotalPackageOverridePrice());
    }

    @Test
    void getMeetingPackageBaseProductPricingOverrides_whenCeilOverride() {
        MeetingPackageBaseProductPricingOverrides entity = createOverride("CEIL", "140.0", "20.0");

        when(tenantCrudService.findByNamedQuery(any(), anyMap(), anyInt(), anyInt())).thenReturn(Collections.singletonList(entity));

        List<MeetingPackageBaseProductPricingOverridesDTO> result = service.getMeetingPackagePricingOverrideDto(REQUEST);
        assertEquals(1, result.size());
        assertEquals("Ceiling", result.get(0).getOverrideType());
        assertEquals(160.0, result.get(0).getTotalPackageOverridePrice());
    }

    @Test
    void getMeetingPackageBaseProductPricingOverrides_whenUserOverride() {
        MeetingPackageBaseProductPricingOverrides entity = createOverride("USER", "80.0", "15.0");

        when(tenantCrudService.findByNamedQuery(any(), anyMap(), anyInt(), anyInt())).thenReturn(Collections.singletonList(entity));

        List<MeetingPackageBaseProductPricingOverridesDTO> result = service.getMeetingPackagePricingOverrideDto(REQUEST);
        assertEquals(1, result.size());
        assertEquals("Specific", result.get(0).getOverrideType());
        assertEquals(95.0, result.get(0).getTotalPackageOverridePrice());
    }

    @Test
    void shouldReturnEmptyList_whenNoConfigsArePresent() {
        when(tenantCrudService.findByNamedQuery(any(), anyMap(), anyInt(), anyInt())).thenReturn(Collections.emptyList());
        List<MeetingPackageBaseProductPricingOverridesDTO> result = service.getMeetingPackagePricingOverrideDto(REQUEST);
        assertTrue(result.isEmpty());
    }

    private MeetingPackageBaseProductPricingOverrides createOverride(String overrideType, String roomRentalPrice, String totalOffset) {
        MeetingPackageBaseProductPricingOverrides entity = new MeetingPackageBaseProductPricingOverrides();
        entity.setOverridePrice(roomRentalPrice);
        entity.setTotalPackagePrice(totalOffset);
        entity.setOccupancyDate(Date.valueOf("2024-01-10"));
        entity.setProductName("Product A");
        entity.setMeetingRoom("Room A");
        entity.setOverrideType(overrideType);
        entity.setOverrideLastModifiedOn("2024-01-05 10:30:00");
        entity.setOverrideLastModifiedBy("<EMAIL>");
        return entity;
    }
}