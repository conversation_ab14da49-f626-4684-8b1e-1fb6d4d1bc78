package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingDigit;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrettyPricingRuleRow;
import com.ideas.tetris.pacman.services.datafeed.dto.CPBARRoundingRulesConfiguration;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CPBarRoundingRulesConfigurationServiceTest {

    @Mock
    PrettyPricingService prettyPricingServiceMock;

    @Mock
    private CrudService crudService;

    @InjectMocks
    Product product = new Product();

    @InjectMocks
    Product independentProduct = new Product();

    @InjectMocks
    CPBarRoundingRulesConfigurationService cpBarRoundingRulesConfigurationService;

    @BeforeEach
    public void setUp() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        product.setId(1);
        product.setName("BAR");
        product.setType(Product.BAR);
        independentProduct.setId(24);
        independentProduct.setType(Product.INDEPENDENT_PRODUCT_CODE);
        independentProduct.setName("INDEPENDENT");
        when(crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_INDEPENDENT_PRODUCTS)).thenReturn(asList(product, independentProduct));
    }

    @Test
    public void testCPBarRoundingRulesConfiguration() {
        insertProductDefinitionData("INDEPENDENT", Product.INDEPENDENT_PRODUCT_CODE);
        PricingRule pricingRule = new PricingRule();
        PricingDigit pricingDigit = PricingDigit.TEN_THOUSANDS;
        PrettyPricingRuleRow prettyPricingRuleRow = new PrettyPricingRuleRow();
        prettyPricingRuleRow.setRuleNumbers("1,2,3");
        pricingRule.addRule(pricingDigit, prettyPricingRuleRow);
        when(prettyPricingServiceMock.getPricingRule(1)).thenReturn(pricingRule);

        PricingRule pricingRuleIndep = new PricingRule();
        PricingDigit pricingDigitIndep = PricingDigit.TEN_THOUSANDS;
        PrettyPricingRuleRow prettyPricingRuleRowIndep = new PrettyPricingRuleRow();
        prettyPricingRuleRowIndep.setRuleNumbers("1,2,3,4");
        pricingRuleIndep.addRule(pricingDigitIndep, prettyPricingRuleRowIndep);
        when(prettyPricingServiceMock.getPricingRule(24)).thenReturn(pricingRuleIndep);

        List<CPBARRoundingRulesConfiguration> actual = cpBarRoundingRulesConfigurationService.getCPBARRoundingRulesConfiguration();
        CPBARRoundingRulesConfiguration cpbarRoundingRulesConfigurationActual = actual.get(0);
        CPBARRoundingRulesConfiguration cpbarRoundingRulesConfigurationActualIndep = actual.get(1);
        assertEquals(prettyPricingRuleRow.getRuleNumbers(), cpbarRoundingRulesConfigurationActual.getTenThousands());
        assertEquals(prettyPricingRuleRowIndep.getRuleNumbers(), cpbarRoundingRulesConfigurationActualIndep.getTenThousands());
    }

    @Test
    public void testCPBarRoundingRulesConfigurationWhenAllPricingDigitAreChecked() {
        insertProductDefinitionData("INDEPENDENT", Product.INDEPENDENT_PRODUCT_CODE);

        PricingRule pricingRule = new PricingRule();
        PricingDigit pricingDigit = PricingDigit.TEN_THOUSANDS;
        PrettyPricingRuleRow prettyPricingRuleRow = new PrettyPricingRuleRow();
        pricingRule.addRule(pricingDigit, prettyPricingRuleRow);
        when(prettyPricingServiceMock.getPricingRule(1)).thenReturn(pricingRule);

        PricingRule pricingRuleIndep = new PricingRule();
        PricingDigit pricingDigitIndep = PricingDigit.TEN_THOUSANDS;
        PrettyPricingRuleRow prettyPricingRuleRowIndep = new PrettyPricingRuleRow();
        pricingRuleIndep.addRule(pricingDigitIndep, prettyPricingRuleRowIndep);
        when(prettyPricingServiceMock.getPricingRule(24)).thenReturn(pricingRuleIndep);

        List<CPBARRoundingRulesConfiguration> actual = cpBarRoundingRulesConfigurationService.getCPBARRoundingRulesConfiguration();
        CPBARRoundingRulesConfiguration cpbarRoundingRulesConfigurationActual = actual.get(0);
        CPBARRoundingRulesConfiguration cpbarRoundingRulesConfigurationActualIndep = actual.get(1);
        assertEquals(prettyPricingRuleRow.getRuleNumbers(), cpbarRoundingRulesConfigurationActual.getTenThousands());
        assertEquals(prettyPricingRuleRowIndep.getRuleNumbers(), cpbarRoundingRulesConfigurationActualIndep.getTenThousands());
    }

    @Test
    public void getBARRoundingRules() throws Exception {
        WorkContextType contextType = new WorkContextType();
        contextType.setPropertyId(5);
        PacmanWorkContextHelper.setWorkContext(contextType);
        PricingRule pricingRule = new PricingRule();
        PricingDigit pricingDigit = PricingDigit.TEN_THOUSANDS;
        PrettyPricingRuleRow prettyPricingRuleRow = new PrettyPricingRuleRow();
        prettyPricingRuleRow.setRuleNumbers(CPBarRoundingRulesConfigurationService.ALL_NUMBERS);
        pricingRule.addRule(pricingDigit, prettyPricingRuleRow);
        when(prettyPricingServiceMock.getPricingRule(1)).thenReturn(pricingRule);
        cpBarRoundingRulesConfigurationService.getBARRoundingRules();
        verify(prettyPricingServiceMock).getPricingRule(1);
    }


    private void insertProductDefinitionData(String productName, String productCode) {
        String insertQuery = "INSERT [dbo].[Product] ([Product_ID], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_ID], [Description], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Product_Floor_Rate], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Display_Order], [Is_Fixed_Above_Bar], [Send_Adjustment], [Centrally_Managed], [Is_Overridable], [Floor_Type], [Floor_Percentage], [Rate_Shopping_LOS_Min], [Rate_Shopping_LOS_Max], [Child_Pricing_Type]) VALUES " +
                "(24, '" + productName + "', 0, 11403, CAST(N'2022-03-11T19:49:21.760' AS DateTime), 11403, CAST(N'2022-03-11T19:53:22.247' AS DateTime), '" + productCode + "', N'DAILY', NULL, NULL, 0, NULL, 7, 14, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 0, NULL, 1, 1, NULL, NULL, 17, 0, 0, 0, 1, 2, NULL, -1, -1, 1)";
        crudService.executeUpdateByNamedQuery(insertQuery);
    }


}