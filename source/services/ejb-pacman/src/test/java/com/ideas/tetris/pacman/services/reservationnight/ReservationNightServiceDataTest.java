package com.ideas.tetris.pacman.services.reservationnight;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.marketsegment.entity.*;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingBusinessTypeShift;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociation;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.NewAMSRule;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationMapping;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType;
import com.ideas.tetris.pacman.services.pmsmigration.services.ReservationDataUpdationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.reservation.service.ReservationDataService;
import com.ideas.tetris.pacman.services.reservationnight.dto.AnalyticsReservation;
import com.ideas.tetris.pacman.services.reservationnight.dto.ReservationNightFilter;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.*;

@SlowDBTest
@Justification("This class should contain tests interacting with database")
public class ReservationNightServiceDataTest extends AbstractG3JupiterTest {

    private static final String BLANK_VALUE = "";
    private final List<Integer> reservationNightIds = new ArrayList<>();
    private final Date startDate = DateUtil.getDate(1, 0, 1970);
    private final Date endDate = DateUtil.getDate(1, 0, 2100);
    private ReservationNightService reservationNightService;
    private ReservationDataService reservationDataService;
    private CrudService tenantCrudService;
    private int totalTransactions = 0;

    @BeforeEach
    public void setUp() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        tenantCrudService = tenantCrudService();
        reservationNightService = new ReservationNightService();
        tenantCrudService.executeUpdateByNativeQuery("update mkt_seg_details set priced_by_bar=1 where mkt_seg_id = 7");
        reservationNightService.tenantCrudService = tenantCrudService;
        reservationDataService = new ReservationDataService();
        reservationDataService.tenantCrudService = tenantCrudService;
    }

    @Test
    public void getMaximumOccupancyDateFromReservationNight() {
        Date lastOccupancyDate = DateUtil.getCurrentDateWithoutTime();
        saveReservationNightRecord(8, 9, "SXBL", "fm1", lastOccupancyDate, null, BLANK_VALUE, "GRP1");
        saveReservationNightRecord(8, 9, "SXBL", "fm2", DateUtil.addDaysToDate(lastOccupancyDate, -2)
                , null, BLANK_VALUE, "GRP1");

        Date maximumOccupancyDate = reservationNightService.getMaximumOccupancyDateFrom(ReservationNightService.RESERVATION_NIGHT);

        assertEquals(lastOccupancyDate, maximumOccupancyDate);
    }

    @Test
    public void getMinimumOccupancyDateFromReservationNight() {
        Date firstOccupancyDate = DateUtil.getCurrentDateWithoutTime();
        saveReservationNightRecord(
                8, 9, "SXBL", "fm1", firstOccupancyDate, null, BLANK_VALUE, "GRP1");
        saveReservationNightRecord(
                8, 9, "SXBL", "fm2", DateUtil.addDaysToDate(firstOccupancyDate, 2), null, BLANK_VALUE, "GRP1");

        Date minimumOccupancyDate = reservationNightService.getMinimumOccupancyDateFrom(ReservationNightService.RESERVATION_NIGHT);

        assertEquals(firstOccupancyDate, minimumOccupancyDate);
    }

    @Test
    public void testUpdateReservationNightWithBlankMarketCodeAndBlankRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, null, BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, null, BLANK_VALUE, 8, 9, "SXBL", "GRP1", reservationNightIds);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithBlankMarketCodeAndBlankRateCode(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight_MS1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        final ReservationNight reservationNight_MS2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        assertEquals("MS1", reservationNight_MS1.getMarketCode());
        assertEquals("MS2", reservationNight_MS2.getMarketCode());
    }

    @Test
    public void testUpdateReservationNightChangeWithBlankMarketCodeAndBlankRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, null, BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, null, BLANK_VALUE, 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        final int updatedRecordsCount = reservationNightService.updateReservationsWithBlankMarketCodeAndBlankRateCode(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<String> resultList = tenantCrudService.findByNativeQuery("select Market_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS1", resultList.get(0));
        assertEquals("MS2", resultList.get(1));
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesWithNewPMSMarketCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC2", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
        assertEquals("RC2", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightGroupNonGroupMarketCodesWithNewPMSMarketCodesForBlankNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupNonGroupMarketCodesWithNewPMSMarketCodesForBlankNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesWithNewPMSMarketCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
        assertEquals("RC1", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesWithNewPMSMarketCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC1", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesWithNewPMSMarketCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC1", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesWithNewPMSMarketCodesCaseSensitive() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "ms1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("ms1", resultList.get(0)[0]);
        assertEquals("RC1", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesWithNewPMSMarketCodesWhenCurrentCodeIsSameAsNewEquivalentCodeForMS() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesWithNewPMSMarketCodesWhenCurrentCodeIsSameAsNewEquivalentCodeForMS() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesWithNewPMSMarketCodesWhenCurrentCodeIsSameAsNewEquivalentCodeForMS() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesWithNewPMSMarketCodesWhenCurrentCodeIsSameAsNewEquivalentCodeForMS() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightMarketCodesAgainstBlankRatesWithNewPMSMappings() {
        MktSeg ms20 = createMktSeg("MS20");
        MktSeg ms30 = createMktSeg("MS30");
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, ms20.getCode(), "", ms20.getId(), 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, ms30.getCode(), "", ms30.getId(), 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS20", "MS40", true, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS30", "ms30", true, null);
        final int updatedRecordsCount = reservationNightService.updateReservationNightMarketCodesAgainstBlankRatesWithNewPMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(2, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesWithNewPMSMarketCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", true, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesWithNewPMSMarketCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", true, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesWithNewPMSMarketCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", true, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesWithNewPMSMarketCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", true, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesWithNewPMSMarketCodesForSplitMarketSegments() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesWithNewPMSMarketCodesForSplitMarketSegments() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesWithNewPMSMarketCodesForSplitMarketSegments() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesWithNewPMSMarketCodesForSplitMarketSegments() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesWithNewPMSMarketCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfGroupMarketCodesWithNewPMSRateCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS1", reservationNight.getMarketCode());
        assertEquals("RC2", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightRateCodesOfGroupNonGroupMarketCodesWithNewPMSRateCodesForBlankNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", BLANK_VALUE, false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfGroupNonGroupMarketCodesWithNewPMSRateCodesForBlankNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", BLANK_VALUE, false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", BLANK_VALUE, false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfNonGroupMarketCodesWithNewPMSRateCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS1", reservationNight.getMarketCode());
        assertEquals("RC2", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesCaseSensitive() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "ms1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "rc1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS1", reservationNight.getMarketCode());
        assertEquals("rc1", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfGroupMarketCodesWithNewPMSRateCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS1", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfNonGroupMarketCodesWithNewPMSRateCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(totalTransactions, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS1", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfGroupMarketCodesWithNewPMSRateCodesWhenCurrentRateCodeIsSameAsNewEquivalentRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesWhenCurrentRateCodeIsSameAsNewEquivalentRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfGroupMarketCodesWithNewPMSRateCodesWhenCurrentRateCodeIsSameAsNewEquivalentRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesWhenCurrentRateCodeIsSameAsNewEquivalentRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfGroupMarketCodesWithNewPMSRateCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", true, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", true, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfGroupMarketCodesWithNewPMSRateCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", true, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesForDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", true, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightRateCodesOfGroupMarketCodesWithNewPMSRateCodesForSplitRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(1, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS1", reservationNight.getMarketCode());
        assertEquals("RC2", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesForSplitRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(1, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS1", reservationNight.getMarketCode());
        assertEquals("RC2", reservationNight.getRateCode());
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfGroupMarketCodesWithNewPMSRateCodesForSplitRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(1, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS1", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightChangeRateCodesOfNonGroupMarketCodesWithNewPMSRateCodesForSplitRateCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsRateCodesWithNewPMSRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        tenantCrudService.flushAndClear();
        assertEquals(1, updatedRecordsCount);
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS1", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createYieldCategoryRule("MS30", null, "MS30_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS30", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
        assertTrue(StringUtils.isBlank(reservationNight.getRateCode()));
    }

    @Test
    public void shouldUpdateMarketCodeInReservationNightForRateCodeShiftAssociations() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CLEAN_RATE_CODE_SHIFT_TABLE_QUERY);
        createMSRecodingRateCodeShiftAssociation("RC1", "MS1", "MS2");
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", "RC1", 7, 9, "SXBL", "GRP1", reservationNightIds);
        tenantCrudService().flushAndClear();
        reservationNightService.tenantCrudService = tenantCrudService();
        //WHEN
        final int updatedRecordsCount = reservationNightService.updateReservationNightForRateCodeShiftAssociation(
                ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        //THEN
        assertEquals(1, updatedRecordsCount);
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS2", reservationNight.getMarketCode());
        assertEquals("RC1", reservationNight.getRateCode());
        assertEquals(7, reservationNight.getMarketSegId().intValue());
    }

    private void createMSRecodingRateCodeShiftAssociation(final String rateCode, final String previousMarketSegmentName, final String newMarketSegmentName) {
        tenantCrudService().executeUpdateByNativeQuery("insert into MS_Recoding_Rate_Code_Shift values('" +
                rateCode + "', '" + previousMarketSegmentName + "', '" + newMarketSegmentName + "')");
    }

    private void createMktSegRecodingBusinessTypeShiftAssociation(final String mktSegName, final String previousBusinessType, final String newBusinessType) {
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.INSERT_BUSINESS_TYPE_SHIFT_RULES_QUERY_PREFIX +
                "('" + mktSegName + "', '" + previousBusinessType + "', '" + newBusinessType + "')");
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsWithBlankEquivalentCodeForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", BLANK_VALUE, false, false);// is this entry allowed by validation?
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
        assertTrue(StringUtils.isBlank(reservationNight.getRateCode()));
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsWithBlankEquivalentCodeForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", BLANK_VALUE, false, false);// is this entry allowed by validation?
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertTrue(StringUtils.isBlank((String) resultList.get(0)[1]));
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createYieldCategoryRule("MS30", null, "MS30_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS30", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
        assertTrue(StringUtils.isBlank(reservationNight.getRateCode()));
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createYieldCategoryRule("MS30", null, "MS30_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS30", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertTrue(StringUtils.isBlank((String) resultList.get(0)[1]));
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingForSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createYieldCategoryRule("MS30", null, "MS30_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS30", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertTrue(StringUtils.isBlank((String) resultList.get(0)[1]));
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingNonSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingNonSplitMktSegCaseSensitive() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("ms1", null, "ms1_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "ms1", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("ms1", reservationNight.getMarketCode());
    }

    @Test
    public void testUpdateReservationNightGroupNonMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingNonSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight.getMarketCode());
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingNonSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<String> resultList = tenantCrudService.findByNativeQuery("select Market_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0));
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingNonSplitMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<String> resultList = tenantCrudService.findByNativeQuery("select Market_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0));
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", true, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", true, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS20", true, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnDiscontinuedMktSeg() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS20", true, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnMappingHavingSameCurrentCodeAndNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS1", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnMappingHavingSameCurrentCodeAndNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS1", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnMappingHavingSameCurrentCodeAndNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS1", "MS1", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationNightChangeNonGroupMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecodingOnMappingHavingSameCurrentCodeAndNewEquivalentCode() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS1", BLANK_VALUE, 7, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS1", null, "MS1_DEF");
        createYieldCategoryRule("MS20", null, "MS20_DEF");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS1", "MS1", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsMarketCodesAgainstBlankRatesWithNewPMSMappingsForMSRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForSplitGroupMktSegmentsInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC1", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        final ReservationNight reservationNight2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        assertEquals("MS20", reservationNight1.getMarketCode());
        assertEquals("RC1", reservationNight1.getRateCode());
        assertEquals("MS30", reservationNight2.getMarketCode());
        assertEquals("RC2", reservationNight2.getRateCode());
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForSplitGroupMktSegmentsSplitRateCodeInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC2", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC3", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight1.getMarketCode());
        assertEquals("RC2", reservationNight1.getRateCode());
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForSplitNonGroupMktSegmentsSplitRateCodeInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC2", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC3", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight1.getMarketCode());
        assertEquals("RC2", reservationNight1.getRateCode());
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForSplitNonGroupMktSegmentsSplitRateCodeInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC2", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC3", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForSplitGroupMktSegmentsSplitRateCodeInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC2", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC3", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC3", false, false);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final List<Object[]> resultList = tenantCrudService.findByNativeQuery("select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC2", resultList.get(0)[1]);
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForSplitNonGroupMktSegmentsInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC1", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        final ReservationNight reservationNight2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        assertEquals("MS20", reservationNight1.getMarketCode());
        assertEquals("RC1", reservationNight1.getRateCode());
        assertEquals("MS30", reservationNight2.getMarketCode());
        assertEquals("RC2", reservationNight2.getRateCode());
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForSplitNonGroupMktSegmentsInRecodingHappyFlowCaseSensitive() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("ms2", "RC1", "ms2_QBL");
        createYieldCategoryRule("MS30", "RC1", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "ms2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        final ReservationNight reservationNight2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        assertEquals("ms2", reservationNight1.getMarketCode());
        assertEquals("RC1", reservationNight1.getRateCode());
        assertEquals("MS30", reservationNight2.getMarketCode());
        assertEquals("RC2", reservationNight2.getRateCode());
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForSplitGroupMktSegmentsInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC1", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        List<Object[]> resultList = tenantCrudService.findByNativeQuery("Select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC1", resultList.get(0)[1]);
        assertEquals("MS30", resultList.get(1)[0]);
        assertEquals("RC2", resultList.get(1)[1]);
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForSplitNonGroupMktSegmentsInRecodingHappyFlow() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC1", "MS30_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QYL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(totalTransactions, updatedRecordsCount);
        tenantCrudService.flushAndClear();
        List<Object[]> resultList = tenantCrudService.findByNativeQuery("Select Market_Code, Rate_Code from " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE);
        assertEquals("MS20", resultList.get(0)[0]);
        assertEquals("RC1", resultList.get(0)[1]);
        assertEquals("MS30", resultList.get(1)[0]);
        assertEquals("RC2", resultList.get(1)[1]);
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForNonSplitGroupMktSegmentsInRecoding() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS30", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationsInReservationNightWithNewMappingsForNonSplitNonGroupMktSegmentsInRecoding() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS3", "MS30", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForNonSplitGroupMktSegmentsInRecoding() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS30", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void testUpdateReservationsInReservationNightChangeWithNewMappingsForNonSplitNonGroupMktSegmentsInRecoding() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC1", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        reservationDataService.initializeReservationNightChangeTable();
        createYieldCategoryRule("MS20", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS30", "RC2", "MS30_QBL");
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS3", "MS30", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC1", "RC1", false, null);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, null);
        final int updatedRecordsCount = reservationNightService.updateReservationsWithNewMappingsForSplitMktSegmentsInRecoding(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(0, updatedRecordsCount);
    }

    @Test
    public void updatesAllReservationNightWhoseRateCodesInYCBRForNonStraightAndNonDefaultMktSegs() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "RC1", "MS2_QYL");
        createReservationNight(3, "MS2", "RC3", "MS2_QYL");
        createReservationNight(2, "MS2", "", "MS2_DEF");
        createReservationNight(1, "MS2", null, "MS2_DEF");
        createReservationNight(4, "MS3", "RC2", "MS3_QYL");
        createReservationNight(3, "MS3", "RC20", "MS3_QYL");
        createReservationNight(2, "MS3", "", "MS3_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);

        assertEquals(8, recordsUpdated);
    }

    @Test
    public void updatesAllReservationNightWhoseRateCodesAreNotInYCBRWithDefaultAMSRule() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "", "MS2_DEF");
        createReservationNight(3, "MS2", "RC10", "MS2_DEF");
        createReservationNight(2, "MS2", null, "MS2_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");
        createReservationNight(1, "MS3", "RC32", "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");

        createAMSRule("MS2", "RC1", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", "RC2", "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS2", null, "MS20_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", null, "MS30_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);

        assertEquals(11, recordsUpdated);
    }

    @Test
    public void updatesAllMktSegIdsInReservationNight() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "RC1", "MS2_QYL");
        createReservationNight(3, "MS2", "RC3", "MS2_QYL");
        createReservationNight(2, "MS2", "", "MS2_DEF");
        createReservationNight(1, "MS2", null, "MS2_DEF");
        createReservationNight(4, "MS3", "RC2", "MS3_QYL");
        createReservationNight(3, "MS3", "RC20", "MS3_QYL");
        createReservationNight(2, "MS3", "", "MS3_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");

        createAMSRule("MS2", "RC1", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", "RC2", "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS2", null, "MS20_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", null, "MS30_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);

        assertEquals(20, recordsUpdated);
    }

    @Test
    public void updatesAllReservationsNightCHangeWhoseRateCodesInYCBRForNonStraightAndNonDefaultMktSegs() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "RC1", "MS2_QYL");
        createReservationNight(3, "MS2", "RC3", "MS2_QYL");
        createReservationNight(2, "MS2", "", "MS2_DEF");
        createReservationNight(1, "MS2", null, "MS2_DEF");
        createReservationNight(4, "MS3", "RC2", "MS3_QYL");
        createReservationNight(3, "MS3", "RC20", "MS3_QYL");
        createReservationNight(2, "MS3", "", "MS3_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");
        reservationDataService.initializeReservationNightChangeTable();

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);

        assertEquals(8, recordsUpdated);
    }

    @Test
    public void updatesAllReservationNightChangeWhoseRateCodesAreNotInYCBRWithDefaultAMSRule() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "", "MS2_DEF");
        createReservationNight(3, "MS2", "RC10", "MS2_DEF");
        createReservationNight(2, "MS2", null, "MS2_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");
        createReservationNight(1, "MS3", "RC32", "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");

        createAMSRule("MS2", "RC1", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", "RC2", "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS2", null, "MS20_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", null, "MS30_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        reservationDataService.initializeReservationNightChangeTable();

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);

        assertEquals(11, recordsUpdated);
    }

    @Test
    public void updatesAllMktSegIdsInReservationNightChange() {
        setUpMktSegsForMSRecoding();
        createReservationNight(4, "MS2", "RC1", "MS2_QYL");
        createReservationNight(3, "MS2", "RC3", "MS2_QYL");
        createReservationNight(2, "MS2", "", "MS2_DEF");
        createReservationNight(1, "MS2", null, "MS2_DEF");
        createReservationNight(4, "MS3", "RC2", "MS3_QYL");
        createReservationNight(3, "MS3", "RC20", "MS3_QYL");
        createReservationNight(2, "MS3", "", "MS3_DEF");
        createReservationNight(1, "MS3", null, "MS3_DEF");

        createYieldCategoryRule("MS2", "RC1", "MS20_QBL");
        createYieldCategoryRule("MS3", "RC2", "MS30_QBL");

        createAMSRule("MS2", "RC1", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", "RC2", "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS2", null, "MS20_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS3", null, "MS30_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        reservationDataService.initializeReservationNightChangeTable();

        long recordsUpdated = reservationNightService.updateReservationsMktSegIDsWithAllAMSMappings(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);

        assertEquals(20, recordsUpdated);
    }

    @Test
    @Disabled("flaky")
    public void testUpdateReservationsMktSegIDsWithEqualsAMSRules() {
        final Integer ms20_qbl_id = createMktSeg("MS20_QBL").getId();
        final Integer ms20_def_id = createMktSeg("MS20_DEF").getId();
        final Integer ms30_qyl_id = createMktSeg("MS30_QYL").getId();
        final Integer ms30_def_id = createMktSeg("MS30_DEF").getId();
        final Integer ms40_id = createMktSeg("MS40").getId();
        final Integer ms9_def_id = createMktSeg("ms9_DEF").getId();
        final Integer ms9_qyl_id = createMktSeg("ms9_QYL").getId();
        final Integer MS9_qyl_id = createMktSegBy("MS9_QYL", 1).getId();
        final Integer MS9_def_id = createMktSegBy("MS9_DEF", 1).getId();

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC5", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC3", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC6", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC4", 9, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms9", "RC11", MS9_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms9", "RC13", MS9_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);

        createAMSRule("MS20", "RC2", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS20", "RC5", "MS20_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC3", "MS30_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC6", "MS30_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("ms9", "RC11", "ms9_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("ms9", "RC13", "ms9_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS9", "RC11", "MS9_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("MS9", "RC13", "MS9_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS40", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS9", "ms9", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC3", "RC3", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC4", "RC4", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC5", "RC5", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC6", "RC6", false, false);
        final int count = reservationNightService.updateReservationsMktSegIDsWithEqualsAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(6, count);
        tenantCrudService.flushAndClear();
        assertEquals(ms20_qbl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketSegId());
        assertEquals(ms20_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1)).getMarketSegId());
        assertEquals(ms30_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(2)).getMarketSegId());
        assertEquals(ms30_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(3)).getMarketSegId());
        assertNotSame(ms40_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(4)).getMarketSegId());
        assertEquals(ms9_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(5)).getMarketSegId());
        assertNotSame(ms9_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(6)).getMarketSegId());
    }

    @Test
    public void testUpdateReservationsWithMktSegIDsForAttributeChangeInMSRecoding() {
        final Integer ms20_def_id = createMktSeg("MS20_DEF").getId();
        final Integer ms20_qy_id = createMktSeg("MS20_QY").getId();
        final Integer ms20_usb_id = createMktSeg("MS20_USB").getId();

        createAMSRule("MS20", "RC2", "MS20_QY", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS20", "RC3", "MS20_USB", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("MS20", "RC4", "MS20_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.PACKAGED);

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC2", ms20_qy_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC3", ms20_qy_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC4", ms20_def_id, 9, "SXBL", "GRP1", reservationNightIds);

        reservationDataService.initializeReservationNightChangeTable();
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS20", "MS20", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC3", "RC3", false, false);

        tenantCrudService.executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        addPMSRevampNewAMSRule("MS20", "RC3", "EQUAL_TO_BAR", "1");
        tenantCrudService.flushAndClear();
        //Reservation_Night table
        int count = reservationNightService.updateReservationsMktSegIDsWithEqualsAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        ReservationNight reservationNight2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        ReservationNight reservationNight3 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(2));

        assertEquals(3, count);
        assertEquals(ms20_qy_id, reservationNight1.getMarketSegId());
        assertEquals(ms20_usb_id, reservationNight2.getMarketSegId());
        assertEquals(ms20_def_id, reservationNight3.getMarketSegId());

        //Reservation_Night_Change table
        count = reservationNightService.updateReservationsMktSegIDsWithEqualsAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE, startDate, endDate);
        assertEquals(3, count);
        List<Integer> updateMtSegIds = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID FROM " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE + " WHERE Market_Code='MS20' AND Rate_Code='RC3'");
        assertEquals(ms20_usb_id, updateMtSegIds.get(0));
        updateMtSegIds = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID FROM " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE + " WHERE Market_Code='MS20' AND Rate_Code='RC2'");
        assertEquals(ms20_qy_id, updateMtSegIds.get(0));
        updateMtSegIds = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID FROM " + ReservationDataUpdationService.RESERVATION_NIGHT_CHANGE + " WHERE Market_Code='MS20' AND Rate_Code='RC4'");
        assertEquals(ms20_def_id, updateMtSegIds.get(0));
    }

    @Test
    public void testUpdateReservationNightMktSegIDsWithStraightAMSRules() {
        final Integer ms20_qbl_id = createMktSeg("MS20_QBL").getId();
        final Integer ms20_def_id = createMktSeg("MS20_DEF").getId();
        final Integer ms30_qyl_id = createMktSeg("MS30_QYL").getId();
        final Integer ms30_def_id = createMktSeg("MS30_DEF").getId();
        final Integer ms9_def_id = createMktSeg("ms9_DEF").getId();
        final Integer ms9_qyl_id = createMktSeg("ms9_QYL").getId();
        final Integer ms40_id = createMktSeg("MS40").getId();
        final Integer ms7_id = createMktSeg("ms7").getId();
        final Integer ms50_qyl_id = createMktSeg("MS50_QYL").getId();
        final Integer ms50_def_id = createMktSeg("MS50_DEF").getId();
        final Integer ms50_id = createMktSegBy("MS50", 1).getId();
        final Integer ms60_id = createMktSegBy("MS60", 1).getId();
        final Integer ms4_id = createMktSegBy("MS4", 1).getId();

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC5", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC3", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC6", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC4", 9, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC7", ms50_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC8", ms50_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC9", ms50_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms9", "RC11", ms9_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms9", "RC13", ms9_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms7", "RC12", 10, 9, "SXBL", "GRP1", reservationNightIds);

        createAMSRule("MS20", "RC2", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS20", "RC5", "MS20_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC3", "MS30_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC6", "MS30_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", "RC7", "MS50_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", null, "MS50_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("MS50", null, "MS50", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        createAMSRule("MS60", null, "MS60", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        createAMSRule("ms9", "RC11", "ms9_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("ms9", "RC13", "ms9_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("ms7", null, "ms7", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS40", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS9", "ms9", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS7", "ms7", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS50", "MS50", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS60", "MS50", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC3", "RC3", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC4", "RC4", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC5", "RC5", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC6", "RC6", false, false);
        final int count = reservationNightService.updateReservationsMktSegIDsWithStraightAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(2, count);
        tenantCrudService.flushAndClear();
        assertNotSame(ms20_qbl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketSegId());
        assertNotSame(ms20_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1)).getMarketSegId());
        assertNotSame(ms30_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(2)).getMarketSegId());
        assertNotSame(ms30_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(3)).getMarketSegId());
        assertEquals(ms40_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(4)).getMarketSegId());
        assertEquals(ms50_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(5)).getMarketSegId());
        assertEquals(ms50_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(6)).getMarketSegId());
        assertEquals(ms50_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(7)).getMarketSegId());
        assertEquals(ms9_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(8)).getMarketSegId());
        assertEquals(ms9_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(9)).getMarketSegId());
        assertEquals(ms7_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(10)).getMarketSegId());
    }

    @Test
    void testUpdateReservationNightMktSegIDsForAmsToStraightScenario() {
        final Integer ms30_qyl_id = createMktSeg("MS30_QYL").getId();
        final Integer ms30_def_id = createMktSeg("MS30_DEF").getId();
        final Integer ms40_id = createMktSegBy("MS40", 1).getId();
        final Integer ms400_id = createMktSeg("MS400").getId();
        final Integer ms7_id = createMktSeg("ms7").getId();
        final Integer ms50_qyl_id = createMktSegBy("MS50_QYL", 1).getId();
        final Integer ms50_def_id = createMktSegBy("MS50_DEF", 1).getId();
        final Integer ms50_id = createMktSeg("MS50").getId();

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC3", ms30_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC6", ms30_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS400", "RC4", ms40_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC7", ms50_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC8", ms50_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS50", "RC9", ms50_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "ms7", "RC12", ms7_id, 9, "SXBL", "GRP1", reservationNightIds);

        createAMSRule("MS30", "RC3", "MS30_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC6", "MS30_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", "RC7", "MS50_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", null, "MS50_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        createAMSRule("ms7", null, "ms7", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);

        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS30", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS40", "MS400", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "ms7", "ms7", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS50", "MS50", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC2", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC3", "RC3", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC4", "RC4", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC5", "RC5", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC6", "RC6", false, false);
        final int count = reservationNightService.updateReservationsMktSegIDsWithStraightAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertEquals(5, count);
        tenantCrudService.flushAndClear();
        assertNotSame(ms30_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketSegId());
        assertNotSame(ms30_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1)).getMarketSegId());
        assertEquals(ms400_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(2)).getMarketSegId());
        assertEquals(ms50_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(3)).getMarketSegId());
        assertEquals(ms50_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(4)).getMarketSegId());
        assertEquals(ms50_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(5)).getMarketSegId());
        assertEquals(ms7_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(6)).getMarketSegId());
    }

    @Test
    public void testUpdateReservationsMktSegIdsWithTransientToGroupAMSRules() {
        //Given
        final Integer ms20_qbl_id = createMktSeg("MS20_QBL").getId();
        final Integer ms20_def_id = createMktSeg("MS20_DEF").getId();
        final Integer ms30_qyl_id = createMktSeg("MS30_QYL").getId();
        final Integer ms30_def_id = createMktSeg("MS30_DEF").getId();
        final Integer ms40_qy_id = createMktSeg("MS40_QY").getId();
        final Integer ms40_id = createMktSeg("MS40").getId();

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC2", ms20_qbl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS20", "RC5", ms20_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC3", ms30_qyl_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS30", "RC6", ms30_def_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC4", ms40_qy_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC7", ms40_qy_id, 9, "SXBL", "GRP1", reservationNightIds);

        createAMSRule("MS20", "RC2", "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS20", "RC5", "MS20_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC3", "MS30_QYL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", "RC6", "MS30_DEF", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.GROUP);

        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CREATE_MKT_SEG_RECODING_BUSINESS_TYPE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CLEAN_BUSINESS_TYPE_SHIFT_TABLE_QUERY);
        createMktSegRecodingBusinessTypeShiftAssociation("MS40", "Transient", "Group");
        //When
        final int count = reservationNightService.updateReservationsMktSegIdsWithTransientToGroupAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        //THen
        assertEquals(2, count);
        tenantCrudService.flushAndClear();
        assertEquals(ms20_qbl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketSegId());
        assertEquals(ms20_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1)).getMarketSegId());
        assertEquals(ms30_qyl_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(2)).getMarketSegId());
        assertEquals(ms30_def_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(3)).getMarketSegId());
        assertEquals(ms40_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(4)).getMarketSegId());
        assertEquals(ms40_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(5)).getMarketSegId());
    }

    @Test
    public void testUpdateReservationsMktSegIdsWithTransientToGroupAMSRules_shouldNotUpdateWhenBusinessTypeShiftTableNotExist() {
        //Given
        final Integer ms40_qy_id = createMktSeg("MS40_QY").getId();
        final Integer ms40_id = createMktSeg("MS40").getId();

        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC4", ms40_qy_id, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS40", "RC7", ms40_qy_id, 9, "SXBL", "GRP1", reservationNightIds);

        createAMSRule("MS40", null, "MS40", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.GROUP);

        //When
        final int count = reservationNightService.updateReservationsMktSegIdsWithTransientToGroupAMSRules(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        //THen
        assertEquals(0, count);
        tenantCrudService.flushAndClear();
        assertEquals(ms40_qy_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketSegId());
        assertEquals(ms40_qy_id, tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1)).getMarketSegId());
    }

    @Test
    public void updateReservationsForSplitStraightMarketCodesAndRateCodesShouldUpdateMarketCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC5", 9, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS3", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS40", false, false);
        createAMSRule("MS20", null, "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", null, "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", null, "MS50_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        final String response = reservationNightService.updateReservationsForSplitStraightMarketCodesAndRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertTrue(response.contains("count of rate codes updated=0 and count of market codes updated=1"));
        tenantCrudService.flushAndClear();
        assertEquals("MS20", tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketCode());
    }

    @Test
    public void updateReservationsForSplitStraightMarketCodesAndRateCodesShouldUpdateMarketCodesCaseSensitive() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC5", 9, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "ms2", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS3", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS40", false, false);
        createAMSRule("ms2", null, "ms2_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", null, "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", null, "MS50_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        final String response = reservationNightService.updateReservationsForSplitStraightMarketCodesAndRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertTrue(response.contains("count of rate codes updated=0 and count of market codes updated=1"));
        tenantCrudService.flushAndClear();
        assertEquals("ms2", tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0)).getMarketCode());
    }

    @Test
    public void updateReservationsForSplitStraightMarketCodesAndRateCodesShouldUpdateRateCodes() {
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS2", "RC2", 8, 9, "SXBL", "GRP1", reservationNightIds);
        createReservationNightRecordsWithMktSegCodeAndRateCode(
                1, "MS3", "RC5", 9, 9, "SXBL", "GRP1", reservationNightIds);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP, "MS2", "MS30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS40", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.MARKET_SEGMENT, "MS3", "MS50", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC20", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC2", "RC30", false, false);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC5", "RC40", false, true);
        createPMSMigrationMapping(PMSMigrationMappingType.RATE_CODE, "RC5", "RC50", false, false);
        createAMSRule("MS20", null, "MS20_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS30", null, "MS30_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS40", null, "MS40_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        createAMSRule("MS50", null, "MS50_QBL", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        final String response = reservationNightService.updateReservationsForSplitStraightMarketCodesAndRateCodes(ReservationDataUpdationService.RESERVATION_NIGHT, startDate, endDate);
        assertTrue(response.contains("count of rate codes updated=2 and count of market codes updated=2"));
        tenantCrudService.flushAndClear();
        final ReservationNight reservationNight1 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(0));
        assertEquals("MS20", reservationNight1.getMarketCode());
        assertEquals("RC20", reservationNight1.getRateCode());
        final ReservationNight reservationNight2 = tenantCrudService.find(ReservationNight.class, reservationNightIds.get(1));
        assertEquals("MS40", reservationNight2.getMarketCode());
        assertEquals("RC40", reservationNight2.getRateCode());
    }

    @Test
    public void testReservationNightGetStraightBarMsAnalticalReservations() {
        createReservationNightRecordWithFilterCondition(4, 7, "CI", 60, 14);
        createReservationNightRecordWithFilterCondition(5, 7, "CI", 60, 14);
        createReservationNightRecordWithFilterCondition(4, 9, "CI", 60, 14);
        createReservationNightRecordWithFilterCondition(4, 7, "XX", 60, 14);
        createReservationNightRecordWithFilterCondition(4, 7, "CI", 61, 14);
        createReservationNightRecordWithFilterCondition(4, 7, "CI", 60, 15);

        ReservationNightFilter filter = new ReservationNightFilter(14, 60, 0, 100, startDate, endDate);
        List<AnalyticsReservation> reservationNightsBetweenOccupancyDates = reservationNightService.getReservationNightsBetweenOccupancyDates(filter);

        assertEquals(1, reservationNightsBetweenOccupancyDates.size());

        AnalyticsReservation analyticsReservation = reservationNightsBetweenOccupancyDates.get(0);
        assertEquals(7, analyticsReservation.getMktSegId());
        assertEquals("CI", analyticsReservation.getIndividualStatus());
        assertEquals(new BigDecimal(4), analyticsReservation.getNumberAdults());
    }

    @Test
    void ConcatProductNameToRateCodeAsPerLos() {
        // Create Mkt_Seg
        String STRAIGHTMS1 = "STRAIGHTMS1";
        String MS2_QYL = "MS2_QYL";
        String GRP_MS1 = "GRPMS1";
        String MS3QB = "MS3_QB";
        final MktSeg ms1 = createMktSeg(STRAIGHTMS1);
        final MktSeg ms2Qyl = createMktSeg(MS2_QYL);
        final MktSeg ms3Qb = createMktSeg(MS3QB);
        final MktSeg grpMs1 = createMktSeg(GRP_MS1);
        createMktSegMasterForHiltonIpp(ms1);
        createMktSegMasterForHiltonIpp(ms2Qyl);


        // Populate Reservation_Night
        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.minusDays(2), STRAIGHTMS1, "RC1", "201");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), STRAIGHTMS1, "RC2", "202");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(10), today.plusDays(22), STRAIGHTMS1, "RC3", "203");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(1), today.plusDays(2), MS2_QYL, "RC4", "204");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(15), today.plusDays(22), MS2_QYL, "RC5", "205");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(4), today.plusDays(11), MS2_QYL, "RC6", "206");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(40), today.plusDays(20), GRP_MS1, "RC7", "207");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), GRP_MS1, "RC8", "208");
        populateReservationNightRecords(ms3Qb.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), GRP_MS1, "RC9", "209");

        // Populate Product Table
        List<Product> ippProducts = new ArrayList<>();
        createProduct("LVO", Product.BAR, 1, 6);
        ippProducts.add(createProduct("LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14));
        ippProducts.add(createProduct("LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29));
        ippProducts.add(createProduct("LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999));
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        createMktSegProductMappingFor(ms1, ippProducts);
        createMktSegProductMappingFor(ms2Qyl, ippProducts);

        // Mkt_Seg_Split_Mapping
        createMktSegMasterForIpp(ms1, ippProducts);
        createMktSegMasterForIpp(ms2Qyl, ippProducts);

        // WHEN
        String reservationNight = "Reservation_Night";
        Date minOccDt = reservationNightService.getMinimumOccupancyDateFrom(reservationNight);
        Date maxOccDt = reservationNightService.getMaximumOccupancyDateFrom(reservationNight);
        int recordsUpdated = reservationNightService.updateRateCodesAsPerLosForIPPSupport(reservationNight, minOccDt, maxOccDt);

        // THEN
        assertEquals(97, recordsUpdated);
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV1'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV2'").get(0));
        assertEquals(2, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV3'").get(0));
    }

    @Test
    void ConcatProductNameToRateCodeAsPerLosStartDtEndDt() {
        // Create Mkt_Seg_Details
        String STRAIGHTMS1 = "STRAIGHTMS1";
        String MS2_QYL = "MS2_QYL";
        String GRP_MS1 = "GRPMS1";
        String MS3QB = "MS3_QB";
        final MktSeg ms1 = createMktSeg(STRAIGHTMS1);
        final MktSeg ms2Qyl = createMktSeg(MS2_QYL);
        final MktSeg ms3Qb = createMktSeg(MS3QB);
        final MktSeg grpMs1 = createMktSeg(GRP_MS1);
        createMktSegMasterForHiltonIpp(ms1);
        createMktSegMasterForHiltonIpp(ms2Qyl);

        // Populate Reservation_Night
        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.minusDays(2), STRAIGHTMS1, "RC1", "201");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), STRAIGHTMS1, "RC2", "202");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(10), today.plusDays(22), STRAIGHTMS1, "RC3", "203");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(1), today.plusDays(2), MS2_QYL, "RC4", "204");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(15), today.plusDays(22), MS2_QYL, "RC5", "205");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(4), today.plusDays(11), MS2_QYL, "RC6", "206");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(40), today.plusDays(20), GRP_MS1, "RC7", "207");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), GRP_MS1, "RC8", "208");
        populateReservationNightRecords(ms3Qb.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), GRP_MS1, "RC9", "209");

        // Populate Product Table
        List<Product> ippProducts = new ArrayList<>();
        createProduct("LVO", Product.BAR, 1, 6);
        ippProducts.add(createProduct("LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14));
        ippProducts.add(createProduct("LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29));
        ippProducts.add(createProduct("LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999));
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        // Mkt_Seg_Split_Mapping
        createMktSegMasterForIpp(ms1, ippProducts);
        createMktSegMasterForIpp(ms2Qyl, ippProducts);

        createMktSegProductMappingFor(ms1, ippProducts);
        createMktSegProductMappingFor(ms2Qyl, ippProducts);

        // WHEN
        int recordsUpdated = reservationNightService.updateRateCodesAsPerLosForIPPSupport("Reservation_Night", LocalDateUtils.toDate(today.minusDays(10)),
                LocalDateUtils.toDate(today.plusDays(10)));

        // THEN
        assertEquals(70, recordsUpdated);
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV1'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV2'").get(0));
        assertEquals(2, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%_LV3'").get(0));
    }

    @Test
    void testUpdateReservationNightMktSegIdForHiltonIppAsPerLOS() {
        Product lvo = createProduct("LV0", "Product.BAR", 1, 6);
        Product lv1 = createProduct("LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product lv2 = createProduct("LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product lv3 = createProduct("LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(lv1, lv2, lv3);

        final MktSeg discDef = createMktSeg("MS_DEF_FUT");
        createMktSegMasterForHiltonIpp(discDef);
        final MktSeg discLv1Def = createMktSeg("MS_"+lv1.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv1Def);
        final MktSeg discLv2Def = createMktSeg("MS_"+lv2.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv2Def);
        final MktSeg discLv3Def = createMktSeg("MS_"+lv3.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv3Def);
        createMktSegProductMappingFor(discDef, ippProducts);

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");

        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(6), "MS", "RC5", "205");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(11), "MS", "RC6_LV1", "206");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(17), "MS", "RC7_LV2", "207");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(40), "MS", "RC8_LV3", "208");

        createAMSRule("MS", null, "MS_DEF_FUT", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("MS", null, "MS_"+ product.getName()+"_DEF_FUT", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });

        int recordsUpdated = reservationNightService.updateReservationNightForDefaultMktSegIdsForHiltonIppAsPerLOS("Reservation_Night", null);
        assertEquals(68, recordsUpdated);
        assertEquals("MS", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Market_Code FROM Reservation_Night WHERE Mkt_Seg_id="+ discLv3Def.getId())));
        assertEquals(11, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv1Def.getId()).get(0));
        assertEquals(17, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv2Def.getId()).get(0));
        assertEquals(40, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv3Def.getId()).get(0));
    }

    @Test
    void testUpdateReservationNightMktSegIdForHiltonIppAsPerLOSForVP() {
        Product lvo = createProduct("LVO", Product.BAR, 1, 6);
        Product p1_lv1 = createProduct("P1_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p1_lv2 = createProduct("P1_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p1_lv3 = createProduct("P1_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        Product p2_lv1 = createProduct("P2_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p2_lv2 = createProduct("P2_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p2_lv3 = createProduct("P2_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(p1_lv1, p1_lv2, p1_lv3, p2_lv1, p2_lv2, p2_lv3);

        final MktSeg discDef = createMktSeg("MS_DEF_FUT");
        createMktSegMasterForHiltonIpp(discDef);
        final MktSeg discLv1Def = createMktSeg("MS_"+p1_lv1.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv1Def);
        final MktSeg discLv2Def = createMktSeg("MS_"+p1_lv2.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv2Def);
        final MktSeg discLv3Def = createMktSeg("MS_"+p1_lv3.getName()+"_DEF_FUT");
        createMktSegMasterForHiltonIpp(discLv3Def);
        createMktSegProductMappingFor(discDef, ippProducts);

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");

        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(6), "MS", "P1_RC1", "210");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(11), "MS", "P1_RC2_LV1", "211");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(17), "MS", "P1_RC3_LV2", "212");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(40), "MS", "P1_RC4_LV3", "213");

        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(6), "MS", "P2_RC5", "214");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(11), "MS", "P2_RC6", "215");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(17), "MS", "P2_RC7", "216");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(40), "MS", "P2_RC8", "217");

        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(6), "MS", "P3_RC5", "218");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(11), "MS", "P3_RC6", "219");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(17), "MS", "P3_RC7", "220");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(0), today.plusDays(40), "MS", "P3_RC8", "221");

        createAMSRule("MS", null, "MS_DEF_FUT", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("MS", null, "MS_"+ product.getName()+"_DEF_FUT", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });

        int recordsUpdatedForP1 = reservationNightService.updateReservationNightForDefaultMktSegIdsForHiltonIppAsPerLOS("Reservation_Night", "P1");
        int recordsUpdatedForP2 = reservationNightService.updateReservationNightForDefaultMktSegIdsForHiltonIppAsPerLOS("Reservation_Night", "P2");
        assertEquals(68, recordsUpdatedForP1);
        assertEquals("MS", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Market_Code FROM Reservation_Night WHERE Mkt_Seg_id="+ discLv3Def.getId())));
        assertEquals(22, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv1Def.getId()).get(0));
        assertEquals(34, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv2Def.getId()).get(0));
        assertEquals(80, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(Market_Code) FROM Reservation_Night WHERE Mkt_Seg_Id="+discLv3Def.getId()).get(0));
        assertEquals(discLv1Def.getId(), tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night where Reservation_Identifier = 211").get(0));
    }

    @Test
    void testUpdateStraightMsInResNightForHiltonIpp() {
        // Populate Product Table
        Product lvo = createProduct("LVO", Product.BAR, 1, 6);
        Product lv1 = createProduct("LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product lv2 = createProduct("LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product lv3 = createProduct("LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(lv1, lv2, lv3);

        final MktSeg discU = createMktSeg("DISC_U");
        createMktSegMasterForHiltonIpp(createMktSeg("DISC_" + lv1.getName() + "_U"));
        final MktSeg discLv2U = createMktSeg("DISC_" + lv2.getName() + "_U");
        createMktSegMasterForHiltonIpp(discLv2U);
        final MktSeg discLv3U = createMktSeg("DISC_" + lv3.getName() + "_U");
        createMktSegMasterForHiltonIpp(discLv3U);
        final MktSeg discDef = createMktSeg("DISC_DEF");
        createMktSegMasterForHiltonIpp(discDef);
        createMktSegMasterForHiltonIpp(createMktSeg("DISC_" + lv1.getName() + "_DEF"));
        final MktSeg discLv2Def = createMktSeg("DISC_" + lv2.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(discLv2Def);
        final MktSeg discLv3Def = createMktSeg("DISC_" + lv3.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(discLv3Def);
        final MktSeg cons = createMktSeg("CONS");

        createAMSRule("CONS", null, "CONS", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("CONS" + product.getName(), null, "CONS_" + product.getName(), RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.minusDays(2), "CONS", "RC9", "201");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), "CONS", "RC2_LV1", "202");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(10), today.plusDays(22), "CONS", "RC3_LV3", "203");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(1), today.plusDays(2), "DISC", "RC4", "204");
        populateReservationNightRecords(discLv3Def.getId(), fileMetadata, today.minusDays(15), today.plusDays(22), "DISC", "RC5", "205");
        populateReservationNightRecords(discLv2Def.getId(), fileMetadata, today.minusDays(4), today.plusDays(11), "DISC", "RC6", "206");
        populateReservationNightRecords(discLv3U.getId(), fileMetadata, today.minusDays(40), today.plusDays(20), "DISC", "RC7", "207");
        populateReservationNightRecords(discLv2Def.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), "DISC", "RC8", "208");
        populateReservationNightRecords(discLv2U.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), "DISC", "RC1_LV2", "209");

        createMktSegMasterForIpp(cons, ippProducts);
        createMktSegProductMappingFor(cons, ippProducts);


        int recordsUpdated = reservationNightService.updateReservationNightMarketSegmentsHiltonIpp("Reservation_Night", LocalDateUtils.toDate(today.minusDays(30)),
                LocalDateUtils.toDate(today.plusDays(30)));

        assertAll(
                () -> assertEquals(40, recordsUpdated),
                () -> assertEquals("CONS_LV1", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Market_Code FROM Reservation_Night WHERE Market_Code='CONS_" + lv1.getName() + "'"))),
                () -> assertEquals("DISC", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Market_Code FROM Reservation_Night WHERE Mkt_Seg_id="+ discLv3Def.getId()))),
                () -> assertEquals("DISC", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Market_Code FROM Reservation_Night WHERE Mkt_Seg_id="+ discLv2U.getId()))),
                () -> assertEquals("202", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + lv1.getName() + "'"))),
                () -> assertEquals("", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + lv2.getName() + "'"))),
                () -> assertEquals("203", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + lv3.getName() + "'")))
        );

    }

    @Test
    void ConcatProductNameToRateCodeAsPerLosForVP() {
        // Create Mkt_Seg
        String MS1_STRAIGHT = "MS1_STRAIGHT";
        String MS2_UF = "MS2_UF";
        String MS3_GROUP = "MS3_GROUP";
        String MS3_TRANSIENT_BLOCK = "MS3_TRANSIENT_BLOCK";
        final MktSeg ms1 = createMktSeg(MS1_STRAIGHT);
        final MktSeg ms2Qyl = createMktSeg(MS2_UF);
        final MktSeg ms3Qb = createMktSeg(MS3_TRANSIENT_BLOCK);
        final MktSeg grpMs1 = createMktSeg(MS3_GROUP);
        createMktSegMasterForHiltonIpp(ms1);
        createMktSegMasterForHiltonIpp(ms2Qyl);

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.minusDays(2), MS1_STRAIGHT, "P1_RC1", "201");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), MS1_STRAIGHT, "P3_RC2", "202");
        populateReservationNightRecords(ms1.getId(), fileMetadata, today.minusDays(10), today.plusDays(22), MS1_STRAIGHT, "P1_RC3", "203");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(1), today.plusDays(2), MS2_UF, "P1_RC4", "204");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(15), today.plusDays(22), MS2_UF, "P2_RC5", "205");
        populateReservationNightRecords(ms2Qyl.getId(), fileMetadata, today.minusDays(4), today.plusDays(11), MS2_UF, "P2_RC6", "206");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(40), today.plusDays(20), MS3_GROUP, "P2_RC7", "207");
        populateReservationNightRecords(grpMs1.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), MS3_GROUP, "P1_RC8", "208");
        populateReservationNightRecords(ms3Qb.getId(), fileMetadata, today.minusDays(5), today.plusDays(12), MS3_GROUP, "P1_RC9", "209");

        List<Product> ippProducts = new ArrayList<>();
        createProduct("LVO", Product.BAR, 1, 6);
        ippProducts.add(createProduct("P1_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14));
        ippProducts.add(createProduct("P1_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29));
        ippProducts.add(createProduct("P1_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999));
        ippProducts.add(createProduct("P2_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14));
        ippProducts.add(createProduct("P2_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29));
        ippProducts.add(createProduct("P2_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999));
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        createMktSegProductMappingFor(ms1, ippProducts);
        createMktSegProductMappingFor(ms2Qyl, ippProducts);

        createMktSegMasterForIpp(ms1, ippProducts);
        createMktSegMasterForIpp(ms2Qyl, ippProducts);

        String reservationNight = "Reservation_Night";
        Date minOccDt = reservationNightService.getMinimumOccupancyDateFrom(reservationNight);
        Date maxOccDt = reservationNightService.getMaximumOccupancyDateFrom(reservationNight);
        int recordsUpdated = reservationNightService.updateRateCodesAsPerLosForIPPSupportForVP(reservationNight, LocalDateUtils.toDate(today.minusDays(10)),
                LocalDateUtils.toDate(today.plusDays(10)), List.of("P1", "P2"));

        assertEquals(62, recordsUpdated);
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1_LV1'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1_LV2'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1_LV3'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2_LV1'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2_LV2'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2_LV3'").get(0));
        assertEquals(1, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1%P1%'").get(0));
        assertEquals(2, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2%P2%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P3%P3%'").get(0));
        validateRateCodeSplitsLimitedToPhysicalPropertyIPP();
    }

    @Test
    void testUpdateReservationsForVirtualProperty() {
        Product lvo = createProduct("LVO", Product.BAR, 1, 6);
        Product p1_lv1 = createProduct("P1_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p1_lv2 = createProduct("P1_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p1_lv3 = createProduct("P1_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        Product p2_lv1 = createProduct("P2_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p2_lv2 = createProduct("P2_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p2_lv3 = createProduct("P2_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(p1_lv1, p1_lv2, p1_lv3, p2_lv1, p2_lv2, p2_lv3);

        final MktSeg ms1 = createMktSeg("MS1_U");
        createMktSegMasterForHiltonIpp(ms1);
        createMktSegMasterForIpp(ms1, ippProducts);
        createMktSegProductMappingFor(ms1, ippProducts);
        final MktSeg ms2 = createMktSeg("MS1_DEF");
        createMktSegMasterForHiltonIpp(ms2);
        createMktSegProductMappingFor(ms2, ippProducts);
        createMktSegMasterForIpp(ms2, ippProducts);
        final MktSeg ms3 = createMktSeg("STRAIGHTMS");
        createMktSegMasterForHiltonIpp(ms3);
        createMktSegMasterForIpp(ms3, ippProducts);
        createMktSegProductMappingFor(ms3, ippProducts);

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata1 = saveUniqueFileMetadata("fm1");
        FileMetadata fileMetadata2 = saveUniqueFileMetadata("fm2");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "MS1", "P1_RC1", "201");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "MS1", "P1_RC2", "202");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "MS1", "P1_RC3", "203");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "MS1", "P1_RC4", "204");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "MS1", "P2_RC5", "205");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "MS1", "P2_RC6", "206");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "MS1", "P2_RC7", "207");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "MS1", "P2_RC8", "208");
        populateReservationNightRecords(ms1.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "MS1", "P1_RC3", "209");

        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "MS1", "P1_RD1", "210");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "MS1", "P1_RD2", "211");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "MS1", "P1_RD3", "212");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "MS1", "P1_RD4", "213");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "MS1", "P2_RD5", "214");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "MS1", "P2_RD6", "215");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "MS1", "P2_RD7", "216");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "MS1", "P2_RD8", "217");
        populateReservationNightRecords(ms2.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "MS1", "P1_RD3", "218");

        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "STRAIGHTMS", "P1_RE1", "219");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "STRAIGHTMS", "P1_RE2", "220");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "STRAIGHTMS", "P1_RE3", "221");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "STRAIGHTMS", "P1_RE4", "222");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "STRAIGHTMS", "P2_RE5", "223");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "STRAIGHTMS", "P2_RE6", "224");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "STRAIGHTMS", "P2_RE7", "225");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "STRAIGHTMS", "P2_RE8", "226");
        populateReservationNightRecords(ms3.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "STRAIGHTMS", "P1_RE3", "227");

        int recordsUpdated = reservationNightService.updateReservationsForVirtualProperty("Reservation_Night", fileMetadata1.getId());

        validateRateCodeSplitsLimitedToPhysicalPropertyIPP();
        assertAll(
                () -> assertEquals(375, recordsUpdated),
                () -> assertEquals(0, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Market_Code like '%" + p1_lv1.getName() + "%'").get(0)),
                () -> assertEquals("213", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Rate_Code='P1_RD4" + p1_lv3.getName() + "'"))),
                () -> assertEquals(0, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code='P2_RD4" + p1_lv3.getName() + "'").get(0)),
                () -> assertEquals(3, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Mkt_Seg_Id = " + ms1.getId()).get(0)),
                () -> assertEquals(18, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(*) FROM Reservation_Night WHERE Mkt_Seg_Id = " + ms2.getId()).get(0))
        );
    }

    @Test
    void testUpdateMarketCodeForStraightMSForVirtualProperty() {
        Product lvo = createProduct("LVO", Product.BAR, 1, 6);
        Product p1_lv1 = createProduct("P1_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p1_lv2 = createProduct("P1_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p1_lv3 = createProduct("P1_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        Product p2_lv1 = createProduct("P2_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p2_lv2 = createProduct("P2_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p2_lv3 = createProduct("P2_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(p1_lv1, p1_lv2, p1_lv3, p2_lv1, p2_lv2, p2_lv3);

        final MktSeg ms1 = createMktSeg("MS1_U");
        createMktSegMasterForHiltonIpp(ms1);
        final MktSeg ms1_p1_lv1 = createMktSeg("MS1_" + p1_lv1.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p1_lv1);
        final MktSeg ms1_p1_lv2 = createMktSeg("MS1_" + p1_lv2.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p1_lv2);
        final MktSeg ms1_p1_lv3 = createMktSeg("MS1_" + p1_lv3.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p1_lv3);
        final MktSeg ms1_p2_lv1 = createMktSeg("MS1_" + p2_lv1.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p2_lv1);
        final MktSeg ms1_p2_lv2 = createMktSeg("MS1_" + p2_lv2.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p2_lv2);
        final MktSeg ms1_p2_lv3 = createMktSeg("MS1_" + p2_lv3.getName() + "_U");
        createMktSegMasterForHiltonIpp(ms1_p2_lv3);
        createMktSegProductMappingFor(ms1, ippProducts);

        final MktSeg ms2 = createMktSeg("MS1_DEF");
        createMktSegMasterForHiltonIpp(ms2);
        final MktSeg ms2_p1_lv1 = createMktSeg("MS1_" + p1_lv1.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p1_lv1);
        final MktSeg ms2_p1_lv2 = createMktSeg("MS1_" + p1_lv2.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p1_lv2);
        final MktSeg ms2_p1_lv3 = createMktSeg("MS1_" + p1_lv3.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p1_lv3);
        final MktSeg ms2_p2_lv1 = createMktSeg("MS1_" + p2_lv1.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p2_lv1);
        final MktSeg ms2_p2_lv2 = createMktSeg("MS1_" + p2_lv2.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p2_lv2);
        final MktSeg ms2_p2_lv3 = createMktSeg("MS1_" + p2_lv3.getName() + "_DEF");
        createMktSegMasterForHiltonIpp(ms2_p2_lv3);
        createMktSegProductMappingFor(ms2, ippProducts);

        final MktSeg ms3 = createMktSeg("STRAIGHTMS");
        createMktSegMasterForHiltonIpp(ms3);
        final MktSeg ms3_p1_lv1 = createMktSeg("STRAIGHTMS_" + p1_lv1.getName());
        createMktSegMasterForHiltonIpp(ms3_p1_lv1);
        final MktSeg ms3_p1_lv2 = createMktSeg("STRAIGHTMS_" + p1_lv2.getName());
        createMktSegMasterForHiltonIpp(ms3_p1_lv2);
        final MktSeg ms3_p1_lv3 = createMktSeg("STRAIGHTMS_" + p1_lv3.getName());
        createMktSegMasterForHiltonIpp(ms3_p1_lv3);
        final MktSeg ms3_p2_lv1 = createMktSeg("STRAIGHTMS_" + p2_lv1.getName());
        createMktSegMasterForHiltonIpp(ms3_p2_lv1);
        final MktSeg ms3_p2_lv2 = createMktSeg("STRAIGHTMS_" + p2_lv2.getName());
        createMktSegMasterForHiltonIpp(ms3_p2_lv2);
        final MktSeg ms3_p2_lv3 = createMktSeg("STRAIGHTMS_" + p2_lv3.getName());
        createMktSegMasterForHiltonIpp(ms3_p2_lv3);
        createMktSegProductMappingFor(ms3, ippProducts);

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata1 = saveUniqueFileMetadata("fm1");
        FileMetadata fileMetadata2 = saveUniqueFileMetadata("fm2");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "MS1", "P1_RC1", "201");
        populateReservationNightRecords(ms1_p1_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "MS1", "P1_RC2_P1_LV1", "202");
        populateReservationNightRecords(ms1_p1_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "MS1", "P1_RC3_P1_LV2", "203");
        populateReservationNightRecords(ms1_p1_lv3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "MS1", "P1_RC4_P1_LV3", "204");
        populateReservationNightRecords(ms1_p2_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "MS1", "P2_RC5_P2_LV1", "205");
        populateReservationNightRecords(ms1_p2_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "MS1", "P2_RC6_P2_LV2", "206");
        populateReservationNightRecords(ms1_p2_lv3.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "MS1", "P2_RC7_P2_LV3", "207");
        populateReservationNightRecords(ms1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "MS1", "P2_RC8", "208");
        populateReservationNightRecords(ms1_p2_lv2.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "MS1", "P1_RC3_P1_LV2", "209");

        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "MS1", "P1_RD1", "210");
        populateReservationNightRecords(ms2_p1_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "MS1", "P1_RD2_P1_LV1", "211");
        populateReservationNightRecords(ms2_p1_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "MS1", "P1_RD3_P1_LV2", "212");
        populateReservationNightRecords(ms2_p1_lv3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "MS1", "P1_RD4_P1_LV3", "213");
        populateReservationNightRecords(ms2_p2_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "MS1", "P2_RD5_P2_LV1", "214");
        populateReservationNightRecords(ms2_p2_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "MS1", "P2_RD6_P2_LV2", "215");
        populateReservationNightRecords(ms2_p2_lv3.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "MS1", "P2_RD7_P2_LV3", "216");
        populateReservationNightRecords(ms2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "MS1", "P2_RD8", "217");
        populateReservationNightRecords(ms2_p2_lv2.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "MS1", "P1_RD3_P1_LV2", "218");

        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(1), "STRAIGHTMS", "P1_RE1", "219");
        populateReservationNightRecords(ms3_p1_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(7), "STRAIGHTMS", "P1_RE2_P1_LV1", "220");
        populateReservationNightRecords(ms3_p1_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(16), "STRAIGHTMS", "P1_RE3_P1_LV2", "221");
        populateReservationNightRecords(ms3_p1_lv3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(32), "STRAIGHTMS", "P1_RE4_P1_LV3", "222");
        populateReservationNightRecords(ms3_p2_lv1.getId(), fileMetadata1, today.minusDays(1), today.plusDays(8), "STRAIGHTMS", "P2_RE5_P2_LV1", "223");
        populateReservationNightRecords(ms3_p2_lv2.getId(), fileMetadata1, today.minusDays(1), today.plusDays(17), "STRAIGHTMS", "P2_RE6_P2_LV2", "224");
        populateReservationNightRecords(ms3_p2_lv3.getId(), fileMetadata1, today.minusDays(7), today.plusDays(33), "STRAIGHTMS", "P2_RE7_P2_LV3", "225");
        populateReservationNightRecords(ms3.getId(), fileMetadata1, today.minusDays(1), today.plusDays(2), "STRAIGHTMS", "P2_RE8", "226");
        populateReservationNightRecords(ms3_p2_lv2.getId(), fileMetadata2, today.minusDays(1), today.plusDays(12), "STRAIGHTMS", "P1_RE3_P1_LV2", "227");

        createAMSRule("STRAIGHTMS", null, "STRAIGHTMS", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("STRAIGHTMS_" + product.getName(), null, "STRAIGHTMS_" + product.getName(), RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });
        createAMSRule("MS1", null, "MS1_U", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("MS1", null, "MS1_" + product.getName() + "_U", RateCodeTypeEnum.EQUALS, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });
        createAMSRule("MS1", null, "MS1_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("MS1", null, "MS1_" + product.getName() + "_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });

        int recordsUpdated = reservationNightService.updateMarketCodeForStraightMSForVirtualProperty("Reservation_Night", fileMetadata1.getId());

        assertAll(
                () -> assertEquals(125, recordsUpdated),
                () -> assertEquals(1, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Market_Code like '%" + p1_lv1.getName() + "%'").get(0)),
                () -> assertEquals(0, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Market_Code like '%" + p1_lv2.getName() + "%'" + " AND File_Metadata_Id = " + fileMetadata2.getId()).get(0)),
                () -> assertEquals(3, tenantCrudService().<Integer>findByNativeQuery("SELECT Count(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Market_Code = '" + ms3.getCode() + "'").get(0))
        );
    }

    private void validateRateCodeSplitsLimitedToPhysicalPropertyIPP() {
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1%P2%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1%P3%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2%P1%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2%P3%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P3%P1%'").get(0));
        assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P3%P2%'").get(0));
    }

    @Test
    void testUpdateStraightMsInResNightForHiltonIppForVP() {
        Product lvo = createProduct("LVO", Product.BAR, 1, 6);
        Product p1_lv1 = createProduct("P1_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p1_lv2 = createProduct("P1_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p1_lv3 = createProduct("P1_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        Product p2_lv1 = createProduct("P2_LV1", Product.INDEPENDENT_PRODUCT_CODE, 7, 14);
        Product p2_lv2 = createProduct("P2_LV2", Product.INDEPENDENT_PRODUCT_CODE, 15, 29);
        Product p2_lv3 = createProduct("P2_LV3", Product.INDEPENDENT_PRODUCT_CODE, 30, 999);
        createProduct("PAG1", Product.AGILE_RATES_PRODUCT_CODE, 1, 6);

        List<Product> ippProducts = List.of(p1_lv1, p1_lv2, p1_lv3, p2_lv1, p2_lv2, p2_lv3);

        final MktSeg cons = createMktSeg("CONS");
        final MktSeg discDef = createMktSeg("DISC_DEF");
        createMktSegMasterForHiltonIpp(discDef);

        createAMSRule("CONS", null, "CONS", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("CONS" + product.getName(), null, "CONS_" + product.getName(), RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });
        createAMSRule("DISC", null, "DISC_DEF", RateCodeTypeEnum.DEFAULT, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        ippProducts.forEach(product -> {
            createAMSRule("DISC" + product.getName(), null, "DISC_" + product.getName() + "_DEF", RateCodeTypeEnum.ALL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        });

        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.minusDays(2), "CONS", "P1_RC", "201");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), "CONS", "P1_RC1_P1_LV1", "202");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.plusDays(20), "CONS", "P1_RC1_P1_LV2", "203");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(3), today.plusDays(35), "CONS", "P1_RC2_P1_LV3", "204");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.plusDays(4), "CONS", "P2_RC1_P2_LV1", "205");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(4), today.plusDays(20), "CONS", "P2_RC1_P2_LV2", "206");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(3), today.plusDays(35), "CONS", "P2_RC2_P2_LV3", "207");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(1), today.plusDays(2), "DISC", "P1_RC4", "208");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(1), today.plusDays(7), "DISC", "P1_RC4_P1_LV1", "209");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(9), today.plusDays(6), "DISC", "P1_RC4_P1_LV2", "210");
        populateReservationNightRecords(discDef.getId(), fileMetadata, today.minusDays(11), today.plusDays(22), "DISC", "P1_RC4_P1_LV3", "211");
        populateReservationNightRecords(cons.getId(), fileMetadata, today.minusDays(3), today.plusDays(15), "CONS", "P2_RC1_P2_LV2", "212");
        createMktSegMasterForIpp(cons, ippProducts);
        createMktSegProductMappingFor(cons, ippProducts);
        createMktSegMasterForIpp(discDef, ippProducts);
        createMktSegProductMappingFor(discDef, ippProducts);


        int recordsUpdated = reservationNightService.updateReservationNightMarketSegmentsHiltonIpp("Reservation_Night", LocalDateUtils.toDate(today.minusDays(30)),
                LocalDateUtils.toDate(today.plusDays(30)));

        assertAll(
                () -> assertEquals(150, recordsUpdated),
                () -> assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1%' AND Market_Code LIKE '%P2%'").get(0)),
                () -> assertEquals(0, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2%' AND Market_Code LIKE '%P1%'").get(0)),
                () -> assertEquals(3, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P1%' AND Market_Code LIKE '%P1%'").get(0)),
                () -> assertEquals(4, (Integer) tenantCrudService().findByNativeQuery("SELECT COUNT(DISTINCT Reservation_Identifier) FROM Reservation_Night WHERE Rate_Code LIKE '%P2%' AND Market_Code LIKE '%P2%'").get(0)),
                () -> assertEquals("202", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p1_lv1.getName() + "'"))),
                () -> assertEquals("203", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p1_lv2.getName() + "'"))),
                () -> assertEquals("204", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p1_lv3.getName() + "'"))),
                () -> assertEquals("205", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p2_lv1.getName() + "'"))),
                () -> assertEquals("206,212", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p2_lv2.getName() + "'"))),
                () -> assertEquals("207", String.join(",", tenantCrudService().<String>findByNativeQuery("SELECT DISTINCT Reservation_Identifier FROM Reservation_Night WHERE Market_Code='CONS_" + p2_lv3.getName() + "'")))
        );

    }

    @Test
    void testUpdateReservationNightTierMSForVP() {
        final MktSeg tier1 = createMktSeg("Tier1");
        final MktSeg tier2 = createMktSeg("Tier2");
        final MktSeg tier3 = createMktSeg("Tier3");
        final MktSeg bar = createMktSeg("BAR");
        final MktSeg vp1_Tier1 = createMktSeg("VP1_Tier1");
        final MktSeg vp2_Tier1 = createMktSeg("VP2_Tier1");
        final MktSeg vp1_Tier2 = createMktSeg("VP1_Tier2");
        final MktSeg vp2_Tier2 = createMktSeg("VP2_Tier2");
        final MktSeg vp1_Tier3 = createMktSeg("VP1_Tier3");
        final MktSeg vp2_Tier3 = createMktSeg("VP2_Tier3");
        java.time.LocalDate today = java.time.LocalDate.now();
        FileMetadata fileMetadata = saveUniqueFileMetadata("fm1");
        populateReservationNightRecords(bar.getId(), fileMetadata, today, today.plusDays(1), "BAR", "RC1", "1201");
        populateReservationNightRecords(bar.getId(), fileMetadata, today, today.plusDays(1), "BAR", "LV7", "1202");
        populateReservationNightRecords(bar.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP1_LV9", "1203");
        populateReservationNightRecords(tier1.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP1_LV1", "1204");
        populateReservationNightRecords(tier1.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP2_LV1", "1205");
        populateReservationNightRecords(tier2.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP1_LV2", "1206");
        populateReservationNightRecords(tier2.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP2_LV2", "1207");
        populateReservationNightRecords(tier3.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP1_LV3", "1208");
        populateReservationNightRecords(tier3.getId(), fileMetadata, today, today.plusDays(1), "BAR", "VP2_LV3", "1209");

        int recordsUpdated = reservationNightService.updateReservationNightTierMSForVP("Reservation_Night");

        assertAll(
                () -> assertEquals(6, recordsUpdated),
                () -> assertEquals(vp1_Tier1.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP1_LV1'").get(0)),
                () -> assertEquals(vp2_Tier1.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP2_LV1'").get(0)),
                () -> assertEquals(vp1_Tier2.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP1_LV2'").get(0)),
                () -> assertEquals(vp2_Tier2.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP2_LV2'").get(0)),
                () -> assertEquals(vp1_Tier3.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP1_LV3'").get(0)),
                () -> assertEquals(vp2_Tier3.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP2_LV3'").get(0)),
                () -> assertEquals(bar.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'RC1'").get(0)),
                () -> assertEquals(bar.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'LV7'").get(0)),
                () -> assertEquals(bar.getId() , tenantCrudService().findByNativeQuery("SELECT Mkt_Seg_Id From Reservation_Night Where Rate_Code = 'VP1_LV9'").get(0))
        );
    }

    private void populateReservationNightRecords(Integer mktSegId, FileMetadata fileMetadata, java.time.LocalDate arrivalDate, java.time.LocalDate departureDate, String marketCode, String rateCode, String reservationIdentifier) {
        final List<ReservationNight> reservationNightList = new ArrayList<>();
        arrivalDate.datesUntil(departureDate).forEach((occupancyDate) -> {
            ReservationNight reservationNight = new ReservationNight();
            reservationNight.setPropertyId(TestProperty.H2.getId());
            reservationNight.setMarketSegId(mktSegId);
            reservationNight.setAccomTypeId(9);
            reservationNight.setBookedAccomTypeCode("SXBL");
            reservationNight.setMarketCode(marketCode);
            reservationNight.setRateCode(rateCode);
            reservationNight.setOccupancyDate(LocalDateUtils.toDate(occupancyDate));
            final Date date = new Date();
            reservationNight.setBookingDate(date);
            reservationNight.setCancellationDate(date);
            reservationNight.setCreateDate(date);
            reservationNight.setReservationIdentifier(reservationIdentifier);
            reservationNight.setIndividualStatus("CO");
            reservationNight.setFileMetadataId(fileMetadata.getId());
            reservationNight.setArrivalDate(LocalDateUtils.toDate(arrivalDate));
            reservationNight.setDepartureDate(LocalDateUtils.toDate(departureDate));
            final BigDecimal revenue = BigDecimal.valueOf(2349.00);
            reservationNight.setRoomRevenue(revenue);
            reservationNight.setTotalRevenue(revenue);
            reservationNightList.add(reservationNight);
        });
        tenantCrudService().save(reservationNightList);
    }

    private Product createProduct(String name, String productCode, Integer minLOS, Integer maxLos) {
        Product product;
        if (productCode.equalsIgnoreCase(Product.INDEPENDENT_PRODUCT_CODE)) {
            product = ProductBuilder.createIndependentProductProduct(name);
        } else {
            product = ProductBuilder.createAgileRateProduct(name);
        }
        product.setMinLOS(minLOS);
        product.setMaxLOS(maxLos);
        product.setStatus(TenantStatusEnum.ACTIVE);
        return tenantCrudService().save(product);
    }

    private void createMktSegMasterForIpp(MktSeg ms, List<Product> products) {
        products.forEach( product -> {
            int index = ms.getCode().indexOf('_');
            if (index != -1) {
                createMktSegMasterForHiltonIpp(createMktSeg(ms.getCode().substring(0, index) + "_" + product.getName() + ms.getCode().substring(index)));
            } else {
                createMktSegMasterForHiltonIpp(createMktSeg(ms.getCode() + "_" + product.getName()));
            }
        });
    }

    private void createMktSegMasterForHiltonIpp(MktSeg ms) {
        MarketSegmentMaster msm = new MarketSegmentMaster();
        msm.setCode(ms.getCode());
        msm.setBusinessTypeId(2);
        msm.setForecastActivityTypeId(1);
        msm.setBookingBlockPc(0);
        msm.setIsEditable(1);
        tenantCrudService().save(msm);
    }

    private void createMktSegProductMappingFor(MktSeg ms, List<Product> products) {
        List<MarketSegmentProductMapping> mappings = new ArrayList<>();
        products.forEach( product -> {
            MarketSegmentProductMapping mspm = new MarketSegmentProductMapping();
            int index = ms.getCode().indexOf('_');
            if (index != -1) {
                mspm.setMarketSegmentCode(ms.getCode().substring(0, index) + "_" + product.getName() + ms.getCode().substring(index));
            } else {
                mspm.setMarketSegmentCode(ms.getCode() + "_" + product.getName());
            }
            mspm.setProduct(product);
            mappings.add(mspm);
        });
        tenantCrudService().save(mappings);
    }

    private void addPMSRevampNewAMSRule(final String marketCode, final String rateCode, final String attribute, final String forecastType) {
        tenantCrudService.executeUpdateByNativeQuery("insert into PMS_Revamp_New_AMS_Rule values('" + marketCode
                + "', " + (null == rateCode ? "null" : "'" + rateCode + "'") + ", '" + attribute + "', " + forecastType + ", null )");
    }

    private void setUpMktSegsForMSRecoding() {
        createMktSeg("MS2_QYL");
        createMktSeg("MS2_DEF");
        createMktSeg("MS3_QYL");
        createMktSeg("MS3_DEF");
        createMktSeg("MS20_QBL");
        createMktSeg("MS20_DEF");
        createMktSeg("MS30_QBL");
        createMktSeg("MS30_DEF");
    }

    private void createAMSRule(String marketCode, String rateCode, String mappedMarketCode, RateCodeTypeEnum rateCodeType, AnalyticalMarketSegmentAttribute attribute) {
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode(marketCode);
        ams.setRateCode(rateCode);
        ams.setMappedMarketCode(mappedMarketCode);
        ams.setRateCodeType(rateCodeType);
        ams.setRank(rateCodeType.getRank());
        ams.setAttribute(attribute);
        tenantCrudService.save(ams);
    }


    private MktSeg createMktSeg(String code) {
        return createMktSegBy(code, TestProperty.H2.getId());
    }

    private MktSeg createMktSegBy(String code, Integer propertyId) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setPropertyId(propertyId);
        mktSeg.setCode(code);
        mktSeg.setStatusId(1);
        return tenantCrudService.save(mktSeg);
    }

    private int getMktSegId(String code) {
        return tenantCrudService.findByNativeQuerySingleResult("select Mkt_Seg_Id from Mkt_Seg where Mkt_Seg_Code = :code", QueryParameter.with("code", code).parameters());
    }

    private void createReservationNight(int noOfTransactions, String mktSegCode, String rateCode, String mappedMarketCode) {
        for (int j = 1; j <= noOfTransactions; j++) {
            Date occupancyDate = DateUtil.addDaysToDate(DateUtil.getDateForNextMonth(1), ++totalTransactions);
            String uniqueFileName = mktSegCode + occupancyDate.getTime();
            saveReservationNightRecord(getMktSegId(mappedMarketCode), 9, "SXBL", uniqueFileName, occupancyDate, mktSegCode, rateCode, "GRP1");
        }
    }

    private void createYieldCategoryRule(String marketCode, String rateCode, String mappedMarketCode) {
        YieldCategoryRule yieldCategoryRule = new YieldCategoryRule();
        yieldCategoryRule.setMarketCode(marketCode);
        yieldCategoryRule.setAnalyticalMarketCode(mappedMarketCode);
        yieldCategoryRule.setBookingEndDate(new LocalDate());
        yieldCategoryRule.setBookingStartDate(new LocalDate());
        yieldCategoryRule.setRank(1);
        yieldCategoryRule.setRateCode(rateCode);
        tenantCrudService.save(yieldCategoryRule);
    }

    private void createPMSMigrationMapping(PMSMigrationMappingType codeType, String currentCode, String newCode, Boolean discontinued, Boolean primary) {
        PMSMigrationMapping pmsMigrationMapping = new PMSMigrationMapping();
        pmsMigrationMapping.setPropertyCode(TestProperty.H2.name());
        pmsMigrationMapping.setCodeType(codeType);
        pmsMigrationMapping.setCurrentCode(currentCode);
        pmsMigrationMapping.setNewEquivalentCode(newCode);
        pmsMigrationMapping.setDiscontinued(discontinued);
        pmsMigrationMapping.setIsPrimaryCodeForOneToManySplits(primary);
        tenantCrudService.save(pmsMigrationMapping);
    }

    private void createReservationNightRecordsWithMktSegCodeAndRateCode(int noOfTransactions, String mktSegCode, String rateCode, Integer mktSegId, Integer accomTypeId, String bookedAccomTypeCode, String invBlockCode, List<Integer> reservationNightIds) {
        for (int j = 1; j <= noOfTransactions; j++) {
            Date occupancyDate = DateUtil.addDaysToDate(DateUtil.getDateForNextMonth(1), ++totalTransactions);
            String uniqueFileName = mktSegCode + occupancyDate.getTime();
            final ReservationNight reservationNight = saveReservationNightRecord(mktSegId, accomTypeId, bookedAccomTypeCode, uniqueFileName, occupancyDate, mktSegCode, rateCode, invBlockCode);
            reservationNightIds.add(reservationNight.getId());
        }
    }

    private ReservationNight saveReservationNightRecord(Integer mktSegId, Integer accomTypeId, String bookedAccomTypeCode, String fileName, Date occupancyDate, String marketCode, String rateCode, String invBlockCode) {
        FileMetadata fileMetadata = saveUniqueFileMetadata(fileName);
        return tenantCrudService().save(createReservationNightRecord(mktSegId, accomTypeId, bookedAccomTypeCode, fileMetadata, occupancyDate, marketCode, rateCode, invBlockCode));
    }

    private FileMetadata saveUniqueFileMetadata(String fileName) {
        FileMetadata fileMetadata = createFileMetadata();
        fileMetadata.setFileName(fileName);
        tenantCrudService().save(fileMetadata);

        return fileMetadata;
    }

    private FileMetadata createFileMetadata() {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setTenantPropertyId(TestProperty.H2.getId());
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setFileLocation("sjdsakjfd");
        fileMetadata.setPreparedDt(new Date(326955));
        fileMetadata.setPreparedTm(new Date(643));
        fileMetadata.setProcessStatusId(2);
        fileMetadata.setPastWindowSize(45);
        fileMetadata.setSnapshotDt(new Date(3463432));
        fileMetadata.setSnapshotTm(new Date(348));
        fileMetadata.setRecordTypeId(2);
        return fileMetadata;
    }

    private ReservationNight createReservationNightRecord(Integer mktSegId, Integer accomTypeId, String bookedAccomTypeCode, FileMetadata fileMetadata, Date occupancyDate, String marketCode, String rateCode, String invBlockCode) {
        ReservationNight reservationNight = new ReservationNight();
        reservationNight.setPropertyId(TestProperty.H2.getId());
        reservationNight.setMarketSegId(mktSegId);
        reservationNight.setAccomTypeId(accomTypeId);
        reservationNight.setBookedAccomTypeCode(bookedAccomTypeCode);
        reservationNight.setMarketCode(marketCode);
        reservationNight.setRateCode(rateCode);
        reservationNight.setInvBlockCode(invBlockCode);
        reservationNight.setOccupancyDate(occupancyDate);
        final Date date = new Date();
        reservationNight.setBookingDate(date);
        reservationNight.setCancellationDate(date);
        reservationNight.setCreateDate(date);
        reservationNight.setReservationIdentifier("201");
        reservationNight.setIndividualStatus("XX");
        reservationNight.setFileMetadataId(fileMetadata.getId());
        reservationNight.setArrivalDate(new Date(23432848));
        reservationNight.setDepartureDate(new Date(63432848));
        final BigDecimal revenue = BigDecimal.valueOf(2349.00);
        reservationNight.setRoomRevenue(revenue);
        reservationNight.setTotalRevenue(revenue);
        return reservationNight;
    }

    private ReservationNight createReservationNightRecordWithFilterCondition(Integer numAdults, Integer mktSegId, String status
            , Integer maxDta, Integer maxLos) {
        return createNewReservationNightRecord(DateUtil.toDate("2017-06-23")
                , "DAILY_2RO", DateUtil.toDate("2017-06-23"), DateUtil.toDate("2017-06-" + (23 + maxLos))
                , DateUtil.toDate(java.time.LocalDate.parse("2017-06-23").minusDays(maxDta).toString())
                , new BigDecimal("127.52293"), new BigDecimal("127.52293"), new BigDecimal("130"), status,
                numAdults, 0, 9, mktSegId);
    }

    private ReservationNight createNewReservationNightRecord(Date occupancyDate, String rateCode, Date arrivalDate,
                                                             Date depatureDate, Date bookingDate, BigDecimal roomrevenue,
                                                             BigDecimal totalrevenue, BigDecimal ratevalue,
                                                             String individualstatus, Integer numberofadults,
                                                             Integer numberofchildren, Integer AccomTypeId, Integer marketSegId) {
        ReservationNight newReservationNight = new ReservationNight();
        final Integer FileMetadata = 1;
        final Date createDate = DateUtil.toDate("2017-06-23");
        final String reservationIdentifier = "IN-NEW" + new Random().nextInt();
        newReservationNight.setPropertyId(TestProperty.H2.getId());
        newReservationNight.setFileMetadataId(FileMetadata);
        newReservationNight.setAccomTypeId(AccomTypeId);
        newReservationNight.setCreateDate(createDate);
        newReservationNight.setMarketSegId(marketSegId);
        newReservationNight.setReservationIdentifier(reservationIdentifier);
        newReservationNight.setOccupancyDate(occupancyDate);
        newReservationNight.setRateCode(rateCode);
        newReservationNight.setArrivalDate(arrivalDate);
        newReservationNight.setDepartureDate(depatureDate);
        newReservationNight.setBookingDate(bookingDate);
        newReservationNight.setRoomRevenue(roomrevenue);
        newReservationNight.setTotalRevenue(totalrevenue);
        newReservationNight.setRateValue(ratevalue);
        newReservationNight.setIndividualStatus(individualstatus);
        newReservationNight.setNumberAdults(numberofadults);
        newReservationNight.setNumberChildren(numberofchildren);
        tenantCrudService.save(newReservationNight);
        return newReservationNight;
    }


}
