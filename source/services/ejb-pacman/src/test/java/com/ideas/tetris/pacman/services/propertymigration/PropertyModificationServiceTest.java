package com.ideas.tetris.pacman.services.propertymigration;

import com.google.common.collect.Lists;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.MultiPropertyCrudServiceBean;
import com.ideas.tetris.pacman.services.database.DBMaintainService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PropertyModificationServiceTest {

    private static final String CLIENT_CODE = "BLKSTN";
    private static final String OLD_PROPERTY_CODE = "H1";
    private static final String NEW_PROPERTY_CODE = "H1_NEW";
    @InjectMocks
    PropertyModificationService propertyModificationService;

    @Mock
    MultiPropertyCrudServiceBean crudBean;
    @Mock
    DBMaintainService dbService;
    @Mock
    PropertyService propertyService;
    @Mock
    JobServiceLocal jobService;

    @SuppressWarnings({"unchecked"})
    @Test
    public void testGenerateReplaceStatements() {
        //setup items
        DBLoc dbLoc = new DBLoc();
        dbLoc.setDbName("000007");

        DatabaseUpdateItem numberOne = new DatabaseUpdateItem();
        numberOne.setSchemaName("dbo");
        numberOne.setTableName("Property");
        numberOne.setColumnName("Property_ID");

        DatabaseUpdateItem numberTwo = new DatabaseUpdateItem();
        numberTwo.setSchemaName("dbo");
        numberTwo.setTableName("RitzyProperty");
        numberTwo.setColumnName("propertyID");

        when(dbService.getDbLocForPropertyId(Mockito.any(Integer.class))).thenReturn(dbLoc);

        List<DatabaseUpdateItem> mockDatabaseUpdateItems = Lists.newArrayList(numberOne, numberTwo);

        when(crudBean.findByNativeQueryForSingleProperty(Mockito.same(Integer.valueOf(7)), Mockito.contains("select ic.TABLE_SCHEMA, ic.TABLE_NAME, ic.COLUMN_NAME"), nullable(Map.class), Mockito.isA(RowMapper.class)))
                .thenReturn(mockDatabaseUpdateItems);

        List<DatabaseUpdateItem> tablesToUpdate = propertyModificationService.generateReplaceStatements(5, 7);

        //expected items
        DatabaseUpdateItem expectedNumberOne
                = new DatabaseUpdateItem("000007", "dbo", "Property", "Property_ID", 5, 7);

        DatabaseUpdateItem expectedNumberTwo
                = new DatabaseUpdateItem("000007", "dbo", "RitzyProperty", "propertyID", 5, 7);

        //the test
        verify(crudBean, Mockito.times(1)).findByNativeQueryForSingleProperty(Mockito.same(Integer.valueOf(7)), Mockito.contains("select ic.TABLE_SCHEMA, ic.TABLE_NAME, ic.COLUMN_NAME"), nullable(Map.class), Mockito.isA(RowMapper.class));
        assertThat(tablesToUpdate).isEqualTo(Lists.newArrayList(expectedNumberOne, expectedNumberTwo));
    }

    @Test
    public void testApplyUpdates() {
        DatabaseUpdateItem expectedNumberOne
                = new DatabaseUpdateItem("000007", "dbo", "Property", "Property_ID", 5, 7);

        String expectedUpdateStatement = "UPDATE [000007].dbo.Property SET Property_ID = 7 WHERE Property_ID = 5";
        Mockito.when(crudBean.executeNativeUpdateOnSingleProperty(7, "EXEC sp_MSforeachtable @command1=\"ALTER TABLE ? NOCHECK CONSTRAINT ALL\"", null)).thenReturn(1);
        Mockito.when(crudBean.executeNativeUpdateOnSingleProperty(7, expectedUpdateStatement, null)).thenReturn(1);
        Mockito.when(crudBean.executeNativeUpdateOnSingleProperty(7, "EXEC sp_MSforeachtable @command1=\"ALTER TABLE ? CHECK CONSTRAINT ALL\"", null)).thenReturn(1);

        propertyModificationService.applyUpdate(expectedNumberOne);

        Mockito.verify(crudBean, Mockito.times(1)).executeNativeUpdateOnSingleProperty(7, "USE [000007] EXEC sp_MSforeachtable @command1=\"ALTER TABLE ? NOCHECK CONSTRAINT ALL\"", null);
        Mockito.verify(crudBean).executeNativeUpdateOnSingleProperty(7, expectedUpdateStatement, null);
        Mockito.verify(crudBean, Mockito.times(1)).executeNativeUpdateOnSingleProperty(7, "USE [000007] EXEC sp_MSforeachtable @command1=\"ALTER TABLE ? CHECK CONSTRAINT ALL\"", null);

        Mockito.verifyNoMoreInteractions(crudBean);
    }

    @Test
    public void testStartPropertyCodeChangeJob() {
        setUpWorkContext();
        when(propertyService.getPropertyByCode(CLIENT_CODE, OLD_PROPERTY_CODE)).thenReturn(getTestProperty());

        propertyModificationService.startPropertyCodeChangeJob(OLD_PROPERTY_CODE, NEW_PROPERTY_CODE);

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.PropertyCodeChangeJob), jobParamsCaptor.capture());
        Map<String, Object> jobParams = jobParamsCaptor.getValue();
        assertEquals(CLIENT_CODE, jobParams.get(JobParameterKey.CLIENT_CODE));
        assertEquals(OLD_PROPERTY_CODE, jobParams.get(JobParameterKey.PROPERTY_CODE));
        assertEquals("1", jobParams.get(JobParameterKey.PROPERTY_ID));
        assertEquals(NEW_PROPERTY_CODE, jobParams.get(JobParameterKey.MIGRATION_NEW_PROPERTY_CODE));
    }

    private void setUpWorkContext() {
        WorkContextType wc = new WorkContextType();
        wc.setClientId(11);
        wc.setClientCode(CLIENT_CODE);
        PacmanWorkContextHelper.setWorkContext(wc);
    }

    private Property getTestProperty() {
        Property property = new Property();
        property.setId(1);
        property.setCode(OLD_PROPERTY_CODE);
        return property;
    }
}
