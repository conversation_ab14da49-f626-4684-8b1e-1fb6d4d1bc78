package com.ideas.tetris.pacman.services.scheduledreport.domain.converter;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverridePricingDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportData;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.OutputOverrideReportCriteria;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.Date;
import java.util.TimeZone;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

/**
 * Created by idnsaw on 7/11/2016.
 */
public class OutputOverrideBARReportConverterTest extends AbstractG3JupiterTest {

    private OutputOverrideBARReportConverter outputOverrideBARReportConverter = new OutputOverrideBARReportConverter();

    @Test
    public void testCreateReportData() throws Exception {
        LocalDate occupancyDate = new LocalDate();
        LocalDate userWashExpire = new LocalDate();
        DateTime overrideLastModifiedOn = new DateTime();
        AlertService alertService = mock(AlertService.class);
        CrudService crudService = mock(CrudService.class);
        OutputOverridePricingDTO dto1 = new OutputOverridePricingDTO();
        dto1.setPropertyName("property");
        dto1.setAccomClassName("acName");
        dto1.setAccomClassCode("acCode");
        dto1.setArrivalDate(occupancyDate.toDate());
        Date createDate = new Date();
        dto1.setCreateDate(createDate);
        dto1.setLos(1);
        dto1.setIsLos("true");
        dto1.setNewOverride("new");
        dto1.setOldOverride("old");
        dto1.setOldRateCodeName("oldrc");
        dto1.setRateCodeName("newrc");
        dto1.setUserEmail("<EMAIL>");
        dto1.setUserName("user");
        dto1.setDow("Friday");
        dto1.setNotes("Some notes");

        ScheduledReport<OutputOverrideReportCriteria> report = new ScheduledReport<>();
        OutputOverrideReportCriteria reportCriteria = new OutputOverrideReportCriteria();
        report.setPropertyId(TestProperty.H1.getId());
        report.setReportCriteria(reportCriteria);

        Language lang = Language.ENGLISH;
        report.setLanguage(lang);

        Property property = new Property();
        property.setId(TestProperty.H1.getId());
        TimeZone clientTimeZone = TimeZone.getTimeZone("America/Los_Angeles");
        Mockito.when(crudService.find(Property.class, TestProperty.H1.getId())).thenReturn(property);
        Mockito.when(alertService.getPropertyTimeZone(property)).thenReturn(clientTimeZone);

        outputOverrideBARReportConverter.setGlobalCrudService(crudService);
        outputOverrideBARReportConverter.setAlertService(alertService);

        ReportData reportData = outputOverrideBARReportConverter.createReportData(Arrays.asList(dto1), report);
        ReportSheet reportSheet = reportData.getReportSheets().get(0);

        assertEquals(11, reportSheet.getColumnSize());
        for (int i = 0; i < reportSheet.getColumnSize(); i++) {
            assertTrue(reportSheet.isVisibleColumn(i));
        }

        assertEquals(1, reportSheet.getHeaderRows().size());
        Object[] headerRow = reportSheet.getHeaderRow(0);
        assertEquals(11, headerRow.length);
        assertEquals(getText("property", lang), headerRow[0]);
        assertEquals(getText("report.dow", lang), headerRow[1]);
        assertEquals(getText("common.arrivalDate", lang), headerRow[2]);
        assertEquals(getText("roomClass", lang), headerRow[3]);
        assertEquals(getText("inputOverrideReport.category", lang), headerRow[4]);
        assertEquals(getText("los", lang), headerRow[5]);
        assertEquals(getText("userSelection", lang), headerRow[6]);
        assertEquals(getText("report.systemSelection", lang), headerRow[7]);
        assertEquals(getText("notes.label", lang), headerRow[8]);
        assertEquals(getText("report.overrideLastModifiedOn", lang), headerRow[9]);
        assertEquals(getText("report.overrideLastModifiedBy", lang), headerRow[10]);

        assertEquals(1, reportSheet.getRows().size());
        Object[] row = reportSheet.getRow(0);
        assertEquals(11, row.length);
        assertEquals("property", row[0]);
        assertEquals("Friday", row[1]);
        assertEquals(occupancyDate.toString(ScheduledReportUtils.REPORT_DATE_FORMAT), row[2]);
        assertEquals("acName", row[3]);
        assertEquals(getText("new", lang), row[4]);
        assertEquals("1", row[5]);
        assertEquals("newrc", row[6]);
        assertEquals("oldrc", row[7]);
        assertEquals("Some notes", row[8]);
        assertEquals(new LocalDate(createDate).toString(ScheduledReportUtils.REPORT_24HOUR_DATE_TIME_TIMEZONE_FORMAT), row[9]);
        assertEquals("user", row[10]);

    }
}
