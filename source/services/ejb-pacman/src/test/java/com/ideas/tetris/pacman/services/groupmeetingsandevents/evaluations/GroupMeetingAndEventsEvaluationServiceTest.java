package com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDataDetailsDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpacePackage;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpacePackageType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpacePackageService;
import com.ideas.tetris.pacman.services.functionspace.util.GuestRoomRentalEnum;
import com.ideas.tetris.pacman.services.groupfloorsettings.GroupFloorConfigurationService;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.approvers.GroupMeetingAndEventsApproverService;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.*;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.input.EvaluationDetail;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.input.RevenueStream;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.input.RevenueStreamType;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.EvaluationResultUserAdjustment;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentOverride;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentsRequest;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentsResponse;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryStream;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackage;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationAncillaryService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.packaging.GroupPricingPackageService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.packaging.PackageService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationRevision;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.grouppricing.ngi.GroupEvaluationRequestService;
import com.ideas.tetris.pacman.services.grouppricing.search.GroupEvaluationSearchCriteria;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.persistence.NoResultException;
import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum.ACTIVE;
import static com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.EvaluationDetailObjectMother.*;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateResultCode.ACCEPTABLE;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCostType.*;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationObjectMother.*;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationType.GUEST_ROOM_ONLY;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.ONE_HUNDRED;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJavaLocalDate;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.apache.commons.collections.MapUtils.isNotEmpty;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupMeetingAndEventsEvaluationServiceTest {
    private static final String MARKET_SEGMENT_CORP = "CORP";
    private static final int PROPERTY_ID = 5;
    @Spy
    @InjectMocks
    private GroupMeetingAndEventsEvaluationService service;
    @Mock
    private GroupEvaluationService groupEvaluationService;
    @Mock
    private GroupPricingConfigurationService groupPricingConfigurationService;
    @Mock
    private GroupPricingConfigurationAncillaryService groupPricingConfigurationAncillaryService;
    @Mock
    private GroupPricingConfigurationConferenceAndBanquetService groupPricingConfigurationConferenceAndBanquetService;
    @Mock
    private FunctionSpaceConfigurationService functionSpaceConfigurationService;
    @Mock
    private ConferenceAndBanquetService conferenceAndBanquetService;
    @Mock
    private FunctionSpacePackageService functionSpacePackageService;
    @Mock
    private GroupPricingPackageService groupPricingPackageService;
    @Mock
    private GroupEvaluationRequestService groupEvaluationRequestService;
    @Mock
    TaxService taxService;
    @Mock
    PackageService packageService;
    @Mock
    GroupMeetingAndEventsEvaluationAdjustmentService evaluationAdjustmentService;
    @Mock
    BusinessAnalysisDashboardService businessAnalysisDashboardService;
    @Mock
    GroupFloorConfigurationService groupFloorConfigurationService;
    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    GroupMeetingAndEventsApproverService groupMeetingAndEventsApproverService;

    @Test
    void shouldGetPreviousEvaluations() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<GlobalUser> globalUserList = createGlobalUserList();
        when(groupEvaluationService.getEvaluationUsers(PROPERTY_ID)).thenReturn(globalUserList);
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(false);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(3);
        evaluationFilter.setPageSize(10);
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertArrivalDateSummary(evaluationSummaryPagination);
        verify(groupEvaluationService).getEvaluationUsers(PROPERTY_ID);
        verify(groupEvaluationService).getPaginatedGroupEvaluations(evaluationFilter);
    }

    @Test
    void shouldGetPreviousEvaluationsWithPaginationArgumentsAreNotReceived() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<GlobalUser> globalUserList = createGlobalUserList();
        when(groupEvaluationService.getEvaluationUsers(PROPERTY_ID)).thenReturn(globalUserList);
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(false);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(15);
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertArrivalDateSummary(evaluationSummaryPagination);
        verify(groupEvaluationService).getEvaluationUsers(PROPERTY_ID);
        verify(groupEvaluationService).getPaginatedGroupEvaluations(evaluationFilter);
    }

    private void assertArrivalDateSummary(EvaluationSummaryPagination evaluationSummaryPagination) {
        ArrivalDateSummary arrivalDateSummary = evaluationSummaryPagination.getRecords().get(0).getArrivalDates().iterator().next();
        assertEquals(1, evaluationSummaryPagination.getRecords().get(0).getEvaluationId());
        assertEquals("Group Name", evaluationSummaryPagination.getRecords().get(0).getGroupName());
        assertEquals("2024-12-08T00:00:00.000", arrivalDateSummary.getEvaluatedOn());
        assertEquals("2017-10-24", arrivalDateSummary.getArrivalDate());
        assertEquals("SSO User", arrivalDateSummary.getSalesPerson());
        assertEquals(300, arrivalDateSummary.getTotalRooms());
        assertEquals(3, arrivalDateSummary.getNumberOfNights());
        assertEquals(roundValue(BigDecimal.valueOf(95.84)), arrivalDateSummary.getProfitPercentageBySystem());
        assertEquals(roundValue(BigDecimal.valueOf(30000.00)), arrivalDateSummary.getContractedRevenueBySystem());
        assertEquals(roundValue(BigDecimal.valueOf(111.50)), arrivalDateSummary.getNetProfitBySystem());
        assertEquals(BigDecimal.valueOf(2.64), arrivalDateSummary.getBreakEvenRate());
        assertEquals(BigDecimal.valueOf(88.12), arrivalDateSummary.getAverageMARRate());
        assertNull(arrivalDateSummary.getNetProfitByUser());
        assertNull(arrivalDateSummary.getContractedRevenueByUser());
        assertNull(arrivalDateSummary.getProfitPercentageByUser());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00000), arrivalDateSummary.getWantRateBySystem()));
        assertNull(arrivalDateSummary.getWantRateByUser());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00000), arrivalDateSummary.getWishRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00000), arrivalDateSummary.getWalkRate()));
    }

    @Test
    void shouldGetEmptyEvaluationSummaryPaginationWhenNoEvaluationsPresent() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<GlobalUser> globalUserList = createGlobalUserList();
        when(groupEvaluationService.getEvaluationUsers(PROPERTY_ID)).thenReturn(globalUserList);
        List<GroupEvaluation> existingGroupEvaluations = new ArrayList<>();
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertEquals(0, evaluationSummaryPagination.getTotal());
        verify(groupEvaluationService).getEvaluationUsers(PROPERTY_ID);
        verify(groupEvaluationService).getPaginatedGroupEvaluations(evaluationFilter);
    }


    @Test
    void shouldGetPreviousEvaluationsWhenEvaluationHasUserAdjustedRates() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<GlobalUser> globalUserList = createGlobalUserList();
        when(groupEvaluationService.getEvaluationUsers(PROPERTY_ID)).thenReturn(globalUserList);
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(true);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        ArrivalDateSummary arrivalDateSummary = evaluationSummaryPagination.getRecords().get(0).getArrivalDates().iterator().next();
        assertEquals(1, evaluationSummaryPagination.getRecords().get(0).getEvaluationId());
        assertEquals("Group Name", evaluationSummaryPagination.getRecords().get(0).getGroupName());
        assertEquals("2024-12-08T00:00:00.000", arrivalDateSummary.getEvaluatedOn());
        assertEquals("2017-10-24", arrivalDateSummary.getArrivalDate());
        assertEquals("SSO User", arrivalDateSummary.getSalesPerson());
        assertEquals(300, arrivalDateSummary.getTotalRooms());
        assertEquals(3, arrivalDateSummary.getNumberOfNights());
        assertEquals(BigDecimal.valueOf(106.84), arrivalDateSummary.getProfitPercentageBySystem());
        assertEquals(roundValue(BigDecimal.valueOf(30000.00)), arrivalDateSummary.getContractedRevenueBySystem());
        assertEquals(roundValue(BigDecimal.valueOf(124.30)), arrivalDateSummary.getNetProfitBySystem());
        assertEquals(roundValue(BigDecimal.valueOf(100.00)), arrivalDateSummary.getWantRateBySystem());
        assertEquals(BigDecimal.valueOf(2.64), arrivalDateSummary.getBreakEvenRate());
        assertEquals(BigDecimal.valueOf(88.12), arrivalDateSummary.getAverageMARRate());
        assertEquals(roundValue(BigDecimal.valueOf(23412.80)), arrivalDateSummary.getNetProfitByUser());
        assertEquals(roundValue(BigDecimal.valueOf(23400.00)), arrivalDateSummary.getContractedRevenueByUser());
        assertEquals(roundValue(BigDecimal.valueOf(100.05)), arrivalDateSummary.getProfitPercentageByUser());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(78.00000), arrivalDateSummary.getWantRateByUser()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00000), arrivalDateSummary.getWishRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00000), arrivalDateSummary.getWalkRate()));
        verify(groupEvaluationService).getEvaluationUsers(PROPERTY_ID);
        verify(groupEvaluationService).getPaginatedGroupEvaluations(evaluationFilter);
    }

    @Test
    void shouldGetPreviousEvaluationsWithApproverDetails() {
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(false);
        addEvaluationApproverDetails(existingGroupEvaluations.get(0));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        GlobalUser approverUser = new GlobalUser();
        approverUser.setId(10124);
        approverUser.setFullName("Thomas Anderson");
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(groupEvaluationService.getGlobalUsersByIds(List.of(10124))).thenReturn(singletonList(approverUser));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertEquals(1, evaluationSummaryPagination.getRecords().size());
        ApprovalDetailsSummary approverDetails = evaluationSummaryPagination.getRecords().get(0).getApproverDetails();
        assertEquals("PARTIAL", approverDetails.getApprovalStatus());
        assertEquals("APPROVED", approverDetails.getApprovers().get(0).getStatus());
        assertEquals("Thomas Anderson", approverDetails.getApprovers().get(0).getApprover());
        assertEquals("No notes", approverDetails.getApprovers().get(0).getNotes());
    }

    @Test
    void shouldGetPreviousEvaluationsWhenApproverDetailsAreNotPresentButToggleIsEnabled() {
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(false);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        GlobalUser approverUser = new GlobalUser();
        approverUser.setId(10124);
        approverUser.setFullName("Thomas Anderson");
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(groupEvaluationService.getGlobalUsersByIds(emptyList())).thenReturn(singletonList(approverUser));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertEquals(1, evaluationSummaryPagination.getRecords().size());
        assertNull(evaluationSummaryPagination.getRecords().get(0).getApproverDetails());
    }

    @Test
    void shouldGetPreviousEvaluationsWithoutApproverDetailsWhenAproverDataIsPresentButToggleIsDisabled() {
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(false);
        addEvaluationApproverDetails(existingGroupEvaluations.get(0));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        when(groupEvaluationService.getPaginatedGroupEvaluations(evaluationFilter)).thenReturn(existingGroupEvaluations);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        EvaluationSummaryPagination evaluationSummaryPagination = service.getPreviousEvaluations(evaluationFilter);

        assertEquals(1, evaluationSummaryPagination.getRecords().size());
        assertNull(evaluationSummaryPagination.getRecords().get(0).getApproverDetails());
    }

    @Test
    void shouldGetEvaluationUsers() {
        when(groupEvaluationService.getEvaluationUsers(PROPERTY_ID)).thenReturn(createGlobalUserList());

        List<EvaluationUser> result = service.getEvaluationUsers();

        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("SSO User", result.get(0).getName());
        assertEquals(2, result.get(1).getId());
        assertEquals("BSTN User", result.get(1).getName());
    }

    private List<GlobalUser> createGlobalUserList() {
        GlobalUser globalUser1 = new GlobalUser();
        globalUser1.setId(1);
        globalUser1.setFullName("SSO User");
        globalUser1.setFirstName("SSO");
        globalUser1.setLastName("User");
        GlobalUser globalUser2 = new GlobalUser();
        globalUser2.setId(2);
        globalUser2.setFullName("BSTN User");
        globalUser2.setFirstName("BSTN");
        globalUser2.setLastName("User");
        return List.of(globalUser1, globalUser2);
    }

    private List<GroupEvaluation> createGroupEvaluations(boolean userAdjustedRates) {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setId(1);
        groupEvaluation.setEvaluationDate(LocalDateUtils.toBeginningTimeOfLocalDate(DateUtil.convertJavaToJodaLocalDate(LocalDate.of(2024, 12, 8))));
        groupEvaluation.setCreatedByUserId(1);
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setCreatedByUserId(1);
        arrivalDate.setBreakEvenRate(BigDecimal.valueOf(2.64));
        arrivalDate.setArrivalDate(DateUtil.convertJavaToJodaLocalDate(LocalDate.of(2017, 10, 24)));
        arrivalDate.setRoomGrossRevenue(BigDecimal.valueOf(116.34000));
        arrivalDate.setRoomGrossRevenue(BigDecimal.valueOf(116.34000));
        arrivalDate.setRoomGrossProfit(BigDecimal.valueOf(111.50000));
        arrivalDate.setIncrementalRoomProfit(BigDecimal.valueOf(111.50000));
        if (userAdjustedRates) {
            arrivalDate.setUserAdjustedOutput(true);
            arrivalDate.setUserAdjustedRoomRate(BigDecimal.valueOf(78.00000));
            arrivalDate.setAncillaryGrossProfit(BigDecimal.valueOf(12.80000));
            arrivalDate.setConferenceAndBanquetGrossProfit(BigDecimal.ZERO);
        }
        arrivalDate.setWalkRate(BigDecimal.valueOf(100.00000));
        arrivalDate.setWishRate(BigDecimal.valueOf(100.00000));
        return List.of(groupEvaluation);
    }

    private void addEvaluationApproverDetails(GroupEvaluation groupEvaluation) {
        GroupEvaluationApproval groupEvaluationApproval = new GroupEvaluationApproval();
        groupEvaluationApproval.setApprovalId("abcde");
        groupEvaluationApproval.setApprovalStatus("PARTIAL");
        groupEvaluationApproval.setApproverId(10124);
        groupEvaluationApproval.setApproverStatus("APPROVED");
        groupEvaluationApproval.setApproverNotes("No notes");
        groupEvaluationApproval.setGroupEvaluation(groupEvaluation);
        groupEvaluation.setGroupEvaluationApprovals(Set.of(groupEvaluationApproval));
    }

    private BigDecimal roundValue(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    @Test
    void shouldGetGroupEvaluationWithResults() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService, times(1)).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(5, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(result);
        assertEvaluatedOnDates(result);
    }

    @Test
    void shouldGetGroupEvaluationApprovalInWhileGettingGroupEvaluationById() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        Set<GroupEvaluationApproval> groupEvaluationApprovals = new HashSet<>();
        GroupEvaluationApproval evaluationApproval = new GroupEvaluationApproval();
        evaluationApproval.setGroupEvaluation(groupEvaluation);
        evaluationApproval.setEvaluatedOn(JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluation.getEvaluationDate()));
        evaluationApproval.setApprovalId("1234");
        evaluationApproval.setApprovalStatus("PENDING");
        evaluationApproval.setNotes("Evaluator notes goes here!");
        evaluationApproval.setApproverId(11403);
        evaluationApproval.setApproverStatus("PENDING");
        evaluationApproval.setApproverNotes("Approver notes goes here!");
        groupEvaluationApprovals.add(evaluationApproval);
        groupEvaluation.setGroupEvaluationApprovals(groupEvaluationApprovals);
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        GlobalUser user = new GlobalUser();
        user.setFullName("John Write");
        when(groupMeetingAndEventsApproverService.getGlobalUser(groupEvaluation.getLastUpdatedByUserId())).thenReturn(user);
        when(groupMeetingAndEventsApproverService.getGroupEvaluationById(groupEvaluation.getId())).thenReturn(groupEvaluation);

        List<GlobalUser> users = new ArrayList<>();
        GlobalUser approver = new GlobalUser();
        approver.setId(11403);
        approver.setCognitoUserId("9876543221");
        approver.setEmail("<EMAIL>");
        approver.setFullName("Ramesh Singh");
        users.add(approver);
        when(groupMeetingAndEventsApproverService.getUsersByIds(Set.of(11403))).thenReturn(users);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService, times(2)).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(5, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(result);
        assertEvaluatedOnDates(result);
        assertEquals("1234", result.getResult().getApprovalDetails().getApprovalId());
        assertEquals("PENDING", result.getResult().getApprovalDetails().getApprovalStatus());
        assertEquals("John Write", result.getResult().getApprovalDetails().getEvaluatorName());
        assertEquals("Evaluator notes goes here!", result.getResult().getApprovalDetails().getEvaluatorNotes());
        assertEquals("9876543221", result.getResult().getApprovalDetails().getApprovers().get(0).getId());
        assertEquals("Ramesh Singh", result.getResult().getApprovalDetails().getApprovers().get(0).getName());
        assertEquals("<EMAIL>", result.getResult().getApprovalDetails().getApprovers().get(0).getEmail());
        assertEquals("PENDING", result.getResult().getApprovalDetails().getApprovers().get(0).getStatus());
        assertEquals("Approver notes goes here!", result.getResult().getApprovalDetails().getApprovers().get(0).getNotes());
        verify(groupMeetingAndEventsApproverService, never()).getAuditEntriesBy(groupEvaluation);
    }

    @Test
    void shouldGetGroupEvaluationApprovalFromAuditForOlderEvaluationsWhileGettingGroupEvaluationById() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        Set<GroupEvaluationApproval> groupEvaluationApprovals = new HashSet<>();
        GroupEvaluationApproval evaluationApproval = new GroupEvaluationApproval();
        evaluationApproval.setGroupEvaluation(groupEvaluation);
        evaluationApproval.setEvaluatedOn(JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluation.getEvaluationDate()));
        evaluationApproval.setApprovalId("1234");
        evaluationApproval.setApprovalStatus("PENDING");
        evaluationApproval.setNotes("Evaluator notes goes here!");
        evaluationApproval.setApproverId(11403);
        evaluationApproval.setApproverStatus("PENDING");
        evaluationApproval.setApproverNotes("Approver notes goes here!");
        groupEvaluationApprovals.add(evaluationApproval);
        when(groupMeetingAndEventsApproverService.getAuditEntriesBy(groupEvaluation)).thenReturn(groupEvaluationApprovals);
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        GlobalUser user = new GlobalUser();
        user.setFullName("John Write");
        when(groupMeetingAndEventsApproverService.getGlobalUser(groupEvaluation.getLastUpdatedByUserId())).thenReturn(user);
        GroupEvaluation lastestGroupEvaluation = new GroupEvaluation();
        lastestGroupEvaluation.setEvaluationDate(JavaLocalDateUtils.toJodaLocalDateTime(LocalDateTime.now()));
        when(groupMeetingAndEventsApproverService.getGroupEvaluationById(groupEvaluation.getId())).thenReturn(lastestGroupEvaluation);

        List<GlobalUser> users = new ArrayList<>();
        GlobalUser approver = new GlobalUser();
        approver.setId(11403);
        approver.setCognitoUserId("9876543221");
        approver.setEmail("<EMAIL>");
        approver.setFullName("Ramesh Singh");
        users.add(approver);
        when(groupMeetingAndEventsApproverService.getUsersByIds(Set.of(11403))).thenReturn(users);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService, times(2)).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(5, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(result);
        assertEvaluatedOnDates(result);
        assertEquals("1234", result.getResult().getApprovalDetails().getApprovalId());
        assertEquals("PENDING", result.getResult().getApprovalDetails().getApprovalStatus());
        assertEquals("John Write", result.getResult().getApprovalDetails().getEvaluatorName());
        assertEquals("Evaluator notes goes here!", result.getResult().getApprovalDetails().getEvaluatorNotes());
        assertEquals("9876543221", result.getResult().getApprovalDetails().getApprovers().get(0).getId());
        assertEquals("Ramesh Singh", result.getResult().getApprovalDetails().getApprovers().get(0).getName());
        assertEquals("<EMAIL>", result.getResult().getApprovalDetails().getApprovers().get(0).getEmail());
        assertEquals("PENDING", result.getResult().getApprovalDetails().getApprovers().get(0).getStatus());
        assertEquals("Approver notes goes here!", result.getResult().getApprovalDetails().getApprovers().get(0).getNotes());
        verify(groupMeetingAndEventsApproverService).getAuditEntriesBy(groupEvaluation);
    }

    @Test
    void shouldSetTransientAndGroupIntoResultOfGroupEvaluation() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetails = new ArrayList<>();
        businessAnalysisDataDetails.add(getBusinessAnalysisDataDetailsDto("Transient", new BigDecimal("80")));
        businessAnalysisDataDetails.add(getBusinessAnalysisDataDetailsDto("Group", new BigDecimal("50")));
        businessAnalysisDataDetails.add(new BusinessAnalysisDataDetailsDto());
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(
                LocalDateUtils.toDate(evaluatedArrivalDate),
                LocalDateUtils.toDate(evaluatedArrivalDate),
                false, false, false, 7,
                "1", false)).thenReturn(businessAnalysisDataDetails);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISCONTINUED_MARKET_SEGMENTS_ENABLED)).thenReturn(false);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        assertEquals(evaluatedArrivalDate, result.getResult().getArrivalDates().get(0).getDate());
        assertEquals(new BigDecimal("80"), result.getResult().getArrivalDates().get(0).getTransientOnBooks());
        assertEquals(new BigDecimal("50"), result.getResult().getArrivalDates().get(0).getGroupOnBooks());
        assertEquals(new BigDecimal("130"), result.getResult().getArrivalDates().get(0).getTotalOnBooks());
    }

    @Test
    void shouldSetTransientAndGroupIntoResultOfGroupEvaluationUsingOnlyActiveMarketSegmentData() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetails = new ArrayList<>();
        businessAnalysisDataDetails.add(getBusinessAnalysisDataDetailsDto("Transient", new BigDecimal("70")));
        businessAnalysisDataDetails.add(getBusinessAnalysisDataDetailsDto("Group", new BigDecimal("50")));
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(
                LocalDateUtils.toDate(evaluatedArrivalDate),
                LocalDateUtils.toDate(evaluatedArrivalDate),
                false, false, false, 7,
                "1,3", false)).thenReturn(businessAnalysisDataDetails);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISCONTINUED_MARKET_SEGMENTS_ENABLED)).thenReturn(true);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        assertEquals(evaluatedArrivalDate, result.getResult().getArrivalDates().get(0).getDate());
        assertEquals(new BigDecimal("70"), result.getResult().getArrivalDates().get(0).getTransientOnBooks());
        assertEquals(new BigDecimal("50"), result.getResult().getArrivalDates().get(0).getGroupOnBooks());
        assertEquals(new BigDecimal("120"), result.getResult().getArrivalDates().get(0).getTotalOnBooks());
    }

    @Test
    void shouldSetZeroOnBooksIntoResultOfGroupEvaluationForZeroData() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(
                LocalDateUtils.toDate(evaluatedArrivalDate),
                LocalDateUtils.toDate(evaluatedArrivalDate),
                false, false, false, 7,
                "1,3", false)).thenReturn(singletonList(new BusinessAnalysisDataDetailsDto()));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISCONTINUED_MARKET_SEGMENTS_ENABLED)).thenReturn(true);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        assertEquals(evaluatedArrivalDate, result.getResult().getArrivalDates().get(0).getDate());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getTransientOnBooks());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getGroupOnBooks());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getTotalOnBooks());
    }

    @Test
    void shouldSetZeroOnBooksIntoResultOfGroupEvaluationForMissingData() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(
                LocalDateUtils.toDate(evaluatedArrivalDate),
                LocalDateUtils.toDate(evaluatedArrivalDate),
                false, false, false, 7,
                "1,3", false)).thenReturn(null);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISCONTINUED_MARKET_SEGMENTS_ENABLED)).thenReturn(true);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, null);

        assertEquals(evaluatedArrivalDate, result.getResult().getArrivalDates().get(0).getDate());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getTransientOnBooks());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getGroupOnBooks());
        assertEquals(BigDecimal.ZERO, result.getResult().getArrivalDates().get(0).getTotalOnBooks());
    }

    private static BusinessAnalysisDataDetailsDto getBusinessAnalysisDataDetailsDto(String businessType, BigDecimal onBooks) {
        BusinessAnalysisDataDetailsDto detailsDto = new BusinessAnalysisDataDetailsDto();
        detailsDto.setName(businessType);
        detailsDto.setOnBooks(onBooks);
        return detailsDto;
    }

    @Test
    void shouldGetGroupEvaluationForPastEvaluatedOnDate() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        LocalDateTime evaluatedOnDate = LocalDateTime.parse("2017-11-14T14:46:43.400");
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.getRevisionIdFromEvaluationIdAndDate(5, evaluatedOnDate)).thenReturn(1136);
        when(groupEvaluationService.getGroupEvaluationAtRevision(1136)).thenReturn(groupEvaluation);
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, evaluatedOnDate);

        verify(groupEvaluationService, never()).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService).getRevisionIdFromEvaluationIdAndDate(5, evaluatedOnDate);
        verify(groupEvaluationService).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(result);
        assertEvaluatedOnDates(result);
    }

    @Test
    void shouldGetGroupEvaluationWithInputDetailsForRC() {
        com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));

        GroupEvaluationDTO evaluatedGroup = service.getGroupEvaluation(5, null);

        verify(groupEvaluationService).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertEquals(GROUP_NAME, evaluatedGroup.getDetails().getGroupName());
        assertEquals(Integer.valueOf(5), evaluatedGroup.getDetails().getEvaluationId());
        assertEquals(ROHGroupEvaluationType.PREVIOUS_STAY.name(), evaluatedGroup.getDetails().getEvaluationType());
        assertEquals(Integer.valueOf(10), evaluatedGroup.getDetails().getMarketSegment().getId());
        assertEquals(MARKET_SEGMENT_CORP, evaluatedGroup.getDetails().getMarketSegment().getName());
        assertEquals(3, evaluatedGroup.getDetails().getNumberOfNights());
        assertEquals(new BigDecimal("100.50"), evaluatedGroup.getDetails().getPreviousStayRate());
        assertEquals(new BigDecimal("150.50"), evaluatedGroup.getDetails().getExpectedBudgetRate());
        assertEquals(GroupPricingEvaluationMethod.RC.name(), evaluatedGroup.getDetails().getEvaluationMethod());
        assertEquals(1, evaluatedGroup.getDetails().getArrivalDates().size());
        assertEquals(toJavaLocalDate(PREFERRED_DATE), evaluatedGroup.getDetails().getArrivalDates().get(0).getDate());
        assertTrue(evaluatedGroup.getDetails().getArrivalDates().get(0).isPreferred());
        assertGuestRoomDetailsForRC(evaluatedGroup);
        assertCosts(evaluatedGroup);
        assertConferenceAndBanquetRevenueStreams(evaluatedGroup, 1, new BigDecimal(80), new BigDecimal(100), new BigDecimal(5));
        assertAncillaryRevenueStreams(evaluatedGroup);
        assertEvaluatedOnDates(evaluatedGroup);
    }

    @Test
    void shouldGetGroupEvaluationWithInputDetailsForROH() {
        com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setSalesCateringBookingId("SnCBookingId");

        GroupEvaluationDTO evaluatedGroup = service.getGroupEvaluation(5, null);

        verify(groupEvaluationService).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertEquals(GROUP_NAME, evaluatedGroup.getDetails().getGroupName());
        assertEquals(Integer.valueOf(5), evaluatedGroup.getDetails().getEvaluationId());
        assertEquals(ROHGroupEvaluationType.PREVIOUS_STAY.name(), evaluatedGroup.getDetails().getEvaluationType());
        assertEquals(Integer.valueOf(10), evaluatedGroup.getDetails().getMarketSegment().getId());
        assertEquals("CORP", evaluatedGroup.getDetails().getMarketSegment().getName());
        assertEquals(1, evaluatedGroup.getDetails().getNumberOfNights());
        assertEquals(new BigDecimal("100.50"), evaluatedGroup.getDetails().getPreviousStayRate());
        assertEquals(new BigDecimal("150.50"), evaluatedGroup.getDetails().getExpectedBudgetRate());
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), evaluatedGroup.getDetails().getEvaluationMethod());
        assertEquals(1, evaluatedGroup.getDetails().getArrivalDates().size());
        assertEquals(toJavaLocalDate(PREFERRED_DATE), evaluatedGroup.getDetails().getArrivalDates().get(0).getDate());
        assertTrue(evaluatedGroup.getDetails().getArrivalDates().get(0).isPreferred());

        assertGuestRoomDetailsForROH(evaluatedGroup);
        assertCosts(evaluatedGroup);
        assertConferenceAndBanquetRevenueStreams(evaluatedGroup, 1, new BigDecimal(80), new BigDecimal(100), new BigDecimal(5));
        assertAncillaryRevenueStreams(evaluatedGroup);
        assertEvaluatedOnDates(evaluatedGroup);
        assertTrue(evaluatedGroup.getDetails().getIsFromSalesAndCatering());
    }

    @Test
    void shouldGetRevenueStreamFromFunctionSpaceGroupEvaluation() {
        com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        Set<GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetsEvaluationDetails = new HashSet<>();
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
        confAndBanq.setId(1);
        confAndBanq.setRevenue(BigDecimal.TEN);
        confAndBanq.setCommissionPercentage(new BigDecimal("0.25"));
        confAndBanq.setProfitPercentage(BigDecimal.ONE);
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup.setId(11);
        functionSpaceRevenueGroup.setName("REVGRP");
        functionSpaceRevenueGroup.setProfitPercent(new BigDecimal("0.20"));
        confAndBanq.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
        confAndBanquetsEvaluationDetails.add(confAndBanq);

        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(confAndBanquetsEvaluationDetails);
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationDTO evaluatedGroup = service.getGroupEvaluation(5, null);

        verify(groupEvaluationService, times(1)).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertEquals(GROUP_NAME, evaluatedGroup.getDetails().getGroupName());
        assertEquals(Integer.valueOf(5), evaluatedGroup.getDetails().getEvaluationId());
        assertEquals(ROHGroupEvaluationType.PREVIOUS_STAY.name(), evaluatedGroup.getDetails().getEvaluationType());
        assertEquals(Integer.valueOf(10), evaluatedGroup.getDetails().getMarketSegment().getId());
        assertEquals("CORP", evaluatedGroup.getDetails().getMarketSegment().getName());
        assertEquals(1, evaluatedGroup.getDetails().getNumberOfNights());
        assertEquals(new BigDecimal("100.50"), evaluatedGroup.getDetails().getPreviousStayRate());
        assertEquals(new BigDecimal("150.50"), evaluatedGroup.getDetails().getExpectedBudgetRate());
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), evaluatedGroup.getDetails().getEvaluationMethod());
        assertEquals(1, evaluatedGroup.getDetails().getArrivalDates().size());
        assertEquals(toJavaLocalDate(PREFERRED_DATE), evaluatedGroup.getDetails().getArrivalDates().get(0).getDate());
        assertTrue(evaluatedGroup.getDetails().getArrivalDates().get(0).isPreferred());

        assertGuestRoomDetailsForROH(evaluatedGroup);
        assertCosts(evaluatedGroup);
        assertConferenceAndBanquetRevenueStreams(evaluatedGroup, 11, new BigDecimal(20).setScale(5), new BigDecimal(10), new BigDecimal(25).setScale(5));
        assertAncillaryRevenueStreams(evaluatedGroup);
        assertEvaluatedOnDates(evaluatedGroup);
        assertFalse(evaluatedGroup.getDetails().getIsFromSalesAndCatering());
    }

    private static void assertGuestRoomDetailsForROH(GroupEvaluationDTO evaluatedGroup) {
        assertGuestRoomDetailsForROH(evaluatedGroup.getDetails());
    }

    private static void assertGuestRoomDetailsForROH(EvaluationDetail evaluationDetail) {
        assertEquals(1, evaluationDetail.getGuestRooms().size());
        assertNull(evaluationDetail.getGuestRooms().get(0).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate(), evaluationDetail.getGuestRooms().get(0).getStayDate());
        assertEquals(10, evaluationDetail.getGuestRooms().get(0).getGuestRooms());
    }

    @Test
    void shouldThrowNotFoundExceptionWhenGroupEvaluationNotFoundForPastEvaluatedOnDate() {
        LocalDateTime evaluatedOnDate = LocalDateTime.parse("2017-11-14T14:46:43.400");
        when(groupEvaluationService.getRevisionIdFromEvaluationIdAndDate(5, evaluatedOnDate)).thenThrow(NoResultException.class);
        NotFoundException exception = assertThrows(NotFoundException.class, () -> service.getGroupEvaluation(5, evaluatedOnDate));
        assertEquals("No evaluation found with id 5 and evaluation date 2017-11-14T14:46:43.400", exception.getMessage());
    }

    private static void assertAncillaryRevenueStreams(GroupEvaluationDTO evaluatedGroup) {
        EvaluationDetail evaluationDetail = evaluatedGroup.getDetails();
        assertAncillaryRevenueStreams(evaluationDetail);
    }

    private static void assertAncillaryRevenueStreams(EvaluationDetail evaluationDetail) {
        List<RevenueStream> ancillaries = evaluationDetail.getRevenueStreams().stream().filter(revenueStream -> RevenueStreamType.ANCILLARY == revenueStream.getType()).collect(Collectors.toList());
        assertEquals(1, ancillaries.get(0).getRevenueStreamId());
        assertEquals(new BigDecimal(10), ancillaries.get(0).getProfitPercentage());
        assertEquals(new BigDecimal(200), ancillaries.get(0).getRevenue());
    }

    private static void assertConferenceAndBanquetRevenueStreams(GroupEvaluationDTO evaluatedGroup, int expectedRevenueStreamId, BigDecimal expectedProfitPercentage, BigDecimal expectedRevenue, BigDecimal expectedCommissionPercentage) {
        EvaluationDetail evaluationDetail = evaluatedGroup.getDetails();
        assertConferenceAndBanquetRevenueStreams(evaluationDetail, expectedRevenueStreamId, expectedProfitPercentage, expectedRevenue, expectedCommissionPercentage);
    }

    private static void assertConferenceAndBanquetRevenueStreams(EvaluationDetail evaluationDetail, int expectedRevenueStreamId, BigDecimal expectedProfitPercentage, BigDecimal expectedRevenue, BigDecimal expectedCommissionPercentage) {
        List<RevenueStream> confAndBanquets = evaluationDetail.getRevenueStreams().stream().filter(revenueStream -> RevenueStreamType.CONFERENCE_AND_BANQUET == revenueStream.getType()).collect(Collectors.toList());
        assertEquals(expectedRevenueStreamId, confAndBanquets.get(0).getRevenueStreamId());
        assertEquals(expectedProfitPercentage, confAndBanquets.get(0).getProfitPercentage());
        assertEquals(expectedRevenue, confAndBanquets.get(0).getRevenue());
        assertEquals(expectedCommissionPercentage, confAndBanquets.get(0).getCommissionPercentage());
    }

    private static void assertCosts(GroupEvaluationDTO evaluatedGroup) {
        EvaluationDetail evaluationDetail = evaluatedGroup.getDetails();
        assertCosts(evaluationDetail);
    }

    private static void assertCosts(EvaluationDetail evaluationDetail) {
        assertEquals(2, evaluationDetail.getCosts().size());
        //COMPLIMENTARY
        assertEquals("COMPLIMENTARY", evaluationDetail.getCosts().get(0).getName());
        assertEquals("TOTAL", evaluationDetail.getCosts().get(0).getType());
        assertEquals(1, evaluationDetail.getCosts().get(0).getDivisorRooms());
        assertEquals(2, evaluationDetail.getCosts().get(0).getTotalRooms());
        assertEquals(new BigDecimal("200.50"), evaluationDetail.getCosts().get(0).getCost());
        assertEquals(new BigDecimal("5.00"), evaluationDetail.getCosts().get(0).getPercentage());
        //COMMISSION
        assertEquals("COMMISSION", evaluationDetail.getCosts().get(1).getName());
        assertNull(evaluationDetail.getCosts().get(1).getType());
        assertNull(evaluationDetail.getCosts().get(1).getDivisorRooms());
        assertNull(evaluationDetail.getCosts().get(1).getTotalRooms());
        assertNull(evaluationDetail.getCosts().get(1).getCost());
        assertEquals(new BigDecimal("2.00"), evaluationDetail.getCosts().get(1).getPercentage());
    }

    private static void assertGuestRoomDetailsForRC(GroupEvaluationDTO evaluatedGroup) {
        assertGuestRoomDetailsForRC(evaluatedGroup.getDetails());
    }

    private static void assertGuestRoomDetailsForRC(EvaluationDetail evaluationDetail) {
        assertEquals(6, evaluationDetail.getGuestRooms().size());
        assertEquals(1, evaluationDetail.getGuestRooms().get(0).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate(), evaluationDetail.getGuestRooms().get(0).getStayDate());
        assertEquals(100, evaluationDetail.getGuestRooms().get(0).getGuestRooms());
        assertEquals(1, evaluationDetail.getGuestRooms().get(1).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate().plusDays(1), evaluationDetail.getGuestRooms().get(1).getStayDate());
        assertEquals(100, evaluationDetail.getGuestRooms().get(1).getGuestRooms());
        assertEquals(1, evaluationDetail.getGuestRooms().get(2).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate().plusDays(2), evaluationDetail.getGuestRooms().get(2).getStayDate());
        assertEquals(100, evaluationDetail.getGuestRooms().get(2).getGuestRooms());

        assertEquals(2, evaluationDetail.getGuestRooms().get(3).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate(), evaluationDetail.getGuestRooms().get(3).getStayDate());
        assertEquals(200, evaluationDetail.getGuestRooms().get(3).getGuestRooms());
        assertEquals(2, evaluationDetail.getGuestRooms().get(4).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate().plusDays(1), evaluationDetail.getGuestRooms().get(4).getStayDate());
        assertEquals(200, evaluationDetail.getGuestRooms().get(4).getGuestRooms());
        assertEquals(2, evaluationDetail.getGuestRooms().get(5).getRoomTypeId());
        assertEquals(GroupEvaluationObjectMother.getPreferredDate().plusDays(2), evaluationDetail.getGuestRooms().get(5).getStayDate());
        assertEquals(200, evaluationDetail.getGuestRooms().get(5).getGuestRooms());
    }


    @Test
    void shouldGetRevisedGroupEvaluationWithResults() {
        LocalDateTime evaluatedOnDate = LocalDateTime.parse("2024-08-01T18:34:43.137");
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = buildGroupEvaluationRevisions();
        when(groupEvaluationService.getRevisionIdFromEvaluationIdAndDate(5, evaluatedOnDate)).thenReturn(14);
        when(groupEvaluationService.getGroupEvaluationAtRevision(14)).thenReturn(groupEvaluation);
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        GroupEvaluationDTO result = service.getGroupEvaluation(5, evaluatedOnDate);

        verify(groupEvaluationService).getRevisionIdFromEvaluationIdAndDate(5, evaluatedOnDate);
        verify(groupEvaluationService).getGroupEvaluationAtRevision(14);
        verify(groupEvaluationService, never()).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(result);
        assertEvaluatedOnDates(result);
    }

    @Test
    void shouldUpdateGroupNameforEvaluationWhenSavedEvaluationExists() {
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        List<GroupEvaluation> existingGroupEvaluations = createGroupEvaluations(true);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(existingGroupEvaluations);
        GroupNameDetails groupNameDetails = new GroupNameDetails();
        groupNameDetails.setGroupName("UpdatedGroupName");
        Integer evaluationId = 1;

        service.updateGroupNameForSavedEvaluation(evaluationId, groupNameDetails);

        verify(groupEvaluationService).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService).save(groupEvaluationCaptor.capture(), eq(false), eq(evaluationId.toString()));
        GroupEvaluation evaluation = groupEvaluationCaptor.getValue();
        assertEquals("UpdatedGroupName", evaluation.getGroupName());
    }

    @Test
    void shouldNotUpdateGroupNameWhenSavedEvaluationForProvidedIdDoesNotExist() {
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(emptyList());
        GroupNameDetails groupNameDetails = new GroupNameDetails();
        groupNameDetails.setGroupName("UpdatedGroupName");
        Integer evaluationId = 23;

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            service.updateGroupNameForSavedEvaluation(evaluationId, groupNameDetails);
        });

        assertEquals("Evaluation not found for the evaluation id.", thrown.getMessage());
        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(groupEvaluationService, never()).save(groupEvaluationCaptor.capture(), eq(false), eq(evaluationId.toString()));
    }

    @Test
    void shouldGetMarketSegment() {
        when(groupEvaluationService.getMarketSegmentSummaryByCode(MARKET_SEGMENT_CORP)).thenReturn(buildMarketSegmentSummary());
        MarketSegmentSummary marketSegment = service.getMarketSegment(MARKET_SEGMENT_CORP);
        assertEquals(10, marketSegment.getId());
        assertEquals(MARKET_SEGMENT_CORP, marketSegment.getName());
    }

    @Test
    void shouldThrowExceptionWhenMarketSegmentIsNotFound() {
        when(groupEvaluationService.getMarketSegmentSummaryByCode(MARKET_SEGMENT_CORP)).thenReturn(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> service.getMarketSegment(MARKET_SEGMENT_CORP));
        assertEquals("Market Segment " + MARKET_SEGMENT_CORP + " not found", exception.getMessage());
    }

    @Test
    void shouldBuildROHGroupEvaluation() {
        EvaluationDetail evaluationDetail = EvaluationDetailObjectMother.buildEvaluationDetailWithROHGuestRooms();
        when(groupEvaluationService.getMarketSegmentSummaryByCode(MARKET_SEGMENT_CORP)).thenReturn(buildMarketSegmentSummary());
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(buildAncillaries());
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        when(groupPricingConfigurationConferenceAndBanquetService.getGroupPricingConfigurationConferenceAndBanquets()).thenReturn(buildGPConfBanqs());
        when(groupPricingPackageService.getAllPackagesWithElementMaps()).thenReturn(buildGPPackages());

        GroupEvaluation groupEvaluation = service.buildGroupEvaluation(evaluationDetail);

        assertGroupEvaluation(groupEvaluation);
        assertGroupEvaluationROHfields(groupEvaluation);
        assertGroupEvaluationConferenceBanquet(groupEvaluation.getGroupEvaluationConferenceAndBanquets());
        assertGroupEvaluationPackage(groupEvaluation.getGroupPricingEvalPackagePricings());
    }

    @Test
    void shouldBuildROHGroupEvaluationWithFSRevenueStreams() {
        EvaluationDetail evaluationDetail = EvaluationDetailObjectMother.buildEvaluationDetailWithROHGuestRooms();
        when(groupEvaluationService.getMarketSegmentSummaryByCode(MARKET_SEGMENT_CORP)).thenReturn(buildMarketSegmentSummary());
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(buildAncillaries());
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        when(functionSpaceConfigurationService.getActiveRevenueGroups()).thenReturn(buildFSConfBanqs());
        when(functionSpacePackageService.getAllPackagesWithElementMaps()).thenReturn(buildFSPackages());

        GroupEvaluation groupEvaluation = service.buildGroupEvaluation(evaluationDetail);

        assertGroupEvaluation(groupEvaluation);
        assertGroupEvaluationROHfields(groupEvaluation);
        assertGroupEvaluationFunctionSpaceConferenceBaquet(groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets());
    }

    @Test
    void shouldBuildRCGroupEvaluation() {
        EvaluationDetail evaluationDetail = EvaluationDetailObjectMother.buildEvaluationDetailWithRCGuestRooms();
        when(groupEvaluationService.getMarketSegmentSummaryByCode(MARKET_SEGMENT_CORP)).thenReturn(buildMarketSegmentSummary());
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(buildAccomTypes());
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(buildAncillaries());
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        when(groupPricingConfigurationConferenceAndBanquetService.getGroupPricingConfigurationConferenceAndBanquets()).thenReturn(buildGPConfBanqs());
        when(groupPricingPackageService.getAllPackagesWithElementMaps()).thenReturn(buildGPPackages());

        GroupEvaluation groupEvaluation = service.buildGroupEvaluation(evaluationDetail);

        assertGroupEvaluation(groupEvaluation);
        assertGroupEvaluationRCfields(groupEvaluation);
        assertGroupEvaluationConferenceBanquet(groupEvaluation.getGroupEvaluationConferenceAndBanquets());
    }

    @Test
    void shouldGetGroupEvaluationWithoutEvaluatedOnDatesWhenEvaluationIsEvaluatedOnce() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        LocalDate evaluatedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7.00));
        List<GroupEvaluationRevision> groupEvaluationRevisions = singletonList(buildGroupEvaluationRevisions().get(0));
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(evaluatedArrivalDate))).thenReturn(tax);
        when(groupEvaluationService.getGroupEvaluationRevisions(5)).thenReturn(groupEvaluationRevisions);

        GroupEvaluationDTO evaluation = service.getGroupEvaluation(5, null);

        verify(groupEvaluationService).search(any(GroupEvaluationSearchCriteria.class));
        verify(groupEvaluationService, never()).getRevisionIdFromEvaluationIdAndDate(any(Integer.class), any(LocalDateTime.class));
        verify(groupEvaluationService, never()).getGroupEvaluationAtRevision(any(Integer.class));
        verify(groupEvaluationService).getGroupEvaluationRevisions(any(Integer.class));
        assertGroupEvaluationResult(evaluation);
        assertNull(evaluation.getEvaluatedOnDates());
    }

    @Test
    void shouldGetEvaluationDetailsWhenGettingOnDemandROHEvaluation() {
        com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        Set<GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetsEvaluationDetails = new HashSet<>();
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
        confAndBanq.setId(1);
        confAndBanq.setRevenue(BigDecimal.TEN);
        confAndBanq.setCommissionPercentage(new BigDecimal("0.25"));
        confAndBanq.setProfitPercentage(BigDecimal.ONE);
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup.setId(11);
        functionSpaceRevenueGroup.setName("REVGRP");
        functionSpaceRevenueGroup.setProfitPercent(new BigDecimal("0.20"));
        confAndBanq.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
        confAndBanquetsEvaluationDetails.add(confAndBanq);
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(confAndBanquetsEvaluationDetails);
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setCallbackUrl(SNC_CALLBACK_URL);
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        doReturn("BSTN").when(service).getClientCode();
        doReturn("H1").when(service).getPropertyCode();
        when(groupEvaluationRequestService.getGroupEvaluationForRequest("BSTN", "H1", SNC_EVALUTION_ID)).thenReturn(groupEvaluation);

        EvaluationDetail evaluationDetail = service.getOnDemandEvaluation(SNC_EVALUTION_ID);

        assertEquals(GROUP_NAME, evaluationDetail.getGroupName());
        assertEquals(Integer.valueOf(5), evaluationDetail.getEvaluationId());
        assertEquals(ROHGroupEvaluationType.PREVIOUS_STAY.name(), evaluationDetail.getEvaluationType());
        assertEquals(Integer.valueOf(10), evaluationDetail.getMarketSegment().getId());
        assertEquals("CORP", evaluationDetail.getMarketSegment().getName());
        assertEquals(1, evaluationDetail.getNumberOfNights());
        assertEquals(new BigDecimal("100.50"), evaluationDetail.getPreviousStayRate());
        assertEquals(new BigDecimal("150.50"), evaluationDetail.getExpectedBudgetRate());
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), evaluationDetail.getEvaluationMethod());
        assertEquals(1, evaluationDetail.getArrivalDates().size());
        assertEquals(toJavaLocalDate(PREFERRED_DATE), evaluationDetail.getArrivalDates().get(0).getDate());
        assertTrue(evaluationDetail.getArrivalDates().get(0).isPreferred());
        assertGuestRoomDetailsForROH(evaluationDetail);
        assertCosts(evaluationDetail);
        assertConferenceAndBanquetRevenueStreams(evaluationDetail, 11, new BigDecimal(20).setScale(5), new BigDecimal(10), new BigDecimal(25).setScale(5));
        assertAncillaryRevenueStreams(evaluationDetail);
        assertTrue(evaluationDetail.getIsFromSalesAndCatering());
        assertEquals(SNC_EVALUTION_ID, evaluationDetail.getSalesAndCateringEvaluationId());
        assertEquals(SNC_BOOKING_ID, evaluationDetail.getSalesAndCateringBookingId());
        assertEquals(SNC_CALLBACK_URL, evaluationDetail.getCallbackUrl());
        verify(service).getClientCode();
        verify(service).getPropertyCode();
    }

    @Test
    void shouldGetEvaluationDetailsWhenGettingOnDemandRCEvaluation() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        groupEvaluation.setId(null);
        groupEvaluation.setPreviousStayRate(null);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setCallbackUrl(SNC_CALLBACK_URL);
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        doReturn("BSTN").when(service).getClientCode();
        doReturn("H1").when(service).getPropertyCode();
        when(groupEvaluationRequestService.getGroupEvaluationForRequest("BSTN", "H1", SNC_EVALUTION_ID)).thenReturn(groupEvaluation);

        EvaluationDetail evaluationDetail = service.getOnDemandEvaluation(SNC_EVALUTION_ID);

        assertEquals(GROUP_NAME, evaluationDetail.getGroupName());
        assertNull(evaluationDetail.getEvaluationId());
        assertEquals(ROHGroupEvaluationType.NEW_EVALUATION.name(), evaluationDetail.getEvaluationType());
        assertEquals(Integer.valueOf(10), evaluationDetail.getMarketSegment().getId());
        assertEquals(MARKET_SEGMENT_CORP, evaluationDetail.getMarketSegment().getName());
        assertEquals(3, evaluationDetail.getNumberOfNights());
        assertNull(evaluationDetail.getPreviousStayRate());
        assertEquals(new BigDecimal("150.50"), evaluationDetail.getExpectedBudgetRate());
        assertEquals(GroupPricingEvaluationMethod.RC.name(), evaluationDetail.getEvaluationMethod());
        assertEquals(1, evaluationDetail.getArrivalDates().size());
        assertEquals(toJavaLocalDate(PREFERRED_DATE), evaluationDetail.getArrivalDates().get(0).getDate());
        assertTrue(evaluationDetail.getArrivalDates().get(0).isPreferred());
        assertEquals(SNC_BOOKING_ID, evaluationDetail.getSalesAndCateringBookingId());
        assertEquals(SNC_EVALUTION_ID, evaluationDetail.getSalesAndCateringEvaluationId());
        assertEquals(SNC_CALLBACK_URL, evaluationDetail.getCallbackUrl());
        assertGuestRoomDetailsForRC(evaluationDetail);
        assertCosts(evaluationDetail);
        assertConferenceAndBanquetRevenueStreams(evaluationDetail, 1, new BigDecimal(80), new BigDecimal(100), new BigDecimal(5));
        assertAncillaryRevenueStreams(evaluationDetail);
        verify(service).getClientCode();
        verify(service).getPropertyCode();

    }

    @Test
    void shouldThrowNotFoundExceptionWhenOnDemandEvaluationNotPresent() {
        String salesAndCateringEvaluationId = "salesAndCateringEvaluationId";
        doReturn("BSTN").when(service).getClientCode();
        doReturn("H1").when(service).getPropertyCode();
        when(groupEvaluationRequestService.getGroupEvaluationForRequest("BSTN", "H1", salesAndCateringEvaluationId)).thenReturn(null);

        assertThrows(NotFoundException.class, () -> service.getOnDemandEvaluation(salesAndCateringEvaluationId));
    }

    @Test
    void shouldGetEvaluationDetailsWhenSCMarketSegmentNotMappedReceivedInOnDemandEvaluationRequest() {
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        groupEvaluation.setId(null);
        groupEvaluation.setPreviousStayRate(null);
        groupEvaluation.setMarketSegment(null);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setCallbackUrl(SNC_CALLBACK_URL);
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        doReturn("BSTN").when(service).getClientCode();
        doReturn("H1").when(service).getPropertyCode();
        when(groupEvaluationRequestService.getGroupEvaluationForRequest("BSTN", "H1", SNC_EVALUTION_ID)).thenReturn(groupEvaluation);

        EvaluationDetail evaluationDetail = service.getOnDemandEvaluation(SNC_EVALUTION_ID);

        assertNotNull(evaluationDetail);
        assertNull(evaluationDetail.getMarketSegment());
    }

    @Test
    void shouldReturnCalculatedGroupEvaluationUserAdjustments() {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setId(1);
        UserAdjustmentsRequest userAdjustmentsRequest = createROHUserAdjustmentsRequest();
        userAdjustmentsRequest.setEvaluationId(1);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(List.of(groupEvaluation));
        when(evaluationAdjustmentService.calculateUserAdjustments(userAdjustmentsRequest)).thenReturn(new UserAdjustmentsResponse());
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);

        UserAdjustmentsResponse response = service.getUserAdjustments(userAdjustmentsRequest);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(1, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertFalse(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(evaluationAdjustmentService).calculateUserAdjustments(userAdjustmentsRequest);
        assertNotNull(response);
        assertFalse(response.getEnableRulesConfigurationForGP());
    }

    @Test
    void shouldReturnCalculatedGroupEvaluationUserAdjustmentsWithRulesValidationIsEnabled() {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setId(1);
        UserAdjustmentsRequest userAdjustmentsRequest = createROHUserAdjustmentsRequest();
        userAdjustmentsRequest.setEvaluationId(1);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(List.of(groupEvaluation));
        when(evaluationAdjustmentService.calculateUserAdjustments(userAdjustmentsRequest)).thenReturn(new UserAdjustmentsResponse());
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);

        UserAdjustmentsResponse response = service.getUserAdjustments(userAdjustmentsRequest);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(1, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertFalse(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(evaluationAdjustmentService).calculateUserAdjustments(userAdjustmentsRequest);
        assertNotNull(response);
        assertTrue(response.getEnableRulesConfigurationForGP());
        assertEquals(100, response.getPeakNightRooms());
    }

    private static UserAdjustmentsRequest createROHUserAdjustmentsRequest() {
        UserAdjustmentOverride rohRateOverride = new UserAdjustmentOverride();
        rohRateOverride.setAdjustmentType(UserAdjustmentOverride.UserAdjustmentType.GUEST_ROOM_ROH);
        rohRateOverride.setIsAdjusted(true);
        rohRateOverride.setOverrideRate(BigDecimal.TEN);
        UserAdjustmentsRequest userAdjustmentsRequest = new UserAdjustmentsRequest();
        userAdjustmentsRequest.setAdjustments(List.of(rohRateOverride));
        return userAdjustmentsRequest;
    }

    private void assertGroupEvaluation(GroupEvaluation groupEvaluation) {
        assertEquals(5, groupEvaluation.getId());
        assertEquals("Test Eval", groupEvaluation.getGroupName());
        assertEquals(GUEST_ROOM_ONLY, groupEvaluation.getEvaluationType());
        assertEquals(10, groupEvaluation.getMarketSegment().getId());
        assertEquals(MARKET_SEGMENT_CORP, groupEvaluation.getMarketSegment().getName());
        assertEquals(SNC_BOOKING_ID, groupEvaluation.getSalesCateringBookingId());
        assertEquals(SNC_EVALUTION_ID, groupEvaluation.getEvaluationRequestId());
        assertEquals(EvaluationDetailObjectMother.SNC_CALLBACK_URL, groupEvaluation.getCallbackUrl());
        assertTrue(groupEvaluation.isFromSalesAndCatering());
        assertEquals(3, groupEvaluation.getNumberOfNights());
        assertGroupEvaluationArrivalDates(groupEvaluation.getGroupEvaluationArrivalDates());
        assertGroupEvaluationCosts(groupEvaluation.getGroupEvaluationCosts());
        assertGroupEvaluationAncillaries(groupEvaluation.getGroupEvaluationAncillaries());
    }

    private void assertGroupEvaluationROHfields(GroupEvaluation groupEvaluation) {
        assertEquals(GroupPricingEvaluationMethod.ROH, groupEvaluation.getEvaluationMethod());
        assertGroupEvaluationDayOfStays(groupEvaluation.getGroupEvaluationDayOfStays());
    }

    private void assertGroupEvaluationRCfields(GroupEvaluation groupEvaluation) {
        assertEquals(GroupPricingEvaluationMethod.RC, groupEvaluation.getEvaluationMethod());
        assertGroupEvaluationRCDayOfStays(groupEvaluation);
    }

    private void assertGroupEvaluationRCDayOfStays(GroupEvaluation groupEvaluation) {
        assertEquals(2, groupEvaluation.getGroupEvaluationRoomTypes().size());
        Iterator<GroupEvaluationRoomType> roomTypeIterator = groupEvaluation.getGroupEvaluationRoomTypes().iterator();
        GroupEvaluationRoomType roomType1 = roomTypeIterator.next();
        assertEquals(1, roomType1.getRoomType().getId());
        assertEquals("STD", roomType1.getRoomType().getName());
        assertEquals(3, roomType1.getGroupEvaluationRoomTypeDayOfStays().size());
        GroupEvaluationRoomType roomType2 = roomTypeIterator.next();
        assertEquals(2, roomType2.getRoomType().getId());
        assertEquals("DLX", roomType2.getRoomType().getName());
        assertEquals(3, roomType2.getGroupEvaluationRoomTypeDayOfStays().size());
    }

    private void assertGroupEvaluationArrivalDates(Set<GroupEvaluationArrivalDate> groupEvaluationArrivalDates) {
        assertEquals(2, groupEvaluationArrivalDates.size());
        Iterator<GroupEvaluationArrivalDate> arrivalDateIterator = groupEvaluationArrivalDates.iterator();
        GroupEvaluationArrivalDate arrivalDate1 = arrivalDateIterator.next();
        assertEquals("2022-10-05", toJavaLocalDate(arrivalDate1.getArrivalDate()).toString());
        assertTrue(arrivalDate1.isPreferredDate());
        GroupEvaluationArrivalDate arrivalDate2 = arrivalDateIterator.next();
        assertEquals("2022-10-15", toJavaLocalDate(arrivalDate2.getArrivalDate()).toString());
        assertFalse(arrivalDate2.isPreferredDate());
    }

    private void assertGroupEvaluationDayOfStays(Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays) {
        assertEquals(3, groupEvaluationDayOfStays.size());
        Iterator<GroupEvaluationDayOfStay> dayOfStaysIterator = groupEvaluationDayOfStays.iterator();
        GroupEvaluationDayOfStay dayOfStay1 = dayOfStaysIterator.next();
        assertEquals(1, dayOfStay1.getDayOfStay());
        assertEquals(30, dayOfStay1.getNumberOfRooms());
        GroupEvaluationDayOfStay dayOfStay2 = dayOfStaysIterator.next();
        assertEquals(2, dayOfStay2.getDayOfStay());
        assertEquals(30, dayOfStay2.getNumberOfRooms());
        GroupEvaluationDayOfStay dayOfStay3 = dayOfStaysIterator.next();
        assertEquals(3, dayOfStay3.getDayOfStay());
        assertEquals(30, dayOfStay3.getNumberOfRooms());
    }

    private void assertGroupEvaluationCosts(Set<GroupEvaluationCost> groupEvaluationCosts) {
        assertEquals(3, groupEvaluationCosts.size());
        Iterator<GroupEvaluationCost> costsIterator = groupEvaluationCosts.iterator();
        GroupEvaluationCost cost1 = costsIterator.next();
        assertEquals(COMMISSION_FIXED, cost1.getGroupEvaluationCostType());
        assertNull(cost1.getDivisor());
        assertNull(cost1.getTotal());
        assertNull(cost1.getCost());
        assertEquals(BigDecimal.TEN, cost1.getPercentage());
        GroupEvaluationCost cost2 = costsIterator.next();
        assertEquals(DISCOUNTED_RATIO, cost2.getGroupEvaluationCostType());
        assertEquals(1, cost2.getDivisor());
        assertEquals(5, cost2.getTotal());
        assertEquals(ONE_HUNDRED, cost2.getCost());
        assertNull(cost2.getPercentage());
        GroupEvaluationCost cost3 = costsIterator.next();
        assertEquals(OTHER_CONCESSIONS_FIXED, cost3.getGroupEvaluationCostType());
        assertNull(cost3.getDivisor());
        assertEquals(2, cost3.getTotal());
        assertEquals(ONE_HUNDRED, cost3.getCost());
        assertNull(cost3.getPercentage());
    }

    private void assertGroupEvaluationAncillaries(Set<GroupEvaluationAncillary> groupEvaluationAncillaries) {
        assertEquals(1, groupEvaluationAncillaries.size());
        GroupEvaluationAncillary ancillary = groupEvaluationAncillaries.iterator().next();
        assertEquals(3, ancillary.getGroupPricingConfigurationAncillaryStream().getId());
        assertEquals("A1", ancillary.getGroupPricingConfigurationAncillaryStream().getRevenueStream());
        assertEquals(ONE_HUNDRED, ancillary.getRevenue());
        assertEquals(BigDecimal.TEN, ancillary.getProfitPercentage());
    }

    private void assertGroupEvaluationConferenceBanquet(Set<GroupEvaluationConferenceAndBanquet> groupEvaluationConferenceAndBanquets) {
        assertEquals(1, groupEvaluationConferenceAndBanquets.size());
        GroupEvaluationConferenceAndBanquet confAndBanq = groupEvaluationConferenceAndBanquets.iterator().next();
        assertEquals(1, confAndBanq.getGroupPricingConfigurationConferenceAndBanquet().getId());
        assertEquals("Room Rental", confAndBanq.getGroupPricingConfigurationConferenceAndBanquet().getRevenueStream());
        assertEquals(BigDecimal.TEN, confAndBanq.getProfitPercentage());
        assertEquals(ONE_HUNDRED, confAndBanq.getRevenue());
        assertEquals(BigDecimal.ONE, confAndBanq.getCommissionPercentage());
    }

    private void assertGroupEvaluationFunctionSpaceConferenceBaquet(Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanquets) {
        assertEquals(1, groupEvaluationFunctionSpaceConfAndBanquets.size());
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = groupEvaluationFunctionSpaceConfAndBanquets.iterator().next();
        assertEquals(1, confAndBanq.getFunctionSpaceRevenueGroup().getId());
        assertEquals("Room Rental", confAndBanq.getFunctionSpaceRevenueGroup().getName());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(0.10), confAndBanq.getProfitPercentage()));
        assertTrue(BigDecimalUtil.equals(ONE_HUNDRED, confAndBanq.getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(0.01), confAndBanq.getCommissionPercentage()));
    }

    private void assertGroupEvaluationPackage(Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings) {
        assertEquals(1, groupPricingEvalPackagePricings.size());
        GroupPricingEvalPackagePricing packagePricing = groupPricingEvalPackagePricings.iterator().next();
        assertEquals(7, packagePricing.getGroupPricingConfigurationPackage().getId());
        assertEquals("Full Board Basic", packagePricing.getGroupPricingConfigurationPackage().getName());
        assertEquals("Full Day", packagePricing.getFunctionSpacePackageType().getName());
        assertTrue(packagePricing.getGroupPricingConfigurationPackage().isGuestRoomIncluded());
        assertEquals(GuestRoomRentalEnum.SINGLE_OCCUPANCY.getCaptionKey(), packagePricing.getPackageNameCategory());
        assertEquals(TenantStatusEnum.NEW, packagePricing.getStatus());
        assertEquals(BigDecimal.TEN, packagePricing.getCommissionPercent());
        assertEquals(1, packagePricing.getPackagePricingDOSList().size());
        GroupPricingEvalPackagePricingDOS packagePricingDOS = packagePricing.getPackagePricingDOSList().iterator().next();
        assertEquals(1, packagePricingDOS.getDayOfStay());
        assertEquals(10, packagePricingDOS.getNoOfDelegates());
        assertEquals(TenantStatusEnum.NEW, packagePricingDOS.getStatus());
    }

    private List<AccomType> buildAccomTypes() {
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomType1.setName("STD");
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setName("DLX");
        return List.of(accomType1, accomType2);
    }

    private List<GroupPricingConfigurationConferenceAndBanquet> buildGPConfBanqs() {
        GroupPricingConfigurationConferenceAndBanquet gpConfBanq = new GroupPricingConfigurationConferenceAndBanquet();
        gpConfBanq.setId(1);
        gpConfBanq.setRevenueStream("Room Rental");
        gpConfBanq.setProfitPercentage(BigDecimal.TEN);
        return List.of(gpConfBanq);
    }

    private List<FunctionSpaceRevenueGroup> buildFSConfBanqs() {
        FunctionSpaceRevenueGroup revenueGroup = new FunctionSpaceRevenueGroup();
        revenueGroup.setId(1);
        revenueGroup.setName("Room Rental");
        revenueGroup.setProfitPercent(BigDecimal.valueOf(0.10));
        revenueGroup.setIncluded(true);
        FunctionSpaceResourceType resourceType = new FunctionSpaceResourceType();
        resourceType.setName(FunctionSpaceResourceType.OTHER_RESOURCE_TYPE);
        revenueGroup.setResourceType(resourceType);
        return List.of(revenueGroup);
    }

    private List<GroupPricingConfigurationAncillaryStream> buildAncillaries() {
        GroupPricingConfigurationAncillaryStream ancillaryStream = new GroupPricingConfigurationAncillaryStream();
        ancillaryStream.setId(3);
        ancillaryStream.setRevenueStream("A1");
        ancillaryStream.setProfitPercentage(BigDecimal.TEN);
        return List.of(ancillaryStream);
    }

    private List<GroupPricingConfigurationPackage> buildGPPackages() {
        GroupPricingConfigurationPackage groupPricingPackage = new GroupPricingConfigurationPackage(
                "Full Board Basic", new FunctionSpacePackageType("Full Day", ACTIVE), true, ACTIVE);
        groupPricingPackage.setId(7);
        return List.of(groupPricingPackage);
    }

    private List<FunctionSpacePackage> buildFSPackages() {
        FunctionSpacePackage functionSpacePackage = new FunctionSpacePackage();
        functionSpacePackage.setId(7);
        functionSpacePackage.setName("Full Board Basic");
        functionSpacePackage.setPackageType(new FunctionSpacePackageType("Full Day", ACTIVE));
        functionSpacePackage.setGuestRoomIncluded(true);
        functionSpacePackage.setStatus(ACTIVE);
        return List.of(functionSpacePackage);
    }

    @Test
    void shouldSaveGroupEvaluationResultWhenEvaluationRequestIsPresent() {
        EvaluationResultUserAdjustment evaluationResultUserAdjustment = buildEvaluationResultUserAdjustment();
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        when(groupEvaluationService.getGroupEvaluationRequest(1)).thenReturn(groupEvaluation);

        service.saveEvaluationResult(evaluationResultUserAdjustment);

        verify(groupEvaluationService).getGroupEvaluationRequest(1);
        verify(evaluationAdjustmentService).mapUserAdjustments(groupEvaluation, evaluationResultUserAdjustment);
        verify(groupEvaluationService).saveGroupEvaluation(groupEvaluation);
        verify(groupEvaluationService).deleteGroupEvaluationRequest(1);
    }

    @Test
    void shouldSaveGroupEvaluationResultWhenEvaluationIdIsPresent() {
        EvaluationResultUserAdjustment evaluationResultUserAdjustment = buildEvaluationResultUserAdjustment();
        evaluationResultUserAdjustment.setEvaluationId(1);
        evaluationResultUserAdjustment.setEvaluationRequestId(null);
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(List.of(groupEvaluation));

        service.saveEvaluationResult(evaluationResultUserAdjustment);

        ArgumentCaptor<GroupEvaluationSearchCriteria> evaluationSearchCriteriaCaptor = ArgumentCaptor.forClass(GroupEvaluationSearchCriteria.class);
        verify(groupEvaluationService).search(evaluationSearchCriteriaCaptor.capture());
        assertNotNull(evaluationSearchCriteriaCaptor.getValue());
        assertEquals(1, evaluationSearchCriteriaCaptor.getValue().getId());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateDeepResults());
        assertTrue(evaluationSearchCriteriaCaptor.getValue().isHydrateForecastGroups());
        verify(evaluationAdjustmentService).mapUserAdjustments(groupEvaluation, evaluationResultUserAdjustment);
        verify(groupEvaluationService).saveGroupEvaluation(groupEvaluation);
        verify(groupEvaluationService, never()).deleteGroupEvaluationRequest(1);
    }

    @Test
    void shouldThrowNotFoundExceptionWhenGroupEvaluationDoNotExist() {
        EvaluationResultUserAdjustment evaluationResultUserAdjustment = buildEvaluationResultUserAdjustment();
        when(groupEvaluationService.getGroupEvaluationRequest(1)).thenReturn(null);

        NotFoundException notFoundException = Assertions.assertThrows(NotFoundException.class, () -> service.saveEvaluationResult(evaluationResultUserAdjustment));
        assertEquals("Group Evaluation request with id 1 not found", notFoundException.getMessage());
    }

    @Test
    void shouldCancelEvaluationRequest() {
        when(groupEvaluationService.getGroupEvaluationRequest(1)).thenReturn(new GroupEvaluation());
        service.cancelEvaluationRequest(1);
        verify(groupEvaluationService).deleteGroupEvaluationRequest(1);
    }

    @Test
    void shouldThrowExceptionWhenEvaluationRequestToBeCancelledIsNotPresent() {
        when(groupEvaluationService.getGroupEvaluationRequest(1)).thenReturn(null);
        assertThrows(NotFoundException.class, () -> service.cancelEvaluationRequest(1));
    }

    @Test
    void verifySendEvaluationResultToSnCServiceWhenEvaluationRequestFromSCAndRedirectToCallbackURLEnable() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setPartOfMultiGroupEvaluation(false);
        when(groupEvaluationService.sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation())).thenReturn(1L);
        when(groupEvaluationService.salesAndCateringUrlRedirectEnabled()).thenReturn(true);
        when(groupEvaluationService.waitForJobStatus(1L)).thenReturn(ExecutionStatus.COMPLETED.name());

        service.sendEvaluationResultToSnCService(groupEvaluation);

        verify(groupEvaluationService).sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation());
        verify(groupEvaluationService).salesAndCateringUrlRedirectEnabled();
        verify(groupEvaluationService).waitForJobStatus(1L);
    }

    @Test
    void verifySendEvaluationResultToSnCServiceShouldThrowExceptionWhenEvaluationResultJobFailure() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setPartOfMultiGroupEvaluation(false);
        when(groupEvaluationService.sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation())).thenReturn(1L);
        when(groupEvaluationService.salesAndCateringUrlRedirectEnabled()).thenReturn(true);
        when(groupEvaluationService.waitForJobStatus(1L)).thenReturn(ExecutionStatus.FAILED.name());

        assertThrows(RuntimeException.class, () -> service.sendEvaluationResultToSnCService(groupEvaluation));

    }

    @Test
    void verifySendEvaluationResultToSnCServiceWhenEvaluationRequestFromSCAndRedirectToCallbackURLNotEnable() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setSalesCateringBookingId(SNC_BOOKING_ID);
        groupEvaluation.setEvaluationRequestId(SNC_EVALUTION_ID);
        groupEvaluation.setPartOfMultiGroupEvaluation(false);
        when(groupEvaluationService.sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation())).thenReturn(1L);
        when(groupEvaluationService.salesAndCateringUrlRedirectEnabled()).thenReturn(false);

        service.sendEvaluationResultToSnCService(groupEvaluation);

        verify(groupEvaluationService).sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation());
        verify(groupEvaluationService).salesAndCateringUrlRedirectEnabled();
        verify(groupEvaluationService, never()).waitForJobStatus(1L);
    }

    @Test
    void verifySendEvaluationResultToSnCServiceWhenEvaluationRequestNotFromSC() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setSalesCateringBookingId(null);
        groupEvaluation.setEvaluationRequestId(null);
        groupEvaluation.setPartOfMultiGroupEvaluation(false);

        service.sendEvaluationResultToSnCService(groupEvaluation);

        verify(groupEvaluationService, never()).sendEvaluationResultToNGI(groupEvaluation, true, groupEvaluation.getEvaluationRequestId(), groupEvaluation.isPartOfMultiGroupEvaluation());
        verify(groupEvaluationService, never()).salesAndCateringUrlRedirectEnabled();
        verify(groupEvaluationService, never()).waitForJobStatus(1L);
    }

    @Test
    void verifyRemoveDuplicateAndAddMissingRoomTypeDayOfStayEntriesWhenNoDuplicateAndMissingEntries() {
        GroupEvaluation evaluation = new GroupEvaluation();
        GroupEvaluation groupEvaluation = spy(evaluation);
        GroupEvaluationRoomTypeDayOfStay dayOfStay11 = new GroupEvaluationRoomTypeDayOfStay(1, 10);
        GroupEvaluationRoomTypeDayOfStay dayOfStay12 = new GroupEvaluationRoomTypeDayOfStay(2, 20);
        GroupEvaluationRoomTypeDayOfStay dayOfStay13 = new GroupEvaluationRoomTypeDayOfStay(3, 30);
        List<GroupEvaluationRoomTypeDayOfStay> roomTypeDayOfStays1 = List.of(dayOfStay11, dayOfStay12, dayOfStay13);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomType1.setName("DLX");
        GroupEvaluationRoomType groupEvaluationRoomType1 = new GroupEvaluationRoomType(accomType1, roomTypeDayOfStays1);
        dayOfStay11.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        dayOfStay12.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        dayOfStay13.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        GroupEvaluationRoomTypeDayOfStay dayOfStay21 = new GroupEvaluationRoomTypeDayOfStay(1, 7);
        GroupEvaluationRoomTypeDayOfStay dayOfStay22 = new GroupEvaluationRoomTypeDayOfStay(2, 8);
        GroupEvaluationRoomTypeDayOfStay dayOfStay23 = new GroupEvaluationRoomTypeDayOfStay(3, 9);
        List<GroupEvaluationRoomTypeDayOfStay> roomTypeDayOfStays2 = List.of(dayOfStay21, dayOfStay22, dayOfStay23);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setName("LUX");
        GroupEvaluationRoomType groupEvaluationRoomType2 = new GroupEvaluationRoomType(accomType2, roomTypeDayOfStays2);
        dayOfStay21.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        dayOfStay22.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        dayOfStay23.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        Set<GroupEvaluationRoomType> groupEvaluationRoomTypes = Set.of(groupEvaluationRoomType1, groupEvaluationRoomType2);
        groupEvaluation.setGroupEvaluationRoomTypes(groupEvaluationRoomTypes);
        doReturn(3).when(groupEvaluation).getNumberOfNights();

        service.removeDuplicateAndAddPlaceholderRoomTypeDayOfStayEntries(groupEvaluation);

        List<GroupEvaluationRoomType> actualRoomTypes = new ArrayList<>(groupEvaluation.getGroupEvaluationRoomTypes());
        assertNotNull(actualRoomTypes);
        assertFalse(actualRoomTypes.isEmpty());
        actualRoomTypes.sort(Comparator.comparing(groupEvaluationRoomType -> groupEvaluationRoomType.getRoomType().getName()));
        GroupEvaluationRoomType evaluationRoomType1 = actualRoomTypes.get(0);
        assertNotNull(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays());
        assertEquals(3, evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().size());
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(0), 1, 10);
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(1), 2, 20);
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(2), 3, 30);
        GroupEvaluationRoomType evaluationRoomType2 = actualRoomTypes.get(1);
        assertNotNull(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays());
        assertEquals(3, evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().size());
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(0), 1, 7);
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(1), 2, 8);
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(2), 3, 9);
    }

    @Test
    void shouldRemoveDuplicateAndAddMissingRoomTypeDayOfStayEntries() {
        GroupEvaluation evaluation = new GroupEvaluation();
        GroupEvaluation groupEvaluation = spy(evaluation);
        GroupEvaluationRoomTypeDayOfStay dayOfStay11 = new GroupEvaluationRoomTypeDayOfStay(1, 10);
        GroupEvaluationRoomTypeDayOfStay dayOfStay12 = new GroupEvaluationRoomTypeDayOfStay(1, 0);
        GroupEvaluationRoomTypeDayOfStay dayOfStay13 = new GroupEvaluationRoomTypeDayOfStay(3, 30);
        List<GroupEvaluationRoomTypeDayOfStay> roomTypeDayOfStays1 = List.of(dayOfStay11, dayOfStay12, dayOfStay13);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomType1.setName("DLX");
        GroupEvaluationRoomType groupEvaluationRoomType1 = new GroupEvaluationRoomType(accomType1, roomTypeDayOfStays1);
        dayOfStay11.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        dayOfStay12.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        dayOfStay13.setGroupEvaluationRoomType(groupEvaluationRoomType1);
        GroupEvaluationRoomTypeDayOfStay dayOfStay21 = new GroupEvaluationRoomTypeDayOfStay(1, 7);
        GroupEvaluationRoomTypeDayOfStay dayOfStay22 = new GroupEvaluationRoomTypeDayOfStay(2, 8);
        GroupEvaluationRoomTypeDayOfStay dayOfStay23 = new GroupEvaluationRoomTypeDayOfStay(2, 0);
        List<GroupEvaluationRoomTypeDayOfStay> roomTypeDayOfStays2 = List.of(dayOfStay21, dayOfStay22, dayOfStay23);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setName("LUX");
        GroupEvaluationRoomType groupEvaluationRoomType2 = new GroupEvaluationRoomType(accomType2, roomTypeDayOfStays2);
        dayOfStay21.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        dayOfStay22.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        dayOfStay23.setGroupEvaluationRoomType(groupEvaluationRoomType2);
        Set<GroupEvaluationRoomType> groupEvaluationRoomTypes = Set.of(groupEvaluationRoomType1, groupEvaluationRoomType2);
        groupEvaluation.setGroupEvaluationRoomTypes(groupEvaluationRoomTypes);
        doReturn(3).when(groupEvaluation).getNumberOfNights();

        service.removeDuplicateAndAddPlaceholderRoomTypeDayOfStayEntries(groupEvaluation);

        List<GroupEvaluationRoomType> actualRoomTypes = new ArrayList<>(groupEvaluation.getGroupEvaluationRoomTypes());
        assertNotNull(actualRoomTypes);
        assertFalse(actualRoomTypes.isEmpty());
        actualRoomTypes.sort(Comparator.comparing(groupEvaluationRoomType -> groupEvaluationRoomType.getRoomType().getName()));
        GroupEvaluationRoomType evaluationRoomType1 = actualRoomTypes.get(0);
        assertNotNull(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays());
        assertEquals(3, evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().size());
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(0), 1, 10);
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(1), 2, 0);
        assertRoomTypeDayOfStay(evaluationRoomType1.getGroupEvaluationRoomTypeDayOfStays().get(2), 3, 30);
        GroupEvaluationRoomType evaluationRoomType2 = actualRoomTypes.get(1);
        assertNotNull(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays());
        assertEquals(3, evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().size());
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(0), 1, 7);
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(1), 2, 8);
        assertRoomTypeDayOfStay(evaluationRoomType2.getGroupEvaluationRoomTypeDayOfStays().get(2), 3, 0);
    }

    @Test
    void verifyRemoveDuplicateAndAddMissingRoomTypeDayOfStayEntriesWhenNoRoomTypes() {
        GroupEvaluation evaluation = new GroupEvaluation();

        service.removeDuplicateAndAddPlaceholderRoomTypeDayOfStayEntries(evaluation);

        assertNotNull(evaluation.getGroupEvaluationRoomTypes());
        assertTrue(evaluation.getGroupEvaluationRoomTypes().isEmpty());
    }

    @Test
    void shouldGetSavedEvaluationResults() {
        EvaluationResultRequest request = new EvaluationResultRequest();
        request.setEvaluationId(3);
        request.setIncludeTax(true);
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        groupEvaluation.setId(3);
        when(groupEvaluationService.search(any(GroupEvaluationSearchCriteria.class))).thenReturn(singletonList(groupEvaluation));

        EvaluationResult evaluationResult = service.getEvaluationResults(request);

        assertEquals(3, evaluationResult.getEvaluationId());
        assertNull(evaluationResult.getEvaluationRequestId());
        assertArrivalDate(evaluationResult.getArrivalDates().get(0));
    }

    @Test
    void shouldGetNewEvaluationResults() {
        EvaluationResultRequest request = new EvaluationResultRequest();
        request.setEvaluationRequestId(5);
        request.setIncludeTax(true);
        GroupEvaluation groupEvaluation = buildTestGroupEvaluation();
        groupEvaluation.setId(null);
        when(groupEvaluationService.getGroupEvaluationRequest(5)).thenReturn(groupEvaluation);

        EvaluationResult evaluationResult = service.getEvaluationResults(request);

        assertNull(evaluationResult.getEvaluationId());
        assertEquals(5, evaluationResult.getEvaluationRequestId());
        assertArrivalDate(evaluationResult.getArrivalDates().get(0));
    }

    @Test
    void shouldGetApproverUsersWhenEvaluationHasApprovalData() {
        List<GroupEvaluation> existingEvaluations = createGroupEvaluations(false);
        addEvaluationApproverDetails(existingEvaluations.get(0));
        GlobalUser approverUser = new GlobalUser();
        approverUser.setId(10124);
        approverUser.setFullName("Thomas Anderson");
        when(groupEvaluationService.getGlobalUsersByIds(List.of(10124))).thenReturn(singletonList(approverUser));

        Map<Integer, String> result = service.getApproverUsers(existingEvaluations);

        assertTrue(isNotEmpty(result));
        assertTrue(result.containsKey(10124));
        assertTrue(result.containsValue("Thomas Anderson"));
    }

    @Test
    void shouldGetNullApproverUsersWhenEvaluationsDoesNotHaveApprovalData() {
        List<GroupEvaluation> existingEvaluations = createGroupEvaluations(false);
        when(groupEvaluationService.getGlobalUsersByIds(anyList())).thenReturn(null);

        Map<Integer, String> result = service.getApproverUsers(existingEvaluations);

        assertNull(result);
    }

    @Test
    void shouldDeleteGroupEvaluationApprovalWhenRulesConfigurationFeatureIsEnabled(){
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(true);
        service.deleteFromGroupEvaluationApprovalIfExist(1);
        verify(groupMeetingAndEventsApproverService).deleteFromGroupEvaluationApproval(1);
    }

    @Test
    void shouldNotDeleteGroupEvaluationApprovalWhenRulesConfigurationFeatureIsDisabled(){
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        service.deleteFromGroupEvaluationApprovalIfExist(1);
        verify(groupMeetingAndEventsApproverService, never()).deleteFromGroupEvaluationApproval(1);
    }

    @Test
    void shouldNotDeleteGroupEvaluationApprovalWhenRulesConfigurationFeatureIsEnabledButGroupEvaluationIdIsNotGiven(){
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING)).thenReturn(false);
        service.deleteFromGroupEvaluationApprovalIfExist(null);
        verify(groupMeetingAndEventsApproverService, never()).deleteFromGroupEvaluationApproval(any());
    }

    @Test
    void shouldUseFSRevenueStreamsWhenUseFSRevenueStreamToggleIsEnabled(){
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        assertTrue(service.shouldUseFSRevenueStreams());
    }

    @Test
    void shouldUseFSRevenueStreamsWhenUseFSRevenueStreamToggleIsDisabledButFunctionSpaceIsEnabledAndUseGPEvaluationForGuestRoomOnlyToggleIsEnabled(){
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value())).thenReturn(true);
        assertTrue(service.shouldUseFSRevenueStreams());
    }

    @Test
    void shouldNotUseFSRevenueStreamsWhenUseFSRevenueStreamToggleIsDisabledAndFunctionSpaceIsDisabledAndUseGPEvaluationForGuestRoomOnlyToggleIsEnabled(){
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value())).thenReturn(true);
        assertFalse(service.shouldUseFSRevenueStreams());
    }

    @Test
    void shouldNotUseFSRevenueStreamsWhenUseFSRevenueStreamToggleIsDisabledAndFunctionSpaceIsDisabledAndUseGPEvaluationForGuestRoomOnlyToggleIsDisabled(){
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value())).thenReturn(false);
        assertFalse(service.shouldUseFSRevenueStreams());
    }

    private void assertRoomTypeDayOfStay(GroupEvaluationRoomTypeDayOfStay groupEvaluationRoomTypeDayOfStay, int dayOfStay, int numberOfRooms) {
        assertEquals(dayOfStay, groupEvaluationRoomTypeDayOfStay.getDayOfStay());
        assertEquals(numberOfRooms, groupEvaluationRoomTypeDayOfStay.getNumberOfRooms());
    }


    private EvaluationResultUserAdjustment buildEvaluationResultUserAdjustment() {
        EvaluationResultUserAdjustment evaluationResultUserAdjustment = new EvaluationResultUserAdjustment();
        evaluationResultUserAdjustment.setEvaluationRequestId(1);
        return evaluationResultUserAdjustment;
    }

    private void assertGroupEvaluationResult(GroupEvaluationDTO result) {
        assertEquals(GROUP_NAME, result.getResult().getGroupName());
        assertEquals(Integer.valueOf(5), result.getResult().getEvaluationId());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(9.00), result.getResult().getTaxRate()),
                "Group Evaluation Tax Rate was expected to be 9.00 but actually was " + result.getResult().getTaxRate());
        assertTrue(BigDecimalUtil.equals(BigDecimal.ZERO, result.getResult().getPerRoomServicingCost()),
                "Group Evaluation Per Room Servicing Cost was expected to be 0.00 but actually was " + result.getResult().getTaxRate());
        assertEvaluationResultArrivalDates(result);
    }

    private void assertEvaluationResultArrivalDates(GroupEvaluationDTO groupEvaluation) {
        LocalDate expectedArrivalDate = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        EvaluationResultArrivalDate resultArrivalDate = groupEvaluation.getResult().getArrivalDates().get(0);
        assertEquals(expectedArrivalDate, resultArrivalDate.getDate());
        assertArrivalDate(resultArrivalDate);
    }

    private void assertArrivalDate(EvaluationResultArrivalDate resultArrivalDate) {
        assertTrue(resultArrivalDate.getPreferred());
        assertEquals(ACCEPTABLE, resultArrivalDate.getResultCode());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), resultArrivalDate.getSuggestedRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(150), resultArrivalDate.getBreakEvenRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), resultArrivalDate.getAverageWeightedMAR()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), resultArrivalDate.getNetIncrementalRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), resultArrivalDate.getNetIncrementalProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(32.50), resultArrivalDate.getNetProfitPercentage()),
                "Net Profit Percentage was expected to be 32.50 but actually was " + resultArrivalDate.getNetProfitPercentage());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(131), resultArrivalDate.getOverrideRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(27522.93600), resultArrivalDate.getContractualRevenue()),
                "Contractual Revenue was expected to be 27522.93600 but actually was " + resultArrivalDate.getContractualRevenue());
        assertGuestRoomDetails(resultArrivalDate.getGuestRoomRevenueDetail());
        assertRoomClassRevenueDetails(resultArrivalDate.getRoomClassRevenueDetails());
        assertAncillaryRevenueDetails(resultArrivalDate.getAncillaryRevenueDetail());
        assertConferenceBanquetRevenueDetails(resultArrivalDate.getConferenceAndBanquetRevenueDetail());
    }

    private void assertGuestRoomDetails(EvaluationResultGuestRoomRevenueDetail resultGuestRoomDetail) {
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(800), resultGuestRoomDetail.getGrossRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), resultGuestRoomDetail.getConcessionRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), resultGuestRoomDetail.getCommissionRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), resultGuestRoomDetail.getNetRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(400), resultGuestRoomDetail.getGrossProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(1000), resultGuestRoomDetail.getNetIncrementalRoomRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, resultGuestRoomDetail.getIncrementalRooms()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), resultGuestRoomDetail.getIncrementalRoomProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(3662.38600), resultGuestRoomDetail.getTotalCost()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(-900), resultGuestRoomDetail.getDisplacedRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(182), resultGuestRoomDetail.getDisplacedProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(18), resultGuestRoomDetail.getCostOfWalk()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), resultGuestRoomDetail.getNetProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(25), resultGuestRoomDetail.getNetProfitPercentage()));
        assertRatesPerNight(resultGuestRoomDetail.getRatesPerNight());
    }

    private void assertRatesPerNight(List<RatesPerNight> ratesPerNight) {
        assertEquals(2, ratesPerNight.size());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(101), ratesPerNight.get(0).getSystemRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(101.50), ratesPerNight.get(0).getOverrideRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(102), ratesPerNight.get(1).getSystemRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(102.50), ratesPerNight.get(1).getOverrideRate()));
    }

    private void assertRoomClassRevenueDetails(List<EvaluationResultRoomClassRevenueDetail> roomClassRevenueDetails) {
        assertEquals("Standard", roomClassRevenueDetails.get(0).getName());
        assertEquals(Integer.valueOf(4), roomClassRevenueDetails.get(0).getRoomTypes().get(0).getId());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), roomClassRevenueDetails.get(0).getRoomTypes().get(0).getSystemRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(144), roomClassRevenueDetails.get(0).getRoomTypes().get(0).getOverrideRate()));
    }

    private void assertAncillaryRevenueDetails(EvaluationResultRevenueStreamDetail ancillaryRevenueDetail) {
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), ancillaryRevenueDetail.getGrossRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), ancillaryRevenueDetail.getNetRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), ancillaryRevenueDetail.getGrossProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(20), ancillaryRevenueDetail.getDisplacedRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(10), ancillaryRevenueDetail.getDisplacedProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(90), ancillaryRevenueDetail.getNetProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(45), ancillaryRevenueDetail.getNetProfitPercentage()));
    }

    private void assertConferenceBanquetRevenueDetails(EvaluationResultRevenueStreamDetail confBanqRevenueDetail) {
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200), confBanqRevenueDetail.getGrossRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(5), confBanqRevenueDetail.getTotalCost()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(195), confBanqRevenueDetail.getNetRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), confBanqRevenueDetail.getGrossProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(30), confBanqRevenueDetail.getDisplacedRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(15), confBanqRevenueDetail.getDisplacedProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100), confBanqRevenueDetail.getNetProfit()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(50), confBanqRevenueDetail.getNetProfitPercentage()));
    }

    private GroupEvaluation buildTestGroupEvaluation() {
        GroupEvaluation groupEvaluation = addGroupEvaluationResultsForRoomClassEvaluation(buildGroupEvaluationForRoomClass());
        groupEvaluation.setId(5);
        groupEvaluation.setTaxRate(BigDecimal.valueOf(9.00));
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().setUserAdjustedRoomRate(BigDecimal.valueOf(131));
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().getGroupEvaluationArrivalDateAccomClasses().get(0).getAccomClass().setName("Standard");
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().setDisplacedConfAndBanquetRevenue(BigDecimal.valueOf(30));
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().setDisplacedConfAndBanquetProfit(BigDecimal.valueOf(15));
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().setCostOfWalk(BigDecimal.valueOf(18));
        groupEvaluation.getPreferredGroupEvaluationArrivalDate().setGroupEvaluationArrivalDateGuestRoomRates(buildArrivalDateGuestRoomRates());
        GroupEvaluationArrivalDateAccomType groupEvaluationArrivalDateAccomType = groupEvaluation.getPreferredGroupEvaluationArrivalDate().getGroupEvaluationArrivalDateAccomClasses().get(0)
                .getGroupEvaluationArrivalDateAccomTypes().get(0);
        groupEvaluationArrivalDateAccomType.setUserAdjustedRate(BigDecimal.valueOf(144));
        AccomType accomType = new AccomType();
        accomType.setId(4);
        accomType.setName("STD");
        groupEvaluationArrivalDateAccomType.setAccomType(accomType);

        //Input Details
        groupEvaluation.setMarketSegment(buildMarketSegmentSummary());
        //Number of nights
        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = new HashSet<>();
        GroupEvaluationDayOfStay dayOfStay = new GroupEvaluationDayOfStay();
        dayOfStay.setGroupEvaluation(groupEvaluation);
        dayOfStay.setDayOfStay(1);
        dayOfStay.setNumberOfRooms(10);
        groupEvaluationDayOfStays.add(dayOfStay);
        groupEvaluation.setGroupEvaluationDayOfStays(groupEvaluationDayOfStays);
        groupEvaluation.setPreviousStayRate(new BigDecimal("100.50"));
        groupEvaluation.setExpectedBudgetRate(new BigDecimal("150.50"));
        //Costs
        Set<GroupEvaluationCost> groupEvaluationCosts = new HashSet<>();
        groupEvaluationCosts.add(getGroupEvaluationCost(groupEvaluation, GroupEvaluationCostType.COMPLIMENTARY_FIXED, 1, 2, new BigDecimal("200.50"), new BigDecimal("5.00")));
        groupEvaluationCosts.add(getGroupEvaluationCost(groupEvaluation, COMMISSION_FIXED, null, null, null, new BigDecimal("2.00")));
        groupEvaluation.setGroupEvaluationCosts(groupEvaluationCosts);
        //Ancillary
        Set<GroupEvaluationAncillary> groupEvaluationAncillaries = new HashSet<>();
        GroupEvaluationAncillary ancillary = new GroupEvaluationAncillary();
        GroupPricingConfigurationAncillaryStream ancillaryStream = new GroupPricingConfigurationAncillaryStream();
        ancillaryStream.setId(1);
        ancillary.setProfitPercentage(new BigDecimal(10));
        ancillary.setGroupPricingConfigurationAncillaryStream(ancillaryStream);
        ancillary.setRevenue(new BigDecimal(200));
        groupEvaluationAncillaries.add(ancillary);
        groupEvaluation.setGroupEvaluationAncillaries(groupEvaluationAncillaries);
        groupEvaluation.getGroupEvaluationConferenceAndBanquets().forEach(gpEvlConfBanq ->
                gpEvlConfBanq.getGroupPricingConfigurationConferenceAndBanquet().setId(1));
        return groupEvaluation;
    }

    private List<GroupEvaluationArrivalDateGuestRoomRates> buildArrivalDateGuestRoomRates() {
        GroupEvaluationArrivalDateGuestRoomRates guestRoomRate1 = new GroupEvaluationArrivalDateGuestRoomRates();
        Date arrivalDate = DateUtil.addYearsToDate(DateUtil.getFirstDayOfNextMonth(), 1);
        guestRoomRate1.setOccupancyDate(LocalDateUtils.fromDate(arrivalDate));
        guestRoomRate1.setOptimalRate(BigDecimal.valueOf(101));
        guestRoomRate1.setUserAdjustedRate(BigDecimal.valueOf(101.50));
        GroupEvaluationArrivalDateGuestRoomRates guestRoomRate2 = new GroupEvaluationArrivalDateGuestRoomRates();
        guestRoomRate2.setOccupancyDate(LocalDateUtils.fromDate(DateUtil.addDaysToDate(arrivalDate, 1)));
        guestRoomRate2.setOptimalRate(BigDecimal.valueOf(102));
        guestRoomRate2.setUserAdjustedRate(BigDecimal.valueOf(102.50));
        return List.of(guestRoomRate1, guestRoomRate2);
    }

    private static MarketSegmentSummary buildMarketSegmentSummary() {
        MarketSegmentSummary marketSegment = new MarketSegmentSummary();
        marketSegment.setId(10);
        marketSegment.setName(MARKET_SEGMENT_CORP);
        return marketSegment;
    }

    private static GroupEvaluationCost getGroupEvaluationCost(GroupEvaluation groupEvaluation, GroupEvaluationCostType groupEvaluationCostType, Integer divisor, Integer total, BigDecimal cost, BigDecimal percentage) {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluation(groupEvaluation);
        groupEvaluationCost.setGroupEvaluationCostType(groupEvaluationCostType);
        groupEvaluationCost.setDivisor(divisor);
        groupEvaluationCost.setTotal(total);
        groupEvaluationCost.setCost(cost);
        groupEvaluationCost.setPercentage(percentage);
        return groupEvaluationCost;
    }

    private void assertEvaluatedOnDates(GroupEvaluationDTO evaluation) {
        assertNotNull(evaluation.getEvaluatedOnDates());
        assertEquals("2017-11-14T14:46:43.414", evaluation.getEvaluatedOnDates().get(0));
        assertEquals("2017-11-14T14:46:43.563", evaluation.getEvaluatedOnDates().get(1));
    }

    private List<GroupEvaluationRevision> buildGroupEvaluationRevisions() {
        GroupEvaluationRevision groupEvaluationRevision1 = new GroupEvaluationRevision();
        groupEvaluationRevision1.setEvaluationDate(JavaLocalDateUtils.toJodaLocalDateTimeWithMillis(LocalDateTime.parse("2017-11-14T14:46:43.414")));
        groupEvaluationRevision1.setRevisionId(1);

        GroupEvaluationRevision groupEvaluationRevision2 = new GroupEvaluationRevision();
        groupEvaluationRevision2.setEvaluationDate(JavaLocalDateUtils.toJodaLocalDateTimeWithMillis(LocalDateTime.parse("2017-11-14T14:46:43.563")));
        groupEvaluationRevision2.setRevisionId(2);

        return List.of(groupEvaluationRevision1, groupEvaluationRevision2);
    }
}
