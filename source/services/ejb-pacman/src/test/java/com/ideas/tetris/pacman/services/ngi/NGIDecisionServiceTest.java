package com.ideas.tetris.pacman.services.ngi;

import com.ideas.g3.integration.opera.dto.*;
import com.ideas.g3.integration.opera.services.OperaDecisionServiceLocal;
import com.ideas.g3.rule.ThreadLocalExtension;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.ngi.decision.OperaDecisionType;
import com.ideas.tetris.pacman.services.ngi.dto.*;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.OPERA_FPLOS_BY_RATE_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.OPERA_MINLOS_BY_RATE_CODE;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class NGIDecisionServiceTest {
    public static final String CLIENT_CODE = "BSTN";
    public static final String PROPERTY_CODE = "H1";
    public static final String BDE_OPERATION_TYPE = "BDE";
    public static final String PROPERTY_ID = "5";
    public static final String CONTEXT = "PACMAN.BSTN.H1";
    @RegisterExtension
    public ThreadLocalExtension threadLocalExtension = new ThreadLocalExtension();    // this will set H1 in the work context

    @Mock
    private OperaDecisionServiceLocal operaDecisionService;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @InjectMocks
    private NGIDecisionService service;

    @Mock
    private AccommodationService accommodationService;

    private LocalDate occupancyDate = new LocalDate(2010, 1, 1);
    private Integer maxSolds1 = 10;
    private Integer maxSolds2 = 20;
    private Integer maxSolds3 = 30;
    private double opportunityCost = 10.0;
    private int ceiling = 12;
    private double delta = 2.0;
    private static final String EXTERNAL_SYSTEM = "opera";

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getLastRoomValueByRoomClassDecisions() {
        //given
        Date occupancyDate1 = occupancyDate.toDate();
        Date occupancyDate2 = occupancyDate.plusDays(1).toDate();
        Date occupancyDate3 = occupancyDate.plusDays(2).toDate();

        LastRoomValueDecision lrv1 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds1, occupancyDate1, "SUITE");
        LastRoomValueDecision lrv2 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds2, occupancyDate2, "SUITE");
        LastRoomValueDecision lrv3 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds3, occupancyDate3, "SUITE");

        when(operaDecisionService.getLastRoomValueDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE)).thenReturn(Arrays.asList(lrv1, lrv2, lrv3));

        //when
        List<LastRoomValue> lastRoomValues = service.getLastRoomValueDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE);

        //then
        assertEquals(lastRoomValues.size(), 3);
        assertLastRoomValue(lastRoomValues.get(0), opportunityCost, ceiling, delta, maxSolds1, occupancyDate1, "SUITE");
        assertLastRoomValue(lastRoomValues.get(1), opportunityCost, ceiling, delta, maxSolds2, occupancyDate2, "SUITE");
        assertLastRoomValue(lastRoomValues.get(2), opportunityCost, ceiling, delta, maxSolds3, occupancyDate3, "SUITE");
    }

    @Test
    public void getLastRoomValueByRoomTypeDecisions() {
        //given
        Date occupancyDate1 = occupancyDate.toDate();
        Date occupancyDate2 = occupancyDate.plusDays(1).toDate();
        Date occupancyDate3 = occupancyDate.plusDays(2).toDate();

        LastRoomValueDecision lrv1 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds1, occupancyDate1, "SUITE");
        LastRoomValueDecision lrv2 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds2, occupancyDate2, "SUITE");
        LastRoomValueDecision lrv3 = createLastRoomValueDecision(opportunityCost, ceiling, delta, maxSolds3, occupancyDate3, "SUITE");

        when(operaDecisionService.getLastRoomValueByRoomTypeDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE)).thenReturn(List.of(lrv1, lrv2, lrv3));

        //when
        List<LastRoomValue> lastRoomValues = service.getLastRoomValueByRoomTypeDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE);

        //then
        assertEquals(3, lastRoomValues.size());
        assertLastRoomValue(lastRoomValues.get(0), opportunityCost, ceiling, delta, maxSolds1, occupancyDate1, "SUITE");
        assertLastRoomValue(lastRoomValues.get(1), opportunityCost, ceiling, delta, maxSolds2, occupancyDate2, "SUITE");
        assertLastRoomValue(lastRoomValues.get(2), opportunityCost, ceiling, delta, maxSolds3, occupancyDate3, "SUITE");
    }

    @Test
    public void getLastRoomValueDecisionsNoneExist() {
        when(operaDecisionService.getLastRoomValueDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE)).thenReturn(new ArrayList<>());

        List<LastRoomValue> lastRoomValues = service.getLastRoomValueDecisions("p123", EXTERNAL_SYSTEM, BDE_OPERATION_TYPE);

        assertTrue(lastRoomValues.isEmpty());
        verify(operaDecisionService, times(1)).getLastRoomValueDecisions("p123", "opera", BDE_OPERATION_TYPE);
    }

    @Test
    public void getStatusForLastRoomValueDecisions() {
        when(operaDecisionService.hasLRVDecisions("p123", EXTERNAL_SYSTEM, "CDP")).thenReturn(true);

        Boolean status = service.getStatusForLastRoomValueDecisions("p123", EXTERNAL_SYSTEM, "CDP");

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasLRVDecisions("p123", EXTERNAL_SYSTEM, "CDP");
    }

    @Test
    public void getStatusForHotelOverBookingDecisions() {
        when(operaDecisionService.hasHotelOverbookingDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForHotelOverbookingDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasHotelOverbookingDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForRoomTypeOverBookingDecisions() {
        when(operaDecisionService.hasAccomOverbookingDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForRoomTypeOverbookingDecisions("p123", EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasAccomOverbookingDecisions("p123", EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForDailyBarDecisions() {
        when(operaDecisionService.hasDailyBarDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusDailyBarDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasDailyBarDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForBarFPLOSByHierarchyDecisions() {
        when(operaDecisionService.hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForBarFPLOSByHierarchyDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForBarFPLOSByHotelDecisions() {
        when(operaDecisionService.hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForBarFPLOSByHotelDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForBarFPLOSByRoomTypeDecisions() {
        when(operaDecisionService.hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForBarFPLOSByRoomTypeDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getStatusForBarByLOSDecisions() {
        when(operaDecisionService.hasBarByLOSDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        Boolean status = service.getStatusForBarByLOSDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasBarByLOSDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void isStatusForBarByLOSByRoomTypeDecisions() {
        when(operaDecisionService.hasBarByLOSByRoomTypeDecisions(EXTERNAL_SYSTEM)).thenReturn(true);

        boolean status = service.isStatusForBarByLOSByRoomTypeDecisions(EXTERNAL_SYSTEM);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasBarByLOSByRoomTypeDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getHotelOverbookingDecisions() {
        when(operaDecisionService.getHotelOverbookingDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createHotelOverBookingDecision()));
        List<HotelOverbooking> hotelOverbookings = service.getHotelOverbookingDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(hotelOverbookings.size(), 1);
        assertEquals(hotelOverbookings.get(0).getPropertyCode(), PROPERTY_CODE);
        assertEquals(hotelOverbookings.get(0).getClientCode(), CLIENT_CODE);
        assertEquals(hotelOverbookings.get(0).getOverbooking(), Integer.valueOf(1));
        assertEquals(hotelOverbookings.get(0).getAuthorizedCapacity(), new BigDecimal(1));
        verify(operaDecisionService, times(1)).getHotelOverbookingDecisions("p123", "opera");
    }

    private HotelOverbookingDecision createHotelOverBookingDecision() {
        HotelOverbookingDecision hotelOverbookingDecision = new HotelOverbookingDecision();
        hotelOverbookingDecision.setOccupancyDate(new Date());
        hotelOverbookingDecision.setOverbooking(1);
        hotelOverbookingDecision.setAuthorizedCapacity(new BigDecimal(1));
        return hotelOverbookingDecision;
    }

    @Test
    public void getRoomTypeOverbookingDecisions() {
        when(operaDecisionService.getAccomOverbookingDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createRoomTypeOverBookingDecision()));
        List<RoomTypeOverbooking> roomTypeOverbookings = service.getRoomTypeOverbookingDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(roomTypeOverbookings.size(), 1);
        assertEquals(roomTypeOverbookings.get(0).getPropertyCode(), PROPERTY_CODE);
        assertEquals(roomTypeOverbookings.get(0).getClientCode(), CLIENT_CODE);
        assertEquals(roomTypeOverbookings.get(0).getOverbooking(), Integer.valueOf(1));
        assertEquals(roomTypeOverbookings.get(0).getRoomTypeCode(), "XYZ");
        assertEquals(roomTypeOverbookings.get(0).getAuthorizedCapacity(), new BigDecimal(1));
        verify(operaDecisionService, times(1)).getAccomOverbookingDecisions("p123", "opera");
    }

    private AccomOverbookingDecision createRoomTypeOverBookingDecision() {
        AccomOverbookingDecision accomOverbookingDecision = new AccomOverbookingDecision();
        accomOverbookingDecision.setOccupancyDate(new Date());
        accomOverbookingDecision.setAccomType("XYZ");
        accomOverbookingDecision.setOverbooking(1);
        accomOverbookingDecision.setAuthorizedCapacity(new BigDecimal(1));
        return accomOverbookingDecision;
    }

    @Test
    public void getDailyBarDecisions() {
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecision()));
        List<DailyBar> dailyBars = service.getDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    @Test
    public void getDailyBarDecisionsWithChildAgeBucketsAndExtraChild() {
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals(BigDecimal.ZERO, dailyBars.get(0).getChildAgeBucketOneRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketTwoRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketThreeRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    @Test
    public void getDailyBarDecisionsWithChildAgeBucketsAndNoExtraChild() {
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals(BigDecimal.ZERO, dailyBars.get(0).getChildAgeBucketOneRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketTwoRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketThreeRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    @Test
    public void shouldSendExtraChildValueWhenParameterIsNotConfiguredForAnyIntegration() {
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    @Test
    public void getAgileRateDecisions() {
        when(operaDecisionService.getAgileRateDecisions("p123", EXTERNAL_SYSTEM, null, null)).thenReturn(singletonList(createAgileRateDecision()));
        List<DailyBar> dailyBars = service.getAgileRateDecision("p123", EXTERNAL_SYSTEM, null, null);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertNull(dailyBars.get(0).getChildOneRate());
        assertNull(dailyBars.get(0).getChildTwoRate());
        assertNull(dailyBars.get(0).getChildThreeRate());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getAgileRateDecisions("p123", EXTERNAL_SYSTEM, null, null);
    }

    @Test
    public void shouldGetAgileRateDecisionsByUploadWindowDates() {
        Date currentDate = DateUtil.getCurrentDate();
        when(operaDecisionService.getAgileRateDecisions("p123", EXTERNAL_SYSTEM, currentDate, currentDate)).thenReturn(singletonList(createAgileRateDecision()));
        List<DailyBar> dailyBars = service.getAgileRateDecision("p123", EXTERNAL_SYSTEM, currentDate, currentDate);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertNull(dailyBars.get(0).getChildOneRate());
        assertNull(dailyBars.get(0).getChildTwoRate());
        assertNull(dailyBars.get(0).getChildThreeRate());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getAgileRateDecisions("p123", EXTERNAL_SYSTEM, currentDate, currentDate);
    }

    @Test
    public void getAgileRateDecisionsWithChildAgeBucketsAndExtraChild() {
        when(operaDecisionService.getAgileRateDecisions("p123", EXTERNAL_SYSTEM, null, null)).thenReturn(singletonList(createAgileRateDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getAgileRateDecision("p123", EXTERNAL_SYSTEM, null, null);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals(BigDecimal.ZERO, dailyBars.get(0).getChildAgeBucketOneRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketTwoRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketThreeRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getAgileRateDecisions("p123", "opera", null, null);
    }

    @Test
    public void getAgileRateDecisionsWithChildAgeBucketsAndNoExtraChild() {
        when(operaDecisionService.getAgileRateDecisions("p123", EXTERNAL_SYSTEM, null, null)).thenReturn(singletonList(createAgileRateDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getAgileRateDecision("p123", EXTERNAL_SYSTEM, null, null);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals(BigDecimal.ZERO, dailyBars.get(0).getChildAgeBucketOneRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketTwoRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildAgeBucketThreeRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getAgileRateDecisions("p123", "opera", null, null);
    }

    @Test
    public void shouldSendExtraChildValueForAgileRatesWhenParameterIsNotConfiguredForAnyIntegration() {
        when(operaDecisionService.getAgileRateDecisions("p123", EXTERNAL_SYSTEM, null, null)).thenReturn(singletonList(createAgileRateDecisionWithChildAgeBuckets()));
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(CONTEXT);
        List<DailyBar> dailyBars = service.getAgileRateDecision("p123", EXTERNAL_SYSTEM, null, null);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        verify(operaDecisionService, times(1)).getAgileRateDecisions("p123", "opera", null, null);
    }

    @Test
    public void getDailyBarDecisionsWithNoOfChildren() {
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecisionWithNoOfChildren()));
        List<DailyBar> dailyBars = service.getDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getChildOneRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getChildTwoRate());
        assertEquals(BigDecimal.ZERO, dailyBars.get(0).getChildThreeRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("ABC", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    private DailyBarDecision createDailyBarDecision() {
        DailyBarDecision dailyBarDecision = new DailyBarDecision();
        dailyBarDecision.setExtraAdultRate(BigDecimal.ONE);
        dailyBarDecision.setExtraChildRate(BigDecimal.ONE);
        dailyBarDecision.setOneAdultRate(BigDecimal.ONE);
        dailyBarDecision.setTwoAdultRate(BigDecimal.ONE);
        dailyBarDecision.setThreeAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFourAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFiveAdultRate(BigDecimal.ONE);
        dailyBarDecision.setRateDate(new Date());
        dailyBarDecision.setRateCode("XYZ");
        dailyBarDecision.setRoomType("ABC");
        return dailyBarDecision;
    }

    private DailyBarDecision createAgileRateDecision() {
        DailyBarDecision dailyBarDecision = new DailyBarDecision();
        dailyBarDecision.setExtraAdultRate(BigDecimal.ONE);
        dailyBarDecision.setExtraChildRate(BigDecimal.ONE);
        dailyBarDecision.setOneAdultRate(BigDecimal.ONE);
        dailyBarDecision.setTwoAdultRate(BigDecimal.ONE);
        dailyBarDecision.setThreeAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFourAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFiveAdultRate(BigDecimal.ONE);
        dailyBarDecision.setRateDate(new Date());
        dailyBarDecision.setRateCode("XYZ");
        dailyBarDecision.setRoomType("ABC");
        return dailyBarDecision;
    }

    private DailyBarDecision createDailyBarDecisionWithChildAgeBuckets() {
        DailyBarDecision dailyBarDecision = createDailyBarDecision();
        dailyBarDecision.setChildAgeOneRate(BigDecimal.ZERO);
        dailyBarDecision.setChildAgeTwoRate(BigDecimal.TEN);
        dailyBarDecision.setChildAgeThreeRate(BigDecimal.TEN);
        return dailyBarDecision;
    }

    private DailyBarDecision createAgileRateDecisionWithChildAgeBuckets() {
        DailyBarDecision dailyBarDecision = createAgileRateDecision();
        dailyBarDecision.setChildAgeOneRate(BigDecimal.ZERO);
        dailyBarDecision.setChildAgeTwoRate(BigDecimal.TEN);
        dailyBarDecision.setChildAgeThreeRate(BigDecimal.TEN);
        return dailyBarDecision;
    }

    private DailyBarDecision createDailyBarDecisionWithNoOfChildren() {
        DailyBarDecision dailyBarDecision = createDailyBarDecision();
        dailyBarDecision.setOneChildRate(BigDecimal.TEN);
        dailyBarDecision.setTwoChildRate(BigDecimal.ONE);
        dailyBarDecision.setThreeChildRate(BigDecimal.ZERO);
        return dailyBarDecision;
    }

    @Test
    public void getBarFPLOSByHierarchyDecisions() {
        when(operaDecisionService.getBarFplosByHierarchyDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createBarFPLOSDecision()));
        List<FPLOS> barFPLOSByHierarchies = service.getBarFPLOSByHierarchyDecision("p123", EXTERNAL_SYSTEM);
        assertValues(barFPLOSByHierarchies);
        assertEquals(barFPLOSByHierarchies.get(0).getRoomTypeCode(), "XYZ");
        verify(operaDecisionService, times(1)).getBarFplosByHierarchyDecisions("p123", "opera");
    }

    @Test
    public void getBarFPLOSByHotelDecisions() {
        when(operaDecisionService.getBarFplosByHotelDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createBarFPLOSDecision()));
        List<FPLOS> barFPLOSByHotel = service.getBarFPLOSByHotelDecision("p123", EXTERNAL_SYSTEM);
        assertValues(barFPLOSByHotel);
        assertNull(barFPLOSByHotel.get(0).getRoomTypeCode());
        verify(operaDecisionService, times(1)).getBarFplosByHotelDecisions("p123", "opera");
    }

    @Test
    public void getBarFPLOSByRoomTypeDecisions() {
        when(operaDecisionService.getBarFplosByRoomTypeDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createBarFPLOSDecision()));
        List<FPLOS> barFPLOSByRoomType = service.getBarFPLOSByRoomTypeDecision("p123", EXTERNAL_SYSTEM);
        assertValues(barFPLOSByRoomType);
        assertEquals(barFPLOSByRoomType.get(0).getRoomTypeCode(), "XYZ");
        verify(operaDecisionService, times(1)).getBarFplosByRoomTypeDecisions("p123", "opera");
    }

    @Test
    public void testHasBarFPLOSByHierarchyByRoomClassDecisions() {
        when(operaDecisionService.hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        assertTrue(service.getStatusForBarFPLOSByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM));
    }

    @Test
    public void testNoBarFPLOSByHierarchyByRoomClassDecisions() {
        when(operaDecisionService.hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM)).thenReturn(false);
        assertFalse(service.getStatusForBarFPLOSByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM));
    }

    @Test
    public void testGetBarFPLOSByHierarchyByRoomClassDecisions() {
        when(operaDecisionService.getBarFplosByHierarchyByRoomClassDecisions("p123", EXTERNAL_SYSTEM))
                .thenReturn(singletonList(createBarFPLOSDecisionByRoomClass()));
        List<FPLOS> hierarchyByRoomClassDecision = service.getBarFPLOSByHierarchyByRoomClassDecision("p123", EXTERNAL_SYSTEM);
        assertNotNull(hierarchyByRoomClassDecision);
        assertEquals("STD", hierarchyByRoomClassDecision.get(0).getRoomTypeCode());
        verify(operaDecisionService, times(1)).getBarFplosByHierarchyByRoomClassDecisions("p123", EXTERNAL_SYSTEM);
    }

    private void assertValues(List<FPLOS> barFPLOS) {
        assertEquals(barFPLOS.size(), 1);
        assertEquals(barFPLOS.get(0).getRateCode(), "ABC");
        assertEquals(barFPLOS.get(0).getLos1(), "Y");
        assertEquals(barFPLOS.get(0).getLos2(), "Y");
        assertEquals(barFPLOS.get(0).getLos3(), "N");
        assertEquals(barFPLOS.get(0).getLos4(), "N");
        assertEquals(barFPLOS.get(0).getLos5(), "Y");
        assertEquals(barFPLOS.get(0).getLos6(), "N");
        assertEquals(barFPLOS.get(0).getLos7(), "Y");
        assertEquals(barFPLOS.get(0).getPropertyCode(), PROPERTY_CODE);
        assertEquals(barFPLOS.get(0).getClientCode(), CLIENT_CODE);
    }

    private OperaFplosQualifiedDecision createBarFPLOSDecision() {
        OperaFplosQualifiedDecision operaFplosQualifiedDecision = new OperaFplosQualifiedDecision();
        operaFplosQualifiedDecision.setRoomType("XYZ");
        operaFplosQualifiedDecision.setRateCode("ABC");
        operaFplosQualifiedDecision.setArrivalDate(new Date());
        operaFplosQualifiedDecision.setFplos("YYNNYNY");
        return operaFplosQualifiedDecision;
    }

    private OperaFplosQualifiedDecision createBarFPLOSDecisionByRoomClass() {
        OperaFplosQualifiedDecision operaFplosQualifiedDecision = new OperaFplosQualifiedDecision();
        operaFplosQualifiedDecision.setRoomClass("STD");
        operaFplosQualifiedDecision.setRateCode("LV0");
        operaFplosQualifiedDecision.setArrivalDate(new Date());
        operaFplosQualifiedDecision.setFplos("YYNNYNY");
        return operaFplosQualifiedDecision;
    }

    private LastRoomValueDecision createLastRoomValueDecision(double lastRoomValue, int ceiling, double delta, int maxSolds, Date occDate, String roomClassCode) {
        LastRoomValueDecision lrv = new LastRoomValueDecision();
        lrv.setOpportunityCost(BigDecimal.valueOf(lastRoomValue));
        lrv.setDeltaValue(BigDecimal.valueOf(delta));
        lrv.setCeilingValue(BigDecimal.valueOf(ceiling));
        lrv.setMaxSolds(BigDecimal.valueOf(maxSolds));
        lrv.setInventoryCode(roomClassCode);
        lrv.setOccupancyDate(occDate);
        return lrv;
    }

    private void assertLastRoomValue(LastRoomValue lastRoomValue, double opportunityCost, int ceiling, double delta, int maxSolds, Date occDate, String roomClassCode) {
        assertEquals(BigDecimal.valueOf(opportunityCost), lastRoomValue.getLastRoomValue());
        assertEquals(ceiling, lastRoomValue.getCeilingValue().intValue());
        assertEquals(BigDecimal.valueOf(delta), lastRoomValue.getDeltaValue());
        assertEquals(maxSolds, lastRoomValue.getMaxSolds().intValue());
        assertEquals(occDate, lastRoomValue.getOccupancyDate());
        assertEquals(roomClassCode, lastRoomValue.getRoomClassCode());
    }

    @Test
    public void getBarByLOSDecisions() {
        //given
        when(operaDecisionService.getBarByLOSDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createBarByLOSDecision()));

        //when
        List<BarByLOS> barByLOSDecision = service.getBarByLOSDecision("p123", EXTERNAL_SYSTEM);

        //then
        assertEquals(1, barByLOSDecision.size());
        assertEquals("ABC", barByLOSDecision.get(0).getRatePlanCode());
        assertEquals(Integer.valueOf(1), barByLOSDecision.get(0).getLos());
        assertEquals(PROPERTY_CODE, barByLOSDecision.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, barByLOSDecision.get(0).getClientCode());
        assertEquals(DateUtil.getDateWithoutTime(10, 10, 2015), barByLOSDecision.get(0).getArrivalDate());
        verify(operaDecisionService, times(1)).getBarByLOSDecisions("p123", "opera");
    }

    private BarByLOSDecision createBarByLOSDecision() {
        BarByLOSDecision barByLOSDecision = new BarByLOSDecision();
        barByLOSDecision.setRateCode("ABC");
        barByLOSDecision.setRateDate(DateUtil.getDateWithoutTime(10, 10, 2015));
        barByLOSDecision.setLos(1);
        return barByLOSDecision;
    }

    @Test
    public void getStatusForMinLOSDecisions() {
        when(operaDecisionService.hasMinLOSDecisions(EXTERNAL_SYSTEM, OPERA_MINLOS_BY_RATE_CODE)).thenReturn(true);

        Boolean status = service.getStatusForMinLOSDecisions(EXTERNAL_SYSTEM, OPERA_MINLOS_BY_RATE_CODE);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasMinLOSDecisions(EXTERNAL_SYSTEM, OPERA_MINLOS_BY_RATE_CODE);
    }

    @Test
    public void getMinLOSDecisions() {
        //given
        AccomClass masterClass = new AccomClass();
        masterClass.addAccomType(createAccomType("RoomType", 10));
        when(accommodationService.findMasterClass(123)).thenReturn(masterClass);
        when(operaDecisionService.getMinLOSDecisions("123", EXTERNAL_SYSTEM, OPERA_MINLOS_BY_RATE_CODE)).thenReturn(singletonList(createMinLOSDecision()));

        //when
        List<MinLOS> minLOSDecision = service.getMinLOSByRateCodeDecisions("123", EXTERNAL_SYSTEM, OPERA_MINLOS_BY_RATE_CODE);

        //then
        assertEquals(1, minLOSDecision.size());
        assertEquals("RateCode", minLOSDecision.get(0).getRateCode());
        assertNull(minLOSDecision.get(0).getRoomTypeCode());
        assertEquals(BigDecimal.ONE, minLOSDecision.get(0).getMinimumLengthOfStay());
        assertEquals(PROPERTY_CODE, minLOSDecision.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, minLOSDecision.get(0).getClientCode());
        assertEquals(DateUtil.getDateWithoutTime(10, 10, 2015), minLOSDecision.get(0).getArrivalDate());
        verify(operaDecisionService, times(1)).getMinLOSDecisions("123", "opera", OPERA_MINLOS_BY_RATE_CODE);
    }

    private OperaMinLOSDecision createMinLOSDecision() {
        OperaMinLOSDecision minLOSDecision = new OperaMinLOSDecision();
        minLOSDecision.setRateCode("RateCode");
        minLOSDecision.setRoomType("RoomType");
        minLOSDecision.setArrivalDate(DateUtil.getDateWithoutTime(10, 10, 2015));
        minLOSDecision.setMinimumLengthOfStay(BigDecimal.ONE);
        return minLOSDecision;
    }

    @Test
    public void getStatusForFPLOSQualifiedDecisions() {
        when(operaDecisionService.hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OPERA_FPLOS_BY_RATE_CODE)).thenReturn(true);

        Boolean status = service.getStatusForFPLOSQualifiedDecisions(EXTERNAL_SYSTEM, OPERA_FPLOS_BY_RATE_CODE);

        assertTrue(status);
        verify(operaDecisionService, times(1)).hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OPERA_FPLOS_BY_RATE_CODE);
    }

    @Test
    public void getFplosQualifiedDecisions() {
        //given
        AccomClass masterClass = new AccomClass();
        masterClass.addAccomType(createAccomType("XYZ", 10));
        when(accommodationService.findMasterClass(123)).thenReturn(masterClass);
        when(operaDecisionService.getFplosQualifiedDecisions("123", EXTERNAL_SYSTEM, OPERA_FPLOS_BY_RATE_CODE)).thenReturn(singletonList(createFPLOSQualifiedDecision()));

        //when
        List<FPLOS> fplosQualifiedDecisions = service.getFPLOSByRateCodeDecisions("123", EXTERNAL_SYSTEM, OPERA_FPLOS_BY_RATE_CODE);

        //then
        assertEquals(1, fplosQualifiedDecisions.size());
        assertEquals("ABC", fplosQualifiedDecisions.get(0).getRateCode());
        assertNull(fplosQualifiedDecisions.get(0).getRoomTypeCode());
        assertEquals("Y", fplosQualifiedDecisions.get(0).getLos1());
        assertEquals("Y", fplosQualifiedDecisions.get(0).getLos2());
        assertEquals("N", fplosQualifiedDecisions.get(0).getLos3());
        assertEquals("N", fplosQualifiedDecisions.get(0).getLos4());
        assertEquals("Y", fplosQualifiedDecisions.get(0).getLos5());
        assertEquals("N", fplosQualifiedDecisions.get(0).getLos6());
        assertEquals("Y", fplosQualifiedDecisions.get(0).getLos7());
        assertEquals(PROPERTY_CODE, fplosQualifiedDecisions.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, fplosQualifiedDecisions.get(0).getClientCode());
        assertEquals(DateUtil.getDateWithoutTime(10, 10, 2015), fplosQualifiedDecisions.get(0).getArrivalDate());
        verify(operaDecisionService, times(1)).getFplosQualifiedDecisions("123", "opera", OPERA_FPLOS_BY_RATE_CODE);
    }

    @Test
    public void getMasterClassRoomTypeCode() {
        //Given
        AccomClass masterClass = new AccomClass();
        masterClass.addAccomType(createAccomType("AT1", 0));
        masterClass.addAccomType(createAccomType("AT2", 10));
        masterClass.addAccomType(createAccomType("AT3", 0));
        Mockito.when(accommodationService.findMasterClass(123)).thenReturn(masterClass);

        //when
        String masterClassRoomTypeCode = service.getMasterClassRoomTypeCode("123");

        //then
        assertEquals("AT2", masterClassRoomTypeCode);
    }

    @Test
    public void getTypedDecision_LRV() {
        when(operaDecisionService.getLastRoomValueDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE)).thenReturn(new ArrayList<>());
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.LAST_ROOM_VALUE);
        verify(operaDecisionService).getLastRoomValueDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE);
    }

    @Test
    public void getTypedDecision_HOTEL_OVBK() {
        when(operaDecisionService.getHotelOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createHotelOverBookingDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.HOTEL_OVER_BOOKING);
        verify(operaDecisionService).getHotelOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_ROOM_TYPE_OVER_BOOKING() {
        when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createRoomTypeOverBookingDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.ROOM_TYPE_OVER_BOOKING);
        verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_DAILY_BAR() {
        when(operaDecisionService.getDailyBarDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createDailyBarDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.DAILY_BAR);
        verify(operaDecisionService).getDailyBarDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_BAR_FPLOS_BY_HIERARCHY() {
        when(operaDecisionService.getBarFplosByHierarchyDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HIERARCHY);
        verify(operaDecisionService).getBarFplosByHierarchyDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_BAR_FPLOS_BY_HOTEL() {
        when(operaDecisionService.getBarFplosByHotelDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HOTEL);
        verify(operaDecisionService).getBarFplosByHotelDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_BAR_FPLOS_BY_ROOM_TYPE() {
        when(operaDecisionService.getBarFplosByRoomTypeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_ROOM_TYPE);
        verify(operaDecisionService).getBarFplosByRoomTypeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_BAR_BY_LOS() {
        when(operaDecisionService.getBarByLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(Collections.singletonList(createBarByLOSDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_BY_LOS);
        verify(operaDecisionService).getBarByLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_MINLOS_BY_RATE_CODE() {
        when(operaDecisionService.getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE.typeAsString())).thenReturn(Collections.singletonList(createMinLOSDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CODE);
        verify(operaDecisionService).getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE.typeAsString());
    }

    @Test
    public void getTypedDecision_MINLOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        when(operaDecisionService.getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString())).thenReturn(Collections.singletonList(createMinLOSDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE);
        verify(operaDecisionService).getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void getTypedDecision_MINLOS_BY_RATE_CATEGORY() {
        when(operaDecisionService.getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY.typeAsString())).thenReturn(Collections.singletonList(createMinLOSDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CATEGORY);
        verify(operaDecisionService).getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY.typeAsString());
    }

    @Test
    public void getTypedDecision_MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        when(operaDecisionService.getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString())).thenReturn(Collections.singletonList(createMinLOSDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE);
        verify(operaDecisionService).getMinLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void getTypedDecision_FPLOS_BY_RATE_CATEGORY() {
        when(operaDecisionService.getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY.typeAsString())).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CATEGORY);
        verify(operaDecisionService).getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY.typeAsString());
    }

    @Test
    public void getTypedDecision_FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        when(operaDecisionService.getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString())).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE);
        verify(operaDecisionService).getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void getTypedDecision_FPLOS_BY_RATE_CODE() {
        when(operaDecisionService.getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE.typeAsString())).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CODE);
        verify(operaDecisionService).getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE.typeAsString());
    }

    @Test
    public void getTypedDecision_FPLOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        when(operaDecisionService.getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString())).thenReturn(Collections.singletonList(createFPLOSQualifiedDecision()));
        service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE);
        verify(operaDecisionService).getFplosQualifiedDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_LRV() {
        when(operaDecisionService.hasLRVDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.LAST_ROOM_VALUE);
        verify(operaDecisionService).hasLRVDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE);
    }

    @Test
    public void hasDecisionsByType_HOTEL_OVBK() {
        when(operaDecisionService.hasHotelOverbookingDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.HOTEL_OVER_BOOKING);
        verify(operaDecisionService).hasHotelOverbookingDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_ROOM_TYPE_OVER_BOOKING() {
        when(operaDecisionService.hasAccomOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.ROOM_TYPE_OVER_BOOKING);
        verify(operaDecisionService).hasAccomOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_DAILY_BAR() {
        when(operaDecisionService.hasDailyBarDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.DAILY_BAR);
        verify(operaDecisionService).hasDailyBarDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_BAR_FPLOS_BY_HIERARCHY() {
        when(operaDecisionService.hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HIERARCHY);
        verify(operaDecisionService).hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_BAR_FPLOS_BY_HOTEL() {
        when(operaDecisionService.hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HOTEL);
        verify(operaDecisionService).hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_BAR_FPLOS_BY_ROOM_TYPE() {
        when(operaDecisionService.hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_ROOM_TYPE);
        verify(operaDecisionService).hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_BAR_BY_LOS() {
        when(operaDecisionService.hasBarByLOSDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_BY_LOS);
        verify(operaDecisionService).hasBarByLOSDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void hasDecisionsByType_MINLOS_BY_RATE_CODE() {
        when(operaDecisionService.hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CODE);
        verify(operaDecisionService).hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_MINLOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        when(operaDecisionService.hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE);
        verify(operaDecisionService).hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_MINLOS_BY_RATE_CATEGORY() {
        when(operaDecisionService.hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CATEGORY);
        verify(operaDecisionService).hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY.typeAsString());
    }

    @Test
    public void hasDecisionsByType_MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        when(operaDecisionService.hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE);
        verify(operaDecisionService).hasMinLOSDecisions(EXTERNAL_SYSTEM, OperaDecisionType.MINLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_FPLOS_BY_RATE_CATEGORY() {
        when(operaDecisionService.hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CATEGORY);
        verify(operaDecisionService).hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY.typeAsString());
    }

    @Test
    public void hasDecisionsByType_FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        when(operaDecisionService.hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE);
        verify(operaDecisionService).hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_FPLOS_BY_RATE_CODE() {
        when(operaDecisionService.hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CODE);
        verify(operaDecisionService).hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_FPLOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        when(operaDecisionService.hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString())).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE);
        verify(operaDecisionService).hasFplosQualifiedDecisions(EXTERNAL_SYSTEM, OperaDecisionType.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE.typeAsString());
    }

    @Test
    public void hasDecisionsByType_BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS() {
        when(operaDecisionService.hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM)).thenReturn(true);
        service.hasDecisionsByType(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS);
        verify(operaDecisionService).hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void getTypedDecision_BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS() {
        when(operaDecisionService.getBarFplosByHierarchyByRoomClassDecisions(PROPERTY_ID, EXTERNAL_SYSTEM))
                .thenReturn(Collections.singletonList(createBarFPLOSDecisionByRoomClass()));
        List<? extends AbstractDecision> typedDecision = service.getTypedDecision(PROPERTY_ID, EXTERNAL_SYSTEM, BDE_OPERATION_TYPE, OperaDecisionType.BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS);
        assertNotNull(typedDecision);
        verify(operaDecisionService).getBarFplosByHierarchyByRoomClassDecisions(PROPERTY_ID, EXTERNAL_SYSTEM);
    }

    @Test
    public void getDifferentialDailyBarDecision_HandleDifferentialDecisions() {
        LocalDate date = new LocalDate("2019-01-01");
        List<DailyBarDecision> dailyBarDecisions = Arrays.asList(
                createDailyBarDecision("", "DLX", date.toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "DLX", date.plusDays(1).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "DLX", date.plusDays(2).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "SUT", date.plusDays(9).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "DLX", date.plusDays(3).toDate(), BigDecimal.ONE),
                createDailyBarDecision("", "SUP", date.plusDays(3).toDate(), BigDecimal.ONE),
                createDailyBarDecision("", "SUP", date.plusDays(5).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "SUP", date.plusDays(6).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "SUP", date.plusDays(8).toDate(), BigDecimal.TEN),
                createDailyBarDecision("", "SUP", date.plusDays(9).toDate(), BigDecimal.TEN));
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(dailyBarDecisions);

        List<DifferentialDailyBar> dailyBars = service.getDifferentialDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(6, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals("", dailyBars.get(0).getRateCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());

        assertEquals("DLX", dailyBars.get(0).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-01").toDate(), dailyBars.get(0).getStartDate());
        assertEquals(new LocalDate("2019-01-03").toDate(), dailyBars.get(0).getEndDate());

        assertEquals("DLX", dailyBars.get(1).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-04").toDate(), dailyBars.get(1).getStartDate());
        assertEquals(new LocalDate("2019-01-04").toDate(), dailyBars.get(1).getEndDate());

        assertEquals("SUP", dailyBars.get(2).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-04").toDate(), dailyBars.get(2).getStartDate());
        assertEquals(new LocalDate("2019-01-04").toDate(), dailyBars.get(2).getEndDate());

        assertEquals("SUP", dailyBars.get(3).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-06").toDate(), dailyBars.get(3).getStartDate());
        assertEquals(new LocalDate("2019-01-07").toDate(), dailyBars.get(3).getEndDate());

        assertEquals("SUP", dailyBars.get(4).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-09").toDate(), dailyBars.get(4).getStartDate());
        assertEquals(new LocalDate("2019-01-10").toDate(), dailyBars.get(4).getEndDate());

        assertEquals("SUT", dailyBars.get(5).getRoomTypeCode());
        assertEquals(new LocalDate("2019-01-10").toDate(), dailyBars.get(5).getStartDate());
        assertEquals(new LocalDate("2019-01-10").toDate(), dailyBars.get(5).getEndDate());
    }

    private DailyBarDecision createDailyBarDecision(String rateCode, String roomType, Date occupancyDate, BigDecimal extraAdultRate) {
        DailyBarDecision dailyBarDecision = new DailyBarDecision();
        dailyBarDecision.setExtraAdultRate(extraAdultRate);
        dailyBarDecision.setExtraChildRate(BigDecimal.ONE);
        dailyBarDecision.setOneAdultRate(BigDecimal.ONE);
        dailyBarDecision.setTwoAdultRate(BigDecimal.ONE);
        dailyBarDecision.setThreeAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFourAdultRate(BigDecimal.ONE);
        dailyBarDecision.setFiveAdultRate(BigDecimal.ONE);
        dailyBarDecision.setRateDate(occupancyDate);
        dailyBarDecision.setRateCode(rateCode);
        dailyBarDecision.setRoomType(roomType);
        return dailyBarDecision;
    }

    @Test
    public void getDifferentialDailyBarDecisionsTest() {
        Date date = new Date();
        when(operaDecisionService.getDailyBarDecisions("p123", EXTERNAL_SYSTEM)).thenReturn(singletonList(createDailyBarDecision("XYZ", "DLX", date, BigDecimal.TEN)));
        List<DifferentialDailyBar> dailyBars = service.getDifferentialDailyBarDecision("p123", EXTERNAL_SYSTEM);
        assertEquals(1, dailyBars.size());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getAdditionalChildRate());
        assertEquals(BigDecimal.TEN, dailyBars.get(0).getAdditionalAdultRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getSingleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getDoubleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getTripleRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuadRate());
        assertEquals(BigDecimal.ONE, dailyBars.get(0).getQuintRate());
        assertEquals("XYZ", dailyBars.get(0).getRateCode());
        assertEquals("DLX", dailyBars.get(0).getRoomTypeCode());
        assertEquals(PROPERTY_CODE, dailyBars.get(0).getPropertyCode());
        assertEquals(CLIENT_CODE, dailyBars.get(0).getClientCode());
        assertEquals(date, dailyBars.get(0).getStartDate());
        assertEquals(date, dailyBars.get(0).getEndDate());
        verify(operaDecisionService, times(1)).getDailyBarDecisions("p123", "opera");
    }

    @Test
    public void ngiProfitAdjustmentFromValueTest() {
        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("adjustment"));
        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("Adjustment"));
        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("ADJUSTMENT"));

        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("adjustments"));
        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("Adjustments"));
        assertEquals(NGIProfitAdjustment.Type.ADJUSTMENT, NGIProfitAdjustment.Type.fromValue("ADJUSTMENTS"));

        assertEquals(NGIProfitAdjustment.Type.REPLACEMENT, NGIProfitAdjustment.Type.fromValue("replacement"));
        assertEquals(NGIProfitAdjustment.Type.REPLACEMENT, NGIProfitAdjustment.Type.fromValue("Replacement"));
        assertEquals(NGIProfitAdjustment.Type.REPLACEMENT, NGIProfitAdjustment.Type.fromValue("REPLACEMENT"));


        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> NGIProfitAdjustment.Type.fromValue("InvalidType"),
                "Unexpected value 'InvalidType'");
        assertTrue(exception.getMessage().contains("Unexpected value 'InvalidType'"));

        IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class,
                () -> NGIProfitAdjustment.Type.fromValue(""),
                "Unexpected value ''");
        assertTrue(exception2.getMessage().contains("Unexpected value ''"));

    }


    private AccomType createAccomType(String accomTypeCode, Integer capacity) {
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCapacity(capacity);
        accomType.setAccomTypeCode(accomTypeCode);
        return accomType;
    }

    private OperaFplosQualifiedDecision createFPLOSQualifiedDecision() {
        OperaFplosQualifiedDecision operaFplosQualifiedDecision = new OperaFplosQualifiedDecision();
        operaFplosQualifiedDecision.setRateCode("ABC");
        operaFplosQualifiedDecision.setRoomType("XYZ");
        operaFplosQualifiedDecision.setArrivalDate(new Date());
        operaFplosQualifiedDecision.setFplos("YYNNYNY");
        operaFplosQualifiedDecision.setArrivalDate(DateUtil.getDateWithoutTime(10, 10, 2015));
        return operaFplosQualifiedDecision;
    }
}