package com.ideas.tetris.pacman.services.informationmanager.systemhealth;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueTotalActivityCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.global.dao.UniquePropertyCreator;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class DQIPastTotalSoldsDoNotExceedCapacityTest extends AbstractG3JupiterTest {

    private Integer propertyId;

    private String currentDate;

    @BeforeEach
    public void setUp() {
        propertyId = UniquePropertyCreator.createUniqueTenantProperty(tenantCrudService()).getId();
        currentDate = DateUtil.sqlDate(DateUtil.getCurrentDate());
    }

    @Test
    public void shouldFailIfThereAreZeroDaysAnalyzed() {
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        Object[] details = tenantCrudService().findByNativeQuerySingleResult(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'N').parameters());
        assertEquals("RED", details[4]);
        assertEquals(0, details[3]);
        assertEquals(0, details[2]);
        assertEquals(propertyId, details[1]);
    }

    @Test
    public void shouldNotReturnAnyDetailsIfThereAreZeroDaysAnalyzed() {
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        List details = tenantCrudService().findByNativeQuery(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'Y').parameters());
        assertEquals(0, details.size());
    }

    @Test
    public void shouldFailIfThereisOnlyOneDaysAnalyzed() {
        createTotalActivityWith(150, DateUtil.addDaysToToday(-3));
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        Object[] details = tenantCrudService().findByNativeQuerySingleResult(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'N').parameters());
        assertEquals("GREEN", details[4]);
        assertEquals(0, details[3]);
        assertEquals(1, details[2]);
        assertEquals(propertyId, details[1]);
    }

    private void createTotalActivityWith(int capacity, Date occupancyDate) {
        UniqueTotalActivityCreator.createTotalActivity(propertyId, occupancyDate, BigDecimal.TEN, BigDecimal.ZERO, BigDecimal.valueOf(capacity));
    }

    @Test
    public void shouldReturnAnyDetailsIfThereisOnlyOneDaysAnalyzed() {
        createTotalActivityWith(150, DateUtil.addDaysToToday(-3));
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        List<Object[]> details = tenantCrudService().findByNativeQuery(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'Y').parameters());
        assertEquals(1, details.size());
        assertEquals("N", details.get(0)[7]);
    }

    @Test
    public void shouldFailIfThereisOnlyOneErroredDaysAnalyzed() {
        createTotalActivityWith(50, DateUtil.addDaysToToday(-3));
        createTotalActivityWith(150, DateUtil.addDaysToToday(-2));
        createTotalActivityWith(50, DateUtil.addDaysToToday(-4));
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        Object[] details = tenantCrudService().findByNativeQuerySingleResult(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'N').parameters());
        assertEquals("RED", details[4]);
        assertEquals(2, details[3]);
        assertEquals(3, details[2]);
        assertEquals(propertyId, details[1]);
    }

    public void shouldFailIfThereisOnly() {
        createTotalActivityWith(100, DateUtil.addDaysToToday(-3));
        createTotalActivityWith(150, DateUtil.addDaysToToday(-2));
        createTotalActivityWith(120, DateUtil.addDaysToToday(-4));
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        Object[] details = tenantCrudService().findByNativeQuerySingleResult(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'N').parameters());
        assertEquals("GREEN", details[4]);
        assertEquals(0, details[3]);
        assertEquals(3, details[2]);
        assertEquals(propertyId, details[1]);
    }

    @Test
    public void shouldReturnDetailsIfThereisOnlyOneErroredDaysAnalyzed() {
        createTotalActivityWith(50, DateUtil.addDaysToToday(-3));
        createTotalActivityWith(150, DateUtil.addDaysToToday(-2));
        createTotalActivityWith(50, DateUtil.addDaysToToday(-4));
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        List<Object[]> details = tenantCrudService().findByNativeQuery(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", currentDate).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", 'Y').parameters());
        assertEquals(3, details.size());
        assertEquals("Y", details.get(0)[7]);
        assertEquals("Y", details.get(1)[7]);
        assertEquals("N", details.get(2)[7]);
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_AllDatesMasked_Red() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        for (int i = 0; i < 10; i++) {
            UniqueTotalActivityCreator.createTotalActivity(propertyId, startDate.plusDays(i).toDate(), BigDecimal.TEN);
        }
        populateIPCFGWithActiveDates(startDate, startDate.plusDays(10));
        List<Object[]> result = executeDQI('N');
        validateResult(result, 0, 0, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_AllDatesMasked_Red_ConfigurableNoOfYears() {
        LocalDate startDate = LocalDate.now().plusYears(-3).plusMonths(1);
        for (int i = 0; i < 10; i++) {
            UniqueTotalActivityCreator.createTotalActivity(propertyId, startDate.plusDays(i).toDate(), BigDecimal.TEN);
        }
        populateIPCFGWithActiveDates(startDate, startDate.plusDays(10));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 3);
        validateResult(result, 0, 0, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_AllDatesMasked_Red_ConfigurableNoOfYears_1() {
        LocalDate startDate = LocalDate.now().plusYears(-4).plusMonths(1);
        for (int i = 0; i < 10; i++) {
            UniqueTotalActivityCreator.createTotalActivity(propertyId, startDate.plusDays(i).toDate(), BigDecimal.TEN);
        }
        populateIPCFGWithActiveDates(startDate, startDate.plusDays(10));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 4);
        validateResult(result, 0, 0, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_AllDatesMasked_Red_DetailAnalysis() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        for (int i = 0; i < 10; i++) {
            UniqueTotalActivityCreator.createTotalActivity(propertyId, startDate.plusDays(i).toDate(), BigDecimal.TEN);
        }
        populateIPCFGWithActiveDates(startDate, startDate.plusDays(10));
        List<Object[]> result = executeDQI('Y');
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    private void validateResult(List<Object[]> result, int daysAnalyzed, int daysFailed, String indicator) {
        assertEquals(1, result.size());
        Object[] details = result.get(0);
        assertEquals(daysAnalyzed, details[2]);
        assertEquals(daysFailed, details[3]);
        assertEquals(indicator, details[4]);
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Red() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        createTotalActivity(startDate, 10, 41, 40);
        createTotalActivity(startDate.plusDays(11), 1, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('N');
        validateResult(result, 10, 1, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Red_ForConfigurableNoOfYears() {
        LocalDate startDate = LocalDate.now().plusYears(-3).plusMonths(1);
        createTotalActivity(startDate, 10, 41, 40);
        createTotalActivity(startDate.plusDays(11), 1, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 3);
        validateResult(result, 10, 1, "RED");
        //validateResult(result, 9, 1, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Red_ForConfigurableNoOfYears_1() {
        LocalDate startDate = LocalDate.now().plusYears(-4).plusMonths(1);
        createTotalActivity(startDate, 10, 41, 40);
        createTotalActivity(startDate.plusDays(11), 1, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 4);
        validateResult(result, 10, 1, "RED");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Red_DetailAnalysis() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        createTotalActivity(startDate, 10, 41, 40);
        createTotalActivity(startDate.plusDays(11), 1, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('Y');
        assertEquals(10, result.size());
        validateDetailResult(result, 0, 9, 41, -1, "2.00", "2.00", "N");
        validateDetailResult(result, 9, 10, 40, 3, "2.15", "2.15", "Y");
    }

    private void validateDetailResult(List<Object[]> result, int from, int to, int availableCapacity, int descrepancySolds, String fivePercentOfSolds, String fivePercentOfSoldsLogical, String errored) {
        for (int i = from; i < to; i++) {
            Object[] details = result.get(i);
            assertEquals(BigDecimal.valueOf(availableCapacity), details[3]);
            assertEquals(BigDecimal.valueOf(descrepancySolds), details[4]);
            assertEquals(fivePercentOfSolds, details[5].toString());
            assertEquals(fivePercentOfSoldsLogical, details[6].toString());
            assertEquals(errored, details[7]);
        }
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Yellow() {
        LocalDate startDate = LocalDate.now().minusMonths(9);
        createTotalActivity(startDate, 97, 41, 40);
        createTotalActivity(startDate.plusDays(97), 4, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('N');
        validateResult(result, 100, 4, "YELLOW");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Yellow_ConfigurableNoOfYears() {
        LocalDate startDate = LocalDate.now().plusYears(-3).plusMonths(1);
        createTotalActivity(startDate, 97, 41, 40);
        createTotalActivity(startDate.plusDays(97), 4, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 3);
        validateResult(result, 100, 4, "YELLOW");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Yellow_ConfigurableNoOfYears_1() {
        LocalDate startDate = LocalDate.now().plusYears(-4).plusMonths(1);
        createTotalActivity(startDate, 97, 41, 40);
        createTotalActivity(startDate.plusDays(97), 4, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 4);
        validateResult(result, 100, 4, "YELLOW");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Yellow_DetailAnalysis() {
        LocalDate startDate = LocalDate.now().minusMonths(9);
        createTotalActivity(startDate, 97, 41, 40);
        createTotalActivity(startDate.plusDays(97), 4, 40, 43);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('Y');
        assertEquals(100, result.size());
        validateDetailResult(result, 0, 96, 41, -1, "2.00", "2.00", "N");
        validateDetailResult(result, 96, 100, 40, 3, "2.15", "2.15", "Y");
    }

    private List<Object[]> executeDQI(char detailAnalysisRequired) {
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        return tenantCrudService().findByNativeQuery(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", LocalDate.now()).and("DQIId", 3).and("configurableNoOfYearsDQI", 2).and("detailAnalysis", detailAnalysisRequired).parameters());
    }

    private List<Object[]> executeDQIForConfigurableNoOfYears(char detailAnalysisRequired, int configurableNoOfYears) {
        String queryString = new StringBuilder().append("EXECUTE [dbo].[DQI_Past_Phy_Cap_Exceeded_At_Total]  ").append("  :propertyId").append("  ,:systemDate ").append("  ,:DQIId").append("  ,:configurableNoOfYearsDQI").append("  ,:detailAnalysis").toString();
        return tenantCrudService().findByNativeQuery(queryString, QueryParameter.with("propertyId", propertyId).and("systemDate", LocalDate.now()).and("DQIId", 3).and("configurableNoOfYearsDQI", configurableNoOfYears).and("detailAnalysis", detailAnalysisRequired).parameters());
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Green() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        createTotalActivity(startDate, 10, 41, 40);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('N');
        validateResult(result, 9, 0, "GREEN");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Green_ConfigurableNoOfYears() {
        LocalDate startDate = LocalDate.now().plusYears(-3).plusMonths(1);
        createTotalActivity(startDate, 10, 41, 40);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 3);
        validateResult(result, 9, 0, "GREEN");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Green_ConfigurableNoOfYears_1() {
        LocalDate startDate = LocalDate.now().plusYears(-4).plusMonths(1);
        createTotalActivity(startDate, 10, 41, 40);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 4);
        validateResult(result, 9, 0, "GREEN");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_Green_DetailAnalysis() {
        LocalDate startDate = LocalDate.now().minusDays(30);
        createTotalActivity(startDate, 10, 41, 40);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQI('Y');
        assertEquals(9, result.size());
        validateDetailResult(result, 0, 9, 41, -1, "2.00", "2.00", "N");
    }

    @Test
    public void testPastRoomsSoldDoNotExceedCapacity_ConsiderIPCFG_ForZeroDaysAnalysis() {
        LocalDate startDate = LocalDate.now().plusYears(-4).plusMonths(1);
        createTotalActivity(startDate, 10, 41, 40);
        populateIPCFGWithActiveDates(startDate.plusDays(1), startDate.plusDays(1));
        List<Object[]> result = executeDQIForConfigurableNoOfYears('N', 2);
        validateResult(result, 0, 0, "RED");
    }

    private void populateIPCFGWithActiveDates(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("insert into ip_cfg_mark_property_date values(" + propertyId + ",'" + startDate + "','" + endDate + "',1,GETDATE(),'', 1)");
    }

    private void createTotalActivity(LocalDate startDate, int numberOfRecords, int capacity, int roomsSold) {
        for (int i = 0; i < numberOfRecords; i++) {
            UniqueTotalActivityCreator.createTotalActivity(propertyId, startDate.plusDays(i).toDate(), BigDecimal.valueOf(capacity), BigDecimal.valueOf(roomsSold));
        }
    }
}
