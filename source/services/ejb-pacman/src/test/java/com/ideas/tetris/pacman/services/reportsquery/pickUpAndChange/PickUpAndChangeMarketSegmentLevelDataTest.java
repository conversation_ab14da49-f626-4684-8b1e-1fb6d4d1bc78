package com.ideas.tetris.pacman.services.reportsquery.pickUpAndChange;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrepareTestDataForFetchingNonComponentRoomTypes;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PickUpAndChangeMarketSegmentLevelDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private Date javaStartDate;
    private String dow;
    private int propertyID = 6;
    private int recordTypeId = 3;
    private int processStatusId = 13;
    private int isRolling = 0;
    private int mktSeg1 = 7;
    private int mktSeg2 = 8;
    private int mktSeg3 = 9;
    private int mktSeg4 = 10;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        startDate = getLocalDate(TestProperty.H2.getId().toString());
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();

        int firstDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven();
        int secondDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight();

        populateForecastPaceForMarketSegmentForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForMarketSegmentForSecondPacePoint(insertQuery, secondDecisionId);
        populateActivityPaceForMarketSegment(insertQuery);
        populateSpecialEventData(insertQuery);
        UpdateOccupancyForecastForGivenOccupancyDate(insertQuery);
        updateMktActivityData(insertQuery);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void updateMktActivityData(StringBuilder insertQuery) {
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 11,[Arrivals] = 7,[Departures] = 2,[Cancellations] = 1, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 653.14500,[Food_Revenue] = 21.98720,[Total_Revenue] = 633.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg1 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 13,[Arrivals] = 9,[Departures] = 3,[Cancellations] = 2, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 655.12500,[Food_Revenue] = 21.98720,[Total_Revenue] = 643.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg2 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 15,[Arrivals] = 11,[Departures] = 4,[Cancellations] = 3, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 677.67500,[Food_Revenue] = 23.98720,[Total_Revenue] = 653.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg3 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 17,[Arrivals] = 13,[Departures] = 5,[Cancellations] = 4, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 689.13700,[Food_Revenue] = 25.98720,[Total_Revenue] = 663.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg4 + " ");
    }

    private void UpdateOccupancyForecastForGivenOccupancyDate(StringBuilder updateQuery) {
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.11111,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.55555,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.94449,Revenue=189.00000  where Accom_Type_ID=11 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.99999,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.14459,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.11111,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.14459,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.99999,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.94449,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.55555,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.11111,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.55555,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.94449,Revenue=190.00000  where Accom_Type_ID=9 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.14459,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.11111,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        updateQuery.append(" update Occupancy_FCST set Occupancy_NBR=13.55555,Revenue=191.00000  where Accom_Type_ID=12 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSix() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(6) + "') as theDayName");
        dow = dowList.get(0).toString();
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight() {
        List secondDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(8).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(secondDecisionIdList.get(0) + "");
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven() {
        List firstDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(firstDecisionIdList.get(0) + "");
    }

    private void populateSpecialEventData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,1,GETDATE(),1,'SP_PickUPReport','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");
    }

    private void populateActivityPaceForMarketSegment(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg1 + ",29,16,14,1");
        insertQuery.append(" ,1,1355.12567,325.76343");
        insertQuery.append(" ,2525.26893,1,1,1,GETDATE())");


        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg3 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg3 + ",31,13,17,3");
        insertQuery.append(" ,1,1425.15627,278.76343");
        insertQuery.append(" ,2358.28784,1,1,1,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForSecondPacePoint(StringBuilder insertQuery, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + secondDecisionId + ",6,7,'" + startDate.plusDays(6).toString() + "',2.52,17.10556,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,8,'" + startDate.plusDays(6).toString() + "',3.35,27.30656,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,9,'" + startDate.plusDays(6).toString() + "',4.56,37.70456,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,10,'" + startDate.plusDays(6).toString() + "',5.45,17.40956,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForFirstPacePoint(StringBuilder insertQuery, int firstDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + firstDecisionId + ",6,7,'" + startDate.plusDays(6).toString() + "',1.25,47.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,8,'" + startDate.plusDays(6).toString() + "',2.25,57.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,9,'" + startDate.plusDays(6).toString() + "',3.25,67.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,10,'" + startDate.plusDays(6).toString() + "',4.25,77.10456,GETDATE())");
    }

    private String getCaughtUpDate(final String propertyId) {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyId + ",3,13)");
        return caughtUpDates.get(0).toString();
    }

    private LocalDate getLocalDate(final String propertyId) {
        return LocalDate.parse(getCaughtUpDate(propertyId));
    }

    private Date getJavaLocalDate(final String propertyId) {
        return DateUtil.toDate(getCaughtUpDate(propertyId));
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionForStaticDate() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',1");
        assertPickUpReportDataAtMarketSegmentLevel("Pickup Report at Market Segment level with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionForPastAnalysisStartDate() {
        javaStartDate = getJavaLocalDate(TestProperty.H2.getId().toString());
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + DateUtil.getDateAsString(javaStartDate, -100) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',1");
        assertNotEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[8]), "shouldValidatePickUpReportMarketSegmentLevelFunctionForPastAnalysisStartDate" + " -  Rooms Sold PickUp ");
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionOnlyBusinessEndDateDataAvailable() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(12).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',1");
        assertPickUpReportDataAtMarketSegmentLevelOnlyBusinessEndDateDataAvailable("Pickup Report at Market Segment level with Static Dates", reportDataByStaticDates);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3',1");
        assertPickUpReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3',1");
        assertPickUpReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    private void assertPickUpReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(List<Object[]> reportData) {
        assertEquals(4, reportData.size());
        assertTrue("0.00".equals(reportData.get(0)[8].toString()));
        assertTrue("-12.00".equals(reportData.get(1)[8].toString()));
        assertTrue("9.00".equals(reportData.get(2)[8].toString()));
        assertTrue("8.00".equals(reportData.get(3)[8].toString()));
        assertTrue("0.00".equals(reportData.get(0)[8].toString()));
        assertTrue("9.65".equals(reportData.get(1)[9].toString()));
        assertTrue("2.21".equals(reportData.get(2)[9].toString()));
        assertTrue("3.60".equals(reportData.get(3)[9].toString()));
        assertTrue("0.00".equals(reportData.get(0)[10].toString()));
        assertTrue("-470.01".equals(reportData.get(1)[10].toString()));
        assertTrue("631.04".equals(reportData.get(2)[10].toString()));
        assertTrue("574.96".equals(reportData.get(3)[10].toString()));
        assertTrue("0.00000".equals(reportData.get(0)[11].toString()));
        assertTrue("163.69344".equals(reportData.get(1)[11].toString()));
        assertTrue("-3.35176".equals(reportData.get(2)[11].toString()));
        assertTrue("12.18604".equals(reportData.get(3)[11].toString()));
        assertTrue("0.00".equals(reportData.get(0)[12].toString()));
        assertTrue("5.39".equals(reportData.get(1)[12].toString()));
        assertTrue("-7.86".equals(reportData.get(2)[12].toString()));
        assertTrue("-5.16".equals(reportData.get(3)[12].toString()));
        assertTrue("0.00000".equals(reportData.get(0)[13].toString()));
        assertTrue("6.54110".equals(reportData.get(1)[13].toString()));
        assertTrue("-3.19427".equals(reportData.get(2)[13].toString()));
        assertTrue("0.07582".equals(reportData.get(3)[13].toString()));
    }

    private void setUpTables() {
        updateMarketAccomActivityData();
        updatePaceMarketActivityData();
        updateOccupancyForecastData();
        updateDecisionTable();
        updatePaceMarketOccupancyForecast();
    }

    private void updateDecisionTable() {
        List decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(1).toString() + "' " +
                " and Decision_Type_ID=1");
        StringBuilder query = new StringBuilder();
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(" + propertyID + ",'" + startDate.plusDays(1) + "','" + new LocalDate() + "'," +
                    "'" + new LocalDate() + "','" + new LocalDate() + "',1,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");
        }
        decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(2).toString() + "' " +
                " and Decision_Type_ID=1");
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(" + propertyID + ",'" + startDate.plusDays(2) + "','" + new LocalDate() + "'," +
                    "'" + new LocalDate() + "','" + new LocalDate() + "',1,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");
        }
        if (!"".equals(query.toString())) {
            tenantCrudService().executeUpdateByNativeQuery(query.toString());
        }
    }

    private void updatePaceMarketOccupancyForecast() {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (" + decisionId + ",6,7,'" + startDate.plusDays(1).toString() + "',3.35,27.30656,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(2).toString() + "',4.56,37.70456,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(3).toString() + "',5.45,17.40956,GETDATE())");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (" + decisionId + ",6,7,'" + startDate.plusDays(2).toString() + "',6.77,34.3528,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(3).toString() + "',9.05,29.5956,GETDATE())");

        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateOccupancyForecastData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Occupancy_FCST");
        tenantCrudService().flushAndClear();
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.toString() + "',10,175,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(1).toString() + "',13,191,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(2).toString() + "',12,189,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(3).toString() + "',11,190,4,5,getDate(), 5)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updatePaceMarketActivityData() {
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(1).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",25,21,18,2");
        query.append(" ,1,1125.136,225.76343");
        query.append(" ,1125.24563,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(2).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",30,12,16,1");
        query.append(" ,3,3125.436,135.76343");
        query.append(" ,2145.74663,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(3).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",32,12,16,1");
        query.append(" ,3,3125.436,135.76343");
        query.append(" ,2145.74663,1,1,1,GETDATE())");

        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(2).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(2).toString() + "'," + mktSeg1 + ",39,13,13,4");
        query.append(" ,5,3756.478,201.76343");
        query.append(" ,2678.84763,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(3).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(2).toString() + "'," + mktSeg1 + ",40,13,13,4");
        query.append(" ,5,3700.4,201.76343");
        query.append(" ,2678.84763,1,1,1,GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateMarketAccomActivityData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Mkt_Accom_Activity");
        tenantCrudService().flushAndClear();
        StringBuilder query = new StringBuilder();
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,11,7,2,0,0,653.145,21.9870,633.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(1) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,13,9,3,0,0,655.125,22.98720,643.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(2) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,15,11,4,0,0,677.675,23.9870,653.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(3) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,17,13,5,0,0,689.137,24.9870,663.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionForRollingDate() {
        createTestData();
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6',1");
        assertPickUpReportDataAtMarketSegmentLevel("Pickup Report at Market Segment level with Rolling Dates", reportDataByRollingDates);
    }

    @Test
    public void shouldValidateChangeReportMarketSegmentLevelFunctionForStaticDate() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6', 1");
        assertChangeReportDataAtMarketSegmentLevel("Change Report at Market Segment level with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateChangeReportMarketSegmentLevelFunctionForRollingDate() {
        createTestData();
        isRolling = 1;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6', 1");
        assertChangeReportDataAtMarketSegmentLevel("Change Report at Market Segment level with Rolling Dates", reportDataByStaticDates);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidateChangeReportMarketSegmentLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + mktSeg1 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY','TODAY+3', 1");
        assertChangeReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    public void shouldValidateChangeReportMarketSegmentLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + mktSeg1 + "'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY','TODAY+3', 1");
        assertChangeReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    private void assertChangeReportDataAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(List<Object[]> reportData) {
        assertEquals(4, reportData.size());
        assertTrue("0.00".equals(reportData.get(0)[3].toString()));
        assertTrue("-12.00".equals(reportData.get(1)[3].toString()));
        assertTrue("-15.00".equals(reportData.get(2)[3].toString()));
        assertTrue("-15.00".equals(reportData.get(3)[3].toString()));
        assertTrue("0.00".equals(reportData.get(0)[5].toString()));
        assertTrue("9.65".equals(reportData.get(1)[5].toString()));
        assertTrue("7.44".equals(reportData.get(2)[5].toString()));
        assertTrue("5.55".equals(reportData.get(3)[5].toString()));
        assertTrue("0.00".equals(reportData.get(0)[7].toString()));
        assertTrue("-470.01".equals(reportData.get(1)[7].toString()));
        assertTrue("-2447.76".equals(reportData.get(2)[7].toString()));
        assertTrue("-2436.30".equals(reportData.get(3)[7].toString()));
        assertTrue("0.00".equals(reportData.get(0)[9].toString()));
        assertTrue("163.69".equals(reportData.get(1)[9].toString()));
        assertTrue("151.30".equals(reportData.get(2)[9].toString()));
        assertTrue("172.59".equals(reportData.get(3)[9].toString()));
        assertTrue("0.00".equals(reportData.get(0)[11].toString()));
        assertTrue("5.39".equals(reportData.get(1)[11].toString()));
        assertTrue("-59.00".equals(reportData.get(2)[11].toString()));
        assertTrue("-57.13".equals(reportData.get(3)[11].toString()));
        assertTrue("0.00".equals(reportData.get(0)[13].toString()));
        assertTrue("6.54".equals(reportData.get(1)[13].toString()));
        assertTrue("7.48".equals(reportData.get(2)[13].toString()));
        assertTrue("14.08".equals(reportData.get(3)[13].toString()));
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForStaticDate() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','','', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevel("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate() {
        createTestData();
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevel("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 0;
        List<Object[]> reportDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "','" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'','','','', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportDates);
    }

    @Test
    public void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForStaticDate_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','','', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevel("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevel("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForStaticDate_Dont_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','','', 0 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate_Dont_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6', 0 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded("Pickup Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForStaticDateWhenActivityDateIsBeyondAnalysisDate_Select_DisconMS() {
        setUpTables();
        isRolling = 0;
        List<Object[]> reportDates = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "','" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'','','','', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportDates);
    }

    @Test
    void shouldValidatePickUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDateWhenActivityDateIsBeyondAnalysisDate_Select_DisconMS() {
        setUpTables();
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " + propertyID + ",'" + mktSeg1 + "','" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3', 1 ");
        assertPickUpReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    private void setMktSegStatusDiscontinued(List<Integer> mktSegIds) {
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (:mktSegIds)"
                , QueryParameter.with("mktSegIds", mktSegIds).parameters());
    }

    private void assertPickUpReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(List<Object[]> reportData) {
        reportData.sort(new Comparator<Object[]>() {
            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertEquals(4, reportData.size());
        assertEquals("0", reportData.get(0)[53].toString());//room sold pick up
        assertEquals("-12", reportData.get(1)[53].toString());
        assertEquals("9", reportData.get(2)[53].toString());
        assertEquals("8", reportData.get(3)[53].toString());

        assertEquals("0.00", reportData.get(0)[153].toString());//occ fcst pickup
        assertEquals("9.65", reportData.get(1)[153].toString());
        assertEquals("2.21", reportData.get(2)[153].toString());
        assertEquals("3.60", reportData.get(3)[153].toString());

        assertEquals("0.00", reportData.get(0)[253].toString());//booked room revenue pickup
        assertEquals("-470.01", reportData.get(1)[253].toString());
        assertEquals("631.04", reportData.get(2)[253].toString());
        assertEquals("574.96", reportData.get(3)[253].toString());

        assertEquals("0.00", reportData.get(0)[353].toString());//fcsted room revenue pickup
        assertEquals("163.69", reportData.get(1)[353].toString());
        assertEquals("-3.35", reportData.get(2)[353].toString());
        assertEquals("12.19", reportData.get(3)[353].toString());

        assertEquals("0.00", reportData.get(0)[453].toString());//booked adr pickup
        assertEquals("5.39", reportData.get(1)[453].toString());
        assertEquals("-7.86", reportData.get(2)[453].toString());
        assertEquals("-5.16", reportData.get(3)[453].toString());

        assertEquals("0.00", reportData.get(0)[553].toString());//fcsted adr pickup
        assertEquals("6.54", reportData.get(1)[553].toString());
        assertEquals("-3.19", reportData.get(2)[553].toString());
        assertEquals("0.08", reportData.get(3)[553].toString());
    }

    @Test
    public void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDate() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevel("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateChangeUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate() {
        createTestData();
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevel("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    public void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "','" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'','','', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDate_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevel("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    void shouldValidateChangeUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevel("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDateWhenActivityDateIsBeyondAnalysisDate_Select_DisconMS() {
        setUpTables();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "','" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'','','', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDate_Not_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'','','', 0)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByStaticDates);
    }

    @Test
    void shouldValidateChangeUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDate_Not_Select_DisconMS() {
        createTestData();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "," + mktSeg3 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6', 0)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded("Change Report at Market Segment level By Side By Side with Static Dates", reportDataByRollingDates);
    }

    @Test
    void shouldValidateChangeReportMarketSegmentLevelFunctionWithSideBySideForStaticDateWhenActivityDateIsBeyondAnalysisDate_Not_Select_DisconMS() {
        setUpTables();
        setMktSegStatusDiscontinued(Collections.singletonList(mktSeg3));
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "','" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'','','', 0)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    @Test
    public void shouldValidateChangeUpReportMarketSegmentLevelFunctionWithSideBySideForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables();
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms (" + propertyID + ",'" + mktSeg1 + "','" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY+1','TODAY','TODAY+3', 1)");
        assertChangeReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(reportData);
    }

    private void assertChangeReportDataForSideBySideAtMarketSegmentLevelWhenActivityDateIsBeyondAnalysisDate(List<Object[]> reportData) {
        reportData.sort(new Comparator<Object[]>() {
            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertEquals(4, reportData.size());
        assertTrue("0".equals(reportData.get(0)[11].toString()));
        assertTrue("-12".equals(reportData.get(1)[11].toString()));
        assertTrue("-15".equals(reportData.get(2)[11].toString()));
        assertTrue("-15".equals(reportData.get(3)[11].toString()));

        assertTrue("0.00".equals(reportData.get(0)[27].toString()));
        assertTrue("9.65".equals(reportData.get(1)[27].toString()));
        assertTrue("7.44".equals(reportData.get(2)[27].toString()));
        assertTrue("5.55".equals(reportData.get(3)[27].toString()));

        assertTrue("0.00000".equals(reportData.get(0)[43].toString()));
        assertTrue("-470.01000".equals(reportData.get(1)[43].toString()));
        assertTrue("-2447.76000".equals(reportData.get(2)[43].toString()));
        assertTrue("-2436.30000".equals(reportData.get(3)[43].toString()));
        assertTrue("-2436.30000".equals(reportData.get(3)[43].toString()));

        assertTrue("0.00000".equals(reportData.get(0)[59].toString()));
        assertTrue("163.69000".equals(reportData.get(1)[59].toString()));
        assertTrue("151.30000".equals(reportData.get(2)[59].toString()));
        assertTrue("172.59000".equals(reportData.get(3)[59].toString()));

        assertTrue("0.00000".equals(reportData.get(0)[75].toString()));
        assertTrue("5.39000".equals(reportData.get(1)[75].toString()));
        assertTrue("-59.00000".equals(reportData.get(2)[75].toString()));
        assertTrue("-57.13000".equals(reportData.get(3)[75].toString()));

        assertTrue("0.00000".equals(reportData.get(0)[91].toString()));
        assertTrue("6.54000".equals(reportData.get(1)[91].toString()));
        assertTrue("7.48000".equals(reportData.get(2)[91].toString()));
        assertTrue("14.08000".equals(reportData.get(3)[91].toString()));
    }

    private void assertPickUpReportDataAtMarketSegmentLevel(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("224", (reportDataByStaticDates.get(0)[2].toString()), Level + " -  Rooms Sold ");
        assertEquals("84.00", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " -  Rooms Sold PickUp ");
        assertEquals("194.93", String.format("%.2f", reportDataByStaticDates.get(0)[5]), Level + " -  Occupancy Forecast ");
        assertEquals("4.88", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " -  Occupancy Forecast Pick Up ");
        assertEquals("10700.33", String.format("%.2f", reportDataByStaticDates.get(0)[3]), Level + " -  Booked Room Revenue ");
        assertEquals("7411.92", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " -  Booked Room Revenue Pick Up ");
        assertEquals("3035.00", String.format("%.2f", reportDataByStaticDates.get(0)[6]), Level + " -  Forecasted Room Revenue ");
        assertEquals("-148.89", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " -  Forecasted Room Revenue Pick Up ");
        assertEquals("47.77", String.format("%.2f", reportDataByStaticDates.get(0)[4]), Level + " -  Booked ADR ");
        assertEquals("27.10", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " -  Booked ADR Pick Up ");
        assertEquals("15.57", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " -  Forecasted ADR ");
        assertEquals("-16.32", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " -  Forecasted ADR Pick Up ");
    }

    private void assertPickUpReportDataAtMarketSegmentLevelOnlyBusinessEndDateDataAvailable(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals("134.00", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " -  Rooms Sold PickUp ");
        assertEquals("9662.20", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " -  Booked Room Revenue Pick Up ");
        assertEquals("72.11", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " -  Booked ADR Pick Up ");
    }

    private void assertChangeReportDataAtMarketSegmentLevel(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("224", (reportDataByStaticDates.get(0)[2].toString()), Level + " -  Rooms Sold ");
        assertEquals("174.00", String.format("%.2f", reportDataByStaticDates.get(0)[3]), Level + " -  Rooms Sold PickUp ");
        assertEquals("194.93", String.format("%.2f", reportDataByStaticDates.get(0)[4]), Level + " -  Occupancy Forecast ");
        assertEquals("183.93", String.format("%.2f", reportDataByStaticDates.get(0)[5]), Level + " -  Occupancy Forecast Pick Up ");
        assertEquals("10700.33", String.format("%.2f", reportDataByStaticDates.get(0)[6]), Level + " -  Booked Room Revenue ");
        assertEquals("8450.06", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " -  Booked Room Revenue Pick Up ");
        assertEquals("3035.00", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " -  Forecasted Room Revenue ");
        assertEquals("2786.58", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " -  Forecasted Room Revenue Pick Up ");
        assertEquals("47.77", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " -  Booked ADR ");
        assertEquals("2.76", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " -  Booked ADR Pick Up ");
        assertEquals("15.57", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " -  Forecasted ADR ");
        assertEquals("-7.01", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " -  Forecasted ADR Pick Up ");
    }

    private void assertPickUpReportDataForSideBySideAtMarketSegmentLevel(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("44", (reportDataByStaticDates.get(0)[52].toString()), Level + " -  Rooms Sold for MS " + mktSeg1 + " ");
        assertEquals("4", (reportDataByStaticDates.get(0)[53].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg1 + " ");
        assertEquals("60", (reportDataByStaticDates.get(0)[54].toString()), Level + " -  Rooms Sold for " + mktSeg3 + " ");
        assertEquals("6", (reportDataByStaticDates.get(0)[55].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg3 + " ");
        assertEquals("47.61", String.format("%.2f", reportDataByStaticDates.get(0)[152]), Level + " -  Occupancy Forecast for " + mktSeg1 + " ");
        assertEquals("1.27", String.format("%.2f", reportDataByStaticDates.get(0)[153]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg1 + " ");
        assertEquals("49.17", String.format("%.2f", reportDataByStaticDates.get(0)[154]), Level + " -  Occupancy Forecast for " + mktSeg3 + " ");
        assertEquals("1.31", String.format("%.2f", reportDataByStaticDates.get(0)[155]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg3 + " ");
        assertEquals("2612.58", String.format("%.2f", reportDataByStaticDates.get(0)[252]), Level + " -  Booked Room Revenue for " + mktSeg1 + " ");
        assertEquals("229.99", String.format("%.2f", reportDataByStaticDates.get(0)[253]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("2710.70", String.format("%.2f", reportDataByStaticDates.get(0)[254]), Level + " -  Booked Room Revenue for " + mktSeg3 + " ");
        assertEquals("300.02", String.format("%.2f", reportDataByStaticDates.get(0)[255]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("757.00", String.format("%.2f", reportDataByStaticDates.get(0)[352]), Level + " -  Forecasted Room Revenue for " + mktSeg1 + " ");
        assertEquals("-30.00", String.format("%.2f", reportDataByStaticDates.get(0)[353]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("759.00", String.format("%.2f", reportDataByStaticDates.get(0)[354]), Level + " -  Forecasted Room Revenue for " + mktSeg3 + " ");
        assertEquals("-29.40", String.format("%.2f", reportDataByStaticDates.get(0)[355]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("59.38", String.format("%.2f", reportDataByStaticDates.get(0)[452]), Level + " -  Booked ADR for " + mktSeg1 + " ");
        assertEquals("1.72", String.format("%.2f", reportDataByStaticDates.get(0)[453]), Level + " -  Booked ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("45.18", String.format("%.2f", reportDataByStaticDates.get(0)[454]), Level + " -  Booked ADR for " + mktSeg3 + " ");
        assertEquals("0.97", String.format("%.2f", reportDataByStaticDates.get(0)[455]), Level + " -  Booked ADR Pick Up for " + mktSeg3 + " ");
        assertEquals("15.90", String.format("%.2f", reportDataByStaticDates.get(0)[552]), Level + " -  Forecasted ADR for " + mktSeg1 + " ");
        assertEquals("-30.90", String.format("%.2f", reportDataByStaticDates.get(0)[553]), Level + " -  Forecasted ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("15.44", String.format("%.2f", reportDataByStaticDates.get(0)[554]), Level + " -  Forecasted ADR for " + mktSeg3 + " ");
        assertEquals("-12.38", String.format("%.2f", reportDataByStaticDates.get(0)[555]), Level + " -  Forecasted ADR Pick Up for " + mktSeg3 + " ");
    }

    private void assertPickUpReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("44", (reportDataByStaticDates.get(0)[52].toString()), Level + " -  Rooms Sold for MS " + mktSeg1 + " ");
        assertEquals("4", (reportDataByStaticDates.get(0)[53].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(0)[54].toString()), Level + " -  Rooms Sold for " + mktSeg3 + " ");
        assertEquals("0", (reportDataByStaticDates.get(0)[55].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg3 + " ");
        assertEquals("47.61", String.format("%.2f", reportDataByStaticDates.get(0)[152]), Level + " -  Occupancy Forecast for " + mktSeg1 + " ");
        assertEquals("1.27", String.format("%.2f", reportDataByStaticDates.get(0)[153]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[154]), Level + " -  Occupancy Forecast for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[155]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg3 + " ");
        assertEquals("2612.58", String.format("%.2f", reportDataByStaticDates.get(0)[252]), Level + " -  Booked Room Revenue for " + mktSeg1 + " ");
        assertEquals("229.99", String.format("%.2f", reportDataByStaticDates.get(0)[253]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[254]), Level + " -  Booked Room Revenue for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[255]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("757.00", String.format("%.2f", reportDataByStaticDates.get(0)[352]), Level + " -  Forecasted Room Revenue for " + mktSeg1 + " ");
        assertEquals("-30.00", String.format("%.2f", reportDataByStaticDates.get(0)[353]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[354]), Level + " -  Forecasted Room Revenue for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[355]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("59.38", String.format("%.2f", reportDataByStaticDates.get(0)[452]), Level + " -  Booked ADR for " + mktSeg1 + " ");
        assertEquals("1.72", String.format("%.2f", reportDataByStaticDates.get(0)[453]), Level + " -  Booked ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[454]), Level + " -  Booked ADR for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[455]), Level + " -  Booked ADR Pick Up for " + mktSeg3 + " ");
        assertEquals("15.90", String.format("%.2f", reportDataByStaticDates.get(0)[552]), Level + " -  Forecasted ADR for " + mktSeg1 + " ");
        assertEquals("-30.90", String.format("%.2f", reportDataByStaticDates.get(0)[553]), Level + " -  Forecasted ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[554]), Level + " -  Forecasted ADR for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[555]), Level + " -  Forecasted ADR Pick Up for " + mktSeg3 + " ");
    }

    private void assertChangeReportDataForSideBySideAtMarketSegmentLevel(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("44", (reportDataByStaticDates.get(0)[10].toString()), Level + " -  Rooms Sold for MS " + mktSeg1 + " ");
        assertEquals("19", (reportDataByStaticDates.get(0)[11].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg1 + " ");
        assertEquals("60", (reportDataByStaticDates.get(0)[12].toString()), Level + " -  Rooms Sold for " + mktSeg3 + " ");
        assertEquals("35", (reportDataByStaticDates.get(0)[13].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg3 + " ");
        assertEquals("47.61", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " -  Occupancy Forecast for " + mktSeg1 + " ");
        assertEquals("46.36", String.format("%.2f", reportDataByStaticDates.get(0)[27]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg1 + " ");
        assertEquals("49.17", String.format("%.2f", reportDataByStaticDates.get(0)[28]), Level + " -  Occupancy Forecast for " + mktSeg3 + " ");
        assertEquals("45.92", String.format("%.2f", reportDataByStaticDates.get(0)[29]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg3 + " ");
        assertEquals("2612.58", String.format("%.2f", reportDataByStaticDates.get(0)[42]), Level + " -  Booked Room Revenue for " + mktSeg1 + " ");
        assertEquals("1487.44", String.format("%.2f", reportDataByStaticDates.get(0)[43]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("2710.70", String.format("%.2f", reportDataByStaticDates.get(0)[44]), Level + " -  Booked Room Revenue for " + mktSeg3 + " ");
        assertEquals("1585.56", String.format("%.2f", reportDataByStaticDates.get(0)[45]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("757.00", String.format("%.2f", reportDataByStaticDates.get(0)[58]), Level + " -  Forecasted Room Revenue for " + mktSeg1 + " ");
        assertEquals("709.90", String.format("%.2f", reportDataByStaticDates.get(0)[59]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("759.00", String.format("%.2f", reportDataByStaticDates.get(0)[60]), Level + " -  Forecasted Room Revenue for " + mktSeg3 + " ");
        assertEquals("691.90", String.format("%.2f", reportDataByStaticDates.get(0)[61]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("59.38", String.format("%.2f", reportDataByStaticDates.get(0)[74]), Level + " -  Booked ADR for " + mktSeg1 + " ");
        assertEquals("14.37", String.format("%.2f", reportDataByStaticDates.get(0)[75]), Level + " -  Booked ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("45.18", String.format("%.2f", reportDataByStaticDates.get(0)[76]), Level + " -  Booked ADR for " + mktSeg3 + " ");
        assertEquals("0.17", String.format("%.2f", reportDataByStaticDates.get(0)[77]), Level + " -  Booked ADR Pick Up for " + mktSeg3 + " ");
        assertEquals("15.90", String.format("%.2f", reportDataByStaticDates.get(0)[90]), Level + " -  Forecasted ADR for " + mktSeg1 + " ");
        assertEquals("-21.78", String.format("%.2f", reportDataByStaticDates.get(0)[91]), Level + " -  Forecasted ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("15.44", String.format("%.2f", reportDataByStaticDates.get(0)[92]), Level + " -  Forecasted ADR for " + mktSeg3 + " ");
        assertEquals("-5.21", String.format("%.2f", reportDataByStaticDates.get(0)[93]), Level + " -  Forecasted ADR Pick Up for " + mktSeg3 + " ");
    }

    private void assertChangeReportDataForSideBySideAtMarketSegmentLevelDisconMSNotIncluded(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("44", (reportDataByStaticDates.get(0)[10].toString()), Level + " -  Rooms Sold for MS " + mktSeg1 + " ");
        assertEquals("19", (reportDataByStaticDates.get(0)[11].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(0)[12].toString()), Level + " -  Rooms Sold for " + mktSeg3 + " ");
        assertEquals("0", (reportDataByStaticDates.get(0)[13].toString()), Level + " -  Rooms Sold PickUp for " + mktSeg3 + " ");
        assertEquals("47.61", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " -  Occupancy Forecast for " + mktSeg1 + " ");
        assertEquals("46.36", String.format("%.2f", reportDataByStaticDates.get(0)[27]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[28]), Level + " -  Occupancy Forecast for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[29]), Level + " -  Occupancy Forecast Pick Up for " + mktSeg3 + " ");
        assertEquals("2612.58", String.format("%.2f", reportDataByStaticDates.get(0)[42]), Level + " -  Booked Room Revenue for " + mktSeg1 + " ");
        assertEquals("1487.44", String.format("%.2f", reportDataByStaticDates.get(0)[43]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[44]), Level + " -  Booked Room Revenue for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[45]), Level + " -  Booked Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("757.00", String.format("%.2f", reportDataByStaticDates.get(0)[58]), Level + " -  Forecasted Room Revenue for " + mktSeg1 + " ");
        assertEquals("709.90", String.format("%.2f", reportDataByStaticDates.get(0)[59]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[60]), Level + " -  Forecasted Room Revenue for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[61]), Level + " -  Forecasted Room Revenue Pick Up for " + mktSeg3 + " ");
        assertEquals("59.38", String.format("%.2f", reportDataByStaticDates.get(0)[74]), Level + " -  Booked ADR for " + mktSeg1 + " ");
        assertEquals("14.37", String.format("%.2f", reportDataByStaticDates.get(0)[75]), Level + " -  Booked ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[76]), Level + " -  Booked ADR for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[77]), Level + " -  Booked ADR Pick Up for " + mktSeg3 + " ");
        assertEquals("15.90", String.format("%.2f", reportDataByStaticDates.get(0)[90]), Level + " -  Forecasted ADR for " + mktSeg1 + " ");
        assertEquals("-21.78", String.format("%.2f", reportDataByStaticDates.get(0)[91]), Level + " -  Forecasted ADR Pick Up for " + mktSeg1 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[92]), Level + " -  Forecasted ADR for " + mktSeg3 + " ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[93]), Level + " -  Forecasted ADR Pick Up for " + mktSeg3 + " ");
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForPickup() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "exec dbo.usp_hotel_pickup_report_ms " + 5 + ",'4'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null,null,1";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 2, 3, 5, 4);
    }

    @Test
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForPickup_ComparativeView_MS() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = " select ms1_roomsoldcurrent ,ms1_bookedroomrevenuecurrent ,ms1_occfcstcurrent ,ms1_bookedadrcurrent " +
                "from ufn_get_pickup_report_comparative_view_ms (" + 5 + ",'4','" + today + "','" + today + "','" + yesterday + "','" + today + "',0 ,null,null,null,null,1)";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 0, 1, 2, 3);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForChange() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "exec dbo.usp_hotel_differential_control_report_ms " + 5 + ",'4'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null,1";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 2, 6, 4, 10);
    }

    @Test
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForChange_ComparativeView_MS() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "select ms1_roomsoldcurrent ,ms1_bookedroomrevenuecurrent ,ms1_occfcstcurrent ,ms1_bookedadrcurrent from ufn_get_change_report_comparative_view_ms " +
                "(5, 4 ,'" + today + "','" + yesterday + "','" + today + "', 0, null ,null ,null, 1)";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 0, 1, 2, 3);
    }

    @Test
    void test_PickUpReport_For_Group_Block_Data_When_disconMS_Not_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,null,1");
        assertGroupBlockData(result, 168, 140, 28);
    }

    @Test
    void test_PickUpReport_For_Group_Block_Data_When_disconMS_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Arrays.asList(4, 7));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,null,1");
        assertGroupBlockData(result, 168, 140, 28);
    }

    @Test
    void test_PickUpReport_For_Group_Block_Data_When_disconMS_Present_And_Not_Selected() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Arrays.asList(4, 7));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,null,0");
        assertGroupBlockData(result, 72, 60, 12);
    }

    @Test
    void test_ChangeReport_For_Group_Block_Data_When_disconMS_Not_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,1");
        assertGroupBlockData(result, 168, 140, 28);
    }

    @Test
    void test_ChangeReport_For_Group_Block_Data_When_disconMS_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,1");
        assertGroupBlockData(result, 168, 140, 28);
    }

    @Test
    void test_ChangeReport_For_Group_Block_Data_When_disconMS_Present_And_Not_Selected() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Arrays.asList(4, 7, 20));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + 5 + ",'4,7,20'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + today + "'," + isRolling + ",null,null,null,0");
        assertGroupBlockData(result, null, null, null);
    }

    @Test
    void test_ChangeReportSideBySide_When_DisconMS_Not_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms " +
                "(5, '4' ,'" + today + "','" + today + "','" + today + "', 0, null ,null ,null, 1)");
        assertGroupBlockData_Change_SideBySide(result, 48, 40, 8);
    }

    @Test
    void test_ChangeReportSideBySide_When_DisconMS_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms " +
                "(5, '4' ,'" + today + "','" + today + "','" + today + "', 0, null ,null ,null, 1)");
        assertGroupBlockData_Change_SideBySide(result, 48, 40, 8);
    }

    @Test
    void test_ChangeReportSideBySide_When_DisconMS_Present_And_Not_Selected() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        List<Object[]> result = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms " +
                "(5, '4' ,'" + today + "','" + today + "','" + today + "', 0, null ,null ,null, 0)");
        assertGroupBlockData_Change_SideBySide(result, 0, 0, 0);
    }

    @Test
    void test_PickUpReportSideBySide_When_DisconMS_Not_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " +
                "5, '4' ,'" + today + "','" + today + "','" + today + "','" + today + "', 0, null ,null ,null,null, 1");
        assertGroupBlockData_PickUp_SideBySide(result, 48, 40, 8);
    }

    @Test
    void test_PickUpReportSideBySide_When_DisconMS_Present() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " +
                "5, '4' ,'" + today + "','" + today + "','" + today + "','" + today + "', 0, null ,null ,null,null, 1");
        assertGroupBlockData_PickUp_SideBySide(result, 48, 40, 8);
    }

    @Test
    void test_PickUpReportSideBySide_When_DisconMS_Present_And_Not_Selected() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        setUpGroupBlockData(today.toString());
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        List<Object[]> result = tenantCrudService().findByNativeQuery("exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns " +
                "5, '4' ,'" + today + "','" + today + "','" + today + "','" + today + "', 0, null ,null ,null,null, 0");
        assertGroupBlockData_PickUp_SideBySide(result, 0, 0, 0);
    }

    private void assertGroupBlockData_PickUp_SideBySide(List<Object[]> result, int i1, int i2, int i3) {
        assertEquals(i1, result.get(0)[652]);
        assertEquals(i2, result.get(0)[653]);
        assertEquals(i3, result.get(0)[654]);
    }

    private void assertGroupBlockData_Change_SideBySide(List<Object[]> result, int i1, int i2, int i3) {
        assertEquals(i1, result.get(0)[106]);
        assertEquals(i2, result.get(0)[107]);
        assertEquals(i3, result.get(0)[108]);
    }

    private void assertGroupBlockData(List<Object[]> result, Integer i1, Integer i2, Integer i3) {
        assertEquals(i1, result.get(0)[14]);
        assertEquals(i2, result.get(0)[15]);
        assertEquals(i3, result.get(0)[16]);
    }

    private void setUpGroupBlockData(String occupancyDt) {
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(11,'" + occupancyDt + "',4,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(11,'" + occupancyDt + "',5,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(18,'" + occupancyDt + "',4,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(18,'" + occupancyDt + "',5,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(49,'" + occupancyDt + "',4,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(49,'" + occupancyDt + "',5,24,4,14,44,0,'2025-06-03 17:04:09.970')");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_block values(49,'" + occupancyDt + "',6,24,4,14,44,0,'2025-06-03 17:04:09.970')");

        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg_details set Booking_Block_Pc = 50 where mkt_seg_id = 4");

        tenantCrudService().executeUpdateByNativeQuery("insert into mkt_seg_details values(7,1,1,1,1,0,0,0,0,null,0,2,2,0,1,getdate(),0,1,getdate(),1)");
        tenantCrudService().executeUpdateByNativeQuery("insert into mkt_seg_details values(20,1,1,1,1,0,0,0,0,null,0,2,2,0,1,getdate(),0,1,getdate(),1)");
    }
}
