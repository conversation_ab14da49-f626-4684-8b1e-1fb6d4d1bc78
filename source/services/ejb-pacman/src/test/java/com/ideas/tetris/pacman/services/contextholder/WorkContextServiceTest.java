package com.ideas.tetris.pacman.services.contextholder;

import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.security.servlet.PacmanWorkContextManager;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.errorhandling.TetrisSecurityException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Map;

import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_PROPERTY_ID_DUMMY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * Created by the Intellij Master
 * - Z to the dubl Foo
 */
public class WorkContextServiceTest {

    WorkContextRestEasyController service;

    PacmanWorkContextManager mockManager;
    ClientConfigService mockClientConfigService;
    PropertyService mockPropertyService;

    @BeforeEach
    public void setup() {
        // Sets up with SSO user
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();

        mockManager = Mockito.mock(PacmanWorkContextManager.class);
        mockClientConfigService = Mockito.mock(ClientConfigService.class);
        mockPropertyService = Mockito.mock(PropertyService.class);

        service = new WorkContextRestEasyController();
        service.pacmanWorkContextManager = (mockManager);
        service.clientConfigService = mockClientConfigService;
        service.propertyService = mockPropertyService;
    }

    @Test
    public void getDefaultWorkContextByClient_validClient_validResult() {
        Client blkstnStub = new Client() {{
            setCode(WC_CLIENT_CODE_BLACKSTONE);
        }};
        Mockito.when(mockClientConfigService.getClientByCode(WC_CLIENT_CODE_BLACKSTONE)).thenReturn(blkstnStub);
        Mockito.when(mockManager.getDefaultWorkContext(blkstnStub)).thenReturn(new WorkContextType());

        assertNotNull(service.getDefaultWorkContextByClient(WC_CLIENT_CODE_BLACKSTONE));
    }

    @Test
    public void getDefaultWorkContextByProperty_badProperty_throwsSecurityException() {

        Property stubProperty = null;
        Mockito.when(mockPropertyService.getPropertyById(new Integer(-1))).thenReturn(stubProperty);

        assertThrows(TetrisSecurityException.class, () -> service.getDefaultWorkContextByProperty(WC_PROPERTY_ID_DUMMY));
    }

    @Test
    public void getDefaultWorkContextByProperty_validProperty_validResult() {
        Property stubProperty = new Property() {{
            setId(new Integer(WC_PROPERTY_ID_DUMMY));
        }};

        Mockito.when(mockPropertyService.getPropertyById(WC_PROPERTY_ID_DUMMY)).thenReturn(stubProperty);
        Mockito.when(mockManager.getDefaultWorkContext(stubProperty)).thenReturn(new WorkContextType());

        assertNotNull(service.getDefaultWorkContextByProperty(WC_PROPERTY_ID_DUMMY));
    }

    @Test
    public void canFetchDefaultAsJson() throws JSONException {
        String isContinuousPricingEnabled = "true";
        Integer propertyId = 5;
        WorkContextType workContextType = new WorkContextType();
        workContextType.setPropertyId(propertyId);
        Mockito.when(mockManager.getDefaultWorkContext()).thenReturn(workContextType);
        WorkContextRestEasyController spy = Mockito.spy(service);
        PacmanConfigParamsService configParamsService = Mockito.mock(PacmanConfigParamsService.class);
        Mockito.doReturn(configParamsService).when(spy).getPacmanConfigParameterService();
        Mockito.doReturn(isContinuousPricingEnabled).when(configParamsService)
                .getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), workContextType.getClientCode(), workContextType.getPropertyCode());

        assertNotNull(spy.getDefaultWorkContextAsJSON());
    }

    @Test
    public void canFetchAsJson() throws JSONException {
        String isContinuousPricingEnabled = "true";
        WorkContextType workContextType = service.getCurrentWorkContext();
        WorkContextRestEasyController spy = Mockito.spy(service);
        PacmanConfigParamsService configParamsService = Mockito.mock(PacmanConfigParamsService.class);
        Mockito.doReturn(configParamsService).when(spy).getPacmanConfigParameterService();
        Mockito.doReturn(isContinuousPricingEnabled).when(configParamsService)
                .getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), workContextType.getClientCode(), workContextType.getPropertyCode());

        TetrisPrincipal tp = (TetrisPrincipal) PlatformThreadLocalContextHolder.get(Constants.SSO_USER_PRINCIPAL);
        Map<String, Object> wc = spy.getCurrentWorkContextAsJSON();
        assertEquals(tp.isInternalUser(), wc.get("isInternal"));
        assertEquals("BSTN", wc.get("clientCode"));
        assertEquals(isContinuousPricingEnabled, wc.get("isContinuousPricingEnabled"));
    }

    @Test
    public void testGetDefaultWorkContextAsJSONAsInternalUser() throws Exception {
        WorkContextType dummyWorkContextType = new WorkContextType();
        dummyWorkContextType.setClientCode("APEX");
        dummyWorkContextType.setPropertyId(123);

        WorkContextRestEasyController spy = Mockito.spy(service);
        Mockito.doReturn(dummyWorkContextType).when(mockManager).getDefaultWorkContext();
        PacmanConfigParamsService configParamsService = Mockito.mock(PacmanConfigParamsService.class);
        Mockito.doReturn(configParamsService).when(spy).getPacmanConfigParameterService();
        Mockito.doReturn("true").when(configParamsService).getParameterValue(anyString(), anyString(), anyString());

        Map<String, Object> defaultWorkContextAsJSON = spy.getDefaultWorkContextAsJSON();

        //Since we are an internal user, we should expect back the default client and property
        String clientCode = (String) defaultWorkContextAsJSON.get(WorkContextRestEasyController.CLIENT_CODE);
        Integer clientId = (Integer) defaultWorkContextAsJSON.get(WorkContextRestEasyController.CLIENT_ID);
        String propertyCode = (String) defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_CODE);
        Integer propertyId = (Integer) defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_ID);

        assertEquals(WorkContextRestEasyController.DEFAULT_CLIENT_CODE, clientCode);
        assertEquals(WorkContextRestEasyController.DEFAULT_CLIENT_ID, clientId);
        assertEquals(WorkContextRestEasyController.DEFAULT_PROPERTY_CODE, propertyCode);
        assertEquals(WorkContextRestEasyController.DEFAULT_PROPERTY_ID, propertyId);
        assertNull(defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_GROUP_ID));
    }

    @Test
    public void testGetDefaultWorkContextAsJSONAsExternalUser() throws Exception {
        //Create our external user by creating a principle with a client other then ideas
        TetrisPrincipal tetrisPrincipal = PacmanWorkContextTestHelper.createTetrisPrincipal("dn", "cn", "user1", "apex", false);
        PlatformThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, tetrisPrincipal);

        WorkContextType dummyWorkContextType = new WorkContextType();
        dummyWorkContextType.setClientCode("apex");
        dummyWorkContextType.setClientId(654);
        dummyWorkContextType.setPropertyId(123);
        dummyWorkContextType.setPropertyCode("h1");

        WorkContextRestEasyController spy = Mockito.spy(service);
        Mockito.doReturn(dummyWorkContextType).when(mockManager).getDefaultWorkContext();
        PacmanConfigParamsService configParamsService = Mockito.mock(PacmanConfigParamsService.class);
        Mockito.doReturn(configParamsService).when(spy).getPacmanConfigParameterService();
        Mockito.doReturn("true").when(configParamsService).getParameterValue(anyString(), anyString(), anyString());

        Map<String, Object> defaultWorkContextAsJSON = spy.getDefaultWorkContextAsJSON();

        String clientCode = (String) defaultWorkContextAsJSON.get(WorkContextRestEasyController.CLIENT_CODE);
        Integer clientId = (Integer) defaultWorkContextAsJSON.get(WorkContextRestEasyController.CLIENT_ID);
        String propertyCode = (String) defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_CODE);
        Integer propertyId = (Integer) defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_ID);

        assertEquals("apex", clientCode);
        assertEquals(new Integer(654), clientId);
        assertEquals("h1", propertyCode);
        assertEquals(new Integer(123), propertyId);
        assertNull(defaultWorkContextAsJSON.get(WorkContextRestEasyController.PROPERTY_GROUP_ID));
    }
}
