package com.ideas.tetris.pacman.services.decisiontranslator;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.integration.opera.dto.AccomOverbookingDecision;
import com.ideas.g3.integration.opera.dto.AccomTypeManualRestrictionDecisionForOpera;
import com.ideas.g3.integration.opera.dto.DailyBarDecision;
import com.ideas.g3.integration.opera.dto.HotelOverbookingDecision;
import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMappingService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.dailybar.DailyBarDecisionService;
import com.ideas.tetris.pacman.services.dailybar.PacmanAgileRatesDecisionService;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisiondelivery.BARDecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.PacmanOverbookingDecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystem;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.pacman.services.fplos.*;
import com.ideas.tetris.pacman.services.fplos.FPLOSByHierarchyByRoomClassDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByHierarchyDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByRoomClassDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByRoomTypeDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSQualifiedDecisionService;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.lra.LRADecisionService;
import com.ideas.tetris.pacman.services.manualrestrictions.upload.ManualRestrictionDecisionService;
import com.ideas.tetris.pacman.services.minlos.MinlosDecisionService;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchange;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchangeResultsMapper;
import com.ideas.tetris.pacman.services.opera.OperaUtilityService;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.roomtypemapping.DailyBarVendorRoomTypeTranslator;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import org.springframework.beans.factory.annotation.Autowired;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.decisiontranslator.OperaDecisionService.QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE;
import static com.ideas.tetris.platform.common.rest.mapper.RestEndpoints.CURRENCY_EXCHANGE;
import static com.ideas.tetris.platform.common.time.LocalDateUtils.toDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyListOf;
import static org.mockito.Mockito.anyMapOf;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaDecisionServiceTest extends AbstractG3JupiterTest  {

    public static final String AGILE_RATES = "AgileRates";
    public static final String TEST_DB_PROPERTY = "00005";
    private static final String THE_PROPERTY = "000006";
    private static final String EXTERNAL_SYSTEM_NAME = "opera";
    private final String roomTypeOne = "RT1";
    private final String vendorRateCodeBar01 = "BAR01";
    private final String pmsOperaRateCode = "PMSratecode";
    @Autowired
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository =
            new DecisionUploadDateToExternalSystemRepository();
    @Mock
    private FPLOSQualifiedDecisionService fplosQualifiedDecisionService;
    @Mock
    private MinlosDecisionService minimumLengthOfStayDecisionService;
    @Mock
    private FPLOSByHierarchyDecisionService fplosByHierarchyDecisionService;
    @Mock
    private FPLOSByHierarchyByRoomClassDecisionService fplosByHierarchyByRoomClassDecisionService;
    @Mock
    private FPLOSByRoomTypeDecisionService fplosByRoomTypeDecisionService;
    @Mock
    private FPLOSByRoomClassDecisionService fplosByRoomClassDecisionService;
    @Mock
    private CrudService crudService;
    @Mock
    private DateService dateService;
    @Mock
    private PacmanConfigParamsService configParamsServiceLocal;
    @Mock
    private EntityManager entityManager;
    @Mock
    private Query query;
    @Mock
    private BARDecisionService barDecisionServiceLocal;
    @Mock
    private DailyBarDecisionService dailyBarDecisionServiceLocal;
    @Mock
    private PacmanAgileRatesDecisionService pacmanAgileRatesDecisionService;
    @Mock
    private AccommodationService accommodationService;
    @Mock
    private DailyBarVendorRoomTypeTranslator dailyBarVendorRoomTypeTranslator;
    @Mock
    private RateCodeVendorMappingService rateCodeVendorMappingService;
    @InjectMocks
    private OperaUtilityService operaUtilityService;
    @InjectMocks
    private OperaDecisionService operaDecisionService;
    @Mock
    private DecisionConfigurationService decisionConfigurationService;
    @Mock
    private LRADecisionService lraDecisionService;
    @Mock
    private ManualRestrictionDecisionService manualRestrictionDecisionService;
    @Mock
    private RestClient restClient;

    private static Integer getPropertyId() {
        return PacmanWorkContextHelper.getPropertyId();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @BeforeEach
    public void setUp() throws Exception {
        List exchangeRateList = new ArrayList();
        exchangeRateList.add(new BigDecimal(1.0));
        inject(operaDecisionService, "decisionUploadDateToExternalSystemRepository", decisionUploadDateToExternalSystemRepository);
        inject(decisionUploadDateToExternalSystemRepository, "crudService", crudService);
        when(configParamsServiceLocal.getBooleanParameterValue(IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value())).thenReturn(true);
        when(configParamsServiceLocal.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value())).thenReturn("USD");
        when(crudService.findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", "USD").parameters())).thenReturn(exchangeRateList);
        when(crudService.findByNativeQuerySingleResult("select count(1) from opera.History_Yield_Currency ", null)).thenReturn(1);
        when(crudService.findByNativeQuerySingleResult("select top 1 base_currency_code from opera.Stage_Yield_Currency", null)).thenReturn("NOK");
        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(configParamsServiceLocal);
        operaDecisionService.setConfigParameterNameService(configParameterNameService);
    }
    @Test
    public void test_insertRecordForDecisionDeliveryTracking_forceFullDecisionsDisabled() {
        List<Object> decisionIDList = new ArrayList<>();
        decisionIDList.add(new BigInteger("3009"));
        Integer propertyId = getPropertyId();
        when(crudService.findByNativeQuery(QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE,
                QueryParameter.with("decisionTypeIDs",
                        Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP)).parameters())).thenReturn(decisionIDList);
        when(crudService.getEntityManager()).thenReturn(entityManager);
        when(query.executeUpdate()).thenReturn(1);
        when(decisionConfigurationService.shouldSendFullDecisions(propertyId)).thenReturn(false);
        int noOfRecordsInserted = operaDecisionService.insertRecordForDecisionDeliveryTracking(Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS, Constants.FULL,
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), EXTERNAL_SYSTEM_NAME);
        assertEquals(1, noOfRecordsInserted);
        verify(decisionConfigurationService).shouldSendFullDecisions(propertyId);
    }
    private CrudService setUpForDataTestOfAgileRates() {
        createMockDataForDecisionDeliveryTracking();
        when(configParamsServiceLocal.getBooleanParameterValue(IntegrationConfigParamName.USE_YIELD_CURRENCY_FOR_DAILY_BAR.value(Constants.OPERA))).thenReturn(true);
        CrudService realCrudService = tenantCrudService();
        injectRealCrudService(realCrudService);
        inject(decisionUploadDateToExternalSystemRepository, "crudService", realCrudService);
        return realCrudService;
    }

    @Test
    public void testDecisionDeliveryTrackingRecordForAgileRates_record_inserted_first_time_upload() {
        CrudService realCrudService = setUpForDataTestOfAgileRates();

        realCrudService.executeUpdateByNativeQuery("insert into decision values(" + TEST_DB_PROPERTY + ",'" + new LocalDate() + "','" + new LocalDate() + "'," +
                "'" + new LocalDate() + "','" + new LocalDate() + "',18,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");

        List<BigInteger> decisionIdList = realCrudService.findByNativeQuery("select max(decision_id) from decision");
        BigInteger decisionId = decisionIdList.get(0);

        operaDecisionService.getDailyBarDecisionsForAgileRates(TEST_DB_PROPERTY, EXTERNAL_SYSTEM_NAME, vendorRateCodeBar01);

        List<Object[]> rows = realCrudService.findByNativeQuery("select * from Decision_Upload_Date_To_External_System where decision_id=" + decisionId);
        assertEquals(1, rows.size());
        Object[] firstRow = rows.get(0);
        assertEquals(null, firstRow[6], "Initial record with null status");
        assertEquals(null, firstRow[5], "Initial record with null lastUploaded date");
        assertEquals(AGILE_RATES, firstRow[2], "Initial record with decision name AgileRates");
        assertEquals("opera", firstRow[8], "Initial record with opera external system");
    }

    @Test
    public void testDecisionDeliveryTrackingRecordForAgileRates_not_inserted_when_already_present_verifyData() {
        CrudService realCrudService = setUpForDataTestOfAgileRates();

        realCrudService.executeUpdateByNativeQuery("insert into decision values(" + TEST_DB_PROPERTY + ",'" + new LocalDate() + "','" + new LocalDate() + "'," +
                "'" + new LocalDate() + "','" + new LocalDate() + "',18,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");

        List<BigInteger> decisionIdList = realCrudService.findByNativeQuery("select max(decision_id) from decision");
        BigInteger decisionId = decisionIdList.get(0);
        realCrudService.executeUpdateByNativeQuery(" insert into Decision_Upload_Date_To_External_System values ( " + decisionId + ", '" + AGILE_RATES + "', '2015-03-12 23:38:55.270', '2015-03-12 23:38:55.270', null, null, 'full', 'opera')");

        operaDecisionService.getDailyBarDecisionsForAgileRates(TEST_DB_PROPERTY, EXTERNAL_SYSTEM_NAME, vendorRateCodeBar01);

        List<Object[]> rows = realCrudService.findByNativeQuery("select * from Decision_Upload_Date_To_External_System where decision_id=" + decisionId);
        assertEquals(1, rows.size());
        Object[] firstRow = rows.get(0);
        assertEquals(null, firstRow[6], "Initial record with null status");
        assertEquals(null, firstRow[5], "Initial record with null lastUploaded date");
        assertEquals(AGILE_RATES, firstRow[2], "Initial record with decision name AgileRates");
        assertEquals("opera", firstRow[8], "Initial record with opera external system");
    }

    @Test
    public void testDecisionDeliveryTrackingRecordForAgileRates_record_inserted_when_initially_failed() {
        CrudService realCrudService = setUpForDataTestOfAgileRates();

        realCrudService.executeUpdateByNativeQuery("insert into decision values(" + TEST_DB_PROPERTY + ",'" + new LocalDate() + "','" + new LocalDate() + "'," +
                "'" + new LocalDate() + "','" + new LocalDate() + "',18,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");

        List<BigInteger> decisionIdList = realCrudService.findByNativeQuery("select max(decision_id) from decision");
        BigInteger newDecisionId = decisionIdList.get(0);
        realCrudService.executeUpdateByNativeQuery(" insert into Decision_Upload_Date_To_External_System values ( " + newDecisionId + ", '" + AGILE_RATES + "', '2015-03-12 23:38:55.270', '2015-03-12 23:38:55.270', null, 'FAIL', 'full', 'opera')");

        operaDecisionService.getDailyBarDecisionsForAgileRates(TEST_DB_PROPERTY, EXTERNAL_SYSTEM_NAME, vendorRateCodeBar01);

        List<Object[]> rows = realCrudService.findByNativeQuery("select * from Decision_Upload_Date_To_External_System where decision_id=" + newDecisionId);
        assertEquals(2, rows.size());
        Object[] firstRow = rows.get(0);
        Object[] secondRow = rows.get(1);
        assertEquals("FAIL", firstRow[6], "Initial record for failure with FAIL status");
        assertEquals(null, firstRow[5], "Initial record for failure with null lastUploaded date");
        assertEquals(AGILE_RATES, firstRow[2], "Initial record with decision name AgileRates");
        assertEquals("opera", firstRow[8], "Initial record for failure with opera external system");
        assertEquals(null, secondRow[6], "New record with null status");
        assertEquals(null, secondRow[5], "New record with null lastUploaded date");
        assertEquals(AGILE_RATES, secondRow[2], "New record with decision name AgileRates");
        assertEquals("opera", secondRow[8], "New record with opera external system");
    }

    @Test
    public void testDecisionDeliveryTrackingRecordForAgileRates_record_inserted_with_initialRecord_different_extSystem() {
        CrudService realCrudService = setUpForDataTestOfAgileRates();

        realCrudService.executeUpdateByNativeQuery("insert into decision values(" + TEST_DB_PROPERTY + ",'" + new LocalDate() + "','" + new LocalDate() + "'," +
                "'" + new LocalDate() + "','" + new LocalDate() + "',18,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");

        List<BigInteger> decisionIdList = realCrudService.findByNativeQuery("select max(decision_id) from decision");
        BigInteger newDecisionId = decisionIdList.get(0);
        realCrudService.executeUpdateByNativeQuery(" insert into Decision_Upload_Date_To_External_System values ( " + newDecisionId + ", '" + AGILE_RATES + "', '2015-03-12 23:38:55.270', '2015-03-12 23:38:55.270', null, null, 'full', 'myfidelio')");

        operaDecisionService.getDailyBarDecisionsForAgileRates(TEST_DB_PROPERTY, EXTERNAL_SYSTEM_NAME, vendorRateCodeBar01);

        List<Object[]> rows = realCrudService.findByNativeQuery("select * from Decision_Upload_Date_To_External_System where decision_id=" + newDecisionId);
        assertEquals(2, rows.size());
        Object[] firstRow = rows.get(0);
        Object[] secondRow = rows.get(1);
        assertEquals(null, firstRow[6], "Initial record for failure with FAIL status");
        assertEquals(null, firstRow[5], "Initial record for failure with null lastUploaded date");
        assertEquals(AGILE_RATES, firstRow[2], "Initial record with decision name AgileRates");
        assertEquals("myfidelio", firstRow[8], "Initial record for failure with opera external system");
        assertEquals(null, secondRow[6], "New record with null status");
        assertEquals(null, secondRow[5], "New record with null lastUploaded date");
        assertEquals(AGILE_RATES, secondRow[2], "New record with decision name AgileRates");
        assertEquals("opera", secondRow[8], "New record with opera external system");
    }
    private String createMockDataForDecisionDeliveryTracking() {
        List<Object> decisionIDList = new ArrayList<>();
        String decisionId = "3009";
        decisionIDList.add(new BigInteger(decisionId));

        when(crudService.findByNativeQuery(QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE,
                QueryParameter.with("decisionTypeIDs", List.of(Constants.DECISION_TYPE_DAILYBAR, Constants.DECISION_TYPE_DAILYBAR_FULL_REFRESH)
                ).parameters())).thenReturn(decisionIDList);

        List<DailyBarDecisions> dailyBarList = createDailyBarDecisions(new Date(), roomTypeOne);
        String uploadType = Constants.PACMAN_INTEGRATION + EXTERNAL_SYSTEM_NAME + "." + Constants.AGILE_RATES + Constants.UPLOAD_TYPE;
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(EXTERNAL_SYSTEM_NAME);
        when(configParamsServiceLocal.getParameterValue(uploadType)).thenReturn(Constants.FULL);
        when(dailyBarVendorRoomTypeTranslator.translateVendorRoomTypes(anyListOf(DailyBarDecisions.class), anyString())).thenReturn(dailyBarList);
        when(configParamsServiceLocal.getBooleanParameterValue(IntegrationConfigParamName.USE_YIELD_CURRENCY_FOR_DAILY_BAR.value(Constants.OPERA))).thenReturn(false);
        when(dailyBarDecisionServiceLocal.getDailyBarDecisionsForAgileRates(pmsOperaRateCode, false, null, EXTERNAL_SYSTEM_NAME, false)).thenReturn(dailyBarList);
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(Constants.OPERA))).thenReturn(pmsOperaRateCode);
        return decisionId;
    }

    @Test
    public void test_insertRecordForDecisionDeliveryTracking_forceFullDecisionsEnabled() {
        List<Object> decisionIDList = new ArrayList<>();
        decisionIDList.add(new BigInteger("3009"));
        Integer propertyId = getPropertyId();
        when(crudService.findByNativeQuery(QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE,
                QueryParameter.with("decisionTypeIDs",
                        Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP)).parameters())).thenReturn(decisionIDList);
        when(crudService.getEntityManager()).thenReturn(entityManager);
        when(query.executeUpdate()).thenReturn(1);
        when(decisionConfigurationService.shouldSendFullDecisions(propertyId)).thenReturn(true);
        int noOfRecordsInserted = operaDecisionService.insertRecordForDecisionDeliveryTracking(Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS, Constants.DIFFERENTIAL,
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), EXTERNAL_SYSTEM_NAME);
        assertEquals(1, noOfRecordsInserted);
        verify(decisionConfigurationService).shouldSendFullDecisions(propertyId);
    }
    private List<DailyBarDecisions> createDailyBarDecisions(Date today, String roomTypeOne) {
        List<DailyBarDecisions> dailyBarList = new ArrayList<>();
        DailyBarDecisions dailybardecision = new DailyBarDecisions();
        dailybardecision.setRoomType(roomTypeOne);
        dailybardecision.setOccupancyDate(today);
        dailybardecision.setAdultRate(BigDecimal.valueOf(100.0));
        dailybardecision.setChildRate(BigDecimal.valueOf(50.0));
        dailybardecision.setSingleRate(BigDecimal.valueOf(150.0));
        dailybardecision.setDoubleRate(BigDecimal.valueOf(280.0));
        dailyBarList.add(dailybardecision);

        return dailyBarList;
    }
    @Test
    public void verifyYCCodeisRetrievedFromCrudService() {
        injectRealCrudService(crudService);
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value())).thenReturn("NOK");
        when(configParamsServiceLocal.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value())).thenReturn("USD");

        when(crudService.findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", "USD").parameters())).thenReturn(Collections.singletonList(new BigDecimal(0.5)));

        assertEquals(Double.valueOf(2.0), operaDecisionService.getExchangeRate());
        verify(crudService).findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", "USD").parameters());
    }

    @Test
    public void verifyYCCodeisRetrievedFromRestCall() {
        injectRealCrudService(crudService);
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value())).thenReturn("NOK");
        when(configParamsServiceLocal.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value())).thenReturn("USD");

        when(crudService.findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", "USD").parameters())).thenReturn(Collections.emptyList());
        CurrencyExchange currencyExchange = new CurrencyExchange();
        currencyExchange.setExchangeRate(BigDecimal.valueOf(0.5));
        when(restClient.getSingleResultFromEndpoint(eq(CURRENCY_EXCHANGE), anyMapOf(String.class, String.class), any(CurrencyExchangeResultsMapper.class))).thenReturn(currencyExchange);

        assertEquals(Double.valueOf(2.0), operaDecisionService.getExchangeRate());
        verify(crudService).findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", "USD").parameters());
        verify(restClient).getSingleResultFromEndpoint(eq(CURRENCY_EXCHANGE), anyMapOf(String.class, String.class), any(CurrencyExchangeResultsMapper.class));
    }

    @Test
    public void verifyBaseCurrencyisRetrieved() {
        injectRealCrudService(tenantCrudService());
        inject(operaUtilityService, "crudService", tenantCrudService());
        DataLoadMetadata dataLoadMetadata = new DataLoadMetadata();
        String CORRELATION_ID = "TEST_123";
        dataLoadMetadata.setCorrelationId(CORRELATION_ID);
        dataLoadMetadata.setCreateDate(new LocalDateTime("2015-03-03"));
        dataLoadMetadata.setIncomingFileTypeCode("YC");
        tenantCrudService().save(dataLoadMetadata);

        DataLoadMetadata dataloadMetadatas = operaUtilityService.getDataLoadMetadataForFileType(CORRELATION_ID, OperaDataLoadTypeCode.YC.name());
        int dlm = dataloadMetadatas.getId();
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.Stage_Yield_Currency values ('OPERATEST', 'NOK','USD','2012-04-02 14:29:38.000',25.0," + dlm + ")");
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.History_Yield_Currency values ('OPERATEST', 'NOK','USD','2012-04-02 14:29:38.000',25.0," + dlm + ")");

        assertEquals(Double.valueOf(0.04), operaDecisionService.getExchangeRate());

    }
    private void injectRealCrudService(CrudService crudService) {
        inject(operaDecisionService, "crudService", crudService);
    }

    @Test
    public void shouldReturnOverbookingPropertyDataOnlyTillDecisionUploadWindowEndDate() {
        Date startDate = DateUtil.removeTimeFromDate(new Date());
        Date uploadWindowEndDate = DateUtil.addDaysToDate(startDate, 2);
        when(configParamsServiceLocal.getParameterValue("pacman.integration.Opera.HotelOverbooking.uploadtype")).thenReturn("Full");
        Date optWindowBDEEndDate = setupOverbookingDecisionService(startDate);

        List<HotelOverbookingDecision> opera = operaDecisionService.getHotelOverbookingDecisions(TestProperty.H1.getId().toString(), "Opera");
        assertEquals(10, opera.size());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(uploadWindowEndDate);
        opera = operaDecisionService.getHotelOverbookingDecisions(TestProperty.H1.getId().toString(), "Opera");
        assertEquals(3, opera.size());
        for (HotelOverbookingDecision hotelOverbookingDecision : opera) {
            assertNotEquals(optWindowBDEEndDate, hotelOverbookingDecision.getOccupancyDate());
        }
    }

    @Test
    public void testOverbookingDataWithoutExternalSystem() {
        Date startDate = DateUtil.removeTimeFromDate(new Date());
        Date uploadWindowEndDate = DateUtil.addDaysToDate(startDate, 2);
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(EXTERNAL_SYSTEM_NAME);
        when(configParamsServiceLocal.getParameterValue("pacman.integration.opera.HotelOverbooking.uploadtype")).thenReturn("Full");
        Date optWindowBDEEndDate = setupOverbookingDecisionService(startDate);

        List<HotelOverbookingDecision> opera = operaDecisionService.getHotelOverbookingDecisions(TestProperty.H1.getId().toString());
        assertEquals(10, opera.size());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(uploadWindowEndDate);
        opera = operaDecisionService.getHotelOverbookingDecisions(TestProperty.H1.getId().toString());
        assertEquals(3, opera.size());
        for (HotelOverbookingDecision hotelOverbookingDecision : opera) {
            assertNotEquals(optWindowBDEEndDate, hotelOverbookingDecision.getOccupancyDate());
        }
    }

    @Test
    public void testHasHotelOverbookingDecisions() {
        setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));
        assertTrue(operaDecisionService.hasHotelOverbookingDecisions(EXTERNAL_SYSTEM_NAME));
    }
    @Test
    public void testBarByLosDecisions() {
        setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));
        when(configParamsServiceLocal.getParameterValueByClientLevel("pacman.integration.opera.BarByLOS.uploadtype")).thenReturn("Differential");
        assertFalse(operaDecisionService.hasBarByLOSDecisions(EXTERNAL_SYSTEM_NAME));
    }

    @Test
    public void testBarByLosByRoomTypeDecisions() {
        setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));
        when(configParamsServiceLocal.getParameterValueByClientLevel("pacman.integration.opera.BARByLOSByRoomTypes.uploadtype")).thenReturn("Differential");
        assertFalse(operaDecisionService.hasBarByLOSByRoomTypeDecisions(EXTERNAL_SYSTEM_NAME));
    }

    private Date setupOverbookingDecisionService(Date startDate) {
        PacmanOverbookingDecisionService pacmanOverbookingDecisionService = new PacmanOverbookingDecisionService();
        inject(operaDecisionService, "pacmanOverbookingDecisionService", pacmanOverbookingDecisionService);

        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        Date optWindowBDEEndDate = DateUtil.addDaysToDate(startDate, 9);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(optWindowBDEEndDate);
        pacmanOverbookingDecisionService.setDateService(dateService);
        pacmanOverbookingDecisionService.setCrudService(tenantCrudService());
        return optWindowBDEEndDate;
    }
    @Test
    public void shouldReturnOverbookingAccomDataOnlyTillDecisionUploadWindowEndDate() {
        when(configParamsServiceLocal.getParameterValue("pacman.integration.Opera.RoomTypeOverbooking.uploadtype")).thenReturn("Full");

        Date startDate = DateUtil.removeTimeFromDate(new Date());
        Date uploadWindowEndDate = DateUtil.addDaysToDate(startDate, 2);

        PacmanOverbookingDecisionService pacmanOverbookingDecisionService = new PacmanOverbookingDecisionService();
        inject(operaDecisionService, "pacmanOverbookingDecisionService", pacmanOverbookingDecisionService);

        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        Date optWindowBDEEndDate = setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));

        List<AccomOverbookingDecision> opera = operaDecisionService.getAccomOverbookingDecisions(TestProperty.H1.getId().toString(), "Opera");
        assertEquals(50, opera.size(), "Expected Accom overboking row are 50 but they are :" + opera.size());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(uploadWindowEndDate);
        opera = operaDecisionService.getAccomOverbookingDecisions(TestProperty.H1.getId().toString(), "Opera");
        assertEquals(15, opera.size(), "Expected Accom overboking row are 15 but they are :" + opera.size());
        for (AccomOverbookingDecision accomOverbookingDecision : opera) {
            assertNotEquals(optWindowBDEEndDate, accomOverbookingDecision.getOccupancyDate());
        }
    }

    @Test
    public void shouldReturnOverbookingAccomDataWithoutExternalSystem() {
        when(configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(EXTERNAL_SYSTEM_NAME);
        when(configParamsServiceLocal.getParameterValue("pacman.integration.opera.RoomTypeOverbooking.uploadtype")).thenReturn("Full");

        PacmanOverbookingDecisionService pacmanOverbookingDecisionService = new PacmanOverbookingDecisionService();
        inject(operaDecisionService, "pacmanOverbookingDecisionService", pacmanOverbookingDecisionService);

        when(dateService.getOptimizationWindowStartDate()).thenReturn(DateUtil.removeTimeFromDate(new Date()));
        setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));

        List<AccomOverbookingDecision> opera = operaDecisionService.getAccomOverbookingDecisions(TestProperty.H1.getId().toString());
        assertEquals(50, opera.size(), "Expected Accom overboking row are 50 but they are :" + opera.size());
    }

    @Test
    public void testHasAccomOverbookingDecisions() {
        setupOverbookingDecisionService(DateUtil.removeTimeFromDate(new Date()));
        assertTrue(operaDecisionService.hasAccomOverbookingDecisions(THE_PROPERTY, EXTERNAL_SYSTEM_NAME));
    }

    @Test
    public void getLRVAtAccomTypeWithTaxQueryTest() {
        tenantCrudService().executeUpdateByNativeQuery(
                "insert into Decision_LRV_AT (Decision_id,property_id,accom_type_id,occupancy_dt,lrv,createDate_dttm) values \n" +
                        " (1,5,4,'2017-03-01',10.0001,GETDATE())\n" +
                        " insert into Decision_LRV_AT (Decision_id,property_id,accom_type_id,occupancy_dt,lrv,createDate_dttm) values \n" +
                        " (1,5,4,'2017-03-02',20.0002,GETDATE())\n" +
                        " insert into Decision_LRV_AT (Decision_id,property_id,accom_type_id,occupancy_dt,lrv,createDate_dttm) values \n" +
                        " (1,5,4,'2017-03-03',30.0003,GETDATE())");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("propertyId", 5);
        parameters.put("startDate", new LocalDate("2017-03-01").toDate());
        parameters.put("endDate", new LocalDate("2017-03-03").toDate());
        parameters.put("taxFactor", BigDecimal.valueOf(0.2));
        List<Object[]> objList = tenantCrudService().findByNamedQuery(LastRoomValueAccomType.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AT_ROOM_TYPE_WITHTAX, parameters);
        assertEquals(new BigDecimal(12.00012).setScale(6, RoundingMode.HALF_UP), objList.get(0)[5]);
        assertEquals(new BigDecimal(24.00024).setScale(6, RoundingMode.HALF_UP), objList.get(1)[5]);
        assertEquals(new BigDecimal(36.00036).setScale(6, RoundingMode.HALF_UP), objList.get(2)[5]);
    }
    @Test
    public void getAgileRateDecisionsWithUploadWindowDates() {
        Date currentDate = DateUtil.getCurrentDate();
        mockAgileRateDecisions(currentDate, currentDate, "P1", "P2");
        mockVendorMapping("P1", Constants.OPERA, "V1", "V2");
        mockVendorMapping("P2", Constants.OPERA);
        doReturn("full").when(configParamsServiceLocal).getParameterValue("pacman.integration.opera.AgileRates.uploadtype");
        DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository = mock(DecisionUploadDateToExternalSystemRepository.class);
        inject(operaDecisionService, "decisionUploadDateToExternalSystemRepository", decisionUploadDateToExternalSystemRepository);
        doCallRealMethod().when(dailyBarVendorRoomTypeTranslator).translateVendorRoomTypes(anyList(), anyString());
        List<DailyBarDecision> decisions = operaDecisionService.getAgileRateDecisions(TEST_DB_PROPERTY, Constants.OPERA, currentDate, currentDate);
        assertTrue(decisions != null && !decisions.isEmpty());
        assertTrue(decisions.stream().filter(decision -> "V1".equals(decision.getRateCode())).count() == 10);
        assertTrue(decisions.stream().filter(decision -> "V2".equals(decision.getRateCode())).count() == 10);
        assertTrue(decisions.stream().filter(decision -> "P1".equals(decision.getRateCode())).count() == 10);
        assertTrue(decisions.stream().filter(decision -> "P2".equals(decision.getRateCode())).count() == 10);
        verify(pacmanAgileRatesDecisionService).getAgileRatesDecision(false, null, "opera", currentDate, currentDate);
    }
    private void mockVendorMapping(String rateCode, String vendor, String... vendorRateCodes) {
        List<RateCodeVendorMapping> mappings = Arrays.asList(vendorRateCodes).stream()
                .map(vendorRateCode -> new RateCodeVendorMapping(rateCode, vendor, vendorRateCode))
                .collect(Collectors.toList());
        doReturn(mappings).when(rateCodeVendorMappingService).getForVendorAndRateCodeName(vendor, rateCode);
    }

    private void mockAgileRateDecisions(Date uploadWindowStartDate, Date uploadWindowEndDate, String... rateCodes) {
        List<DailyBarDecisions> mockDecisions = new ArrayList<>();
        Arrays.asList(rateCodes).forEach(rateCode -> {
            java.time.LocalDate rateDate = java.time.LocalDate.parse("2016-05-01");
            for (int i = 1; i <= 10; i++) {
                DailyBarDecisions decision = new DailyBarDecisions();
                decision.setRatePlan(rateCode);
                decision.setOccupancyDate(toDate(rateDate));
                mockDecisions.add(decision);
                rateDate = rateDate.plusDays(i);
            }
        });
        doReturn(mockDecisions).when(pacmanAgileRatesDecisionService).getAgileRatesDecision(anyBoolean(), any(), anyString(), eq(uploadWindowStartDate), eq(uploadWindowEndDate));
    }
}
