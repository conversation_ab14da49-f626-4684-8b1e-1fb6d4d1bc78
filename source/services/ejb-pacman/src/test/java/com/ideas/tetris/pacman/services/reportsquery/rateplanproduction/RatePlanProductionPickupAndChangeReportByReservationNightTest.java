package com.ideas.tetris.pacman.services.reportsquery.rateplanproduction;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class RatePlanProductionPickupAndChangeReportByReservationNightTest extends AbstractG3JupiterTest {
    private LocalDate startDate = LocalDate.of(2019, 5, 8);
    private int propertyID = 6;
    private int isRolling = 0;
    private int post_departure_revenue_adjustment_enabled = 0;
    private int mktSeg2 = 8;
    private String rateCode1 = "RATCOD1";
    private String date;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        createTestData();
        date = LocalDate.of(2019, 5, 8).toString();
    }

    private void createTestData() {

        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        populateIndividualTransactionData(insertQuery);
        //populateReservationNightData(insertQuery);
        populateGroupMasterData(insertQuery);
        populateGroupBlockData(insertQuery);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void populateIndividualTransactionData(StringBuilder insertQuery) {
        tenantCrudService().executeUpdateByNativeQuery("delete from dbo.individual_trans");
        insertQuery.append(" INSERT INTO [individual_trans]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM]) ");
        insertQuery.append("  VALUES (2,6,144746682000,'SS','2019-05-08','2019-08-15','2019-04-07',NULL,'QN',11,8,8414.01000," +
                "0.00000,0.00000,0.00000,0.00000,8414.01000,NULL,NULL,'RATCOD1',84.99000,NULL,'4P',0,1,'2019-04-07 11:56:08.870',NULL,NULL,'12:20:36.0000000');");
    }

    private void populateReservationNightData(StringBuilder insertQuery) {
        tenantCrudService().executeUpdateByNativeQuery("delete from dbo.RESERVATION_NIGHT");
        String propertyId = "6";
        String arrivalDate = "'2019-05-08'";
        String departureDate = "'2019-08-15'";

        String bookingDate = "'2019-04-07'";
        LocalDate fromOccupancyDate = LocalDate.parse("2019-05-08");
        LocalDate toOccupancyDate = LocalDate.parse("2019-08-14");

        String insertQueryStrPrefix = "INSERT INTO [RESERVATION_NIGHT]\n" +
                "([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]," +
                "[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue],[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue]," +
                "[Other_Revenue],[Total_Revenue],[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]," +
                "[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM],[Occupancy_DT])\n" +
                "VALUES\n";


        for (LocalDate date = fromOccupancyDate; date.isBefore(toOccupancyDate.plusDays(1)); date = date.plusDays(1)) {
            String occupancyDate = date.toString();
            String insertQueryStr = "(" +
                    "2," + propertyId + ",144746682000,'SS'," + arrivalDate + "," + departureDate + "," + bookingDate +
                    ",NULL,'QN',11,8,84.99000,0.00000,0.00000,0.00000,0.00000,84.99000,NULL,NULL,'RATCOD1',84.99000,NULL,'4P',0,1,'2019-04-07 11:56:08.870',NULL,NULL," +
                    "'12:20:36.0000000','" + occupancyDate + "')";
            insertQuery.append(insertQueryStrPrefix).append(insertQueryStr).append(";\n");
        }
    }


    @Test
    public void shouldValidateRatePlanProductionChangeReportForStaticDate_forSP_Post_Departure_Revenue_Adjustment_ON() {
        isRolling = 0;
        post_departure_revenue_adjustment_enabled = 1;
        populatePostDepData();
        List<Object[]> reportDataByStaticDates = tenantCrudService()
                .findByNativeQuery("exec dbo.usp_get_change_report_comparative_view_srp " + propertyID + ",'" + rateCode1 +
                        "','','','','','','','','','','','','','','','','','','','','','','','','',null,'2019-05-08','2019-08-15'," + isRolling +
                        ",'','',''," + post_departure_revenue_adjustment_enabled);
        assertRatePlanProductionReportForChangeData_With_PostDep_for_max_rate_code("Rate Plan Production Report For Change Data With Static Dates", reportDataByStaticDates);
        List<Object[]> reportDataByStaticDatesFromReservationNight = tenantCrudService()
                .findByNativeQuery("exec dbo.usp_get_change_report_comparative_view_srp_by_reservation_night " + propertyID + ",'" + rateCode1 +
                        "','','','','','','','','','','','','','','','','','','','','','','','','',null,'2019-05-08','2019-08-15'," + isRolling +
                        ",'','',''," + post_departure_revenue_adjustment_enabled);
        assertRatePlanProductionReportForChangeData_With_PostDep_for_max_rate_code("Rate Plan Production Report For Change Data With Static Dates", reportDataByStaticDatesFromReservationNight);
    }

    @Test
    public void shouldValidateRatePlanProductionChangeReportForStaticDate_forSP() {
        isRolling = 0;
        post_departure_revenue_adjustment_enabled = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService()
                .findByNativeQuery("exec dbo.usp_get_change_report_comparative_view_srp " + propertyID + ",'" + rateCode1 +
                        "','','','','','','','','','','','','','','','','','','','','','','','','',null,'2019-05-08','2019-08-15'," + isRolling +
                        ",'','',''," + post_departure_revenue_adjustment_enabled);
        assertRatePlanProductionReportForChangeData_for_max_rate_code("Rate Plan Production Report For Change Data With Static Dates", reportDataByStaticDates);
        List<Object[]> reportDataByStaticDatesFromReservationNight = tenantCrudService()
                .findByNativeQuery("exec dbo.usp_get_change_report_comparative_view_srp_by_reservation_night " + propertyID + ",'" + rateCode1 +
                        "','','','','','','','','','','','','','','','','','','','','','','','','',null,'2019-05-08','2019-08-15'," + isRolling +
                        ",'','',''," + post_departure_revenue_adjustment_enabled);
        assertRatePlanProductionReportForChangeData_for_max_rate_code("Rate Plan Production Report For Change Data With Static Dates", reportDataByStaticDatesFromReservationNight);
    }

    @Test
    public void shouldValidateRatePlanProductionPickupReportForStaticDate_forSP() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec  usp_get_pickup_report_comparative_view_srp 6," + rateCode1 + ",'','','','','','','','','','','','','','','','','','','','','','','','',null,null,'2019-05-08','2019-08-15',0,null,null,null,null,0");
        assertRatePlanProductionReportForPickupData_withMaxRateCode("Rate Plan Production Report For Pickup Data With Static Dates", reportDataByStaticDates);
        List<Object[]> reportDataByReservationNight = tenantCrudService().findByNativeQuery("exec  usp_get_pickup_report_comparative_view_srp_by_reservation_night 6," + rateCode1 + ",'','','','','','','','','','','','','','','','','','','','','','','','',null,null,'2019-05-08','2019-08-15',0,null,null,null,null,0");
        assertRatePlanProductionReportForPickupData_withMaxRateCode("Rate Plan Production Report For Pickup Data With Static Dates", reportDataByReservationNight);

    }

    @Test
    public void shouldValidateRatePlanProductionPickupReportForStaticDate_forSP_Post_Departure_Revenue_Adjustment_ON() {
        isRolling = 0;
        populatePostDepData();
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec  usp_get_pickup_report_comparative_view_srp 6," + rateCode1 + ",'','','','','','','','','','','','','','','','','','','','','','','','',null,null,'2019-05-08','2019-08-15',0,null,null,null,null,1");
        assertRatePlanProductionReportForPickupData_With_PostDep_for_max_rate_code("Rate Plan Production Report For Pickup Data With Static Dates", reportDataByStaticDates);
        List<Object[]> reportDataByReservationNight = tenantCrudService().findByNativeQuery("exec  usp_get_pickup_report_comparative_view_srp_by_reservation_night 6," + rateCode1 + ",'','','','','','','','','','','','','','','','','','','','','','','','',null,null,'2019-05-08','2019-08-15',0,null,null,null,null,1");
        assertRatePlanProductionReportForPickupData_With_PostDep_for_max_rate_code("Rate Plan Production Report For Pickup Data With Static Dates", reportDataByReservationNight);

    }

    private void assertRatePlanProductionReportForPickupData_With_PostDep_for_max_rate_code(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(date, (reportDataByStaticDates.get(0)[0].toString()), Level + " - Day Of Arrival ");
        assertEquals("1", (reportDataByStaticDates.get(0)[2].toString()), Level + " - Rooms On Books ");
        assertEquals("0", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Pick Up ");
        assertEquals("-115.01", String.format("%.2f", reportDataByStaticDates.get(0)[52]), Level + " - Revenue On Books ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[53]), Level + " - Revenue Difference ");
        assertEquals("-115.01", String.format("%.2f", reportDataByStaticDates.get(0)[102]), Level + " - ADR On Books ");

    }

    private void assertRatePlanProductionReportForPickupData_withMaxRateCode(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(date, (reportDataByStaticDates.get(0)[0].toString()), Level + " - Day Of Arrival ");
        assertEquals("1", (reportDataByStaticDates.get(0)[2].toString()), Level + " - Rooms On Books ");
        assertEquals("0", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Pick Up ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[52]), Level + " - Revenue On Books ");
        assertEquals("0.00", String.format("%.2f", reportDataByStaticDates.get(0)[53]), Level + " - Revenue Difference ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[102]), Level + " - ADR On Books ");
    }

    private void assertRatePlanProductionReportForChangeData_With_PostDep_for_max_rate_code(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(date, (reportDataByStaticDates.get(0)[0].toString()), Level + " - Occupancy Dt ");
        assertEquals("1", (reportDataByStaticDates.get(0)[2].toString()), Level + " - Rooms On Books ");
        assertEquals("1", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Rooms On Books Difference ");
        assertEquals("-115.01", String.format("%.2f", reportDataByStaticDates.get(0)[52]), Level + " - Revenue On Books ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[53]), Level + " - Revenue Difference ");
        assertEquals("-115.01", String.format("%.2f", reportDataByStaticDates.get(0)[102]), Level + " - ADR On Books ");
        assertEquals("-115.01", String.format("%.2f", reportDataByStaticDates.get(0)[103]), Level + " - ADR Difference ");
    }

    private void assertRatePlanProductionReportForChangeData_for_max_rate_code(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(date, (reportDataByStaticDates.get(0)[0].toString()), Level + " - Occupancy Dt ");
        assertEquals("1", (reportDataByStaticDates.get(0)[2].toString()), Level + " - Rooms On Books ");
        assertEquals("1", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Rooms On Books Difference ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[52]), Level + " - Revenue On Books ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[53]), Level + " - Revenue Difference ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[102]), Level + " - ADR On Books ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(0)[103]), Level + " - ADR Difference ");
        assertEquals(LocalDate.of(2019, 5, 8).plusDays(98).toString(), (reportDataByStaticDates.get(98)[0].toString()), Level + " - Occupancy Dt ");
        assertEquals("1", (reportDataByStaticDates.get(1)[2].toString()), Level + " - Rooms On Books ");
        assertEquals("1", (reportDataByStaticDates.get(1)[3].toString()), Level + " - Rooms On Books Difference ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(1)[52]), Level + " - Revenue On Books ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(1)[53]), Level + " - Revenue Difference ");
        assertEquals("84.99", String.format("%.2f", reportDataByStaticDates.get(1)[102]), Level + " - ADR On Books ");
    }

    private void populatePostDepData() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" INSERT INTO [post_departure_revenue]");
        insertQuery.append(" ([Reservation_Identifier],[occupancy_dt]");
        insertQuery.append(" ,[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[market_code],[Rate_Code],[Rate_Value])");
        insertQuery.append("  VALUES (144746682000,'" + LocalDate.of(2019, 5, 8).toString() + "' ");
        insertQuery.append(" ,11,'" + mktSeg2 + "',84.99,0,0,84.99,'MS2','" + rateCode1 + "',-200)");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private LocalDate getLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(6,3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }


    private void populateGroupMasterData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Group_Master]");
        insertQuery.append(" ([Property_ID],[Group_Code],[Group_Name],[Group_Description],[Master_Group_ID],[Master_Group_Code]");
        insertQuery.append(" ,[Group_Status_Code],[Group_Type_Code],[Mkt_Seg_ID],[Start_DT]");
        insertQuery.append(" ,[End_DT],[Booking_DT],[Pickup_Type_Code],[Cancel_DT],[Booking_type]");
        insertQuery.append(" ,[Sales_Person],[Cut_Off_date],[Cut_Off_days])");
        insertQuery.append("  VALUES (" + propertyID + ",'" + rateCode1 + "','" + rateCode1 + "','" + rateCode1 + "' ");
        insertQuery.append(" ,NULL,NULL,'DEFINITE','GROUP','" + mktSeg2 + "','" + startDate.plusDays(4).toString() + "','" + startDate.plusDays(15).toString() + "','" + startDate.minusDays(1).toString() + "',NULL,NULL,NULL,NULL,NULL,NULL)");

    }

    private void populateGroupBlockData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Group_Block]");
        insertQuery.append(" ([Group_ID],[Occupancy_DT],[Accom_Type_ID],[Blocks],[Pickup],[Original_Blocks],[rate])");
        insertQuery.append("  VALUES ((select group_id from Group_Master where Group_Code='" + rateCode1 + "'),'" + startDate.plusDays(7).toString() + "',11,10,2,0,100 )");
    }
}
