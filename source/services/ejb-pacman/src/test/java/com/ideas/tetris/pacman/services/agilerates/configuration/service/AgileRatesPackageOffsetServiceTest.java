package com.ideas.tetris.pacman.services.agilerates.configuration.service;

import com.ideas.tetris.pacman.services.agilerates.AgileRatesTestUtil;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackageChargeType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertTrue;

public class AgileRatesPackageOffsetServiceTest {

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private TenantCrudServiceBean crudService;

    @InjectMocks
    private AgileRatesPackageOffsetService service;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        service.syncEventAggregatorService = mock(SyncEventAggregatorService.class);
    }


    @Test
    public void deleteSeasonOffsets() {
        List<AgileRatesPackageChargeType> seasons = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();

        List<Integer> agileRatesPackageChargeTypeList = seasons
                .stream()
                .map(offset -> offset.getId())
                .collect(Collectors.toList());

        service.deleteSeasonOffsets(seasons);

        verify(crudService, times(1)).executeUpdateByNamedQuery(AgileRatesPackageChargeType.DELETE_BY_AGILE_RATES_PACKAGE_CHARGE_TYPE_ID,
                QueryParameter.with(AgileRatesPackageChargeType.AGILE_RATES_PACKAGE_CHARGE_TYPE_LIST, agileRatesPackageChargeTypeList).parameters());

        verify(crudService, times(1)).flushAndClear();
        verify(service.syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

    }

    @Test
    public void retrieveOffsetConfigDTO_whenSeasonStartDateIsNull_shouldNotCreateSeason() {

        var configOffsets = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();
        var packages = AgileRatesTestUtil.createAgileRatesPackages();

        var actualResult = service.retrieveOffsetConfigDTO(packages, configOffsets);

        assertTrue(actualResult.getSeasonOffsets().isEmpty());
    }

    @Test
    public void retrieveOffsetConfigDTO_whenSeasonStartDatePresent_shouldCreateSeason() {

        LocalDate now = LocalDate.now();
        LocalDate twoDaysFromToday = LocalDate.now().plusDays(2);
        var configOffsets = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();
        configOffsets.get(0).setStartDate(now);
        configOffsets.get(0).setEndDate(twoDaysFromToday);
        configOffsets.get(0).setName("Spring");
        var packages = AgileRatesTestUtil.createAgileRatesPackages();

        var actualResult = service.retrieveOffsetConfigDTO(packages, configOffsets);

        assertEquals(1, actualResult.getSeasonOffsets().size());
        assertTrue(actualResult.getSeasonOffsets().containsKey(now));
        assertEquals(now, actualResult.getSeasonOffsets().get(now).getStartDate());
        assertEquals(twoDaysFromToday, actualResult.getSeasonOffsets().get(now).getEndDate());
        assertEquals(1, actualResult.getSeasonOffsets().get(now).getOffsets().size());
        assertTrue(actualResult.getSeasonOffsets().get(now).getOffsets().containsKey(packages.get(0)));
        assertEquals(configOffsets, actualResult.getSeasonOffsets().get(now).getOffsets().get(packages.get(0)));
    }

    @Test
    public void addNewOccupancyBucketsInExistingPackageSeasonsOffset_withOnePackageChargeTypeAndNoSeason() {
        List<AgileRatesPackageChargeType> packageElement = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();

        when(agileRatesConfigurationService.findAllAgileRatesPackageChargeTypes()).thenReturn(packageElement);

        service.addNewOccupancyBucketsInExistingPackageSeasonsOffset(Collections.singletonList(OccupancyType.CHILD_BUCKET_1));

        ArgumentCaptor<List<AgileRatesPackageChargeType>> itemsCaptor = ArgumentCaptor.forClass(List.class);
        verify(crudService, times(1)).save(itemsCaptor.capture());
        List<AgileRatesPackageChargeType> offsetsToBeSaved = itemsCaptor.getValue();
        assertTrue(offsetsToBeSaved.size() == 1);
        assertTrue(offsetsToBeSaved.get(0).getStartDate() == null);
        assertEquals(packageElement.get(0).getName(), offsetsToBeSaved.get(0).getName());
    }

    @Test
    public void addNewOccupancyBucketsInExistingPackageSeasonsOffset_withOnePackageChargeTypeAndOneSeason() {
        List<AgileRatesPackageChargeType> packageElement = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();

        List<AgileRatesPackageChargeType> season = AgileRatesTestUtil.createAgileRatesPackageChargeTypes();
        season.get(0).setName("good season");
        season.get(0).setStartDate(new LocalDate("2022-02-02"));
        season.get(0).setEndDate(new LocalDate("2022-02-03"));

        List<AgileRatesPackageChargeType> packageAndSeasonList = new ArrayList();
        packageAndSeasonList.addAll(packageElement);
        packageAndSeasonList.addAll(season);

        when(agileRatesConfigurationService.findAllAgileRatesPackageChargeTypes()).thenReturn(packageAndSeasonList);

        service.addNewOccupancyBucketsInExistingPackageSeasonsOffset(Collections.singletonList(OccupancyType.CHILD_BUCKET_1));

        ArgumentCaptor<List<AgileRatesPackageChargeType>> itemsCaptor = ArgumentCaptor.forClass(List.class);
        verify(crudService, times(1)).save(itemsCaptor.capture());
        List<AgileRatesPackageChargeType> offsetsToBeSaved = itemsCaptor.getValue();
        assertTrue(offsetsToBeSaved.size() == 2);

        assertTrue(offsetsToBeSaved.get(1).getStartDate() == null);
        assertEquals(packageElement.get(0).getAgileRatesPackage().getName(), offsetsToBeSaved.get(1).getAgileRatesPackage().getName());

        assertTrue(offsetsToBeSaved.get(0).getStartDate().equals(new LocalDate("2022-02-02")));
        assertTrue(offsetsToBeSaved.get(0).getEndDate().equals(new LocalDate("2022-02-03")));
        assertEquals("good season", offsetsToBeSaved.get(0).getName());
    }

}
