package com.ideas.tetris.pacman.services.fplos;

import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetailsStg;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.apache.commons.lang3.Range;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

class RateQualifiedFixedDetailResolverTest extends RateQualifiedDetailResolverTestHelper {

    @Nested
    class ResolveRateValues {

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedWhenRetainIsTrue(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(4);
            assertEquals(1, result.size());
            assertEquals(BigDecimal.valueOf(80.), result.get(0).getMonday());
            assertEquals(BigDecimal.valueOf(80.), result.get(0).getTuesday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedWhenRetainIsTrueMultipleRates(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(80.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(100.), resultById.get(2).get(0).getMonday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedSomeNotRetained(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            Map<String, String> srpRelation = Map.of(SRPB, SRPA, SRPC, SRPB);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(80.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(2).get(0).getMonday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedSomeMoreNotRetained(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            Map<String, String> srpRelation = Map.of(SRPC, SRPB);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(80.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(80.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(2).get(0).getMonday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedSomeMoreNotRetainedWithAdjustments(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            Map<String, String> srpRelation = Map.of(SRPC, SRPB);

            var adjustment = new RateQualifiedAdjustment();
            adjustment.setNetValue(10.0f);
            adjustment.setNetValueTypeId(1);
            var rateCodeToAdjustment = Map.of(SRPB, adjustment);
            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, rateCodeToAdjustment, rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(90.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(2).get(0).getMonday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedSomeMoreNotRetainedWithPercentAdjustments(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            Map<String, String> srpRelation = Map.of(SRPC, SRPB);

            var adjustment = new RateQualifiedAdjustment();
            adjustment.setNetValue(10.0f);
            adjustment.setNetValueTypeId(2);
            var rateCodeToAdjustment = Map.of(SRPB, adjustment);
            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, rateCodeToAdjustment, rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(88., resultById.get(4).get(0).getMonday().doubleValue());
            assertEquals(88., resultById.get(3).get(0).getMonday().doubleValue());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(2).get(0).getMonday());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void populateStageToFixedSomeMoreNotRetainedWithSetAdjustments(boolean isRefinementEnabled) {
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(Map.of());
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(Map.of());
            Map<String, String> srpRelation = Map.of(SRPC, SRPB);

            var adjustment = new RateQualifiedAdjustment();
            adjustment.setNetValue(85.0f);
            adjustment.setNetValueTypeId(3);
            var rateCodeToAdjustment = Map.of(SRPB, adjustment);
            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, rateCodeToAdjustment, rateQualifiedById, srpRelation, isRefinementEnabled);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(85., resultById.get(4).get(0).getMonday().doubleValue());
            assertEquals(85., resultById.get(3).get(0).getMonday().doubleValue());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(2).get(0).getMonday());
        }


    }


    @Nested
    class ResolveSeasonWithRefinement {
        private final boolean IS_REFINEMENT_ENABLED = true;

        @Test
        void whenParentHasMissingAccomTypeAndChildHasRatesPresent() {
            List<Range<LocalDate>> seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, seasons, SRPA, seasons, SRPB, seasons, SRPC, seasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);
            child.get(SRPB).get(0).setAccomTypeId(2);
            parent.get(SRPB).get(0).setAccomTypeId(2);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(70.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(80.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(100.), resultById.get(2).get(0).getMonday());
        }

        @Test
        void whenParentIsSupersetOfChildSeasons() {
            var lv0seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, endDate.minusDays(2), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate.minusDays(3), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(endDate.minusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(4).get(0).getEndDate()));
            assertEquals(endDate.minusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(3).get(0).getEndDate()));
            assertEquals(endDate.minusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getEndDate()));
        }

        @Test
        void whenParentIsFragmented() {
            var lv0seasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo)
                    , Range.between(startDate.plusDays(3), endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, endDate.minusDays(2), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate.minusDays(3), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(4, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(endDate.minusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getEndDate()));
            assertEquals(startDate.plusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getStartDate()));
            assertEquals(startDate.plusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getStartDate()));
        }

        @Test
        void whenChildIsLargerThanParent() {
            var lv0seasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo)
                    , Range.between(startDate.plusDays(3), endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo), Range.between(startDate.plusDays(3), startDate.plusDays(4), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(7, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertSeasons(startDate, startDate.plusDays(2), _100, resultById.get(2).get(0));
            assertSeasons(startDate.plusDays(3), endDate.minusDays(1), _100, resultById.get(2).get(1));

            assertSeasons(startDate, startDate.plusDays(2), BigDecimal.valueOf(90.0), resultById.get(3).get(0));
            assertSeasons(startDate.plusDays(3), startDate.plusDays(4), BigDecimal.valueOf(90.0), resultById.get(3).get(1));

            assertSeasons(startDate, startDate.plusDays(2), _80, resultById.get(4).get(0));
            assertSeasons(startDate.plusDays(3), startDate.plusDays(4), _80, resultById.get(4).get(1));
            assertSeasons(startDate.plusDays(5), endDate, BigDecimal.valueOf(70.0), resultById.get(4).get(2));
        }

        @Test
        void gapsInParentRate_ShouldNotBeSkippedForChildRate() {
            var lv0seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(4), startDate.plusDays(5), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(2), startDate.plusDays(3), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(4), startDate.plusDays(5), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, true);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(6, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertSeasons(startDate, startDate.plusDays(1), _100, resultById.get(2).get(0));
            assertSeasons(startDate.plusDays(4), startDate.plusDays(5), _100, resultById.get(2).get(1));
            assertSeasons(startDate, startDate.plusDays(1), BigDecimal.valueOf(90.0), resultById.get(3).get(0));
            assertSeasons(startDate.plusDays(2), startDate.plusDays(3), _80, resultById.get(3).get(1));
            assertSeasons(startDate, startDate.plusDays(1), _80, resultById.get(4).get(0));
            assertSeasons(startDate.plusDays(4), startDate.plusDays(5), BigDecimal.valueOf(70.0), resultById.get(4).get(1));
        }

        @Test
        void gapsInRateSingleDaySeason() {
            var lv0seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(3), startDate.plusDays(3), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(4), startDate.plusDays(4), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, true);
            var result = resolver.resolve(List.of(2, 3));
            assertEquals(4, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertSeasons(startDate, startDate.plusDays(1), _100, resultById.get(2).get(0));
            assertSeasons(startDate.plusDays(3), startDate.plusDays(3), _100, resultById.get(2).get(1));
            assertSeasons(startDate, startDate.plusDays(1), BigDecimal.valueOf(90.0), resultById.get(3).get(0));
            assertSeasons(startDate.plusDays(4), startDate.plusDays(4), _80, resultById.get(3).get(1));
        }

        void assertSeasons(LocalDate startDate, LocalDate endDate, BigDecimal value, RateQualifiedFixedDetails rateQualifiedFixedDetails) {
            assertEquals(endDate, LocalDateUtils.toJavaLocalDate(rateQualifiedFixedDetails.getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(rateQualifiedFixedDetails.getStartDate()));
            assertEquals(value, rateQualifiedFixedDetails.getSunday());
        }
    }

    @Nested
    class ResolveSeason {
        private final boolean IS_REFINEMENT_ENABLED = false;

        @Test
        void whenParentAndChildHaveSameSeasons() {
            var seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, seasons, SRPA, seasons, SRPB, seasons, SRPC, seasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(BigDecimal.valueOf(80.), resultById.get(4).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(90.), resultById.get(3).get(0).getMonday());
            assertEquals(BigDecimal.valueOf(100.), resultById.get(2).get(0).getMonday());
        }

        @Test
        void whenParentIsSupersetOfChildSeasons() {
            var lv0seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, endDate.minusDays(2), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate.minusDays(3), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(3, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(endDate.minusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(4).get(0).getEndDate()));
            assertEquals(endDate.minusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(3).get(0).getEndDate()));
            assertEquals(endDate.minusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getEndDate()));
        }

        @Test
        void whenParentIsFragmented() {
            var lv0seasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo)
                    , Range.between(startDate.plusDays(3), endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, endDate.minusDays(2), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate.minusDays(3), LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, IS_REFINEMENT_ENABLED);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(4, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(endDate.minusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getEndDate()));
            assertEquals(startDate.plusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getStartDate()));
            assertEquals(startDate.plusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getStartDate()));
        }

        @Test
        void whenChildIsLargerThanParent() {
            var lv0seasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo)
                    , Range.between(startDate.plusDays(3), endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, endDate.minusDays(1), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo), Range.between(startDate.plusDays(3), startDate.plusDays(4), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, false);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(6, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(endDate.minusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getEndDate()));
            assertEquals(startDate.plusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(1).getStartDate()));
            assertEquals(startDate.plusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(resultById.get(2).get(0).getStartDate()));

            assertEquals(startDate.plusDays(4), LocalDateUtils.toJavaLocalDate(resultById.get(4).get(1).getEndDate()));
            assertEquals(startDate.plusDays(3), LocalDateUtils.toJavaLocalDate(resultById.get(4).get(1).getStartDate()));
            assertEquals(startDate.plusDays(2), LocalDateUtils.toJavaLocalDate(resultById.get(4).get(0).getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(resultById.get(4).get(0).getStartDate()));
        }

        @Test
        void gapsInRate() {
            var lv0seasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            var srpAseasons = List.of(Range.between(startDate, startDate.plusDays(1), LocalDate::compareTo), Range.between(startDate.plusDays(4), startDate.plusDays(5), LocalDate::compareTo));
            var srpBseasons = List.of(Range.between(startDate, startDate.plusDays(2), LocalDate::compareTo), Range.between(startDate.plusDays(3), startDate.plusDays(5), LocalDate::compareTo));
            var srpCseasons = List.of(Range.between(startDate, endDate, LocalDate::compareTo));
            Map<String, List<Range<LocalDate>>> seasonsByRateCode = Map.of(LV_0, lv0seasons, SRPA, srpAseasons, SRPB, srpBseasons, SRPC, srpCseasons);
            Map<String, List<RateQualifiedFixedDetailsStg>> child = createChildRates(seasonsByRateCode);
            child.get(SRPA).get(1).setSunday(_50);
            child.get(SRPA).get(1).setMonday(_50);
            child.get(SRPA).get(1).setTuesday(_50);
            child.get(SRPA).get(1).setWednesday(_50);
            child.get(SRPA).get(1).setThursday(_50);
            child.get(SRPA).get(1).setFriday(_50);
            child.get(SRPA).get(1).setSaturday(_50);
            Map<String, List<RateQualifiedFixedDetailsStg>> parent = createChildRates(seasonsByRateCode);
            parent.get(SRPA).get(1).setSunday(_50);
            parent.get(SRPA).get(1).setMonday(_50);
            parent.get(SRPA).get(1).setTuesday(_50);
            parent.get(SRPA).get(1).setWednesday(_50);
            parent.get(SRPA).get(1).setThursday(_50);
            parent.get(SRPA).get(1).setFriday(_50);
            parent.get(SRPA).get(1).setSaturday(_50);

            RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(child, parent, Map.of(), rateQualifiedById, srpRelation, false);
            var result = resolver.resolve(List.of(2, 3, 4));
            assertEquals(6, result.size());
            var resultById = result.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
            assertEquals(startDate.plusDays(5), LocalDateUtils.toJavaLocalDate(resultById.get(3).get(1).getEndDate()));
            assertEquals(startDate.plusDays(4), LocalDateUtils.toJavaLocalDate(resultById.get(3).get(1).getStartDate()));
            assertEquals(startDate.plusDays(1), LocalDateUtils.toJavaLocalDate(resultById.get(3).get(0).getEndDate()));
            assertEquals(startDate, LocalDateUtils.toJavaLocalDate(resultById.get(3).get(0).getStartDate()));
            assertEquals(_50, resultById.get(3).get(1).getSunday());
            assertEquals(_50, resultById.get(3).get(1).getMonday());
            assertEquals(_50, resultById.get(3).get(1).getTuesday());
            assertEquals(_50, resultById.get(3).get(1).getWednesday());
            assertEquals(_50, resultById.get(3).get(1).getThursday());
            assertEquals(_50, resultById.get(3).get(1).getFriday());
            assertEquals(_50, resultById.get(3).get(1).getSaturday());
        }

    }


}