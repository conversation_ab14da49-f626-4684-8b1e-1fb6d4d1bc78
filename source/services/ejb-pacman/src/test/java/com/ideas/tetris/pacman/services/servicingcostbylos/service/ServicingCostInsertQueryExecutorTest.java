package com.ideas.tetris.pacman.services.servicingcostbylos.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ServicingCostInsertQueryExecutorTest {
    @InjectMocks
    ServicingCostInsertQueryExecutor servicingCostInsertQueryExecutor;

    @Mock
    @TenantCrudServiceBean.Qualifier
    private CrudService crudService;
    public static final String QUERY = "sample query";

    @Test
    public void shouldThrowTetrisExceptionForInvalidRateCode() throws Exception {
        assertThrows(TetrisException.class, () -> {
            try {
                servicingCostInsertQueryExecutor.executeNativeQueryWithoutRequiresNew("XNAES", QUERY, Collections.singleton("Standard"), Collections.singleton("InvalidRateCode"));
                Assertions.fail("Should not execute this line");
            } catch (TetrisException e) {
                assertTrue(e.getMessage().contains("(InvalidRateCode)"));
                throw e;
            }
        });
    }

    @Test
    public void shouldThrowTetrisExceptionForInvalidAccomClassName() throws Exception {
        assertThrows(TetrisException.class, () -> {
            try {
                servicingCostInsertQueryExecutor.executeNativeQueryWithoutRequiresNew("XNAES", QUERY, Collections.singleton("InvalidAccomClassName"), Collections.singleton("CHHMYF"));
                Assertions.fail("Should not execute this line");
            } catch (TetrisException e) {
                assertTrue(e.getMessage().contains("(InvalidAccomClassName)"));
                throw e;
            }
        });
    }

    @Test
    public void shouldThrowTetrisExceptionForInvalidAccomClassNameAndInvalidRateCode() throws Exception {
        assertThrows(TetrisException.class, () -> {
            try {
                servicingCostInsertQueryExecutor.executeNativeQueryWithoutRequiresNew("XNAES", QUERY, Collections.singleton("InvalidAccomClassName"), Collections.singleton("InvalidRateCode"));
                Assertions.fail("Should not execute this line");
            } catch (TetrisException e) {
                assertTrue(e.getMessage().contains("(InvalidAccomClassName)"));
                assertTrue(e.getMessage().contains("(InvalidRateCode)"));
                throw e;
            }
        });
    }

    @Test
    public void shouldThrowExceptionDueToSQLExecution() throws Exception {
        assertThrows(Exception.class, () -> {
            try {
                when(crudService.executeUpdateByNativeQuery(QUERY)).thenThrow(RuntimeException.class);
                servicingCostInsertQueryExecutor.executeNativeQueryWithoutRequiresNew("XNAES", QUERY, Collections.singleton("Standard"), Collections.singleton("CHHMYF"));
                Assertions.fail("Should not execute this line");
            } catch (Exception sqe) {
                assertTrue(sqe.getMessage().contains("(Standard)"));
                assertTrue(sqe.getMessage().contains("(CHHMYF)"));
                throw sqe;
            }
        });
    }

    @Test
    public void ExecuteNativeQueriesHappyFlow() throws Exception {
        try {
            when(crudService.findByNativeQuery("SELECT Accom_Class_Name FROM Accom_Class WHERE Accom_Class_Name IN('Standard')")).thenReturn(Collections.singletonList("Standard"));
            when(crudService.findByNativeQuery("select Distinct(Rate_Code) from Reservation_Night rn inner join Mkt_seg_details msd ON rn.Mkt_Seg_ID = msd.Mkt_Seg_ID inner join Business_Type bt on bt.Business_Type_ID = msd.Business_Type_ID and bt.Business_Type_Name = 'Transient' and Rate_Code IN ('CHHMYF')")).thenReturn(Collections.singletonList("CHHMYF"));
            when(crudService.executeUpdateByNativeQuery(QUERY)).thenReturn(1);
            servicingCostInsertQueryExecutor.executeNativeQueryWithoutRequiresNew("XNAES", QUERY, Collections.singleton("Standard"), Collections.singleton("CHHMYF"));
        } catch (Exception e) {
            Assertions.fail("Should not reach here");
        }
    }
}