package com.ideas.tetris.pacman.services.demandoverride;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.bestavailablerate.RateDeterminator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.dto.DemandOverrideSummary;
import com.ideas.tetris.pacman.services.demandoverride.dto.ForecastGroupRemainingDemand;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride;
import com.ideas.tetris.pacman.services.groupwash.IndividualGroupForWashOverrideService;
import com.ideas.tetris.pacman.services.groupwash.WashOverrideByGroupCriteria;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.CrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.services.demandoverride.DemandSummaryService.SQL_FORECAST_GROUP_DEMAND;
import static com.ideas.tetris.pacman.services.demandoverride.DemandSummaryService.SQL_FORECAST_GROUP_INV_GROUP_DEMAND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class DemandSummaryServiceTest extends AbstractG3JupiterTest {

    private static final Integer TEST_FORECAST_GROUP = new Integer(4);
    private static final Integer TEST_ACCOMMODATION_CLASS = new Integer(3);
    private static final double DELTA = 0.01;

    private DemandSummaryService service = new DemandSummaryService();
    private DateService dateService;
    private DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private InventoryGroup inventoryGroup = new InventoryGroup();
    private InventoryGroupDetails inventoryGroupDetails;

    @BeforeEach
    public void setUp() {
        dateService = new DateService() {
            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getCurrentDateWithoutTime();
            }
        };
        service.setCrudService(tenantCrudService());
        service.setDateService(dateService);

        service.individualGroupForWashOverrideService = new IndividualGroupForWashOverrideService();
        service.individualGroupForWashOverrideService.setCrudService(tenantCrudService());

        RateDeterminator rateDeterminator = new RateDeterminator();
        rateDeterminator.setCrudService(tenantCrudService());

        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setUserId("1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        mockEnablePhysicalCapacityConsiderationAs(false);
        createInventoryGroupTestData(inventoryGroup, inventoryGroupDetails, TEST_ACCOMMODATION_CLASS);
    }

    @Test
    public void testGetDemandOverrideSummaryDataPropertyLevelDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, null, null);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(186), data.getRoomsSold());
        assertEquals(new BigDecimal("10.00"), data.getSystemDemand());
//		assertEquals(62, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("10.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(28, data.getWash().intValue());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataInvGrpLevelDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, null, null, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(31), data.getRoomsSold());
        assertEquals(new BigDecimal("5.00"), data.getSystemDemand());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("5.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(28, data.getWash().intValue());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());

    }

    @Test
    public void testGetDemandOverrideSummaryDataPropertyLevelSpecificDates()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), result.size());

        DemandOverrideSummary data = result.get(startDate);
        assertNotNull(data);
        assertEquals(startDate, data.getOccupancyDate());
        assertEquals(new BigDecimal(186), data.getRoomsSold());
        assertEquals(new BigDecimal("10.00"), data.getSystemDemand());
        assertEquals(61, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("10.00"), data.getUserDemand());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
//		assertEquals(true, data.isExistingWashOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        assertNotNull(data.getWash().intValue());

        data = result.get(endDate);
        assertNotNull(data);
        assertEquals(endDate, data.getOccupancyDate());
    }

 /*   @Test
    public void testSetOverrideFlagsForInvGrp(){
        CrudService crudService = mock(TenantCrudServiceBean.class);
        service.setCrudService(crudService);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        List<Object> accomClasses = new ArrayList<>();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClasses.add(new AccomClass());
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, null, null, inventoryGroup.getId());
        when( crudService.findByNamedQuery(InventoryGroupDetails.ALL_ACCOM_CLASSES_BY_VIEW_ORDER,
                QueryParameter.with("inventoryGroup", inventoryGroup.getId()).parameters())).thenReturn(accomClasses);
        verify(crudService).findByNamedQuery(eq(InventoryGroupDetails.ALL_ACCOM_CLASSES_BY_VIEW_ORDER), anyList());
        verify(crudService).findByNamedQuery(eq(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AND_ACCOM_CLASSES), anyList());

    }*/

    @Test
    public void testGetDemandOverrideSummaryDataInvGroupLevelSpecificDates()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), result.size());

        DemandOverrideSummary data = result.get(startDate);
        assertNotNull(data);
        assertEquals(startDate, data.getOccupancyDate());
        assertEquals(new BigDecimal(31), data.getRoomsSold());
        assertEquals(new BigDecimal("5.00"), data.getSystemDemand());
        assertEquals(153, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("5.00"), data.getUserDemand());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        assertNotNull(data.getWash().intValue());
        data = result.get(endDate);
        assertNotNull(data);
        assertEquals(endDate, data.getOccupancyDate());

    }

    public void setDataOfOverrideFlagsForInvGrp(Date startDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_Demand_FCST_OVR where Occupancy_DT = :dt", QueryParameter.with("dt", startDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("delete from arrival_Demand_FCST_OVR where Arrival_DT= :dt", QueryParameter.with("dt", startDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("insert into Occupancy_Demand_FCST_OVR values  (1,5,2,3,:dt,4.00,4.00, 8, 1, 12,11403, :dt, 11403,:dt, null,1,null); ", QueryParameter.with("dt", startDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("insert into arrival_Demand_FCST_OVR values  (1,5,2,3,:dt,1,'4.00','4.40', 8, 1, 11403, :dt, 11403,:dt, null,1,null) ", QueryParameter.with("dt", startDate).parameters());
    }

    @Test
    public void shouldConsiderPhysicalCapacityWhileGettingDemandOverrideSummaryDataAtPropertyLevelWhenPhysicalCapacityConsiderationToggleIsON()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();

        List<Object[]> resultList = new ArrayList<Object[]>();
        Object[] row = {193, new java.sql.Date(startDate.getTime()), new BigDecimal("10.00"), new BigDecimal("186"), new BigDecimal("64.607100"), new BigDecimal("10.00"), new BigDecimal("28.00"), new BigDecimal("43.750000"), 5, new BigDecimal("236.00"), new BigDecimal("43.750000"), new BigDecimal(448), new BigDecimal("276.00")};
        resultList.add(row);
        Query mockQuery = mock(Query.class);
        EntityManager mockEntityManager = mock(EntityManager.class);
        when(mockEntityManager.createNativeQuery(getPropertyDemandDataQuery())).thenReturn(mockQuery);
        when(mockQuery.getResultList()).thenReturn(resultList);
        CrudService spyCrudService = spy(new CrudServiceBean(null, mockEntityManager){});
        mockOverrideQueries(mockEntityManager);
        service.setCrudService(spyCrudService);

        IndividualGroupForWashOverrideService mockIndividualGroupForWashOverrideService = mock(IndividualGroupForWashOverrideService.class);
        service.individualGroupForWashOverrideService = mockIndividualGroupForWashOverrideService;
        when(mockIndividualGroupForWashOverrideService.getWashOverrideByGroups(Mockito.any(WashOverrideByGroupCriteria.class))).thenReturn(null);

        mockEnablePhysicalCapacityConsiderationAs(true);

        Map<Date, DemandOverrideSummary> result = service.getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);

        DemandOverrideSummary data = result.get(startDate);
        assertNotNull(data);
        assertEquals(startDate, data.getOccupancyDate());
        assertEquals(new BigDecimal(186), data.getRoomsSold());
        assertEquals(new BigDecimal("10.00"), data.getSystemDemand());
        assertEquals(61, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("10.00"), data.getUserDemand());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        assertNotNull(data.getWash().intValue());
    }

    @Test
    public void shouldConsiderPhysicalCapacityWhileGettingDemandOverrideSummaryDataAtInvGroupLevelWhenPhysicalCapacityConsiderationToggleIsON()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();

        List<Object[]> resultList = new ArrayList<Object[]>();
        Object[] row = {193, new java.sql.Date(startDate.getTime()), new BigDecimal("10.00"), new BigDecimal("186"), new BigDecimal("64.607100"), new BigDecimal("10.00"), new BigDecimal("28.00"), new BigDecimal("43.750000"), 5, new BigDecimal("236.00"), new BigDecimal("43.750000"), new BigDecimal(448), new BigDecimal("276.00")};
        resultList.add(row);
        Query mockQuery = mock(Query.class);
        EntityManager mockEntityManager = mock(EntityManager.class);
        when(mockEntityManager.createNativeQuery(getInvGroupDemandQuery())).thenReturn(mockQuery);
        when(mockQuery.getResultList()).thenReturn(resultList);
        CrudService spyCrudService = spy(new CrudServiceBean(null, mockEntityManager) {});
        mockOverrideQueries(mockEntityManager);
        service.setCrudService(spyCrudService);

        IndividualGroupForWashOverrideService mockIndividualGroupForWashOverrideService = mock(IndividualGroupForWashOverrideService.class);
        service.individualGroupForWashOverrideService = mockIndividualGroupForWashOverrideService;
        when(mockIndividualGroupForWashOverrideService.getWashOverrideByGroups(Mockito.any(WashOverrideByGroupCriteria.class))).thenReturn(null);
        when(spyCrudService.find(InventoryGroup.class, inventoryGroup.getId())).thenReturn(inventoryGroup);
        mockEnablePhysicalCapacityConsiderationAs(true);

        Map<Date, DemandOverrideSummary> result = service.getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        assertNotNull(result);

        DemandOverrideSummary data = result.get(startDate);
        assertNotNull(data);
        assertEquals(startDate, data.getOccupancyDate());
        assertEquals(new BigDecimal(186), data.getRoomsSold());
        assertEquals(new BigDecimal("10.00"), data.getSystemDemand());
        assertEquals(61, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("10.00"), data.getUserDemand());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(-1, data.getLastRoomValue().intValue());
        assertNotNull(data.getWash().intValue());
    }

    private void mockEnablePhysicalCapacityConsiderationAs(boolean isEnablePhysicalCapcity) {
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.pacmanConfigParamsService = mockPacmanConfigParamsService;
        when(mockPacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(isEnablePhysicalCapcity);
    }

    private void mockOverrideQueries(EntityManager mockEntityManager) {
        Query mockNullQuery = mock(Query.class);
        when(mockEntityManager.createNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID)).thenReturn(mockNullQuery);
        when(mockEntityManager.createNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_RANGE_AND_PROPERTY_ID)).thenReturn(mockNullQuery);
        when(mockEntityManager.createNamedQuery(WashOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID)).thenReturn(mockNullQuery);
        when(mockEntityManager.createNamedQuery(InventoryGroupDetails.ALL_ACCOM_CLASSES_BY_VIEW_ORDER)).thenReturn(mockNullQuery);
        when(mockEntityManager.createNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AND_ACCOM_CLASSES)).thenReturn(mockNullQuery);
        when(mockEntityManager.createNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_RANGE_AND_PROPERTY_ID_AND_ACCOM_CLASSES)).thenReturn(mockNullQuery);

        when(mockNullQuery.getResultList()).thenReturn(Collections.EMPTY_LIST);
    }

    private String getPropertyDemandDataQuery() {
        return "select df.Peak_Demand_ID, df.Occupancy_DT, df.Total_Remaining_Demand, ta.Rooms_Sold, occ.Occupancy_Percent, " +
                "df.Total_User_Remaining_Demand, wf.User_Wash, df.Peak_Demand_Percentage, df.Property_ID, " +
                "df.Total_Peak_Demand, df.User_Peak_Demand_Percentage, " +
                "occ.Total_Accom_Capacity, occ.Occupancy_NBR" +
                " from Vw_Peak_Demand as df, Total_Activity as ta, " +
                "FN_Occupancy_Forecast_Zero_Diet(:caughtUpDate, 0) as occ, Wash_Property_FCST as wf " +
                "where df.Property_ID = :propertyId and ta.Property_ID = df.Property_ID " +
                "and ta.Occupancy_DT = df.Occupancy_DT and occ.Property_ID = df.Property_ID " +
                "and occ.Occupancy_DT = df.Occupancy_DT " +
                "and wf.Property_ID = df.Property_ID and wf.Occupancy_DT = df.Occupancy_DT " +
                "and df.Occupancy_DT >= :startDate and df.Occupancy_DT <= :endDate " +
                "order by df.Occupancy_DT";
    }

    private String getInvGroupDemandQuery() {
        return new StringBuilder()
                .append(" select  df.Peak_Demand_ID,df.Occupancy_DT ")
                .append(" 	, df.Total_Remaining_Demand,aa.Rooms_Sold, occ.Occupancy_Percent ")
                .append(" 	,df.Total_User_Remaining_Demand, wf.User_Wash, df.Peak_Demand_Percentage ")
                .append(" 	,:propertyId, df.Total_Peak_Demand, df.User_Peak_Demand_Percentage ")
                .append(" 	,occ.Accom_Capacity, occ.Occupancy_NBR ")
                .append(" from Vw_Peak_Demand_Inventory_Group as df ")
                .append(" join Accom_Activity as aa ")
                .append(" on aa.Occupancy_DT = df.Occupancy_DT ")
                .append(" and df.Inventory_Group_ID = :inventoryGroupId ")
                .append(" join Accom_Type as at ")
                .append(" on aa.Accom_Type_ID = at.Accom_Type_ID ")
                .append(" join Inventory_Group_Details as igd ")
                .append(" on igd.Accom_Class_ID = at.Accom_Class_ID ")
                .append(" and igd.Inventory_Group_ID = :inventoryGroupId ")
                .append(" join FN_Occupancy_Forecast_Inventory_Group_View(:caughtUpDate, 0, :inventoryGroupId, 2) as occ ")
                .append(" on occ.Occupancy_DT = df.Occupancy_DT ")
                .append(" join Wash_Property_FCST as wf ")
                .append(" on wf.Occupancy_DT = df.Occupancy_DT ")
                .append(" where df.Occupancy_DT >= :startDate and df.Occupancy_DT <= :endDate ")
                .append(" order by df.Occupancy_DT ")
                .toString();
    }

    @Test
    public void testGetDemandOverrideSummaryDataPropertyLevelInvalidDates()
            throws Exception {
        Date startDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        Date endDate = DateUtil.getFirstDayOfNextMonth();
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDemandOverrideSummaryDataPropertyLevelDistantFuture()
            throws Exception {
        Date startDate = DateUtil.addMonthsToDate(DateUtil.getCurrentDate(), 6);
        Date endDate = DateUtil.addMonthsToDate(startDate, 1);
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDemandOverrideSummaryDataInvGroupLevelDistantFuture()
            throws Exception {
        Date startDate = DateUtil.addMonthsToDate(DateUtil.getCurrentDate(), 6);
        Date endDate = DateUtil.addMonthsToDate(startDate, 1);
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDemandOverrideSummaryDataPropertyLevelDistantPast()
            throws Exception {
        Date startDate = dateFormat.parse("2001-04-01");
        Date endDate = dateFormat.parse("2001-05-31");
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDemandOverrideSummaryDataInvGroupLevelDistantPast()
            throws Exception {
        Date startDate = dateFormat.parse("2001-04-01");
        Date endDate = dateFormat.parse("2001-05-31");
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDemandOverrideSummaryDataForecastGroupLevelDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(15), data.getRoomsSold());
        assertEquals(new BigDecimal("2.00"), data.getSystemDemand());
        assertEquals(25, data.getOccupancyForecast().intValue());
//		assertEquals(62, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(new BigDecimal("2.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(3, data.getWash().intValue());
        assertEquals(-1, data.getLastRoomValue().intValue());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataForecastGroupLevelForInvGroupDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(2), data.getRoomsSold());
        assertEquals(new BigDecimal("1.00"), data.getSystemDemand());
        assertEquals(4, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("1.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(3, data.getWash().intValue());
        assertEquals(-1, data.getLastRoomValue().intValue());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void shouldSkipComponentDataForDemandAndWashManagementIfRoomClassIsPropertyAndForecastGroupIsSelected() throws Exception {
        updateAccomTypeToComponent("N");
        Map<Date, DemandOverrideSummary> dataWithoutComponent = service.getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null);
        assertNotNull(dataWithoutComponent);
        Date occupancyDate = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary dataWithoutComponents = dataWithoutComponent.get(occupancyDate);
        assertNotNull(dataWithoutComponents);
        assertEquals(new BigDecimal(15), dataWithoutComponents.getRoomsSold());
        assertEquals(25, dataWithoutComponents.getOccupancyForecast().intValue());

        updateAccomTypeToComponent("Y");
        Map<Date, DemandOverrideSummary> dataWhenComponentRoomPresent = service.getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null);
        assertNotNull(dataWhenComponentRoomPresent);
        DemandOverrideSummary dataWhenComponentPresent = dataWhenComponentRoomPresent.get(occupancyDate);
        assertNotNull(dataWhenComponentPresent);

        Object[] roomsSoldsAndOccForecast = getCRRoomsSoldAndOccForecastForOccDate(occupancyDate);
        BigDecimal crRoomsSold = (BigDecimal) roomsSoldsAndOccForecast[1];

        assertEquals(new BigDecimal(15).subtract(crRoomsSold).doubleValue(), dataWhenComponentPresent.getRoomsSold().doubleValue(), DELTA);
    }

    @Test
    public void shouldSkipComponentDataForDemandAndWashManagementForInvGroupIfRoomClassIsPropertyAndForecastGroupIsSelected() throws Exception {
        updateAccomTypeToComponent("N");
        Map<Date, DemandOverrideSummary> dataWithoutComponent = service.getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null, inventoryGroup.getId());
        assertNotNull(dataWithoutComponent);
        Date occupancyDate = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary dataWithoutComponents = dataWithoutComponent.get(occupancyDate);
        assertNotNull(dataWithoutComponents);
        assertEquals(new BigDecimal(2), dataWithoutComponents.getRoomsSold());
        assertEquals(4, dataWithoutComponents.getOccupancyForecast().intValue());

        updateAccomTypeToComponent("Y");
        Map<Date, DemandOverrideSummary> dataWhenComponentRoomPresent = service.getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, null, null, inventoryGroup.getId());
        assertNotNull(dataWhenComponentRoomPresent);
        DemandOverrideSummary dataWhenComponentPresent = dataWhenComponentRoomPresent.get(occupancyDate);
        assertNotNull(dataWhenComponentPresent);

        Object[] roomsSoldsAndOccForecast = getCRRoomsSoldAndOccForecastForOccDate(occupancyDate);
        BigDecimal crRoomsSold = (BigDecimal) roomsSoldsAndOccForecast[1];

        assertEquals(new BigDecimal(5).subtract(crRoomsSold).doubleValue(), dataWhenComponentPresent.getRoomsSold().doubleValue(), DELTA);
    }

    private Object[] getCRRoomsSoldAndOccForecastForOccDate(Date occupancyDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("foreCastGroupId", TEST_FORECAST_GROUP);
        parameters.put("occupancyDate", occupancyDate);

        String queryToGetComponentRoomsSoldAndOccupancyForecast = "SELECT occ.Occupancy_DT, SUM(maa.Rooms_Sold) AS Rooms_Sold " +
                "FROM dbo.Occupancy_FCST AS occ INNER JOIN " +
                "dbo.Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID AND at.isComponentRoom = 'Y' INNER JOIN " +
                "dbo.Mkt_Seg_Forecast_Group AS msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID LEFT JOIN " +
                "dbo.Occupancy_Demand_FCST AS df ON occ.Property_ID = df.Property_ID AND occ.Occupancy_DT = df.Occupancy_DT AND " +
                "msfg.Forecast_Group_ID = df.Forecast_Group_ID AND at.Accom_Class_ID = df.Accom_Class_ID INNER JOIN " +
                "dbo.Mkt_Accom_Activity AS maa ON occ.Property_ID = maa.Property_ID AND occ.Occupancy_DT = maa.Occupancy_DT AND " +
                "maa.Accom_Type_ID = at.Accom_Type_ID AND maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID " +
                "WHERE     (msfg.Status_ID = 1) and occ.Occupancy_DT = :occupancyDate " +
                "and msfg.Forecast_Group_ID = :foreCastGroupId " +
                "GROUP BY occ.Occupancy_DT";

        List<Object[]> roomsSoldsAndOccForecastList = tenantCrudService().findByNativeQuery(queryToGetComponentRoomsSoldAndOccupancyForecast, parameters);
        assertNotNull(roomsSoldsAndOccForecastList);
        assertTrue(roomsSoldsAndOccForecastList.size() == 1);
        return roomsSoldsAndOccForecastList.get(0);
    }

    private void updateAccomTypeToComponent(String isComponentRoom) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = '" + isComponentRoom + "' where Accom_Type_ID = 6 and Property_ID = 5");
        tenantCrudService().flushAndClear();
    }

    @Test
    public void testGetDemandOverrideSummaryDataAccomClassLevelDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, TEST_ACCOMMODATION_CLASS, null, null);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(2), data.getRoomsSold());
        assertEquals(new BigDecimal("1.00"), data.getSystemDemand());
        assertEquals(4, data.getOccupancyForecast().intValue());
//		assertEquals(62, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(new BigDecimal("1.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(3, data.getWash().intValue());
        assertEquals(35, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataForInvGroupAccomClassLevelDefaultDates()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, TEST_ACCOMMODATION_CLASS, null, null, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(2), data.getRoomsSold());
        assertEquals(new BigDecimal("1.00"), data.getSystemDemand());
        assertEquals(4, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("1.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(3, data.getWash().intValue());
        assertEquals(35, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataAccomClassLevelDefaultDates_no_fcst_FG()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(7, 2, null, null);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(9), data.getRoomsSold());
        assertEquals(new BigDecimal("0.00"), data.getSystemDemand());
        assertEquals(15, data.getOccupancyForecast().intValue());
        assertTrue(data.getPropertyOccupancyForecastPercentage().intValue() > 0, "Expected an occupanc forecast percentage greater than 0");
        assertEquals(new BigDecimal("0.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(30, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataForInvGroupAccomClassLevelDefaultDates_no_fcst_FG()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(7, TEST_ACCOMMODATION_CLASS, null, null, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(2), data.getRoomsSold());
        assertEquals(new BigDecimal("0.00"), data.getSystemDemand());
        assertEquals(4, data.getOccupancyForecast().intValue());
        assertTrue(data.getPropertyOccupancyForecastPercentage().intValue() > 0, "Expected an occupanc forecast percentage greater than 0");
        assertEquals(new BigDecimal("0.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(35, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataAccomClassLevelDefaultDates_no_fcst_wash_FG()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service.getDemandOverrideSummaryData(1, 2, null, null);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(8), data.getRoomsSold());
        assertEquals(new BigDecimal("0.00"), data.getSystemDemand());
        assertEquals(14, data.getOccupancyForecast().intValue());
        assertTrue(data.getPropertyOccupancyForecastPercentage().intValue() > 0, "Expected an occupanc forecast percentage greater than 0");
        assertEquals(new BigDecimal("0.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(6, data.getWash().intValue());
        assertEquals(30, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testGetDemandOverrideSummaryDataForInvGroupAccomClassLevelDefaultDates_no_fcst_wash_FG()
            throws Exception {
        Map<Date, DemandOverrideSummary> result = service.getDemandOverrideSummaryData(1, TEST_ACCOMMODATION_CLASS, null, null, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        DemandOverrideSummary data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
        assertEquals(new BigDecimal(2), data.getRoomsSold());
        assertEquals(new BigDecimal("0.00"), data.getSystemDemand());
        assertEquals(4, data.getOccupancyForecast().intValue());
        assertTrue(data.getPropertyOccupancyForecastPercentage().intValue() > 0, "Expected an occupanc forecast percentage greater than 0");
        assertEquals(new BigDecimal("0.00"), data.getUserDemand());
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        assertEquals(6, data.getWash().intValue());
        assertEquals(35, data.getLastRoomValue().intValue());
        assertEquals("LV2", data.getBestAvailableRate());
        date = DateUtil.getLastDayOfCurrentMonth();
        data = result.get(date);
        assertNotNull(data);
        assertEquals(date, data.getOccupancyDate());
    }

    @Test
    public void testOverrideIndicatorsPropertyLevel()
            throws Exception {
        Date startDate = DateUtil.getCurrentDateWithoutTime();
        Date endDate = DateUtil.addDaysToDate(startDate, 90);
        createArrivalDemandOverrideDataForArrivalDates(List.of(
                DateUtil.addDaysToDate(startDate, 30),
                DateUtil.addDaysToDate(startDate, 50)
        ), null, null);
        createWashOverrideDataForOccupancyDates(List.of(
                DateUtil.addDaysToDate(startDate, 30)
        ), null);
        createOccupancyOverrideDataForOccupancyDates(List.of(
                DateUtil.addDaysToDate(startDate, 60)
        ), null, null);

        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(91, result.size());

        Date targetDate = DateUtil.addDaysToDate(startDate, 30);
        DemandOverrideSummary data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(true, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 20);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(true, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 10);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(true, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
    }

    @Test
    public void testOverrideIndicatorsForecastGroupLevel()
            throws Exception {
        Date startDate = DateUtil.getCurrentDateWithoutTime();
        Date endDate = DateUtil.addDaysToDate(startDate, 90);
        createWashOverrideDataForOccupancyDates(List.of(
                DateUtil.addDaysToDate(startDate, 30)
        ), TEST_FORECAST_GROUP);

        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(91, result.size());

        Date targetDate = DateUtil.addDaysToDate(startDate, 30);
        DemandOverrideSummary data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 20);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 10);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        result = service.getDemandOverrideSummaryData(4, null, startDate, endDate);
        assertNotNull(result);
        assertEquals(91, result.size());

        targetDate = DateUtil.addDaysToDate(startDate, 30);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 20);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 10);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
    }

    @Test
    public void testOverrideIndicatorsAccomClassLevel()
            throws Exception {
        Date startDate = DateUtil.getCurrentDateWithoutTime();
        Date endDate = DateUtil.addDaysToDate(startDate, 90);
        createWashOverrideDataForOccupancyDates(List.of(
                DateUtil.addDaysToDate(startDate, 30)
        ), TEST_FORECAST_GROUP);

        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(TEST_FORECAST_GROUP, 3, startDate, endDate);
        assertNotNull(result);
        assertEquals(91, result.size());

        Date targetDate = DateUtil.addDaysToDate(startDate, 30);
        DemandOverrideSummary data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 20);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 10);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        result = service.getDemandOverrideSummaryData(TEST_FORECAST_GROUP, 3, startDate, endDate);
        assertNotNull(result);
        assertEquals(91, result.size());

        targetDate = DateUtil.addDaysToDate(startDate, 30);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 20);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
        targetDate = DateUtil.addDaysToDate(targetDate, 10);
        data = result.get(targetDate);
        assertNotNull(data);
        assertEquals(false, data.isExistingArrivalDemandOverride());
        assertEquals(false, data.isExistingOccupancyDemandOverride());
        assertEquals(false, data.isExistingWashOverride());
    }

    private void createInventoryGroupTestData(InventoryGroup inventoryGroup, InventoryGroupDetails inventoryGroupDetails, Integer accomClass) {
        inventoryGroup.setName("INV_GRP" + accomClass);
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, accomClass);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, accomClass));
        tenantCrudService().save(inventoryGroupDetails);
    }

    @Test
    public void testGetDemandOverrideSummaryDataInvGroupLevelSpecificDatesWithIndicators()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        setDataOfOverrideFlagsForInvGrp(startDate);

        Map<Date, DemandOverrideSummary> result = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), result.size());
        DemandOverrideSummary data = result.get(startDate);
        assertNotNull(data);

        assertEquals(true, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingArrivalDemandOverride());
        assertEquals(new BigDecimal(31), data.getRoomsSold());
        assertEquals(new BigDecimal("5.00"), data.getSystemDemand());
        assertEquals(153, data.getPropertyOccupancyForecastPercentage().intValue());
        assertEquals(-1, data.getOccupancyForecast().intValue());
        assertEquals(new BigDecimal("5.00"), data.getUserDemand());
        assertEquals(-1, data.getLastRoomValue().intValue());
        assertNotNull(data.getWash().intValue());

    }

    @Test
    public void testGetDemandOverrideIndicatorsForInvGrps()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        setDataOfOverrideFlagsForInvGrp(startDate);

        InventoryGroup inv2 = new InventoryGroup();
        InventoryGroupDetails inventoryGroupDetails = null;
        createInventoryGroupTestData(inv2, inventoryGroupDetails, 4);
        Map<Date, DemandOverrideSummary> resultOfInv1 = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inventoryGroup.getId());
        Map<Date, DemandOverrideSummary> resultofInv2 = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate, inv2.getId());
        assertNotNull(resultOfInv1);
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), resultOfInv1.size());
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), resultofInv2.size());
        DemandOverrideSummary dataOfInv1 = resultOfInv1.get(startDate);
        assertNotNull(dataOfInv1);
        DemandOverrideSummary dataOFINV2 = resultofInv2.get(startDate);
        assertEquals(true, dataOfInv1.isExistingOccupancyDemandOverride());
        assertEquals(true, dataOfInv1.isExistingArrivalDemandOverride());
        assertEquals(false, dataOFINV2.isExistingOccupancyDemandOverride());
        assertEquals(false, dataOFINV2.isExistingArrivalDemandOverride());

    }

    @Test
    public void testGetDemandOverrideIndicatorsForInvGrpsAndProperty()
            throws Exception {
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getLastDayOfTwoMonthsFromNow();
        setDataOfOverrideFlagsForInvGrp(startDate);
        Map<Date, DemandOverrideSummary> result1 = service
                .getDemandOverrideSummaryData(null, null, startDate, endDate);
        assertNotNull(result1);
        assertEquals(DateUtil.getNumberOfDaysInNextMonthAndNextMonthAfter(), result1.size());
        DemandOverrideSummary data = result1.get(startDate);
        assertNotNull(data);
        assertEquals(true, data.isExistingOccupancyDemandOverride());
        assertEquals(true, data.isExistingArrivalDemandOverride());

        assertNotNull(data.getWash().intValue());

    }

    @Test
    public void validateForecastGroupDemandQuery() {
        Map<String, Object> map = getParamMap();
        List<Object[]> resultList = tenantCrudService().findByNativeQuery(SQL_FORECAST_GROUP_DEMAND, map);
        assertEquals(1, resultList.size());
        assertEquals(0, resultList.get(0)[8]);
    }

    @Test
    public void validateForecastGroupDemandInvGroupQuery() {
        Map<String, Object> map = getParamMap();
        map.remove("propertyId");
        map.put("inventoryGroupId", inventoryGroup.getId());
        List<Object[]> resultList = tenantCrudService().findByNativeQuery(String.format(SQL_FORECAST_GROUP_INV_GROUP_DEMAND, "FN_Occupancy_Forecast_Inventory_Group_View(:caughtUpDate, 0, :inventoryGroupId, 2)"), map);
        assertEquals(1, resultList.size());
        assertEquals(0, resultList.get(0)[8]);
    }

    private Map<String, Object> getParamMap() {
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        Map<String, Object> map = new HashMap<>();
        map.put("startDate", date);
        map.put("caughtUpDate", dateService.getCaughtUpDate());
        map.put("endDate", date);
        map.put("propertyId", 5);
        map.put("forecastGroupId", TEST_FORECAST_GROUP);
        return map;
    }

    @Test
    public void shouldGetDemandOverridesByDateAndAccomClass() {
        //GIVEN
        LocalDate today = LocalDate.now();
        Integer accomClassId = 2;
        tenantCrudService().executeUpdateByNativeQuery("insert into Occupancy_Demand_FCST values(312,5,2," + accomClassId + "," +
                "'" + today + "','3.00','0.00','0.16','10.25',getdate(),null,null,1, null)");
        addOccupancyDemandFcst(today, accomClassId, "100.25", "1");
        addOccupancyDemandFcst(today, accomClassId, "90.25", "2");
        addOccupancyDemandFcst(today, accomClassId, "80.25", "3");
        addOccupancyDemandFcst(today, accomClassId, "70.25", "4");
        addOccupancyDemandFcst(today, accomClassId, "60.25", "7");
        addOccupancyDemandFcst(today, accomClassId, "50.25", "8");
        addOccupancyDemandFcst(today, accomClassId, "40.25", "9");
        addOccupancyDemandFcst(today, accomClassId, "30.25", "10");
        //WHEN
        List<ForecastGroupRemainingDemand> demandOverridesByDateAndAccomClass =
                service.getDemandOverridesByDateAndAccomClass(today, 2);
        //THEN
        assertEquals(1, demandOverridesByDateAndAccomClass.get(0).getForecastGroupId().intValue());
        assertEquals("Complementary", demandOverridesByDateAndAccomClass.get(0).getForecastGroupName());
        assertEquals(1, demandOverridesByDateAndAccomClass.get(0).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Barter", demandOverridesByDateAndAccomClass.get(0).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("100.25"), demandOverridesByDateAndAccomClass.get(0).getUserRemainingDemand());

        assertEquals(2, demandOverridesByDateAndAccomClass.get(1).getForecastGroupId().intValue());
        assertEquals("Corporate", demandOverridesByDateAndAccomClass.get(1).getForecastGroupName());
        assertEquals(4, demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("CORP", demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(0).getName());
        assertEquals(5, demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(1).getId().intValue());
        assertEquals("OTA", demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(1).getName());
        assertEquals(new BigDecimal("90.25"), demandOverridesByDateAndAccomClass.get(1).getUserRemainingDemand());

        assertEquals(3, demandOverridesByDateAndAccomClass.get(2).getForecastGroupId().intValue());
        assertEquals("Transient Unqualified", demandOverridesByDateAndAccomClass.get(2).getForecastGroupName());
        assertEquals(6, demandOverridesByDateAndAccomClass.get(2).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("RACK", demandOverridesByDateAndAccomClass.get(2).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("80.25"), demandOverridesByDateAndAccomClass.get(2).getUserRemainingDemand());

        assertEquals(4, demandOverridesByDateAndAccomClass.get(3).getForecastGroupId().intValue());
        assertEquals("Transient Qualified", demandOverridesByDateAndAccomClass.get(3).getForecastGroupName());
        assertEquals(3, demandOverridesByDateAndAccomClass.get(3).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("CONS", demandOverridesByDateAndAccomClass.get(3).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("70.25"), demandOverridesByDateAndAccomClass.get(3).getUserRemainingDemand());

        assertEquals(7, demandOverridesByDateAndAccomClass.get(4).getForecastGroupId().intValue());
        assertEquals("wash only", demandOverridesByDateAndAccomClass.get(4).getForecastGroupName());
        assertEquals(2, demandOverridesByDateAndAccomClass.get(4).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("COMP", demandOverridesByDateAndAccomClass.get(4).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("60.25"), demandOverridesByDateAndAccomClass.get(4).getUserRemainingDemand());

        assertEquals(8, demandOverridesByDateAndAccomClass.get(5).getForecastGroupId().intValue());
        assertEquals("Transient Block", demandOverridesByDateAndAccomClass.get(5).getForecastGroupName());
        assertEquals(11, demandOverridesByDateAndAccomClass.get(5).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Intern", demandOverridesByDateAndAccomClass.get(5).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("50.25"), demandOverridesByDateAndAccomClass.get(5).getUserRemainingDemand());

        assertEquals(9, demandOverridesByDateAndAccomClass.get(6).getForecastGroupId().intValue());
        assertEquals("Qualified Semi Yieldable", demandOverridesByDateAndAccomClass.get(6).getForecastGroupName());
        assertEquals(12, demandOverridesByDateAndAccomClass.get(6).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Wholesale", demandOverridesByDateAndAccomClass.get(6).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("40.25"), demandOverridesByDateAndAccomClass.get(6).getUserRemainingDemand());

        assertEquals(10, demandOverridesByDateAndAccomClass.get(7).getForecastGroupId().intValue());
        assertEquals("Group", demandOverridesByDateAndAccomClass.get(7).getForecastGroupName());
        assertEquals(14, demandOverridesByDateAndAccomClass.get(7).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Discount", demandOverridesByDateAndAccomClass.get(7).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("30.25"), demandOverridesByDateAndAccomClass.get(7).getUserRemainingDemand());

    }

    @Test
    public void shouldGetAllActiveBaseProducts() {
        List<Product> allActiveBaseProducts = service.getAllActiveBaseProducts();
        assertEquals(1, allActiveBaseProducts.size());
        assertEquals("BAR", allActiveBaseProducts.get(0).getName());
        CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery("insert into Mkt_Seg_Product_Mapping values('CONS',2,0,0,0,1,getDate(),1,getDate())");
        crudService.executeUpdateByNativeQuery("update product set code = 'INDEPENDENT', Name = 'IP1', Status_Id = 1 where Product_ID=2");
        crudService.executeUpdateByNativeQuery("insert into Product_AT values(2,4,1,getDate(),1,GETDATE())");
        allActiveBaseProducts = service.getAllActiveBaseProducts();
        assertEquals(2, allActiveBaseProducts.size());
        assertEquals("IP1", allActiveBaseProducts.get(1).getName());
    }

    @Test
    public void shouldIgnoreInactiveForecastGroupWhileGettingDemandOverridesByDateAndAccomClass() {
        //GIVEN
        LocalDate today = LocalDate.now();
        Integer accomClassId = 2;
        tenantCrudService().executeUpdateByNativeQuery("insert into Occupancy_Demand_FCST values(312,5,2," + accomClassId + "," +
                "'" + today + "','3.00','0.00','0.16','10.25',getdate(),null,null,1,null)");
        inactivateForecastGroup(9);
        addOccupancyDemandFcst(today, accomClassId, "100.25", "1");
        addOccupancyDemandFcst(today, accomClassId, "90.25", "2");
        addOccupancyDemandFcst(today, accomClassId, "80.25", "3");
        addOccupancyDemandFcst(today, accomClassId, "70.25", "4");
        addOccupancyDemandFcst(today, accomClassId, "60.25", "7");
        addOccupancyDemandFcst(today, accomClassId, "50.25", "8");
        addOccupancyDemandFcst(today, accomClassId, "40.25", "9");
        addOccupancyDemandFcst(today, accomClassId, "30.25", "10");
        //WHEN
        List<ForecastGroupRemainingDemand> demandOverridesByDateAndAccomClass =
                service.getDemandOverridesByDateAndAccomClass(today, 2);
        //THEN
        assertEquals(1, demandOverridesByDateAndAccomClass.get(0).getForecastGroupId().intValue());
        assertEquals("Complementary", demandOverridesByDateAndAccomClass.get(0).getForecastGroupName());
        assertEquals(1, demandOverridesByDateAndAccomClass.get(0).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Barter", demandOverridesByDateAndAccomClass.get(0).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("100.25"), demandOverridesByDateAndAccomClass.get(0).getUserRemainingDemand());

        assertEquals(2, demandOverridesByDateAndAccomClass.get(1).getForecastGroupId().intValue());
        assertEquals("Corporate", demandOverridesByDateAndAccomClass.get(1).getForecastGroupName());
        assertEquals(4, demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("CORP", demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(0).getName());
        assertEquals(5, demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(1).getId().intValue());
        assertEquals("OTA", demandOverridesByDateAndAccomClass.get(1).getAssignedMarketSegments().get(1).getName());
        assertEquals(new BigDecimal("90.25"), demandOverridesByDateAndAccomClass.get(1).getUserRemainingDemand());

        assertEquals(3, demandOverridesByDateAndAccomClass.get(2).getForecastGroupId().intValue());
        assertEquals("Transient Unqualified", demandOverridesByDateAndAccomClass.get(2).getForecastGroupName());
        assertEquals(6, demandOverridesByDateAndAccomClass.get(2).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("RACK", demandOverridesByDateAndAccomClass.get(2).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("80.25"), demandOverridesByDateAndAccomClass.get(2).getUserRemainingDemand());

        assertEquals(4, demandOverridesByDateAndAccomClass.get(3).getForecastGroupId().intValue());
        assertEquals("Transient Qualified", demandOverridesByDateAndAccomClass.get(3).getForecastGroupName());
        assertEquals(3, demandOverridesByDateAndAccomClass.get(3).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("CONS", demandOverridesByDateAndAccomClass.get(3).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("70.25"), demandOverridesByDateAndAccomClass.get(3).getUserRemainingDemand());

        assertEquals(7, demandOverridesByDateAndAccomClass.get(4).getForecastGroupId().intValue());
        assertEquals("wash only", demandOverridesByDateAndAccomClass.get(4).getForecastGroupName());
        assertEquals(2, demandOverridesByDateAndAccomClass.get(4).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("COMP", demandOverridesByDateAndAccomClass.get(4).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("60.25"), demandOverridesByDateAndAccomClass.get(4).getUserRemainingDemand());

        assertEquals(8, demandOverridesByDateAndAccomClass.get(5).getForecastGroupId().intValue());
        assertEquals("Transient Block", demandOverridesByDateAndAccomClass.get(5).getForecastGroupName());
        assertEquals(11, demandOverridesByDateAndAccomClass.get(5).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Intern", demandOverridesByDateAndAccomClass.get(5).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("50.25"), demandOverridesByDateAndAccomClass.get(5).getUserRemainingDemand());

        assertEquals(10, demandOverridesByDateAndAccomClass.get(6).getForecastGroupId().intValue());
        assertEquals("Group", demandOverridesByDateAndAccomClass.get(6).getForecastGroupName());
        assertEquals(14, demandOverridesByDateAndAccomClass.get(6).getAssignedMarketSegments().get(0).getId().intValue());
        assertEquals("Discount", demandOverridesByDateAndAccomClass.get(6).getAssignedMarketSegments().get(0).getName());
        assertEquals(new BigDecimal("30.25"), demandOverridesByDateAndAccomClass.get(6).getUserRemainingDemand());
    }

    private void inactivateForecastGroup(Integer forecastGroupId) {
        tenantCrudService().executeUpdateByNativeQuery("update Forecast_Group set Status_ID = 2 where Forecast_Group_ID = " + forecastGroupId);
    }

    private void addOccupancyDemandFcst(LocalDate today, Integer accomClassId, String remainingDemand, String forecastGroupId) {
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery("update Occupancy_Demand_FCST set User_Remaining_Demand = " + remainingDemand + " " +
                "where Occupancy_DT = '" + today + "' and Accom_Class_ID = " + accomClassId + " and Forecast_Group_ID = " + forecastGroupId);
        if (affectedRows == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Occupancy_Demand_FCST values(1, 5, " + forecastGroupId + ", " + accomClassId + ",'" + today
                    + "', '3.00', '" + remainingDemand + "', '0.26', '" + remainingDemand + "', getdate(), null, null, 1,null)");
        }
    }

    public void createOccupancyOverrideDataForOccupancyDates(List<Date> occupancyDates, Integer forecastGroupID, Integer accomClassID) {
        for (Date date : occupancyDates) {
            OccupancyDemandOverride occupancyDemandOverride = new OccupancyDemandOverride();
            occupancyDemandOverride.setDecisionID(315);
            occupancyDemandOverride.setPropertyID(5);
            occupancyDemandOverride.setForecastGroupId(forecastGroupID == null ? 3 : forecastGroupID);
            occupancyDemandOverride.setAccomClassID(accomClassID == null ? 3 : accomClassID);
            occupancyDemandOverride.setRemainingDemand(BigDecimal.ONE);
            occupancyDemandOverride.setOverrideValue(BigDecimal.TEN);
            occupancyDemandOverride.setStatusId(1);
            occupancyDemandOverride.setRoomsSold(BigDecimal.TEN);
            occupancyDemandOverride.setCreatedByUserId(1);
            occupancyDemandOverride.setLastUpdatedByUserId(1);
            occupancyDemandOverride.setOccupancyDate(date);
            occupancyDemandOverride.setCreateDate(date);
            occupancyDemandOverride.setLastUpdatedDate(date);
            tenantCrudService().save(occupancyDemandOverride);
        }
    }

    public void createArrivalDemandOverrideDataForArrivalDates(List<Date> arrivalDates, Integer forecastGroupID, Integer accomClassID) {
        for (Date date : arrivalDates) {
            ArrivalDemandOverride arrivalDemandOverride = new ArrivalDemandOverride();
            arrivalDemandOverride.setDecisionID(314);
            arrivalDemandOverride.setPropertyID(5);
            arrivalDemandOverride.setForecastGroupId(forecastGroupID == null ? 3 : forecastGroupID);
            arrivalDemandOverride.setAccomClassID(accomClassID == null ? 4 : accomClassID);
            arrivalDemandOverride.setLengthOfStay(4);
            arrivalDemandOverride.setRemainingDemand(BigDecimal.TEN);
            arrivalDemandOverride.setOverrideValue(BigDecimal.TEN);
            arrivalDemandOverride.setRateUnqualified(tenantCrudService().find(RateUnqualified.class, 5));
            arrivalDemandOverride.setStatusId(1);
            arrivalDemandOverride.setCreatedByUserId(1);
            arrivalDemandOverride.setLastUpdatedByUserId(1);
            arrivalDemandOverride.setArrivalDate(date);
            arrivalDemandOverride.setCreateDate(date);
            arrivalDemandOverride.setLastUpdatedDate(date);
            tenantCrudService().save(arrivalDemandOverride);
        }
    }

    public void createWashOverrideDataForOccupancyDates(List<Date> occupancyDates, Integer forecastGroupID) {
        for (Date date : occupancyDates) {
            WashOverride washOverride = new WashOverride();
            washOverride.setDecisionID(320);
            washOverride.setPropertyID(5);
            washOverride.setForecastGroupId(forecastGroupID == null ? 8 : forecastGroupID);
            washOverride.setSystemWash(BigDecimal.TEN);
            washOverride.setOverrideValue(BigDecimal.TEN);
            washOverride.setStatusId(1);
            washOverride.setCreatedByUserId(1);
            washOverride.setLastUpdatedByUserId(1);
            washOverride.setOccupancyDate(date);
            washOverride.setExpirationDate(date);
            washOverride.setCreateDate(date);
            washOverride.setLastUpdatedDate(date);
            tenantCrudService().save(washOverride);
        }
    }
}
