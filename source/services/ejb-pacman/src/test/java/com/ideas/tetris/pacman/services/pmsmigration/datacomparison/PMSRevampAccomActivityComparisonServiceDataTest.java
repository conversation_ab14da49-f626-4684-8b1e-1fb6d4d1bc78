package com.ideas.tetris.pacman.services.pmsmigration.datacomparison;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomActivityCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.accom.AccomActivityDelta;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.accom.PMSAccomTypeMapping;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationBackupService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationMappingService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.pmsmigration.PMSMigrationTestUtil.createPMSMigrationMapping;
import static com.ideas.tetris.pacman.services.pmsmigration.datacomparison.ActivityComparator.scaled;
import static com.ideas.tetris.pacman.services.pmsmigration.datacomparison.PMSRevampActivityComparisonScenarios.SCENARIO_ACCOM_ACTIVITY_COMPARISON;
import static com.ideas.tetris.pacman.services.pmsmigration.datacomparison.PMSRevampActivityComparisonScenarios.SCENARIO_ACCOM_ACTIVITY_COMPARISON_INCONSISTENT_DATA;
import static com.ideas.tetris.pacman.services.pmsmigration.datacomparison.PMSRevampActivityComparisonScenarios.SCENARIO_ACCOM_ACTIVITY_COMPARISON_WITH_NON_DISCONTINUED_ACCOM_TYPES;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.formatDate;
import static java.text.MessageFormat.format;
import static java.util.Objects.nonNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PMSRevampAccomActivityComparisonServiceDataTest extends AbstractG3JupiterTest {

    private PMSMigrationService pmsMigrationService;

    private PMSMigrationBackupService pmsMigrationBackupService;

    private PMSMigrationMappingService pmsMigrationMappingService;

    private AccommodationService accommodationService;

    private static final Date startDate = DateUtil.getDateForNextMonth(1);

    private static final Date endDate = DateUtil.getDateForNextMonth((Integer) SCENARIO_ACCOM_ACTIVITY_COMPARISON[1][SCENARIO_ACCOM_ACTIVITY_COMPARISON[1].length - 1][0]);

    private static final String INSERT_ACCOM_ACTIVITY_BAKUP_INSTANCE_QUERY = "insert into Accom_Activity_Pms_Bkp " +
            "(Property_ID, Occupancy_DT, SnapShot_DTTM, Accom_Type_Id, Accom_Capacity, Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate) " +
            "values " +
            "({0}, ''{1}'', GETDATE(), {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}, {12}, {13}, 1, GETDATE(), GETDATE())";

    private static final Function<Stream<BigDecimal>, BigDecimal> AGGREGATOR_FN = values -> values.reduce(BigDecimal.ZERO, BigDecimal::add);

    @BeforeEach
    public void setUp() {
        pmsMigrationBackupService = new PMSMigrationBackupService();
        inject(pmsMigrationBackupService, "tenantCrudService", tenantCrudService());

        pmsMigrationMappingService = new PMSMigrationMappingService();
        inject(pmsMigrationMappingService, "tenantCrudService", tenantCrudService());

        accommodationService = new AccommodationService();
        inject(accommodationService, "tenantCrudService", tenantCrudService());
        MktSegRecodingService mktSegRecodingService = new MktSegRecodingService();
        pmsMigrationService = new PMSMigrationService();
        inject(pmsMigrationService, "tenantCrudService", tenantCrudService());
        inject(pmsMigrationService, "globalCrudService", globalCrudService());
        inject(mktSegRecodingService, "globalCrudService", globalCrudService());
        inject(pmsMigrationService, "pmsMigrationBackupService", pmsMigrationBackupService);
        inject(pmsMigrationService, "pmsMigrationMappingService", pmsMigrationMappingService);
        inject(pmsMigrationService, "accommodationService", accommodationService);
        inject(pmsMigrationService, "mktSegRecodingService", mktSegRecodingService);


        pmsMigrationBackupService.createBackupTablesQuery("Accom_Type", "Accom_Type_PMS_Bkp", "");
        deleteAccomActivity();
    }

    @Test
    public void shouldInitializeActivityComparisonTables() {
        pmsMigrationService.initializeActivityComparisonTables();
        assertTrue(pmsMigrationBackupService.backedUpTableExists(AccomActivityDelta.TABLE_NAME));
    }

    @Test
    public void shouldMatchAccomActivityDeltaExceedingRowThresholdLevel() {
        createAndSavePMSMigrationConfig();
        createPMSMappingForScenario(SCENARIO_ACCOM_ACTIVITY_COMPARISON[0]);
        createAccomActivityBackupInstance(SCENARIO_ACCOM_ACTIVITY_COMPARISON[1]);
        createAccomActivityAfterBackfill(SCENARIO_ACCOM_ACTIVITY_COMPARISON[2]);
        generateAccomActivityDelta(1, 2);

        List<AccomActivityDelta> accomActivityDeltaWithThreshold = pmsMigrationService.findAccomActivityDeltaSummaryExceedingThreshold(10);
        assertEquals(9, accomActivityDeltaWithThreshold.size());
    }

    @Test
    public void shouldMatchAccomActivityDeltaExceedingOverallThresholdLevel() {
        createAndSavePMSMigrationConfig();
        createPMSMappingForScenario(SCENARIO_ACCOM_ACTIVITY_COMPARISON[0]);
        createAccomActivityBackupInstance(SCENARIO_ACCOM_ACTIVITY_COMPARISON[1]);
        createAccomActivityAfterBackfill(SCENARIO_ACCOM_ACTIVITY_COMPARISON[2]);
        generateAccomActivityDelta(1, 2);

        assertTrue(pmsMigrationService.accomActivityDeltaExceedsOverallThreshold(10, 5));
    }

    @Test
    public void shouldMatchFiguresOfAccomActivityDelta() {
        createAndSavePMSMigrationConfig();
        createPMSMappingForScenario(SCENARIO_ACCOM_ACTIVITY_COMPARISON[0]);
        Map<String, Object[]> accomActivityBackupSnapshot = createAccomActivityBackupInstance(SCENARIO_ACCOM_ACTIVITY_COMPARISON[1]);
        Map<String, AccomActivity> accomActivitySnapshot = createAccomActivityAfterBackfill(SCENARIO_ACCOM_ACTIVITY_COMPARISON[2]);
        generateAccomActivityDelta(1, 2);

        assertAccomActivityDelta(SCENARIO_ACCOM_ACTIVITY_COMPARISON[3], accomActivityBackupSnapshot, accomActivitySnapshot);
    }

    @Test
    public void shouldMatchFiguresOfAccomActivityDeltaForInconsistentData() {
        createAndSavePMSMigrationConfig();
        createPMSMappingForScenario(SCENARIO_ACCOM_ACTIVITY_COMPARISON_INCONSISTENT_DATA[0]);
        Map<String, Object[]> accomActivityBackupSnapshot = createAccomActivityBackupInstance(SCENARIO_ACCOM_ACTIVITY_COMPARISON_INCONSISTENT_DATA[1]);
        Map<String, AccomActivity> accomActivitySnapshot = createAccomActivityAfterBackfill(SCENARIO_ACCOM_ACTIVITY_COMPARISON_INCONSISTENT_DATA[2]);
        generateAccomActivityDelta(2, 6);

        assertAccomActivityDelta(SCENARIO_ACCOM_ACTIVITY_COMPARISON_INCONSISTENT_DATA[3], accomActivityBackupSnapshot, accomActivitySnapshot);
    }

    @Test
    public void shouldMatchFiguresOfAccomActivityDeltaWhenDiscontinuedAccomTypesDoesNotExist() {
        createAndSavePMSMigrationConfig();
        createPMSMappingForScenario(SCENARIO_ACCOM_ACTIVITY_COMPARISON_WITH_NON_DISCONTINUED_ACCOM_TYPES[0]);
        Map<String, Object[]> accomActivityBackupSnapshot = createAccomActivityBackupInstance(SCENARIO_ACCOM_ACTIVITY_COMPARISON_WITH_NON_DISCONTINUED_ACCOM_TYPES[1]);
        Map<String, AccomActivity> accomActivitySnapshot = createAccomActivityAfterBackfill(SCENARIO_ACCOM_ACTIVITY_COMPARISON_WITH_NON_DISCONTINUED_ACCOM_TYPES[2]);
        generateAccomActivityDelta(1, 1);

        assertAccomActivityDelta(SCENARIO_ACCOM_ACTIVITY_COMPARISON_WITH_NON_DISCONTINUED_ACCOM_TYPES[3], accomActivityBackupSnapshot, accomActivitySnapshot);
    }

    private void generateAccomActivityDelta(int start, int end) {
        Date startDate = DateUtil.getDateForNextMonth(start);
        Date endDate = DateUtil.getDateForNextMonth(end);

        List<PMSAccomTypeMapping> pmsAccomTypeMappings = pmsMigrationService.getMappingsAsOldAndNewAccomTypeIDs();

        pmsMigrationService.initializeActivityComparisonTables();
        pmsMigrationService.generateAccomActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate), pmsAccomTypeMappings);
    }

    private void assertAccomActivityDelta(Object[][] expectedAccomActivityDelta, Map<String, Object[]> accomActivityBackupSnapshot, Map<String, AccomActivity> accomActivitySnapshot) {
        List<AccomActivityDelta> accomActivityDeltaList = pmsMigrationService.findAccomActivityDeltaSummaryExceedingThreshold(0);
        Map<String, AccomActivityDelta> actualAccomActivityDeltaByDateAndCode = accomActivityDeltaList.stream().collect(Collectors.toMap(this::accomActivityDeltaKey, Function.identity()));
        assertEquals(expectedAccomActivityDelta.length - 1, actualAccomActivityDeltaByDateAndCode.size());

        for (int i = 1; i < expectedAccomActivityDelta.length; i++) {
            int day = (Integer) expectedAccomActivityDelta[i][0];
            Date occupancyDate = DateUtil.getDateForNextMonth(day);

            AccomActivityDelta accomActivityDelta = actualAccomActivityDeltaByDateAndCode.get(LocalDate.fromDateFields(occupancyDate) + (nonNull(expectedAccomActivityDelta[i][2]) ? (String) expectedAccomActivityDelta[i][2] : (String) expectedAccomActivityDelta[i][1]));
            assertNotNull(accomActivityDelta);
            AccomActivity accomActivity = accomActivitySnapshot.get(LocalDate.fromDateFields(occupancyDate) + (String) expectedAccomActivityDelta[i][2]);

            if (nonNull(expectedAccomActivityDelta[i][1]) && nonNull(accomActivity)) {
                String[] oldAccomTypes = ((String) expectedAccomActivityDelta[i][1]).split(",");
                List<Object[]> accomActivityBackup = Arrays.stream(oldAccomTypes).map(oldAccomTypeCode -> accomActivityBackupSnapshot.get(LocalDate.fromDateFields(occupancyDate) + oldAccomTypeCode)).collect(Collectors.toList());
                assertOldAndNewAccomActivityDeltaValues(expectedAccomActivityDelta[i], accomActivityDelta, accomActivity, accomActivityBackup);
            } else if (nonNull(expectedAccomActivityDelta[i][1])) {
                String[] oldAccomTypes = ((String) expectedAccomActivityDelta[i][1]).split(",");
                List<Object[]> accomActivityBackup = Arrays.stream(oldAccomTypes).map(oldAccomTypeCode -> accomActivityBackupSnapshot.get(LocalDate.fromDateFields(occupancyDate) + oldAccomTypeCode)).collect(Collectors.toList());
                assertOldAccomActivityDeltaValues(accomActivityDelta, accomActivityBackup);
            } else if (nonNull(accomActivity)) {
                assertNewAccomActivityDeltaValues(accomActivityDelta, accomActivity);
            }
        }
    }

    private void assertOldAndNewAccomActivityDeltaValues(Object[] expectedAccomActivityDelta, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity, List<Object[]> accomActivityBackup) {
        String errorMessage = String.format("Failed for date %s, old accom types %s and new accom types %s", accomActivityDelta.getOccupancyDate(), accomActivityDelta.getOldAccomTypeCodes(), accomActivityDelta.getNewAccomTypeCode());
        assertAccomCapacity(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 1), accomActivity.getAccomCapacity(), expectedAccomActivityDelta);
        assertRoomsSold(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 2), accomActivity.getRoomsSold(), expectedAccomActivityDelta);
        assertRoomsNotAvailableMaintenance(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 3), accomActivity.getRoomsNotAvailableMaintenance(), expectedAccomActivityDelta);
        assertRoomsNotAvailableOther(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 4), accomActivity.getRoomsNotAvailableOther(), expectedAccomActivityDelta);
        assertArrivals(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 5), accomActivity.getArrivals(), expectedAccomActivityDelta);
        assertDepartures(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 6), accomActivity.getDepartures(), expectedAccomActivityDelta);
        assertCancellations(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 7), accomActivity.getCancellations(), expectedAccomActivityDelta);
        assertNoShows(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 8), accomActivity.getNoShows(), expectedAccomActivityDelta);
        assertRoomRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 9), accomActivity.getRoomRevenue(), expectedAccomActivityDelta);
        assertFoodRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 10), accomActivity.getFoodRevenue(), expectedAccomActivityDelta);
        assertTotalRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 11), accomActivity.getTotalRevenue(), expectedAccomActivityDelta);
    }

    private void assertNewAccomActivityDeltaValues(AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        String errorMessage = String.format("Failed for date %s and new accom type %s", accomActivityDelta.getOccupancyDate(), accomActivityDelta.getNewAccomTypeCode());
        assertNewAccomCapacity(errorMessage, accomActivityDelta, accomActivity);
        assertNewRoomsSold(errorMessage, accomActivityDelta, accomActivity);
        assertNewRoomsNotAvailableMaintenance(errorMessage, accomActivityDelta, accomActivity);
        assertNewRoomsNotAvailableOther(errorMessage, accomActivityDelta, accomActivity);
        assertNewArrivals(errorMessage, accomActivityDelta, accomActivity);
        assertNewDepartures(errorMessage, accomActivityDelta, accomActivity);
        assertNewCancellations(errorMessage, accomActivityDelta, accomActivity);
        assertNewNoShows(errorMessage, accomActivityDelta, accomActivity);
        assertNewRoomRevenue(errorMessage, accomActivityDelta, accomActivity);
        assertNewFoodRevenue(errorMessage, accomActivityDelta, accomActivity);
        assertNewTotalRevenue(errorMessage, accomActivityDelta, accomActivity);
    }

    private void assertOldAccomActivityDeltaValues(AccomActivityDelta accomActivityDelta, List<Object[]> accomActivityBackup) {
        String errorMessage = String.format("Failed for date %s and old accom types %s", accomActivityDelta.getOccupancyDate(), accomActivityDelta.getOldAccomTypeCodes());
        assertOldAccomCapacity(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 1));
        assertOldRoomsSold(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 2));
        assertOldRoomsNotAvailableMaintenance(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 3));
        assertOldRoomsNotAvailableOther(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 4));
        assertOldArrivals(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 5));
        assertOldDepartures(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 6));
        assertOldCancellations(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 7));
        assertOldNoShows(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 8));
        assertOldRoomRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 9));
        assertOldFoodRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 10));
        assertOldTotalRevenue(errorMessage, accomActivityDelta, getAggregatedValue(accomActivityBackup, 11));
    }

    private String accomActivityDeltaKey(AccomActivityDelta aad) {
        return LocalDate.fromDateFields(aad.getOccupancyDate()) + (nonNull(aad.getNewAccomTypeCode()) ? aad.getNewAccomTypeCode() : aad.getOldAccomTypeCodes());
    }

    private void assertTotalRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldTotalRevenue, BigDecimal newTotalRevenue, Object[] expectedAccomActivityDelta) {
        BigDecimal accomRevenueDelta = scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[23]));
        BigDecimal accomRevenueDeltaPercent = expectedAccomActivityDelta[24] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[24])) : null;
        assertEquals(oldTotalRevenue, accomActivityDelta.getOldTotalRevenue(), errorMessage);
        assertEquals(newTotalRevenue, accomActivityDelta.getNewTotalRevenue(), errorMessage);
        assertEquals(accomRevenueDelta, accomActivityDelta.getTotalRevenueDelta(), errorMessage);
        assertEquals(accomRevenueDeltaPercent, accomActivityDelta.getTotalRevenueDeltaPercent(), errorMessage);
    }

    private void assertNewTotalRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldTotalRevenue(), errorMessage);
        assertEquals(accomActivity.getTotalRevenue(), accomActivityDelta.getNewTotalRevenue(), errorMessage);
        assertNull(accomActivityDelta.getTotalRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getTotalRevenueDeltaPercent(), errorMessage);
    }

    private void assertOldTotalRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldTotalRevenue(), errorMessage);
        assertNull(accomActivityDelta.getNewTotalRevenue(), errorMessage);
        assertNull(accomActivityDelta.getTotalRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getTotalRevenueDeltaPercent(), errorMessage);
    }

    private void assertFoodRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldFoodRevenue, BigDecimal newFoodRevenue, Object[] expectedAccomActivityDelta) {
        BigDecimal foodRevenueDelta = scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[21]));
        BigDecimal foodRevenueDeltaPercent = expectedAccomActivityDelta[22] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[22])) : null;
        assertEquals(oldFoodRevenue, accomActivityDelta.getOldFoodRevenue(), errorMessage);
        assertEquals(newFoodRevenue, accomActivityDelta.getNewFoodRevenue(), errorMessage);
        assertEquals(foodRevenueDelta, accomActivityDelta.getFoodRevenueDelta(), errorMessage);
        assertEquals(foodRevenueDeltaPercent, accomActivityDelta.getFoodRevenueDeltaPercent(), errorMessage);
    }

    private void assertNewFoodRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldFoodRevenue(), errorMessage);
        assertEquals(accomActivity.getFoodRevenue(), accomActivityDelta.getNewFoodRevenue(), errorMessage);
        assertNull(accomActivityDelta.getFoodRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getFoodRevenueDeltaPercent(), errorMessage);
    }

    private void assertOldFoodRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldFoodRevenue(), errorMessage);
        assertNull(accomActivityDelta.getNewFoodRevenue(), errorMessage);
        assertNull(accomActivityDelta.getFoodRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getFoodRevenueDeltaPercent(), errorMessage);
    }

    private void assertRoomRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldRoomRevenue, BigDecimal newRoomRevenue, Object[] expectedAccomActivityDelta) {
        BigDecimal roomRevenueDelta = scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[19]));
        BigDecimal roomRevenueDeltaPercent = expectedAccomActivityDelta[20] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[20])) : null;
        assertEquals(oldRoomRevenue, accomActivityDelta.getOldRoomRevenue(), errorMessage);
        assertEquals(newRoomRevenue, accomActivityDelta.getNewRoomRevenue(), errorMessage);
        assertEquals(roomRevenueDelta, accomActivityDelta.getRoomRevenueDelta(), errorMessage);
        assertEquals(roomRevenueDeltaPercent, accomActivityDelta.getRoomRevenueDeltaPercent(), errorMessage);
    }

    private void assertNewRoomRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldRoomRevenue(), errorMessage);
        assertEquals(accomActivity.getRoomRevenue(), accomActivityDelta.getNewRoomRevenue(), errorMessage);
        assertNull(accomActivityDelta.getRoomRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomRevenueDeltaPercent(), errorMessage);
    }

    private void assertOldRoomRevenue(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldRoomRevenue(), errorMessage);
        assertNull(accomActivityDelta.getNewRoomRevenue(), errorMessage);
        assertNull(accomActivityDelta.getRoomRevenueDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomRevenueDeltaPercent(), errorMessage);
    }

    private void assertNoShows(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldNoShows, BigDecimal newNoShows, Object[] expectedAccomActivityDelta) {
        BigDecimal noShowsDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[17]);
        BigDecimal noShowsDeltaPercent = expectedAccomActivityDelta[18] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[18])) : null;
        assertEquals(oldNoShows, accomActivityDelta.getOldNoShows(), errorMessage);
        assertEquals(newNoShows, accomActivityDelta.getNewNoShows(), errorMessage);
        assertEquals(noShowsDelta, accomActivityDelta.getNoShowsDelta(), errorMessage);
        assertEquals(noShowsDeltaPercent, accomActivityDelta.getNoShowsDeltaPercent(), errorMessage);
    }

    private void assertNewNoShows(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldNoShows(), errorMessage);
        assertEquals(accomActivity.getNoShows(), accomActivityDelta.getNewNoShows(), errorMessage);
        assertNull(accomActivityDelta.getNoShowsDelta(), errorMessage);
        assertNull(accomActivityDelta.getNoShowsDeltaPercent(), errorMessage);
    }

    private void assertOldNoShows(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldNoShows(), errorMessage);
        assertNull(accomActivityDelta.getNewNoShows(), errorMessage);
        assertNull(accomActivityDelta.getNoShowsDelta(), errorMessage);
        assertNull(accomActivityDelta.getNoShowsDeltaPercent(), errorMessage);
    }

    private void assertCancellations(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldCancellations, BigDecimal newCancellations, Object[] expectedAccomActivityDelta) {
        BigDecimal cancellationsDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[15]);
        BigDecimal cancellationsDeltaPercent = expectedAccomActivityDelta[16] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[16])) : null;
        assertEquals(oldCancellations, accomActivityDelta.getOldCancellations(), errorMessage);
        assertEquals(newCancellations, accomActivityDelta.getNewCancellations(), errorMessage);
        assertEquals(cancellationsDelta, accomActivityDelta.getCancellationsDelta(), errorMessage);
        assertEquals(cancellationsDeltaPercent, accomActivityDelta.getCancellationsDeltaPercent(), errorMessage);
    }

    private void assertNewCancellations(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldCancellations(), errorMessage);
        assertEquals(accomActivity.getCancellations(), accomActivityDelta.getNewCancellations(), errorMessage);
        assertNull(accomActivityDelta.getCancellationsDelta(), errorMessage);
        assertNull(accomActivityDelta.getCancellationsDeltaPercent(), errorMessage);
    }

    private void assertOldCancellations(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldCancellations(), errorMessage);
        assertNull(accomActivityDelta.getNewCancellations(), errorMessage);
        assertNull(accomActivityDelta.getCancellationsDelta(), errorMessage);
        assertNull(accomActivityDelta.getCancellationsDeltaPercent(), errorMessage);
    }

    private void assertDepartures(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldDepartures, BigDecimal newDepartures, Object[] expectedAccomActivityDelta) {
        BigDecimal departuresDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[13]);
        BigDecimal departuresDeltaPercent = expectedAccomActivityDelta[14] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[14])) : null;
        assertEquals(oldDepartures, accomActivityDelta.getOldDepartures(), errorMessage);
        assertEquals(newDepartures, accomActivityDelta.getNewDepartures(), errorMessage);
        assertEquals(departuresDelta, accomActivityDelta.getDeparturesDelta(), errorMessage);
        assertEquals(departuresDeltaPercent, accomActivityDelta.getDeparturesDeltaPercent(), errorMessage);
    }

    private void assertNewDepartures(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldDepartures(), errorMessage);
        assertEquals(accomActivity.getDepartures(), accomActivityDelta.getNewDepartures(), errorMessage);
        assertNull(accomActivityDelta.getDeparturesDelta(), errorMessage);
        assertNull(accomActivityDelta.getDeparturesDeltaPercent(), errorMessage);
    }

    private void assertOldDepartures(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldDepartures(), errorMessage);
        assertNull(accomActivityDelta.getNewDepartures(), errorMessage);
        assertNull(accomActivityDelta.getDeparturesDelta(), errorMessage);
        assertNull(accomActivityDelta.getDeparturesDeltaPercent(), errorMessage);
    }

    private void assertArrivals(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldArrivals, BigDecimal newArrivals, Object[] expectedAccomActivityDelta) {
        BigDecimal arrivalsDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[11]);
        BigDecimal arrivalsDeltaPercent = expectedAccomActivityDelta[12] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[12])) : null;
        assertEquals(oldArrivals, accomActivityDelta.getOldArrivals(), errorMessage);
        assertEquals(newArrivals, accomActivityDelta.getNewArrivals(), errorMessage);
        assertEquals(arrivalsDelta, accomActivityDelta.getArrivalsDelta(), errorMessage);
        assertEquals(arrivalsDeltaPercent, accomActivityDelta.getArrivalsDeltaPercent(), errorMessage);
    }

    private void assertNewArrivals(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldArrivals(), errorMessage);
        assertEquals(accomActivity.getArrivals(), accomActivityDelta.getNewArrivals(), errorMessage);
        assertNull(accomActivityDelta.getArrivalsDelta(), errorMessage);
        assertNull(accomActivityDelta.getArrivalsDeltaPercent(), errorMessage);
    }

    private void assertOldArrivals(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldArrivals(), errorMessage);
        assertNull(accomActivityDelta.getNewArrivals(), errorMessage);
        assertNull(accomActivityDelta.getArrivalsDelta(), errorMessage);
        assertNull(accomActivityDelta.getArrivalsDeltaPercent(), errorMessage);
    }

    private void assertRoomsNotAvailableOther(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldRoomsNotAvailableOther, BigDecimal newRoomsNotAvailableOther, Object[] expectedAccomActivityDelta) {
        BigDecimal roomsNotAvailOtherDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[9]);
        BigDecimal roomsNotAvailOtherDeltaPercent = expectedAccomActivityDelta[10] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[10])) : null;
        assertEquals(oldRoomsNotAvailableOther, accomActivityDelta.getOldRoomsNotAvailableOther(), errorMessage);
        assertEquals(newRoomsNotAvailableOther, accomActivityDelta.getNewRoomsNotAvailableOther(), errorMessage);
        assertEquals(roomsNotAvailOtherDelta, accomActivityDelta.getRoomsNotAvailableOtherDelta(), errorMessage);
        assertEquals(roomsNotAvailOtherDeltaPercent, accomActivityDelta.getRoomsNotAvailableOtherDeltaPercent(), errorMessage);
    }

    private void assertNewRoomsNotAvailableOther(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldRoomsNotAvailableOther(), errorMessage);
        assertEquals(accomActivity.getRoomsNotAvailableOther(), accomActivityDelta.getNewRoomsNotAvailableOther(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableOtherDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableOtherDeltaPercent(), errorMessage);
    }

    private void assertOldRoomsNotAvailableOther(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldRoomsNotAvailableOther(), errorMessage);
        assertNull(accomActivityDelta.getNewRoomsNotAvailableOther(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableOtherDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableOtherDeltaPercent(), errorMessage);
    }

    private void assertRoomsNotAvailableMaintenance(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldRoomsNotAvailableMaintenance, BigDecimal newRoomsNotAvailableMaintenance, Object[] expectedAccomActivityDelta) {
        BigDecimal roomsNotAvailMaintDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[7]);
        BigDecimal roomsNotAvailMaintDeltaPercent = expectedAccomActivityDelta[8] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[8])) : null;
        assertEquals(oldRoomsNotAvailableMaintenance, accomActivityDelta.getOldRoomsNotAvailableMaintenance(), errorMessage);
        assertEquals(newRoomsNotAvailableMaintenance, accomActivityDelta.getNewRoomsNotAvailableMaintenance(), errorMessage);
        assertEquals(roomsNotAvailMaintDelta, accomActivityDelta.getRoomsNotAvailableMaintenanceDelta(), errorMessage);
        assertEquals(roomsNotAvailMaintDeltaPercent, accomActivityDelta.getRoomsNotAvailableMaintenanceDeltaPercent(), errorMessage);
    }

    private void assertNewRoomsNotAvailableMaintenance(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldRoomsNotAvailableMaintenance(), errorMessage);
        assertEquals(accomActivity.getRoomsNotAvailableMaintenance(), accomActivityDelta.getNewRoomsNotAvailableMaintenance(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableMaintenanceDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableMaintenanceDeltaPercent(), errorMessage);
    }

    private void assertOldRoomsNotAvailableMaintenance(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldRoomsNotAvailableMaintenance(), errorMessage);
        assertNull(accomActivityDelta.getNewRoomsNotAvailableMaintenance(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableMaintenanceDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsNotAvailableMaintenanceDeltaPercent(), errorMessage);
    }

    private void assertRoomsSold(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldRoomsSold, BigDecimal newRoomsSold, Object[] expectedAccomActivityDelta) {
        BigDecimal roomsSoldDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[5]);
        BigDecimal roomsSoldDeltaPercent = expectedAccomActivityDelta[6] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[6])) : null;
        assertEquals(oldRoomsSold, accomActivityDelta.getOldRoomsSold(), errorMessage);
        assertEquals(newRoomsSold, accomActivityDelta.getNewRoomsSold(), errorMessage);
        assertEquals(roomsSoldDelta, accomActivityDelta.getRoomsSoldDelta(), errorMessage);
        assertEquals(roomsSoldDeltaPercent, accomActivityDelta.getRoomsSoldDeltaPercent(), errorMessage);
    }

    private void assertNewRoomsSold(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldRoomsSold(), errorMessage);
        assertEquals(accomActivity.getRoomsSold(), accomActivityDelta.getNewRoomsSold(), errorMessage);
        assertNull(accomActivityDelta.getRoomsSoldDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsSoldDeltaPercent(), errorMessage);
    }

    private void assertOldRoomsSold(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldRoomsSold(), errorMessage);
        assertNull(accomActivityDelta.getNewRoomsSold(), errorMessage);
        assertNull(accomActivityDelta.getRoomsSoldDelta(), errorMessage);
        assertNull(accomActivityDelta.getRoomsSoldDeltaPercent(), errorMessage);
    }

    private void assertAccomCapacity(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal oldCapacity, BigDecimal newAccomCapacity, Object[] expectedAccomActivityDelta) {
        BigDecimal accomCapacityDelta = BigDecimal.valueOf((Integer) expectedAccomActivityDelta[3]);
        BigDecimal accomAccomCapacityDeltaPercent = expectedAccomActivityDelta[4] != null ? scaled(BigDecimal.valueOf((Double) expectedAccomActivityDelta[4])) : null;
        assertEquals(oldCapacity, accomActivityDelta.getOldAccomCapacity(), errorMessage);
        assertEquals(newAccomCapacity, accomActivityDelta.getNewAccomCapacity(), errorMessage);
        assertEquals(accomCapacityDelta, accomActivityDelta.getAccomCapacityDelta(), errorMessage);
        assertEquals(accomAccomCapacityDeltaPercent, accomActivityDelta.getAccomCapacityDeltaPercent(), errorMessage);
    }

    private void assertNewAccomCapacity(String errorMessage, AccomActivityDelta accomActivityDelta, AccomActivity accomActivity) {
        assertNull(accomActivityDelta.getOldAccomCapacity(), errorMessage);
        assertEquals(accomActivity.getAccomCapacity(), accomActivityDelta.getNewAccomCapacity(), errorMessage);
        assertNull(accomActivityDelta.getAccomCapacityDelta(), errorMessage);
        assertNull(accomActivityDelta.getAccomCapacityDeltaPercent(), errorMessage);
    }

    private void assertOldAccomCapacity(String errorMessage, AccomActivityDelta accomActivityDelta, BigDecimal aggregatedValue) {
        assertEquals(aggregatedValue, accomActivityDelta.getOldAccomCapacity(), errorMessage);
        assertNull(accomActivityDelta.getNewAccomCapacity(), errorMessage);
        assertNull(accomActivityDelta.getAccomCapacityDelta(), errorMessage);
        assertNull(accomActivityDelta.getAccomCapacityDeltaPercent(), errorMessage);
    }

    private BigDecimal getAggregatedValue(List<Object[]> accomActivityBackup, Integer index) {
        return AGGREGATOR_FN.apply(accomActivityBackup.stream().map(b -> (BigDecimal) b[index]));
    }

    private Map<String, AccomActivity> createAccomActivityAfterBackfill(Object[][] accomActivitySnapshot) {
        Map<String, AccomActivity> accomActivityByDate = new HashMap<>();
        Map<String, Integer> accomTypeCodeToIdMap = getAccomTypeIdToCodeMap("accom_type");
        for (int i = 1; i < accomActivitySnapshot.length; i++) {
            int day = (Integer) accomActivitySnapshot[i][0];
            Date occupancyDate = DateUtil.getDateForNextMonth(day);
            Integer accomTypeId = accomTypeCodeToIdMap.get((String) accomActivitySnapshot[i][1]);
            BigDecimal accomCapacity = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][2]);
            BigDecimal roomsSold = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][3]);
            BigDecimal roomsNotAvailMaint = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][4]);
            BigDecimal roomsNotAvailOther = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][5]);
            BigDecimal arrivals = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][6]);
            BigDecimal departures = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][7]);
            BigDecimal cancellations = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][8]);
            BigDecimal noShows = BigDecimal.valueOf((Integer) accomActivitySnapshot[i][9]);
            BigDecimal roomRevenue = scaled(BigDecimal.valueOf((Double) accomActivitySnapshot[i][10]));
            BigDecimal foodRevenue = scaled(BigDecimal.valueOf((Double) accomActivitySnapshot[i][11]));
            BigDecimal accomRevenue = scaled(BigDecimal.valueOf((Double) accomActivitySnapshot[i][12]));

            AccomActivity accomActivity = UniqueAccomActivityCreator.createUniqueAccomActivityFor(TestProperty.H1.getId(), accomTypeId, occupancyDate, accomCapacity, roomsSold, roomsNotAvailMaint, roomsNotAvailOther, arrivals, departures, cancellations, noShows, roomRevenue, foodRevenue, accomRevenue);
            accomActivityByDate.put(LocalDate.fromDateFields(occupancyDate) + (String) accomActivitySnapshot[i][1], accomActivity);
        }

        return accomActivityByDate;
    }

    private Map<String, Object[]> createAccomActivityBackupInstance(Object[][] accomActivityBackupSnapshot) {
        tenantCrudService().executeUpdateByNativeQuery("SELECT top 1 * INTO Accom_Activity_Pms_Bkp FROM Accom_Activity");
        Map<String, Integer> accomTypeCodeToIdMap = getAccomTypeIdToCodeMap("accom_type_pms_bkp");
        deleteAccomActivityBackup();

        Map<String, Object[]> accomActivityBackupByDate = new HashMap<>();
        for (int i = 1; i < accomActivityBackupSnapshot.length; i++) {
            int day = (Integer) accomActivityBackupSnapshot[i][0];
            Date occupancyDate = DateUtil.getDateForNextMonth(day);
            Integer accomTypeId = accomTypeCodeToIdMap.get((String) accomActivityBackupSnapshot[i][1]);
            BigDecimal accomCapacity = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][2]);
            BigDecimal roomsSold = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][3]);
            BigDecimal roomsNotAvailMaint = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][4]);
            BigDecimal roomsNotAvailOther = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][5]);
            BigDecimal arrivals = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][6]);
            BigDecimal departures = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][7]);
            BigDecimal cancellations = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][8]);
            BigDecimal noShows = BigDecimal.valueOf((Integer) accomActivityBackupSnapshot[i][9]);
            BigDecimal roomRevenue = scaled(BigDecimal.valueOf((Double) accomActivityBackupSnapshot[i][10]));
            BigDecimal foodRevenue = scaled(BigDecimal.valueOf((Double) accomActivityBackupSnapshot[i][11]));
            BigDecimal accomRevenue = scaled(BigDecimal.valueOf((Double) accomActivityBackupSnapshot[i][12]));

            String insertQuery = format(
                    INSERT_ACCOM_ACTIVITY_BAKUP_INSTANCE_QUERY,
                    TestProperty.H1.getId(),
                    formatDate(occupancyDate, DEFAULT_DATE_FORMAT), accomTypeId,
                    accomCapacity.toPlainString(), roomsSold.toPlainString(),
                    roomsNotAvailMaint.toPlainString(), roomsNotAvailOther.toPlainString(),
                    arrivals.toPlainString(), departures.toPlainString(), cancellations.toPlainString(), noShows.toPlainString(),
                    roomRevenue.toPlainString(), foodRevenue.toPlainString(), accomRevenue.toPlainString()
            );

            tenantCrudService().executeUpdateByNativeQuery(insertQuery);
            accomActivityBackupByDate.put(LocalDate.fromDateFields(occupancyDate) + (String) accomActivityBackupSnapshot[i][1], new Object[]{accomTypeId, accomCapacity, roomsSold, roomsNotAvailMaint, roomsNotAvailOther, arrivals, departures, cancellations, noShows, roomRevenue, foodRevenue, accomRevenue});
        }

        return accomActivityBackupByDate;
    }

    private Map<String, Integer> getAccomTypeIdToCodeMap(String tableName) {
        List<Object[]> accomTypes = tenantCrudService().findByNativeQuery(String.format("select accom_type_id, accom_type_code from %s where status_id = 1 and system_default = 0", tableName));
        Map<String, Integer> idToCodeMap = accomTypes.stream().collect(Collectors.toMap(o -> (String) o[1], o -> (Integer) o[0]));
        return idToCodeMap;
    }

    private void deleteAccomActivityBackup() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Accom_Activity_Pms_Bkp");
    }

    private void deleteAccomActivity() {
        tenantCrudService().deleteAll(AccomActivity.class);
    }

    private void createPMSMappingForScenario(Object[][] pmsMigrationMappings) {
        Map<String, AccomType> accomTypeMappings = new HashMap<>();
        Set<String> oldAccomTypes = new HashSet<>();
        for (int i = 1; i < pmsMigrationMappings.length; i++) {
            String currentAccomTypeCode = (String) pmsMigrationMappings[i][0];
            oldAccomTypes.add(currentAccomTypeCode);
            String newAccomTypeCode = (String) pmsMigrationMappings[i][1];
            Boolean accomTypeDiscontinued = (Boolean) pmsMigrationMappings[i][2];
            Boolean primary = (Boolean) pmsMigrationMappings[i][3];

            if (nonNull(newAccomTypeCode) && !accomTypeMappings.containsKey(newAccomTypeCode)) {
                accomTypeMappings.put(newAccomTypeCode, saveAccomType(newAccomTypeCode, 1));
            }
            savePMSMigrationMapping(PMSMigrationMappingType.ROOM_TYPE, currentAccomTypeCode, newAccomTypeCode, accomTypeDiscontinued, primary);
        }
        oldAccomTypes.stream().forEach(s -> saveOldAccomType(s));
    }

    private void saveOldAccomType(String accomTypeCode) {
        tenantCrudService().executeUpdateByNativeQuery(String.format("insert into Accom_Type_PMS_Bkp " +
                        "([Accom_Type_Code],[Accom_Type_Capacity],[Accom_Class_ID],[System_Default],[Status_ID],[Property_ID],[Last_Updated_DTTM],[Roh_Type],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[isComponentRoom],[Display_Status_ID],[Excluded_From_Soldout] )" +
                        " values('%s',250,4,0,1,5,getdate(),0,1,getdate(),1,'N',1,0)",
                accomTypeCode));
        tenantCrudService().flushAndClear();
    }

    private AccomType saveAccomType(String accomTypeCode, Integer statusId) {
        AccomType accomType = createAccomType(accomTypeCode, statusId);
        tenantCrudService().save(accomType);
        return accomType;
    }

    private AccomType createAccomType(String accomTypeCode, Integer statusId) {
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 4);
        AccomType accomType = new AccomType();
        accomType.setPropertyId(TestProperty.H1.getId());
        accomType.setName(accomTypeCode);
        accomType.setAccomTypeCode(accomTypeCode);
        accomType.setAccomTypeCapacity(250);
        accomType.setAccomClass(accomClass);
        accomType.setStatusId(statusId);
        accomType.setSystemDefault(0);
        return accomType;
    }

    private void savePMSMigrationMapping(PMSMigrationMappingType mappingType, String currentCode, String newCode, Boolean discontinued, Boolean primary) {
        tenantCrudService().save(createPMSMigrationMapping(currentCode, newCode, mappingType, discontinued, primary));
        tenantCrudService().flushAndClear();
    }

    private void createAndSavePMSMigrationConfig() {
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setMigrationState(PMSMigrationState.PMS_COMMIT_FG_DONE);
        pmsMigrationConfig.setClient(globalCrudService().find(Client.class, 2));
        pmsMigrationConfig.setProperty(globalCrudService().find(Property.class, 5));
        pmsMigrationConfig.setOldSystem("OPERA");
        pmsMigrationConfig.setNewSystem("OXI");
        pmsMigrationConfig.setUseGenericTemplateForMigration(false);
        pmsMigrationConfig.setRoomTypeChanged(true);
        pmsMigrationConfig.setOldSysDate(DateUtil.getDate(5, 4, 2018));
        pmsMigrationConfig.setNewSysDate(DateUtil.getDate(10, 4, 2018));
        globalCrudService().save(pmsMigrationConfig);
    }
}
