package com.ideas.tetris.pacman.services.centralrms.services.alerts;

import com.ideas.g3.data.TestProperty;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Created by idnsrw on 14-09-2022.
 */
@MockitoSettings(strictness = Strictness.LENIENT)
public class PriceAtCeilingFloorAlertDataTest extends AbstractCentralRMSAlertDataTest {

    private int isRolling = 0;
    private String OCC_DATE = "";
    private String S1_ST_DT = "";
    private String S1_EN_DT = "";

    @BeforeEach
    public void setUp() {
        super.setUp();
        setWorkContextProperty(TestProperty.H1);
        setDefaultCP_CFG_BAST_AT_Data();
        clearCentralRMSPriceData();
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRAndMinAdjCompPriceConditionsAreTrue_TC1() {
     /* EXAMPLE:Following conditions are evaluated for Ceiling
        Condition1	Final BAR	>=	Ceiling
                    250	        >=	250
        Condition2	Hist_BAR_ADR_Lower	>	Ceiling
                    251	                >	250
        Condition3	Min_Adj_Comp_Price	>	Ceiling
                    255	                >	250
        Expected
        Evaluation=	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                        TRUE		      	 TRUE		           TRUE */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "351", "351", "352", "351", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "356", "352", "354", "356", "357", "355"},
                {"STE", "4", OCC_DATE, "362", "361", "353", "356", "361", "368", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "351.00", "352.00", "351.00", "352.00", "1", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "356.00", "358.00", "356.00", "357.00", "1", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "360.00", "150.00", "360.00", "361.00", "362.00", "361.00", "368.00", "1", "1", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsTrueMinAdjCompPriceConditionIsFalse_TC2() {
    /*   EXAMPLE:Condition1	 FinalBAR	>=	Ceiling
                          250	>=	250
        Condition2	Hist_BAR_ADR_Lower	>	Ceiling
                                  251	>	250
        Condition3	Min_Adj_Comp_Price	>	Ceiling
                                  240	>	250
        Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                          TRUE			   TRUE		         FALSE */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "351", "351", "352", "341", "342", "350"},
                {"DLX", "3", OCC_DATE, "358", "356", "352", "354", "346", "347", "355"},
                {"STE", "4", OCC_DATE, "362", "361", "353", "356", "348", "348", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "351.00", "352.00", "341.00", "342.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "356.00", "358.00", "346.00", "347.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "360.00", "150.00", "360.00", "361.00", "362.00", "348.00", "348.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsFalseMinAdjCompPriceConditionIsTrue_TC3() {
        /*
        Condition1	Final BAR	>=	Ceiling
		                  250	>=	250

	    Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                          249	>	250
	    Condition3	Min_Adj_Comp_Price	>	Ceiling
		                         251	>	250
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
	                    TRUE			     FALSE		       TRUE
         */

        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "342", "341", "351", "352", "351", "352", "350"},
                {"DLX", "3", OCC_DATE, "347", "346", "352", "354", "356", "357", "355"},
                {"STE", "4", OCC_DATE, "348", "348", "353", "356", "361", "362", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "341.00", "342.00", "351.00", "352.00", "0", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "346.00", "347.00", "356.00", "357.00", "0", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "360.00", "150.00", "360.00", "348.00", "348.00", "361.00", "362.00", "0", "1", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsFalseMinAdjCompPriceConditionIsFalse_TC4() {
        /*
         Condition1	Final BAR	>=	Ceiling
		                 250	>=	250
	     Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                          249	>	250
	     Condition3	Min_Adj_Comp_Price	>	Ceiling
		                          249	>	250
         Expected
         Condition1	AND	[	Condition2  	OR	Condition3	]	=	FALSE	Ceiling Count not increased
	          TRUE			  FALSE		          FALSE
         */

        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "342", "341", "351", "352", "341", "342", "350"},
                {"DLX", "3", OCC_DATE, "347", "346", "352", "354", "346", "347", "355"},
                {"STE", "4", OCC_DATE, "348", "348", "353", "356", "348", "348", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertEquals(0, alertData.size());  //No records should be returned since evaluation condition fails
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsTrueMinAdjCompPriceConditionIsTrue_TC5() {
        /* This TestCase has DOW value different from the rest DOW
        Condition1	Final BAR	>=	Ceiling
		                  251	>=	250
	    Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                          251	>	250
	    Condition3	Min_Adj_Comp_Price	>	Ceiling
		                          255	>	250
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
	                     TRUE			TRUE		TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "400.00", "None"},
                {"DLX", "4", OCC_DATE, "455.00", "None"},
                {"STE", "5", OCC_DATE, "460.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "401", "351", "352", "401", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "456", "352", "354", "456", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "461", "353", "356", "461", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        //update DOW values
        update_CP_CFG_BASE_AT_Data_By_DOW(S1_ST_DT, OCC_DATE, "6", "180", "400");
        update_CP_CFG_BASE_AT_Data_By_DOW(S1_ST_DT, OCC_DATE, "4", "180", "455");
        update_CP_CFG_BASE_AT_Data_By_DOW(S1_ST_DT, OCC_DATE, "5", "180", "460");
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "400.00", "180.00", "400.00", "401.00", "452.00", "401.00", "452.00", "1", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "455.00", "180.00", "455.00", "456.00", "458.00", "456.00", "458.00", "1", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "460.00", "180.00", "460.00", "461.00", "462.00", "461.00", "462.00", "1", "1", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsNotEvaluatedMinAdjCompPriceConditionIsTrue_TC6() {
       /*
        Conditions	Condition1	Final BAR	>=	Ceiling
                                      251	>=	250
        Condition2	     Hist_BAR_ADR_Lower	>	Ceiling
                            NULL
        Condition3	   Min_Adj_Comp_Price	>	Ceiling
                                      255	>	250
        Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                          TRUE			Not Evaluated		TRUE
       */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "null", "351", "352", "351", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "null", "352", "354", "356", "357", "355"},
                {"STE", "4", OCC_DATE, "362", "null", "353", "356", "361", "368", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "null", "352.00", "351.00", "352.00", "0", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "null", "358.00", "356.00", "357.00", "0", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "360.00", "150.00", "360.00", "null", "362.00", "361.00", "368.00", "0", "1", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsTrueMinAdjCompPriceConditionIsNotEvaluated_TC7() {
        /*
        Condition1	Final BAR	>=	Ceiling
                         251	>=	250
        Condition2	Hist_BAR_ADR_Lower	>	Ceiling
                                 251	>	250
        Condition3	Min_Adj_Comp_Price	>	Ceiling
                            NULL
        Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                          TRUE			      TRUE		     Not Evaluated           */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "351", "351", "352", "null", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "356", "352", "354", "null", "357", "355"},
                {"STE", "4", OCC_DATE, "362", "361", "353", "356", "null", "368", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "351.00", "352.00", "null", "352.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "356.00", "358.00", "null", "357.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "360.00", "150.00", "360.00", "361.00", "362.00", "null", "368.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsNotEvaluatedMinAdjCompPriceConditionIsNotEvaluated_TC8() {
        /*
        Condition1	Final BAR	>=	Ceiling
                         251	>=	250
        Condition2	Hist_BAR_ADR_Lower	>	Ceiling
                        NULL
        Condition3	Min_Adj_Comp_Price	>	Ceiling
                      NULL
        Expected
        Evaluation	Condition1	AND	[	Condition2   	OR	 Condition 3	]	=	FALSE	Ceiling Count not increased
                       TRUE		    	Not Evaluated		Not Evaluated  */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "null", "null", "null", "null", "null", "null", "350"},
                {"DLX", "3", OCC_DATE, "null", "null", "null", "null", "null", "null", "355"},
                {"STE", "4", OCC_DATE, "null", "null", "null", "null", "null", "null", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertEquals(0, alertData.size()); //returns zero records as evaluation condition fails.

    }

    @Test
    public void verifyFloorEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC16() {
        /*
         Condition1	Final BAR	<=	Floor
		                   251	<=	251
	     Condition2	Hist_BAR_ADR_Upper	<	Floor
		                            249	<	251
	     Condition3	Max_Adj_Comp_Price	<	Floor
		                         260	<	251
 	     Expected
         Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count increased
	                    TRUE			  TRUE		           FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "150.00", "None"},
                {"DLX", "4", OCC_DATE, "160.00", "None"},
                {"STE", "5", OCC_DATE, "170.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "149", "141", "141", "149", "141", "149", "149"},
                {"DLX", "3", OCC_DATE, "159", "151", "151", "159", "151", "159", "159"},
                {"STE", "4", OCC_DATE, "169", "161", "161", "169", "161", "169", "169"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "150.00", "150.00", "350.00", "141.00", "149.00", "141.00", "149.00", "1", "1", "299.99000", "28.812500", "1", "0", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "160.00", "160.00", "355.00", "151.00", "159.00", "151.00", "159.00", "1", "1", "309.12000", "28.812500", "1", "0", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "170.00", "170.00", "360.00", "161.00", "169.00", "161.00", "169.00", "1", "1", "320.00000", "28.812500", "1", "0", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsFalse_TC17() {
        /*
         Condition1	Final BAR	<=	Floor
		                 251	<=	251
	     Condition2	Hist_BAR_ADR_Upper	<	Floor
		                         251	<	251
	     Condition3	Max_Adj_Comp_Price	<	Floor
		                         260	<	251
         Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Floor Count not  increased
	                     TRUE			   FALSE		      FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "150.00", "None"},
                {"DLX", "4", OCC_DATE, "160.00", "None"},
                {"STE", "5", OCC_DATE, "170.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "160", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "170", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "249", "241", "141", "149", "141", "149", "149"},
                {"DLX", "3", OCC_DATE, "259", "251", "151", "159", "151", "159", "159"},
                {"STE", "4", OCC_DATE, "269", "261", "161", "169", "161", "169", "169"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "150.00", "150.00", "350.00", "241.00", "249.00", "141.00", "149.00", "0", "1", "299.99000", "28.812500", "1", "0", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "160.00", "160.00", "355.00", "251.00", "259.00", "151.00", "159.00", "0", "1", "309.12000", "28.812500", "1", "0", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "None", "s1", "170.00", "170.00", "360.00", "261.00", "269.00", "161.00", "169.00", "0", "1", "320.00000", "28.812500", "1", "0", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenSpecificOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC9() {
        /*
        Override	OverrideType	OverrideValue
            Y	       Specific	      280
        Condition1	Final BAR	>=	Ceiling
                         280	>=	280
        Condition2	Hist_BAR_ADR_Lower	>	Ceiling
                                 281	>	280
        Condition3	Min_Adj_Comp_Price	>	Ceiling
                                 281	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                        TRUE			  TRUE	          	TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "OVERRIDE_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "USER", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "USER", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "USER", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "381", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "386", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "391", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Specific_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "USER", "s1", "380.00", "380.00", "380.00", "381.00", "452.00", "381.00", "452.00", "1", "1", "299.99000", "28.812500", "0", "1", "", "USER"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "USER", "s1", "385.00", "385.00", "385.00", "386.00", "458.00", "386.00", "458.00", "1", "1", "309.12000", "28.812500", "0", "1", "", "USER"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "USER", "s1", "390.00", "390.00", "390.00", "391.00", "462.00", "391.00", "462.00", "1", "1", "320.00000", "28.812500", "0", "1", "", "USER"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenSpecificOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC10() {
        /*
        Override	OverrideType	Override Value
             Y	        Specific	280
        Condition1	Final BAR	>=	Ceiling
                    		280	>=	280
     	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
	                            	290	>	280
   	    Condition3	Min_Adj_Comp_Price	>	Ceiling
		                          249	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count not increased
	                   TRUE			      TRUE		          FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "OVERRIDE_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "USER", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "USER", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "USER", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "380", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "381", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "382", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Specific_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "USER", "s1", "380.00", "380.00", "380.00", "381.00", "452.00", "380.00", "452.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "USER"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "USER", "s1", "385.00", "385.00", "385.00", "386.00", "458.00", "381.00", "458.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "USER"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "USER", "s1", "390.00", "390.00", "390.00", "391.00", "462.00", "382.00", "462.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "USER"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC11() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    280
	        Y             Floor	    250
       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	281	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOORANDCEIL", "360.00", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOORANDCEIL", "365.00", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOORANDCEIL", "370.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "381", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "386", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "391", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_FloorCeil_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "380.00", "360.00", "380.00", "381.00", "452.00", "381.00", "452.00", "1", "1", "299.99000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "385.00", "365.00", "385.00", "386.00", "458.00", "386.00", "458.00", "1", "1", "309.12000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "390.00", "370.00", "390.00", "391.00", "462.00", "391.00", "462.00", "1", "1", "320.00000", "28.812500", "0", "1", "", "CEIL"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC12() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    280
	        Y             Floor	    250
       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	255	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOORANDCEIL", "360.00", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOORANDCEIL", "365.00", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOORANDCEIL", "370.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "379", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "384", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "390", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "380.00", "360.00", "380.00", "381.00", "452.00", "379.00", "452.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "385.00", "365.00", "385.00", "386.00", "458.00", "384.00", "458.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "390.00", "370.00", "390.00", "391.00", "462.00", "390.00", "462.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "CEIL"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenCeilingOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC13() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    280

       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	255	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "CEIL", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "CEIL", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "CEIL", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "379", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "384", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "390", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "CEIL", "s1", "380.00", "150.00", "380.00", "381.00", "452.00", "379.00", "452.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "CEIL", "s1", "385.00", "150.00", "385.00", "386.00", "458.00", "384.00", "458.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "CEIL", "s1", "390.00", "150.00", "390.00", "391.00", "462.00", "390.00", "462.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "CEIL"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC14() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Floor	    280

       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	255	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOOR", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOOR", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOOR", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "379", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "384", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "390", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOOR", "s1", "380.00", "380.00", "350.00", "381.00", "452.00", "379.00", "452.00", "1", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOOR", "s1", "385.00", "385.00", "355.00", "386.00", "458.00", "384.00", "458.00", "1", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOOR", "s1", "390.00", "390.00", "360.00", "391.00", "462.00", "390.00", "462.00", "1", "1", "320.00000", "28.812500", "0", "1", "", "NONE"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyCeilEvalWhenSameCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsFalse_TC15() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    280
	        Y             Floor	    280
       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	280	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          FALSE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOORANDCEIL", "380.00", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOORANDCEIL", "385.00", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOORANDCEIL", "390.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "351", "452", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "356", "458", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "361", "462", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "380.00", "380.00", "380.00", "381.00", "452.00", "351.00", "452.00", "1", "0", "299.99000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "385.00", "385.00", "385.00", "386.00", "458.00", "356.00", "458.00", "1", "0", "309.12000", "28.812500", "0", "1", "", "CEIL"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "390.00", "390.00", "390.00", "391.00", "462.00", "361.00", "462.00", "1", "0", "320.00000", "28.812500", "0", "1", "", "CEIL"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorAndCeilEvalWhenSameCeilingAndFloorOvrExistsAndFinalBarConditionIsTrue_TC34() {
        /* EXAMPLE - FLOORANDCEIL VIOLATION - NEGATIVE CONDITION CHECK
        Override	OverrideType	Override Value
            Y	        Ceiling	    280
	        Y             Floor	    280
       	Condition1	Final BAR	>=	Ceiling
                        280	    >=	280
      	Condition2	Hist_BAR_ADR_Lower	>	Ceiling
		                           281	>	280
      	Condition3	Min_Adj_Comp_Price	>	Ceiling
	                            	281	>	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Ceiling  Count  increased
	                     TRUE		  	 TRUE		          TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "345.00", "FLOORANDCEIL", "345.00", "345.00"},
                {"DLX", "4", OCC_DATE, "345.00", "FLOORANDCEIL", "345.00", "345.00"},
                {"STE", "5", OCC_DATE, "345.00", "FLOORANDCEIL", "345.00", "345.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "452", "381", "351", "352", "251", "252", "350"},
                {"DLX", "3", OCC_DATE, "458", "386", "352", "354", "256", "258", "355"},
                {"STE", "4", OCC_DATE, "462", "391", "353", "356", "261", "262", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "345.00", "345.00", "345.00", "381.00", "452.00", "251.00", "252.00", "1", "1", "299.99000", "28.812500", "1", "1", "", "FLOORANDCEIL"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "345.00", "345.00", "345.00", "386.00", "458.00", "256.00", "258.00", "1", "1", "309.12000", "28.812500", "1", "1", "", "FLOORANDCEIL"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "345.00", "345.00", "345.00", "391.00", "462.00", "261.00", "262.00", "1", "1", "320.00000", "28.812500", "1", "1", "", "FLOORANDCEIL"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionAndMinAdjCompPriceConditionAreNotEvaluated_TC18() {
        /*
         Condition1	Final BAR	<=	Floor
		                 250	<=	250
	     Condition2	Hist_BAR_ADR_Upper	<	Floor
		                          NULL	>	250
	     Condition3	Max_Adj_Comp_Price	<	Floor
		                          NULL	<	250
         Expected
         Condition1	AND	[	Condition2  	OR	Condition3	]	=	NOT Evaluated	Floor Count not increased
	          TRUE			  NULL		          NULL
         */

        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "150.00", "None"},
                {"DLX", "4", OCC_DATE, "150.00", "None"},
                {"STE", "5", OCC_DATE, "150.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "null", "null", "null", "null", "null", "null", "350"},
                {"DLX", "3", OCC_DATE, "null", "null", "null", "null", "null", "null", "355"},
                {"STE", "4", OCC_DATE, "null", "null", "null", "null", "null", "null", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertEquals(0, alertData.size());  //No records should be returned since evaluation condition fails
    }

    @Test
    public void verifyFloorEvalWhenSpecificOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC19() {
        /*
        Override	OverrideType	OverrideValue
            Y	       Specific	      280
        Condition1	Final BAR	<= Floor
                         280	<=	280
        Condition2	Hist_BAR_ADR_Upper	< Floor
                                 279	<	280
        Condition3	Max_Adj_Comp_Price  < Floor
                                 279	<	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count increased
                        TRUE			  TRUE	          	TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "OVERRIDE_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "USER", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "USER", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "USER", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "281", "351", "352", "321", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "286", "352", "354", "326", "358", "355"},
                {"STE", "4", OCC_DATE, "362", "291", "353", "356", "331", "362", "360"}
        };

        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "USER", "s1", "380.00", "380.00", "380.00", "281.00", "352.00", "321.00", "352.00", "1", "1", "299.99000", "28.812500", "1", "0", "", "USER"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "USER", "s1", "385.00", "385.00", "385.00", "286.00", "358.00", "326.00", "358.00", "1", "1", "309.12000", "28.812500", "1", "0", "", "USER"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "USER", "s1", "390.00", "390.00", "390.00", "291.00", "362.00", "331.00", "362.00", "1", "1", "320.00000", "28.812500", "1", "0", "", "USER"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorEvalWhenFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC20() {
        /*
        Override	OverrideType	OverrideValue
            Y	       Floor	      280
        Condition1	Final BAR	<= Floor
                         280	<=	280
        Condition2	Hist_BAR_ADR_Upper	< Floor
                                 279	<	280
        Condition3	Max_Adj_Comp_Price  < Floor
                                 279	<	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count increased
                        TRUE			  TRUE	          	TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "OVERRIDE_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOOR", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOOR", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOOR", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "281", "351", "352", "321", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "286", "352", "354", "326", "358", "355"},
                {"STE", "4", OCC_DATE, "362", "291", "353", "356", "331", "362", "360"}
        };

        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOOR", "s1", "380.00", "380.00", "350.00", "281.00", "352.00", "321.00", "352.00", "1", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOOR", "s1", "385.00", "385.00", "355.00", "286.00", "358.00", "326.00", "358.00", "1", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOOR", "s1", "390.00", "390.00", "360.00", "291.00", "362.00", "331.00", "362.00", "1", "1", "320.00000", "28.812500", "1", "0", "", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorEvalWhenFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC21() {
        /*
        Season DOW
        Ex:Thursday  Floor 290
                     Ceil  350

        Override	OverrideType	OverrideValue
            Y	       Floor	      300
        Condition1	Final BAR	<= Floor
                         300	<=	300
        Condition2	Hist_BAR_ADR_Upper	< Floor
                                 279	<	280
        Condition3	Max_Adj_Comp_Price  < Floor
                                 279	<	280
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count increased
                        TRUE			  TRUE	          	TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "OVERRIDE_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOOR", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOOR", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOOR", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };

        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "400", "500");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "281", "351", "352", "321", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "286", "352", "354", "326", "358", "355"},
                {"STE", "4", OCC_DATE, "362", "291", "353", "356", "331", "362", "360"}
        };

        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOOR", "s1", "380.00", "380.00", "500.00", "281.00", "352.00", "321.00", "352.00", "1", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOOR", "s1", "385.00", "385.00", "500.00", "286.00", "358.00", "326.00", "358.00", "1", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOOR", "s1", "390.00", "390.00", "500.00", "291.00", "362.00", "331.00", "362.00", "1", "1", "320.00000", "28.812500", "1", "0", "", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsFalseAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC22() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    400
	        Y             Floor	    300
     Conditions	Condition1	Final BAR	<=	Floor
	                        	300	<=	300

	Condition2	Hist_BAR_ADR_Upper	<	Floor
	                        	275	<	300

	Condition3	Max_Adj_Comp_Price	<	Floor
                        		279	<	300

        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Floor  Count  Not increased
	                     FALSE		  	 TRUE		          TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "380.00", "FLOORANDCEIL", "300.00", "380.00"},
                {"DLX", "4", OCC_DATE, "385.00", "FLOORANDCEIL", "310.00", "385.00"},
                {"STE", "5", OCC_DATE, "390.00", "FLOORANDCEIL", "320.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "400", "500");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "151", "252", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "156", "258", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "161", "262", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertEquals(0, alertData.size());  //No records should be returned since evaluation condition fails
    }

    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsTrueAndMinAdjCompPriceConditionIsTrue_TC23() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    400
	        Y             Floor	    300
     Conditions	Condition1	Final BAR	<=	Floor
	                        	300	<=	300

	Condition2	Hist_BAR_ADR_Upper	<	Floor
	                        	275	<	300

	Condition3	Max_Adj_Comp_Price	<	Floor
                        		279	<	300

        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Floor  Count  Not increased
	                     TRUE		  	 TRUE		          TRUE
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "300.00", "FLOORANDCEIL", "300.00", "380.00"},
                {"DLX", "4", OCC_DATE, "310.00", "FLOORANDCEIL", "310.00", "385.00"},
                {"STE", "5", OCC_DATE, "320.00", "FLOORANDCEIL", "320.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "400", "500");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "151", "252", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "156", "258", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "161", "262", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "300.00", "300.00", "380.00", "181.00", "252.00", "151.00", "252.00", "1", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "310.00", "310.00", "385.00", "186.00", "258.00", "156.00", "258.00", "1", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "320.00", "320.00", "390.00", "191.00", "262.00", "161.00", "262.00", "1", "1", "320.00000", "28.812500", "1", "0", "", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsFalseAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsFalse_TC24() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Conditions	Condition1	Final BAR	<=	Floor
	                        	175	<=	150

    	Condition2	Hist_BAR_ADR_Upper	<	Floor
		                            275	<	150

    	Condition3	Max_Adj_Comp_Price	<	Floor
		                            279	<	150
		Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	FALSE	Floor Count    not increased
	                    FALSE		   	FALSE		        FALSE
         */

        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "310.00", "FLOORANDCEIL", "160.00", "380.00"},
                {"DLX", "4", OCC_DATE, "320.00", "FLOORANDCEIL", "170.00", "385.00"},
                {"STE", "5", OCC_DATE, "330.00", "FLOORANDCEIL", "180.00", "390.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "400", "500");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "151", "252", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "156", "258", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "161", "262", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertEquals(0, alertData.size());  //No records should be returned since evaluation condition fails
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_TC25() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "400", "500");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "400", "500");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "170.00", "170.00", "270.00", "191.00", "262.00", "123.00", "143.00", "0", "1", "320.00000", "28.812500", "1", "0", "", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_TC26() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "170.00", "170.00", "270.00", "191.00", "262.00", "123.00", "143.00", "0", "1", "320.00000", "28.812500", "1", "0", "", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
    }

    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_PriceExcludedClassNotDisplayed_TC27() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

        --->Price Excluded STE Room Class<------
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setPriceExcludedRoomClass("STE", 1);
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "", "FLOOR"},
        };
        assertTrue(alertData.size() == 2);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        setPriceExcludedRoomClass("STE", 0);
    }

    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_Two_PriceExcludedClassNotDisplayed_TC28() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

        --->Price Excluded DLX,STE Room Class<------
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setPriceExcludedRoomClass("DLX", 1);
        setPriceExcludedRoomClass("STE", 1);
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "", "FLOOR"},
        };
        assertTrue(alertData.size() == 1);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        setPriceExcludedRoomClass("DLX", 0);
        setPriceExcludedRoomClass("STE", 0);
    }

    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_ALL_PriceExcludedClassNotDisplayed_TC29() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

        --->Price Excluded STD,DLX,STE Room Class<------
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setPriceExcludedRoomClass("STD", 1);
        setPriceExcludedRoomClass("DLX", 1);
        setPriceExcludedRoomClass("STE", 1);
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        assertTrue(alertData.size() == 0);
        setPriceExcludedRoomClass("STD", 0);
        setPriceExcludedRoomClass("DLX", 0);
        setPriceExcludedRoomClass("STE", 0);
    }

    @Test
    public void verifyCeilEvalWhenFinalBarConditionIsTrueAndHistBarADRConditionIsNotEvaluatedMinAdjCompPriceConditionIsTrue_PriceExcluded_TC30() {
       /*
        Conditions	Condition1	Final BAR	>=	Ceiling
                                      251	>=	250
        Condition2	     Hist_BAR_ADR_Lower	>	Ceiling
                            NULL
        Condition3	   Min_Adj_Comp_Price	>	Ceiling
                                      255	>	250
        Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Ceiling Count increased
                          TRUE			Not Evaluated		TRUE
                            --->Price Excluded STE Room Class<------
       */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE"},
                {"STD", "6", OCC_DATE, "350.00", "None"},
                {"DLX", "4", OCC_DATE, "355.00", "None"},
                {"STE", "5", OCC_DATE, "360.00", "None"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "352", "null", "351", "352", "351", "352", "350"},
                {"DLX", "3", OCC_DATE, "358", "null", "352", "354", "356", "357", "355"},
                {"STE", "4", OCC_DATE, "362", "null", "353", "356", "361", "368", "360"}
        };
        insert_CP_Decision_BAR_Output_Data(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setPriceExcludedRoomClass("STE", 1);
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "None", "s1", "350.00", "150.00", "350.00", "null", "352.00", "351.00", "352.00", "0", "1", "299.99000", "28.812500", "0", "1", "", "NONE"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "None", "s1", "355.00", "150.00", "355.00", "null", "358.00", "356.00", "357.00", "0", "1", "309.12000", "28.812500", "0", "1", "", "NONE"},
        };
        assertTrue(alertData.size() == 2);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        setPriceExcludedRoomClass("STE", 0);
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_WithSplEvent_TC31() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE
         Special Event Type Convention -> TestSplEvent
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setSpecialEventInformation("TestSplEvent");
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "Convention", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "Convention", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "170.00", "170.00", "270.00", "191.00", "262.00", "123.00", "143.00", "0", "1", "320.00000", "28.812500", "1", "0", "Convention", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        deleteSpecialEventInformation("TestSplEvent");
    }


    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_WithSplEvent_TC32() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE
         Special Event Type Convention -> TestSplEvent
         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", "null", "null", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", "null", "null", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", "null", "null", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        clearDefaultAndSeasonData();
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setSpecialEventInformation("TestSplEvent");
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "null", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "Convention", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "null", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "Convention", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "null", "170.00", "170.00", "270.00", "191.00", "262.00", "123.00", "143.00", "0", "1", "320.00000", "28.812500", "1", "0", "Convention", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        deleteSpecialEventInformation("TestSplEvent");
        clearDefaultAndSeasonData();
        setDefaultCP_CFG_BAST_AT_Data();
    }

    @Test
    public void verifyFloorEvalWhenCeilingAndFloorOvrExistsAndFinalBarConditionIsTrueAndHistBarADRConditionIsFalseAndMinAdjCompPriceConditionIsTrue_WithSplEvent_TC33() {
        /* EXAMPLE
        Override	OverrideType	Override Value
            Y	        Ceiling	    250
	        Y             Floor	    150
        Season Ceil Floor are less than overrides
        Thursday
           150
           250

        Conditions	Condition1	Final BAR	<=	Floor
	                                	150	<=	150

	    Condition2	Hist_BAR_ADR_Upper	<	Floor
	                            	275	<	150

	    Condition3	Max_Adj_Comp_Price	<	Floor
	                               	149	<	150
										Expected
        Evaluation	Condition1	AND	[	Condition2  	OR	Condition 3	]	=	TRUE	Floor Count     increased
                    	TRUE			FALSE	        	TRUE

          Special Event Type Convention -> TestSplEvent
          Special Event Type Festival -> TestSplEvent1

         */
        OCC_DATE = getLocalSystemDate();
        S1_ST_DT = getLocalSystemDate();
        S1_EN_DT = getLocalSystemDatePlusNDays(7);
        String[][] CP_DECISION_BAR_OUTPUT = new String[][]{
                {"ACCOM_CLASS", "BASE_AT_ID", "ARRIVAL_DT", "FINAL_BAR", "OVERRIDE", "FLOOR_OVR_VALUE", "CEIL_OVR_VALUE"},
                {"STD", "6", OCC_DATE, "150.00", "FLOORANDCEIL", "150.00", "250.00"},
                {"DLX", "4", OCC_DATE, "160.00", "FLOORANDCEIL", "160.00", "260.00"},
                {"STE", "5", OCC_DATE, "170.00", "FLOORANDCEIL", "170.00", "270.00"}
        };
        String[][] CP_CFG_BASE_AT = new String[][]{
                {"ACCOM_TYPE_ID", "START_DATE", "END_DATE", "SU_F", "SU_C", "M_F", "M_C", "TU_F", "TU_C", "W_F", "W_C", "TH_F", "TH_C", "F_F", "F_C", "SA_F", "SA_C", "SU_W_F", "SU_W_C", "M_W_F", "M_W_C", "TU_W_F", "TU_W_C", "W_W_F", "W_W_C", "TH_W_F", "TH_W_C", "F_W_F", "F_W_C", "SA_W_F", "SA_W_C", "SeasonName"},
                {"6", S1_ST_DT, S1_EN_DT, "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "150", "350", "s1"},
                {"4", S1_ST_DT, S1_EN_DT, "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "150", "355", "s1"},
                {"5", S1_ST_DT, S1_EN_DT, "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "150", "360", "s1"}
        };
        setDOWCeilingFloor(CP_CFG_BASE_AT, 1, 0, "100", "130");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 2, 0, "110", "140");
        setDOWCeilingFloor(CP_CFG_BASE_AT, 3, 0, "120", "145");

        String[][] CENTRAL_RMS_PRICE_DATA = new String[][]{
                {"ACCOM_CLASS", "ACCOM_CLASS_ID", "Occupancy_DT", "Hist_BAR_ADR_Upper", "Hist_BAR_ADR_Lower", "Min_Comp_Price", "Max_Comp_Price", "Min_Adj_Comp_Price", "Max_Adj_Comp_Price", "BAR_FG_Ref_Price"},
                {"STD", "2", OCC_DATE, "252", "181", "351", "352", "121", "141", "350"},
                {"DLX", "3", OCC_DATE, "258", "186", "352", "354", "122", "142", "355"},
                {"STE", "4", OCC_DATE, "262", "191", "353", "356", "123", "143", "360"}
        };
        insert_CP_Decision_BAR_Output_Data_With_Override(CP_DECISION_BAR_OUTPUT);
        insert_CP_CFG_BASE_AT_Data(CP_CFG_BASE_AT);
        insert_CENTRAL_RMS_PRICE_DATA_Data(CENTRAL_RMS_PRICE_DATA);
        setLRVDataForRoomClass(OCC_DATE, "STD", BigDecimal.valueOf(299.99));
        setLRVDataForRoomClass(OCC_DATE, "DLX", BigDecimal.valueOf(309.12));
        setLRVDataForRoomClass(OCC_DATE, "STE", BigDecimal.valueOf(320.00));
        setOccupancyFcstDataForAccomType(OCC_DATE, 5, BigDecimal.valueOf(129.08));
        setMultipleSpecialEventInformation();
        List<Object[]> alertData = tenantCrudService().findByNativeQuery("exec usp_central_rms_cf_price_alert_detail '" + OCC_DATE + "',1,1");
        String[][] PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT = new String[][]{
                {"ARRIVAL_DT", "ACC_CLS_ID", "ACC_CLS_CODE", "ACC_CLS_NAME", "IS_MASTER_CLASS", "ACCOM_RANK_ORDER", "OVERRIDE", "SEASON", "FINAL_BAR", "EFF_FLOOR_RT", "EFF_CEIL_RT", "HIST_BAR_ADR_LOWER", "HIST_BAR_ADR_UPPER", "MIN_ADJ_COMP_PRICE", "MAX_ADJ_COMP_PRICE", "ADR_INC", "COMP_INC", "LRV", "OCC_FCST", "FLOOR", "CEIL", "SPL_EVENTS", "OVR_VIOLATION"},
                {OCC_DATE, "2", "STD", "STD", "0", "1", "FLOORANDCEIL", "s1", "150.00", "150.00", "250.00", "181.00", "252.00", "121.00", "141.00", "0", "1", "299.99000", "28.812500", "1", "0", "Convention,Festival", "FLOOR"},
                {OCC_DATE, "3", "DLX", "DLX", "1", "2", "FLOORANDCEIL", "s1", "160.00", "160.00", "260.00", "186.00", "258.00", "122.00", "142.00", "0", "1", "309.12000", "28.812500", "1", "0", "Convention,Festival", "FLOOR"},
                {OCC_DATE, "4", "STE", "STE", "0", "3", "FLOORANDCEIL", "s1", "170.00", "170.00", "270.00", "191.00", "262.00", "123.00", "143.00", "0", "1", "320.00000", "28.812500", "1", "0", "Convention,Festival", "FLOOR"}
        };
        assertTrue(alertData.size() == 3);
        verifyExpected_PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT(alertData, PRICE_AT_CEIL_FLOOR_ALERT_EXPECTED_OUTPUT);
        deleteMultipleSplEventInfo();
    }


}

