package com.ideas.tetris.pacman.services.fplos;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.integration.opera.agent.dto.FplosDecision;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.ConfigParamName;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.MasterClassOverrideHelperBean;
import com.ideas.tetris.pacman.services.bestavailablerate.OverrideService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionBarOutputCreator;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.decisiontranslator.OperaDecisionService;
import com.ideas.tetris.pacman.services.fplos.batchupdate.DecisionQualifiedFPLOSBatchUpdater;
import com.ideas.tetris.pacman.services.fplos.batchupdate.DecisionQualifiedFPLOSBatchUpdaterBean;
import com.ideas.tetris.pacman.services.fplos.batchupdate.DecisionQualifiedFPLOSBatchUpdaterForTARSOutbound;
import com.ideas.tetris.pacman.services.fplos.dto.FPLOSQualifiedChunkDetail;
import com.ideas.tetris.pacman.services.fplos.entity.DecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.limittotal.service.LimitTotalService;
import com.ideas.tetris.pacman.services.opera.OperaAgentDecisionService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteTaskService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedDetails;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.hamcrest.core.Is;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.BARDECISIONOVERRIDE_NONE;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_LOS;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.DIFFERENTIAL;
import static com.ideas.tetris.pacman.common.constants.Constants.FALSE;
import static com.ideas.tetris.pacman.common.constants.Constants.FULL;
import static com.ideas.tetris.pacman.common.constants.Constants.NONE;
import static com.ideas.tetris.pacman.common.constants.Constants.OPERA;
import static com.ideas.tetris.pacman.common.constants.Constants.TRUE;
import static com.ideas.tetris.pacman.services.demandoverride.DemandOverrideService.MAX_LOS;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class FPLOSQualifiedRecommendationServiceOldTest extends AbstractG3JupiterTest {
    public static final String YIELDABLE_COST = "YieldableCost";
    public static final String YIELDABLE_VALUE = "YieldableValue";
    public static final String SRP_FPLOS_AT_TOTAL_LEVEL = "pacman.integration.srpFplosAtTotalLevel";
    public static final String BAR_DECISION = "pacman.bar.barDecision";
    public static final String APPLY_VARIABLE_DECISION_WINDOW = "pacman.integration.applyVariableDecisionWindow";
    public static final String OVERRIDE_VARIABLE_DECISION_WINDOW = "pacman.integration.overrideVariableDecisionWindow";
    private static final int DERIVED_VALUE_TYPE = 1;
    private static final int DERIVED_PERCENTAGE_TYPE = 2;
    private static final int FIXED_TYPE = 3;
    private static final int PER_ROOM_PER_STAY = 1;
    private static final int PER_ROOM_PER_NIGHT = 2;
    private static final String PACMAN_FEATURE_SERVICING_COST_BY_LOSENABLE = "pacman.feature.ServicingCostByLOSEnable";
    private static final String IS_EXTENDED_STAY = "pacman.integration.RACHET.useExtendedStayMapping";
    private static final String OPTMIZATION_WINDOW_BDE_DAYSOFWEEK = "pacman.integration.completeDecisionUploadDaysOfWeek";
    private static final String QUALIFIED_FPLOS_MAX_LOS = "pacman.core.property.QualifiedFPLOSMaxLOS";
    private static final String OPTMIZATION_WINDOW_BDE_VARIABLE = "pacman.integration.variableDecisionWindowDays";
    private static final String LRA_ENABLED = "pacman.feature.LRAEnabled";
    private static final String OPTMIZATION_WINDOW_BDE = "pacman.optimization.optimizationWindowBDE";
    private static final String PACMAN_INTEGRATION_RATCHET_USEEXTENDEDSTAYMAPPING = "pacman.integration.RATCHET.useExtendedStayMapping";
    private static final String OPEN_LV0_ENABLED = "pacman.OPEN_LV0.OpenLV0Enabled";
    private static final String DECISION_UPLOAD_WINDOW_BDE = "pacman.upload.DecisionUploadWindowBDEDays";
    private static final String OPERA_FPLOS_BY_RATE_CODE_BY_ROOM_TYPE_UPLOADTYPE = "pacman.integration.opera.FplosByRateCode.uploadtype";
    private static final String OPERA_FPLOS_BY_RATE_CODE_UPLOADTYPE = "pacman.integration.opera.FplosByRateCodeByRoomType.uploadtype";
    private static final String PACMAN_FEATURE = "pacman.feature";
    private static final String PACMAN_FEATURE_DERIVED_QUALIFIED_RATE_PLAN_ENABLED = PACMAN_FEATURE + ".isDerivedQualifiedRatePlanEnabled";
    private static final String PACMAN_FEATURE_CONTINUOUS_PRICING_ENABLED = PACMAN_FEATURE + ".isContinuousPricingEnabled";
    private static final String CLOSE_HIGHEST_BAR_ENABLED = "pacman.feature.highestBarRestrictedEnabled";
    private static final String TARS_MIN_MAX_LOS_BYRATECODE_UPLOADTYPE = "pacman.integration.TARS.MinMaxLosByRateCode.uploadtype";
    private static final String FPLOS_FORCE_LEGACY_CARDINALITY_ESTIMATION = "pacman.feature.fplos.forceLegacyCardinalityEstimator";
    private final int propertyID = 5;
    private final int accomClassID = 3;
    private final int accomTypeID = 4;
    @InjectMocks
    FPLOSQualifiedRecommendationService service;
    @InjectMocks
    FPLOSQualifiedRecommendationCdpService cdpService;
    @Mock
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository;
    @Mock
    DateService dateService;
    @Mock
    DecisionService decisionService;
    @Mock
    RemoteTaskService remoteTaskService;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    JpaJdbcUtil jpaJdbcUtil;
    @Mock
    PropertyService propertyService;
    @Mock
    HospitalityRoomsService hospitalityRoomsService;
    @Mock
    LimitTotalService limitTotalService;
    List<BigDecimal> lrvList;
    private int optimizationWindow = 10;
    @Mock
    private DecisionConfigurationService decisionConfigurationService;

    private static final String LV0 = "LV0";

    private StringBuilder queries;

    @BeforeEach
    public void setUp() {
        WorkContextType workContext = new WorkContextType();
        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);
        PacmanWorkContextHelper.getWorkContext().setUserId("1");
        setWorkContextProperty(TestProperty.H2);
        service.crudService = tenantCrudService();
        service.hospitalityRoomsService = hospitalityRoomsService;
        stagePacmanConfigParamsServiceMock();
        stageDateServiceMock();
        stageDecisionMock();
        when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
        prepareLRVList(39, 9);
        System.setProperty("FPLOSQualifiedRecommendationStep.chunkSize", "90");
        when(limitTotalService.isLimitTotalEnabled()).thenReturn(Boolean.FALSE);
        queries = new StringBuilder();

    }

    private void prepareLRVList(int startingValue, int length) {
        lrvList = new ArrayList<>();
        lrvList.add(BigDecimal.valueOf(startingValue));
        for (int i = 2; i < length; i++) {
            lrvList.add(BigDecimal.valueOf(startingValue + i));
        }
    }

    @Test
    public void testFPLOSQualifiedDecisionsWithZeroRateOffset() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);

        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals("NNNNNNN", differentialsDecisions.get(0).getFplos());

    }

    private void prepareQualifiedRatePlanData(int rateQualifiedId, int propertyID, int accomTypeID, String rateCodeName, String qualifiedRateplanStartDate, String qualifiedRateplanEndDate, int rateQualifiedTypeId, BigDecimal sunday, BigDecimal monday,
                                              BigDecimal tuesday, BigDecimal wednesday, BigDecimal thursday, BigDecimal friday, BigDecimal saturday) {
        queries.append("delete from Rate_Qualified_Details where Rate_Qualified_ID = " + rateQualifiedId);
        queries.append("delete from Rate_Qualified where Rate_Qualified_ID = " + rateQualifiedId);
        String rateQualifiedQuery = "SET IDENTITY_INSERT [Rate_Qualified] ON" +
                " INSERT INTO [Rate_Qualified]" +
                " ([Rate_Qualified_ID],[File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Start_Date_DT],[End_Date_DT]" +
                "   ,[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" +
                "   ,[Status_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Rate_Qualified_Type_Id])" +
                " VALUES (" + rateQualifiedId + ",(select MAX(File_Metadata_ID) from File_Metadata), " + propertyID + ", '" + rateCodeName + "', 'rate description', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, " + rateQualifiedTypeId + ")" +
                "SET IDENTITY_INSERT [Rate_Qualified] OFF;";
        queries.append(rateQualifiedQuery);
        prepareQualifiedRatePlanDetails(rateQualifiedId, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate, sunday, monday, tuesday, wednesday, thursday, friday, saturday);
    }

    private void prepareQualifiedRatePlanDetails(int rateQualifiedId, int accomTypeID, String qualifiedRateplanDetailsStartDate, String qualifiedRateplanDetailsEndDate, BigDecimal sunday, BigDecimal monday, BigDecimal tuesday, BigDecimal wednesday, BigDecimal thursday, BigDecimal friday, BigDecimal saturday) {
        queries.append("INSERT INTO [Rate_Qualified_Details]" +
                " ([Rate_Qualified_ID],[Accom_Type_ID],[Start_Date_DT],[End_Date_DT],[Sunday],[Monday],[Tuesday],[Wednesday],[Thursday],[Friday],[Saturday],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM])\n" +
                " VALUES (" + rateQualifiedId + "," + accomTypeID + ",'" + qualifiedRateplanDetailsStartDate + "', '" + qualifiedRateplanDetailsEndDate + "'," + sunday + "," +
                monday + "," + tuesday + "," + wednesday + "," + thursday + "," + friday + "," + saturday + ", 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP);");
    }

    private void updateCaughtUpDate(int propertyID, String caughtUpDate) {
        String updateCaughtUpDateQuery = "UPDATE File_Metadata SET SnapShot_DT = :caughtUpDate"
                + " WHERE Record_Type_ID = 3 AND Process_Status_ID = 13 AND IsBDE = 1 AND property_Id =  :propertyID";
        tenantCrudService().executeUpdateByNativeQuery(updateCaughtUpDateQuery,
                QueryParameter.with("caughtUpDate", caughtUpDate).and("propertyID", propertyID).parameters());
    }

    private void resetUp() {
        setWorkContextProperty(TestProperty.H1);
        service.crudService = tenantCrudService();
        stagePacmanConfigParamsServiceMock();
        stageDateServiceMock();
        stageDecisionMock();
        when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
    }

    @Test
    public void testQualifiedFPLOSForDerivedRatePlanWithoutRateAdjustmentsBasedOnDOWRateCalculations() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, FALSE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    private FPLOSQualifiedRecommendationService getFplosQualifiedRecommendationService(PacmanConfigParamsService pacmanConfigParamsService, DateService dateService) {
        DecisionService decisionService = DecisionService.createTestInstance();
        decisionService.setDateServiceLocal(dateService);
        decisionService.setCrudService(tenantCrudService());

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = new FPLOSQualifiedRecommendationService();
        fplosQualifiedRecommendationService.crudService = tenantCrudService();
        fplosQualifiedRecommendationService.dateService = dateService;
        fplosQualifiedRecommendationService.configService = pacmanConfigParamsService;
        fplosQualifiedRecommendationService.decisionService = decisionService;
        fplosQualifiedRecommendationService.jpaJdbcUtil = jpaJdbcUtil;
        fplosQualifiedRecommendationService.hospitalityRoomsService = hospitalityRoomsService;
        return fplosQualifiedRecommendationService;
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculationsBasedOnDowRates() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_ForLV0_WithPostingRule1AndNetTypeCost_QFPLOSCalculationsBasedOnDOWFlagIsSet_SRPFPLOSTrue() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, FALSE, FALSE, "7", TRUE, TRUE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 1, -200, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals("NNNYNNN", differentialsDecisions.get(0).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_NOBARDecision_NoRatePlanConfigured_DerivedFPLOSIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testQualifiedFPLOS_ForFixedRateMissingSpecturm_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("20", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 7), lrvArray.length, Arrays.asList(lrvArray));
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 14), lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeID, DateUtil.addDaysToDate(startDate, 11), 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        prepareQualifiedRatePlanDetails(1, accomTypeID, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 12), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 18), DateUtil.DEFAULT_DATE_FORMAT), BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(2, differentialsDecisions.size());
        assertEquals("2016-01-03", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("NNNYNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("2016-01-15", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("YYYYYYY", differentialsDecisions.get(1).getFplos());
        //assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        //assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_ForFixedRate_AccomCapacityIsZero_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 0, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());

    }

    @Test
    public void testQualifiedFPLOS_ForFixed_DecisionsWindowDaysMinusMaxLOSDays_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("14", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 30), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 7), lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 30, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(14, differentialsDecisions.size());
        assertEquals("2016-01-10", differentialsDecisions.get(7).getArrivalDate().toString());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForRates_Fixed_DerivedValueOfBAR_DerviedPercentOfBAR_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(9, differentialsDecisions.size());
        assertEquals("YNYNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NYNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("YNNNNNNY", differentialsDecisions.get(2).getFplos());
        assertEquals("YYNNNNNN", differentialsDecisions.get(3).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(5).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(6).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(7).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(8).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_ForRateDerivedPercentOfBAR_WithRateAdjPostingRule2_NetTypeAmount_Set_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 2, BigDecimal.valueOf(-120), BigDecimal.valueOf(-120), BigDecimal.valueOf(-60), BigDecimal.valueOf(-70), BigDecimal.valueOf(-80), BigDecimal.valueOf(-90), BigDecimal.valueOf(-120));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 1, -150, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 1, -45, 2);
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 1, 250, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 1, 60, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 3, 500, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(1, differentialsDecisions.size());
        assertEquals("NNNYYYY", differentialsDecisions.get(0).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypePercentYV_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 2, 1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypeAmountYC_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypeSet_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 3, 40, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypePercent_YV_DifferentRange_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 1, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), qualifiedRateplanEndDate, YIELDABLE_VALUE, 2, 50, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNYY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypePercent_YV_MissingRateAdjFor_LOS1_LOS6_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 1, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 50, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNYYYY", differentialsDecisions.get(2).getFplos());
    }


    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_MultipleRoomTypes_RateAdjWithPostingRule2AndNetTypeAmountYC_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;
        final List<AccomClass> accomClassList = tenantCrudService().findByNamedQuery(AccomClass.BY_PROPERTY_ID, QueryParameter.with("propertyId", 5).parameters());
        final AccomClass accomClass = accomClassList.stream().filter(accomClassObj -> accomClassObj.getId().equals(3)).findFirst().get();
        final AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClass);

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        prepareAccomActivityData(propertyID, accomType.getId(), startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));
        prepareQualifiedRatePlanDetails(1, accomType.getId(), qualifiedRateplanStartDate, qualifiedRateplanEndDate, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(6, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(3).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(5).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypePercent_Different_YC_YV_EachDay_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray =
                {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.81), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.03), BigDecimal.valueOf(822.94), BigDecimal.valueOf(2412.07), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(150), BigDecimal.valueOf(300), BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(200), BigDecimal.valueOf(150));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 2, -300, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 2, -200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 2, -150, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 2, -800, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 2, -150, 2);

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, "YieldableValue", 2, 50, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 2, 600, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 2, 100, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 2, 200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 2, 4000, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), "YieldableValue", 2, 200, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypeAmount_With_YC_YV_EachDay_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1, 2);
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 1, 1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate__RateAdjWithPostingRule2_EachDay_NetTypeAmountYC_NetTypePercentYV_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1, 2);
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 2, 1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOSForQualifiedRatesDerivedOffBarForBARByLOSWithMissingBARForSomeLOS_Derived_RatePlansArePresent() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, "None", null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-150), BigDecimal.valueOf(-100), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-3000), BigDecimal.valueOf(-300));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 70, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -100, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testQualifiedFPLOSForQualifiedRatesDerivedOffBarForBARByLOSWithMissingBARForSomeLOS_NoQualifiedRatePlans() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, "None", null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, "None", null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, "None", null, null);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule2_NetTypeAmount_YC_YV_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-150), BigDecimal.valueOf(-100), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-3000), BigDecimal.valueOf(-300));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 70, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -100, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertEquals(1, differentialsDecisions.size());
        assertEquals("YYYYYYN", differentialsDecisions.get(0).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule2_NetTypeAmount_Set_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 2, BigDecimal.valueOf(-120), BigDecimal.valueOf(-120), BigDecimal.valueOf(-60), BigDecimal.valueOf(-70), BigDecimal.valueOf(-80), BigDecimal.valueOf(-90), BigDecimal.valueOf(-120));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -150, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -45, 2);


        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 250, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 60, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 500, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(1, differentialsDecisions.size());
        assertEquals("NNNYYYY", differentialsDecisions.get(0).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule1_NetTypePercent_YC_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-150), BigDecimal.valueOf(-100), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-50), BigDecimal.valueOf(-2500), BigDecimal.valueOf(-300));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 2, -100, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(1, differentialsDecisions.size());
        assertEquals("NYYYYYY", differentialsDecisions.get(0).getFplos());

    }


    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_ForRateDerivedValueOfBAR_WithRateAdj_PostingRule_1_NetTypeAmount_PostingRule_2_NetTypeAmount_Percent_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2015-10-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(4.85476), BigDecimal.valueOf(0.79766), BigDecimal.valueOf(3.94594), BigDecimal.valueOf(5.94108), BigDecimal.valueOf(6.99333), BigDecimal.valueOf(8.154), BigDecimal.valueOf(9.24435)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        final RateUnqualified rateUnqualified1 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(492), BigDecimal.valueOf(492),
                BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492), BigDecimal.valueOf(492));
        final RateUnqualified rateUnqualified2 = createUniqueUnqualifiedRateWithDetails(accomTypeID, startDate, endDateForUnqualifiedRatePlan, propertyID, BigDecimal.valueOf(533), BigDecimal.valueOf(533),
                BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533), BigDecimal.valueOf(533));


        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified1.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 3, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 5, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 6, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 7, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified2.getName(), null, null);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-493), BigDecimal.valueOf(210), BigDecimal.valueOf(-530), BigDecimal.valueOf(-530), BigDecimal.valueOf(-530), BigDecimal.valueOf(-525), BigDecimal.valueOf(-300));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_COST, 1, -185, 1);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -860, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 95, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 95, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(1, differentialsDecisions.size());
        assertEquals("YNNNNNY", differentialsDecisions.get(0).getFplos());

    }


    private RateUnqualified createUniqueUnqualifiedRateWithDetails(Integer accomTypeID, Date startDate, Date endDateForUnqualifiedRatePlan, Integer propertyID, BigDecimal sunday, BigDecimal monday,
                                                                   BigDecimal tuesday, BigDecimal wednesday, BigDecimal thursday, BigDecimal friday, BigDecimal saturday) {
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(sunday);
        rateUnqualifiedDetails.setMonday(monday);
        rateUnqualifiedDetails.setTuesday(tuesday);
        rateUnqualifiedDetails.setWednesday(wednesday);
        rateUnqualifiedDetails.setThursday(thursday);
        rateUnqualifiedDetails.setFriday(friday);
        rateUnqualifiedDetails.setSaturday(saturday);
        return rateUnqualified;
    }

    @Test
    public void testQualifiedFPLOS_CPProperty_ForFixedRate__RateAdjWithPostingRule2_EachDay_NetTypeAmountYC_NetTypePercentYV_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(38));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1, 2);
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 2, 1, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }


    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypePercent_With_YC_YV_EachDay_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray =
                {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.81), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.03), BigDecimal.valueOf(822.94), BigDecimal.valueOf(2412.07), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(150), BigDecimal.valueOf(300), BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(200), BigDecimal.valueOf(150));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -300, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -150, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -800, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -150, 2);

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_VALUE, 2, 50, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 600, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 100, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 4000, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 200, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }

    private void prepareAccomActivityData(int propertyID, int accomTypeID, Date startDate, int activityDaysLength, int accomCapacity, int roomsSold) {


        String snapshotDateTime = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        queries.append("INSERT INTO [Accom_Activity] (Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID" +
                ",Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations" +
                ",No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID,Last_Updated_DTTM,CreateDate) VALUES\n");
        IntStream.range(0, activityDaysLength).forEach(i -> {
            queries.append(" (" + propertyID + ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "','" + snapshotDateTime + "'," + accomTypeID + "," + accomCapacity + "," + roomsSold + ",0,0,0,0,0,0,0,0,0,1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)\n");
            if (i < (activityDaysLength - 1)) {
                queries.append(", ");
            }
        });
    }

    private void cleanupAccomActivityData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table [Accom_Activity];");
    }

    private void prepareDecisionLRVData(int propertyID, int accomClassID, Date startDate, int days, List<BigDecimal> lrvList) {
        final StringBuilder lrvDecisionQuery = new StringBuilder();
        lrvDecisionQuery.append("DECLARE @decisionID int\n");
        lrvDecisionQuery.append("select @decisionID = Max(Decision_ID) from Decision_LRV\n");
        lrvDecisionQuery.append("INSERT INTO Decision_LRV([Decision_ID],[Property_ID],[Accom_Class_ID],[Occupancy_DT],[LRV],[CreateDate_DTTM]) VALUES\n");
        IntStream.range(0, days).forEach(i -> {
            lrvDecisionQuery.append(" (@decisionID," + propertyID + "," + accomClassID + ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "'," + lrvList.get(i) + ",CURRENT_TIMESTAMP)\n");
            if (i < (days - 1)) {
                lrvDecisionQuery.append(", ");
            }
        });
        lrvDecisionQuery.append(";");
        tenantCrudService().executeUpdateByNativeQuery(lrvDecisionQuery.toString());
    }

    private void prepareQualifiedRatePlanAdjustmentData(int rateQualifiedId, String adjustmentStartDate, String adjustmentEndDate, String adjustmentType, int netValueTypeId, int netValue, int postingRuleTypeId) {
        tenantCrudService().executeUpdateByNativeQuery(" INSERT INTO [Rate_Qualified_Adjustment]" +
                " ([Rate_Qualified_ID],[Start_Date_DT],[End_Date_DT],[Posting_Rule_ID],[Net_Value],[Net_Value_Type_ID]" +
                "   ,[CreateDate_DTTM],[AdjustmentType])" +
                " VALUES (" + rateQualifiedId + ", '" + adjustmentStartDate + "', '" + adjustmentEndDate + "'" +
                "       , " + postingRuleTypeId + ", " + netValue + ", '" + netValueTypeId + "', CURRENT_TIMESTAMP, '" + adjustmentType + "')");
    }

    private void prepareQualifiedRatePlanAdjustmentData(String adjustmentStartDate, String adjustmentEndDate, String adjustmentType, int netValueTypeId, int netValue, int postingRuleTypeId) {
        queries.append(" INSERT INTO [Rate_Qualified_Adjustment]" + " ([Rate_Qualified_ID],[Start_Date_DT],[End_Date_DT],[Posting_Rule_ID],[Net_Value],[Net_Value_Type_ID]" + "   ,[CreateDate_DTTM],[AdjustmentType])" + " VALUES (1, '").append(adjustmentStartDate).append("', '").append(adjustmentEndDate).append("'").append("       , ").append(postingRuleTypeId).append(", ").append(netValue).append(", '").append(netValueTypeId).append("', CURRENT_TIMESTAMP, '").append(adjustmentType).append("')");
    }

    @Test
    @Disabled
    public void testQualifiedFPLOS_RateOfDayProperty_ForRateDerivedPercentageOfBAR_WithRateAdj_PostingRule_2_NetTypeAmount_Per_Set_AcrossLOS_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);


        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(BigDecimal.valueOf(1075.81));
        rateUnqualifiedDetails.setMonday(BigDecimal.valueOf(1103.58));
        rateUnqualifiedDetails.setTuesday(BigDecimal.valueOf(1529.51));
        rateUnqualifiedDetails.setWednesday(BigDecimal.valueOf(1751.73));
        rateUnqualifiedDetails.setThursday(BigDecimal.valueOf(1196.18));
        rateUnqualifiedDetails.setFriday(BigDecimal.valueOf(1566.55));
        rateUnqualifiedDetails.setSaturday(BigDecimal.valueOf(1566.55));
        tenantCrudService().save(rateUnqualifiedDetails);
        tenantCrudService().flushAndClear();

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 2, BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30));

        //Cost
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_COST, 1, -400, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -120, 2);

        //Value
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_VALUE, 1, 200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 30, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 600, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForFixedRate_RateAdjWithPostingRule2AndNetTypeSet_DifferentValueEachDay_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0),};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(875.81), BigDecimal.valueOf(903.58), BigDecimal.valueOf(1329.51), BigDecimal.valueOf(1551.73), BigDecimal.valueOf(996.18), BigDecimal.valueOf(1366.55), BigDecimal.valueOf(1366.55));
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_VALUE, 3, 100, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 10, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 300, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 400, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }

    @Test
    @Disabled
    public void testQualifiedFPLOS_RateOfDayProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule1_NetTypeAmountYC_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.02), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(BigDecimal.valueOf(1075.81));
        rateUnqualifiedDetails.setMonday(BigDecimal.valueOf(1103.58));
        rateUnqualifiedDetails.setTuesday(BigDecimal.valueOf(1529.51));
        rateUnqualifiedDetails.setWednesday(BigDecimal.valueOf(1751.73));
        rateUnqualifiedDetails.setThursday(BigDecimal.valueOf(1196.18));
        rateUnqualifiedDetails.setFriday(BigDecimal.valueOf(1566.55));
        rateUnqualifiedDetails.setSaturday(BigDecimal.valueOf(1566.55));

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(5), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30), BigDecimal.valueOf(-30));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -1300, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NYYYYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("NYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_ForFixedRate_DerivedFPLOSFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(4, differentialsDecisions.size());
        assertEquals("NNNYNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNYNYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYY", differentialsDecisions.get(2).getFplos());
        assertEquals("YYYYYYY", differentialsDecisions.get(3).getFplos());

    }


    @Test
    public void testQualifiedFPLOS_ForFixedRate_QualifiedFplosCalculationsBasedOnDowRatesIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, FALSE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(4, differentialsDecisions.size());
        assertEquals("NNNYNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNYNYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYY", differentialsDecisions.get(2).getFplos());
        assertEquals("YYYYYYY", differentialsDecisions.get(3).getFplos());
    }


    @Test
    public void testQualifiedFPLOS_ForFixedRate_RateAdjWithPostingRule1AndNetTypeAmountYC_QFPLOSCalculationsBasedOnDOWFlagIsSet() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, FALSE, FALSE, "7", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-03";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.48)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(150), BigDecimal.valueOf(200), BigDecimal.valueOf(250), BigDecimal.valueOf(450), BigDecimal.valueOf(450), BigDecimal.valueOf(50), BigDecimal.valueOf(100));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT), "YieldableCost", 1, -200, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals("NNNYNNN", differentialsDecisions.get(0).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_FixedRate_RateAdjWith_PostingRule_1_NetTypeAmountYC_PostingRule_2_NetTypePercentYV_QFPLOSCalculationsBasedOnDOWFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, FALSE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0),};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(50), BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(50));

        //per stay
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -10, 1);
        //per night
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanStartDate, YIELDABLE_VALUE, 2, 20, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -200, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 20, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -50, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }


    @Test
    @Disabled
    public void testQualifiedFPLOS_RateOfDayProperty_ForRateDerivedValueOfBAR_WithRateAdj_PostingRule_1_NetTypeAmountYC_PostingRule_2_NetTypeAmountYV_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.2), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(BigDecimal.valueOf(1075.81));
        rateUnqualifiedDetails.setMonday(BigDecimal.valueOf(1103.58));
        rateUnqualifiedDetails.setTuesday(BigDecimal.valueOf(1529.51));
        rateUnqualifiedDetails.setWednesday(BigDecimal.valueOf(1751.73));
        rateUnqualifiedDetails.setThursday(BigDecimal.valueOf(1196.18));
        rateUnqualifiedDetails.setFriday(BigDecimal.valueOf(1566.55));
        rateUnqualifiedDetails.setSaturday(BigDecimal.valueOf(1566.55));

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-500), BigDecimal.valueOf(-200), BigDecimal.valueOf(-200), BigDecimal.valueOf(-200), BigDecimal.valueOf(-200), BigDecimal.valueOf(-200), BigDecimal.valueOf(-200));

        //per stay
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_COST, 1, -4, 1);

        //per night
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 1, 50, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYYYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }


    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForRateDerivedValueOfBAR_WithRateAdj_MissingFor_LOS1_LOS4To8_PostingRule2_NetTypeAmountYC_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.02), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(BigDecimal.valueOf(1075.81));
        rateUnqualifiedDetails.setMonday(BigDecimal.valueOf(1103.58));
        rateUnqualifiedDetails.setTuesday(BigDecimal.valueOf(1529.51));
        rateUnqualifiedDetails.setWednesday(BigDecimal.valueOf(1751.73));
        rateUnqualifiedDetails.setThursday(BigDecimal.valueOf(1196.18));
        rateUnqualifiedDetails.setFriday(BigDecimal.valueOf(1566.55));
        rateUnqualifiedDetails.setSaturday(BigDecimal.valueOf(1566.55));

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800));

        //per night
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -999, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_CPProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule1_NetTypeAmount_Percent_YV_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2050-09-13";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(177.97606), BigDecimal.valueOf(134.67187), BigDecimal.valueOf(506.76502), BigDecimal.valueOf(5.6096), BigDecimal.valueOf(31.79406), BigDecimal.valueOf(223.11187), BigDecimal.valueOf(272.56282)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(150), BigDecimal.valueOf(-150), BigDecimal.valueOf(-300), BigDecimal.valueOf(-600), BigDecimal.valueOf(-300), BigDecimal.valueOf(-350), BigDecimal.valueOf(100));

        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), BigDecimal.valueOf(475.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), BigDecimal.valueOf(702.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), BigDecimal.valueOf(248.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), BigDecimal.valueOf(268.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), BigDecimal.valueOf(442.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), BigDecimal.valueOf(539.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), BigDecimal.valueOf(368.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), BigDecimal.valueOf(702.74));

        //per stay
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 1, 100, 1);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 100, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYNYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYNYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_CPProperty_ForRateDerivedValueOfBAR_WithRateAdjPostingRule1_NetTypeAmount_Percent_YV_DerivedFPLOSFlagIsSet_SRPAtTotalLevel_True() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2050-09-13";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(177.97606), BigDecimal.valueOf(134.67187), BigDecimal.valueOf(506.76502), BigDecimal.valueOf(5.6096), BigDecimal.valueOf(31.79406), BigDecimal.valueOf(223.11187), BigDecimal.valueOf(272.56282)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(150), BigDecimal.valueOf(-150), BigDecimal.valueOf(-300), BigDecimal.valueOf(-600), BigDecimal.valueOf(-300), BigDecimal.valueOf(-350), BigDecimal.valueOf(100));

        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), BigDecimal.valueOf(475.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), BigDecimal.valueOf(702.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), BigDecimal.valueOf(248.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), BigDecimal.valueOf(268.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), BigDecimal.valueOf(442.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), BigDecimal.valueOf(539.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), BigDecimal.valueOf(368.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), BigDecimal.valueOf(702.74));

        //per stay
        prepareQualifiedRatePlanAdjustmentData(qualifiedRateplanStartDate, qualifiedRateplanEndDate, YIELDABLE_VALUE, 1, 100, 1);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 6), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 100, 1);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYNYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYNYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_CPProperty_FinalBARNull() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2050-09-13";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(177.97606), BigDecimal.valueOf(134.67187), BigDecimal.valueOf(506.76502), BigDecimal.valueOf(5.6096), BigDecimal.valueOf(31.79406), BigDecimal.valueOf(223.11187), BigDecimal.valueOf(272.56282)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(150), BigDecimal.valueOf(-150), BigDecimal.valueOf(-300), BigDecimal.valueOf(-600), BigDecimal.valueOf(-300), BigDecimal.valueOf(-350), BigDecimal.valueOf(100));

        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), BigDecimal.valueOf(475.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), BigDecimal.valueOf(702.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), BigDecimal.valueOf(248.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), BigDecimal.valueOf(268.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), BigDecimal.valueOf(442.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), BigDecimal.valueOf(539.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), BigDecimal.valueOf(368.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), null);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(1, differentialsDecisions.size());
        assertEquals("NYNNYYYY", differentialsDecisions.get(0).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_CPProperty_FinalBARNull_NoQualifiedRatePlans() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2050-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(177.97606), BigDecimal.valueOf(134.67187), BigDecimal.valueOf(506.76502), BigDecimal.valueOf(5.6096), BigDecimal.valueOf(31.79406), BigDecimal.valueOf(223.11187), BigDecimal.valueOf(272.56282)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);


        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), null);
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), null);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    @Disabled
    public void testQualifiedFPLOS_RateOfDayProperty_ForRates_Fixed_DerivedValueOfBAR_DerivedPercentOfBAR_AccomCapacityIsZero_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 0, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    @Disabled
    public void testQualifiedFPLOS_RateOfDayProperty_BARDecisionsArePresent_NoRatePlanConfigured_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());

    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_BARDecisionsNOTPresent_RatePlanConfigured_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size(), "FPLOS generated for Fixed Rate Plan");
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_CPProperty_ForRateDerivedValueOfBAR_WithRateAdj_MissingFor_LOS1_LOS7_LOS8_PostingRule2_NetTypeCombined_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2050-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(177.97606), BigDecimal.valueOf(134.67187), BigDecimal.valueOf(506.76502), BigDecimal.valueOf(5.6096), BigDecimal.valueOf(31.79406), BigDecimal.valueOf(223.11187), BigDecimal.valueOf(272.56282)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), BigDecimal.valueOf(475.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), BigDecimal.valueOf(702.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), BigDecimal.valueOf(248.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), BigDecimal.valueOf(268.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), BigDecimal.valueOf(442.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), BigDecimal.valueOf(539.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), BigDecimal.valueOf(368.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), BigDecimal.valueOf(768.74));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), BigDecimal.valueOf(702.74));

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(150), BigDecimal.valueOf(-150), BigDecimal.valueOf(-300), BigDecimal.valueOf(-600), BigDecimal.valueOf(-300), BigDecimal.valueOf(-350), BigDecimal.valueOf(100));

        //per night
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 100, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -555, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 999, 2);
        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 5), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -40, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NNNNYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNYYYYYY", differentialsDecisions.get(2).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_RateOfDayProperty_ForRateDerivedValueOfBAR_BARDecisionsNOTPresentForSomeDays_DerivedFPLOSFlagIsSet() throws Exception {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2013-09-15";
        updateCaughtUpDate(propertyID, caughtUpDate);
        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        BigDecimal[] lrvArray = {BigDecimal.valueOf(0), BigDecimal.valueOf(0.02), BigDecimal.valueOf(58.94), BigDecimal.valueOf(1487.13), BigDecimal.valueOf(55.49), BigDecimal.valueOf(1410.69), BigDecimal.valueOf(1208.52), BigDecimal.valueOf(0)};
        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 8), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.DEFAULT_DATE_FORMAT);
        final RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F, propertyID);
        rateUnqualifiedDetails.setSunday(BigDecimal.valueOf(1075.81));
        rateUnqualifiedDetails.setMonday(BigDecimal.valueOf(1103.58));
        rateUnqualifiedDetails.setTuesday(BigDecimal.valueOf(1529.51));
        rateUnqualifiedDetails.setWednesday(BigDecimal.valueOf(1751.73));
        rateUnqualifiedDetails.setThursday(BigDecimal.valueOf(1196.18));
        rateUnqualifiedDetails.setFriday(BigDecimal.valueOf(1566.55));
        rateUnqualifiedDetails.setSaturday(BigDecimal.valueOf(1566.55));

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 1, BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800), BigDecimal.valueOf(-800));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(2, differentialsDecisions.size(), "BAR Decisions are from 15 through 23 , hence FPLOS generated for 2 days");
        assertEquals("YYYYYYNY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYYYYNNY", differentialsDecisions.get(1).getFplos());

    }

    @Test
    public void testQualifiedFPLOS_CPProperty_ForRates_Fixed_DerivedValueOfBAR_DerviedPercentOfBAR_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_RATEOFDAY, TRUE, TRUE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);

        addDecisionBARCPOutput(accomTypeID, new LocalDate(startDate), new BigDecimal(6 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 1)), new BigDecimal(7 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 2)), new BigDecimal(1 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 3)), new BigDecimal(2 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 4)), new BigDecimal(3 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 5)), new BigDecimal(4 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 6)), new BigDecimal(5 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 7)), new BigDecimal(6 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 8)), new BigDecimal(7 * 6.0F));
        addDecisionBARCPOutput(accomTypeID, new LocalDate(DateUtil.addDaysToDate(startDate, 9)), new BigDecimal(8 * 6.0F));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(9, differentialsDecisions.size());
        assertEquals("YNYNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NYNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("YNNNNNNY", differentialsDecisions.get(2).getFplos());
        assertEquals("YYNNNNNN", differentialsDecisions.get(3).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(5).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(6).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(7).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(8).getFplos());
    }

    private void addDecisionBARCPOutput(int accomType, LocalDate arrivalDate, BigDecimal finalBar) {
        final int propertyId = ((WorkContextType) workContext()).getPropertyId();
        // Set the default values
        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        queries.append("INSERT INTO [CP_Decision_Bar_Output] " +
                "([Property_ID],[Product_ID],[Decision_Reason_Type_ID],[Decision_ID],[LOS]," +
                "[Rooms_Only_BAR],[Final_BAR],[Optimal_BAR],[Pretty_BAR],[Accom_Type_ID],[Arrival_DT],[Override])" +
                " VALUES ( " + propertyId + "," + product.getId() + "," + DecisionReasonType.ALL_IS_WELL.getId() + "," + 2 + ","
                + "-1" + "," + finalBar + "," + finalBar + "," + BigDecimal.ONE + "," + BigDecimal.ONE + "," + accomType + ",'" + arrivalDate + "','" + "NONE" + "'" + ")");
    }

    @Test
    public void testQualifiedFPLOS_RatePlansConfiguredForAllRateTypes_isDerivedTrue_BarByLosProperty_BarDecisionsPresent_GeneratesDecisionsForAllQualifiedRates() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputDataForBarByLosProperty(accomClassID, startDate, rateUnqualified, DecisionReasonType.ALL_IS_WELL, 3);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(9, differentialsDecisions.size());
        assertEquals("YNYYYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("NYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(2).getFplos());
        assertEquals("YYNNNNNN", differentialsDecisions.get(3).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(5).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(6).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(7).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(8).getFplos());
    }

    @Test
    public void testQualifiedFPLOS_RatePlansConfiguredForAllRateTypes_isDerivedTrue_BarByLosProperty_BarDecisionsNotPresent_GeneratesDecisionsOnlyForFixedQualifiedRates() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    private void prepareDecisionBarOutputData(int accomClassID, Date startDate, RateUnqualified rateUnqualified) {
        IntStream.range(0, 10).forEach(i -> UniqueDecisionBarOutputCreator.createDecisionBarOutput(DateUtil.addDaysToDate(startDate, i), -1, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified.getName(), null, null));
    }

    private void prepareDecisionBarOutputDataForBarByLosProperty(int accomClassID, Date startDate, RateUnqualified rateUnqualified, DecisionReasonType decisionReasonType, int dayRange) {
        IntStream.range(0, dayRange).forEach(days ->
                IntStream.range(1, 9).forEach(los ->
                        UniqueDecisionBarOutputCreator.createDecisionBarOutput(DateUtil.addDaysToDate(startDate, days), los, accomClassID, BARDECISIONOVERRIDE_NONE, rateUnqualified.getName(), null, null, decisionReasonType)
                )
        );
    }

    private RateUnqualified prepareRateUnqualifiedData(int accomTypeID, Date startDate, Date endDateForUnqualifiedRatePlan) {
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDateForUnqualifiedRatePlan);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F);
        return rateUnqualified;
    }

    private RateUnqualified prepareRateUnqualifiedDataWithName(int accomTypeID, Date startDate, Date endDateForUnqualifiedRatePlan, String ratePlanName) {
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDateAndName(startDate, endDateForUnqualifiedRatePlan, ratePlanName);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F);
        return rateUnqualified;
    }

    private void prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(int propertyID, int accomTypeID, String qualifiedRateplanStartDate, String qualifiedRateplanEndDate) {
        String rateQualifiedInsertQuery = "SET IDENTITY_INSERT [Rate_Qualified] ON" +
                " INSERT INTO [Rate_Qualified]" +
                " ([Rate_Qualified_ID],[File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Start_Date_DT],[End_Date_DT]" +
                "   ,[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" +
                "   ,[Status_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Rate_Qualified_Type_Id])" +
                " VALUES (1,(select MAX(File_Metadata_ID) from File_Metadata), " + propertyID + ", 'QR1', 'qr1', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, 1)" +
                ", (2,(select MAX(File_Metadata_ID) from File_Metadata), " + propertyID + ", 'QR2', 'qr2', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, 2)" +
                ", (3,(select MAX(File_Metadata_ID) from File_Metadata), " + propertyID + ", 'QR3', 'qr3', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, 3)" +
                "SET IDENTITY_INSERT [Rate_Qualified] OFF;";

        queries.append(rateQualifiedInsertQuery);

        String rateQualifiedDetailsInsertQuery = "INSERT INTO [Rate_Qualified_Details]" +
                " ([Rate_Qualified_ID],[Accom_Type_ID],[Start_Date_DT],[End_Date_DT],[Sunday],[Monday],[Tuesday],[Wednesday],[Thursday],[Friday],[Saturday],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM])\n" +
                " VALUES (1," + accomTypeID + ",'" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "', 40 ,5, 5, 5, 5, 5, -5, 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP)" +
                ", (2," + accomTypeID + ",'" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "', 5, 5, 5, 5, 5, 20, 5, 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP)" +
                ", (3," + accomTypeID + ",'" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "', 40, 40, 40, 40, 40, 40, 38, 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP);";

        queries.append(rateQualifiedDetailsInsertQuery);
    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_AccomCapacityZero_ForRates_Fixed_DerivedValueOfBAR_DerivedPercentOfBAR_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 0, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputDataForBarByLosProperty(accomClassID, startDate, rateUnqualified, DecisionReasonType.NO_RATES_DEFINED, 3);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_BARDecisionsArePresent_NoRatePlanConfigured_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputDataForBarByLosProperty(accomClassID, startDate, rateUnqualified, DecisionReasonType.NO_RATES_DEFINED, 3);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());

    }

    @Test
    public void testQualifiedFPLOS_BARBYLOSProperty_BARDecisionsAreNOTPresent_RatePlanConfigured_DerivedFPLOSFlagIsSet() throws ParseException {
        resetUp();

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, TRUE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, 10, 40, 0);

        prepareQualifiedRatePlanDataForDifferentQualifiedRateTypeIds(propertyID, accomTypeID, qualifiedRateplanStartDate, qualifiedRateplanEndDate);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size(), "FPLOS generated for Fixed rate plan");
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testBarFPLOSQualifiedDecisionForDecisionUploadWindow() {
        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        fplosQualifiedRecommendationService.deriveFPLOS();

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        queries.append("UPDATE File_Metadata SET SnapShot_DT = DATEADD(D,1,SnapShot_DT) WHERE Record_Type_ID = 3 AND Process_Status_ID = 13 AND IsBDE = 1 AND property_Id = 6 ");
        fplosQualifiedRecommendationService.deriveFPLOS();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String updateDate = simpleDateFormat.format(DateUtil.addDaysToDate(startDate, 2));
        queries.append("UPDATE Decision_Qualified_FPLOS SET FPLOS = \'NNNNNNNN\' WHERE Rate_Qualified_ID = 21 AND Arrival_DT = '" + updateDate + "'  AND Accom_Type_ID = 12 ");
        queries.append("UPDATE PACE_Qualified_FPLOS SET FPLOS = \'NNNNNNNN\' WHERE Rate_Qualified_ID = 21 AND  Accom_Type_ID = 12 AND Arrival_DT = '" + updateDate + "' AND Decision_ID = (SELECT MAX(Decision_ID) FROM Decision_Qualified_FPLOS )");
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        differentialsDecisions.forEach(fplosDecision -> assertTrue(fplosDecision.getArrivalDate().equals(endDate) || simpleDateFormat.format(fplosDecision.getArrivalDate()).equals(updateDate)));

    }

    private FPLOSQualifiedDecisionService getFplosQualifiedDecisionService(PacmanConfigParamsService pacmanConfigParamsService, DateService dateService) {
        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = new FPLOSQualifiedDecisionService();
        fplosQualifiedDecisionService.configService = pacmanConfigParamsService;
        fplosQualifiedDecisionService.crudService = tenantCrudService();
        fplosQualifiedDecisionService.dateServiceLocal = dateService;
        fplosQualifiedDecisionService.limitTotalService = limitTotalService;
        return fplosQualifiedDecisionService;
    }

    private FPLOSQualifiedDecisionService getFPLosQaulifiedDecisionService() {
        DateService dateService = getDateService();
        dateService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE), dateService);
        return fplosQualifiedDecisionService;

    }

    private DateService getDateService() {
        DateService dateService = DateService.createTestInstance();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        dateService.setCrudService(tenantCrudService());
        return dateService;
    }

    private void createQualifiedDecisions() {
        DateService dateService = getDateService();
        dateService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET))).thenReturn("false");
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        fplosQualifiedRecommendationService.deriveFPLOS();
    }

    private AccommodationService getAccomodationService() {
        AccommodationService accommodationService = new AccommodationService();
        accommodationService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));
        accommodationService.setTenantCrudService(tenantCrudService());
        return accommodationService;
    }

    private OperaDecisionService getOperaDecisionService() {
        DateService dateService = getDateService();
        dateService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));

        FPLOSByRoomTypeDecisionService fplosByRoomTypeDecisionService = new FPLOSByRoomTypeDecisionService();
        fplosByRoomTypeDecisionService.setCrudService(tenantCrudService());

        OperaDecisionService operaDecisionService = new OperaDecisionService();
        inject(operaDecisionService, "fplosQualifiedDecisionService", getFPLosQaulifiedDecisionService());
        operaDecisionService.setTenantCrudService(tenantCrudService());
        operaDecisionService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));
        operaDecisionService.setGlobalCrudService(globalCrudService());
        operaDecisionService.setAccommodationService(getAccomodationService());
        operaDecisionService.setFplosByRoomTypeDecisionService(fplosByRoomTypeDecisionService);
        operaDecisionService.setCrudService(tenantCrudService());
        operaDecisionService.setPropertyService(propertyService);
        operaDecisionService.setDateService(dateService);
        inject(operaDecisionService, "decisionConfigurationService", decisionConfigurationService);
        inject(operaDecisionService, "decisionUploadDateToExternalSystemRepository", decisionUploadDateToExternalSystemRepository);

        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(pacmanConfigParamsService);
        operaDecisionService.setConfigParameterNameService(configParameterNameService);

        return operaDecisionService;
    }

    private void updateDecisionRecords(String updateDate) {
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Decision_Qualified_FPLOS SET FPLOS = \'NNNNNNNN\' WHERE Rate_Qualified_ID = 21 AND Arrival_DT = '" + updateDate + "'  AND Accom_Type_ID = 10 ");
        tenantCrudService().executeUpdateByNativeQuery("UPDATE PACE_Qualified_FPLOS SET FPLOS = \'NNNNNNNN\' WHERE Rate_Qualified_ID = 21 AND  Accom_Type_ID = 10 AND Arrival_DT = '" + updateDate + "' AND Decision_ID = (SELECT MAX(Decision_ID) FROM Decision_Qualified_FPLOS )");
    }

    private void updateSnapShotDate() {
        queries.append("UPDATE File_Metadata SET SnapShot_DT = DATEADD(D,1,SnapShot_DT) WHERE Record_Type_ID = 3 AND Process_Status_ID = 13 AND IsBDE = 1 AND property_Id = 6 ");
    }

    @Test
    public void testFplosByRateCodeDecisionForDecisionUploadWindow() {
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(false);
        createQualifiedDecisions();
        DateService dateService = getDateService();
        dateService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));

        OperaAgentDecisionService operaAgentDecisionService = new OperaAgentDecisionService();
        operaAgentDecisionService.setCrudService(tenantCrudService());
        operaAgentDecisionService.setOperaDecisionService(getOperaDecisionService());
        operaAgentDecisionService.setAccommodationService(getAccomodationService());
        operaAgentDecisionService.setRemoteTaskService(remoteTaskService);
        operaAgentDecisionService.setConfigParamsService(pacmanConfigParamsService);

        operaAgentDecisionService.getFplosByRateCodeDecisions(6, OPERA, null);
        queries.append("UPDATE opera.Decision_Update_Date_To_External_System SET Last_Update_DTTM = CreateDate_DTTM  WHERE Decision_Name = 'FplosByRateCode' AND External_System_Name = 'opera'");

        Date startDate = dateService.getCaughtUpDate();
        updateSnapShotDate();
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        createQualifiedDecisions();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String updateDate = simpleDateFormat.format(DateUtil.addDaysToDate(startDate, 2));
        updateDecisionRecords(updateDate);
        List<FplosDecision> differentialsDecisions = operaAgentDecisionService.getFplosByRateCodeDecisions(6, OPERA, null);
        final String finalEndDate = simpleDateFormat.format(dateService.getDecisionUploadWindowEndDate());
        List<String> arrivalDates = differentialsDecisions.stream().map(FplosDecision::getArrivalDate).collect(Collectors.toList());
        assertTrue(arrivalDates.contains(finalEndDate));
        assertTrue(arrivalDates.contains(updateDate));
    }

    @Test
    public void testFplosByRateCodeByRoomTypeDecisionForDecisionUploadWindow() {
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET))).thenReturn("false");
        createQualifiedDecisions();
        DateService dateService = getDateService();
        dateService.setConfigParamsService(getPacmanConfigParamsService("3", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", FALSE, FALSE, FALSE));

        OperaAgentDecisionService operaAgentDecisionService = new OperaAgentDecisionService();
        operaAgentDecisionService.setCrudService(tenantCrudService());
        operaAgentDecisionService.setOperaDecisionService(getOperaDecisionService());
        operaAgentDecisionService.setAccommodationService(getAccomodationService());
        operaAgentDecisionService.setRemoteTaskService(remoteTaskService);
        operaAgentDecisionService.setConfigParamsService(pacmanConfigParamsService);

        operaAgentDecisionService.getFplosByRateCodeByRoomTypeDecisions(6, OPERA, null);
        queries.append("UPDATE opera.Decision_Update_Date_To_External_System SET Last_Update_DTTM = CreateDate_DTTM  WHERE Decision_Name = 'FplosByRateCodeByRoomType' AND External_System_Name = 'opera'");

        Date startDate = dateService.getCaughtUpDate();
        updateSnapShotDate();
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        createQualifiedDecisions();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String updateDate = simpleDateFormat.format(DateUtil.addDaysToDate(startDate, 2));
        updateDecisionRecords(updateDate);
        List<FplosDecision> differentialsDecisions = operaAgentDecisionService.getFplosByRateCodeByRoomTypeDecisions(6, OPERA, null);
        final String finalEndDate = simpleDateFormat.format(dateService.getDecisionUploadWindowEndDate());
        List<String> arrivalDates = differentialsDecisions.stream().map(FplosDecision::getArrivalDate).collect(Collectors.toList());
        assertTrue(arrivalDates.contains(finalEndDate));
        assertTrue(arrivalDates.contains(updateDate));
    }

    private PacmanConfigParamsService getPacmanConfigParamsService(final String decisionUploadWindowBDE, final String barDecision, final String isDerivedQualifiedRatePlanEnabled,
                                                                   String isCPEnabled, String qualifiedFPLOSMaxLOS, String srpFPLOSAtTotalLevel, String closeHighestBar,
                                                                   String lraEnabled) {

        return new PacmanConfigParamsService() {
            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, ExternalSystem syste) {
                return (T) getParameterValue(configParamName.value(syste.getCode()));
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)) {
                    return (T) Boolean.FALSE;
                }
                if (configParamName.equals(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)) {
                    return (T) srpFPLOSAtTotalLevel;
                }
                return null;
            }

            @Override
            public boolean getBooleanParameterValue(String parameterName) {
                return "true".equalsIgnoreCase(getParameterValue(parameterName));
            }

            @Override
            public String getParameterValue(String parameterName) {
                switch (parameterName) {
                    case APPLY_VARIABLE_DECISION_WINDOW:
                    case PACMAN_INTEGRATION_RATCHET_USEEXTENDEDSTAYMAPPING:
                    case OPEN_LV0_ENABLED:
                    case IS_EXTENDED_STAY:
                        return FALSE;
                    case LRA_ENABLED:
                        return lraEnabled;
                    case DECISION_UPLOAD_WINDOW_BDE:
                        return decisionUploadWindowBDE;
                    case OPTMIZATION_WINDOW_BDE:
                        return "100";
                    case QUALIFIED_FPLOS_MAX_LOS:
                        return qualifiedFPLOSMaxLOS;
                    case OPERA_FPLOS_BY_RATE_CODE_BY_ROOM_TYPE_UPLOADTYPE:
                    case OPERA_FPLOS_BY_RATE_CODE_UPLOADTYPE:
                        return DIFFERENTIAL;
                    case PACMAN_FEATURE_DERIVED_QUALIFIED_RATE_PLAN_ENABLED:
                        return isDerivedQualifiedRatePlanEnabled;
                    case BAR_DECISION:
                        return barDecision;
                    case PACMAN_FEATURE_CONTINUOUS_PRICING_ENABLED:
                        return isCPEnabled;
                    case MAX_LOS:
                        return "7";
                    case SRP_FPLOS_AT_TOTAL_LEVEL:
                        return srpFPLOSAtTotalLevel;
                    case CLOSE_HIGHEST_BAR_ENABLED:
                        return closeHighestBar;
                    case PACMAN_FEATURE_SERVICING_COST_BY_LOSENABLE:
                        return FALSE;
                    case FPLOS_FORCE_LEGACY_CARDINALITY_ESTIMATION:
                        return TRUE;
                    default:
                        return null;
                }
            }
        };
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesAvailableForMasterClassOnly() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 10, 100, 15);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRateplanStartDate, qualifiedRateplanEndDate, BigDecimal.valueOf(32)
                , BigDecimal.valueOf(43), BigDecimal.valueOf(52), BigDecimal.valueOf(23), BigDecimal.valueOf(62)
                , BigDecimal.valueOf(58), BigDecimal.valueOf(42));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYYYNYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesAvailableForNonMasterClassOnly() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDDOUBLE = 6;
        final int accomTypeIDQUEEN = 7;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDOUBLE, startDate, 10, 40, 10);
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDQUEEN, startDate, 10, 100, 15);
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 100, 15);


        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDOUBLE, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(55), BigDecimal.valueOf(20), BigDecimal.valueOf(70)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanDetails(1, accomTypeIDQUEEN, qualifiedRateplanStartDate, qualifiedRateplanEndDate, BigDecimal.valueOf(32)
                , BigDecimal.valueOf(37), BigDecimal.valueOf(57), BigDecimal.valueOf(17), BigDecimal.valueOf(72)
                , BigDecimal.valueOf(57), BigDecimal.valueOf(37));

        prepareQualifiedRatePlanData(2, propertyID, accomTypeIDDLX, "QR2", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(6, differentialsDecisions.size());
        assertEquals("YYYYYNYY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNYNYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesAvailableForMasterAndNonMasterClass() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;
        final int accomTypeIDDOUBLE = 6;
        final int accomTypeIDQUEEN = 7;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("20", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRatePlanFirstSeasonStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final String qualifiedRatePlanFirstSeasonEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        final String qualifiedRatePlanSecondSeasonStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);
        final String qualifiedRatePlanSecondSeasonEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 19), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareLRVList(39, 21);
        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 20, 40, 10);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 20, 100, 15);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRatePlanFirstSeasonStartDate, qualifiedRatePlanFirstSeasonEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRatePlanFirstSeasonStartDate, qualifiedRatePlanFirstSeasonEndDate, BigDecimal.valueOf(32)
                , BigDecimal.valueOf(43), BigDecimal.valueOf(52), BigDecimal.valueOf(23), BigDecimal.valueOf(62)
                , BigDecimal.valueOf(58), BigDecimal.valueOf(42));

        prepareQualifiedRatePlanDetails(1, accomTypeIDDOUBLE, qualifiedRatePlanSecondSeasonStartDate, qualifiedRatePlanSecondSeasonEndDate, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(55), BigDecimal.valueOf(20), BigDecimal.valueOf(70)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanDetails(1, accomTypeIDQUEEN, qualifiedRatePlanSecondSeasonStartDate, qualifiedRatePlanSecondSeasonEndDate, BigDecimal.valueOf(32)
                , BigDecimal.valueOf(37), BigDecimal.valueOf(57), BigDecimal.valueOf(17), BigDecimal.valueOf(72)
                , BigDecimal.valueOf(57), BigDecimal.valueOf(37));

        prepareQualifiedRatePlanData(2, propertyID, accomTypeIDDLX, "QR2", qualifiedRatePlanSecondSeasonStartDate, qualifiedRatePlanSecondSeasonEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(9, differentialsDecisions.size());
        assertEquals("YYYYYNYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(3).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(5).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesHavingZeroRemainingCapacity() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareLRVList(39, 11);
        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 200);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 10, 100, 200);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRateplanStartDate, qualifiedRateplanEndDate, BigDecimal.valueOf(32)
                , BigDecimal.valueOf(43), BigDecimal.valueOf(52), BigDecimal.valueOf(23), BigDecimal.valueOf(62)
                , BigDecimal.valueOf(58), BigDecimal.valueOf(42));

        System.setProperty("pacman.useRemCapFix", TRUE);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYYYNYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesWithMissingAccomActivity() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 5, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesWithRateAdjustment() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(30)
                , BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -10, 1);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -10, 1);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 1, -10, 2);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 1, 5, 2);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_COST, 2, -10, 2);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 3), DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 4), DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 2, 5, 2);

        prepareQualifiedRatePlanAdjustmentData(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 8), DateUtil.DEFAULT_DATE_FORMAT)
                , DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT), YIELDABLE_VALUE, 3, 100, 2);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("YNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRates_LV0() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int nonMasterClassId = 2;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        prepareLRVList(39, 21);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, nonMasterClassId, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 10, 100, 15);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3
                , BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanData(2, propertyID, accomTypeIDKING, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3
                , BigDecimal.valueOf(32), BigDecimal.valueOf(43), BigDecimal.valueOf(52), BigDecimal.valueOf(23)
                , BigDecimal.valueOf(62), BigDecimal.valueOf(58), BigDecimal.valueOf(42));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        prepareRateUnqualifiedDataWithName(accomTypeIDDLX, startDate, endDate, "BAR0");

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(6, differentialsDecisions.size());
        assertEquals("YYYYYNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
        assertEquals("YYYYYNYY", differentialsDecisions.get(3).getFplos());
        assertEquals("YNNNNNYN", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNYNN", differentialsDecisions.get(5).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRates_LV0_LRAEnabled() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int nonMasterClassId = 2;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, TRUE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        prepareLRVList(39, 21);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, nonMasterClassId, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 10, 100, 15);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3
                , BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(60)
                , BigDecimal.valueOf(55), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanData(2, propertyID, accomTypeIDDLX, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3
                , BigDecimal.valueOf(32), BigDecimal.valueOf(43), BigDecimal.valueOf(52), BigDecimal.valueOf(23)
                , BigDecimal.valueOf(62), BigDecimal.valueOf(58), BigDecimal.valueOf(42));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        RateUnqualified rateUnqualified = prepareRateUnqualifiedDataWithName(accomTypeIDDLX, startDate, endDate, "BAR0");

        prepareDecisionBarOutputDataForBarByLosProperty(masterAccomClassID, startDate, rateUnqualified, DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA, 3);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(6, differentialsDecisions.size());
        assertEquals("NNNNNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(2).getFplos());
        assertEquals("YYYYYNNY", differentialsDecisions.get(3).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(4).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(5).getFplos());

    }

    private void updateAccomType(int accomClassId, int accomTypeID) {
        String updateAccomTypeQuery = "update Accom_Type set Accom_Class_ID = " + accomClassId + " where Accom_Type_ID = " + accomTypeID;
        queries.append(updateAccomTypeQuery);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS() throws Exception {
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getDateForNextMonth(28);

        service.processChunk(startDate, endDate);
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        assertFalse(decisions.isEmpty());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP() throws Exception {
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getDateForNextMonth(28);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET))).thenReturn("false");
        service.processChunk(startDate, endDate);
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        assertFalse(decisions.isEmpty());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_False() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", FALSE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);

        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 27)));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_True() throws Exception {
        reset(pacmanConfigParamsService);
        reset(dateService);
        stageDateServiceMock_15();
        stagePacmanConfigParamsServiceMockfplosBySRP_by_VDE_True();
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", TRUE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);

        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 14)));
        assertThat(DateUtil.addDaysToDate(startDate, 15).compareTo(d1), is(1));

    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_toggle_set_to_false_los10() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", FALSE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);

        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 27)));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
        assertThat(decFPlos.getFplos().length(), is(10));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_toggle_set_to_false_los_7() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", FALSE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("7");
        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 27)));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
        assertThat(decFPlos.getFplos().length(), is(7));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_toggle_set_to_true() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", TRUE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);

        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 27)));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_toggle_set_to_true_los_10() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", TRUE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);

        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(DateUtil.addDaysToDate(startDate, 27), is(d1));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
        assertThat(decFPlos.getFplos().length(), is(10));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_by_VDE_toggle_set_to_true_los_7() throws Exception {
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", TRUE);
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.addDaysToDate(startDate, 28);
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("7");
        service.deriveFPLOS();
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        assertNotNull(decisions);
        DecisionQualifiedFPLOS decFPlos = decisions.get(decisions.size() - 1);
        Date d1 = new Date(decFPlos.getArrivalDate().getTime());
        assertThat(d1, is(DateUtil.addDaysToDate(startDate, 27)));
        assertThat(DateUtil.addDaysToDate(startDate, 28).compareTo(d1), is(1));
        assertThat(decFPlos.getFplos().length(), is(7));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOSExtendedStay() throws Exception {
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET))).thenReturn("false");
        Date endDate = DateUtil.getDateForNextMonth(28);
        service.processChunk(startDate, endDate);
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());
        assertNotNull(decisions);
        assertFalse(decisions.isEmpty());

        // Every FPLOS where the rate is SRP0 should have the last position set to Y
        for (DecisionQualifiedFPLOS decision : decisions) {
            if (decision.getRateQualifiedId() == 22) {
                assertTrue(decision.getFplos().endsWith("Y"));
            }
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_LRAEnabled() throws Exception {
        reset(pacmanConfigParamsService);

        stagePacmanConfigParamsServiceMock_LRA_False();
        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getDateForNextMonth(28);

        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        Integer accomClassID = masterClass.getId();
        queries.append("update Rate_Qualified set Rate_Code_Name= 'LV0' where Rate_Code_Name = 'BAR0'");
        queries.append("update Rate_UnQualified set Rate_Code_Name= 'LV0' where Rate_Code_Name = 'BAR0'");
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        RateUnqualified lv0Unqualified = tenantCrudService().find(RateUnqualified.class, tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", "LV0").parameters()));

        Date arrivalDate1 = DateUtil.addDaysToDate(startDate, 10);
        Integer lengthOfStay1 = 2;
        DecisionBAROutput dbo1 = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate1).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay1).parameters());

        int orginal_reasonType1 = dbo1.getReasonTypeId();
        RateUnqualified original_rateQualified1 = dbo1.getRateUnqualified();

        dbo1.setReasonTypeId(6);
        dbo1.setRateUnqualified(lv0Unqualified);
        tenantCrudService().save(dbo1);

        Date arrivalDate2 = DateUtil.addDaysToDate(startDate, 15);
        Integer lengthOfStay2 = 4;
        DecisionBAROutput dbo2 = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate2).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay2).parameters());

        int orginal_reasonType2 = dbo2.getReasonTypeId();
        RateUnqualified original_rateQualified2 = dbo2.getRateUnqualified();
        dbo2.setReasonTypeId(6);
        dbo2.setRateUnqualified(lv0Unqualified);
        tenantCrudService().save(dbo2);

        tenantCrudService().flushAndClear();

        service.processChunk(startDate, endDate);
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        for (DecisionQualifiedFPLOS d : decisions) {
            if (d.getArrivalDate().compareTo(arrivalDate1) == 0 && d.getAccomTypeId() == 10 && d.getRateQualifiedId() == 22) {
                String expected_fplos = d.getFplos().substring(0, lengthOfStay1 - 1) + "N" + d.getFplos().substring(lengthOfStay1, d.getFplos().length());
                assertEquals(d.getFplos(), expected_fplos);
            }
            if (d.getArrivalDate().compareTo(arrivalDate2) == 0 && d.getAccomTypeId() == 10 && d.getRateQualifiedId() == 22) {
                String expected_fplos = d.getFplos().substring(0, lengthOfStay2 - 1) + "N" + d.getFplos().substring(lengthOfStay2, d.getFplos().length());
                assertEquals(d.getFplos(), expected_fplos);
            }
        }
        DecisionBAROutput dbo1_org = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate1).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay1).parameters());

        dbo1_org.setReasonTypeId(orginal_reasonType1);
        dbo1_org.setRateUnqualified(original_rateQualified1);
        tenantCrudService().save(dbo1_org);

        DecisionBAROutput dbo2_org = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate2).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay2).parameters());

        dbo2_org.setReasonTypeId(orginal_reasonType2);
        dbo2_org.setRateUnqualified(original_rateQualified2);
        tenantCrudService().save(dbo2_org);

        assertNotNull(decisions);
        assertFalse(decisions.isEmpty());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testDeriveFPLOS_fplosBySRP_LRAEnabled() throws Exception {
        reset(pacmanConfigParamsService);
        stagePacmanConfigParamsServiceMockfplosBySRP_by_VDE_True_LRA_true();

        PacmanWorkContextHelper.setPropertyId(6);
        Date startDate = DateUtil.getFirstDayOfNextMonth();
        Date endDate = DateUtil.getDateForNextMonth(28);

        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        Integer accomClassID = masterClass.getId();

        queries.append("update Rate_Qualified set Rate_Code_Name= 'LV0' where Rate_Code_Name = 'BAR0'");
        queries.append("update Rate_UnQualified set Rate_Code_Name= 'LV0' where Rate_Code_Name = 'BAR0'");
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        RateUnqualified lv0Unqualified = tenantCrudService().find(RateUnqualified.class, tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", "LV0").parameters()));

        Date arrivalDate1 = DateUtil.addDaysToDate(startDate, 12);
        Integer lengthOfStay1 = 3;
        DecisionBAROutput dbo1 = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate1).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay1).parameters());

        int orginal_reasonType1 = dbo1.getReasonTypeId();
        RateUnqualified original_rateQualified1 = dbo1.getRateUnqualified();

        dbo1.setReasonTypeId(6);
        dbo1.setRateUnqualified(lv0Unqualified);
        tenantCrudService().save(dbo1);

        Date arrivalDate2 = DateUtil.addDaysToDate(startDate, 18);
        Integer lengthOfStay2 = 5;
        DecisionBAROutput dbo2 = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate2).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay2).parameters());

        int orginal_reasonType2 = dbo2.getReasonTypeId();
        RateUnqualified original_rateQualified2 = dbo2.getRateUnqualified();

        dbo2.setReasonTypeId(6);
        dbo2.setRateUnqualified(lv0Unqualified);
        tenantCrudService().save(dbo2);

        tenantCrudService().flushAndClear();

        service.processChunk(startDate, endDate);
        List<DecisionQualifiedFPLOS> decisions = tenantCrudService().findByNamedQuery(DecisionQualifiedFPLOS.BY_PROPERTY_ID_AND_DATE_RANGE_AND_ACTIVE_YIELDABLE_RATEPLAN, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters());

        for (DecisionQualifiedFPLOS d : decisions) {
            if (d.getArrivalDate().compareTo(arrivalDate1) == 0 && d.getAccomTypeId() == 10 && d.getRateQualifiedId() == 22) {
                String expected_fplos = d.getFplos().substring(0, lengthOfStay1 - 1) + "N" + d.getFplos().substring(lengthOfStay1, d.getFplos().length());
                assertEquals(d.getFplos(), expected_fplos);
            }
            if (d.getArrivalDate().compareTo(arrivalDate2) == 0 && d.getAccomTypeId() == 10 && d.getRateQualifiedId() == 22) {
                String expected_fplos = d.getFplos().substring(0, lengthOfStay2 - 1) + "N" + d.getFplos().substring(lengthOfStay2, d.getFplos().length());
                assertEquals(d.getFplos(), expected_fplos);
            }
        }

        DecisionBAROutput dbo1_org = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate1).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay1).parameters());

        dbo1_org.setReasonTypeId(orginal_reasonType1);
        dbo1_org.setRateUnqualified(original_rateQualified1);
        tenantCrudService().save(dbo1_org);

        DecisionBAROutput dbo2_org = tenantCrudService().findByNamedQuerySingleResult(DecisionBAROutput.BY_PROPERTYID_AND_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                QueryParameter.with("accomClassId", accomClassID).
                        and("arrivalDate", arrivalDate2).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        and("lengthOfStay", lengthOfStay2).parameters());

        dbo2_org.setReasonTypeId(orginal_reasonType2);
        dbo2_org.setRateUnqualified(original_rateQualified2);
        tenantCrudService().save(dbo2_org);

        assertNotNull(decisions);
        assertFalse(decisions.isEmpty());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelWithCloseHighestBarEnabled() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, TRUE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDDLX);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        //create and save close LV0 data.
        createAndSaveBarOverrides(masterAccomClassID, startDate, accomTypeIDDLX);

        Date lastUploadDate = new Date();
        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(130)
                , BigDecimal.valueOf(140), BigDecimal.valueOf(150), BigDecimal.valueOf(120), BigDecimal.valueOf(160)
                , BigDecimal.valueOf(155), BigDecimal.valueOf(140));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NYNYNYNY", differentialsDecisions.get(0).getFplos());
        // bolew assert data is cooked data for 1st and 8th LOS but in assert 8th position seen as Y only.
        // reason for this is MAXLOS; MAXLOS is 7 here, so any further LOS is considering decision of 7th position.
        assertEquals("NYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelWithCloseHighestBarEnabled_LRAenabledWithNoReasonTypeSet() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, TRUE, TRUE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDDLX);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        //create and save close LV0 data.
        createAndSaveBarOverrides(masterAccomClassID, startDate, accomTypeIDDLX);

        Date lastUploadDate = new Date();
        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(130)
                , BigDecimal.valueOf(140), BigDecimal.valueOf(150), BigDecimal.valueOf(120), BigDecimal.valueOf(160)
                , BigDecimal.valueOf(155), BigDecimal.valueOf(140));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NYNYNYNY", differentialsDecisions.get(0).getFplos());
        // bolew assert data is cooked data for 1st and 8th LOS but in assert 8th position seen as Y only.
        // reason for this is MAXLOS; MAXLOS is 7 here, so any further LOS is considering decision of 7th position.
        assertEquals("NYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelWithCloseHighestBarAndLRAEnabled() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int nonMasterClassId = 2;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, TRUE, TRUE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        prepareLRVList(39, 21);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, nonMasterClassId, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, 10, 100, 15);

        prepareQualifiedRatePlanData(2, propertyID, accomTypeIDDLX, LV0, qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3
                , BigDecimal.valueOf(132), BigDecimal.valueOf(143), BigDecimal.valueOf(152), BigDecimal.valueOf(123)
                , BigDecimal.valueOf(162), BigDecimal.valueOf(158), BigDecimal.valueOf(142));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 2, masterAccomClassID, BARDECISIONOVERRIDE_NONE, LV0, null, null, DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA);
        UniqueDecisionBarOutputCreator.createDecisionBarOutput(startDate, 4, masterAccomClassID, BARDECISIONOVERRIDE_NONE, LV0, null, null, DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA);

        //create and save close LV0 data.
        createAndSaveBarOverrides(masterAccomClassID, startDate, accomTypeIDDLX);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(3, differentialsDecisions.size());
        // here LOS 2 and 4 is N due to LRA enabled with reason type 6. And LOS 1,3,5,7 are N due to Close Highest Bar Enabled.
        assertEquals("NNNNNYNY", differentialsDecisions.get(0).getFplos());
        // bolew assert data is cooked data for 1st and 8th LOS but in assert 8th position seen as Y only.
        // reason for this is MAXLOS; MAXLOS is 7 here, so any further LOS is considering decision of 7th position.
        assertEquals("NYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());


    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelWithCloseHighestBarDisabled() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, FALSE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        //create and save close LV0 data.
        createAndSaveBarOverrides(masterAccomClassID, startDate, accomTypeIDDLX);

        Date lastUploadDate = new Date();
        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(130)
                , BigDecimal.valueOf(140), BigDecimal.valueOf(150), BigDecimal.valueOf(120), BigDecimal.valueOf(160)
                , BigDecimal.valueOf(155), BigDecimal.valueOf(140));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        prepareRateUnqualifiedDataWithName(accomTypeIDDLX, DateUtil.parseDate(qualifiedRateplanStartDate, DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT), "QR1");

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYYYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }


    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelWithCloseHighestBarEnabled_NoRHBData() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsService("10", BAR_DECISION_VALUE_LOS, FALSE, FALSE, "8", TRUE, TRUE, FALSE);
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";

        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();


        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, 10, 40, 10);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeIDDLX, "QR1", qualifiedRateplanStartDate, qualifiedRateplanEndDate, 3, BigDecimal.valueOf(130)
                , BigDecimal.valueOf(140), BigDecimal.valueOf(150), BigDecimal.valueOf(120), BigDecimal.valueOf(160)
                , BigDecimal.valueOf(155), BigDecimal.valueOf(140));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        prepareRateUnqualifiedDataWithName(accomTypeIDDLX, DateUtil.parseDate(qualifiedRateplanStartDate, DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT), "QR1");

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertEquals(3, differentialsDecisions.size());
        assertEquals("YYYYYYYY", differentialsDecisions.get(0).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(2).getFplos());
    }

    private BAROverride createBarOverrideforCloseLV0(Integer accomClassId, Date arrivalDate, Integer los, Integer accomTypeIDDLX) {
        BAROverride override = new BAROverride();
        override.setAccomClassId(accomClassId);
        override.setArrivalDate(arrivalDate);
        override.setLengthOfStay(los);
        override.setRestrictHighestBarEnabled(true);
        override.setAccomTypeId(accomTypeIDDLX);
        return override;
    }


    private OverrideService setupOverrideService() {
        OverrideService overrideService = new OverrideService();
        DecisionService decisionService = DecisionService.createTestInstance();
        PacmanConfigParamsService configService = mock(PacmanConfigParamsService.class);
        CloseHighestBarService closeHighestBarService = new CloseHighestBarService();
        MasterClassOverrideHelperBean masterClassOverrideHelper = new MasterClassOverrideHelperBean();
        inject(masterClassOverrideHelper, "crudService", tenantCrudService());
        BusinessContextService businessContextService = new BusinessContextService();
        businessContextService.setConfigService(configService);
        businessContextService.setCrudService(tenantCrudService());
        inject(masterClassOverrideHelper, "businessContextService", businessContextService);
        inject(overrideService, "masterClassHelper", masterClassOverrideHelper);
        inject(closeHighestBarService, "crudService", tenantCrudService());
        inject(overrideService, "decisionService", decisionService);
        decisionService.setDateServiceLocal(getDateService());
        decisionService.setCrudService(tenantCrudService());
        inject(overrideService, "configService", configService);
        inject(overrideService, "closeHighestBarService", closeHighestBarService);

        when(configService.getBooleanParameterValue(PACMAN_FEATURE_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(CLOSE_HIGHEST_BAR_ENABLED)).thenReturn(true);

        return overrideService;
    }

    @Test
    public void shouldReadTarsMinMaxLOSByRateCodeConfigParam() {
        service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
    }

    @Test
    public void shouldReturnTrueWhenTarsMinMaxLOSParamIsSetToDifferentialAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(DIFFERENTIAL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        boolean expected = service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertTrue(expected);
    }

    @Test
    public void shouldReturnTrueWhenTarsMinMaxLOSParamIsSetToFullAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(FULL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        boolean expected = service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertTrue(expected);
    }

    @Test
    public void shouldReturnFalseWhenTarsMinMaxLOSParamIsSetToNoneAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(NONE);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        boolean expected = service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(0)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertFalse(expected);
    }

    @Test
    public void shouldReturnFalseWhenTarsMinMaxLOSParamIsSetToNullAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(null);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        boolean expected = service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(0)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertFalse(expected);
    }

    @Test
    public void shouldReturnFalseWhenTarsMinMaxLOSParamIsSetToDifferentialAndSRPTotalLevelAsFalse() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(DIFFERENTIAL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(false);
        boolean expected = service.isTarsOutBoundSet();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertFalse(expected);
    }

    @Test
    public void shouldReturnSpecialSRPIdAsMinusOneWhenTarsMinMaxLOSParamIsSetToDifferentialAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(DIFFERENTIAL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        int actual = service.getSpecialSRPId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertEquals(-1, actual);
    }

    @Test
    public void shouldReturnLV0UnqualifiedAsMinusOneWhenTarsMinMaxLOSParamIsSetToDifferentialAndSRPTotalLevelAsTrue() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(DIFFERENTIAL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        int actual = service.getLV0UnqualifiedId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        assertEquals(-1, actual);
    }

    @Test
    public void shouldReturnLV0UnqualifiedIdWhenTarsMinMaxLOSParamIsNOTSet() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(NONE);
        int lv0RateUnQualified = createLV0RateUnQualified();
        createLV0RateQualified();
        int actual = service.getLV0UnqualifiedId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        assertEquals(lv0RateUnQualified, actual);
    }

    @Test
    public void shouldReturnSpecialSrpIdWhenTarsMinMaxLOSParamIsNOTSet() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(NONE);
        int lv0RateQualified = createLV0RateQualified();
        createLV0RateUnQualified();
        int actual = service.getSpecialSRPId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        assertEquals(lv0RateQualified, actual);
    }

    @Test
    public void shouldReturnSpecialSrpIdWhenTarsMinMaxLOSParamIsSetAndSRPTotalLevelIsFalse() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(FULL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(false);
        int lv0RateQualified = createLV0RateQualified();
        createLV0RateUnQualified();
        int actual = service.getSpecialSRPId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        assertEquals(lv0RateQualified, actual);
    }

    @Test
    public void shouldReturnLV0UnqualifiedIdWhenTarsMinMaxLOSParamIsSetAndSRPTotalLevelIsFalse() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(DIFFERENTIAL);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(false);
        int lv0RateUnQualified = createLV0RateUnQualified();
        createLV0RateQualified();
        int actual = service.getLV0UnqualifiedId();
        verify(pacmanConfigParamsService, times(1)).getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS);
        assertEquals(lv0RateUnQualified, actual);
    }


    private DecisionQualifiedFPLOSBatchUpdater getDecisionQualifiedFPLOSBatchUpdater(String tarsUploadType, String srpFPLOSAtTotalLevel) {
        PacmanConfigParamsService pacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, ExternalSystem.TARS)).thenReturn(tarsUploadType);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(Boolean.valueOf(srpFPLOSAtTotalLevel));
        service.configService = pacmanConfigParamsService;
        DecisionQualifiedFPLOSBatchUpdaterBean decisionQualifiedFPLOSBatchUpdaterBean = new DecisionQualifiedFPLOSBatchUpdaterBean();
        decisionQualifiedFPLOSBatchUpdaterBean
                .setConnection(jpaJdbcUtil.getJdbcConnection(tenantCrudService()))
                .setPropertyId(5)
                .setMaxLOS(8)
                .setSrpFplosAtTotalLevel(true)
                .setDerivedRatePlanEnabled(true)
                .setContinuousPricingEnabled(false)
                .setBarByLos(false)
                .setRestrictHighestBarEnabled(false)
                .setBarMaxLos(8)
                .setLraEnabledValue(0)
                .setServicingCostEnabledAndConfigured(false)
                .setForceLegacyCardinalityEstimation(true);

        return service.getDecisionQualifiedFPLOSBatchUpdater(decisionQualifiedFPLOSBatchUpdaterBean);
    }


    @Test
    public void shouldReturnDecisionQualifiedFPLOSBatchUpdater_whenTarsMinMaxUploadTypeIsNone_SRPAtTotalLevelFalse() {
        DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater = getDecisionQualifiedFPLOSBatchUpdater(NONE, FALSE);
        assertTrue(decisionQualifiedFPLOSBatchUpdater != null);
        assertFalse(decisionQualifiedFPLOSBatchUpdater instanceof DecisionQualifiedFPLOSBatchUpdaterForTARSOutbound);
    }

    @Test
    public void shouldReturnDecisionQualifiedFPLOSBatchUpdater_whenTarsMinMaxUploadTypeIsNone_SRPAtTotalLevelTrue() {
        DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater = getDecisionQualifiedFPLOSBatchUpdater(NONE, TRUE);
        assertTrue(decisionQualifiedFPLOSBatchUpdater != null);
        assertFalse(decisionQualifiedFPLOSBatchUpdater instanceof DecisionQualifiedFPLOSBatchUpdaterForTARSOutbound);
    }

    @Test
    public void shouldReturnDecisionQualifiedFPLOSBatchUpdater_whenTarsMinMaxUploadTypeIsFull_SRPAtTotalLevelTrue() {
        DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater = getDecisionQualifiedFPLOSBatchUpdater(FULL, TRUE);
        assertTrue(decisionQualifiedFPLOSBatchUpdater != null);
        assertTrue(decisionQualifiedFPLOSBatchUpdater instanceof DecisionQualifiedFPLOSBatchUpdaterForTARSOutbound);
    }

    @Test
    public void shouldReturnDecisionQualifiedFPLOSBatchUpdater_whenTarsMinMaxUploadTypeIsDifferential_SRPAtTotalLevelTrue() {
        DecisionQualifiedFPLOSBatchUpdater decisionQualifiedFPLOSBatchUpdater = getDecisionQualifiedFPLOSBatchUpdater(DIFFERENTIAL, TRUE);
        assertTrue(decisionQualifiedFPLOSBatchUpdater != null);
        assertTrue(decisionQualifiedFPLOSBatchUpdater instanceof DecisionQualifiedFPLOSBatchUpdaterForTARSOutbound);
    }

    @Test
    public void testFPLOSForTARS_WithRateValueZeroWithSameOptimizationAndUpload() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;
        int optimizationWindow = 10;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "7", String.valueOf(optimizationWindow));
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadDate = new Date();
        populateDecisionData(propertyID, accomClassID, accomTypeID, optimizationWindow, pacmanConfigParamsService, dateService, startDate);
        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 4, Arrays.asList("NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_WithRateValueZeroWithDiffOptimizationAndUploadAndLesserThanMaxLOS() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;
        int optimizationWindow = 20;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("15", TRUE, "7", String.valueOf(optimizationWindow));
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadDate = new Date();
        populateDecisionData(propertyID, accomClassID, accomTypeID, optimizationWindow, pacmanConfigParamsService, dateService, startDate);
        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 14, Arrays.asList("NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_WithRateValueZeroWithDiffOptimizationAndUploadAndGreaterThanMaxLOS() {
        resetUp();
        int propertyID = 5;
        int accomClassID = 3;
        int accomTypeID = 4;
        int optimizationWindow = 20;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "7", String.valueOf(optimizationWindow));
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadDate = new Date();
        populateDecisionData(propertyID, accomClassID, accomTypeID, optimizationWindow, pacmanConfigParamsService, dateService, startDate);
        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 10, Arrays.asList("NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN", "NNNNNNN"));
    }

    private void populateDecisionData(int propertyID, int accomClassID, int accomTypeID, int optimizationWindow, PacmanConfigParamsService pacmanConfigParamsService, DateService dateService, Date startDate) {
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        BigDecimal[] lrvArray = {BigDecimal.valueOf(206.29), BigDecimal.valueOf(253.77), BigDecimal.valueOf(233.6), BigDecimal.valueOf(151.55), BigDecimal.valueOf(717.06), BigDecimal.valueOf(100.0), BigDecimal.valueOf(0.48), BigDecimal.valueOf(0.58)};

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvArray.length, Arrays.asList(lrvArray));
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvArray.length, Arrays.asList(lrvArray));

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        int optimizationWindow = 20;
        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 10, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN", "YYYYYYYY", "NNNNNNYN", "NNNNNYYN", "NNNNYYYN", "NNNYYYYN", "YYYYYYYN", "NYYYYYNY"));
    }

    @Test
    public void testFPLOSForTARS_masterClassWeekendRatePresent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        int optimizationWindow = 20;
        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10",
                TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 20), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE);
        //first weekend rate
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(240), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 20), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(40), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("YYYNNNNN", "NYNNNNNN", "YNNNNNNN", "NNNNNNNN", "YYNNNNNN", "NNNNNNNN"));
        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("2016-01-08", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("2016-01-09", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("2016-01-10", differentialsDecisions.get(5).getArrivalDate().toString());

    }

    @Test
    public void testFPLOSForTARS_NonMasterClassAllRatePresent() {
        resetUp();
        int propertyID = 5;

        //master accomClass
        int masterAccomClassId = 3;
        //non master roomtype
        int accomTypeID = 5;

        int optimizationWindow = 20;
        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void testFPLOSForTARS_MasterNonMasterClassAllRatePresent() {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10",
                TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();

        //master class's accomType
        int accomTypeID = 4;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        //non master accom type
        accomTypeID = 5;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(2, propertyID, accomTypeID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN", "YYNYYYYN", "NNYYYYNN", "NYYYYNNN"));
        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
        assertEquals("2016-01-01", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(3).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(4).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(5).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(5).getRateCodeName());
    }

    @Test
    public void testFPLOSForTARS_weightedRateTwoAccomType() {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE);

        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 10), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, qualifiedRatePlanStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRatePlanStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(40), BigDecimal.valueOf(200), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYNYYYYY", "NNYYYYYY", "NYYYYYYY"));
    }


    @Test
    public void testFPLOSForTARS_weightedRateTwoAccomType_WeekendRate() {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE);

        //1st weekend
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(240), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(100), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(240), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate, BigDecimal.valueOf(100), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYNNNNN", "NYNNNNNN", "YNNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_MasterNonMasterClassAllRatePresent_OnlyWeekend() {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();


        //master class's accomType
        int accomTypeID = 4;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        //non master accom type
        accomTypeID = 5;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(2, propertyID, accomTypeID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("YYNNNNNN", "NNNNNNNN", "NNNNNNNN", "YYNNNNNN", "NNNNNNNN", "NNNNNNNN"));
        assertEquals(6, differentialsDecisions.size());
        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
        assertEquals("2016-01-01", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(3).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(4).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(5).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(5).getRateCodeName());
    }


    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_NoRatePlanConfigured() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int accomClassID = 3;
        final int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);

        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);
        final String caughtUpDate = "2016-01-01";
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
//        final String qualifiedRateplanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        final String qualifiedRateplanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRateplanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedData(accomTypeID, startDate, endDateForUnqualifiedRatePlan);

        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());

    }

    @Test
    public void testFPLOSForTARS_NoBARDecisionsArePresent_RatePlanConfigured() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        //derived rate
        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));

        prepareQualifiedRatePlanData(2, propertyID, accomTypeID, "QR2 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-3));

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan, 7F);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }


    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan, 7F);
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYNNNNNN", "YNNNNNNN", "NNNNNNNN"));

    }


    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_percentageType() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYNNN", "YYYNNNNN", "YYNNNNNN"));
    }


    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType_weekendRate() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);

        //first weekend rate
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));

        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYNNNNN", "YYNNNNNN", "YNNNNNNN"));
    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_percentageType_weekendRate() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE);

        //first weekend rate
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNYNNNNN", "YYNNNNNN", "YNNNNNNN"));
    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType_differentOffset() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);

        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYNNNN", "YYYNNNNN", "YNNNNNNN"));
    }

    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_percentageType_differentOffset() throws ParseException {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1 derived", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE);

        //first weekend rate
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeID, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-5), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-5), BigDecimal.valueOf(-5));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);

        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);
        createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));
        prepareDecisionBarOutputData(accomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNYNNNNN", "YYYNNNNN", "YYNNNNNN"));
    }


    @Test
    public void getFplosQualifiedChunkDetail() {
        FPLOSQualifiedChunkDetail fplosQualifiedChunkDetail = service.getFplosQualifiedChunkDetail();
        assertFalse(fplosQualifiedChunkDetail.getFPLOSAtTotal());
        assertFalse(fplosQualifiedChunkDetail.getLimitTotalSRPRatesEnabled());

        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(Boolean.TRUE);
        fplosQualifiedChunkDetail = service.getFplosQualifiedChunkDetail();
        assertTrue(fplosQualifiedChunkDetail.getFPLOSAtTotal());
        assertTrue(fplosQualifiedChunkDetail.getLimitTotalSRPRatesEnabled());

    }

    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType_twoAccomType() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);

        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDDLX, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDKING, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        prepareDecisionBarOutputData(masterAccomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNYYNNNN", "YYYNNNNN", "YYNNNNNN"));
    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_percentageType_twoAccomType() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE);

        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDDLX, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDKING, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));

        prepareDecisionBarOutputData(masterAccomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYNNN", "YYYNNNNN", "YYNNNNNN"));
    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType_twoAccomType_weekend() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);

        //1st weekend
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDDLX, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDKING, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        prepareDecisionBarOutputData(masterAccomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNYNNNNN", "YYNNNNNN", "YNNNNNNN"));

    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_percentageType_twoAccomType_weekend() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_PERCENTAGE_TYPE);

        //1st weekend
        String ratePlanDetailsStartDate = qualifiedRatePlanStartDate;
        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 2), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));

        //second weekend rate
        ratePlanDetailsStartDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 9), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, ratePlanDetailsStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-2), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDDLX, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDKING, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(50));

        prepareDecisionBarOutputData(masterAccomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYNNNNN", "YYNNNNNN", "YNNNNNNN"));

    }

    @Test
    public void testFPLOSForTARS_MasterNonMasterClassAllRatePresent_threeAccomTypeInNonMaster() {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();

        //master class's accomType
        int accomTypeID = 4;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        //non master accom type
        int accomTypeDoubleID = 6;
        int accomTypeQueenID = 7;
        int accomTypeKingId = 8;
        prepareAccomActivityData(propertyID, accomTypeDoubleID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeQueenID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeKingId, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(2, propertyID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE);
        prepareQualifiedRatePlanDetails(2, accomTypeDoubleID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(45), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        prepareQualifiedRatePlanDetails(2, accomTypeQueenID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(35), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30));
        prepareQualifiedRatePlanDetails(2, accomTypeKingId, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(100), BigDecimal.valueOf(35), BigDecimal.valueOf(35));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN", "YYYYNNYY", "NYNNNYYY", "YYNNYYYY"));
        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
        assertEquals("2016-01-01", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(3).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(4).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(5).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(5).getRateCodeName());
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableCost_ValueTypeAmount() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 1, -10, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNNNNNNN", "NNNNNNNN", "NYNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableCost_ValueTypePercent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 2, -10, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNNYNNNN", "NNYNNNNN", "NYYNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableCost_ValueTypeSet() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 3, 42, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYNNN", "YYYNNNNN", "YNNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableValue_ValueTypeAmount() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));
        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 1, 2, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYNNN", "YYYNNNNN", "YNNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableValue_ValueTypePercent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 2, 1, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYNYYYYY", "NNYYYYYY", "NYYYYYYY"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerNight_YieldableValue_ValueTypeSet() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 3, 42, PER_ROOM_PER_NIGHT);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYNNN", "YYYNNNNN", "YNNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableCost_ValueTypeAmount() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 1, -10, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNNYYYNN", "NNYYYNNN", "NYYYNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableCost_ValueTypePercent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(70), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 2, -10, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertDecisions(differentialsDecisions, 3, Arrays.asList("NNNYYYYN", "NNYYYYNN", "NYYYYNYN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableCost_ValueTypeSet() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 3, 280, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYYNN", "YYYYYYNN", "YYYYYYNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableValue_ValueTypeAmount() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(60), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 1, 2, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYYYN", "YYYYYYNN", "YYYYYNNY"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableValue_ValueTypePercent() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 2, 1, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_PerRoomPerStay_YieldableValue_ValueTypeSet() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        prepareQualifiedRatePlanAdjustmentData(qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_VALUE, 3, 280, PER_ROOM_PER_STAY);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYYYNN", "YYYYYYNN", "YYYYYYNN"));
    }

    @Test
    public void testFPLOSForTARS_NoBARDecisionsPresent_MasterNonMasterClassAllRatePresent_threeAccomTypeInNonMaster() {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();

        //master class's accomType
        int accomTypeDLXId = 4;
        prepareAccomActivityData(propertyID, accomTypeDLXId, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeDLXId, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        //non master accom type
        int accomTypeDoubleID = 6;
        int accomTypeQueenID = 7;
        int accomTypeKingId = 8;
        prepareAccomActivityData(propertyID, accomTypeDoubleID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeQueenID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeKingId, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(2, propertyID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);
        prepareQualifiedRatePlanDetails(2, accomTypeDoubleID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(45), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        prepareQualifiedRatePlanDetails(2, accomTypeQueenID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(35), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30));
        prepareQualifiedRatePlanDetails(2, accomTypeKingId, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(100), BigDecimal.valueOf(35), BigDecimal.valueOf(35));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);
        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN"));

        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
    }

    @Test
    public void testFPLOSForTARS_BARDecisionsArePresent_MasterNonMasterClassAllRatePresent_threeAccomTypeInNonMaster() throws ParseException {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        int nonMasterAccomClassId = 2;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();

        //master class's accomType
        int accomTypeDLXId = 4;
        prepareAccomActivityData(propertyID, accomTypeDLXId, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeDLXId, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));

        //non master accom type
        int accomTypeDoubleID = 6;
        int accomTypeQueenID = 7;
        int accomTypeKingId = 8;
        prepareAccomActivityData(propertyID, accomTypeDoubleID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeQueenID, startDate, optimizationWindow, 40, 0);
        prepareAccomActivityData(propertyID, accomTypeKingId, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanDataWithOutDetails(2, propertyID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);
        prepareQualifiedRatePlanDetails(2, accomTypeDoubleID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(2, accomTypeQueenID, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        prepareQualifiedRatePlanDetails(2, accomTypeKingId, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2), BigDecimal.valueOf(-2));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeDLXId, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeDoubleID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeQueenID, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeKingId, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        prepareDecisionBarOutputData(masterAccomClassId, startDate, rateUnqualified);
        prepareDecisionBarOutputData(nonMasterAccomClassId, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("YYNYYYYN", "NNYYYYNN", "NYYYYNNN", "NNYNNNNN", "YYYNNNNN", "YYNNNNNN"));

        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
        assertEquals("2016-01-01", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(3).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(4).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(5).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(5).getRateCodeName());
    }

    @Test
    public void testFPLOSForTARS_MasterNonMasterClassAllRatePresent_PerRoomPerNight_WillAffectOnlyMasterRates() {
        resetUp();
        int propertyID = 5;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        int masterAccomClassId = 3;
        prepareDecisionLRVData(propertyID, masterAccomClassId, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassId, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);
        cleanupAccomActivityData();

        //master class's accomType
        int accomTypeID = 4;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(50), BigDecimal.valueOf(75), BigDecimal.valueOf(50), BigDecimal.valueOf(45), BigDecimal.valueOf(45), BigDecimal.valueOf(45), BigDecimal.valueOf(45));
        //non master accom type
        accomTypeID = 5;
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);
        prepareQualifiedRatePlanData(2, propertyID, accomTypeID, "QR2", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        prepareQualifiedRatePlanAdjustmentData(1, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 1, -10, PER_ROOM_PER_NIGHT);
        prepareQualifiedRatePlanAdjustmentData(2, qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, YIELDABLE_COST, 1, -10, PER_ROOM_PER_NIGHT);
        queries.setLength(0);
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 6, Arrays.asList("NNNYYNNN", "NNYYYNNN", "NYYYNNNN", "YYNYYYYN", "NNYYYYNN", "NYYYYNNN"));

        assertEquals("2016-01-01", differentialsDecisions.get(0).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(0).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(1).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(1).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(2).getArrivalDate().toString());
        assertEquals("QR1", differentialsDecisions.get(2).getRateCodeName());
        assertEquals("2016-01-01", differentialsDecisions.get(3).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(3).getRateCodeName());
        assertEquals("2016-01-02", differentialsDecisions.get(4).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(4).getRateCodeName());
        assertEquals("2016-01-03", differentialsDecisions.get(5).getArrivalDate().toString());
        assertEquals("QR2", differentialsDecisions.get(5).getRateCodeName());
    }


    @Test
    @Disabled
    public void testFPLOSForTARS_BARDecisionsArePresent_valueType_threeAccomType_missingBarValue() throws ParseException {
        resetUp();
        final int propertyID = 5;
        final int masterAccomClassID = 3;
        final int accomTypeIDDLX = 4;
        final int accomTypeIDQueen = 7;
        final int accomTypeIDKING = 8;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        updateAccomType(masterAccomClassID, accomTypeIDKING);
        updateAccomType(masterAccomClassID, accomTypeIDQueen);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, masterAccomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, masterAccomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeIDDLX, startDate, optimizationWindow, 10, 0);
        prepareAccomActivityData(propertyID, accomTypeIDKING, startDate, optimizationWindow, 20, 0);
        prepareAccomActivityData(propertyID, accomTypeIDQueen, startDate, optimizationWindow, 15, 0);

        prepareQualifiedRatePlanDataWithOutDetails(1, propertyID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, DERIVED_VALUE_TYPE);

        String ratePlanDetailsEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);
        prepareQualifiedRatePlanDetails(1, accomTypeIDDLX, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        prepareQualifiedRatePlanDetails(1, accomTypeIDKING, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        prepareQualifiedRatePlanDetails(1, accomTypeIDQueen, qualifiedRatePlanStartDate, ratePlanDetailsEndDate,
                BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1), BigDecimal.valueOf(-1));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());

        final Date endDateForUnqualifiedRatePlan = DateUtil.parseDate(qualifiedRatePlanEndDate, DateUtil.DEFAULT_DATE_FORMAT);
        RateUnqualified rateUnqualified = prepareRateUnqualifiedWithoutDetails(startDate, endDateForUnqualifiedRatePlan);

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDDLX, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(70), BigDecimal.valueOf(50));

        createRateUnqualifiedDetailsForRateUnqualified(accomTypeIDKING, rateUnqualified, startDate, endDateForUnqualifiedRatePlan,
                BigDecimal.valueOf(50), BigDecimal.valueOf(40), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(30), BigDecimal.valueOf(50));

        //no bar configured for queen AT

        prepareDecisionBarOutputData(masterAccomClassID, startDate, rateUnqualified);

        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertDecisions(differentialsDecisions, 3, Arrays.asList("YYYYNNNN", "YYYNNNNN", "YYNNNNNN"));
    }

    @Test
    public void testFPLOSForTARS_masterClassAllDaysRatePresent_masterAccomCapacityZero() {
        resetUp();
        int propertyID = 5;
        //master classId
        int accomClassID = 3;
        //only roomtype in master class
        int accomTypeID = 4;

        PacmanConfigParamsService pacmanConfigParamsService = getPacmanConfigParamsServiceForTARS("10", TRUE, "8", String.valueOf(optimizationWindow));

        DateService dateService = getDateService();
        dateService.setConfigParamsService(pacmanConfigParamsService);
        FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService = getFplosQualifiedRecommendationService(pacmanConfigParamsService, dateService);

        final String caughtUpDate = "2016-01-01"; //Friday
        updateCaughtUpDate(propertyID, caughtUpDate);
        String updateAccomTypeQuery = "update Accom_Type set Accom_Type_Capacity=0 where Accom_Type_Code= 'DLX'";
        queries.append(updateAccomTypeQuery);

        final Date startDate = dateService.getCaughtUpDate();
        String qualifiedRatePlanStartDate = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        final Date endDate = dateService.getDecisionUploadWindowEndDate();
        String qualifiedRatePlanEndDate = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, optimizationWindow), DateUtil.DEFAULT_DATE_FORMAT);

        FPLOSQualifiedDecisionService fplosQualifiedDecisionService = getFplosQualifiedDecisionService(pacmanConfigParamsService, dateService);

        Date lastUploadDate = new Date();

        prepareDecisionLRVData(propertyID, accomClassID, startDate, lrvList.size(), lrvList);
        prepareDecisionLRVData(propertyID, accomClassID, DateUtil.addDaysToDate(startDate, 8), lrvList.size(), lrvList);

        cleanupAccomActivityData();
        prepareAccomActivityData(propertyID, accomTypeID, startDate, optimizationWindow, 40, 0);

        prepareQualifiedRatePlanData(1, propertyID, accomTypeID, "QR1", qualifiedRatePlanStartDate, qualifiedRatePlanEndDate, FIXED_TYPE,
                BigDecimal.valueOf(40), BigDecimal.valueOf(65), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40), BigDecimal.valueOf(40));
        tenantCrudService().executeUpdateByNativeQuery(queries.toString());
        fplosQualifiedRecommendationService.deriveFPLOS();

        List<FPLOSDecisions> differentialsDecisions = fplosQualifiedDecisionService.getFPLOSDecisionData(startDate, endDate, lastUploadDate);

        assertEquals(0, differentialsDecisions.size());
    }

    @Test
    public void shouldNotAddLOSToBDEWindowForNonTars() {
        final Date endDate = new Date();
        final int maxLos = 7;
        final Date endDateForBDE = service.adjustEndDate(endDate, maxLos);
        assertThat(endDateForBDE.compareTo(DateUtil.addDaysToDate(endDate, 6)), Is.is(0));
    }

    @Test
    public void shouldAddLOSToCDPWindow() {
        final int optimizationWindowBDEDays = 10;
        final int decisionUploadWindowCDPDays = 7;
        final Date endDate = DateUtil.addDaysToDate(new Date(), decisionUploadWindowCDPDays);
        final int maxLos = 7;
        cdpService.configService = getPacmanConfigParamsServiceForTARS(String.valueOf(decisionUploadWindowCDPDays), TRUE, Integer.toString(maxLos), String.valueOf(optimizationWindowBDEDays));
        final Date endDateForCDP = cdpService.adjustEndDate(endDate, maxLos);
        assertEquals(DateUtil.addDaysToDate(endDate, maxLos - 1), endDateForCDP, "Dates must match");
    }

    @Test
    public void shouldAddLOSToBDEWindowForTARS_whenDecisionPresentAndMaxLosLesserThanDiffOfOptimizationAndUploadWindow() {
        final Date endDate = new Date();
        final int maxLos = 3;
        service.configService = getPacmanConfigParamsServiceForTARS("7", TRUE, Integer.toString(maxLos), String.valueOf(10));
        final Date endDateForBDE = service.adjustEndDate(endDate, maxLos);
        assertEquals(DateUtil.addDaysToDate(endDate, maxLos - 1), endDateForBDE, "Dates must match");
    }

    @Test
    public void shouldAddLOSToBDEWindowForTARS_whenDecisionPresentAndMaxLosGreaterThanDiffOfOptimizationAndUploadWindow() {
        final Date endDate = new Date();
        final int maxLos = 21;
        service.configService = getPacmanConfigParamsServiceForTARS("550", TRUE, Integer.toString(maxLos), String.valueOf(565));
        final Date endDateForBDE = service.adjustEndDate(endDate, maxLos);
        assertEquals(DateUtil.addDaysToDate(endDate, 20), endDateForBDE, "Dates must match");
    }

    @Test
    public void shouldAddLOSToBDEWindowForTARS_whenDecisionPresent() {
        final Date endDate = new Date();
        final int maxLos = 5;
        //optimization window 10[6 + (5-1)<= 10]
        service.configService = getPacmanConfigParamsServiceForTARS("6", TRUE, Integer.toString(maxLos), String.valueOf(10));
        final Date endDateForBDE = service.adjustEndDate(endDate, maxLos);
        assertEquals(DateUtil.addDaysToDate(endDate, maxLos - 1), endDateForBDE, "Dates must match");
    }

    private void assertDecisions(List<FPLOSDecisions> results, int expectedCount, List<String> expectedDecisions) {
        assertEquals(expectedCount, results.size(), "Expected fplos count not matched with result's count");
        assertEquals(expectedCount, expectedDecisions.size(), "Expected fplos count not matched with expected decisions list size");
        for (int index = 0; index < expectedCount; index++) {
            assertEquals(expectedDecisions.get(index), results.get(index).getFplos(), "FPLOS pattern not matched for days " + (index + 1));
        }
    }

    private RateUnqualified prepareRateUnqualifiedWithoutDetails(Date startDate, Date endDate) {
        return UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate);
    }

    private void createRateUnqualifiedDetailsForRateUnqualified(int accomTypeID, RateUnqualified rateUnqualified, Date startDate, Date endDateForUnqualifiedRatePlan, Float rateMultiplier) {
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, rateMultiplier);
    }

    private void createRateUnqualifiedDetailsForRateUnqualified(int accomTypeID, RateUnqualified rateUnqualified, Date startDate, Date endDateForUnqualifiedRatePlan, BigDecimal sunday, BigDecimal monday,
                                                                BigDecimal tuesday, BigDecimal wednesday, BigDecimal thursday, BigDecimal friday, BigDecimal saturday) {

        final RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomTypeID, rateUnqualified.getId(), startDate, endDateForUnqualifiedRatePlan, 6.0F);
        rateUnqualifiedDetails.setSunday(sunday);
        rateUnqualifiedDetails.setMonday(monday);
        rateUnqualifiedDetails.setTuesday(tuesday);
        rateUnqualifiedDetails.setWednesday(wednesday);
        rateUnqualifiedDetails.setThursday(thursday);
        rateUnqualifiedDetails.setFriday(friday);
        rateUnqualifiedDetails.setSaturday(saturday);

        tenantCrudService().save(rateUnqualifiedDetails);
        tenantCrudService().flushAndClear();
    }

    private PacmanConfigParamsService getPacmanConfigParamsServiceForTARS(final String decisionUploadWindowBDE,
                                                                          final String isDerivedQualifiedRatePlanEnabled,
                                                                          String qualifiedFPLOSMaxLOS, String optimizationWindow) {

        return new PacmanConfigParamsService() {

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, ExternalSystem syste) {
                return (T) getParameterValue(configParamName.value(syste.getCode()));
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)) {
                    return (T) Boolean.FALSE;
                }
                if (configParamName.equals(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)) {
                    return (T) TRUE;
                }
                return null;
            }

            @Override
            public boolean getBooleanParameterValue(String parameterName) {
                return "true".equalsIgnoreCase(getParameterValue(parameterName));
            }

            @Override
            public String getParameterValue(String parameterName) {
                switch (parameterName) {
                    case APPLY_VARIABLE_DECISION_WINDOW:
                    case PACMAN_INTEGRATION_RATCHET_USEEXTENDEDSTAYMAPPING:
                    case OPEN_LV0_ENABLED:
                        return FALSE;
                    case LRA_ENABLED:
                        return FALSE;
                    case DECISION_UPLOAD_WINDOW_BDE:
                        return decisionUploadWindowBDE;
                    case OPTMIZATION_WINDOW_BDE:
                        return optimizationWindow;
                    case QUALIFIED_FPLOS_MAX_LOS:
                        return qualifiedFPLOSMaxLOS;
                    case OPERA_FPLOS_BY_RATE_CODE_BY_ROOM_TYPE_UPLOADTYPE:
                    case OPERA_FPLOS_BY_RATE_CODE_UPLOADTYPE:
                        return DIFFERENTIAL;
                    case PACMAN_FEATURE_DERIVED_QUALIFIED_RATE_PLAN_ENABLED:
                        return isDerivedQualifiedRatePlanEnabled;
                    case BAR_DECISION:
                        return BAR_DECISION_VALUE_RATEOFDAY;
                    case PACMAN_FEATURE_CONTINUOUS_PRICING_ENABLED:
                        return FALSE;
                    case MAX_LOS:
                        return "7";
                    case SRP_FPLOS_AT_TOTAL_LEVEL:
                        return TRUE;
                    case CLOSE_HIGHEST_BAR_ENABLED:
                    case PACMAN_FEATURE_SERVICING_COST_BY_LOSENABLE:
                        return FALSE;
                    case TARS_MIN_MAX_LOS_BYRATECODE_UPLOADTYPE:
                        return DIFFERENTIAL;
                    case FPLOS_FORCE_LEGACY_CARDINALITY_ESTIMATION:
                        return TRUE;
                    default:
                        return null;
                }
            }
        };
    }

    private void prepareQualifiedRatePlanDataWithOutDetails(int rateQualifiedId, int propertyID, String rateCodeName, String qualifiedRateplanStartDate, String qualifiedRateplanEndDate, int rateQualifiedTypeId) {
        queries.append("delete from Rate_Qualified_Details where Rate_Qualified_ID = " + rateQualifiedId);
        queries.append("delete from Rate_Qualified where Rate_Qualified_ID = " + rateQualifiedId);
        String rateQualifiedQuery = "SET IDENTITY_INSERT [Rate_Qualified] ON" +
                " INSERT INTO [Rate_Qualified]" +
                " ([Rate_Qualified_ID],[File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Start_Date_DT],[End_Date_DT]" +
                "   ,[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" +
                "   ,[Status_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Rate_Qualified_Type_Id])" +
                " VALUES (" + rateQualifiedId + ",(select MAX(File_Metadata_ID) from File_Metadata), " + propertyID + ", '" + rateCodeName + "', 'rate description', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, " + rateQualifiedTypeId + ")" +
                "SET IDENTITY_INSERT [Rate_Qualified] OFF;";
        queries.append(rateQualifiedQuery);
    }

    public void createAndSaveBarOverrides(Integer masterAccomClassID, Date startDate, Integer accomTypeIDDLX) {
        OverrideService overrideService = setupOverrideService();
        List<BAROverride> barOverrideList = new ArrayList<>();

        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, startDate, 1, accomTypeIDDLX));
        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, startDate, 3, accomTypeIDDLX));
        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, startDate, 5, accomTypeIDDLX));
        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, startDate, 7, accomTypeIDDLX));

        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, DateUtil.addDaysToDate(startDate, 1), 1, accomTypeIDDLX));
        barOverrideList.add(createBarOverrideforCloseLV0(masterAccomClassID, DateUtil.addDaysToDate(startDate, 1), 8, accomTypeIDDLX));

        overrideService.saveBAROverrides(barOverrideList);
    }


    private void stageDecisionMock() {
        when(decisionService.createQualifiedFPLOSDecision()).thenReturn(getDecision());
    }

    private Decision getDecision() {
        Decision de = new Decision();
        de.setId(100);
        return de;
    }

    private void stageDateServiceMock_15() {
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getFirstDayOfNextMonth());
        when(dateService.getBusinessDate()).thenReturn(DateUtil.getFirstDayOfNextMonth());
        when(dateService.getOptimizationWindowStartDate()).thenReturn(DateUtil.getFirstDayOfCurrentMonth());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(DateUtil.getDateForNextMonth(28));
        when(dateService.getDecisionUploadWindowEndDateBDEVariable()).thenReturn(DateUtil.getDateForNextMonth(15));
    }

    private void stageDateServiceMock() {
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getFirstDayOfNextMonth());
        when(dateService.getBusinessDate()).thenReturn(DateUtil.getFirstDayOfNextMonth());
        when(dateService.getOptimizationWindowStartDate()).thenReturn(DateUtil.getFirstDayOfCurrentMonth());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(DateUtil.getDateForNextMonth(28));
        when(dateService.getDecisionUploadWindowEndDateBDEVariable()).thenReturn(DateUtil.getDateForNextMonth(28));
    }

    private void stagePacmanConfigParamsServiceMock() {
        when(pacmanConfigParamsService.getBooleanParameterValue(eq(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL))).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(eq(BAR_DECISION))).thenReturn(BAR_DECISION_VALUE_LOS);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, Constants.RATCHET)).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("10");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_DAYSOFWEEK))).thenReturn("Sunday");
        when(pacmanConfigParamsService.getParameterValue(eq(OVERRIDE_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(APPLY_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_VARIABLE))).thenReturn("180");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.FALSE);
    }

    private void stagePacmanConfigParamsServiceMock_LRA_False() {
        when(pacmanConfigParamsService.getBooleanParameterValue(eq(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL))).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(eq(BAR_DECISION))).thenReturn(BAR_DECISION_VALUE_LOS);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, Constants.RATCHET)).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("10");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_DAYSOFWEEK))).thenReturn("Sunday");
        when(pacmanConfigParamsService.getParameterValue(eq(OVERRIDE_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(APPLY_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_VARIABLE))).thenReturn("180");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.FALSE);
    }

    private void stagePacmanConfigParamsServiceMockfplosBySRP_by_VDE_True() {
        when(pacmanConfigParamsService.getBooleanParameterValue(eq(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL))).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(eq(BAR_DECISION))).thenReturn(BAR_DECISION_VALUE_LOS);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, Constants.RATCHET)).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("10");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_DAYSOFWEEK))).thenReturn("Sunday");
        when(pacmanConfigParamsService.getParameterValue(eq(OVERRIDE_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(APPLY_VARIABLE_DECISION_WINDOW))).thenReturn(TRUE);
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_VARIABLE))).thenReturn("15");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.FALSE);
    }

    private void stagePacmanConfigParamsServiceMockfplosBySRP_by_VDE_True_LRA_true() {
        when(pacmanConfigParamsService.getBooleanParameterValue(eq(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL))).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(eq(BAR_DECISION))).thenReturn(BAR_DECISION_VALUE_LOS);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, Constants.RATCHET)).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(QUALIFIED_FPLOS_MAX_LOS))).thenReturn("10");
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_DAYSOFWEEK))).thenReturn("Sunday");
        when(pacmanConfigParamsService.getParameterValue(eq(OVERRIDE_VARIABLE_DECISION_WINDOW))).thenReturn("False");
        when(pacmanConfigParamsService.getParameterValue(eq(APPLY_VARIABLE_DECISION_WINDOW))).thenReturn(TRUE);
        when(pacmanConfigParamsService.getParameterValue(eq(OPTMIZATION_WINDOW_BDE_VARIABLE))).thenReturn("15");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.FALSE);
    }

    private int createLV0RateQualified() {
        RateQualified rq = new RateQualified();
        rq.setFileMetadataId(1);
        rq.setPropertyId(6);
        rq.setName(LV0);
        rq.setDescription(LV0);
        rq.setStartDate(new Date());
        rq.setEndDate(new Date());
        rq.setYieldable(1);
        rq.setPriceRelative(0);
        rq.setReferenceRateCode("0");
        rq.setIncludesPackage(0);
        rq.setStatusId(1);
        rq.setRateQualifiedTypeId(3);
        return tenantCrudService().save(rq).getId();
    }

    private int createLV0RateUnQualified() {
        RateUnqualified lv0RateUnqualified = new RateUnqualified();
        lv0RateUnqualified.setFileMetadataId(1);
        lv0RateUnqualified.setName(LV0);
        lv0RateUnqualified.setDescription(LV0);
        lv0RateUnqualified.setStatusId(Constants.ACTIVE_STATUS_ID);
        lv0RateUnqualified.setPropertyId(6);
        lv0RateUnqualified.setSystemDefault(1);
        Date now = new Date();
        lv0RateUnqualified.setStartDate(now);
        lv0RateUnqualified.setEndDate(now);
        lv0RateUnqualified.setYieldable(1);
        lv0RateUnqualified.setPriceRelative(0);
        lv0RateUnqualified.setDerivedRateCode("0");
        lv0RateUnqualified.setIncludesPackage(0);
        return tenantCrudService().save(lv0RateUnqualified).getId();
    }
}
