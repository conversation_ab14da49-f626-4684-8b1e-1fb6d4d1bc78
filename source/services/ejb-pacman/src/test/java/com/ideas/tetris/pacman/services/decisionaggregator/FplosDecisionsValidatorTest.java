package com.ideas.tetris.pacman.services.decisionaggregator;

import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class FplosDecisionsValidatorTest {

    @Test
    void validateWhenDecisionsAreSame() {
        var decisionsFromG3 = new ArrayList<FPLOSDecisions>();
        var decisionsFromService = new ArrayList<FPLOSDecisions>();

        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYY"));
        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYY"));
        var response = FplosDecisionsValidator.validate(decisionsFromService, decisionsFromG3);
        Assertions.assertTrue(response.isEmpty());
    }

    @Test
    void validateWhenDecisionsAreDifferent() {
        var decisionsFromG3 = new ArrayList<FPLOSDecisions>();
        var decisionsFromService = new ArrayList<FPLOSDecisions>();

        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYN"));
        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "STANDARD", "SRP1", "YYYYNNN"));

        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYY"));
        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "STANDARD", "SRP1", "YYYYYYY"));

        var response = FplosDecisionsValidator.validate(decisionsFromService, decisionsFromG3);
        Assertions.assertEquals(2, response.size());
        Assertions.assertEquals(decisionsFromService.get(0), response.get(0));
        Assertions.assertEquals(decisionsFromService.get(1), response.get(1));
    }

    @Test
    void validateWhenDecisionsAreEmpty() {
        var response = FplosDecisionsValidator.validate(List.of(), List.of());
        Assertions.assertTrue(response.isEmpty());
    }

    @Test
    void validateWhenG3DecisionsExceedsServiceDecisions() {
        var decisionsFromG3 = new ArrayList<FPLOSDecisions>();
        var decisionsFromService = new ArrayList<FPLOSDecisions>();

        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYY"));
        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "STANDARD", "SRP1", "YYYYYYY"));
        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 11)), "STANDARD", "SRP1", "YYYYYYY"));

        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYN"));
        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "STANDARD", "SRP1", "YYYYYYY"));

        var response = FplosDecisionsValidator.validate(decisionsFromService, decisionsFromG3);

        Assertions.assertEquals(2, response.size());
        Assertions.assertEquals(decisionsFromService.get(0), response.get(0));
        Assertions.assertEquals(decisionsFromG3.get(2), response.get(1));
    }

    @Test
    void validateWhenServiceDecisionsExceedsG3Decisions() {
        var decisionsFromG3 = new ArrayList<FPLOSDecisions>();
        var decisionsFromService = new ArrayList<FPLOSDecisions>();

        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYY"));
        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "STANDARD", "SRP1", "YYYYYYY"));
        decisionsFromService.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 11)), "STANDARD", "SRP1", "YYYYYYY"));

        decisionsFromG3.add(new FPLOSDecisions(1000, Date.valueOf(LocalDate.of(2024, 9, 10)), "DELUXE", "SRP1", "YYYYYYN"));

        var response = FplosDecisionsValidator.validate(decisionsFromService, decisionsFromG3);

        Assertions.assertEquals(3, response.size());
        Assertions.assertEquals(decisionsFromService.get(0), response.get(0));
        Assertions.assertEquals(decisionsFromService.get(1), response.get(1));
        Assertions.assertEquals(decisionsFromService.get(2), response.get(2));
    }

}