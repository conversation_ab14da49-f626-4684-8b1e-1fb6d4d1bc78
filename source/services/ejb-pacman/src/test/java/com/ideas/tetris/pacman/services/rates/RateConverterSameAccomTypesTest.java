package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.RateHeader;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.fest.util.Lists;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;

import java.text.ParseException;
import java.util.*;

import static com.ideas.tetris.pacman.services.rates.RateConverterTestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class RateConverterSameAccomTypesTest extends AbstractRateConverterTest {

    @Test
    public void shouldReturnEmptyWhenRatesDetailsDTOIsEmpty_UnQualified() {
        when(tenantCrudService.findByNamedQuery(eq(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES), anyMapOf(String.class, Object.class))).thenReturn(Collections.emptyList());

        List<HashMap<String, Object>> rateDataList = setUpDataForEmptyRatesDetails();
        assertTrue(unqualifiedRateConverter.convert(rateDataList).isEmpty());
    }

    @Test
    public void shouldReturnEmptyWhenRatesDetailsDTOIsEmpty_Qualified() {
        when(tenantCrudService.findByNamedQuery(eq(RateQualified.GET_RATE_QUALIFIED_BY_NAMES), anyMapOf(String.class, Object.class))).thenReturn(Collections.emptyList());

        List<HashMap<String, Object>> rateDataList = setUpDataForEmptyRatesDetails();
        assertTrue(qualifiedRateConverter.convert(rateDataList).isEmpty());
    }

    @Test
    public void shouldNOTCallSaveWithFlushAndClearMethod() throws ParseException {
        testFlushAndClearToggleNOTCalled(UN_QUALIFIED);
        testFlushAndClearToggleNOTCalled(QUALIFIED);
    }

    @Test
    public void shouldReturnEmptyWhenNullOrEmptyDetails() throws ParseException {
        when(tenantCrudService.findByNamedQuery(eq(RateQualified.GET_RATE_QUALIFIED_BY_NAMES), anyMapOf(String.class, Object.class))).thenReturn(Collections.emptyList());

        //Empty Seasons
        givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .givenPseudoRoomsAreSetTo(ACCOM_TYPE_CODE_2 + ",foo")
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

        givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

        //Null seasons
        seasons = null;
        givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

        givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

        //No rates key
        rateHeaderWithSeasons.remove(RATES_KEY);
        givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

        givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);

    }

    @Test
    public void shouldFilterOutPseudoRoomTypes_toggleOFF() throws ParseException {
        givenSeason(JAN_01_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .givenDeleteFutureSeasonToggleValueSetTo(true)
                .givenPseudoRoomsAreSetTo(ACCOM_TYPE_CODE_2 + ",foo")
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
    }

    //TODO complete or remove
    @Test
    public void shoulrReturnEmptyWhenThereIsNothingToSplit() {

    }

    @Test
    public void shouldReturnEmptyWhenThereNoOriginalRates() {
        givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .whenSaveOriginalRatesCalled(new ArrayList<>())
                .assertEmptyResult(semiYieldableRatesFinalOutput);

//        When no original rates key
        rateHeaderWithSeasons.remove(RATES_KEY);
        whenSaveOriginalRatesCalled(new ArrayList<>())
                .assertEmptyResult(semiYieldableRatesFinalOutput);
    }

    @Test
    public void shouldReturnEmptyWhenThereNULLOriginalRates() {
        originalRateSeasons = null;
        whenSaveOriginalRatesCalled(new ArrayList<>())
                .assertEmptyResult(semiYieldableRatesFinalOutput);
    }

    @Test
    public void shouldNotSaveOriginalRatesIfRateQualifiedsNotPresent() {
        givenOriginalSeason(JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .whenSaveOriginalRatesCalled(new ArrayList<>())
                .assertEmptyResult(semiYieldableRatesFinalOutput);
    }

    @Test
    public void shouldSaveSemiYieldableOriginalRatesNonOverlapping_OriginalRecordsNotModified() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_07_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_444, DOW_RATES_ALL_444)
                .givenSeason(JAN_08_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenOriginalSeason(JAN_01_2015_DATE, JAN_07_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenOriginalSeason(JAN_08_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .whenSaveOriginalRatesCalled(qualifiedRatesFinalOutput)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSemiYieldableRateDetailData(0, JAN_01_2015_DATE, JAN_07_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSemiYieldableRateDetailData(1, JAN_08_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_07_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_444)
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_08_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_222);
    }

    @Test
    public void shouldSaveSemiYieldableOriginalRatesCompletelyOverlapping() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_444, DOW_RATES_ALL_222)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_444)
                .givenOriginalSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenOriginalSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .whenSaveOriginalRatesCalled(qualifiedRatesFinalOutput)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSemiYieldableRateDetailData(0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_222);
    }

    /*
     *new     :     Start|______|End
     * */
    @Test
    @Tag("rateConverter-flaky")
    public void shouldWorkWhenOnlyOneRateAndSeasonAndSkipPseudoRooms_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .givenPseudoRoomsAreSetTo(ACCOM_TYPE_CODE_2 + ",foo")
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, MAY_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_SUN_MON_WED_FRI_111);
//                .assertRateHeaderDBCalls(1, 1, 1, JAN_01_2015_DATE, false);

    }

    //TODO test case for out of range dow values, logic should only include rate values which are in within start and end date range
    //TODO Different for each season
    /*
     *                      S1                      S2                      s3
     *new     :     Start|______|End        Start|______|End        Start|______|End
     *          Feb 01           08   Jan   11           20     Jan 01          10
     * */
    @Test
    public void shouldWorkWhenNoOverlapping_1_Qual_toggleON() throws ParseException {
        givenSeason(FEB_01_2015_DATE, FEB_08_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenSeason(JAN_11_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_SUN_MON_WED_FRI_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_11_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, FEB_01_2015_DATE, FEB_08_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333);

    }

    /*
     *new     :     Start|______|End
     *              Start|______|End
     * */
    @Test
    public void shouldWorkWhenCompletelyOverlapping_WithAllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    /*
     *new     :     Start|______|End
     *              Start|______|End
     * */
    @Test
    public void shouldWorkWhenCompletelyOverlapping_WithLessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_TUE_THU_SAT_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_333, VALUE_111, VALUE_333, VALUE_111, VALUE_333));
    }

    /*
     *new     :     Start|______|End
     *        Start|_________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingBeyond_WithAllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    /*
     *new     :     Start|______|End
     *        Start|_________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingBeyond_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_THU_FRI_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_TUE_THU_SAT_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_111, VALUE_333));
    }

    /*
     *new     :  Start|__________________|End
     *                Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingWithin_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_05_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_04_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_05_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_16_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111));
    }

    //TODO Order change?
    /*
     *new     :  Start|__________________|End
     *                Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingWithin_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_05_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_TUE_THU_SAT_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_04_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_05_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_333, VALUE_111, VALUE_333, VALUE_111, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_16_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111));
    }

    /*
     *new     :  Start|__________________|End
     *           Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStart_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_333, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111));
    }

    /*
     *new     :  Start|__________________|End
     *           Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStart_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_THU_FRI_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_MON_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111));
    }

    /*
     *new     :  Start|______|End
     *           Start|__________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStartBeyond_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_06_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_06_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_333, VALUE_333, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333));
    }

    /*
     *new     :  Start|______|End
     *           Start|__________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStartBeyond_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_THU_FRI_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_06_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_MON_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_06_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111));
    }

    /*
     *new     :  Start|__________________|End
     *                       Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromEnd_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333));
    }

    /*
     *new     :  Start|__________________|End
     *                       Start|______|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromEnd_LessDOWValues_Qual_toggleON_scenario1() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_THU_FRI_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_TUE_THU_SAT_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_111, VALUE_333));
    }

    /*
     *new     :             Start|______|End
     *          Start|__________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromEndBeyond_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    /*
     *new     :              Start|______|End
     *           Start|__________________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromEndBeyond_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_07_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_THU_FRI_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_MON_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111));
    }

    /*
     *new     :  Start|_________|End
     *                  Start|________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStartBetween_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_06_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333));
    }

    /*
     *           :  Start|__________|End
     *              Start|__________|End
     *              Start|__________|End
     *                         Start|________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingEndAndStart_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_13_2015_DATE, JAN_19_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_13_2015_DATE, JAN_19_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_13_2015_DATE, JAN_19_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_111)
                .givenSeason(JAN_19_2015_DATE, JAN_23_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 2, JAN_01_2015_DATE, DEC_31_2015_DATE)
                //TODO should we sort on start date?
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_13_2015_DATE, JAN_18_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_19_2015_DATE, JAN_23_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_NEGATIVE_ONE))
                .thenAssertDataPassedToDB();
    }

    /*
     *              3. Start|_________|End
     *                  2. Start|________|End 1. Start|________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStartBetween_AllDOWValuesSeasonOrderChange_Qual_toggleON() throws ParseException {
        givenSeason(JAN_15_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_06_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_06_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_15_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111);
    }

    /*
     *new     :  Start|_________|End
     *                  Start|________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromStartBetween_LessDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_TUE_THU_SAT_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_08_2015_DATE, JAN_12_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_MON_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_06_2015_DATE, JAN_12_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_333, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_111));
    }

    /*
     *                  Start|________|End
     *new     :  Start|_________|End
     * */
    @Test
    public void shouldWorkWhenOverlappingFromEndBetween_AllDOWValues_Qual_toggleON() throws ParseException {
        givenSeason(JAN_07_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_06_2015_DATE, JAN_08_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_06_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333, VALUE_111, VALUE_111));
    }

    /*
     * 1 New:           Start|________|End
     * 2         Start|_________|End
     * 3         Start|_________|End
     * */
    @Test
    public void testDOWMissingAndOverlap_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE), DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222), DOW_RATES_ALL_111)
                .givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333), DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_04_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_111, VALUE_333, VALUE_111, VALUE_333, VALUE_222, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE));
    }

    @Test
    public void shouldWorkWhenDiffSplitMergeConditions_toggleON() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_02_2015_DATE, JAN_05_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_11_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_16_2015_DATE, JAN_20_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_20_2015_DATE, JAN_21_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_21_2015_DATE, JAN_22_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_23_2015_DATE, JAN_25_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_18_2015_DATE, JAN_25_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_11_2015_DATE, JAN_18_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_01_2015_DATE, JAN_11_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_25_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 1, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_25_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111);
    }

    //TODO: similar scenario is not working in multiple accom type
    @Test
    public void testOvelappingfromStartScenario() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)

                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)

                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID, Arrays.asList(
                        VALUE_222, VALUE_222, VALUE_222, VALUE_222, VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE
                ))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_16_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111);

    }

    @Test
    public void testConvertExistingUnqualifiedRateWithNoDetails_toggleOFF() throws Exception {
        Map<String, Object> headerMap = createData();
        // remove the rate details
        headerMap.remove(RATES_KEY);

        when(rateUnqualifiedComponent.fetchDetails(anyListOf(RateUnqualified.class))).thenReturn(new ArrayList<>());


        List<RateUnqualified> rateUnqualifiedList = new ArrayList<RateUnqualified>();
        rateUnqualifiedList.add(existingRateUnqualified);
        when(tenantCrudService.save(ArgumentMatchers.<RateUnqualified>anyCollection())).thenReturn(rateUnqualifiedList);
        List<AbstractRate> convertedRates = unqualifiedRateConverter.convert(Arrays.asList(headerMap));
        assertEquals(convertedRates.size(), 1);
        assertEquals(convertedRates.get(0).getStatusId().intValue(), 2);

        verify(rateUnqualifiedComponent).saveUnqualifiedRates(anyListOf(RateHeader.class), eq(fileMetadata.getId()));
    }

    @Test
    public void testConvertUnqualifiedToRateUnqualifiedDetailsDoubleFriSunSingleMonThur_toggleOff() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .amountTypeMockedTo(RateAmountCalculator.SINGLE_OCCUPANT_AMOUNT, DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY)
                .amountTypeMockedTo(RateAmountCalculator.DOUBLE_OCCUPANT_AMOUNT, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_333, VALUE_333));
    }

    @Test
    public void testConvertUnqualifiedToRateUnqualifiedDetailsDoubleThuSingleFriSun_toggleOff() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .amountTypeMockedTo(RateAmountCalculator.DOUBLE_OCCUPANT_AMOUNT, DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY)
                .amountTypeMockedTo(RateAmountCalculator.SINGLE_OCCUPANT_AMOUNT, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_111, VALUE_111));
    }

    @Test
    public void testConvertUnqualifiedToRateUnqualifiedDetailsAllDouble_toggleOff() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111), DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .amountTypeMockedTo(RateAmountCalculator.DOUBLE_OCCUPANT_AMOUNT, DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333));
    }

    @Test
    public void testConvertUnqualifiedToRateUnqualifiedDetailsBase_VerifyRoomTypeFindOrCreated() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111), DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenUnQualifiedConverterCalled()
                .thenAssertUnQualifiedRatesData(1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertUnQualifiedRateDetailData(0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111));

        verify(accommodationService).findOrCreateRoomTypeForCode(nullable(Integer.class), anyString());
    }

    @Test
    public void shouldReturnNullWhenG3ManagedActiveRateFoundInDBWithGiveName() throws Exception {

        existingRateUnqualified.setManagedInG3(true);
        //Empty Seasons
        givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenUnQualifiedConverterCalled()
                .assertEmptyResult(unQualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
    }

    @Test
    public void shouldThrowExceptionForUnsupportedNotifType() throws ParseException {
        assertThrows(TetrisException.class, () -> {
            // Empty Seasons
            givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE).notifTypeSetTo("UnSupported").givenRatesFeatureToggleValueSetTo(false).givenPseudoRoomsAreSetTo(ACCOM_TYPE_CODE_2 + ",foo").whenConverterCalled(QUALIFIED).assertEmptyResult(qualifiedRatesFinalOutput).assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
        });
    }

    //TODO change test data dates as it might go out of range for 3 yrs logic
    @Test
    public void testCreateWeeklySeasons() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE),
                Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE))
                .givenSeason(JAN_01_2015_DATE, FEB_28_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenSeason(JAN_15_2015_DATE, FEB_15_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_222, VALUE_222),
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 17, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_111, VALUE_111, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_111, VALUE_111, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 5, FEB_01_2015_DATE, FEB_07_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 6, FEB_08_2015_DATE, FEB_14_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 7, FEB_15_2015_DATE, FEB_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE))
                .thenAssertSingleRateDetailData(QUALIFIED, 8, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_2_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 9, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 10, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 11, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 12, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 13, FEB_01_2015_DATE, FEB_07_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 14, FEB_08_2015_DATE, FEB_14_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 15, FEB_15_2015_DATE, FEB_21_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 16, FEB_22_2015_DATE, FEB_28_2015_DATE, ACCOM_TYPE_2_ID, DOW_RATES_ALL_333);
    }

    @Test
    public void testHappyPathOneRoomTypeOneMonth() throws Exception {

        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_222)
                .givenSeason(JAN_16_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_444)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    @Test
    public void testOverlapOnSingleDay() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_15_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    @Test
    public void testOverlapOnMultipleDates() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_15_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_12_2015_DATE, JAN_25_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_333, VALUE_222, VALUE_222, VALUE_222, VALUE_222, VALUE_222, VALUE_222));
    }

    @Test
    public void testLessThanAWeekSeason() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_15_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_18_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_222, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    @Test
    public void testGapInDetails() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_13_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenSeason(JAN_15_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_333, DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 5, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_13_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222, VALUE_333, VALUE_333, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_333);
    }

    @Test
    public void testLastWeekShort() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_16_2015_DATE, FEB_01_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_222, DOW_RATES_ALL_222)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 6, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_17_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 3, JAN_18_2015_DATE, JAN_24_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_222)
                .thenAssertSingleRateDetailData(QUALIFIED, 4, JAN_25_2015_DATE, JAN_31_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_222)
                .thenAssertSingleRateDetailData(QUALIFIED, 5, FEB_01_2015_DATE, FEB_01_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_222, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE));
    }

    @Test
    public void shouldIgnoreOutsideOfRangeHeaderRateEndsBeforeYesterday() {
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getDate(2, 1, 2015));
        Map<String, Object> rateHeader = createRateHeaderMap();
        Map<String, Object> rt1 = createDetailMapWithValue(JAN_01_2015_DATE, JAN_31_2015_DATE, "100.00", "rt1");
        addDetailsToHeader(rateHeader, Lists.newArrayList(rt1));

        unqualifiedRateConverter.convert(Lists.newArrayList(rateHeader));

        verify(tenantCrudService, never()).save(anyListOf(AbstractRate.class));
        verify(tenantCrudService, never()).save(anyListOf(AbstractDetail.class));
    }

    @Test
    @Tag("rateConverter-flaky")
    public void shouldTrimDetailStartDateAnd_NOT_RateHeaderStartDateUnQualified() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, DEC_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .givenDeleteFutureSeasonToggleValueSetTo(true)
                .whenUnQualifiedConverterCalled()
                .thenAssertUnQualifiedRatesData(1, 3, DEC_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertUnQualifiedRateDetailData(0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertUnQualifiedRateDetailData(1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertUnQualifiedRateDetailData(2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE))
                .assertRateHeaderDBCalls(1, 1, 0, JAN_01_2015_DATE, true);
    }

    @Test
    @Tag("rateConverter-flaky")
    public void shouldTrimDetailStartDateAnd_NOT_RateHeaderStartDateQualified() throws ParseException {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, DEC_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .givenDeleteFutureSeasonToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, DEC_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_111, VALUE_111))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID, DOW_RATES_ALL_111)
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE))
                .assertRateHeaderDBCalls(1, 1, 0, JAN_01_2015_DATE, false);
    }

    @Test
    public void shouldIgnoreDetailEndsBeforeYesterday() throws ParseException {

        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getDate(17, 0, 2015));
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
    }

    @Test
    public void shouldIgnoreOutsideOfRangeHeaderRateBeginsAfterThreeYearsFromCaughtUp() throws ParseException {
        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getDate(30, 11, 2011));
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
    }

    @Test
    @Tag("rateConverter-flaky")
    public void testDOWMissing() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222),
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222))
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .givenDeleteFutureSeasonToggleValueSetTo(true)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE))
                .assertRateHeaderDBCalls(1, 1, 0, JAN_01_2015_DATE, false);
    }

    @Test
    public void testDOWMissingAndOverlap() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE), DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222), DOW_RATES_ALL_111)
                .givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333), DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_333, VALUE_111, VALUE_333, VALUE_222, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE));

    }

    @Test
    public void testDOWMissingAndOverlapAndGap() throws Exception {
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1,
                Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE), DOW_RATES_ALL_111)
                .givenSeason(JAN_01_2015_DATE, JAN_08_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_222, VALUE_222), DOW_RATES_ALL_222)
                .givenSeason(JAN_05_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_1,
                        Arrays.asList(VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333), DOW_RATES_ALL_333)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .thenAssertRatesData(QUALIFIED, 1, 3, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .thenAssertSingleRateDetailData(QUALIFIED, 0, JAN_01_2015_DATE, JAN_03_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE, VALUE_111, VALUE_222, VALUE_222))
                .thenAssertSingleRateDetailData(QUALIFIED, 1, JAN_04_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_333, VALUE_111, VALUE_333, VALUE_NEGATIVE_ONE, VALUE_333))
                .thenAssertSingleRateDetailData(QUALIFIED, 2, JAN_11_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_1_ID,
                        Arrays.asList(VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_111, VALUE_NEGATIVE_ONE, VALUE_NEGATIVE_ONE));
    }

    @Test
    public void shouldIgnoreOutsideOfRangeHeader() throws ParseException {

        when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getDate(31, 01, 2015));
        when(tenantCrudService.findByNamedQuery(eq(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES), anyMapOf(String.class, Object.class))).thenReturn(Collections.emptyList());
        givenSeason(JAN_01_2015_DATE, JAN_15_2015_DATE, ACCOM_TYPE_CODE_1, DOW_RATES_ALL_111, DOW_RATES_ALL_111)
                //Set start date before SystemDate, system date is 2015-01-01
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(false)
                .whenConverterCalled(QUALIFIED)
                .assertEmptyResult(qualifiedRatesFinalOutput)
                .assertRateHeaderDBCalls(0, 0, 0, JAN_01_2015_DATE, false);
    }

    @Test
    public void shouldReturnNullWhenNoRateFoundInDBWithGivenName() {
        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Collections.emptyList());
        assertNull(qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Collections.emptyList());
        assertNull(unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnRateFoundInDBWhenSingleRateFoundIsNonG3Managed() {
        AbstractRate abstractRate = createRateHeader(QUALIFIED);
        abstractRate.setManagedInG3(false);

        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertEquals(abstractRate, qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        abstractRate = createRateHeader(UN_QUALIFIED);
        abstractRate.setManagedInG3(false);
        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertEquals(abstractRate, unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnRateFoundInDBWhenRateIsG3ManagedAndActive() {
        AbstractRate abstractRate = createRateHeader(QUALIFIED);
        abstractRate.setManagedInG3(true);

        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertEquals(abstractRate, qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        abstractRate = createRateHeader(UN_QUALIFIED);
        abstractRate.setManagedInG3(true);
        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertEquals(abstractRate, unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnNullWhenSingleRateFoundIsG3ManagedAndInActive() {
        AbstractRate abstractRate = createRateHeader(QUALIFIED);
        abstractRate.setManagedInG3(true);
        abstractRate.setStatusId(2);

        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertNull(qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Arrays.asList(abstractRate));
        assertNull(unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnActiveRateWhenMultipleRatesFoundInDB() {
        AbstractRate inActiveRate = createRateHeader(QUALIFIED);
        inActiveRate.setStatusId(2);
        AbstractRate activeRate = createRateHeader(QUALIFIED);
        activeRate.setId(2);

        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Arrays.asList(inActiveRate, activeRate));
        assertEquals(activeRate, qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        inActiveRate = createRateHeader(UN_QUALIFIED);
        inActiveRate.setStatusId(2);
        activeRate = createRateHeader(UN_QUALIFIED);
        activeRate.setId(2);
        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Arrays.asList(inActiveRate, activeRate));
        assertEquals(activeRate, unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnNullWhenOnlyMultipleInactiveRatesFoundInDB() {
        AbstractRate inActiveRateOne = createRateHeader(QUALIFIED);
        inActiveRateOne.setStatusId(2);
        inActiveRateOne.setId(1);
        AbstractRate inActiveRateTwo = createRateHeader(QUALIFIED);
        inActiveRateTwo.setStatusId(2);
        inActiveRateTwo.setId(2);

        whenQueryReturns(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, Arrays.asList(inActiveRateOne, inActiveRateTwo));
        assertNull(qualifiedRateConverter.findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, TEST_RATE_NAME));

        inActiveRateOne = createRateHeader(UN_QUALIFIED);
        inActiveRateOne.setStatusId(2);
        inActiveRateOne.setId(1);
        inActiveRateTwo = createRateHeader(UN_QUALIFIED);
        inActiveRateTwo.setStatusId(2);
        inActiveRateTwo.setId(2);
        whenQueryReturns(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, Arrays.asList(inActiveRateOne, inActiveRateTwo));
        assertNull(unqualifiedRateConverter.findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, TEST_RATE_NAME));
    }

    @Test
    public void shouldReturnTrueWhenNotifTypeIsREMOVE() {
        assertTrue(qualifiedRateConverter.isRemove("REMOVE"));
        assertFalse(qualifiedRateConverter.isRemove(NOTIF_TYPE_OVERLAY));
        assertTrue(unqualifiedRateConverter.isRemove("REMOVE"));
        assertFalse(unqualifiedRateConverter.isRemove(NOTIF_TYPE_OVERLAY));
    }


    private void testFlushAndClearToggle(String rateType) {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(rateType)
                .thenAssertSaveWithFlushAndClearCalled();
    }

    private void testFlushAndClearToggleNOTCalled(String rateType) {
        givenSeason(JAN_01_2015_DATE, JAN_10_2015_DATE, ACCOM_TYPE_CODE_2, DOW_RATES_SUN_MON_WED_FRI_111, DOW_RATES_ALL_111)
                .givenAboveSeasonsAddedToRate(TEST_RATE_NAME, JAN_01_2015_DATE, DEC_31_2015_DATE)
                .givenRatesFeatureToggleValueSetTo(true)
                .whenConverterCalled(rateType)
                .thenAssertSaveWithFlushAndClearNOTCalled();
    }
}
