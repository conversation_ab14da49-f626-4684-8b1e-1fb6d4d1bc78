package com.ideas.tetris.pacman.services.configsparam.listeners;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.platform.common.configparams.components.ConfigParamChangeEvent;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AMSRebuildConfigParamListenerTest {
    public static final String CONTEXT = "context";
    private static final String CLIENT_CODE = "BSTN";
    private static final String PROPERTY_CODE = "H1";
    private static final String WORK_CONTEXT = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @InjectMocks
    AMSRebuildConfigParamListener listener;

    ConfigParamChangeEvent event;
    ConfigParameterValue eventValue;
    ConfigParameter parameter;
    @BeforeEach
    public void setUp() throws Exception {
        parameter = new ConfigParameter();
        parameter.setName(FeatureTogglesConfigParamName.AMS_AMS_REBUILD_ENABLED.value());
        eventValue = new ConfigParameterValue();
        eventValue.setConfigParameter(parameter);
        eventValue.setContext(CONTEXT);

        event = new ConfigParamChangeEvent(eventValue);
    }

    @Test
    public void parameterChange_true() {
        eventValue.setValue("true");
        WorkContextType workContext = new WorkContextType();
        workContext.setClientCode(WorkContextType.HYATT);
        PacmanWorkContextHelper.setWorkContext(workContext);

        listener.parameterChange(event);

        verify(pacmanConfigParamsService).addParameterValue(CONTEXT, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true", true);
    }

    @Test
    public void parameterChange_false() {
        eventValue.setValue("false");

        listener.parameterChange(event);

        verify(pacmanConfigParamsService).addParameterValue(CONTEXT, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "false", true);
    }
}