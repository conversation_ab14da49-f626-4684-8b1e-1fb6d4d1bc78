package com.ideas.tetris.pacman.services.groupfinalforecast;

import com.google.common.collect.ImmutableMap;

import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;

class GFFForecastGroupDistributionScenarios {
    private GFFForecastGroupDistributionScenarios() {
    }

    static final class AccomClassGFFConfig {
        private final Integer id;
        private final List<Integer> accomTypeIds;
        private final Integer order;

        AccomClassGFFConfig(Integer id, List<Integer> accomTypeIds, Integer order) {
            this.id = id;
            this.accomTypeIds = accomTypeIds;
            this.order = order;
        }

        public Integer getId() {
            return id;
        }

        public List<Integer> getAccomTypeIds() {
            return accomTypeIds;
        }

        public Integer getOrder() {
            return order;
        }
    }

    static final Map<String, Integer> FG_MS_MAPPINGS =
            ImmutableMap.<String, Integer>builder()
                    .put("Block", 11)
                    .put("Group", 14)
                    .put("Complement", 1)
                    .put("Corporate", 4)
                    .put("Trans", 3)
                    .build();

    static final Map<String, Integer> ACCOM_CAPACITY_MAPPINGS =
            ImmutableMap.<String, Integer>builder()
                    .put("DBL", 102)
                    .put("Q", 118)
                    .put("K", 136)
                    .put("DLX", 35)
                    .put("STE", 15)
                    .build();

    static final Map<String, Integer> FG_CODE_ID_MAPPINGS =
            ImmutableMap.<String, Integer>builder()
                    .put("Complement", 1)
                    .put("Block", 8)
                    .put("Group", 10)
                    .build();

    static final Map<String, AccomClassGFFConfig> AC_GFF_CONFIG =
            ImmutableMap.<String, AccomClassGFFConfig>builder()
                    .put("STD", new AccomClassGFFConfig(2, asList(6, 7, 8), 1))
                    .put("DLX", new AccomClassGFFConfig(3, singletonList(4), 2))
                    .put("STE", new AccomClassGFFConfig(4, singletonList(5), 3))
                    .build();

    static final Object[][][][] GFF_FG_DISTRIBUTION_SCENARIOS = {
            // SCENARIO 1
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 30.0, 49.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 9.0, 49.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 10.0, 49.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 30.0},
                            {"Block", "DLX", 9.0},
                            {"Block", "STE", 10.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 46.0},
                            {"Block", 80.0},
                            {"Group", 99.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 225.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 34.0, true, true},
                            {"Complement", "DLX", 6.0, true, true},
                            {"Complement", "STE", 6.0, true, true},
                            {"Block", "STD", 61.0, true, true},
                            {"Block", "DLX", 9.0, true, true},
                            {"Block", "STE", 10.0, true, true},
                            {"Group", "STD", 78.0, true, true},
                            {"Group", "DLX", 10.0, true, true},
                            {"Group", "STE", 11.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 2
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 30.0, 49.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 9.0, 49.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 10.0, 49.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 30.0},
                            {"Block", "DLX", 4.0},
                            {"Block", "STE", 1.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 46.0},
                            {"Block", 52.0},
                            {"Group", 54.55}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 152.55}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 34.0, true, true},
                            {"Complement", "DLX", 6.0, true, true},
                            {"Complement", "STE", 6.0, true, true},
                            {"Block", "STD", 47.0, true, true},
                            {"Block", "DLX", 4.0, true, true},
                            {"Block", "STE", 1.0, true, true},
                            {"Group", "STD", 42.55, true, true},
                            {"Group", "DLX", 10.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    },
                    // Overrides need to be saved
                    {
                            {"Save these overrides"}
                    }
            },
            // SCENARIO 3
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Group", "Group", "STD", 356, 1, 9.0, 15.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 15.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 2.0, 15.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Group", "STD", 9.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Group", 8.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 8.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Group", "STD", 2.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 4
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Group", "Group", "STD", 356, 1, 9.0, 15.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 15.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 2.0, 15.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Group", "STD", 9.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Group", 6.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 6.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 5
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Group", "Group", "STD", 356, 1, 9.0, 15.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 15.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 2.0, 15.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Group", "STD", 9.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Group", 1.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 1.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 1.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    },
                    // Overrides need to be saved
                    {
                            {"Save these overrides"}
                    }
            },
            // SCENARIO 6 - 18a
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 30.0, 49.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 9.0, 49.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 10.0, 49.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 30.0},
                            {"Block", "DLX", 9.0},
                            {"Block", "STE", 10.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 350.0},
                            {"Block", 55.0},
                            {"Group", 0.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 405.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 338.0, true, true},
                            {"Complement", "DLX", 6.0, true, true},
                            {"Complement", "STE", 6.0, true, true},
                            {"Block", "STD", 36.0, true, true},
                            {"Block", "DLX", 9.0, true, true},
                            {"Block", "STE", 10.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 471.0}
                    }
            },
            // SCENARIO 7 - 18b
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 30.0, 49.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 9.0, 49.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 10.0, 49.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 30.0},
                            {"Block", "DLX", 9.0},
                            {"Block", "STE", 10.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 0.0},
                            {"Block", 0.0},
                            {"Group", 0.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 0.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 0.0, true, true},
                            {"Block", "STD", 0.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 8 - 18c
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 0.0},
                            {"Block", 300.0},
                            {"Group", 0.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 300.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 0.0, true, true},
                            {"Block", "STD", 300.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 9 - 18d
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 0.0},
                            {"Block", 400.0},
                            {"Group", 0.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 400.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 0.0, true, true},
                            {"Block", "STD", 356.0, true, true},
                            {"Block", "DLX", 35.0, true, true},
                            {"Block", "STE", 9.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 466.0}
                    }
            },
            // SCENARIO 10 - 18e
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 5.0},
                            {"Block", 5.0},
                            {"Group", 15.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 25.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 5.0, true, true},
                            {"Block", "STD", 5.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 11.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 11 - 18f
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 1.0},
                            {"Block", 360.0},
                            {"Group", 15.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 376.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 356.0, true, true},
                            {"Block", "DLX", 4.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 11.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 442.0}
                    }
            },
            // SCENARIO 12 - 18g
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 48, 308},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 14, 21},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 4, 11},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 48, 308},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 14, 21},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 4, 11},
                            {"Group", "Group", "STD", 356, 1, 33.0, 54.0, 48, 308},
                            {"Group", "Group", "DLX", 35, 0, 10.0, 54.0, 14, 21},
                            {"Group", "Group", "STE", 15, 0, 11.0, 54.0, 4, 11}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 42},
                            {"Trans", "STD", 6},
                            {"Corporate", "DLX", 10},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 2},
                            {"Trans", "STE", 2}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 33.0},
                            {"Group", "DLX", 10.0},
                            {"Group", "STE", 11.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 1.0},
                            {"Block", 300.0},
                            {"Group", 2.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 303.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 300.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    },
                    // Overrides need to be saved
                    {
                            {"Save these overrides"}
                    }
            },
            // SCENARIO 13 - 18h
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 21.0, 33.0, 0, 356},
                            {"Block", "Complement", "DLX", 35, 0, 6.0, 33.0, 0, 35},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 33.0, 0, 15},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 0, 356},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 0, 35},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 0, 15}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 0},
                            {"Trans", "STD", 0},
                            {"Corporate", "DLX", 0},
                            {"Trans", "DLX", 0},
                            {"Corporate", "STE", 0},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 21.0},
                            {"Complement", "DLX", 6.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 1.0},
                            {"Block", 376.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 377.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 356.0, true, true},
                            {"Block", "DLX", 20.0, true, true},
                            {"Block", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 14 - 18i
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 0, 356},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 0, 35},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 0, 15},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 0, 356},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 0, 35},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 0, 15},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 0, 356},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 0, 35},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 0, 15}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 0},
                            {"Trans", "STD", 0},
                            {"Corporate", "DLX", 0},
                            {"Trans", "DLX", 0},
                            {"Corporate", "STE", 0},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 1.0},
                            {"Block", 311.0},
                            {"Group", 30.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 342.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 0.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 311.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 30.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    },
                    // Overrides need to be saved
                    {
                            {"Save these overrides"}
                    }
            },
            // SCENARIO 15 - 18j
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 0, 356},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 0, 35},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 0, 15},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 0, 356},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 0, 35},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 0, 15},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 0, 356},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 0, 35},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 0, 15}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 0},
                            {"Trans", "STD", 0},
                            {"Corporate", "DLX", 0},
                            {"Trans", "DLX", 0},
                            {"Corporate", "STE", 0},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 354.0},
                            {"Block", 1.0},
                            {"Group", 1.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 356.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 352.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 1.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 1.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 16 - 18k
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 0, 356},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 0, 35},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 0, 15},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 0, 356},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 0, 35},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 0, 15},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 0, 356},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 0, 35},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 0, 15}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 0},
                            {"Trans", "STD", 0},
                            {"Corporate", "DLX", 0},
                            {"Trans", "DLX", 0},
                            {"Corporate", "STE", 0},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 10.0},
                            {"Block", 100.0},
                            {"Group", 1.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 111.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 8.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 100.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 1.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides do not exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"}
                    }
            },
            // SCENARIO 17 - 18m
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 0, 356},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 0, 35},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 0, 15},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 0, 356},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 0, 35},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 0, 15},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 0, 356},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 0, 35},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 0, 15}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 0},
                            {"Trans", "STD", 0},
                            {"Corporate", "DLX", 0},
                            {"Trans", "DLX", 0},
                            {"Corporate", "STE", 0},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 420.0},
                            {"Block", 0.0},
                            {"Group", 0.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 420.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 356.0, true, false},
                            {"Complement", "DLX", 35.0, true, false},
                            {"Complement", "STE", 29.0, false, false},
                            {"Block", "STD", 0.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 0.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 420.0}
                    }
            },
            // SCENARIO 18 - 18o
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 52, 304},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 20, 15},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 10, 5},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 52, 304},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 20, 15},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 10, 5},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 52, 304},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 20, 15},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 10, 5}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 36},
                            {"Trans", "STD", 16},
                            {"Corporate", "DLX", 8},
                            {"Trans", "DLX", 12},
                            {"Corporate", "STE", 7},
                            {"Trans", "STE", 3}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 400.0},
                            {"Block", 400.0},
                            {"Group", 100.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 900.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 356.0, true, true},
                            {"Complement", "DLX", 35.0, true, true},
                            {"Complement", "STE", 9.0, true, true},
                            {"Block", "STD", 356.0, true, true},
                            {"Block", "DLX", 35.0, true, true},
                            {"Block", "STE", 9.0, true, true},
                            {"Group", "STD", 100.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 982.0}
                    }
            },
            // SCENARIO 19 - 18p
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.89, 12.89, 52, 304},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.89, 20, 15},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.89, 10, 5},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 52, 304},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 20, 15},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 10, 5},
                            {"Group", "Group", "STD", 356, 1, 3.0, 3.0, 52, 304},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 3.0, 20, 15},
                            {"Group", "Group", "STE", 15, 0, 0.0, 3.0, 10, 5}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 36},
                            {"Trans", "STD", 16},
                            {"Corporate", "DLX", 8},
                            {"Trans", "DLX", 12},
                            {"Corporate", "STE", 7},
                            {"Trans", "STE", 3}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.89},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 100.0},
                            {"Block", 100.0},
                            {"Group", 176.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 376.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 98.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 100.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 176.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 458.0}
                    }
            },
            // SCENARIO 20 - Negative block capacity
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.0, 12.0, 390, -34},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.0, 12, 23},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.0, 25, -10},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 390, -34},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 12, 23},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 25, -10},
                            {"Group", "Group", "STD", 356, 1, 3.0, 9.0, 390, -34},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 9.0, 12, 23},
                            {"Group", "Group", "STE", 15, 0, 2.0, 9.0, 25, -10}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 301},
                            {"Trans", "STD", 89},
                            {"Corporate", "DLX", 8},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 13}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.0},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 100.0},
                            {"Block", 68.0},
                            {"Group", 176.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 344.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 98.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 68.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 170.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 771.0}
                    }
            },
            // SCENARIO 21 - Negative block capacity (all Accom Classes)
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 17.0, 25.0, 415, -59},
                            {"Block", "Complement", "DLX", 35, 0, 3.0, 25.0, 50, -15},
                            {"Block", "Complement", "STE", 15, 0, 5.0, 25.0, 47, -32},
                            {"Group", "Block", "STD", 356, 1, 8.0, 17.0, 415, -59},
                            {"Group", "Block", "DLX", 35, 0, 7.0, 17.0, 50, -15},
                            {"Group", "Block", "STE", 15, 0, 2.0, 17.0, 47, -32},
                            {"Group", "Group", "STD", 356, 1, 0.0, 0.0, 415, -59},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 0.0, 50, -15},
                            {"Group", "Group", "STE", 15, 0, 0.0, 0.0, 47, -32}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 380},
                            {"Trans", "STD", 35},
                            {"Corporate", "DLX", 30},
                            {"Trans", "DLX", 20},
                            {"Corporate", "STE", 35},
                            {"Trans", "STE", 12}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 17.0},
                            {"Complement", "DLX", 3.0},
                            {"Complement", "STE", 5.0},
                            {"Block", "STD", 8.0},
                            {"Block", "DLX", 7.0},
                            {"Block", "STE", 2.0},
                            {"Group", "STD", 0.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 117.44},
                            {"Block", 88.23},
                            {"Group", 171.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 376.67}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 109.44, true, true},
                            {"Complement", "DLX", 3.0, true, true},
                            {"Complement", "STE", 5.0, true, true},
                            {"Block", "STD", 79.23, true, true},
                            {"Block", "DLX", 7.0, true, true},
                            {"Block", "STE", 2.0, true, true},
                            {"Group", "STD", 171.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 888.67}
                    }
            },
            // SCENARIO 22 - Negative block capacity (all Accom Classes), Zero forecast
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 0.0, 0.0, 415, -59},
                            {"Block", "Complement", "DLX", 35, 0, 0.0, 0.0, 50, -15},
                            {"Block", "Complement", "STE", 15, 0, 0.0, 0.0, 47, -32},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 415, -59},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 50, -15},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 47, -32},
                            {"Group", "Group", "STD", 356, 1, 0.0, 0.0, 415, -59},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 0.0, 50, -15},
                            {"Group", "Group", "STE", 15, 0, 0.0, 0.0, 47, -32}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 380},
                            {"Trans", "STD", 35},
                            {"Corporate", "DLX", 30},
                            {"Trans", "DLX", 20},
                            {"Corporate", "STE", 35},
                            {"Trans", "STE", 12}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 0.0},
                            {"Complement", "DLX", 0.0},
                            {"Complement", "STE", 0.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 0.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 117.44},
                            {"Block", 88.23},
                            {"Group", 171.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 376.67}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 117.44, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 0.0, true, true},
                            {"Block", "STD", 88.23, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 171.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 888.67}
                    }
            },
            // SCENARIO 23 - Zero block capacity
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.0, 12.0, 356, 0},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.0, 12, 23},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.0, 12, 3},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 12, 23},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 12, 3},
                            {"Group", "Group", "STD", 356, 1, 3.0, 9.0, 356, 0},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 9.0, 12, 23},
                            {"Group", "Group", "STE", 15, 0, 2.0, 9.0, 12, 3}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 300},
                            {"Trans", "STD", 56},
                            {"Corporate", "DLX", 8},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.0},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 100.0},
                            {"Block", 68.0},
                            {"Group", 176.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 344.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 98.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 68.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 170.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 724.0}
                    }
            },
            // SCENARIO 24 - Zero block capacity, Zero Forecast
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Block", "Complement", "DLX", 35, 0, 0.0, 0.0, 12, 23},
                            {"Block", "Complement", "STE", 15, 0, 0.0, 0.0, 12, 3},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 12, 23},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 12, 3},
                            {"Group", "Group", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Group", "Group", "DLX", 35, 0, 0.0, 0.0, 12, 23},
                            {"Group", "Group", "STE", 15, 0, 0.0, 0.0, 12, 3}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 300},
                            {"Trans", "STD", 56},
                            {"Corporate", "DLX", 8},
                            {"Trans", "DLX", 4},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 0}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 0.0},
                            {"Complement", "DLX", 0.0},
                            {"Complement", "STE", 0.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 0.0},
                            {"Group", "DLX", 0.0},
                            {"Group", "STE", 0.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 100.0},
                            {"Block", 68.0},
                            {"Group", 176.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 344.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 100.0, true, true},
                            {"Complement", "DLX", 0.0, true, true},
                            {"Complement", "STE", 0.0, true, true},
                            {"Block", "STD", 68.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 176.0, true, true},
                            {"Group", "DLX", 0.0, true, true},
                            {"Group", "STE", 0.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 724.0}
                    }
            },
            // SCENARIO 25 - Zero block capacity (all Accom Classes)
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 14.0, 25.0, 356, 0},
                            {"Block", "Complement", "DLX", 35, 0, 5.0, 25.0, 35, 0},
                            {"Block", "Complement", "STE", 15, 0, 6.0, 25.0, 15, 0},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 35, 0},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 15, 0},
                            {"Group", "Group", "STD", 356, 1, 7.0, 17.0, 356, 0},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 17.0, 35, 0},
                            {"Group", "Group", "STE", 15, 0, 6.0, 17.0, 15, 0}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 300},
                            {"Trans", "STD", 56},
                            {"Corporate", "DLX", 20},
                            {"Trans", "DLX", 15},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 3}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 14.0},
                            {"Complement", "DLX", 5.0},
                            {"Complement", "STE", 6.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 7.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 6.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 95.0},
                            {"Block", 79.0},
                            {"Group", 134.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 308.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 84.0, true, true},
                            {"Complement", "DLX", 5.0, true, true},
                            {"Complement", "STE", 6.0, true, true},
                            {"Block", "STD", 79.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 124.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 6.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 714.0}
                    }
            },
            // SCENARIO 26 - Negative block capacity
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.0, 12.0, 212, 144},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.0, 67, -32},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.0, 32, -17},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 212, 144},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 67, -32},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 32, -17},
                            {"Group", "Group", "STD", 356, 1, 3.0, 9.0, 212, 144},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 9.0, 67, -32},
                            {"Group", "Group", "STE", 15, 0, 2.0, 9.0, 32, -17}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 190},
                            {"Trans", "STD", 22},
                            {"Corporate", "DLX", 50},
                            {"Trans", "DLX", 17},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 20}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.0},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 100.0},
                            {"Block", 68.0},
                            {"Group", 176.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 344.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 98.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 68.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 170.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 655.0}
                    }
            },
            // SCENARIO 27 - Negative, Zero block capacity
            {
                    // REFERENCE
                    {
                            {"FGName", "FGCode", "ACCode", "accomCap", "IsMasterClass", "occFcstAC", "fgFcst", "NonBlockOnBooksAC", "capAvailForOVERRIDEAtACLvl"},
                            {"Block", "Complement", "STD", 356, 1, 10.0, 12.0, 356, 0},
                            {"Block", "Complement", "DLX", 35, 0, 1.0, 12.0, 67, -32},
                            {"Block", "Complement", "STE", 15, 0, 1.0, 12.0, 32, -17},
                            {"Group", "Block", "STD", 356, 1, 0.0, 0.0, 356, 0},
                            {"Group", "Block", "DLX", 35, 0, 0.0, 0.0, 67, -32},
                            {"Group", "Block", "STE", 15, 0, 0.0, 0.0, 32, -17},
                            {"Group", "Group", "STD", 356, 1, 3.0, 9.0, 356, 0},
                            {"Group", "Group", "DLX", 35, 0, 4.0, 9.0, 67, -32},
                            {"Group", "Group", "STE", 15, 0, 2.0, 9.0, 32, -17}
                    },
                    // Non Block data for Accom Classes
                    {
                            {"Forecast Group", "Accom Class", "On Books"},
                            {"Corporate", "STD", 334},
                            {"Trans", "STD", 22},
                            {"Corporate", "DLX", 50},
                            {"Trans", "DLX", 17},
                            {"Corporate", "STE", 12},
                            {"Trans", "STE", 20}
                    },
                    // Occupancy Forecast
                    {
                            {"Forecast Group", "Accom Class", "OF"},
                            {"Complement", "STD", 10.0},
                            {"Complement", "DLX", 1.0},
                            {"Complement", "STE", 1.0},
                            {"Block", "STD", 0.0},
                            {"Block", "DLX", 0.0},
                            {"Block", "STE", 0.0},
                            {"Group", "STD", 3.0},
                            {"Group", "DLX", 4.0},
                            {"Group", "STE", 2.0}
                    },
                    // Override at FG
                    {
                            {"Forecast Group", "Override"},
                            {"Complement", 129.0},
                            {"Block", 113.0},
                            {"Group", 101.0}
                    },
                    // Expected Block Occupancy Forecast aggregation
                    {
                            {"OF Aggregation", 343.0}
                    },
                    // Expected distribution
                    {
                            {"Forecast Group", "Accom Class", "Distribution", "AC Validity", "FG Validity"},
                            {"Complement", "STD", 127.0, true, true},
                            {"Complement", "DLX", 1.0, true, true},
                            {"Complement", "STE", 1.0, true, true},
                            {"Block", "STD", 113.0, true, true},
                            {"Block", "DLX", 0.0, true, true},
                            {"Block", "STE", 0.0, true, true},
                            {"Group", "STD", 95.0, true, true},
                            {"Group", "DLX", 4.0, true, true},
                            {"Group", "STE", 2.0, true, true}
                    },
                    // Overrides exceed physical capacity
                    {
                            {"Available Capacity", "Exceeding Capacity"},
                            {406.0, 798.0}
                    }
            }
    };
}
