package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDTARange;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OptimalBarType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.product.FloorType;
import com.ideas.tetris.pacman.services.product.OverridableProductEnum;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.aggregator.ArgumentsAccessor;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.CHILD_ONE_OF_INDEPENDENT_PRODUCT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.CHILD_ONE_OF_SYSTEM_DEFAULT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GRANDCHILD_ONE_OF_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.filterOutputsByProduct;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.getDefaultPrecisionValueOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class AgileRatesCPOptimalBARServiceTest extends AgileRatesServiceTest {

    private CPOptimalBARService service = new CPOptimalBARService();

    @Override
    CPBarDecisionService getCPBarDecisionService() {
        return service;
    }

    @Override
    protected boolean isPerPersonEnabled() {
        return true;
    }

    @BeforeEach
    public void setup() {
        super.setup();

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("110"), BigDecimal.ZERO, independentProduct.getId());
    }

    @Test
    public void barProductOnlyWhenAgileRatesEnabled() {
        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Verify that the only decision that was created was for the barProduct
        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertEquals(barProduct, cpDecisionBAROutput.getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void onlyPackageProduct() {
        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add breakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput breakfastPackagedOutput = addCPDecisionBAROutput(breakfastPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(20.0)), null, null);

        // Add breakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput breakfastAndSpaPackagedOutput = addCPDecisionBAROutput(breakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(120.0)), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentProductOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add independentBreakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput independentBreakfastPackagedOutput = addCPDecisionBAROutput(independentBreakfastPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(20.0)), null, null);

        // Add independentBreakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput independentBreakfastAndSpaPackagedOutput = addCPDecisionBAROutput(independentBreakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(120.0)), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(breakfastPackagedOutput, cpDecisionBAROutputs.get(1));
        assertEquals(breakfastPackagedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(20.0)), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(breakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(2));
        assertEquals(breakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(120.0)), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentProductOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentBreakfastPackagedOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentBreakfastPackagedProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110).add(BigDecimal.valueOf(20.0)), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentBreakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentBreakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110).add(BigDecimal.valueOf(120.0)), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void packageProduct_followsRoundingRules() {
        setRoundingRule(9, 8, 7, independentProduct.getId());

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add breakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput breakfastPackagedOutput = addCPDecisionBAROutput(breakfastPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(20.0)), null, null);

        // Add breakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput breakfastAndSpaPackagedOutput = addCPDecisionBAROutput(breakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(120.0)), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add IndependentBreakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput independentBreakfastPackagedOutput = addCPDecisionBAROutput(independentBreakfastPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(20.0)), null, null);

        // Add IndependentBreakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput independentBreakfastAndSpaPackagedOutput = addCPDecisionBAROutput(independentBreakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(120.0)), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(breakfastPackagedOutput, cpDecisionBAROutputs.get(1));
        assertEquals(breakfastPackagedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(119.87), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(breakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(2));
        assertEquals(breakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentBreakfastPackagedOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentBreakfastPackagedProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(129.87), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentBreakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentBreakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void onlyPackageProductWithOffsetsAndSupplements() {
        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));

        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());

        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(10.0));

        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add breakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput breakfastPackagedOutput = addCPDecisionBAROutput(breakfastPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(20.0)), null, null);

        // Add breakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput breakfastAndSpaPackagedOutput = addCPDecisionBAROutput(breakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(120.0)), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add independentBreakfastPackagedProduct CPDecisionBAROutput record = OptimalBAR + (10*2)
        CPDecisionBAROutput independentBreakfastPackagedOutput = addCPDecisionBAROutput(independentBreakfastPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(20.0)), null, null);

        // Add independentBreakfastAndSpaPackagedProduct CPDecisionBAROutput record = OptimalBAR + ((10+50) * 2)
        CPDecisionBAROutput independentBreakfastAndSpaPackagedOutput = addCPDecisionBAROutput(independentBreakfastAndSpaPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110).add(BigDecimal.valueOf(120.0)), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(breakfastPackagedOutput, cpDecisionBAROutputs.get(1));
        assertEquals(breakfastPackagedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(20.0)), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(breakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(2));
        assertEquals(breakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED.add(BigDecimal.valueOf(120.0)), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentBreakfastPackagedOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentBreakfastPackagedProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110).add(BigDecimal.valueOf(20.0)), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.0), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentBreakfastAndSpaPackagedOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentBreakfastAndSpaPackagedProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110).add(BigDecimal.valueOf(120.0)), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void onlyFencedProduct() {
        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add loyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add independentLoyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(100.0), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(4, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(2));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(2).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());
    }

    @Test
    public void onlyFencedProductWithOffsetsAndSupplements() {
        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));

        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());

        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(10.0));

        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add breakfastPackagedProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add breakfastPackagedProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(100.0), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(4, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(2));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(2).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());
    }

    @Test
    public void fencedAndPackagedProduct() {
        BigDecimal optimalBAR = new BigDecimal("100.00");
        BigDecimal independentOptimalBAR = new BigDecimal("110.00");

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, optimalBAR, null, null);

        // Add loyaltyFencedAndPackagedProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedAndPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(140.0), null, null);

        // Add independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, independentOptimalBAR, null, null);

        // Add independentLoyaltyFencedAndPackagedProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedAndPackagedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(150.0), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(4, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedAndPackagedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("190.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(2));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(2).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentLoyaltyFencedAndPackagedProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("200.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());
    }

    @Test
    public void offsetProductForNonBaseRTOnly() {
        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - loyaltyFencedProduct
        CPDecisionBAROutput loyaltyFencedProductNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the Base RT - independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - independentLoyaltyFencedProduct
        CPDecisionBAROutput independentLoyaltyFencedProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-5.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);
        addProductRateOffset(loyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("6.5"));
        addProductRateOffset(independentLoyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("11.5"));

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(loyaltyFencedProductNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentLoyaltyFencedProductNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("39.00"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("12.00"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void offsetProductForNonBaseRTWithPriceExcludeRC() {
        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - loyaltyFencedProduct
        CPDecisionBAROutput loyaltyFencedProductNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the Base RT - Independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("37.00"), null, null);

        // Add the non-Base RT - Independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - independentLoyaltyFencedProduct
        CPDecisionBAROutput independentLoyaltyFencedProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("70.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-10.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);
        addProductRateOffset(loyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("6.5"));
        addProductRateOffset(independentLoyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("11.5"));
        AccomType nonBaseRoomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", nonBaseRT).parameters());
        PricingAccomClass pricingAccomClass = tenantCrudService().findAll(PricingAccomClass.class).stream()
                .filter(pac -> pac.getAccomClass().equals(nonBaseRoomType.getAccomClass()))
                .findFirst().orElse(null);
        pricingAccomClass.setPriceExcluded(true);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(loyaltyFencedProductNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("37.00"), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentLoyaltyFencedProductNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("44.00"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("12.00"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void agileProductDefaultOffsetAdjustsForOffset() {
        Product golfProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Golf", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(golfProduct, AgileRatesOffsetMethod.FIXED, BigDecimal.TEN);

        // Add BAR CPDecisionBAROutput records
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addCPDecisionBAROutput(golfProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        Product independentGolfProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Independent Golf", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentGolfProduct, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(15));

        // Add independentProduct CPDecisionBAROutput records
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);
        addCPDecisionBAROutput(independentGolfProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(4, cpDecisionBAROutputs.size());

        // Verify the BAR results
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        // Verify the golf package results
        assertBigDecimalEquals(new BigDecimal("110.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        // Verify the Independent results
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(2).getFinalBAR());

        // Verify the independent golf package results
        assertBigDecimalEquals(new BigDecimal("125.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());
    }

    /**
     * finalBar = BAR * (1 + optimalBar(of agile)/100)
     * - When finalBar is less than product-floor we set agileFinalBar = agileProductFloor
     */
    @ParameterizedTest
    @CsvSource({
            "80, 10, 20, 10, 1, 110, 0",
            "80, -40, 40, -20, 1, 80, 0",
            "170, 10, 20, 90, 1, 190, 0",
            "80, -40, -20, -30, 1, 80, 0",
            "170, -40, 40, 30, 1, 170, 0"
    })
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_One(ArgumentsAccessor arguments) {
        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);

        setUPAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimalUtil.ONE_HUNDRED, expectedFinalBar, expectedAdjustmentValue);
    }

    /**
     * finalBar = BAR * (1 + optimalBar(of agile)/100)
     * - When finalBar is less than product-floor we set agileFinalBar = agileProductFloor
     */
    @ParameterizedTest
    @CsvSource({
            "80, 10, 20, 10, 1, 121, 0",
            "80, -40, 40, -20, 1, 88, 0",
            "170, 10, 20, 90, 1, 209, 0",
            "80, -40, -20, -30, 1, 80, 0",
            "170, -40, 40, 30, 1, 170, 0"
    })
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_One(ArgumentsAccessor arguments) {
        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);

        setUpIndependentAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimal.valueOf(110), expectedFinalBar, expectedAdjustmentValue);
    }

    private void verifyThatFinalBarIs(BigDecimal barProductFinalBar, double expectedAgileFinalBar, double expectedAdjustmentValue) {
        // Get the DecisionDailybarOutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(2, cpDecisionBAROutputs.size());

        // Verify the BAR results
        assertBigDecimalEquals(barProductFinalBar, cpDecisionBAROutputs.get(0).getFinalBAR());

        // Verify the golf package results
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAdjustmentValue), cpDecisionBAROutputs.get(1).getAdjustmentValue());
    }

    private void verifyThatFinalBarIs(BigDecimal barProductFinalBar, double expectedAgileFinalBar, double expectedAdjustmentValue, double expectedAgileChildFinalBar, double expectedChildAdjustmentValue) {
        // Get the DecisionDailybarOutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(3, cpDecisionBAROutputs.size());

        // Verify the BAR results
        assertBigDecimalEquals(barProductFinalBar, cpDecisionBAROutputs.get(0).getFinalBAR());

        // Verify the product results
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAdjustmentValue), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        // Verify the product child results
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileChildFinalBar), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedChildAdjustmentValue), cpDecisionBAROutputs.get(2).getAdjustmentValue());
    }

    private CPDecisionBAROutput setUPAgileProductData(double productFloorValue, double floorAdjustment, double ceilingAdjustment, double agileOptimalBar, int decisionReasonTypeId) {
        // Add Agile CPDecisionBAROutput record
        Product golfProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Golf", barProduct);
        golfProduct.setOptimized(true);
        golfProduct.setFloor(BigDecimal.valueOf(productFloorValue));
        golfProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        addProductRateOffset(golfProduct, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        CPDecisionBAROutput agileCPBarOutput = addCPDecisionBAROutput(golfProduct, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarOutput);
        return agileCPBarOutput;
    }

    private CPDecisionBAROutput setUpIndependentAgileProductData(double productFloorValue, double floorAdjustment, double ceilingAdjustment, double agileOptimalBar, int decisionReasonTypeId) {
        // Add Agile CPDecisionBAROutput record
        Product independentGolfProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Independent Golf", independentProduct);
        independentGolfProduct.setOptimized(true);
        independentGolfProduct.setFloor(BigDecimal.valueOf(productFloorValue));
        independentGolfProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        addProductRateOffset(independentGolfProduct, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add independentProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);
        CPDecisionBAROutput agileCPBarOutput = addCPDecisionBAROutput(independentGolfProduct, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarOutput);
        return agileCPBarOutput;
    }

    private List<CPDecisionBAROutput> setUPAgileProductData(Product product, Product childProduct, double productFloorValue, double floorAdjustment, double ceilingAdjustment, double agileOptimalBar, int decisionReasonTypeId) {
        // Add Agile CPDecisionBAROutput record
        product.setOptimized(true);
        product.setFloor(BigDecimal.valueOf(productFloorValue));
        product.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(product);
        addProductRateOffset(product, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add Agile CPDecisionBAROutput Child record
        childProduct.setOptimized(true);
        childProduct.setFloor(BigDecimal.valueOf(productFloorValue));
        childProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(childProduct);
        addProductRateOffset(childProduct, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        CPDecisionBAROutput agileCPBarOutput = addCPDecisionBAROutput(product, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarOutput);

        CPDecisionBAROutput agileCPBarChildOutput = addCPDecisionBAROutput(childProduct, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarChildOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarChildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarChildOutput);

        return Arrays.asList(agileCPBarOutput, agileCPBarChildOutput);
    }

    private List<CPDecisionBAROutput> setUpIndependentAgileProductData(Product product, Product childProduct, double productFloorValue, double floorAdjustment, double ceilingAdjustment, double agileOptimalBar, int decisionReasonTypeId) {
        // Add Agile CPDecisionBAROutput record
        product.setOptimized(true);
        product.setFloor(BigDecimal.valueOf(productFloorValue));
        product.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(product);
        addProductRateOffset(product, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add Agile CPDecisionBAROutput Child record
        childProduct.setOptimized(true);
        childProduct.setFloor(BigDecimal.valueOf(productFloorValue));
        childProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(childProduct);
        addProductRateOffset(childProduct, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(floorAdjustment), BigDecimal.valueOf(ceilingAdjustment));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);
        CPDecisionBAROutput agileCPBarOutput = addCPDecisionBAROutput(product, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarOutput);

        CPDecisionBAROutput agileCPBarChildOutput = addCPDecisionBAROutput(childProduct, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        agileCPBarChildOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        agileCPBarChildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(agileCPBarChildOutput);

        return Arrays.asList(agileCPBarOutput, agileCPBarChildOutput);
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, floorAdjustment={2}, ceilingAdjustment={3}, agileOptimalBar={4}")
    @CsvSource({
            "80, 10, 20, 10, 15, 80, 0",
            "80, -40, 40, -20, 15, 80, 0",
            "170, 10, 20, 90, 15, 170, 0",
            "80, -40, -20, -30, 15, 80, 0",
            "170, -40, 40, 30, 15, 170, 0"
    })
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldAlwaysSetToProductFloor(ArgumentsAccessor arguments) {
        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);

        setUPAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimalUtil.ONE_HUNDRED, expectedFinalBar, expectedAdjustmentValue);
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, floorAdjustment={2}, ceilingAdjustment={3}, agileOptimalBar={4}")
    @CsvSource({
            "80, 10, 20, 10, 15, 80, 0",
            "80, -40, 40, -20, 15, 80, 0",
            "170, 10, 20, 90, 15, 170, 0",
            "80, -40, -20, -30, 15, 80, 0",
            "170, -40, 40, 30, 15, 170, 0"
    })
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldAlwaysSetToProductFloor(ArgumentsAccessor arguments) {
        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);

        setUpIndependentAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimal.valueOf(110), expectedFinalBar, expectedAdjustmentValue);
    }

    @Test
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldThrowExceptionWhenProductFloorIsZero() {
        double productFloorValue = 0.0;
        double floorAdjustment = 10;
        double ceilingAdjustment = 20;
        double agileOptimalBar = 60;
        int decisionReasonTypeId = 15;
        CPDecisionBAROutput agileCPBarOutput = setUPAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);


        Exception exception = assertThrows(TetrisException.class, () ->
                service.roundOptimalBARs(startDate, endDate));

        String expectedMessage = "Tetris Errors: [0 - UNEXPECTED_ERROR - Configuration Error:Agile product floor value is " +
                "set to ZERO (0.00) for optimized product:" + agileCPBarOutput.getProduct().getName() + " and decisionTypeId= 15 received for " +
                "{Date:" + agileCPBarOutput.getArrivalDate() + ", RoomType:" + agileCPBarOutput.getAccomType().getAccomTypeCode() + ", productId:" + agileCPBarOutput.getProduct().getId() + ", decisionId:" + agileCPBarOutput.getDecisionId() + "} - User.id: 11403 - Workcontext: null]";
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldThrowExceptionWhenProductFloorIsZero() {
        double productFloorValue = 0.0;
        double floorAdjustment = 10;
        double ceilingAdjustment = 20;
        double agileOptimalBar = 60;
        int decisionReasonTypeId = 15;
        CPDecisionBAROutput agileCPBarOutput = setUpIndependentAgileProductData(productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);


        Exception exception = assertThrows(TetrisException.class, () ->
                service.roundOptimalBARs(startDate, endDate));

        String expectedMessage = "Tetris Errors: [0 - UNEXPECTED_ERROR - Configuration Error:Agile product floor value is " +
                "set to ZERO (0.00) for optimized product:" + agileCPBarOutput.getProduct().getName() + " and decisionTypeId= 15 received for " +
                "{Date:" + agileCPBarOutput.getArrivalDate() + ", RoomType:" + agileCPBarOutput.getAccomType().getAccomTypeCode() + ", productId:" + agileCPBarOutput.getProduct().getId() + ", decisionId:" + agileCPBarOutput.getDecisionId() + "} - User.id: 11403 - Workcontext: null]";
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    public void roundOptimalBARsMixedOptimizedMixedOffsetsWithMixedPackage() {
        //GIVEN
        //Rounding Rules 9.87
        setRoundingRule(9, 8, 7, independentProduct.getId());

        //BAR - optimal bar at 100.00
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);

        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(-7.89), barProduct, true);
        Product independentChild = createAgileProduct(BigDecimal.valueOf(-12.89), independentProduct, true);

        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.00), BigDecimal.valueOf(-15.00));
        addProductRateOffset(independentChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10.00), BigDecimal.valueOf(-20.00));

        //child has optimal bar of -7.89 (which represents the offset decision)
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(-7.89), null, null);
        childOutput.setOptimalBarType(OptimalBarType.FIXED);
        tenantCrudService().save(childOutput);

        CPDecisionBAROutput independentChildOutput = addCPDecisionBAROutput(independentChild, RT_KING, startDate, BigDecimal.valueOf(-12.89), null, null);
        independentChildOutput.setOptimalBarType(OptimalBarType.FIXED);
        tenantCrudService().save(independentChildOutput);

        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(independentChild, adultBreakfastPackage);

        //GRANDCHILD
        Product grandchild = createChildProduct(GRANDCHILD_ONE_OF_CHILD_ONE, child.getId());
        grandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        grandchild.setOptimized(false);
        grandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        grandchild = tenantCrudService().save(grandchild);
        addProductRateOffset(grandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5));
        Product independentGrandchild = createChildProduct(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentChild.getId());
        independentGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentGrandchild.setOptimized(false);
        independentGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentGrandchild = tenantCrudService().save(independentGrandchild);
        addProductRateOffset(independentGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10));

        //grandchild has optimal bar of 86.6165 (92.11 - 5.49350 (5% of parent final bar (109.87)))
        CPDecisionBAROutput grandchildOutput = addCPDecisionBAROutput(grandchild, RT_KING, startDate, getDefaultPrecisionValueOf(86.6165), null, null);
        grandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(grandchildOutput);
        CPDecisionBAROutput independentGrandchildOutput = addCPDecisionBAROutput(independentGrandchild, RT_KING, startDate, getDefaultPrecisionValueOf(91.6165), null, null);
        independentGrandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(independentGrandchildOutput);

        //GREAT GRANDCHILD
        Product greatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, grandchild.getId());
        greatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        greatGrandchild.setOptimized(true);
        greatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(greatGrandchild);
        addProductRateOffset(greatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(2.00), BigDecimal.valueOf(14.00));
        Product independentGreatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentGrandchild.getId());
        independentGreatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        independentGreatGrandchild.setOptimized(true);
        independentGreatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentGreatGrandchild);
        addProductRateOffset(independentGreatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(7.00), BigDecimal.valueOf(19.00));

        //great grandchild has optimal bar of 9.5% (which represents the offset decision)
        CPDecisionBAROutput greatGrandchildOutput = addCPDecisionBAROutput(greatGrandchild, RT_KING, startDate, BigDecimal.valueOf(9.5), null, null);
        greatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(greatGrandchildOutput);
        CPDecisionBAROutput independentGreatGrandchildOutput = addCPDecisionBAROutput(independentGreatGrandchild, RT_KING, startDate, BigDecimal.valueOf(14.5), null, null);
        independentGreatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentGreatGrandchildOutput);

        //great grandchild has adult breakfast package
        addProductPackage(greatGrandchild, adultPercentPackage);
        addProductPackage(independentGreatGrandchild, adultPercentPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output:
        CPDecisionBAROutput barOutput = filterOutputsByProduct(barProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(100), barOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87).setScale(2, RoundingMode.HALF_UP), barOutput.getFinalBAR());

        //assert CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = optimal bar (92.11) + 20 for adult breakfast package (10 * 2 occupants) then rounded ending in 9.87
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-7.89), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), childOutput.getAdjustmentValue());

        //assert GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: parent final bar (109.87) - 5.4935 (5% of 109.87) = 99.87 (rounded from 104.3765)
        grandchildOutput = filterOutputsByProduct(grandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(86.6165), grandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getAdjustmentValue());

        //assert GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 99.87 (parent final bar)
        //           + 9.48765 for product rate offset (9.5% of 99.87)
        //           + 19.974 for package (10% of 109.87 for 2 occupants)
        //           = 129.87 (rounded from 129.33165)
        greatGrandchildOutput = filterOutputsByProduct(greatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(9.5), greatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(129.87).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(30.00).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getAdjustmentValue());

        //assert Independent output:
        CPDecisionBAROutput independentOutput = filterOutputsByProduct(independentProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(110), independentOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87).setScale(2, RoundingMode.HALF_UP), independentOutput.getFinalBAR());

        //assert Independent CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = optimal bar (92.11) + 20 for adult breakfast package (10 * 2 occupants) then rounded ending in 9.87
        independentChildOutput = filterOutputsByProduct(independentChild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-12.89), independentChildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(119.87).setScale(2, RoundingMode.HALF_UP), independentChildOutput.getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("0.00"), independentChildOutput.getAdjustmentValue());

        //assert Independent GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: parent final bar (109.87) - 5.4935 (5% of 109.87) = 99.87 (rounded from 104.3765)
        independentGrandchildOutput = filterOutputsByProduct(independentGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(91.6165), independentGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getAdjustmentValue());

        //assert Independent GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 99.87 (parent final bar)
        //           + 9.48765 for product rate offset (9.5% of 99.87)
        //           + 19.974 for package (10% of 109.87 for 2 occupants)
        //           = 129.87 (rounded from 129.33165)
        independentGreatGrandchildOutput = filterOutputsByProduct(independentGreatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(14.5), independentGreatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(149.87).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(40.00).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getAdjustmentValue());
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, agileOptimalBar={2}, decisionReasonTypeId={3}, expectedFinalBar={4}, expectedAdjustmentValue={5}")
    @CsvSource({
            "FIXED, 60, -7.89, 1, 109.87, 0",
            "PERCENT, 60, -25.00, 1, 99.87, 0",
            "PERCENT, 150, 10.00, 1, 150.00, 0",
            "PERCENT, 150, 40.00, 1, 159.87, 0",
            "PERCENT, 60, -25.00, 15, 60.00, 0",
            "PERCENT, 150, 10.00, 15, 150.00, 0",
            "PERCENT, 150, 40.00, 15, 150.00, 0"
    })
    void roundOptimalBARsWithOptimizedProductWithPackages(String optimalBarType, double agileProductFloorValue, double agileOptimalBar, int decisionReasonTypeId, double expectedAgileFinalBar, double expectedAdjustmentValue) {
        //GIVEN
        //Rounding Rules 9.87
        setRoundingRule(9, 8, 7, independentProduct.getId());

        //BAR - optimal bar at 100.00
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(agileProductFloorValue), barProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.00), BigDecimal.valueOf(-15.00));
        //child has optimal bar of -7.89 (which represents the offset decision)
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        childOutput.setOptimalBarType(OptimalBarType.valueOf(optimalBarType));
        childOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        tenantCrudService().save(childOutput);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output:
        CPDecisionBAROutput barOutput = filterOutputsByProduct(barProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(100), barOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87).setScale(2, RoundingMode.HALF_UP), barOutput.getFinalBAR());
        //assert CHILD output:
        //final bar = optimal bar  + 20 for adult breakfast package (10 * 2 occupants) then rounded ending in 9.87
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, agileOptimalBar={2}, decisionReasonTypeId={3}, expectedFinalBar={4}, expectedAdjustmentValue={5}")
    @CsvSource({
            "FIXED, 60, -7.89, 1, 119.87, 0",
            "PERCENT, 60, -25.00, 1, 99.87, 0",
            "PERCENT, 150, 10.00, 1, 150.00, 0",
            "PERCENT, 150, 40.00, 1, 169.87, 0",
            "PERCENT, 60, -25.00, 15, 60.00, 0",
            "PERCENT, 150, 10.00, 15, 150.00, 0",
            "PERCENT, 150, 40.00, 15, 150.00, 0"
    })
    void independentRoundOptimalBARsWithOptimizedProductWithPackages(String optimalBarType, double agileProductFloorValue, double agileOptimalBar, int decisionReasonTypeId, double expectedAgileFinalBar, double expectedAdjustmentValue) {
        //GIVEN
        //Rounding Rules 9.87
        setRoundingRule(9, 8, 7, independentProduct.getId());

        //BAR - optimal bar at 100.00
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(agileProductFloorValue), independentProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.00), BigDecimal.valueOf(-15.00));
        //child has optimal bar of -7.89 (which represents the offset decision)
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        childOutput.setOptimalBarType(OptimalBarType.valueOf(optimalBarType));
        childOutput.setDecisionReasonTypeId(decisionReasonTypeId);
        tenantCrudService().save(childOutput);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output:
        CPDecisionBAROutput barOutput = filterOutputsByProduct(independentProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(110), barOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87).setScale(2, RoundingMode.HALF_UP), barOutput.getFinalBAR());
        //assert CHILD output:
        //final bar = optimal bar  + 20 for adult breakfast package (10 * 2 occupants) then rounded ending in 9.87
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsMixedOptimizedMixedOffsetsWithMixedRoundingRules() {
        //GIVEN
        //Rounding Rules 9.87
        setRoundingRule(9, 8, 7, independentProduct.getId());

        //BAR - optimal bar at 100.00
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);

        //CHILD - unfenced and packaged with no rounding rule and a offset decision of -7.89 fixed
        Product child = createChildProduct(CHILD_ONE_OF_SYSTEM_DEFAULT, barProduct.getId());
        child.setRoundingRule(RoundingRule.NONE);
        child.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        child.setOptimized(true);
        child.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        child = tenantCrudService().save(child);

        Product independentChild = createChildProduct(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentProduct.getId());
        independentChild.setRoundingRule(RoundingRule.NONE);
        independentChild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentChild.setOptimized(true);
        independentChild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentChild = tenantCrudService().save(independentChild);

        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.00), BigDecimal.valueOf(-15.00));
        addProductRateOffset(independentChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10.00), BigDecimal.valueOf(-20.00));

        //child has optimal bar of -7.89 (which represents the offset decision)
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(-7.89), null, null);
        childOutput.setOptimalBarType(OptimalBarType.FIXED);
        tenantCrudService().save(childOutput);

        CPDecisionBAROutput independentChildOutput = addCPDecisionBAROutput(independentChild, RT_KING, startDate, BigDecimal.valueOf(-12.89), null, null);
        independentChildOutput.setOptimalBarType(OptimalBarType.FIXED);
        tenantCrudService().save(independentChildOutput);

        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(independentChild, adultBreakfastPackage);

        //GRANDCHILD
        Product grandchild = createChildProduct(GRANDCHILD_ONE_OF_CHILD_ONE, child.getId());
        grandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        grandchild.setOptimized(false);
        grandchild.setRoundingRule(RoundingRule.UP);
        grandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        grandchild = tenantCrudService().save(grandchild);
        addProductRateOffset(grandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5));

        Product independentGrandchild = createChildProduct(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentChild.getId());
        independentGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentGrandchild.setOptimized(false);
        independentGrandchild.setRoundingRule(RoundingRule.UP);
        independentGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentGrandchild = tenantCrudService().save(independentGrandchild);
        addProductRateOffset(independentGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10));

        //grandchild has optimal bar of 86.6165 (92.11 - 5.49350 (5% of parent final bar (109.87)))
        CPDecisionBAROutput grandchildOutput = addCPDecisionBAROutput(grandchild, RT_KING, startDate, getDefaultPrecisionValueOf(86.6165), null, null);
        grandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(grandchildOutput);

        CPDecisionBAROutput independentGrandchildOutput = addCPDecisionBAROutput(independentGrandchild, RT_KING, startDate, getDefaultPrecisionValueOf(91.6165), null, null);
        independentGrandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(independentGrandchildOutput);

        //GREAT GRANDCHILD
        Product greatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, grandchild.getId());
        greatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        greatGrandchild.setOptimized(true);
        greatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        greatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(greatGrandchild);
        addProductRateOffset(greatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(2.00), BigDecimal.valueOf(14.00));

        Product independentGreatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentGrandchild.getId());
        independentGreatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        independentGreatGrandchild.setOptimized(true);
        independentGreatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        independentGreatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentGreatGrandchild);
        addProductRateOffset(independentGreatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(7.00), BigDecimal.valueOf(19.00));

        //great grandchild has optimal bar of 9.5% (which represents the offset decision)
        CPDecisionBAROutput greatGrandchildOutput = addCPDecisionBAROutput(greatGrandchild, RT_KING, startDate, BigDecimal.valueOf(9.5), null, null);
        greatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(greatGrandchildOutput);

        CPDecisionBAROutput independentGreatGrandchildOutput = addCPDecisionBAROutput(independentGreatGrandchild, RT_KING, startDate, BigDecimal.valueOf(14.5), null, null);
        independentGreatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentGreatGrandchildOutput);

        //great grandchild has adult breakfast package
        addProductPackage(greatGrandchild, adultPercentPackage);
        addProductPackage(independentGreatGrandchild, adultPercentPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output:
        CPDecisionBAROutput barOutput = filterOutputsByProduct(barProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(100), barOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87).setScale(2, RoundingMode.HALF_UP), barOutput.getFinalBAR());

        //assert CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = optimal bar (92.11) + 20 for adult breakfast package (10 * 2 occupants); price rounding NONE
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-7.89), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(111.98).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), childOutput.getAdjustmentValue());

        //assert GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: 111.98 - 5% (5.599) = 107.00 (106.381 rounded up)
        grandchildOutput = filterOutputsByProduct(grandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(86.6165), grandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(107.00).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), grandchildOutput.getAdjustmentValue());

        //assert GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 107.00 (parent final bar)
        //           + 9.5% for offset (10.165)
        //           + 10% for package (10.70 * 2 occupants)
        //           = 139.00 (rounded from 138.565)
        greatGrandchildOutput = filterOutputsByProduct(greatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(9.5), greatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(139.00).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(32.00), greatGrandchildOutput.getAdjustmentValue());

        //assert Independent output:
        CPDecisionBAROutput independentOutput = filterOutputsByProduct(independentProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(110), independentOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(109.87).setScale(2, RoundingMode.HALF_UP), independentOutput.getFinalBAR());

        //assert Independent CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = optimal bar (92.11) + 20 for adult breakfast package (10 * 2 occupants); price rounding NONE
        independentChildOutput = filterOutputsByProduct(independentChild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-12.89), independentChildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(116.98).setScale(2, RoundingMode.HALF_UP), independentChildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), independentChildOutput.getAdjustmentValue());

        //assert Independent GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: 111.98 - 5% (5.599) = 107.00 (106.381 rounded up)
        independentGrandchildOutput = filterOutputsByProduct(independentGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(91.6165), independentGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(106.00).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), independentGrandchildOutput.getAdjustmentValue());

        //assert Independent GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 107.00 (parent final bar)
        //           + 9.5% for offset (10.165)
        //           + 10% for package (10.70 * 2 occupants)
        //           = 139.00 (rounded from 138.565)
        independentGreatGrandchildOutput = filterOutputsByProduct(independentGreatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(14.50), independentGreatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(143.00).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(37.00), independentGreatGrandchildOutput.getAdjustmentValue());
    }

    @Test
    public void roundOptimalAgileRatesDerivedFromPreviousDailyBarOutput() {
        //GIVEN
        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_KING, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        // less then increment |89.88 - 99.87| = 9.99
        double previousDailyBarOutput = 89.88;
        double independentPreviousDailyBarOutput = 109.87;
        addDecisionDailybarOutput(RT_KING, startDate, BigDecimal.valueOf(previousDailyBarOutput), BigDecimal.valueOf(previousDailyBarOutput), BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_KING, startDate, BigDecimal.valueOf(independentPreviousDailyBarOutput), BigDecimal.valueOf(independentPreviousDailyBarOutput), BigDecimal.ZERO, BigDecimal.ZERO, independentProduct);

        //Rounding Rules 9.87
        setRoundingRule(9, 8, 7, independentProduct.getId());

        //BAR - optimal bar at 100.00
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(100), null, null);
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(110), null, null);

        //CHILD - unfenced and packaged with no rounding rule and a offset decision of -7.89 fixed
        Product child = createChildProduct(CHILD_ONE_OF_SYSTEM_DEFAULT, barProduct.getId());
        child.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        child.setOptimized(true);
        child.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        child = tenantCrudService().save(child);

        Product independentChild = createChildProduct(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentProduct.getId());
        independentChild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentChild.setOptimized(true);
        independentChild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentChild = tenantCrudService().save(independentChild);

        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-15.00), BigDecimal.valueOf(-5.00));
        addProductRateOffset(independentChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-20.00), BigDecimal.valueOf(-10.00));

        //child has optimal bar of -7.89 (which represents the offset decision)
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(-7.89), null, null);
        childOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(childOutput);

        CPDecisionBAROutput independentChildOutput = addCPDecisionBAROutput(independentChild, RT_KING, startDate, BigDecimal.valueOf(-12.89), null, null);
        independentChildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentChildOutput);

        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(independentChild, adultBreakfastPackage);

        //GRANDCHILD
        Product grandchild = createChildProduct(GRANDCHILD_ONE_OF_CHILD_ONE, child.getId());
        grandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        grandchild.setOptimized(false);
        grandchild.setRoundingRule(RoundingRule.UP);
        grandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        grandchild = tenantCrudService().save(grandchild);
        addProductRateOffset(grandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5));

        Product independentGrandchild = createChildProduct(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentChild.getId());
        independentGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentGrandchild.setOptimized(false);
        independentGrandchild.setRoundingRule(RoundingRule.UP);
        independentGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentGrandchild = tenantCrudService().save(independentGrandchild);
        addProductRateOffset(independentGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10));

        //grandchild has optimal bar of 86.6165 (92.11 - 5.49350 (5% of parent final bar (109.87)))
        CPDecisionBAROutput grandchildOutput = addCPDecisionBAROutput(grandchild, RT_KING, startDate, getDefaultPrecisionValueOf(86.6165), null, null);
        grandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(grandchildOutput);
        CPDecisionBAROutput independentGrandchildOutput = addCPDecisionBAROutput(independentGrandchild, RT_KING, startDate, getDefaultPrecisionValueOf(91.6165), null, null);
        independentGrandchildOutput.setOptimalBarType(OptimalBarType.PRICE);
        tenantCrudService().save(independentGrandchildOutput);

        //GREAT GRANDCHILD
        Product greatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, grandchild.getId());
        greatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        greatGrandchild.setOptimized(true);
        greatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        greatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(greatGrandchild);
        addProductRateOffset(greatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(2.00), BigDecimal.valueOf(14.00));
        Product independentGreatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentGrandchild.getId());
        independentGreatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        independentGreatGrandchild.setOptimized(true);
        independentGreatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        independentGreatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentGreatGrandchild);
        addProductRateOffset(independentGreatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(7.00), BigDecimal.valueOf(19.00));

        //great grandchild has optimal bar of 9.5% (which represents the offset decision)
        CPDecisionBAROutput greatGrandchildOutput = addCPDecisionBAROutput(greatGrandchild, RT_KING, startDate, BigDecimal.valueOf(9.5), null, null);
        greatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(greatGrandchildOutput);

        CPDecisionBAROutput independentGreatGrandchildOutput = addCPDecisionBAROutput(independentGreatGrandchild, RT_KING, startDate, BigDecimal.valueOf(14.5), null, null);
        independentGreatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentGreatGrandchildOutput);

        //great grandchild has adult breakfast package
        addProductPackage(greatGrandchild, adultPercentPackage);
        addProductPackage(independentGreatGrandchild, adultPercentPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output uses previous price:
        CPDecisionBAROutput barOutput = filterOutputsByProduct(barProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(BigDecimal.valueOf(99.87), barOutput.getPrettyBAR());
        assertBigDecimalEquals(getDefaultPrecisionValueOf(previousDailyBarOutput), barOutput.getFinalBAR());

        //assert CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = 102.788468 (final bar of parent/previous output (89.88) - 7.89% (7.091532) + 20 for adult breakfast package (10 * 2 occupants))
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-7.89), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(99.87).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), childOutput.getAdjustmentValue());

        //assert GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: 99.87 - 5% (4.9935) = 107.00 (94.8765 rounded up)
        grandchildOutput = filterOutputsByProduct(grandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(86.6165), grandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(95.00).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), grandchildOutput.getAdjustmentValue());

        //assert GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 95.00 (parent final bar)
        //           + 9.5% for offset (9.025)
        //           + 10% for package (9.50 * 2 occupants)
        //           = 123.00 (rounded to nearest WHOLE number of 123.025)
        greatGrandchildOutput = filterOutputsByProduct(greatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(9.5), greatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(123.00).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(28.00), greatGrandchildOutput.getAdjustmentValue());

        //assert Independent output uses previous price:
        CPDecisionBAROutput independentOutput = filterOutputsByProduct(independentProduct, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(BigDecimal.valueOf(109.87), independentOutput.getPrettyBAR());
        assertBigDecimalEquals(getDefaultPrecisionValueOf(independentPreviousDailyBarOutput), independentOutput.getFinalBAR());

        //assert Independent CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = 102.788468 (final bar of parent/previous output (89.88) - 7.89% (7.091532) + 20 for adult breakfast package (10 * 2 occupants))
        independentChildOutput = filterOutputsByProduct(independentChild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(-12.89), independentChildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(119.87).setScale(2, RoundingMode.HALF_UP), independentChildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), independentChildOutput.getAdjustmentValue());

        //assert Independent GRANDCHILD output:
        //optimal bar: parent optimal bar (92.11) - 5.4935 (5% of 109.87) for product rate offset
        //final bar: 99.87 - 5% (4.9935) = 107.00 (94.8765 rounded up)
        independentGrandchildOutput = filterOutputsByProduct(independentGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(91.6165), independentGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(108.00).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(0.00), independentGrandchildOutput.getAdjustmentValue());

        //assert Independent GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 95.00 (parent final bar)
        //           + 9.5% for offset (9.025)
        //           + 10% for package (9.50 * 2 occupants)
        //           = 123.00 (rounded to nearest WHOLE number of 123.025)
        independentGreatGrandchildOutput = filterOutputsByProduct(independentGreatGrandchild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(14.50), independentGreatGrandchildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(145.00).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(37.00), independentGreatGrandchildOutput.getAdjustmentValue());
    }

    @Test
    public void dtaRangeBoundariesUpdateNotRequired() {
        //given
        //remove all products except for BAR
        tenantCrudService().deleteAll(ProductPackage.class);
        tenantCrudService().deleteAll(ProductRateOffset.class);
        List<Product> products = tenantCrudService().findAll(Product.class);
        products.remove(barProduct);
        products.remove(independentProduct);
        tenantCrudService().delete(products);

        //add dta ranges
        List<AgileRatesDTARange> agileRatesDTARanges = tenantCrudService().findAll(AgileRatesDTARange.class);
        AgileRatesDTARange firstBucketRange = agileRatesDTARanges.get(0);
        firstBucketRange.setMaxDaysToArrival(3);
        AgileRatesDTARange secondBucketRange = new AgileRatesDTARange();
        secondBucketRange.setMinDaysToArrival(4);
        secondBucketRange.setMaxDaysToArrival(6);
        AgileRatesDTARange thirdBucketRange = new AgileRatesDTARange();
        thirdBucketRange.setMinDaysToArrival(7);
        thirdBucketRange.setMaxDaysToArrival(null);
        tenantCrudService().save(Arrays.asList(firstBucketRange, secondBucketRange, thirdBucketRange));

        //add dta product
        Product dtaProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED, "DTA Product", barProduct);
        Product independentDtaProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED, "Independent DTA Product", independentProduct);

        //add dta product rate offset (NOTE: every subsequent dta range will have an offset 5 less of the previous dta range)
        addProductRateOffset(dtaProduct, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(-5));
        addProductRateOffset(independentDtaProduct, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(-10));

        //add bar output starting at 90 and adding 1 each day for ten days
        BigDecimal optimalBAR = BigDecimal.valueOf(90);
        for (int i = 0; i < 10; i++) {
            addCPDecisionBAROutput(barProduct, RT_KING, startDate.plusDays(i), optimalBAR, null, null);
            optimalBAR = optimalBAR.add(BigDecimal.ONE);
        }

        BigDecimal independentOptimalBAR = BigDecimal.valueOf(100);
        for (int i = 0; i < 10; i++) {
            addCPDecisionBAROutput(independentProduct, RT_KING, startDate.plusDays(i), independentOptimalBAR, null, null);
            independentOptimalBAR = independentOptimalBAR.add(BigDecimal.ONE);
        }

        //add dta product outputs (NOTE: optimal bar not needed here)
        addTenDaysOfDecisionBarOutputs(dtaProduct, 0);
        addTenDaysOfDecisionBarOutputs(independentDtaProduct, 0);

        //update pricing accom class with min diff of 11 so as not to trigger update
        List<PricingAccomClass> pricingAccomClasses = tenantCrudService().findAll(PricingAccomClass.class);
        PricingAccomClass stnPricingAccomClass = pricingAccomClasses.get(0);
        stnPricingAccomClass.setMinimumIncrementValue(BigDecimal.valueOf(11));
        stnPricingAccomClass.setMinimumIncrementMethod(MinimumIncrementMethod.FIXED_OFFSET);
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", RT_KING).parameters());
        stnPricingAccomClass.setAccomType(accomType);
        tenantCrudService().save(stnPricingAccomClass);

        //add previous daily bar output for BAR
        for (int i = 0; i < 10; i++) {
            addPreviousDailyBarOutput(accomType, startDate.plusDays(i), barProduct, BigDecimal.valueOf(100), i + 1);
        }
        for (int i = 0; i < 10; i++) {
            addPreviousDailyBarOutput(accomType, startDate.plusDays(i), independentProduct, BigDecimal.valueOf(110), i + 1);
        }

        //add previous daily bar outputs for dta product (mocking values from yesterday's BDE)
        addPreviousDailyBarOutput(accomType, startDate.plusDays(0), dtaProduct, BigDecimal.valueOf(95), 1);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(1), dtaProduct, BigDecimal.valueOf(95), 2);
        //NOTE: the max dta for first bucket is now a value previously calculated from the second bucket offset
        addPreviousDailyBarOutput(accomType, startDate.plusDays(2), dtaProduct, BigDecimal.valueOf(90), 3);

        addPreviousDailyBarOutput(accomType, startDate.plusDays(3), dtaProduct, BigDecimal.valueOf(90), 4);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(4), dtaProduct, BigDecimal.valueOf(90), 5);

        //NOTE: the max of second bucket is now a value previously calculated from the third bucket offset
        addPreviousDailyBarOutput(accomType, startDate.plusDays(5), dtaProduct, BigDecimal.valueOf(85), 6);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(6), dtaProduct, BigDecimal.valueOf(85), 7);

        addPreviousDailyBarOutput(accomType, startDate.plusDays(7), dtaProduct, BigDecimal.valueOf(85), 8);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(8), dtaProduct, BigDecimal.valueOf(85), 9);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(9), dtaProduct, BigDecimal.valueOf(85), 10);

        //add previous daily bar outputs for independent dta product (mocking values from yesterday's BDE)
        addPreviousDailyBarOutput(accomType, startDate.plusDays(0), independentDtaProduct, BigDecimal.valueOf(100), 1);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(1), independentDtaProduct, BigDecimal.valueOf(100), 2);
        //NOTE: the max dta for first bucket is now a value previously calculated from the second bucket offset
        addPreviousDailyBarOutput(accomType, startDate.plusDays(2), independentDtaProduct, BigDecimal.valueOf(95), 3);

        addPreviousDailyBarOutput(accomType, startDate.plusDays(3), independentDtaProduct, BigDecimal.valueOf(95), 4);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(4), independentDtaProduct, BigDecimal.valueOf(95), 5);

        //NOTE: the max of second bucket is now a value previously calculated from the third bucket offset
        addPreviousDailyBarOutput(accomType, startDate.plusDays(5), independentDtaProduct, BigDecimal.valueOf(90), 6);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(6), independentDtaProduct, BigDecimal.valueOf(90), 7);

        addPreviousDailyBarOutput(accomType, startDate.plusDays(7), independentDtaProduct, BigDecimal.valueOf(90), 8);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(8), independentDtaProduct, BigDecimal.valueOf(90), 9);
        addPreviousDailyBarOutput(accomType, startDate.plusDays(9), independentDtaProduct, BigDecimal.valueOf(90), 10);

        //when
        service.roundOptimalBARs(startDate, startDate.plusDays(10));

        //then
        List<CPDecisionBAROutput> outputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //assert day of arrival
        List<CPDecisionBAROutput> dayOfArrivalOutputs =
                outputs.stream()
                        .filter(output -> output.getArrivalDate().equals(startDate))
                        .collect(Collectors.toList());

        //final bar of system default is 100 (previous output); Independent is 110
        //final bar of dta product is 95 (previous output)
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(100.00), 5), dayOfArrivalOutputs.get(0).getFinalBAR());
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(110.00), 5), dayOfArrivalOutputs.get(1).getFinalBAR());
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(95.00), 5), dayOfArrivalOutputs.get(2).getFinalBAR());
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(-5.00), 5), dayOfArrivalOutputs.get(2).getAdjustmentValue());
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(100.00), 5), dayOfArrivalOutputs.get(3).getFinalBAR());
        assertEquals(BigDecimalUtil.round(BigDecimal.valueOf(-10.00), 5), dayOfArrivalOutputs.get(3).getAdjustmentValue());

        //assert first bucket max dta
        List<CPDecisionBAROutput> firstBucketMaxDtaOutputs =
                outputs.stream()
                        .filter(output -> output.getArrivalDate().equals(startDate.plusDays(2)))
                        .collect(Collectors.toList());
        //final bar of system default is 100; Independent is 110
        //final bar of dta is 95 from 90 (must update max dta range)
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(100), 5), firstBucketMaxDtaOutputs.get(0).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(110), 5), firstBucketMaxDtaOutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(95), 2), firstBucketMaxDtaOutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-5), 2), firstBucketMaxDtaOutputs.get(2).getAdjustmentValue());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(100), 2), firstBucketMaxDtaOutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-10), 2), firstBucketMaxDtaOutputs.get(3).getAdjustmentValue());

        //assert second bucket min dta
        List<CPDecisionBAROutput> secondBucketMinDtaOutputs =
                outputs.stream()
                        .filter(output -> output.getArrivalDate().equals(startDate.plusDays(3)))
                        .collect(Collectors.toList());
        //final bar of system default is 100; Independent is 110
        //final bar of dta product is 90
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(100), 5), secondBucketMinDtaOutputs.get(0).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(110), 5), secondBucketMinDtaOutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(90), 5), secondBucketMinDtaOutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-10), 5), secondBucketMinDtaOutputs.get(2).getAdjustmentValue());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(95), 5), secondBucketMinDtaOutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-15), 5), secondBucketMinDtaOutputs.get(3).getAdjustmentValue());

        //assert second bucket max dta
        List<CPDecisionBAROutput> secondBucketMaxDtaOutputs =
                outputs.stream()
                        .filter(output -> output.getArrivalDate().equals(startDate.plusDays(5)))
                        .collect(Collectors.toList());

        //final bar of system default is 90; Independent is 110
        //final bar of dta product is 85 from 80 (must update max dta range)
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(100.00), 5), secondBucketMaxDtaOutputs.get(0).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(110.00), 5), secondBucketMaxDtaOutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(90.00), 2), secondBucketMaxDtaOutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-10), 2), secondBucketMaxDtaOutputs.get(2).getAdjustmentValue());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(95.00), 2), secondBucketMaxDtaOutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-15), 2), secondBucketMaxDtaOutputs.get(3).getAdjustmentValue());

        //assert third bucket min dta
        List<CPDecisionBAROutput> thirdBucketMaxDtaOutputs =
                outputs.stream()
                        .filter(output -> output.getArrivalDate().equals(startDate.plusDays(7)))
                        .collect(Collectors.toList());

        //final bar of system default is 90
        //final bar of dta product is 85
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(100.00), 5), thirdBucketMaxDtaOutputs.get(0).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(110.00), 5), thirdBucketMaxDtaOutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(85.00), 5), thirdBucketMaxDtaOutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-15), 5), thirdBucketMaxDtaOutputs.get(2).getAdjustmentValue());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(90.00), 5), thirdBucketMaxDtaOutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(BigDecimalUtil.round(BigDecimal.valueOf(-20), 5), thirdBucketMaxDtaOutputs.get(3).getAdjustmentValue());
    }

    private void addPreviousDailyBarOutput(AccomType accomType, LocalDate date, Product product, BigDecimal rate, int index) {
        DecisionDailybarOutput dailybarOutputBarDayOfArrival = new DecisionDailybarOutput();
        dailybarOutputBarDayOfArrival.setOccupancyDate(date);
        dailybarOutputBarDayOfArrival.setDecisionId(0);
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setPropertyId(TestProperty.H2.getId());
        rateUnqualified.setStatusId(1);
        rateUnqualified.setSystemDefault(product.isSystemDefault() ? 1 : 0);
        rateUnqualified.setDescription("test");
        rateUnqualified.setName("test " + product.getName() + index);
        rateUnqualified.setStartDate(new Date());
        rateUnqualified.setEndDate(new Date());
        rateUnqualified.setDerivedRateCode("test");
        rateUnqualified.setIncludesPackage(0);
        rateUnqualified.setYieldable(0);
        rateUnqualified.setPriceRelative(0);
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setFileLocation("test location " + index);
        fileMetadata.setFileName("test file name" + index);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setSnapshotTm(new Date());
        fileMetadata.setPreparedDt(new Date());
        fileMetadata.setPreparedTm(new Date());
        ProcessStatus processStatus = new ProcessStatus();
        processStatus.setName("test " + product.getName() + index);
        fileMetadata.setProcessStatusId(tenantCrudService().save(processStatus).getId());
        fileMetadata.setTenantPropertyId(TestProperty.H2.getId());
        List<RecordType> recordTypes = tenantCrudService().findAll(RecordType.class);
        fileMetadata.setRecordTypeId(recordTypes.get(0).getId());
        rateUnqualified.setFileMetadataId(tenantCrudService().save(fileMetadata).getId());
        tenantCrudService().save(rateUnqualified);
        dailybarOutputBarDayOfArrival.setRateUnqualified(rateUnqualified);
        dailybarOutputBarDayOfArrival.setAccomType(accomType);
        dailybarOutputBarDayOfArrival.setProduct(product);
        dailybarOutputBarDayOfArrival.setSingleRate(rate);
        dailybarOutputBarDayOfArrival.setDoubleRate(rate);
        dailybarOutputBarDayOfArrival.setCreateDate(new Date());
        tenantCrudService().save(dailybarOutputBarDayOfArrival);
    }

    private void addTenDaysOfDecisionBarOutputs(Product product, int value) {
        addCPDecisionBAROutput(product, RT_KING, startDate, BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(1), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(2), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(3), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(4), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(5), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(6), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(7), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(8), BigDecimal.valueOf(value), null, null);
        addCPDecisionBAROutput(product, RT_KING, startDate.plusDays(9), BigDecimal.valueOf(value), null, null);
    }

    private Product createChildProduct(String name, Integer dependentProductId) {
        Product childProduct = new Product();
        ProductCode pc = new ProductCode(2);
        childProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        childProduct.setName(name);
        childProduct.setDependentProductId(dependentProductId);
        childProduct.setStatus(TenantStatusEnum.ACTIVE);
        childProduct.setMinDTA(0);
        childProduct.setMinLOS(1);
        childProduct.setSystemDefault(false);
        childProduct.setOffsetForExtraAdult(true);
        childProduct.setOffsetForExtraChild(true);
        childProduct.setUpload(true);
        childProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        childProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        childProduct.setCentrallyManaged(false);
        childProduct.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        childProduct.setFloorType(FloorType.FIXED_RATE);
        childProduct.setFloorPercentage(BigDecimal.ONE);
        childProduct.setRateShoppingLOSMin(1);
        childProduct.setRateShoppingLOSMax(2);
        childProduct.setMaxRooms(-1);
        childProduct.setMinRooms(-1);
        childProduct.setProductCode(pc);
        childProduct.setUseInSmallGroupEval(false);
        childProduct.setFreeUpgradeEnabled(false);
        childProduct.setFreeNightEnabled(false);

        return childProduct;
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}")
    @CsvSource({
            "false, 100.00,  FIXED,   -7.89,  102.11",
            "false, 100.00,  PERCENT, -25.00, 85",
            "false, 100.00,  PERCENT, 25.00, 135",
            "true,  430.00,  FIXED,   -7.89,  432.11",
            "true,  430.00,  PERCENT, -25.00, 332.50",
            "true,  430.00,  PERCENT, 25.00, 547.50"
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void roundOptimalBARsForAgilePriceWithComponentRoomsPriceAsSumOfPartsEnabled(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar) {
        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(80.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(barProduct, STE, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO);
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO);
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(60), barProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-30.00), BigDecimal.valueOf(30));
        //child has optimal bar of -7.89 (which represents the offset decision)
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        CPDecisionBAROutput childOutput;
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(80).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(90).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}")
    @CsvSource({
            "false, 110.00,  FIXED,   -7.89,  112.11",
            "false, 110.00,  PERCENT, -25.00, 92.50",
            "false, 110.00,  PERCENT, 25.00, 147.50",
            "true,  455.00,  FIXED,   -7.89,  457.11",
            "true,  455.00,  PERCENT, -25.00, 351.25",
            "true,  455.00,  PERCENT, 25.00, 578.75"
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void independentRoundOptimalBARsForAgilePriceWithComponentRoomsPriceAsSumOfPartsEnabled(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar) {
        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(85.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(independentProduct, STE, startDate, BigDecimal.valueOf(110), null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO, independentProduct.getId());
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN, independentProduct.getId());
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO, independentProduct.getId());
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(65), independentProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-35.00), BigDecimal.valueOf(35));
        //child has optimal bar of -7.89 (which represents the offset decision)
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        CPDecisionBAROutput childOutput;
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(85).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(95).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}, expectedAgileAdjustmentValue={5}")
    @CsvSource({
            "false, 96.78,  FIXED,   -7.89,  96.78,  0",
            "false, 96.78,  PERCENT, -25.00, 86.78,  0",
            "false, 96.78,  PERCENT, 25.00, 126.78,  0",
            "true,  416.78,  FIXED,   -7.89,  416.78,  0",
            "true,  416.78,  PERCENT, -25.00, 326.78,  0",
            "true,  416.78,  PERCENT, 25.00, 526.78,  0"
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void roundOptimalBARsForAgilePriceWithComponentRoomsSumOfPartsFunctionalityAndRoundingRuleIsSet(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar, double expectedAgileAdjustmentValue) {
        //GIVEN
        setRoundingRule(6, 7, 8, independentProduct.getId());
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(80.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(barProduct, STE, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO);
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO);
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(60), barProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-30.00), BigDecimal.valueOf(30.00));
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(76.78).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(86.78).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());

        CPDecisionBAROutput childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }


    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}, expectedAgileAdjustmentValue={5}")
    @CsvSource({
            "false, 106.78,  FIXED,   -7.89,  106.78,  0",
            "false, 106.78,  PERCENT, -25.00, 86.78,  0",
            "false, 106.78,  PERCENT, 25.00, 146.78,  0",
            "true,  466.78,  FIXED,   -7.89,  466.78,  0",
            "true,  466.78,  PERCENT, -25.00, 356.78,  0",
            "true,  466.78,  PERCENT, 25.00, 596.78,  0"
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void independentRoundOptimalBARsForAgilePriceWithComponentRoomsSumOfPartsFunctionalityAndRoundingRuleIsSet(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar, double expectedAgileAdjustmentValue) {
        //GIVEN
        setRoundingRule(6, 7, 8, independentProduct.getId());
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(85.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(independentProduct, STE, startDate, BigDecimal.valueOf(110), null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("55"), new BigDecimal("305"), BigDecimal.ZERO, independentProduct.getId());
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN, independentProduct.getId());
        addTransientPricingBaseAccomType(STE, new BigDecimal("55"), new BigDecimal("705"), BigDecimal.ZERO, independentProduct.getId());
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(65), independentProduct, true);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-35.00), BigDecimal.valueOf(35.00));
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(86.78).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(96.78).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());

        CPDecisionBAROutput childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, agileGrandChildOptimalBar={5},expectedAgileGrandChildFinalBar={6}, expectedChildAdjustmentValue={7}, expectedGrandChildAdjustmentValue={7}")
    @CsvSource({
            "false, 100.00,  FIXED,    10,       120,    -7.89,   122.11,   0,   0 ",
            "false, 100.00,  PERCENT,  10,       120,    -25,     100.00,   0,   0 ",
            "false, 100.00,  PERCENT, -10,       100,     25,     135.00,   0,   0 ",
            "true,  430.00,  FIXED,    10,       450.00, -7.89,   452.11,   0,   0 ",
            "true,  430.00,  PERCENT,  10,       483.00,  -25,    372.25,   0,   0 ",
            "true,  430.00,  PERCENT, -10,       397.00,   25,    506.25,   0,   0  "
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     *  finalBar for STE(AGILE Grand child)  =  finalBar for STE(parent-Agile) + ( adjustment on finalBar for STE(parent-agile) ) + 10(adult package)
     */
    void roundOptimalBARsForAgileGrandChildPriceWithComponentRoomsPriceAsSumOfPartsEnabled(ArgumentsAccessor argumentsAccessor) {
        boolean isPriceAsSumOfPartEnabled = argumentsAccessor.getBoolean(0);
        double componentRoomFinalBar = argumentsAccessor.getDouble(1);
        String optimalBarType = argumentsAccessor.getString(2);
        double agileOptimalBar = argumentsAccessor.getDouble(3);
        double expectedAgileFinalBar = argumentsAccessor.getDouble(4);
        double grandChildOptimalBar = argumentsAccessor.getDouble(5);
        double expectedAgileGrandChildFinalBar = argumentsAccessor.getDouble(6);
        double expectedChildAdjustmentValue = argumentsAccessor.getDouble(7);
        double expectedGrandChildAdjustmentValue = argumentsAccessor.getDouble(8);

        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(80.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(barProduct, STE, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO);
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO);
        //CHILD  and GrandChild- unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(60), barProduct, true);
        Product grandChild = createAgileProduct(BigDecimal.valueOf(60), child, true);
        //add agile product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-30.00), BigDecimal.valueOf(30.00));
        addProductRateOffset(grandChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-30.00), BigDecimal.valueOf(30.00));

        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        addCPDecisionBarOutput(STE, grandChild, optimalBarType, grandChildOptimalBar);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(grandChild, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(80).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(90).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        CPDecisionBAROutput childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedChildAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
        //BAR GrandChild final Bar checking
        CPDecisionBAROutput grandChildOutput = filterOutputsByProduct(grandChild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(grandChildOptimalBar), grandChildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileGrandChildFinalBar).setScale(2, RoundingMode.HALF_UP), grandChildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedGrandChildAdjustmentValue).setScale(2, RoundingMode.HALF_UP), grandChildOutput.getAdjustmentValue());
    }


    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, agileGrandChildOptimalBar={5},expectedAgileGrandChildFinalBar={6}, expectedChildAdjustmentValue={7}, expectedGrandChildAdjustmentValue={7}")
    @CsvSource({
            "false, 110.00,  FIXED,    10,       130,    -7.89,   132.11,   0,   0 ",
            "false, 110.00,  PERCENT,  10,       131,    -25,     108.25,   0,   0 ",
            "false, 110.00,  PERCENT, -10,       109,     25,     146.25,   0,   0 ",
            "true,  455.00,  FIXED,    10,       475.00, -7.89,   477.11,   0,   0 ",
            "true,  455.00,  PERCENT,  10,       510.50,  -25,    392.88,   0,   0 ",
            "true,  455.00,  PERCENT, -10,       419.50,   25,    534.38,   0,   0  "
    })
    /**
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     *  finalBar for STE(AGILE Grand child)  =  finalBar for STE(parent-Agile) + ( adjustment on finalBar for STE(parent-agile) ) + 10(adult package)
     */
    void independentRoundOptimalBARsForAgileGrandChildPriceWithComponentRoomsPriceAsSumOfPartsEnabled(ArgumentsAccessor argumentsAccessor) {
        boolean isPriceAsSumOfPartEnabled = argumentsAccessor.getBoolean(0);
        double componentRoomFinalBar = argumentsAccessor.getDouble(1);
        String optimalBarType = argumentsAccessor.getString(2);
        double agileOptimalBar = argumentsAccessor.getDouble(3);
        double expectedAgileFinalBar = argumentsAccessor.getDouble(4);
        double grandChildOptimalBar = argumentsAccessor.getDouble(5);
        double expectedAgileGrandChildFinalBar = argumentsAccessor.getDouble(6);
        double expectedChildAdjustmentValue = argumentsAccessor.getDouble(7);
        double expectedGrandChildAdjustmentValue = argumentsAccessor.getDouble(8);

        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(85.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(independentProduct, STE, startDate, BigDecimal.valueOf(110), null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("55"), new BigDecimal("305"), BigDecimal.ZERO, independentProduct.getId());
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN, independentProduct.getId());
        addTransientPricingBaseAccomType(STE, new BigDecimal("55"), new BigDecimal("705"), BigDecimal.ZERO, independentProduct.getId());
        //CHILD  and GrandChild- unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(65), independentProduct, true);
        Product grandChild = createAgileProduct(BigDecimal.valueOf(65), child, true);
        //add agile product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-35.00), BigDecimal.valueOf(35.00));
        addProductRateOffset(grandChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-35.00), BigDecimal.valueOf(35.00));

        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        addCPDecisionBarOutput(STE, grandChild, optimalBarType, grandChildOptimalBar);
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(grandChild, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(85).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(95).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        CPDecisionBAROutput childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedChildAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
        //BAR GrandChild final Bar checking
        CPDecisionBAROutput grandChildOutput = filterOutputsByProduct(grandChild, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(grandChildOptimalBar), grandChildOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileGrandChildFinalBar).setScale(2, RoundingMode.HALF_UP), grandChildOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedGrandChildAdjustmentValue).setScale(2, RoundingMode.HALF_UP), grandChildOutput.getAdjustmentValue());
    }

    private CPDecisionBAROutput addCPDecisionBarOutput(String accomTypeCode, Product product, String optimalBarType, double agileOptimalBar) {
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(product, accomTypeCode, startDate, BigDecimal.valueOf(agileOptimalBar), null, null);
        childOutput.setOptimalBarType(OptimalBarType.valueOf(optimalBarType));
        childOutput.setDecisionReasonTypeId(1);
        tenantCrudService().save(childOutput);
        return childOutput;
    }

    private Product createAgileProduct(BigDecimal floorValue, Product parentProduct, boolean optimized) {
        Product child = createChildProduct("Child of " + parentProduct.getName(), parentProduct.getId());
        child.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        child.setFloor(floorValue);
        child.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        child.setOptimized(optimized);
        child = tenantCrudService().save(child);
        return child;
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}, expectedAgileAdjustmentValue={5}")
    @CsvSource({
            "false, 100.00,  FIXED,   -7.89,  80, 0",
            "false, 100.00,  PERCENT, -30.00, 80, 0",
            "true,  430.00,  PERCENT, -30.00, 311, 0"
    })

    /**
     *  For non-optimized product optimal-bar does not matter ..It only reads the product rate offsets data
     *  Offsets for AccomClass of KING,Queen is set to -25 and offsets for accomClass of STE is set to -30 . Hence use -30% for finalBar calculation of STE RT
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void roundOptimalBARsForNonOptimizedAgileProductWithComponentRoomsPriceAsSumOfPartsEnabled(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar, double expectedAgileAdjustmentValue) {
        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(80.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(barProduct, STE, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO);
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO);
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(60), barProduct, false);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-25.00));
        //child has optimal bar of -7.89 (which represents the offset decision)
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        CPDecisionBAROutput childOutput;
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(80).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(90).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }

    @ParameterizedTest(name = "row: {index} ==> isPriceAsSumOfPartEnabled= {0}, componentRoomFinalBar={1}, optimalBarType={2}, agileOptimalBar={3}, expectedAgileFinalBar={4}, expectedAgileAdjustmentValue={5}")
    @CsvSource({
            "false, 110.00,  FIXED,   -7.89,  81.50, 0",
            "false, 110.00,  PERCENT, -30.00, 81.50, 0",
            "true,  455.00,  PERCENT, -30.00, 305.75, 0"
    })

    /**
     *  For non-optimized product optimal-bar does not matter ..It only reads the product rate offsets data
     *  Offsets for AccomClass of KING,Queen is set to -25 and offsets for accomClass of STE is set to -30 . Hence use -30% for finalBar calculation of STE RT
     *  when componentRoomPriceAsSumOfPart Enabled then
     *  finalBar for STE(BAR Product)  = 2 * finalBar of King + 3 * finalBar of Queen
     *  finalBar for STE(AGILE Product)  =  finalBar for STE + ( adjustment on finalBar for STE ) + 10(adult package)
     */
    void independentRoundOptimalBARsForNonOptimizedAgileProductWithComponentRoomsPriceAsSumOfPartsEnabled(boolean isPriceAsSumOfPartEnabled, double componentRoomFinalBar, String optimalBarType, double agileOptimalBar, double expectedAgileFinalBar, double expectedAgileAdjustmentValue) {
        //GIVEN
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfPartEnabled);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_KING, RT_QUEEN);
        //BAR - optimal bar for component RT is 100.00 , King = 80 and queen = 90
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(85.00), null, null);
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.00), null, null);
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBAROutput(independentProduct, STE, startDate, BigDecimal.valueOf(110), null, null);
        addTransientPricingBaseAccomType(RT_KING, new BigDecimal("50"), new BigDecimal("300"), BigDecimal.ZERO, independentProduct.getId());
        addOffset(RT_QUEEN, startDate, startDate, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN, independentProduct.getId());
        addTransientPricingBaseAccomType(STE, new BigDecimal("50"), new BigDecimal("700"), BigDecimal.ZERO, independentProduct.getId());
        //CHILD - unfenced and packaged with
        Product child = createAgileProduct(BigDecimal.valueOf(65), independentProduct, false);
        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-30.00));
        //child has optimal bar of -7.89 (which represents the offset decision)
        addCPDecisionBarOutput(STE, child, optimalBarType, agileOptimalBar);
        CPDecisionBAROutput childOutput;
        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(85).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(95).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(BigDecimal.valueOf(componentRoomFinalBar).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
        //final bar = optimalBar + ( agileOffsetAdjustments on OptimalBar) + 10 for adult breakfast package
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertBigDecimalEquals(getDefaultPrecisionValueOf(agileOptimalBar), childOutput.getOptimalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileFinalBar).setScale(2, RoundingMode.HALF_UP), childOutput.getFinalBAR());
        assertBigDecimalEquals(BigDecimal.valueOf(expectedAgileAdjustmentValue).setScale(2, RoundingMode.HALF_UP), childOutput.getAdjustmentValue());
    }

    @Test
    public void shouldRecalculatesAgileRatesForNonOptimizedProductWhenBARSpecificOverrideIsDoneAndManualBarUploadIsTriggered_ShouldIgnoreBARMinimumPriceChange() {
        //GIVEN
        // Set a minimum increment value that minimumPriceChange won't be satisfied
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        updatePricingAccomClassData(RT_KING, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        // less than increment |89.88 - 99.87| = 9.99
        double previousDailyBarOutput = 89.88;
        double independentPreviousDailyBarOutput = 99.88;
        addDecisionDailybarOutput(RT_KING, startDate, BigDecimal.valueOf(previousDailyBarOutput), BigDecimal.valueOf(previousDailyBarOutput), BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_KING, startDate, BigDecimal.valueOf(independentPreviousDailyBarOutput), BigDecimal.valueOf(independentPreviousDailyBarOutput), BigDecimal.ZERO, BigDecimal.ZERO, independentProduct);

        //BAR - optimal bar at 89.88 and specificOverride = 99.87. prettyBAR and finalBAR would also be set to 99.87 as we update specificOverride values immediately in prettyBAR and finalBAR
        addCPDecisionBAROutput(barProduct, RT_KING, startDate, BigDecimal.valueOf(89.88), BigDecimal.valueOf(99.87), BigDecimal.valueOf(99.87));
        addCPDecisionBAROutput(independentProduct, RT_KING, startDate, BigDecimal.valueOf(99.88), BigDecimal.valueOf(109.87), BigDecimal.valueOf(109.87));

        //CHILD - unfenced and packaged with no rounding rule and a offset decision of -7.89 fixed
        Product child = createChildProduct(CHILD_ONE_OF_SYSTEM_DEFAULT, barProduct.getId());
        child.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        child.setOptimized(true);
        child.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        child = tenantCrudService().save(child);

        Product independentChild = createChildProduct(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentProduct.getId());
        independentChild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentChild.setOptimized(true);
        independentChild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentChild = tenantCrudService().save(independentChild);

        //add child product rate offset floor and ceiling
        addProductRateOffset(child, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-15.00), BigDecimal.valueOf(-5.00));
        addProductRateOffset(independentChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-20.00), BigDecimal.valueOf(-10.00));

        //child has optimal bar of -7.89 (which represents the offset decision), previous prettyBar = 89.88 -(89.88 * 0.0789) + 10 = 92.788
        CPDecisionBAROutput childOutput = addCPDecisionBAROutput(child, RT_KING, startDate, BigDecimal.valueOf(-7.89), BigDecimal.valueOf(92.79), null);
        childOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(childOutput);
        CPDecisionBAROutput independentChildOutput = addCPDecisionBAROutput(independentChild, RT_KING, startDate, BigDecimal.valueOf(-12.89), BigDecimal.valueOf(97.79), null);
        independentChildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentChildOutput);

        //child has adult breakfast package
        addProductPackage(child, adultBreakfastPackage);
        addProductPackage(independentChild, adultBreakfastPackage);

        //GRANDCHILD
        Product grandchild = createChildProduct(GRANDCHILD_ONE_OF_CHILD_ONE, child.getId());
        grandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        grandchild.setOptimized(false);
        grandchild.setRoundingRule(RoundingRule.UP);
        grandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        grandchild = tenantCrudService().save(grandchild);
        addProductRateOffset(grandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5));
        addProductPackage(grandchild, adultBreakfastPackage);

        Product independentGrandchild = createChildProduct(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentChild.getId());
        independentGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue());
        independentGrandchild.setOptimized(false);
        independentGrandchild.setRoundingRule(RoundingRule.UP);
        independentGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        independentGrandchild = tenantCrudService().save(independentGrandchild);
        addProductRateOffset(independentGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10));
        addProductPackage(independentGrandchild, adultBreakfastPackage);

        //grandchild has optimal bar = -5 and previousPrettyBar 98.15 (92.79 - 4.64 (5% of parent final bar (92.79)) + 10)
        CPDecisionBAROutput grandchildOutput = addCPDecisionBAROutput(grandchild, RT_KING, startDate, getDefaultPrecisionValueOf(-5), BigDecimal.valueOf(98.15), null);
        grandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(grandchildOutput);
        CPDecisionBAROutput independentGrandchildOutput = addCPDecisionBAROutput(independentGrandchild, RT_KING, startDate, getDefaultPrecisionValueOf(-10), BigDecimal.valueOf(103.15), null);
        independentGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentGrandchildOutput);

        //GREAT GRANDCHILD
        Product greatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, grandchild.getId());
        greatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        greatGrandchild.setOptimized(true);
        greatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        greatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(greatGrandchild);
        addProductRateOffset(greatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(2.00), BigDecimal.valueOf(14.00));

        Product independentGreatGrandchild = createChildProduct(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentGrandchild.getId());
        independentGreatGrandchild.setType(AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.getValue());
        independentGreatGrandchild.setOptimized(true);
        independentGreatGrandchild.setRoundingRule(RoundingRule.WHOLE);
        independentGreatGrandchild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentGreatGrandchild);
        addProductRateOffset(independentGreatGrandchild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(7.00), BigDecimal.valueOf(19.00));

        //great grandchild has optimal bar of 9.5% (which represents the offset decision) previousPrettyBar 117.47 (98.15 + 4.64 (5% of parent final bar (98.15)) + 10)
        CPDecisionBAROutput greatGrandchildOutput = addCPDecisionBAROutput(greatGrandchild, RT_KING, startDate, BigDecimal.valueOf(9.5), BigDecimal.valueOf(117.47), null);
        greatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(greatGrandchildOutput);
        CPDecisionBAROutput independentGreatGrandchildOutput = addCPDecisionBAROutput(independentGreatGrandchild, RT_KING, startDate, BigDecimal.valueOf(14.5), BigDecimal.valueOf(122.47), null);
        independentGreatGrandchildOutput.setOptimalBarType(OptimalBarType.PERCENT);
        tenantCrudService().save(independentGreatGrandchildOutput);

        //great grandchild has adult breakfast package
        addProductPackage(greatGrandchild, adultPercentPackage);
        addProductPackage(independentGreatGrandchild, adultPercentPackage);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);

        //THEN
        //assert BAR output uses new rate(specific override)
        CPDecisionBAROutput barOutput = filterOutputsByProduct(barProduct, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(99.87), barOutput.getPrettyBAR().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(99.87), barOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));

        //assert CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = 101.99 (final bar of parent/previous output (99.87) - 7.89% (7.091532) + 10 for adult breakfast package (10 * 2 occupants))
        childOutput = filterOutputsByProduct(child, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(-7.89), childOutput.getOptimalBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(101.99), childOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(101.99), childOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("0.00"), childOutput.getAdjustmentValue());

        //assert GRANDCHILD output:
        //optimal bar: parent optimal bar (101.99) - 5.4935 (5% of 101.99) for product rate offset
        //final bar: 101.99 - 5% (101.99) + 10 = 106.89 = 107.00 (rounding)
        grandchildOutput = filterOutputsByProduct(grandchild, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(107.00).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(107.00).setScale(2, RoundingMode.HALF_UP), grandchildOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("0.00"), grandchildOutput.getAdjustmentValue());

        //assert GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 107 (parent final bar)
        //           + 9.5% for offset (10.165)
        //           + 10% for package (10.7)
        //           = 127.865 =128.00(rounded to nearest WHOLE number)
        greatGrandchildOutput = filterOutputsByProduct(greatGrandchild, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(9.50).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getOptimalBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(128.00).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(128.00).setScale(2, RoundingMode.HALF_UP), greatGrandchildOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("21.00"), greatGrandchildOutput.getAdjustmentValue());

        //assert BAR output uses new rate(specific override)
        CPDecisionBAROutput independentOutput = filterOutputsByProduct(independentProduct, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(109.87), independentOutput.getPrettyBAR().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(109.87), independentOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));

        //assert CHILD output:
        //optimal bar = -7.89 SAS derived offset
        //final bar = 101.99 (final bar of parent/previous output (99.87) - 7.89% (7.091532) + 10 for adult breakfast package (10 * 2 occupants))
        independentChildOutput = filterOutputsByProduct(independentChild, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(-12.89), independentChildOutput.getOptimalBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(105.71), independentChildOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(105.71), independentChildOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("0.00"), independentChildOutput.getAdjustmentValue());

        //assert GRANDCHILD output:
        //optimal bar: parent optimal bar (101.99) - 5.4935 (5% of 101.99) for product rate offset
        //final bar: 101.99 - 5% (101.99) + 10 = 106.89 = 107.00 (rounding)
        independentGrandchildOutput = filterOutputsByProduct(independentGrandchild, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(106.00).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(106.00).setScale(2, RoundingMode.HALF_UP), independentGrandchildOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("0.00"), independentGrandchildOutput.getAdjustmentValue());

        //assert GREAT GRANDCHILD output:
        //optimal bar: 9.5 SAS derived offset
        //final bar: 107 (parent final bar)
        //           + 9.5% for offset (10.165)
        //           + 10% for package (10.7)
        //           = 127.865 =128.00(rounded to nearest WHOLE number)
        independentGreatGrandchildOutput = filterOutputsByProduct(independentGreatGrandchild, cpDecisionBAROutputs).get(0);
        assertEquals(BigDecimal.valueOf(14.50).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getOptimalBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(132.00).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(132.00).setScale(2, RoundingMode.HALF_UP), independentGreatGrandchildOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("26.00"), independentGreatGrandchildOutput.getAdjustmentValue());
    }

    @Test
    public void onlyFencedFixedProductWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedProduct);

        loyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedChildProduct);

        independentLoyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedProduct);

        independentLoyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedChildProduct);

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add loyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);

        // Add loyaltyFencedChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutputChild = addCPDecisionBAROutput(loyaltyFencedChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(80.0), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add IndependentloyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.0), null, null);

        // Add IndependentloyaltyFencedChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutputChild = addCPDecisionBAROutput(independentLoyaltyFencedChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(85.0), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(loyaltyOutputChild, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedChildProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("80.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10"), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentLoyaltyOutputChild, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedChildProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void onlyFencedPercentageProductWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add loyaltyFencedPercentageProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedPercentageProduct, RT_QUEEN, startDate, BigDecimal.valueOf(85.0), null, null);

        // Add loyaltyFencedPercentageChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutputChild = addCPDecisionBAROutput(loyaltyFencedPercentageChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(72.25), null, null);

        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);
        CPDecisionBAROutput independentLoyaltyOutputChild = addCPDecisionBAROutput(independentLoyaltyFencedPercentageChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(77.25), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("85.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(loyaltyOutputChild, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("72.25"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentLoyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("93.50"), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15"), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentLoyaltyOutputChild, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("79.48"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-14.99"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void onlyFencedFixedProductWithOffsetsAndSupplementsWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedProduct);

        loyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedChildProduct);

        independentLoyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedProduct);

        independentLoyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedChildProduct);

        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0), independentProduct.getId());

        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add loyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);

        // Add loyaltyFencedChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutputChild = addCPDecisionBAROutput(loyaltyFencedChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(80.0), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add IndependentloyaltyFencedProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, RT_QUEEN, startDate, BigDecimal.valueOf(95.0), null, null);

        // Add IndependentloyaltyFencedChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutputChild = addCPDecisionBAROutput(independentLoyaltyFencedChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(85.0), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(loyaltyOutputChild, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedChildProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("80.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentLoyaltyOutputChild, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedChildProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-10.00"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void onlyFencedPercentageProductWithOffsetsAndSupplementsWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.THREE_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FOUR_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.FIVE_ADULTS, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0), independentProduct.getId());


        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(10.0));
        addSupplement(RT_QUEEN, OccupancyType.THREE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FOUR_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());
        addSupplement(RT_QUEEN, OccupancyType.FIVE_ADULTS, BigDecimal.valueOf(15.0), independentProduct.getId());


        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput barOutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add loyaltyFencedPercentageProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutput = addCPDecisionBAROutput(loyaltyFencedPercentageProduct, RT_QUEEN, startDate, BigDecimal.valueOf(85.0), null, null);

        // Add loyaltyFencedPercentageChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput loyaltyOutputChild = addCPDecisionBAROutput(loyaltyFencedPercentageChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(72.25), null, null);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentOutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(110), null, null);

        // Add IndependentloyaltyFencedPercentageProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageProduct, RT_QUEEN, startDate, BigDecimal.valueOf(90.0), null, null);

        // Add IndependentloyaltyFencedPercentageChildProduct CPDecisionBAROutput record
        CPDecisionBAROutput independentLoyaltyOutputChild = addCPDecisionBAROutput(independentLoyaltyFencedPercentageChildProduct, RT_QUEEN, startDate, BigDecimal.valueOf(77.25), null, null);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(6, cpDecisionBAROutputs.size());

        assertEquals(barOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(loyaltyOutput, cpDecisionBAROutputs.get(1));
        assertEquals(loyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("85.00"), cpDecisionBAROutputs.get(1).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15.00"), cpDecisionBAROutputs.get(1).getAdjustmentValue());

        assertEquals(loyaltyOutputChild, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("72.25"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(independentOutput, cpDecisionBAROutputs.get(3));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(BigDecimal.valueOf(110), cpDecisionBAROutputs.get(3).getFinalBAR());

        assertEquals(independentLoyaltyOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentLoyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("93.50"), cpDecisionBAROutputs.get(4).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-15.00"), cpDecisionBAROutputs.get(4).getAdjustmentValue());

        assertEquals(independentLoyaltyOutputChild, cpDecisionBAROutputs.get(5));
        assertEquals(independentLoyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("79.48"), cpDecisionBAROutputs.get(5).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("-14.99"), cpDecisionBAROutputs.get(5).getAdjustmentValue());
    }

    @Test
    public void offsetFixedProductForNonBaseRTOnlyWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedProduct);

        loyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedChildProduct);

        independentLoyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedProduct);

        independentLoyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedChildProduct);

        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - product
        CPDecisionBAROutput productNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the non-Base RT - child product
        CPDecisionBAROutput productNonBaseRTChildOutput = addCPDecisionBAROutput(loyaltyFencedChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("59.00"), null, null);

        // Add the Base RT - Independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("37.00"), null, null);

        // Add the non-Base RT - Independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - Independent product
        CPDecisionBAROutput independentLoyaltyFencedProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("70.00"), null, null);

        // Add the non-Base RT - Independent child product
        CPDecisionBAROutput independentLoyaltyFencedChildProductNonBaseRTChildOutput = addCPDecisionBAROutput(independentLoyaltyFencedChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("64.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-10.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);
        addProductRateOffset(loyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("6.5"));
        addProductRateOffset(loyaltyFencedChildProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("6.5"));
        addProductRateOffset(independentLoyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("11.5"));
        addProductRateOffset(independentLoyaltyFencedChildProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("11.5"));

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(8, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(productNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(productNonBaseRTChildOutput, cpDecisionBAROutputs.get(3));
        assertEquals(loyaltyFencedChildProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("41.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("37.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(5).getFinalBAR());

        assertEquals(independentLoyaltyFencedProductNonBaseRTOutput, cpDecisionBAROutputs.get(6));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(6).getProduct());
        assertBigDecimalEquals(new BigDecimal("44.00"), cpDecisionBAROutputs.get(6).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("12.00"), cpDecisionBAROutputs.get(6).getAdjustmentValue());

        assertEquals(independentLoyaltyFencedChildProductNonBaseRTChildOutput, cpDecisionBAROutputs.get(7));
        assertEquals(independentLoyaltyFencedChildProduct, cpDecisionBAROutputs.get(7).getProduct());
        assertBigDecimalEquals(new BigDecimal("56.00"), cpDecisionBAROutputs.get(7).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("12.00"), cpDecisionBAROutputs.get(7).getAdjustmentValue());
    }

    @Test
    public void offsetPercentageProductForNonBaseRTOnlyWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - product
        CPDecisionBAROutput productNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedPercentageProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the non-Base RT - child product
        CPDecisionBAROutput productNonBaseRTChildOutput = addCPDecisionBAROutput(loyaltyFencedPercentageChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("70.00"), null, null);

        // Add the Base RT - Independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("35.00"), null, null);

        // Add the non-Base RT - Independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - Independent product
        CPDecisionBAROutput independentProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageProduct, nonBaseRT, LocalDate.now(), new BigDecimal("70.00"), null, null);

        // Add the non-Base RT - Independent child product
        CPDecisionBAROutput independentProductNonBaseRTChildOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("75.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);
        addProductRateOffset(loyaltyFencedPercentageProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("25.5"));
        addProductRateOffset(loyaltyFencedPercentageChildProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("61.5"));
        addProductRateOffset(independentLoyaltyFencedPercentageProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("30.5"));
        addProductRateOffset(independentLoyaltyFencedPercentageChildProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("66.5"));

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(8, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(productNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("25.93"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(productNonBaseRTChildOutput, cpDecisionBAROutputs.get(3));
        assertEquals(loyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("55.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("61.76"), cpDecisionBAROutputs.get(3).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("35.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(5).getFinalBAR());

        assertEquals(independentProductNonBaseRTOutput, cpDecisionBAROutputs.get(6));
        assertEquals(independentLoyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(6).getProduct());
        assertBigDecimalEquals(new BigDecimal("42.00"), cpDecisionBAROutputs.get(6).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("31.25"), cpDecisionBAROutputs.get(6).getAdjustmentValue());

        assertEquals(independentProductNonBaseRTChildOutput, cpDecisionBAROutputs.get(7));
        assertEquals(independentLoyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(7).getProduct());
        assertBigDecimalEquals(new BigDecimal("70.00"), cpDecisionBAROutputs.get(7).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("66.67"), cpDecisionBAROutputs.get(7).getAdjustmentValue());
    }

    @Test
    public void offsetFixedProductForNonBaseRTWithPriceExcludeRCWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedProduct);

        loyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(loyaltyFencedChildProduct);

        independentLoyaltyFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedProduct);

        independentLoyaltyFencedChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedChildProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        tenantCrudService().save(independentLoyaltyFencedChildProduct);

        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - product
        CPDecisionBAROutput productNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the non-Base RT - child product
        CPDecisionBAROutput productNonBaseRTChildOutput = addCPDecisionBAROutput(loyaltyFencedChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("98.00"), null, null);

        // Add the Base RT - independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("37.00"), null, null);

        // Add the non-Base RT - independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - independent product
        CPDecisionBAROutput independentProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedProduct, nonBaseRT, LocalDate.now(), new BigDecimal("70.00"), null, null);

        // Add the non-Base RT - independent child product
        CPDecisionBAROutput independentProductNonBaseRTChildOutput = addCPDecisionBAROutput(independentLoyaltyFencedChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("103.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(-10.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);
        addProductRateOffset(loyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("6.5"));
        addProductRateOffset(loyaltyFencedChildProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("9.5"));
        addProductRateOffset(independentLoyaltyFencedProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("11.5"));
        addProductRateOffset(independentLoyaltyFencedChildProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("14.5"));

        AccomType nonBaseRoomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", nonBaseRT).parameters());
        PricingAccomClass pricingAccomClass = tenantCrudService().findAll(PricingAccomClass.class).stream()
                .filter(pac -> pac.getAccomClass().equals(nonBaseRoomType.getAccomClass()))
                .findFirst().orElse(null);
        pricingAccomClass.setPriceExcluded(true);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(8, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(productNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.00"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(productNonBaseRTChildOutput, cpDecisionBAROutputs.get(3));
        assertEquals(loyaltyFencedChildProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("44.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("10.00"), cpDecisionBAROutputs.get(3).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("37.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(5).getFinalBAR());

        assertEquals(independentProductNonBaseRTOutput, cpDecisionBAROutputs.get(6));
        assertEquals(independentLoyaltyFencedProduct, cpDecisionBAROutputs.get(6).getProduct());
        assertBigDecimalEquals(new BigDecimal("44.00"), cpDecisionBAROutputs.get(6).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("12.00"), cpDecisionBAROutputs.get(6).getAdjustmentValue());

        assertEquals(independentProductNonBaseRTChildOutput, cpDecisionBAROutputs.get(7));
        assertEquals(independentLoyaltyFencedChildProduct, cpDecisionBAROutputs.get(7).getProduct());
        assertBigDecimalEquals(new BigDecimal("59.00"), cpDecisionBAROutputs.get(7).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("15.00"), cpDecisionBAROutputs.get(7).getAdjustmentValue());
    }

    @Test
    public void offsetPercentageProductForNonBaseRTWithPriceExcludeRCWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        String baseRT = RT_KING;
        String nonBaseRT = RT_QUEEN;

        setRoundingRule(null, 0, 0, independentProduct.getId());

        setTax(new BigDecimal("9.00"));

        // Add the Base RT - BAR
        CPDecisionBAROutput barBaseRTOutput = addCPDecisionBAROutput(barProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - BAR
        CPDecisionBAROutput barNonBaseRTOutput = addCPDecisionBAROutput(barProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - product
        CPDecisionBAROutput productNonBaseRTOutput = addCPDecisionBAROutput(loyaltyFencedPercentageProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the non-Base RT - child product
        CPDecisionBAROutput productNonBaseRTChildOutput = addCPDecisionBAROutput(loyaltyFencedPercentageChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("98.00"), null, null);

        // Add the Base RT - Independent
        CPDecisionBAROutput independentBaseRTOutput = addCPDecisionBAROutput(independentProduct, baseRT, LocalDate.now(), new BigDecimal("32.00"), null, null);

        // Add the non-Base RT - Independent
        CPDecisionBAROutput independentNonBaseRTOutput = addCPDecisionBAROutput(independentProduct, nonBaseRT, LocalDate.now(), new BigDecimal("27.00"), null, null);

        // Add the non-Base RT - Independent product
        CPDecisionBAROutput independentLoyaltyFencedPercentageProductNonBaseRTOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageProduct, nonBaseRT, LocalDate.now(), new BigDecimal("65.00"), null, null);

        // Add the non-Base RT - Independent child product
        CPDecisionBAROutput independentLoyaltyFencedPercentageChildProductNonBaseRTChildOutput = addCPDecisionBAROutput(independentLoyaltyFencedPercentageChildProduct, nonBaseRT, LocalDate.now(), new BigDecimal("98.00"), null, null);

        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(-5.0));
        addOffset(nonBaseRT, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(-10.0), independentProduct.getId());

        tenantCrudService().deleteAll(ProductRateOffset.class);

        addProductRateOffset(loyaltyFencedPercentageProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("6.5"));
        addProductRateOffset(loyaltyFencedPercentageChildProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("9.5"));
        addProductRateOffset(independentLoyaltyFencedPercentageProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("11.5"));
        addProductRateOffset(independentLoyaltyFencedPercentageChildProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("14.5"));

        AccomType nonBaseRoomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", nonBaseRT).parameters());
        PricingAccomClass pricingAccomClass = tenantCrudService().findAll(PricingAccomClass.class).stream()
                .filter(pac -> pac.getAccomClass().equals(nonBaseRoomType.getAccomClass()))
                .findFirst().orElse(null);
        pricingAccomClass.setPriceExcluded(true);

        service.roundOptimalBARs(startDate, endDate);

        // Get the CPDecisionBAROutputs
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findAll(CPDecisionBAROutput.class);
        assertEquals(8, cpDecisionBAROutputs.size());

        assertEquals(barBaseRTOutput, cpDecisionBAROutputs.get(0));
        assertEquals(barProduct, cpDecisionBAROutputs.get(0).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(0).getFinalBAR());

        assertEquals(barNonBaseRTOutput, cpDecisionBAROutputs.get(1));
        assertEquals(barProduct, cpDecisionBAROutputs.get(1).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(1).getFinalBAR());

        assertEquals(productNonBaseRTOutput, cpDecisionBAROutputs.get(2));
        assertEquals(loyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(2).getProduct());
        assertBigDecimalEquals(new BigDecimal("29.00"), cpDecisionBAROutputs.get(2).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("7.41"), cpDecisionBAROutputs.get(2).getAdjustmentValue());

        assertEquals(productNonBaseRTChildOutput, cpDecisionBAROutputs.get(3));
        assertEquals(loyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(3).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(3).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("10.34"), cpDecisionBAROutputs.get(3).getAdjustmentValue());

        assertEquals(independentBaseRTOutput, cpDecisionBAROutputs.get(4));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(4).getProduct());
        assertBigDecimalEquals(new BigDecimal("32.00"), cpDecisionBAROutputs.get(4).getFinalBAR());

        assertEquals(independentNonBaseRTOutput, cpDecisionBAROutputs.get(5));
        assertEquals(independentProduct, cpDecisionBAROutputs.get(5).getProduct());
        assertBigDecimalEquals(new BigDecimal("27.00"), cpDecisionBAROutputs.get(5).getFinalBAR());

        assertEquals(independentLoyaltyFencedPercentageProductNonBaseRTOutput, cpDecisionBAROutputs.get(6));
        assertEquals(independentLoyaltyFencedPercentageProduct, cpDecisionBAROutputs.get(6).getProduct());
        assertBigDecimalEquals(new BigDecimal("30.00"), cpDecisionBAROutputs.get(6).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("11.11"), cpDecisionBAROutputs.get(6).getAdjustmentValue());

        assertEquals(independentLoyaltyFencedPercentageChildProductNonBaseRTChildOutput, cpDecisionBAROutputs.get(7));
        assertEquals(independentLoyaltyFencedPercentageChildProduct, cpDecisionBAROutputs.get(7).getProduct());
        assertBigDecimalEquals(new BigDecimal("34.00"), cpDecisionBAROutputs.get(7).getFinalBAR());
        assertBigDecimalEquals(new BigDecimal("13.33"), cpDecisionBAROutputs.get(7).getAdjustmentValue());
    }

    /**
     * finalBar = BAR * (1 + optimalBar(of agile)/100)
     * - When finalBar is less than product-floor we set agileFinalBar = agileProductFloor
     */
    @ParameterizedTest
    @CsvSource({
            "80, 10, 20, 10, 1, 110, 10, 121, 10",
            "80, -40, 40, -20, 1, 80, -20, 80, 0",
            "170, 10, 20, 90, 1, 190, 90, 361, 90",
            "80, -40, -20, -30, 1, 80, -20, 80, 0",
            "170, -40, 40, 30, 1, 170, 70, 221, 30"
    })
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_OneWithHiltonOptionToSendAdjustmentForAgileEnabled(ArgumentsAccessor arguments) {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);
        double expectedChildFinalBar = arguments.getDouble(7);
        double expectedChildAdjustmentValue = arguments.getDouble(8);

        setUPAgileProductData(loyaltyFencedPercentageProduct, loyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimalUtil.ONE_HUNDRED, expectedFinalBar, expectedAdjustmentValue, expectedChildFinalBar, expectedChildAdjustmentValue);
    }

    /**
     * finalBar = BAR * (1 + optimalBar(of agile)/100)
     * - When finalBar is less than product-floor we set agileFinalBar = agileProductFloor
     */
    @ParameterizedTest
    @CsvSource({
            "80, 10, 20, 10, 1, 121, 10, 133.10, 10",
            "80, -40, 40, -20, 1, 88, -20, 80, -9.09",
            "170, 10, 20, 90, 1, 209, 90, 397.10, 90",
            "80, -40, -27.27, -30, 1, 80, -27.27, 80, 0",
            "170, -40, 40, 30, 1, 170, 54.55, 221, 30"
    })
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_OneWithHiltonOptionToSendAdjustmentForAgileEnabled(ArgumentsAccessor arguments) {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);
        double expectedChildFinalBar = arguments.getDouble(7);
        double expectedChildAdjustmentValue = arguments.getDouble(8);

        setUpIndependentAgileProductData(independentLoyaltyFencedPercentageProduct, independentLoyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimal.valueOf(110), expectedFinalBar, expectedAdjustmentValue, expectedChildFinalBar, expectedChildAdjustmentValue);
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, floorAdjustment={2}, ceilingAdjustment={3}, agileOptimalBar={4}")
    @CsvSource({
            "80, 10, 20, 10, 15, 80, -20, 80, 0",
            "80, -40, 40, -20, 15, 80, -20, 80, 0",
            "170, 10, 20, 90, 15, 170, 70, 170, 0",
            "80, -40, -20, -30, 15, 80, -20, 80, 0",
            "170, -40, 40, 30, 15, 170, 70, 170, 0"
    })
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldAlwaysSetToProductFloorWithHiltonOptionToSendAdjustmentForAgileEnabled(ArgumentsAccessor arguments) {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);
        double expectedChildFinalBar = arguments.getDouble(7);
        double expectedChildAdjustmentValue = arguments.getDouble(8);

        setUPAgileProductData(loyaltyFencedPercentageProduct, loyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimalUtil.ONE_HUNDRED, expectedFinalBar, expectedAdjustmentValue, expectedChildFinalBar, expectedChildAdjustmentValue);
    }

    @ParameterizedTest(name = "row: {index} ==> optimalBarType={0}, productFloor={1}, floorAdjustment={2}, ceilingAdjustment={3}, agileOptimalBar={4}")
    @CsvSource({
            "80, 10, 20, 10, 15, 80, -27.27, 80, 0",
            "80, -40, 40, -20, 15, 80, -27.27, 80, 0",
            "170, 10, 20, 90, 15, 170, 54.55, 170, 0",
            "80, -40, -20, -30, 15, 80, -27.27, 80, 0",
            "170, -40, 40, 30, 15, 170, 54.55, 170, 0"
    })
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldAlwaysSetToProductFloorWithHiltonOptionToSendAdjustmentForAgileEnabled(ArgumentsAccessor arguments) {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        double productFloorValue = arguments.getDouble(0);
        double floorAdjustment = arguments.getDouble(1);
        double ceilingAdjustment = arguments.getDouble(2);
        double agileOptimalBar = arguments.getDouble(3);
        int decisionReasonTypeId = arguments.getInteger(4);
        double expectedFinalBar = arguments.getDouble(5);
        double expectedAdjustmentValue = arguments.getDouble(6);
        double expectedChildFinalBar = arguments.getDouble(7);
        double expectedChildAdjustmentValue = arguments.getDouble(8);

        setUpIndependentAgileProductData(independentLoyaltyFencedPercentageProduct, independentLoyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);

        service.roundOptimalBARs(startDate, endDate);

        verifyThatFinalBarIs(BigDecimal.valueOf(110), expectedFinalBar, expectedAdjustmentValue, expectedChildFinalBar, expectedChildAdjustmentValue);
    }

    @Test
    void agileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldThrowExceptionWhenProductFloorIsZeroWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        loyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageProduct);

        loyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        loyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(loyaltyFencedPercentageChildProduct);

        double productFloorValue = 0.0;
        double floorAdjustment = 10;
        double ceilingAdjustment = 20;
        double agileOptimalBar = 60;
        int decisionReasonTypeId = 15;

        List<CPDecisionBAROutput> agileCPBarOutputs = setUPAgileProductData(loyaltyFencedPercentageProduct, loyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);
        CPDecisionBAROutput agileCPBarOutput = agileCPBarOutputs.get(0);

        Exception exception = assertThrows(TetrisException.class, () ->
                service.roundOptimalBARs(startDate, endDate));

        String expectedMessage = "Tetris Errors: [0 - UNEXPECTED_ERROR - Configuration Error:Agile product floor value is " +
                "set to ZERO (0.00) for optimized product:" + agileCPBarOutput.getProduct().getName() + " and decisionTypeId= 15 received for " +
                "{Date:" + agileCPBarOutput.getArrivalDate() + ", RoomType:" + agileCPBarOutput.getAccomType().getAccomTypeCode() + ", productId:" + agileCPBarOutput.getProduct().getId() + ", decisionId:" + agileCPBarOutput.getDecisionId() + "} - User.id: 11403 - Workcontext: null]";
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void independentAgileOptimizedProductFinalBarForDecisionReasonTypeId_Fifteen_shouldThrowExceptionWhenProductFloorIsZeroWithHiltonOptionToSendAdjustmentForAgileEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);

        independentLoyaltyFencedPercentageProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageProduct);

        independentLoyaltyFencedPercentageChildProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        independentLoyaltyFencedPercentageChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        tenantCrudService().save(independentLoyaltyFencedPercentageChildProduct);

        double productFloorValue = 0.0;
        double floorAdjustment = 10;
        double ceilingAdjustment = 20;
        double agileOptimalBar = 60;
        int decisionReasonTypeId = 15;

        List<CPDecisionBAROutput> agileCPBarOutputs = setUpIndependentAgileProductData(independentLoyaltyFencedPercentageProduct, independentLoyaltyFencedPercentageChildProduct, productFloorValue, floorAdjustment, ceilingAdjustment, agileOptimalBar, decisionReasonTypeId);
        CPDecisionBAROutput agileCPBarOutput = agileCPBarOutputs.get(0);

        Exception exception = assertThrows(TetrisException.class, () ->
                service.roundOptimalBARs(startDate, endDate));

        String expectedMessage = "Tetris Errors: [0 - UNEXPECTED_ERROR - Configuration Error:Agile product floor value is " +
                "set to ZERO (0.00) for optimized product:" + agileCPBarOutput.getProduct().getName() + " and decisionTypeId= 15 received for " +
                "{Date:" + agileCPBarOutput.getArrivalDate() + ", RoomType:" + agileCPBarOutput.getAccomType().getAccomTypeCode() + ", productId:" + agileCPBarOutput.getProduct().getId() + ", decisionId:" + agileCPBarOutput.getDecisionId() + "} - User.id: 11403 - Workcontext: null]";
        assertEquals(expectedMessage, exception.getMessage());
    }
}