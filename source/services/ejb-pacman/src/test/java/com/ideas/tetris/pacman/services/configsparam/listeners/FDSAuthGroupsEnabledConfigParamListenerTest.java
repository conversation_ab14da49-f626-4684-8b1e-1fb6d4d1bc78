package com.ideas.tetris.pacman.services.configsparam.listeners;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.platform.common.configparams.components.ConfigParamChangeEvent;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class FDSAuthGroupsEnabledConfigParamListenerTest {

    @InjectMocks
    private FDSAuthGroupsEnabledConfigParamListener listener;
    @Mock
    JobServiceLocal jobService;
    @Mock
    private ClientService clientService;
    @Mock
    private CrudService globalCrudService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Captor
    ArgumentCaptor<HashMap<String, Object>> jobParameterCaptor;

    private static final String CLIENT_CODE = "BSTN";
    private static final String WORK_CONTEXT_CLIENT = "pacman." + CLIENT_CODE;
    private static final String WORK_CONTEXT_GLOBAL = "pacman";

    @Test
    public void toggle_clientLevel_Enabled() {
        Client client = new Client(3);
        client.setCode(CLIENT_CODE);
        when(clientService.getClientByCode(anyString())).thenReturn(client);

        ConfigParameterValue parameterValue = createConfigParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), Constants.TRUE, WORK_CONTEXT_CLIENT);
        ConfigParamChangeEvent event = new ConfigParamChangeEvent(parameterValue);

        listener.parameterChange(event);

        Mockito.verify(jobService).startJob(eq(JobName.FDSAuthGroupsMigrationJob), jobParameterCaptor.capture());
        Map<String, Object> parameters = jobParameterCaptor.getValue();
        assertEquals(5, parameters.size());
        assertEquals(client.getId(), parameters.get(JobParameterKey.CLIENT_ID));
        assertEquals(client.getCode(), parameters.get(JobParameterKey.CLIENT_CODE));
        assertEquals("false", parameters.get("deleteAll"));
        assertNotNull(parameters.get(JobParameterKey.DATE));
    }

    @Test
    public void toggle_clientLevel_Disabled() {
        Client client = new Client(3);
        client.setCode(CLIENT_CODE);
        when(clientService.getClientByCode(anyString())).thenReturn(client);

        ConfigParameterValue parameterValue = createConfigParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), Constants.FALSE, WORK_CONTEXT_CLIENT);
        ConfigParamChangeEvent event = new ConfigParamChangeEvent(parameterValue);

        listener.parameterChange(event);

        Mockito.verify(jobService, never()).startJob(eq(JobName.FDSAuthGroupsMigrationJob), Mockito.any());
    }

    @Test
    public void toggle_GlobalLevel_Enabled() {
        Client client = new Client(3);
        client.setCode(CLIENT_CODE);
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn("false");
        Client client2 = new Client(4);
        client2.setCode("Hilton");
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client2.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn("true");
        Client client3 = new Client(5);
        client3.setCode("HiltonTest");
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client3.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn(null);
        when(globalCrudService.findAll(Client.class)).thenReturn(Arrays.asList(client, client2, client3));

        ConfigParameterValue parameterValue = createConfigParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), Constants.TRUE, WORK_CONTEXT_GLOBAL);
        ConfigParamChangeEvent event = new ConfigParamChangeEvent(parameterValue);

        listener.parameterChange(event);

        Mockito.verify(jobService, never()).startJob(eq(JobName.FDSAuthGroupsMigrationJob), anyMap());
        Mockito.verify(pacmanConfigParamsService).addParameterValue("pacman." + client.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "true");
        Mockito.verify(pacmanConfigParamsService, never()).addParameterValue("pacman." + client2.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "true");
        Mockito.verify(pacmanConfigParamsService).addParameterValue("pacman." + client3.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "true");
    }

    @Test
    public void toggle_GlobalLevel_Disabled() {
        Client client = new Client(3);
        client.setCode(CLIENT_CODE);
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn("true");
        Client client2 = new Client(4);
        client2.setCode("Hilton");
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client2.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn("false");
        Client client3 = new Client(5);
        client3.setCode("HiltonTest");
        when(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(client3.getCode()), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), true)).thenReturn(null);
        when(globalCrudService.findAll(Client.class)).thenReturn(Arrays.asList(client, client2, client3));

        ConfigParameterValue parameterValue = createConfigParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), Constants.FALSE, WORK_CONTEXT_GLOBAL);
        ConfigParamChangeEvent event = new ConfigParamChangeEvent(parameterValue);

        listener.parameterChange(event);

        Mockito.verify(jobService, never()).startJob(eq(JobName.FDSAuthGroupsMigrationJob), anyMap());
        Mockito.verify(pacmanConfigParamsService).addParameterValue("pacman." + client.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "false");
        Mockito.verify(pacmanConfigParamsService, never()).addParameterValue("pacman." + client2.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "false");
        Mockito.verify(pacmanConfigParamsService, never()).addParameterValue("pacman." + client3.getCode(), FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value(), "false");
    }

    private ConfigParameterValue createConfigParameterValue(String name, String value, String context) {
        ConfigParameter parameter = new ConfigParameter();
        parameter.setName(name);

        ConfigParameterValue parameterValue = new ConfigParameterValue();
        parameterValue.setConfigParameter(parameter);
        parameterValue.setContext(context);
        parameterValue.setValue(value);

        return parameterValue;
    }
}