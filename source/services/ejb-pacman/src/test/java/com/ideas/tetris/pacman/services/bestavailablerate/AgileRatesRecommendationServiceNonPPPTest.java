package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesChargeType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputNonHiltonCRS;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutputNonHiltonCRS;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaToJodaLocalDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class AgileRatesRecommendationServiceNonPPPTest extends AgileRatesServiceTest {

    private CPRecommendationService service = new CPRecommendationService();

    @Override
    CPBarDecisionService getCPBarDecisionService() {
        return service;
    }

    @Override
    protected boolean isPerPersonEnabled() {
        return false;
    }

    @Test
    public void baseProductsRatesAreNotChangedBecMinPriceNotMetAndChildsRateAreNotChanged_NonOptimizedProduct_MinChangeShouldNotBeConsideredForChildProduct() {
        executeAndCompare("300", 90, "126.00", "90", 76.5, "76.50", 0, false, 95, "131.00", "95", 81.5, "81.50");
    }

    @Test
    public void baseProductsRatesAreChangedAndChildsRateAreChanged_NonOptimizedProduct_MinChangeShouldNotBeConsideredForChildProduct() {
        executeAndCompare("30", 90, "126.00", "126.00", 76.5, "107.10000", 0, false, 95, "131.00", "131.00", 81.5, "104.80");
    }

    @Test
    public void baseProductsRatesAreChangedAndChildsRateAreNotChanged_optimizedProduct_when_MinChangeNotMet_forChildProduct() {
        executeAndCompare("30", 90, "126.00", "126.00", 76.5, "76.5", 300, true, 95, "131.00", "131.00", 81.5, "81.50");
    }

    @Test
    public void baseProductsRatesAreChangedAndChildsRateAreChanged_optimizedProduct_when_MinChangeMet_forbaseProduct() {
        executeAndCompare("30", 90, "126.00", "126.00", 76.5, "107.10000", 30, true, 95, "131.00", "131.00", 81.5, "81.50");
    }

    @Test
    public void baseProductsRatesAreNotChangedButChildsRateAreChanged_optimizedProduct_when_MinChangeNotMet_forbaseProduct() {
        executeAndCompare("300", 90, "126.00", "90.00", 76.5, "107.10000", 30, true, 95, "131.00", "95.00", 81.5, "81.50");
    }

    private void executeAndCompare(String barMinPriceChangeValue, int oldBarRate, String newBarValue, String expectedSingleDoubleRateOfBAR,
                                   double oldAgileRate, String expectedSingleDoubleRateOfAgile, int childMinPriceChange, boolean isOptimizedProduct,
                                   int oldIndependentRate, String newIndependentValue, String expectedSingleDoubleRateOfIndependent, double oldIndependentAgileRate,
                                   String expectedSingleDoubleRateOfIndependentAgile) {

        System.setProperty("pacman.singleRateDoubleRate.recalculate.enabled", "true");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal(barMinPriceChangeValue), false);

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Breakfast", barProduct);
        breakfastProduct.setMinimumPriceChange(BigDecimal.valueOf(childMinPriceChange));
        breakfastProduct.setOptimized(isOptimizedProduct);
        breakfastProduct.setFloor(BigDecimal.ONE);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));

        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        independentBreakfastProduct.setMinimumPriceChange(BigDecimal.valueOf(childMinPriceChange));
        independentBreakfastProduct.setOptimized(isOptimizedProduct);
        independentBreakfastProduct.setFloor(BigDecimal.ONE);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-20.00"));

        AccomType accomType_queen = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", RT_QUEEN).parameters());

        addPreviousDailyBarOutput(accomType_queen, startDate, barProduct, BigDecimal.valueOf(oldBarRate), 1);
        addPreviousDailyBarOutput(accomType_queen, startDate, breakfastProduct, BigDecimal.valueOf(oldAgileRate), 1);
        addPreviousDailyBarOutput(accomType_queen, startDate, independentProduct, BigDecimal.valueOf(oldIndependentRate), 2);
        addPreviousDailyBarOutput(accomType_queen, startDate, independentBreakfastProduct, BigDecimal.valueOf(oldIndependentAgileRate), 3);

        // Add BAR CPDecisionBAROutput record
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal(newBarValue));
        tenantCrudService().save(cpDecisionBAROutput);

        // Add Breakfast product CPDecisionBAROutput record
        CPDecisionBAROutput cpDecisionBAROutput1 = addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.5"));
        tenantCrudService().save(cpDecisionBAROutput1);

        // Add Independent CPDecisionBAROutput record
        CPDecisionBAROutput independentCpDecisionBAROutput = addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal(newIndependentValue));
        tenantCrudService().save(independentCpDecisionBAROutput);

        // Add Breakfast product CPDecisionBAROutput record
        CPDecisionBAROutput independentBreakfastProductCpDecisionBAROutput1 = addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.5"));
        tenantCrudService().save(independentBreakfastProductCpDecisionBAROutput1);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        tenantCrudService().flushAndClear();
        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);

        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfBAR), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfBAR), barDailybarOutput.getDoubleRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfAgile), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfAgile), breakfastDailybarOutput.getDoubleRate());

        // Verify the BAR results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);

        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfIndependent), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfIndependent), independentDailybarOutput.getDoubleRate());

        // Verify the breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfIndependentAgile), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal(expectedSingleDoubleRateOfIndependentAgile), independentBreakfastDailybarOutput.getDoubleRate());
    }

    private void addPreviousDailyBarOutput(AccomType accomType, LocalDate date, Product product, BigDecimal rate, int index) {
        DecisionDailybarOutput dailybarOutputBarDayOfArrival = new DecisionDailybarOutput();
        dailybarOutputBarDayOfArrival.setOccupancyDate(date);
        dailybarOutputBarDayOfArrival.setDecisionId(0);
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setPropertyId(TestProperty.H2.getId());
        rateUnqualified.setStatusId(1);
        rateUnqualified.setSystemDefault(product.isSystemDefault() ? 1 : 0);
        rateUnqualified.setDescription("test");
        rateUnqualified.setName("test " + product.getName() + index);
        rateUnqualified.setStartDate(new Date());
        rateUnqualified.setEndDate(new Date());
        rateUnqualified.setDerivedRateCode("test");
        rateUnqualified.setIncludesPackage(0);
        rateUnqualified.setYieldable(0);
        rateUnqualified.setPriceRelative(0);
        String fileName = "test file name" + index;
        FileMetadata fileMetadata = tenantCrudService().findByNamedQuerySingleResult(FileMetadata.BY_FILE_NAME_AND_PROPERTY, QueryParameter.with("name", fileName).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        if (fileMetadata == null) {
            fileMetadata = new FileMetadata();
            fileMetadata.setFileName(fileName);
        }
        fileMetadata.setFileLocation("test location " + index);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setSnapshotTm(new Date());
        fileMetadata.setPreparedDt(new Date());
        fileMetadata.setPreparedTm(new Date());
        ProcessStatus processStatus = new ProcessStatus();
        processStatus.setName("test " + product.getName() + index);
        fileMetadata.setProcessStatusId(tenantCrudService().save(processStatus).getId());
        fileMetadata.setTenantPropertyId(TestProperty.H2.getId());
        List<RecordType> recordTypes = tenantCrudService().findAll(RecordType.class);
        fileMetadata.setRecordTypeId(recordTypes.get(0).getId());
        rateUnqualified.setFileMetadataId(tenantCrudService().save(fileMetadata).getId());
        tenantCrudService().save(rateUnqualified);
        dailybarOutputBarDayOfArrival.setRateUnqualified(rateUnqualified);
        dailybarOutputBarDayOfArrival.setAccomType(accomType);
        dailybarOutputBarDayOfArrival.setProduct(product);
        dailybarOutputBarDayOfArrival.setSingleRate(rate);
        dailybarOutputBarDayOfArrival.setDoubleRate(rate);
        dailybarOutputBarDayOfArrival.setCreateDate(new Date());
        tenantCrudService().save(dailybarOutputBarDayOfArrival);
    }

    @Test
    public void productPercentOffsetsWithExtraAdultChildAndPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("148.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("32.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the Independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("109.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("151.56"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("58.25"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentOffsetsAndPercentSupplementWithExtraAdultChildAndPackages() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.72"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.14"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("52.86"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("153.15"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("76.83"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("57.57"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());

        // Verify the Independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("109.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("156.88"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("79.93"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.85"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    @Disabled("Flaky")
    public void productPercentOffsetsAndPercentSupplementWithExtraAdultChildAndPackages_ChildAgeBucketsEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.72"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.14"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("52.86"), barDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.71"), barDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.43"), barDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.14"), barDailybarOutput.getChildAgeThreeRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("153.15"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("76.83"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("57.57"), breakfastDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15.14"), breakfastDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("20.29"), breakfastDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("25.43"), breakfastDailybarOutput.getChildAgeThreeRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("6.25"), independentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("12.5"), independentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("18.75"), independentDailybarOutput.getChildAgeThreeRate());

        // Verify the Independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("109.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("156.88"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("79.93"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.85"), independentBreakfastDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15.39"), independentBreakfastDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("20.79"), independentBreakfastDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("26.18"), independentBreakfastDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void productPercentOffsetsWithExtraAdultAndPackageOnly() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        addProductPackage(breakfastProduct, adultBreakfast);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("148.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("109.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("151.56"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("58.25"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentOffsetsWithExtraAdultChildNoPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("95.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(5, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), smallGroupDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("90.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("89.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("111.56"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("38.25"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentOffsetsPercentSupplementWithExtraAdultChildNoPackages_BAR_IPP_SG_Linked() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);

        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add linkedFixedFencedProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(linkedFixedFencedProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.72"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.14"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("52.86"), barDailybarOutput.getChildRate());

        // Verify the linkedFixedFenced results
        DecisionDailybarOutput linkedFixedFencedDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.00"), linkedFixedFencedDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("135.72"), linkedFixedFencedDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("73.14"), linkedFixedFencedDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("62.86"), linkedFixedFencedDailybarOutput.getChildRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("113.15"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.83"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("47.57"), smallGroupDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());
    }

    @Test
    @Disabled("Flaky")
    public void productPercentOffsetsPercentSupplementWithExtraAdultChildNoPackages_ChildAgeBucketsEnabled_BAR_IPP_SG_Linked() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add linkedFixedFencedProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(linkedFixedFencedProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.72"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.14"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("52.86"), barDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.71"), barDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.43"), barDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.14"), barDailybarOutput.getChildAgeThreeRate());

        // Verify the linkedFixedFenced results
        DecisionDailybarOutput linkedFixedFencedDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.00"), linkedFixedFencedDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("135.72"), linkedFixedFencedDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("73.14"), linkedFixedFencedDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("62.86"), linkedFixedFencedDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15.71"), linkedFixedFencedDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("21.43"), linkedFixedFencedDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("27.14"), linkedFixedFencedDailybarOutput.getChildAgeThreeRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("113.15"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.83"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("47.57"), smallGroupDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.14"), smallGroupDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10.29"), smallGroupDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("15.43"), smallGroupDailybarOutput.getChildAgeThreeRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("6.25"), independentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("12.5"), independentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("18.75"), independentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void productFixedOffsetsPercentSupplementWithExtraAdultChildNoPackages_BAR_IPP_SG_Linked() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);

        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add linkedFixedFencedProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(linkedFixedFencedProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("126.76"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.29"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("53.05"), barDailybarOutput.getChildRate());

        // Verify the linkedFixedFenced results
        DecisionDailybarOutput linkedFixedFencedDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.00"), linkedFixedFencedDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("136.76"), linkedFixedFencedDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("73.29"), linkedFixedFencedDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("63.05"), linkedFixedFencedDailybarOutput.getChildRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("114.08"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.96"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("47.75"), smallGroupDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());
    }

    @Test
    public void productFixedOffsetsPercentSupplementWithExtraAdultChildNoPackages_ChildAgeBucketsEnabled_BAR_IPP_SG_Linked() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(5.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(10.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(15.0));
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(5.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(10.0), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(15.0), independentProduct.getId());

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, 1, new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, 1, new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, 1, new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, 1, new BigDecimal("20"));

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, independentProduct.getId(), new BigDecimal("5"));
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, independentProduct.getId(), new BigDecimal("10"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, independentProduct.getId(), new BigDecimal("15"));
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, independentProduct.getId(), new BigDecimal("20"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add linkedFixedFencedProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(linkedFixedFencedProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("126.76"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("63.29"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("53.05"), barDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5"), barDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10"), barDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("15"), barDailybarOutput.getChildAgeThreeRate());

        // Verify the linkedFixedFenced results
        DecisionDailybarOutput linkedFixedFencedDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.00"), linkedFixedFencedDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("136.76"), linkedFixedFencedDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("73.29"), linkedFixedFencedDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("63.05"), linkedFixedFencedDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15"), linkedFixedFencedDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("20"), linkedFixedFencedDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("25"), linkedFixedFencedDailybarOutput.getChildAgeThreeRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("114.08"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("56.96"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("47.75"), smallGroupDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5"), smallGroupDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10"), smallGroupDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("15"), smallGroupDailybarOutput.getChildAgeThreeRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("137.50"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.50"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("61.00"), independentDailybarOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5"), independentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10"), independentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("15"), independentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void productPercentOffsetsWithExtraAdultOnlyAndNoPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        breakfastProduct.setOffsetForExtraAdult(true);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        independentBreakfastProduct.setOffsetForExtraAdult(true);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("95.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(5, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the small group package results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), smallGroupDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("90.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("89.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("111.56"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("38.25"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentOffsetsNoExtraAdultOrChildAndNoPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        breakfastProduct.setOffsetForExtraAdult(false);
        breakfastProduct.setOffsetForExtraChild(false);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        independentBreakfastProduct.setOffsetForExtraAdult(false);
        independentBreakfastProduct.setOffsetForExtraChild(false);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small group product CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("95.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(5, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the small group package results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), smallGroupDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("90.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("89.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("111.56"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentOffsetsWithExtraAdultOffsetOfZero() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.ZERO);
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.ZERO, independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("10.00"));

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("15.00"));

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group product CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(5, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), barDailybarOutput.getChildRate());

        // Verify the small group package results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), smallGroupDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("132.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("120.75"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("150.94"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0.00"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentPositiveOffsetsWithExtraAdultChildAndPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        // Add a breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("130.00"), breakfastDailybarOutput.getSingleRate()); // 100 + 10%(100) + 20(adult package)
        assertBigDecimalEquals(new BigDecimal("172.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("64.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("37.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("140.75"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("190.94"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("71.75"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("44.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productFixedOffsetWithExtraAdultChildAndPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("130.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("170.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("70.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("140.00"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("186.25"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("80.00"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("55.00"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPercentNegativeFixedOffsetsWithExtraAdultChildAndPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("-10.00"));
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.FIXED, new BigDecimal("-15.00"));
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("150.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("50.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("110.00"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("156.25"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("50.00"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productFixedOffsetsWithExtraAdultOnlyAndNoPackages() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-10.00"));
        breakfastProduct.setOffsetForExtraAdult(true);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("-15.00"));
        breakfastProduct.setOffsetForExtraAdult(true);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Small Group CPDecisionBAROutput record
        addCPDecisionBAROutput(smallGroupProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("90.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("95.00"));

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(true);

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(5, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the Small Group results
        DecisionDailybarOutput smallGroupDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("90.00"), smallGroupDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), smallGroupDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), smallGroupDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), smallGroupDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("90.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("108.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("36.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.50"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.00"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("89.25"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("110.50"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("38.25"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productPackagesApplyToExtraAdultExtraChild() {
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(20));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(40));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(25));
        addOffset(RT_QUEEN, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(25), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(45), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(30), independentProduct.getId());

        // Add a breakfast product
        Product breakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Breakfast", barProduct);
        breakfastProduct.setOffsetForExtraAdult(false);
        breakfastProduct.setOffsetForExtraChild(false);

        // Add a independent breakfast product
        Product independentBreakfastProduct = addProduct(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED, "Independent Breakfast", independentProduct);
        independentBreakfastProduct.setOffsetForExtraAdult(false);
        independentBreakfastProduct.setOffsetForExtraChild(false);

        // Product Rate Offset should not apply if extra adult/child if offset for each is false
        addProductRateOffsetDefaultAccomClassAndDTA(breakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("10.00"));
        addProductRateOffsetDefaultAccomClassAndDTA(independentBreakfastProduct, AgileRatesOffsetMethod.PERCENTAGE, new BigDecimal("15.00"));

        // Packages should still apply to extra adult/child regardless if apply offsets is checked for them
        AgileRatesPackage adultBreakfast = addAgileRatesPackage("Adult", AgileRatesChargeType.PER_ADULT, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(20));
        AgileRatesPackage childBreakfast = addAgileRatesPackage("Child", AgileRatesChargeType.PER_CHILD, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(10));
        addProductPackage(breakfastProduct, adultBreakfast);
        addProductPackage(breakfastProduct, childBreakfast);
        addProductPackage(independentBreakfastProduct, adultBreakfast);
        addProductPackage(independentBreakfastProduct, childBreakfast);

        // Add BAR CPDecisionBAROutput record
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, new BigDecimal("100.00"));

        // Add Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(breakfastProduct, RT_QUEEN, startDate, new BigDecimal("110.00"));

        // Add Independent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, new BigDecimal("105.00"));

        // Add Independent Breakfast product CPDecisionBAROutput record
        addCPDecisionBAROutput(independentBreakfastProduct, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100.00"), barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.00"), barDailybarOutput.getChildRate());

        // Verify the breakfast package results
        DecisionDailybarOutput breakfastDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("130.00"), breakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("172.00"), breakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("60.00"), breakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.00"), breakfastDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("131.25"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("45.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("140.75"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("190.94"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("65.00"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("40.00"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void fencedProductWithPackageRatePercent() {
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(10));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(5));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(15), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(10), independentProduct.getId());

        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED);

        // Add breakfastPackagedProduct CPDecisionBAROutput record
        addCPDecisionBAROutput(packagedProductWithPercent, RT_QUEEN, startDate, new BigDecimal("110.00"));

        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(105));

        // Add independentPackagedProductWithPercent CPDecisionBAROutput record
        addCPDecisionBAROutput(independentPackagedProductWithPercent, RT_QUEEN, startDate, new BigDecimal("115.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("10.00"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("5.00"), barDailybarOutput.getChildRate());

        // Verify the percent package result
        DecisionDailybarOutput packagePercentOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.00"), packagePercentOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120.00"), packagePercentOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("11.00"), packagePercentOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("5.25"), packagePercentOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("105.00"), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("15.00"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("10.00"), independentDailybarOutput.getChildRate());

        // Verify the independent breakfast package results
        DecisionDailybarOutput independentBreakfastDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("115.50"), independentBreakfastDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("126.00"), independentBreakfastDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("16.50"), independentBreakfastDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("10.50"), independentBreakfastDailybarOutput.getChildRate());
    }

    @Test
    public void productWitPriceExcludedRoomTypeAndFixedOffset() {
        //GIVEN
        //room type offsets
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(1.23));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(4.56));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(6.23), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(9.56), independentProduct.getId());

        //add bar decision output
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED);
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(105));

        //add product decision output
        addCPDecisionBAROutputPriceExcluded(unfencedNoPackageWithExtraAdultAndChild, RT_QUEEN, startDate, BigDecimal.ZERO, new BigDecimal("108.91"), null);
        addProductRateOffset(unfencedNoPackageWithExtraAdultAndChild, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(8.91));
        addCPDecisionBAROutputPriceExcluded(independentUnfencedNoPackageWithExtraAdultAndChild, RT_QUEEN, startDate, BigDecimal.ZERO, new BigDecimal("113.91"), null);
        addProductRateOffset(independentUnfencedNoPackageWithExtraAdultAndChild, AgileRatesOffsetMethod.FIXED, BigDecimal.valueOf(13.91));

        //add tax
        Tax tax = tenantCrudService().findAll(Tax.class).get(0);
        tax.setRoomTaxRate(BigDecimal.TEN);
        tenantCrudService().save(tax);

        //WHEN
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        //THEN
        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("1.23"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("4.56"), barDailybarOutput.getChildRate());

        // Verify the percent package result
        DecisionDailybarOutput productDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("108.91"), productDailybarOutput.getSingleRate());
        //8.91 (9.08 - tax) NOTE: we need to remove tax as price excluded values in Vw_Product_Rate_Offset
        assertBigDecimalEquals(new BigDecimal("108.91"), productDailybarOutput.getDoubleRate());
        //1.23 + 8.91 (9.08 - tax)
        assertBigDecimalEquals(new BigDecimal("10.14"), productDailybarOutput.getAdultRate());
        //4.56 + 8.91 (9.08 - tax)
        assertBigDecimalEquals(new BigDecimal("13.47"), productDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(BigDecimal.valueOf(105), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.valueOf(105), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("6.23"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("9.56"), independentDailybarOutput.getChildRate());

        // Verify the independent percent package result
        DecisionDailybarOutput independentProductDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("118.91"), independentProductDailybarOutput.getSingleRate());
        //13.91 (9.08 - tax) NOTE: we need to remove tax as price excluded values in Vw_Product_Rate_Offset
        assertBigDecimalEquals(new BigDecimal("118.91"), independentProductDailybarOutput.getDoubleRate());
        //6.23 + 13.91 (9.08 - tax)
        assertBigDecimalEquals(new BigDecimal("20.14"), independentProductDailybarOutput.getAdultRate());
        //9.56 + 13.91 (9.08 - tax)
        assertBigDecimalEquals(new BigDecimal("23.47"), independentProductDailybarOutput.getChildRate());
    }

    @Test
    public void productWithPriceExcludedRoomTypeAndPercentageOffset() {
        //GIVEN
        //room type offsets
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(1.23));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(4.56));
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(6.23), independentProduct.getId());
        addOffset(RT_QUEEN, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.valueOf(9.56), independentProduct.getId());

        //add bar decision output
        addCPDecisionBAROutput(barProduct, RT_QUEEN, startDate, BigDecimalUtil.ONE_HUNDRED);
        addCPDecisionBAROutput(independentProduct, RT_QUEEN, startDate, BigDecimal.valueOf(105));

        //add product decision output
        addCPDecisionBAROutputPriceExcluded(unfencedNoPackageWithExtraAdultAndChild, RT_QUEEN, startDate, BigDecimal.ZERO, new BigDecimal("105.00"), null);
        addProductRateOffset(unfencedNoPackageWithExtraAdultAndChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(5));
        addCPDecisionBAROutputPriceExcluded(independentUnfencedNoPackageWithExtraAdultAndChild, RT_QUEEN, startDate, BigDecimal.ZERO, new BigDecimal("110.00"), null);
        addProductRateOffset(independentUnfencedNoPackageWithExtraAdultAndChild, AgileRatesOffsetMethod.PERCENTAGE, BigDecimal.valueOf(10));

        //add tax
        Tax tax = tenantCrudService().findAll(Tax.class).get(0);
        tax.setRoomTaxRate(BigDecimal.valueOf(1000));
        tenantCrudService().save(tax);

        //WHEN
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);

        //THEN
        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify the BAR results
        DecisionDailybarOutput barDailybarOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimalUtil.ONE_HUNDRED, barDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("1.23"), barDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("4.56"), barDailybarOutput.getChildRate());

        // Verify the percent package result
        DecisionDailybarOutput productDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105.00"), productDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("105.00"), productDailybarOutput.getDoubleRate());
        //1.23 + 5% (.0651)
        assertBigDecimalEquals(new BigDecimal("1.29"), productDailybarOutput.getAdultRate());
        //4.56 + 5% (.228)
        assertBigDecimalEquals(new BigDecimal("4.79"), productDailybarOutput.getChildRate());

        // Verify the Independent results
        DecisionDailybarOutput independentDailybarOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(BigDecimal.valueOf(105), independentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.valueOf(105), independentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("6.23"), independentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("9.56"), independentDailybarOutput.getChildRate());

        // Verify the independent percent package result
        DecisionDailybarOutput independentProductDailybarOutput = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("115.50"), independentProductDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.50"), independentProductDailybarOutput.getDoubleRate());
        //6.23 + 10%
        assertBigDecimalEquals(new BigDecimal("6.85"), independentProductDailybarOutput.getAdultRate());
        //9.56 + 10%
        assertBigDecimalEquals(new BigDecimal("10.52"), independentProductDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsTax_ChildAgeBucketsEnabled_FreeNight() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();
        enableFreeNightProduct();

        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        java.time.LocalDate now = java.time.LocalDate.now();
        java.time.LocalDate nowPlusOne = now.plusDays(1);
        java.time.LocalDate nowPlusTwo = now.plusDays(2);

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(nowPlusOne), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(nowPlusTwo), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        addCPDecisionBAROutput(freeNightProduct, baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED);
        addCPDecisionBAROutput(freeNightProduct, baseRT, convertJavaToJodaLocalDate(nowPlusOne), BigDecimalUtil.ONE_HUNDRED);
        addCPDecisionBAROutput(freeNightProduct, baseRT, convertJavaToJodaLocalDate(nowPlusTwo), BigDecimal.valueOf(-100));

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(now), new BigDecimal("105"), new BigDecimal("110.25"), null);
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(nowPlusOne), new BigDecimal("105"), new BigDecimal("110.25"), null);
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(nowPlusTwo), new BigDecimal("105"), new BigDecimal("110.25"), null);

        addCPDecisionBAROutput(freeNightProduct, nonBaseRT, convertJavaToJodaLocalDate(now), new BigDecimal("105"));
        addCPDecisionBAROutput(freeNightProduct, nonBaseRT, convertJavaToJodaLocalDate(nowPlusOne), new BigDecimal("105"));
        addCPDecisionBAROutput(freeNightProduct, nonBaseRT, convertJavaToJodaLocalDate(nowPlusTwo), BigDecimal.valueOf(-100));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(nowPlusTwo));
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(12, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutputNow = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutputNow.getChildAgeThreeRate());

        DecisionDailybarOutput baseRTOutputNowPlusOne = decisionDailybarOutputs.get(4);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutputNowPlusOne.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutputNowPlusOne.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutputNowPlusOne.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutputNowPlusOne.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutputNowPlusOne.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutputNowPlusOne.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutputNowPlusOne.getChildAgeThreeRate());

        DecisionDailybarOutput baseRTOutputNowPlusTwo = decisionDailybarOutputs.get(8);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutputNowPlusTwo.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutputNowPlusTwo.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutputNowPlusTwo.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutputNowPlusTwo.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutputNowPlusTwo.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutputNowPlusTwo.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutputNowPlusTwo.getChildAgeThreeRate());

        // Verify the BaseRT values for FreeNight product
        DecisionDailybarOutput freeNightBaseRTOutputNow = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105"), freeNightBaseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), freeNightBaseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), freeNightBaseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), freeNightBaseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), freeNightBaseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), freeNightBaseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), freeNightBaseRTOutputNow.getChildAgeThreeRate());

        DecisionDailybarOutput freeNightBaseRTOutputNowPlusOne = decisionDailybarOutputs.get(5);
        assertBigDecimalEquals(new BigDecimal("105"), freeNightBaseRTOutputNowPlusOne.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), freeNightBaseRTOutputNowPlusOne.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), freeNightBaseRTOutputNowPlusOne.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), freeNightBaseRTOutputNowPlusOne.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), freeNightBaseRTOutputNowPlusOne.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), freeNightBaseRTOutputNowPlusOne.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), freeNightBaseRTOutputNowPlusOne.getChildAgeThreeRate());

        DecisionDailybarOutput freeNightBaseRTOutputNowPlusTwo = decisionDailybarOutputs.get(9);
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getChildAgeOneRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getChildAgeTwoRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightBaseRTOutputNowPlusTwo.getChildAgeThreeRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutputNow = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), nonBaseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), nonBaseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), nonBaseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), nonBaseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), nonBaseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), nonBaseRTOutputNow.getChildAgeThreeRate());

        DecisionDailybarOutput nonBaseRTOutputNowPlusOne = decisionDailybarOutputs.get(6);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutputNowPlusOne.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), nonBaseRTOutputNowPlusOne.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), nonBaseRTOutputNowPlusOne.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), nonBaseRTOutputNowPlusOne.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), nonBaseRTOutputNowPlusOne.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), nonBaseRTOutputNowPlusOne.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), nonBaseRTOutputNowPlusOne.getChildAgeThreeRate());

        DecisionDailybarOutput nonBaseRTOutputNowPlusTwo = decisionDailybarOutputs.get(10);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutputNowPlusTwo.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), nonBaseRTOutputNowPlusTwo.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), nonBaseRTOutputNowPlusTwo.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), nonBaseRTOutputNowPlusTwo.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), nonBaseRTOutputNowPlusTwo.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), nonBaseRTOutputNowPlusTwo.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), nonBaseRTOutputNowPlusTwo.getChildAgeThreeRate());

        // Verify the non-BaseRT values for FreeNightProduct
        DecisionDailybarOutput freeNightNonBaseRTOutputNow = decisionDailybarOutputs.get(3);
        assertBigDecimalEquals(new BigDecimal("110.25"), freeNightNonBaseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), freeNightNonBaseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), freeNightNonBaseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), freeNightNonBaseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), freeNightNonBaseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), freeNightNonBaseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), freeNightNonBaseRTOutputNow.getChildAgeThreeRate());

        DecisionDailybarOutput freeNightNonBaseRTOutputNowPlusOne = decisionDailybarOutputs.get(7);
        assertBigDecimalEquals(new BigDecimal("110.25"), freeNightNonBaseRTOutputNowPlusOne.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), freeNightNonBaseRTOutputNowPlusOne.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), freeNightNonBaseRTOutputNowPlusOne.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), freeNightNonBaseRTOutputNowPlusOne.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), freeNightNonBaseRTOutputNowPlusOne.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), freeNightNonBaseRTOutputNowPlusOne.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), freeNightNonBaseRTOutputNowPlusOne.getChildAgeThreeRate());

        DecisionDailybarOutput freeNightNonBaseRTOutputNowPlusTwo = decisionDailybarOutputs.get(11);
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getChildAgeOneRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getChildAgeTwoRate());
        assertBigDecimalEquals(BigDecimal.ZERO, freeNightNonBaseRTOutputNowPlusTwo.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsTax_ChildAgeBucketsEnabled_NonHiltonCRS() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.HILTON_OPTION_TO_UPDATE_VALUES_SENT_FOR_EXTRA_ADULT_EXTRA_CHILD)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED)).thenReturn(true);

        enableChildAgeBuckets();

        linkedFixedFencedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedFixedFencedProduct = tenantCrudService().save(linkedFixedFencedProduct);

        setTax(new BigDecimal("7.25"));

        String baseRT = "k";

        java.time.LocalDate now = java.time.LocalDate.now();

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal("5"));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);
        CPDecisionBAROutput linkedProductBaseRTCpDecisionBAROutput = addCPDecisionBAROutput(linkedFixedFencedProduct, baseRT, convertJavaToJodaLocalDate(now), BigDecimal.valueOf(110), BigDecimal.valueOf(115), null);
        linkedProductBaseRTCpDecisionBAROutput.setAdjustmentValue(BigDecimal.TEN);
        tenantCrudService().save(linkedProductBaseRTCpDecisionBAROutput);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutputNow = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutputNow.getChildAgeThreeRate());

        DecisionDailybarOutput linkedProductBaseRTOutputNow = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(BigDecimal.TEN, linkedProductBaseRTOutputNow.getSingleRate());
        assertBigDecimalEquals(BigDecimal.TEN, linkedProductBaseRTOutputNow.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.TEN, linkedProductBaseRTOutputNow.getAdultRate());
        assertBigDecimalEquals(BigDecimal.TEN, linkedProductBaseRTOutputNow.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15.65"), linkedProductBaseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("21.31"), linkedProductBaseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("26.95"), linkedProductBaseRTOutputNow.getChildAgeThreeRate());

        List<DecisionDailybarOutputNonHiltonCRS> nonHiltonCRSDecisionDailybarOutputs = findNonHiltonCRSDecisionDailybarOutputs();
        List<PaceDailyBarOutputNonHiltonCRS> paceDailyBarOutputNonHiltonCRS = findNonHiltonCRSPaceDailyBarOutput();
        assertEquals(1, nonHiltonCRSDecisionDailybarOutputs.size());
        assertEquals(1, paceDailyBarOutputNonHiltonCRS.size());

        DecisionDailybarOutputNonHiltonCRS decisionDailybarOutputNonHiltonCRS0 = nonHiltonCRSDecisionDailybarOutputs.get(0);
        assertEquals(linkedFixedFencedProduct, decisionDailybarOutputNonHiltonCRS0.getProduct());
        assertEquals(baseRT, decisionDailybarOutputNonHiltonCRS0.getAccomType().getAccomTypeCode());

        assertBigDecimalEquals(new BigDecimal("115"), decisionDailybarOutputNonHiltonCRS0.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.9"), decisionDailybarOutputNonHiltonCRS0.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("32.30"), decisionDailybarOutputNonHiltonCRS0.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("37.85"), decisionDailybarOutputNonHiltonCRS0.getChildRate());
        assertBigDecimalEquals(new BigDecimal("15.65"), linkedProductBaseRTOutputNow.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("21.31"), linkedProductBaseRTOutputNow.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("26.95"), linkedProductBaseRTOutputNow.getChildAgeThreeRate());
    }
}