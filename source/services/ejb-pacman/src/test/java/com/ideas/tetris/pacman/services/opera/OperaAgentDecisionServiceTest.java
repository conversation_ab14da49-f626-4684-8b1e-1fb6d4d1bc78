package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.integration.opera.agent.dto.*;
import com.ideas.g3.integration.opera.agent.task.*;
import com.ideas.g3.integration.opera.dto.*;
import com.ideas.g3.integration.opera.services.OperaDecisionServiceLocal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionContentToJsonConverter;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionDeliveredService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.lra.dto.LRARestrictionType;
import com.ideas.tetris.pacman.services.manualrestrictions.type.ManualRestrictionDecisionNames;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteTaskService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteTask;
import com.ideas.tetris.pacman.services.remoteAgent.entity.TaskParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaAgentDecisionServiceTest {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private static final Integer PROPERTY_ID = 913;
    private static final String EXTERNAL_SYSTEM = "opera";
    private static final BigDecimal OPPORTUNITY_COST = new BigDecimal("9.13");
    private static final BigDecimal DELTA = new BigDecimal("12.11");
    private static final BigDecimal CEILING = new BigDecimal("200.8");
    private static final BigDecimal MAX_SOLDS = new BigDecimal("526");
    private static final String RATE_CODE = "myRateCode";
    private static final String ROOM_TYPE = "myRoomType";
    private static final BigDecimal ONE_ADULT_RATE = new BigDecimal("427");
    private static final BigDecimal TWO_ADULT_RATE = new BigDecimal("724");
    private static final BigDecimal EXTRA_ADULT_RATE = new BigDecimal("319");
    private static final BigDecimal EXTRA_CHILD_RATE = new BigDecimal("625");
    private static final BigDecimal CHILD_ONE_RATE = new BigDecimal("725");
    private static final BigDecimal CHILD_TWO_RATE = new BigDecimal("825");
    private static final BigDecimal CHILD_THREE_RATE = new BigDecimal("925");
    private static final String FPLOS = "YNYNYNYN";
    private static final String ROOM_CLASS = "myRoomClass";
    private static final BigDecimal AUTHORIZED_CAPACITY = new BigDecimal("511");
    private static final BigDecimal EXPECTED_WALKS = new BigDecimal("4");
    private static final int OVERBOOKING = 12;
    private static final String DECISON_NAME = "Test Decision";
    private static Date OCCUPANCY_DATE;
    private static Date RATE_DATE;
    private static Date ARRIVAL_DATE;
    @Mock
    OperaDecisionServiceLocal operaDecisionService;
    @Mock
    AccommodationService accommodationService;
    @Mock
    RemoteTaskService remoteTaskService;
    @Mock
    PacmanConfigParamsService configParamsService;
    @Mock
    DecisionDeliveredService decisionDeliveredService;
    @Mock
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository;
    @InjectMocks
    OperaAgentDecisionService operaAgentDecisionService;

    @BeforeEach
    public void setUp() {
        try {
            OCCUPANCY_DATE = DateUtil.removeTimeFromDate(dateFormat.parse("2015-12-11"));
            RATE_DATE = DateUtil.removeTimeFromDate(dateFormat.parse("2015-09-13"));
            ARRIVAL_DATE = DateUtil.removeTimeFromDate(dateFormat.parse("2015-05-26"));
        } catch (ParseException e) {

        }
        MockitoAnnotations.initMocks(this);
        Mockito.when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(false);


        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(configParamsService);
        operaAgentDecisionService.setConfigParameterNameService(configParameterNameService);
    }

    @Test
    public void test_getLrvDecisions() {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.opera.includeCeilingDeltaMaxSold")).thenReturn(true);
        List<LRVDecision> results = createAndExecuteForLRVDecisions(EXTERNAL_SYSTEM);
        assertEquals(1, results.size());
        LRVDecision result = results.get(0);
        assertEquals(ROOM_CLASS, result.getRoomClass());
        assertEquals(OPPORTUNITY_COST, result.getOpportunityCost());
        assertEquals(DELTA, result.getDeltaValue());
        assertEquals(CEILING, result.getCeilingValue());
        assertEquals(MAX_SOLDS, result.getMaxSolds());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        Mockito.verify(operaDecisionService).getLastRoomValueDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void getLrvDecisionsShouldNotInvokeDecisionDeliveryService() {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.opera.includeCeilingDeltaMaxSold")).thenReturn(true);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Collections.emptyList());
        List<LRVDecision> results = createAndExecuteForLRVDecisions(EXTERNAL_SYSTEM);
        assertEquals(1, results.size());
        Mockito.verify(operaDecisionService).getLastRoomValueDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        verify(decisionDeliveredService, times(0)).saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void test_getLrvDecisionsShouldInvokeDecisionDeliveryService() {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.opera.includeCeilingDeltaMaxSold")).thenReturn(true);
        PropertyTask propertyTask = getPropertyTask(Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS, PropertyTaskType.UPLOAD_DECISIONS);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Arrays.asList(propertyTask));
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<LRVDecision> results = createAndExecuteForLRVDecisions(EXTERNAL_SYSTEM);
        assertEquals(1, results.size());
        Mockito.verify(operaDecisionService).getLastRoomValueDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), anyString(), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void shouldReturnDefaultValuesForOperaSystemForDeltaCeilingAndMaxSoldsWhenIncludeCeilingDeltaMaxSoldFalse() throws Exception {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.opera.includeCeilingDeltaMaxSold")).thenReturn(false);
        List<LRVDecision> results = createAndExecuteForLRVDecisions("opera");
        assertEquals(1, results.size());
        LRVDecision result = results.get(0);
        verifyDefaultValues(result);
    }

    @Test
    public void shouldReturnDefaultValuesForORSSystemForDeltaCeilingAndMaxSoldsWhenIncludeCeilingDeltaMaxSoldFalse() throws Exception {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.ORS.includeCeilingDeltaMaxSold")).thenReturn(false);
        List<LRVDecision> results = createAndExecuteForLRVDecisions("ORS");
        assertEquals(1, results.size());
        LRVDecision result = results.get(0);
        verifyDefaultValues(result);
    }

    @Test
    public void shouldReturnDefaultValuesForMYFIDELIOSystemForDeltaCeilingAndMaxSoldsWhenIncludeCeilingDeltaMaxSoldFalse() throws Exception {
        Mockito.when(configParamsService.getBooleanParameterValue("pacman.integration.MYFIDELIO.includeCeilingDeltaMaxSold")).thenReturn(false);
        List<LRVDecision> results = createAndExecuteForLRVDecisions("MYFIDELIO");
        assertEquals(1, results.size());
        LRVDecision result = results.get(0);
        verifyDefaultValues(result);
    }

    private void verifyDefaultValues(LRVDecision result) {
        assertEquals(BigDecimal.ZERO, result.getDeltaValue());
        assertEquals(BigDecimal.ZERO, result.getCeilingValue());
        assertEquals(BigDecimal.valueOf(9999), result.getMaxSolds());
    }

    private List<LRVDecision> createAndExecuteForLRVDecisions(String externalSystem) {
        List<LastRoomValueDecision> rawResults = new ArrayList<LastRoomValueDecision>();
        LastRoomValueDecision rawResult = new LastRoomValueDecision();
        rawResult.setInventoryCode(ROOM_CLASS);
        rawResult.setOpportunityCost(OPPORTUNITY_COST);
        rawResult.setDeltaValue(DELTA);
        rawResult.setCeilingValue(CEILING);
        rawResult.setMaxSolds(MAX_SOLDS);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLastRoomValueDecisions(PROPERTY_ID.toString(), externalSystem)).thenReturn(rawResults);
        return operaAgentDecisionService.getLRVDecisions(PROPERTY_ID, externalSystem, null);
    }

    @Test
    public void test_getDailyBarDecisions() {
        List<DailyBarDecision> rawResults = new ArrayList<DailyBarDecision>();
        DailyBarDecision rawResult = new DailyBarDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setRateDate(RATE_DATE);
        rawResult.setOneAdultRate(ONE_ADULT_RATE);
        rawResult.setTwoAdultRate(TWO_ADULT_RATE);
        rawResult.setExtraAdultRate(EXTRA_ADULT_RATE);
        rawResult.setExtraChildRate(EXTRA_CHILD_RATE);
        rawResult.setChildAgeOneRate(CHILD_ONE_RATE);
        rawResult.setChildAgeTwoRate(CHILD_TWO_RATE);
        rawResult.setChildAgeThreeRate(CHILD_THREE_RATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<DailyBARDecision> results = operaAgentDecisionService.getDailyBarDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        DailyBARDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(RATE_DATE, result.rateDateAsDate());
        assertEquals(ONE_ADULT_RATE, result.getOneAdultRate());
        assertEquals(TWO_ADULT_RATE, result.getTwoAdultRate());
        assertEquals(EXTRA_ADULT_RATE, result.getExtraAdultRate());
        assertEquals(EXTRA_CHILD_RATE, result.getExtraChildRate());
        assertEquals(CHILD_ONE_RATE, result.getChildAgeOneRate());
        assertEquals(CHILD_TWO_RATE, result.getChildAgeTwoRate());
        assertEquals(CHILD_THREE_RATE, result.getChildAgeThreeRate());
        Mockito.verify(operaDecisionService).getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void getDailyBarDecisionsShouldNotInvokeDecisionDeliveryService() {
        List<DailyBarDecision> rawResults = new ArrayList<DailyBarDecision>();
        DailyBarDecision rawResult = new DailyBarDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setRateDate(RATE_DATE);
        rawResult.setOneAdultRate(ONE_ADULT_RATE);
        rawResult.setTwoAdultRate(TWO_ADULT_RATE);
        rawResult.setExtraAdultRate(EXTRA_ADULT_RATE);
        rawResult.setExtraChildRate(EXTRA_CHILD_RATE);
        rawResult.setChildAgeOneRate(CHILD_ONE_RATE);
        rawResult.setChildAgeTwoRate(CHILD_TWO_RATE);
        rawResult.setChildAgeThreeRate(CHILD_THREE_RATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Collections.emptyList());
        List<DailyBARDecision> results = operaAgentDecisionService.getDailyBarDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        Mockito.verify(operaDecisionService).getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        verify(decisionDeliveredService, times(0)).saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void getDailyBarDecisionsShouldInvokeDecisionDeliveryService() {
        List<DailyBarDecision> rawResults = new ArrayList<DailyBarDecision>();
        DailyBarDecision rawResult = new DailyBarDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setRateDate(RATE_DATE);
        rawResult.setOneAdultRate(ONE_ADULT_RATE);
        rawResult.setTwoAdultRate(TWO_ADULT_RATE);
        rawResult.setExtraAdultRate(EXTRA_ADULT_RATE);
        rawResult.setExtraChildRate(EXTRA_CHILD_RATE);
        rawResult.setChildAgeOneRate(CHILD_ONE_RATE);
        rawResult.setChildAgeTwoRate(CHILD_TWO_RATE);
        rawResult.setChildAgeThreeRate(CHILD_THREE_RATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        PropertyTask propertyTask = getPropertyTask(Constants.OPERA_DAILYBAR, PropertyTaskType.UPLOAD_DECISIONS);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Arrays.asList(propertyTask));
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<DailyBARDecision> results = operaAgentDecisionService.getDailyBarDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        Mockito.verify(operaDecisionService).getDailyBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), anyString(), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void test_getLRAControlFPLOSByRateCodeDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getLRAControlFPLOSByRateCodeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE);
    }

    @Test
    public void getLRAControlFPLOSByRateCodeDecisionsShouldNotInvokeDecisionDeliveryService() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE)).thenReturn(rawResults);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Collections.emptyList());
        List<FplosDecision> results = operaAgentDecisionService.getLRAControlFPLOSByRateCodeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE);
        verify(decisionDeliveredService, times(0)).saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void getLRAControlFPLOSByRateCodeDecisionsShouldInvokeDecisionDeliveryService() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE)).thenReturn(rawResults);
        PropertyTask propertyTask = getPropertyTask(Constants.LRA_CONTROL_FPLOS_BY_RATE_CODE, PropertyTaskType.UPLOAD_DECISIONS);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Arrays.asList(propertyTask));
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<FplosDecision> results = operaAgentDecisionService.getLRAControlFPLOSByRateCodeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CODE);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), eq(Constants.LRA_CONTROL_FPLOS_BY_RATE_CODE), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void test_getLRAControlFPLOSByRateCategoryDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CATEGORY)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getLRAControlFPLOSByRateCategoryDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAFPLOSDecisionWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_FPLOS_BY_RATE_CATEGORY);
    }

    @Test
    public void test_getLRAControlMinLOSByRateCodeDecisions() {
        List<OperaMinLOSDecision> rawResults = new ArrayList<>();
        OperaMinLOSDecision rawResult = new OperaMinLOSDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAMinlosDecisionsWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_MINLOS_BY_RATE_CODE)).thenReturn(rawResults);
        List<MinLOSDecision> results = operaAgentDecisionService.getLRAControlMinLOSByRateCodeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        MinLOSDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAMinlosDecisionsWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_MINLOS_BY_RATE_CODE);
    }

    @Test
    public void test_getLRAControlMinLOSByRateCategoryDecisions() {
        List<OperaMinLOSDecision> rawResults = new ArrayList<>();
        OperaMinLOSDecision rawResult = new OperaMinLOSDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getLRAMinlosDecisionsWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_MINLOS_BY_RATE_CATEGORY)).thenReturn(rawResults);
        List<MinLOSDecision> results = operaAgentDecisionService.getLRAControlMinLOSByRateCategoryDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        MinLOSDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getLRAMinlosDecisionsWithExternalSystem(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, LRA_CONTROL_MINLOS_BY_RATE_CATEGORY);
    }

    @Test
    public void test_getBarByLOSDecisions() {
        List<BarByLOSDecision> rawResults = new ArrayList<BarByLOSDecision>();
        BarByLOSDecision rawResult = new BarByLOSDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRateDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarByLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<BarByLosDecision> results = operaAgentDecisionService.getBarByLOSDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        BarByLosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ARRIVAL_DATE, result.rateDateAsDate());
        Mockito.verify(operaDecisionService).getBarByLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_getDBARDecisions() {
        List<BarByLOSDecision> rawResults = new ArrayList<BarByLOSDecision>();
        BarByLOSDecision rawResult = new BarByLOSDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRateDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getDBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<BarByLosDecision> results = operaAgentDecisionService.getDBARDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        BarByLosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ARRIVAL_DATE, result.rateDateAsDate());
        Mockito.verify(operaDecisionService).getDBarDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    void testFailMaxDecisionIdStatus() {
        when(decisionUploadDateToExternalSystemRepository.findMaxDecisionIdByDecisionNameExternalNameNoLastUpdate(DECISON_NAME, EXTERNAL_SYSTEM)).thenReturn(1);
        operaAgentDecisionService.resetStatusAndModifyTimeForDecisionUpload(DECISON_NAME, EXTERNAL_SYSTEM);
        verify(decisionUploadDateToExternalSystemRepository).findMaxDecisionIdByDecisionNameExternalNameNoLastUpdate(DECISON_NAME, EXTERNAL_SYSTEM);
        verify(decisionUploadDateToExternalSystemRepository).failStatusForMaxDecisionId(1, DECISON_NAME, EXTERNAL_SYSTEM);
    }

    @Test
    public void test_getMinLOSByRateCategoryDecisions() {
        AccomClass masterClass = new AccomClass();
        AccomType accomType1 = new AccomType();
        accomType1.setAccomTypeCapacity(100);
        accomType1.setAccomTypeCode(ROOM_TYPE);
        masterClass.addAccomType(accomType1);
        Mockito.when(accommodationService.findMasterClass(PROPERTY_ID)).thenReturn(masterClass);
        List<OperaMinLOSDecision> rawResults = new ArrayList<OperaMinLOSDecision>();
        OperaMinLOSDecision rawResult = new OperaMinLOSDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getMinLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, Constants.OPERA_MINLOS_BY_RATE_CATEGORY)).thenReturn(rawResults);
        List<MinLOSDecision> results = operaAgentDecisionService.getMinLOSByRateCategoryDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        MinLOSDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        Mockito.verify(operaDecisionService).getMinLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, Constants.OPERA_MINLOS_BY_RATE_CATEGORY);
    }

    @Test
    public void test_getBarFplosByHotelDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByHotelDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByHotelDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        Mockito.verify(operaDecisionService).getBarFplosByHotelDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void getBarFplosByHierarchyDecisionsShouldNotInvokeDecisionDeliveryService() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByHierarchyDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByHierarchyDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        Mockito.verify(operaDecisionService).getBarFplosByHierarchyDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void getBarFplosByHierarchyDecisionsShouldInvokeDecisionDeliveryService() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByHierarchyDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        PropertyTask propertyTask = getPropertyTask(Constants.OPERA_BAR_FPLOS_BY_HIERARCHY, PropertyTaskType.UPLOAD_DECISIONS);
        when(remoteTaskService.getPropertyRemoteTasks(anySet(), anySet())).thenReturn(Arrays.asList(propertyTask));
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByHierarchyDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        Mockito.verify(operaDecisionService).getBarFplosByHierarchyDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), eq(Constants.OPERA_BAR_FPLOS_BY_HIERARCHY), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    private PropertyTask getPropertyTask(String decisionType, PropertyTaskType propertyTaskType) {
        PropertyTask propertyTask = new PropertyTask(propertyTaskType);
        TaskParameterDto taskParameterDto = new TaskParameterDto(ParameterType.DECISION_TYPE, decisionType);
        propertyTask.addParameter(taskParameterDto);
        return propertyTask;
    }

    @Test
    public void test_getBarFplosByHierarchyByRoomClassDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResult.setRoomClass(ROOM_CLASS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByHierarchyByRoomClassDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByHierarchyByRoomClassDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        assertEquals(ROOM_CLASS, result.getRoomClass());
        Mockito.verify(operaDecisionService).getBarFplosByHierarchyByRoomClassDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_getBarFplosDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByRoomTypeDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByRoomTypeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        Mockito.verify(operaDecisionService).getBarFplosByRoomTypeDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_getBarFplosRoomClassDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult = new OperaFplosQualifiedDecision();
        rawResult.setRateCode(RATE_CODE);
        rawResult.setRoomType(ROOM_TYPE);
        rawResult.setArrivalDate(ARRIVAL_DATE);
        rawResult.setFplos(FPLOS);
        rawResult.setRoomClass(ROOM_CLASS);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getBarFplosByRoomClassDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<FplosDecision> results = operaAgentDecisionService.getBarFplosByRoomClassDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals(ROOM_TYPE, result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        assertEquals(ROOM_CLASS, result.getRoomClass());
        Mockito.verify(operaDecisionService).getBarFplosByRoomClassDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasBarFplosByRoomClassDecisions() {
        operaAgentDecisionService.hasBarFplosByRoomClassDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarFplosByRoomClassDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasBarFplosByHierarchyByRoomClassDecisions() {
        operaAgentDecisionService.hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarFplosByHierarchyByRoomClassDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasBarFplosByHotelDecisions() {
        operaAgentDecisionService.hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarFplosByHotelDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasHotelOverbookingDecisions() {
        operaAgentDecisionService.hasHotelOverbookingDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasHotelOverbookingDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasDailyBarDecisions() {
        operaAgentDecisionService.hasDailyBarDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasDailyBarDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasBarFplosByHierarchyDecisions() {
        operaAgentDecisionService.hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarFplosByHierarchyDecisions(EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasBarFplosByRoomTypeDecisions() {
        operaAgentDecisionService.hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarFplosByRoomTypeDecisions(EXTERNAL_SYSTEM);
    }


    @Test
    public void test_hasBarByLOSDecisions() {
        operaAgentDecisionService.hasBarByLOSDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasBarByLOSDecisions(EXTERNAL_SYSTEM);
    }


    @Test
    public void test_hasDBarDecisions() {
        operaAgentDecisionService.hasDBarDecisions(EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasDBarDecisions(EXTERNAL_SYSTEM);
    }


    @Test
    public void test_hasLRAMinLOSByRateCodeDecisions() {
        operaAgentDecisionService.hasLRAMinLOSDecisions(EXTERNAL_SYSTEM, LRARestrictionType.RATE_CODE_LEVEL);
        Mockito.verify(operaDecisionService).hasLRAMinlosDecisions(EXTERNAL_SYSTEM, LRARestrictionType.RATE_CODE_LEVEL.name());
    }


    @Test
    public void test_hasLRAFplosByRateCodeDecision() {
        operaAgentDecisionService.hasLRAFplosDecision(EXTERNAL_SYSTEM, LRARestrictionType.RATE_CODE_LEVEL);
        Mockito.verify(operaDecisionService).hasLRAFplosDecision(EXTERNAL_SYSTEM, LRARestrictionType.RATE_CODE_LEVEL.name());
    }

    @Test
    public void test_hasAccomOverbookingDecisions() {
        operaAgentDecisionService.hasAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasLRVDecisions() {
        operaAgentDecisionService.hasLRVDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasLRVDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasMinLOSDecisions() {
        operaAgentDecisionService.hasMinLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasMinLOSDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_hasFplosQualifiedDecisions() {
        operaAgentDecisionService.hasFplosQualifiedDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(operaDecisionService).hasFplosQualifiedDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }


    @Test
    public void test_getFplosByRateCodeDecisions() {
        List<OperaFplosQualifiedDecision> rawResults = new ArrayList<OperaFplosQualifiedDecision>();
        OperaFplosQualifiedDecision rawResult1 = new OperaFplosQualifiedDecision();
        rawResult1.setRateCode(RATE_CODE);
        rawResult1.setRoomType("AT1");
        rawResult1.setArrivalDate(ARRIVAL_DATE);
        rawResult1.setFplos(FPLOS);
        OperaFplosQualifiedDecision rawResult2 = new OperaFplosQualifiedDecision();
        rawResult2.setRateCode(RATE_CODE);
        rawResult2.setRoomType("AT2");
        rawResult2.setArrivalDate(ARRIVAL_DATE);
        rawResult2.setFplos(FPLOS);
        OperaFplosQualifiedDecision rawResult3 = new OperaFplosQualifiedDecision();
        rawResult3.setRateCode(RATE_CODE);
        rawResult3.setRoomType(ROOM_TYPE);
        rawResult3.setArrivalDate(ARRIVAL_DATE);
        rawResult3.setFplos(FPLOS);
        rawResults.add(rawResult1);
        rawResults.add(rawResult2);
        rawResults.add(rawResult3);
        Mockito.when(operaDecisionService.getFplosQualifiedDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, Constants.OPERA_FPLOS_BY_RATE_CODE)).thenReturn(rawResults);
        AccomClass masterClass = new AccomClass();
        AccomType accomType1 = new AccomType();
        accomType1.setAccomTypeCapacity(0);
        accomType1.setAccomTypeCode("AT1");
        AccomType accomType2 = new AccomType();
        accomType2.setAccomTypeCapacity(10);
        accomType2.setAccomTypeCode("AT2");
        AccomType accomType3 = new AccomType();
        accomType3.setAccomTypeCapacity(0);
        accomType3.setAccomTypeCode("AT3");
        masterClass.addAccomType(accomType1);
        masterClass.addAccomType(accomType2);
        masterClass.addAccomType(accomType3);
        Mockito.when(accommodationService.findMasterClass(PROPERTY_ID)).thenReturn(masterClass);
        List<FplosDecision> results = operaAgentDecisionService.getFplosByRateCodeDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        FplosDecision result = results.get(0);
        assertEquals(RATE_CODE, result.getRateCode());
        assertEquals("AT2", result.getRoomType());
        assertEquals(ARRIVAL_DATE, result.arrivalDateAsDate());
        assertEquals(FPLOS, result.getFplos());
        Mockito.verify(operaDecisionService).getFplosQualifiedDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM, Constants.OPERA_FPLOS_BY_RATE_CODE);
    }

    @Test
    public void test_getHotelOverbookingDecisions() {
        List<HotelOverbookingDecision> rawResults = new ArrayList<HotelOverbookingDecision>();
        HotelOverbookingDecision rawResult = new HotelOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setExpectedWalks(EXPECTED_WALKS);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        Mockito.when(operaDecisionService.getHotelOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<TotalHotelOverbookingDecision> results = operaAgentDecisionService.getHotelOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        TotalHotelOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(EXPECTED_WALKS, result.getExpectedWalks());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking());
        Mockito.verify(operaDecisionService).getHotelOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_getAccomTypeOverbookingDecisions() {
        List<AccomOverbookingDecision> rawResults = new ArrayList<AccomOverbookingDecision>();
        AccomOverbookingDecision rawResult = new AccomOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        RemoteTask task = new RemoteTask();
        TaskParameter parameter = new TaskParameter();
        parameter.setParameterType(ParameterType.DECISION_TYPE.toString());
        parameter.setValue(Constants.OPERA_ROOM_TYPE_OVERBOOKING);
        task.addParameter(parameter);
        List<RemoteTask> tasks = Arrays.asList(task);
        Mockito.when(remoteTaskService.findRemoteTasks(Mockito.any())).thenReturn(tasks);
        Mockito.when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<AccomTypeOverbookingDecision> results = operaAgentDecisionService.getAccomTypeOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        AccomTypeOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking().longValue());
        Mockito.verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_attachDecisionsToTask() {
        List<AccomOverbookingDecision> rawResults = new ArrayList<AccomOverbookingDecision>();
        AccomOverbookingDecision rawResult = new AccomOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        RemoteTask task = new RemoteTask();
        TaskParameter parameter = new TaskParameter();
        parameter.setParameterType(ParameterType.DECISION_TYPE.toString());
        parameter.setValue(Constants.OPERA_ROOM_TYPE_OVERBOOKING);
        task.addParameter(parameter);
        List<RemoteTask> tasks = Arrays.asList(task);
        Mockito.when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(true);
        Mockito.when(remoteTaskService.findRemoteTasks(Mockito.any())).thenReturn(tasks);
        Mockito.when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<AccomTypeOverbookingDecision> results = operaAgentDecisionService.getAccomTypeOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        AccomTypeOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking().longValue());
        Mockito.verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
    }

    @Test
    public void test_attachDecisionsToTask_tenant() {
        List<AccomOverbookingDecision> rawResults = new ArrayList<AccomOverbookingDecision>();
        AccomOverbookingDecision rawResult = new AccomOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        PropertyTask task = new PropertyTask();
        TaskParameterDto parameter = new TaskParameterDto();
        parameter.setType(ParameterType.DECISION_TYPE);
        parameter.setValue(Constants.OPERA_ROOM_TYPE_OVERBOOKING);
        task.addParameter(parameter);
        Mockito.when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(true);
        Set<String> types = Stream.of(PropertyTaskType.UPLOAD_DECISIONS.toString()).collect(Collectors.toSet());
        Set<String> statuses = Stream.of(RemoteTaskStatus.IN_PROGRESS.toString()).collect(Collectors.toSet());
        Mockito.when(remoteTaskService.getPropertyRemoteTasks(types, statuses)).thenReturn(Arrays.asList(task));
        Mockito.when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<AccomTypeOverbookingDecision> results = operaAgentDecisionService.getAccomTypeOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        AccomTypeOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking().longValue());
        Mockito.verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(remoteTaskService).savePropertyRemoteTask(task);
        assertNotNull(task.getNotes());
        Mockito.verify(remoteTaskService).getPropertyRemoteTasks(types, statuses);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), anyString(), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void test_attachDecisionsToTask_tenantWhenSaveUploadedDecisionEnabledIsTrue() {
        List<AccomOverbookingDecision> rawResults = new ArrayList<AccomOverbookingDecision>();
        AccomOverbookingDecision rawResult = new AccomOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        PropertyTask task = new PropertyTask();
        TaskParameterDto parameter = new TaskParameterDto();
        parameter.setType(ParameterType.DECISION_TYPE);
        parameter.setValue(Constants.OPERA_ROOM_TYPE_OVERBOOKING);
        task.addParameter(parameter);
        Mockito.when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS)).thenReturn(true);
        Set<String> types = Stream.of(PropertyTaskType.UPLOAD_DECISIONS.toString()).collect(Collectors.toSet());
        Set<String> statuses = Stream.of(RemoteTaskStatus.IN_PROGRESS.toString()).collect(Collectors.toSet());
        Mockito.when(remoteTaskService.getPropertyRemoteTasks(types, statuses)).thenReturn(Arrays.asList(task));
        Mockito.when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), anyLong(), any(DecisionContentToJsonConverter.class))).thenReturn(true);
        List<AccomTypeOverbookingDecision> results = operaAgentDecisionService.getAccomTypeOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        AccomTypeOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking().longValue());
        Mockito.verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(remoteTaskService).savePropertyRemoteTask(task);
        assertNotNull(task.getNotes());
        Mockito.verify(remoteTaskService).getPropertyRemoteTasks(types, statuses);
        verify(decisionDeliveredService, times(1)).saveDecisionDelivered(anyList(), anyString(), nullable(Long.class), any(DecisionContentToJsonConverter.class));
    }

    @Test
    public void test_attachDecisionsToTask_disabled() {
        List<AccomOverbookingDecision> rawResults = new ArrayList<AccomOverbookingDecision>();
        AccomOverbookingDecision rawResult = new AccomOverbookingDecision();
        rawResult.setAuthorizedCapacity(AUTHORIZED_CAPACITY);
        rawResult.setOccupancyDate(OCCUPANCY_DATE);
        rawResult.setOverbooking(OVERBOOKING);
        rawResults.add(rawResult);
        RemoteTask task = new RemoteTask();
        TaskParameter parameter = new TaskParameter();
        parameter.setParameterType(ParameterType.DECISION_TYPE.toString());
        parameter.setValue(Constants.OPERA_ROOM_TYPE_OVERBOOKING);
        task.addParameter(parameter);
        List<RemoteTask> tasks = Arrays.asList(task);
        Mockito.when(remoteTaskService.findRemoteTasks(Mockito.any())).thenReturn(tasks);
        Mockito.when(operaDecisionService.getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM)).thenReturn(rawResults);
        List<AccomTypeOverbookingDecision> results = operaAgentDecisionService.getAccomTypeOverbookingDecisions(PROPERTY_ID, EXTERNAL_SYSTEM, null);
        assertEquals(1, results.size());
        AccomTypeOverbookingDecision result = results.get(0);
        assertEquals(AUTHORIZED_CAPACITY, result.getAuthorizedCapacity());
        assertEquals(OCCUPANCY_DATE, result.occupancyDateAsDate());
        assertEquals(OVERBOOKING, result.getOverbooking().longValue());
        Mockito.verify(operaDecisionService).getAccomOverbookingDecisions(PROPERTY_ID.toString(), EXTERNAL_SYSTEM);
        Mockito.verify(remoteTaskService, times(1)).getPropertyRemoteTasks(anySet(), anySet());
    }

    @Test
    public void getPropertyManualRestrictionDecisions() {
        String occupancyDate = "2020-08-20";
        List<PropertyManualRestrictionDecisionForOpera> commonOperaDecisions = Collections.singletonList(dummyPropertyLevelManualRestrictionDecision(occupancyDate));
        when(operaDecisionService.getPropertyLevelManualRestrictions("5", "opera", ManualRestrictionDecisionNames.PROPERTY_MAX_LOS_RESTRICTION))
                .thenReturn(commonOperaDecisions);

        List<PropertyManualRestrictionDecision> agentDecisions =
                operaAgentDecisionService.getPropertyManualRestrictionDecisions(5, "opera", ManualRestrictionDecisionNames.PROPERTY_MAX_LOS_RESTRICTION);

        assertEquals(1, agentDecisions.size());
        assertEquals(occupancyDate, agentDecisions.get(0).getDate());
        assertEquals("A_MINLOS", agentDecisions.get(0).getRestrictionCode());
        assertEquals(BigDecimal.valueOf(3), agentDecisions.get(0).getValue());
        assertFalse(agentDecisions.get(0).isClearsPreviousRestriction());
    }

    @Test
    public void getAccomTypeManualRestrictionDecisions() {
        String occupancyDate = "2020-08-20";
        String accomType = "RM_TYPE";
        List<AccomTypeManualRestrictionDecisionForOpera> commonOperaDecisions = Collections.singletonList(dummyAccomTypeLevelManualRestrictionDecision(occupancyDate, accomType));
        when(operaDecisionService.getRoomTypeLevelManualRestrictions("5", "opera", ManualRestrictionDecisionNames.ROOM_TYPE_MIN_LOS_RESTRICTION))
                .thenReturn(commonOperaDecisions);

        List<AccomTypeManualRestrictionDecision> agentDecisions =
                operaAgentDecisionService.getAccomTypeManualRestrictionDecisions(5, "opera", ManualRestrictionDecisionNames.ROOM_TYPE_MIN_LOS_RESTRICTION);

        assertEquals(1, agentDecisions.size());
        assertEquals(occupancyDate, agentDecisions.get(0).getDate());
        assertEquals(accomType, agentDecisions.get(0).getAccomType());
        assertEquals("A_MINLOS", agentDecisions.get(0).getRestrictionCode());
        assertEquals(BigDecimal.valueOf(3), agentDecisions.get(0).getValue());
        assertFalse(agentDecisions.get(0).isClearsPreviousRestriction());
    }

    private AccomTypeManualRestrictionDecisionForOpera dummyAccomTypeLevelManualRestrictionDecision(String date, String accomType) {

        AccomTypeManualRestrictionDecisionForOpera decision = new AccomTypeManualRestrictionDecisionForOpera();
        decision.setDate(LocalDate.parse(date).toDate());
        decision.setAccomType(accomType);
        decision.setRestrictionCode("A_MINLOS");
        decision.setValue(3);
        decision.setClearsPreviousRestriction(false);
        return decision;
    }

    private PropertyManualRestrictionDecisionForOpera dummyPropertyLevelManualRestrictionDecision(String date) {

        PropertyManualRestrictionDecisionForOpera decision = new PropertyManualRestrictionDecisionForOpera();
        decision.setDate(LocalDate.parse(date).toDate());
        decision.setRestrictionCode("A_MINLOS");
        decision.setValue(3);
        decision.setClearsPreviousRestriction(false);
        return decision;
    }
}
