package com.ideas.tetris.pacman.services.roa.forecastparams.service;

/**
 * <AUTHOR>
 **/

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.roa.forecastparams.entity.RuntimeParam;
import com.ideas.tetris.pacman.services.roa.forecastparams.entity.RuntimeParamOverride;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.DowGroup;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.HorizonGroup;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.LosGroup;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.ProcessGroupConfig;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.SeasonGroup;
import com.ideas.tetris.pacman.services.sas.entity.ProcessGroup;
import com.ideas.tetris.pacman.services.sas.entity.ProcessGroupScope;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.ACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ROARuntimeParamServiceTest extends AbstractG3JupiterTest {

    private static final String OUTLIER_DETECT = "OUTLIER_DETECT";
    private static final String MAX_ELAS_PARAM_NAME = "MAX_ELAS";
    private static final String TS_CLASS_ID = "TS_CLASS_ID";
    private static final String MIN_ELAS_PARAM_NAME = "MIN_ELAS";
    public static final String GET_OVVERIDE = "select * from IP_Cfg_Runtime_Param_Override override where override.IP_Cfg_process_Group_Id = :processGroupId and override.IP_Cfg_fcst_Task_Id = :fcstTaskId  and override.IP_Cfg_runtime_Param_Id = :runtimeParamId and override.IP_Cfg_process_Group_Type_Id = :processGroupTypeId";
    ROARuntimeParamService service;
    @Mock
    CrudService tenantCrudService;
    private final int UNQUALIFIED_FORECAST_TYPE_ID = 1;
    private final int QUALIFIED_FORECAST_TYPE_ID = 2;
    private final int QUALIFIED_YIELDABLE_TYPE_ID = 2;
    private final int GROUP_FORECAST_TYPE_ID = 6;
    private final int QUALIFIED_LINKED_TYPE_ID = 9;
    private final int MIN_ELAS = 19;
    private final int MAX_ELAS = 20;
    private final int FCST_METHOD = 32;

    private final int BASE_COMP_WEIGHT = 37;
    private final int IGNORE_MISSING = 81;


    @BeforeEach
    public void setUp() {
        service = new ROARuntimeParamService();
        service.setTenantCrudService(tenantCrudService());
    }

    @Test
    public void testSave() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 1, "ARIMAX");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getRuntimeParamId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals("ARIMAX", overrides.get(0).getValue());
    }

    @Test
    public void testRuntimeParamIdPresent() {
        boolean isRuntimeIdPresent = service.isRunTimeParamIdPresent(1);
        assertFalse(isRuntimeIdPresent);
    }
    @Test
    public void testPresenceOfRuntimeParamIds() {
        List<String> paramIds = new ArrayList<>();
        paramIds.add("FORCE_BKCRV_TYPE");
        paramIds.add("BKCRV_MIN_SIZE_CUTOFF");
        paramIds.add("BKCRV_MIN_NUM_VALID_PACE");
        paramIds.add("BKCRV_MIN_SIZE_CUTOFF");
        assertNotNull(service.getRunTimeParamIdPresentForGivenParams(paramIds));
    }

   @Test
    public void testGetFcstTaskIdByForecastTypeIds() {
       service.setTenantCrudService(tenantCrudService);
       when(tenantCrudService.findByNamedQuery(ProcessGroupScope.FIND_DISTINCT_FCST_TASK_ID,
               QueryParameter.with("fcstTypeIds", List.of(1,2,3)).parameters())).thenReturn(Arrays.asList(1));
       Object qualifiedFcstTaskId = service.getFcstTaskIdByForecastTypeIds(List.of(1,2,3));
       assertEquals(1, qualifiedFcstTaskId);
    }
    @Test
    public void testGetProcessGroupIdByForecastTypeIds() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuery(ProcessGroupScope.FIND_DISTINCT_PROCESS_GROUP_ID,
                QueryParameter.with("fcstTypeIds", List.of(1,2,3)).parameters())).thenReturn(Arrays.asList(1));
        Object qualifiedProcessGroupId = service.getProcessGroupIdByForecastTypeIds(List.of(1,2,3));
        assertEquals(1, qualifiedProcessGroupId);
    }

    @Test
    public void testShouldDoInvokeTrue() {
        service.setTenantCrudService(tenantCrudService);
        List<Object> paramList = new ArrayList<>();
        paramList.add(1);
        when(tenantCrudService.findByNativeQuery(anyString(), anyMap())).thenReturn(paramList);
        when(tenantCrudService.findByNativeQuery(anyString(), QueryParameter.with("id", anyInt()).parameters())).thenReturn(Arrays.asList(1));
        assertTrue(service.shouldDoInvoke());
    }
    @Test
    public void testShouldDoInvokeFalse() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuery(anyString(), QueryParameter.with("paramNames", anyList()).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNativeQuery(anyString(), QueryParameter.with("id", anyInt()).parameters())).thenReturn(Arrays.asList(1));
        assertFalse(service.shouldDoInvoke());
    }

    @Test
    public void testProcessAndSaveOverride() {
        service.setTenantCrudService(tenantCrudService);

        when(tenantCrudService.findByNamedQuery(eq(ProcessGroupScope.FIND_DISTINCT_PROCESS_GROUP_ID), anyMap())).thenReturn(Arrays.asList(1));
        when(tenantCrudService.findByNamedQuery(eq(ProcessGroupScope.FIND_DISTINCT_FCST_TASK_ID), anyMap())).thenReturn(Arrays.asList(2));
        when(tenantCrudService.findByNativeQuerySingleResult(anyString(), nullable(Map.class), any(RowMapper.class))).thenReturn(1);

        Object abc = service.processAndSaveOverride();
    }

    @Test
    public void testSaveWithExisting() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        RuntimeParamOverride override = new RuntimeParamOverride();
        override.setProcessGroupId(pg2.getId());
        override.setPropertyId(new Integer(TestProperty.H1.getId()));
        override.setFcstTaskId(new Integer(2));
        override.setRuntimeParamId(new Integer(1));
        override.setProcessGroupTypeId(new Integer(1));
        override.setValue("ARIMAX");
        override.setStatusId(new Integer(1));
        override.setModifiedDTTM(new Date());
        override.setUser_Id(11403);

        tenantCrudService().save(override);

        service.save(1, 2, 1, "ARIMAX_2");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getRuntimeParamId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals("ARIMAX_2", overrides.get(0).getValue());
    }

    @Test
    public void testGetForecastParameters() {
        Integer forecastTaskId = new Integer(9);

        ArrayList<RuntimeParam> listForecastParameter = (ArrayList<RuntimeParam>) service
                .getForecastParameters(forecastTaskId);
        RuntimeParam runtimeParam = listForecastParameter.get(0);

        assertEquals("ADJ_COMP_CLOSE", runtimeParam.getParamName());
        assertEquals("1", runtimeParam.getDefaultValue());
        assertEquals(5, listForecastParameter.size());
    }

    @Test
    public void testGetForecastParametersHonoursNewlyAddedParams() {
        Integer forecastTaskId = new Integer(9);
        String newParam = "NEW_PARAM";
        tenantCrudService().executeUpdateByNativeQuery(String.format("insert into IP_Cfg_Runtime_Param_Name_List values('%s', 'Parameter Description', 0, '0 or 1', 1, getdate())", newParam));
        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Task_Runtime_Param_Map values(1, (select top 1 Ip_Cfg_Runtime_Param_ID from IP_Cfg_Runtime_Param_Name_List where Param_Name='NEW_PARAM'), 9, 1, getdate(), 1)");

        ArrayList<RuntimeParam> listForecastParameter = (ArrayList<RuntimeParam>) service
                .getForecastParameters(forecastTaskId);
        RuntimeParam runtimeParam = listForecastParameter.get(0);

        assertEquals("ADJ_COMP_CLOSE", runtimeParam.getParamName());
        assertEquals("1", runtimeParam.getDefaultValue());
        assertTrue(listForecastParameter.stream().anyMatch(param -> param.getParamName().equalsIgnoreCase(newParam)));
        assertEquals(6, listForecastParameter.size());
    }

    @Test
    public void testGetProcessGroupTypeId() {
        Integer processGroupId = new Integer(4);

        Integer processGroupTypeId = service.getProcessGroupTypeId(processGroupId);

        assertEquals(Integer.valueOf(-1), processGroupTypeId);
    }

    @Test
    public void testgetParameterValue_Default() {
        Integer processGroupId = new Integer(4);
        Integer fcstTaskId = new Integer(4);
        Integer runtimeParamId = new Integer(1);

        String paramValue = service.getParameterValue(processGroupId, fcstTaskId, runtimeParamId);
        assertEquals("ARIMAX", paramValue);
    }

    @Test
    public void testSaveValidation_FCST_HISTORY_LENGTH_VALUE_success() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 23, "1");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(23), overrides.get(0).getRuntimeParamId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals("1", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_FCST_HISTORY_LENGTH_VALUE_fail() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 23, "0");
        });
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXPCT_success_1() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 44, "0");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(44), overrides.get(0).getRuntimeParamId());
        assertEquals("0", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXPCT_success_2() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 44, "24");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(44), overrides.get(0).getRuntimeParamId());
        assertEquals("24", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXPCT_fail() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 44, "1110");
        });
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_success_0() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 5, "0");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(5), overrides.get(0).getRuntimeParamId());
        assertEquals("0", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_success_3() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 5, "3");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(5), overrides.get(0).getRuntimeParamId());
        assertEquals("3", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_fail_1() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 5, "1.45");
        });
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_fail_2() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 5, "5");
        });
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXNUM_success_1() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 45, "0");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(45), overrides.get(0).getRuntimeParamId());
        assertEquals("0", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXNUM_success_2() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 45, "2514");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(45), overrides.get(0).getRuntimeParamId());
        assertEquals("2514", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXNUM_fail_1() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 45, "35000");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(45), overrides.get(0).getRuntimeParamId());
            assertEquals("35000", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_OUTLIER_DETECT_MAXNUM_fail_2() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 45, "54.23");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(45), overrides.get(0).getRuntimeParamId());
            assertEquals("54.23", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_RANDOMWALK_THRESHOLD_success_1() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 55, "0.64");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(55), overrides.get(0).getRuntimeParamId());
        assertEquals("0.64", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_RANDOMWALK_THRESHOLD_success_2() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 55, "0.99");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(55), overrides.get(0).getRuntimeParamId());
        assertEquals("0.99", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_RANDOMWALK_THRESHOLD_fail_1() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 55, "54.23");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(55), overrides.get(0).getRuntimeParamId());
            assertEquals("54.23", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_FORCE_BKCRV_TYPE_success_0() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 76, "3");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(76), overrides.get(0).getRuntimeParamId());
        assertEquals("3", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_FORCE_BKCRV_TYPE_fail_0() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 76, "4");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(76), overrides.get(0).getRuntimeParamId());
            assertEquals("4", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_FCST_METHOD_success() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 32, "2");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(32), overrides.get(0).getRuntimeParamId());
        assertEquals("2", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_FCST_METHOD_fail() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 32, "3");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(32), overrides.get(0).getRuntimeParamId());
            assertEquals("3", overrides.get(0).getValue());
            ;
        });
    }

    @Test
    public void testSaveValidation_BASE_COMP_WEIGHT_success() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 37, "0.45");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(37), overrides.get(0).getRuntimeParamId());
        assertEquals("0.45", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_BASE_COMP_WEIGHT_fail() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(new Integer(TestProperty.H1.getId()));
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 37, "1.3");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(Integer.valueOf(pg2.getId()), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(37), overrides.get(0).getRuntimeParamId());
            assertEquals("1.3", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_DESEASON_FLAG_success_1() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(TestProperty.H1.getId());
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 46, "1");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(46), overrides.get(0).getRuntimeParamId());
        assertEquals("1", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_DESEASON_FLAG_success_0() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(TestProperty.H1.getId());
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 46, "0");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(46), overrides.get(0).getRuntimeParamId());
        assertEquals("0", overrides.get(0).getValue());
    }

    @Test
    public void testSaveValidation_DESEASON_FLAG_Fail_decimal() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(TestProperty.H1.getId());
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 46, "1.2");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(46), overrides.get(0).getRuntimeParamId());
            assertEquals("1.2", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_DESEASON_FLAG_Fail_non_supported_integer() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(TestProperty.H1.getId());
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 46, "4");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(46), overrides.get(0).getRuntimeParamId());
            assertEquals("4", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_GRP_BKCRV_HIER_METHOD_fail() {
        assertThrows(TetrisException.class, () -> {
            ProcessGroup pg = new ProcessGroup();
            pg.setProcessGroupId(1);
            pg.setPropertyId(TestProperty.H1.getId());
            pg.setStatusId(1);
            pg.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pg);
            List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
            ProcessGroup pg2 = listProcessGroup.get(1);
            ProcessGroupScope pgs = new ProcessGroupScope();
            pgs.setProcessGroupId(pg2.getId());
            pgs.setAccomClassId(2);
            pgs.setFcstTaskId(18);
            pgs.setForecastGroupId(3);
            pgs.setForecastTypeId(1);
            pgs.setModifiedDate(LocalDateTime.now());
            tenantCrudService().save(pgs);
            service.save(1, 2, 82, "1.36");
            List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
            assertEquals(1, overrides.size());
            assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
            assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
            assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
            assertEquals(Integer.valueOf(82), overrides.get(0).getRuntimeParamId());
            assertEquals("1.36", overrides.get(0).getValue());
        });
    }

    @Test
    public void testSaveValidation_GRP_BKCRV_HIER_METHOD_success() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(TestProperty.H1.getId());
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);

        ProcessGroupScope pgs = new ProcessGroupScope();
        pgs.setProcessGroupId(pg2.getId());
        pgs.setAccomClassId(2);
        pgs.setFcstTaskId(18);
        pgs.setForecastGroupId(3);
        pgs.setForecastTypeId(1);
        pgs.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pgs);

        service.save(1, 2, 82, "2");

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        assertEquals(pg2.getId(), overrides.get(0).getProcessGroupId());
        assertEquals(Integer.valueOf(1), overrides.get(0).getProcessGroupTypeId());
        assertEquals(Integer.valueOf(2), overrides.get(0).getFcstTaskId());
        assertEquals(Integer.valueOf(82), overrides.get(0).getRuntimeParamId());
        assertEquals("2", overrides.get(0).getValue());
    }

    @Test
    public void shouldOverrideIpCfgRuntimeParamForLdb() {
        //GIVEN
        ProcessGroup processGroup2 = createProcessGroup(2, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);

        createProcessGroupScope(processGroup2, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        //WHEN
        service.overrideIpCfgRuntimeParamForLdb();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(4, rows.size());
        assertRuntimeParamOverrideFor(processGroup2, rows.get(0), MIN_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(1), MAX_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(2), BASE_COMP_WEIGHT, "0.9", 8, 1);
        assertRuntimeParamOverrideFor(processGroup4, rows.get(3), IGNORE_MISSING, "0", 19, 3);
    }

    @Test
    public void shouldIgnoreOverrideIpCfgRuntimeParamForGroupWhenGroupFgNotAvailable() {
        //GIVEN
        ProcessGroup processGroup2 = createProcessGroup(2, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup2, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        //WHEN
        service.overrideIpCfgRuntimeParamForLdb();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(3, rows.size());
        assertRuntimeParamOverrideFor(processGroup2, rows.get(0), MIN_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(1), MAX_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(2), BASE_COMP_WEIGHT, "0.9", 8, 1);
    }

    @Test
    public void shouldIgnoreOverrideIpCfgRuntimeParamForGroupWhenUnqualifiedFgNotAvailable() {
        //GIVEN
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup2 = createProcessGroup(2, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup2, GROUP_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        createProcessGroupScope(processGroup3, GROUP_FORECAST_TYPE_ID, LocalDate.now().plusDays(2).toDate());
        //WHEN
        service.overrideIpCfgRuntimeParamForLdb();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(1, rows.size());
        assertRuntimeParamOverrideFor(processGroup3, rows.get(0), IGNORE_MISSING, "0", 19, 3);
    }

    @Test
    public void testAvoidMultipleInsertForLdbRuntimeParamForQualified() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_YIELDABLE_TYPE_ID, LocalDate.now().toDate());

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "2");
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "5");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        RuntimeParamOverride outlierDetectParam = overrides.get(0);


        List<Integer> paramIds = tenantCrudService().findByNativeQuery("select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + OUTLIER_DETECT + "'");
        assertEquals(1, paramIds.size());
        assertEquals(outlierDetectParam.getRuntimeParamId(), paramIds.get(0));
        assertEquals(outlierDetectParam.getValue(), "2");
    }

    @Test
    public void testNoInsertionWhenNoQualifiedGroups() {
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "5");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
    }

    @Test
    public void testNoInsertionWhenNoUnqualifiedGroups() {
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamForUnqualified(OUTLIER_DETECT, "5");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
    }

    @Test
    public void testNoInsertionWhenNoQualifiedGroupsForIfValueExistsQuery() {
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
    }

    @Test
    public void testUpdatesStatusWhenOverrideAlreadyExistsForQualified() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_YIELDABLE_TYPE_ID, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.updateInsertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        assertEquals(1, overrides.get(0).getStatusId().intValue());

        service.removeOverrideForRuntimeParamForQualified("FCST_METHOD", "1");
        flushAndClear();
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        assertEquals(2, overrides.get(0).getStatusId().intValue());

        service.updateInsertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        flushAndClear();
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        assertEquals(1, overrides.get(0).getStatusId().intValue());
    }

    @Test
    public void testInsertWhenSameValueDoesNotExistsForQualified() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_YIELDABLE_TYPE_ID, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
        service.insertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        service.insertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        RuntimeParamOverride fcstMethodRuntimeParam = overrides.get(0);

        List<Integer> paramIds = tenantCrudService().findByNativeQuery("select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='FCST_METHOD'");
        assertEquals(1, paramIds.size());
        assertEquals(fcstMethodRuntimeParam.getRuntimeParamId(), paramIds.get(0));
        assertEquals(fcstMethodRuntimeParam.getValue(), "1");
    }

    @Test
    public void testRemoveOverrideForQualified() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_YIELDABLE_TYPE_ID, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
        service.insertRuntimeParamIfNotExistsForQualified("FCST_METHOD", "1");
        service.removeOverrideForRuntimeParamForQualified("FCST_METHOD", "1");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());

        List<RuntimeParamOverride> activeOverrides = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override where IP_Cfg_Runtime_Param_id = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='FCST_METHOD') and Status_id=1");
        assertEquals(0, activeOverrides.size());

        List<RuntimeParamOverride> inactiveParams = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override where IP_Cfg_Runtime_Param_id = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='FCST_METHOD') and Status_id=2");
        assertEquals(1, inactiveParams.size());
    }

    @Test
    public void shouldUpdateUnqualifiedParamOverridesWhichIsInactiveForActiveProcessGroupId() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
        service.insertRuntimeParamForUnqualified(OUTLIER_DETECT, "2");
        tenantCrudService().executeUpdateByNativeQuery("update [IP_Cfg_Runtime_Param_Override] set Status_ID = 2 where IP_Cfg_Runtime_Param_ID=5 and IP_Cfg_Fcst_Task_ID = 10");
        service.updateStatusOfInactiveParamForUnqualifiedPresentAlreadyWithActiveProcessGroupId(OUTLIER_DETECT, "2", 10);

        List<RuntimeParamOverride> activeUnQualifiedParam = tenantCrudService().findByNativeQuery(" select * from IP_Cfg_Runtime_Param_Override where IP_Cfg_Runtime_Param_id = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + OUTLIER_DETECT + "') and IP_Cfg_Fcst_Task_ID =10 and Status_id = 1");
        assertEquals(1, activeUnQualifiedParam.size());
    }

    @Test
    public void shouldUpdateQualifiedParamOverridesWhichIsInactiveForActiveProcessGroupId() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "2");
        tenantCrudService().executeUpdateByNativeQuery("update [IP_Cfg_Runtime_Param_Override] set Status_ID = 2 where IP_Cfg_Runtime_Param_ID=5 and IP_Cfg_Fcst_Task_ID = 15");
        service.updateStatusOfInactiveParamForQualifiedPresentAlreadyWithActiveProcessGroupId(OUTLIER_DETECT, "2");

        List<RuntimeParamOverride> activeQualifiedParam = tenantCrudService().findByNativeQuery(" select * from IP_Cfg_Runtime_Param_Override where IP_Cfg_Runtime_Param_id = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + OUTLIER_DETECT + "') and IP_Cfg_Fcst_Task_ID =15 and Status_id = 1");
        assertEquals(1, activeQualifiedParam.size());
    }

    @Test
    public void testAvoidMultipleInsertForLdbRuntimeParamForUnqualified() {

        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, QUALIFIED_LINKED_TYPE_ID, LocalDate.now().toDate());

        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamForUnqualified(OUTLIER_DETECT, "2");
        service.insertRuntimeParamForUnqualified(OUTLIER_DETECT, "5");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        RuntimeParamOverride outlierDetectParam = overrides.get(0);


        List<Integer> paramIds = tenantCrudService().findByNativeQuery("select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + OUTLIER_DETECT + "'");
        assertEquals(1, paramIds.size());
        assertEquals(outlierDetectParam.getRuntimeParamId(), paramIds.get(0));
        assertEquals(outlierDetectParam.getValue(), "2");

    }

    @Test
    public void insertRuntimeParamForTravelodge() {
        ProcessGroup processGroup = tenantCrudService().save(createProcessGroup(1, ACTIVE_STATUS_ID));
        SeasonGroup seasonGroup = createSeasonGroup();
        DowGroup dowGroup = createDOWGroup();
        HorizonGroup horizonGroup = createHorizoneGroup();
        LosGroup losGroup = createLosGroup();
        createProcessGroupConfig(processGroup, seasonGroup, dowGroup, horizonGroup, losGroup);
        createProcessGroupScope(processGroup, QUALIFIED_LINKED_TYPE_ID, LocalDate.now().toDate());

        service.insertRuntimeParamForTravelodge(MAX_ELAS_PARAM_NAME);
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        service.insertRuntimeParamForTravelodge(MAX_ELAS_PARAM_NAME);
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        RuntimeParamOverride param = overrides.get(0);

        List<Integer> paramIds = tenantCrudService().findByNativeQuery("select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + MAX_ELAS_PARAM_NAME + "'");
        assertEquals(1, paramIds.size());
        assertEquals(param.getRuntimeParamId(), paramIds.get(0));
        assertEquals(param.getValue(), "-1.2");

    }

    private ProcessGroupConfig createProcessGroupConfig(ProcessGroup group, SeasonGroup seasonGroup, DowGroup dowGroup, HorizonGroup horizonGroup, LosGroup losGroup) {
        ProcessGroupConfig processGroupConfig = new ProcessGroupConfig();
        processGroupConfig.setProcessGroupId(group.getId());
        processGroupConfig.setDowGroupId(dowGroup.getId());
        processGroupConfig.setSeasonGroupId(seasonGroup.getId());
        processGroupConfig.setHorizonGroupId(horizonGroup.getId());
        processGroupConfig.setDowGroupId(dowGroup.getId());
        processGroupConfig.setCreateDate_DTTM(LocalDateTime.now());
        processGroupConfig.setReadingHorizonGroupId(horizonGroup.getId());
        processGroupConfig.setStatusId(1);
        return tenantCrudService().save(processGroupConfig);
    }

    private LosGroup createLosGroup() {
        LosGroup losGroup = new LosGroup();
        losGroup.setPropertyId(05);
        losGroup.setCreateDate_DTTM(LocalDateTime.now());
        losGroup.setLosGroupId(2);
        return tenantCrudService().save(losGroup);
    }

    private HorizonGroup createHorizoneGroup() {
        HorizonGroup horizonGroup = new HorizonGroup();
        horizonGroup.setCreateDate_DTTM(LocalDateTime.now());
        horizonGroup.setHorizonGroupId(4);
        return tenantCrudService().save(horizonGroup);
    }

    private DowGroup createDOWGroup() {
        DowGroup dowGroup = new DowGroup();
        dowGroup.setPropertyId(05);
        dowGroup.setCreateDate_DTTM(LocalDateTime.now());
        dowGroup.setDowGroupId(1);
        return tenantCrudService().save(dowGroup);
    }

    private SeasonGroup createSeasonGroup() {
        SeasonGroup seasonGroup = new SeasonGroup();
        seasonGroup.setPropertyId(05);
        seasonGroup.setSeasonGroupId(01);
        seasonGroup.setCreateDate_DTTM(LocalDateTime.now());
        return tenantCrudService().save(seasonGroup);
    }

    @Test
    public void testAvoidMultipleInsertForRuntimeParamForScandicSgroup() {

        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, 9, LocalDate.now().toDate());
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(0, overrides.size());

        service.insertRuntimeParamForUnqualified("TS_CLASS_ID", "20000");
        service.insertRuntimeParamForUnqualified("TS_CLASS_ID", "20000");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size(), "Should skip duplicate insert");
        RuntimeParamOverride outlierDetectParam = overrides.get(0);


        List<Integer> paramIds = tenantCrudService().findByNativeQuery("select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='" + TS_CLASS_ID + "'");
        assertEquals(1, paramIds.size());
        assertEquals(outlierDetectParam.getRuntimeParamId(), paramIds.get(0), "IP_Cfg_Runtime_Param_ID should be matching");
        assertEquals(outlierDetectParam.getValue(), "20000", "value  of TS_CLASS_ID should be right");
    }

    @Test
    public void shouldNotInsertParamOveridesWhenBothActiveAndInactiveOverirdesPresent() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, 2, LocalDate.now().toDate());
        service.insertRuntimeParamForQualified("TS_CLASS_ID", "20000");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=2 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='TS_CLASS_ID')");
        List<RuntimeParamOverride> overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(1, overrides.size());
        service.insertRuntimeParamForQualified("TS_CLASS_ID", "30000");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(2, overrides.size());
        service.insertRuntimeParamForQualified("TS_CLASS_ID", "30000");
        overrides = tenantCrudService().findAll(RuntimeParamOverride.class);
        assertEquals(2, overrides.size());
    }

    @Test
    public void shouldHandleOverrideForExistingIpCfgRuntimeParamForLdb() {
        //GIVEN
        ProcessGroup processGroup2 = createProcessGroup(2, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup2, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        //WHEN
        service.overrideIpCfgRuntimeParamForLdb();
        service.overrideIpCfgRuntimeParamForLdb();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(4, rows.size());
        assertRuntimeParamOverrideFor(processGroup2, rows.get(0), MIN_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(1), MAX_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup2, rows.get(2), BASE_COMP_WEIGHT, "0.9", 8, 1);
        assertRuntimeParamOverrideFor(processGroup4, rows.get(3), IGNORE_MISSING, "0", 19, 3);
    }

    @Test
    public void shouldIgnoreInactiveProcessGroupWhileOverrideIpCfgRuntimeParamForLdb() {
        //GIVEN
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup2 = createProcessGroup(2, INACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup2, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        createProcessGroupScope(processGroup3, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().plusDays(1).toDate());
        //WHEN
        service.overrideIpCfgRuntimeParamForLdb();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(4, rows.size());
        assertRuntimeParamOverrideFor(processGroup1, rows.get(0), MIN_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup1, rows.get(1), MAX_ELAS, "-1", 9, 1);
        assertRuntimeParamOverrideFor(processGroup1, rows.get(2), BASE_COMP_WEIGHT, "0.9", 8, 1);
        assertRuntimeParamOverrideFor(processGroup4, rows.get(3), IGNORE_MISSING, "0", 19, 3);
    }

    @Test
    public void restoreParameterOverridesValuesTest() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup3, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());

        service.insertRuntimeParamForUnqualified(TS_CLASS_ID, "20000");
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "4");
        //Before CCFG
        Date yesterday = LocalDate.now().minusDays(1).toDate();
        int rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(0, rowsAffected);

        //CCFG
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='TS_CLASS_ID')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Process_Group Set Status_ID=4 where Process_Group_Id In(1)");
        ProcessGroup processGroup5 = createProcessGroup(5, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup5, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        //After CCFG
        rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(1, rowsAffected);
    }

    @Test
    public void restoreParameterOverridesValuesInsertShouldSkipWhenOverridesWereAlreadyAddedByAnotherJob() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup3, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());

        service.insertRuntimeParamForUnqualified(TS_CLASS_ID, "20000");
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "4");
        //Before CCFG
        Date yesterday = LocalDate.now().minusDays(1).toDate();
        int rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(0, rowsAffected);

        //CCFG
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='TS_CLASS_ID')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Process_Group Set Status_ID=4 where Process_Group_Id In(1)");
        ProcessGroup processGroup5 = createProcessGroup(5, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup5, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        //After CCFG
        rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(1, rowsAffected);

        Date twoDaysBeforeYesterday = LocalDate.now().minusDays(3).toDate();
        rowsAffected = service.restoreParameterOverridesValues(twoDaysBeforeYesterday);
        assertEquals(0, rowsAffected);
    }

    @Test
    public void restoreParameterOverridesValuesBetweenMultipleCCFGTest() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        createProcessGroupScope(processGroup3, QUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());

        service.insertRuntimeParamForUnqualified(TS_CLASS_ID, "20000");
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "4");

        Date startTime = LocalDate.now().minusDays(3).toDate();
        int rowsAffected = service.restoreParameterOverridesValues(startTime);
        assertEquals(0, rowsAffected);


        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='TS_CLASS_ID')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='OUTLIER_DETECT')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Process_Group Set Status_ID=4 where Process_Group_Id In(1,3)");

        ProcessGroup processGroup5 = createProcessGroup(5, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup5, QUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        tenantCrudService().executeUpdateByNativeQuery("update IP_Cfg_Process_Group Set Modified_DTTM = DATEADD(DAY, -2, GETDATE()) where  Process_Group_Id =5");


        ProcessGroup processGroup6 = createProcessGroup(6, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup6, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());


        Date yesterday = LocalDate.now().minusDays(1).toDate();
        rowsAffected = service.restoreParameterOverridesValues(yesterday);

        assertEquals(1, rowsAffected);
    }

    @Test
    public void restoreParameterOverridesValuesShouldWorkWhenOverridesAbsentTest() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup3, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().toDate());

        //Before CCFG
        Date yesterday = LocalDate.now().minusDays(1).toDate();
        int rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(0, rowsAffected);

        //CCFG
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Process_Group Set Status_ID=4 where Process_Group_Id In(1)");
        ProcessGroup processGroup5 = createProcessGroup(5, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup5, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        //After CCFG
        rowsAffected = service.restoreParameterOverridesValues(yesterday);
        assertEquals(0, rowsAffected);

    }

    @Test
    public void restoreParameterOverridesValues_skipInsertsForStatusId2_BetweenMultipleCCFGTest() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        ProcessGroup processGroup3 = createProcessGroup(3, ACTIVE_STATUS_ID);
        ProcessGroup processGroup4 = createProcessGroup(4, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        createProcessGroupScope(processGroup3, QUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        createProcessGroupScope(processGroup4, GROUP_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());

        service.insertRuntimeParamForUnqualified(TS_CLASS_ID, "20000");
        service.insertRuntimeParamForUnqualified(MAX_ELAS_PARAM_NAME, "1");
        service.insertRuntimeParamForQualified(OUTLIER_DETECT, "4");
        service.insertRuntimeParamForUnqualified(MIN_ELAS_PARAM_NAME, "2");


        Date startTime = LocalDate.now().minusDays(3).toDate();
        int rowsAffected = service.restoreParameterOverridesValues(startTime);
        assertEquals(0, rowsAffected);


        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='TS_CLASS_ID')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='OUTLIER_DETECT')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=2 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='MAX_ELAS')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Runtime_Param_Override Set Status_ID=4 where IP_Cfg_Runtime_Param_ID = (select ip_cfg_runtime_param_id from IP_Cfg_Runtime_Param_Name_List where Param_name ='MIN_ELAS')");
        tenantCrudService().executeUpdateByNativeQuery("Update IP_Cfg_Process_Group Set Status_ID=4 where Process_Group_Id In(1,3)");

        ProcessGroup processGroup5 = createProcessGroup(5, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup5, QUALIFIED_FORECAST_TYPE_ID, LocalDate.now().minusDays(2).toDate());
        tenantCrudService().executeUpdateByNativeQuery("update IP_Cfg_Process_Group Set Modified_DTTM = DATEADD(DAY, -2, GETDATE()) where  Process_Group_Id =5");


        ProcessGroup processGroup6 = createProcessGroup(6, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup6, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());

        Date yesterday = LocalDate.now().minusDays(1).toDate();
        rowsAffected = service.restoreParameterOverridesValues(yesterday);

        assertEquals(2, rowsAffected);
    }

    @Test
    public void shouldRemoveLDBOverrides() {
        ProcessGroup pg = new ProcessGroup();
        pg.setProcessGroupId(1);
        pg.setPropertyId(new Integer(TestProperty.H1.getId()));
        pg.setStatusId(1);
        pg.setModifiedDate(LocalDateTime.now());
        tenantCrudService().save(pg);

        List<ProcessGroup> listProcessGroup = tenantCrudService().findAll(ProcessGroup.class);
        ProcessGroup pg2 = listProcessGroup.get(1);


        tenantCrudService().save(getRuntimeParamOverride(pg2, 8, 37, 1, "0.9"));
        tenantCrudService().save(getRuntimeParamOverride(pg2, 19, 81, 3, "0"));
        tenantCrudService().save(getRuntimeParamOverride(pg2, 15, 32, 2, "1"));

        service.removeLDBSpecificOverrides();

        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(3, rows.size());
        assertEquals(2, rows.get(0)[6]);
        assertEquals(2, rows.get(1)[6]);
        assertEquals(2, rows.get(2)[6]);

    }

    @Test
    public void shouldResetTlukParameters() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, LocalDate.now().toDate());
        tenantCrudService().save(getRuntimeParamOverride(processGroup1, 9, 18, 1, "-1"));
        tenantCrudService().save(getRuntimeParamOverride(processGroup1, 9, 20, 1, "-1"));

        service.resetTlukParemeters();

        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(2, rows.size());
        assertEquals("-1.2", rows.get(0)[5].toString());
        assertEquals("-1.2", rows.get(1)[5].toString());

    }

    @Test
    void testRemoveRuntimeOverridesForSpecifiedParameters() {
        ProcessGroup processGroup1 = createProcessGroup(1, ACTIVE_STATUS_ID);
        createProcessGroupScope(processGroup1, UNQUALIFIED_FORECAST_TYPE_ID, JavaLocalDateUtils.toDate(java.time.LocalDate.now()));
        tenantCrudService().save(getRuntimeParamOverride(processGroup1, 9, 133, 1, "10000"));
        tenantCrudService().save(getRuntimeParamOverride(processGroup1, 9, 5, 1, "2"));

        service.removeRuntimeOverridesForSpecifiedParameters(List.of(TS_CLASS_ID, OUTLIER_DETECT));

        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from IP_Cfg_Runtime_Param_Override");
        assertEquals(2, rows.size());
        assertEquals(INACTIVE_STATUS_ID, rows.get(0)[6]);
        assertEquals(INACTIVE_STATUS_ID, rows.get(1)[6]);
    }

    private List<RuntimeParamOverride> getOverride(ProcessGroup pg2, int fcstTaskId, int runtimeParamId, int processGroupTypeId) {
        return tenantCrudService().findByNativeQuerySingleResult(
                GET_OVVERIDE,
                QueryParameter.with("processGroupId", pg2.getId()).and("fcstTaskId", fcstTaskId)
                        .and("runtimeParamId", runtimeParamId).and("processGroupTypeId", processGroupTypeId)
                        .parameters());
    }

    private RuntimeParamOverride getRuntimeParamOverride(ProcessGroup pg2, int fcstId, int paramId, int processGroupTypeId, String value) {
        RuntimeParamOverride override = new RuntimeParamOverride();
        override.setProcessGroupId(pg2.getId());
        override.setPropertyId(new Integer(TestProperty.H1.getId()));
        override.setFcstTaskId(fcstId);
        override.setRuntimeParamId(paramId);
        override.setProcessGroupTypeId(processGroupTypeId);
        override.setValue(value);
        override.setStatusId(new Integer(1));
        override.setModifiedDTTM(new Date());
        override.setUser_Id(11403);
        return override;
    }

    private void assertRuntimeParamOverrideFor(ProcessGroup processGroup, Object[] row, int expectedParamId, String expectedValue, int expectedFcstTaskId, int expectedProcessGroupType) {
        assertEquals(processGroup.getId(), row[1], "Process Group didn't match ");
        assertEquals(5, row[2], "Property_ID didn't match ");
        assertEquals(expectedParamId, row[3], "Runtime Param ID didn't match ");
        assertEquals(expectedFcstTaskId, row[4], "Fcst Task ID didn't match ");
        assertEquals(expectedValue, row[5], "Value didn't match ");
        assertEquals(1, row[6], "Status ID didn't match ");
        assertEquals(expectedProcessGroupType, row[8], "Process group type ID didn't match ");
        assertEquals(11403, ((BigInteger) row[9]).intValue(), "User ID didn't match ");
    }

    private void createProcessGroupScope(ProcessGroup savedProcessGroup, int forecastTypeId, Date date) {
        ProcessGroupScope processGroupScope = new ProcessGroupScope();
        processGroupScope.setProcessGroupId(savedProcessGroup.getId());
        processGroupScope.setFcstTaskId(1);
        processGroupScope.setForecastGroupId(3);
        processGroupScope.setAccomClassId(2);
        processGroupScope.setForecastTypeId(forecastTypeId);
        processGroupScope.setModifiedDate(LocalDateUtils.toJavaLocalDateTime(date));
        tenantCrudService().save(processGroupScope);
    }

    private ProcessGroup createProcessGroup(int processGroupId, Integer statusId) {
        ProcessGroup processGroup = new ProcessGroup();
        processGroup.setProcessGroupId(processGroupId);
        processGroup.setPropertyId(5);
        processGroup.setStatusId(statusId);
        processGroup.setModifiedDate(LocalDateTime.now());
        return tenantCrudService().save(processGroup);
    }

}
