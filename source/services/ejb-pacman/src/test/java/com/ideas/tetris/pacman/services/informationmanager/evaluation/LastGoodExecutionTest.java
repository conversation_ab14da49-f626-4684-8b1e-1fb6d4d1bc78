package com.ideas.tetris.pacman.services.informationmanager.evaluation;

import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.MultiPropertyCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.CrsFileArrivedPropertyEvaluationService;
import com.ideas.tetris.pacman.services.informationmanager.evaluation.dal.InfoMgrEvalExecutions;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.anyMapOf;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LastGoodExecutionTest {

    @Spy
    private InformationManagerEvaluationServicesExecsTracker informationManagerEvaluationServicesExecsTracker;

    private PacmanConfigParamsService configParamsService;

    private final Integer propertyId = TestProperty.H1.getId();

    @BeforeEach
    public void setUp() throws Exception {
        DateService dateService = DateService.createTestInstance();
        mockConfigParamService();
        mockMultiPropertyCrudService();
        dateService.setConfigParamsService(configParamsService);
        informationManagerEvaluationServicesExecsTracker.setDateService(dateService);
        setWorkContext();
        mockCurrentDate();
    }

    @Test
    public void shouldEvaluateSystemNotUpToDateAlertWhenOnlyServerDateChanges() {
        String serviceId = CrsFileArrivedPropertyEvaluationService.class.getSimpleName();
        boolean okToProceed = informationManagerEvaluationServicesExecsTracker.isOkToProceed(propertyId, serviceId);
        assertFalse(okToProceed);
    }

    private void mockConfigParamService() {
        configParamsService = mock(PacmanConfigParamsService.class);
        informationManagerEvaluationServicesExecsTracker.setConfigService(configParamsService);
        when(configParamsService.getValue(anyString(), eq(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))).thenReturn("JST");
        when(configParamsService.getValue(anyString(), eq("pacman.InfoMgrEvalService.EnableInfoMgrLastGoodRunCheck"))).thenReturn("true");
    }

    private void mockMultiPropertyCrudService() {
        MultiPropertyCrudServiceBean multiPropertyCrudService = mock(MultiPropertyCrudServiceBean.class);
        informationManagerEvaluationServicesExecsTracker.setMultiPropertyCrudService(multiPropertyCrudService);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(eq(propertyId), eq(InfoMgrEvalExecutions.FIND_BY_PROPERTY_ID_AND_SERVICE_ID), anyMapOf(String.class, Object.class))).thenReturn(createInfoMgrEvalExecutions());
    }

    private void mockCurrentDate() {
        Calendar calendar = Calendar.getInstance();
        // 2017-09-11 3:00:00
        calendar.setTime(new Date(1505079000000L));
        doReturn(calendar.getTime()).when(informationManagerEvaluationServicesExecsTracker).getCurrentDate();
    }

    private void setWorkContext() {
        WorkContextType contextType = new WorkContextType();
        contextType.setClientCode("BSTN");
        contextType.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.setWorkContext(contextType);
    }

    private InfoMgrEvalExecutions createInfoMgrEvalExecutions() {
        InfoMgrEvalExecutions evalExecutions = new InfoMgrEvalExecutions();
        evalExecutions.setServiceId(CrsFileArrivedPropertyEvaluationService.class.getSimpleName());
        // 2017-09-10 22:10:00
        Date lastGoodExecDateInServerTimezone = new Date(1505061600000L);
        evalExecutions.setLastGoodExecDate(lastGoodExecDateInServerTimezone);
        return evalExecutions;
    }
}
