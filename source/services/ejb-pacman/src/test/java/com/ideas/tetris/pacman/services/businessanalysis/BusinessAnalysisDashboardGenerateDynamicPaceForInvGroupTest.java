package com.ideas.tetris.pacman.services.businessanalysis;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BusinessAnalysisDashboardGenerateDynamicPaceForInvGroupTest extends AbstractG3JupiterTest {

    @Test
    void testGroupBlockDataAtBTLevel(){
        clearAllReferredTables();
        Date latestDate = tenantCrudService().findByNamedQuerySingleResult(FileMetadata.GET_LATEST_SNAPSHOT_DT);
        LocalDate snapshotDT = LocalDateUtils.toJavaLocalDate(latestDate);
        LocalDate businessDT = snapshotDT.minusDays(1);
        InventoryGroup inventoryGroup = createInventoryGroup();

        StringBuilder insertQuery = new StringBuilder();
        insertIntoGroupMaster(insertQuery,"CHTSOC",snapshotDT.plusDays(10),snapshotDT.plusDays(20));
        insertIntoGroupMaster(insertQuery,"CHTPSG",snapshotDT.plusDays(15),snapshotDT.plusDays(45));
        insertIntoGroupBlock(insertQuery,"CHTSOC",snapshotDT.plusDays(10),4,11,8);
        insertIntoPaceGroupBlock(insertQuery,"CHTPSG",snapshotDT.plusDays(15),snapshotDT.minusDays(10),11,6);
        insertIntoPaceGroupBlock(insertQuery,"CHTSOC",snapshotDT.plusDays(10),businessDT,11,8);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_mkt_accom_activity_dynamic_pace_at_BT 5," + inventoryGroup.getId() +
                 ",'" + snapshotDT.plusDays(10) + "','" + snapshotDT.plusDays(20) + "','" + businessDT + "'," + 1 + "," + 0 + "," + 0);
        assertEquals(8, results.get(0)[2],"Group Business Room Sold assertion failed");

    }

    private void clearAllReferredTables() {
        String cleanTables = "TRUNCATE TABLE Pace_Group_Block;" +
                "TRUNCATE TABLE Pace_Group_Master;" +
                "DELETE FROM Group_Block;" +
                "DELETE FROM Wash_Ind_Group_Fcst_OVR;" +
                "DELETE FROM Wash_Ind_Group_Fcst;" +
                "DELETE FROM Group_Master;";
        tenantCrudService().executeUpdateByNativeQuery(cleanTables);
    }

    private InventoryGroup createInventoryGroup() {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("INV_GRP");
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 3);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, 3));
        tenantCrudService().save(inventoryGroupDetails);
        return inventoryGroup;
    }

    private void insertIntoGroupMaster(StringBuilder insertQuery, String groupCode, LocalDate startDate, LocalDate endDate) {
        insertQuery.append("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) " + "values(000005, '")
                .append(groupCode).append("', 'Test Group Name', 'Test GM',(select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = 'DISC'),'DEFINITE','TRANS','")
                .append(startDate).append("','").append(endDate).append("','2025-01-11');");
    }

    private void insertIntoGroupBlock(StringBuilder insertQuery, String groupCode, LocalDate occupancyDate, int accomTypeId, int blocks, int pickup) {
        insertQuery.append(" insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate) " + "values ((select Group_ID from Group_Master where Group_Code='")
                .append(groupCode).append("'),'").append(occupancyDate).append("',").append(accomTypeId).append(",").append(blocks).append(",").append(pickup).append(",").append(blocks).append(",75.00);");
    }

    private void insertIntoPaceGroupBlock(StringBuilder insertQuery, String groupCode, LocalDate occupancyDate, LocalDate bdeDate, int blocks, int pickup) {
       insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " + "values ((select Group_ID from Group_Master where Group_Code= '")
               .append(groupCode).append("'),'").append(occupancyDate).append("',4,'").append(bdeDate).append("',").append(blocks).append(",").append(pickup).append(",").append(blocks).append(",75.00);");
    }

}
