package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.informationmanager.alert.repository.InsufficientCompetitorExceptionRepository;
import com.ideas.tetris.pacman.services.informationmanager.cache.InfoMgrStatusEntityCache;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.dto.Step;
import com.ideas.tetris.pacman.services.informationmanager.entity.*;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionAlertService;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.rdl.entity.WebrateTypeProduct;
import com.ideas.tetris.pacman.services.webrate.entity.UniqueWebrateRankingCreator;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateRankingAccomClass;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateRankingAccomClassOverride;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateType;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
class InsufficientCompetitorExceptionServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    InsufficientCompetitorExceptionService service;

    @InjectMocks
    ExceptionAlertService exceptionAlertService;

    @InjectMocks
    InsufficientCompetitorExceptionRepository insufficientCompetitorExceptionRepository;

    @InjectMocks
    AccommodationMappingService accommodationMappingService;

    @InjectMocks
    private AccommodationService accommodation2Service;

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    private CrudService tenantCrudService = tenantCrudService();

    private int propertyId;

    private String originalMarketPostionConstraintToggleValue;

    private LocalDate occupancyDate = LocalDate.now();

    @Mock
    CompetitorRateInfoService competitorRateInfoService;

    @BeforeEach
    void setUp() throws Exception {
        inject(service, "insufficientCompetitorExceptionRepository", insufficientCompetitorExceptionRepository);
        inject(insufficientCompetitorExceptionRepository, "crudService", tenantCrudService);
        accommodationMappingService.setAccommodationService(accommodation2Service);
        inject(service, "accommodationMappingService", accommodationMappingService);
        inject(accommodationMappingService, "crudService", tenantCrudService);
        inject(accommodation2Service, "tenantCrudService", tenantCrudService);
        service.setExceptionAlertService(createExceptionalAlertService());
        propertyId = PacmanWorkContextHelper.getPropertyId();
        originalMarketPostionConstraintToggleValue = System.getProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        createWebrate();
        inject(service, "productManagementService", productManagementService);
        inject(productManagementService, "crudService", tenantCrudService);
    }

    @AfterEach
    public void tearDown() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", originalMarketPostionConstraintToggleValue);
    }

    private ExceptionAlertService createExceptionalAlertService() {
        exceptionAlertService = new ExceptionAlertService();
        exceptionAlertService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionAlertService.setGlobalCrudService(globalCrudService());
        exceptionAlertService.setTenantCrudService(tenantCrudService());
        InfoMgrStatusEntityCache infoMgrStatusEntityCache = Mockito.mock(InfoMgrStatusEntityCache.class);
        when(infoMgrStatusEntityCache.get(anyString())).thenReturn(tenantCrudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID));
        exceptionAlertService.setInfoMgrStatusEntityCache(infoMgrStatusEntityCache);
        exceptionAlertService.setConfigService(pacmanConfigParamsService);
        return exceptionAlertService;
    }

    @Test
    void shouldCreateInSufficientCompetitorException() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - High Range"));
    }

    @Test
    void shouldCreateInSufficientCompetitorExceptionWithDCMPC() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_INFO_MANAGER)).thenReturn(true);
        when(competitorRateInfoService.getDCMPCBasedCompetitiveMarketPositionConstraint(any(), any(), any())).thenReturn("28::33");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("occupancyBasedConstraint"));
    }

    @Test
    void shouldNotCreateInSufficientCompetitorException() {
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 0);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(0, exceptionAlertList.size());
    }

    @Test
    void getAllExistingExceptionsTest() {
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 1);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        InfoMgrTypeEntity type = tenantCrudService.findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.InsufficientCompetitorRatesForCompetitiveMarket.toString()).parameters());
        Map<Date, InfoMgrExcepNotifEntity> allExistingExceptions = insufficientCompetitorExceptionRepository.getAllExistingExceptions(type, informationManagerAlert, Arrays.asList(DateUtil.convertLocalDateToJavaUtilDate(occupancyDate)));
        assertEquals(1, allExistingExceptions.size());
    }

    @Test
    void testRoomClassConstraintForDefault() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - High Range"));
    }

    @Test
    void testRoomClassConstraintWhenSeasonalDateIsOutOfRange() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "true");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);

        Date startDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.minusDays(15));
        Date endDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.minusDays(10));

        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createWebrateRankingAccomClassWithOvr(accomClass1, startDate, endDate);
        createWebrateRankingAccomClassWithOvr(accomClass2, startDate, endDate);

        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - High Range"));
    }

    @Test
    void testRoomClassConstraintWhenSeasonalDateFallsOnRange() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "true");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);

        Date startDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.minusDays(2));
        Date endDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.plusDays(20));

        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createWebrateRankingAccomClassWithOvr(accomClass1, startDate, endDate);
        createWebrateRankingAccomClassWithOvr(accomClass2, startDate, endDate);

        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - Mid Range"));
    }

    @Test
    void testRoomClassConstraintWithMultipleSeasonal() {
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "true");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);

        Date startDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.minusDays(2));
        Date endDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.plusDays(20));

        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createWebrateRankingAccomClassWithOvr(accomClass1, startDate, endDate);
        createWebrateRankingAccomClassWithOvr(accomClass2, startDate, endDate);
        createWebrateRankingAccomClassWithOvr(accomClass1, DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.plusDays(40)), DateUtil.convertLocalDateToJavaUtilDate(occupancyDate.plusDays(50)));

        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - Mid Range"));
    }

    @Test
    void shouldCreateInSufficientCompetitorExceptionForIndependentProduct() {
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        Product indProduct = ProductBuilder.createIndependentProductProduct("IDP1");
        tenantCrudService.save(indProduct);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass1, indProduct.getId());
        createWebrateRankingAccomClass(accomClass2, indProduct.getId());
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass1, accomType1, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 0, indProduct.getId());
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId, indProduct);
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains(indProduct.getName()));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - High Range"));
        assertTrue(alertDetails.contains("XYZ - High Range"));
        assertTrue(alertDetails.contains("XYZ - High Range"));
        List<Step> steps = exceptionAlertService.getSteps(alerts.get(0).getId(), propertyId);
        assertNotNull(steps);
        assertTrue(steps.get(0).getUrl().contains("destination=/solutions/pricing?productId=" + indProduct.getId()));
    }

    @Test
    void testExistingInSufficientCompetitorExceptionForIndependentProduct() {
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        Product indProduct = ProductBuilder.createIndependentProductProduct("IDP1");
        tenantCrudService.save(indProduct);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass1, indProduct.getId());
        createWebrateRankingAccomClass(accomClass2, indProduct.getId());
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass1, accomType1, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 0, indProduct.getId());
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId, indProduct);
        assertException(informationManagerAlert, 20);
        // score will be increased in second evaluation
        assertException(informationManagerAlert, 40);
    }

    @Test
    void testSkippedExistingInSufficientCompetitorExceptionForIndependentProduct() {
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        createWebrateMappingData(accomClass1, accomClass2);
        Product indProduct = ProductBuilder.createIndependentProductProduct("IDP1");
        tenantCrudService.save(indProduct);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass1, indProduct.getId());
        createWebrateRankingAccomClass(accomClass2, indProduct.getId());
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass1, accomType1, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 1, indProduct.getId());
        createDecsionSupportData(accomClass2, accomType2, 0, indProduct.getId());
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId, indProduct);
        assertException(informationManagerAlert, 20);
        createSkipDateToSuspend(informationManagerAlert);
        // score will be decremented to 0 due to suspended occupancy date
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(0, exceptionAlertList.size());
    }

    @Test
    void shouldCreateInSufficientCompetitorExceptionForRDL() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        AccomClass accomClass1 = createAccomClass("ABC");
        AccomClass accomClass2 = createAccomClass("XYZ");
        AccomType accomType1 = createAccomType(accomClass1, 5);
        AccomType accomType2 = createAccomType(accomClass2, 5);
        Product product = new Product();
        product.setId(1);
        createWebrateTypeProduct(product);
        createWebrate(2, 365);
        createWebrateMappingData(accomClass1, accomClass2);
        createWebrateRankingAccomClass(accomClass1);
        createWebrateRankingAccomClass(accomClass2);
        createDecsionSupportData(accomClass1, accomType1, 1);
        createDecsionSupportData(accomClass2, accomType2, 0);
        InformationMgrAlertConfigEntity informationManagerAlert = createExceptionAlertConfigEntity(propertyId);

        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        String alertDetails = alerts.get(0).getDetails();
        String occupancyDateStr = occupancyDate.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
        assertTrue(alertDetails.contains("occupancyDate:DT_" + occupancyDateStr + "_DT"));
        assertTrue(alertDetails.contains("ABC - High Range"));
    }

    private void createWebrateTypeProduct(Product product) {
        WebrateType webrateType = new WebrateType();
        webrateType.setId(2);
        WebrateTypeProduct webrateTypeProduct = new WebrateTypeProduct();
        webrateTypeProduct.setWebrateType(webrateType);
        webrateTypeProduct.setProduct(product);
        webrateTypeProduct.setLos(3);
        tenantCrudService.save(webrateTypeProduct);
    }

    private void assertException(InformationMgrAlertConfigEntity informationManagerAlert, int score) {
        List<ExceptionAlert> exceptionAlertList = service.evaluate(informationManagerAlert, AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        assertEquals(1, exceptionAlertList.size());
        List<InfoMgrInstanceEntity> alerts = tenantCrudService.findAll(InfoMgrInstanceEntity.class);
        assertEquals(score, alerts.get(0).getScore());
    }

    private void createSkipDateToSuspend(InformationMgrAlertConfigEntity informationManagerAlert) {
        InfoMgrExcepNotifSkipDatesEntity skipDatesEntity = new InfoMgrExcepNotifSkipDatesEntity();
        skipDatesEntity.setOccupancyDate(DateUtil.convertLocalDateToJavaUtilDate(occupancyDate));
        skipDatesEntity.setExceptionAlertConfigEntity(informationManagerAlert);
        skipDatesEntity.setCreatedBy("110403");
        skipDatesEntity.setCreateDate(new Date());
        tenantCrudService.save(skipDatesEntity);
    }


    private AccomType createAccomType(AccomClass accomClass, int capacity) {
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass);
        accomType.setAccomTypeCapacity(capacity);
        accomType.setIsComponentRoom("Y");
        return accomType;
    }

    private AccomClass createAccomClass(String name) {
        AccomClass ac = new AccomClass();
        ac.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        ac.setName(name);
        ac.setCode(name);
        ac.setDescription("Class description");
        ac.setSystemDefault(0);
        ac.setStatusId(Constants.ACTIVE_STATUS_ID);
        ac.setCreateDate(DateUtil.getCurrentTimestamp());
        return tenantCrudService.save(ac);
    }

    private InformationMgrAlertConfigEntity createExceptionAlertConfigEntity(Integer propertyId) {
        return createExceptionAlertConfigEntity(propertyId, null);
    }

    private InformationMgrAlertConfigEntity createExceptionAlertConfigEntity(Integer propertyId, Product product) {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setName(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
        alertTypeEntity.setEnabled(true);
        alertTypeEntity = tenantCrudService.findByExampleSingleResult(alertTypeEntity);
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService.findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", LevelType.ROOM_CLASS.getCode()).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService.findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        List<InformationMgrSubLevelEntity> subLevels = tenantCrudService.findByNamedQuery(InformationMgrSubLevelEntity.BY_EXCEPTION_LEVEL, QueryParameter.with("levelId", objExceptionLevelEntity.getId()).parameters());
        config.setPropertyId(propertyId);
        config.setAlertTypeEntity(alertTypeEntity);
        config.setCreatedByUserId(1);
        config.setDisabled(false);
        config.setExceptionLevel(objExceptionLevelEntity);
        config.setExceptionSubLevel(subLevels.get(0).getId());
        config.setExceptionSubType(objExceptionSubTypeEntity);
        config.setStartDate("Today+1");
        config.setEndDate("Today+1");
        config.setFrequency("1");
        config.setStatusId(1);
        config.setProduct(product);
        config.setThresholdOperator(RelationalOperator.GREATER_OR_EQUAL.getCode());
        config.setThresholdValue(BigDecimal.ZERO);
        config.setThresholdMetricType(MetricType.CURRENCY);
        multiPropertyCrudService().save(propertyId, config);
        return config;
    }

    private void createDecsionSupportData(AccomClass accomClass, AccomType accomType1, int inSufficientCompetitorFlag, int productId) {
        createDecsionSupportData(occupancyDate, accomClass, accomType1, inSufficientCompetitorFlag, productId);
    }

    private void createDecsionSupportData(LocalDate occupancyDate, AccomClass accomClass, AccomType accomType1, int inSufficientCompetitorFlag, int productId) {
        tenantCrudService.executeUpdateByNativeQuery("insert into PI_Decision_Support values" +
                "('" + occupancyDate.toString() + "'," + accomClass.getId() + "," + accomType1.getId() + "," + productId + ",2,200,'',''," + inSufficientCompetitorFlag + ",1,1,0,0,0,0,0,0,0,0,0,300,200,100)");
    }

    private void createDecsionSupportData(AccomClass accomClass, AccomType accomType1, int inSufficientCompetitorFlag) {
        createDecsionSupportData(accomClass, accomType1, inSufficientCompetitorFlag, 1);
    }

    private void createWebrate(Integer webrateTypeId, Integer dayAmount) {
        tenantCrudService.executeUpdateByNativeQuery("insert into Webrate values(1,GetDate(), 1, 3, 3, " + webrateTypeId + ", '" + occupancyDate.plusDays(dayAmount) + "','3','TestData','A','USD', '100.00', 1, 1, 1, getdate(), -1, '100.00')");
    }

    private void createWebrate() {
        createWebrate(1, 365);
    }

    private void createWebrateMappingData(AccomClass accomClass1, AccomClass accomClass2) {
        int webrateAccomTypeId1 = 98;
        int webrateAccomTypeId2 = 99;
        String qry = " SET IDENTITY_INSERT Webrate_Accom_Type ON insert into Webrate_Accom_Type([Webrate_Accom_Type_ID],[Property_ID],[Webrate_Accom_Name],[Webrate_Accom_Alias],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM])" +
                " values(" + webrateAccomTypeId1 + "," + PacmanWorkContextHelper.getPropertyId() + ",'" + accomClass1.getName() + "','" + accomClass1.getCode() + "',1,GETDATE(),1,GETDATE())," +
                "(" + webrateAccomTypeId2 + "," + PacmanWorkContextHelper.getPropertyId() + ",'" + accomClass2.getName() + "','" + accomClass2.getCode() + "',1,GETDATE(),1,GETDATE())" +
                " SET IDENTITY_INSERT Webrate_Accom_Type OFF ";
        tenantCrudService.executeUpdateByNativeQuery(qry);
        insertWebrateAccomClassMapping(webrateAccomTypeId1, accomClass1.getId());
        insertWebrateAccomClassMapping(webrateAccomTypeId2, accomClass2.getId());

    }

    private void createWebrateRankingAccomClass(final AccomClass accomClass) {
        createWebrateRankingAccomClass(accomClass, 1);
    }

    private void createWebrateRankingAccomClass(final AccomClass accomClass, int productId) {
        WebrateRankingAccomClass rankingAccomClass = new WebrateRankingAccomClass();
        rankingAccomClass.setWebrateRanking(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setAccomClass(accomClass);
        rankingAccomClass.setProductID(productId);
        rankingAccomClass.setWebrateRankingMonday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingTuesday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingWednesday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingThursday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingFriday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingSaturday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClass.setWebrateRankingSunday(UniqueWebrateRankingCreator.createWebrateRanking());
        tenantCrudService.save(rankingAccomClass);
    }

    private void createWebrateRankingAccomClassWithOvr(final AccomClass accomClass, Date startDate, Date endDate) {

        WebrateRankingAccomClassOverride rankingAccomClassOvr = new WebrateRankingAccomClassOverride();
        rankingAccomClassOvr.setAccomClass(accomClass);
        rankingAccomClassOvr.setProductID(1);
        rankingAccomClassOvr.setWebrateRankingStartDT(startDate);
        rankingAccomClassOvr.setWebrateRankingEndDT(endDate);
        rankingAccomClassOvr.setWebrateRankingOvrMonday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrTuesday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrWednesday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrThursday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrFriday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrSaturday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        rankingAccomClassOvr.setWebrateRankingOvrSunday(UniqueWebrateRankingCreator.createWebrateRankingMidRange());
        tenantCrudService.save(rankingAccomClassOvr);
    }

    private void insertWebrateAccomClassMapping(int webrateAccomTypeId, int accomClassID) {
        tenantCrudService.executeUpdateByNativeQuery("INSERT INTO dbo.Webrate_Accom_Class_Mapping(Webrate_Accom_Type_ID,Accom_Class_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)VALUES" +
                "(" + webrateAccomTypeId + "," + accomClassID + ",1, getdate(),1,getdate())");
    }
}
