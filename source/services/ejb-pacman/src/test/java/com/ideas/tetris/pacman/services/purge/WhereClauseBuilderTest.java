package com.ideas.tetris.pacman.services.purge;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.purge.dto.TablePurgeStrategyDto;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.sql.Date;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import static com.ideas.tetris.pacman.services.purge.TenantPurgeService.GET_TABLE_PURGE_STRATEGY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class WhereClauseBuilderTest extends AbstractG3JupiterTest {

    @Mock
    CrudService crudService;
    @Mock
    DateService dateService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @InjectMocks
    WhereClauseBuilder builder;


    @Test
    void testCreateTenantWhereClause() {
        String[] expectedWheresClauses = {
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE Persistent_Key IN (SELECT Persistent_Key FROM Reservation_Night WHERE %sDeparture_DT < DATEADD(YY,-2,'2018-12-03') AND Departure_DT < '2018-12-03')",
                "WHERE reservation_identifier IN (SELECT distinct reservation_identifier FROM Reservation_Night WHERE %sDEPARTURE_DT < DATEADD(YY,-2,'2018-12-03') AND DEPARTURE_DT < '2018-12-03')",
                "WHERE %sDEPARTURE_DT < DATEADD(YY,-2,'2018-12-03') AND DEPARTURE_DT < '2018-12-03'",
                "WHERE %sDEPARTURE_DT < DATEADD(YY,-2,'2018-12-03') AND DEPARTURE_DT < '2018-12-03'",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(YY,-5,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(YY,-5,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(YY,-5,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(YY,-5,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE %sEND_DT < DATEADD(YY,-5,'2018-12-03') AND END_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCAPTURE_DT < DATEADD(YY,-2,'2018-12-03') AND CAPTURE_DT < '2018-12-03'",
                "WHERE %sCAPTURE_DT < DATEADD(dd,-7,'2018-12-03') AND CAPTURE_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE Decision_Bar_Output_OVR_ID IN (SELECT Decision_Bar_Output_OVR_ID FROM Decision_Bar_Output_OVR WHERE %sArrival_DT < DATEADD(YY,-1,'2018-12-03') AND Arrival_DT < '2018-12-03')",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-7,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-7,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOccupancy_DT < DATEADD(YY,-1,'2018-12-03') AND Occupancy_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sLAST_UPLOAD_DTTM < DATEADD(YY,-1,'2018-12-03') AND LAST_UPLOAD_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, -10, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOccupancy_DT < DATEADD(YY,-1,'2018-12-03') AND Occupancy_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(dd,-10,'2018-12-03') AND CREATED_DTTM < '2018-12-03' and TASK_STATUS NOT IN ('IN_PROGRESS', 'PENDING', 'FAILED')",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sCreateDate_DTTM < DATEADD(dd,-10,'2018-12-03') AND CreateDate_DTTM < '2018-12-03'",
                "WHERE %sLast_Updated_DTTM < DATEADD(YY,-5,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(YY,-1,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-60,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(YY,-1,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-60,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sLast_Updated_DTTM < DATEADD(YY,-2,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(YY,-1,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(YY,-1,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sSnapshot_Date < DATEADD(YY,-1,'2018-12-03') AND Snapshot_Date < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(YY,-1,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-2,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03' and Revenue_Stream_ID IN (SELECT Revenue_Stream_ID FROM Revenue_Stream)",
                "WHERE %sLast_Updated_DTTM < DATEADD(dd,-30,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-60,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(YY,-1,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-2,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sForecast_As_Of_Date < DATEADD(YY,-5,'2018-12-03') AND Forecast_As_Of_Date < '2018-12-03'",
                "WHERE %sCreateDate_DTTM < DATEADD(YY,-2,'2018-12-03') AND CreateDate_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(YY,-1,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(YY,-3,'2018-12-03') AND CREATED_DTTM < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(YY,-3,'2018-12-03') AND CREATED_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(mm,-6,'2018-12-03') AND CREATED_DTTM < '2018-12-03'"
        };
        assertEquals(expectedWheresClauses.length, TenantPurgeEnum.values().length);  // Assume one expected per enum instance
        testCreateTenantWhereClause(expectedWheresClauses, false);
    }

    @Test
    void testCreateTenantWhereClauseWhenEnhancedG3PurgingProcessEnabled() {
        String[] expectedWhereClauses = getExpectedWhereClauses();
        assertEquals(expectedWhereClauses.length, TenantPurgeEnum.values().length);  // Assume one expected per enum instance
        testCreateTenantWhereClause(expectedWhereClauses, true);
    }

    @Test
    void testTenantWhereClauseFromTablePurgeStrategy() throws Exception {
        String[] expectedWhereClauses = getExpectedWhereClauses();
        ArrayList<String> list = getTenantTables();
        ZonedDateTime zdt = ZonedDateTime.of(2018, 12, 3, 0, 0, 0, 0, ZoneId.systemDefault());
        List<TablePurgeStrategyDto> tablePurgeStrategyDtos = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                QueryParameter.with("Category", "TENANT").parameters(), row -> getPurgeStrategyDto(row));

        for (boolean applyPropertyIdToWhereClause : Arrays.asList(true, false)) {
            int i = 0;
            for (String purgeEnum : list) {
                TablePurgeStrategyDto purgePolicy = tablePurgeStrategyDtos.stream()
                        .filter(dto -> dto.getTenantPurgeEnumName().equalsIgnoreCase(purgeEnum))
                        .findFirst().orElseThrow(()->new Exception("Could not find DTO for "+purgeEnum));
                Integer propertyId = (applyPropertyIdToWhereClause) ? 456 : null;
                String propertyIdConditional = (applyPropertyIdToWhereClause) ? "(PROPERTY_ID = 456) AND " : "";
                String selectQuery = String.format("SELECT DATEADD(DAY, 12, MIN(%s)) FROM %s", purgePolicy.getFieldToCompare(), purgePolicy.getTableToCompare()).toUpperCase();
                String expected = String.format(expectedWhereClauses[i], propertyIdConditional);
                when(dateService.getForecastWindowOffsetBDE()).thenReturn(3);
                when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate().withYear(2018).withMonthOfYear(12).withDayOfMonth(3));
                when(crudService.findByNativeQuerySingleResult(selectQuery, new HashMap<>())).thenReturn(new Date(zdt.toInstant().toEpochMilli()));  // ms for 12/3/2018

                String  actual = builder.createWhereClauseForDeletion(propertyId, purgePolicy);
                verify(crudService, times(1)).findByNativeQuerySingleResult(selectQuery, new HashMap<>());
                if(!expected.toUpperCase().equalsIgnoreCase(actual.toUpperCase())){
                    System.out.println(purgePolicy.getTenantPurgeEnumName());
                }
                assertEquals(expected.toUpperCase(), actual.toUpperCase());
                Mockito.reset(crudService, dateService);
                i++;
            }
        }
    }

    private TablePurgeStrategyDto getPurgeStrategyDto(Object[] row) {
        return new TablePurgeStrategyDto((String) row[0], (String) row[1], (Integer) row[2], (String) row[3], (String) row[4], (String) row[5], (String) row[6],
                (Boolean) row[7], (String) row[8], (Integer) row[9], (Boolean) row[10],(String) row[11]);
    }

    @Test
    void testDecisionWhereClauseFromTablePurgeStrategy() {

        String[] expectedWheresClauses = {"WHERE BUSINESS_DT < DATEADD(dd,-368,'2019-03-04') AND BUSINESS_DT < '1970-01-01'"};
        List<TablePurgeStrategyDto> tablePurgeStrategyDtos = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                QueryParameter.with("Category", "DECISION").parameters(),
                row -> getPurgeStrategyDto(row));
        assertEquals(expectedWheresClauses.length, tablePurgeStrategyDtos.size());

        int i = 0;
        for (TablePurgeStrategyDto purgePolicy : tablePurgeStrategyDtos) {
            when(dateService.getForecastWindowOffsetBDE()).thenReturn(3);
            when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate().withYear(2019).withMonthOfYear(3).withDayOfMonth(4));
            Date returnDate = new Date(new LocalDate().withYear(1970).withMonthOfYear(1).withDayOfMonth(1).toDate().getTime());

            when(crudService.findByNativeQuerySingleResult("SELECT DATEADD(DAY, 12, MIN(BUSINESS_DT)) FROM DECISION", new HashMap<>())).thenReturn(returnDate);
            String actual = builder.createWhereClauseForDeletion(null, purgePolicy);
            assertEquals(expectedWheresClauses[i++], actual);
        }
    }

    @Test
    void testInfoMgrCommentsWhereClauseFromTablePurgeStrategy() {
        java.time.LocalDate date = java.time.LocalDate.now();
        Optional<TablePurgeStrategyDto> tablePurgeStrategyDto = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                QueryParameter.with("Category", "INFO_MGR").parameters(), this::getPurgeStrategyDto)
                .stream().filter(dto->dto.getTenantPurgeEnumName().equalsIgnoreCase("Info_Mgr_Comments")).findFirst();

        when(dateService.getCaughtUpLocalDate()).thenReturn(JavaLocalDateUtils.toJodaLocalDate(date));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.INFO_MGR_ALERT_CATEGORIES_TO_PURGE)).thenReturn("Alert,Notification,Exception");
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.EXCLUDE_INFO_MGR_TYPE_IDS_PURGING)).thenReturn("-1");
        LinkedHashMap<Integer, Integer> map = new LinkedHashMap<>();
        map.put(12345, 1);
        when(crudService.findByNativeQuery(anyString(), any(), any(RowMapper.class))).thenReturn(List.of(map));
        String whereClause = builder.createWhereClauseForDeletion(null ,tablePurgeStrategyDto.get());

        assertEquals("WHERE Info_Mgr_History_ID in (select Info_Mgr_History_ID from Info_Mgr_History where INFO_MGR_INSTANCE_ID in (12345))", whereClause);
    }
    @Test
    void testInfoMgrInstanceWhereClauseFromTablePurgeStrategy() {
        java.time.LocalDate date = java.time.LocalDate.now();
        Optional<TablePurgeStrategyDto> tablePurgeStrategyDto = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                        QueryParameter.with("Category", "INFO_MGR").parameters(), this::getPurgeStrategyDto)
                .stream().filter(dto->dto.getTenantPurgeEnumName().equalsIgnoreCase("Info_Mgr_Instance")).findFirst();

        when(dateService.getCaughtUpLocalDate()).thenReturn(JavaLocalDateUtils.toJodaLocalDate(date));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.INFO_MGR_ALERT_CATEGORIES_TO_PURGE)).thenReturn("Alert,Notification,Exception");
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.EXCLUDE_INFO_MGR_TYPE_IDS_PURGING)).thenReturn("-1");
        LinkedHashMap<Integer, Integer> map = new LinkedHashMap<>();
        map.put(12345, 1);
        map.put(12346, 2);
        map.put(12347, 3);
        when(crudService.findByNativeQuery(anyString(), any(), any(RowMapper.class))).thenReturn(List.of(map));

        String whereClause = builder.createWhereClauseForDeletion(null, tablePurgeStrategyDto.get());

        assertEquals("WHERE INFO_MGR_INSTANCE_ID in (12345,12346,12347)", whereClause);
    }

    @Test
    void testInfoMgrInstanceStepStateWhereClauseFromTablePurgeStrategy() {
        java.time.LocalDate date = java.time.LocalDate.now();
        Optional<TablePurgeStrategyDto> tablePurgeStrategyDto = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                        QueryParameter.with("Category", "INFO_MGR").parameters(), this::getPurgeStrategyDto)
                .stream().filter(dto->dto.getTenantPurgeEnumName().equalsIgnoreCase("Info_Mgr_Instance_Step_State")).findFirst();

        when(dateService.getCaughtUpLocalDate()).thenReturn(JavaLocalDateUtils.toJodaLocalDate(date));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.INFO_MGR_ALERT_CATEGORIES_TO_PURGE)).thenReturn("Alert,Notification,Exception");
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.EXCLUDE_INFO_MGR_TYPE_IDS_PURGING)).thenReturn("-1");
        LinkedHashMap<Integer, Integer> map = new LinkedHashMap<>();
        map.put(12345, 1);
        map.put(12346, 2);
        map.put(12347, 3);
        when(crudService.findByNativeQuery(anyString(), any(), any(RowMapper.class))).thenReturn(List.of(map));
        String whereClause = builder.createWhereClauseForDeletion(null, tablePurgeStrategyDto.get());

        assertEquals("WHERE INFO_MGR_INSTANCE_ID in (12345,12346,12347)", whereClause);
    }
    @Test
    void testOperaWhereClauseFromTablePurgeStrategy() {
        String[] expectedWheresClauses = {
                "WHERE DATA_LOAD_METADATA_ID in ('')","WHERE DATA_LOAD_METADATA_ID in ('')","WHERE DATA_LOAD_METADATA_ID in ('')",
                "WHERE DATA_LOAD_METADATA_ID in ('')","WHERE DATA_LOAD_METADATA_ID in ('')","WHERE DATA_LOAD_METADATA_ID in ('')",
                "WHERE DATA_LOAD_METADATA_ID in ('')","WHERE DATA_LOAD_METADATA_ID in ('') AND INCOMING_FILE_TYPE_CODE='YC'"
        };
        List<TablePurgeStrategyDto> tablePurgeStrategyDto = globalCrudService().findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                        QueryParameter.with("Category", "OPERA").parameters(), this::getPurgeStrategyDto);

        assertEquals(expectedWheresClauses.length, tablePurgeStrategyDto.size());

        int i = 0;
        for (TablePurgeStrategyDto purgePolicy : tablePurgeStrategyDto) {
            String expected = expectedWheresClauses[i];
            lenient().when(dateService.getForecastWindowOffsetBDE()).thenReturn(3);
            when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate().withYear(2019).withMonthOfYear(3).withDayOfMonth(4));
            lenient().when(crudService.findByNativeQuerySingleResult("SELECT DATEADD(DAY, 10, MIN(BUSINESS_DT)) FROM DECISION", new HashMap<>())).thenReturn(new java.sql.Date(0));

            String actual = builder.createWhereClauseForDeletion(null,purgePolicy);
            assertEquals(expected, actual);
            actual = builder.createWhereClauseForDeletion(123,purgePolicy);
            assertEquals(expected, actual);
            i++;
        }
    }

    private ArrayList<String> getTenantTables() {
        return new ArrayList<>(Arrays.asList(
                "ACCOM_ACTIVITY", "RESERVATION_NIGHT_CHANGE", "POST_DEPARTURE_REVENUE", "RESERVATION_NIGHT", "RESTORED_NO_SHOW_RESERVATION",
                "OPERA_GROUP_BLOCK_CODE", "PACE_GROUP_MASTER", "PACE_GROUP_BLOCK", "GROUP_BLOCK", "GROUP_MASTER",
                "MKT_ACCOM_ACTIVITY", "PACE_ACCOM_ACTIVITY", "PACE_MKT_ACTIVITY", "PACE_TOTAL_ACTIVITY", "PACE_WEBRATE",
                "PACE_WEBRATE_DIFFERENTIAL", "TOTAL_ACTIVITY", "CR_ACCOM_ACTIVITY", "CR_MKT_ACCOM_ACTIVITY", "CR_TOTAL_ACTIVITY",
                "D360_BOOKING_SUMMARY_PACE", "D360_MKT_HIST_CAPACITY", "HD360_BOOKING_SUMMARY_PACE", "HD360_TRANSITORY_BOOKING_SUMMARY_PACE",
                "HD360_MKT_HIST_CAPACITY", "HOTEL_MKT_ACCOM_ACTIVITY", "PACE_CR_ACCOM_ACTIVITY", "WEBRATE", "DECISION_BAR_OUTPUT_OVR_DETAILS",
                "DECISION_ACK_STATUS", "MP_DECISION_ACK_STATUS", "ARRIVAL_DEMAND_FCST_OVR", "ARRIVAL_DEMAND_FCST", "CP_UNQUALIFIED_DEMAND_FCST_PRICE",
                "CP_DECISION_BAR_OUTPUT_OVR", "CP_DECISION_BAR_OUTPUT", "CP_DECISION_BAR_NOVR", "CP_DECISION_BAR_NOVR_DETAILS",
                "DECISION_BAR_OUTPUT_OVR", "DECISION_BAR_OUTPUT", "DECISION_DAILYBAR_OUTPUT", "DECISION_COW_VALUE_OVR", "DECISION_FPLOS_BY_RANK",
                "DECISION_LRV", "DECISION_OVRBK_ACCOM_OVR", "DECISION_OVRBK_ACCOM", "DECISION_OVRBK_PROPERTY_OVR", "DECISION_OVRBK_PROPERTY",
                "VP_OVRBK_PROPERTY_SPLIT_RATIO", "DECISION_QUALIFIED_FPLOS", "DECISION_RESTRICT_HIGHEST_BAR_OVR", "NOTES",
                "OCCUPANCY_DEMAND_FCST_OVR", "OCCUPANCY_DEMAND_FCST", "OCCUPANCY_FCST", "OCCUPANCY_FCST_NOVR",
                "PACE_ACCOM_OCCUPANCY_FCST", "CP_PACE_DECISION_BAR_OUTPUT", "PACE_BAR_OUTPUT", "PACE_BAR_OUTPUT_UPLOAD", "PACE_DAILYBAR_OUTPUT",
                "PACE_FPLOS_BY_RANK", "PACE_LRV", "PACE_MKT_OCCUPANCY_FCST", "PACE_OVRBK_ACCOM", "PACE_OVRBK_ACCOM_UPLOAD",
                "PACE_OVRBK_PROPERTY", "PACE_OVRBK_PROPERTY_UPLOAD", "PACE_QUALIFIED_FPLOS", "UNQUALIFIED_DEMAND_FCST_PRICE", "WASH_FCST",
                "WASH_FORECAST_GROUP_FCST_OVR", "WASH_FORECAST_GROUP_FCST", "WASH_IND_GROUP_FCST_OVR", "WASH_IND_GROUP_FCST",
                "WASH_PROPERTY_FCST", "ARR_DEP_FCST", "DECISION_FPLOS_BY_HIERARCHY", "DECISION_FPLOS_BY_ROOMTYPE", "DECISION_LRA_FPLOS",
                "DECISION_LRA_MINLOS", "DECISION_LRV_AT", "DECISION_MINLOS", "DECISION_UPLOAD_DATE_TO_EXTERNAL_SYSTEM",
                "OCC_FCST_ORG", "PACE_ACCOM_OCCUPANCY_FCST_NOTIFICATION", "PACE_BAR_OUTPUT_NOTIFICATION",
                "PACE_DECISION_LRA_FPLOS", "PACE_DECISION_LRA_MINLOS", "PACE_FPLOS_BY_HIERARCHY", "PACE_FPLOS_BY_ROOMTYPE",
                "PACE_LRV_AT", "PACE_LRV_NOTIFICATION", "PACE_MINLOS", "PACE_MKT_OCCUPANCY_FCST_NOTIFICATION",
                "PACE_OVRBK_ACCOM_NOTIFICATION", "PACE_OVRBK_PROPERTY_NOTIFICATION", "DECISION_RESTRICT_HIGHEST_BAR", "FS_FCST",
                "FS_FCST_EVAL_OVERRIDE", "FS_FCST_OVERRIDE", "REMOTE_TASK", "PP_OCCUPANCY_FCST", "GROUP_FINAL_FORECAST_OVR",
                "UNEXPECTED_DEMAND_NOTIFICATION_DETAILS", "DECISIONS_DELIVERED", "SYNC_FLAGS_AUD", "GROUP_FLOOR_OVR", "CP_CFG_BASE_AT",
                "CP_CFG_BASE_AT_DRAFT", "CP_CFG_OFFSET_AT", "CP_CFG_OFFSET_AT_DRAFT","CP_CFG_OFFSET_AT_FLOOR_CEILING", "GROUP_FLOOR_OVR_ALERT_DETAILS", "GROUP_FLOOR_OVR_CONSTRAINING_BAR",
                "GRP_PRC_CFG_BASE_AT", "ACCOM_TYPE_SUPPLEMENT", "GFF_FG_OVR", "GFF_FG_OVR_AUD", "DECISION_ANOMALY_SMOKE_TEST_RESULT",
                "MANUAL_RESTRICTION_PROPERTY_OVR", "PACE_MANUAL_RESTRICTION_PROPERTY_OVR", "MANUAL_RESTRICTION_ACCOM_OVR",
                "PACE_MANUAL_RESTRICTION_ACCOM_OVR", "REVENUE_STREAM_DETAIL", "SCHEDULEDREPORT_DELIVERY_AUDIT", "ASYNC_REPORTS",
                "MVCR_RATE_AT", "DECISION_DAILYBAR_OUTPUT_NONHILTONCRS", "PACE_DAILYBAR_OUTPUT_NONHILTONCRS", "PRICE_TEST_CFG_SCALE",
                "CENTRAL_RMS_PRICE_DATA", "CENTRAL_RMS_COMP_OUTLIER", "OPERATIONAL_FCST", "CENTRAL_RMS_TRAN_DMD_SCALE",
                "CP_PACE_DECISION_BAR_OUTPUT_DIFFERENTIAL", "OCCUPANCY_FCST_EXT", "CP_DECISION_BAR_OUT_EXT",
                "OCCUPANCY_DEMAND_FCST_EXT", "PACE_FROM_SERVICE", "PACE_OPERATIONAL_MKT_FCST", "PACE_PROFIT_ADJUSTMENT",
                "DECISION_GP_INV_LIMIT_OVR", "DECISION_GP_INV_LIMIT_OVR_AUD", "REFERENCE_PRICE_LATEST", "DECISION_GP_INV_LIMIT",
                "PACE_GP_INV_LIMIT_UPLOAD", "VP_GP_INV_LIMIT_PROPERTY_SPLIT_RATIO", "MP_DECISION_BAR_OVR", "MP_DECISION_BAR", "MP_PACE_DECISION_BAR_DIFFERENTIAL"
        ));
    }

    private String[] getExpectedWhereClauses() {
        String[] expectedWhereClauses = {
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE Persistent_Key IN (SELECT Persistent_Key FROM Reservation_Night WHERE %sDeparture_DT < DATEADD(dd,-1200,'2018-12-03') AND Departure_DT < '2018-12-03')",
                "WHERE reservation_identifier IN (SELECT distinct reservation_identifier FROM Reservation_Night WHERE %sDEPARTURE_DT < DATEADD(dd,-1200,'2018-12-03') AND DEPARTURE_DT < '2018-12-03')",
                "WHERE %sDEPARTURE_DT < DATEADD(dd,-1200,'2018-12-03') AND DEPARTURE_DT < '2018-12-03'",
                "WHERE %sDEPARTURE_DT < DATEADD(dd,-365,'2018-12-03') AND DEPARTURE_DT < '2018-12-03'",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(dd,-1200,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(dd,-1200,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(dd,-1200,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM WHERE %sEND_DT < DATEADD(dd,-1200,'2018-12-03') AND END_DT < '2018-12-03')",
                "WHERE %sEND_DT < DATEADD(dd,-1200,'2018-12-03') AND END_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-800,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-800,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-800,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCAPTURE_DT < DATEADD(dd,-365,'2018-12-03') AND CAPTURE_DT < '2018-12-03'",
                "WHERE %sCAPTURE_DT < DATEADD(dd,-7,'2018-12-03') AND CAPTURE_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-1200,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-800,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE Decision_Bar_Output_OVR_ID IN (SELECT Decision_Bar_Output_OVR_ID FROM Decision_Bar_Output_OVR WHERE %sArrival_DT < DATEADD(dd,-365,'2018-12-03') AND Arrival_DT < '2018-12-03')",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-7,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-7,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOccupancy_DT < DATEADD(dd,-365,'2018-12-03') AND Occupancy_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sLAST_UPLOAD_DTTM < DATEADD(dd,-365,'2018-12-03') AND LAST_UPLOAD_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sDecision_Id not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= DATEADD(dd,-365, '2018-12-03'))",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOccupancy_DT < DATEADD(dd,-365,'2018-12-03') AND Occupancy_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(dd,-10,'2018-12-03') AND CREATED_DTTM < '2018-12-03' and TASK_STATUS NOT IN ('IN_PROGRESS', 'PENDING', 'FAILED')",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sCreateDate_DTTM < DATEADD(dd,-10,'2018-12-03') AND CreateDate_DTTM < '2018-12-03'",
                "WHERE %sLast_Updated_DTTM < DATEADD(dd,-1825,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-365,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-60,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-365,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-60,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sLast_Updated_DTTM < DATEADD(dd,-800,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-365,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-365,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sSnapshot_Date < DATEADD(dd,-365,'2018-12-03') AND Snapshot_Date < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-365,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03' and Revenue_Stream_ID IN (SELECT Revenue_Stream_ID FROM Revenue_Stream)",
                "WHERE %sLast_Updated_DTTM < DATEADD(dd,-30,'2018-12-03') AND Last_Updated_DTTM < '2018-12-03'",
                "WHERE %sCreated_DTTM < DATEADD(dd,-60,'2018-12-03') AND Created_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sOCCUPANCY_DATE < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DATE < '2018-12-03'",
                "WHERE %sEnd_Date < DATEADD(dd,-365,'2018-12-03') AND End_Date < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-15,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sForecast_As_Of_Date < DATEADD(dd,-1825,'2018-12-03') AND Forecast_As_Of_Date < '2018-12-03'",
                "WHERE %sCreateDate_DTTM < DATEADD(dd,-365,'2018-12-03') AND CreateDate_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sARRIVAL_DT < DATEADD(dd,-365,'2018-12-03') AND ARRIVAL_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(dd,-365,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(YY,-3,'2018-12-03') AND CREATED_DTTM < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(YY,-3,'2018-12-03') AND CREATED_DTTM < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-3,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sOCCUPANCY_DT < DATEADD(YY,-1,'2018-12-03') AND OCCUPANCY_DT < '2018-12-03'",
                "WHERE %sCREATED_DTTM < DATEADD(mm,-6,'2018-12-03') AND CREATED_DTTM < '2018-12-03'"
        };
        return expectedWhereClauses;
    }


    private void testCreateTenantWhereClause(String[] expectedWheresClauses, boolean isEnhancedG3PurgingProcessEnabled) {
        ZonedDateTime zdt = ZonedDateTime.of(2018, 12, 3, 0, 0, 0, 0, ZoneId.systemDefault());
        List<TablePurgeStrategyDto> tablePurgeStrategyDtoList = null;
        if (isEnhancedG3PurgingProcessEnabled) {
            tablePurgeStrategyDtoList = globalCrudService().findByNativeQuery("SELECT Tenant_Purge_Enum_Name, Category_Name, Number_Of_Days_To_Persist, Is_Purging_Enabled " +
                            "FROM Table_Purge_Strategy as tps INNER JOIN Purge_Category as pc " +
                            "ON tps.Purge_Category_Id = pc.Purge_Category_Id",
                    null,
                    row -> new TablePurgeStrategyDto((String) row[0], (String) row[1], (Integer) row[2],(Boolean) row[3]));
        }

        for (boolean applyPropertyIdToWhereClause : Arrays.asList(true, false)) {  // with and without a property_id provided
            int i = 0;
            for (TenantPurgeEnum purgePolicy : TenantPurgeEnum.values()) {
                Integer propertyId = (applyPropertyIdToWhereClause) ? 456 : null;
                String propertyIdConditional = (applyPropertyIdToWhereClause) ? "(PROPERTY_ID = 456) AND " : "";
                String selectQuery = String.format("SELECT DATEADD(DAY, 12, MIN(%s)) FROM %s", purgePolicy.getFieldToCompare(), purgePolicy.getTableToCompare());
                String expected = String.format(expectedWheresClauses[i], propertyIdConditional);
                when(dateService.getForecastWindowOffsetBDE()).thenReturn(3);
                when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate().withYear(2018).withMonthOfYear(12).withDayOfMonth(3));
                when(crudService.findByNativeQuerySingleResult(selectQuery, new HashMap<>())).thenReturn(new Date(zdt.toInstant().toEpochMilli()));  // ms for 12/3/2018

                String actual;
                if (CollectionUtils.isNotEmpty(tablePurgeStrategyDtoList)) {
                    TablePurgeStrategyDto purgableDto = tablePurgeStrategyDtoList.stream()
                            .filter(dto -> dto.getTenantPurgeEnumName().equalsIgnoreCase(purgePolicy.toString()))
                            .findFirst().orElse(null);
                    actual = builder.createWhereClause(propertyId, purgePolicy, purgableDto);
                } else {
                    actual = builder.createWhereClause(propertyId, purgePolicy, null);
                }

                verify(crudService, times(1)).findByNativeQuerySingleResult(selectQuery, new HashMap<>());
                assertEquals(expected, actual);
                Mockito.reset(crudService, dateService);
                i++;
            }
        }
    }
}
