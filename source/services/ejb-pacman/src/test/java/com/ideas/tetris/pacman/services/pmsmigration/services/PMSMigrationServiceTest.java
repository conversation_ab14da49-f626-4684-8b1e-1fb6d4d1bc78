package com.ideas.tetris.pacman.services.pmsmigration.services;

import com.google.common.collect.ImmutableMap;
import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.catchup.CatchupParameters;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobExecution;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegRecodingConfigDto;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.pmsmigration.PMSMigrationTestUtil;
import com.ideas.tetris.pacman.services.pmsmigration.alert.PMSMigrationAlertDetailsBuilder;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.PMSRevampAMSDataService;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.ActivityDelta;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.PostMigrationOldInhouseReservation;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.accom.AccomActivityDelta;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.accom.AccomActivityPmsBkp;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.total.TotalActivityDelta;
import com.ideas.tetris.pacman.services.pmsmigration.datacomparison.total.TotalActivityPmsBkp;
import com.ideas.tetris.pacman.services.pmsmigration.dto.PMSMigrationMappingImportDTO;
import com.ideas.tetris.pacman.services.pmsmigration.dto.PMSMigrationRateCodeMktSegMappingDTO;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationMapping;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState;
import com.ideas.tetris.pacman.services.property.PropertyExcludeDatesService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.property.dto.PropertyExcludedDates;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteAgentConfigService;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteTaskService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.AgentProperty;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgent;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.CatchupMode;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.ws.rs.core.Response;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.BUSINESS_TYPE_GROUP;
import static com.ideas.tetris.pacman.common.constants.Constants.NO;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_CCFG_REQUIRED_EXCEPTION_MESSAGE;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_FAILED_JOB_EXECUTION_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_POST_BACKFILL_DATA_VALIDATION_EXCEPTION_MESSAGE;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_SYSTEM_DATA_VALIDATION_EXCEPTION_MESSAGE;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_UNASSIGNED_ROOM_TYPES_EXCEPTION_MESSAGE;
import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_VALIDATE_REQUIRED_MAPPINGS_EXCEPTION_MESSAGE;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.YES;
import static com.ideas.tetris.pacman.services.pmsmigration.PMSMigrationTestUtil.setOf;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType.MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType.RATE_CODE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_COMMIT_FG_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_COMMIT_FG_REQUIRED;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_DATA_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_DATA_VALIDATION_REQUIRED;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_FORECAST_AND_DECISION_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_INBOUND_CONFIGURATION_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_MAPPINGS_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_MAPPINGS_VALIDATION_REQUIRED;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_MIGRATION_COMPLETE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_MIGRATION_NOT_STARTED;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_MIGRATION_READY;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_POST_BACKFILL_DATA_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationState.PMS_POST_BACKFILL_DATA_VALIDATION_REQUIRED;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.ACCOM_ACTIVITY;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.AMS_SUFFIX;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.CLIENT_ID_KEY;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.CLIENT_LEVEL_MAPPING_NOT_PROVIDED_FOR_PROPERTY;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.CONFIGURE_INBOUND_ALERT_DESCRIPTION;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.GROUP_CODE;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.PMS_CRS_MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.PMS_MIGRATION_MAPPING_TEMPLATE_VALIDATION_ERROR;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.PMS_MIGRATION_NEW_AMS_ATTR_MISSING_ERROR;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.POST_BACKFILL_DATA_VALIDATION_XLSX;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.PROPERTY_ID_KEY;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.ROOM_TYPE;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.TOTAL_ACTIVITY;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.VENDOR_CONFIGURATION_BEFORE_RESOLVING_THIS_ALERT;
import static com.ideas.tetris.platform.services.Stage.TWO_WAY;
import static java.text.MessageFormat.format;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyListOf;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PMSMigrationServiceTest {

    public static final String RT_1 = "RT1";

    public static final String RT_2 = "RT2";

    private static final String RT_3 = "RT3";

    private static final String GC_1 = "GC1";

    private static final String GC_2 = "GC2";

    private static final String GC_3 = "GC3";

    private static final String BSTN = "BSTN";

    private static final String H_2 = "H2";

    private static final Integer H_2_ID = 6;

    private static final String _11403 = "11403";

    private static final String PACMAN_BSTN_H2 = "pacman.BSTN.H2";

    private static final String NGI_DATA_VALIDATION_URL = "<ul><li><a target=\"_blank\" href=\"_HOST_URL_/solutions/rest/pmsMigrationTools/newSystemReservationsDiscrepancyReport?propertyId=6\">view.reservation.discrepancy.report</a></li></ul>";

    private static final String POST_BACKFILL_DATA_VALIDATION_URL = "<a target=\"_blank\" href=\"_HOST_URL_/solutions/rest/pmsMigrationTools/postBackFillDataValidationReport?propertyId=6\">view.report</a>";

    private static final List<String> totalActivityHeaderList = Arrays.asList("Occupancy_DT", "Old_Total_Accom_Capacity", "New_Total_Accom_Capacity", "Total_Accom_Capacity_Delta", "Total_Accom_Capacity_Delta_Percent", "Old_Rooms_Sold", "New_Rooms_Sold", "Rooms_Sold_Delta", "Rooms_Sold_Delta_Percent", "Old_Rooms_Not_Avail_Maint", "New_Rooms_Not_Avail_Maint", "Rooms_Not_Avail_Maint_Delta", "Rooms_Not_Avail_Maint_Delta_Percent", "Old_Rooms_Not_Avail_Other", "New_Rooms_Not_Avail_Other", "Rooms_Not_Avail_Other_Delta", "Rooms_Not_Avail_Other_Delta_Percent", "Old_Arrivals", "New_Arrivals", "Arrivals_Delta", "Arrivals_Delta_Percent", "Old_Departures", "New_Departures", "Departures_Delta", "Departures_Delta_Percent", "Old_Cancellations", "New_Cancellations", "Cancellations_Delta", "Cancellations_Delta_Percent", "Old_NoShows", "New_NoShows", "NoShows_Delta", "NoShows_Delta_Percent", "Old_Room_Revenue", "New_Room_Revenue", "Room_Revenue_Delta", "Room_Revenue_Delta_Percent", "Old_Food_Revenue", "New_Food_Revenue", "Food_Revenue_Delta", "Food_Revenue_Delta_Percent", "Old_Total_Revenue", "New_Total_Revenue", "Total_Revenue_Delta", "Total_Revenue_Delta_Percent");

    private static final List<String> accomActivityHeaderList = Arrays.asList("Occupancy_DT", "Old_Accom_Type_Codes", "New_Accom_Type_Code", "Old_Accom_Capacity", "New_Accom_Capacity", "Accom_Capacity_Delta", "Accom_Capacity_Delta_Percent", "Old_Rooms_Sold", "New_Rooms_Sold", "Rooms_Sold_Delta", "Rooms_Sold_Delta_Percent", "Old_Rooms_Not_Avail_Maint", "New_Rooms_Not_Avail_Maint", "Rooms_Not_Avail_Maint_Delta", "Rooms_Not_Avail_Maint_Delta_Percent", "Old_Rooms_Not_Avail_Other", "New_Rooms_Not_Avail_Other", "Rooms_Not_Avail_Other_Delta", "Rooms_Not_Avail_Other_Delta_Percent", "Old_Arrivals", "New_Arrivals", "Arrivals_Delta", "Arrivals_Delta_Percent", "Old_Departures", "New_Departures", "Departures_Delta", "Departures_Delta_Percent", "Old_Cancellations", "New_Cancellations", "Cancellations_Delta", "Cancellations_Delta_Percent", "Old_NoShows", "New_NoShows", "NoShows_Delta", "NoShows_Delta_Percent", "Old_Room_Revenue", "New_Room_Revenue", "Room_Revenue_Delta", "Room_Revenue_Delta_Percent", "Old_Food_Revenue", "New_Food_Revenue", "Food_Revenue_Delta", "Food_Revenue_Delta_Percent", "Old_Total_Revenue", "New_Total_Revenue", "Total_Revenue_Delta", "Total_Revenue_Delta_Percent");
    private static final Map<String, Set<String>> MKT_SEG_WISE_RATE_CODES = mutate(ImmutableMap.of(
            PMSMigrationTestUtil.MS_1, setOf(PMSMigrationTestUtil.RC_1),
            PMSMigrationTestUtil.MS_2, setOf(PMSMigrationTestUtil.RC_2),
            PMSMigrationTestUtil.MS_3, setOf(PMSMigrationTestUtil.RC_3)));

    private static final Map<String, Set<String>> MKT_SEG_WISE_RATE_CODES_MISSING_RC3 = mutate(ImmutableMap.of(
            PMSMigrationTestUtil.MS_1, setOf(PMSMigrationTestUtil.RC_1),
            PMSMigrationTestUtil.MS_2, setOf(PMSMigrationTestUtil.RC_2),
            PMSMigrationTestUtil.MS_3, setOf()));

    private static final Map<String, Set<String>> MKT_SEG_WISE_RATE_CODES_EXTRA_MS4 = mutate(ImmutableMap.of(
            PMSMigrationTestUtil.MS_1, setOf(PMSMigrationTestUtil.RC_1),
            PMSMigrationTestUtil.MS_2, setOf(PMSMigrationTestUtil.RC_2),
            PMSMigrationTestUtil.MS_3, setOf(PMSMigrationTestUtil.RC_3),
            PMSMigrationTestUtil.MS_4, setOf(PMSMigrationTestUtil.RC_4)));
    private static final Long FAILED_JOB_ID = 1L;

    private Date caughtUpDate;

    private Client clientBSTN;

    private Property propertyH2;

    @Mock
    private CrudService globalCrudService;

    @Mock
    private AlertService alertService;

    @Mock
    private PropertyStageChangeService propertyStageChangeService;

    @Mock
    private PropertyExcludeDatesService propertyExcludeDatesService;

    @Mock
    private DateService dateService;

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private JobServiceLocal jobService;

    @Mock
    private PropertyService propertyService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private RemoteTaskService remoteTaskService;

    @Mock
    private RemoteAgentConfigService remoteAgentConfigService;

    @Mock
    private AccommodationService accommodationService;

    @Mock
    private PMSRevampAMSDataService pmsRevampAMSDataService;

    @Mock
    private JobMonitorService jobMonitorService;

    @Mock
    private MarketSegmentComponent marketSegmentComponent;

    @Mock
    private PMSMigrationMappingService pmsMigrationMappingService;

    @Mock
    private PMSMigrationBackupService pmsMigrationBackupService;

    @Mock
    private MktSegRecodingService mktSegRecodingService;

    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @Spy
    @InjectMocks
    private PMSMigrationService pmsMigrationService;

    private static Map<String, Set<String>> mutate(ImmutableMap<String, Set<String>> immutableMap) {
        return new HashMap<>(immutableMap);
    }

    @BeforeEach
    public void setUp() {
        setupWorkContext();
        caughtUpDate = new LocalDate(2018, 5, 11).toDate();
        clientBSTN = new Client();
        clientBSTN.setId(TestClient.BSTN.getId());
        clientBSTN.setCode(BSTN);
        propertyH2 = new Property();
        propertyH2.setClient(clientBSTN);
        propertyH2.setId(TestProperty.H2.getId());
        propertyH2.setCode(H_2);
        pmsMigrationService.setPmsMigrationAlertDetailsBuilder(new PMSMigrationAlertDetailsBuilder());
    }

    private void setupWorkContext() {
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(TestProperty.H2.getId());
        workContext.setClientCode(BSTN);
        workContext.setPropertyCode(H_2);
        workContext.setUserId(_11403);
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    @Test
    public void getAllUnassignedAccomTypes() {
        pmsMigrationService.getAllUnassignedAccomTypes();
        verify(accommodationService).getAllUnassignedAccomTypes();
    }

    @Test
    public void checkForUnassignedRoomTypeAndGenerateAlert_NoAlert() {
        when(accommodationService.getAllUnassignedAccomTypes()).thenReturn(Collections.emptyList());
        pmsMigrationService.checkForUnassignedRoomTypeAndGenerateAlert(getPMSMigrationTestConfig(PMS_DATA_VALIDATION_DONE), FAILED_JOB_ID);
        verify(pmsMigrationService, times(0)).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());
    }

    @Test
    public void checkForUnassignedRoomTypeAndGenerateAlert() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());
        when(accommodationService.getAllUnassignedAccomTypes()).thenReturn(Collections.singletonList(getAccomType("A")));
        pmsMigrationService.checkForUnassignedRoomTypeAndGenerateAlert(getPMSMigrationTestConfig(PMS_DATA_VALIDATION_DONE), FAILED_JOB_ID);
        verify(pmsMigrationService).generateAlertAndHaltProcessing(eq(AlertType.PMSMigrationUnassignedRoomTypes),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                eq("A"),
                eq(PMS_MIGRATION_UNASSIGNED_ROOM_TYPES_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_MAPPINGS_VALIDATION_DONE)
        );
    }

    @Test
    public void shouldStartExhaustivePMSMigration() {
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.ConfigureInbound, true);
        when(alertService.getAlertType(AlertType.ConfigureInbound.getName())).thenReturn(alertTypeEntity);
        PMSMigrationConfig config = new PMSMigrationConfig();
        config.setProperty(propertyH2);
        config.setOldSysDate(caughtUpDate);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_MIGRATION_STATE), any())).thenReturn(config);
        when(propertyStageChangeService.getCurrentStage(anyInt())).thenReturn(TWO_WAY);
        assertNull(config.getOriginalStage());

        pmsMigrationService.startExhaustivePMSMigration();
        assertEquals(TWO_WAY, config.getOriginalStage());
        assertEquals(PMSMigrationState.PMS_MIGRATION_READY, config.getMigrationState());

        verify(globalCrudService).save(config);
        verifyNoMoreInteractions(alertService);
        verify(propertyStageChangeService).changeStage(TestProperty.H2.getId(), Stage.DORMANT, null, "Changing stage to Dormant as part of Exhaustive PMS Migration start step.");
    }

    @Test
    public void generateAndDownloadPostBackFillDataReport() throws IOException, InvalidFormatException {
        when(tenantCrudService.<TotalActivityDelta>findByNamedQuery(eq(TotalActivityDelta.FIND_DELTA_SUMMARY_EXCEEDING_ROW_THRESHOLD_LEVEL), anyMap())).thenReturn(getTotalActivityData());
        when(tenantCrudService.<AccomActivityDelta>findByNamedQuery(eq(AccomActivityDelta.FIND_DELTA_SUMMARY_EXCEEDING_ROW_THRESHOLD_LEVEL), anyMap())).thenReturn(getAccomActivityData());
        Response response = pmsMigrationService.generateAndDownloadPostBackFillDataReport(5);
        assertNotNull(response);
        Workbook workbook = getWorkbookFromResponse(response);
        assertEquals(TOTAL_ACTIVITY, workbook.getSheetName(0));
        assertEquals(ACCOM_ACTIVITY, workbook.getSheetName(1));
        validateActivitySheet(workbook, 0, totalActivityHeaderList, 1);
        validateActivitySheet(workbook, 1, accomActivityHeaderList, 3);
    }

    private Workbook getWorkbookFromResponse(Response response) throws IOException, InvalidFormatException {
        File file = (File) response.getEntity();
        FileInputStream fis = new FileInputStream(file);
        Workbook workbook = WorkbookFactory.create(fis);
        fis.close();
        assertEquals(2, workbook.getNumberOfSheets());
        assertEquals(POST_BACKFILL_DATA_VALIDATION_XLSX, file.getName());
        file.delete();
        return workbook;
    }

    private void validateActivitySheet(Workbook workbook, int sheetNo, List<String> headerList, int numericCellStart) {
        Sheet totalActivitySheet = workbook.getSheetAt(sheetNo);
        assertEquals(3, totalActivitySheet.getPhysicalNumberOfRows());
        Row row0 = totalActivitySheet.getRow(0);
        for (int i = 0; i < headerList.size(); i++) {
            assertEquals(headerList.get(i), row0.getCell(i).getStringCellValue());
        }
        Row row1 = totalActivitySheet.getRow(1);
        assertEquals("01-Jan-2019", row1.getCell(0).getStringCellValue());
        for (int i = numericCellStart, j = 1; i < headerList.size(); i++, j++) {
            assertEquals(j, row1.getCell(i).getNumericCellValue(), 0);
        }
    }


    private List<AccomActivityDelta> getAccomActivityData() {
        List<AccomActivityDelta> list = new ArrayList<>();
        list.add(getAccomActivityDto("2019-01-01"));
        list.add(getAccomActivityDto("2019-01-02"));
        return list;
    }

    private AccomActivityDelta getAccomActivityDto(String date) {
        AccomActivityDelta deltaDto = new AccomActivityDelta();
        deltaDto.setOccupancyDate(new LocalDate(date).toDate());
        deltaDto.setOldAccomTypeCodes("Old_code");
        deltaDto.setNewAccomTypeCode("New_code");
        deltaDto.setOldAccomCapacity(new BigDecimal(1));
        deltaDto.setNewAccomCapacity(new BigDecimal(2));
        deltaDto.setAccomCapacityDelta(new BigDecimal(3));
        deltaDto.setAccomCapacityDeltaPercent(new BigDecimal(4));
        setCommonActivityData(deltaDto);
        return deltaDto;
    }

    private List<TotalActivityDelta> getTotalActivityData() {
        List<TotalActivityDelta> list = new ArrayList<>();
        list.add(getTotalActivityDto("2019-01-01"));
        list.add(getTotalActivityDto("2019-01-02"));
        return list;
    }

    private TotalActivityDelta getTotalActivityDto(String date) {
        TotalActivityDelta deltaDto = new TotalActivityDelta();
        deltaDto.setOccupancyDate(new LocalDate(date).toDate());
        deltaDto.setOldTotalAccomCapacity(new BigDecimal(1));
        deltaDto.setNewTotalAccomCapacity(new BigDecimal(2));
        deltaDto.setTotalAccomCapacityDelta(new BigDecimal(3));
        deltaDto.setTotalAccomCapacityDeltaPercent(new BigDecimal(4));
        setCommonActivityData(deltaDto);
        return deltaDto;
    }

    private void setCommonActivityData(ActivityDelta deltaDto) {
        deltaDto.setOldRoomsSold(new BigDecimal(5));
        deltaDto.setNewRoomsSold(new BigDecimal(6));
        deltaDto.setRoomsSoldDelta(new BigDecimal(7));
        deltaDto.setRoomsSoldDeltaPercent(new BigDecimal(8));
        deltaDto.setOldRoomsNotAvailableMaintenance(new BigDecimal(9));
        deltaDto.setNewRoomsNotAvailableMaintenance(new BigDecimal(10));
        deltaDto.setRoomsNotAvailableMaintenanceDelta(new BigDecimal(11));
        deltaDto.setRoomsNotAvailableMaintenanceDeltaPercent(new BigDecimal(12));
        deltaDto.setOldRoomsNotAvailableOther(new BigDecimal(13));
        deltaDto.setNewRoomsNotAvailableOther(new BigDecimal(14));
        deltaDto.setRoomsNotAvailableOtherDelta(new BigDecimal(15));
        deltaDto.setRoomsNotAvailableOtherDeltaPercent(new BigDecimal(16));
        deltaDto.setOldArrivals(new BigDecimal(17));
        deltaDto.setNewArrivals(new BigDecimal(18));
        deltaDto.setArrivalsDelta(new BigDecimal(19));
        deltaDto.setArrivalsDeltaPercent(new BigDecimal(20));
        deltaDto.setOldDepartures(new BigDecimal(21));
        deltaDto.setNewDepartures(new BigDecimal(22));
        deltaDto.setDeparturesDelta(new BigDecimal(23));
        deltaDto.setDeparturesDeltaPercent(new BigDecimal(24));
        deltaDto.setOldCancellations(new BigDecimal(25));
        deltaDto.setNewCancellations(new BigDecimal(26));
        deltaDto.setCancellationsDelta(new BigDecimal(27));
        deltaDto.setCancellationsDeltaPercent(new BigDecimal(28));
        deltaDto.setOldNoShows(new BigDecimal(29));
        deltaDto.setNewNoShows(new BigDecimal(30));
        deltaDto.setNoShowsDelta(new BigDecimal(31));
        deltaDto.setNoShowsDeltaPercent(new BigDecimal(32));
        deltaDto.setOldRoomRevenue(new BigDecimal(33));
        deltaDto.setNewRoomRevenue(new BigDecimal(34));
        deltaDto.setRoomRevenueDelta(new BigDecimal(35));
        deltaDto.setRoomRevenueDeltaPercent(new BigDecimal(36));
        deltaDto.setOldFoodRevenue(new BigDecimal(37));
        deltaDto.setNewFoodRevenue(new BigDecimal(38));
        deltaDto.setFoodRevenueDelta(new BigDecimal(39));
        deltaDto.setFoodRevenueDeltaPercent(new BigDecimal(40));
        deltaDto.setOldTotalRevenue(new BigDecimal(41));
        deltaDto.setNewTotalRevenue(new BigDecimal(42));
        deltaDto.setTotalRevenueDelta(new BigDecimal(43));
        deltaDto.setTotalRevenueDeltaPercent(new BigDecimal(44));
    }

    @Test
    public void validatePostBackFillData_WithVeryLessPercentChange() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_MAPPINGS_VALIDATION_DONE);
        when(tenantCrudService.findByNamedQuerySingleResult(eq(TotalActivityDelta.FIND_DELTA_PERCENT_EXCEEDING_ROW_LEVEL_THRESHOLD), anyMap())).thenReturn(BigDecimal.ONE);
        when(tenantCrudService.findByNamedQuerySingleResult(eq(AccomActivityDelta.FIND_DELTA_PERCENT_EXCEEDING_ROW_LEVEL_THRESHOLD), anyMap())).thenReturn(BigDecimal.ONE);
        pmsMigrationService.validatePostBackFillData(pmsMigrationConfig, FAILED_JOB_ID);
        assertEquals(PMS_POST_BACKFILL_DATA_VALIDATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(alertService, times(0)).updateMigrationStateAndCreateAlertWithNewTransaction(any(), any(), any(), any(), any(), any());
    }

    @Test
    public void validatePostBackFillData_TotalActivity() {
        when(tenantCrudService.findByNamedQuerySingleResult(eq(TotalActivityDelta.FIND_DELTA_PERCENT_EXCEEDING_ROW_LEVEL_THRESHOLD), anyMap())).thenReturn(BigDecimal.TEN);
        validatePostBAckfillData();
    }

    @Test
    public void validatePostBackFillData_AccomActivity() {
        when(tenantCrudService.findByNamedQuerySingleResult(eq(AccomActivityDelta.FIND_DELTA_PERCENT_EXCEEDING_ROW_LEVEL_THRESHOLD), anyMap())).thenReturn(BigDecimal.TEN);
        when(tenantCrudService.findByNamedQuerySingleResult(eq(TotalActivityDelta.FIND_DELTA_PERCENT_EXCEEDING_ROW_LEVEL_THRESHOLD), anyMap())).thenReturn(BigDecimal.ONE);
        validatePostBAckfillData();
    }

    public void validatePostBAckfillData() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationPostBackFillDataValidation, true);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_MAPPINGS_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationPostBackFillDataValidation.getName())).thenReturn(alertTypeEntity);
        try {
            pmsMigrationService.validatePostBackFillData(pmsMigrationConfig, FAILED_JOB_ID);
        } catch (TetrisException ex) {
            assertEquals(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, ex.getErrorCode());
            assertTrue(ex.getMessage().contains(PMS_MIGRATION_POST_BACKFILL_DATA_VALIDATION_EXCEPTION_MESSAGE));
        }
        assertEquals(PMS_POST_BACKFILL_DATA_VALIDATION_REQUIRED, pmsMigrationConfig.getMigrationState());
        verify(alertService).getAlertType(AlertType.PMSMigrationPostBackFillDataValidation.getName());
        verify(alertService).updateMigrationStateAndCreateAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(),
                alertTypeEntity, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID,
                POST_BACKFILL_DATA_VALIDATION_URL, AlertType.PMSMigrationPostBackFillDataValidation, pmsMigrationConfig);
    }

    @Test
    public void detachRemoteAgent() {
        List<AgentProperty> agentPropertyList = createAgentPropertyList();
        when(remoteAgentConfigService.getAgentProperties(anyInt())).thenReturn(agentPropertyList);
        pmsMigrationService.removePropertyFromAgent();
        verify(remoteTaskService, times(2)).deleteRemoteTasks(anyInt(), anyInt());
        verify(remoteTaskService, times(2)).createDeletePropertyTask(anyInt(), anyInt());
        verify(remoteAgentConfigService, times(2)).removePropertyFromAgent(any());
    }

    @Test
    public void detachRemoteAgent_NoRemoteAgent() {
        when(remoteAgentConfigService.getAgentProperties(anyInt())).thenReturn(null);
        pmsMigrationService.removePropertyFromAgent();
        verify(remoteTaskService, times(0)).deleteRemoteTasks(anyInt(), anyInt());
        verify(remoteTaskService, times(0)).createDeletePropertyTask(anyInt(), anyInt());
        verify(remoteAgentConfigService, times(0)).removePropertyFromAgent(any());
    }

    private List<AgentProperty> createAgentPropertyList() {
        AgentProperty agentProp1 = new AgentProperty();
        AgentProperty agentProp2 = new AgentProperty();
        agentProp1.setPropertyId(propertyH2.getId());
        agentProp2.setPropertyId(propertyH2.getId());
        RemoteAgent agent1 = new RemoteAgent();
        agent1.setId(1);
        RemoteAgent agent2 = new RemoteAgent();
        agent2.setId(1);
        agentProp1.setRemoteAgent(agent1);
        agentProp2.setRemoteAgent(agent2);
        return Arrays.asList(agentProp1, agentProp2);
    }

    @Test
    public void createValidateNewIntegrationDataAlert() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_INBOUND_CONFIGURATION_DONE);

        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationNewIntegrationDataValidation, true);
        when(alertService.getAlertType(AlertType.PMSMigrationNewIntegrationDataValidation.getName())).thenReturn(alertTypeEntity);

        pmsMigrationService.createValidateNewIntegrationDataAlert(pmsMigrationConfig);

        assertEquals(PMS_DATA_VALIDATION_REQUIRED, pmsMigrationConfig.getMigrationState());
        verify(globalCrudService).save(pmsMigrationConfig);
        verify(alertService).createAlert(PacmanWorkContextHelper.getWorkContext(), alertTypeEntity, "",
                NGI_DATA_VALIDATION_URL, AlertType.PMSMigrationNewIntegrationDataValidation);
    }

    @Test
    public void shouldNotGenerateSystemDataValidationAlertWhenAlertTypeIsNotEnabled() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationSystemDataValidation, false);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_DATA_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationSystemDataValidation.getName())).thenReturn(alertTypeEntity);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(tenantCrudService.findByNativeQuerySingleResult(PostMigrationOldInhouseReservation.TABLE_EXISTS, Collections.emptyMap())).thenReturn(0);
        pmsMigrationService.validateSystemData(pmsMigrationConfig, FAILED_JOB_ID);

        assertEquals(PMSMigrationState.PMS_DATA_VALIDATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(alertService, times(0)).updateMigrationStateAndCreateAlertWithNewTransaction(any(), any(), any(), any(), any(), any());
    }

    @Test
    public void shouldUpdateMigrationStateAndGenerateSystemDataValidationAlertWhenAlertTypeIsEnabled() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationSystemDataValidation, true);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_DATA_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationSystemDataValidation.getName())).thenReturn(alertTypeEntity);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(tenantCrudService.findByNativeQuerySingleResult(PostMigrationOldInhouseReservation.TABLE_EXISTS, Collections.emptyMap())).thenReturn(0);
        try {
            pmsMigrationService.validateSystemData(pmsMigrationConfig, FAILED_JOB_ID);
        } catch (TetrisException ex) {
            assertEquals(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, ex.getErrorCode());
            assertTrue(ex.getMessage().contains(PMS_MIGRATION_SYSTEM_DATA_VALIDATION_EXCEPTION_MESSAGE));
        }
        assertEquals(PMSMigrationState.PMS_SYSTEM_VALIDATION_REQUIRED, pmsMigrationConfig.getMigrationState());
        verify(alertService).getAlertType(AlertType.PMSMigrationSystemDataValidation.getName());
        String details = "<ul><li><a target=\"_blank\" href=\"_flowId=viewReportFlow&standAlone=true&ParentFolderUri=/public/Reports&reportUnit=/public/Reports/DataExtraction&decorate=no&Comp1=-1&Comp2=-1&Comp3=-1&Comp4=-1&Comp5=-1&Competitor=false&IS_IGNORE_PAGINATION=true&IsADR_BV=false&IsADR_FG=false&IsADR_Hotel=false&IsADR_MS=false&IsADR_RC=false&IsADR_RT=false&IsArr_BV=false&IsArr_FG=false&IsArr_Hotel=true&IsArr_MS=true&IsArr_RC=true&IsArr_RT=false&IsBAR_Hotel=false&IsBAR_RC=false&IsBAR_RT=false&IsBV=false&IsBudgetHotel=false&IsCap_Hotel=true&IsCap_RC=true&IsCap_RT=false&IsCncel_BV=false&IsCncel_FG=false&IsCncel_Hotel=true&IsCncel_MS=true&IsCncel_RC=true&IsCncel_RT=false&IsDep_BV=false&IsDep_FG=false&IsDep_Hotel=true&IsDep_MS=true&IsDep_RC=true&IsDep_RT=false&IsFG=false&IsHotel=true&IsLRV_Hotel=false&IsLRV_RC=false&IsLRV_RT=false&IsMS=true&IsNS_BV=false&IsNS_FG=false&IsNS_Hotel=true&IsNS_MS=true&IsNS_RC=true&IsNS_RT=false&IsOOO_Hotel=true&IsOOO_RC=true&IsOOO_RT=false&IsOcFcst_BV=false&IsOcFcst_FG=false&IsOcFcst_Hotel=false&IsOcFcst_MS=false&IsOcFcst_RC=false&IsOvbk_Hotel=false&IsOvbk_RT=false&IsRC=true&IsRNAO_Hotel=true&IsRNAO_RC=true&IsRNAO_RT=false&IsRS_BV=false&IsRS_FG=false&IsRS_Hotel=true&IsRS_MS=true&IsRS_RC=true&IsRS_RT=false&IsRS_STLY_BV=false&IsRS_STLY_FG=false&IsRS_STLY_Hotel=false&IsRS_STLY_MS=false&IsRS_STLY_RC=false&IsRS_STLY_RT=false&IsRT=false&IsRemainingCapacityHotel=false&IsRemainingCapacityRC=false&IsRemainingCapacityRT=false&IsRevPar_Hotel=false&IsRevPar_RC=false&IsRevPar_RT=false&IsRev_BV=false&IsRev_FG=false&IsRev_Hotel=true&IsRev_MS=true&IsRev_RC=true&IsRev_RT=false&IsRev_STLY_BV=false&IsRev_STLY_FG=false&IsRev_STLY_Hotel=false&IsRev_STLY_MS=false&IsRev_STLY_RC=false&IsRev_STLY_RT=false&IsSplEvt_Hotel=false&IsSysGrpWashPer_FG=false&IsSysUnConDmdTtl_Hotel=false&IsSysUnConDmd_RC=false&IsSysUnDmd_FG=false&IsUserProjected_FG=false&IsUserProjected_Hotel=false&IsUserProjected_MS=false&IsUserUnConDmdTotal_Hotel=false&IsUserUnConDmd_RC=false&IsUserUnDmd_FG=false&includeInactiveRT=0&param_IsShowLastYearDataChecked=false&param_IsWashAtHotelLevel=false&param_Rolling_End_Date=null&param_Rolling_Start_Date=null&param_isRollingDate=0&IsSTR=false&isMarketPerformanceChecked=false&StartDate=20180511&EndDate=20190510REPLACE_UI_SPECIFIC_PARAMS\">view.report</a></li></ul>";
        verify(alertService).updateMigrationStateAndCreateAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(), alertTypeEntity, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID, details, AlertType.PMSMigrationSystemDataValidation, pmsMigrationConfig);
    }

    @Test
    public void shouldUpdateMigrationStateAndGenerateSystemDataValidationAlertWhenAlertTypeIsEnabledBackupExists() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationSystemDataValidation, true);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_DATA_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationSystemDataValidation.getName())).thenReturn(alertTypeEntity);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(tenantCrudService.findByNativeQuerySingleResult(PostMigrationOldInhouseReservation.TABLE_EXISTS, Collections.emptyMap())).thenReturn(1);
        try {
            pmsMigrationService.validateSystemData(pmsMigrationConfig, FAILED_JOB_ID);
        } catch (TetrisException ex) {
            assertEquals(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, ex.getErrorCode());
            assertTrue(ex.getMessage().contains(PMS_MIGRATION_SYSTEM_DATA_VALIDATION_EXCEPTION_MESSAGE));
        }
        assertEquals(PMSMigrationState.PMS_SYSTEM_VALIDATION_REQUIRED, pmsMigrationConfig.getMigrationState());
        verify(alertService).getAlertType(AlertType.PMSMigrationSystemDataValidation.getName());
        String details = "<ul><li><a target=\"_blank\" href=\"_HOST_URL_/solutions/rest/pmsMigrationTools/postSystemValidationReservationsReport?propertyId=6\">view.reservations.compare.report</a></li><li><a target=\"_blank\" href=\"_flowId=viewReportFlow&standAlone=true&ParentFolderUri=/public/Reports&reportUnit=/public/Reports/DataExtraction&decorate=no&Comp1=-1&Comp2=-1&Comp3=-1&Comp4=-1&Comp5=-1&Competitor=false&IS_IGNORE_PAGINATION=true&IsADR_BV=false&IsADR_FG=false&IsADR_Hotel=false&IsADR_MS=false&IsADR_RC=false&IsADR_RT=false&IsArr_BV=false&IsArr_FG=false&IsArr_Hotel=true&IsArr_MS=true&IsArr_RC=true&IsArr_RT=false&IsBAR_Hotel=false&IsBAR_RC=false&IsBAR_RT=false&IsBV=false&IsBudgetHotel=false&IsCap_Hotel=true&IsCap_RC=true&IsCap_RT=false&IsCncel_BV=false&IsCncel_FG=false&IsCncel_Hotel=true&IsCncel_MS=true&IsCncel_RC=true&IsCncel_RT=false&IsDep_BV=false&IsDep_FG=false&IsDep_Hotel=true&IsDep_MS=true&IsDep_RC=true&IsDep_RT=false&IsFG=false&IsHotel=true&IsLRV_Hotel=false&IsLRV_RC=false&IsLRV_RT=false&IsMS=true&IsNS_BV=false&IsNS_FG=false&IsNS_Hotel=true&IsNS_MS=true&IsNS_RC=true&IsNS_RT=false&IsOOO_Hotel=true&IsOOO_RC=true&IsOOO_RT=false&IsOcFcst_BV=false&IsOcFcst_FG=false&IsOcFcst_Hotel=false&IsOcFcst_MS=false&IsOcFcst_RC=false&IsOvbk_Hotel=false&IsOvbk_RT=false&IsRC=true&IsRNAO_Hotel=true&IsRNAO_RC=true&IsRNAO_RT=false&IsRS_BV=false&IsRS_FG=false&IsRS_Hotel=true&IsRS_MS=true&IsRS_RC=true&IsRS_RT=false&IsRS_STLY_BV=false&IsRS_STLY_FG=false&IsRS_STLY_Hotel=false&IsRS_STLY_MS=false&IsRS_STLY_RC=false&IsRS_STLY_RT=false&IsRT=false&IsRemainingCapacityHotel=false&IsRemainingCapacityRC=false&IsRemainingCapacityRT=false&IsRevPar_Hotel=false&IsRevPar_RC=false&IsRevPar_RT=false&IsRev_BV=false&IsRev_FG=false&IsRev_Hotel=true&IsRev_MS=true&IsRev_RC=true&IsRev_RT=false&IsRev_STLY_BV=false&IsRev_STLY_FG=false&IsRev_STLY_Hotel=false&IsRev_STLY_MS=false&IsRev_STLY_RC=false&IsRev_STLY_RT=false&IsSplEvt_Hotel=false&IsSysGrpWashPer_FG=false&IsSysUnConDmdTtl_Hotel=false&IsSysUnConDmd_RC=false&IsSysUnDmd_FG=false&IsUserProjected_FG=false&IsUserProjected_Hotel=false&IsUserProjected_MS=false&IsUserUnConDmdTotal_Hotel=false&IsUserUnConDmd_RC=false&IsUserUnDmd_FG=false&includeInactiveRT=0&param_IsShowLastYearDataChecked=false&param_IsWashAtHotelLevel=false&param_Rolling_End_Date=null&param_Rolling_Start_Date=null&param_isRollingDate=0&IsSTR=false&isMarketPerformanceChecked=false&StartDate=20180511&EndDate=20190510REPLACE_UI_SPECIFIC_PARAMS\">view.report</a></li></ul>";
        verify(alertService).updateMigrationStateAndCreateAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(), alertTypeEntity, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID, details, AlertType.PMSMigrationSystemDataValidation, pmsMigrationConfig);
    }

    @Test
    public void shouldNotGenerateDecisionValidationAlertWhenAlertTypeIsNotEnabled() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationDecisionValidation, false);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_SYSTEM_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationDecisionValidation.getName())).thenReturn(alertTypeEntity);

        pmsMigrationService.validateDecisionsAndForecast(pmsMigrationConfig, FAILED_JOB_ID);

        assertEquals(PMSMigrationState.PMS_SYSTEM_VALIDATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(alertService, times(0)).updateMigrationStateAndCreateAlertWithNewTransaction(any(), any(), any(), any(), any(), any());
    }

    @Test
    public void shouldUpdateMigrationStateAndGenerateDecisionValidationAlertWhenAlertTypeIsEnabled() {
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.PMSMigrationDecisionValidation, true);
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_SYSTEM_VALIDATION_DONE);
        when(alertService.getAlertType(AlertType.PMSMigrationDecisionValidation.getName())).thenReturn(alertTypeEntity);
        pmsMigrationService.validateDecisionsAndForecast(pmsMigrationConfig, FAILED_JOB_ID);
        assertEquals(PMSMigrationState.PMS_FORECAST_AND_DECISION_VALIDATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(alertService).getAlertType(AlertType.PMSMigrationDecisionValidation.getName());
        verify(alertService).updateMigrationStateAndCreateAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(), alertTypeEntity, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID, "", AlertType.PMSMigrationDecisionValidation, pmsMigrationConfig);
    }

    @Test
    public void shouldMaskDatesToBeExcludedFromForecasting() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_SYSTEM_VALIDATION_DONE);

        pmsMigrationService.maskDatesToBeExcludedFromForecasting(pmsMigrationConfig);

        ArgumentCaptor<PropertyExcludedDates> maskedDatesCaptor = ArgumentCaptor.forClass(PropertyExcludedDates.class);
        verify(propertyExcludeDatesService).createExcludedDates(eq(TestProperty.H2.getId()), maskedDatesCaptor.capture());
        final PropertyExcludedDates maskedDates = maskedDatesCaptor.getValue();
        assertEquals(DateUtil.addDaysToDate(pmsMigrationConfig.getOldSysDate(), -7), maskedDates.getStartDate());
        assertEquals(DateUtil.addDaysToDate(pmsMigrationConfig.getNewSysDate(), 7), maskedDates.getEndDate());
        assertEquals("Dates excluded because of PMS Migration", maskedDates.getNotes());
    }

    @Test
    public void checkForOutboundConfiguraionAndGenerateAlertIfStageWas2WayPreviously() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_FORECAST_AND_DECISION_VALIDATION_DONE);
        final InfoMgrTypeEntity alertTypeEntity = getMockedInfoMgrTypeEntity(AlertType.ConfigureOutbound, true);
        when(alertService.getAlertType(AlertType.ConfigureOutbound.getName())).thenReturn(alertTypeEntity);
        pmsMigrationService.checkForOutboundConfiguraionAndGenerateAlert(FAILED_JOB_ID, pmsMigrationConfig);
        assertEquals(PMSMigrationState.PMS_OUTBOUND_CONFIGURATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(alertService).getAlertType(AlertType.ConfigureOutbound.getName());
        verify(alertService).updateMigrationStateAndCreateAlertWithNewTransaction(PacmanWorkContextHelper.getWorkContext(), alertTypeEntity, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID, "", AlertType.ConfigureOutbound, pmsMigrationConfig);
    }

    @Test
    public void savePMSMigrationConfig() {
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setProperty(new Property());
        pmsMigrationService.savePMSMigrationConfig(pmsMigrationConfig);
        verify(globalCrudService).save(any(PMSMigrationConfig.class));
    }

    @Test
    public void updateMigrationState() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_DATA_VALIDATION_REQUIRED);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyObject())).thenReturn(pmsMigrationConfig);
        pmsMigrationService.updateMigrationState(PMSMigrationState.PMS_DATA_VALIDATION_DONE);
        assertEquals(PMSMigrationState.PMS_DATA_VALIDATION_DONE, pmsMigrationConfig.getMigrationState());
        verify(globalCrudService).save(pmsMigrationConfig);
    }

    @Test
    public void testIsPMSMigratedProperty() {
        when(globalCrudService.findByNamedQuerySingleResult(anyString(), anyMap())).thenReturn(getPMSMigrationTestConfig(PMSMigrationState.PMS_MIGRATION_COMPLETE));

        boolean isMigratedProperty = pmsMigrationService.getPMSMigrationConfiguration(PMS_MIGRATION_COMPLETE) != null;

        assertTrue(isMigratedProperty);
    }

    @Test
    public void shouldStartPMSRevampOrchestratorJob() {
        verifyCorrectJobIsStarted(JobName.PMSRevampOrchestratorJob, new PMSMigrationConfig());
    }

    @Test
    public void shouldStartPMSGenericOrchestratorJob() {
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        verifyCorrectJobIsStarted(JobName.PMSGenericOrchestratorJob, pmsMigrationConfig);
    }

    private void verifyCorrectJobIsStarted(JobName pmsGenericOrchestratorJob, PMSMigrationConfig pmsMigrationConfig) {
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_PROPERTY_ID_AND_CLIENT_ID), anyMap())).thenReturn(pmsMigrationConfig);
        pmsMigrationService.startPMSOrchestratorJob(false);

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(pmsGenericOrchestratorJob), jobParamsCaptor.capture());
        assertEquals(4, jobParamsCaptor.getValue().size());
        assertNotNull(jobParamsCaptor.getValue().get(JobParameterKey.TIMESTAMP));
        assertEquals(H_2_ID, jobParamsCaptor.getValue().get(PROPERTY_ID));
        assertEquals(false, jobParamsCaptor.getValue().get(JobParameterKey.SKIP_BACKUP));
        assertEquals(1, jobParamsCaptor.getValue().get(JobParameterKey.USER_ID));
    }

    @Test
    public void shouldStartNGICatchupJob() {
        // catchup date range completely falls into the period when property was on NGI, so we should directly start ngicatchupjob
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_MIGRATION_COMPLETE);
        CatchupParameters catchupParameters = mockCatchupParameters();
        catchupParameters.setStartDate(LocalDate.fromDateFields(DateUtil.getDateWithoutTime(10, 4, 2018)));
        when(globalCrudService.findByNamedQuerySingleResult(anyString(), anyMap())).thenReturn(pmsMigrationConfig);
        when(propertyService.getPropertyById(H_2_ID)).thenReturn(propertyH2);
        when(jobService.startJob(any(), any())).thenReturn(1L);

        pmsMigrationService.startCatchupJob(H_2_ID, catchupParameters);

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.NGICatchupJob), jobParamsCaptor.capture());
        assertCatchupJobParams(catchupParameters.getStartDate().toDate(), catchupParameters.getEndDate().toDate(), jobParamsCaptor.getValue());
        verify(pacmanConfigParamsService).updateParameterValue(PACMAN_BSTN_H2, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), "NGI");
        verify(pacmanConfigParamsService).updateParameterValue(PACMAN_BSTN_H2, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), pmsMigrationConfig.getNewSystem());
    }

    @Test
    public void shouldStartOperaCatchupJob() {
        // catchup date range completely falls into the period when property was on OPERA and partially falls into the period when property was on NGI, so we should directly do operacatchup first
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_MIGRATION_COMPLETE);
        CatchupParameters catchupParameters = mockCatchupParameters();
        when(globalCrudService.findByNamedQuerySingleResult(anyString(), anyMap())).thenReturn(pmsMigrationConfig);
        when(propertyService.getPropertyById(H_2_ID)).thenReturn(propertyH2);
        when(jobService.startJob(any(), any())).thenReturn(1L);

        pmsMigrationService.startCatchupJob(H_2_ID, catchupParameters);

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.OperaCatchupJob), jobParamsCaptor.capture());
        assertOperaCatchupJobParams(catchupParameters.getStartDate().toDate(), pmsMigrationConfig.getOldSysDate(), catchupParameters.getEndDate().toDate(), jobParamsCaptor.getValue());
    }

    @Test
    public void shouldStartOperaCatchupJobWithCorrectCatchupDateRange() {
        // catchup date range completely falls into the period when property was on OPERA, so we should start operacatchupjob
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMSMigrationState.PMS_MIGRATION_COMPLETE);
        CatchupParameters catchupParameters = mockCatchupParameters();
        catchupParameters.setEndDate(LocalDate.fromDateFields(DateUtil.getDateWithoutTime(1, 4, 2018)));
        when(globalCrudService.findByNamedQuerySingleResult(anyString(), anyMap())).thenReturn(pmsMigrationConfig);
        when(propertyService.getPropertyById(H_2_ID)).thenReturn(propertyH2);
        when(jobService.startJob(any(), any())).thenReturn(1L);

        pmsMigrationService.startCatchupJob(H_2_ID, catchupParameters);

        ArgumentCaptor<Map> jobParamsCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.OperaCatchupJob), jobParamsCaptor.capture());
        assertOperaCatchupJobParams(catchupParameters.getStartDate().toDate(), catchupParameters.getEndDate().toDate(), catchupParameters.getEndDate().toDate(), jobParamsCaptor.getValue());

    }

    @Test
    public void shouldGenerateConfigureInboundAlert() {
        InfoMgrTypeEntity infoMgrTypeEntity = new InfoMgrTypeEntity();
        infoMgrTypeEntity.setEnabled(true);
        when(alertService.getAlertType(AlertType.ConfigureInbound.getName())).thenReturn(infoMgrTypeEntity);

        pmsMigrationService.generateConfigureInboundAlert();
        verify(alertService).createAlert(any(WorkContextType.class), eq(infoMgrTypeEntity), eq(CONFIGURE_INBOUND_ALERT_DESCRIPTION), eq(VENDOR_CONFIGURATION_BEFORE_RESOLVING_THIS_ALERT), eq(AlertType.ConfigureInbound));
    }

    @Test
    public void testGetExistingMappings() {
        when(tenantCrudService.findAll(PMSMigrationMapping.class)).thenReturn(getExistingMappings());
        List<PMSMigrationMapping> result = pmsMigrationService.getExistingMappingsWithoutSecondaryMapping();
        verify(tenantCrudService).findAll(PMSMigrationMapping.class);
        assertEquals(2, result.size());
    }

    @Test
    public void testAreThereAnyExistingMappings() {
        assertFalse(pmsMigrationService.areThereAnyExistingMappings());
        verify(tenantCrudService).findOne(PMSMigrationMapping.class);
    }

    @Test
    public void getDistinctRoomTypes() {
        when(accommodationService.getAllActiveAccomTypes()).thenReturn(Arrays.asList(getAccomType(RT_1), getAccomType(RT_2)));
        List<String> distinctRoomTypes = pmsMigrationService.getDistinctRoomTypes();
        verify(accommodationService).getAllActiveAccomTypes();
        assertEquals(Arrays.asList(RT_1, RT_2), distinctRoomTypes);
    }

    @Test
    public void validateAMSSuffix() {
        assertTrue(AMS_SUFFIX.containsAll(Arrays.asList("G", "TBYL", "TBYNL", "W", "N", "UF", "UP", "UFP", "USB", "U", "QSL", "QYL", "QN", "QS", "QY", "DEF")));
    }

    @Test
    public void getDistinctMktSegForBlankRateCodes() {
        List<String> distinctMktSegForGroups = Arrays.asList("MS1", "MS2", "MS3");
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_DISTINCT_MKT_SEG_FOR_BLANK_RATE_CODES)).thenReturn(Arrays.asList("MS1", "MS4", "MS3_DEF", "MS3_QYL", "MS5_DEF", "MS2_QSL", "MS6"));
        List<String> result = pmsMigrationService.getDistinctMktSegForBlankRateCodes(distinctMktSegForGroups);
        assertEquals(3, result.size());
        assertTrue(result.containsAll(Arrays.asList("MS4", "MS5", "MS6")));
    }

    @Test
    public void validateMissingOrExtraMappings() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        when(tenantCrudService.findByNamedQuery(eq(GroupBlockMaster.GET_DISTINCT_GROUP_CODES), anyMap())).thenReturn(Arrays.asList(GC_1, GC_3));
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_3));
        when(accommodationService.getAllActiveAccomTypes()).thenReturn(Arrays.asList(getAccomType(RT_1), getAccomType(RT_3)));
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappings(getDtoList(), pmsMigrationConfig);
        assertEquals(2, errorMap.size());
        validateCodeExistsButNoMapping(errorMap, GC_3, RT_3, PMSMigrationTestUtil.MS_3);
        assertEquals(2, errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM).get(GC_2).intValue());
        assertEquals(4, errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM).get(RT_2).intValue());
        assertEquals(6, errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM).get(PMSMigrationTestUtil.MS_2).intValue());
    }

    @Test
    void validateMissingOrExtraMappingsForClientLevelUploadedSheetFromScreen() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_3));
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappings(getMSDtoList(), pmsMigrationConfig);
        assertEquals(0, errorMap.size());
        assertNull(errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM));
    }

    @Test
    void validateMissingOrExtraMappingsForClientLevelUploadEmptySheet() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappings(Collections.emptyList(), pmsMigrationConfig);
        assertEquals(1, errorMap.size());
        assertEquals(-2, errorMap.get(CLIENT_LEVEL_MAPPING_NOT_PROVIDED_FOR_PROPERTY).get("ALL").intValue());
    }

    @Test
    public void addMissingRTGCMappings() {
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        list.add(getPMSMigrationMappingImportDTO(ROOM_TYPE, "RT1"));
        list.add(getPMSMigrationMappingImportDTO(GROUP_CODE, "GC1"));
        when(accommodationService.getAllActiveAccomTypes()).thenReturn(Arrays.asList(getAccomType(RT_1), getAccomType(RT_2)));
        when(tenantCrudService.findByNamedQuery(eq(GroupBlockMaster.GET_DISTINCT_GROUP_CODES), anyMap())).thenReturn(Arrays.asList(GC_1, GC_3));
        pmsMigrationService.addMissingRTGCMappings(list);
        assertEquals(4, list.size());
        assertEquals(RT_2, list.get(2).getCurrentCode());
        assertEquals(GC_3, list.get(3).getCurrentCode());

    }

    private PMSMigrationMappingImportDTO getPMSMigrationMappingImportDTO(String codeType, String currentCode) {
        PMSMigrationMappingImportDTO dto = new PMSMigrationMappingImportDTO();
        dto.setCodeType(codeType);
        dto.setCurrentCode(currentCode);
        return dto;
    }

    @Test
    public void savePMSMigrationGenericMapping() {
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        pmsMigrationService.savePMSMigrationGenericMapping(list);
        verify(pmsMigrationService).addMissingRTGCMappings(list);
        verify(pmsMigrationService).saveMSRecodingMappings(list);
    }

    @Test
    public void validateMissingOrExtraMappingsForGenericTemplate_withMSChanges() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        pmsMigrationService.validateExtraMappingsForGenericTemplate(list, pmsMigrationConfig);
        verify(pmsMigrationService, times(1)).validateExtraMappingsForMSRecoding(list, Collections.emptyMap());
        verify(pmsMigrationService, times(5)).validateNotAvailableInSystemButGivenInMapping(anyMap(), anyMap(), anyList());
        verify(pmsMigrationService).getDistinctRateCodes();
        verify(pmsMigrationService).getDistinctRoomTypes();
    }

    @Test
    public void validateMissingOrExtraMappingsForGenericTemplate_withoutMSChanges() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig(false);
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        pmsMigrationService.validateExtraMappingsForGenericTemplate(list, pmsMigrationConfig);
        verify(pmsMigrationService, times(0)).validateExtraMappingsForMSRecoding(list, Collections.emptyMap());
    }

    private PMSMigrationConfig getPMSMigrationConfig(boolean msChanged) {
        PMSMigrationConfig config = new PMSMigrationConfig();
        config.setMktSegChanged(msChanged);
        return config;
    }

    private PMSMigrationConfig getPMSMigrationConfig() {
        PMSMigrationConfig config = getPMSMigrationConfig(true);
        config.setPropertyCodeChanged(true);
        config.setGroupCodeChanged(true);
        config.setRoomTypeChanged(true);
        return config;
    }

    private void validateCodeExistsButNoMapping(Map<String, Map<String, Integer>> errorMap, String... codes) {
        for (String code : codes) {
            assertEquals(-2, errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING).get(code).intValue());
        }
    }

    @Test
    public void validateMissingOrExtraMappingsForSecondSheet() {
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList(PMSMigrationTestUtil.RC_1, PMSMigrationTestUtil.RC_3));
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig(true);
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappingsForSecondSheet(getRateCodeMappingDtoList(), pmsMigrationConfig.isMktSegChanged(), pmsMigrationConfig);
        assertEquals(2, errorMap.size());
        assertEquals(-2, errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING).get(PMSMigrationTestUtil.RC_3).intValue());
        assertEquals(2, errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM).get(PMSMigrationTestUtil.RC_2).intValue());
        assertEquals(1, errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM).get(PMSMigrationTestUtil.rc_1).intValue());
    }

    @Test
    void validateMissingOrExtraMappingsForSecondSheetClientUploadClientUploadOnAndBeforeJob() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList(PMSMigrationTestUtil.RC_1, PMSMigrationTestUtil.RC_3));
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig(true);
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappingsForSecondSheet(getRateCodeMappingDtoList(), pmsMigrationConfig.isMktSegChanged(), pmsMigrationConfig);
        assertEquals(0, errorMap.size());
        assertNull(errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING));
        assertNull(errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM));
    }

    @Test
    void validateMissingOrExtraMappingsForSecondSheetClientUploadClientUploadOnAndDuringJob() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList(PMSMigrationTestUtil.RC_1, PMSMigrationTestUtil.RC_3));
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig(true);
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_READY);
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappingsForSecondSheet(getRateCodeMappingDtoList(), pmsMigrationConfig.isMktSegChanged(), pmsMigrationConfig);
        assertEquals(1, errorMap.size());
        assertEquals(-2, errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING).get(PMSMigrationTestUtil.RC_3).intValue());
        assertNull(errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM));
    }

    @Test
    public void shouldValidateRequiredConfigurationAndGenerateAlertIfMappingsForSecondSheetAreInconsistent() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig(true);
        pmsMigrationConfig.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        // Mapping template is NOT up-to-date
        doReturn(Collections.emptyList()).when(pmsMigrationService).getAllRateCodeMktSegMappings();
        doReturn(Collections.singletonMap(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING,
                Collections.singletonMap(PMSMigrationTestUtil.RC_3, 1))).when(pmsMigrationService)
                .validateMissingOrExtraMappingsForSecondSheet(anyListOf(PMSMigrationRateCodeMktSegMappingDTO.class), anyBoolean(), any(PMSMigrationConfig.class));
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), any())).thenReturn(pmsMigrationConfig);
        pmsMigrationService.validateRequiredConfigurationForExhaustiveMigration(
                getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(eq(AlertType.PMSMigrationInconsistentMappingsValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                eq(PMS_MIGRATION_MAPPING_TEMPLATE_VALIDATION_ERROR), // Inconsistent mapping template validation error
                eq(PMS_MIGRATION_VALIDATE_REQUIRED_MAPPINGS_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_MAPPINGS_VALIDATION_REQUIRED)
        );
        verifyZeroInteractions(pmsRevampAMSDataService);
    }

    @Test
    public void validateInconsistentMappingsAndGetError_SpecificTemplate() {
        PMSMigrationConfig config = new PMSMigrationConfig();
        config.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), any()))
                .thenReturn(config);
        pmsMigrationService.validateInconsistentMappingsAndGetError();
        verify(pmsMigrationService).validateInconsistentMappingTemplateForSpecificTemplate(config);
    }

    @Test
    public void validateMissingOrExtraMappingsForGenericTemplate() {
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        PMSMigrationConfig config = new PMSMigrationConfig();
        pmsMigrationService.validateMissingOrExtraMappingsForGenericTemplate(list, config);
        verify(pmsMigrationService).validateMissingOrExtraMappingsForMSRCGenericTemplate(list, config, Collections.emptyMap());
        verify(pmsMigrationService).validateMissingOrExtraMappingsForGroupCode(list, config, Collections.emptyMap());
        verify(pmsMigrationService).validateMissingOrExtraMappingsForRoomType(list, config, Collections.emptyMap());
    }

    @Test
    public void validateMissingOrExtraMappingsForMSRCGenericTemplateWhenMSRecodingIsNotEnabled() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "Ms_1", "Ms_1_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "MS_3", "MS_3_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "Ms_4", "Ms_4_new"));
        uploadedMappingList.add(createMappingDto(3, MARKET_SEGMENT_NON_GROUP, "MS_NG_1", "MS_NG_1_NEW"));
        uploadedMappingList.add(createMappingDto(4, MARKET_SEGMENT_NON_GROUP, "MS_NG_3", "MS_NG_3_NEW"));
        uploadedMappingList.add(createMappingDto(5, RATE_CODE, "RC_1", "RC_1_New"));
        uploadedMappingList.add(createMappingDto(6, RATE_CODE, "RC_3", "RC_3_New"));

        PMSMigrationConfig config = getPMSMigrationConfig(true);
        Map<String, Map<String, Integer>> errorMap = new HashMap<>();
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MS_RECODING_ENABLED)).thenReturn(Boolean.FALSE);
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList("Ms_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_CODES)).thenReturn(Arrays.asList("MS_NG_1", "MS_NG_2"));
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList("RC_1", "RC_2"));
        pmsMigrationService.validateMissingOrExtraMappingsForMSRCGenericTemplate(uploadedMappingList, config, errorMap);
        assertEquals(2, errorMap.size());

        Map<String, Integer> extraMappings = errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM);
        assertEquals(4, extraMappings.size());
        assertTrue(extraMappings.keySet().containsAll(Arrays.asList("MS_3", "Ms_4", "MS_NG_3", "RC_3")));

        Map<String, Integer> missingMappings = errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING);
        assertEquals(3, missingMappings.size());
        assertTrue(missingMappings.keySet().containsAll(Arrays.asList("MS_2", "MS_NG_2", "RC_2")));
    }

    @Test
    void validateMissingOrExtraMappingsForClientTemplateWhenAllowClientImportOnAndBeforeJob() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "Ms_1", "Ms_1_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "MS_3", "MS_3_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "Ms_4", "Ms_4_new"));
        uploadedMappingList.add(createMappingDto(3, MARKET_SEGMENT_NON_GROUP, "MS_NG_1", "MS_NG_1_NEW"));
        uploadedMappingList.add(createMappingDto(4, MARKET_SEGMENT_NON_GROUP, "MS_NG_3", "MS_NG_3_NEW"));
        uploadedMappingList.add(createMappingDto(5, RATE_CODE, "RC_1", "RC_1_New"));
        uploadedMappingList.add(createMappingDto(6, RATE_CODE, "RC_3", "RC_3_New"));

        PMSMigrationConfig config = getPMSMigrationConfig(true);
        config.setMigrationState(PMS_MIGRATION_NOT_STARTED);
        config.setUseGenericTemplateForMigration(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PMS_MIGRATION_ENABLED)).thenReturn(Boolean.TRUE);
        doReturn(uploadedMappingList).when(pmsMigrationService).getAllExistingMappings();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList("Ms_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_CODES)).thenReturn(Arrays.asList("MS_NG_1", "MS_NG_2"));
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList("RC_1", "RC_2"));
        Optional<String> errorStr = pmsMigrationService.validateInconsistentMappingTemplateForSpecificTemplate(config);
        assertTrue(errorStr.isEmpty());

//        Map<String, Integer> extraMappings = errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM);
//        assertEquals(4, extraMappings.size());
//        assertTrue(extraMappings.keySet().containsAll(Arrays.asList("MS_3", "Ms_4", "MS_NG_3", "RC_3")));
//
//        Map<String, Integer> missingMappings = errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING);
//        assertEquals(3, missingMappings.size());
//        assertTrue(missingMappings.keySet().containsAll(Arrays.asList("MS_2", "MS_NG_2", "RC_2")));
    }

    @Test
    void validateMissingOrExtraMappingsForClientTemplateWhenAllowClientImportOnAndDuringJob() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "Ms_1", "Ms_1_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "MS_3", "MS_3_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "Ms_4", "Ms_4_new"));
        uploadedMappingList.add(createMappingDto(3, MARKET_SEGMENT_NON_GROUP, "MS_NG_1", "MS_NG_1_NEW"));
        uploadedMappingList.add(createMappingDto(4, MARKET_SEGMENT_NON_GROUP, "MS_NG_3", "MS_NG_3_NEW"));
        uploadedMappingList.add(createMappingDto(5, RATE_CODE, "RC_1", "RC_1_New"));
        uploadedMappingList.add(createMappingDto(6, RATE_CODE, "RC_3", "RC_3_New"));

        PMSMigrationConfig config = getPMSMigrationConfig(true);
        config.setMigrationState(PMS_MIGRATION_READY);
        config.setUseGenericTemplateForMigration(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PMS_MIGRATION_ENABLED)).thenReturn(Boolean.TRUE);
        doReturn(uploadedMappingList).when(pmsMigrationService).getAllExistingMappings();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList("Ms_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_CODES)).thenReturn(Arrays.asList("MS_NG_1", "MS_NG_2"));
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList("RC_1", "RC_2"));
        Optional<String> errorStr = pmsMigrationService.validateInconsistentMappingTemplateForSpecificTemplate(config);

        assertFalse(errorStr.isEmpty());
        assertEquals(PMS_MIGRATION_MAPPING_TEMPLATE_VALIDATION_ERROR, errorStr.get());
    }

    @Test
    void validateMissingOrExtraMappingsWhenAllowClientImportOnAndDuringJob() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "Ms_1", "Ms_1_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "MS_3", "MS_3_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "Ms_4", "Ms_4_new"));
        uploadedMappingList.add(createMappingDto(3, PMSMigrationMappingType.MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE, "MS_NG_1", "MS_NG_1_NEW"));
        uploadedMappingList.add(createMappingDto(4, PMSMigrationMappingType.MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE, "MS_NG_3", "MS_NG_3_NEW"));

        PMSMigrationConfig config = getPMSMigrationConfig(true);
        config.setMigrationState(PMS_MIGRATION_READY);
        config.setUseGenericTemplateForMigration(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_CLIENT_LEVEL_MIGRATION_TEMPLATE_UPLOAD)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PMS_MIGRATION_ENABLED)).thenReturn(Boolean.TRUE);
        doReturn(uploadedMappingList).when(pmsMigrationService).getAllExistingMappings();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList("Ms_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_DISTINCT_MKT_SEG_FOR_BLANK_RATE_CODES)).thenReturn(Arrays.asList("MS_NG_1", "MS_NG_2", "Ms_1"));
        Map<String, Map<String, Integer>> errorMap = pmsMigrationService.validateMissingOrExtraMappings(uploadedMappingList, config);

        assertNull(errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM));
        Map<String, Integer> missingMappings = errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING);
        assertEquals(2, missingMappings.size());
        assertTrue(missingMappings.keySet().containsAll(Arrays.asList("MS_2", "MS_NG_2")));
    }

    @Test
    public void validateMissingOrExtraMappingsForMSRCGenericTemplateWhenMSRecodingIsEnabled() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "MS_1", "MS_1_new"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT, "MS_3", "MS_3_new"));
        uploadedMappingList.add(createMappingDto(3, MARKET_SEGMENT_NON_GROUP, "MS_NG_1", "MS_NG_1_NEW"));
        uploadedMappingList.add(createMappingDto(4, MARKET_SEGMENT_NON_GROUP, "MS_NG_3", "MS_NG_3_NEW"));
        uploadedMappingList.add(createMappingDto(5, RATE_CODE, "RC_1", "RC_1_New"));
        uploadedMappingList.add(createMappingDto(6, RATE_CODE, "RC_3", "RC_3_New"));

        PMSMigrationConfig config = getPMSMigrationConfig(true);
        Map<String, Map<String, Integer>> errorMap = new HashMap<>();
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MS_RECODING_ENABLED)).thenReturn(Boolean.TRUE);
        when(tenantCrudService.findByNamedQuery(MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE,
                QueryParameter.with("businessTypeId", BUSINESS_TYPE_GROUP).parameters())).thenReturn(Arrays.asList("MS_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Arrays.asList("MS_1", "MS_2"));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_MKT_SEG_CODES)).thenReturn(Arrays.asList("MS_NG_1", "MS_NG_2"));
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Arrays.asList("RC_1", "RC_2"));
        pmsMigrationService.validateMissingOrExtraMappingsForMSRCGenericTemplate(uploadedMappingList, config, errorMap);
        assertEquals(2, errorMap.size());

        Map<String, Integer> extraMappings = errorMap.get(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM);
        assertEquals(3, extraMappings.size());
        assertTrue(extraMappings.keySet().containsAll(Arrays.asList("MS_3", "MS_NG_3", "RC_3")));

        Map<String, Integer> missingMappings = errorMap.get(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING);
        assertEquals(3, missingMappings.size());
        assertTrue(missingMappings.keySet().containsAll(Arrays.asList("MS_2", "MS_NG_2", "RC_2")));
    }

    @Test
    public void validateInconsistentMappingTemplateForGenericTemplate_NoChangeInMS() {
        PMSMigrationConfig config = new PMSMigrationConfig();
        pmsMigrationService.validateInconsistentMappingTemplateForGenericTemplate(config);
        verify(pmsMigrationService).validateMissingOrExtraMappingsForGenericTemplate(anyListOf(PMSMigrationMappingImportDTO.class), eq(config));
    }

    @Test
    public void validateInconsistentMappingTemplateForGenericTemplate() {
        PMSMigrationConfig config = new PMSMigrationConfig();
        config.setMktSegChanged(true);
        pmsMigrationService.validateInconsistentMappingTemplateForGenericTemplate(config);
        verify(pmsMigrationService).validateMissingOrExtraMappingsForGenericTemplate(anyListOf(PMSMigrationMappingImportDTO.class), eq(config));
        verify(mktSegRecodingService).isAMSAttributionPresentForAllNewMSAndRCInMSRecoding();
    }

    @Test
    public void validateInconsistentMappingsAndGetError_GenericTemplate() {
        PMSMigrationConfig config = new PMSMigrationConfig();
        config.setUseGenericTemplateForMigration(true);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), any()))
                .thenReturn(config);
        pmsMigrationService.validateInconsistentMappingsAndGetError();
        verify(pmsMigrationService).validateInconsistentMappingTemplateForGenericTemplate(config);
    }

    @Test
    public void shouldValidateRequiredConfigurationAndGenerateAlertIfMappingsAreInconsistent() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        // Mapping template is NOT up-to-date
        doReturn(Collections.emptyList()).when(pmsMigrationService).getAllExistingMappings();
        doReturn(Collections.singletonMap(CODE_AVAILABLE_IN_SYSTEM_BUT_MAPPING_IS_MISSING,
                Collections.singletonMap(PMSMigrationTestUtil.RC_3, 1))).when(pmsMigrationService)
                .validateMissingOrExtraMappings(anyListOf(PMSMigrationMappingImportDTO.class), any(PMSMigrationConfig.class));

        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), any())).thenReturn(new PMSMigrationConfig());
        pmsMigrationService.validateRequiredConfigurationForExhaustiveMigration(
                getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(eq(AlertType.PMSMigrationInconsistentMappingsValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                eq(PMS_MIGRATION_MAPPING_TEMPLATE_VALIDATION_ERROR), // Inconsistent mapping template validation error
                eq(PMS_MIGRATION_VALIDATE_REQUIRED_MAPPINGS_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_MAPPINGS_VALIDATION_REQUIRED)
        );
        verifyZeroInteractions(pmsRevampAMSDataService);
    }

    @Test
    public void shouldValidateRequiredConfigurationAndGenerateAlertIfNewAMSMappingsAreInconsistent() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        // Mapping template is up-to-date

        PMSMigrationConfig pmsConfig = new PMSMigrationConfig();
        pmsConfig.setMktSegChanged(true);
        Map<String, Object> params = new HashMap<>();
        params.put(CLIENT_ID_KEY, null);
        params.put(PROPERTY_ID_KEY, 6);
        doReturn(pmsConfig).when(globalCrudService).findByNamedQuerySingleResult(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE, params);
        doReturn(Collections.emptyList()).when(pmsMigrationService).getAllExistingMappings();
        doReturn(Collections.emptyMap()).when(pmsMigrationService)
                .validateMissingOrExtraMappings(anyListOf(PMSMigrationMappingImportDTO.class), any(PMSMigrationConfig.class));
        doReturn(Collections.emptyList()).when(pmsMigrationService).getAllRateCodeMktSegMappings();
        doReturn(Collections.emptyMap()).when(pmsMigrationService)
                .validateMissingOrExtraMappingsForSecondSheet(anyListOf(PMSMigrationRateCodeMktSegMappingDTO.class), anyBoolean(), any(PMSMigrationConfig.class));

        // But new AMS rules haven't been uploaded
        doReturn(false).when(pmsMigrationService).isAmsAttributionPresentForAllNewMSAndRC(any(PMSMigrationConfig.class));
        pmsMigrationService.validateRequiredConfigurationForExhaustiveMigration(
                getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(eq(AlertType.PMSMigrationInconsistentMappingsValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                eq(PMS_MIGRATION_NEW_AMS_ATTR_MISSING_ERROR), // New AMS rule not uploaded validation error
                eq(PMS_MIGRATION_VALIDATE_REQUIRED_MAPPINGS_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_MAPPINGS_VALIDATION_REQUIRED)
        );
        verify(pmsMigrationService).validateMissingOrExtraMappings(anyListOf(PMSMigrationMappingImportDTO.class), any(PMSMigrationConfig.class));
        verify(pmsMigrationService).isAmsAttributionPresentForAllNewMSAndRC(any(PMSMigrationConfig.class));
        verifyNoMoreInteractions(pmsRevampAMSDataService);
    }

    @Test
    public void isAmsAttributionPresentForAllNewMSAndRC() {
        assertTrue(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(false)));
    }

    @Test
    public void shouldValidateRequiredConfigurationAndNotGenerateAlertIfConfigsAreConsistent() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        // All validations are passed
        doReturn(Collections.emptyList()).when(pmsMigrationService).getAllExistingMappings();
        doReturn(Collections.emptyMap()).when(pmsMigrationService)
                .validateMissingOrExtraMappings(anyListOf(PMSMigrationMappingImportDTO.class), any(PMSMigrationConfig.class));
        doReturn(true).when(pmsMigrationService).isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true));

        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), any())).thenReturn(getPMSMigrationConfig(true));
        pmsMigrationService.validateRequiredConfigurationForExhaustiveMigration(
                getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService, times(0))
                .generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());
    }

    @Test
    public void shouldNotFindIfCCFGHasCompletedWithInvalidJobExecutionId() {
        assertFalse(pmsMigrationService.isCCFGProcessCompleted(null));
        verifyNoMoreInteractions(jobMonitorService, marketSegmentComponent);
    }

    @Test
    public void returnsFalseWhenNoFailedJobFound() {
        final long revampJobExecutionId = 1L;
        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.RUNNING, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(false);

        assertFalse(pmsMigrationService.isCCFGProcessCompleted(revampJobExecutionId));
        verifyZeroInteractions(marketSegmentComponent);
    }

    @Test
    public void returnsFalseIfDataExistsInMktSegProposedTable() {
        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(true);

        assertFalse(pmsMigrationService.isCCFGProcessCompleted(FAILED_JOB_ID));
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent, times(0)).proposedForecastGroupsExists();
    }

    @Test
    public void returnsFalseIfDataExistsInForecastGroupProposedTable() {
        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(false);
        when(marketSegmentComponent.proposedForecastGroupsExists()).thenReturn(true);

        assertFalse(pmsMigrationService.isCCFGProcessCompleted(FAILED_JOB_ID));
        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent).proposedForecastGroupsExists();
    }

    @Test
    public void returnsTrueIfCCFGProcessIsCompleted() {
        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(false);
        when(marketSegmentComponent.proposedForecastGroupsExists()).thenReturn(false);

        assertTrue(pmsMigrationService.isCCFGProcessCompleted(FAILED_JOB_ID));
        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent).proposedForecastGroupsExists();
    }

    @Test
    public void testIsAmsAttributionPresentForAllNewMSRC_IgnoreAttributedExtraMktSegs() {
        when(pmsMigrationMappingService.getNewMarketSegments()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2));
        when(pmsRevampAMSDataService.getAttributedNewMSCodes()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));

        when(pmsMigrationMappingService.getMarketSegmentWiseRateCodes()).thenReturn(MKT_SEG_WISE_RATE_CODES);
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(MKT_SEG_WISE_RATE_CODES);

        assertTrue(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true)));
    }

    @Test
    public void testIsAmsAttributionPresentForAllNewMSRC_IgnoreExtraMktSegsWhenCheckingForRateCodes() {
        when(pmsMigrationMappingService.getNewMarketSegments()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2));
        when(pmsRevampAMSDataService.getAttributedNewMSCodes()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));

        when(pmsMigrationMappingService.getMarketSegmentWiseRateCodes()).thenReturn(MKT_SEG_WISE_RATE_CODES);
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(MKT_SEG_WISE_RATE_CODES_EXTRA_MS4);

        assertTrue(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true)));
    }

    @Test
    public void testIsAmsAttributionPresentForAllNewMSRC_AttrForSomeMSesAreMissing() {
        when(pmsMigrationMappingService.getNewMarketSegments()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));
        when(pmsRevampAMSDataService.getAttributedNewMSCodes()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2));

        when(pmsMigrationMappingService.getMarketSegmentWiseRateCodes()).thenReturn(MKT_SEG_WISE_RATE_CODES);
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(MKT_SEG_WISE_RATE_CODES);

        assertFalse(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true)));
    }

    @Test
    public void testIsAmsAttributionPresentForAllNewMSRC_AttrForSomeRCsAreMissing() {
        when(pmsMigrationMappingService.getNewMarketSegments()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));
        when(pmsRevampAMSDataService.getAttributedNewMSCodes()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));

        when(pmsMigrationMappingService.getMarketSegmentWiseRateCodes()).thenReturn(MKT_SEG_WISE_RATE_CODES);
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(MKT_SEG_WISE_RATE_CODES_MISSING_RC3);

        assertFalse(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true)));
    }

    @Test
    public void testIsAmsAttributionPresentForAllNewMS_NoAttrIsMissing() {
        when(pmsMigrationMappingService.getNewMarketSegments()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));
        when(pmsRevampAMSDataService.getAttributedNewMSCodes()).thenReturn(setOf(PMSMigrationTestUtil.MS_1, PMSMigrationTestUtil.MS_2, PMSMigrationTestUtil.MS_3));

        when(pmsMigrationMappingService.getMarketSegmentWiseRateCodes()).thenReturn(MKT_SEG_WISE_RATE_CODES);
        when(pmsRevampAMSDataService.getMarketSegmentWiseRateCodesFromUpload()).thenReturn(MKT_SEG_WISE_RATE_CODES);

        assertTrue(pmsMigrationService.isAmsAttributionPresentForAllNewMSAndRC(getPMSMigrationConfig(true)));
    }

    @Test
    public void shouldValidateCCFGProcessAndGenerateAlertIfPMSRevampJobHasNotFailed() {
        final long revampJobExecutionId = 1L;
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());
        mockJobExecution(ExecutionStatus.RUNNING, LocalDateTime.now());

        pmsMigrationService.validateCCFGProcessCompletionForExhaustiveMigration(getPMSMigrationTestConfig(PMS_MIGRATION_READY), revampJobExecutionId);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(
                eq(AlertType.PMSMigrationCCFGValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                anyString(),
                eq(PMS_MIGRATION_CCFG_REQUIRED_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_COMMIT_FG_REQUIRED)
        );

        verifyNoMoreInteractions(marketSegmentComponent);
    }

    @Test
    public void shouldValidateCCFGProcessAndGenerateAlertIfPMSRevampJobHasFailedButCCFGJobWasNotCompleted() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);

        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(false);
        pmsMigrationService.validateCCFGProcessCompletionForExhaustiveMigration(getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(
                eq(AlertType.PMSMigrationCCFGValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                anyString(),
                eq(PMS_MIGRATION_CCFG_REQUIRED_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_COMMIT_FG_REQUIRED)
        );

        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verifyNoMoreInteractions(marketSegmentComponent);
    }

    @Test
    public void shouldValidateCCFGProcessAndGenerateAlertIfDataExistsInMktSegProposedTable() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(true);

        pmsMigrationService.validateCCFGProcessCompletionForExhaustiveMigration(getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(
                eq(AlertType.PMSMigrationCCFGValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                anyString(),
                eq(PMS_MIGRATION_CCFG_REQUIRED_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_COMMIT_FG_REQUIRED)
        );

        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent, times(0)).proposedForecastGroupsExists();
    }

    @Test
    public void shouldValidateCCFGProcessAndGenerateAlertIfDataExistsInForecastGroupProposedTable() {
        doNothing().when(pmsMigrationService).generateAlertAndHaltProcessing(any(), any(), any(), any(), any(), any());

        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(false);
        when(marketSegmentComponent.proposedForecastGroupsExists()).thenReturn(true);

        pmsMigrationService.validateCCFGProcessCompletionForExhaustiveMigration(getPMSMigrationTestConfig(PMS_MIGRATION_READY), FAILED_JOB_ID);

        verify(pmsMigrationService).generateAlertAndHaltProcessing(
                eq(AlertType.PMSMigrationCCFGValidation),
                eq(PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + FAILED_JOB_ID),
                anyString(),
                eq(PMS_MIGRATION_CCFG_REQUIRED_EXCEPTION_MESSAGE),
                any(),
                eq(PMS_COMMIT_FG_REQUIRED)
        );

        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent).proposedForecastGroupsExists();
    }

    @Test
    public void shouldValidateCCFGProcessAndUpdateMigrationStateIfCCFGIsCompleted() {
        LocalDateTime lastUpdated = LocalDateTime.now();
        mockJobExecution(ExecutionStatus.FAILED, lastUpdated);
        when(jobMonitorService.anyJobExistsForStatusAfterDate(any(), any(), any(), any())).thenReturn(true);
        when(marketSegmentComponent.proposedMktSegExists()).thenReturn(false);
        when(marketSegmentComponent.proposedForecastGroupsExists()).thenReturn(false);

        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationTestConfig(PMS_MIGRATION_READY);
        pmsMigrationService.validateCCFGProcessCompletionForExhaustiveMigration(pmsMigrationConfig, FAILED_JOB_ID);

        verify(jobMonitorService).anyJobExistsForStatusAfterDate(PacmanWorkContextHelper.getPropertyId(), JobName.CommitForecastGroups.name(), ExecutionStatus.COMPLETED, lastUpdated);
        verify(marketSegmentComponent).proposedMktSegExists();
        verify(marketSegmentComponent).proposedForecastGroupsExists();

        assertEquals(PMS_COMMIT_FG_DONE, pmsMigrationConfig.getMigrationState());
        verify(globalCrudService).save(eq(pmsMigrationConfig));
    }

    @Test
    public void shouldFindMinOccupancyDateOfTotalActivityAsEarliestOccupancyDate() {
        Date expectedOccupancyDateRangeStart = DateUtil.getDate(1, 1, 2018);
        when(dateService.getEarliestOccupancyDate()).thenReturn(expectedOccupancyDateRangeStart);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 6, 13));
        Date actualOccupancyDateRangeStart = pmsMigrationService.findMinOccupancyDateForTotalActivityComparison();
        assertEquals(expectedOccupancyDateRangeStart, actualOccupancyDateRangeStart);
    }

    @Test
    public void shouldFindMinOccupancyDateOfTotalActivityAsPastBackfillBoundedDate() {
        when(dateService.getEarliestOccupancyDate()).thenReturn(DateUtil.getDateWithoutTime(1, 1, 2014));
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 6, 13));
        Date actualOccupancyDateRangeStart = pmsMigrationService.findMinOccupancyDateForTotalActivityComparison();
        assertEquals(DateUtil.getDateWithoutTime(13, 5, 2016), actualOccupancyDateRangeStart);
    }

    @Test
    public void shouldFindMaxOccupancyDateForTotalActivityComparison() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 1, 1));
        when(dateService.getMaxFutureOccupancyDate()).thenReturn(DateUtil.getDateWithoutTime(1, 3, 2019));

        Date actualMaxOccupancyDate = pmsMigrationService.findMaxOccupancyDateForTotalActivityComparison();
        assertEquals(DateUtil.getDateWithoutTime(1, 3, 2019), actualMaxOccupancyDate);
    }

    @Test
    public void shouldNotBeAbleToCompareTotalActivityIfMinSummaryDoesNotExist() {
        assertThrows(TetrisException.class, () -> {
            when(dateService.getEarliestOccupancyDate()).thenReturn(null);
            when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 1, 1));
            pmsMigrationService.findMinOccupancyDateForTotalActivityComparison();
        });
    }

    @Test
    public void shouldNotBeAbleToCompareTotalActivityIfMaxSummaryDoesNotExist() {
        assertThrows(TetrisException.class, () -> {
            when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 1, 1));
            when(dateService.getMaxFutureOccupancyDate()).thenReturn(null);
            pmsMigrationService.findMaxOccupancyDateForTotalActivityComparison();
        });
    }

    @Test
    public void updateNecessaryDataAndEnableToggles() {
        pmsMigrationService.updateNecessaryDataAndEnableToggles();
        String context = "pacman.BSTN.H2";
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(pacmanConfigParamsService).addParameterValue(context, IntegrationConfigParamName.SUSPEND_BDE_AND_CDP.value(), "true");
    }

    @Test
    public void shouldPerformInsertInBatches() {
        List<String> parameterValues = IntStream.rangeClosed(1, 6017).mapToObj(i -> format("({0})", Integer.toString(i))).collect(Collectors.toList());
        pmsMigrationService.performInsertInBatches("test", "insert into table_with_single_column values", parameterValues);

        verify(tenantCrudService, times(7)).executeUpdateByNativeQuery(any());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldVerifyOngoingConfigurationIsFetched() {
        pmsMigrationService.fetchOngoingMigrationConfiguration(1, 2);
        ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);

        verify(globalCrudService).findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), captor.capture());
        assertEquals(1, captor.getValue().get(CLIENT_ID_KEY));
        assertEquals(2, captor.getValue().get(PROPERTY_ID_KEY));
    }

    @Test
    public void syncsGroupStatusCodesIfNecessaryIfOperaToNGI() {
        doNothing().when(pmsMigrationService).syncGroupStatusCodesFromOperaToNGI();
        pmsMigrationService.syncGroupStatusCodesIfNecessary(getPMSMigrationTestConfig(PMS_INBOUND_CONFIGURATION_DONE));
        verify(pmsMigrationService).syncGroupStatusCodesFromOperaToNGI();
    }

    @Test
    public void doesNotSyncGroupStatusCodesIfNecessaryIfOtherThanOperaToNGI() {
        PMSMigrationConfig config = getPMSMigrationTestConfig(PMS_INBOUND_CONFIGURATION_DONE);
        config.setOldSystem("HTNG");
        config.setNewSystem("OPERA");
        pmsMigrationService.syncGroupStatusCodesIfNecessary(config);
        verify(pmsMigrationService, times(0)).syncGroupStatusCodesFromOperaToNGI();
    }

    @Test
    public void configureInboundSystem() {
        PMSMigrationConfig config = getPMSMigrationTestConfig(PMS_COMMIT_FG_DONE);
        doReturn(config).when(pmsMigrationService).fetchOngoingMigrationConfiguration();
        doNothing().when(pmsMigrationService).updateMigrationState(any(), any());
        doNothing().when(pmsMigrationService).syncGroupStatusCodesIfNecessary(config);
        doNothing().when(pmsMigrationService).flipExternalSystem(config);
        doNothing().when(pmsMigrationService).createValidateNewIntegrationDataAlert(config);

        pmsMigrationService.configureInboundSystem();

        verify(pmsMigrationService).updateMigrationState(config, PMS_INBOUND_CONFIGURATION_DONE);
        verify(pmsMigrationService).syncGroupStatusCodesIfNecessary(config);
        verify(pmsMigrationService).flipExternalSystem(config);
        verify(pmsMigrationService).createValidateNewIntegrationDataAlert(config);
    }

    @Test
    public void flipExternalSystem_ToHTNG() {
        PMSMigrationConfig config = getPMSMigrationTestConfig(PMS_INBOUND_CONFIGURATION_DONE);
        config.setOldSystem("OXI");
        config.setNewSystem("HTNG");
        pmsMigrationService.flipExternalSystem(config);
        verify(pacmanConfigParamsService).updateParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.NGI.getConfigParameterValue());
        verify(pacmanConfigParamsService).updateParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSystem.HTNG.getCode());
    }

    @Test
    public void flipExternalSystem_ToOpera() {
        PMSMigrationConfig config = getPMSMigrationTestConfig(PMS_INBOUND_CONFIGURATION_DONE);
        config.setOldSystem("OXI");
        config.setNewSystem("OPERA");
        pmsMigrationService.flipExternalSystem(config);
        verify(pacmanConfigParamsService).updateParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.OPERA.getConfigParameterValue());
        verify(pacmanConfigParamsService).updateParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.None.getConfigParameterValue());
        verifyNoMoreInteractions(pacmanConfigParamsService);
    }

    @Test
    public void shouldValidateAndFailWhenGroupMktSegIsAvailableInSystemButNotGivenInMapping() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "CORP", "CORP_NEW"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT_NON_GROUP, "DISC", "DISC_NEW"));
        uploadedMappingList.add(createMappingDto(3, MARKET_SEGMENT_NON_GROUP, "CORP", "CORP_NEW"));
        uploadedMappingList.add(createMappingDto(4, RATE_CODE, "1DAY", "1DAY"));
        Map<String, Map<String, Integer>> errorMap = new HashMap<>();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Stream.of("DTA").collect(Collectors.toList()));
        pmsMigrationService.validateExtraMappingsForMSRecoding(uploadedMappingList, errorMap);
        assertTrue(errorMap.containsKey(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM), "Expected: " + CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM + " is missing ");
    }

    @Test
    public void shouldValidateAndFailWhenNonGroupMktSegAvailableInSystemButNotGivenInMapping() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "CORP", "CORP_NEW"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT_NON_GROUP, "DTA", "DIA_NEW"));
        uploadedMappingList.add(createMappingDto(3, RATE_CODE, "1DAY", "1DAY"));
        Map<String, Map<String, Integer>> errorMap = new HashMap<>();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Stream.of("CORP").collect(Collectors.toList()));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_DISTINCT_MKT_SEG_FROM_RESERVATIONS)).thenReturn(Arrays.asList("DISC", "CORP"));
        pmsMigrationService.validateExtraMappingsForMSRecoding(uploadedMappingList, errorMap);
        assertTrue(errorMap.containsKey(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM), "Expected: " + CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM + " is missing ");
    }

    @Test
    public void shouldValidateAndFailWhenRateCodeNotAvailableInSystemButGivenInMappings() {
        List<PMSMigrationMappingImportDTO> uploadedMappingList = new ArrayList<>();
        uploadedMappingList.add(createMappingDto(1, MARKET_SEGMENT, "CORP", "CORP_NEW"));
        uploadedMappingList.add(createMappingDto(2, MARKET_SEGMENT_NON_GROUP, "DISC", "DISC_NEW"));
        uploadedMappingList.add(createMappingDto(3, RATE_CODE, "1DAY", "1DAY"));
        Map<String, Map<String, Integer>> errorMap = new HashMap<>();
        when(tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_DISTINCT_GROUP_MKT_SEG)).thenReturn(Stream.of("CORP").collect(Collectors.toList()));
        when(tenantCrudService.findByNamedQuery(MktSeg.GET_DISTINCT_MKT_SEG_FROM_RESERVATIONS)).thenReturn(Arrays.asList("DISC", "CORP"));
        when(tenantCrudService.findByNativeQuery(ReservationNight.GET_DISTINCT_RATE_CODES)).thenReturn(Stream.of("2DAY").collect(Collectors.toList()));
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_RATE_CODES)).thenReturn(Stream.of("3DAY").collect(Collectors.toList()));
        pmsMigrationService.validateExtraMappingsForMSRecoding(uploadedMappingList, errorMap);
        assertTrue(errorMap.containsKey(CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM), "Expected: " + CODE_IS_NOT_ALLOWED_IN_MAPPING_AS_IT_IS_NOT_IN_SYSTEM + " is missing ");
    }

    @Test
    public void isHybridCatchupEnabledForMigratedProperty_No_If_MigrationNotCompletedOrIsAbsent() {
        doReturn(null).when(pmsMigrationService).getCompletedMigrationConfigForPropertyId(TestProperty.H1.getId());
        assertFalse(pmsMigrationService.isHybridCatchupEnabledForMigratedProperty(TestProperty.H1.getId()));
    }

    @Test
    public void isHybridCatchupEnabledForMigratedProperty_No_If_MigrationCompletedButRollbackCatchupNotSupported() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setRollbackCatchupSupported(false);
        doReturn(pmsMigrationConfig).when(pmsMigrationService).getCompletedMigrationConfigForPropertyId(TestProperty.H1.getId());
        assertFalse(pmsMigrationService.isHybridCatchupEnabledForMigratedProperty(TestProperty.H1.getId()));
    }

    @Test
    public void isHybridCatchupEnabledForMigratedProperty_Yes_If_MigrationCompletedAndRollbackCatchupIsSupported() {
        PMSMigrationConfig pmsMigrationConfig = getPMSMigrationConfig();
        pmsMigrationConfig.setRollbackCatchupSupported(true);
        doReturn(pmsMigrationConfig).when(pmsMigrationService).getCompletedMigrationConfigForPropertyId(TestProperty.H1.getId());
        assertTrue(pmsMigrationService.isHybridCatchupEnabledForMigratedProperty(TestProperty.H1.getId()));
    }

    @Test
    public void shouldSaveMSRecodingMappings() {
        //GIVEN
        List<PMSMigrationMappingImportDTO> dtoList = new ArrayList<>();
        dtoList.add(createMappingDto(1, MARKET_SEGMENT, "CORP", "CORP_New"));
        dtoList.add(createMappingDto(2, MARKET_SEGMENT, "OTA", ""));
        //WHEN
        pmsMigrationService.saveMSRecodingMappings(dtoList);
        //THEN
        ArgumentCaptor<List> mappingEntityListArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).deleteAll(PMSMigrationMapping.class);
        verify(tenantCrudService).save(mappingEntityListArgumentCaptor.capture());
        List<PMSMigrationMapping> entitiesForSave = mappingEntityListArgumentCaptor.getValue();
        assertEquals(MARKET_SEGMENT, entitiesForSave.get(0).getCodeType());
        assertEquals("CORP", entitiesForSave.get(0).getCurrentCode());
        assertEquals("CORP_New", entitiesForSave.get(0).getNewEquivalentCode());
        assertEquals(MARKET_SEGMENT, entitiesForSave.get(1).getCodeType());
        assertEquals("OTA", entitiesForSave.get(1).getCurrentCode());
        assertEquals("OTA", entitiesForSave.get(1).getNewEquivalentCode());
    }

    @Test
    public void savesNewEquivalentCodeOfDiscontinuesMktSegsAsBlanks() {
        List<PMSMigrationMappingImportDTO> dtos = new ArrayList<>();
        dtos.add(createMappingDto(1, MARKET_SEGMENT, "CORP", "CORP_NEW"));
        dtos.add(createMappingDtoForDiscontinued(2, MARKET_SEGMENT, "OTA", ""));

        pmsMigrationService.saveMSRecodingMappings(dtos);

        ArgumentCaptor<List> mappingEntityListArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).deleteAll(PMSMigrationMapping.class);
        verify(tenantCrudService).save(mappingEntityListArgumentCaptor.capture());
        List<PMSMigrationMapping> entitiesForSave = mappingEntityListArgumentCaptor.getValue();
        assertEquals(MARKET_SEGMENT, entitiesForSave.get(0).getCodeType());
        assertEquals("CORP", entitiesForSave.get(0).getCurrentCode());
        assertEquals("CORP_NEW", entitiesForSave.get(0).getNewEquivalentCode());
        assertEquals(MARKET_SEGMENT, entitiesForSave.get(1).getCodeType());
        assertEquals("OTA", entitiesForSave.get(1).getCurrentCode());
        assertEquals("", entitiesForSave.get(1).getNewEquivalentCode());
    }

    @Test
    public void testGenerateTotalActivityDeltaBetweenForPMSMigration() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(new PMSMigrationConfig());
        when(pmsMigrationBackupService.backedUpTableExists(TotalActivityPmsBkp.TABLE_NAME)).thenReturn(Boolean.TRUE);
        pmsMigrationService.generateTotalActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate));
        verify(mktSegRecodingService).fetchInProgressRecodingConfiguration();
        verify(tenantCrudService).findByNamedQuery(eq(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService, times(0)).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE), anyMap(), Matchers.<RowMapper<TotalActivity>>any());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_FROM_TOTAL_ACTIVITY), anyMap());
    }

    @Test
    public void testGenerateTotalActivityDeltaBetweenForPMSMigration_GenericTemplate() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        PMSMigrationConfig pmsConfig = new PMSMigrationConfig();
        pmsConfig.setUseGenericTemplateForMigration(true);
        when(globalCrudService.<PMSMigrationConfig>findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(pmsConfig);
        when(pmsMigrationBackupService.backedUpTableExists(TotalActivityPmsBkp.TABLE_NAME)).thenReturn(Boolean.TRUE);
        pmsMigrationService.generateTotalActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate));

        verify(tenantCrudService, times(0)).findByNamedQuery(eq(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID), anyMap());

        verify(tenantCrudService).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE), anyMap(), Matchers.<RowMapper<TotalActivity>>any());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_FROM_TOTAL_ACTIVITY), anyMap());

    }

    @Test
    public void testGenerateTotalActivityDeltaBetweenForMktSegRecoding() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(null);
        when(mktSegRecodingService.fetchInProgressRecodingConfiguration()).thenReturn(new MktSegRecodingConfig());
        pmsMigrationService.generateTotalActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate));

        verify(tenantCrudService, times(0)).findByNamedQuery(eq(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID), anyMap());

        verify(tenantCrudService).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE), anyMap(), Matchers.<RowMapper<TotalActivity>>any());
        verify(tenantCrudService).findByNamedQuery(eq(TotalActivityPmsBkp.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_FROM_TOTAL_ACTIVITY), anyMap());

        verifyZeroInteractions(pmsMigrationBackupService);
    }

    @Test
    public void testGenerateAccomActivityDeltaBetweenForPMSMigration_NonGenericTemplate() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setUseGenericTemplateForMigration(false);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(pmsMigrationConfig);
        when(pmsMigrationBackupService.backedUpTableExists(AccomActivityPmsBkp.TABLE_NAME)).thenReturn(Boolean.TRUE);
        when(pmsMigrationBackupService.backedUpTableExists("accom_type_pms_bkp")).thenReturn(Boolean.TRUE);

        pmsMigrationService.generateAccomActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate), new ArrayList<>());
        verify(tenantCrudService).findByNativeQuery("select accom_type_id, accom_type_code from accom_type_pms_bkp where status_id = 1 and system_default = 0");
        verify(tenantCrudService).findByNamedQuery(eq(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE), anyMap());

        verify(mktSegRecodingService).fetchInProgressRecodingConfiguration();
        verify(tenantCrudService, times(0)).findByNativeQuery("select accom_type_id, accom_type_code from accom_type where status_id = 1 and system_default = 0");
        verify(tenantCrudService, times(0)).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE_AND_ACCOM_TYPE_ID), anyMap(), Matchers.<RowMapper<AccomActivity>>any());
        verify(tenantCrudService, times(0)).findByNamedQuery(
                eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE_FROM_ACCOM_ACTIVITY), anyMap());
    }

    @Test
    public void testGenerateAccomActivityDeltaBetweenForPMSMigration_RoomTypeChanges_And_GenericTemplate() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setRoomTypeChanged(true);
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(pmsMigrationConfig);
        when(pmsMigrationBackupService.backedUpTableExists(AccomActivityPmsBkp.TABLE_NAME)).thenReturn(Boolean.TRUE);
        when(pmsMigrationBackupService.backedUpTableExists("accom_type_pms_bkp")).thenReturn(Boolean.TRUE);

        pmsMigrationService.generateAccomActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate), new ArrayList<>());
        verify(tenantCrudService).findByNativeQuery("select accom_type_id, accom_type_code from accom_type_pms_bkp where status_id = 1 and system_default = 0");
        verify(tenantCrudService).findByNamedQuery(eq(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE), anyMap());

        verify(mktSegRecodingService).fetchInProgressRecodingConfiguration();
        verify(tenantCrudService, times(0)).findByNativeQuery("select accom_type_id, accom_type_code from accom_type where status_id = 1 and system_default = 0");
        verify(tenantCrudService, times(0)).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE_AND_ACCOM_TYPE_ID), anyMap(), Matchers.<RowMapper<AccomActivity>>any());
        verify(tenantCrudService, times(0)).findByNamedQuery(
                eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE_FROM_ACCOM_ACTIVITY), anyMap());
    }

    @Test
    public void testGenerateAccomActivityDeltaBetweenForPMSMigration_NoRoomTypeChanges_And_GenericTemplate() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setRoomTypeChanged(false);
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(pmsMigrationConfig);
        when(pmsMigrationBackupService.backedUpTableExists(AccomActivityPmsBkp.TABLE_NAME)).thenReturn(Boolean.TRUE);
        when(pmsMigrationBackupService.backedUpTableExists("accom_type_pms_bkp")).thenReturn(Boolean.TRUE);

        pmsMigrationService.generateAccomActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate), new ArrayList<>());

        verify(tenantCrudService).findByNativeQuery("select accom_type_id, accom_type_code from accom_type_pms_bkp where status_id = 1 and system_default = 0");
        verify(tenantCrudService).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE_AND_ACCOM_TYPE_ID), anyMap(), Matchers.<RowMapper<AccomActivity>>any());
        verify(tenantCrudService).findByNamedQuery(
                eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE), anyMap());


        verify(pmsMigrationBackupService).backedUpTableExists(AccomActivityPmsBkp.TABLE_NAME);
        verify(pmsMigrationBackupService).backedUpTableExists("accom_type_pms_bkp");
        verify(tenantCrudService).findByNativeQuery("select accom_type_id, accom_type_code from accom_type_pms_bkp where status_id = 1 and system_default = 0");
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE_FROM_ACCOM_ACTIVITY), anyMap());

    }

    @Test
    public void testGenerateAccomActivityDeltaBetweenForMktSegRecoding() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 1);

        when(globalCrudService.findByNamedQuerySingleResult(eq(PMSMigrationConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE), anyMap())).thenReturn(null);
        when(mktSegRecodingService.fetchInProgressRecodingConfiguration()).thenReturn(new MktSegRecodingConfig());
        pmsMigrationService.generateAccomActivityDeltaBetween(LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate), new ArrayList<>());

        verify(tenantCrudService).findByNativeQuery("select accom_type_id, accom_type_code from accom_type where status_id = 1 and system_default = 0");
        verify(tenantCrudService).findByNativeQuery(
                eq(MktSegAccomActivity.GET_BY_OCCUPANCY_DATE_RANGE_GROUPED_BY_OCCUPANCY_DATE_AND_ACCOM_TYPE_ID), anyMap(), Matchers.<RowMapper<AccomActivity>>any());
        verify(tenantCrudService).findByNamedQuery(
                eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE_FROM_ACCOM_ACTIVITY), anyMap());


        verify(pmsMigrationBackupService, times(0)).backedUpTableExists(AccomActivityPmsBkp.TABLE_NAME);
        verify(pmsMigrationBackupService, times(0)).backedUpTableExists("accom_type_pms_bkp");
        verify(tenantCrudService, times(0)).findByNativeQuery("select accom_type_id, accom_type_code from accom_type_pms_bkp where status_id = 1 and system_default = 0");
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID), anyMap());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(AccomActivityPmsBkp.BY_PROPERTY_ID_AND_OCCUPANCY_DATE_RANGE), anyMap());

    }

    @Test
    void saveMktSegRecodingConfig_NGIPROP() {
        var configDto = new MktSegRecodingConfigDto();
        configDto.setClient(clientBSTN);
        configDto.setProperty(propertyH2);
        configDto.setRecodingTemplateType(MktSegRecodingConfigDto.RecodingTemplateType.CLIENT_SPECIFIC);
        java.time.LocalDate lastExtractDate = java.time.LocalDate.of(2021, Month.APRIL, 2);
        configDto.setLastExtractDate(lastExtractDate);
        when(propertyService.isNGI(H_2_ID)).thenReturn(true);
        when(propertyService.getReservationSubSystem(H_2_ID)).thenReturn(ExternalSubSystem.HTNG);

        pmsMigrationService.saveMktSegRecodingConfig(configDto);

        var savedConfig = ArgumentCaptor.forClass(PMSMigrationConfig.class);
        verify(globalCrudService).save(savedConfig.capture());
        PMSMigrationConfig config = savedConfig.getValue();
        assertEquals(clientBSTN, config.getClient());
        assertEquals(propertyH2, config.getProperty());
        assertEquals(ExternalSubSystem.HTNG.getConfigParameterValue(), config.getOldSystem());
        assertEquals(ExternalSubSystem.HTNG.getConfigParameterValue(), config.getNewSystem());
        assertEquals(LocalDateUtils.toDate(lastExtractDate), config.getOldSysDate());
        assertFalse(config.isRollbackCatchupSupported());
        assertTrue(config.isOnlyMktSegRecoding());
        assertTrue(config.isMktSegChanged());
        assertFalse(config.isRoomTypeChanged());
        assertFalse(config.isGroupCodeChanged());
        assertFalse(config.isUseGenericTemplateForMigration());
    }

    @Test
    void saveMktSegRecodingConfig_OPERAPROP() {
        var configDto = new MktSegRecodingConfigDto();
        configDto.setClient(clientBSTN);
        configDto.setProperty(propertyH2);
        configDto.setRecodingTemplateType(MktSegRecodingConfigDto.RecodingTemplateType.CLIENT_SPECIFIC);
        java.time.LocalDate lastExtractDate = java.time.LocalDate.of(2021, Month.APRIL, 2);
        configDto.setLastExtractDate(lastExtractDate);
        when(propertyService.isNGI(H_2_ID)).thenReturn(false);
        when(propertyService.getReservationSystem(H_2_ID)).thenReturn(ReservationSystem.OPERA);

        pmsMigrationService.saveMktSegRecodingConfig(configDto);

        var savedConfig = ArgumentCaptor.forClass(PMSMigrationConfig.class);
        verify(globalCrudService).save(savedConfig.capture());
        PMSMigrationConfig config = savedConfig.getValue();
        assertEquals(clientBSTN, config.getClient());
        assertEquals(propertyH2, config.getProperty());
        assertEquals(ReservationSystem.OPERA.getConfigParameterValue(), config.getOldSystem());
        assertEquals(ReservationSystem.OPERA.getConfigParameterValue(), config.getNewSystem());
        assertEquals(LocalDateUtils.toDate(lastExtractDate), config.getOldSysDate());
        assertFalse(config.isRollbackCatchupSupported());
        assertTrue(config.isOnlyMktSegRecoding());
        assertTrue(config.isMktSegChanged());
        assertFalse(config.isRoomTypeChanged());
        assertFalse(config.isGroupCodeChanged());
        assertFalse(config.isUseGenericTemplateForMigration());
    }

    private PMSMigrationMappingImportDTO createMappingDtoForDiscontinued(int id, PMSMigrationMappingType codeType, String currentCode, String newCode) {
        PMSMigrationMappingImportDTO mappingDto = new PMSMigrationMappingImportDTO();
        mappingDto.setId(id);
        mappingDto.setPropertyCode("H1");
        mappingDto.setCodeType(codeType.getDisplayTextKey());
        mappingDto.setCurrentCode(currentCode);
        mappingDto.setNewEquivalentCode(newCode);
        mappingDto.setDiscontinued(YES);
        mappingDto.setIsPrimaryCodeForOneToManySplits("NO");
        return mappingDto;
    }

    private PMSMigrationMappingImportDTO createMappingDto(int id, PMSMigrationMappingType codeType, String currentCode, String newCode) {
        PMSMigrationMappingImportDTO mappingDto = new PMSMigrationMappingImportDTO();
        mappingDto.setId(id);
        mappingDto.setPropertyCode("H1");
        mappingDto.setCodeType(codeType.getDisplayTextKey());
        mappingDto.setCurrentCode(currentCode);
        mappingDto.setNewEquivalentCode(newCode);
        mappingDto.setDiscontinued(NO);
        mappingDto.setIsPrimaryCodeForOneToManySplits("NO");
        return mappingDto;
    }

    private void mockJobExecution(ExecutionStatus executionStatus, LocalDateTime lastUpdated) {
        JobExecution jobExecution = createJobExecution(executionStatus, lastUpdated);
        jobExecution.setLastUpdated(lastUpdated);
        when(jobMonitorService.getJobExecution(any())).thenReturn(jobExecution);
    }

    private JobExecution createJobExecution(ExecutionStatus running, LocalDateTime lastUpdated) {
        JobExecution jobExecution = new JobExecution();
        jobExecution.setJobExecutionId(FAILED_JOB_ID);
        jobExecution.setStatus(running.name());
        jobExecution.setLastUpdated(lastUpdated);
        return jobExecution;
    }

    private List<PMSMigrationMapping> getExistingMappings() {
        PMSMigrationMapping mapping1 = new PMSMigrationMapping();
        mapping1.setIsPrimaryCodeForOneToManySplits(null);
        PMSMigrationMapping mapping2 = new PMSMigrationMapping();
        mapping2.setIsPrimaryCodeForOneToManySplits(Boolean.FALSE);
        PMSMigrationMapping mapping3 = new PMSMigrationMapping();
        mapping3.setIsPrimaryCodeForOneToManySplits(Boolean.TRUE);
        return Arrays.asList(mapping1, mapping2, mapping3);
    }

    private void assertCatchupJobParams(Date expectedCatchupStartDate, Date expectedCatchupEndDate, Map<String, Object> actualJobParams) {
        assertEquals(H_2_ID, actualJobParams.get(JobParameterKey.PROPERTY_ID));
        assertEquals(BSTN, actualJobParams.get(JobParameterKey.CLIENT_CODE));
        assertEquals(H_2, actualJobParams.get(JobParameterKey.PROPERTY_CODE));
        assertEquals(_11403, actualJobParams.get(JobParameterKey.USER_ID));
        assertEquals(DateUtils.truncate(expectedCatchupStartDate, Calendar.DATE).toString(), actualJobParams.get(JobParameterKey.DATE_START));
        assertEquals(DateUtils.truncate(expectedCatchupEndDate, Calendar.DATE).toString(), actualJobParams.get(JobParameterKey.DATE_END));
        assertEquals(CatchupMode.ACTIVITY_ONLY.toString(), actualJobParams.get(JobParameterKey.CATCHUP_MODE));
        assertNotNull(actualJobParams.get(JobParameterKey.DATE));
    }

    private void assertOperaCatchupJobParams(Date startDate, Date endDate, Date actualEndDate, Map<String, Object> actualJobParams) {
        assertCatchupJobParams(startDate, endDate, actualJobParams);
        assertEquals(actualEndDate.toString(), actualJobParams.get(JobParameterKey.PMS_MIGRATION_CATCHUP_END_DATE));
    }

    private InfoMgrTypeEntity getMockedInfoMgrTypeEntity(AlertType alertType, boolean isEnabled) {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setName(alertType.getName());
        alertTypeEntity.setEnabled(isEnabled);
        return alertTypeEntity;
    }

    private PMSMigrationConfig getPMSMigrationTestConfig(PMSMigrationState state) {
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setClient(clientBSTN);
        pmsMigrationConfig.setProperty(propertyH2);
        pmsMigrationConfig.setMigrationState(state);
        pmsMigrationConfig.setOldSystem("OPERA");
        pmsMigrationConfig.setNewSystem("OXI");
        pmsMigrationConfig.setOldSysDate(DateUtil.getDateWithoutTime(5, 4, 2018));
        pmsMigrationConfig.setNewSysDate(DateUtil.getDateWithoutTime(10, 4, 2018));
        return pmsMigrationConfig;
    }

    private CatchupParameters mockCatchupParameters() {
        CatchupParameters catchupParameters = new CatchupParameters();
        catchupParameters.setStartDate(LocalDate.fromDateFields(DateUtil.getDateWithoutTime(1, 1, 2018)));
        catchupParameters.setEndDate(LocalDate.fromDateFields(DateUtil.getDateWithoutTime(15, 4, 2018)));
        catchupParameters.setCatchupMode(CatchupMode.ACTIVITY_ONLY);
        return catchupParameters;
    }

    private AccomType getAccomType(String code) {
        AccomType at = new AccomType();
        at.setAccomTypeCode(code);
        return at;
    }

    private List<PMSMigrationRateCodeMktSegMappingDTO> getRateCodeMappingDtoList() {
        List<PMSMigrationRateCodeMktSegMappingDTO> list = new ArrayList<>();
        list.add(getRateCodeMappingDto(1, PMSMigrationTestUtil.RC_1));
        list.add(getRateCodeMappingDto(1, PMSMigrationTestUtil.rc_1));
        list.add(getRateCodeMappingDto(2, PMSMigrationTestUtil.RC_2));
        return list;
    }

    private PMSMigrationRateCodeMktSegMappingDTO getRateCodeMappingDto(int id, String rateCode) {
        PMSMigrationRateCodeMktSegMappingDTO dto = new PMSMigrationRateCodeMktSegMappingDTO();
        dto.setId(id);
        dto.setCurrentRateCode(rateCode);
        return dto;
    }

    private List<PMSMigrationMappingImportDTO> getDtoList() {
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        int i = 1;
        list.add(getDto(i++, GROUP_CODE, GC_1));
        list.add(getDto(i++, GROUP_CODE, GC_2));
        list.add(getDto(i++, ROOM_TYPE, RT_1));
        list.add(getDto(i++, ROOM_TYPE, RT_2));
        list.add(getDto(i++, PMS_CRS_MARKET_SEGMENT, PMSMigrationTestUtil.MS_1));
        list.add(getDto(i++, PMS_CRS_MARKET_SEGMENT, PMSMigrationTestUtil.MS_2));
        return list;
    }

    private List<PMSMigrationMappingImportDTO> getMSDtoList() {
        List<PMSMigrationMappingImportDTO> list = new ArrayList<>();
        int i = 1;
        list.add(getDto(i++, PMS_CRS_MARKET_SEGMENT, PMSMigrationTestUtil.MS_1));
        list.add(getDto(i++, PMS_CRS_MARKET_SEGMENT, PMSMigrationTestUtil.MS_2));
        return list;
    }

    private PMSMigrationMappingImportDTO getDto(Integer id, String codeType, String currentCode) {
        PMSMigrationMappingImportDTO dto = new PMSMigrationMappingImportDTO();
        dto.setId(id);
        dto.setCodeType(codeType);
        dto.setCurrentCode(currentCode);
        return dto;
    }
}