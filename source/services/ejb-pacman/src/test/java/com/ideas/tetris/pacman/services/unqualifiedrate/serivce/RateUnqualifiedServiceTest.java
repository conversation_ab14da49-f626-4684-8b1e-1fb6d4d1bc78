package com.ideas.tetris.pacman.services.unqualifiedrate.serivce;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.rates.UnqualifiedRateConverter;
import com.ideas.tetris.pacman.services.unqualifiedrate.component.RateUnqualifiedComponent;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.Season;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.SeasonDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum;
import com.ideas.tetris.pacman.util.ConfigParamsUtil;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum.CREATE;
import static com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum.DELETE;
import static com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum.UNCHANGED;
import static com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum.UPDATE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class RateUnqualifiedServiceTest extends AbstractG3JupiterTest {

    private static final String CURRENCY_USD = "USD";
    @Mock
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Mock
    private ConfigParamsUtil configParamsUtil;
    @InjectMocks
    private RateUnqualifiedService service;
    @Mock
    private UnqualifiedRateConverter rateConverter;
    @Mock
    private RateUnqualifiedComponent rateUnqualifiedComponent;
    @Captor
    private ArgumentCaptor<List<Map<String, Object>>> argumentCaptor;
    @Captor
    private ArgumentCaptor<RateUnqualified> rateUnqualifiedArgumentCaptor;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSaveFromMap() {
        List<Map<String, Object>> listToSave = new ArrayList<>();
        listToSave.add(MapBuilder.with("map1", "value2").get());
        listToSave.add(MapBuilder.with("map2", "value2").get());

        when(configParamsUtil.getCurrency()).thenReturn(CURRENCY_USD);
        when(rateConverter.convert(listToSave)).thenReturn(Arrays.asList(new RateUnqualified(), new RateUnqualified()));

        service.saveFromMap(listToSave);

        verify(rateConverter).convert(argumentCaptor.capture());
        List<Map<String, Object>> rates = argumentCaptor.getValue();
        assertEquals(rates.size(), 2);
        assertEquals(CURRENCY_USD, rates.get(0).get("currency"));
        assertEquals(CURRENCY_USD, rates.get(1).get("currency"));
    }

    @Test
    public void testGetRateUnqualifiedByProperty() {
        service.getRateUnqualifiedByProperty(100);
        verify(rateUnqualifiedComponent).getRateUnqualifiedByProperty(100);
    }

    @Test
    public void testIsAvailableOnArrivalSelected() {
        service.isAvailableOnArrivalSelected();
        verify(rateUnqualifiedComponent).isAvailableOnArrivalSelected();
    }

    @Test
    public void testIsLOSSelected() {
        service.isLOSSelected();
        verify(rateUnqualifiedComponent).isLOSSelected();

    }

    @Test
    public void testGetAccomClassDetailsByRateUnqualified() {
        service.getAccomClassDetailsByRateUnqualified(100);
        verify(rateUnqualifiedComponent).getAccomClassDetailsByRateUnqualified(100);
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClass() {
        service.getRateUnqualfiedDetailsByAccomClass(100, 100);
        verify(rateUnqualifiedComponent).getRateUnqualfiedDetailsByAccomClass(100, 100);

    }

    @Test
    public void testGetRateUnqualifiedByAccomClassId() {
        service.getRateUnqualifiedByAccomClassId(100);
        verify(rateUnqualifiedComponent).getRateUnqualifiedByAccomClassId(100);
    }

    @Test
    public void testDeleteRateUnqualifiedClosed() {
        service.deleteRateUnqualifiedClosed(100);
        verify(rateUnqualifiedComponent).deleteRateUnqualifiedClosed(100);

    }

    @Test
    public void testDeleteRateUnqualifiedLOSOverride() {
        service.deleteRateUnqualifiedLOSOverride(100);
        verify(rateUnqualifiedComponent).deleteRateUnqualifiedLOSOverride(100);
    }

    @Test
    public void testDeleteRateUnqualifiedUserOverride() {
        service.deleteRateUnqualifiedUserOverride(100);
        verify(rateUnqualifiedComponent).deleteRateUnqualifiedUserOverride(100);
    }

    @Test
    public void testfetchActiveAccomTypes() {
        service.fetchActiveAccomTypes();
        verify(rateUnqualifiedComponent).fetchActiveAccomTypes();
    }

    @Test
    public void shouldBeAbleToSplitWhenExistingSeasonIsCompletelyOverlappedByNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    public void shouldMarkSeasonForUpdateWhenStartDateEndDateIsSimilarToNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("300.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("300.0"), CREATE, 12, null);
    }

    @Test
    public void shouldBeAbleToSplitWhenExistingSeasonIsCompletelyOverlapByNewSeasonWithSameStartDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(null, 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    public void shouldBeAbleToSplitWhenExistingSeasonIsCompletelyOverlapByNewSeasonWithSameEndDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(null, 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(15, 0, 2016), new DateParameter(31, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1"), UNCHANGED, 12, null);
    }

    @Test
    public void shouldBeAbleToSplitWhenMoreThanOneExistingSeasonIsCompletelyOverlapByNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason1 = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1"), 12, null, UNCHANGED));
        existingSeason1.setSeasonDetails(existingSeasonDetails);
        Season existingSeason2 = createSeasonWith(new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        existingSeason2.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason1);
        existingSeasons.add(existingSeason2);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenOneExistingSeasonIsCompletelyOverlapByNewSeasonAndAnotherExistingSeasonIsOverlappedByThEndOfSameNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason1 = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1"), 12, null, UNCHANGED));
        existingSeason1.setSeasonDetails(existingSeasonDetails);
        Season existingSeason2 = createSeasonWith(new DateParameter(17, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        existingSeason2.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason1);
        existingSeasons.add(existingSeason2);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(21, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenOneExistingSeasonIsCompletelyOverlapByNewSeasonAndAnotherExistingSeasonIsOverlappedByTheStartOfSameNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason1.setSeasonDetails(existingSeasonDetails);
        Season existingSeason2 = createSeasonWith(new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        existingSeason2.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason1);
        existingSeasons.add(existingSeason2);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(9, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenOneExistingSeasonIsCompletelyOverlapByNewSeasonAndOtherExistingSeasonIsOverlappedByTheStartAndEndOfSameNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(5, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(10, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason1.setSeasonDetails(existingSeasonDetails);
        Season existingSeason2 = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        existingSeason2.setSeasonDetails(existingSeasonDetails);
        Season existingSeason3 = createSeasonWith(new DateParameter(21, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        existingSeason3.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason1);
        existingSeasons.add(existingSeason2);
        existingSeasons.add(existingSeason3);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(4, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(4, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(5, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(15, 0, 2016), new DateParameter(20, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(3), new DateParameter(26, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenEndDateOfExistingSeasonIsSimilarToStartDateOfNewSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(14, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(15, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenEndDateOfNewSeasonIsSimilarToStartDateOfExistingSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(16, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenNewSeasonIsCompletelyOverlappedByExistingSeasonWithSimilarEndDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(14, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(15, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenNewSeasonIsCompletelyOverlappedByExistingSeasonWithSimilarStartDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(16, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenExistingSeasonStartAndEndDateIsOverlappedByDifferentNewSeasons() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(12, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(16, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(12, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(13, 0, 2016), new DateParameter(15, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(16, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenExistingSeasonMeetsThreeNewSeasonsOverlappedWithEachOther() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(12, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(16, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        Season season3 = createSeasonWith(new DateParameter(5, 0, 2016), new DateParameter(17, 0, 2016), CREATE);
        season3.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        newSeasons.add(season3);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(4, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(4, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(5, 0, 2016), new DateParameter(17, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(13, 0, 2016), new DateParameter(15, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), DELETE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(3), new DateParameter(18, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    private void assertSeasonDetail(SeasonDetail actualSeasonDetail, String roomClassName, BigDecimal expectedRate, ActionKeyEnum expectedActionKey, Integer expectedAccomTypeId, Integer expectedSeasonDetailId) {
        assertEquals(roomClassName, actualSeasonDetail.getRoomClassName());
        assertEquals(1, actualSeasonDetail.getRoomClassOrder().intValue());
        assertEquals(expectedRate.setScale(2), actualSeasonDetail.getMonday().setScale(2));
        assertEquals(expectedActionKey, actualSeasonDetail.getActionKey());
        assertEquals(expectedAccomTypeId, actualSeasonDetail.getAccomTypeId());
        assertEquals(expectedSeasonDetailId, actualSeasonDetail.getId());
    }

    private void assertSeason(Season actualSeason, DateParameter expectedStartDate, DateParameter expectedEndDate, ActionKeyEnum expectedActionKey) {
        assertEquals(expectedStartDate, actualSeason.getDetailsStartDate());
        assertEquals(expectedEndDate, actualSeason.getDetailsEndDate());
        assertEquals(expectedActionKey, actualSeason.getActionKey());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenExistingSeasonStartAndEndDateSimilarToNewSeasonsStartAndEndDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(10, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(20, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(10, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

        assertSeason(splittedSeasons.get(1), new DateParameter(11, 0, 2016), new DateParameter(19, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);

        assertSeason(splittedSeasons.get(2), new DateParameter(20, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenNewSeasonOverlappsWithinExistingSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(9, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);

        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

        assertSeason(splittedSeasons.get(2), new DateParameter(21, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenMultipleNewSeasonsOverlappsWithinSingleExistingSeasonAndSecondSeasonHasSameEndDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(16, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(4, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(9, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(16, 0, 2016), new DateParameter(31, 0, 2016), DELETE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UNCHANGED, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(3), new DateParameter(16, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

    }

    @Test
    public void shouldSplitWhenMultipleNewSeasonsOverlappsWithinSingleExistingSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(31, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(5, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(9, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(16, 0, 2016), new DateParameter(19, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(3), new DateParameter(20, 0, 2016), new DateParameter(25, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(4), new DateParameter(26, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(4).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(4).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenMultipleNewSeasonsOverlapsWithinSingleExistingSeasonAndOtherNewSeasonsOverlapsTheStartAndEndPartOfExistingSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season1 = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(7, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season1.setSeasonDetails(seasonDetails);
        Season season2 = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        season2.setSeasonDetails(seasonDetails);
        Season season3 = createSeasonWith(new DateParameter(18, 0, 2016), new DateParameter(21, 0, 2016), CREATE);
        season3.setSeasonDetails(seasonDetails);
        Season season4 = createSeasonWith(new DateParameter(23, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        season4.setSeasonDetails(seasonDetails);
        newSeasons.add(season1);
        newSeasons.add(season2);
        newSeasons.add(season3);
        newSeasons.add(season4);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(5, 0, 2016), new DateParameter(25, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(31, 11, 2015).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(7, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(7, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

        assertSeason(splittedSeasons.get(1), new DateParameter(8, 0, 2016), new DateParameter(9, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);

        assertSeason(splittedSeasons.get(2), new DateParameter(10, 0, 2016), new DateParameter(15, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

        assertSeason(splittedSeasons.get(3), new DateParameter(16, 0, 2016), new DateParameter(17, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(3).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);

        assertSeason(splittedSeasons.get(4), new DateParameter(18, 0, 2016), new DateParameter(21, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(4).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(4).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);

        assertSeason(splittedSeasons.get(5), new DateParameter(22, 0, 2016), new DateParameter(22, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(5).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(5).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);

        assertSeason(splittedSeasons.get(6), new DateParameter(23, 0, 2016), new DateParameter(31, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(6).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(6).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenSystemDateIsBetweenExistingSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, 1, UPDATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, 2, UPDATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 12, 2, UPDATE));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(15, 0, 2016).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(10, 0, 2016), new DateParameter(14, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("200.0"), UPDATE, 12, 2);
        assertSeason(splittedSeasons.get(1), new DateParameter(15, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenNewSeasonHasSimilarDatesAsExistingSeasonAndSystemDateIsBetweenSeason() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(7, 0, 2016).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(6, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(7, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSplitWhenSystemDateFallsBetweenExistingSeasonAndNewSeasonEndDateIsEqualsToSystemDate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(7, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 12, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(1, 0, 2016), new DateParameter(20, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(7, 0, 2016).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(3, splittedSeasons.size());
        assertSeason(splittedSeasons.get(0), new DateParameter(1, 0, 2016), new DateParameter(6, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(1), new DateParameter(7, 0, 2016), new DateParameter(7, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("100.0"), CREATE, 12, null);
        assertSeason(splittedSeasons.get(2), new DateParameter(8, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), CREATE, 11, null);
        assertSeasonDetail(splittedSeasons.get(2).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldIgnoreSuchSeasonDetailsWhichDoesNotHaveIdWithNoRatesForRoomTypeButMarkedAsUpdate() {
        //GIVEN
        List<Season> newSeasons = new ArrayList<>();
        Season season = createSeasonWith(new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        List<SeasonDetail> seasonDetails = new ArrayList<>();
        seasonDetails.add(createSeasonDetailWith(new BigDecimal("100.0"), 11, null, CREATE));
        season.setSeasonDetails(seasonDetails);
        newSeasons.add(season);
        List<Season> existingSeasons = new ArrayList<>();
        Season existingSeason = createSeasonWith(new DateParameter(15, 0, 2016), new DateParameter(30, 0, 2016), UPDATE);
        List<SeasonDetail> existingSeasonDetails = new ArrayList<>();
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("200.0"), 11, 1, UPDATE));
        existingSeasonDetails.add(createSeasonDetailWith(new BigDecimal("-1.0"), 12, null, UNCHANGED));
        existingSeason.setSeasonDetails(existingSeasonDetails);
        existingSeasons.add(existingSeason);
        DateService mockDateService = mock(DateService.class);
        service.dateService = mockDateService;
        when(mockDateService.getCaughtUpDate()).thenReturn(new DateParameter(1, 0, 2016).getTime());
        service.seasonService = new SeasonService();
        //WHEN
        List<Season> splittedSeasons = service.applySplit(newSeasons, existingSeasons);
        //THEN
        assertEquals(2, splittedSeasons.size());
        assertSeason(splittedSeasons.get(1), new DateParameter(21, 0, 2016), new DateParameter(30, 0, 2016), UPDATE);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(0), "STD", new BigDecimal("200.0"), UPDATE, 11, 1);
        assertSeasonDetail(splittedSeasons.get(1).getSeasonDetails().get(1), "STD", new BigDecimal("-1.0"), UNCHANGED, 12, null);
        assertSeason(splittedSeasons.get(0), new DateParameter(10, 0, 2016), new DateParameter(20, 0, 2016), CREATE);
        assertSeasonDetail(splittedSeasons.get(0).getSeasonDetails().get(0), "STD", new BigDecimal("100.0"), CREATE, 11, null);
    }

    public Season createSeasonWith(DateParameter detailsStartDate, DateParameter detailsEndDate, ActionKeyEnum actionKey) {
        Season season = new Season();
        season.setDetailsStartDate(detailsStartDate);
        season.setDetailsEndDate(detailsEndDate);
        season.setActionKey(actionKey);
        return season;
    }

    public SeasonDetail createSeasonDetailWith(BigDecimal rate, int accomTypeId, Integer id, ActionKeyEnum actionKey) {
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setId(id);
        seasonDetail.setMonday(rate);
        seasonDetail.setTuesday(rate);
        seasonDetail.setWednesday(rate);
        seasonDetail.setThursday(rate);
        seasonDetail.setFriday(rate);
        seasonDetail.setSaturday(rate);
        seasonDetail.setSunday(rate);
        seasonDetail.setAccomTypeId(accomTypeId);
        seasonDetail.setActionKey(actionKey);
        seasonDetail.setRoomClassName("STD");
        seasonDetail.setRoomClassOrder(1);
        return seasonDetail;
    }

    @Test
    public void name() throws Exception {
        Mockito.when(multiPropertyCrudService.findByNamedQuery(Mockito.anyList(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(Arrays.asList(Arrays.asList(new RateUnqualified())));
        List<RateUnqualified> rateUnqualifiedForProperty = service.getRateUnqualifiedForProperty(5);
        assertNotNull(rateUnqualifiedForProperty);
        assertFalse(rateUnqualifiedForProperty.isEmpty());
    }

    @Test
    public void getRateUnqualifiedIsNotNull() {
        service.tenantCrudService = tenantCrudService();
        RateUnqualified rate = service.getRateUnqualified();
        assertNotNull(rate);
    }

    @Test
    public void getRateUnqualifiedByNameTest() {
        service.tenantCrudService = tenantCrudService();
        assertEquals(4, service.getRateUnqualifiedByName("LV0").getId());
    }

    @Test
    public void saveRateUnqualified() {
        String rateName = "Prod 1";
        String rateDescription = "Prod des";
        ;

        service.tenantCrudService = tenantCrudService();
        service.dateService = mock(DateService.class);

        service.saveRateUnqualified(rateName, rateDescription);

        verify(rateUnqualifiedComponent).saveRateUnqualified(rateUnqualifiedArgumentCaptor.capture());
        RateUnqualified value = rateUnqualifiedArgumentCaptor.getValue();
        assertEquals(rateName, value.getName());
        assertEquals(rateDescription, value.getDescription());
        verify(rateUnqualifiedComponent).saveRateUnqualified(Mockito.any(RateUnqualified.class));
    }

    @Test
    public void saveRateUnqualified_new_nullDescription() {
        String rateName = "Prod 1";
        String rateDescription = null;
        ;

        service.tenantCrudService = tenantCrudService();
        service.dateService = mock(DateService.class);

        service.saveRateUnqualified(rateName, rateDescription);

        verify(rateUnqualifiedComponent).saveRateUnqualified(rateUnqualifiedArgumentCaptor.capture());
        RateUnqualified value = rateUnqualifiedArgumentCaptor.getValue();
        assertEquals(rateName, value.getName());
        assertEquals(rateName, value.getDescription());
        verify(rateUnqualifiedComponent).saveRateUnqualified(Mockito.any(RateUnqualified.class));
    }

    @Test
    public void saveRateUnqualified_existing_nullDescription() {
        String rateName = "LV0";
        String rateDescription = null;
        ;

        service.tenantCrudService = tenantCrudService();
        service.dateService = mock(DateService.class);

        service.saveRateUnqualified(rateName, rateDescription);

        verify(rateUnqualifiedComponent).saveRateUnqualified(rateUnqualifiedArgumentCaptor.capture());
        RateUnqualified value = rateUnqualifiedArgumentCaptor.getValue();
        assertEquals(rateName, value.getName());
        assertEquals(rateName, value.getDescription());
        verify(rateUnqualifiedComponent).saveRateUnqualified(Mockito.any(RateUnqualified.class));
    }

    @Test
    public void isRateActiveAndPresent() {
        service.tenantCrudService = tenantCrudService();
        RateUnqualified rate = UniqueRateUnqualified.createRateUnqualifiedByDate(
                LocalDate.now().toDate(),
                LocalDate.now().plusMonths(12).toDate(),
                PacmanWorkContextHelper.getPropertyId()
        );
        assertTrue(service.isRateActiveAndPresent(rate.getName()));
        rate.setStatusId(2);
        tenantCrudService().save(rate);
        assertFalse(service.isRateActiveAndPresent(rate.getName()));
    }

    @Test
    public void getConsortiaRate() {
        service.tenantCrudService = tenantCrudService();
        RateUnqualified rate = UniqueRateUnqualified.createRateUnqualifiedByDate(
                LocalDate.now().toDate(),
                LocalDate.now().plusMonths(12).toDate(),
                PacmanWorkContextHelper.getPropertyId()
        );
        RateUnqualified consortiaRate = service.getConsortiaRate(rate.getName());
        assertNull(consortiaRate);
        rate.setStatusId(2);
        rate.setManagedInG3(true);
        tenantCrudService().save(rate);
        assertNotNull(service.getConsortiaRate(rate.getName()));
    }

    @Test
    public void createConsortiaRate() {
        service.tenantCrudService = tenantCrudService();
        service.fileMetadataService = new FileMetadataService();
        service.fileMetadataService.setCrudService(tenantCrudService());
        service.createConsortiaRate("TEST", 1);
        List<RateUnqualified> rates = tenantCrudService().findByNamedQuery(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAME, QueryParameter.with("name", Arrays.asList("TEST")).parameters());
        assertNotNull(rates);
        assertEquals(1, rates.size());
        RateUnqualified rate = rates.get(0);
        assertTrue(rate.getManagedInG3());
        assertEquals(2, rate.getStatusId());
        assertEquals("consortia", rate.getDescription());
        tenantCrudService().getEntityManager().detach(rate);
        service.createConsortiaRate("TEST", 1);
        List<RateQualified> ratesAfter = tenantCrudService().findByNamedQuery(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAME, QueryParameter.with("name", Arrays.asList("TEST")).parameters());
        assertNotNull(ratesAfter);
        assertEquals(1, ratesAfter.size());
        RateUnqualified rateAfter = rates.get(0);
        assertEquals(rate.getId(), rateAfter.getId());
    }


}
