package com.ideas.tetris.pacman.services.reports.dataextractionreport;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.testdatabuilder.InventoryGroupBuilder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DataExtractionReportBVLevelTest extends AbstractG3JupiterTest {

    private static final String ACCOM_CLASS_NAME_ID_6 = "STN";
    private static final String ACCOM_CLASS_NAME_ID_7 = "Deluxe";
    private static final String BUSINESS_VIEW1 = "BVTest1";
    private static final String BUSINESS_VIEW2 = "BVTest2";
    private static final int PROPERTY_ID = 6;
    private static final int RECORD_TYPE_ID = 3;
    private static final int PROCESS_STATUS_ID = 13;
    private static final int MKT_SEG_ID_7 = 7;
    private static final int MKT_SEG_ID_8 = 8;

    private LocalDate TODAY;
    private DateService dateService;
    private InventoryGroup testInventoryGroup;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        dateService = DateService.createTestInstance();
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        TODAY = new LocalDate(dateService.getCaughtUpDate(PacmanWorkContextHelper.getPropertyId()));

        testInventoryGroup = createTestInventoryGroup(ACCOM_CLASS_NAME_ID_6);
        createTestInventoryGroupDetails(testInventoryGroup, ACCOM_CLASS_NAME_ID_6);

        populateBusinessViewData();
    }

    @Test
    public void testDataExtractionReportAtBVLevel_InventoryGroupSelected() {
        //when
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        //setup
        createTestInventoryGroupDetails(testInventoryGroup, ACCOM_CLASS_NAME_ID_7);
        addUpdateData_ForInventoryGroupTest(startDate, endDate);

        //if
        List<Object> dataExtractionAtMSResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate.toString() + "', '" + endDate.toString() + "', 0, null, null, " + testInventoryGroup.getId() + ", '0,1'");

        //verify
        validateData_InventoryGroupSelected(dataExtractionAtMSResult, startDate, endDate);
    }

    @Test
    public void testDataExtractionReportAtBVLevel_InventoryGroupSelected__HavingDisContinuedRoomTypes() {
        //when
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        //setup
        updateAccomTypeDisplayStatus();
        createTestInventoryGroupDetails(testInventoryGroup, ACCOM_CLASS_NAME_ID_7);
        addUpdateData_ForInventoryGroupTest(startDate, endDate);

        //if
        List<Object> dataExtractionAtMSResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate.toString() + "', '" + endDate.toString() + "', 0, null, null, " + testInventoryGroup.getId() + ", '0,1'");

        //verify
        validateData_InventoryGroupSelected(dataExtractionAtMSResult, startDate, endDate);
    }

    @Test
    public void testDataExtractionReportAtBVLevel_DefaultInventoryGroupSelected() {
        //when
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        //setup
        addUpdateData_ForInventoryGroupTest(startDate, endDate);

        //if
        List<Object> dataExtractionAtMSResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate.toString() + "', '" + endDate.toString() + "', 0, null, null, -1, '0,1'");

        //verify
        validate_DefaultInventoryGroupSelected(dataExtractionAtMSResult);
    }

    @Test
    public void testDataExtractionReportAtBVLevel_DefaultInventoryGroupSelected_HavingDisContinuedRoomTypes() {
        //when
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        //setup
        updateAccomTypeDisplayStatus();
        addUpdateData_ForInventoryGroupTest(startDate, endDate);

        //if
        List<Object> dataExtractionAtMSResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate.toString() + "', '" + endDate.toString() + "', 0, null, null, -1, '0,1'");

        //verify
        validate_DefaultInventoryGroupSelected(dataExtractionAtMSResult);
    }

    @Test
    public void testDataExtractionReportAtBVLevel_WithExcludeCompRoomFilterTrue_AllMappedMktSegExcluded() {
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        excludeMktSegments();

        List<Object[]> dataExtractionResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate + "', '" + endDate + "', 0, null, null, -1, '0'");

        assertEquals(0, dataExtractionResult.size());

        unExcludeMktSegments();
    }

    @Test
    @Disabled
    public void testDataExtractionReportAtBVLevel_WithExcludeCompRoomFilterTrue_SomeMappedMktSegExcluded() {
        LocalDate startDate = TODAY.minusDays(1);
        LocalDate endDate = TODAY.plusDays(1);

        excludeMktSegments(MKT_SEG_ID_7);

        List<Object[]> dataExtractionResult = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv " + 6 + ", " + RECORD_TYPE_ID + ", " + PROCESS_STATUS_ID + ", '" + startDate + "', '" + endDate + "', 0, null, null, -1, '0'");

        assertEquals(3, dataExtractionResult.size());
        assertEquals("27", dataExtractionResult.get(0)[4].toString());
        assertEquals("1440.00000", dataExtractionResult.get(0)[5].toString());
        assertEquals("53.333333", dataExtractionResult.get(0)[6].toString());
        assertEquals("1520.00000", dataExtractionResult.get(0)[13].toString());
        assertEquals("53.333333", dataExtractionResult.get(0)[14].toString());
        assertEquals("27.00000", dataExtractionResult.get(0)[15].toString());

        unExcludeMktSegments(MKT_SEG_ID_7);
    }

    private void validate_DefaultInventoryGroupSelected(List<Object> dataExtractionAtMSResult) {
        assertEquals(3, dataExtractionAtMSResult.size());
        assertTrue("16".equals(((Object[]) dataExtractionAtMSResult.get(0))[4].toString()));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[5].toString().startsWith("900"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[13].toString().startsWith("950.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[6].toString().startsWith("56.25"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[15].toString().startsWith("16.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[14].toString().startsWith("56.25"));
    }

    private void validateData_InventoryGroupSelected(List<Object> dataExtractionAtMSResult, LocalDate startDate, LocalDate endDate) {
        assertEquals(3, dataExtractionAtMSResult.size());
        assertOccupancyDates(dataExtractionAtMSResult, startDate, endDate);
        assertRoomsSold_InventoryGroupSelected(dataExtractionAtMSResult);
        assertOnBooksRoomRevenue_InventoryGroupSelected(dataExtractionAtMSResult);
        assertTotalRevenue_InventoryGroupSelected(dataExtractionAtMSResult);
        assertOnBooksADR(dataExtractionAtMSResult);
        assertOccupancyFCST_InventoryGroupSelected(dataExtractionAtMSResult);
        assertADR(dataExtractionAtMSResult);
    }

    private void assertOccupancyFCST_InventoryGroupSelected(List<Object> dataExtractionAtMSResult) {
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[15].toString().startsWith("16.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(1))[15].toString().startsWith("20.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(2))[15].toString().startsWith("20.0"));
    }

    private void assertTotalRevenue_InventoryGroupSelected(List<Object> dataExtractionAtMSResult) {
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[13].toString().startsWith("950.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(1))[13].toString().startsWith("430.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(2))[13].toString().startsWith("430.0"));
    }

    private void assertRoomsSold_InventoryGroupSelected(List<Object> dataExtractionAtMSResult) {
        assertTrue("16".equals(((Object[]) dataExtractionAtMSResult.get(0))[4].toString()));
        assertTrue("18".equals(((Object[]) dataExtractionAtMSResult.get(1))[4].toString()));
        assertTrue("18".equals(((Object[]) dataExtractionAtMSResult.get(2))[4].toString()));
    }

    private void assertOnBooksRoomRevenue_InventoryGroupSelected(List<Object> dataExtractionAtMSResult) {
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[5].toString().startsWith("900"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(1))[5].toString().startsWith("900"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(2))[5].toString().startsWith("900"));
    }

    private void addUpdateData_ForInventoryGroupTest(LocalDate startDate, LocalDate endDate) {
        cleanMktAccomActivityForDateRange_considerOnlyOneMktSeg(startDate, endDate);
        cleanMktAccomActivityForDateRange_considerOnlyTwoAccomType(TODAY.minusDays(1), TODAY.plusDays(1));
        updateMktAccomActivityForDateRange(TODAY.minusDays(1), TODAY.plusDays(1));
        cleanOccupancyFCSTForDateRange_ConsiderOnlyOneMktSegAndTwoAccomType(TODAY.minusDays(1), TODAY.plusDays(1));
        updateOccupancyFcstForDateRange(TODAY.minusDays(1), TODAY.plusDays(1));
    }

    private void cleanMktAccomActivityForDateRange_considerOnlyTwoAccomType(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Mkt_Accom_Activity where Occupancy_Dt between '" + startDate + "' and '" + endDate + "' and Accom_Type_ID NOT IN(9,10)");
        tenantCrudService().flushAndClear();
    }

    private void cleanOccupancyFCSTForDateRange_ConsiderOnlyOneMktSegAndTwoAccomType(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_FCST where Occupancy_DT not between '" + startDate + "' and '" + endDate + "'");
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_FCST where Accom_Type_ID NOT IN(9,10)");
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_FCST where Mkt_Seg_ID <> 7");
        tenantCrudService().flushAndClear();
    }

    private void assertADR(List<Object> dataExtractionAtMSResult) {
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[14].toString().startsWith("56.25"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(1))[14].toString().startsWith("19.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(2))[14].toString().startsWith("19.0"));
    }

    private void assertOnBooksADR(List<Object> dataExtractionAtMSResult) {
        assertTrue(((Object[]) dataExtractionAtMSResult.get(0))[6].toString().startsWith("56.25"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(1))[6].toString().startsWith("50.0"));
        assertTrue(((Object[]) dataExtractionAtMSResult.get(2))[6].toString().startsWith("50.0"));
    }

    private void assertOccupancyDates(List<Object> dataExtractionAtMSResult, LocalDate startDate, LocalDate endDate) {
        assertTrue(startDate.toString().equals(((Object[]) dataExtractionAtMSResult.get(0))[2].toString()));
        assertTrue(TODAY.toString().equals(((Object[]) dataExtractionAtMSResult.get(1))[2].toString()));
        assertTrue(endDate.toString().equals(((Object[]) dataExtractionAtMSResult.get(2))[2].toString()));
    }

    private InventoryGroup createTestInventoryGroup(String accomClassId) {
        InventoryGroup inventoryGroup = new InventoryGroupBuilder()
                .withName("TestInventoryGroup")
                .withDescription("TestInventoryGroupDescription")
                .withBaseAccomClass(getAccomClass(accomClassId))
                .buildData();
        return tenantCrudService().save(inventoryGroup);
    }

    private AccomClass getAccomClass(String accomClassCode) {
        return (AccomClass) tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", accomClassCode)
                .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private void createTestInventoryGroupDetails(InventoryGroup inventoryGroup, String accomClassName) {
        InventoryGroupDetails inventoryGroupDetails = getInventoryGroupDetails(inventoryGroup, getAccomClass(accomClassName));
        tenantCrudService().save(inventoryGroupDetails);
    }

    private InventoryGroupDetails getInventoryGroupDetails(InventoryGroup inventoryGroup, AccomClass accomClass) {
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(accomClass);
        return inventoryGroupDetails;
    }

    private void populateBusinessViewData() {
        StringBuffer insertQuery = new StringBuffer(" INSERT INTO [Business_Group]([Business_Group_Name],[Business_Group_Description],[Ranking] ");
        insertQuery.append(" ,[Status_ID],[Created_by_User_ID],[Last_Updated_by_User_ID] ");
        insertQuery.append(" ,[Last_Updated_DTTM],[Created_DTTM],[Property_ID]) ");
        insertQuery.append(" VALUES ('" + BUSINESS_VIEW1 + "','For Unit Test',1,1,1,1,GETDATE(),GETDATE()," + PROPERTY_ID + "), ");
        insertQuery.append(" ('" + BUSINESS_VIEW2 + "','For Unit Test',2,1,1,1,GETDATE(),GETDATE()," + PROPERTY_ID + ") ");

        insertQuery.append(" INSERT INTO [Mkt_Seg_Business_Group]([Business_Group_ID],[Mkt_Seg_ID],[Ranking],[Created_By_User_ID], ");
        insertQuery.append(" [Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM]) ");
        insertQuery.append(" VALUES ((select Business_Group_ID from Business_Group where Business_Group_Name='" + BUSINESS_VIEW1 + "' ), ");
        insertQuery.append(" " + MKT_SEG_ID_7 + ",1,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + BUSINESS_VIEW2 + "' ), ");
        insertQuery.append(" " + MKT_SEG_ID_8 + ",1,1,GETDATE(),1,GETDATE())");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void excludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 1 WHERE Mkt_Seg_ID IN (" + MKT_SEG_ID_7 + "," + MKT_SEG_ID_8 + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    private void excludeMktSegments(int... mktSegIds) {
        final StringBuilder updateQuery = new StringBuilder("UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 1 WHERE Mkt_Seg_ID IN (");
        for (int i = 0, mktSegIdsLength = mktSegIds.length; i < mktSegIdsLength; i++) {
            updateQuery.append(mktSegIds[i]);
            if (i < mktSegIdsLength - 1) {
                updateQuery.append(", ");
            }
        }
        updateQuery.append(");");
        tenantCrudService().executeUpdateByNativeQuery(updateQuery.toString());
    }

    private void unExcludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 0 WHERE Mkt_Seg_ID  IN (" + MKT_SEG_ID_7 + "," + MKT_SEG_ID_8 + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    private void unExcludeMktSegments(int... mktSegIds) {
        final StringBuilder updateQuery = new StringBuilder("UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 0 WHERE Mkt_Seg_ID IN (");
        for (int i = 0, mktSegIdsLength = mktSegIds.length; i < mktSegIdsLength; i++) {
            updateQuery.append(mktSegIds[i]);
            if (i < mktSegIdsLength - 1) {
                updateQuery.append(", ");
            }
        }
        updateQuery.append(");");
        tenantCrudService().executeUpdateByNativeQuery(updateQuery.toString());
    }

    private void updateOccupancyFcstForDateRange(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_FCST set Occupancy_NBR = 10, Revenue = 190 where Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        tenantCrudService().flushAndClear();
    }

    private void updateAccomTypeDisplayStatus() {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Display_Status_ID = 2 where Accom_Type_ID = 9");
        tenantCrudService().flushAndClear();
    }

    private void updateMktAccomActivityForDateRange(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Accom_Activity set Rooms_Sold = 8, Arrivals = 6, Departures = 5, Cancellations = 0, No_Shows = 0, Room_Revenue = 450, Food_Revenue = 25, Total_Revenue = 475 where Occupancy_DT = '" + startDate + "'");
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Accom_Activity set Rooms_Sold = 9, Arrivals = 6, Departures = 5, Cancellations = 0, No_Shows = 0, Room_Revenue = 450, Food_Revenue = 25, Total_Revenue = 475 where Occupancy_DT = '" + startDate.plusDays(1) + "'");
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Accom_Activity set Rooms_Sold = 9, Arrivals = 7, Departures = 5, Cancellations = 0, No_Shows = 0, Room_Revenue = 450, Food_Revenue = 25, Total_Revenue = 475 where Occupancy_DT = '" + endDate + "'");
        tenantCrudService().flushAndClear();
    }

    private void cleanMktAccomActivityForDateRange_considerOnlyOneMktSeg(LocalDate startDate, LocalDate endDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Mkt_Accom_Activity where Occupancy_Dt between '" + startDate + "' and '" + endDate + "' and Mkt_Seg_ID <> 7");
        tenantCrudService().flushAndClear();
    }
}
