package com.ideas.tetris.pacman.services.grouppricing.evaluation.service;

import com.google.common.collect.Sets;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.configparams.SyncConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.bestavailablerate.rowmapper.RoomTypeOccupancyDateCapacityDetailsRowMapper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.entity.WashForecastForecastGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.EvaluationFilter;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.SortBy;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.SortOrder;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackage;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingRateConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByRevenueGroup;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.BookingEvaluationResultMapperService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.EvaluationResultMapperService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.GroupEvaluationArrivalDateMapperService;
import com.ideas.tetris.pacman.services.grouppricing.search.GroupEvaluationSearchCriteria;
import com.ideas.tetris.pacman.services.groupwash.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.JobExecution;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.job.entity.StepExecution;
import com.ideas.tetris.pacman.services.job.entity.StepExecutionContext;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomLevelView;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.problem.ProblemService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.servicingcostbylos.entity.ServicingCostByLOSConfiguration;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.services.useractivity.ActivityType;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivityPageCodeEnum;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Hibernate;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationSmallGroupPricingResult.fullEvaluationNeeded;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationSmallGroupPricingResult.success;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation.FIND_GE_BY_IDS;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationObjectMother.*;
import static com.ideas.tetris.platform.common.time.LocalDateUtils.toDate;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class GroupEvaluationServiceTest extends AbstractG3JupiterTest {
    public static final Integer USER_ID = 11403;
    private static final long JOB_TIMEOUT = 415000;
    public static final String NOT_NULL_BOOKING_ID = "bookingId";
    private final Date MOCK_START_DATE = DateUtil.getDate(13, 1, 2018);
    private static final int TEST_START_POSITION = 1;
    private static final int TEST_SIZE = 10;

    @Mock
    private GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    private FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Mock
    private DateService dateService;

    @Mock
    private JobServiceLocal jobService;

    @Mock
    private RegulatorService regulatorService;


    @Mock
    private CrudService tenantCrudService;

    @Mock
    private JobMonitorService jobMonitorService;

    @Mock
    private GroupEvaluationSmallGroupPricingService groupEvaluationSmallGroupPricingService;

    @Mock
    private StepExecutionContext context;

    @Mock
    private UserActivityService userActivityService;

    @Mock
    private ProblemService problemService;

    @Mock
    private OverbookingOverrideService overbookingService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private GroupPricingRateConfigurationService groupPricingRateConfigurationService;

    @Mock
    private PropertyService propertyService;

    @Mock
    ServicingCostByLOSService servicingCostByLOSService;

    @InjectMocks
    @Spy
    private GroupEvaluationService service;

    @Mock
    private SyncFlagService syncFlagService;

    @Mock
    private EvaluationResultMapperService evaluationResultMapperService;

    @Mock
    private BookingEvaluationResultMapperService bookingEvaluationResultMapperService;

    @Mock
    private UserService userService;

    @Mock
    private ConferenceAndBanquetService conferenceAndBanquetService;

    @Spy
    private GroupEvaluationArrivalDateMapperService groupEvaluationArrivalDateMapperService;


    private GroupEvaluation groupEvaluation;
    private boolean isEvaluationRequestFromFDC;
    private GroupEvaluation groupEvaluationToSave;
    private GroupEvaluation hydratedEvaluation;

    @BeforeEach
    public void setUp() {
        service.setTenantCrudService(tenantCrudService());
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        // Need to save the C&B configuration first
        groupEvaluation = buildGroupEvaluation();
        when(groupEvaluationSmallGroupPricingService.evaluate(groupEvaluation)).thenReturn(fullEvaluationNeeded());
        tenantCrudService().save(groupEvaluation.getGroupEvaluationConferenceAndBanquets().iterator().next()
                .getGroupPricingConfigurationConferenceAndBanquet());

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERIC_SALES_AND_CATERING_ENABLED)).thenReturn(false);
        when(regulatorService.isSpringTXEnableRegulatorService()).thenReturn(false);

    }

    @Test
    void shouldReturnMarketSegmentsInDescOrderByVolume() {
        List<MarketSegmentSummary> result = service.getGroupMarketSegmentsInDescOrderByVolume(5);

        assertEquals(2, result.size());
        assertEquals("Disc", result.get(0).getCode()); // 17632 rooms_sold
        assertEquals("CONV", result.get(1).getCode());   // 160 rooms_sold
    }

    public void getCommonPropertyMarketSegmentsForGroupForecastType() throws Exception {
        List<Integer> propertyIds = new ArrayList<>();
        propertyIds.add(PacmanWorkContextHelper.getPropertyId());

        List<MarketSegmentSummary> marketSegmentSummaries = service
                .getCommonPropertyMarketSegmentsForGroupForecastType(propertyIds);

        assertNotNull(marketSegmentSummaries);
        assertTrue(marketSegmentSummaries.size() > 0);
        assertNotNull(marketSegmentSummaries.get(0).getId());
        assertNotNull(marketSegmentSummaries.get(0).getName());
    }

    @Test
    public void getMarketSegmentsAssignedToForecastGroups() {
        List<MarketSegmentSummary> summaries = service.getAllMarketSegmentsAssignedToForecastGroups();
        assertEquals(11, summaries.size(), "has all by forecast group");
    }

    @Test
    public void testGetGroupMarketSegments() {
        List<MarketSegmentSummary> marketSegmentSummaries = service.getMarketSegmentsForGroupForecastType();

        assertNotNull(marketSegmentSummaries);
        assertTrue(marketSegmentSummaries.size() > 0);
        assertNotNull(marketSegmentSummaries.get(0).getId());
        assertNotNull(marketSegmentSummaries.get(0).getName());
    }

    @Test
    public void getCapacitiesForDateRange() {
        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate endDate = startDate.plusDays(27);
        List<OccupancyDateCapacity> occupancyDateCapacities = service.getCapacitiesFromActivityForDateRange(startDate, endDate);
        assertNotNull(occupancyDateCapacities);
        assertEquals(28, occupancyDateCapacities.size());
    }

    @Test
    public void getCapacitiesForDateRange_withAnExcludedRoomClass() {
        // Just get 1 day capacity with no room classes excluded
        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        List<OccupancyDateCapacity> occupancyDateCapacities = service.getCapacitiesFromActivityForDateRange(startDate, startDate);
        Integer preExcludedCapacity = occupancyDateCapacities.get(0).getPhysicalCapacity();

        // Mark STE Accom Class as excluded for Group Pricing
        AccomClass excludedAccomClass = tenantCrudService().find(AccomClass.class, 4);
        excludedAccomClass.setExcludedForGroupEvaluation(true);
        tenantCrudService().save(excludedAccomClass);

        // Get same day capacity with accom class excluded
        List<OccupancyDateCapacity> occupancyDateCapacitiesWithExcludedRCs = service.getCapacitiesFromActivityForDateRange(startDate, startDate);

        Integer excludedCapacity = occupancyDateCapacitiesWithExcludedRCs.get(0).getPhysicalCapacity();

        // Asset that these two values are not equal
        assertNotEquals(preExcludedCapacity, excludedCapacity);
    }

    @Test
    public void getCapacitiesForDateRange_WithWashForecastData() {
        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate endDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        List<OccupancyDateCapacity> occupancyDateCapacities = service.getCapacitiesFromActivityForDateRange(startDate, endDate);
        assertTrue(occupancyDateCapacities.size() == 1);

        // Delete wash forecast groups for one day to simulate no forecast data
        List<WashForecastForecastGroup> groups = tenantCrudService().findByNamedQuery(WashForecastForecastGroup.BY_OCCUPANCY_DATE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("occupancyDate", startDate.toDate()).parameters());
        tenantCrudService().delete(groups);

        List<OccupancyDateCapacity> occupancyDateCapacitiesNew = service.getCapacitiesFromActivityForDateRange(startDate, endDate);
        assertTrue(occupancyDateCapacitiesNew.size() == 1);

        assertEquals(occupancyDateCapacities.get(0).getPhysicalCapacity(), occupancyDateCapacitiesNew.get(0).getPhysicalCapacity());
        assertEquals(occupancyDateCapacities.get(0).getAvailableCapacity(), occupancyDateCapacitiesNew.get(0).getAvailableCapacity());

    }

    @Test
    public void getCapacitiesForDateRange_NoWashForecastData() {
        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate endDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        // Delete wash forecast groups for one day to simulate no forecast data
        List<WashForecastForecastGroup> groups = tenantCrudService().findByNamedQuery(WashForecastForecastGroup.BY_OCCUPANCY_DATE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("occupancyDate", startDate.toDate()).parameters());
        tenantCrudService().delete(groups);

        List<OccupancyDateCapacity> occupancyDateCapacities = service.getCapacitiesFromActivityForDateRange(startDate, endDate);
        assertTrue(occupancyDateCapacities.size() == 1);
        // These values should never be equal, as the available qty should account for rooms sold even without wash
        // forecast and H1 test data does not contain any days where rooms sold is zero
        assertNotEquals(occupancyDateCapacities.get(0).getPhysicalCapacity(), occupancyDateCapacities.get(0).getAvailableCapacity());

    }

    @Test
    public void getCapacitiesForPropertyDataTest() {
        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        startDate = startDate.plusDays(6);
        List<OccupancyDateCapacity> occupancyDateCapacities = service.getCapacitiesFromActivityForDateRange(startDate, startDate);
        assertTrue(occupancyDateCapacities.size() == 1);
        assertEquals(7, occupancyDateCapacities.get(0).getAvailableCapacity().intValue());
    }

    @Test
    public void getForecastWindowEndDate() {
        Date date = DateUtil.getFirstDayOfCurrentMonth();
        when(dateService.getForecastWindowEndDateBDE()).thenReturn(date);

        assertEquals(new LocalDate(date).minusDays(SystemConfig.getGroupPricingWindowSize()),
                service.getEvaluationWindowEndDate());
    }

    @Test
    public void getFunctionSpaceForecastWindowEndDate() {
        Date firstDayOfCurrentMonth = DateUtil.getFirstDayOfCurrentMonth();
        when(dateService.getCaughtUpDate()).thenReturn(firstDayOfCurrentMonth);
        assertEquals(new LocalDate(firstDayOfCurrentMonth).plusDays(1100).minusDays(
                SystemConfig.getGroupPricingWindowSize()), service.getFunctionSpaceEvaluationWindowEndDate());
    }

    @Test
    void shouldGetGroupEvaluationById() {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setId(1);
        when(tenantCrudService.find(GroupEvaluation.class, 1)).thenReturn(groupEvaluation);

        GroupEvaluation savedGroupEvaluation = service.getGroupEvaluationById(1);

        assertNotNull(savedGroupEvaluation);
    }

    @Test
    public void getPerRoomServicingCostForGroupPricingCost() {
        GroupEvaluationService spy = Mockito.spy(service);
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(new BigDecimal(10.0));
        when(groupPricingConfigurationService.getROHGroupPricingConfigurationForPropertyUsingTenant(PacmanWorkContextHelper.getPropertyId())).thenReturn(groupPricingConfiguration);
        assertEquals(10.0, spy.getPerRoomServicingCostUsingTenant(groupEvaluation).doubleValue(), 0);
        verify(groupPricingConfigurationService).getROHGroupPricingConfigurationForPropertyUsingTenant(PacmanWorkContextHelper.getPropertyId());
    }

    @Test
    public void getPerRoomServicingCostForServicingCost() {
        GroupEvaluationService spy = Mockito.spy(service);
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.TRUE);
        ServicingCostByLOSConfiguration servicingCostByLOSConfiguration = new ServicingCostByLOSConfiguration();
        servicingCostByLOSConfiguration.setFullTurnServicingCost(new BigDecimal(20.0));
        when(servicingCostByLOSService.getROHServicingCostConfiguration(PacmanWorkContextHelper.getPropertyId())).thenReturn(servicingCostByLOSConfiguration);
        assertEquals(20.0, spy.getPerRoomServicingCost(groupEvaluation).doubleValue(), 0);
        verify(servicingCostByLOSService).getROHServicingCostConfiguration(PacmanWorkContextHelper.getPropertyId());
    }

    @Test
    public void saveGroupEvaluation() {
        GroupEvaluationService spy = Mockito.spy(service);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        spy.save(groupEvaluation, false, "1234");
        tenantCrudService().flushAndClear();

        GroupEvaluation retVal = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());

        assertNotNull(retVal);
        assertNotNull(retVal.getId());
        assertEquals(BigDecimal.ONE.setScale(2), retVal.getPerRoomServicingCost());
        assertEquals(groupEvaluation.getPropertyId(), retVal.getPropertyId());
        assertEquals(groupEvaluation.getEvaluationMethod(), retVal.getEvaluationMethod());
        assertEquals(groupEvaluation.getMaterializationStatus(), retVal.getMaterializationStatus());
        assertEquals(groupEvaluation.getFollowUpDate(), retVal.getFollowUpDate());
        assertEquals(groupEvaluation.getGroupName(), retVal.getGroupName());
        assertEquals(groupEvaluation.getMarketSegment().getId(), retVal.getMarketSegment().getId());
        assertEquals(groupEvaluation.getNotes(), retVal.getNotes());
        assertEquals(groupEvaluation.getEvaluationType(), retVal.getEvaluationType());

        Set<GroupEvaluationArrivalDate> groupEvaluationDates = retVal.getGroupEvaluationArrivalDates();
        GroupEvaluationArrivalDate retGroupEvaluationDate = groupEvaluationDates.iterator().next();
        assertTrue(retGroupEvaluationDate.isPreferredDate());
        assertEquals(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next().getArrivalDate(),
                retGroupEvaluationDate.getArrivalDate());

        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = retVal.getGroupEvaluationDayOfStays();
        GroupEvaluationDayOfStay retGroupEvaluationDayOfStay = groupEvaluationDayOfStays.iterator().next();
        assertEquals(groupEvaluation.getGroupEvaluationDayOfStays().iterator().next().getNumberOfRooms(),
                retGroupEvaluationDayOfStay.getNumberOfRooms());

        verifyZeroInteractions(jobService);
    }

    @Test
    public void saveGroupEvaluation_genericSalesAndCateringEnabled_shouldTriggerNGIBookingEvaluationResultJob() {
        //Given
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERIC_SALES_AND_CATERING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);

        GroupEvaluationService spyGroupEvaluationService = Mockito.spy(service);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spyGroupEvaluationService).getPerRoomServicingCost(groupEvaluation);

        groupEvaluation.setSalesCateringBookingId("4321");
        //When
        spyGroupEvaluationService.save(groupEvaluation, true, "1234");
        //Then
        verify(jobService).startGuaranteedNewInstance(eq(JobName.NGIBookingEvaluationResultJob), anyMap());

    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_shouldTriggerNGIBookingEvaluationResultJob() {
        //Given
        groupEvaluation.setSalesCateringBookingId("123");
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        //When
        service.sendGenericSCEvaluationResultToNGI(groupEvaluation, true, false, "321");
        //Then
        verify(jobService).startGuaranteedNewInstance(eq(JobName.NGIBookingEvaluationResultJob), anyMap());
    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_isEvaluationRequestFromFDC_false() {
        //Given
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        groupEvaluation.setSalesCateringBookingId("123");
        //When
        service.sendGenericSCEvaluationResultToNGI(groupEvaluation, false, false, "321");
        //Then
        verifyNoInteractions(jobService);
    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_salesCateringBookingIdIsMissing() {
        //Given
        groupEvaluation.setSalesCateringBookingId(null);
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        //When
        service.sendGenericSCEvaluationResultToNGI(groupEvaluation, true, false, "321");
        //Then
        verifyNoInteractions(jobService);
    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_evaluationIdIsMissing() {
        //Given
        groupEvaluation.setSalesCateringBookingId("123");
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        //When
        service.sendGenericSCEvaluationResultToNGI(groupEvaluation, true, false, "");
        //Then
        verifyNoInteractions(jobService);
    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_isSendEvalResToNGIEnabled_false() {
        //Given
        groupEvaluation.setSalesCateringBookingId("123");
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(false);
        //When
        service.sendGenericSCEvaluationResultToNGI(groupEvaluation, true, false, "321");
        //Then
        verifyNoInteractions(jobService);
    }

    @Test
    public void sendGenericSCEvaluationResultToNGI_shouldTriggerNGIBookingEvaluationResultJobWith() {
        //Given
        groupEvaluation.setSalesCateringBookingId("123");
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        when(jobService.startGuaranteedNewInstance(eq(JobName.NGIBookingEvaluationResultJob), anyMap())).thenReturn(1L);
        when(jobMonitorService.getJobInstanceId(1L)).thenReturn(2L);
        //When
        Long result = service.sendGenericSCEvaluationResultToNGI(groupEvaluation, true, false, "321");
        //Then
        assertEquals(2L, result);
        verify(jobService).startGuaranteedNewInstance(eq(JobName.NGIBookingEvaluationResultJob), anyMap());
    }

    @Test
    public void startNGIAhwsGroupEvalResultsJob() {
        int propertyId = 1;
        String salesCateringBookingId = "1234";
        String evaluationId = "3222";
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, propertyId);
        jobParams.put(JobParameterKey.SALES_CATERING_BOOKING_ID, salesCateringBookingId);
        jobParams.put(JobParameterKey.AHWS_EVALUATION_ID, evaluationId);
        jobParams.put(JobParameterKey.CURRENCY_EXCHANGE_RATE, BigDecimal.ONE);

        Mockito.when(jobService.startGuaranteedNewInstance(JobName.NGIGroupEvalResults, jobParams)).thenReturn(new Long(1234));

        assertEquals(new Long(1234), service.startNGIAhwsGroupEvalResultsJob(salesCateringBookingId, propertyId, evaluationId, BigDecimal.ONE));
        verify(jobService).startGuaranteedNewInstance(JobName.NGIGroupEvalResults, jobParams);
    }

    @Test
    public void startNGIBookingEvaluationResultJob() {
        int propertyId = 1;
        String salesCateringBookingId = "12345";
        String evaluationId = "54321";
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, propertyId);
        jobParams.put(JobParameterKey.SALES_CATERING_BOOKING_ID, salesCateringBookingId);
        jobParams.put(JobParameterKey.BOOKING_EVALUATION_ID, evaluationId);

        Mockito.when(jobService.startGuaranteedNewInstance(JobName.NGIBookingEvaluationResultJob, jobParams)).thenReturn(new Long(100));

        assertEquals(new Long(100), service.startNGIBookingEvaluationResultJob(salesCateringBookingId, propertyId, evaluationId));
        verify(jobService).startGuaranteedNewInstance(JobName.NGIBookingEvaluationResultJob, jobParams);
    }

    @Test
    public void getGroupEvalResultsROHLevel() {
        String testBookingId = "1234";
        CrudService mockedCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockedCrudService);
        GroupEvaluation dummyGroupEvaluation = getDummyGroupEvaluation(GroupPricingEvaluationMethod.ROH);
        Property dummyNGIProperty = getNGIProperty();
        when(mockedCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID,
                QueryParameter.with("salesCateringBookingId", testBookingId).parameters())).thenReturn(dummyGroupEvaluation);
        when(propertyService.getPropertyById(dummyGroupEvaluation.getPropertyId())).thenReturn(dummyNGIProperty);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        GroupPricingConfiguration groupPricingConfigurationROH = new GroupPricingConfiguration();
        groupPricingConfigurationROH.setPerRoomServicingCost(BigDecimal.ONE);

        when(groupPricingConfigurationService.getROHGroupPricingConfigurationForPropertyUsingTenant(dummyGroupEvaluation.getPropertyId())).thenReturn(groupPricingConfigurationROH);
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        service.getGroupEvalResults(testBookingId, BigDecimal.ONE);

        verify(servicingCostByLOSService, times(1)).isProfitOptimizationEnabled();
        verify(groupPricingConfigurationService, times(1)).getROHGroupPricingConfigurationForPropertyUsingTenant(dummyGroupEvaluation.getPropertyId());
        verify(evaluationResultMapperService, times(1)).buildEvaluationResult(groupEvaluationCaptor.capture(), eq(dummyNGIProperty), eq(BigDecimal.ONE));
        assertEquals(BigDecimal.ONE, groupEvaluationCaptor.getValue().getPerRoomServicingCost());
    }

    @Test
    public void getBookingEvaluationResultTrackerROHLevel() {
        String testBookingId = "1234";
        String testEvaluationRequestId = "12111";
        CrudService mockedCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockedCrudService);
        GroupEvaluation dummyGroupEvaluation = getDummyGroupEvaluation(GroupPricingEvaluationMethod.ROH);
        Property dummyNGIProperty = getNGIProperty();
        when(mockedCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID,
                QueryParameter.with("salesCateringBookingId", testBookingId).parameters())).thenReturn(dummyGroupEvaluation);
        when(propertyService.getPropertyById(dummyGroupEvaluation.getPropertyId())).thenReturn(dummyNGIProperty);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        GroupPricingConfiguration groupPricingConfigurationROH = new GroupPricingConfiguration();
        groupPricingConfigurationROH.setPerRoomServicingCost(BigDecimal.ONE);

        when(groupPricingConfigurationService.getROHGroupPricingConfigurationForPropertyUsingTenant(dummyGroupEvaluation.getPropertyId())).thenReturn(groupPricingConfigurationROH);
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        service.getBookingEvaluationResultTracker(testBookingId, testEvaluationRequestId);

        verify(servicingCostByLOSService, times(1)).isProfitOptimizationEnabled();
        verify(groupPricingConfigurationService, times(1)).getROHGroupPricingConfigurationForPropertyUsingTenant(dummyGroupEvaluation.getPropertyId());
        verify(bookingEvaluationResultMapperService, times(1)).buildBookingEvaluationResultTracker(groupEvaluationCaptor.capture(), eq(dummyNGIProperty));
        assertEquals(BigDecimal.ONE, groupEvaluationCaptor.getValue().getPerRoomServicingCost());
    }

    @Test
    public void shouldHydrateGroupEvaluationForROH() {
        groupEvaluationSavedWithRequiredFields()
                .whenFetchGroupevaluationMethodCalled()
                .thenAssertHydratedGroupEvaluationObject();
    }

    @Test
    public void getGroupEvalResultsRC() {
        String testBookingId = "1234";
        CrudService mockedCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockedCrudService);
        GroupEvaluation dummyGroupEvaluationRC = getDummyGroupEvaluation(GroupPricingEvaluationMethod.RC);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        when(groupPricingConfigurationService.getGroupPricingConfigurationsPerRoomClass()).thenReturn(prepareGroupPricingConfigurationRCLevel());

        Property dummyNGIProperty = getNGIProperty();
        when(mockedCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID,
                QueryParameter.with("salesCateringBookingId", testBookingId).parameters())).thenReturn(dummyGroupEvaluationRC);
        when(propertyService.getPropertyById(dummyGroupEvaluationRC.getPropertyId())).thenReturn(dummyNGIProperty);
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        service.getGroupEvalResults(testBookingId, BigDecimal.ONE);

        verify(servicingCostByLOSService, times(1)).isProfitOptimizationEnabled();
        verify(groupPricingConfigurationService, times(1)).getGroupPricingConfigurationsPerRoomClass();
        verify(evaluationResultMapperService, times(1)).buildEvaluationResult(groupEvaluationCaptor.capture(), eq(dummyNGIProperty), eq(BigDecimal.ONE));
        final Map<AccomClass, BigDecimal> map = groupEvaluationCaptor.getValue().getPerRoomServingCostByRoomType();
        assertEquals(2, map.size());
        final Set<Map.Entry<AccomClass, BigDecimal>> entries = map.entrySet();
        final Iterator<Map.Entry<AccomClass, BigDecimal>> iterator = entries.iterator();
        Map.Entry<AccomClass, BigDecimal> entry = iterator.next();
        assertEquals("A", entry.getKey().getName());
        assertEquals(BigDecimal.ONE, entry.getValue());

        entry = iterator.next();
        assertEquals("B", entry.getKey().getName());
        assertEquals(BigDecimal.ONE, entry.getValue());
    }

    @Test
    public void getBookingEvaluationResultTrackerRC() {
        String testBookingId = "1234";
        String testEvaluationRequestId = "12111";
        CrudService mockedCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockedCrudService);
        GroupEvaluation dummyGroupEvaluationRC = getDummyGroupEvaluation(GroupPricingEvaluationMethod.RC);
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.FALSE);
        when(groupPricingConfigurationService.getGroupPricingConfigurationsPerRoomClass()).thenReturn(prepareGroupPricingConfigurationRCLevel());

        Property dummyNGIProperty = getNGIProperty();
        when(mockedCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID,
                QueryParameter.with("salesCateringBookingId", testBookingId).parameters())).thenReturn(dummyGroupEvaluationRC);
        when(propertyService.getPropertyById(dummyGroupEvaluationRC.getPropertyId())).thenReturn(dummyNGIProperty);
        ArgumentCaptor<GroupEvaluation> groupEvaluationCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);

        service.getBookingEvaluationResultTracker(testBookingId, testEvaluationRequestId);

        verify(servicingCostByLOSService, times(1)).isProfitOptimizationEnabled();
        verify(groupPricingConfigurationService, times(1)).getGroupPricingConfigurationsPerRoomClass();
        verify(bookingEvaluationResultMapperService, times(1)).buildBookingEvaluationResultTracker(groupEvaluationCaptor.capture(), eq(dummyNGIProperty));
        final Map<AccomClass, BigDecimal> map = groupEvaluationCaptor.getValue().getPerRoomServingCostByRoomType();
        assertEquals(2, map.size());
        final Set<Map.Entry<AccomClass, BigDecimal>> entries = map.entrySet();
        final Iterator<Map.Entry<AccomClass, BigDecimal>> iterator = entries.iterator();
        Map.Entry<AccomClass, BigDecimal> entry = iterator.next();
        assertEquals("A", entry.getKey().getName());
        assertEquals(BigDecimal.ONE, entry.getValue());

        entry = iterator.next();
        assertEquals("B", entry.getKey().getName());
        assertEquals(BigDecimal.ONE, entry.getValue());
    }

    private List<GroupPricingConfiguration> prepareGroupPricingConfigurationRCLevel() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("A");
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("B");
        GroupPricingConfiguration groupPricingConfiguration1 = new GroupPricingConfiguration();
        groupPricingConfiguration1.setAccomClass(accomClass1);
        groupPricingConfiguration1.setPerRoomServicingCost(BigDecimal.ONE);
        GroupPricingConfiguration groupPricingConfiguration2 = new GroupPricingConfiguration();
        groupPricingConfiguration2.setAccomClass(accomClass2);
        groupPricingConfiguration2.setPerRoomServicingCost(BigDecimal.ONE);
        return Arrays.asList(groupPricingConfiguration1, groupPricingConfiguration2);
    }


    private Property getNGIProperty() {
        Client client = new Client();
        client.setCode("NGI");
        Property property = new Property();
        property.setCode("ahws");
        property.setClient(client);
        return property;
    }

    @Test
    public void save_FromGroupEvaluation() {
        GroupEvaluationService spy = Mockito.spy(service);
        String newGroupName = "CloneMultiEval";
        // Set multi group id
        groupEvaluation.setGroupEvaluationMultiId(1);
        // Set different group name
        groupEvaluation.setGroupName(newGroupName);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        spy.save(groupEvaluation, false, "1233");
        tenantCrudService().flushAndClear();

        // Use search criteria based on group name above to fetch the new group evaluation created
        GroupEvaluationSearchCriteria searchCriteria = new GroupEvaluationSearchCriteria();
        searchCriteria.setGroupName(newGroupName);

        List<GroupEvaluation> retVals = tenantCrudService().findByCriteria(searchCriteria);

        assertTrue(retVals.size() == 1);

        GroupEvaluation clonedGroupEval = retVals.get(0);
        assertTrue(groupEvaluation.getId() != clonedGroupEval.getId());
        assertEquals(newGroupName, clonedGroupEval.getGroupName());
    }

    @Test
    public void save_updateEvaluationDateForAuditPurposes() throws Exception {
        GroupEvaluationService spy = Mockito.spy(service);

        LocalDateTime previousEvalDateTime = groupEvaluation.getEvaluationDate();
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        ArgumentCaptor<GroupEvaluation> groupEvlCaptor = ArgumentCaptor.forClass(GroupEvaluation.class);
        when(tenantCrudService.save(groupEvaluation)).thenReturn(groupEvaluation);
        spy.setTenantCrudService(tenantCrudService);

        spy.save(groupEvaluation, false, "1233");

        verify(tenantCrudService).save(groupEvlCaptor.capture());
        assertNotEquals(previousEvalDateTime, groupEvlCaptor.getValue().getEvaluationDate());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void evaluate() {
        GroupEvaluationService spy = Mockito.spy(service);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, groupEvaluation);

        Long jobInstanceId = 1L;
        Long jobExecutionId = 2L;

        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        when(jobService.startGuaranteedNewInstance(JobName.GroupEvaluation, parameters))
                .thenReturn(jobExecutionId);

        JobView jobView = new JobView();
        jobView.setJobInstanceId(jobInstanceId);
        JobExecution jobExecution = new JobExecution();
        jobExecution.setJobView(jobView);
        jobView.setJobExecutions(singletonList(jobExecution));

        StepExecution stepExecution = new StepExecution();
        stepExecution.setStepName(GroupEvaluationService.GROUP_EVALUATION_SAS_INVOCATION_STEP_NAME);
        stepExecution.setStepExecutionContext(context);
        jobExecution.setStepExecutions(singletonList(stepExecution));

        Map<String, Object> deserializedContext = new HashMap<>();
        deserializedContext.put(JobView.RESPONSE_KEY, groupEvaluation);

        when(context.getDeserializedContext()).thenReturn(deserializedContext);

        when(jobMonitorService.getJobInstanceId(jobExecutionId)).thenReturn(jobInstanceId);
        when(jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT)).thenReturn(jobView);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        // Mock out validation for capacities
        List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
        doReturn(occupancyDateCapacities).when(spy).getCapacitiesFromActivityForDateRange(Mockito.any(LocalDate.class),
                Mockito.any(LocalDate.class));
        // Set contract rate, then verify it is cleared after evaluation
        groupEvaluation.setContractedRate(new BigDecimal(150));

        GroupEvaluation retVal = spy.evaluate(groupEvaluation);

        assertEquals(groupEvaluation, retVal);
        assertNull(groupEvaluation.getContractedRate());

        verify(jobService).startGuaranteedNewInstance(JobName.GroupEvaluation, parameters);
        verify(jobMonitorService).getJobInstanceId(jobExecutionId);
        verify(jobMonitorService).getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT);
        verify(userActivityService).startActivity(
                UserActivityPageCodeEnum.GROUP_PRICING_EVALUATION.getPageCode(), ActivityType.RUN_EVALUATION,
                groupEvaluation.getEvaluationType().name(), null);
    }

    @Test
    void shouldReturnEvaluatedAsUNKNOWNWhenshouldUseFSRevenueStreamsIsDisabled() {
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        assertEquals(EvaluationCategory.UNKNOWN, service.getEvaluationCategory());
    }

    @Test
    void shouldReturnEvaluatedAsGPWhenGroupPricingIsEnabled() {
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        assertEquals(EvaluationCategory.GP, service.getEvaluationCategory());
    }

    @Test
    void shouldReturnEvaluatedAsGPWhenGroupPricingIsDisabled() {
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        assertEquals(EvaluationCategory.FS, service.getEvaluationCategory());
    }

    @Test
    void shouldSetEvaluatedAsValueBeforeGeneratingEvaluation() {
        GroupEvaluationService spy = Mockito.spy(service);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, groupEvaluation);
        Long jobInstanceId = 1L;
        Long jobExecutionId = 2L;
        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        when(jobService.startGuaranteedNewInstance(JobName.GroupEvaluation, parameters))
                .thenReturn(jobExecutionId);
        JobView jobView = new JobView();
        jobView.setJobInstanceId(jobInstanceId);
        JobExecution jobExecution = new JobExecution();
        jobExecution.setJobView(jobView);
        jobView.setJobExecutions(singletonList(jobExecution));
        StepExecution stepExecution = new StepExecution();
        stepExecution.setStepName(GroupEvaluationService.GROUP_EVALUATION_SAS_INVOCATION_STEP_NAME);
        stepExecution.setStepExecutionContext(context);
        jobExecution.setStepExecutions(singletonList(stepExecution));
        Map<String, Object> deserializedContext = new HashMap<>();
        deserializedContext.put(JobView.RESPONSE_KEY, groupEvaluation);
        when(context.getDeserializedContext()).thenReturn(deserializedContext);
        when(jobMonitorService.getJobInstanceId(jobExecutionId)).thenReturn(jobInstanceId);
        when(jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT)).thenReturn(jobView);
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);
        List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
        doReturn(occupancyDateCapacities).when(spy).getCapacitiesFromActivityForDateRange(Mockito.any(),
                Mockito.any());
        groupEvaluation.setContractedRate(new BigDecimal(150));
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);

        GroupEvaluation retVal = spy.generateEvaluation(groupEvaluation);

        assertEquals(groupEvaluation, retVal);
        assertNull(groupEvaluation.getContractedRate());
        assertEquals(EvaluationCategory.FS, groupEvaluation.getEvaluationCategory());
        verify(jobService).startGuaranteedNewInstance(JobName.GroupEvaluation, parameters);
        verify(jobMonitorService).getJobInstanceId(jobExecutionId);
        verify(jobMonitorService).getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT);
        verify(userActivityService).startActivity(
                UserActivityPageCodeEnum.GROUP_PRICING_EVALUATION.getPageCode(), ActivityType.RUN_EVALUATION,
                groupEvaluation.getEvaluationType().name(), null);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void evaluateWhenSmallGroupPricingEvaluationIsSuccessful() {
        GroupEvaluationService spy = Mockito.spy(service);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, groupEvaluation);

        Long jobInstanceId = 1L;
        Long jobExecutionId = 2L;

        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        when(groupEvaluationSmallGroupPricingService.evaluate(groupEvaluation)).thenReturn(success(groupEvaluation));

        JobView jobView = new JobView();
        jobView.setJobInstanceId(jobInstanceId);
        JobExecution jobExecution = new JobExecution();
        jobExecution.setJobView(jobView);
        jobView.setJobExecutions(singletonList(jobExecution));

        StepExecution stepExecution = new StepExecution();
        stepExecution.setStepName(GroupEvaluationService.GROUP_EVALUATION_SAS_INVOCATION_STEP_NAME);
        stepExecution.setStepExecutionContext(context);
        jobExecution.setStepExecutions(singletonList(stepExecution));

        Map<String, Object> deserializedContext = new HashMap<>();
        deserializedContext.put(JobView.RESPONSE_KEY, groupEvaluation);

        when(context.getDeserializedContext()).thenReturn(deserializedContext);

        when(jobMonitorService.getJobInstanceId(jobExecutionId)).thenReturn(jobInstanceId);
        when(jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT)).thenReturn(jobView);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);
        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        // Mock out validation for capacities
        List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
        doReturn(occupancyDateCapacities).when(spy).getCapacitiesFromActivityForDateRange(Mockito.any(LocalDate.class),
                Mockito.any(LocalDate.class));
        // Set contract rate, then verify it is cleared after evaluation
        groupEvaluation.setContractedRate(new BigDecimal(150));

        GroupEvaluation retVal = spy.evaluate(groupEvaluation);

        assertEquals(groupEvaluation, retVal);
        assertNull(groupEvaluation.getContractedRate());

        verify(jobService, never()).startGuaranteedNewInstance(JobName.GroupEvaluation, parameters);

    }

    @SuppressWarnings("unchecked")
    @Test
    public void evaluateWhereEvaluationFailed() {
        GroupEvaluationService spy = Mockito.spy(service);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, groupEvaluation);

        Long jobInstanceId = 1L;
        Long jobExecutionId = 2L;

        JobView jobView = new JobView();
        jobView.setJobInstanceId(jobInstanceId);
        JobExecution jobExecution = new JobExecution();
        jobExecution.setJobView(jobView);
        jobView.setJobExecutions(singletonList(jobExecution));

        StepExecution stepExecution = new StepExecution();
        stepExecution.setStepExecutionContext(context);
        jobExecution.setStepExecutions(singletonList(stepExecution));

        TetrisException tetrisException = new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, "BOOM!");

        Map<String, Object> deserializedContext = new HashMap<>();
        deserializedContext.put(JobView.RESPONSE_KEY, tetrisException);

        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        when(jobService.startGuaranteedNewInstance(JobName.GroupEvaluation, parameters)).thenReturn(jobExecutionId);
        when(context.getDeserializedContext()).thenReturn(deserializedContext);
        when(jobMonitorService.getJobInstanceId(jobExecutionId)).thenReturn(jobInstanceId);
        when(jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT)).thenReturn(jobView);
        doNothing().when(problemService).createClosedProblem(jobInstanceId, jobExecutionId, stepExecution.getStepExecutionId(), "Group Evaluation Failed", ErrorCode.GROUP_EVALUATION_FAILED, PacmanWorkContextHelper.getPropertyId());

        assertThrows(TetrisException.class, () -> spy.evaluate(groupEvaluation));

        verify(problemService).createClosedProblem(jobInstanceId, jobExecutionId, stepExecution.getStepExecutionId(), "Group Evaluation Failed", ErrorCode.GROUP_EVALUATION_FAILED, PacmanWorkContextHelper.getPropertyId());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void evaluateWherePropertyisReadOnly() {
        GroupEvaluationService spy = Mockito.spy(service);

        when(regulatorService.isPropertyReadOnly()).thenReturn(true);

        // Mock out validation for capacities
        List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
        doReturn(occupancyDateCapacities).when(spy).getCapacitiesFromActivityForDateRange(Mockito.any(LocalDate.class),
                Mockito.any(LocalDate.class));

        assertThrows(TetrisException.class, () -> spy.evaluate(groupEvaluation));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void evaluate_isThrottledForEvaluation() {
        GroupEvaluationService spy = Mockito.spy(service);

        when(regulatorService.isPropertyReadOnly()).thenReturn(true);

        // Mock out validation for capacities
        List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
        doReturn(occupancyDateCapacities).when(spy).getCapacitiesFromActivityForDateRange(Mockito.any(LocalDate.class),
                Mockito.any(LocalDate.class));

        assertThrows(TetrisException.class, () -> spy.evaluate(groupEvaluation));
    }

    @Test
    public void evaluate_ExceptionThrownIfRoomClassSyncRequired() {
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        when(syncFlagService.isSyncEnabledFor(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED)).thenReturn(true);

        assertThrows(TetrisException.class, () -> service.evaluate(groupEvaluation));
    }

    @Test
    public void searchForGroupNameExactMatch() {
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setGroupName(GroupEvaluationObjectMother.GROUP_NAME);

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForGroupNameLikeFirstMatch() {
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setGroupName("Group");

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        // Search
        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForGroupNameLikeLastMatch() {
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setGroupName("Group");

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForGroupNameNotFound() {
        saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setGroupName("Blah");

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size());
    }

    @Test
    public void searchForMaterializationStatus() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setMaterializationStatus(GroupPricingMaterializationStatus.SCENARIO);

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForMaterializationStatusNoneFound() {
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setMaterializationStatus(GroupPricingMaterializationStatus.TENTATIVE);

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        // Search
        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationStartDateIncludesPreviousDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartEvaluationDate(new LocalDate().minusDays(1));

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationStartDateIncludesCurrentDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartEvaluationDate(new LocalDate());

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationStartDateDoesNotIncludeFutureDate() {
        saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartEvaluationDate(new LocalDate().plusDays(1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationEndDateDoesNotIncludePastDate() {
        saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndEvaluationDate(new LocalDate(2012, 1, 1).minusDays(1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationEndDateIncludesCurrentDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndEvaluationDate(new LocalDate());

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationEndDateDoesIncludesFutureDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndEvaluationDate(new LocalDate().plusDays(1));

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithUser() {
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setUserIds(Sets.newHashSet(USER_ID));

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithUserWithNoEvaluations() {
        saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setUserIds(Sets.newHashSet(-1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStartStayDateSameAsPreferredDate() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertTrue(1 <= foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStartStayDateSameAsPreferredDateUsingOnlyPreferredDates() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setPreferredDatesOnly(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertTrue(1 <= foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStartStayDateBeforeArrivalDate() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate().minusDays(1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertTrue(1 <= foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStartStayDateBeforeArrivalDateOnlyPreferredDates() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setPreferredDatesOnly(false);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertTrue(1 <= foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithEndStayDateSameAsPreferredDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndStayDate(GroupEvaluationObjectMother.PREFERRED_DATE);

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithEndStayDateSameAsPreferredDateOnlyPreferredDates() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndStayDate(GroupEvaluationObjectMother.PREFERRED_DATE);
        groupEvaluationSearchCriteria.setPreferredDatesOnly(true);

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithEndStayDateBeforeDate() {
        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setEndStayDate(GroupEvaluationObjectMother.PREFERRED_DATE.minusDays(1));

        saveGroupEvaluationForSearching();

        List<GroupEvaluation> originalFoundEvaluations = service.search(groupEvaluationSearchCriteria);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(0, foundEvaluations.size() - originalFoundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithSameStartAndEndDate() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithSameStayDatesContainedWithinDayOfStayRange() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate().plusDays(1));
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate().plusDays(1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStartStayDateAfterFirstDayOfStayDateButInRange() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate().plusDays(1));

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertTrue(1 <= foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithStayDatesAndHydrateResults() {
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        foundEvaluations.forEach(evaluation -> {
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationArrivalDates()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationDayOfStays()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationCosts()));
            assertTrue(Hibernate.isInitialized(evaluation.getMarketSegment()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationRoomTypes()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationOnBooks()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationOnBooksCurrentBARSet()));
        });
        assertEquals(1, foundEvaluations.size());
    }

    @Test
    public void searchForEvaluationWithFunctionSpaceAndHydrateResults() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithFunctionSpace(new LocalDateTime(2015, 1, 1, 8, 0), new LocalDateTime(2015, 1, 1, 10, 0));

        FunctionSpaceResourceType resourceType = FunctionSpaceObjectMother.buildFunctionSpaceResourceType("Bevarage",
                "Bevarage", false, false);
        resourceType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService().save(resourceType);

        FunctionSpaceRevenueGroup revenueGroup = groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets()
                .iterator().next().getFunctionSpaceRevenueGroup();

        revenueGroup.setResourceType(resourceType);
        tenantCrudService().save(groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().iterator().next()
                .getFunctionSpaceRevenueGroup());

        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
        assertEquals(1, foundEvaluations.get(0).getGroupEvaluationFunctionSpaces().size());
    }

    @Test
    public void searchForEvaluationWithFunctionSpaceComboRoomAndHydrateResults() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithFunctionSpace(new LocalDateTime(2015, 1, 1, 8, 0), new LocalDateTime(2015, 1, 1, 10, 0));
        FunctionSpaceCombinationFunctionRoom comboRoom = saveCombinationFunctionRoom();

        GroupEvaluationFunctionSpaceFunctionRoom groupEvalRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        groupEvalRoom.setFunctionSpaceFunctionRoom(comboRoom);
        groupEvalRoom
                .setGroupEvaluationFunctionSpace(groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next());

        groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next()
                .addGroupEvaluationFunctionSpaceFunctionRoom(groupEvalRoom);

        FunctionSpaceResourceType resourceType = FunctionSpaceObjectMother.buildFunctionSpaceResourceType("Bevarage",
                "Bevarage", false, false);
        resourceType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService().save(resourceType);

        FunctionSpaceRevenueGroup revenueGroup = groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets()
                .iterator().next().getFunctionSpaceRevenueGroup();

        revenueGroup.setResourceType(resourceType);
        tenantCrudService().save(groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().iterator().next()
                .getFunctionSpaceRevenueGroup());

        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
        assertEquals(1, foundEvaluations.get(0).getGroupEvaluationFunctionSpaces().size());

        GroupEvaluationFunctionSpace retFunctionSpace = foundEvaluations.get(0).getGroupEvaluationFunctionSpaces()
                .iterator().next();
        assertEquals(1, retFunctionSpace.getGroupEvaluationFunctionSpaceFunctionRooms().size());

        GroupEvaluationFunctionSpaceFunctionRoom functionRoom = retFunctionSpace
                .getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();

        assertTrue(functionRoom.getFunctionSpaceFunctionRoom() instanceof FunctionSpaceCombinationFunctionRoom);

        FunctionSpaceCombinationFunctionRoom retComboRoom = (FunctionSpaceCombinationFunctionRoom) functionRoom
                .getFunctionSpaceFunctionRoom();
        assertEquals(2, retComboRoom.getIndivisibleFunctionRoomParts().size());
    }

    @Test
    public void search_forRoomClassEvaluationAndHydrateResultsUsingServicingCostByLOS() {
        when(servicingCostByLOSService.isProfitOptimizationEnabled()).thenReturn(Boolean.TRUE);
        AccomClass newAccomClass = new AccomClass();
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setAccomClass(newAccomClass);
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ZERO);
        when(servicingCostByLOSService.getGroupPricingConfigDetails(eq(PacmanWorkContextHelper.getPropertyId()))).thenReturn(singletonList(groupPricingConfiguration));
        List<GroupEvaluation> foundEvaluations = getGroupEvaluations();
        GroupEvaluation foundEvaluation = foundEvaluations.get(0);

        Map<AccomClass, BigDecimal> roomServiceCosts = foundEvaluation.getPerRoomServingCostByRoomType();
        assertEquals(BigDecimal.ZERO, roomServiceCosts.get(newAccomClass));
        verify(servicingCostByLOSService).getGroupPricingConfigDetails(PacmanWorkContextHelper.getPropertyId());
    }

    @Test
    public void search_forRoomClassEvaluationAndHydrateResults() {

        AccomClass newAccomClass = new AccomClass();
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setAccomClass(newAccomClass);
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ZERO);
        when(groupPricingConfigurationService.getGroupPricingConfigurationsPerRoomClass()).thenReturn(singletonList(groupPricingConfiguration));

        List<GroupEvaluation> foundEvaluations = getGroupEvaluations();

        GroupEvaluation foundEvaluation = foundEvaluations.get(0);

        Map<AccomClass, BigDecimal> roomServiceCosts = foundEvaluation.getPerRoomServingCostByRoomType();
        assertEquals(BigDecimal.ZERO, roomServiceCosts.get(newAccomClass));
        verify(groupPricingConfigurationService).getGroupPricingConfigurationsPerRoomClass();
    }

    private List<GroupEvaluation> getGroupEvaluations() {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationForRoomClassSearch();
        tenantCrudService().save(groupEvaluation.getGroupEvaluationConferenceAndBanquets().iterator().next().getGroupPricingConfigurationConferenceAndBanquet());
        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();
        tenantCrudService().deleteAll(GroupPricingConfiguration.class);
        tenantCrudService.flushAndClear();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
        return foundEvaluations;
    }

    @Test
    public void applyOnBooks() {
        GroupEvaluationArrivalDateDisplacementAndForecastDetail detail = new GroupEvaluationArrivalDateDisplacementAndForecastDetail();
        detail.setOccupancyDate(new LocalDate());

        service.applyOnBooks(singletonList(detail));

        assertTrue(0 < detail.getGroupOnBooks());
        assertTrue(0 < detail.getTransientOnBooks());
    }

    @Test
    void shouldInitOnBooksIfNeededForDisplacementAndForecastDetails() {
        GroupEvaluationArrivalDateDisplacementAndForecastDetail postStayDisplacementAndForecastDetail =
                createDisplacementAndForecastDetail(AbstractGroupEvaluationDetail.RecordType.POST_STAY);
        GroupEvaluationArrivalDateDisplacementAndForecastDetail dayOfStayDisplacementAndForecastDetail =
                createDisplacementAndForecastDetail(AbstractGroupEvaluationDetail.RecordType.DAY_OF_STAY);

        service.applyOnBooks(List.of(postStayDisplacementAndForecastDetail, dayOfStayDisplacementAndForecastDetail));

        assertTrue(postStayDisplacementAndForecastDetail.isOnBooksApplied());
        assertTrue(dayOfStayDisplacementAndForecastDetail.isOnBooksApplied());
        assertTrue(0 < dayOfStayDisplacementAndForecastDetail.getGroupOnBooks());
        assertTrue(0 < dayOfStayDisplacementAndForecastDetail.getTransientOnBooks());
    }

    @Test
    public void getGroupEvaluationFunctionSpaceRooms() throws Exception {
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom comboRoom = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Blue");
        FunctionSpaceFunctionRoom indivisibleRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red");
        allRooms.add(comboRoom);
        allRooms.add(indivisibleRoom);

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);

        List<FunctionSpaceFunctionRoom> returnedRooms = service.getGroupEvaluationFunctionSpaceRooms();

        assertEquals(2, returnedRooms.size());

        verify(functionSpaceConfigurationService).getAllActiveRoomsIncludedForPricing();
    }

    @Test
    public void getGroupEvaluationFunctionSpaceRooms_NoActivRoomsIncludedForPricing() throws Exception {
        List<FunctionSpaceFunctionRoom> indivisibleRooms = new ArrayList<>();

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing())
                .thenReturn(indivisibleRooms);

        List<FunctionSpaceFunctionRoom> returnedRooms = service.getGroupEvaluationFunctionSpaceRooms();

        assertEquals(0, returnedRooms.size());

        verify(functionSpaceConfigurationService).getAllActiveRoomsIncludedForPricing();
    }

    @Test
    public void getCapacitiesForDateRangeByAccomType() throws Exception {
        LocalDate startDate = new LocalDate(2015, 7, 1);
        LocalDate endDate = new LocalDate(2015, 7, 1);

        AccomType accomType = new AccomType();
        accomType.setId(5);
        accomType.setAccomTypeCode("TEST");

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType);

        OverbookingAccomLevelView accomLevel = new OverbookingAccomLevelView();
        accomLevel.setAvailableCapacity(25);
        accomLevel.setOccupancyDate(startDate.toDate());
        List<OverbookingAccomLevelView> accomLevels = new ArrayList<>();
        accomLevels.add(accomLevel);

        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(accomTypes);
        when(
                overbookingService.executeQueryAccomTypeLevel(accomType.getId(), startDate.toDate(), endDate.toDate()))
                .thenReturn(accomLevels);

        Map<AccomType, List<OverbookingAccomLevelView>> retMap = service.getCapacitiesForDateRangeByAccomType(startDate,
                endDate);

        assertNotNull(retMap);

        List<OverbookingAccomLevelView> accomTypeLevels = retMap.get(accomType);

        assertEquals(1, accomTypeLevels.size());
        assertEquals(startDate, new LocalDate(accomTypeLevels.get(0).getOccupancyDate()));
        assertEquals(new Integer(25), accomTypeLevels.get(0).getAvailableCapacity());

        verify(overbookingService).executeQueryAccomTypeLevel(accomType.getId(), startDate.toDate(),
                endDate.toDate());
    }

    @Test
    public void getRohPhysicalCapacity() throws Exception {
        Integer physicalCapacity = service.getRohPhysicalCapacity();
        assertNotNull(physicalCapacity);
        assert (physicalCapacity >= 0);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getRoomTypeCapacitiesForDateRange() throws Exception {

        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate endDate = startDate.plusDays(4);

        AccomType accomType = new AccomType();
        accomType.setId(6);
        accomType.setAccomTypeCode("TEST");

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType);

        RoomTypeOccupancyDateCapacityDetails roomTypeCapacity = new RoomTypeOccupancyDateCapacityDetails();
        roomTypeCapacity.setCapacity(39);
        roomTypeCapacity.setOccupancyDate(startDate);
        roomTypeCapacity.setAccomTypeId(6);
        roomTypeCapacity.setRemainingCapacity(1);
        List<RoomTypeOccupancyDateCapacityDetails> roomTypeCapacities = new ArrayList<>();
        roomTypeCapacities.add(roomTypeCapacity);

        when(tenantCrudService.findByNativeQuery(Mockito.anyString(), Mockito.anyMap(),
                Mockito.any(RoomTypeOccupancyDateCapacityDetailsRowMapper.class))).thenReturn(roomTypeCapacities);
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> retMap = service
                .getRoomTypeCapacitiesFromActivityForDateRange(accomTypes, startDate, endDate);

        assertNotNull(retMap);

        List<RoomTypeOccupancyDateCapacityDetails> roomTypeCapacitiesForDate = retMap.get(accomType);
        assertEquals(5, roomTypeCapacitiesForDate.size());
        assertEquals(6, roomTypeCapacities.get(0).getAccomTypeId().intValue());
        assertEquals(new Integer(39), roomTypeCapacitiesForDate.get(0).getCapacity());

        // remaining capacity also contains overbooking value
        assertEquals(new Integer(2), roomTypeCapacitiesForDate.get(0).getRemainingCapacity());
    }

    @Test
    public void getRoomTypeCapacitiesForDateRangeDetailsTest() {
        GroupEvaluationService realService = new GroupEvaluationService();
        List<AccomType> accomTypes = tenantCrudService().findAll(AccomType.class);

        inject(realService, "tenantCrudService", tenantCrudService());

        LocalDate startDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate endDate = startDate.plusDays(2);
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> resultMap = realService.getRoomTypeCapacitiesFromActivityForDateRange(accomTypes, startDate, endDate);
        assertEquals(5, resultMap.size());

        for (AccomType at : resultMap.keySet()) {
            if (at.getId() == 5) {
                List<RoomTypeOccupancyDateCapacityDetails> list = resultMap.get(at);
                assertEquals(35, list.get(0).getCapacity().intValue());
                assertEquals(1, list.get(0).getRemainingCapacity().intValue());
                assertEquals(39, list.get(1).getCapacity().intValue());
                assertEquals(2, list.get(1).getRemainingCapacity().intValue());
            }
        }
    }

    @Test
    public void getRoomTypeCapacitiesForDateRangeDetails_NoWashForecastData() {
        GroupEvaluationService realService = new GroupEvaluationService();
        GroupPricingConfigurationService groupPricingConfigurationService = new GroupPricingConfigurationService();
        inject(groupPricingConfigurationService, "tenantCrudService", tenantCrudService());
        inject(realService, "tenantCrudService", tenantCrudService());
        inject(realService, "groupPricingConfigurationService", groupPricingConfigurationService);

        LocalDate startDate = new LocalDate(DateUtil.getDateForCurrentMonth(10));
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> resultForCR = realService.getRoomTypeCapacitiesFromActivityForDateRange(new ArrayList(), startDate, startDate);

        // Delete wash forecast groups for one day to simulate no forecast data
        tenantCrudService().executeUpdateByNativeQuery("delete from Wash_FCST where Occupancy_DT=:occupancyDate",
                QueryParameter.with("occupancyDate", startDate.toDate()).parameters());

        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> resultWithoutWash = realService.getRoomTypeCapacitiesFromActivityForDateRange(new ArrayList<>(), startDate, startDate);

        for (AccomType at : resultForCR.keySet()) {
            if (at.getId() == 5) {
                List<RoomTypeOccupancyDateCapacityDetails> listCr = resultForCR.get(at);
                List<RoomTypeOccupancyDateCapacityDetails> listWithoutWash = resultWithoutWash.get(at);
                assertEquals(listCr.get(0).getCapacity(), listWithoutWash.get(0).getCapacity());
                assertEquals(listCr.get(0).getRemainingCapacity(), listWithoutWash.get(0).getRemainingCapacity());
            }
        }
    }

    private GroupEvaluation saveGroupEvaluationForSearching() {
        GroupEvaluationService spy = Mockito.spy(service);

        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPerRoomServicingCost(BigDecimal.ONE);

        doReturn(BigDecimal.ONE).when(spy).getPerRoomServicingCost(groupEvaluation);

        spy.save(groupEvaluation, false, "1233");
        tenantCrudService().flushAndClear();

        return groupEvaluation;
    }

    private FunctionSpaceCombinationFunctionRoom saveCombinationFunctionRoom() {
        FunctionSpaceCombinationFunctionRoom functionSpaceCombinationFunctionRoom = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Combination Room");
        functionSpaceCombinationFunctionRoom.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        // Need to save a room type
        FunctionSpaceFunctionRoomType functionSpaceFunctionRoomType = FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoomType();
        functionSpaceFunctionRoomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService().save(functionSpaceFunctionRoomType);

        for (FunctionSpaceFunctionRoom indivisibleFunctionRoomPart : functionSpaceCombinationFunctionRoom
                .getIndivisibleFunctionRoomParts()) {
            indivisibleFunctionRoomPart.setFunctionSpaceFunctionRoomType(functionSpaceFunctionRoomType);
            indivisibleFunctionRoomPart.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            tenantCrudService().save(indivisibleFunctionRoomPart);
        }

        functionSpaceCombinationFunctionRoom.setFunctionSpaceFunctionRoomType(functionSpaceFunctionRoomType);

        functionSpaceCombinationFunctionRoom = tenantCrudService().save(functionSpaceCombinationFunctionRoom);

        tenantCrudService().flushAndClear();

        return functionSpaceCombinationFunctionRoom;
    }

    @Test
    public void getMarketSegmentForProperty() {
        Integer propertyId = 5;
        MarketSegmentSummary mss = new MarketSegmentSummary();
        mss.setCode("Disc");

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setPropertyId(propertyId);
        groupEvaluation.setMarketSegment(mss);

        MarketSegmentSummary retVal = service.getMarketSegmentForProperty(groupEvaluation);

        assertNotNull(retVal);
        assertEquals(mss.getCode(), retVal.getCode());
        assertEquals("Discount", retVal.getName());
    }


    @Test
    public void isPropertyOneWayOrTwoWay() throws Exception {
        Mockito.when(propertyService.getPropertyById(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()))
                .thenReturn(getProperty(null))
                .thenReturn(getProperty(Stage.ONE_WAY))
                .thenReturn(getProperty(Stage.TWO_WAY))
                .thenReturn(getProperty(Stage.DATA_CAPTURE));
        assertFalse(service.isPropertyOneWayOrTwoWay());
        assertTrue(service.isPropertyOneWayOrTwoWay());
        assertTrue(service.isPropertyOneWayOrTwoWay());
        assertFalse(service.isPropertyOneWayOrTwoWay());
    }

    private Property getProperty(Stage stage) {
        Property property = new Property();
        property.setStage(stage);
        return property;
    }

    @Test
    public void name() throws Exception {
        service.setTenantCrudService(tenantCrudService);
        GroupEvaluationService spy = Mockito.spy(service);
        GroupEvaluation mock = Mockito.mock(GroupEvaluation.class);
        Mockito.when(mock.getEvaluationDate()).thenReturn(new LocalDateTime());
        Mockito.when(mock.isPartOfMultiGroupEvaluation()).thenReturn(true);
        Mockito.when(mock.cloneFromMultiGroupEvaluation()).thenReturn(groupEvaluation);

        spy.saveAndSetParentReferences(mock);

        Mockito.verify(spy).setParentReference(mock);
        Mockito.verify(tenantCrudService).save(groupEvaluation);
    }

    @Test
    public void onValidSave_whenEvalRequestFromClient_sendResultBackToClient() {
        cookMocks()
                .getGroupEvaluationForSaving(NOT_NULL_BOOKING_ID)
                .isEvaluationRequestFromFDC(true)
                .saveGrpEvaluation()
                .verifyGroupEvaluationIsSaved()
                .verifyEvaluationResultSentToClient();
    }


    @Test
    public void onValidSave_whenEvalRequestFromG3_shouldNotSendResultBackToClient() {
        cookMocks()
                .getGroupEvaluationForSaving(null)
                .isEvaluationRequestFromFDC(false)
                .saveGrpEvaluation()
                .verifyGroupEvaluationIsSaved()
                .verifyEvaluationResultNotSentToClient();
    }

    @Test
    public void onValidSave_whenReEvaluationRequestFromG3_shouldNotSendResultBackToClient() {
        cookMocks()
                .getGroupEvaluationForSaving(null)
                .isEvaluationRequestFromFDC(false)
                .saveGrpEvaluation()
                .verifyGroupEvaluationIsSaved()
                .verifyEvaluationResultNotSentToClient();
    }

    @Test
    public void onValidSave_whenSendToEvalResultToggleIsTurnedOn() {
        cookMocks()
                .sendToEvalResultIsFalse(true)
                .getGroupEvaluationForSaving(NOT_NULL_BOOKING_ID)
                .isEvaluationRequestFromFDC(true)
                .saveGrpEvaluation()
                .verifyGroupEvaluationIsSaved()
                .verifyEvaluationResultSentToClient();
    }

    @Test
    public void onValidSave_whenSendToEvalResultToggleISTurnedOff() {
        cookMocks()
                .sendToEvalResultIsFalse(false)
                .getGroupEvaluationForSaving(NOT_NULL_BOOKING_ID)
                .isEvaluationRequestFromFDC(false)
                .saveGrpEvaluation()
                .verifyGroupEvaluationIsSaved()
                .verifyEvaluationResultNotSentToClient();
    }


    @Test
    public void dataFeedShouldFailWhenStartDateNull() {
        //When
        service.setTenantCrudService(tenantCrudService);
        assertThrows(TetrisException.class, () -> service.getSavedGroupEvaluationsForDateRange(null, TEST_START_POSITION, TEST_SIZE));
    }

    private Map<String, Object> getParamMap(Date startDate) {
        LocalDateTime startDateTime = LocalDateTime.fromDateFields(startDate);
        Map<String, Object> parameters = QueryParameter.with(GroupEvaluationService.START_DATE, startDateTime).parameters();
        return parameters;
    }


    @Test
    public void getDataFeedForGroupEvaluationsWhenNoDataPresent() {

        //Given
        Map<String, Object> queryParams = getParamMap(MOCK_START_DATE);

        //When
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuery(GroupEvaluation.FIND_BY_DATE_RANGE, queryParams, TEST_START_POSITION, TEST_SIZE)).thenReturn(Collections.emptyList());
        List<GroupEvaluation> list = service.getSavedGroupEvaluationsForDateRange(MOCK_START_DATE, TEST_START_POSITION, TEST_SIZE);

        //Then
        assertEquals(0, list.size());
        verify(tenantCrudService, times(1)).findByNamedQuery(GroupEvaluation.FIND_BY_DATE_RANGE, queryParams, TEST_START_POSITION, TEST_SIZE);
    }


    @Test
    public void testGroupEvaluationPresent() {

        //Given
        Map<String, Object> queryParams = getParamMap(MOCK_START_DATE);
        GroupEvaluation evaluation = getDummyGroupEvaluation(GroupPricingEvaluationMethod.RC);

        //When
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuery(any(), any(), anyInt(), anyInt())).thenReturn(singletonList(evaluation));
        when(tenantCrudService.findByNamedQuery(any(), any())).thenReturn(singletonList(evaluation));
        List<GroupEvaluation> list = service.getSavedGroupEvaluationsForDateRange(MOCK_START_DATE, TEST_START_POSITION, TEST_SIZE);

        //Then
        assertEquals(1, list.size());
    }

    @Test
    public void testGetUserIdToFullnameMapForNoGroupEvaluations() {

        //Given
        List<GroupEvaluation> evaluations = Collections.EMPTY_LIST;

        //When
        when(userService.getGlobalUsers(Mockito.any())).thenReturn(getGlobalUsers());
        Map<Integer, String> userMap = service.getUserIdToFullnameMapForGroupEvaluations(evaluations);

        //Then
        assertEquals(0, userMap.size());
    }

    @Test
    public void testGetUserIdToFullnameMapForGroupEvaluations() {

        //Given
        GroupEvaluation evaluation = getDummyGroupEvaluation(GroupPricingEvaluationMethod.RC);
        List<GroupEvaluation> evaluations = new ArrayList<>();
        evaluations.add(evaluation);

        //When
        when(userService.getGlobalUsers(Mockito.any())).thenReturn(getGlobalUsers());
        Map<Integer, String> userMap = service.getUserIdToFullnameMapForGroupEvaluations(evaluations);

        //Then
        assertEquals(1, userMap.size());
        assertNotNull(userMap.get(USER_ID));
    }

    @Test
    public void shouldHydrateArrivalDatesForGroupEvaluations() {
        final List<GroupEvaluation> groupEvaluations = service.getSavedGroupEvaluationsForDateRange(MOCK_START_DATE, 0, 100);
        assertTrue(CollectionUtils.isNotEmpty(groupEvaluations));
        final GroupEvaluation groupEvaluation = groupEvaluations.get(0);
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationArrivalDates()));
        Object[] objects = groupEvaluation.getGroupEvaluationArrivalDates().toArray();
        assertTrue((Hibernate.isInitialized(((GroupEvaluationArrivalDate) (objects[0])).getGroupEvaluationArrivalDateAccomClasses())));
        assertTrue((Hibernate.isInitialized(((GroupEvaluationArrivalDate) (objects[0])).getGroupEvaluationArrivalDateForecastGroups())));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationRoomTypes()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getMarketSegment()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationDayOfStays()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationConferenceAndBanquets()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationAncillaries()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationCosts()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationFunctionSpaces()));

    }

    @Test
    public void shouldNotFailWithSyntaxErrorWhenNoGroupEvaluationIsPresent() {
        Date wayFutureDate = DateUtil.getDate(13, 1, 2999);
        final List<GroupEvaluation> groupEvaluations = service.getSavedGroupEvaluationsForDateRange(wayFutureDate, 0, 100);
        assertEquals(0, groupEvaluations.size());
    }

    @Test
    public void arrivalDateGuestRoomRatesShouldNotBeInitializedWhenEmpty() {
        final List<GroupEvaluation> groupEvaluations = service.getSavedGroupEvaluationsForDateRange(MOCK_START_DATE, 0, 100);
        assertTrue(CollectionUtils.isNotEmpty(groupEvaluations));
        final GroupEvaluation groupEvaluation = groupEvaluations.get(0);
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationArrivalDates()));
        groupEvaluation.getGroupEvaluationArrivalDates().forEach(
                groupEvaluationArrivalDate -> assertFalse(Hibernate.isInitialized(groupEvaluationArrivalDate.getGroupEvaluationArrivalDateGuestRoomRates()))
        );
    }

    @Test
    public void guestRoomRatesAreInitialized() {
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getPreferredGroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.addGroupEvaluationArrivalDateGuestRoomRates(new GroupEvaluationArrivalDateGuestRoomRates());
        service.loadGuestRoomRatesByOccupancyDateDetails(groupEvaluationArrivalDate);

        assertTrue(Hibernate.isInitialized(groupEvaluationArrivalDate.getGroupEvaluationArrivalDateGuestRoomRates()));
        assertTrue(Hibernate.isInitialized(groupEvaluationArrivalDate.getGroupEvaluationArrivalDateGuestRoomRates().get(0)));
    }

    @Test
    public void onBooksAreInitialized() {
        GroupEvaluationOnBooks groupEvaluationOnBooks = new GroupEvaluationOnBooks();
        groupEvaluation.setGroupEvaluationOnBooks(Stream.of(groupEvaluationOnBooks).collect(Collectors.toSet()));
        service.loadGroupEvaluationOnBooks((groupEvaluation));

        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationOnBooks()));
        assertTrue(Hibernate.isInitialized(groupEvaluation.getGroupEvaluationOnBooks().iterator().next()));
    }

    @Test
    void shouldAddCurrentBarDataToGroupEvaluation() {
        final int los = 1;
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(los);

        GroupEvaluation groupEvaluation = spy(new GroupEvaluation());
        doReturn(los).when(groupEvaluation).getNumberOfNights();
        when(groupEvaluation.getEarliestArrivalDate()).thenReturn(startDate);

        List<CurrentBarDTO> currentBarDTOList = new ArrayList<>();

        final List<CurrentBarDetails> currentBarDetails = Arrays.asList(new CurrentBarDetails(startDate, BigDecimal.valueOf(10.00)), new CurrentBarDetails(endDate, BigDecimal.valueOf(10.00)));
        currentBarDTOList.add((new CurrentBarDTO("DELUXE", 1, 1,
                currentBarDetails)));
        assertEquals(0, groupEvaluation.getGroupEvaluationOnBooksCurrentBARSet().size());
        service.addCurrentBarDataToGroupEvaluation(currentBarDTOList, groupEvaluation);
        assertEquals(2, groupEvaluation.getGroupEvaluationOnBooksCurrentBARSet().size());
    }

    @Test
    void populateCurrentBarDTOListUsingCPDecisionBAROutputShouldReturnEmptyList() {
        final List<Object> result = tenantCrudService().findByNativeQuery("select max(Arrival_DT) from CP_Decision_Bar_Output");

        LocalDate endDate = LocalDate.fromDateFields((Date) result.get(0)).plusDays(2);
        LocalDate startDate = endDate.minusDays(1);
        final List<CurrentBarDTO> currentBarDTOs = service.populateCurrentBarDTOListUsingCPDecisionBAROutputByDateRange(startDate, endDate);

        assertEquals(0, currentBarDTOs.size());
    }

    @Test
    void populateCurrentBarDTOListUsingCPDecisionBAROutputShouldReturnValidData() {

        final List<Object> result = tenantCrudService().findByNativeQuery("select max(Arrival_DT) from CP_Decision_Bar_Output");

        LocalDate endDate = LocalDate.fromDateFields((Date) result.get(0));
        LocalDate startDate = endDate.minusDays(2);
        final BigDecimal currentBAR = BigDecimal.valueOf(100.00).setScale(2, RoundingMode.HALF_UP);

        prepareCPDecisionBAROutputTestData(endDate, startDate, currentBAR);

        final List<CurrentBarDTO> currentBarDTOs = service.populateCurrentBarDTOListUsingCPDecisionBAROutputByDateRange(startDate, endDate);

        assertEquals(3, currentBarDTOs.size());

        currentBarDTOs.forEach(currentBarDTO1 -> {
            assertNotNull(currentBarDTO1.getAccomClassId());
            assertNotNull(currentBarDTO1.getAccomClassCode());
            assertNotNull(currentBarDTO1.getAccomClassRankOrder());
            List<CurrentBarDetails> currentBarDetailsList = currentBarDTO1.getCurrentBarDetailsList();
            assertEquals(startDate, currentBarDetailsList.get(0).getOccupancyDate());
            assertEquals(currentBAR, currentBarDetailsList.get(0).getCurrentBar());

            assertEquals(startDate.plusDays(1), currentBarDetailsList.get(1).getOccupancyDate());
            assertEquals(currentBAR, currentBarDetailsList.get(1).getCurrentBar());

            assertEquals(startDate.plusDays(2), currentBarDetailsList.get(2).getOccupancyDate());
            assertEquals(currentBAR, currentBarDetailsList.get(2).getCurrentBar());
        });
    }

    @Test
    void getMarketSegmentsForGroupForecastTypeAndStatus() {
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set Status_ID = 3 where mkt_seg_id = 14");
        List<MarketSegmentSummary> mktSegs = service.getMarketSegmentsForGroupForecastTypeAndStatus(singletonList(PacmanWorkContextHelper.getPropertyId()), true);
        assertTrue(mktSegs.isEmpty());
        mktSegs = service.getMarketSegmentsForGroupForecastTypeAndStatus(singletonList(PacmanWorkContextHelper.getPropertyId()), false);
        assertEquals(1, mktSegs.size());
    }

    @Test
    void shouldBeAbleToGetMarketSegmentsForGroupForecastType(){
        List<MarketSegmentSummary> mktSegs = service.getMarketSegmentsForGroupForecastTypeAndStatus(singletonList(PacmanWorkContextHelper.getPropertyId()), true);
        assertEquals("Disc", mktSegs.get(0).getCode());
    }

    private void prepareCPDecisionBAROutputTestData(LocalDate endDate, LocalDate startDate, BigDecimal currentBAR) {
        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService().findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, startDate, endDate));


        cpDecisionBAROutputs.forEach(cpDecisionBAROutput -> {
            cpDecisionBAROutput.setFinalBAR(currentBAR);
        });

        tenantCrudService().save(cpDecisionBAROutputs);
        tenantCrudService().flushAndClear();
    }

    @Test
    void shouldPopulateAndSaveCurrentBarInformation() {
        final List<Object> result = tenantCrudService().findByNativeQuery("select max(Arrival_DT) from CP_Decision_Bar_Output");

        LocalDate endDate = LocalDate.fromDateFields((Date) result.get(0));
        final int days = 2;
        LocalDate startDate = endDate.minusDays(days);
        final BigDecimal currentBAR = BigDecimal.valueOf(100.00).setScale(2, RoundingMode.HALF_UP);

        prepareCPDecisionBAROutputTestData(endDate, startDate, currentBAR);
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        when(groupEvaluation.getEarliestArrivalDate()).thenReturn(startDate);
        when(groupEvaluation.getNumberOfNights()).thenReturn(days);
        final List<GroupEvaluationOnBooksCurrentBAR> groupEvaluationOnBooksCurrentBARList = service.populateAndSaveCurrentBarInformation(groupEvaluation);
        assertEquals(9, groupEvaluationOnBooksCurrentBARList.size());
    }

    @Test
    public void getRoomsCapacitiesForExtendedDates_PartialActivityDataForSelectedDates() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.minusDays(10);
        LocalDate endDate = maxAccomActivityDate.plusDays(10);

        List<OccupancyDateCapacity> results = service.getRoomsCapacitiesForExtendedDates(startDate, endDate);

        assertEquals(21, results.size());
        results.stream().map(OccupancyDateCapacity::getPhysicalCapacity).forEach(Assertions::assertNotNull);
        results.stream().map(OccupancyDateCapacity::getAvailableCapacity).forEach(Assertions::assertNotNull);
    }

    @Test
    public void getRoomsCapacitiesForExtendedDates_lastOccupancyIsTheStartDate() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate endDate = maxAccomActivityDate.plusDays(10);

        List<OccupancyDateCapacity> results = service.getRoomsCapacitiesForExtendedDates(maxAccomActivityDate, endDate);

        assertEquals(11, results.size());
        results.stream().map(OccupancyDateCapacity::getPhysicalCapacity).forEach(Assertions::assertNotNull);
        results.stream().map(OccupancyDateCapacity::getAvailableCapacity).forEach(Assertions::assertNotNull);
    }

    @Test
    public void getRoomsCapacitiesForExtendedDates_NoActivityDataForSelectedDates() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.plusDays(10);
        LocalDate endDate = maxAccomActivityDate.plusDays(20);

        List<OccupancyDateCapacity> results = service.getRoomsCapacitiesForExtendedDates(startDate, endDate);

        assertEquals(11, results.size());
        results.stream().map(OccupancyDateCapacity::getPhysicalCapacity).forEach(Assertions::assertNotNull);
        results.stream().map(OccupancyDateCapacity::getAvailableCapacity).forEach(Assertions::assertNotNull);
    }

    @Test
    public void getRoomTypeCapacitiesForExtendedDateDetails_PartialActivityDataForSelectedDates() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.minusDays(10);
        LocalDate endDate = maxAccomActivityDate.plusDays(10);
        List<AccomType> accomTypes = tenantCrudService().findAll(AccomType.class);

        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> results =
                service.getRoomTypeCapacitiesForExtendedDateDetails(accomTypes, startDate, endDate);

        assertEquals(accomTypes.size(), results.size());
        results.values().forEach(occupancyDatesList -> assertEquals(21, occupancyDatesList.size()));
        results.values().stream().flatMap(Collection::stream)
                .map(RoomTypeOccupancyDateCapacityDetails::getCapacity).forEach(Assertions::assertNotNull);
        results.values().stream().flatMap(Collection::stream)
                .map(RoomTypeOccupancyDateCapacityDetails::getRemainingCapacity).forEach(Assertions::assertNotNull);
    }

    @Test
    public void getRoomTypeCapacitiesForExtendedDateDetails_NoActivityDataForSelectedDates() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.plusDays(10);
        LocalDate endDate = maxAccomActivityDate.plusDays(20);
        List<AccomType> accomTypes = tenantCrudService().findAll(AccomType.class);

        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> results =
                service.getRoomTypeCapacitiesForExtendedDateDetails(accomTypes, startDate, endDate);

        assertEquals(accomTypes.size(), results.size());
        results.values().forEach(occupancyDatesList -> assertEquals(11, occupancyDatesList.size()));
        results.values().stream().flatMap(Collection::stream)
                .map(RoomTypeOccupancyDateCapacityDetails::getCapacity).forEach(Assertions::assertNotNull);
        results.values().stream().flatMap(Collection::stream)
                .map(RoomTypeOccupancyDateCapacityDetails::getRemainingCapacity).forEach(Assertions::assertNotNull);
    }

    @Test
    public void getRoomTypeCapacitiesForMaxOccupancyDate() {
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> activityDatesCapacitiesMap = new HashMap<>();
        activityDatesCapacitiesMap = createAccomTypeCapacities();
        Map<AccomType, RoomTypeOccupancyDateCapacityDetails> result =
                service.getRoomTypeCapacitiesForMaxOccupancyDate(activityDatesCapacitiesMap);
        assertEquals(3, result.size());
        result.forEach((key, value) -> assertTrue(value.getOccupancyDate().isEqual(new LocalDate().plusDays(4))));
    }

    @Test
    public void createAccomTypeCapacityDetailsBeyondForecast_NoForecastData() {
        Map<AccomType, RoomTypeOccupancyDateCapacityDetails> maxOccupancyDateCapacityMap = new HashMap<>();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        RoomTypeOccupancyDateCapacityDetails roomTypeOccupancyDateCapacityDetails = new RoomTypeOccupancyDateCapacityDetails();
        roomTypeOccupancyDateCapacityDetails.setAccomTypeId(1);
        roomTypeOccupancyDateCapacityDetails.setOccupancyDate(new LocalDate().minusDays(10));
        roomTypeOccupancyDateCapacityDetails.setCapacity(100);
        roomTypeOccupancyDateCapacityDetails.setRemainingCapacity(100);
        maxOccupancyDateCapacityMap.put(accomType, roomTypeOccupancyDateCapacityDetails);
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(10);

        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> result =
                service.createAccomTypeCapacityDetailsBeyondForecast(maxOccupancyDateCapacityMap, emptyList(), startDate, endDate);

        assertEquals(1, result.size());
        result.forEach((at, capacityDetails) -> {
            assertEquals(11, capacityDetails.size());
            capacityDetails.stream()
                    .min(Comparator.comparing(RoomTypeOccupancyDateCapacityDetails::getOccupancyDate))
                    .ifPresent(minDateDetail -> assertTrue(minDateDetail.getOccupancyDate().isEqual(startDate)));
            capacityDetails.stream()
                    .max(Comparator.comparing(RoomTypeOccupancyDateCapacityDetails::getOccupancyDate))
                    .ifPresent(minDateDetail -> assertTrue(minDateDetail.getOccupancyDate().isEqual(endDate)));
        });
    }

    private Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> createAccomTypeCapacities() {
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> accomTypeCapacitiesMap = new HashMap<>();
        for (int i = 1; i < 4; i++) {
            AccomType accomType = new AccomType();
            accomType.setId(i);
            accomTypeCapacitiesMap.put(accomType, createAccomOccupancyDateCapacityDetails(i));
        }
        return accomTypeCapacitiesMap;
    }

    private List<RoomTypeOccupancyDateCapacityDetails> createAccomOccupancyDateCapacityDetails(Integer accomTypeId) {
        List<RoomTypeOccupancyDateCapacityDetails> accomOccupancyDateCapacityDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RoomTypeOccupancyDateCapacityDetails roomTypeOccupancyDateCapacityDetails = new RoomTypeOccupancyDateCapacityDetails();
            roomTypeOccupancyDateCapacityDetails.setAccomTypeId(accomTypeId);
            roomTypeOccupancyDateCapacityDetails.setOccupancyDate(new LocalDate().plusDays(i));
            roomTypeOccupancyDateCapacityDetails.setCapacity(100);
            roomTypeOccupancyDateCapacityDetails.setRemainingCapacity(100);
            accomOccupancyDateCapacityDetails.add(roomTypeOccupancyDateCapacityDetails);
        }
        return accomOccupancyDateCapacityDetails;
    }

    @Test
    public void getRoomsCapacitiesForExtendedDates_WithForecastedRoomsSold() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.plusDays(100);
        LocalDate endDate = maxAccomActivityDate.plusDays(110);

        doReturn(createFunctionSpaceBookingData(startDate, endDate))
                .when(service).getRoomsCapacitiesFromFunctionSpaceBooking(any(LocalDate.class), any(LocalDate.class));

        List<OccupancyDateCapacity> results = service.getRoomsCapacitiesForExtendedDates(startDate, endDate);

        assertEquals(11, results.size());
        results.stream().map(OccupancyDateCapacity::getPhysicalCapacity).forEach(Assertions::assertNotNull);
        results.stream().map(OccupancyDateCapacity::getAvailableCapacity).forEach(Assertions::assertNotNull);
        for (int i = 0; i < 5; i++) {
            assertEquals(0, results.get(i).getPhysicalCapacity() - results.get(i).getAvailableCapacity());
        }
        for (int i = 5; i < results.size(); i++) {
            assertEquals(10, results.get(i).getPhysicalCapacity() - results.get(i).getAvailableCapacity());
        }
    }

    private Map<LocalDate, Integer> createFunctionSpaceBookingData(LocalDate startDate, LocalDate endDate) {
        Map<LocalDate, Integer> results = new HashMap<>();
        for (LocalDate date = startDate.plusDays(5); !date.isAfter(endDate); date = date.plusDays(1)) {
            results.put(date, 10);
        }
        return results;
    }

    @Test
    public void getRoomTypeCapacitiesForExtendedDateDetails_WithForecastedRoomsSold() {
        LocalDate maxAccomActivityDate = LocalDateUtils.fromDate((Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        LocalDate startDate = maxAccomActivityDate.plusDays(100);
        LocalDate endDate = maxAccomActivityDate.plusDays(110);
        List<AccomType> accomTypes = tenantCrudService().findAll(AccomType.class);

        doReturn(createFunctionSpaceBookingDataByAccomType(accomTypes, startDate, endDate))
                .when(service).getRoomTypeCapacitiesFromFunctionSpaceBooking(any(LocalDate.class), any(LocalDate.class));

        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> results =
                service.getRoomTypeCapacitiesForExtendedDateDetails(accomTypes, startDate, endDate);

        assertEquals(accomTypes.size(), results.size());
        results.values().forEach(occupancyDatesList -> assertEquals(11, occupancyDatesList.size()));
        results.values().forEach(occupancyDatesList -> {
            for (int i = 0; i < 5; i++) {
                assertEquals(0, occupancyDatesList.get(i).getCapacity() - occupancyDatesList.get(i).getRemainingCapacity());
            }
        });
        results.values().forEach(occupancyDatesList -> {
            for (int i = 5; i < results.size(); i++) {
                assertEquals(0, occupancyDatesList.get(i).getCapacity() - occupancyDatesList.get(i).getRemainingCapacity());
            }
        });
    }

    private List<FunctionSpaceBookingRoomTypeForecastDetail> createFunctionSpaceBookingDataByAccomType(
            List<AccomType> accomTypes, LocalDate startDate, LocalDate endDate) {
        List<FunctionSpaceBookingRoomTypeForecastDetail> results = new ArrayList<>();
        for (LocalDate date = startDate.plusDays(5); !date.isAfter(endDate); date = date.plusDays(1)) {
            for (AccomType accomType : accomTypes) {
                FunctionSpaceBookingRoomTypeForecastDetail forecastDetail
                        = new FunctionSpaceBookingRoomTypeForecastDetail(new Object[]{date.toString(), accomType.getId(), 10});
                results.add(forecastDetail);
            }
        }
        return results;
    }

    private List<GlobalUser> getGlobalUsers() {
        List<GlobalUser> globalUsers = new ArrayList<>();
        GlobalUser user = new GlobalUser();
        user.setId(USER_ID);
        user.setFirstName("Test");
        user.setLastName("User");
        globalUsers.add(user);
        return globalUsers;
    }

    private GroupEvaluationServiceTest sendToEvalResultIsFalse(Boolean value) {
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(value);
        return this;
    }

    private void verifyEvaluationResultNotSentToClient() {
        verify(jobService, never()).startGuaranteedNewInstance(eq(JobName.NGIGroupEvalResults), anyMapOf(String.class, Object.class));
    }


    private void verifyEvaluationResultSentToClient() {
        verify(jobService).startGuaranteedNewInstance(eq(JobName.NGIGroupEvalResults), anyMapOf(String.class, Object.class));
    }

    private GroupEvaluationServiceTest verifyGroupEvaluationIsSaved() {
        verify(tenantCrudService).save(any(GroupEvaluation.class));
        return this;
    }

    private GroupEvaluationServiceTest saveGrpEvaluation() {
        service.save(groupEvaluationToSave, isEvaluationRequestFromFDC, "1233");
        return this;
    }

    private GroupEvaluationServiceTest getGroupEvaluationForSaving(String bookingId) {
        groupEvaluationToSave = buildGroupEvaluationFor(5, bookingId);
        groupEvaluationToSave.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        return this;
    }

    private GroupEvaluationServiceTest isEvaluationRequestFromFDC(boolean b) {
        isEvaluationRequestFromFDC = b;
        return this;
    }

    private GroupEvaluationServiceTest cookMocks() {
        service.setTenantCrudService(tenantCrudService);
        when(jobService.startGuaranteedNewInstance(eq(JobName.NGIGroupEvalResults), anyMapOf(String.class, Object.class))).thenReturn(1L);
        when(pacmanConfigParamsService.getParameterValue(IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value())).thenReturn("alias");
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value())).thenReturn(true);
        when(tenantCrudService.save(any(GroupEvaluation.class))).thenReturn(new GroupEvaluation());
        return this;
    }


    private GroupEvaluationServiceTest groupEvaluationSavedWithRequiredFields() {
        ForecastGroupSummary groupSummary = saveForecastGroupSummary();
        GroupPricingConfigurationConferenceAndBanquet conferenceAndBanquet = saveConferenceAndBanquet();
        FunctionSpaceResourceType resourceType = saveFunctionSpaceResourceType();
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = saveFunctionSpaceRevenueGroup(resourceType);

        GroupEvaluation dummyGroupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationFor(GroupPricingEvaluationMethod.ROH, TestProperty.H1.getId(), "testHydration",
                groupSummary, conferenceAndBanquet, functionSpaceRevenueGroup);
        tenantCrudService().save(dummyGroupEvaluation);
        return this;
    }

    private FunctionSpaceRevenueGroup saveFunctionSpaceRevenueGroup(FunctionSpaceResourceType resourceType) {
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = GroupEvaluationObjectMother.buildFunctionSpaceRevenueGroup(TestProperty.H1.getId(), resourceType);
        tenantCrudService().save(functionSpaceRevenueGroup);
        return functionSpaceRevenueGroup;
    }

    private FunctionSpaceResourceType saveFunctionSpaceResourceType() {
        FunctionSpaceResourceType resourceType = GroupEvaluationObjectMother.buildFunctionSpaceResourceType(TestProperty.H1.getId());
        tenantCrudService().save(resourceType);
        return resourceType;
    }

    private GroupPricingConfigurationConferenceAndBanquet saveConferenceAndBanquet() {
        GroupPricingConfigurationConferenceAndBanquet conferenceAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(TestProperty.H1.getId());
        tenantCrudService().save(conferenceAndBanquet);
        return conferenceAndBanquet;
    }

    private ForecastGroupSummary saveForecastGroupSummary() {
        ForecastGroupSummary groupSummary = GroupEvaluationObjectMother.buildFOreCastGroupSummary(TestProperty.H1.getId());
        tenantCrudService().save(groupSummary);
        return groupSummary;
    }

    private GroupEvaluationServiceTest whenFetchGroupevaluationMethodCalled() {
        hydratedEvaluation = service.fetchGroupEvaluation("testHydration");
        return this;
    }

    private GroupEvaluationServiceTest thenAssertHydratedGroupEvaluationObject() {
        assertNotNull(hydratedEvaluation.getGroupEvaluationArrivalDates());
        assertNotNull(hydratedEvaluation.getGroupEvaluationConferenceAndBanquets());
        assertNotNull(hydratedEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets());
        assertNotNull(hydratedEvaluation.getGroupEvaluationCosts());
        assertNotNull(new ArrayList<>(hydratedEvaluation.getGroupEvaluationArrivalDates()).get(0).getGroupEvaluationArrivalDateForecastGroups());
        return this;
    }

    private GroupEvaluation getDummyGroupEvaluation(GroupPricingEvaluationMethod groupPricingEvaluationMethod) {
        GroupEvaluation dummyGroupEvaluation = new GroupEvaluation();
        dummyGroupEvaluation.setPropertyId(TestProperty.H1.getId());
        dummyGroupEvaluation.setEvaluationMethod(groupPricingEvaluationMethod);
        return dummyGroupEvaluation;
    }

    @Test
    public void getAccomActivityMaxOccupancyDate() {
        Date currentDate = new Date();
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(currentDate);
        assertEquals(currentDate, service.getAccomActivityMaxOccupancyDate());
    }

    @Test
    public void getExtendedGPEAccomInventoryPopulationNumDays() {
        Date caughtUpDate = new Date();
        Date accomActivityMaxOccDate = DateUtil.addDaysToDate(caughtUpDate, 371);
        service.setTenantCrudService(tenantCrudService);
        when(pacmanConfigParamsService.getIntegerParameterValue(
                FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1094);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);

        Integer accomInventoryPopulationNumDays = service.getExtendedGPEAccomInventoryPopulationNumDays(accomActivityMaxOccDate);

        assertEquals(732, accomInventoryPopulationNumDays);
    }

    @Test
    public void getMktSegAccomActivityMaxOccupancyDate() {
        Date currentDate = new Date();
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(currentDate);

        assertEquals(currentDate, service.getMktSegAccomActivityMaxOccupancyDate());
    }

    @Test
    public void getExtendedGPEMktAccomInventoryPopulationNumDays() {
        Date caughtUpDate = new Date();
        Date mktAccomActivityMaxOccDate = DateUtil.addDaysToDate(caughtUpDate, 371);
        service.setTenantCrudService(tenantCrudService);
        when(pacmanConfigParamsService.getIntegerParameterValue(
                FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1094);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);

        Integer mktAccomInventoryPopulationNumDays = service.getExtendedGPEMktAccomInventoryPopulationNumDays(mktAccomActivityMaxOccDate);

        assertEquals(732, mktAccomInventoryPopulationNumDays);
    }

    @Test
    public void shouldReturnTrueWhenAccomActivityIsNonEmpty() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(new Date());

        assertTrue(service.isAccomActivityOrMktAccomActivityTableNonEmpty());
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        verify(tenantCrudService, times(0)).findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE);
    }

    @Test
    public void shouldReturnTrueWhenMktAccomActivityIsNonEmpty() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(null);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(new Date());

        assertTrue(service.isAccomActivityOrMktAccomActivityTableNonEmpty());
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE);
    }

    @Test
    public void shouldReturnFalseWhenAccomActivityAndMktAccomActivityTablesAreEmpty() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(null);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE)).thenReturn(null);

        assertFalse(service.isAccomActivityOrMktAccomActivityTableNonEmpty());
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        verify(tenantCrudService, times(1)).findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE);
    }

    @Test
    public void shouldInitializeGroupPricingPackageEntities() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setName("Test");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGuestRoomIncluded(true);
        groupPricingConfigurationPackage.setPackageType(functionSpacePackageType);
        tenantCrudService().save(groupPricingConfigurationPackage);
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = buildGroupPricingEvalPackagePricing(groupEvaluation, functionSpacePackageType, groupPricingConfigurationPackage);
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = buildGroupPricingPackageDetail(groupPricingEvalPackagePricing, groupEvaluation);
        buildGroupEvaluationRevenueByRevenueGroup(groupPricingPackageDetail);

        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());

        service.hydrateGroupEvaluations(List.of(savedEvaluation), false);

        assertEquals(1, savedEvaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        GroupEvaluationGroupPricingPackageDetail savedGroupPricingPackageDetail = savedEvaluation.getGroupEvaluationGroupPricingPackageDetails().iterator().next();
        assertNull(savedGroupPricingPackageDetail.getPackageRevenueByArrivalDates());
        assertEquals(1, savedGroupPricingPackageDetail.getRevenueByRevenueGroups().size());
    }

    @Test
    public void shouldGetInitializedGroupEvaluationWithGPPackageDetails() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setName("Test");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGuestRoomIncluded(true);
        groupPricingConfigurationPackage.setPackageType(functionSpacePackageType);
        tenantCrudService().save(groupPricingConfigurationPackage);
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = buildGroupPricingEvalPackagePricing(groupEvaluation, functionSpacePackageType, groupPricingConfigurationPackage);
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = buildGroupPricingPackageDetail(groupPricingEvalPackagePricing, groupEvaluation);
        buildGroupEvaluationRevenueByRevenueGroup(groupPricingPackageDetail);
        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);

        assertEquals(1, foundEvaluations.size());
        foundEvaluations.forEach(evaluation -> {
            assertTrue(Hibernate.isInitialized(evaluation.getGroupPricingEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getFunctionSpaceEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationGroupPricingPackageDetails()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationFunctionSpacePackageDetails()));
        });
        GroupEvaluation evaluation = foundEvaluations.get(0);
        assertEquals(1, evaluation.getGroupPricingEvalPackagePricings().size());
        assertEquals(0, evaluation.getFunctionSpaceEvalPackagePricings().size());
        assertEquals(1, evaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        assertEquals(0, evaluation.getGroupEvaluationFunctionSpacePackageDetails().size());
    }

    @Test
    public void shouldGetInitializedGroupEvaluationWithGPPackageDetailsWhenHydratedPackageDetailsAreRequired() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setName("Test");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGuestRoomIncluded(true);
        groupPricingConfigurationPackage.setPackageType(functionSpacePackageType);
        tenantCrudService().save(groupPricingConfigurationPackage);
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = buildGroupPricingEvalPackagePricing(groupEvaluation, functionSpacePackageType, groupPricingConfigurationPackage);
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = buildGroupPricingPackageDetail(groupPricingEvalPackagePricing, groupEvaluation);
        buildGroupEvaluationRevenueByRevenueGroup(groupPricingPackageDetail);
        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(false);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria, true);

        assertEquals(1, foundEvaluations.size());
        foundEvaluations.forEach(evaluation -> {
            assertTrue(Hibernate.isInitialized(evaluation.getGroupPricingEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getFunctionSpaceEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationGroupPricingPackageDetails()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationFunctionSpacePackageDetails()));
        });
        GroupEvaluation evaluation = foundEvaluations.get(0);
        assertEquals(1, evaluation.getGroupPricingEvalPackagePricings().size());
        assertEquals(0, evaluation.getFunctionSpaceEvalPackagePricings().size());
        assertEquals(1, evaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        assertEquals(0, evaluation.getGroupEvaluationFunctionSpacePackageDetails().size());
    }

    @Test
    public void shouldGetInitializedGroupEvaluationOnlyWhenHydratedPackageDetailsAreNotRequired() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setName("Test");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGuestRoomIncluded(true);
        groupPricingConfigurationPackage.setPackageType(functionSpacePackageType);
        tenantCrudService().save(groupPricingConfigurationPackage);
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = buildGroupPricingEvalPackagePricing(groupEvaluation, functionSpacePackageType, groupPricingConfigurationPackage);
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = buildGroupPricingPackageDetail(groupPricingEvalPackagePricing, groupEvaluation);
        buildGroupEvaluationRevenueByRevenueGroup(groupPricingPackageDetail);
        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(false);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria, false);

        assertEquals(1, foundEvaluations.size());
        GroupEvaluation evaluation = foundEvaluations.get(0);
        assertEquals(1, evaluation.getGroupPricingEvalPackagePricings().size());
        assertEquals(0, evaluation.getFunctionSpaceEvalPackagePricings().size());
        assertEquals(1, evaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        assertEquals(0, evaluation.getGroupEvaluationFunctionSpacePackageDetails().size());
    }

    @Test
    public void shouldGetInitializedGroupEvaluationWithFSPackageDetails() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Test", functionSpacePackageType, 0, true);
        tenantCrudService().save(functionSpacePackage);
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(
                functionSpacePackage, Set.of(), "non.guest.room.type", BigDecimal.TEN);
        groupEvaluation.addFunctionSpaceEvalPackagePricing(functionSpaceEvalPackagePricing);
        buildFunctionSpacePackageDetail(functionSpacePackage, groupEvaluation);
        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);

        assertEquals(1, foundEvaluations.size());
        foundEvaluations.forEach(evaluation -> {
            assertTrue(Hibernate.isInitialized(evaluation.getGroupPricingEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getFunctionSpaceEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationGroupPricingPackageDetails()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationFunctionSpacePackageDetails()));
        });
        GroupEvaluation evaluation = foundEvaluations.get(0);
        assertEquals(0, evaluation.getGroupPricingEvalPackagePricings().size());
        assertEquals(1, evaluation.getFunctionSpaceEvalPackagePricings().size());
        assertEquals(0, evaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        assertEquals(1, evaluation.getGroupEvaluationFunctionSpacePackageDetails().size());
    }

    @Test
    public void shouldGetInitializedGroupEvaluationWithFSPackageDetailsWhenHydratedPackageDetailsAreRequired() {
        FunctionSpacePackageType functionSpacePackageType = new FunctionSpacePackageType();
        functionSpacePackageType.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackageType.setName("Test");
        tenantCrudService().save(functionSpacePackageType);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Test", functionSpacePackageType, 0, true);
        tenantCrudService().save(functionSpacePackage);
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(
                functionSpacePackage, Set.of(), "non.guest.room.type", BigDecimal.TEN);
        groupEvaluation.addFunctionSpaceEvalPackagePricing(functionSpaceEvalPackagePricing);
        buildFunctionSpacePackageDetail(functionSpacePackage, groupEvaluation);
        tenantCrudService().save(groupEvaluation);
        GroupEvaluation savedEvaluation = tenantCrudService().find(GroupEvaluation.class, groupEvaluation.getId());
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(savedEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(false);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria, true);

        assertEquals(1, foundEvaluations.size());
        foundEvaluations.forEach(evaluation -> {
            assertTrue(Hibernate.isInitialized(evaluation.getGroupPricingEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getFunctionSpaceEvalPackagePricings()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationGroupPricingPackageDetails()));
            assertTrue(Hibernate.isInitialized(evaluation.getGroupEvaluationFunctionSpacePackageDetails()));
        });
        GroupEvaluation evaluation = foundEvaluations.get(0);
        assertEquals(0, evaluation.getGroupPricingEvalPackagePricings().size());
        assertEquals(1, evaluation.getFunctionSpaceEvalPackagePricings().size());
        assertEquals(0, evaluation.getGroupEvaluationGroupPricingPackageDetails().size());
        assertEquals(1, evaluation.getGroupEvaluationFunctionSpacePackageDetails().size());
    }


    @Test
    public void shouldDisableExtendedGPEToggleAtPropertyLevel() {
        String context = "pacman.blkstn.h1";
        ConfigParameter extendedGPEConfigParam = new ConfigParameter();
        ConfigParameterValue configParameterValue = getConfigParameterValue(context, extendedGPEConfigParam);
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext())).thenReturn(context);
        when(pacmanConfigParamsService.getParameter(IPConfigParamName.GP_USE_EXTENDED_WND.value())).thenReturn(extendedGPEConfigParam);
        when(pacmanConfigParamsService.getParameterValue(context, extendedGPEConfigParam)).thenReturn(configParameterValue);

        service.disableExtendedGroupEvaluationAtPropertyLevel();

        verify(pacmanConfigParamsService).propertyNode(PacmanWorkContextHelper.getWorkContext());
        verify(pacmanConfigParamsService).getParameter(IPConfigParamName.GP_USE_EXTENDED_WND.value());
        verify(pacmanConfigParamsService).getParameterValue(context, extendedGPEConfigParam);
        ArgumentCaptor<ConfigParameterValue> savedConfigParameterValue = ArgumentCaptor.forClass(ConfigParameterValue.class);
        verify(pacmanConfigParamsService).updateParameterValue(savedConfigParameterValue.capture(), eq(false));
        assertEquals("false", savedConfigParameterValue.getValue().getValue());
    }

    @Test
    public void shouldExitGracefullyWhenExtendedGPEConfigParameterValueAtPropertyLevelIsNotPresent() {
        String context = "pacman.blkstn.h1";
        ConfigParameter extendedGPEConfigParam = new ConfigParameter();
        ConfigParameterValue configParameterValue = getConfigParameterValue(context, extendedGPEConfigParam);
        when(pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext())).thenReturn(context);
        when(pacmanConfigParamsService.getParameter(IPConfigParamName.GP_USE_EXTENDED_WND.value())).thenReturn(extendedGPEConfigParam);

        service.disableExtendedGroupEvaluationAtPropertyLevel();

        verify(pacmanConfigParamsService).propertyNode(PacmanWorkContextHelper.getWorkContext());
        verify(pacmanConfigParamsService).getParameter(IPConfigParamName.GP_USE_EXTENDED_WND.value());
        verify(pacmanConfigParamsService).getParameterValue(context, extendedGPEConfigParam);
        verify(pacmanConfigParamsService, times(0)).updateParameterValue(any(), anyString());
    }

    @Test
    void shouldReturnTrueWhenComponentRoomsIsEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);
        assertTrue(service.isComponentRoomsEnabledEnabled());
        verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED);
    }

    @Test
    void shouldSaveGroupEvaluation() {
        GroupEvaluation groupEvaluationToBeSaved = buildGroupEvaluationForRoomClass();
        service.setTenantCrudService(tenantCrudService);

        service.saveGroupEvaluation(groupEvaluationToBeSaved);

        assertNotNull(groupEvaluationToBeSaved.getEvaluationDate());
        verify(tenantCrudService).save(groupEvaluationToBeSaved);
    }

    @Test
    void shouldReturnDeserializedGroupEvaluation() {
        service.setTenantCrudService(tenantCrudService);
        GroupEvaluation groupEvaluationToBeSaved = buildGroupEvaluation();
        GroupEvaluationRequest groupEvaluationRequest = new GroupEvaluationRequest();
        groupEvaluationRequest.addGroupEvaluationContent(groupEvaluationToBeSaved);
        when(tenantCrudService.findByNativeQuerySingleResult(anyString(), anyMap(), any())).thenReturn(groupEvaluationRequest);

        GroupEvaluation deserializedGroupEvaluation = service.getGroupEvaluationRequest(1);

        assertEquals(groupEvaluationToBeSaved.getGroupName(), deserializedGroupEvaluation.getGroupName());
        verify(tenantCrudService).findByNativeQuerySingleResult(anyString(), anyMap(), any());
    }

    @Test
    void shouldDeleteGroupEvaluationRequest() {
        service.setTenantCrudService(tenantCrudService);
        service.deleteGroupEvaluationRequest(1);
        verify(tenantCrudService).executeUpdateByNativeQuery(anyString(), anyMap());
    }

    private ConfigParameterValue getConfigParameterValue(String context, ConfigParameter extendedGPEConfigParam) {
        ConfigParameterValue configParameterValue = new ConfigParameterValue();
        configParameterValue.setConfigParameter(extendedGPEConfigParam);
        configParameterValue.setValue("true");
        configParameterValue.setContext(context);
        return configParameterValue;
    }

    private GroupPricingEvalPackagePricing buildGroupPricingEvalPackagePricing(GroupEvaluation groupEvaluation,
                                                                               FunctionSpacePackageType functionSpacePackageType,
                                                                               GroupPricingConfigurationPackage groupPricingConfigurationPackage) {
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = new GroupPricingEvalPackagePricing();
        groupPricingEvalPackagePricing.setGroupPricingConfigurationPackage(groupPricingConfigurationPackage);
        groupPricingEvalPackagePricing.setCommissionPercent(BigDecimal.TEN);
        groupPricingEvalPackagePricing.setPackageNameCategory("non.guest.room.type");
        groupPricingEvalPackagePricing.setFunctionSpacePackageType(functionSpacePackageType);
        groupPricingEvalPackagePricing.setStatus(TenantStatusEnum.ACTIVE);
        groupEvaluation.addGroupPricingEvalPackagePricing(groupPricingEvalPackagePricing);
        return groupPricingEvalPackagePricing;
    }

    private GroupEvaluationGroupPricingPackageDetail buildGroupPricingPackageDetail(GroupPricingEvalPackagePricing groupPricingEvalPackagePricing,
                                                                                    GroupEvaluation groupEvaluation) {
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = new GroupEvaluationGroupPricingPackageDetail();
        groupPricingPackageDetail.setPackageName("Test");
        groupPricingPackageDetail.setPackageCategory("non.guest.room.type");
        groupPricingPackageDetail.setGuestRoomIncluded(true);
        groupEvaluation.addGroupEvaluationGroupPricingPackageDetail(groupPricingPackageDetail);
        groupPricingPackageDetail.setGroupPricingEvalPackagePricing(groupPricingEvalPackagePricing);
        return groupPricingPackageDetail;
    }

    private GroupEvaluationFunctionSpacePackageDetail buildFunctionSpacePackageDetail(FunctionSpacePackage functionSpacePackage,
                                                                                      GroupEvaluation groupEvaluation) {
        GroupEvaluationFunctionSpacePackageDetail functionSpacePackageDetail = new GroupEvaluationFunctionSpacePackageDetail();
        functionSpacePackageDetail.setPackageName("Test");
        functionSpacePackageDetail.setPackageCategory("non.guest.room.type");
        functionSpacePackageDetail.setGuestRoomIncluded(true);
        functionSpacePackageDetail.setFunctionSpacePackage(functionSpacePackage);
        groupEvaluation.addGroupEvaluationFunctionSpacePackageDetail(functionSpacePackageDetail);
        return functionSpacePackageDetail;
    }

    private void buildGroupEvaluationRevenueByRevenueGroup(GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail) {
        GroupEvaluationGroupPricingPackageRevenueByRevenueGroup revenueByRevenueGroup = new GroupEvaluationGroupPricingPackageRevenueByRevenueGroup();
        revenueByRevenueGroup.setName("Test");
        revenueByRevenueGroup.setRevenue(BigDecimal.TEN);
        revenueByRevenueGroup.setCommission(BigDecimal.TEN);
        revenueByRevenueGroup.setProfit(BigDecimal.TEN);
        revenueByRevenueGroup.setRevenueWithoutTax(BigDecimal.TEN);
        groupPricingPackageDetail.addGroupEvaluationGroupPricingPackageRevenueByRevenueGroup(revenueByRevenueGroup);
    }

    @Test
    public void verifyRCCapacityIsCalculatedUsingMaxOccupancyDateWhenArrivalDateFallsInExtendedWindow() {
        Date optimizationWindowDate = (Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        Date startDate = DateUtil.addDaysToDate(optimizationWindowDate, 1);
        List<BigDecimal> capacityForStartDate = tenantCrudService().findByNamedQuery(AccomType.CAPACITY_BY_ROOM_CLASS_BY_DATE,
                QueryParameter.with("accomClassId", 4)
                        .and("startDate", optimizationWindowDate).parameters());
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)).thenReturn(true);
        Assertions.assertEquals(capacityForStartDate.get(0), service.getTotalRoomClassCapacityForOccupancyDate(4, startDate));
    }

    @Test
    public void verifyRCCapacityIsCalculatedUsingMaxOccupancyDateWhenArrivalDateIsSameAsOptimizationWindowDate() {
        Date optimizationWindowDate = (Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        Date startDate = optimizationWindowDate;

        List<BigDecimal> capacityForStartDate = tenantCrudService().findByNamedQuery(AccomType.CAPACITY_BY_ROOM_CLASS_BY_DATE,
                QueryParameter.with("accomClassId", 4)
                        .and("startDate", startDate).parameters());
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)).thenReturn(true);
        Assertions.assertEquals(capacityForStartDate.get(0), service.getTotalRoomClassCapacityForOccupancyDate(4, startDate));
    }

    @Test
    public void verifyRCCapacityWhenArrivalDateFallsInOptimizationWindow() {
        Date optimizationWindowDate = (Date) tenantCrudService().findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
        Date startDate = DateUtil.addDaysToDate(optimizationWindowDate, -10);

        List<BigDecimal> capacityForStartDate = tenantCrudService().findByNamedQuery(AccomType.CAPACITY_BY_ROOM_CLASS_BY_DATE,
                QueryParameter.with("accomClassId", 4)
                        .and("startDate", startDate).parameters());
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)).thenReturn(true);
        Assertions.assertEquals(capacityForStartDate.get(0), service.getTotalRoomClassCapacityForOccupancyDate(4, startDate));
    }

    @Test
    void shouldGetTotalNumberOfEvaluations() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuerySingleResult(eq("SELECT distinct  count(ge.Grp_Evl_ID)  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID "), eq(Collections.EMPTY_MAP))).thenReturn(Integer.valueOf("821"));
        Integer result = service.getTotalNumberOfEvaluations(new EvaluationFilter());
        verify(tenantCrudService).findByNativeQuerySingleResult(eq("SELECT distinct  count(ge.Grp_Evl_ID)  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID "), eq(Collections.EMPTY_MAP));
        assertEquals(821, result);
    }

    @Test
    void shouldGetPaginatedGroupEvaluationsForFirstPage() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuery(eq("SELECT distinct  ge.Grp_Evl_ID , ge.Group_Name, ge.Evaluation_Date  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID  order by  ge.Group_Name, ge.Evaluation_Date "), eq(Collections.EMPTY_MAP), eq(0), eq(10), any())).thenReturn(Arrays.asList(1,2,3));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        service.getPaginatedGroupEvaluations(evaluationFilter);
        verify(tenantCrudService).findByNativeQuery(eq("SELECT distinct  ge.Grp_Evl_ID , ge.Group_Name, ge.Evaluation_Date  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID  order by  ge.Group_Name, ge.Evaluation_Date "), eq(Collections.EMPTY_MAP), eq(0), eq(10), any());
        verify(service).hydrateGroupEvaluations(anyList(), eq(true));
    }

    @Test
    void shouldGetPaginatedGroupEvaluationsWithSkippedPages() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuery(eq("SELECT distinct  ge.Grp_Evl_ID , ge.Group_Name, ge.Evaluation_Date  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID  order by  ge.Group_Name, ge.Evaluation_Date "), eq(Collections.EMPTY_MAP), eq(40), eq(20), any())).thenReturn(Arrays.asList(1,2,3));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(3);
        evaluationFilter.setPageSize(20);
        service.getPaginatedGroupEvaluations(evaluationFilter);
        verify(tenantCrudService).findByNativeQuery(eq("SELECT distinct  ge.Grp_Evl_ID , ge.Group_Name, ge.Evaluation_Date  FROM Grp_Evl ge  inner join Users u on ge.Created_By_User_ID = u.User_ID  order by  ge.Group_Name, ge.Evaluation_Date "), eq(Collections.EMPTY_MAP), eq(40), eq(20), any());
        verify(service).hydrateGroupEvaluations(anyList(), eq(true));
    }

    @Test
    void shouldGetEmptyListWhenGroupEvaluationsAreNotFoundForPagination(){
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuery(eq("SELECT  ge.Grp_Evl_ID  FROM Grp_Evl ge  order by ge.Group_Name, ge.Evaluation_Date "), eq(Collections.EMPTY_MAP), eq(1), eq(20), any())).thenReturn(new ArrayList<>());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(20);
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        verify(tenantCrudService, never()).findByNamedQuery(FIND_GE_BY_IDS, QueryParameter.with("groupEvaluationIds", Arrays.asList(0)).parameters());
    }

    @Test
    void shouldLoadEvaluationCostsDataForPaginatedEvaluations() {
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(new EvaluationFilter(2, 1, null, null, null, null, null, null, null, null, null, null, false));
        assertEquals(1, result.size());
        assertTrue(Hibernate.isInitialized(result.get(0).getGroupEvaluationCosts()));
    }

    @Test
    void shouldGetGroupEvaluationsByGroupName() {
        service.setTenantCrudService(tenantCrudService());
        createEvaluationGroup("Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Ideas Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setGroupName("Apple");
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("Ideas Apple Meetup", result.get(1).getGroupName());
    }

    @Test
    void shouldGetGroupEvaluationsOfGuestRoomOnlyForFSRMProperty() {
        service.setTenantCrudService(tenantCrudService());
        createEvaluationGroup("Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE.getId());
        createEvaluationGroup("Ideas Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setGroupName("Meetup");
        evaluationFilter.setUseGroupPricingEvaluationForFSRMGuestRoomOnlyRequest(true);
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("Ideas Apple Meetup", result.get(1).getGroupName());
    }

    @Test
    void shouldGetGroupEvaluationsByMarketSegment() {
        service.setTenantCrudService(tenantCrudService());
        createEvaluationGroup("Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setMarketSegment("CORP");
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(1, result.size());
        assertEquals("CORP", result.get(0).getMarketSegment().getName());
    }

    @Test
    void shouldGetGroupEvaluationsBySalesPersons() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        createEvaluationGroup("Apple Meetup", "COMP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IM Meetup", "CORP", 3, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSalesPersons(Arrays.asList("ROA Member", "SSO User"));
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(3, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals(11403, result.get(0).getCreatedByUserId());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
        assertEquals(4, result.get(1).getCreatedByUserId());
        assertEquals("GopherTailgate.com", result.get(2).getGroupName());
        assertEquals("Disc", result.get(2).getMarketSegment().getCode());
        assertEquals(11403, result.get(2).getCreatedByUserId());
    }

    @Test
    void shouldGetGroupEvaluationsByGroupNameAndMarketSegment() {
        service.setTenantCrudService(tenantCrudService());
        createEvaluationGroup("Apple Meetup", "COMP", 3, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Apple Meetup", "CORP", 11403, java.time.LocalDateTime.now(), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setGroupName("Apple Meetup");
        evaluationFilter.setMarketSegment("CORP");
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(1, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsBetweenEvaluationStartDateAndEndDate() {
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setEvaluationStartDate(toDate(today.minusDays(1)));
        evaluationFilter.setEvaluationEndDate(toDate(today.plusDays(3)));
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsWhenEvaluationStartDateAndEndDateEqualsToTheFilterDates() {
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setEvaluationStartDate(toDate(today));
        evaluationFilter.setEvaluationEndDate(toDate(today.plusDays(2)));
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsWhenEvaluationStartDatePresentButEndDateIsNot() {
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setEvaluationStartDate(toDate(today.plusDays(2)));
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(2, result.size());
        assertEquals("Google Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
        assertEquals("IDeaS Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsWhenEvaluationEndDatePresentButStartDateIsNot() {
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setEvaluationEndDate(toDate(today.plusDays(2)));
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        assertEquals(4, result.size());//Seed evaluations available in the DB
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsBetweenStayDateStartAndStayDateEnd() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        //StayDates
        createDayOfStayFor(appleGroupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(appleGroupEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 2, 5, 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setStayDateStart(toDate(today.plusDays(1)));
        evaluationFilter.setStayDateEnd(toDate(today.plusDays(3)));
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsWhenStayDateStartIsPresentButAndStayDateEndIsNot() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        //StayDates
        createDayOfStayFor(appleGroupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(appleGroupEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 2, 5, 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setStayDateStart(toDate(today.plusDays(2)));
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(2, result.size());
        assertEquals("Google Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
        assertEquals("IDeaS Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldGetGroupEvaluationsWhenStayDateEndIsPresentButAndStayDateStartIsNot() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        //StayDates
        createDayOfStayFor(appleGroupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(appleGroupEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(googleMeetUpEvlGrpId, 2, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 1, 5, 11403);
        createDayOfStayFor(ideasMeetupEvlGrpId, 2, 5, 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setStayDateEnd(toDate(today.plusDays(3)));
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(4, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSearchByGroupNameContainsInGroupEvaluationWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Company Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSearchByName("Company");
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(1, result.size());
        assertEquals("IDeaS Company Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSearchBySalesPersonNameContainsInGroupEvaluationWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS Company Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSearchByName("ROA");
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(1, result.size());
        assertEquals("Google Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSearchByNameInGroupNameOrSalesPersonContainsInGroupEvaluationWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSearchByName("ROA");
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(2, result.size());
        assertEquals("Google Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
        assertEquals("IDeaS ROA Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSearchByNameInGroupNameOrSalesPersonContainsInGroupEvaluationWithinDateRangeWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSearchByName("e");
        evaluationFilter.setEvaluationStartDate(toDate(today));
        evaluationFilter.setEvaluationEndDate(toDate(today.plusDays(2)));
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(2, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSortByGroupNameWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.GROUP_NAME);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getCode());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
        assertEquals("GopherTailgate.com", result.get(2).getGroupName());
        assertEquals("Disc", result.get(2).getMarketSegment().getCode());
        assertEquals("IDeaS ROA Meetup", result.get(3).getGroupName());
        assertEquals("CORP", result.get(3).getMarketSegment().getCode());
        assertEquals("T3 Scrapbooking Party", result.get(4).getGroupName());
        assertEquals("Disc", result.get(4).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSortByArrivalDateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(5), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(5);
        evaluationFilter.setSortBy(SortBy.ARRIVAL_DATE);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());

        assertEquals("IDeaS ROA Meetup", result.get(0).getGroupName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(3).getGroupName());
        assertEquals("GopherTailgate.com", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByEvaluatedOnWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.EVALUATED_ON);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals("IDeaS ROA Meetup", result.get(0).getGroupName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
    }

    @Test
    void shouldBeAbleToSortBySalesPersonWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 3);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 3);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 4);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 4);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.SALES_PERSON);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(1).getGroupName());
        assertEquals("Google Meetup", result.get(2).getGroupName());
        assertEquals("Apple Meetup", result.get(3).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByRecommendedRateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("100.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("320.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("200.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("100.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("300.00"), new BigDecimal("150.00"));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.RECOMMENDED_RATE);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals("IDeaS ROA Meetup", result.get(0).getGroupName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("GopherTailgate.com", result.get(2).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByWantRateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("100.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("320.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("200.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("100.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("300.00"), new BigDecimal("150.00"));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.WANT_RATE);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals("IDeaS ROA Meetup", result.get(0).getGroupName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("GopherTailgate.com", result.get(2).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByBreakEvenRateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("100.00"), new BigDecimal("150.00"));
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("120.00"), new BigDecimal("250.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("200.00"), new BigDecimal("170.00"));
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("100.00"), new BigDecimal("290.00"));
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("300.00"), new BigDecimal("300.00"));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.BREAK_EVEN_RATE);
        evaluationFilter.setSortOrder(SortOrder.DESCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals("IDeaS ROA Meetup", result.get(0).getGroupName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
        assertEquals("GopherTailgate.com", result.get(3).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByGroupNameAlongWithSearchByNameWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.GROUP_NAME);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        evaluationFilter.setSearchByName("ROA");
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(2, result.size());
        assertEquals("Google ROA Meetup", result.get(0).getGroupName());
        assertEquals("CORP", result.get(0).getMarketSegment().getCode());
        assertEquals("IDeaS ROA Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getCode());
    }

    @Test
    void shouldBeAbleToSortByNetProfitWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithProfit(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("100.00"), new BigDecimal("120.00"));
        createEvaluationArrivalDateWithProfit(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("90.00"), new BigDecimal("110.00"));
        createEvaluationArrivalDateWithProfit(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("200.00"), new BigDecimal("220.00"));
        createEvaluationArrivalDateWithProfit(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("250.00"), new BigDecimal("230.00"));
        createEvaluationArrivalDateWithProfit(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("80.00"), null);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.NET_PROFIT);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(2).getGroupName());
        assertEquals("Apple Meetup", result.get(3).getGroupName());
        assertEquals("Google ROA Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByProfitPercentageWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithProfitPercentage(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("90.00"), new BigDecimal("95.00"));
        createEvaluationArrivalDateWithProfitPercentage(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("70.00"), new BigDecimal("83.00"));
        createEvaluationArrivalDateWithProfitPercentage(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("79.00"), new BigDecimal("85.00"));
        createEvaluationArrivalDateWithProfitPercentage(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("94.00"), new BigDecimal("97.00"));
        createEvaluationArrivalDateWithProfitPercentage(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("80.00"), null);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.PROFIT_PERCENTAGE);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(2).getGroupName());
        assertEquals("Google ROA Meetup", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByNightsWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithNights(appleGroupEvlGrpId, 1, today, 11403, 2);
        createEvaluationArrivalDateWithNights(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, 3);
        createEvaluationArrivalDateWithNights(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, 4);
        createEvaluationArrivalDateWithNights(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, 7);
        createEvaluationArrivalDateWithNights(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, 6);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.NIGHTS);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
        assertEquals("Google ROA Meetup", result.get(3).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByTotalRoomsWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithTotalRooms(appleGroupEvlGrpId, 1, today, 11403, 2);
        createEvaluationArrivalDateWithTotalRooms(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, 3);
        createEvaluationArrivalDateWithTotalRooms(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, 4);
        createEvaluationArrivalDateWithTotalRooms(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, 7);
        createEvaluationArrivalDateWithTotalRooms(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, 6);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.TOTAL_ROOMS);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
        assertEquals("Google ROA Meetup", result.get(3).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByContractualRevenueWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithContractualRevenue(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("90.00"), new BigDecimal("95.00"));
        createEvaluationArrivalDateWithContractualRevenue(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("70.00"), new BigDecimal("83.00"));
        createEvaluationArrivalDateWithContractualRevenue(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("79.00"), new BigDecimal("85.00"));
        createEvaluationArrivalDateWithContractualRevenue(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("94.00"), new BigDecimal("97.00"));
        createEvaluationArrivalDateWithContractualRevenue(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("80.00"), null);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.CONTRACTUAL_REVENUE);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(2).getGroupName());
        assertEquals("Google ROA Meetup", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }
    @Test
    void shouldBeAbleToSortByWishRateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithWishRate(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("90.00"));
        createEvaluationArrivalDateWithWishRate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("70.00"));
        createEvaluationArrivalDateWithWishRate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("79.00"));
        createEvaluationArrivalDateWithWishRate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("94.00"));
        createEvaluationArrivalDateWithWishRate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("80.00"));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.WISH_RATE);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("Google ROA Meetup", result.get(2).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByApprovalStatusWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        //Group Evaluation Approvals
        createEvaluationApproval(appleGroupEvlGrpId, "41b49974-95e9-40ca-8f65-e02e3f7285ac", "ACCEPTED", 11403, "ACCEPTED");
        createEvaluationApproval(appleGroupEvlGrpId, "41b49974-95e9-40ca-8f65-e02e3f7285ac", "ACCEPTED", 11403, "ACCEPTED");
        createEvaluationApproval(googleMeetUpEvlGrpId, "7f5263e9-e3ba-42d0-abd1-ae4c13bf98a9", "PARTIALLY_ACCEPTED", 11403, "ACCEPTED");
        createEvaluationApproval(googleMeetUpEvlGrpId, "7f5263e9-e3ba-42d0-abd1-ae4c13bf98a9", "PARTIALLY_ACCEPTED", 11403, "PENDING");
        createEvaluationApproval(ideasMeetupEvlGrpId, "adfdf67d-a97c-4aaa-87b1-830aa35240a1", "REJECTED", 11403, "PENDING");
        createEvaluationApproval(ideasMeetupEvlGrpId, "adfdf67d-a97c-4aaa-87b1-830aa35240a1", "REJECTED", 11403, "REJECTED");
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.APPROVAL_STATUS);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("Apple Meetup", result.get(2).getGroupName());
        assertEquals("Google Meetup", result.get(3).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByWalkRateWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google ROA Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDateWithWalkRate(appleGroupEvlGrpId, 1, today, 11403, new BigDecimal("90.00"));
        createEvaluationArrivalDateWithWalkRate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403, new BigDecimal("70.00"));
        createEvaluationArrivalDateWithWalkRate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403, new BigDecimal("79.00"));
        createEvaluationArrivalDateWithWalkRate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403, new BigDecimal("94.00"));
        createEvaluationArrivalDateWithWalkRate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403, new BigDecimal("80.00"));
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.WALK_RATE);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("GopherTailgate.com", result.get(0).getGroupName());
        assertEquals("T3 Scrapbooking Party", result.get(1).getGroupName());
        assertEquals("Google ROA Meetup", result.get(2).getGroupName());
        assertEquals("IDeaS ROA Meetup", result.get(3).getGroupName());
        assertEquals("Apple Meetup", result.get(4).getGroupName());
    }

    @Test
    void shouldBeAbleToSortByMarketSegmentWhileGettingPreviousEvaluations() {
        //GIVEN
        service.setTenantCrudService(tenantCrudService());
        java.time.LocalDateTime today = java.time.LocalDateTime.now();
        Integer appleGroupEvlGrpId = createEvaluationGroup("Apple Meetup", "COMP", 3, today, GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer googleMeetUpEvlGrpId = createEvaluationGroup("Google Meetup", "CORP", 4, today.plusDays(2), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        Integer ideasMeetupEvlGrpId = createEvaluationGroup("IDeaS ROA Meetup", "CORP", 11403, today.plusDays(4), GroupEvaluationType.GUEST_ROOM_ONLY.getId());
        //ArrivalDates
        createEvaluationArrivalDate(appleGroupEvlGrpId, 1, today, 11403);
        createEvaluationArrivalDate(appleGroupEvlGrpId, 0, today.plusDays(1), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 1, today.plusDays(2), 11403);
        createEvaluationArrivalDate(googleMeetUpEvlGrpId, 0, today.plusDays(3), 11403);
        createEvaluationArrivalDate(ideasMeetupEvlGrpId, 1, today.plusDays(4), 11403);
        EvaluationFilter evaluationFilter = new EvaluationFilter();
        evaluationFilter.setIndex(1);
        evaluationFilter.setPageSize(10);
        evaluationFilter.setSortBy(SortBy.MARKET_SEGMENT);
        evaluationFilter.setSortOrder(SortOrder.ASCENDING);
        //WHEN
        List<GroupEvaluation> result = service.getPaginatedGroupEvaluations(evaluationFilter);
        //THEN
        assertEquals(5, result.size());
        assertEquals("Apple Meetup", result.get(0).getGroupName());
        assertEquals("COMP", result.get(0).getMarketSegment().getName());
        assertEquals("Google Meetup", result.get(1).getGroupName());
        assertEquals("CORP", result.get(1).getMarketSegment().getName());
        assertEquals("IDeaS ROA Meetup", result.get(2).getGroupName());
        assertEquals("CORP", result.get(2).getMarketSegment().getName());
        assertEquals("GopherTailgate.com", result.get(3).getGroupName());
        assertEquals("Discount", result.get(3).getMarketSegment().getName());
        assertEquals("T3 Scrapbooking Party", result.get(4).getGroupName());
        assertEquals("Discount", result.get(4).getMarketSegment().getName());
    }

    @Test
    void shouldGetFinalPriceForRoomTypesForDateRange(){
        //GIVEN
        java.time.LocalDate today = java.time.LocalDate.now();
        createCpDecisionBAROutput(4, new BigDecimal("150.55"), today);
        createCpDecisionBAROutput(5, new BigDecimal("160.55"), today);
        createCpDecisionBAROutput(4, new BigDecimal("170.55"), today.plusDays(1));
        createCpDecisionBAROutput(5, new BigDecimal("180.55"), today.plusDays(1));
        //WHEN
        List<BarRate> barRates = service.getFinalPriceForRoomTypesForDateRange(toDate(today), toDate(today.plusDays(1)), Arrays.asList(4, 5));
        //THEN
        assertBarRateFor(new BigDecimal("150.55000"), 4, toDate(today), barRates.get(0));
        assertBarRateFor(new BigDecimal("160.55000"), 5, toDate(today), barRates.get(1));
        assertBarRateFor(new BigDecimal("170.55000"), 4, toDate(today.plusDays(1)), barRates.get(2));
        assertBarRateFor(new BigDecimal("180.55000"), 5, toDate(today.plusDays(1)), barRates.get(3));
    }

    @Test
    void shouldGetFinalPriceForROHForDateRange(){
        //GIVEN
        java.time.LocalDate today = java.time.LocalDate.now();
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID = 3 where Accom_Type_ID = 5");
        createCpDecisionBAROutput(4, new BigDecimal("150.55"), today);
        createCpDecisionBAROutput(5, new BigDecimal("160.55"), today);
        createCpDecisionBAROutput(4, new BigDecimal("190.55"), today.plusDays(1));
        createCpDecisionBAROutput(5, new BigDecimal("180.55"), today.plusDays(1));
        //WHEN
        Map<Date, BigDecimal> finalPriceForDateRange = service.getFinalPriceForROHForDateRange(toDate(today), toDate(today.plusDays(1)));
        //THEN
        assertEquals(new BigDecimal("150.55000"), finalPriceForDateRange.get(toDate(today)));
        assertEquals(new BigDecimal("180.55000"), finalPriceForDateRange.get(toDate(today.plusDays(1))));
    }

    @Test
    void shouldSaveEvaluationRequest() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        Integer evaluationRequestId = service.saveEvaluationRequest(groupEvaluation);
        assertNotNull(evaluationRequestId);
    }

    @Test
    void shouldUpdateExistingGroupEvaluation() {
        //When
        service.setTenantCrudService(tenantCrudService());
        service.groupEvaluationArrivalDateMapperService = groupEvaluationArrivalDateMapperService;
        List<GroupEvaluationArrivalDate> beforeGroupEvaluationArrivalDates = tenantCrudService().findAll(GroupEvaluationArrivalDate.class);
        service.updateExistingGPAndFSEvaluationFields(service.getAllGroupEvaluationArrivalDatesToBeUpdated(5));

        //Then
        List<GroupEvaluationArrivalDate> afterGroupEvaluationArrivalDates = tenantCrudService().findAll(GroupEvaluationArrivalDate.class);
        assertFalse(afterGroupEvaluationArrivalDates.isEmpty());
        assertEquals(afterGroupEvaluationArrivalDates.size(), beforeGroupEvaluationArrivalDates.size());

        GroupEvaluationArrivalDate arrivalDate = afterGroupEvaluationArrivalDates.stream().findFirst().get();
        assertEquals(arrivalDate.getTotalRooms(), arrivalDate.getGroupEvaluation().getTotalNumberOfRooms());
        assertEquals(arrivalDate.getNumberOfNights(), arrivalDate.getGroupEvaluation().getNumberOfNights());
        assertEquals(arrivalDate.getContractedRevenue(), arrivalDate.getContractualRevenueRequired());
        assertEquals(arrivalDate.getSystemTotalNetProfit(), arrivalDate.getTotalNetProfit(true));
        assertEquals(arrivalDate.getTotalProfitPercentage(), arrivalDate.getTotalNetProfitPercentage(true));
    }

    @Test
    void shouldUpdateExistingGroupEvaluationWithUserAdjusted() {
        //GIVEN
        List<GroupEvaluationArrivalDate> beforeGroupEvaluationArrivalDates = tenantCrudService().findAll(GroupEvaluationArrivalDate.class);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = beforeGroupEvaluationArrivalDates.get(0);
        groupEvaluationArrivalDate.setUserAdjustedOutput(true);
        tenantCrudService().save(groupEvaluationArrivalDate);

        //When
        service.setTenantCrudService(tenantCrudService());
        service.groupEvaluationArrivalDateMapperService = groupEvaluationArrivalDateMapperService;
        service.updateExistingGPAndFSEvaluationFields(service.getAllGroupEvaluationArrivalDatesToBeUpdated(5));

        //Then
        List<GroupEvaluationArrivalDate> afterGroupEvaluationArrivalDates = tenantCrudService().findAll(GroupEvaluationArrivalDate.class);
        ;
        GroupEvaluationArrivalDate arrivalDate = afterGroupEvaluationArrivalDates.stream()
                .filter(arrivalDate1 -> Boolean.TRUE.equals(arrivalDate1.getUserAdjustedOutput())).findFirst().get();
        assertFalse(afterGroupEvaluationArrivalDates.isEmpty());
        assertEquals(beforeGroupEvaluationArrivalDates.size(), afterGroupEvaluationArrivalDates.size());
        assertEquals(arrivalDate.getTotalRooms(), arrivalDate.getGroupEvaluation().getTotalNumberOfRooms());
        assertEquals(arrivalDate.getNumberOfNights(), arrivalDate.getGroupEvaluation().getNumberOfNights());
        assertEquals(arrivalDate.getContractedRevenue(), arrivalDate.getContractualRevenueRequired());
        assertEquals(arrivalDate.getSystemTotalNetProfit(), arrivalDate.getTotalNetProfit(true));
        assertEquals(arrivalDate.getTotalProfitPercentage(), arrivalDate.getTotalNetProfitPercentage(true));
        assertNotNull(arrivalDate.getUserAdjustedContractedRevenue());
        assertNotNull(arrivalDate.getUserAdjustedTotalNetProfit());
        assertNotNull(arrivalDate.getUserAdjustedTotalProfitPercentage());
    }

    @Test
    void shouldTriggerFSAndGPEvaluationMigrationJob(){
        service.triggerFSAndGPEvaluationMigrationJob(5);
        verify(jobService).startGuaranteedNewInstance(JobName.MigrationGPAndFunctionSpaceEvaluationsJob,
                QueryParameter.with(JobParameterKey.PROPERTY_ID, 5).parameters());
    }

    @Test
    void salesAndCateringUrlRedirectEnabledShouldReturnTrue() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(true);

        assertTrue(service.salesAndCateringUrlRedirectEnabled());
    }

    @Test
    void salesAndCateringUrlRedirectEnabledShouldReturnFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(false);

        assertFalse(service.salesAndCateringUrlRedirectEnabled());
    }

    @Test
    void disAllowNegativeValueForUserAdjustedFSEvalShouldReturnTrue() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DISALLOW_NEGATIVE_VALUES_USER_ADJUSTED_EVAL)).thenReturn(true);
        assertTrue(service.disAllowNegativeValueForUserAdjustedFSEval());
    }

    @Test
    void disAllowNegativeValueForUserAdjustedFSEvalShouldReturnFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DISALLOW_NEGATIVE_VALUES_USER_ADJUSTED_EVAL)).thenReturn(false);
        assertFalse(service.disAllowNegativeValueForUserAdjustedFSEval());
    }

    @Test
    void shouldReturnLatestRevisionWhenMultipleGrpEvlRecordsExistForEvaluatedOnDate() {
        java.time.LocalDateTime evaluatedOn = java.time.LocalDateTime.of(2025, 1,1, 1, 1, 10);
        addGrpEvlAuditRecord(1, evaluatedOn.toString());
        addGrpEvlAuditRecord(2, evaluatedOn.toString());

        assertEquals(2, service.getRevisionIdFromEvaluationIdAndDate(1, evaluatedOn));
    }

    @Test
    public void searchForEvaluationWithFunctionSpaceComboRoomAndHydrateResultsWhenPerformanceImprovementFunctionSpaceLandingPage() throws Exception {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.PERFORMANCE_IMPROVEMENT_FUNCTION_SPACE_LANDING_PAGE)).thenReturn(true);
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithFunctionSpace(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2015,1,1,8,0)),
                JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2015,1,1,10,0)));
        FunctionSpaceCombinationFunctionRoom comboRoom = saveCombinationFunctionRoom();

        GroupEvaluationFunctionSpaceFunctionRoom groupEvalRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        groupEvalRoom.setFunctionSpaceFunctionRoom(comboRoom);
        groupEvalRoom
                .setGroupEvaluationFunctionSpace(groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next());

        groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next()
                .addGroupEvaluationFunctionSpaceFunctionRoom(groupEvalRoom);

        FunctionSpaceResourceType resourceType = FunctionSpaceObjectMother.buildFunctionSpaceResourceType("Bevarage",
                "Bevarage", false, false);
        resourceType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService().save(resourceType);

        FunctionSpaceRevenueGroup revenueGroup = groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets()
                .iterator().next().getFunctionSpaceRevenueGroup();

        revenueGroup.setResourceType(resourceType);
        tenantCrudService().save(groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().iterator().next()
                .getFunctionSpaceRevenueGroup());

        GroupEvaluation groupEvaluation = saveGroupEvaluationForSearching();

        // Search
        GroupEvaluationSearchCriteria groupEvaluationSearchCriteria = new GroupEvaluationSearchCriteria();
        groupEvaluationSearchCriteria.setStartStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setEndStayDate(groupEvaluation.getPreferredDate());
        groupEvaluationSearchCriteria.setHydrateDeepResults(true);
        groupEvaluationSearchCriteria.setHydrateForecastGroups(true);

        List<GroupEvaluation> foundEvaluations = service.search(groupEvaluationSearchCriteria);
        assertEquals(1, foundEvaluations.size());
        assertEquals(1, foundEvaluations.get(0).getGroupEvaluationFunctionSpaces().size());

        GroupEvaluationFunctionSpace retFunctionSpace = foundEvaluations.get(0).getGroupEvaluationFunctionSpaces()
                .iterator().next();
        assertEquals(1, retFunctionSpace.getGroupEvaluationFunctionSpaceFunctionRooms().size());

        GroupEvaluationFunctionSpaceFunctionRoom functionRoom = retFunctionSpace
                .getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();

        assertTrue(functionRoom.getFunctionSpaceFunctionRoom() instanceof FunctionSpaceCombinationFunctionRoom);

        FunctionSpaceCombinationFunctionRoom retComboRoom = (FunctionSpaceCombinationFunctionRoom) functionRoom
                .getFunctionSpaceFunctionRoom();
        assertEquals(2, retComboRoom.getIndivisibleFunctionRoomParts().size());
    }

    private void addGrpEvlAuditRecord(int revisionId, String evaluatedOn) {
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Grp_Evl_AUD " +
                "(Grp_Evl_ID, REV, REVTYPE, Property_ID, Group_Name, Mkt_Seg_ID, Evaluation_Method, Materialization_Status, Evaluation_Date, Per_Room_Servicing_Cost, Created_by_User_ID, Created_DTTM, Last_Updated_by_User_ID, Last_Updated_DTTM, Evaluation_Type, Tax_Rate, Evaluator, Evaluation_Category) VALUES " +
                "(1, " + revisionId + ", 1, 5, 'Test', 4, 0, 0, '" + evaluatedOn + "', 2.42, 1, getdate(), 1, getdate(), 1, 9.00, 1, 1)");
    }

    private static void assertBarRateFor(BigDecimal expectedFinalPrice, int expectedAccomTypeId, Date expectedArrivalDate, BarRate barRate) {
        assertEquals(expectedFinalPrice, barRate.getFinalPrice());
        assertEquals(expectedAccomTypeId, barRate.getAccomTypeId());
        assertEquals(expectedArrivalDate, barRate.getArrivalDate());
    }

    public void createCpDecisionBAROutput(int accomTypeId, BigDecimal barRateValue, java.time.LocalDate date) {
        String query = "update CP_Decision_Bar_Output set Final_BAR = '"+barRateValue+"' where Accom_Type_ID = "+accomTypeId+" and Arrival_DT = '"+date.toString()+"' and Product_ID = 1 ";
        tenantCrudService().executeUpdateByNativeQuery(query);
    }

    private void createEvaluationArrivalDate(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Grp_Evl_Arr_DT (Grp_Evl_ID, Preferred_Date, Arrival_Date, Created_by_User_ID, Created_DTTM, Last_Updated_by_User_ID, Last_Updated_DTTM, Non_Pkg_Rental, User_Adjusted_Non_Pkg_Rental, " +
                "Number_Of_Nights, Total_Rooms, Contracted_Revenue, User_Adjusted_Contracted_Revenue, Total_Profit_Percentage, " +
                "User_Adjusted_Total_Profit_Percentage, Total_Net_Profit,User_Adjusted_Total_Net_Profit) " +
                "values(" + grpEvlId + ", " + isPreferred + ", '" + arrivalDate.toLocalDate() + "', " + userId + ", getDate(), 11403, getDate(), '0.000', '0.000', " +
                "1, 10, '100.00', '200.00', '90.00', '95.00', '300.00', '350.00')");
    }


    private void createEvaluationArrivalDateWithTotalRooms(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                       Integer totalRooms) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                new BigDecimal("90.00"), new BigDecimal("95.00"), 2, totalRooms, new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("57.00"), new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithNights(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                                 Integer numberOfNights) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                new BigDecimal("90.00"), new BigDecimal("95.00"), numberOfNights, 10, new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("57.00"), new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithContractualRevenue(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                                 BigDecimal contractualRevenue, BigDecimal userAdjustedContractualRevenue) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                new BigDecimal("90.00"), new BigDecimal("95.00"), 1, 10, contractualRevenue, userAdjustedContractualRevenue, new BigDecimal("57.00"), new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithProfitPercentage(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                       BigDecimal profitPercentage, BigDecimal userAdjustedProfitPercentage) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                profitPercentage, userAdjustedProfitPercentage, 1, 10, new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("57.00"), new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithProfit(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                       BigDecimal netProfit, BigDecimal userAdjustedNetProfit) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, netProfit, userAdjustedNetProfit,
                new BigDecimal("90.00"), new BigDecimal("95.00"), 1, 10, new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("57.00"), new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithWishRate(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                       BigDecimal wishRate) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                new BigDecimal("90.00"), new BigDecimal("95.00"), 1, 10, new BigDecimal("100.00"), new BigDecimal("200.00"), wishRate, new BigDecimal("57.00"));
    }

    private void createEvaluationArrivalDateWithWalkRate(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                         BigDecimal walkRate) {
        createEvaluationArrivalDateWith(grpEvlId, isPreferred, arrivalDate, userId, new BigDecimal("300.00"), new BigDecimal("350.00"),
                new BigDecimal("90.00"), new BigDecimal("95.00"), 1, 10, new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("57.00"), walkRate);
    }

    private void createEvaluationArrivalDateWith(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId,
                                                 BigDecimal netProfit, BigDecimal userAdjustedNetProfit,
                                                 BigDecimal profitPercentage, BigDecimal userAdjustedProfitPercentage, Integer numberOfNights, Integer totalRooms,
                                                 BigDecimal contractualRevenue, BigDecimal userAdjustedContractualRevenue, BigDecimal wishRate, BigDecimal walkRate) {
        String userAdjustedNetProf = null != userAdjustedNetProfit ? "'"+userAdjustedNetProfit+"' " : null;
        String userAdjustedProfitPerc = null != userAdjustedProfitPercentage ? "'"+userAdjustedProfitPercentage+"' " : null;
        String userAdjustedContractualRev = null != userAdjustedContractualRevenue ? "'"+userAdjustedContractualRevenue+"' " : null;
        tenantCrudService().executeUpdateByNativeQuery("insert into Grp_Evl_Arr_DT (Grp_Evl_ID, Preferred_Date, Arrival_Date, Created_by_User_ID, Created_DTTM, Last_Updated_by_User_ID, Last_Updated_DTTM, Non_Pkg_Rental, User_Adjusted_Non_Pkg_Rental, " +
                "Number_Of_Nights, Total_Rooms, Contracted_Revenue, User_Adjusted_Contracted_Revenue, Total_Profit_Percentage, " +
                "User_Adjusted_Total_Profit_Percentage, Total_Net_Profit,User_Adjusted_Total_Net_Profit, Wish_Rate, Walk_Rate) " +
                "values(" + grpEvlId + ", " + isPreferred + ", '" + arrivalDate.toLocalDate() + "', " + userId + ", getDate(), 11403, getDate(), '0.000', '0.000', " +
                numberOfNights + ", " + totalRooms + ", '" + contractualRevenue + "', " + userAdjustedContractualRev + ", '" + profitPercentage + "', " + userAdjustedProfitPerc + ", '" +netProfit+"', " + userAdjustedNetProf +", '" + wishRate+"', '" + walkRate+ "')");
    }

    private void createEvaluationArrivalDate(Integer grpEvlId, Integer isPreferred, java.time.LocalDateTime arrivalDate, Integer userId, BigDecimal suggestedRate, BigDecimal breakEvenRate) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Grp_Evl_Arr_DT (Grp_Evl_ID, Preferred_Date, Arrival_Date, Suggested_Rate, Break_Even_Rate, Created_by_User_ID, Created_DTTM, Last_Updated_by_User_ID, Last_Updated_DTTM, Non_Pkg_Rental, User_Adjusted_Non_Pkg_Rental)\n" +
                "values(" + grpEvlId + ", " + isPreferred + ", '" + arrivalDate.toLocalDate() + "', '"+suggestedRate+"' , '"+breakEvenRate+"'," + userId + ", getDate(), 11403, getDate(), '0.000', '0.000')");
    }

    private void createEvaluationApproval(Integer grpEvlId, String approvalId, String approvalStatus, Integer approverId, String approverStatus) {
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Grp_Evl_Approval " +
                "(Grp_Evl_ID, Approval_ID, Approval_Status, Approver_ID, Approver_Status, Notes) VALUES " +
                "(" + grpEvlId.toString() + ", '" + approvalId + "', '" + approvalStatus + "', " + approverId.toString() + ", '" + approverStatus + "', 'No Notes')");
    }

    private void createDayOfStayFor(Integer appleGroupEvlGrpId, Integer dayOfStay, Integer numberOfNights, Integer userID) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Grp_Evl_Day_Of_Stay values("+ appleGroupEvlGrpId + ", " + dayOfStay + ", " + numberOfNights + ", " + userID + ", getDate(), 11403, getDate(), null, null, null, null)");
    }

    private Integer createEvaluationGroup(String groupName, String marketSegment, Integer userId, java.time.LocalDateTime evaluatedOnDate, Integer evaluationType) {
        MarketSegmentSummary marketSegmentSummary = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentSummary.BY_CODE,
                QueryParameter.with("code", marketSegment).parameters());
        tenantCrudService().executeUpdateByNativeQuery("insert into Grp_Evl values(5, '"+groupName+"', "+marketSegmentSummary.getId()+", 0, 0, '"+ evaluatedOnDate.toLocalDate() +"', null, null, null, null, null, "+ userId +", getDate(), "+ userId + ", getDate(), " + evaluationType + ", null, null, null, null, null, 1, 1)");
        Integer groupEvalId = tenantCrudService().findByNativeQuerySingleResult("select Grp_Evl_ID from Grp_Evl ge where Group_Name = :groupName and Mkt_Seg_ID = :mktSegId", QueryParameter.with("groupName", groupName).and("mktSegId", marketSegmentSummary.getId()).parameters());
        return groupEvalId;
    }

    private static GroupEvaluationArrivalDateDisplacementAndForecastDetail createDisplacementAndForecastDetail(AbstractGroupEvaluationDetail.RecordType recordType) {
        GroupEvaluationArrivalDateDisplacementAndForecastDetail displacementAndForecastDetail = new GroupEvaluationArrivalDateDisplacementAndForecastDetail();
        displacementAndForecastDetail.setOccupancyDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.now()));
        displacementAndForecastDetail.setRecordType(recordType);
        displacementAndForecastDetail.setOnBooksApplied(false);
        return displacementAndForecastDetail;
    }
}
