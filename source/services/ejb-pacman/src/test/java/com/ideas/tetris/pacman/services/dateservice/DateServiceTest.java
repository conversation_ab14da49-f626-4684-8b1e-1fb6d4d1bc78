package com.ideas.tetris.pacman.services.dateservice;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.DomainStubPersister;
import com.ideas.tetris.pacman.services.analytics.services.AnalyticsConfigService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueTotalActivityCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueTenantPropertyCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.dateservice.dto.MinDateDto;
import com.ideas.tetris.pacman.services.dateservice.dto.SystemDateDto;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.demand360.Demand360Service;
import com.ideas.tetris.pacman.services.extendedevaluation.service.ExtendedEvaluationService;
import com.ideas.tetris.pacman.services.filemetadata.CaughtUpDateCache;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.limiteddatabuild.UniqueLDBProjectionCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValueType;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.configparams.PlatformParameterComponent;
import com.ideas.tetris.platform.services.configparams.PlatformParameterValueComponent;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType.*;
import static com.ideas.tetris.pacman.services.dateservice.DateService.DAYS_IN_A_MONTH;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DateServiceTest extends AbstractG3JupiterTest {
    public static final int EXPECTED_DAY_MILLIS = 52171305;
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
    private static final String DATE_FORMAT_SIMPLE = "dd/MM/yyyy";
    private static final Integer PG_HILTON_ALL = 1;
    Date snapshotDt_5;
    Date snapshotDt_6;
    Date snapshotDt_7;
    Map<Integer, Date> propertyCaughtUpDateMap;
    PacmanConfigParamsService configParamsService;
    DateService dateService = new DateService();
    FileMetadata fileMetadata = null;
    DecisionService decisionService = null;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    DomainStubPersister domainStubPersister;
    private UserService userService;
    @Mock
    private Demand360Service demand360Service;
    @Mock
    private LDBService ldbService;
    @Mock
    private ExtendedEvaluationService extendedEvaluationService;
    @Mock
    private CrudService jobCrudService;
    @Mock
    private PropertyGroupService propertyGroupService;
    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Mock
    private AnalyticsConfigService analyticsConfigService;

    @BeforeEach
    public void setUp() {
        domainStubPersister = new DomainStubPersister();
        domainStubPersister.setCrudService(tenantCrudService());
        configParamsService = mock(PacmanConfigParamsService.class);
        userService = mock(UserService.class);
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        propertyGroupService.setGlobalCrudService(globalCrudService());
        dateService.setConfigParamsService(configParamsService);
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setCrudService(tenantCrudService());
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        inject(dateService, "demand360Service", demand360Service);
        inject(dateService, "ldbService", ldbService);
        inject(dateService, "extendedEvaluationService", extendedEvaluationService);
        inject(dateService, "jobCrudService", jobCrudService);
        inject(dateService, "analyticsConfigService", analyticsConfigService);
        fileMetadata = domainStubPersister.getSimplePopulationFileMetadata_BDE(); // Stages an expected type
        // FileMetaData record

        PacmanWorkContextHelper.setPropertyId(5);
        decisionService = DecisionService.createTestInstance();
        dateService.setUserService(userService);
    }

    @Test
    void getLastSnapDate() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        SasDbToolService sasDbToolService = mock(SasDbToolService.class);
        dateService.globalCrudService = mockGlobalCrudService;
        dateService.sasDbToolService = sasDbToolService;
        Property property = new Property();
        Client client = new Client(23);
        client.setCode("TEST");
        property.setClient(client);
        property.setCode("testProperty");
        property.setId(23);
        when(mockGlobalCrudService.find(eq(Property.class), anyInt())).thenReturn(property);
        SasDbQueryResult result = new SasDbQueryResult();
        ArrayList<Object> objects = new ArrayList<>(List.of("2021-01-27", "23:11"));
        result.setData(new ArrayList<>(List.of(objects)));
        when(sasDbToolService.executeQuery(anyString(), anyInt(), any(), any())).thenReturn(result);
        assertNotNull(dateService.getLastSnapStartDate(23));
    }

    @Test
    void getLastSnapDate_1() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        SasDbToolService sasDbToolService = mock(SasDbToolService.class);
        dateService.globalCrudService = mockGlobalCrudService;
        dateService.sasDbToolService = sasDbToolService;
        Property property = new Property();
        Client client = new Client(23);
        client.setCode("TEST");
        property.setClient(client);
        property.setCode("testProperty");
        property.setId(23);
        when(mockGlobalCrudService.find(eq(Property.class), anyInt())).thenReturn(property);
        SasDbQueryResult result = new SasDbQueryResult();
        ArrayList<Object> objects = new ArrayList<>(List.of("2021-01-27", "2:11"));
        result.setData(new ArrayList<>(List.of(objects)));
        when(sasDbToolService.executeQuery(anyString(), anyInt(), any(), any())).thenReturn(result);
        assertNotNull(dateService.getLastSnapStartDate(23));
    }

    @Test
    public void test_getForecastWindowOffset_BDE() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("120");
        int i = dateService.getForecastWindowOffsetBDE();
        assertEquals(119, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    @Test
    public void test_getForecastWindowOffset_CDP() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value())).thenReturn("30");
        int i = dateService.getForecastWindowOffsetCDP();
        assertEquals(29, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value());
    }

    @Test
    public void test_getForecastWindowOffset_BDE_no_Config() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn(null);
        int i = dateService.getForecastWindowOffsetBDE();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    @Test
    public void test_getForecastWindowOffset_CDP_no_Config() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value())).thenReturn(null);
        int i = dateService.getForecastWindowOffsetCDP();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value());
    }

    @Test
    public void test_getOptimizationWindowOffset_BDE() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("366");
        int i = dateService.getOptimizationWindowOffsetBDE();
        assertEquals(365, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
    }

    @Test
    public void test_getOptimizationWindowOffsetBDEVariable() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");
        int i = dateService.getVaribleDecisionWindowDays();
        assertEquals(179, i);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
    }

    @Test
    public void test_getOptimizationWindowOffsetBDEVariable_Negative() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("-1");
        int i = dateService.getVaribleDecisionWindowDays();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
    }

    @Test
    public void test_getOptimizationWindowOffsetBDEVariable_noConfigValue() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn(null);
        int i = dateService.getVaribleDecisionWindowDays();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
    }

    @Test
    public void test_getOptimizationWindowOffset_BDE_noConfigValue() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn(null);
        int i = dateService.getOptimizationWindowOffsetBDE();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
    }

    @Test
    public void test_getOptimizationWindowOffset_CDP() {
        when(analyticsConfigService.getNextIdpWindowOverride()).thenReturn(-1);
        int i = dateService.getOptimizationWindowOffsetCDP();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value());
    }

    @Test
    public void test_getOptimizationWindowOffset_CDP_useAnalyticsConfig_enabled() {
        when(analyticsConfigService.getNextIdpWindowOverride()).thenReturn(100);
        int i = dateService.getOptimizationWindowOffsetCDP();
        assertEquals(100, i);
    }

    @Test
    public void test_getCaughtUpDate() {
        dateService.setCrudService(tenantCrudService());
        Date caughtUpDate = dateService.getCaughtUpDate();
        assertNotNull(caughtUpDate);

    }

    @Test
    public void test_getCaughtUpDateCacheHit() {
        CaughtUpDateCache caughtUpDateCache = Mockito.mock(CaughtUpDateCache.class);
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.CAUGHT_UP_DATE_CACHE_ENABLED)).thenReturn(true);

        when(caughtUpDateCache.getCaughtUpDate(List.of(5))).thenReturn(new Date());

        AbstractMultiPropertyCrudService crudService = Mockito.mock(AbstractMultiPropertyCrudService.class);
        dateService.setCaughtUpDateCache(caughtUpDateCache);
        dateService.setMultiPropertyCrudService(crudService);
        Date caughtUpDate = dateService.getCaughtUpDateFromCache();

        verifyNoInteractions(crudService);
        assertNotNull(caughtUpDate);
    }

    @Test
    public void test_getCaughtUpDateCacheMiss() {
        CaughtUpDateCache caughtUpDateCache = Mockito.mock(CaughtUpDateCache.class);
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.CAUGHT_UP_DATE_CACHE_ENABLED)).thenReturn(true);

        when(caughtUpDateCache.getCaughtUpDate(List.of(5))).thenReturn(null);

        AbstractMultiPropertyCrudService crudService = Mockito.mock(AbstractMultiPropertyCrudService.class);
        dateService.setCaughtUpDateCache(caughtUpDateCache);
        dateService.setMultiPropertyCrudService(crudService);
        Date caughtUpDate = dateService.getCaughtUpDateFromCache();
        verify(crudService,times(1)).findByNativeQueryReduceAcrossPropertiesSingleResult(anyList(), any(String.class),
                anyMap(),any());
        assertNotNull(caughtUpDate);
    }

    @Test
    public void test_getCaughtUpDateCacheToggleOff() {
        CaughtUpDateCache caughtUpDateCache = Mockito.mock(CaughtUpDateCache.class);
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.CAUGHT_UP_DATE_CACHE_ENABLED)).thenReturn(false);

        when(caughtUpDateCache.getCaughtUpDate(List.of(5))).thenReturn(null);

        AbstractMultiPropertyCrudService crudService = Mockito.mock(AbstractMultiPropertyCrudService.class);
        dateService.setCaughtUpDateCache(caughtUpDateCache);
        dateService.setMultiPropertyCrudService(crudService);
        Date caughtUpDate = dateService.getCaughtUpDateFromCache();
        verify(crudService,times(1)).findByNativeQueryReduceAcrossPropertiesSingleResult(anyList(), any(String.class),
                anyMap(),any());
        verifyNoInteractions(caughtUpDateCache);
        assertNotNull(caughtUpDate);
    }

    @Test
    public void test_getCaughtUpDateNull() {
        PacmanWorkContextHelper.setPropertyId(999);
        dateService.setCrudService(tenantCrudService());
        Date caughtUpDate = dateService.getCaughtUpDate(false);
        assertNull(caughtUpDate);

    }

    @Test
    public void test_getCaughtUpDateNotNull() {
        PacmanWorkContextHelper.setPropertyId(999);
        dateService.setCrudService(tenantCrudService());
        Date caughtUpDate = dateService.getCaughtUpDate(true);
        assertNotNull(caughtUpDate);

    }

    @Test
    public void test_getCaughtUpJavaLocalDate() {
        dateService.setCrudService(tenantCrudService());
        java.time.LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        assertNotNull(caughtUpDate);
    }

    @Test
    public void test_getForecastWindowStartDate() {
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getForecastWindowStartDate();
        assertNotNull(date);

    }

    @Test
    public void test_getWebRateShoppingDate_No_FileMetaData() {
        dateService.setCrudService(tenantCrudService());
        assertNull(dateService.getWebRateShoppingDate());

    }

    @Test
    public void test_getForecastWindowEndDate_BDE() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("120");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getForecastWindowEndDateBDE();
        assertNotNull(date);
        // assertEquals("Mon Apr 11 00:00:00 CDT 2011", date.toString());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    @Test
    public void test_getForecastWindowEndDate_BDE_No_config() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn(null);
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getForecastWindowEndDateBDE();
        assertNotNull(date);
        // assertEquals("Mon Apr 11 00:00:00 CDT 2011", date.toString());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    @Test
    public void test_getForecastWindowEndDate_CDP() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value())).thenReturn("30");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getForecastWindowEndDateCDP();
        assertNotNull(date);
        // assertEquals("Mon Apr 11 00:00:00 CDT 2011", date.toString());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value());
    }

    @Test
    public void test_getOptimizationWindowStartDate() {
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowStartDate();
        assertNotNull(date);
        // assertEquals(dateService.getCaughtUpDate(), date);
    }

    @Test
    public void test_getUploadDecisionWindowOffsetBDEVariable() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("365");
        int i = dateService.getUploadWindowOffsetBDE();
        assertEquals(364, i);
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value());
    }

    @Test
    public void test_getUploadDecisionWindowOffsetBDEVariable_Negative() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("-1");
        int i = dateService.getUploadWindowOffsetBDE();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value());
    }

    @Test
    public void test_getUploadDecisionWindowOffsetBDEVariable_noConfigValue() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn(null);
        int i = dateService.getUploadWindowOffsetBDE();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value());
    }

    @Test
    public void test_getUploadWindowEndDate_BDE() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("365");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getUploadWindowEndDateBDE();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value());
    }

    @Test
    public void test_getLastBackfillDate() {
        LocalDate SNAPSHOT_DATE = new LocalDate("2016-09-13");
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(3);
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata.setFileName("PaceHistoryBuild");
        fileMetadata.setSnapshotDt(SNAPSHOT_DATE.toDate());
        fileMetadata = tenantCrudService().save(fileMetadata);
        FileMetadata fileMetadata2 = domainStubPersister.getFileMetadata();
        fileMetadata2.setRecordTypeId(3);
        fileMetadata2.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata2.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata2.setFileName("PaceHistoryBuild");
        fileMetadata2.setSnapshotDt(SNAPSHOT_DATE.minusDays(5).toDate());
        fileMetadata = tenantCrudService().save(fileMetadata);
        dateService.setCrudService(tenantCrudService());
        LocalDate results = dateService.getLastBackfillDate();
        assertEquals(SNAPSHOT_DATE.plusDays(1), results);
    }

    @Test
    public void validateDecisionUploadWindowBDEForBusinessEndDate() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("180");
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        String dayOfWeek = dateService.getDayofWeek(dateService.getBusinessDate());
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn(dayOfWeek);
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("100");

        Date date = dateService.getDecisionUploadWindowEndDateBDEVariable();

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();

        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(uploadDate, date);
    }

    @Test
    public void test_getDecisionUploadWindowEndDate() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("365");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getDecisionUploadWindowEndDate();
        assertNotNull(date);
//        verify(configParamsService).getBooleanParameterValue(Constants.USE_DECISION_UPLOAD_WINDOW);

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();

        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(date, uploadDate);

    }

    @Test
    public void test_getOperationTypeDecisionUploadEndDateWith() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("365");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOperationTypeDecisionUploadEndDate(Constants.BDE);
        assertNotNull(date);

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();

        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(date, uploadDate);
    }

    @Test
    public void test_getOperationTypeDecisionUploadEndDateIdpWithOperationTypeAgnosticUpload() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("365");
        dateService.setCrudService(tenantCrudService());
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERATION_TYPE_AGNOSTIC_UPLOAD_ENABLED)).thenReturn(true);
        Date date = dateService.getOperationTypeDecisionUploadEndDate(Constants.CDP);
        assertNotNull(date);

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();

        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(date, uploadDate);
    }


    @Test
    public void test_getOptimizationWindowEndDateBDE() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("120");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateBDE();
        assertNotNull(date);
        // assertEquals("Mon Apr 11 00:00:00 CDT 2011", date.toString());
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateCDPVariable() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");

        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("SuNdaY");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateCDPVariable();
        assertNotNull(date);
        // assertEquals("Mon Apr 11 00:00:00 CDT 2011", date.toString());
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateCDPVariable_definately_VariableWindowDay() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");

        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("SuNdaY");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateCDPVariable();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateCDPVariable_VariableWindow_Larger() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("365");

        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateCDPVariable();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateBDEVariable() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");

        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("SuNdaY");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateBDEVariable();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateBDEVariable_caseChange() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");

        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateBDEVariable();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
        verify(configParamsService).getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value());
    }

    @Test
    public void test_getOptimizationWindowEndDateBDEVariableApplyAndOverride() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("180");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamsService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(
                IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);

        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateBDEVariableCheckApplyAndOverride();
        assertNotNull(date);

        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
    }

    @Test
    public void test_getOptimizationWindowEndDate_CDP() {
        when(configParamsService.getBooleanParameterValue(
                IPConfigParamName.USE_ANALYTICS_CONFIG_ENABLED)).thenReturn(false);
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value())).thenReturn("120");
        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getOptimizationWindowEndDateCDP();
        assertNotNull(date);
        verify(configParamsService).getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value());
    }

    @Test
    public void test_getForecastWindowOffset_invalidValue() {
        when(configParamsService.getParameterValue(
                IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("abc");
        int i = dateService.getForecastWindowOffsetBDE();
        assertEquals(0, i);
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    @Test
    public void getBusinessDate_BDEisTrue() {
        dateService.setCrudService(tenantCrudService());

        Date CAUGHT_UP_DATE = dateService.getCaughtUpDate();

        Date businessDate = dateService.getBusinessDate();
        assertNotNull(businessDate);
        Calendar c = new GregorianCalendar();
        c.setTime(CAUGHT_UP_DATE);
        c.add(Calendar.DATE, -1);
        assertTrue(businessDate.compareTo(c.getTime()) == 0, "business date should be day prior to caught-up-date");
    }

    @Test
    public void getBusinessDate_BDEisFalse() {
        dateService.setCrudService(tenantCrudService());
        final FileMetadata fm = domainStubPersister.getSimplePopulationFileMetadata_CDP();
        fm.setSnapshotDt(DateUtils.addDays(fm.getSnapshotDt(), 1));
        tenantCrudService().save(fm);

        Date CAUGHT_UP_DATE = dateService.getCaughtUpDate();

        Date businessDate = dateService.getBusinessDate();
        assertNotNull(businessDate);
        assertEquals(CAUGHT_UP_DATE, businessDate, "Since it is a CDP, business date should be caught up date");
    }

    @Test
    void getAdjustedBusinessDate_IsBDE() {
        Date caughtUpDate = dateService.getCaughtUpDate();

        Date businessDate = dateService.getAdjustedBusinessDate(1);
        assertNotNull(businessDate);
        assertEquals(DateUtil.addDaysToDate(caughtUpDate, -1), businessDate);
    }

    @Test
    void getAdjustedBusinessDate_IsCDP() {
        final FileMetadata fm = domainStubPersister.getSimplePopulationFileMetadata_CDP();
        fm.setSnapshotDt(DateUtils.addDays(fm.getSnapshotDt(), 1));
        tenantCrudService().save(fm);

        Date caughtUpDate = dateService.getCaughtUpDate();

        Date businessDate = dateService.getAdjustedBusinessDate(-1);
        assertNotNull(businessDate);
        assertEquals(DateUtil.addDaysToDate(caughtUpDate, -1), businessDate);

        businessDate = dateService.getAdjustedBusinessDate(0);
        assertNotNull(businessDate);
        assertEquals(caughtUpDate, businessDate);

        businessDate = dateService.getAdjustedBusinessDate(1);
        assertNotNull(businessDate);
        assertEquals(DateUtil.addDaysToDate(caughtUpDate, 1), businessDate);
    }

    @Test
    public void getWasLastLoadBDE() {
        dateService.setCrudService(tenantCrudService());
        assertTrue(dateService.getWasLastLoadBDE());
    }

    @Test
    public void getWasLastLoadBDE_is_CDP() {
        dateService.setCrudService(tenantCrudService());

        FileMetadata fm = domainStubPersister.getSimplePopulationFileMetadata_CDP();
        fm.setSnapshotDt(new LocalDate(2050, 2, 2).toDate());
        fm.setTenantPropertyId(5);
        tenantCrudService().save(fm);
        tenantCrudService().findAll(FileMetadata.class);

        assertFalse(dateService.getWasLastLoadBDE());
    }

    @Test
    public void getWasLastLoadBDE_No_Entries() {
        dateService.setCrudService(tenantCrudService());
        tenantCrudService().executeUpdateByNativeQuery("update [dbo].[File_Metadata] set [Record_Type_ID] = 1");
        assertTrue(dateService.getWasLastLoadBDE());
    }

    @Test
    public void testGetDateByDayMonthYear_1January2011() {
        Date actual = dateService.getDateWithoutTime(1, 0, 2011);

        Calendar cal = Calendar.getInstance();
        cal.setTime(actual);

        assertEquals(1, cal.get(Calendar.DAY_OF_MONTH));
        assertEquals(0, cal.get(Calendar.MONTH));
        assertEquals(2011, cal.get(Calendar.YEAR));
        assertEquals(0, cal.get(Calendar.SECOND));
        assertEquals(0, cal.get(Calendar.MINUTE));
        assertEquals(0, cal.get(Calendar.HOUR));
    }

    @Test
    public void testGetDateByDayMonthYear_4July2012() {
        Date actual = dateService.getDateWithoutTime(4, 6, 2012);

        Calendar cal = Calendar.getInstance();
        cal.setTime(actual);

        assertEquals(4, cal.get(Calendar.DAY_OF_MONTH));
        assertEquals(6, cal.get(Calendar.MONTH));
        assertEquals(2012, cal.get(Calendar.YEAR));
        assertEquals(0, cal.get(Calendar.SECOND));
        assertEquals(0, cal.get(Calendar.MINUTE));
        assertEquals(0, cal.get(Calendar.HOUR));
    }

    @Test
    public void testGetDateByDayMonthYear_RollToNextMonth() {
        Date actual = dateService.getDateWithoutTime(32, 6, 2012);

        Calendar cal = Calendar.getInstance();
        cal.setTime(actual);

        // will roll over to first of next month
        assertEquals(1, cal.get(Calendar.DAY_OF_MONTH));
        assertEquals(7, cal.get(Calendar.MONTH));
        assertEquals(2012, cal.get(Calendar.YEAR));
        assertEquals(0, cal.get(Calendar.SECOND));
        assertEquals(0, cal.get(Calendar.MINUTE));
        assertEquals(0, cal.get(Calendar.HOUR));
    }

    @Test
    public void testGetDateByDayMonthYear_RollToNextYear() {
        Date actual = dateService.getDateWithoutTime(32, 11, 2012);

        Calendar cal = Calendar.getInstance();
        cal.setTime(actual);

        // will roll over to first of next year
        assertEquals(1, cal.get(Calendar.DAY_OF_MONTH));
        assertEquals(0, cal.get(Calendar.MONTH));
        assertEquals(2013, cal.get(Calendar.YEAR));
        assertEquals(0, cal.get(Calendar.SECOND));
        assertEquals(0, cal.get(Calendar.MINUTE));
        assertEquals(0, cal.get(Calendar.HOUR));
    }

    /*
     * For now, getForecastedDate and getDecisionDate are identical
     */
    @Test
    public void testGetDecisionDate() {
        decisionService.setCrudService(tenantCrudService());
        decisionService.setDateServiceLocal(dateService);
        replayConfigParamsService_CST();
        // BDE
        Decision createBdeDecision = decisionService.createBdeDecision();
        Date actualDecisionDate = dateService.getDecisionDate();

        assertEquals(sdf.format(DateUtil.removeMinutesSeconds(createBdeDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
        // CDP
        Decision createCdpDecision = decisionService.createCdpDecision();
        actualDecisionDate = dateService.getDecisionDate();
        assertEquals(sdf.format(DateUtil.removeMinutesSeconds(createCdpDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
        // Negative test
        Decision createBAROverrideDecision = decisionService.createBAROverrideDecision();
        actualDecisionDate = dateService.getDecisionDate();
        assertNotSame(sdf.format(DateUtil.removeMinutesSeconds(createBAROverrideDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
    }

    /*
     * For now, getForecastedDate and getDecisionDate are identical
     */
    @Test
    public void testGetForecastedDate() {
        decisionService.setCrudService(tenantCrudService());
        decisionService.setDateServiceLocal(dateService);
        replayConfigParamsService_CST();

        // BDE
        Decision createBdeDecision = decisionService.createBdeDecision();
        tenantCrudService().flushAndClear();
        Date actualDecisionDate = dateService.getForecastedDate();
        assertEquals(sdf.format(DateUtil.removeMinutesSeconds(createBdeDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
        // CDP
        Decision createCdpDecision = decisionService.createCdpDecision();
        actualDecisionDate = dateService.getForecastedDate();
        assertEquals(sdf.format(DateUtil.removeMinutesSeconds(createCdpDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
        // Negative test
        Decision createBAROverrideDecision = decisionService.createBAROverrideDecision();
        actualDecisionDate = dateService.getForecastedDate();
        assertNotSame(sdf.format(DateUtil.removeMinutesSeconds(createBAROverrideDecision.getCreateDate())), sdf.format(DateUtil.removeMinutesSeconds(actualDecisionDate)));
    }

    @Test
    public void testGetWebRateProcessedDate() throws Exception {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10);
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata = tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        Date txnPopDate = dateService.getWebRateProcessedDate();
        assertEquals(sdf.format(fileMetadata.getCreatedate()), sdf.format(txnPopDate));
    }

    @Test
    public void testGetTransactionCaughtUpDate() {
        // Create a FileMetadata record with record type: IndTrans
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(11); // Record type - IndTrans
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata = tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        Date txnPopDate = dateService.getTransactionDataPopulationDate();
        assertEquals(sdf.format(
                        DateUtil.mergeSqlDateAndTime(fileMetadata.getSnapshotDt(), fileMetadata.getSnapshotTm()).getTime()),
                sdf.format(txnPopDate));

        System.out.println(sdf.format(txnPopDate));
    }

    @Test
    public void testGetTransactionDataPopulationDate() throws Exception {
        // Create a FileMetadata record with record type: IndTrans
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        // Record type - IndTrans
        fileMetadata.setRecordTypeId(11);
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata = tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        Date txnPopDate = dateService.getTransactionDataPopulationDate();
        assertEquals(sdf.format(fileMetadata.getCreatedate()), sdf.format(txnPopDate));
    }

    @Test
    public void getPropertyTimeZone_noConfigParam_givesUTC() {
        TimeZone utc = dateService.getPropertyTimeZone();
        assertEquals(0, utc.getRawOffset()); // -6 * DateUtil.HOUR_IN_MILLISECONDS
    }

    @Test
    public void getPropertyTimeZone_configCST_givesCST() {
        replayConfigParamsService_CST();

        TimeZone cst = dateService.getPropertyTimeZone();
        assertEquals(-6 * DateUtil.HOUR_IN_MILLISECONDS, cst.getRawOffset()); //
    }

    @Test
    public void getPropertyTimeZone_configIST_givesCST() {
        replayConfigParamsService_IST();

        TimeZone cst = dateService.getPropertyTimeZone();
        assertEquals(5 * DateUtil.HOUR_IN_MILLISECONDS + 30 * DateUtil.MINUTE_IN_MILLISECONDS, cst.getRawOffset()); //
    }

    private String getPropertyTimeZoneNodeName() {
        StringBuilder nodeName = new StringBuilder();
        nodeName.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".")
                .append(PacmanWorkContextHelper.getWorkContext().getClientCode()).append(".")
                .append(PacmanWorkContextHelper.getWorkContext().getPropertyCode());
        return nodeName.toString();
    }

    private void replayConfigParamsService_CST() {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Chicago");
    }

    private void verify_replayConfigParamService_CST() {
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
    }

    private void replayConfigParamsService_IST() {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("GMT+05:30");
    }

    @Test
    public void testGetCaughtUpDateLabel() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(3); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        fileMetadata = tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        SimpleDateFormat sdf = new SimpleDateFormat();
        sdf.applyPattern("EEE dd-MMM-yyyy");
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");
        assertNotNull(dateService.getCaughtUpDateLabel());
        verify_replayConfigParamService_CST();
    }

    @Test
    public void testGetWebRateShoppingDateLabel() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        SimpleDateFormat sdf = new SimpleDateFormat();
        sdf.applyPattern("EEE dd-MMM-yyyy ");
        assertNotNull(dateService.getWebRateShoppingDateLabel());
    }

    @Test
    public void testGetBARDisplayWindowStartDate() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        SimpleDateFormat sdf = new SimpleDateFormat();
        sdf.applyPattern("EEE MMM dd yyyy");
        Date today = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();
        assertEquals(sdf.format(firstDayOfMonth), sdf.format(dateService.getBARDisplayWindowStartDate()));
    }

    @Test
    public void testGetUnqualifiedRatcaughtUpDateLabel() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        assertNotNull(dateService.getUnqualifiedRateCaughtUpDateLabel());
    }

    @Test
    public void testGetDecisionDateLabel() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        assertNotNull(dateService.getDecisionDateLabel());
    }

    @Test
    public void testTransactionCaughtUpDate() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(11); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        assertNotNull(dateService.getTransactionCaughtUpDate());
    }

    @Test
    public void testGetTransactionCaughtUpDateLabel() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(11); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");
        dateService.setCrudService(tenantCrudService());
        assertNotNull(dateService.getTransactionCaughtUpDateLabel());
    }

    @Test
    public void testGetHeatMapDateWindowStartDate() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(3); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        assertNotNull(dateService.getHeatMapDisplayWindowStartDate());
    }

    @Test
    public void testGetHeatMapDateWindowEndDate() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(3); // Record type - T2SNAP
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        replayConfigParamsService_CST();
        dateService.setCrudService(tenantCrudService());
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        assertNotNull(dateService.getHeatMapDisplayWindowEndDate());
    }

    @Test
    public void testWebRateProcessedDate() {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setRecordTypeId(10); // Record type - Webrate
        fileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        fileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        tenantCrudService().save(fileMetadata);
        dateService.setCrudService(tenantCrudService());
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        assertNotNull(dateService.getWebRateProcessedDateLabel("dd-MMM-yyyy"));
    }

    @Test
    @Tag("dateService-flaky")
    public void testEarliestOccupancyForecastDate() {
        // 1 year two months ago
        java.time.LocalDate expected = java.time.LocalDate.now().minusDays(426);
        Date actual = dateService.getEarliestOccupancyForecastDate();
        assertEquals(expected.toString(), actual.toString(), "Wrong occupancy forecast date");
    }

    @Test
    @Tag("dateService-flaky")
    public void testLatestOccupancyForecastDate() {
        java.time.LocalDate expected = java.time.LocalDate.now().plusDays(120);
        Date actual =
                dateService.getLatestOccupancyForecastDate(UniqueTenantPropertyCreator.getTenantProperty().getId());
        assertEquals(expected.toString(), actual.toString(), "Wrong occupancy forecast date");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetMaxCaughtUpDate() throws ParseException {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<FileMetadata>();
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/05/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));

        fileMetadataList.add(fileMetadata);
        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/02/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));

        fileMetadataList.add(fileMetadata);
        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/07/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));

        fileMetadataList.add(fileMetadata);
        when(mockMultiCrudSvc.findByNamedQueryUnionAcrossProperties(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        Date caughtUpDate = dateService.getMaxCaughtUpDate(new ArrayList<Integer>());
        assertEquals(caughtUpDate, SIMPLE_DATE_FORMAT.parse("12/07/2012"));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testGetMaxCaughtUpDate_ByClientId() throws ParseException {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<FileMetadata>();
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/05/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);
        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/02/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);
        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(SIMPLE_DATE_FORMAT.parse("12/07/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));

        fileMetadataList.add(fileMetadata);
        when(mockMultiCrudSvc.findByNamedQueryUnionAcrossProperties(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        dateService.globalCrudService = globalCrudService();
        Date caughtUpDate = dateService.getMaxCaughtUpDate(PacmanWorkContextHelper.getClientId());
        assertEquals(caughtUpDate, SIMPLE_DATE_FORMAT.parse("12/07/2012"));
    }

    @Test
    public void testDateLabels() throws Exception {
        // as of US4346, the native sql in the business method:: dateService.getDateLabels() was modified.

        FileMetadata t2snapFileMetadata = domainStubPersister.getFileMetadata();
        t2snapFileMetadata.setRecordTypeId(3);
        t2snapFileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        t2snapFileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        t2snapFileMetadata = tenantCrudService().save(t2snapFileMetadata);

        FileMetadata unqualifiedRateFileMetadata = domainStubPersister.getFileMetadata();
        unqualifiedRateFileMetadata.setRecordTypeId(9);
        unqualifiedRateFileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        unqualifiedRateFileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        unqualifiedRateFileMetadata.setCreatedate(DateUtil.getDate(11, 6, 2012));
        unqualifiedRateFileMetadata = tenantCrudService().save(unqualifiedRateFileMetadata);

        FileMetadata webrateFileMetadata = domainStubPersister.getFileMetadata();
        webrateFileMetadata.setRecordTypeId(10);
        webrateFileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        webrateFileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        webrateFileMetadata.setCreatedate(DateUtil.getDate(2, 7, 2012));
        webrateFileMetadata = tenantCrudService().save(webrateFileMetadata);

        FileMetadata indTransFileMetadata = domainStubPersister.getFileMetadata();
        indTransFileMetadata.setRecordTypeId(11);
        indTransFileMetadata.setTenantPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        indTransFileMetadata.setProcessStatusId(domainStubPersister.getProcessStatus_Successful().getId());
        indTransFileMetadata.setCreatedate(DateUtil.getDate(2, 5, 2012));
        indTransFileMetadata = tenantCrudService().save(indTransFileMetadata);

        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Chicago");
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value()))
                .thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(false);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        Map<String, String> dateLabels = dateService.getDateLabels();

        assertEquals(dateService.getWebRateProcessedDateLabel("dd-MMM-yyyy HH:mm:ss"),
                dateLabels.get("webRateProcessedDateLabel"));
        assertEquals(dateService.getUnqualifiedRateCaughtUpDateLabel(),
                dateLabels.get("unqualifiedRateCaughtUpDateLabel"));
        assertEquals(dateService.getForecastedDateLabel(), dateLabels.get("forecastedDateLabel"));
        assertEquals(dateService.getDecisionDateLabel(), dateLabels.get("decisionDateLabel"));
        assertEquals(dateService.getTransactionDataPopulationDateLabel().substring(0, 21),
                dateLabels.get("transactionDataPopulationDateLabel").substring(0, 21));
//        assertEquals(dateService.getTransactionCaughtUpDateLabel(), dateLabels.get("transactionCaughtUpDateLabel"));

//        assertEquals(dateService.formatServerDate(t2snapFileMetadata.getCreatedate()), dateLabels.get
//        ("transactionDataPopulationDateLabel"));
//        assertEquals(dateService.formatServerDate(t2snapFileMetadata.getSnapshotDtTm()), dateLabels.get
//        ("transactionCaughtUpDateLabel"));
    }

    @Test
    public void testDateLabelsForPropertyInDifferentTimezoneThanTheServer() throws Exception {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Phoenix");
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value()))
                .thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(false);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);

        Map<String, String> dateLabels = dateService.getDateLabels();
        assertEquals(dateService.getForecastedDateLabel(), dateLabels.get("forecastedDateLabel"));
        assertEquals(dateService.getDecisionDateLabel(), dateLabels.get("decisionDateLabel"));
        verify(configParamsService, times(3)).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED);
    }

    private void setData(boolean value) {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Phoenix");
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RRAENABLED)).thenReturn(value);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");
    }

    @Test
    public void shouldNotShowRraProcessedInDateLabels() throws Exception {
        setData(false);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        Map<String, String> dateLabels = dateService.getDateLabels();
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.RRAENABLED);
        assertFalse(dateLabels.containsKey("rraProcessedDateLabel"));
        assertNull(dateLabels.get("rraProcessedDateLabel"));
    }

    @Test
    public void shouldShowRraProcessedInDateLabels() throws Exception {
        setData(true);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        Map<String, String> dateLabels = dateService.getDateLabels();
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.RRAENABLED);
        assertTrue(dateLabels.containsKey("rraProcessedDateLabel"));
        assertNull(dateLabels.get("rraProcessedDateLabel"));
    }

    @Test
    public void rraProcessedDataTest() {
        tenantCrudService().executeUpdateByNativeQuery(
                "insert into File_Metadata values (22,0,'rraFileName','rraFileLocation',5,0,0,NULL,NULL,'2017-01-01'," +
                        "'01:01:00.0000000','2017-01-01','01:01:00.0000000',NULL,13,'2017-01-01 01:01:00.000')");
        setData(true);
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn(TimeZone.getDefault().getID());
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        Map<String, String> dateLabels = dateService.getDateLabels();
        String rraCreateDate = dateLabels.get("rraProcessedDateLabel");

        assertTrue(Pattern.compile("Sun 01-Jan-2017 ..:..:..").matcher(rraCreateDate).find());
    }

    @Test
    public void shouldNotShowRateUnqualifiedDateInDateLabels() throws Exception {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Phoenix");
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED))
                .thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED))
                .thenReturn(false);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");
        Map<String, String> dateLabels = dateService.getDateLabels();
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED);
        assertNull(dateLabels.get("unqualifiedRateCaughtUpDateLabel"));
        assertFalse(dateLabels.containsKey("unqualifiedRateCaughtUpDateLabel"));
    }

    @Test
    public void shouldShowRateUnqualifiedDateInDateLabels() throws Exception {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Phoenix");
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED))
                .thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED))
                .thenReturn(false);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        Map<String, String> dateLabels = dateService.getDateLabels();
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED);
        assertNotNull(dateLabels.get("unqualifiedRateCaughtUpDateLabel"));
    }

    @Test
    public void shouldShowFunctionSpaceDateInDateLabel() throws Exception {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/Phoenix");
        when(configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED))
                .thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED))
                .thenReturn(true);
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        Map<String, String> dateLabels = dateService.getDateLabels();
        verify(configParamsService).getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(configParamsService).getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED);
        assertTrue(dateLabels.containsKey(DateService.FUNCTION_SPACE_CAUGHTUP_DATE_LABEL));
    }

    @Test
    public void getDayOfWeek() {
        Calendar calendar = new GregorianCalendar(2012, 11, 10);
        assertEquals("monday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("tuesday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("wednesday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("thursday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("friday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("saturday", dateService.getDayofWeek(calendar.getTime()));
        calendar.add(Calendar.DATE, 1);
        assertEquals("sunday", dateService.getDayofWeek(calendar.getTime()));
    }

    @Test
    public void testEarliestOccupancyDate() {
        Date OccupancyDate = dateService.getEarliestOccupancyDate();
        assertNotNull(OccupancyDate);
    }

    @Test
    public void shouldMakeQueryCallToGetMaxOccupancyDtByProperty() {
        CrudService mockCrudService = mock(CrudService.class);
        when(mockCrudService.findByNamedQuerySingleResult(TotalActivity.MAX_OCCUPANCY_DT_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new Date());
        dateService.setCrudService(mockCrudService);
        dateService.getMaxFutureOccupancyDateParameter();
        verify(mockCrudService).findByNamedQuerySingleResult(TotalActivity.MAX_OCCUPANCY_DT_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void findMostRecentMinPropertySnapshotDateTimeForRecordType() {
        assertNotNull(dateService.findMostRecentMinPropertySnapshotDateTimeForRecordType(3));
    }

    @Test
    void findCaughtUpDate() {
        assertNotNull(dateService.getCaughtUpDate());
    }

    @Test
    void findCaughtUpLocalDateTime() {
        assertNotNull(dateService.getCaughtUpLocalDateTime());
    }

    @Test
    public void getSupportedAndUnsupportedTimezones() {
        ConfigParameterPredefinedValueType predefinedType = new ConfigParameterPredefinedValueType();
        Set<ConfigParameterPredefinedValue> predefinedValues = new HashSet<ConfigParameterPredefinedValue>();
        String[] javaTimezones = TimeZone.getAvailableIDs();
        for (int i = 1; i <= 10; i++) {
            ConfigParameterPredefinedValue predefinedValue = new ConfigParameterPredefinedValue();
            predefinedValue.setId(i);
            predefinedValue.setValue(javaTimezones[i]);
            predefinedValues.add(predefinedValue);
        }
        predefinedType.setConfigParameterPredefinedValues(predefinedValues);
        when(configParamsService.getPredefinedValueTypeByCode(Constants.CONFIG_PARAM_CODE_TIMEZONE))
                .thenReturn(predefinedType);

        List<String> timezones = dateService.getSupportedTimezones();
        Assertions.assertTrue(timezones.size() > 0);
        List<String> unsupportedTimezones = dateService.getUnsupportedTimezones();
        Assertions.assertTrue(unsupportedTimezones.size() > 0);
        verify(configParamsService, times(2)).getPredefinedValueTypeByCode(Constants.CONFIG_PARAM_CODE_TIMEZONE);
    }

    @Test
    public void timezoneImpedance() {
        DateService realDateService = DateService.createTestInstance();
        PacmanConfigParamsService realConfigParamsService = new PacmanConfigParamsService();
        PlatformParameterComponent parameterComponent = new PlatformParameterComponent();
        parameterComponent.setCrudService(globalCrudService());
        realConfigParamsService.setParameterComponent(parameterComponent);
        PlatformParameterValueComponent parameterValueComponent = new PlatformParameterValueComponent();
        parameterValueComponent.setCrudService(globalCrudService());
        realConfigParamsService.setParameterValueComponent(parameterValueComponent);
        realDateService.setConfigParamsService(realConfigParamsService);

        List<String> javaTimezones = Arrays.asList(TimeZone.getAvailableIDs());
        Assertions.assertNotNull(javaTimezones);
        Collections.sort(javaTimezones);
        System.out.println("Java timezone count: " + javaTimezones.size());

        List<String> databaseTimezones = realDateService.getSupportedTimezones();
        Assertions.assertNotNull(databaseTimezones);
        System.out.println("\nDatabase timezone count: " + databaseTimezones.size());

        List<String> missingInDatabase = realDateService.getUnsupportedTimezones();
        System.out.println("\nTimezones in java but not database count: " + missingInDatabase.size());
        for (String missingTimezone : missingInDatabase) {
            System.out.println(missingTimezone);
        }
        Assertions.assertTrue(missingInDatabase.size() > 0);
        List<String> missingInJava = new ArrayList<String>();
        for (String databaseTimezone : databaseTimezones) {
            boolean wasInJava = false;
            for (String javaTimeZone : javaTimezones) {
                if (javaTimeZone.equals(databaseTimezone)) {
                    wasInJava = true;
                    break;
                }
            }

            if (!wasInJava) {
                missingInJava.add(databaseTimezone);
            }
        }

        System.out.println("\nTimezones in datbase but not java count: " + missingInJava.size());
        for (String missingTimezone : missingInJava) {
            System.out.println(missingTimezone);
        }
        Assertions.assertEquals(1, missingInJava.size());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getLastSuccessfullyProcessedT2snapFile() throws ParseException {
        AbstractMultiPropertyCrudService mockMultiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        dateService.multiPropertyCrudService = mockMultiPropertyCrudService;

        DateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT_SIMPLE);

        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setSnapshotDt(dateFormatter.parse("12/05/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadata.setFileName("12-05-2012.T2SNAP.zip");
        fileMetadata.setRecordTypeId(RecordType.INDIVIDUAL_TRANS_RECORD_TYPE_ID);
        fileMetadata.setProcessStatusId(ProcessStatus.SUCCESSFUL);

        when(mockMultiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(anyInt(), any(String.class),
                any(Map.class))).thenReturn(fileMetadata);
        String lastProcessedFile = dateService.getLastSuccessfullyProcessedT2snapFile(1);
        Assertions.assertEquals(lastProcessedFile, "12-05-2012.T2SNAP.zip");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getLastSuccessfullyProcessedT2snapFileNoEntries() throws ParseException {
        AbstractMultiPropertyCrudService mockMultiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        dateService.multiPropertyCrudService = mockMultiPropertyCrudService;

        when(mockMultiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(anyInt(), any(String.class),
                anyMap())).thenReturn(null);
        String lastProcessedFile = dateService.getLastSuccessfullyProcessedT2snapFile(1);
        verify(mockMultiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(1,
                FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_AND_STATUS_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with("propertyId", Arrays.asList(1)).and("recordTypeId", T2SNAP_RECORD_TYPE_ID)
                        .and("processStatus", ProcessStatus.SUCCESSFUL).parameters());

        Assertions.assertNull(lastProcessedFile);
    }

    @Test
    public void getLastSuccessfullyProcessedCrsFile() throws ParseException {
        SasDbToolService mockSasDbToolService = mock(SasDbToolService.class);
        dateService.setSasDbToolService(mockSasDbToolService);
        CrudService mockGlobalCrudService = mock(CrudService.class);
        dateService.globalCrudService = mockGlobalCrudService;

        Property property = new Property();
        Client client = new Client();
        client.setCode("ZYXYZ");
        property.setId(1);
        property.setCode("ABCBA");
        property.setClient(client);

        ArrayList<Object> rowData = new ArrayList<Object>();
        // Data from SAS had a lot of trailing whitespace for some reason
        rowData.add("DALMC.20110225.0000            ");
        SasDbQueryResult queryResult = new SasDbQueryResult();
        queryResult.addRow(rowData);

        when(mockSasDbToolService.executeQuery(isA(String.class), anyInt(), isA(String.class),
                eq(DateService.SQL_CRS_PROCESSED_LAST))).thenReturn(queryResult);
        when(mockGlobalCrudService.find(Property.class, 1)).thenReturn(property);
        String lastProcessedFile = dateService.getLastSuccessfullyProcessedCrsFile(1);
        verify(mockSasDbToolService).executeQuery(property.getClient().getCode(), 1, property.getCode(),
                DateService.SQL_CRS_PROCESSED_LAST);
        verify(mockGlobalCrudService).find(Property.class, 1);

        Assertions.assertEquals("DALMC.20110225.0000", lastProcessedFile);
    }

    @Test
    public void getLastSuccessfullyProcessedCrsFileNoEntries() throws ParseException {
        SasDbToolService mockSasDbToolService = mock(SasDbToolService.class);
        dateService.setSasDbToolService(mockSasDbToolService);
        CrudService mockGlobalCrudService = mock(CrudService.class);
        dateService.globalCrudService = mockGlobalCrudService;

        Property property = new Property();
        Client client = new Client();
        client.setCode("ZYXYZ");
        property.setId(1);
        property.setCode("ABCBA");
        property.setClient(client);

        SasDbQueryResult queryResult = new SasDbQueryResult();
        queryResult.addRow(new ArrayList<Object>());

        when(mockSasDbToolService.executeQuery(isA(String.class), anyInt(), isA(String.class),
                eq(DateService.SQL_CRS_PROCESSED_LAST))).thenReturn(queryResult);
        when(mockGlobalCrudService.find(Property.class, 1)).thenReturn(property);
        String lastProcessedFile = dateService.getLastSuccessfullyProcessedCrsFile(1);
        verify(mockSasDbToolService).executeQuery(isA(String.class), anyInt(), isA(String.class),
                eq(DateService.SQL_CRS_PROCESSED_LAST));
        verify(mockGlobalCrudService).find(Property.class, 1);
        Assertions.assertNull(lastProcessedFile);
    }

    @Test
    public void testGetFunctionSpaceCaughtUpDate() {
        dateService.setCrudService(tenantCrudService());
        Date functionSpaceCaughtUpDate = dateService.getFunctionSpaceCaughtUpDate();
        assertNull(functionSpaceCaughtUpDate);
    }

    @Test
    public void testGetFunctionSpaceMostRecentPreparedDate() {
        dateService.setCrudService(tenantCrudService());
        Date functionSpaceCaughtUpDate = dateService.getFunctionSpaceMostRecentPreparedDate();
        assertNull(functionSpaceCaughtUpDate);
    }

    @Test
    public void getFunctionSpaceMaxFcstDate() throws Exception {
        dateService.setCrudService(tenantCrudService());
        LocalDate maxFcstDate = dateService
                .getFunctionSpaceMaxFcstDate();

        assertNotNull(maxFcstDate);
    }

    @Test
    public void getMaxCaughtUpDateForPropertyGroup() {
        dateService.setCrudService(tenantCrudService());
        Date caughtUpDate = dateService.getMaxCaughtUpDateForPropertyGroup(PG_HILTON_ALL);
        assertNotNull(caughtUpDate);
    }

    @Test
    public void testGetDecisionUploadEndDateForBDE() {
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("4");

        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getDecisionUploadEndDate(Constants.BDE);
        assertNotNull(date);

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();
        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(date, uploadDate);
    }

    @Test
    public void test_FormatDate_To_PropertyTimeZone() {
        when(configParamsService.getValue(getPropertyTimeZoneNodeName(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))
                .thenReturn("America/New_York");
        when(userService.getUserPreferredDateFormat()).thenReturn("dd-MMM-yyyy");

        Calendar calendar = Calendar.getInstance();

        //Not daylight
        calendar.set(2014, 11, 21, 12, 11, 11);
        TimeZone timeZone = TimeZone.getTimeZone("America/New_York");
        calendar.setTimeZone(timeZone);
        Date date = calendar.getTime();
        boolean isPropertyInDaylightSavingsNow = calendar.getTimeZone().inDaylightTime(date);

        String propertyDateString = dateService.formatDateToPropertyTimeZone(date);

        DateFormat dateFormatter = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");
        String expectedDateString =
                dateFormatter.format(DateUtil.getDateTimeByTimeZone(date, timeZone)) + " " + timeZone.getDisplayName(
                        isPropertyInDaylightSavingsNow, 0);
        assertEquals(expectedDateString, propertyDateString, "date converted to proper timezone");
        calendar.set(2014, 5, 1, 12, 11, 11);
        calendar.setTimeZone(timeZone);
        date = calendar.getTime();
        isPropertyInDaylightSavingsNow = calendar.getTimeZone().inDaylightTime(date);

        propertyDateString = dateService.formatDateToPropertyTimeZone(date);

        dateFormatter = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");
        expectedDateString =
                dateFormatter.format(DateUtil.getDateTimeByTimeZone(date, timeZone)) + " " + timeZone.getDisplayName(
                        isPropertyInDaylightSavingsNow, 0);
        assertEquals(expectedDateString, propertyDateString, "date converted to proper timezone");
    }

    @Test
    public void testGetDecisionUploadEndDateForVariableWindow() {
        System.out.println(dateService.getDayofWeek(dateService.getBusinessDate()));
        when(configParamsService.getBooleanParameterValue(
                IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        when(configParamsService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("3");
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("5");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("dayOfWeek");

        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getDecisionUploadEndDate(Constants.BDE);
        assertNotNull(date);
        verify(configParamsService).getBooleanParameterValue(
                IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        verify(configParamsService).isApplyVaribaleDecisionWindow();

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();
        assertEquals(date, caughtUpDate);
    }

    @Test
    public void testGetDecisionUploadEndDateForVariableWindow_DayOfWeek() {

        Calendar c = new GregorianCalendar();
        c.setTime(dateService.getCaughtUpDate());
        c = DateUtil.removeTimeFromCalendar(c);
        Date caughtUpDate = c.getTime();

        when(configParamsService.getBooleanParameterValue(
                IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        when(configParamsService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn("3");
        when(configParamsService.getParameterValue(
                IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("5");
        when(configParamsService.getParameterValue(
                IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn(
                dateService.getDayofWeek(dateService.getBusinessDate()));

        dateService.setCrudService(tenantCrudService());
        Date date = dateService.getDecisionUploadEndDate(Constants.BDE);
        assertNotNull(date);
//        verify(configParamsService).getBooleanParameterValue(Constants.USE_DECISION_UPLOAD_WINDOW);
        verify(configParamsService).getBooleanParameterValue(
                IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        verify(configParamsService).isApplyVaribaleDecisionWindow();

        int uploadWindow = Integer.parseInt(configParamsService.getParameterValue(
                IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) - 1;
        Date uploadDate = DateUtil.addDaysToDate(caughtUpDate, uploadWindow);
        assertEquals(date, uploadDate);
    }

    @Test
    public void testLDBHotelOpeningDateMinDateWithNonZeroSolds() {
        UniqueTotalActivityCreator totalActivityCreator = new UniqueTotalActivityCreator();
        totalActivityCreator.createTotalActivity(5, DateUtil.toDate("2015-05-21"), BigDecimal.valueOf(448),
                BigDecimal.valueOf(400));
        assertEquals(DateUtil.toDate("2015-05-21"), dateService.getLDBHotelOpeningDate());
    }

    @Test
    public void testLDBHotelOpeningDateMinDateWithZeroSolds() {
        UniqueTotalActivityCreator totalActivityCreator = new UniqueTotalActivityCreator();
        totalActivityCreator.createTotalActivity(5, DateUtil.toDate("2015-05-20"), BigDecimal.valueOf(448),
                BigDecimal.ZERO);
        totalActivityCreator.createTotalActivity(5, DateUtil.toDate("2015-05-21"), BigDecimal.valueOf(448),
                BigDecimal.valueOf(400));
        assertEquals(DateUtil.toDate("2015-05-21"), dateService.getLDBHotelOpeningDate());
    }

    @Test
    public void testLDBHotelProjectionDateMinDateWithNonZeroSolds() {
        UniqueLDBProjectionCreator ldbProjectionCreator = new UniqueLDBProjectionCreator();
        LocalDate date = new LocalDate(DateUtil.toDate("2015-01-20"));
        ldbProjectionCreator.create(date, "MS1", 1, 50);
        assertEquals(date, dateService.getFirstNonZeroProjectionDate());
    }

    @Test
    public void testLDBHotelProjectionDateMinDateWithZeroSolds() {
        UniqueLDBProjectionCreator ldbProjectionCreator = new UniqueLDBProjectionCreator();
        LocalDate date = new LocalDate(DateUtil.toDate("2017-01-27"));
        ldbProjectionCreator.create(date, "MS1", 0, 0);
        assertEquals(null, dateService.getFirstNonZeroProjectionDate());
    }

    @Test
    public void testGetSpecificDateFromRollingDate() {
        LocalDate expected = new LocalDate();
        Date actual = dateService.getSpecificDateFromRollingDate("TODAY");
        assertEquals(expected.toDate(), actual);
        expected = new LocalDate().plusDays(7);
        actual = dateService.getSpecificDateFromRollingDate("TODAY+7");
        assertEquals(expected.toDate(), actual);
        expected = new LocalDate().minusDays(15);
        actual = dateService.getSpecificDateFromRollingDate("TODAY-15");
        assertEquals(expected.toDate(), actual);
    }

    @Test
    public void shouldGetFileMetadataList() throws Exception {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<>();

        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new SimpleDateFormat("dd/MM/yyyy").parse("12/05/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(6);
        fileMetadata.setSnapshotDt(new SimpleDateFormat("dd/MM/yyyy").parse("12/02/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(7);
        fileMetadata.setSnapshotDt(new SimpleDateFormat("dd/MM/yyyy").parse("12/07/2012"));
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;

        List<FileMetadata> fileMetadatas = dateService.getFileMetadataListForPropertyIds(Arrays.asList(5, 6, 7));
        assertEquals(5, fileMetadatas.get(0).getTenantPropertyId().intValue());
        assertEquals(new SimpleDateFormat("dd/MM/yyyy").parse("12/05/2012"), fileMetadatas.get(0).getSnapshotDt());

        assertEquals(6, fileMetadatas.get(1).getTenantPropertyId().intValue());
        assertEquals(7, fileMetadatas.get(2).getTenantPropertyId().intValue());
    }

    @Test
    public void shouldReturnEmptyFileMetadataListIfNoMatchingRecordsFoundInDB() throws Exception {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(null);

        assertNotNull(dateService.getFileMetadataListForPropertyIds(Arrays.asList(5)));
        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(new ArrayList<>());

        assertNotNull(dateService.getFileMetadataListForPropertyIds(Arrays.asList(5)));
    }

    @Test
    public void shouldReturnEmptyFileMetadataListIfEmptyPropertyIDListIsReceivedAsInput() throws Exception {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(new ArrayList<>());
        assertNotNull(dateService.getFileMetadataListForPropertyIds(new ArrayList<>()));
    }

    @Test
    public void shouldGetPropertyCaughtUpDateMap() throws Exception {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<>();

        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(5);
        Date snapshotDt_5 = SIMPLE_DATE_FORMAT.parse("12/05/2012");
        fileMetadata.setSnapshotDt(snapshotDt_5);
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(6);
        Date snapshotDt_6 = SIMPLE_DATE_FORMAT.parse("12/02/2012");
        fileMetadata.setSnapshotDt(snapshotDt_6);
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(7);
        Date snapshotDt_7 = SIMPLE_DATE_FORMAT.parse("12/07/2012");
        fileMetadata.setSnapshotDt(snapshotDt_7);
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        fileMetadataList.add(fileMetadata);

        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;

        Map<Integer, Date> propertyCaughtUpDateMap = dateService.getPropertyCaughtUpDateMap(Arrays.asList(5, 6, 7));
        assertResults(propertyCaughtUpDateMap, snapshotDt_5, snapshotDt_6, snapshotDt_7);
    }

    @Test
    public void shouldReturnNullFileMetadataIfNoCaughUpDateFoundForGivenPropertyIds() throws Exception {
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<>();
        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        Map<Integer, Date> propertyCaughtUpDateMap = dateService.getPropertyCaughtUpDateMap(Arrays.asList(5, 6, 7));
        assertEquals(0, propertyCaughtUpDateMap.size());
    }

    @Test
    public void shouldGetPropertyIdCaughtUpDateMap() throws Exception {
        cookMockData()
                .fetchPropertyIdCaughtupDateMap();
        assertResults(propertyCaughtUpDateMap, snapshotDt_5, snapshotDt_6, snapshotDt_7);
    }

    private void assertResults(Map<Integer, Date> propertyCaughtUpDateMap, Date snapshotDt_5, Date snapshotDt_6,
                               Date snapshotDt_7) {
        assertNotNull(propertyCaughtUpDateMap);
        assertEquals(3, propertyCaughtUpDateMap.size());
        assertTrue(propertyCaughtUpDateMap.containsKey(5));
        assertEquals(snapshotDt_5, propertyCaughtUpDateMap.get(5));
        assertTrue(propertyCaughtUpDateMap.containsKey(6));
        assertEquals(snapshotDt_6, propertyCaughtUpDateMap.get(6));
        assertTrue(propertyCaughtUpDateMap.containsKey(7));
        assertEquals(snapshotDt_7, propertyCaughtUpDateMap.get(7));
    }

    private DateServiceTest fetchPropertyIdCaughtupDateMap() {
        propertyCaughtUpDateMap = dateService.getPropertyIdCaughtUpDateMap(Arrays.asList(5, 6, 7));
        return this;
    }

    private DateServiceTest cookMockData() throws ParseException {
        snapshotDt_5 = SIMPLE_DATE_FORMAT.parse("12/05/2012");
        snapshotDt_6 = SIMPLE_DATE_FORMAT.parse("12/02/2012");
        snapshotDt_7 = SIMPLE_DATE_FORMAT.parse("12/07/2012");
        AbstractMultiPropertyCrudService mockMultiCrudSvc = mock(AbstractMultiPropertyCrudService.class);
        List<FileMetadata> fileMetadataList = new ArrayList<>();

        fileMetadataList.add(getFileMetadata(5, snapshotDt_5));
        fileMetadataList.add(getFileMetadata(6, snapshotDt_6));
        fileMetadataList.add(getFileMetadata(7, snapshotDt_7));
        when(mockMultiCrudSvc.findByNamedQuerySingleResult(anyList(), any(String.class), anyMap()))
                .thenReturn(fileMetadataList);
        dateService.multiPropertyCrudService = mockMultiCrudSvc;
        return this;
    }

    private FileMetadata getFileMetadata(int propertyId, Date snapshotDt) {
        FileMetadata fileMetadata = domainStubPersister.getFileMetadata();
        fileMetadata.setTenantPropertyId(propertyId);
        fileMetadata.setSnapshotDt(snapshotDt);
        fileMetadata.setSnapshotTm(DateUtil.removeTimeFromDate(fileMetadata.getSnapshotDt()));
        return fileMetadata;
    }

    @Test
    public void testGetSystemDates() {
        var dateServiceSpy = Mockito.spy(dateService);
        Date snapshotDate = new Date(2018, 5, 5);
        Date createDate = new Date(2017, 1, 4);

        SystemDateDto webRateDto = new SystemDateDto();
        SystemDateDto unqualifiedDto = new SystemDateDto();
        SystemDateDto t2SnapDto = new SystemDateDto();
        SystemDateDto rraDto = new SystemDateDto();

        List<SystemDateDto> systemDateDtos = new ArrayList<>();
        systemDateDtos.add(webRateDto);
        systemDateDtos.add(unqualifiedDto);
        systemDateDtos.add(t2SnapDto);
        systemDateDtos.add(rraDto);

        when(dateServiceSpy.getSystemDateDtos()).thenReturn(systemDateDtos);
        when(dateServiceSpy.configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RRAENABLED)).thenReturn(true);
        when(dateServiceSpy.configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)).thenReturn(false);
        when(dateServiceSpy.configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(dateServiceSpy.configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(true);
        when(demand360Service.getLatestCaptureDate()).thenReturn(LocalDate.fromDateFields(createDate));
        when(dateServiceSpy.configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        when(dateServiceSpy.configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LONG_TERM_BDE_PROCESSING_ENABLED)).thenReturn(true);
        LocalDateTime lastExtendedEvaluation = LocalDateTime.parse("2023-05-11T00:00");
        when(extendedEvaluationService.getLastExtendedEvaluation()).thenReturn(lastExtendedEvaluation);
        LocalDateTime lastLimitedDataBuildDate = LocalDateTime.of(2024, 1, 1, 10, 10, 15);
        when(ldbService.getLastLimitedDataBuildDate()).thenReturn(lastLimitedDataBuildDate);

        webRateDto.setRecordTypeId(WEBRATE_RECORD_TYPE_ID);
        webRateDto.setSnapshotDate(snapshotDate);
        webRateDto.setCreateDate(createDate);

        unqualifiedDto.setRecordTypeId(UNQUALIFIED_RATE_RECORD_TYPE_ID);
        unqualifiedDto.setSnapshotDate(snapshotDate);
        unqualifiedDto.setCreateDate(createDate);

        t2SnapDto.setRecordTypeId(T2SNAP_RECORD_TYPE_ID);
        t2SnapDto.setSnapshotDate(snapshotDate);
        t2SnapDto.setCreateDate(createDate);

        rraDto.setRecordTypeId(RRA_RECORD_TYPE_ID);
        rraDto.setSnapshotDate(snapshotDate);
        rraDto.setCreateDate(createDate);

        SystemDates systemDates = dateServiceSpy.getSystemDates();
        assertEquals(DateUtil.convertLocalDateTimeToUtilDate(lastExtendedEvaluation), systemDates.getLastExtendedEvaluation(), "Wrong Extended Evaluation date");
        assertEquals(createDate, systemDates.getLastRateShoppingExtract(), "Wrong web rate processed date");
        assertEquals(createDate, systemDates.getUnqualifiedProcessed(), "Wrong unqualified rate caught up date");
        assertEquals(createDate, systemDates.getTransactionDataPopulation(), "Wrong transaction data population date");
        assertEquals(snapshotDate, systemDates.getTransactionSystem(), "Wrong transaction caught up date");
        assertEquals(createDate, systemDates.getLastReputationExtract(), "Wrong rra processed date");
        assertEquals(dateServiceSpy.getForecastedDate(), systemDates.getForecast(), "Wrong forecasted date");
        assertEquals(dateServiceSpy.getForecastedDate(), systemDates.getControl(), "Wrong decision date");
        assertEquals(JavaLocalDateUtils.toDate(lastLimitedDataBuildDate), systemDates.getLastLimitedDataBuildDate());
        assertTrue(systemDates.isLimitedDataBuildEnabled(), "limited data build should be enabled");
        assertTrue(systemDates.isLastExtendedEvaluationEnabled(), "last Extended evaluation should be enabled");
        assertEquals(true, systemDates.isLastReputationExtractEnabled(), "last reputation extract should be enabled");
        assertEquals(true, systemDates.isUnqualifiedProcessedEnabled(), "unqualified process should be enabled");
        assertEquals(true, systemDates.isFunctionSpaceEnabled(), "function space should be enabled");
        assertEquals(true, systemDates.isDemand360PopulationEnabled(), "demand360 should be enabled");
        assertEquals(createDate, systemDates.getLastDemand360PopulationDate(), "Wrong demand360 last population date");
    }

    @Test
    public void testUnqualifiedRateNull() {
        when(configParamsService.getParameterValue(
                FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED)).thenReturn(false);
        var dateServiceSpy = Mockito.spy(dateService);
        when(dateServiceSpy.getSystemDateDtos()).thenReturn(null);
        Date currentDate = new Date(2018, 3, 15);
        when(dateServiceSpy.getCurrentDate()).thenReturn(currentDate);
        SystemDates systemDates = dateServiceSpy.getSystemDates();
        assertEquals(currentDate, systemDates.getUnqualifiedProcessed(), "wrong unqualified rate caught up date");
    }

    @Test
    public void testGetLastCompletedFunctionSpaceDataLoadJobDateForAmadeus() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("jobName", DateService.JOB_NAME);
        parameters.put("jobStatus", DateService.JOB_STATUS);
        parameters.put("propertyId", 5);
        Date newdate = DateUtil.removeMillisFromDate(new Date());
        when(jobCrudService.findByNativeQuerySingleResult(DateService.LAST_COMPLETED_FUNCTION_SPACE_DATA_LOAD_JOB,
                parameters)).thenReturn(newdate);
        Date date = dateService.getLastCompletedFunctionSpaceDataLoadJobDateForAmadeus();
        assertNotNull(date);
        assertEquals(date, newdate, "function space data load job completion date is incorrect. ");
    }

    @Test
    public void testGetLastCompletedFunctionSpaceDataLoadJobDateForAmadeusNullCheck() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("jobName", DateService.JOB_NAME);
        parameters.put("jobStatus", DateService.JOB_STATUS);
        parameters.put("propertyId", 0);
        Date newdate = DateUtil.removeMillisFromDate(new Date());
        when(jobCrudService.findByNativeQuerySingleResult(DateService.LAST_COMPLETED_FUNCTION_SPACE_DATA_LOAD_JOB,
                parameters)).thenReturn(newdate);
        Date date = dateService.getLastCompletedFunctionSpaceDataLoadJobDateForAmadeus();
        assertNull(date);
    }

    @Nested
    class lastSnapDateReturnsDefault {

        @BeforeEach
        void setUp() {
            when(propertyGroupService.getPropertyContextAsList()).thenReturn(List.of(23));
            MinDateDto min = new MinDateDto();
            min.setDate(new Date());
            when(multiPropertyCrudService
                    .findByNativeQueryReduceAcrossPropertiesSingleResult(anyList(), any(), anyMap(), any())).thenReturn(
                    min);


        }

        @Test
        void getLastSnapDateReturnsCaughtUpDate() {
            CrudService mockGlobalCrudService = mock(CrudService.class);
            SasDbToolService sasDbToolService = mock(SasDbToolService.class);
            dateService.globalCrudService = mockGlobalCrudService;
            dateService.sasDbToolService = sasDbToolService;
            Property property = new Property();
            Client client = new Client(23);
            client.setCode("TEST");
            property.setClient(client);
            property.setCode("testProperty");
            property.setId(23);
            when(mockGlobalCrudService.find(eq(Property.class), anyInt())).thenReturn(property);
            SasDbQueryResult result = new SasDbQueryResult();
            when(sasDbToolService.executeQuery(anyString(), anyInt(), any(), any())).thenReturn(result);
            assertNotNull(dateService.getLastSnapStartDate(23));
        }

        @Test
        void getLastSnapDateReturnsDefaultCaughtUpDateIfEmptyResult() {
            CrudService mockGlobalCrudService = mock(CrudService.class);
            SasDbToolService sasDbToolService = mock(SasDbToolService.class);
            dateService.globalCrudService = mockGlobalCrudService;
            dateService.sasDbToolService = sasDbToolService;
            Property property = new Property();
            Client client = new Client(23);
            client.setCode("TEST");
            property.setClient(client);
            property.setCode("testProperty");
            property.setId(23);
            when(mockGlobalCrudService.find(eq(Property.class), anyInt())).thenReturn(property);
            SasDbQueryResult result = new SasDbQueryResult();
            result.setData(new ArrayList<>());
            when(sasDbToolService.executeQuery(anyString(), anyInt(), any(), any())).thenReturn(result);
            assertNotNull(dateService.getLastSnapStartDate(23));
        }

        @Test
        void getLastSnapDateReturnsCaughtUpDateIfEmptyResult_2() {
            CrudService mockGlobalCrudService = mock(CrudService.class);
            SasDbToolService sasDbToolService = mock(SasDbToolService.class);
            dateService.globalCrudService = mockGlobalCrudService;
            dateService.sasDbToolService = sasDbToolService;
            Property property = new Property();
            Client client = new Client(23);
            client.setCode("TEST");
            property.setClient(client);
            property.setCode("testProperty");
            property.setId(23);
            when(mockGlobalCrudService.find(eq(Property.class), anyInt())).thenReturn(property);
            SasDbQueryResult result = new SasDbQueryResult();
            ArrayList<Object> objects = new ArrayList<>();
            result.setData(new ArrayList<>(List.of(objects)));
            when(sasDbToolService.executeQuery(anyString(), anyInt(), any(), any())).thenReturn(result);
            assertNotNull(dateService.getLastSnapStartDate(23));
        }
    }

    @Test
    void getLTBDEWindowOffset() {
        when(configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1034);
        assertEquals(1034 - 1 + DAYS_IN_A_MONTH, dateService.getLTBDEWindowOffset());
        assertEquals(32, DAYS_IN_A_MONTH);
        verify(configParamsService).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
    }

    @Test
    void getForecastWindowEndDateLTBDE() {
        var dateServiceSpy = Mockito.spy(dateService);
        when(configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1094);
        dateServiceSpy.setCrudService(tenantCrudService());
        Date date = dateServiceSpy.getForecastWindowEndDateLTBDE();
        assertNotNull(date);
        verify(configParamsService).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
        verify(dateServiceSpy).getLTBDEWindowOffset();
    }

    @Test
    void getOptimizationWindowEndDateLTBDE() {
        var dateServiceSpy = Mockito.spy(dateService);
        when(configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1094);
        dateServiceSpy.setCrudService(tenantCrudService());
        Date date = dateServiceSpy.getOptimizationWindowEndDateLTBDE();
        assertNotNull(date);
        verify(configParamsService).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
        verify(dateServiceSpy).getLTBDEWindowOffset();
    }
}
