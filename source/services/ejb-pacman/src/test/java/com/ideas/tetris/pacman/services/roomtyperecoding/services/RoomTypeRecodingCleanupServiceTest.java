package com.ideas.tetris.pacman.services.roomtyperecoding.services;

import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roomtyperecoding.entity.RTRecodingRoomTypesDto;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyMapOf;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RoomTypeRecodingCleanupServiceTest {
    private static final String CLIENT_CODE = "BSTN";
    private static final String PROPERTY_H1 = "H1";
    private static final String PROPERTY_H2 = "H2";
    private static final String PROPERTY_H3 = "H3";
    private static final String PROPERTY_H4 = "H4";
    private static final String RT1 = "RT1", RT2 = "RT2", RT4 = "RT4";

    @Mock
    private HiltonRoomTypeRecodingCleanupService hiltonRoomTypeRecodingCleanupService;
    @Mock
    private PropertyService propertyService;
    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Mock
    private OperaRoomTypeRecodingService operaRoomTypeRecodingService;
    @Mock
    private NGIRoomTypeRecodingService ngiRoomTypeRecodingService;
    @Mock
    private ExternalSystemHelper externalSystemHelper;
    @Spy
    @InjectMocks
    private RoomTypeRecodingCleanupService roomTypeRecodingCleanupService;

    @BeforeEach
    public void setUp() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
    }

    @Test
    public void shouldFetchMissingRoomTypesForClient() {
        Property p1 = createProperty(1, PROPERTY_H1);
        Property p2 = createProperty(2, PROPERTY_H2);
        Property p3 = createProperty(3, PROPERTY_H3);
        Property p4 = createProperty(4, PROPERTY_H4);

        List<Property> activeProperties = Arrays.asList(p1, p2, p3, p4);
        List<Integer> operaPropertyIds = Arrays.asList(p1.getId(), p4.getId());
        List<Integer> ngiPropertyIds = Arrays.asList(p2.getId(), p3.getId());

        when(propertyService.getActivePropertiesForClientCode(CLIENT_CODE)).thenReturn(activeProperties);
        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p1.getCode())).thenReturn(ReservationSystem.OPERA);
        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p2.getCode())).thenReturn(ReservationSystem.NGI);
        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p3.getCode())).thenReturn(ReservationSystem.NGI);
        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p4.getCode())).thenReturn(ReservationSystem.OPERA);

        RTRecodingRoomTypesDto p1Rt1Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT1, 50);
        RTRecodingRoomTypesDto p1Rt2Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT2, 50);
        RTRecodingRoomTypesDto p4Rt2Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT2, 50);
        RTRecodingRoomTypesDto p3Rt2Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT2, 50);
        RTRecodingRoomTypesDto p3Rt4Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT4, 50);

        List<List<RTRecodingRoomTypesDto>> roomTypesInPacman = Arrays.asList(Arrays.asList(p1Rt1Pacman, p1Rt2Pacman), Collections.singletonList(p4Rt2Pacman));
        when(multiPropertyCrudService.findByNamedQuery(operaPropertyIds, AccomType.ALL_DISPLAY_STATUS_ACTIVE_ROOM_TYPES, Collections.emptyMap())).thenReturn(roomTypesInPacman);

        RTRecodingRoomTypesDto p2Rt2Pacman = new RTRecodingRoomTypesDto(p2.getId(), RT2, 50);
        RTRecodingRoomTypesDto p3Rt1Pacman = new RTRecodingRoomTypesDto(p3.getId(), RT1, 50);
        List<List<RTRecodingRoomTypesDto>> mappedRTsInNGI = Arrays.asList(Collections.singletonList(p2Rt2Pacman), Collections.singletonList(p3Rt1Pacman));
        when(multiPropertyCrudService.findByNamedQuery(ngiPropertyIds, AccomType.ALL_DISPLAY_STATUS_ACTIVE_ROOM_TYPES, Collections.emptyMap())).thenReturn(mappedRTsInNGI);

        Map<String, Map<String, Integer>> roomTypesInNGI = new HashMap<String, Map<String, Integer>>() {{
            put(PROPERTY_H2, Collections.emptyMap());
            put(PROPERTY_H3, new HashMap<String, Integer>() {{
                put(RT2, 25);
                put(RT4, 40);
            }});
        }};
        when(ngiRoomTypeRecodingService.getRoomTypesInLatestExtractByPropertyCodes(CLIENT_CODE, Arrays.asList(PROPERTY_H2, PROPERTY_H3))).thenReturn(roomTypesInNGI);

        RTRecodingRoomTypesDto p1Rt2External = new RTRecodingRoomTypesDto(p1.getId(), RT2, 50);
        RTRecodingRoomTypesDto p4Rt2External = new RTRecodingRoomTypesDto(p4.getId(), RT2, 50);
        RTRecodingRoomTypesDto p4Rt4External = new RTRecodingRoomTypesDto(p4.getId(), RT4, 50);
        Map<Integer, List<RTRecodingRoomTypesDto>> roomTypesInExternalSystem = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Collections.singletonList(p1Rt2External));
            put(p4.getId(), Arrays.asList(p4Rt2External, p4Rt4External));
        }};

        doReturn(roomTypesInExternalSystem).when(operaRoomTypeRecodingService).getDistinctRoomTypesAvailableForProperties(operaPropertyIds);

        Map<Integer, List<RTRecodingRoomTypesDto>> expectedMissingRTs = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Collections.singletonList(p1Rt1Pacman));
            put(p2.getId(), Collections.singletonList(p2Rt2Pacman));
            put(p3.getId(), Collections.singletonList(p3Rt1Pacman));
            put(p4.getId(), Collections.emptyList());
        }};

        Map<Integer, List<RTRecodingRoomTypesDto>> actualMissingRTs = roomTypeRecodingCleanupService.fetchMissingRoomTypesForClient();
        assertEquals(expectedMissingRTs.size(), actualMissingRTs.size());

        assertEquals(expectedMissingRTs.get(p1.getId()).size(), actualMissingRTs.get(p1.getId()).size());
        assertEquals(RT1, actualMissingRTs.get(p1.getId()).get(0).getRoomTypeCode());

        assertEquals(expectedMissingRTs.get(p2.getId()).size(), actualMissingRTs.get(p2.getId()).size());

        assertEquals(expectedMissingRTs.get(p3.getId()).size(), actualMissingRTs.get(p3.getId()).size());
        assertEquals(RT1, actualMissingRTs.get(p3.getId()).get(0).getRoomTypeCode());

        assertTrue(actualMissingRTs.get(p4.getId()).isEmpty());
    }

    @Test
    public void shouldDeactivateRoomTypesForClient() {
        Property p1 = createProperty(1, PROPERTY_H1);
        Property p4 = createProperty(4, PROPERTY_H4);

        RTRecodingRoomTypesDto p1Rt1Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT1, 50);

        Map<Integer, List<RTRecodingRoomTypesDto>> expectedMissingRTsByProperty = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Collections.singletonList(p1Rt1Pacman));
            put(p4.getId(), Collections.emptyList());
        }};

        AccomType rt1AccomType = createAccomType(RT1, 30);

        doReturn(expectedMissingRTsByProperty).when(roomTypeRecodingCleanupService).fetchMissingRoomTypesForClient();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(p1.getId(), AccomType.ALL_BY_CODES, QueryParameter.with("accomTypeList", Collections.singletonList(RT1))
                .and(PROPERTY_ID, p1.getId()).parameters())).thenReturn(Collections.singletonList(rt1AccomType));

        Map<Integer, Integer> deactivationCountByPropertyId = roomTypeRecodingCleanupService.deactivateMissingRoomTypesForClient();
        assertEquals(2, deactivationCountByPropertyId.size());
        assertEquals(Integer.valueOf(1), deactivationCountByPropertyId.get(p1.getId()));
        assertEquals(Integer.valueOf(0), deactivationCountByPropertyId.get(p4.getId()));

        ArgumentCaptor<AccomType> accomTypeArgumentCaptor = ArgumentCaptor.forClass(AccomType.class);
        verify(multiPropertyCrudService, times(1)).save(eq(p1.getId()), accomTypeArgumentCaptor.capture());
        assertEquals(Integer.valueOf(0), accomTypeArgumentCaptor.getValue().getAccomTypeCapacity());
        assertEquals(Integer.valueOf(2), accomTypeArgumentCaptor.getValue().getDisplayStatusId());
    }

    @Test
    public void shouldDeactivateRoomTypesForProperties() {
        Property p1 = createProperty(1, PROPERTY_H1);
        p1.setStatus(Status.ACTIVE);
        Property p4 = createProperty(4, PROPERTY_H4);
        p4.setStatus(Status.ACTIVE);

        List<Integer> propertyIds = Arrays.asList(p1.getId(), p4.getId());
        List<Property> properties = Arrays.asList(p1, p4);

        RTRecodingRoomTypesDto p1Rt1Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT1, 50);
        RTRecodingRoomTypesDto p1Rt2Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT2, 50);
        RTRecodingRoomTypesDto p4Rt2Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT2, 50);
        RTRecodingRoomTypesDto p4Rt4Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT4, 50);

        Map<Integer, List<RTRecodingRoomTypesDto>> expectedMissingRTsByProperty = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Arrays.asList(p1Rt1Pacman, p1Rt2Pacman));
            put(p4.getId(), Collections.singletonList(p4Rt2Pacman));
        }};

        AccomType p1Rt1AccomType = createAccomType(RT1, 30);
        AccomType p1Rt2AccomType = createAccomType(RT2, 40);
        AccomType p4Rt4AccomType = createAccomType(RT4, 25);

        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p1.getCode())).thenReturn(ReservationSystem.OPERA);
        when(externalSystemHelper.getExternalSystem(CLIENT_CODE, p4.getCode())).thenReturn(ReservationSystem.NGI);

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(p1.getId(), AccomType.ALL_BY_CODES, QueryParameter.with("accomTypeList", Arrays.asList(RT1, RT2))
                .and(PROPERTY_ID, p1.getId()).parameters())).thenReturn(Arrays.asList(p1Rt1AccomType, p1Rt2AccomType));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(p4.getId(), AccomType.ALL_BY_CODES, QueryParameter.with("accomTypeList", Collections.singletonList(RT4))
                .and(PROPERTY_ID, p4.getId()).parameters())).thenReturn(Collections.singletonList(p4Rt4AccomType));
        when(propertyService.findPropertyByIds(propertyIds)).thenReturn(properties);
        doReturn(expectedMissingRTsByProperty).when(roomTypeRecodingCleanupService).getMissingRoomTypesInOpera(Collections.singletonList(p1.getId()));

        List<List<RTRecodingRoomTypesDto>> roomTypesInPacman = Collections.singletonList(Collections.singletonList(p4Rt4Pacman));
        when(multiPropertyCrudService.findByNamedQuery(Collections.singletonList(p4.getId()), AccomType.ALL_DISPLAY_STATUS_ACTIVE_ROOM_TYPES, Collections.emptyMap())).thenReturn(roomTypesInPacman);

        Map<String, Map<String, Integer>> roomTypesInNGI = new HashMap<String, Map<String, Integer>>() {{
            put(PROPERTY_H4, new HashMap<String, Integer>() {{
                put(RT2, 25);
            }});
        }};
        when(ngiRoomTypeRecodingService.getRoomTypesInLatestExtractByPropertyCodes(CLIENT_CODE, Collections.singletonList(PROPERTY_H4))).thenReturn(roomTypesInNGI);

        Map<Integer, Integer> deactivationCountByProperty = roomTypeRecodingCleanupService.deactivateMissingRoomTypesForProperties(propertyIds);
        assertEquals(2, deactivationCountByProperty.size());
        assertEquals(Integer.valueOf(2), deactivationCountByProperty.get(p1.getId()));
        assertEquals(Integer.valueOf(1), deactivationCountByProperty.get(p4.getId()));

        ArgumentCaptor<AccomType> p1AccomTypeArgumentCaptor = ArgumentCaptor.forClass(AccomType.class);
        verify(multiPropertyCrudService, times(2)).save(eq(p1.getId()), p1AccomTypeArgumentCaptor.capture());
        assertTrue(p1AccomTypeArgumentCaptor.getAllValues().stream().allMatch(at -> at.getAccomTypeCapacity() == 0));
        assertTrue(p1AccomTypeArgumentCaptor.getAllValues().stream().allMatch(at -> at.getDisplayStatusId() == 2));

        ArgumentCaptor<AccomType> p4AccomTypeArgumentCaptor = ArgumentCaptor.forClass(AccomType.class);
        verify(multiPropertyCrudService, times(1)).save(eq(p4.getId()), p4AccomTypeArgumentCaptor.capture());
        assertEquals(Integer.valueOf(0), p4AccomTypeArgumentCaptor.getValue().getAccomTypeCapacity());
        assertEquals(Integer.valueOf(2), p4AccomTypeArgumentCaptor.getValue().getDisplayStatusId());
    }

    @Test
    public void shouldFetchUsagesOfMissingRTsForProperties() {
        Property p1 = createProperty(1, PROPERTY_H1);
        Property p4 = createProperty(4, PROPERTY_H4);

        List<Integer> propertyIds = Arrays.asList(p1.getId(), p4.getId());
        RTRecodingRoomTypesDto p1Rt1Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT1, 50);
        RTRecodingRoomTypesDto p4Rt1Pacman = new RTRecodingRoomTypesDto(p4.getId(), RT1, 50);

        Map<Integer, List<RTRecodingRoomTypesDto>> expectedMissingRTsByProperty = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Collections.singletonList(p1Rt1Pacman));
            put(p4.getId(), Collections.singletonList(p4Rt1Pacman));
        }};

        List<Object[]> counts = new ArrayList<>();
        counts.add(new Object[]{1});

        when(multiPropertyCrudService.findByNativeQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class))).thenReturn(counts);
        doReturn(expectedMissingRTsByProperty).when(roomTypeRecodingCleanupService).fetchMissingRoomTypesForProperties(propertyIds);

        roomTypeRecodingCleanupService.fetchUsagesOfMissingRTsForProperties(propertyIds);

        verify(multiPropertyCrudService, times(2)).findByNamedQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class));
        verify(multiPropertyCrudService, times(20)).findByNativeQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class));
    }

    @Test
    public void shouldFetchUsagesOfMissingRTsForClient() {
        Property p1 = createProperty(1, PROPERTY_H1);
        RTRecodingRoomTypesDto p1Rt1Pacman = new RTRecodingRoomTypesDto(p1.getId(), RT1, 50);
        Map<Integer, List<RTRecodingRoomTypesDto>> expectedMissingRTsByProperty = new HashMap<Integer, List<RTRecodingRoomTypesDto>>() {{
            put(p1.getId(), Collections.singletonList(p1Rt1Pacman));
        }};

        List<Object[]> counts = new ArrayList<>();
        counts.add(new Object[]{1});

        when(multiPropertyCrudService.findByNativeQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class))).thenReturn(counts);
        doReturn(expectedMissingRTsByProperty).when(roomTypeRecodingCleanupService).fetchMissingRoomTypesForClient();

        roomTypeRecodingCleanupService.fetchUsagesOfMissingRTsForClient();

        verify(multiPropertyCrudService, times(1)).findByNamedQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class));
        verify(multiPropertyCrudService, times(10)).findByNativeQueryForSingleProperty(anyInt(), anyString(), anyMapOf(String.class, Object.class));
    }

    @Test
    void getMissingForHilton() {
        List<Property> properties = Stream.of(TestProperty.H1)
                .map(prop -> {
                    Property property = new Property();
                    property.setId(prop.getId());
                    return property;
                })
                .collect(Collectors.toList());

        roomTypeRecodingCleanupService = spy(roomTypeRecodingCleanupService);
        Map<Integer, List<RTRecodingRoomTypesDto>> pacmanRooms = Collections.singletonMap(5, Collections.emptyList());
        doReturn(pacmanRooms).when(roomTypeRecodingCleanupService).getDistinctRoomTypesAvailableInPacmanForProperties(Collections.singletonList(5));
        Map<Integer, List<RTRecodingRoomTypesDto>> inboundRooms = Collections.singletonMap(5, Collections.emptyList());
        when(hiltonRoomTypeRecodingCleanupService.getActiveRoomTypesInInboundData(properties)).thenReturn(inboundRooms);

        assertEquals(Collections.singletonMap(5, Collections.emptyList()), roomTypeRecodingCleanupService.getMissingRoomTypesInHiltonLikeIntegration(properties));
    }

    private Property createProperty(Integer id, String code) {
        Property property = new Property(id);
        property.setCode(code);
        return property;
    }

    private AccomType createAccomType(String code, Integer capacity) {
        AccomType p1Rt1AccomType = new AccomType();
        p1Rt1AccomType.setAccomTypeCode(code);
        p1Rt1AccomType.setAccomTypeCapacity(capacity);
        return p1Rt1AccomType;
    }
}
