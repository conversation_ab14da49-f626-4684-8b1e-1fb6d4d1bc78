package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class OptimizationCPLowestDecisionCheckQueriesTest extends AbstractG3JupiterTest {

    private static int masterClassBaseRoomTypeId = 10;

    private static int nonMasterClassBaseRoomTypeId = 12;

    private static TestProperty testProperty = TestProperty.H2;

    private static int propertyId = testProperty.getId();

    private static final String master_base_room_type = "STE";

    private static final String non_master_base_room_type = "D";

    @Mock
    private OptimizationDecisionAlertGenerationStep optimizationDecisionAlertGenerationStep;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(testProperty);
    }

    @Test
    public void functionShouldReturnBaseRoomTypeIdOfMasterClass() {
        String getBaseAccomTypeIdOfMasterClass = "select  [dbo].[ufn_get_base_room_type_of_master_class] ()";
        int baseRoomTypeId = tenantCrudService().findByNativeQuerySingleResult(getBaseAccomTypeIdOfMasterClass, null);
        assertEquals(masterClassBaseRoomTypeId, baseRoomTypeId);
    }

    @Test
    public void functionShouldReturnDefaultFloorRates() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 31);
        QueryParameter queryParameter = QueryParameter.with("startDate", startDate).and("endDate", endDate);
        String getDefaultFloorRates = "select * from dbo.ufn_get_cp_default_floor_rate_for_master_class_base_room_type(:startDate, :endDate)";
        updateCP_Cfg_Base_AT(masterClassBaseRoomTypeId, 100.00, 100.00, 100.00, 100.00, 100.00, 100.00, 100.00);
        updateCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, 150.00, 150.00, 150.00, 150.00, 150.00, 150.00, 150.00);
        // when
        List<Object[]> defaultFloorRates = tenantCrudService().findByNativeQuery(getDefaultFloorRates, queryParameter.parameters());
        // then
        assertEquals(31, defaultFloorRates.size());
        defaultFloorRates.forEach(record -> assertEquals(new BigDecimal("100.00"), record[1]));
    }

    @Test
    public void functionShouldReturnDefaultFloorRatesInclusiveOfDefaultTax() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 7);

        updateCP_Cfg_Base_AT(masterClassBaseRoomTypeId, 110.00, 90.00, 90.00, 100.00, 100.00, 110.00, 120.00);
        updateCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, 150.00, 150.00, 150.00, 150.00, 150.00, 150.00, 150.00);

        String updateDefaultTax = "UPDATE Tax SET Room_Tax_Rate = 9.0 WHERE Start_Date is null and End_Date is null";
        tenantCrudService().executeUpdateByNativeQuery(updateDefaultTax);

        String getDefaultFloorRates = "select * from dbo.ufn_get_cp_default_floor_rate_for_master_class_base_room_type(:startDate, :endDate)";
        QueryParameter queryParameter = QueryParameter.with("startDate", startDate).and("endDate", endDate);
        // when
        List<Object[]> defaultFloorRates = tenantCrudService().findByNativeQuery(getDefaultFloorRates, queryParameter.parameters());
        // then
        assertEquals(7, defaultFloorRates.size());

        IntStream.rangeClosed(0, 6).forEach(index -> assertEquals(startDate.plusDays(index).toDate(), defaultFloorRates.get(index)[0]));

        assertEquals(new BigDecimal("119.90"), defaultFloorRates.get(0)[1]);
        assertEquals(new BigDecimal("98.10"), defaultFloorRates.get(1)[1]);
        assertEquals(new BigDecimal("98.10"), defaultFloorRates.get(2)[1]);
        assertEquals(new BigDecimal("109.00"), defaultFloorRates.get(3)[1]);
        assertEquals(new BigDecimal("109.00"), defaultFloorRates.get(4)[1]);
        assertEquals(new BigDecimal("119.90"), defaultFloorRates.get(5)[1]);
        assertEquals(new BigDecimal("130.80"), defaultFloorRates.get(6)[1]);
    }

    @Test
    public void functionShouldReturnSeasonsFloorRates() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 2);
        insertIntoCP_Cfg_Base_AT(masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");
        insertIntoCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");
        String getSeasonsFloorRates = "select * from dbo.ufn_get_cp_seasons_floor_rate_for_master_class_base_room_type(:startDate, :endDate)";
        // when
        List seasonsFloorRate = tenantCrudService().findByNativeQuery(getSeasonsFloorRates, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        // then
        assertEquals(2, seasonsFloorRate.size());
        Object[] sundayFloorRate = (Object[]) seasonsFloorRate.get(0);
        Object[] mondayFloorRate = (Object[]) seasonsFloorRate.get(1);
        assertEquals(startDate.toDate(), sundayFloorRate[0]);
        assertEquals(endDate.toDate(), mondayFloorRate[0]);
        assertEquals(new BigDecimal("100.00"), sundayFloorRate[1]);
        assertEquals(new BigDecimal("101.00"), mondayFloorRate[1]);
        assertEquals(1, sundayFloorRate[2]);
        assertEquals(1, mondayFloorRate[2]);
    }

    @Test
    public void functionShouldReturnSeasonsFloorRatesInclusiveOfSeasonalTax() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 4);

        insertIntoCP_Cfg_Base_AT(masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");
        insertIntoCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");

        final Tax tax1 = buildTax(5.00, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 12), "S1");
        final Tax tax2 = buildTax(10.00, new LocalDate(2012, 1, 3), new LocalDate(2012, 1, 4), "S2");

        tenantCrudService().save(Arrays.asList(tax1, tax2));

        String getSeasonsFloorRates = "select * from dbo.ufn_get_cp_seasons_floor_rate_for_master_class_base_room_type(:startDate, :endDate)";
        // when
        List<Object[]> seasonsFloorRates = tenantCrudService().findByNativeQuery(getSeasonsFloorRates, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        // then
        assertEquals(4, seasonsFloorRates.size());

        IntStream.rangeClosed(0, 3).forEach(index -> assertEquals(startDate.plusDays(index).toDate(), seasonsFloorRates.get(index)[0]));

        assertEquals(new BigDecimal("105.00"), seasonsFloorRates.get(0)[1]);
        assertEquals(new BigDecimal("106.05"), seasonsFloorRates.get(1)[1]);
        assertEquals(new BigDecimal("107.10"), seasonsFloorRates.get(2)[1]);
        assertEquals(new BigDecimal("108.15"), seasonsFloorRates.get(3)[1]);
    }

    @Test
    public void functionShouldReturnSeasonsFloorRatesInclusiveOfDefaultTaxWhenNoSeasonTax() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 4);

        insertIntoCP_Cfg_Base_AT(masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");
        insertIntoCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 10), 100, 200, 101, 201, 102, 202, 103, 203, 104, 204, 105, 205, 106, 206, 1, "Test Season");

        String updateDefaultTax = "UPDATE Tax SET Room_Tax_Rate = 10.0 WHERE Start_Date is null and End_Date is null";
        tenantCrudService().executeUpdateByNativeQuery(updateDefaultTax);

        final Tax tax1 = buildTax(5.00, new LocalDate(2012, 1, 1), new LocalDate(2012, 1, 2), "S1");
        final Tax tax2 = buildTax(10.00, new LocalDate(2012, 1, 3), new LocalDate(2012, 1, 4), "S2");

        tenantCrudService().save(Arrays.asList(tax1, tax2));

        String getSeasonsFloorRates = "select * from dbo.ufn_get_cp_seasons_floor_rate_for_master_class_base_room_type(:startDate, :endDate)";
        // when
        List<Object[]> seasonsFloorRates = tenantCrudService().findByNativeQuery(getSeasonsFloorRates, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        // then
        assertEquals(4, seasonsFloorRates.size());

        IntStream.rangeClosed(0, 3).forEach(index -> assertEquals(startDate.plusDays(index).toDate(), seasonsFloorRates.get(index)[0]));

        assertEquals(new BigDecimal("110.00"), seasonsFloorRates.get(0)[1]);
        assertEquals(new BigDecimal("111.10"), seasonsFloorRates.get(1)[1]);
        assertEquals(new BigDecimal("112.20"), seasonsFloorRates.get(2)[1]);
        assertEquals(new BigDecimal("113.30"), seasonsFloorRates.get(3)[1]);
    }

    @Test
    public void functionShouldReturnOnlyFloorOverriddenRates() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 5);
        // for master class base room type
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), -1, 5, "NONE", "FLOOR", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), 2, 5, "NONE", "FLOOR", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 2), -1, 5, "NONE", "USER", 110.00000, 400.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 3), -1, 5, "NONE", "USER", 100.00000, 700.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "NONE", "FLOORANDCEIL", 208.74000, null, null, 200.00000, null, 500.0000);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "FLOORANDCEIL", "PENDING", null, null, 200.00000, null, 500.00000, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 5), -1, 5, "NONE", "CEIL", 500.40174, null, null, null, null, 500.00000);
        // for non master class base room type
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), -1, 5, "NONE", "FLOOR", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 2), -1, 5, "NONE", "USER", 110.00000, 400.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 3), -1, 5, "NONE", "USER", 100.00000, 700.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "NONE", "FLOORANDCEIL", 208.74000, null, null, 200.00000, null, 500.0000);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "FLOORANDCEIL", "PENDING", null, null, 200.00000, null, 500.00000, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 5), -1, 5, "NONE", "CEIL", 500.40174, null, null, null, null, 500.00000);
        String floorOverrideOnlyQuery = "select * from dbo.ufn_get_cp_floor_overridden_rate_for_master_class_base_room_type(:startDate, :endDate)";
        // when
        List floorOverrides = tenantCrudService().findByNativeQuery(floorOverrideOnlyQuery, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        // then
        assertEquals(1, floorOverrides.size());
        Object[] sundayFloorRate = (Object[]) floorOverrides.get(0);
        assertEquals(startDate.toDate(), sundayFloorRate[0]);
        assertEquals(new BigDecimal("105.00000"), sundayFloorRate[1]);
        assertEquals(1, sundayFloorRate[2]);
    }

    @Test
    public void functionShouldReturnOnlySpecificOverriddenRates() {
        // Given
        LocalDate startDate = new LocalDate(2012, 1, 1);
        LocalDate endDate = new LocalDate(2012, 1, 5);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), -1, 5, "NONE", "FLOOR", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), 2, 5, "NONE", "USER", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 2), -1, 5, "NONE", "USER", 110.00000, 400.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 3), -1, 5, "NONE", "USER", 100.00000, 700.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "NONE", "FLOORANDCEIL", 208.74000, null, null, 200.00000, null, 500.0000);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "FLOORANDCEIL", "PENDING", null, null, 200.00000, null, 500.00000, null);
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, new LocalDate(2012, 1, 5), -1, 5, "NONE", "CEIL", 500.40174, null, null, null, null, 500.00000);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 1), -1, 5, "NONE", "FLOOR", 100.00000, null, null, 105.00000, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 2), -1, 5, "NONE", "USER", 110.00000, 400.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 3), -1, 5, "NONE", "USER", 100.00000, 700.00000, null, null, null, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "NONE", "FLOORANDCEIL", 208.74000, null, null, 200.00000, null, 500.0000);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 4), -1, 5, "FLOORANDCEIL", "PENDING", null, null, 200.00000, null, 500.00000, null);
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, new LocalDate(2012, 1, 5), -1, 5, "NONE", "CEIL", 500.40174, null, null, null, null, 500.00000);
        String specificOverrideOnlyQuery = "select * from dbo.ufn_get_cp_specific_overridden_rate_for_master_class_base_room_type(:startDate, :endDate)";
        // when
        List seasonsFloorRate = tenantCrudService().findByNativeQuery(specificOverrideOnlyQuery, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        // then
        assertEquals(2, seasonsFloorRate.size());
        Object[] mondaySpecificFloorRate = (Object[]) seasonsFloorRate.get(0);
        Object[] tuesdaySpecificFloorRate = (Object[]) seasonsFloorRate.get(1);
        assertEquals(startDate.plusDays(1).toDate(), mondaySpecificFloorRate[0]);
        assertEquals(startDate.plusDays(2).toDate(), tuesdaySpecificFloorRate[0]);
        assertEquals(new BigDecimal("400.00000"), mondaySpecificFloorRate[1]);
        assertEquals(new BigDecimal("700.00000"), tuesdaySpecificFloorRate[1]);
        assertEquals(1, mondaySpecificFloorRate[2]);
        assertEquals(1, tuesdaySpecificFloorRate[2]);
    }

    @Test
    public void testGetNonWellDecisionCountWhenConfigIssue() {
        // add decision for 3 day
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // for master class base room type
        BigDecimal bar = new BigDecimal("100.00");
        addCPDecisionBAROutput(1, master_base_room_type, startDate, bar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(1, master_base_room_type, startDate.plusDays(1), bar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(1, master_base_room_type, startDate.plusDays(2), bar, DecisionReasonType.ALL_IS_WELL.getId());
        // for non master class base room type
        bar = new BigDecimal("102.00");
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate, bar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate.plusDays(1), bar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate.plusDays(2), bar, DecisionReasonType.ALL_IS_WELL.getId());
        QueryParameter queryParameters = QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(2));
        List<Object[]> result = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.GET_NON_WELL_DECISION_COUNT_CP, queryParameters.parameters());
        assertEquals(2, result.get(0)[1]);
    }

    @Test
    public void testGetNonWellDecisionCountWhenAllIsWell() {
        // add decision for 3 day
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // for master class base room type
        BigDecimal bar = new BigDecimal("100.00");
        addCPDecisionBAROutput(1, master_base_room_type, startDate, bar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(1, master_base_room_type, startDate.plusDays(1), bar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(1, master_base_room_type, startDate.plusDays(2), bar, DecisionReasonType.ALL_IS_WELL.getId());
        // for non master class base room type
        bar = new BigDecimal("102.00");
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate, bar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate.plusDays(1), bar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(1, non_master_base_room_type, startDate.plusDays(2), bar, DecisionReasonType.ALL_IS_WELL.getId());
        QueryParameter queryParameters = QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(2));
        List<Object[]> result = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.GET_NON_WELL_DECISION_COUNT_CP, queryParameters.parameters());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFailedDaysCountForCP() {
        // it's thursday
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // for master class base room type
        BigDecimal optimalBar = new BigDecimal("100.00");
        // thu
        addCPDecisionBAROutput(2, master_base_room_type, startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // fri
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sat
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sun
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(3), optimalBar, DecisionReasonType.LRV_GT_BAR.getId());
        // mon
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(4), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // tue
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(5), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // for non master class base room type
        optimalBar = new BigDecimal("102.00");
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(3), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(4), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(5), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        // session for master base room type : friday
        insertIntoCP_Cfg_Base_AT(masterClassBaseRoomTypeId, startDate, startDate.plusDays(1), 100, 200, 100, 201, 100, 202, 100, 203, 100, 204, 100, 205, 100, 206, 1, "Test Season");
        // session for non master : friday
        insertIntoCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, startDate, startDate.plusDays(1), 105, 200, 105, 201, 103, 202, 108, 203, 109, 204, 100, 205, 100, 206, 1, "Test Season");
        // override for master class base room type
        // floor  : saturday
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, startDate.plusDays(2), -1, 5, "NONE", "FLOOR", 107.00000, null, null, 100.00000, null, null);
        // specific so it should not come into count : monday
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, startDate.plusDays(4), -1, 5, "NONE", "USER", 400.00000, 100.00000, null, null, null, null);
        // override for non master class base room type
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, startDate.plusDays(2), -1, 5, "NONE", "FLOOR", 107.00000, null, null, 105.00000, null, null);
        List<Object[]> failedDays = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.CP_LOWEST_DECISION_CHECK_SQL, QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(5)).parameters());
        assertEquals(4, failedDays.get(0)[1]);
    }

    @Test
    public void testFailedDaysCountForCPWithFloorRateInclusiveOfTax() {
        // Given
        // it's thursday
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // thu
        addCPDecisionBAROutput(2, master_base_room_type, startDate, new BigDecimal("109.00"), DecisionReasonType.ALL_IS_WELL.getId());
        // fri
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(1), new BigDecimal("109.00"), DecisionReasonType.ALL_IS_WELL.getId());
        // sat
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(2), new BigDecimal("105.00"), DecisionReasonType.ALL_IS_WELL.getId());
        // sun
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(3), new BigDecimal("105.00"), DecisionReasonType.LRV_GT_BAR.getId());
        // mon
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(4), new BigDecimal("105.00"), DecisionReasonType.ALL_IS_WELL.getId());
        // tue
        addCPDecisionBAROutput(2, master_base_room_type, startDate.plusDays(5), new BigDecimal("105.00"), DecisionReasonType.ALL_IS_WELL.getId());
        // for non master class base room type
        BigDecimal optimalBar = new BigDecimal("102.00");
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(3), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(4), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutput(2, non_master_base_room_type, startDate.plusDays(5), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        // session for master base room type : friday
        insertIntoCP_Cfg_Base_AT(masterClassBaseRoomTypeId, startDate, startDate.plusDays(1), 100, 200, 100, 201, 100, 202, 100, 203, 100, 204, 100, 205, 100, 206, 1, "S1");
        // session for non master : friday
        insertIntoCP_Cfg_Base_AT(nonMasterClassBaseRoomTypeId, startDate, startDate.plusDays(1), 105, 200, 105, 201, 103, 202, 108, 203, 109, 204, 100, 205, 100, 206, 1, "S1");
        // override for master class base room type
        // floor  : saturday
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, startDate.plusDays(2), -1, 5, "NONE", "FLOOR", 107.00000, null, null, 105.00000, null, null);
        // specific so it should not come into count : monday
        insertIntoCPBarOutputOverRide(1, 1, masterClassBaseRoomTypeId, startDate.plusDays(4), -1, 5, "NONE", "USER", 400.00000, 100.00000, null, null, null, null);
        // override for non master class base room type
        insertIntoCPBarOutputOverRide(1, 1, nonMasterClassBaseRoomTypeId, startDate.plusDays(2), -1, 5, "NONE", "FLOOR", 107.00000, null, null, 105.00000, null, null);

        //Tax data
        String updateDefaultTax = "UPDATE Tax SET Room_Tax_Rate = 5.0 WHERE Start_Date is null and End_Date is null";
        tenantCrudService().executeUpdateByNativeQuery(updateDefaultTax);

        final Tax tax1 = buildTax(9.00, new LocalDate(2005, 1, 1), new LocalDate(2005, 1, 2), "S1");
        final Tax tax2 = buildTax(10.00, new LocalDate(2005, 2, 3), new LocalDate(2012, 2, 4), "S2");

        tenantCrudService().save(Arrays.asList(tax1, tax2));

        // When
        List<Object[]> failedDays = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.CP_LOWEST_DECISION_CHECK_SQL, QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(5)).parameters());
        // Then
        assertEquals(4, failedDays.get(0)[1]);
    }

    @Test
    public void testFailedDaysCountForCPWithDefaultFloorRateForIndependentProducts() {
        // Given
        Product product = ProductBuilder.createIndependentProductProduct("IP1");
        tenantCrudService().save(product);
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // for master class base room type
        BigDecimal optimalBar = new BigDecimal("100.00");
        // thu
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // fri
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sat
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sun
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(3), optimalBar, DecisionReasonType.LRV_GT_BAR.getId());
        // mon
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(4), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // tue
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(5), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());

        // for non master class base room type
        optimalBar = new BigDecimal("102.00");
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(3), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(4), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(5), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());

        addCPPricingBaseAccomType(master_base_room_type, null, null, "Test Season", product);

        addCpDecisionBarOutputOverride(product, master_base_room_type, startDate.plusDays(2),
                DecisionOverrideType.NONE, DecisionOverrideType.FLOOR,
                BigDecimal.valueOf(107), null, null, BigDecimal.valueOf(105), null, null);
        addCpDecisionBarOutputOverride(product, master_base_room_type, startDate.plusDays(4),
                DecisionOverrideType.NONE, DecisionOverrideType.USER,
                BigDecimal.valueOf(400), BigDecimal.valueOf(100), null, null, null, null);
        addCpDecisionBarOutputOverride(product, non_master_base_room_type, startDate.plusDays(2),
                DecisionOverrideType.NONE, DecisionOverrideType.FLOOR,
                BigDecimal.valueOf(107), null, null, BigDecimal.valueOf(100), null, null);

        List<Object[]> failedDays = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.CP_LOWEST_DECISION_CHECK_SQL, QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(5)).parameters());
        assertEquals(3, failedDays.get(0)[1]);
    }

    @Test
    public void testFailedDaysCountForCPWithSeasonFloorRateForIndependentProducts() {
        // Given
        Product product = ProductBuilder.createIndependentProductProduct("IP1");
        tenantCrudService().save(product);
        LocalDate startDate = new LocalDate(2005, 1, 1);
        // for master class base room type
        BigDecimal optimalBar = new BigDecimal("100.00");
        // thu
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // fri
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sat
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // sun
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(3), optimalBar, DecisionReasonType.LRV_GT_BAR.getId());
        // mon
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(4), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        // tue
        addCPDecisionBAROutputForIndependentProduct(2, master_base_room_type, product,
                startDate.plusDays(5), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());

        // for non master class base room type
        optimalBar = new BigDecimal("102.00");
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate, optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(1), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(2), optimalBar, DecisionReasonType.ALL_IS_WELL.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(3), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(4), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());
        addCPDecisionBAROutputForIndependentProduct(2, non_master_base_room_type, product,
                startDate.plusDays(5), optimalBar, DecisionReasonType.CONFIG_ISSUES.getId());

        addCPPricingBaseAccomType(master_base_room_type, null, null, "Default", product);
        addCPPricingBaseAccomType(master_base_room_type, startDate, startDate.plusDays(1), "Test Season", product);

        addCpDecisionBarOutputOverride(product, master_base_room_type, startDate.plusDays(2),
                DecisionOverrideType.NONE, DecisionOverrideType.FLOOR,
                BigDecimal.valueOf(107), null, null, BigDecimal.valueOf(100), null, null);
        addCpDecisionBarOutputOverride(product, master_base_room_type, startDate.plusDays(4),
                DecisionOverrideType.NONE, DecisionOverrideType.USER,
                BigDecimal.valueOf(400), BigDecimal.valueOf(100), null, null, null, null);
        addCpDecisionBarOutputOverride(product, non_master_base_room_type, startDate.plusDays(2),
                DecisionOverrideType.NONE, DecisionOverrideType.FLOOR,
                BigDecimal.valueOf(107), null, null, BigDecimal.valueOf(100), null, null);

        List<Object[]> failedDays = tenantCrudService().findByNativeQuery(OptimizationDecisionAlertGenerationStep.CP_LOWEST_DECISION_CHECK_SQL, QueryParameter.with("startDate", startDate).and("endDate", startDate.plusDays(5)).parameters());
        assertEquals(4, failedDays.get(0)[1]);
    }

    private Tax buildTax(double roomTaxRate, LocalDate startDate, LocalDate endDate, String seasonName) {
        Tax tax = new Tax();
        tax.setRoomTaxRate(new BigDecimal(roomTaxRate));
        tax.setStartDate(startDate);
        tax.setEndDate(endDate);
        tax.setSeasonName(seasonName);
        return tax;
    }

    private void insertIntoCPBarOutputOverRide(int decisionId, int productId, int accomTypeId, LocalDate arrivalDate, int los, int userId, String oldOverride, String newOverride, Double oldBar, Double newBar, Double oldFloorRate, Double newFloorRate, Double oldCeilRate, Double newCeilRate) {
        String insertQuery = "INSERT INTO CP_Decision_Bar_Output_OVR\n" + "           (Property_ID       ,    Decision_ID   ,    Product_ID   ,    Accom_Type_ID  ,   " + " Arrival_DT     ,    LOS    ,   User_ID    ,    Old_Override     ,     New_Override    ," + "    Old_BAR   ,   New_BAR    ,    Old_Floor_Rate  ,    New_Floor_Rate  ,    Old_Ceil_Rate  ,  New_Ceil_Rate) VALUES\n" + "           (" + propertyId + "," + decisionId + "," + productId + "," + accomTypeId + ",'" + arrivalDate + "'," + los + "," + userId + ",'" + oldOverride + "','" + newOverride + "'," + oldBar + "," + newBar + "," + oldFloorRate + "," + newFloorRate + "," + oldCeilRate + "," + newCeilRate + ")";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);
    }

    private void insertIntoCP_Cfg_Base_AT(int accomTypeId, LocalDate startDate, LocalDate endDate, double sundayFloorRate, double sundayCeilRate, double mondayFloorRate, double mondayCeilRate, double tuesdayFloorRate, double tuesdayCeilRate, double wednesdayFloorRate, double wednesdayCeilRate, double thursdayFloorRate, double thursdayCeilRate, double fridayFloorRate, double fridayCeilRate, double saturdayFloorRate, double saturdayCeilRate, int statusId, String seasonName) {
        String seasonsQuery = "INSERT INTO CP_Cfg_Base_AT \n" + "           (Property_ID      ,   Accom_Type_ID    ,    Start_Date  ,    End_Date ," + "Sunday_Floor_Rate   ,    Sunday_Ceil_Rate ,   Monday_Floor_Rate ,     Monday_Ceil_Rate ,     Tuesday_Floor_Rate ,   Tuesday_Ceil_Rate," + "Wednesday_Floor_Rate   ,    Wednesday_Ceil_Rate ,     Thursday_Floor_Rate ,   Thursday_Ceil_Rate  ," + "Friday_Floor_Rate   ,     Friday_Ceil_Rate ,    Saturday_Floor_Rate  ,     Saturday_Ceil_Rate  ,   Status_ID    ,   Season_Name, Product_ID) VALUES\n" + "           (" + propertyId + "," + accomTypeId + ",'" + startDate + "','" + endDate + "'," + sundayFloorRate + "," + sundayCeilRate + "," + mondayFloorRate + "," + mondayCeilRate + "," + tuesdayFloorRate + "," + tuesdayCeilRate + "," + wednesdayFloorRate + "," + wednesdayCeilRate + "," + thursdayFloorRate + "," + thursdayCeilRate + "," + fridayFloorRate + "," + fridayCeilRate + "," + saturdayFloorRate + "," + saturdayCeilRate + "," + statusId + ",'" + seasonName + "',1)";
        tenantCrudService().executeUpdateByNativeQuery(seasonsQuery);
    }

    private void updateCP_Cfg_Base_AT(int accomTypeId, double sundayFloorRate, double mondayFloorRate, double tuesdayFloorRate, double wednesdayFloorRate, double thursdayFloorRate, double fridayFloorRate, double saturdayFloorRate) {
        String updateFloorRateOfBaseType = "UPDATE CP_Cfg_Base_AT  SET Sunday_Floor_Rate = " + sundayFloorRate + "      ,Monday_Floor_Rate = " + mondayFloorRate + "      ,Tuesday_Floor_Rate = " + tuesdayFloorRate + "      ,Wednesday_Floor_Rate =" + wednesdayFloorRate + "      ,Thursday_Floor_Rate = " + thursdayFloorRate + "      ,Friday_Floor_Rate =" + fridayFloorRate + "      ,Saturday_Floor_Rate = " + saturdayFloorRate + "       WHERE Accom_Type_ID= " + accomTypeId + "and Start_Date is null and End_Date is null";
        tenantCrudService().executeUpdateByNativeQuery(updateFloorRateOfBaseType);
    }

    private CPDecisionBAROutput addCPDecisionBAROutput(int decisionId, String accomType, LocalDate arrivalDate, BigDecimal optimalBAR, int decisionReasonId) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        // Set the default values
        cpDecisionBAROutput.setPropertyId(propertyId);
        cpDecisionBAROutput.setProduct(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
        cpDecisionBAROutput.setDecisionReasonTypeId(decisionReasonId);
        cpDecisionBAROutput.setDecisionId(decisionId);
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.NONE);
        // Set the values that will change per entry
        cpDecisionBAROutput.setAccomType(tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomType).parameters()));
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        cpDecisionBAROutput.setOptimalBAR(optimalBAR);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();
        return cpDecisionBAROutput;
    }

    private CPDecisionBAROutput addCPDecisionBAROutputForIndependentProduct(
            int decisionId, String accomType, Product product, LocalDate arrivalDate, BigDecimal optimalBAR, int decisionReasonId) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        // Set the default values
        cpDecisionBAROutput.setPropertyId(propertyId);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setDecisionReasonTypeId(decisionReasonId);
        cpDecisionBAROutput.setDecisionId(decisionId);
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.NONE);
        // Set the values that will change per entry
        cpDecisionBAROutput.setAccomType(tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomType).parameters()));
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        cpDecisionBAROutput.setOptimalBAR(optimalBAR);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();
        return cpDecisionBAROutput;
    }

    private TransientPricingBaseAccomType addCPPricingBaseAccomType(String accomType, LocalDate startDate, LocalDate endDate,
                                                                    String seasonName, Product product1) {
        TransientPricingBaseAccomType cpCfgBaseAT = new TransientPricingBaseAccomType();
        cpCfgBaseAT.setPropertyId(propertyId);
        cpCfgBaseAT.setAccomType(tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomType).parameters()));
        cpCfgBaseAT.setStartDate(startDate);
        cpCfgBaseAT.setEndDate(endDate);
        cpCfgBaseAT.setSundayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setSundayCeilingRate(BigDecimal.valueOf(200));
        cpCfgBaseAT.setMondayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setMondayCeilingRate(BigDecimal.valueOf(201));
        cpCfgBaseAT.setTuesdayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setTuesdayCeilingRate(BigDecimal.valueOf(202));
        cpCfgBaseAT.setWednesdayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setWednesdayCeilingRate(BigDecimal.valueOf(203));
        cpCfgBaseAT.setThursdayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setThursdayCeilingRate(BigDecimal.valueOf(204));
        cpCfgBaseAT.setFridayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setFridayCeilingRate(BigDecimal.valueOf(205));
        cpCfgBaseAT.setSaturdayFloorRate(BigDecimal.valueOf(100));
        cpCfgBaseAT.setSaturdayCeilingRate(BigDecimal.valueOf(206));
        cpCfgBaseAT.setStatus(Status.ACTIVE);
        cpCfgBaseAT.setSeasonName(seasonName);
        cpCfgBaseAT.setProductID(product1.getId());
        tenantCrudService().save(cpCfgBaseAT);
        tenantCrudService().flushAndClear();
        return cpCfgBaseAT;
    }

    private CPDecisionBAROutputOverride addCpDecisionBarOutputOverride(
            Product product, String accomType, LocalDate arrivalDate, DecisionOverrideType oldOverride, DecisionOverrideType newOverride,
            BigDecimal oldBar, BigDecimal newBar, BigDecimal oldFloorRate, BigDecimal newFloorRate, BigDecimal oldCeilingRate, BigDecimal newCeilingRate) {
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = new CPDecisionBAROutputOverride();
        cpDecisionBAROutputOverride.setPropertyId(propertyId);
        cpDecisionBAROutputOverride.setProduct(product);
        cpDecisionBAROutputOverride.setDecisionId(1);
        cpDecisionBAROutputOverride.setAccomType(tenantCrudService().findByNamedQuerySingleResult(
                AccomType.BY_CODE, QueryParameter.with("code", accomType).parameters()));
        cpDecisionBAROutputOverride.setArrivalDate(arrivalDate);
        cpDecisionBAROutputOverride.setLengthOfStay(-1);
        cpDecisionBAROutputOverride.setUser(5);
        cpDecisionBAROutputOverride.setOldOverrideType(oldOverride);
        cpDecisionBAROutputOverride.setNewOverrideType(newOverride);
        cpDecisionBAROutputOverride.setOldUserOverride(oldBar);
        cpDecisionBAROutputOverride.setNewUserOverride(newBar);
        cpDecisionBAROutputOverride.setOldFloorRate(oldFloorRate);
        cpDecisionBAROutputOverride.setNewFloorRate(newFloorRate);
        cpDecisionBAROutputOverride.setOldCeilingRate(oldCeilingRate);
        cpDecisionBAROutputOverride.setNewCeilingRate(newCeilingRate);
        cpDecisionBAROutputOverride.setCreateDate(new Date());
        tenantCrudService().save(cpDecisionBAROutputOverride);
        tenantCrudService().flushAndClear();
        return cpDecisionBAROutputOverride;
    }
}
