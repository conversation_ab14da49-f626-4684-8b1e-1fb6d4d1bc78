package com.ideas.tetris.pacman.services.ratepopulation;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.PostingRuleEnum;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.RateDefinition;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.RateType;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.TypeEnum;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.activesrp.entity.ActiveSrp;
import com.ideas.tetris.pacman.services.activesrp.repository.ActiveSrpsRepository;
import com.ideas.tetris.pacman.services.activesrp.service.ActiveSrpService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueFileMetadataCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.UniqueRateQualifiedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.dtos.RateQualifiedActiveDto;
import com.ideas.tetris.pacman.services.ratepopulation.mapper.RateQualifiedMapper;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified;
import com.ideas.tetris.pacman.util.jaxb.rates.request.RatesRequestJAXBUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.util.xml.XmlHelper;
import com.ideas.tetris.platform.common.util.xml.XmlHelperImpl;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.io.IOUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.xml.datatype.XMLGregorianCalendar;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.USE_SRP_ACTIVE_DATES;
import static com.ideas.tetris.pacman.common.constants.Constants.ACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.ADJUSTMENT_TYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.QUALIFIED_RATE_PLAN_FIXED_TYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.QUALIFIED_RATE_PLAN_VALUE_TYPE;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.DUPLICATE_LV0_MESSAGE;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.LV0;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.MANAGED_IN_G3_NOT_UPDATED_MESSAGE;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.MANAGED_IN_G3_UPDATED_MESSAGE;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.RATE_QUALIFIED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class QualifiedRateServiceTest extends AbstractG3JupiterTest {

    public static final String HEADER_ADJUST = "HeaderAdjust";
    public static final String RATE_CODE_5PCTB = "5PCTB";
    public static final String DLX = "DLX";
    public static final String STE = "STE";
    public static final String DBL = "DBL";
    public static final String Q = "Q";
    public static final String K = "K";
    public static final int RATE_PLAN_TYPE = 12;
    public static final String LHHGVT = "LHHGVT";
    public static final String WKDD = "WKDD";
    public static final String CHHART_NEW = "CHHART_NEW";
    public static final String CHHART = "CHHART";
    public static final String SPLIT_SRP = "SplitSRP";
    public static final String MISCE = "MISCE";
    public static final BigDecimal ZERO_OFFSET = new BigDecimal("0.00000");
    public static final BigDecimal NON_ZERO_OFFSET = new BigDecimal("55");
    public static final String INSERT_LV0_IN_RATE_QUALIFIED_TABLE =
            "DECLARE @caughtupdate DATE\n" +
                    "SET @caughtupdate = (SELECT dbo.ufn_get_caughtup_date_by_property(:propertyID," + RecordType.T2SNAP_RECORD_TYPE_ID + ", " + ProcessStatus.SUCCESSFUL + "))\n" +
                    "INSERT INTO Rate_Qualified VALUES(:fileMetadataID, :propertyID, 'LV0', 'LV0', NULL, NULL, @caughtupdate, DATEADD(YEAR, DATEDIFF(YEAR, @caughtupdate, '2273-12-31'), @caughtupdate), 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, :userID, CURRENT_TIMESTAMP, :userID, :rateQualifiedTypeId, :managedInG3)";
    public static final String UPDATE_MANAGED_IN_G3 =
            "UPDATE Rate_Qualified\n" +
                    "SET Managed_In_G3 = :managedInG3\n" +
                    "WHERE Rate_Code_Name = 'LV0'";
    public static final String ZNYIELD = "ZNYIELD";
    public static final String GAPSRP = "GAPSRP";
    private static final String RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED = "pacman.ratequalifieddetails.tablebatch.enabled";
    RateDefinition firstRateDefinition;
    RateDefinition moreRateDefinition;
    RateDefinition secondRateDefinition;
    RateDefinition lessRateAdjustmentDef;
    String firstFile;
    String secondFilePath;
    String secondFile;
    XmlHelper xmlHelper;
    CrudService tenantCrudService;
    QualifiedRateService qualifiedRateService = new QualifiedRateService();
    @RatesRequestJAXBUtil.RatesRequestQualifier
    JAXBUtilLocal jaxbUtilLocal;
    @Mock
    JpaJdbcUtil jpaJdbcUtil;
    @Mock
    DateService dateService;
    Map<String, RateQualified> managedRates = new HashMap<>();
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private RateQualifiedMapper rateQualifiedMapper;
    private Date snapShotDate;
    private Date snapShotTime;
    private Date preparedDate;
    private Date preparedTime;
    private Date secondSnapShotDate;
    private Integer propertyId = 5;
    private Map cacheAccomData;
    private String firstFilePath;
    private String lessRateAdj;
    private String moreRateAdjFile;
    private RateDefinition moreRateAdjustmentDef;

    @BeforeEach
    public void setUp() throws Exception {
        xmlHelper = new XmlHelperImpl();
        jaxbUtilLocal = new RatesRequestJAXBUtil();
        jaxbUtilLocal.init();
        when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
        tenantCrudService = tenantCrudService();
        qualifiedRateService.setCrudService(tenantCrudService);
        cacheAccomData = new HashMap<String, Integer>();
        cacheAccomData.put(DLX, 4);
        cacheAccomData.put(STE, 5);
        cacheAccomData.put(DBL, 6);
        cacheAccomData.put(Q, 7);
        cacheAccomData.put(K, 8);
        qualifiedRateService.setConfigService(configParamsService);
        firstFile = getClass().getResource("/rates/RateQualified_Header.xml").getPath();
        moreRateAdjFile = getClass().getResource("/rates/RQ_Header_MoreAdj.xml").getPath();
        secondFile = getClass().getResource("/rates/RateQualified_Header_nextDay.xml").getPath();
        lessRateAdj = getClass().getResource("/rates/RQ_Header_LessAdj.xml").getPath();

        firstRateDefinition = (RateDefinition) jaxbUtilLocal.unmarshall(readInput(new File(firstFile)));
        secondRateDefinition = (RateDefinition) jaxbUtilLocal.unmarshall(readInput(new File(secondFile)));
        lessRateAdjustmentDef = (RateDefinition) jaxbUtilLocal.unmarshall(readInput(new File(lessRateAdj)));
        moreRateAdjustmentDef = (RateDefinition) jaxbUtilLocal.unmarshall(readInput(new File(lessRateAdj)));
        snapShotDate = xmlHelper.convertXMLGregorianToDate(firstRateDefinition.getSnapshotDate());
        snapShotTime = xmlHelper.convertXMLGregorianToDate(firstRateDefinition.getSnapshotTime());
        preparedDate = xmlHelper.convertXMLGregorianToDate(firstRateDefinition.getPreparedDate());
        preparedTime = xmlHelper.convertXMLGregorianToDate(firstRateDefinition.getPreparedTime());

        secondSnapShotDate = xmlHelper.convertXMLGregorianToDate(secondRateDefinition.getSnapshotDate());

        firstFilePath = firstFile.replace('/', '\\');
        secondFilePath = secondFile.replace('/', '\\');
        setField(qualifiedRateService, RateService.class.getDeclaredField("jpaJdbcUtil"), jpaJdbcUtil);

        AccommodationService accommodationService = new AccommodationService();
        ActiveSrpsRepository activeSrpsRepository = new ActiveSrpsRepository();
        ActiveSrpService activeSrpService = new ActiveSrpService();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        inject(qualifiedRateService, "accommodationService", accommodationService);
        inject(accommodationService, "tenantCrudService", tenantCrudService());
        inject(qualifiedRateService, "activeSrpsRepository", activeSrpsRepository);
        inject(activeSrpsRepository, "tenantCrudService", tenantCrudService());
        inject(qualifiedRateService, "activeSrpService", activeSrpService);
        inject(qualifiedRateService, "dateService", dateService);
        RateQualifiedMapper rateQualifiedMapper = Mappers.getMapper(RateQualifiedMapper.class);
        inject(qualifiedRateService, "rateQualifiedMapper", rateQualifiedMapper);
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        dateService.setConfigParamsService(configParamsService);
        inject(propertyGroupService, "globalCrudService", globalCrudService());
        when(configParamsService.getBooleanParameterValue(USE_SRP_ACTIVE_DATES)).thenReturn(false);
        final LocalDate caughtUpDate = new LocalDate("2013-04-01");
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(DateUtil.addDaysToDate(caughtUpDate.toDate(), 720));//2015-06-20
    }

    @Test
    public void testQualifiedRateHeaderPopulationMultiScen() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        assertRateQualified();
    }

    @Test
    @Tag("qualifiedRatePopulation-flaky")
    public void testQualifiedRateHeaderPopulationMultiScenWithActiveDates() {
        //GIVEN
        //PreAndPostOptimizationActiveSrp
        activeSrp(GAPSRP, "2013-02-01", "2013-03-01");
        activeSrp(ZNYIELD, "2013-01-01", "2013-03-01");
        activeSrp(ZNYIELD, "2016-01-01", "2016-03-01");
        activeSrp(WKDD, "2016-01-01", "2016-03-01");

        activeSrp(CHHART, "2013-04-24", "2013-06-10");
        activeSrp(RATE_CODE_5PCTB, "2014-05-22", "2014-06-10");
        activeSrp(RATE_CODE_5PCTB, "2014-07-01", "2014-09-10");
        activeSrp(RATE_CODE_5PCTB, "2014-09-11", "2014-12-31");
        activeSrp(RATE_CODE_5PCTB, "2015-01-01", "2015-06-10");
        //Rate code with gap of one day in extract season
        activeSrp(MISCE, "2014-01-01", "2015-02-01");
        //WHEN
        when(configParamsService.getBooleanParameterValue(USE_SRP_ACTIVE_DATES)).thenReturn(true);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        //THEN
        validatePreAndPostOptimizationActiveSrp();
        final List<RateQualifiedDetails> rateDetails_CHHART_DLX = getRateDetailsByRatePlanRoomtype(CHHART, DLX);
        final List<RateQualifiedDetails> rateDetails_CHHART_DBL = getRateDetailsByRatePlanRoomtype(CHHART, DBL);
        assertEquals(1, rateDetails_CHHART_DLX.size());
        assertEquals(1, rateDetails_CHHART_DBL.size());
        assertRateQualifiedDetails("24-Apr-2013", "10-Jun-2013", rateDetails_CHHART_DLX.get(0), 129.00, 129.00, 129.00, 129.00, 129.00, 129.00, 129.00);
        assertRateQualifiedDetails("25-Apr-2013", "10-Jun-2013", rateDetails_CHHART_DBL.get(0), 129.55, 139.00, 149.00, 159.00, 169.00, 179.00, 189.00);
        final List<RateQualifiedDetails> rateDetails_5PCTB_DLX = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DLX);
        final List<RateQualifiedDetails> rateDetails_5PCTB_DBl = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DBL);
        assertEquals(4, rateDetails_5PCTB_DLX.size());
        assertEquals(5, rateDetails_5PCTB_DBl.size());
        assertRateQualifiedDetails("22-May-2014", "10-Jun-2014", rateDetails_5PCTB_DLX.get(0), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("01-Jul-2014", "10-Sep-2014", rateDetails_5PCTB_DLX.get(1), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("11-Sep-2014", "31-Dec-2014", rateDetails_5PCTB_DLX.get(2), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("01-Jan-2015", "10-Jun-2015", rateDetails_5PCTB_DLX.get(3), 56.99, 56.99, 56.99, 56.99, 56.99, 56.99, 56.99);
        assertRateQualifiedDetails("01-Oct-2014", "02-Oct-2014", rateDetails_5PCTB_DBl.get(0), 66.99, 66.99, 66.99, 66.99, 66.99, 66.99, 66.99);
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_5PCTB_DBl.get(1), 77.49, 77.49, 77.49, 77.49, 77.49, 77.49, 77.49);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_5PCTB_DBl.get(2), 88.99, 88.99, 88.99, 88.99, 88.99, 56.99, 88.99);
        assertRateQualifiedDetails("05-Oct-2014", "31-Dec-2014", rateDetails_5PCTB_DBl.get(3), 99.49, 99.49, 99.49, 99.49, 99.49, 99.49, 99.49);
        assertRateQualifiedDetails("01-Jan-2015", "10-Jun-2015", rateDetails_5PCTB_DBl.get(4), 99.49, 99.49, 99.49, 99.49, 99.49, 99.49, 99.49);
        final List<RateQualifiedDetails> rateDetails_MISCE_Q = getRateDetailsByRatePlanRoomtype(MISCE, Q);
        final List<RateQualifiedDetails> rateDetails_MISCE_DLX = getRateDetailsByRatePlanRoomtype(MISCE, DLX);
        final List<RateQualifiedDetails> rateDetails_MISCE_DBL = getRateDetailsByRatePlanRoomtype(MISCE, DBL);
        assertEquals(3, rateDetails_MISCE_Q.size());
        assertEquals(2, rateDetails_MISCE_DLX.size());
        assertEquals(1, rateDetails_MISCE_DBL.size());
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_Q.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_MISCE_Q.get(1), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("05-Oct-2014", "31-Dec-2014", rateDetails_MISCE_Q.get(2), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_DBL.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_DLX.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("05-Oct-2014", "31-Dec-2014", rateDetails_MISCE_DLX.get(1), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
    }

    private void validatePreAndPostOptimizationActiveSrp() {
        final List<RateQualifiedDetails> rateDetails_ZNYIELD_DLX = getRateDetailsByRatePlanRoomtype(ZNYIELD, DLX);
        final List<RateQualifiedDetails> rateDetails_ZNYIELD_DBL = getRateDetailsByRatePlanRoomtype(ZNYIELD, DBL);
        assertEquals(0, rateDetails_ZNYIELD_DLX.size());
        assertEquals(0, rateDetails_ZNYIELD_DBL.size());
        final List<RateQualifiedDetails> rateDetails_WKDD_DLX = getRateDetailsByRatePlanRoomtype(WKDD, DLX);
        final List<RateQualifiedDetails> rateDetails_WKDD_DBL = getRateDetailsByRatePlanRoomtype(WKDD, DBL);
        assertEquals(0, rateDetails_WKDD_DLX.size());
        assertEquals(0, rateDetails_WKDD_DBL.size());
        final List<RateQualifiedDetails> rateDetails_GAPSRP_Q = getRateDetailsByRatePlanRoomtype(GAPSRP, Q);
        assertEquals(0, rateDetails_GAPSRP_Q.size());
    }

    @Test
    @Tag("qualifiedRatePopulation-flaky")
    public void testQualifiedRatePopulation2ExtractActiveDates() {
        //Given
        //PreAndPostOptimizationActiveSrp
        activeSrp(GAPSRP, "2013-02-01", "2013-03-01");
        activeSrp(ZNYIELD, "2013-01-01", "2013-03-01");
        activeSrp(ZNYIELD, "2016-01-01", "2016-03-01");
        activeSrp(WKDD, "2016-01-01", "2016-03-01");

        activeSrp(CHHART, "2013-04-24", "2013-06-10");
        activeSrp(RATE_CODE_5PCTB, "2014-05-22", "2014-06-10");
        activeSrp(RATE_CODE_5PCTB, "2014-07-01", "2014-09-10");
        activeSrp(RATE_CODE_5PCTB, "2014-09-11", "2014-12-31");
        activeSrp(RATE_CODE_5PCTB, "2015-01-01", "2015-06-10");
        //Rate code with gap of one day in extract season
        activeSrp(MISCE, "2014-01-01", "2015-02-01");
        //Active SRP dates with Gap in Seasons for the in progress Season received from Extract.
        activeSrp(SPLIT_SRP, "2014-01-01", "2014-02-01");
        activeSrp(SPLIT_SRP, "2014-10-04", "2014-10-04");
        activeSrp(SPLIT_SRP, "2014-10-15", "2014-10-25");
        activeSrp(SPLIT_SRP, "2014-11-01", "2014-12-31");
        //WHEN
        when(configParamsService.getBooleanParameterValue(USE_SRP_ACTIVE_DATES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.TRUE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        //THEN
        validatePreAndPostOptimizationActiveSrp();
        final List<RateQualifiedDetails> rateDetails_CHHART_DLX = getRateDetailsByRatePlanRoomtype(CHHART, DLX);
        assertEquals(1, rateDetails_CHHART_DLX.size());//CHHART_DLX
        assertRateQualifiedDetails("24-Apr-2013", "10-Jun-2013", rateDetails_CHHART_DLX.get(0), 129.00, 129.00, 129.00, 129.00, 129.00, 129.00, 129.00);
        final List<RateQualifiedDetails> rateDetails_CHHART_DBL = getRateDetailsByRatePlanRoomtype(CHHART, DBL);
        assertEquals(1, rateDetails_CHHART_DBL.size());//CHHART_DBL
        assertRateQualifiedDetails("25-Apr-2013", "10-Jun-2013", rateDetails_CHHART_DBL.get(0), 129.55, 139.00, 149.00, 159.00, 169.00, 179.00, 189.00);
        final List<RateQualifiedDetails> rateDetails_5PCTB_DLX = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DLX);
        assertEquals(5, rateDetails_5PCTB_DLX.size());//5PCTB_DLX
        assertRateQualifiedDetails("22-May-2014", "10-Jun-2014", rateDetails_5PCTB_DLX.get(0), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("01-Jul-2014", "10-Sep-2014", rateDetails_5PCTB_DLX.get(1), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("11-Sep-2014", "03-Oct-2014", rateDetails_5PCTB_DLX.get(2), 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);
        assertRateQualifiedDetails("04-Oct-2014", "31-Dec-2014", rateDetails_5PCTB_DLX.get(3), 60.45, 60.45, 60.45, 60.45, 60.45, 60.45, 60.45);
        final List<RateQualifiedDetails> rateDetails_5PCTB_DBl = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DBL);
        assertEquals(2, rateDetails_5PCTB_DBl.size());//5PCTB_DBL
        assertRateQualifiedDetails("01-Jan-2015", "10-Jun-2015", rateDetails_5PCTB_DLX.get(4), 55.00, 55.00, 55.00, 55.00, 55.00, 55.00, 55.00);
        assertRateQualifiedDetails("01-Oct-2014", "02-Oct-2014", rateDetails_5PCTB_DBl.get(0), 66.99, 66.99, 66.99, 66.99, 66.99, 66.99, 66.99);
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_5PCTB_DBl.get(1), 77.49, 77.49, 77.49, 77.49, 77.49, 77.49, 77.49);
        final List<RateQualifiedDetails> rateDetails_MISCE_Q = getRateDetailsByRatePlanRoomtype(MISCE, Q);
        assertEquals(3, rateDetails_MISCE_Q.size());//MISCE_Q
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_Q.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_MISCE_Q.get(1), 91.33, 92.33, 95.33, 94.33, 93.33, 99.33, 97.33);
        assertRateQualifiedDetails("05-Oct-2014", "31-Dec-2014", rateDetails_MISCE_Q.get(2), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        final List<RateQualifiedDetails> rateDetails_MISCE_DBL = getRateDetailsByRatePlanRoomtype(MISCE, DBL);
        assertEquals(2, rateDetails_MISCE_DBL.size());//MISCE_DBL
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_DBL.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("04-Oct-2014", "31-Dec-2014", rateDetails_MISCE_DBL.get(1), 91.33, 92.33, 95.33, 94.33, 93.33, 99.33, 97.33);
        final List<RateQualifiedDetails> rateDetails_MISCE_DLX = getRateDetailsByRatePlanRoomtype(MISCE, DLX);
        assertEquals(2, rateDetails_MISCE_DLX.size());//MISCE_DLX
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateDetails_MISCE_DLX.get(0), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        assertRateQualifiedDetails("04-Oct-2014", "31-Dec-2014", rateDetails_MISCE_DLX.get(1), 91.00, 92.00, 95.00, 94.00, 93.00, 99.00, 97.00);
        final List<RateQualifiedDetails> rateDetails_SPLIT_SRP_STE = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, STE);
        assertEquals(4, rateDetails_SPLIT_SRP_STE.size());//SPLIT_SRP_STE
        assertRateQualifiedDetails("01-Jan-2014", "01-Feb-2014", rateDetails_SPLIT_SRP_STE.get(0), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_SPLIT_SRP_STE.get(1), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("15-Oct-2014", "25-Oct-2014", rateDetails_SPLIT_SRP_STE.get(2), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("01-Nov-2014", "31-Dec-2014", rateDetails_SPLIT_SRP_STE.get(3), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        final List<RateQualifiedDetails> rateDetails_SPLIT_SRP_DLX = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, DLX);
        assertEquals(4, rateDetails_SPLIT_SRP_DLX.size());//SPLIT_SRP_DLX
        assertRateQualifiedDetails("01-Jan-2014", "01-Feb-2014", rateDetails_SPLIT_SRP_DLX.get(0), 96.33, 96.34, 96.35, 96.36, 96.37, 96.38, 96.39);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_SPLIT_SRP_DLX.get(1), 96.33, 96.34, 96.75, 96.36, 96.37, 96.38, 96.39);
        assertRateQualifiedDetails("15-Oct-2014", "25-Oct-2014", rateDetails_SPLIT_SRP_DLX.get(2), 96.33, 96.34, 96.75, 96.36, 96.37, 96.38, 96.39);
        assertRateQualifiedDetails("01-Nov-2014", "31-Dec-2014", rateDetails_SPLIT_SRP_DLX.get(3), 96.33, 96.34, 96.75, 96.36, 96.37, 96.38, 96.39);
        final List<RateQualifiedDetails> rateDetails_SPLIT_SRP_DBl = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, DBL);
        assertEquals(5, rateDetails_SPLIT_SRP_DBl.size());//SPLIT_SRP_DBl
        assertRateQualifiedDetails("01-Jan-2014", "01-Feb-2014", rateDetails_SPLIT_SRP_DBl.get(0), 133.33, 296.00, 346.00, 496.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_SPLIT_SRP_DBl.get(1), 133.33, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("15-Oct-2014", "25-Oct-2014", rateDetails_SPLIT_SRP_DBl.get(2), 133.33, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("01-Nov-2014", "30-Nov-2014", rateDetails_SPLIT_SRP_DBl.get(3), 133.33, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("01-Dec-2014", "31-Dec-2014", rateDetails_SPLIT_SRP_DBl.get(4), 197.00, 297.00, 397.00, 497.00, 597.00, 697.00, 797.00);
        final List<RateQualifiedDetails> rateDetails_SPLIT_SRP_Q = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, Q);
        assertEquals(4, rateDetails_SPLIT_SRP_Q.size());//SPLIT_SRP_Q
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_SPLIT_SRP_Q.get(0), 196.00, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("15-Oct-2014", "25-Oct-2014", rateDetails_SPLIT_SRP_Q.get(1), 196.00, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("01-Nov-2014", "30-Nov-2014", rateDetails_SPLIT_SRP_Q.get(2), 196.00, 291.00, 396.33, 491.00, 596.00, 696.00, 796.00);
        assertRateQualifiedDetails("01-Dec-2014", "31-Dec-2014", rateDetails_SPLIT_SRP_Q.get(3), 159.00, 159.00, 159.00, 159.00, 159.00, 159.00, 159.00);
        final List<RateQualifiedDetails> rateDetails_SPLIT_SRP_K = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, K);
        assertEquals(4, rateDetails_SPLIT_SRP_K.size());//SPLIT_SRP_K
        assertRateQualifiedDetails("01-Jan-2014", "01-Feb-2014", rateDetails_SPLIT_SRP_K.get(0), 99.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("04-Oct-2014", "04-Oct-2014", rateDetails_SPLIT_SRP_K.get(1), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("15-Oct-2014", "25-Oct-2014", rateDetails_SPLIT_SRP_K.get(2), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
        assertRateQualifiedDetails("01-Nov-2014", "31-Dec-2014", rateDetails_SPLIT_SRP_K.get(3), 96.00, 96.00, 96.00, 96.00, 96.00, 96.00, 96.00);
    }

    @Test
    public void testQualifiedRateDetailsPopulationMultiScenWhenTableBatchingDisabled() {

        System.setProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED, "false");

        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath
                        , snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        assertRateQualifiedDetails("25-Apr-2013", "18-Jun-2013");

        System.clearProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED);
    }

    @Test
    public void testQualifiedRatePopulation2Extract() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.TRUE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        assertNotNull(secondRateExtract);
        assertEquals(10, secondRateExtract.size());
        final RateQualified rateQualified = secondRateExtract.get(3);
        assertEquals(WKDD, rateQualified.getName());
        assertEquals(WKDD, rateQualified.getDescription());
        assertEquals(new Date("01-Oct-2014"), rateQualified.getStartDate());
        assertEquals(new Date("15-Oct-2173"), rateQualified.getEndDate());
        assertEquals("USD", rateQualified.getCurrency());
        assertEquals("None", rateQualified.getReferenceRateCode());
        assertNull(rateQualified.getRemarks());
        assertEquals(new Integer(1), rateQualified.getStatusId());
        assertEquals(new Integer(0), rateQualified.getYieldable());
        assertEquals(new Integer(0), rateQualified.getPriceRelative());
        assertEquals(new Integer(1), rateQualified.getIncludesPackage());
        assertEquals(CHHART_NEW, secondRateExtract.get(9).getName());
        assertEquals(ZNYIELD, secondRateExtract.get(7).getName());
        assertEquals(new Integer(0), secondRateExtract.get(7).getYieldable());
    }

    @Test
    public void testQualifiedRateDetailsPopulation2Extract() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        RateQualifiedDetails rateQualifiedDetails = getRateDetailsByRatePlanRoomtype(CHHART, DBL).get(0);
        assertNotNull(rateQualifiedDetails);
        assertEquals(new Date("25-Apr-2013"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("18-Jun-2013"), rateQualifiedDetails.getEndDate());
        assertTrue(129.55 == rateQualifiedDetails.getSunday().doubleValue());

        List<RateQualifiedDetails> rateDetailsByRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(WKDD, STE);
        assertEquals(1, rateDetailsByRatePlanRoomtype.size(), "For discontinued Accommodation Type there should be only session's in the past");
        RateQualifiedDetails qualifiedDetails = rateDetailsByRatePlanRoomtype.get(0);
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getEndDate(), "End date is set to snapshot date -1 ");
        assertTrue(94.00 == qualifiedDetails.getSunday().doubleValue());

        //5PCTB DBL
        rateDetailsByRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DBL);
        assertEquals(2, rateDetailsByRatePlanRoomtype.size(), "For discontinued Accommodation Type there should be only session's in the past");
        qualifiedDetails = rateDetailsByRatePlanRoomtype.get(0);
        assertEquals(new Date("01-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("02-Oct-2014"), qualifiedDetails.getEndDate(), "End date remains same ");
        assertTrue(66.99 == qualifiedDetails.getSunday().doubleValue());

        qualifiedDetails = rateDetailsByRatePlanRoomtype.get(1);
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getEndDate(), "End date remains same ");
        assertTrue(77.49 == qualifiedDetails.getSunday().doubleValue());

        rateQualifiedDetails = getRateDetailsByRatePlanRoomtype(CHHART_NEW, STE).get(0);
        assertNotNull(rateQualifiedDetails);
        assertEquals(new Date("24-Apr-2013"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("19-Jun-2013"), rateQualifiedDetails.getEndDate());
        assertTrue(129.00 == rateQualifiedDetails.getSunday().doubleValue());
    }

    @Test
    public void testQualifiedRateDetailsPopulation2ExtractWhenRateQualifiedDetailsTableBatching_Disabled() {
        System.setProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED, "false");

        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        RateQualifiedDetails rateQualifiedDetails = getRateDetailsByRatePlanRoomtype(CHHART, DBL).get(0);
        assertNotNull(rateQualifiedDetails);
        assertEquals(new Date("25-Apr-2013"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("18-Jun-2013"), rateQualifiedDetails.getEndDate());
        assertTrue(129.55 == rateQualifiedDetails.getSunday().doubleValue());

        List<RateQualifiedDetails> rateDetailsByRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(WKDD, STE);
        assertEquals(1, rateDetailsByRatePlanRoomtype.size(), "For discontinued Accommodation Type there should be only session's in the past");
        RateQualifiedDetails qualifiedDetails = rateDetailsByRatePlanRoomtype.get(0);
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getEndDate(), "End date is set to snapshot date -1 ");
        assertTrue(94.00 == qualifiedDetails.getSunday().doubleValue());

        //5PCTB DBL
        rateDetailsByRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(RATE_CODE_5PCTB, DBL);
        assertEquals(2, rateDetailsByRatePlanRoomtype.size(), "For discontinued Accommodation Type there should be only session's in the past");
        qualifiedDetails = rateDetailsByRatePlanRoomtype.get(0);
        assertEquals(new Date("01-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("02-Oct-2014"), qualifiedDetails.getEndDate(), "End date remains same ");
        assertTrue(66.99 == qualifiedDetails.getSunday().doubleValue());

        qualifiedDetails = rateDetailsByRatePlanRoomtype.get(1);
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getStartDate(), "Start date remains same ");
        assertEquals(new Date("03-Oct-2014"), qualifiedDetails.getEndDate(), "End date remains same ");
        assertTrue(77.49 == qualifiedDetails.getSunday().doubleValue());

        rateQualifiedDetails = getRateDetailsByRatePlanRoomtype(CHHART_NEW, STE).get(0);
        assertNotNull(rateQualifiedDetails);
        assertEquals(new Date("24-Apr-2013"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("19-Jun-2013"), rateQualifiedDetails.getEndDate());
        assertTrue(129.00 == rateQualifiedDetails.getSunday().doubleValue());
        System.clearProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED);
    }

    @Test
    public void testDiscontinuedRateHeaderDetailsPopulation() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.FALSE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        tenantCrudService.executeUpdateByNativeQuery("update Rate_Qualified set Status_ID = 2 where Rate_Code_Name = 'MISCE'");

        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);

        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        RateQualified rateQualified = secondRateExtract.get(5);
        assertEquals(new Integer(1), rateQualified.getStatusId(), "Status Id should be set to active once a discontinued rate code appears in the next extract");
        // discontinued
        List<RateQualifiedDetails> byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(LHHGVT, DBL);
        RateQualifiedDetails rateQualifiedDetails = byRatePlanRoomtype.get(0);
        assertTrue(1 == byRatePlanRoomtype.size(), "Future rate should be deleted for discontinued rate plans");
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue());

        byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(LHHGVT, DLX);
        rateQualifiedDetails = byRatePlanRoomtype.get(0);
        assertTrue(1 == byRatePlanRoomtype.size(), "Inprogress rate starting on snapshotdate should be deleted");
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue());

        byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(GAPSRP, Q);
        rateQualifiedDetails = byRatePlanRoomtype.get(1);
        assertTrue(4 == byRatePlanRoomtype.size(), "Inprogress rate doesnot come in extract");
        assertEquals(new Date("01-Oct-2014"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(191.00 == rateQualifiedDetails.getSunday().doubleValue());
        rateQualifiedDetails = byRatePlanRoomtype.get(2);
        assertEquals(new Date("06-Nov-2014"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("30-Nov-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(293.00 == rateQualifiedDetails.getSunday().doubleValue());

        assertEquals(ZNYIELD, secondRateExtract.get(7).getName());
        assertEquals(new Integer(1), secondRateExtract.get(7).getYieldable());
    }

    @Test
    public void testDiscontinuedRateHeaderDetailsPopulationWhenRateQualifiedDetailsTableBatchingDisabled() {

        System.setProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED, "false");

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.FALSE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        tenantCrudService.executeUpdateByNativeQuery("update Rate_Qualified set Status_ID = 2 where Rate_Code_Name = 'MISCE'");

        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);

        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        RateQualified rateQualified = secondRateExtract.get(5);
        assertEquals(new Integer(1), rateQualified.getStatusId(), "Status Id should be set to active once a discontinued rate code appears in the next extract");
        // discontinued
        List<RateQualifiedDetails> byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(LHHGVT, DBL);
        RateQualifiedDetails rateQualifiedDetails = byRatePlanRoomtype.get(0);
        assertTrue(1 == byRatePlanRoomtype.size(), "Future rate should be deleted for discontinued rate plans");
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue());

        byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(LHHGVT, DLX);
        rateQualifiedDetails = byRatePlanRoomtype.get(0);
        assertTrue(1 == byRatePlanRoomtype.size(), "Inprogress rate starting on snapshotdate should be deleted");
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue());

        byRatePlanRoomtype = getRateDetailsByRatePlanRoomtype(GAPSRP, Q);
        rateQualifiedDetails = byRatePlanRoomtype.get(1);
        assertTrue(4 == byRatePlanRoomtype.size(), "Inprogress rate doesnot come in extract");
        assertEquals(new Date("01-Oct-2014"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(191.00 == rateQualifiedDetails.getSunday().doubleValue());
        rateQualifiedDetails = byRatePlanRoomtype.get(2);
        assertEquals(new Date("06-Nov-2014"), rateQualifiedDetails.getStartDate());
        assertEquals(new Date("30-Nov-2014"), rateQualifiedDetails.getEndDate());
        assertTrue(293.00 == rateQualifiedDetails.getSunday().doubleValue());

        assertEquals(ZNYIELD, secondRateExtract.get(7).getName());
        assertEquals(new Integer(1), secondRateExtract.get(7).getYieldable());

        System.clearProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED);
    }


    @Test
    public void testSplitQualifiedRates() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualifiedDetails> splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, STE);
        assertNotNull(splitSrpSteDetails);

        RateQualifiedDetails rateQualifiedDetails = splitSrpSteDetails.get(0);
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate(), "Change for past detected for start date. No change in rates with two seasons ");
        assertEquals(new Date("31-Dec-2013"), rateQualifiedDetails.getEndDate(), "Change for past detected for end date. No change in rates with two seasons ");
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue(), "Change for past detected for sunday rate. No change in rates with two seasons ");
        rateQualifiedDetails = splitSrpSteDetails.get(1);
        assertEquals(new Date("01-Jan-2014"), rateQualifiedDetails.getStartDate(), "Split for future detected for start date.No change in rates with two seasons ");
        assertEquals(new Date("31-Dec-2023"), rateQualifiedDetails.getEndDate(), "Split for future detected for end date.No change in rates with two seasons");
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue(), "Split for future detected for sunday rate. No change in rates with two seasons");
        splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, DLX);

        String testTitle = "Split case splited rate details for in past ";
        rateQualifiedDetails = splitSrpSteDetails.get(0);
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(96.35 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        testTitle = "Split case splited rate details for in future ";
        rateQualifiedDetails = splitSrpSteDetails.get(1);
        assertEquals(new Date("04-Oct-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("31-Dec-2023"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(96.75 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, DBL);
        String testTitle1 = "Multiple Season rate and date change : Actual object \n ";
        rateQualifiedDetails = splitSrpSteDetails.get(0);

        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("31-Dec-2013"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(93.33 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        rateQualifiedDetails = splitSrpSteDetails.get(1);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("01-Jan-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(133.33 == rateQualifiedDetails.getSunday().doubleValue(), testTitle);
        assertTrue(296.00 == rateQualifiedDetails.getMonday().doubleValue(), testTitle);
        assertTrue(346.00 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        assertTrue(496.00 == rateQualifiedDetails.getWednesday().doubleValue(), testTitle);
        assertTrue(596.00 == rateQualifiedDetails.getThursday().doubleValue(), testTitle);
        assertTrue(696.00 == rateQualifiedDetails.getFriday().doubleValue(), testTitle);
        assertTrue(796.00 == rateQualifiedDetails.getSaturday().doubleValue(), testTitle);
        rateQualifiedDetails = splitSrpSteDetails.get(2);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("04-Oct-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("30-Nov-2014"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(396.33 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        rateQualifiedDetails = splitSrpSteDetails.get(3);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("01-Dec-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("31-Dec-2023"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(397.00 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        testTitle1 = "New room type : Actual object \n ";
        splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, Q);
        rateQualifiedDetails = splitSrpSteDetails.get(1);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("01-Jan-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("30-Nov-2014"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(396.33 == rateQualifiedDetails.getTuesday().doubleValue(), testTitle);
        testTitle1 = "Season changes but rate doesnt. No split  \n ";
        splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(SPLIT_SRP, K);
        rateQualifiedDetails = splitSrpSteDetails.get(0);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("01-Oct-2010"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("02-Oct-2014"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(99.00 == rateQualifiedDetails.getSunday().doubleValue(), testTitle);
        rateQualifiedDetails = splitSrpSteDetails.get(1);
        testTitle = testTitle1 + rateQualifiedDetails.toString();
        assertEquals(new Date("03-Oct-2014"), rateQualifiedDetails.getStartDate(), testTitle);
        assertEquals(new Date("31-Dec-2023"), rateQualifiedDetails.getEndDate(), testTitle);
        assertTrue(96.00 == rateQualifiedDetails.getSunday().doubleValue(), testTitle);
    }

    @Test
    public void testMisceQualifiedRates() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualifiedDetails> splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(MISCE, Q);
        assertNotNull(splitSrpSteDetails);

        RateQualifiedDetails rateQualifiedDetails = splitSrpSteDetails.get(1);
        assertEquals(new Date("04-Oct-2014"), rateQualifiedDetails.getStartDate(), "Snapshot = start date = end date ");
        assertEquals(new Date("04-Oct-2014"), rateQualifiedDetails.getEndDate(), "Snapshot = start date = end date ");
        assertTrue(91.33 == rateQualifiedDetails.getSunday().doubleValue(), " ");
        splitSrpSteDetails = getRateDetailsByRatePlanRoomtype(MISCE, DBL);
        assertNotNull(splitSrpSteDetails);

        rateQualifiedDetails = splitSrpSteDetails.get(1);
        assertEquals(new Date("04-Oct-2014"), rateQualifiedDetails.getStartDate(), "Snapshot = start date = end date ");
        assertEquals(new Date("31-Dec-2014"), rateQualifiedDetails.getEndDate(), "Snapshot = start date = end date ");
        assertTrue(91.33 == rateQualifiedDetails.getSunday().doubleValue(), " ");
    }

    @Test
    public void testAdjustRateHeaderStartDateAsPerRateDetails() {
        String testTitle = "Adjust RateHeader start date based on min details date in case of details are in past";
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.TRUE);
        when(configParamsService.getBooleanParameterValue(USE_SRP_ACTIVE_DATES)).thenReturn(false);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath
                        , snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        assertNotNull(secondRateExtract);
        RateQualified rateQualified = secondRateExtract.get(8);
        assertEquals(HEADER_ADJUST, rateQualified.getName(), testTitle);
        assertEquals(new Date("24-Mar-2013"), rateQualified.getStartDate(), testTitle);
        assertEquals(new Date("03-Oct-2014"), rateQualified.getEndDate(), testTitle);
    }

    @Test
    public void testAdjustRateHeaderStartDateAsPerRateDetailsWhenRateQualifiedDetailsTableBatchingDisabled() {
        System.setProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED, "false");
        String testTitle = "Adjust RateHeader start date based on min details date in case of details are in past";
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.TRUE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath
                        , snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        assertNotNull(secondRateExtract);
        RateQualified rateQualified = secondRateExtract.get(8);
        assertEquals(HEADER_ADJUST, rateQualified.getName(), testTitle);
        assertEquals(new Date("24-Mar-2013"), rateQualified.getStartDate(), testTitle);
        assertEquals(new Date("03-Oct-2014"), rateQualified.getEndDate(), testTitle);
        System.clearProperty(RATE_QUALIFIED_DETAILS_TABLE_BATCH_ENABLED);
    }

    @Test
    public void testDoNotAdjustRateHeaderStartDateAsPerRateDetails() {
        String testTitle = "Adjust RateHeader start date based on min details date in case of details are in past";
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.FALSE);
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath
                        , snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
        List<RateQualified> secondRateExtract = tenantCrudService.findAll(RateQualified.class);
        assertNotNull(secondRateExtract);
        RateQualified rateQualified = secondRateExtract.get(8);
        assertEquals(HEADER_ADJUST, rateQualified.getName(), testTitle);
        assertEquals(new Date("24-Apr-2013"), rateQualified.getStartDate(), testTitle);
        assertEquals(new Date("03-Oct-2014"), rateQualified.getEndDate(), testTitle);
    }

    private List<RateQualifiedDetails> getRateDetailsByRatePlanRoomtype(String ratePlan, String accomTypeName) {
        RateQualified byNamedQuerySingleResult = tenantCrudService.findByNamedQuerySingleResult(RateQualified.BY_PROPERTY_AND_RATE_CODE_NAME,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("rateCodeName", ratePlan).parameters());
        return tenantCrudService.findByNamedQuery(
                RateQualifiedDetails.BY_RATE_PLAN_ID_AND_ACCOM_TYPE_ID, QueryParameter.with("rateQualifiedId",
                        byNamedQuerySingleResult.getId()).and("accomTypeId", cacheAccomData.get(accomTypeName)).parameters());
    }

    private void assertRateQualifiedDetails(String startDate, String endDate, RateQualifiedDetails rateQualifiedDetails, double sundayRate, double mondayRate, double tuesdayRate, double wednesdayRate, double thursdayRate, double fridayRate, double saturdayRate) {
        assertEquals(new Date(startDate), new Date(rateQualifiedDetails.getStartDate().getTime()));
        assertEquals(new Date(endDate), new Date(rateQualifiedDetails.getEndDate().getTime()));
        assertTrue(sundayRate == rateQualifiedDetails.getSunday().doubleValue());
        assertTrue(mondayRate == rateQualifiedDetails.getMonday().doubleValue());
        assertTrue(tuesdayRate == rateQualifiedDetails.getTuesday().doubleValue());
        assertTrue(wednesdayRate == rateQualifiedDetails.getWednesday().doubleValue());
        assertTrue(thursdayRate == rateQualifiedDetails.getThursday().doubleValue());
        assertTrue(fridayRate == rateQualifiedDetails.getFriday().doubleValue());
        assertTrue(saturdayRate == rateQualifiedDetails.getSaturday().doubleValue());
    }

    private void assertRateQualified() {
        final List<RateQualified> all = tenantCrudService.findAll(RateQualified.class);
        assertNotNull(all);
        assertEquals(9, all.size());
        final RateQualified rateQualified = all.get(3);
        assertEquals(WKDD, rateQualified.getName());
        assertEquals(WKDD, rateQualified.getDescription());
        assertEquals(new Date("28-Sep-2014"), rateQualified.getStartDate());
        assertEquals(new Date("14-Oct-2173"), rateQualified.getEndDate());
        assertEquals("INR", rateQualified.getCurrency());
        assertEquals("LV1", rateQualified.getReferenceRateCode());
        assertNull(rateQualified.getRemarks());
        assertEquals(new Integer(1), rateQualified.getStatusId());
        assertEquals(new Integer(1), rateQualified.getYieldable());
        assertEquals(new Integer(1), rateQualified.getPriceRelative());
        assertEquals(new Integer(1), rateQualified.getIncludesPackage());
    }

    private void assertRateQualifiedDetails(String startDate, String endDate) {
        List<RateQualifiedDetails> all = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertNotNull(all);
        final RateQualifiedDetails rateQualifiedDetails = all.get(1);
        assertRateQualifiedDetails(startDate, endDate, rateQualifiedDetails, 129.55, 139.00, 149.00, 159.00, 169.00, 179.00, 189.00);
    }

    private String readInput(File input) {
        BufferedReader bufferedReader = null;
        StringBuilder stringBuffer = new StringBuilder();
        try {
            bufferedReader = new BufferedReader(new FileReader(input));
            String str;
            while ((str = bufferedReader.readLine()) != null) {
                stringBuffer.append(str);
            }
        } catch (IOException e) {
        } finally {
            IOUtils.closeQuietly(bufferedReader);
        }
        return stringBuffer.toString();
    }

    FileMetadata createFileMetadata(String rateFilePath, Date snapShotDate, Date snapShotTime,
                                    Date preparedDate, Date preparedTime, int ratePlanType) {
        FileMetadata fileMetadata = setDefaultValuesToFileMetadata(rateFilePath, snapShotDate, snapShotTime,
                preparedDate, preparedTime, ratePlanType);
        return tenantCrudService.save(fileMetadata);
    }

    private FileMetadata setDefaultValuesToFileMetadata(String rateFilePath, Date snapShotDate,
                                                        Date snapShotTime, Date preparedDate, Date preparedTime, int ratePlanType) {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setRecordTypeId(ratePlanType);
        fileMetadata.setFileName(rateFilePath);
        fileMetadata.setFileLocation(rateFilePath);
        fileMetadata.setTenantPropertyId(PacmanWorkContextHelper.getPropertyId());
        fileMetadata.setSnapshotDt(snapShotDate);
        fileMetadata.setSnapshotTm(new java.sql.Time(snapShotTime.getTime()));
        fileMetadata.setPreparedDt(preparedDate);
        fileMetadata.setPreparedTm(new java.sql.Time(preparedTime.getTime()));
        fileMetadata.setProcessStatusId(Constants.PROCESS_STATUS_PROGRESS);
        return fileMetadata;
    }

    public void testRateAdjustmentsNew() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), null, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments.get(0);

    }

    @Test
    public void testRateAdjustments() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertRateAdjustments(rateQualifiedAdjustments, "31-Dec-2023", "03-Oct-2014");
    }

    @Test
    public void testRateAdjustmentsDeleteAllFuture() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertTrue(rateQualifiedAdjustments.size() == 3);

        snapShotDate = new Date("03-Oct-2014");
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);

        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertTrue(rateQualifiedAdjustments2.size() == 3);
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments2.get(0);
        assertEquals(new Date("03-Oct-2014"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(new Integer(3), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(new Integer(1), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertTrue(-450.35f == rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableCost.name(), rateQualifiedAdjustment.getAdjustmentType());

        rateQualifiedAdjustment = rateQualifiedAdjustments2.get(1);
        assertEquals(new Date("03-Oct-2014"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(new Integer(1), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(new Integer(2), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertTrue(350.21f == rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment.getAdjustmentType());

    }

    @Test
    void testRateAdjustmentsDeleteAllFuture_deleteInProgress() {
        qualifiedRateService.processRates(moreRateAdjustmentDef.getRateHeader(), snapShotDate,
                createFileMetadata(moreRateAdjFile, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(5, rateQualifiedAdjustments.size());

        snapShotDate = new Date("04-Oct-2014");
        qualifiedRateService.processRates(lessRateAdjustmentDef.getRateHeader(), snapShotDate,
                createFileMetadata(lessRateAdj, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);

        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME,
                QueryParameter.with("rateCodeName", RATE_CODE_5PCTB).parameters());
        assertEquals(1, rateQualifiedAdjustments2.size());
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments2.get(0);
        assertEquals(new Date("04-Oct-2014"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(Integer.valueOf(3), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(-450.35f, rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableCost.name(), rateQualifiedAdjustment.getAdjustmentType());

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsLHHGVTs = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME,
                QueryParameter.with("rateCodeName", LHHGVT).parameters());
        assertEquals(0, rateQualifiedAdjustmentsLHHGVTs.size());

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsWKDD = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME,
                QueryParameter.with("rateCodeName", WKDD).parameters());
        assertEquals(1, rateQualifiedAdjustmentsWKDD.size());
        RateQualifiedAdjustment rateQualifiedAdjustment1 = rateQualifiedAdjustmentsWKDD.get(0);
        assertEquals(new Date("04-Oct-2014"), new Date(rateQualifiedAdjustment1.getStartDate().getTime()), "rateQualifiedAdjustment1 start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment1.getEndDate().getTime()), "rateQualifiedAdjustment1 end date doesnt not match ");
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment1.getNetValueTypeId());
        assertEquals(Integer.valueOf(2), rateQualifiedAdjustment1.getPostingRuleTypeId());
        assertEquals(243.21f, rateQualifiedAdjustment1.getNetValue());
        assertEquals(ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment1.getAdjustmentType());
    }

    @Test
    public void testRateAdjustmentsExisting_doesNotOverlap() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(3, rateQualifiedAdjustments.size());

        snapShotDate = new Date("31-Dec-2015");
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        tenantCrudService.clear();
        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(3, rateQualifiedAdjustments2.size());
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments2.get(0);
        assertEquals(new Date("31-Dec-2015"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(Integer.valueOf(3), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(-450.35f, rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableCost.name(), rateQualifiedAdjustment.getAdjustmentType());

        rateQualifiedAdjustment = rateQualifiedAdjustments2.get(1);
        assertEquals(new Date("31-Dec-2015"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(2), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(350.21f, rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment.getAdjustmentType());

        rateQualifiedAdjustment = rateQualifiedAdjustments2.get(2);
        assertEquals(new Date("31-Dec-2015"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2019"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(2), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(365.25f, rateQualifiedAdjustment.getNetValue());
        assertEquals(Constants.ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment.getAdjustmentType());
    }

    @Test
    public void testNoRateAdjustmentinNewFeed() throws Exception {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertTrue(rateQualifiedAdjustments.size() == 3);

        List<String> rateCodeNames = Arrays.asList("MISCE", RATE_CODE_5PCTB);
        RateQualified rq = new RateQualified();
        rq.setName(RATE_CODE_5PCTB);
        List<RateDefinition.RateHeader.RateAdjustment> rateadjList = new ArrayList<RateDefinition.RateHeader.RateAdjustment>();
        Date snapDate = new Date("01-Jan-2015");
        qualifiedRateService.updateAllCurrentRateAdjustments(rateCodeNames, snapDate, false, new ArrayList<>());
        tenantCrudService.clear();
        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        RateQualifiedAdjustment o1 = populateObj(-113.35f, "03-Oct-2014", "31-Dec-2014", "YieldableCost", 1, 3);
        RateQualifiedAdjustment o2 = populateObj(242.21f, "03-Oct-2014", "31-Dec-2014", "YieldableValue", 2, 1);
        assertTrue(checkObjectExistsinList(o1, rateQualifiedAdjustments2));
        assertTrue(checkObjectExistsinList(o2, rateQualifiedAdjustments2));
    }

    @Test
    public void testNoYCDataInFeed() throws Exception {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertTrue(rateQualifiedAdjustments.size() == 3);

        List<RateDefinition.RateHeader.RateAdjustment> rateadjList = new ArrayList<RateDefinition.RateHeader.RateAdjustment>();
        Date snapDate = new Date("01-Jan-2015");
        RateQualified rateQualified = new RateQualified();
        rateQualified.setId(1);
        rateQualified.setName(RATE_CODE_5PCTB);

        qualifiedRateService.processRateAdjustments(rateadjList, rateQualified, snapDate);
        tenantCrudService.clear();
        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        RateQualifiedAdjustment o1 = populateObj(-113.35f, "03-Oct-2014", "31-Dec-2023", "YieldableCost", 1, 3);
        RateQualifiedAdjustment o2 = populateObj(242.21f, "03-Oct-2014", "31-Dec-2023", "YieldableValue", 2, 1);
        RateQualifiedAdjustment o3 = populateObj(365.25f, "03-Oct-2014", "31-Dec-2019", "YieldableValue", 2, 1);
        assertTrue(checkObjectExistsinList(o1, rateQualifiedAdjustments2));
        assertTrue(checkObjectExistsinList(o2, rateQualifiedAdjustments2));
        assertTrue(checkObjectExistsinList(o3, rateQualifiedAdjustments2));
    }

    @Test
    public void testRecordforNoChange() throws Exception {
        Date snapShotDate = new Date("10-Feb-2010");
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        List<RateQualifiedAdjustment> rateQualifiedAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(4, rateQualifiedAdjustments.size());

        snapShotDate = new Date("11-Feb-2010");
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(secondFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        tenantCrudService.clear();
        List<RateQualifiedAdjustment> rateQualifiedAdjustments2 = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(4, rateQualifiedAdjustments2.size());

        RateQualifiedAdjustment o1 = populateObj(365.25f, "11-Feb-2010", "31-Dec-2019", "YieldableValue", 2, 1);
        assertTrue(checkObjectExistsinList(o1, rateQualifiedAdjustments2));
    }

    @Test
    public void getActiveG3ManagedQualifiedRatesShouldNotFailIfNoRecordsFound() throws Exception {
        final List<RateQualified> activeManagedQualifiedRates = qualifiedRateService.getActiveG3ManagedQualifiedRates(propertyId);
        assertEquals(0, activeManagedQualifiedRates.size());
    }

    @Test
    public void getActiveManagedQualifiedRatesHappyFlow() throws Exception {
        final Date rateStartDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified1.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified1.setManagedInG3(Boolean.TRUE);
        rateQualified1.setStatusId(ACTIVE_STATUS_ID);

        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "ABC");
        rateQualified2.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified2.setManagedInG3(Boolean.TRUE);
        rateQualified2.setStatusId(INACTIVE_STATUS_ID);

        RateQualified rateQualified3 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "EFG");
        rateQualified3.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified3.setManagedInG3(Boolean.FALSE);
        rateQualified3.setStatusId(ACTIVE_STATUS_ID);

        RateQualified rateQualified4 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "XYZ");
        rateQualified4.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified4.setManagedInG3(Boolean.FALSE);
        rateQualified4.setStatusId(ACTIVE_STATUS_ID);

        tenantCrudService.save(Arrays.asList(rateQualified1, rateQualified2, rateQualified3, rateQualified4));

        final List<RateQualified> activeManagedQualifiedRates = qualifiedRateService.getActiveG3ManagedQualifiedRates(propertyId);
        assertEquals(1, activeManagedQualifiedRates.size());
        final Optional<RateQualified> rateQualifiedOptional = activeManagedQualifiedRates.stream().filter(rateQualified -> Objects.equals(rateQualified.getId(), rateQualified1.getId())).findFirst();
        assertTrue(rateQualifiedOptional.isPresent(), "Record with required ID should exist");
        assertEquals(rateQualified1.getId(), rateQualifiedOptional.get().getId());
    }

    @Test
    public void getActiveRateQualifiedTest() throws Exception {
        final Date rateStartDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified1.setName("rateQualified1");
        rateQualified1.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified1.setStatusId(ACTIVE_STATUS_ID);

        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "ABC");
        rateQualified2.setName("rateQualified2");
        rateQualified2.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified2.setStatusId(INACTIVE_STATUS_ID);

        tenantCrudService.save(Arrays.asList(rateQualified1, rateQualified2));

        final List<RateQualifiedActiveDto> activeQualifiedRates = qualifiedRateService.getActiveRateQualified();

        RateQualifiedActiveDto expectedRate1 = RateQualifiedActiveDto.builder()
                .name("rateQualified1")
                .rateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE)
                .startDate(rateStartDate)
                .endDate(rateEndDate)
                .yieldable(1)
                .priceRelative(500)
                .referenceRateCode("None")
                .includesPackage(1)
                .lastUpdatedDate(rateQualified1.getLastUpdatedDate())
                .build();

        assertEquals(1, activeQualifiedRates.size());
        assertEquals(expectedRate1, activeQualifiedRates.get(0));
    }

    @Test
    public void excludeRateDetailsForManagedRatesWhenNoManagedRatesAvailableInDB() throws Exception {
        RateDetails rateDetails1 = new RateDetails(1, 1);
        RateDetails rateDetails2 = new RateDetails(2, 1);

        final List<RateDetails> rateDetails = qualifiedRateService.excludeRateDetailsForManagedRates(
                Arrays.asList(rateDetails1, rateDetails2), Collections.emptyList());
        assertEquals(2, rateDetails.size());
    }

    @Test
    public void excludeRateDetailsForManagedRatesHappyFlow() throws Exception {
        RateDetails rateDetails1 = new RateDetails(1, 1);
        RateDetails rateDetails2 = new RateDetails(2, 1);

        final List<RateDetails> rateDetails = qualifiedRateService.excludeRateDetailsForManagedRates(Arrays.asList(rateDetails1, rateDetails2), Collections.singletonList(1));
        assertEquals(1, rateDetails.size());
        assertEquals(rateDetails2.getRateQualifiedId(), rateDetails.get(0).getRateQualifiedId());
    }

    @Test
    public void excludeRateQualifiedDetailsForManagedRatesWhenNoManagedRatesAvailableInDB() throws Exception {
        RateQualifiedDetails rateQualifiedDetails1 = new RateQualifiedDetails();
        rateQualifiedDetails1.setRateQualifiedId(1);
        RateQualifiedDetails rateQualifiedDetails2 = new RateQualifiedDetails();
        rateQualifiedDetails2.setRateQualifiedId(2);
        final List<RateQualifiedDetails> rateQualifiedDetailsList =
                qualifiedRateService.excludeRateQualifiedDetailsForManagedRates(
                        Arrays.asList(rateQualifiedDetails1, rateQualifiedDetails2), Collections.emptyList());
        assertEquals(2, rateQualifiedDetailsList.size());
    }

    @Test
    public void excludeRateQualifiedDetailsForManagedRatesHappyFlow() throws Exception {
        RateQualifiedDetails rateQualifiedDetails1 = new RateQualifiedDetails();
        rateQualifiedDetails1.setRateQualifiedId(1);
        RateQualifiedDetails rateQualifiedDetails2 = new RateQualifiedDetails();
        rateQualifiedDetails2.setRateQualifiedId(2);
        final List<RateQualifiedDetails> rateQualifiedDetailsList =
                qualifiedRateService.excludeRateQualifiedDetailsForManagedRates(
                        Arrays.asList(rateQualifiedDetails1, rateQualifiedDetails2), Collections.singletonList(1));
        assertEquals(1, rateQualifiedDetailsList.size());
        assertEquals(rateQualifiedDetails2.getRateQualifiedId(), rateQualifiedDetailsList.get(0).getRateQualifiedId());
    }

    @Test
    public void deleteAllFutureQualifiedRateDetailsShouldNotFailIfNoManagedRatesFoundInDB() throws Exception {
        final Date snapShotDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final int deletedRecords = qualifiedRateService.deleteAllFutureRateDetails(snapShotDate, RATE_QUALIFIED);
        assertEquals(0, deletedRecords);
    }

    @Test
    public void deleteAllFutureQualifiedRateDetailsShouldNotDeleteRateDetailsForManagedRates() throws Exception {
        final Date snapShotDate = DateUtil.getDate(25, 3, 2013, 00, 00, 00);
        final Date rateStartDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified.setManagedInG3(Boolean.TRUE);
        tenantCrudService.save(rateQualified);

        final RateQualifiedDetails rateQualifiedDetailsForDLXAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(rateStartDate, rateEndDate, propertyId, rateQualified.getId(), 4, 1.1);
        final RateQualifiedDetails rateQualifiedDetailsForDBLAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(DateUtil.addDaysToDate(rateStartDate, 1), rateEndDate, propertyId, rateQualified.getId(), 6, 1.1);
        final RateQualifiedDetails rateQualifiedDetailsForQAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(DateUtil.addDaysToDate(rateStartDate, 2), rateEndDate, propertyId, rateQualified.getId(), 7, 1.1);

        tenantCrudService.save(Arrays.asList(rateQualifiedDetailsForDLXAccomType, rateQualifiedDetailsForDBLAccomType, rateQualifiedDetailsForQAccomType));
        flushAndClear();
        final int deletedRecords = qualifiedRateService.deleteAllFutureRateDetails(snapShotDate, RATE_QUALIFIED);
        assertEquals(0, deletedRecords);
    }

    @Test
    public void deleteAllFutureQualifiedRateDetailsShouldDeleteRateDetails() throws Exception {
        final Date snapShotDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateStartDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified1.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified1.setManagedInG3(Boolean.FALSE);

        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "ABC");
        rateQualified2.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified2.setManagedInG3(Boolean.FALSE);

        tenantCrudService.save(Arrays.asList(rateQualified1, rateQualified2));

        final RateQualifiedDetails rateQualifiedDetailsForDLXAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(DateUtil.addDaysToDate(rateStartDate, 2), rateEndDate, propertyId, rateQualified1.getId(), 4, 1.1);
        final RateQualifiedDetails rateQualifiedDetailsForDBLAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(DateUtil.addDaysToDate(rateStartDate, 3), rateEndDate, propertyId, rateQualified2.getId(), 6, 1.1);

        tenantCrudService.save(Arrays.asList(rateQualifiedDetailsForDLXAccomType, rateQualifiedDetailsForDBLAccomType));

        final int deletedRecords = qualifiedRateService.deleteAllFutureRateDetails(snapShotDate, RATE_QUALIFIED);
        assertEquals(2, deletedRecords);
    }

    @Test
    public void fetchDistinctRateDetailsHappyFlow() throws Exception {
        final Date rateStartDate = DateUtil.getDate(22, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified1.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified1.setManagedInG3(Boolean.TRUE);

        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "ABC");
        rateQualified2.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified2.setManagedInG3(Boolean.FALSE);

        tenantCrudService.save(Arrays.asList(rateQualified1, rateQualified2));

        final RateQualifiedDetails rateQualifiedDetailsForDLXAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(rateStartDate, rateEndDate, propertyId, rateQualified1.getId(), 4, 1.1);
        final RateQualifiedDetails rateQualifiedDetailsForDBLAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(rateStartDate, rateEndDate, propertyId, rateQualified2.getId(), 6, 1.1);

        tenantCrudService.save(Arrays.asList(rateQualifiedDetailsForDLXAccomType, rateQualifiedDetailsForDBLAccomType));
        flushAndClear();

        final List<RateDetails> rateDetailsList = qualifiedRateService.fetchDistinctRateDetailsExcluding(Collections.singletonList(rateQualified1.getId()));
        assertEquals(1, rateDetailsList.size());
        assertEquals(rateQualified2.getId(), rateQualifiedDetailsForDBLAccomType.getRateQualifiedId());
    }

    @Test
    public void getActiveOnDateRateQualifiedDetailsHappyFlow() throws Exception {
        final Date snapShotDate = DateUtil.getDate(24, 3, 2013, 00, 00, 00);
        final Date rateStartDate = DateUtil.getDate(22, 3, 2013, 00, 00, 00);
        final Date rateEndDate = DateUtil.getDate(19, 5, 2013, 00, 00, 00);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "CHHART");
        rateQualified1.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified1.setManagedInG3(Boolean.TRUE);

        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDateAndName(rateStartDate, rateEndDate, propertyId, "ABC");
        rateQualified2.setRateQualifiedTypeId(QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualified2.setManagedInG3(Boolean.FALSE);

        tenantCrudService.save(Arrays.asList(rateQualified1, rateQualified2));

        final RateQualifiedDetails rateQualifiedDetailsForDLXAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(rateStartDate, rateEndDate, propertyId, rateQualified1.getId(), 4, 1.1);
        final RateQualifiedDetails rateQualifiedDetailsForDBLAccomType = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(rateStartDate, rateEndDate, propertyId, rateQualified2.getId(), 6, 1.1);

        tenantCrudService.save(Arrays.asList(rateQualifiedDetailsForDLXAccomType, rateQualifiedDetailsForDBLAccomType));
        flushAndClear();

        final List<RateQualifiedDetails> rateQualifiedDetailsList = qualifiedRateService.getActiveOnDateRateQualifiedDetails(snapShotDate, Collections.singletonList(rateQualified1.getId()));
        assertEquals(1, rateQualifiedDetailsList.size());
        assertEquals(rateQualifiedDetailsForDBLAccomType.getId(), rateQualifiedDetailsList.get(0).getId());
    }

    @Test
    public void processRatesShouldActivateExistingInactiveManagedRate() throws Exception {

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.FALSE);

        firstExtractExecution();

        List<RateQualified> rateQualifiedListFromFirstExtract = tenantCrudService.findByNamedQuery(RateQualified.ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        assertEquals(9, rateQualifiedListFromFirstExtract.size());


        final RateQualified rateQualifiedFromFirstExtract = qualifiedRateService.findSingleFixedRateByName(RATE_CODE_5PCTB);
        assertNotNull(rateQualifiedFromFirstExtract);

        List<RateQualifiedDetails> rateQualifiedDetailsListFromFirstExtract =
                tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID, QueryParameter.with("rateQualifiedId", rateQualifiedFromFirstExtract.getId()).parameters());
        assertEquals(10, rateQualifiedDetailsListFromFirstExtract.size());

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsFromFirstExtract
                = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME, QueryParameter.with("rateCodeName", RATE_CODE_5PCTB).parameters());
        assertEquals(2, rateQualifiedAdjustmentsFromFirstExtract.size());
        assertRateAdjustmentsFromFirstExtract(rateQualifiedAdjustmentsFromFirstExtract);

        userMakesExistingRateAsG3ManagedAndUpdatesStatus(RATE_CODE_5PCTB, INACTIVE_STATUS_ID);

        final List<RateQualified> rateQualifiedListFromDB = tenantCrudService.findByNamedQuery(RateQualified.ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        final List<RateQualified> rateQualifiedListNotManagedByG3 = rateQualifiedListFromDB.stream().filter(rateQualified -> !rateQualified.getManagedInG3()).collect(Collectors.toList());
        assertEquals(rateQualifiedListFromFirstExtract.size() - 1, rateQualifiedListNotManagedByG3.size());

        nextDayExtractExecution();

        List<RateQualified> rateQualifiedListFromSecondExtract = tenantCrudService.findByNamedQuery(RateQualified.ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        assertEquals(10, rateQualifiedListFromSecondExtract.size());

        final RateQualified rateQualifiedFromSecondExtract = qualifiedRateService.findSingleRateByNameAndStatusId(RATE_CODE_5PCTB, ACTIVE_STATUS_ID);
        assertEquals(rateQualifiedFromFirstExtract.getId(), rateQualifiedFromSecondExtract.getId(), "ID should be same");
        assertEquals(rateQualifiedFromFirstExtract.getName(), rateQualifiedFromSecondExtract.getName());
        assertFalse(rateQualifiedFromSecondExtract.getManagedInG3());

        List<RateQualifiedDetails> rateQualifiedDetailsListFromSecondExtract =
                tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID, QueryParameter.with("rateQualifiedId", rateQualifiedFromSecondExtract.getId()).parameters());
        assertEquals(10, rateQualifiedDetailsListFromSecondExtract.size());

        assertRateQualifiedDetailsList(rateQualifiedDetailsListFromSecondExtract);

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsFromSecondExtract = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME, QueryParameter.with("rateCodeName", RATE_CODE_5PCTB).parameters());
        assertEquals(2, rateQualifiedAdjustmentsFromSecondExtract.size());
        assertRateAdjustmentsFromSecondExtract(rateQualifiedAdjustmentsFromSecondExtract, "04-Oct-2014", "31-Dec-2023");
    }

    private void assertRateQualifiedDetailsList(List<RateQualifiedDetails> rateQualifiedDetailsListFromSecondExtract) {
        RateQualifiedDetails rateQualifiedDetails = rateQualifiedDetailsListFromSecondExtract.get(1);
        assertRateQualifiedDetails("22-May-2014", "03-Oct-2014", rateQualifiedDetails, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49, 66.49);

        rateQualifiedDetails = rateQualifiedDetailsListFromSecondExtract.get(2);
        assertRateQualifiedDetails("01-Oct-2014", "02-Oct-2014", rateQualifiedDetails, 66.99, 66.99, 66.99, 66.99, 66.99, 66.99, 66.99);

        rateQualifiedDetails = rateQualifiedDetailsListFromSecondExtract.get(3);
        assertRateQualifiedDetails("03-Oct-2014", "03-Oct-2014", rateQualifiedDetails, 77.49, 77.49, 77.49, 77.49, 77.49, 77.49, 77.49);
    }

    @Test
    public void testProcessRatesForManagedRate() throws Exception {

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SET_ALL_SRPS_AS_YIELDABLE))
                .thenReturn(Boolean.FALSE);

        firstExtractExecution();

        final RateQualified rateQualifiedFromFirstExtract = qualifiedRateService.findSingleFixedRateByName(RATE_CODE_5PCTB);
        assertNotNull(rateQualifiedFromFirstExtract);

        List<RateQualifiedDetails> rateQualifiedDetailsListFromFirstExtract =
                tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID, QueryParameter.with("rateQualifiedId", rateQualifiedFromFirstExtract.getId()).parameters());
        assertEquals(10, rateQualifiedDetailsListFromFirstExtract.size());

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsFromFirstExtract
                = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME, QueryParameter.with("rateCodeName", RATE_CODE_5PCTB).parameters());
        assertEquals(2, rateQualifiedAdjustmentsFromFirstExtract.size());
        assertRateAdjustmentsFromFirstExtract(rateQualifiedAdjustmentsFromFirstExtract);

        userMakesExistingRateAsG3ManagedAndUpdatesStatus(RATE_CODE_5PCTB, ACTIVE_STATUS_ID);

        nextDayExtractExecution();

        final RateQualified rateQualifiedFromSecondExtract = qualifiedRateService.findSingleFixedRateByName(RATE_CODE_5PCTB);
        assertRateQualified(rateQualifiedFromFirstExtract, rateQualifiedFromSecondExtract);

        List<RateQualifiedDetails> rateQualifiedDetailsListFromSecondExtract =
                tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID, QueryParameter.with("rateQualifiedId", rateQualifiedFromSecondExtract.getId()).parameters());
        assertEquals(10, rateQualifiedDetailsListFromSecondExtract.size());
        assertRateQualifiedDetails(rateQualifiedDetailsListFromFirstExtract, rateQualifiedDetailsListFromSecondExtract);

        List<RateQualifiedAdjustment> rateQualifiedAdjustmentsFromSecondExtract = tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME, QueryParameter.with("rateCodeName", RATE_CODE_5PCTB).parameters());
        assertEquals(2, rateQualifiedAdjustmentsFromSecondExtract.size());
        assertRateAdjustmentsFromSecondExtract(rateQualifiedAdjustmentsFromSecondExtract, "03-Oct-2014", "31-Dec-2023");
    }

    private void assertRateQualified(RateQualified rateQualifiedFromFirstExtract, RateQualified rateQualifiedFromSecondExtract) {
        assertEquals(rateQualifiedFromSecondExtract.getName(), rateQualifiedFromSecondExtract.getName());
        assertEquals(rateQualifiedFromFirstExtract.getDescription(), rateQualifiedFromSecondExtract.getDescription());
        assertEquals(rateQualifiedFromFirstExtract.getStartDate(), rateQualifiedFromSecondExtract.getStartDate());
        assertEquals(rateQualifiedFromFirstExtract.getEndDate(), rateQualifiedFromSecondExtract.getEndDate());
        assertEquals(rateQualifiedFromFirstExtract.getCurrency(), rateQualifiedFromSecondExtract.getCurrency());
        assertEquals(rateQualifiedFromFirstExtract.getReferenceRateCode(), rateQualifiedFromSecondExtract.getReferenceRateCode());
        assertEquals(rateQualifiedFromFirstExtract.getRemarks(), rateQualifiedFromSecondExtract.getRemarks());
        assertEquals(rateQualifiedFromFirstExtract.getStatusId(), rateQualifiedFromSecondExtract.getStatusId());
        assertEquals(rateQualifiedFromFirstExtract.getYieldable(), rateQualifiedFromSecondExtract.getYieldable());
        assertEquals(rateQualifiedFromFirstExtract.getPriceRelative(), rateQualifiedFromSecondExtract.getPriceRelative());
        assertEquals(rateQualifiedFromFirstExtract.getIncludesPackage(), rateQualifiedFromSecondExtract.getIncludesPackage());
    }

    private void assertRateAdjustmentsFromSecondExtract(List<RateQualifiedAdjustment> rateQualifiedAdjustments, String startDate, String endDateString) {
        //assertRateAdjustments(rateQualifiedAdjustments, endDateString, startDate);
        assertNextDayExtractRateAdjustments(rateQualifiedAdjustments);
    }

    private void assertNextDayExtractRateAdjustments(List<RateQualifiedAdjustment> rateQualifiedAdjustments) {
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments.get(0);
        assertEquals(new Date("04-Oct-2014"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(new Integer(3), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(new Integer(1), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(-450.35f, rateQualifiedAdjustment.getNetValue());
        assertEquals(ADJUSTMENT_TYPE.YieldableCost.name(), rateQualifiedAdjustment.getAdjustmentType());

        rateQualifiedAdjustment = rateQualifiedAdjustments.get(1);
        assertEquals(new Date("04-Oct-2014"), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date("31-Dec-2023"), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(2), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(350.21f, rateQualifiedAdjustment.getNetValue());
        assertEquals(ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment.getAdjustmentType());
    }

    private void assertRateQualifiedDetails(List<RateQualifiedDetails> rateQualifiedDetailsListAfterFirstExtract, List<RateQualifiedDetails> rateQualifiedDetailsListAfterSecondExtract) {
        rateQualifiedDetailsListAfterFirstExtract.sort(Comparator.comparing(RateQualifiedDetails::getId));
        rateQualifiedDetailsListAfterSecondExtract.sort(Comparator.comparing(RateQualifiedDetails::getId));
        for (int i = 0; i < rateQualifiedDetailsListAfterFirstExtract.size(); i++) {
            assertRateQualifiedDetails(rateQualifiedDetailsListAfterFirstExtract.get(i), rateQualifiedDetailsListAfterSecondExtract.get(i));
        }
    }

    private void assertRateQualifiedDetails(RateQualifiedDetails rateQualifiedDetailsFromFirstList, RateQualifiedDetails rateQualifiedDetailsFromSecondList) {
        assertEquals(rateQualifiedDetailsFromFirstList.getId(), rateQualifiedDetailsFromSecondList.getId());
        assertEquals(rateQualifiedDetailsFromFirstList.getRateQualifiedId(), rateQualifiedDetailsFromSecondList.getRateQualifiedId());
        assertEquals(rateQualifiedDetailsFromFirstList.getWednesday(), rateQualifiedDetailsFromSecondList.getWednesday());
        assertEquals(rateQualifiedDetailsFromFirstList.getTuesday(), rateQualifiedDetailsFromSecondList.getTuesday());
        assertEquals(rateQualifiedDetailsFromFirstList.getThursday(), rateQualifiedDetailsFromSecondList.getThursday());
        assertEquals(rateQualifiedDetailsFromFirstList.getSunday(), rateQualifiedDetailsFromSecondList.getSunday());
        assertEquals(rateQualifiedDetailsFromFirstList.getSaturday(), rateQualifiedDetailsFromSecondList.getSaturday());
        assertEquals(rateQualifiedDetailsFromFirstList.getMonday(), rateQualifiedDetailsFromSecondList.getMonday());
        assertEquals(rateQualifiedDetailsFromFirstList.getFriday(), rateQualifiedDetailsFromSecondList.getFriday());
        assertEquals(rateQualifiedDetailsFromFirstList.getAccomTypeId(), rateQualifiedDetailsFromSecondList.getAccomTypeId());
        assertEquals(rateQualifiedDetailsFromFirstList.getAccomTypeName(), rateQualifiedDetailsFromSecondList.getAccomTypeName());
        assertEquals(rateQualifiedDetailsFromFirstList.getDaysBetween(), rateQualifiedDetailsFromSecondList.getDaysBetween());
        assertEquals(rateQualifiedDetailsFromFirstList.getEndDate(), rateQualifiedDetailsFromSecondList.getEndDate());
        assertEquals(rateQualifiedDetailsFromFirstList.getStartDate(), rateQualifiedDetailsFromSecondList.getStartDate());
        assertEquals(rateQualifiedDetailsFromFirstList.getCreateDate(), rateQualifiedDetailsFromSecondList.getCreateDate());
        assertEquals(rateQualifiedDetailsFromFirstList.getCreatedByUserId(), rateQualifiedDetailsFromSecondList.getCreatedByUserId());
        assertEquals(rateQualifiedDetailsFromFirstList.getLastUpdatedByUserId(), rateQualifiedDetailsFromSecondList.getLastUpdatedByUserId());
        assertEquals(rateQualifiedDetailsFromFirstList.getLastUpdatedDate(), rateQualifiedDetailsFromSecondList.getLastUpdatedDate());
    }

    private void assertRateAdjustmentsFromFirstExtract(List<RateQualifiedAdjustment> rateQualifiedAdjustments) {
        assertRateAdjustments(rateQualifiedAdjustments, "31-Dec-2023", "03-Oct-2014");
    }

    private void assertRateAdjustments(List<RateQualifiedAdjustment> rateQualifiedAdjustments, String endDateString, String startDate) {
        RateQualifiedAdjustment rateQualifiedAdjustment = rateQualifiedAdjustments.get(0);
        assertEquals(new Date(startDate), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date(endDateString), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(Integer.valueOf(3), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(Integer.valueOf(1), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertEquals(-113.35f, rateQualifiedAdjustment.getNetValue());
        assertEquals(ADJUSTMENT_TYPE.YieldableCost.name(), rateQualifiedAdjustment.getAdjustmentType());

        rateQualifiedAdjustment = rateQualifiedAdjustments.get(1);
        assertEquals(new Date(startDate), new Date(rateQualifiedAdjustment.getStartDate().getTime()), "rateQualifiedAdjustment start date doesnt not match ");
        assertEquals(new Date(endDateString), new Date(rateQualifiedAdjustment.getEndDate().getTime()), "rateQualifiedAdjustment end date doesnt not match ");
        assertEquals(new Integer(1), rateQualifiedAdjustment.getNetValueTypeId());
        assertEquals(new Integer(2), rateQualifiedAdjustment.getPostingRuleTypeId());
        assertTrue(242.21f == rateQualifiedAdjustment.getNetValue());
        assertEquals(ADJUSTMENT_TYPE.YieldableValue.name(), rateQualifiedAdjustment.getAdjustmentType());
    }

    private void userMakesExistingRateAsG3ManagedAndUpdatesStatus(String rateCode, Integer status) {
        RateQualified rateQualified = qualifiedRateService.findSingleFixedRateByName(rateCode);
        assertEquals(false, rateQualified.getManagedInG3());
        rateQualified.setManagedInG3(Boolean.TRUE);
        rateQualified.setStatusId(status);
        tenantCrudService.save(rateQualified);
        flushAndClear();
        rateQualified = qualifiedRateService.findSingleFixedRateByName(rateCode);
        assertTrue(rateQualified.getManagedInG3());
        assertEquals(status, rateQualified.getStatusId());
    }

    private void nextDayExtractExecution() {
        managedRates = qualifiedRateService.getManagedRates();
        qualifiedRateService.processRates(secondRateDefinition.getRateHeader(), secondSnapShotDate,
                createFileMetadata(secondFilePath,
                        snapShotDate, snapShotTime, preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
    }

    private void firstExtractExecution() {
        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);
        flushAndClear();
    }

    private RateQualifiedAdjustment populateObj(float netValue, String startDate, String endDate, String adjType, int postingRule, int valueType) {
        RateQualifiedAdjustment rd1 = new RateQualifiedAdjustment();
        rd1.setRateQualifiedId(1234);
        rd1.setNetValue(netValue);
        rd1.setStartDate(new Date(startDate));
        rd1.setEndDate(new Date(endDate));
        rd1.setAdjustmentType(adjType);
        rd1.setPostingRuleTypeId(postingRule);
        rd1.setNetValueTypeId(valueType);
        return rd1;
    }

    private boolean isObjectEqual(RateQualifiedAdjustment thisObj, RateQualifiedAdjustment that) {
        if (thisObj == that) {
            return true;
        }

        if (thisObj.getEndDate() != null ? !thisObj.getEndDate().equals(that.getEndDate()) : that.getEndDate() != null) {
            return false;
        }
        if (thisObj.getNetValue() != null ? !thisObj.getNetValue().equals(that.getNetValue()) : that.getNetValue() != null) {
            return false;
        }
        if (thisObj.getNetValueTypeId() != null ? !thisObj.getNetValueTypeId().equals(that.getNetValueTypeId()) : that.getNetValueTypeId() != null) {
            return false;
        }
        if (thisObj.getPostingRuleTypeId() != null ? !thisObj.getPostingRuleTypeId().equals(that.getPostingRuleTypeId()) : that.getPostingRuleTypeId() != null) {
            return false;
        }
        if (thisObj.getStartDate() != null ? !thisObj.getStartDate().equals(that.getStartDate()) : that.getStartDate() != null) {
            return false;
        }

        return true;
    }

    private boolean checkObjectExistsinList(RateQualifiedAdjustment that, List<RateQualifiedAdjustment> rtList) {
        for (RateQualifiedAdjustment rt : rtList) {
            if (isObjectEqual(rt, that)) {
                return true;
            }
        }
        return false;
    }

    @Test
    public void shouldFetchRateQualifiedIdsOfLinkedSRPs() {
        LocalDate date1 = LocalDate.parse("2019-12-10");
        Integer fileMetadata1 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 12);
        Integer fileMetadata2 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 23);

        RateQualified rate1 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate1", fileMetadata1);
        RateQualified rate2 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate2", fileMetadata1);

        RateQualified rate3 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate3", fileMetadata2);

        Integer fileMetadata3 = UniqueFileMetadataCreator.createFileMetadata(date1.plusDays(1).toDate(), 10, 30, 12);

        List<Integer> ratesNotToBeModified = qualifiedRateService.getRatesNotToBeModified(fileMetadata3); //fileMetadata3 - non-Linked SRP
        assertEquals(1, ratesNotToBeModified.size());
        assertTrue(ratesNotToBeModified.contains(rate3.getId()));
    }

    @Test
    public void shouldFetchRateQualifiedIdsOfNonLinkedSRPs() {
        LocalDate date1 = LocalDate.parse("2019-12-10");
        Integer fileMetadata1 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 12);
        Integer fileMetadata2 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 23);
        RateQualified rate1 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate1", fileMetadata1);
        RateQualified rate2 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate2", fileMetadata1);
        RateQualified rate3 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate3", fileMetadata2);
        Integer fileMetadata3 = UniqueFileMetadataCreator.createFileMetadata(date1.plusDays(1).toDate(), 10, 30, 23);

        List<Integer> ratesNotToBeModified = qualifiedRateService.getRatesNotToBeModified(fileMetadata3);//fileMetadata3 - Linked SRP
        assertEquals(2, ratesNotToBeModified.size());
        assertTrue(ratesNotToBeModified.contains(rate1.getId()));
        assertTrue(ratesNotToBeModified.contains(rate2.getId()));
    }

    @Test
    public void shouldFetchIdsOfNonLinkedSRPsWithMultipleFileMetadataIds() {
        LocalDate date1 = LocalDate.parse("2019-12-10");
        Integer fileMetadata_nonLinked1 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 12);
        Integer fileMetadata_linked1_1 = UniqueFileMetadataCreator.createFileMetadata(date1.toDate(), 10, 30, 23);
        Integer fileMetadata_nonLinked2 = UniqueFileMetadataCreator.createFileMetadata(date1.plusDays(1).toDate(), 10, 30, 12);

        RateQualified rate1 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate1", fileMetadata_nonLinked1);
        RateQualified rate2 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate2", fileMetadata_nonLinked1);

        RateQualified rate3 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate3", fileMetadata_nonLinked2);

        RateQualified rate4 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(date1.toDate(), date1.plusDays(10).toDate(), 5, "Rate4", fileMetadata_linked1_1);
        Integer fileMetadata_linked2 = UniqueFileMetadataCreator.createFileMetadata(date1.plusDays(1).toDate(), 10, 30, 23);

        List<Integer> ratesNotToBeModified = qualifiedRateService.getRatesNotToBeModified(fileMetadata_linked2);//fileMetadata_nonLinked2 - Linked SRP
        assertEquals(1, ratesNotToBeModified.size());
        assertTrue(ratesNotToBeModified.contains(rate3.getId()));
    }

    @Test
    public void shouldDeleteFutureDetailsOnlyOfNonLinkedSRPs() {
        LocalDate startDate = LocalDate.parse("2019-12-12");
        LocalDate endDate = startDate.plusDays(30);
        LocalDate snapShotDate = LocalDate.parse("2019-12-10");
        RateQualified rate_nonLinked = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_NonLinked");
        RateQualified rate_linked = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_Linked");

        RateQualifiedDetails rateQualifiedDetails_nonLinked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_nonLinked.getId());
        RateQualifiedDetails rateQualifiedDetails_nonLinked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_nonLinked.getId());

        RateQualifiedDetails rateQualifiedDetails_linked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_linked.getId());
        RateQualifiedDetails rateQualifiedDetails_linked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_linked.getId());

        qualifiedRateService.deleteFutureRateDetailsExcludingRateIds(snapShotTime, QualifiedRateService.RATE_QUALIFIED, Arrays.asList(rate_linked.getId()));

        List<RateQualifiedDetails> qualifiedDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(2, qualifiedDetails.size());
        assertEquals(rate_linked.getId(), qualifiedDetails.get(0).getRateQualifiedId());
        assertEquals(rate_linked.getId(), qualifiedDetails.get(1).getRateQualifiedId());
        List<Integer> ids = qualifiedDetails.stream().map(r -> r.getId()).collect(Collectors.toList());
        assertTrue(ids.contains(rateQualifiedDetails_linked1.getId()));
        assertTrue(ids.contains(rateQualifiedDetails_linked2.getId()));
    }

    @Test
    public void shouldDeleteFutureDetailsOnlyOfLinkedSRPs() {
        LocalDate startDate = LocalDate.parse("2019-12-12");
        LocalDate endDate = startDate.plusDays(30);
        LocalDate snapShotDate = LocalDate.parse("2019-12-10");
        RateQualified rate_nonLinked = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_NonLinked");
        RateQualified rate_linked = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_Linked");

        RateQualifiedDetails rateQualifiedDetails_nonLinked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_nonLinked.getId());
        RateQualifiedDetails rateQualifiedDetails_nonLinked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_nonLinked.getId());

        RateQualifiedDetails rateQualifiedDetails_linked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_linked.getId());
        RateQualifiedDetails rateQualifiedDetails_linked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_linked.getId());

        qualifiedRateService.deleteFutureRateDetailsExcludingRateIds(snapShotTime, QualifiedRateService.RATE_QUALIFIED, Arrays.asList(rate_nonLinked.getId()));

        List<RateQualifiedDetails> qualifiedDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(2, qualifiedDetails.size());
        assertEquals(rate_nonLinked.getId(), qualifiedDetails.get(0).getRateQualifiedId());
        assertEquals(rate_nonLinked.getId(), qualifiedDetails.get(1).getRateQualifiedId());
        List<Integer> ids = qualifiedDetails.stream().map(r -> r.getId()).collect(Collectors.toList());
        assertTrue(ids.contains(rateQualifiedDetails_nonLinked1.getId()));
        assertTrue(ids.contains(rateQualifiedDetails_nonLinked2.getId()));
    }


    @Test
    public void shouldNotDeleteNonLinkedRatesDuringLinkedRatePopulation() {
        LocalDate localDate = LocalDate.parse("2019-12-12");
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail1 = buildRateDetails(localDate, localDate.plusDays(30));
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail2 = buildRateDetails(localDate.plusDays(31), localDate.plusDays(30).plusDays(31));
        RateDefinition.RateHeader.AccommodationType accommodationType = buildAccomodationType(Arrays.asList(rateDetail1, rateDetail2));
        RateDefinition.RateHeader rateHeader = buildRateHeader(accommodationType, localDate, localDate.plusDays(70));

        RateQualified nonLinkedRate = createRateInDB(localDate, localDate.plusDays(10), Constants.QUALIFIED_RATE_PLAN, "Rate_NonLinked");

        qualifiedRateService.processRates(Arrays.asList(rateHeader), localDate.toDate(),
                createFileMetadata("DUMMY", localDate.toDate(), localDate.toDate(),
                        localDate.toDate(), localDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(2, allRates.size());
        RateQualified linkedRate = allRates.get(1);
        assertEquals("LinkedSRP1", linkedRate.getName());
        assertEquals(1, allRates.get(0).getStatusId().intValue());
        assertEquals(nonLinkedRate.getId(), allRates.get(0).getId());

        List<RateQualifiedDetails> allRateDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(4, allRateDetails.size());
        assertEquals(nonLinkedRate.getId(), allRateDetails.get(0).getRateQualifiedId());
        RateQualifiedDetails nonLinkedrateQualifiedDetail = allRateDetails.get(1);
        assertEquals(nonLinkedRate.getId(), nonLinkedrateQualifiedDetail.getRateQualifiedId());
        assertEquals(localDate.plusDays(6).toDate(), nonLinkedrateQualifiedDetail.getStartDate());
        assertEquals(localDate.plusDays(10).toDate(), nonLinkedrateQualifiedDetail.getEndDate());
        assertEquals(linkedRate.getId(), allRateDetails.get(2).getRateQualifiedId());
        assertEquals(linkedRate.getId(), allRateDetails.get(3).getRateQualifiedId());
    }

    @Test
    public void shouldPopulateLinkedRateAdjustments() {
        LocalDate localDate = LocalDate.parse("2019-12-12");
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail1 = buildRateDetails(localDate, localDate.plusDays(30));
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail2 = buildRateDetails(localDate.plusDays(31), localDate.plusDays(30).plusDays(31));
        RateDefinition.RateHeader.AccommodationType accommodationType = buildAccomodationType(Arrays.asList(rateDetail1, rateDetail2));
        List<RateDefinition.RateHeader.RateAdjustment> rateAdjustmentYieldableValue = buildRateAdjustment(localDate, localDate.plusDays(70));
        RateDefinition.RateHeader rateHeader = buildRateHeader(accommodationType, localDate, localDate.plusDays(70));

        RateQualified nonLinkedRate = createRateInDB(localDate, localDate.plusDays(10), Constants.QUALIFIED_RATE_PLAN, "Rate_NonLinked");

        qualifiedRateService.processRates(Arrays.asList(rateHeader), localDate.toDate(),
                createFileMetadata("DUMMY", localDate.toDate(), localDate.toDate(),
                        localDate.toDate(), localDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(2, allRates.size());
        RateQualified linkedRate = allRates.get(1);
        assertEquals("LinkedSRP1", linkedRate.getName());
        assertEquals(1, allRates.get(0).getStatusId().intValue());
        assertEquals(nonLinkedRate.getId(), allRates.get(0).getId());

        List<RateQualifiedDetails> allRateDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(4, allRateDetails.size());
        assertEquals(nonLinkedRate.getId(), allRateDetails.get(0).getRateQualifiedId());
        RateQualifiedDetails nonLinkedrateQualifiedDetail = allRateDetails.get(1);
        assertEquals(nonLinkedRate.getId(), nonLinkedrateQualifiedDetail.getRateQualifiedId());
        assertEquals(localDate.plusDays(6).toDate(), nonLinkedrateQualifiedDetail.getStartDate());
        assertEquals(localDate.plusDays(10).toDate(), nonLinkedrateQualifiedDetail.getEndDate());
        assertEquals(linkedRate.getId(), allRateDetails.get(2).getRateQualifiedId());
        assertEquals(linkedRate.getId(), allRateDetails.get(3).getRateQualifiedId());
    }

    private List<RateDefinition.RateHeader.RateAdjustment> buildRateAdjustment(LocalDate startDate, LocalDate endDate) {
        RateDefinition.RateHeader.RateAdjustment rateAdjustment = new RateDefinition.RateHeader.RateAdjustment();
        rateAdjustment.getYieldableValue().addAll(buildYieldableValue(startDate, endDate));
        rateAdjustment.getYieldableCost().addAll(buildYieldableCosts(startDate));
        return Collections.singletonList(rateAdjustment);
    }

    private Collection<? extends RateDefinition.RateHeader.RateAdjustment.YieldableCost> buildYieldableCosts(LocalDate startDate) {
        RateDefinition.RateHeader.RateAdjustment.YieldableCost yieldCost1 = new RateDefinition.RateHeader.RateAdjustment.YieldableCost();
        yieldCost1.setRule(PostingRuleEnum.PER_ROOM_PER_STAY);
        yieldCost1.setStartDate(convertDateToXmlDate(startDate));
        yieldCost1.setEndDate(convertDateToXmlDate(startDate.plusDays(2)));
        yieldCost1.setValue(BigDecimal.ONE);
        yieldCost1.setType(TypeEnum.fromValue("Set"));

        RateDefinition.RateHeader.RateAdjustment.YieldableCost yieldCost2 = new RateDefinition.RateHeader.RateAdjustment.YieldableCost();
        yieldCost2.setRule(PostingRuleEnum.PER_ROOM_PER_NIGHT);
        yieldCost2.setStartDate(convertDateToXmlDate(startDate.plusDays(3)));
        yieldCost2.setEndDate(convertDateToXmlDate(startDate.plusDays(5)));
        yieldCost2.setValue(BigDecimal.ONE);
        yieldCost2.setType(TypeEnum.fromValue("Amount"));

        return Arrays.asList(yieldCost1, yieldCost2);
    }

    private Collection<? extends RateDefinition.RateHeader.RateAdjustment.YieldableValue> buildYieldableValue(LocalDate startDate, LocalDate endDate) {
        RateDefinition.RateHeader.RateAdjustment.YieldableValue yieldValue = new RateDefinition.RateHeader.RateAdjustment.YieldableValue();
        yieldValue.setRule(PostingRuleEnum.PER_ROOM_PER_NIGHT);
        yieldValue.setStartDate(convertDateToXmlDate(startDate));
        yieldValue.setEndDate(convertDateToXmlDate(endDate));
        yieldValue.setValue(BigDecimal.TEN);
        yieldValue.setType(TypeEnum.fromValue("Amount"));
        return Collections.singletonList(yieldValue);


    }

    @Test
    public void shouldNotDeleteLinkedRatesDuringNonLinkedRatePopulation() {
        LocalDate localDate = LocalDate.parse("2019-12-12");
        RateQualified linkedRateQualified = createRateInDB(localDate, localDate.plusDays(10), Constants.QUALIFIED_LINKED_RATE_PLAN, "Rate_Linked");

        qualifiedRateService.processRates(firstRateDefinition.getRateHeader(), snapShotDate,
                createFileMetadata(firstFilePath, snapShotDate, snapShotTime,
                        preparedDate, preparedTime, RATE_PLAN_TYPE).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(10, allRates.size());
        RateQualified linkedRate = allRates.get(0);
        assertEquals(linkedRateQualified.getName(), linkedRate.getName());
        assertEquals(1, allRates.get(0).getStatusId().intValue());

        List<RateQualifiedDetails> allRateDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(linkedRateQualified.getId(), allRateDetails.get(0).getRateQualifiedId());
        assertEquals(linkedRateQualified.getId(), allRateDetails.get(1).getRateQualifiedId());
        assertEquals(localDate.plusDays(5).toDate(), allRateDetails.get(0).getEndDate());
        assertEquals(localDate.plusDays(10).toDate(), allRateDetails.get(1).getEndDate());
    }


    @Test
    public void shouldSplitHeaderSeasonForLinkedSPRPopulation() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        RateQualified spr1 = createRateInDB(startDate, endDate, Constants.QUALIFIED_LINKED_RATE_PLAN, "LinkedSRP1");

        LocalDate newStartDate = LocalDate.parse("2019-01-12");
        LocalDate newEndDate = LocalDate.parse("2019-01-22");

        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail1 = buildRateDetails(newStartDate, newStartDate.plusDays(2));
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail2 = buildRateDetails(newStartDate.plusDays(3), newEndDate);
        RateDefinition.RateHeader.AccommodationType accommodationType = buildAccomodationType(Arrays.asList(rateDetail1, rateDetail2));
        RateDefinition.RateHeader rateHeader = buildRateHeader(accommodationType, newStartDate, newEndDate);

        LocalDate snapShotDate = LocalDate.parse("2019-01-12");

        qualifiedRateService.processRates(Arrays.asList(rateHeader), snapShotDate.toDate(),
                createFileMetadata("DUMMY", snapShotDate.toDate(), snapShotDate.toDate(),
                        snapShotDate.toDate(), snapShotDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(1, allRates.size());
        assertEquals("LinkedSRP1", allRates.get(0).getName());

        //Header's start date /End updated as per the new rate header. Earlier is overriden
        assertEquals(newStartDate.toDate(), allRates.get(0).getStartDate()); //2019-01-12
        assertEquals(newEndDate.toDate(), allRates.get(0).getEndDate()); //2019-01-22

        List<RateQualifiedDetails> allRateDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(3, allRateDetails.size());

        //Earlier season is split as per snapshot Date/future season is deleted
        assertEquals(startDate.toDate(), allRateDetails.get(0).getStartDate()); //2019-01-10
        assertEquals(startDate.plusDays(1).toDate(), allRateDetails.get(0).getEndDate()); //2019-01-11

        assertEquals(newStartDate.toDate(), allRateDetails.get(1).getStartDate()); //2019-01-12
        assertEquals(newStartDate.plusDays(2).toDate(), allRateDetails.get(1).getEndDate()); //2019-01-14

        assertEquals(newStartDate.plusDays(3).toDate(), allRateDetails.get(2).getStartDate()); //2019-01-15
        assertEquals(newEndDate.toDate(), allRateDetails.get(2).getEndDate()); //2019-01-22
    }

    @Test
    public void shouldNotDeleteFutureDetailsOfManagedInG3Rates() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        RateQualified spr1 = createRateInDB(startDate, endDate, Constants.QUALIFIED_LINKED_RATE_PLAN, "LinkedSRP1");

        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail1 = buildRateDetails(startDate, startDate.plusDays(2));
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail2 = buildRateDetails(startDate.plusDays(3), endDate);
        RateDefinition.RateHeader.AccommodationType accommodationType = buildAccomodationType(Arrays.asList(rateDetail1, rateDetail2));
        RateDefinition.RateHeader rateHeader = buildRateHeader(accommodationType, startDate, endDate);

        LocalDate snapShotDate = LocalDate.parse("2019-01-11");

        Map<String, RateQualified> managedLinkedRates = new HashMap();
        managedLinkedRates.put("LinkedSRP1", spr1);

        qualifiedRateService.processRates(Arrays.asList(rateHeader), snapShotDate.toDate(),
                createFileMetadata("DUMMY", snapShotDate.toDate(), snapShotDate.toDate(),
                        snapShotDate.toDate(), snapShotDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedLinkedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(1, allRates.size());
        assertEquals("LinkedSRP1", allRates.get(0).getName());

        //Header's start date /End updated as per the new rate header. Earlier is overriden
        assertEquals(startDate.toDate(), allRates.get(0).getStartDate()); //2019-01-10
        assertEquals(endDate.toDate(), allRates.get(0).getEndDate()); //2019-01-20

        List<RateQualifiedDetails> allRateDetails = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(2, allRateDetails.size());

        //Earlier season is split as per snapshot Date/future season is deleted
        assertEquals(startDate.toDate(), allRateDetails.get(0).getStartDate()); //2019-01-10
        assertEquals(startDate.plusDays(5).toDate(), allRateDetails.get(0).getEndDate()); //2019-01-15

        assertEquals(startDate.plusDays(6).toDate(), allRateDetails.get(1).getStartDate()); //2019-01-16
        assertEquals(endDate.toDate(), allRateDetails.get(1).getEndDate()); //2019-01-20

    }

    @Test
    public void shouldDeactivateAllExistingRatesWhenNoRatesInCurrentProcessing() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        RateQualified spr1 = createRateInDB(startDate, endDate, Constants.QUALIFIED_LINKED_RATE_PLAN, "LinkedSRP1");
        LocalDate snapShotDate = LocalDate.parse("2019-01-11");
        tenantCrudService.detach(spr1);
        qualifiedRateService.processRates(new ArrayList<>(), snapShotDate.toDate(),
                createFileMetadata("DUMMY", snapShotDate.toDate(), snapShotDate.toDate(),
                        snapShotDate.toDate(), snapShotDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(1, allRates.size());
        assertEquals("LinkedSRP1", allRates.get(0).getName());
        assertEquals(2, allRates.get(0).getStatusId().intValue());  //Inactivated existing linked rate

        List<Object[]> allRateDetails = tenantCrudService.findByNativeQuery("select * from rate_qualified_details");
        assertEquals(1, allRateDetails.size());
        assertEquals("2019-01-10", allRateDetails.get(0)[3].toString()); //2019-01-10 - Season is split, future deleted
        assertEquals("2019-01-10", allRateDetails.get(0)[3].toString()); //2019-01-10
    }

    @Test
    public void shouldDeactivateOnlyLinkedRatesWhenNoLinkedRatesInCurrentProcessing() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        RateQualified spr1 = createRateInDB(startDate, endDate, Constants.QUALIFIED_LINKED_RATE_PLAN, "LinkedSRP1");
        RateQualified nonLinkedRate = createRateInDB(startDate, startDate.plusDays(10), Constants.QUALIFIED_RATE_PLAN, "Rate_NonLinked");
        tenantCrudService.detach(spr1);
        tenantCrudService.detach(nonLinkedRate);
        LocalDate snapShotDate = LocalDate.parse("2019-01-11");

        qualifiedRateService.processRates(new ArrayList<>(), snapShotDate.toDate(),
                createFileMetadata("DUMMY", snapShotDate.toDate(), snapShotDate.toDate(),
                        snapShotDate.toDate(), snapShotDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(2, allRates.size());
        RateQualified linkedRateQualified = allRates.get(0);
        assertEquals("LinkedSRP1", linkedRateQualified.getName());
        assertEquals(2, linkedRateQualified.getStatusId().intValue());  //Inactivated existing linked rate

        assertEquals("Rate_NonLinked", allRates.get(1).getName());
        assertEquals(1, allRates.get(1).getStatusId().intValue());  //Non-Linked not deactivated

    }

    @Test
    public void shouldUpdateAdjustmentEndDatesWhenNoLinkedRatesInCurrentProcessing() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        RateQualified spr1 = createRateInDB(startDate, endDate, Constants.QUALIFIED_LINKED_RATE_PLAN, "LinkedSRP1");
        RateQualified nonLinkedRate = createRateInDB(startDate, startDate.plusDays(10), Constants.QUALIFIED_RATE_PLAN, "Rate_NonLinked");
        tenantCrudService.detach(spr1);
        tenantCrudService.detach(nonLinkedRate);

        RateQualifiedAdjustment adjustmentLinked = new RateQualifiedAdjustment();
        adjustmentLinked.setRateQualifiedId(spr1.getId());
        adjustmentLinked.setStartDate(startDate.toDate());
        adjustmentLinked.setEndDate(endDate.toDate());
        adjustmentLinked.setPostingRuleTypeId(1);
        adjustmentLinked.setNetValue(10.1f);
        adjustmentLinked.setAdjustmentType("Amount");
        adjustmentLinked.setNetValueTypeId(1);

        RateQualifiedAdjustment adjustmentNonLinked = new RateQualifiedAdjustment();
        adjustmentNonLinked.setRateQualifiedId(nonLinkedRate.getId());
        adjustmentNonLinked.setStartDate(startDate.toDate());
        adjustmentNonLinked.setEndDate(endDate.toDate());
        adjustmentNonLinked.setPostingRuleTypeId(1);
        adjustmentNonLinked.setNetValue(10.1f);
        adjustmentNonLinked.setAdjustmentType("Amount");
        adjustmentNonLinked.setNetValueTypeId(1);


        tenantCrudService.save(Arrays.asList(adjustmentLinked, adjustmentNonLinked));
        tenantCrudService.detach(adjustmentLinked);
        tenantCrudService.detach(adjustmentNonLinked);
        LocalDate snapShotDate = LocalDate.parse("2019-01-11");

        qualifiedRateService.processRates(new ArrayList<>(), snapShotDate.toDate(),
                createFileMetadata("DUMMY", snapShotDate.toDate(), snapShotDate.toDate(),
                        snapShotDate.toDate(), snapShotDate.toDate(), Constants.QUALIFIED_LINKED_RATE_PLAN).getId(), cacheAccomData, managedRates);

        List<RateQualified> allRates = tenantCrudService.findAll(RateQualified.class);
        assertEquals(2, allRates.size());
        RateQualified linkedRateQualified = allRates.get(0);
        assertEquals("LinkedSRP1", linkedRateQualified.getName());
        assertEquals(2, linkedRateQualified.getStatusId().intValue());  //Inactivated existing linked rate

        assertEquals("Rate_NonLinked", allRates.get(1).getName());
        assertEquals(1, allRates.get(1).getStatusId().intValue());  //Non-Linked not deactivated

        List<RateQualifiedAdjustment> allAdjustments = tenantCrudService.findAll(RateQualifiedAdjustment.class);
        assertEquals(2, allAdjustments.size());
        assertEquals(snapShotDate.plusDays(-1).toDate(), allAdjustments.get(0).getEndDate()); //End date of the adjustmentLinked is updated to snapshot day -1
        assertEquals(endDate.toDate(), allAdjustments.get(1).getEndDate()); //End date of the adjustment-NonLinked remains the same - not modified
    }

    @Test
    public void shouldFetchRateQualifiedByFileMetadata() {
        LocalDate startDate = LocalDate.parse("2019-01-10");
        LocalDate endDate = startDate.plusDays(10);
        Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(startDate.toDate(), 10, 90, 23);
        RateQualified rate1 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(startDate.toDate(), endDate.toDate(), 5, "Rate1", fileMetadata);
        RateQualified rate2 = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(startDate.toDate(), endDate.toDate(), 5, "Rate2", fileMetadata);

        Map<String, RateQualified> qualifiedRatesForFileMetadataId = qualifiedRateService.getQualifiedRatesForFileMetadataId(fileMetadata);
        assertNotNull(qualifiedRatesForFileMetadataId.get("Rate1"));
        assertNotNull(qualifiedRatesForFileMetadataId.get("Rate2"));

    }

    @Test
    void testSetLV0OffsetToZeroForNewRoomType_WhenAccomTypeIsNotPresentInRateQualified() {
        //GIVEN
        updateAccomTypeToUnassigned();
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Type_Capacity = 0 where Accom_Type_ID = 5");
        LocalDate now = LocalDate.now();
        Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(now.toDate(), 10, 90, RecordType.T2SNAP_RECORD_TYPE_ID);
        RateQualified rate = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(now.toDate(), now.toDate(), 5, "LV0", fileMetadata);
        //WHEN
        qualifiedRateService.setLV0OffsetToZeroForNewRoomType();
        //THEN
        RateQualifiedDetails rateQualifiedDetail = tenantCrudService().findByNamedQuerySingleResult(RateQualifiedDetails.BY_RATE_QUALIFIED_ID,
                QueryParameter.with("rateQualifiedId", rate.getId()).parameters());
        assertTrue(Objects.nonNull(rateQualifiedDetail));
        assertEquals(rateQualifiedDetail.getRateQualifiedId(), rate.getId());
        assertEquals(rateQualifiedDetail.getSunday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getMonday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getTuesday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getWednesday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getThursday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getFriday(), ZERO_OFFSET);
        assertEquals(rateQualifiedDetail.getSaturday(), ZERO_OFFSET);
    }

    @Test
    void testSetLV0OffsetToZeroForNewRoomType_WhenAccomTypeIsPresentInRateQualified() {
        updateAccomTypeToUnassigned();

        LocalDate now = LocalDate.now();
        Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(now.toDate(), 10, 90, RecordType.T2SNAP_RECORD_TYPE_ID);
        RateQualified rate = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(now.toDate(), now.toDate(), 5, "LV0", fileMetadata);
        UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(now.toDate(), now.plusDays(10).toDate(), 5, rate.getId(), 5, NON_ZERO_OFFSET.doubleValue());
        qualifiedRateService.setLV0OffsetToZeroForNewRoomType();
        RateQualifiedDetails rateQualifiedDetail = tenantCrudService().findByNamedQuerySingleResult(RateQualifiedDetails.BY_RATE_QUALIFIED_ID,
                QueryParameter.with("rateQualifiedId", rate.getId()).parameters());
        assertTrue(Objects.nonNull(rateQualifiedDetail));
        assertNotEquals(rateQualifiedDetail.getSunday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getMonday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getTuesday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getWednesday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getThursday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getFriday(), NON_ZERO_OFFSET);
        assertNotEquals(rateQualifiedDetail.getSaturday(), NON_ZERO_OFFSET);
    }

    @Test
    void testUpdateLV0SetOffsetAndDerivedRate() {
        LocalDate now = LocalDate.now();
        Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(now.toDate(), 10, 90, RecordType.T2SNAP_RECORD_TYPE_ID);
        RateQualified rate = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(now.toDate(), now.toDate(), 5, "LV0", fileMetadata);
        qualifiedRateService.updateLV0SetOffsetAndDerivedRate();
        flushAndClear();
        RateQualified updatedRateQualified = tenantCrudService().findByNamedQuerySingleResult(RateQualified.BY_PROPERTY_ID_AND_LV0,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        assertTrue(updatedRateQualified.getManagedInG3());
        assertEquals(2, updatedRateQualified.getRateQualifiedTypeId());
        List<RateQualifiedDetails> rateQualifiedDetails = tenantCrudService().findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID,
                QueryParameter.with("rateQualifiedId", rate.getId()).parameters());
        assertEquals(5, rateQualifiedDetails.size());
        rateQualifiedDetails.forEach(detail -> {
            assertEquals(detail.getRateQualifiedId(), rate.getId());
            assertEquals(detail.getSunday(), ZERO_OFFSET);
            assertEquals(detail.getMonday(), ZERO_OFFSET);
            assertEquals(detail.getTuesday(), ZERO_OFFSET);
            assertEquals(detail.getWednesday(), ZERO_OFFSET);
            assertEquals(detail.getThursday(), ZERO_OFFSET);
            assertEquals(detail.getFriday(), ZERO_OFFSET);
            assertEquals(detail.getSaturday(), ZERO_OFFSET);
        });
    }

    @Test
    void shouldUpdateManagedInG3ForLV0IfOnlyOneActiveEntryFound() {
        insertLV0IfNotAvailable(0);
        String messageReceived = qualifiedRateService.checkDuplicateLV0AndUpdateManagedInG3();
        tenantCrudService.flushAndClear();
        RateQualified rateQualifiedLV0 = tenantCrudService.findByNamedQuerySingleResult(RateQualified.GET_ACTIVE_RATE_QUALIFIED_BY_NAME, QueryParameter.with("name", LV0).parameters());
        assertNotNull(rateQualifiedLV0);
        assertTrue(rateQualifiedLV0.getManagedInG3());
        assertEquals(MANAGED_IN_G3_UPDATED_MESSAGE, messageReceived);
    }

    @Test
    void shoulNotdUpdateManagedInG3ForLV0IfOnlyOneActiveEntryWithManagedInG3TrueFound() {
        insertLV0IfNotAvailable(1);
        String messageReceived = qualifiedRateService.checkDuplicateLV0AndUpdateManagedInG3();
        RateQualified rateQualifiedLV0 = tenantCrudService.findByNamedQuerySingleResult(RateQualified.GET_ACTIVE_RATE_QUALIFIED_BY_NAME, QueryParameter.with("name", LV0).parameters());
        assertNotNull(rateQualifiedLV0);
        assertTrue(rateQualifiedLV0.getManagedInG3());
        assertEquals(MANAGED_IN_G3_NOT_UPDATED_MESSAGE, messageReceived);
    }

    @Test
    void shouldNotUpdateManagedInG3ForLV0IfDuplicateActiveEntriesFound() {
        insertDuplicateLV0Data();
        String messageReceived = qualifiedRateService.checkDuplicateLV0AndUpdateManagedInG3();
        List<RateQualified> rateQualifiedLV0 = tenantCrudService.findByNamedQuery(RateQualified.GET_ACTIVE_RATE_QUALIFIED_BY_NAME, QueryParameter.with("name", LV0).parameters());
        assertNotNull(rateQualifiedLV0);
        assertThat("Duplicate LV0", rateQualifiedLV0.size(), greaterThan(1));
        assertEquals(DUPLICATE_LV0_MESSAGE, messageReceived);
    }

    @Test
    void getNameToIdMap() {
        LocalDate startDate = LocalDate.now();
        RateQualified fixedRate = UniqueRateQualified.createRateQualifiedByDateAndNameAndTypeAndStatus
                (startDate.toDate(), startDate.plusDays(30).toDate(), PacmanWorkContextHelper.getPropertyId(), "Rate_FixedRate", QUALIFIED_RATE_PLAN_FIXED_TYPE, 1);
        RateQualified fixedInactiveRate = UniqueRateQualified.createRateQualifiedByDateAndNameAndTypeAndStatus
                (startDate.toDate(), startDate.plusDays(30).toDate(), PacmanWorkContextHelper.getPropertyId(), "Rate_FixedInActiveRate", QUALIFIED_RATE_PLAN_FIXED_TYPE, 2);
        RateQualified linkedRate = UniqueRateQualified.createRateQualifiedByDateAndNameAndTypeAndStatus
                (startDate.toDate(), startDate.plusDays(30).toDate(), PacmanWorkContextHelper.getPropertyId(), "Rate_LinkedRate", QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE, 1);
        final Map<String, Integer> result = qualifiedRateService.getNameToIdMap(Arrays.asList(3));
        assertEquals(1, result.size());
        assertTrue(result.containsKey(fixedRate.getName()));
        assertFalse(result.containsKey(fixedInactiveRate.getName()));
        assertFalse(result.containsKey(linkedRate.getName()));
        assertEquals(fixedRate.getId(), result.get(fixedRate.getName()));
    }

    @Test
    public void testHandleDiscontinuedRates() {
        LocalDate startDate = LocalDate.parse("2019-12-12");
        LocalDate endDate = startDate.plusDays(30);
        LocalDate snapShotDate = LocalDate.parse("2019-12-10");

        RateQualified rate_nonLinked1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_NonLinked_1");
        RateQualified rate_nonLinked2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_NonLinked_2");

        RateQualified rate_linked1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_Linked_1");
        RateQualified rate_linked2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), 5, "Rate_Linked_2");

        RateQualifiedDetails rateQualifiedDetails_nonLinked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_nonLinked1.getId());
        RateQualifiedDetails rateQualifiedDetails_nonLinked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_nonLinked1.getId());

        RateQualifiedDetails rateQualifiedDetails_nonLinked3 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_nonLinked2.getId());
        RateQualifiedDetails rateQualifiedDetails_nonLinked4 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_nonLinked2.getId());

        RateQualifiedDetails rateQualifiedDetails_linked1 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_linked1.getId());
        RateQualifiedDetails rateQualifiedDetails_linked2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_linked1.getId());

        RateQualifiedDetails rateQualifiedDetails_linked3 = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), 5, rate_linked2.getId());
        RateQualifiedDetails rateQualifiedDetails_linked4 = UniqueRateQualifiedDetails.createRateQualifiedDetails(endDate.plusDays(1).toDate(), endDate.plusDays(10).toDate(), 5, rate_linked2.getId());

        flushAndClear();

        qualifiedRateService.handleDiscontinuedRates(Arrays.asList(rate_nonLinked1.getId(), rate_linked1.getId()), snapShotDate.toDate(), RATE_QUALIFIED);

        List<RateQualified> rateQualifiedList = tenantCrudService.findAll(RateQualified.class);
        assertEquals(4, rateQualifiedList.size());
        List<Integer> idsList = rateQualifiedList.stream().filter(x -> x.getStatusId() == 2).map(RateQualified::getId).collect(Collectors.toList());
        assertEquals(2, idsList.size());
        assertTrue(idsList.contains(rate_nonLinked2.getId()));
        assertTrue(idsList.contains(rate_linked2.getId()));

        List<RateQualifiedDetails> rateQualifiedDetailsList = tenantCrudService.findAll(RateQualifiedDetails.class);
        assertEquals(4, rateQualifiedDetailsList.size());
        List<Integer> idsList1 = rateQualifiedDetailsList.stream().map(RateQualifiedDetails::getId).collect(Collectors.toList());
        assertTrue(idsList1.contains(rateQualifiedDetails_linked1.getId()));
        assertTrue(idsList1.contains(rateQualifiedDetails_linked2.getId()));
        assertTrue(idsList1.contains(rateQualifiedDetails_nonLinked1.getId()));
        assertTrue(idsList1.contains(rateQualifiedDetails_nonLinked2.getId()));
    }

    private void updateAccomTypeToUnassigned() {
        tenantCrudService().executeUpdateByNativeQuery("update accom_class set System_Default = 1 where Accom_Class_ID = 4");
    }

    private RateQualified createRateInDB(LocalDate startDate, LocalDate endDate, int recordTypeId, String rateName) {
        Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(startDate.toDate(), 10, 90, recordTypeId);
        RateQualified rate = UniqueRateQualified.createRateQualifiedByDateAndNameAndFileMetaData(startDate.toDate(), endDate.toDate(), 5, rateName, fileMetadata);

        UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(startDate.toDate(), startDate.plusDays(5).toDate(), 5, rate.getId(), 4, 1.1);
        UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(startDate.plusDays(6).toDate(), endDate.toDate(), 5, rate.getId(), 4, 1.1);
        return rate;

    }

    private RateDefinition.RateHeader buildRateHeader(RateDefinition.RateHeader.AccommodationType accommodationType, LocalDate startDate, LocalDate endDate) {
        RateDefinition.RateHeader rateHeader = new RateDefinition.RateHeader();
        rateHeader.setName("LinkedSRP1");
        rateHeader.setCurrency("USD");
        rateHeader.setType(RateType.QUALIFIED);
        rateHeader.setStartDate(convertDateToXmlDate(startDate));
        rateHeader.setEndDate(convertDateToXmlDate(endDate));
        rateHeader.setPriceRelative(true);
        rateHeader.setYieldable(true);
        rateHeader.setReferenceRateCode("LV0");
        rateHeader.getAccommodationType().add(accommodationType);
        return rateHeader;
    }

    private RateDefinition.RateHeader.AccommodationType buildAccomodationType(List<RateDefinition.RateHeader.AccommodationType.RateDetail> rateDetails) {
        RateDefinition.RateHeader.AccommodationType accommodationType = new RateDefinition.RateHeader.AccommodationType();
        accommodationType.setName("DLX");
        accommodationType.getRateDetail().addAll(rateDetails);
        return accommodationType;
    }

    private RateDefinition.RateHeader.AccommodationType.RateDetail buildRateDetails(LocalDate startDate, LocalDate endDate) {
        RateDefinition.RateHeader.AccommodationType.RateDetail rateDetail = new RateDefinition.RateHeader.AccommodationType.RateDetail();
        rateDetail.setStartDate(convertDateToXmlDate(startDate));
        rateDetail.setEndDate(convertDateToXmlDate(endDate));
        rateDetail.setSunday(BigDecimal.ONE);
        rateDetail.setMonday(BigDecimal.ONE);
        rateDetail.setTuesday(BigDecimal.ONE);
        rateDetail.setWednesday(BigDecimal.ONE);
        rateDetail.setThursday(BigDecimal.ONE);
        rateDetail.setFriday(BigDecimal.ONE);
        rateDetail.setSaturday(BigDecimal.ONE);
        return rateDetail;
    }

    private XMLGregorianCalendar convertDateToXmlDate(LocalDate date) {
        return new XmlHelperImpl().convertDateToXMLGregorian(date.toDate());
    }

    private void insertLV0IfNotAvailable(int managedInG3) {
        long activeLV0Count = tenantCrudService.findByNamedQuerySingleResult(RateQualified.GET_ACTIVE_RATE_QUALIFIED_COUNT_BY_NAME, QueryParameter.with("name", LV0).parameters());
        if (activeLV0Count == 0) {
            Integer fileMetadataID = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.GET_MAX_FILE_METADATA_ID);
            Integer propertyID = tenantCrudService.findByNamedQuerySingleResult(TenantProperty.GET_MAX_PROPERTY_ID);
            if (fileMetadataID != null && propertyID != null) {
                tenantCrudService.executeUpdateByNativeQuery(INSERT_LV0_IN_RATE_QUALIFIED_TABLE, QueryParameter.with("fileMetadataID", fileMetadataID).and("propertyID", propertyID).and("userID", PacmanWorkContextHelper.getUserId()).and("rateQualifiedTypeId", 3).and("managedInG3", managedInG3).parameters());
            }
        } else if (activeLV0Count == 1 && managedInG3 == 1) {
            tenantCrudService.executeUpdateByNativeQuery(UPDATE_MANAGED_IN_G3, QueryParameter.with("managedInG3", managedInG3).parameters());
        }
    }

    private void insertDuplicateLV0Data() {
        long activeLV0Count = tenantCrudService.findByNamedQuerySingleResult(RateQualified.GET_ACTIVE_RATE_QUALIFIED_COUNT_BY_NAME, QueryParameter.with("name", LV0).parameters());
        Integer fileMetadataID = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.GET_MAX_FILE_METADATA_ID);
        Integer propertyID = tenantCrudService.findByNamedQuerySingleResult(TenantProperty.GET_MAX_PROPERTY_ID);

        if (activeLV0Count == 1) {
            if (fileMetadataID != null && propertyID != null) {
                tenantCrudService.executeUpdateByNativeQuery(INSERT_LV0_IN_RATE_QUALIFIED_TABLE, QueryParameter.with("fileMetadataID", fileMetadataID).and("propertyID", propertyID).and("userID", PacmanWorkContextHelper.getUserId()).and("rateQualifiedTypeId", 1).and("managedInG3", 0).parameters());
            }
        } else if (activeLV0Count == 0) {
            if (fileMetadataID != null && propertyID != null) {
                tenantCrudService.executeUpdateByNativeQuery(INSERT_LV0_IN_RATE_QUALIFIED_TABLE, QueryParameter.with("fileMetadataID", fileMetadataID).and("propertyID", propertyID).and("userID", PacmanWorkContextHelper.getUserId()).and("rateQualifiedTypeId", 3).and("managedInG3", 0).parameters());
                tenantCrudService.executeUpdateByNativeQuery(INSERT_LV0_IN_RATE_QUALIFIED_TABLE, QueryParameter.with("fileMetadataID", fileMetadataID).and("propertyID", propertyID).and("userID", PacmanWorkContextHelper.getUserId()).and("rateQualifiedTypeId", 1).and("managedInG3", 0).parameters());
            }
        }
    }

    private ActiveSrp activeSrp(String srpCode, String startDate, String endDate) {
        ActiveSrp activeSrp = ActiveSrp.builder().srpCode(srpCode).startDate(new LocalDate(startDate)).endDate(new LocalDate(endDate)).build();
        return tenantCrudService().save(activeSrp);

    }
}
