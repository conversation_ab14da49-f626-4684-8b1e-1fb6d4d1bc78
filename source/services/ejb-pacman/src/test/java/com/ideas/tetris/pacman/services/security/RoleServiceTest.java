package com.ideas.tetris.pacman.services.security;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.AuthGroupExploder;
import com.ideas.infra.tetris.security.ExplosionException;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.infra.tetris.security.MockAuthGroupExploder;
import com.ideas.infra.tetris.security.domain.*;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.fds.uas.PermissionDefinition;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRole;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.IJobServicesBridge;
import com.ideas.tetris.platform.common.job.IJobServicesBridgeExtension;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.*;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.opends.sdk.EntryNotFoundException;

import java.io.IOException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.ideas.tetris.pacman.services.security.RoleService.CLIENT_CODE_KEY;
import static com.ideas.tetris.pacman.services.security.RoleService.ROLE_NAME;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class RoleServiceTest extends AbstractG3JupiterTest {
    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    static final String AGENT_ROLE = "G3Agent";
    public static final String TEST_ROLE = "Test Role";
    private RoleService roleService;
    private WorkContextType workContext;
    private Role testRole;
    private static Boolean hasFeatureIsCorporate = false;
    private Boolean hasFeatureRoleAllPermissions = false;
    protected static CrudService globalCrudService;
    protected static ClientService clientService;
    protected static JobServiceLocal jobService;

    //Spy class for those who like to unit test with mocks
    @InjectMocks
    @Spy
    private RoleService roleServiceSpy;
    @Mock
    private UserGlobalDBService mockUserGlobalDBService;
    @Mock
    private CrudService mockGlobalCrudService;
    @Mock
    private UserService mockUserService;
    @Mock
    private ClientConfigService mockClientConfigService;
    @Mock
    private GlobalRoleCache globalRoleCache;
    @Mock
    private JobServiceLocal mockJobService;
    @Mock
    private ClientService mockClientService;
    @Mock
    IJobServicesBridge jobServicesBridge;
    @Mock
    private IJobServicesBridgeExtension jobServicesBridgeExtension;
    @Mock
    private UASService uasService;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    public static final String CLIENT = "SomeClient";
    private static final String ROLE_ID = "183019b2-04cc-4aed-bf25-5b2397004dff";

    LDAPUser ldapUser = null;
    private final static String USER_ID = "105";
    private Client client;
    @Captor
    ArgumentCaptor<HashMap<String, Object>> jobParameterCaptor;

    @BeforeAll
    public static void beforeClass() {
        hasFeatureIsCorporate = SystemConfig.hasFeatureCorporateUsers();
        System.setProperty("feature.corporate.users", Boolean.TRUE.toString());
    }

    @BeforeEach
    public void setup() {
        roleService = new RoleService();
        globalCrudService = globalCrudService();
        clientService = new ClientService();

        globalRoleCache.globalCrudService = mockGlobalCrudService;
        when(globalRoleCache.get(anyInt())).thenReturn(null);
        roleService.setGlobalRoleCache(globalRoleCache);
        UserService userService = new UserService();
        roleService.setUserService(userService);

        clientService.clientConfigService = mockClientConfigService;
        roleService.setClientService(clientService);

        roleService.setJobService(mockJobService);

        roleService.setUasService(uasService);

        UserGlobalDBService userGlobalDBService = new UserGlobalDBService();
        userGlobalDBService.setGlobalCrudService(globalCrudService);
        roleService.setUserGlobalDBService(userGlobalDBService);
        roleService.globalCrudService = mockGlobalCrudService;
        roleService.pacmanConfigParamsService = pacmanConfigParamsService;

        when(mockGlobalCrudService.save(any(GlobalRole.class))).thenAnswer(invocationOnMock -> {
            GlobalRole role = (GlobalRole) invocationOnMock.getArguments()[0];
            if (role.getId() == null) {
                role.setId(1);
            }
            return role;
        });

        workContext = PacmanWorkContextTestHelper.createWorkContext(USER_ID,
                PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE, WC_CLIENT_CODE_BLACKSTONE,
                PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE, PacmanWorkContextTestHelper.WC_PROPERTY_CODE_PUNE);

        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        hasFeatureRoleAllPermissions = SystemConfig.hasFeatureRoleAllPermissions();

        AuthGroupRoleMapping authGroupRoleMapping = new AuthGroupRoleMapping();
        authGroupRoleMapping.setAuthGroupId("1");
        authGroupRoleMapping.setRoleId("1");
        List<AuthGroupRoleMapping> authGroupRoleMappings = new ArrayList<>();
        authGroupRoleMappings.add(authGroupRoleMapping);

        PropertyRoleMapping propertyRoleMapping = new PropertyRoleMapping();
        propertyRoleMapping.setRoleId("1");
        propertyRoleMapping.setPropertyId("10");
        List<PropertyRoleMapping> propertyRoleMappings = new ArrayList<>();
        propertyRoleMappings.add(propertyRoleMapping);

        ldapUser = new LDAPUser();
        ldapUser.setUserId(Integer.parseInt(USER_ID));
        ldapUser.setIsCorporate(false);
        ldapUser.setDefaultPropertyGroup("1");
        ldapUser.setViewingPreference("tabular");
        ldapUser.setDefaultProperty("6");
        ldapUser.setDefaultDateFormat("dd-mmm-yyyy");
        ldapUser.setLanguage("en_US");
        ldapUser.setCn("test");
        ldapUser.setGivenName("User1");
        ldapUser.setMail("<EMAIL>");
        ldapUser.setSn("test");
        ldapUser.setUid(USER_ID);
        ldapUser.setActive(false);
        ldapUser.setClient(WC_CLIENT_CODE_BLACKSTONE);
        ldapUser.setAuthGroupRoles(authGroupRoleMappings);
        ldapUser.setSalesforceOrganizationId("00001");
        ldapUser.setSalesforcePortalId("00001");
        ldapUser.setIsCorporate(true);
        ldapUser.setHasSalesforceAccess(false);
        ldapUser.setNotifyPasswordExpiration(true);
        ldapUser.setDaysRemainingToExpirePassword(10);
        ldapUser.setPropertyRoles(propertyRoleMappings);
        ldapUser.setPasswordNeverExpire(false);
        ldapUser.setInternal(true);
        ldapUser.setDN("test");

        client = new Client();
        client.setCode(CLIENT);
        client.setId(123);
        client.setUpsClientUuid(UUID.randomUUID().toString());
    }

    @AfterAll
    public static void afterClass() {
        System.setProperty("feature.corporate.users", hasFeatureIsCorporate.toString());
    }

    /*
     * Delete the test role if it exists
     */
    @AfterEach
    public void cleanUp() {
        if (null != testRole && StringUtils.isNotEmpty(testRole.getUniqueIdentifier())) {
            if (null != globalCrudService.find(GlobalRole.class, Integer.parseInt(testRole.getUniqueIdentifier()))) {
                roleService.delete(testRole.getUniqueIdentifier());
            }
        }

        System.setProperty("feature.role.all.permissions", hasFeatureRoleAllPermissions.toString());
    }

    static Role getTestRole() {
        String roleName = MessageFormat.format("TestRole{0}", DATE_FORMAT.format(Calendar.getInstance().getTime()));
        Role role = new Role();
        role.setRoleName(roleName);
        role.setClientCode(WC_CLIENT_CODE_BLACKSTONE);
        role.setDescription("This is a unit test role. Should have been reaped after test. Role Service Test.");
        role.setUniqueIdentifier("123");
        return role;
    }

    @Test
    public void getAllRolesForClient() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        Set<Role> roles = roleServiceSpy.getAllRoles("BSTN");
        assertNotNull(roles, "Has roles");
        assertTrue(roles.size() > 0, "Expect at least one role");
    }

    @Test
    public void getAllRoles_notNull() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        Set<Role> roles = roleServiceSpy.getAllRoles(false);
        assertNotNull(roles, "Has roles");
        assertTrue(roles.size() > 0, "Expect at least one role");
    }

    @Test
    public void getAllRoles_fromGlobal() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        Set<Role> roles = roleServiceSpy.getAllRoles(false);
        assertEquals(4, roles.size());
    }

    private GlobalRole getGlobalRole() {
        GlobalRole globalRole = new GlobalRole();
        globalRole.setId(4);
        return globalRole;
    }

    @Test
    public void shouldFilterOutG3AgentRoleForExternalUser() {
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getLoggedInUser(false));
        Set<Role> roles = getRoles();
        roleService.setUserGlobalDBService(mockUserGlobalDBService);
        roleService.filterG3AgentRole(roles);
        boolean wasFound = false;
        for (Role role : roles) {
            if (role.getRoleName().equalsIgnoreCase(AGENT_ROLE)) {
                wasFound = true;
                break;
            }
        }
        assertFalse(wasFound, "G3Agent role filtered out ");
    }

    @Test
    public void testGetPropertyUserRoleWhenIndividualPropertyRoleIsAvailable() throws Exception {
        UserIndividualPropertyRole userIndividualPropertyRole = new UserIndividualPropertyRole();
        userIndividualPropertyRole.setPropertyId(5);
        userIndividualPropertyRole.setRoleId("Role1");

        when(mockGlobalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", 1).and("propertyId", 5).parameters())).thenReturn(Arrays.asList(userIndividualPropertyRole));

        Role role = new Role();
        role.setUniqueIdentifier("Role1");
        role.setRoleName("RoleName");
        doReturn(role).when(roleServiceSpy).getRole("Role1");

        roleServiceSpy.setGlobalCrudService(mockGlobalCrudService);

        Role propertyUserRole = roleServiceSpy.getPropertyUserRole("1", 5);
        assertNotNull(propertyUserRole);
        assertEquals(role.getUniqueIdentifier(), propertyUserRole.getUniqueIdentifier());
        assertEquals(role.getRoleName(), propertyUserRole.getRoleName());
        verify(mockGlobalCrudService, times(0)).findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", 1).parameters());
    }

    @Test
    public void testGetPropertyUserRoleWhenUserAuthGroupRoleIsAvailable() throws Exception {
        when(mockGlobalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", 1).and("propertyId", 5).parameters())).thenReturn(Collections.emptyList());

        UserAuthGroupRole userAuthGroupRole = new UserAuthGroupRole();
        userAuthGroupRole.setRoleId("Role1");
        GlobalUser globalUser = getGlobalUser();
        userAuthGroupRole.setGlobalUser(globalUser);

        when(mockGlobalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", 1).parameters())).thenReturn(Arrays.asList(userAuthGroupRole));

        Role role = new Role();
        role.setUniqueIdentifier("Role1");
        role.setRoleName("RoleName");
        doReturn(role).when(roleServiceSpy).getRole("Role1");

        roleServiceSpy.setGlobalCrudService(mockGlobalCrudService);

        Role propertyUserRole = roleServiceSpy.getPropertyUserRole("1", 5);
        assertNotNull(propertyUserRole);
        assertEquals(role.getUniqueIdentifier(), propertyUserRole.getUniqueIdentifier());
        assertEquals(role.getRoleName(), propertyUserRole.getRoleName());
        verify(mockGlobalCrudService, times(1)).findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", 1).and("propertyId", 5).parameters());
    }

    @Test
    public void testGetPropertyUserRoleWhenIndividualPropertyRoleAndUserAuthGroupRoleAvailable() throws Exception {
        UserIndividualPropertyRole userIndividualPropertyRole = new UserIndividualPropertyRole();
        userIndividualPropertyRole.setPropertyId(5);
        userIndividualPropertyRole.setRoleId("Role1");

        when(mockGlobalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", 1).and("propertyId", 5).parameters())).thenReturn(Arrays.asList(userIndividualPropertyRole));

        UserAuthGroupRole userAuthGroupRole = new UserAuthGroupRole();
        userAuthGroupRole.setRoleId("Role2");
        GlobalUser globalUser = getGlobalUser();
        userAuthGroupRole.setGlobalUser(globalUser);

        when(mockGlobalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", 1).parameters())).thenReturn(Arrays.asList(userAuthGroupRole));

        Role role1 = new Role();
        role1.setUniqueIdentifier("Role1");
        role1.setRoleName("RoleName");
        doReturn(role1).when(roleServiceSpy).getRole("Role1");

        roleServiceSpy.setGlobalCrudService(mockGlobalCrudService);

        Role propertyUserRole = roleServiceSpy.getPropertyUserRole("1", 5);
        assertNotNull(propertyUserRole);
        assertEquals(role1.getUniqueIdentifier(), propertyUserRole.getUniqueIdentifier());
        assertEquals(role1.getRoleName(), propertyUserRole.getRoleName());

    }

    @Test
    public void testGetPropertyUserRoleWhenNoRoleIsAvailable() throws Exception {
        when(mockGlobalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", 1).and("propertyId", 5).parameters())).thenReturn(Collections.emptyList());
        when(mockGlobalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", 1).parameters())).thenReturn(Collections.emptyList());
        roleService.setGlobalCrudService(mockGlobalCrudService);
        assertNull(roleService.getPropertyUserRole("1", 5));
    }

    @Test
    public void testGetUserRoles_usingDB() {
        GlobalUser globalUser = getLoggedInUser(true);
        globalUser.setClientCode(Constants.INTERNAL_CLIENT_NAME);
        saveAuthGroupAndIndPropRolesInDB();
        List<String> roles = roleService.getUserRoles("1");
        assertEquals(2, roles.size());
        assertEquals("roleId=1 ON GROUP groupId=11", roles.get(0));
        assertEquals("roleId=2 ON PROP propertyId=22", roles.get(1));
    }

    private void saveAuthGroupAndIndPropRolesInDB() {
        roleService.globalCrudService = globalCrudService;
        globalCrudService.deleteAll(UserIndividualPropertyRole.class);
        globalCrudService.deleteAll(UserAuthGroupRole.class);
        globalCrudService.save(getAuthGroupRoles());
        globalCrudService.save(getIndividualPropertyRoles());
    }

    @Test
    public void testGetUserRoles() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        List<String> roles = roleServiceSpy.getUserRoles("11403");
        assertEquals(1, roles.size());
    }

    @Test
    public void shouldNotfilterG3AgentRoleForInternalUser() {
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getLoggedInUser(true));
        roleService.setUserGlobalDBService(mockUserGlobalDBService);
        Set<Role> roles = getRoles();
        roleService.filterG3AgentRole(roles);
        boolean wasFound = false;
        for (Role role : roles) {
            if (role.getRoleName().equalsIgnoreCase(AGENT_ROLE)) {
                wasFound = true;
                break;
            }
        }
        assertTrue(wasFound, "O where, o where did our role go");
    }

    @Test
    public void shouldNotfilterG3AgentRoleNonNumericUserId() {
        // Non-Integer UserID can occur during startup, etc
        PlatformThreadLocalContextHolder.getWorkContext().setUserId("System");

        Set<Role> roles = getRoles();
        roleService.filterG3AgentRole(roles);
        boolean wasFound = false;
        for (Role role : roles) {
            if (role.getRoleName().equalsIgnoreCase(AGENT_ROLE)) {
                wasFound = true;
                break;
            }
        }
        assertTrue(wasFound, "O where, o where did our role go");
    }

    private Set<Role> getRoles() {
        Set<Role> roles = new HashSet<Role>();
        roles.add(new Role("uid1234", AGENT_ROLE, null, true));
        roles.add(new Role("uid12345", "Some other role", "Some desc", true));
        return roles;
    }

    @Test
    public void getAllRolesInternal() throws LDAPException {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        // Can we make assumptions about pacmanApplication
        Set<Role> roles = roleServiceSpy.getAllRoles(true);
        assertNotNull(roles);
        assertFalse(roles.isEmpty(), "No internal roles.");

        boolean wasFound = false;
        for (Role role : roles) {
            if (role.getRoleName().equalsIgnoreCase(PacmanWorkContextTestHelper.WC_ROLE_NAME_INTERNAL)) {
                wasFound = true;
                break;
            }
        }
        assertTrue(wasFound, "O where, o where did our role go");
    }

    @Test
    public void getAllRolesGoBoom() throws Exception {
        assertThrows(TetrisException.class, () -> {
            when(roleServiceSpy.getAllRoles(false)).thenThrow(new LDAPException("Boom goes the dynamite"));
            roleServiceSpy.getAllRoles(false);
        });
    }

    @Test
    public void getAllAssignableRolesWithFeatureRoleAllPermissions() {
        System.setProperty("feature.role.all.permissions", Boolean.TRUE.toString());

        // Non-corporate
        PacmanWorkContextTestHelper.createTetrisPrincipal(PacmanWorkContextTestHelper.WC_DN_BSTN_USER,
                PacmanWorkContextTestHelper.WC_CN_REGULAR_USER, USER_ID,
                PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE, false);

        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        globalCrudService.executeUpdateByNativeQuery("update users set corporate= 0 where user_id =" + USER_ID);
        globalCrudService.executeUpdateByNativeQuery("update Roles set corporateUser= 0 where role_id in (1,2,3)");

        roleServiceSpy.setGlobalCrudService(globalCrudService);

        Set<Role> nonCorpRoles = roleServiceSpy.getAllAssignableRoles(false);
        assertNotNull(nonCorpRoles, "Should have assignable roles");
        assertTrue(nonCorpRoles.size() > 0, "Should have at least one assignable roles");
        Role allPermRole = getAllPermsRole(nonCorpRoles);
        Assertions.assertNull(allPermRole, "Non corporate should not see all perms");

        for (Role role : nonCorpRoles) {
            Assertions.assertFalse(role.isCorporate(), "Non corp user should not see corp role");
        }
        globalCrudService.flushAndClear();
        globalCrudService.executeUpdateByNativeQuery("update users set corporate= 1 where user_id =" + USER_ID);
        // Corporate user
        PacmanWorkContextTestHelper.createTetrisPrincipal(PacmanWorkContextTestHelper.WC_DN_BSTN_USER,
                PacmanWorkContextTestHelper.WC_CN_REGULAR_USER, USER_ID,
                PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE, true);

        workContext.setUserId(USER_ID);
        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        Set<Role> corpRoles = roleServiceSpy.getAllAssignableRoles(false);
        assertNotNull(corpRoles, "Should have assignable roles");
        assertTrue(corpRoles.size() > 0, "Should have at least one assignable roles");

        assertTrue(corpRoles.size() > nonCorpRoles.size(),
                "Corporate user should have more role options than non-corporate");

        allPermRole = getAllPermsRole(corpRoles);
        assertNotNull(allPermRole, "Should be an all perms role");
        assertEquals(1, getAllPermsRoleQuantity(corpRoles), "Should only be one all perms role");

        // Internal user
        Set<Role> internalRoles = roleServiceSpy.getAllAssignableRoles(true);
        assertNotNull(internalRoles, "Should have assignable roles");
        assertTrue(internalRoles.size() > 0, "Should have at least one assignable roles");

        allPermRole = getAllPermsRole(internalRoles);
        assertNotNull(allPermRole, "Should be an all perms role");
        assertEquals(1, getAllPermsRoleQuantity(internalRoles), "Should only be one all perms role");
    }

    @Test
    public void getAllAssignableRolesWithoutFeatureRoleAllPermissions() {
        System.setProperty("feature.role.all.permissions", Boolean.FALSE.toString());

        // Non-corporate
        PacmanWorkContextTestHelper.createTetrisPrincipal(PacmanWorkContextTestHelper.WC_DN_BSTN_USER,
                PacmanWorkContextTestHelper.WC_CN_REGULAR_USER, USER_ID,
                PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE, false);

        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        globalCrudService.executeUpdateByNativeQuery("update users set corporate= 0 where user_id =" + USER_ID);
        globalCrudService.executeUpdateByNativeQuery("update Roles set corporateUser= 0 where role_id in (1,2,3)");
        roleServiceSpy.setGlobalCrudService(globalCrudService);

        Set<Role> nonCorpRoles = roleServiceSpy.getAllAssignableRoles(false);
        assertNotNull(nonCorpRoles, "Should have assignable roles");
        assertTrue(nonCorpRoles.size() > 0, "Should have at least one assignable roles");
        Role allPermRole = getAllPermsRole(nonCorpRoles);
        Assertions.assertNull(allPermRole, "Non corporate should not see all perms");

        for (Role role : nonCorpRoles) {
            Assertions.assertFalse(role.isCorporate(), "Non corp user should not see corp role");
        }

        // Corporate user
        globalCrudService.flushAndClear();
        globalCrudService.executeUpdateByNativeQuery("update users set corporate= 1 where user_id =" + USER_ID);
        // Corporate user
        PacmanWorkContextTestHelper.createTetrisPrincipal(PacmanWorkContextTestHelper.WC_DN_BSTN_USER,
                PacmanWorkContextTestHelper.WC_CN_REGULAR_USER, USER_ID,
                PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE, true);

        workContext.setUserId(USER_ID);
        PlatformThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        Set<Role> corpRoles = roleServiceSpy.getAllAssignableRoles(false);
        assertNotNull(corpRoles, "Should have assignable roles");
        assertTrue(corpRoles.size() > 0, "Should have at least one assignable roles");

        assertTrue(corpRoles.size() > nonCorpRoles.size(),
                "Corporate user should have more role options than non-corporate");

        allPermRole = getAllPermsRole(corpRoles);
        assertNotNull(allPermRole, "Should be an all perms role");
        assertEquals(1, getAllPermsRoleQuantity(corpRoles), "Should only be one all perms role");

        // Internal user
        Set<Role> internalRoles = roleServiceSpy.getAllAssignableRoles(true);
        assertNotNull(internalRoles, "Should have assignable roles");
        assertTrue(internalRoles.size() > 0, "Should have at least one assignable roles");

        allPermRole = getAllPermsRole(internalRoles);
        assertNotNull(allPermRole, "Should be an all perms role");
        assertEquals(1, getAllPermsRoleQuantity(internalRoles), "Should only be one all perms role");
    }

    @Test
    public void getAllAssignableRolesGoBoom() throws Exception {
        assertThrows(TetrisException.class, () -> {
            LDAPUser user = new LDAPUser();
            user.setIsCorporate(true);
            roleService.setGlobalCrudService(mockGlobalCrudService);
            when(mockGlobalCrudService.find(anyObject(), anyInt())).thenThrow(new LDAPException("Boom goes the dynamite"));
            roleService.getAllAssignableRoles(false);
        });

    }

    @Test
    public void getAllAssignableRoles_fromGlobal() throws Exception {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        assertNotNull(roleServiceSpy.getAllAssignableRoles(false));
    }

    private Role getAllPermsRole(Set<Role> roles) {
        Role allPermsRole = null;
        for (Role role : roles) {
            if (role.getUniqueIdentifier().equals(Role.ALL_PERMS_ID)) {
                allPermsRole = role;
                break;
            }
        }
        return allPermsRole;
    }

    private int getAllPermsRoleQuantity(Set<Role> roles) {
        int matches = 0;
        for (Role role : roles) {
            if (role.getUniqueIdentifier().equals(Role.ALL_PERMS_ID)) {
                matches++;
            }
        }
        return matches;
    }

    @Test
    public void corporateVsNoncorporate() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        when(globalRoleCache.get(Integer.valueOf(1))).thenReturn(globalCrudService.find(GlobalRole.class, 1));

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");

        Role role = roleServiceSpy.getRole("1");
        assertTrue(role.isCorporate());
        role.setCorporate(false);
        roleServiceSpy.update(role.getUniqueIdentifier(), role);

        role = roleServiceSpy.getRole("1");
        Assertions.assertFalse(role.isCorporate());
        verify(uasService, never()).saveRoleInFDS(any());
    }

    @Test
    public void corporateVsNoncorporate_internalRole() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        when(globalRoleCache.get(Integer.valueOf(1))).thenReturn(globalCrudService.find(GlobalRole.class, 1));

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        Role role = roleServiceSpy.getRole("1");
        assertTrue(role.isCorporate());
        role.setCorporate(false);
        role.setClientCode(Constants.CLIENT_INTERNAL);
        roleServiceSpy.update(role.getUniqueIdentifier(), role);

        role = roleServiceSpy.getRole("1");
        Assertions.assertFalse(role.isCorporate());
        verify(uasService, never()).saveRoleInFDS(any());
    }

    @Test
    public void corporateVsNoncorporate_FDSEnabled() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole globalRole = globalCrudService.find(GlobalRole.class, 1);
        when(globalRoleCache.get(Integer.valueOf(1))).thenReturn(globalRole);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        Role role = roleServiceSpy.getRole("1");
        assertTrue(role.isCorporate());
        role.setCorporate(false);
        roleServiceSpy.update(role.getUniqueIdentifier(), role);

        role = roleServiceSpy.getRole("1");
        Assertions.assertFalse(role.isCorporate());
        verify(uasService).saveRoleInFDS(globalRole);
    }

    @Test
    public void corporateVsNoncorporate_FDSInternalEnabled() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole globalRole = globalCrudService.find(GlobalRole.class, 1);
        when(globalRoleCache.get(Integer.valueOf(1))).thenReturn(globalRole);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");

        Role role = roleServiceSpy.getRole("1");
        role.setClientCode(Constants.CLIENT_INTERNAL);
        assertTrue(role.isCorporate());
        role.setCorporate(false);
        roleServiceSpy.update(role.getUniqueIdentifier(), role);

        role = roleServiceSpy.getRole("1");
        Assertions.assertFalse(role.isCorporate());
        verify(uasService).saveRoleInFDS(globalRole);
    }

    @Test
    public void searchRole_Pacman_usingDB() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        cookRolesInDB();
        Set<Role> roles = roleServiceSpy.search("Property", false);
        assertTrue(roles.size() > 0, "Expected at least one role");
    }

    @Test
    public void searchRole_view_usingDB() {
        cookRolesInDB();
        Set<Role> roles = roleService.search("view", false);
        assertTrue(roles.size() > 0, "Expected at least one role");
    }

    @Test
    public void searchRole_P_usingDB() {
        cookRolesInDB();
        Set<Role> roles = roleService.search("P", false);
        assertTrue(roles.size() > 0, "Expected at least one role");
    }

    @Test
    public void searchRole_ZZZZZ_usingDB() {
        cookRolesInDB();
        Set<Role> roles = roleService.search("ZZZZ", false);
        assertEquals(0, roles.size(), "Expected at least no roles");
    }

    private void cookRolesInDB() {
        roleService.globalCrudService = globalCrudService;
        globalCrudService.deleteAll(GlobalRole.class);
        globalCrudService.save(getRole("MyProperty"));
        globalCrudService.save(getRole("MountainView"));
    }

    private GlobalRole getRole(String roleName) {
        GlobalRole globalRole = new GlobalRole();
        globalRole.setRoleName(roleName);
        globalRole.setClientCode(WC_CLIENT_CODE_BLACKSTONE);
        return globalRole;
    }

    @Test
    public void createAndDeleteRole() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        testRole = getTestRole();
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        testRole = roleServiceSpy.create(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        String delete = roleServiceSpy.delete(testRole.getUniqueIdentifier());
        assertEquals(delete, testRole.getUniqueIdentifier());
        verify(globalRoleCache, times(2)).remove(anyInt());
        verify(globalRoleCache, times(1)).get(anyInt());
        verify(uasService, never()).saveRoleInFDS(any());
        verify(uasService, never()).deleteRoleInFDS(any());
    }

    @Test
    public void createAndDeleteRole_FDSEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        testRole = getTestRole();
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        testRole = roleServiceSpy.create(testRole, false);
        GlobalRole globalRole = new GlobalRole(testRole, testRole.getClientCode());
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(globalRole);
        String delete = roleServiceSpy.delete(testRole.getUniqueIdentifier());
        assertEquals(delete, testRole.getUniqueIdentifier());
        verify(globalRoleCache, times(2)).remove(anyInt());
        verify(globalRoleCache, times(1)).get(anyInt());
        verify(uasService).saveRoleInFDS(globalRole);
        verify(uasService).deleteRoleInFDS(globalRole);
    }

    @Test
    public void createNoPermissionRole() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        testRole = getTestRole();
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        testRole = roleServiceSpy.create(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        assertNotNull(testRole);
        assertNotNull(testRole.getPermissions());
        assertEquals(1, testRole.getPermissions().size());
        assertEquals(Role.NO_PERMISSIONS, testRole.getPermissions().get(0));
        roleServiceSpy.delete(testRole.getUniqueIdentifier());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void createReservedRoleFailsEpically() {
        assertThrows(TetrisException.class, () -> {
            testRole = getTestRole();
            roleServiceSpy.setGlobalCrudService(globalCrudService);
            testRole.setRoleName(Role.ALL_PERMS_ROLE_ASSIGNABLE.getRoleName());
            testRole = roleServiceSpy.create(testRole, false);
        });
    }

    @Test
    public void createReservedRoleCaseInsensitiveFailsEpically() {
        assertThrows(TetrisException.class, () -> {
            testRole = getTestRole();
            testRole.setRoleName(Role.ALL_PERMS_ROLE_ASSIGNABLE.getRoleName().toLowerCase());
            testRole = roleService.create(testRole, false);
        });
    }

    @Test
    public void updateNameToOneInUseFailsEpically() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        assertThrows(TetrisException.class, () -> {
            testRole = getTestRole();
            testRole = roleService.create(testRole, false);
            assertNotNull(testRole);

            testRole.setRoleName(PacmanWorkContextTestHelper.WC_ROLE_NAME_NON_INTERNAL);
            roleService.update(testRole.getUniqueIdentifier(), testRole);
            roleService.delete(testRole.getUniqueIdentifier());
            verify(globalRoleCache, times(1)).remove(anyInt());
            verify(clientService, never()).getClientByCode(any());
            verify(jobService, never()).startJob(any(), anyMap());
        });
    }

    @Disabled
    @Test
    public void updateNameToOneInUseCaseInsensitiveFailsEpically() {
        assertThrows(TetrisException.class, () -> {
            testRole = getTestRole();
            testRole = roleServiceSpy.create(testRole, false);
            assertNotNull(testRole);
            // when(mockGlobalCrudService.find(GlobalRole.class, Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole());
            testRole.setRoleName(PacmanWorkContextTestHelper.WC_ROLE_NAME_NON_INTERNAL.toLowerCase());
            roleServiceSpy.update(testRole.getUniqueIdentifier(), testRole);
            roleServiceSpy.delete(testRole.getUniqueIdentifier());
        });
    }

    @Test
    public void updateNameToReservedFailsEpically() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        assertThrows(TetrisException.class, () -> {
            testRole = getTestRole();
            testRole = roleService.create(testRole, false);
            assertNotNull(testRole);
            testRole.setRoleName(Role.ALL_PERMS_ROLE_ASSIGNABLE.getRoleName());
            roleService.update(testRole.getUniqueIdentifier(), testRole);
            roleService.delete(testRole.getUniqueIdentifier());
            verify(clientService, never()).getClientByCode(any());
            verify(jobService, never()).startJob(any(), anyMap());
        });
    }

    @Test
    public void updateGoBoom() throws Exception {
        assertThrows(TetrisException.class, () -> {
            roleServiceSpy.setGlobalCrudService(globalCrudService);
            roleServiceSpy.setGlobalRoleCache(globalRoleCache);
            globalRoleCache.globalCrudService = mockGlobalCrudService;
            testRole = getTestRole();
            testRole = roleServiceSpy.create(testRole, false);
            when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
            when(mockGlobalCrudService.find(GlobalRole.class, Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
            LDAPRole existingRole = new LDAPRole(testRole, testRole.getClientCode());
            existingRole.setUniqueIdentifier(testRole.getUniqueIdentifier());
            when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
            when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

            roleServiceSpy.update("0", testRole);
            verify(globalRoleCache, times(1)).remove(anyInt());
            verify(globalRoleCache, times(1)).get(anyInt());
            verify(clientService, never()).getClientByCode(any());
            verify(jobService, never()).startJob(any(), anyMap());
        });
    }

    @Test
    public void createRole_alreadyExists() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        assertThrows(TetrisException.class, () -> {
            roleServiceSpy.setGlobalCrudService(globalCrudService);
            roleServiceSpy.setGlobalRoleCache(globalRoleCache);
            globalRoleCache.globalCrudService = mockGlobalCrudService;
            testRole = getTestRole();
            testRole = roleServiceSpy.create(testRole, false);
            when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
            when(mockGlobalCrudService.find(GlobalRole.class, Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));

            roleServiceSpy.create(testRole, false);
            verify(clientService, never()).getClientByCode(any());
            verify(jobService, never()).startJob(any(), anyMap());
        });
    }

    @Test
    public void createUpdateAndDeleteRole_deskClerk() throws LDAPException {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        // Start by deleting any desk clerks that may exist
        Set<Role> roles = roleServiceSpy.search("deskClerk", false);

        for (Role returnedRole : roles) {
            roleServiceSpy.delete(returnedRole.getUniqueIdentifier());
        }

        // On with the test!
        String roleName = "deskClerk-" + RandomStringUtils.randomAlphanumeric(6);

        Role role = new Role();
        role.setRoleName(roleName);
        role.setClientCode(WC_CLIENT_CODE_BLACKSTONE);
        role.setDescription("Desk Clerk");
        role = roleServiceSpy.create(role, false);
        when(globalRoleCache.get(Integer.parseInt(role.getUniqueIdentifier()))).thenReturn(new GlobalRole(role, role.getClientCode()));

        assertEquals(roleName, role.getRoleName(), "Wrong name");
        assertEquals("Desk Clerk", role.getDescription(), "Wrong description");

        String updatedRoleName = "deskClerk2-" + RandomStringUtils.randomAlphanumeric(6);
        role.setRoleName(updatedRoleName);
        roleServiceSpy.update(role.getUniqueIdentifier(), role);

        roles = roleServiceSpy.search("deskClerk", false);

        Role expectedRole = new Role();
        expectedRole.setDescription("Desk Clerk");
        expectedRole.setRoleName(updatedRoleName);

        for (Role returnedRole : roles) {
            Assertions.assertEquals(updatedRoleName, returnedRole.getRoleName());
        }

        roleServiceSpy.delete(role.getUniqueIdentifier());
        verify(globalRoleCache, Mockito.times(1)).get(Integer.parseInt(role.getUniqueIdentifier()));
        verify(globalRoleCache, Mockito.times(3)).remove(Integer.parseInt(role.getUniqueIdentifier()));
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void updateRole_useOpenDS_false() throws LDAPException {
        Role role = new Role();
        role.setRoleName("deskClerk");
        role.setClientCode(CLIENT);
        role.setDescription("Desk Clerk");
        List<String> permissions = new ArrayList<String>();
        permissions.add("pageCode=nick-bauman&access=readWrite");
        permissions.add("pageCode=paul-samargia&access=readOnly");
        role.setPermissions(permissions);
        role.setUniqueIdentifier("5");
        when(mockGlobalCrudService.find(GlobalRole.class, Integer.valueOf(role.getUniqueIdentifier()))).thenReturn(new GlobalRole());
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        roleService.update(role.getUniqueIdentifier(), role);
        verify(mockGlobalCrudService, Mockito.times(1)).save(Mockito.any(GlobalRole.class));
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    // TTRS-2762 - any annotation that we can reap these from?
    @Test()
    public void updateRoleWithoutPerms() throws LDAPException {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        // Start by deleting any desk clerks that may exist
        Set<Role> roles = roleServiceSpy.search("deskClerk", false);
        for (Role returnedRole : roles) {
            roleServiceSpy.delete(returnedRole.getUniqueIdentifier());
        }

        // On with the test!
        Role role = new Role();
        role.setRoleName("deskClerk");
        role.setDescription("Desk Clerk");
        List<String> permissions = new ArrayList<String>();
        permissions.add("pageCode=nick-bauman&access=readWrite");
        permissions.add("pageCode=paul-samargia&access=readOnly");
        role.setPermissions(permissions);
        role = roleServiceSpy.create(role, false);
        assertNotNull(role);
        assertEquals("deskClerk", role.getRoleName(), "Wrong name");
        assertEquals(2, role.getPermissions().size(), "Should have been 2 perms");
        when(globalRoleCache.get(Integer.valueOf(role.getUniqueIdentifier()))).thenReturn(new GlobalRole(role, role.getClientCode()));
        role.setPermissions(new ArrayList<>());
        role = roleServiceSpy.update(role.getUniqueIdentifier(), role);
        assertNotNull(role);
        assertEquals("deskClerk", role.getRoleName(), "Wrong name");
        assertEquals(0, role.getPermissions().size(), "Should have been 0 perms");

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        // clean up
        roleServiceSpy.delete(role.getUniqueIdentifier());

        verify(globalRoleCache, times(3)).remove(anyInt());
        verify(globalRoleCache, times(1)).get(anyInt());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void createAndDeleteRoleWithProvidedClientWhenUsingDB() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole role = new GlobalRole();
        role.setId(1);
        role.setClientCode("unittest");
        when(mockGlobalCrudService.find(any(), any())).thenReturn(role);
        when(mockUserGlobalDBService.listUsersForRole("1")).thenReturn(Collections.emptySet());

        roleService.setUserGlobalDBService(mockUserGlobalDBService);

        testRole = getTestRole();
        testRole.setClientCode("unittest");

        testRole = roleService.createRoleWithProvidedClient(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        assertTrue(StringUtils.isNumeric(testRole.getUniqueIdentifier()));
        roleService.delete(testRole.getUniqueIdentifier());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
        verify(uasService, never()).deleteRoleInFDS(any());
    }

    @Test
    public void createAndDeleteRoleWithProvidedClientWhenUsingDB_Internal() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole role = new GlobalRole();
        role.setId(1);
        role.setInternal(true);
        when(mockGlobalCrudService.find(any(), any())).thenReturn(role);
        when(mockUserGlobalDBService.listUsersForRole("1")).thenReturn(Collections.emptySet());

        roleService.setUserGlobalDBService(mockUserGlobalDBService);

        testRole = getTestRole();
        testRole.setClientCode(Constants.CLIENT_INTERNAL);

        testRole = roleService.createRoleWithProvidedClient(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        assertTrue(StringUtils.isNumeric(testRole.getUniqueIdentifier()));
        roleService.delete(testRole.getUniqueIdentifier());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
        verify(uasService, never()).deleteRoleInFDS(any());
    }

    @Test
    public void createAndDeleteRoleWithProvidedClientWhenUsingDB_FDSEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole role = new GlobalRole();
        role.setId(1);
        role.setClientCode("unittest");
        when(mockGlobalCrudService.find(any(), any())).thenReturn(role);
        when(mockUserGlobalDBService.listUsersForRole("1")).thenReturn(Collections.emptySet());

        roleService.setUserGlobalDBService(mockUserGlobalDBService);

        testRole = getTestRole();
        testRole.setClientCode("unittest");

        testRole = roleService.createRoleWithProvidedClient(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        assertTrue(StringUtils.isNumeric(testRole.getUniqueIdentifier()));
        roleService.delete(testRole.getUniqueIdentifier());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
        verify(uasService).deleteRoleInFDS(role);
    }

    @Test
    public void createAndDeleteRoleWithProvidedClientWhenUsingDB_Internal_FDSInternalEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");
        roleServiceSpy.setGlobalRoleCache(globalRoleCache);
        globalRoleCache.globalCrudService = mockGlobalCrudService;
        GlobalRole role = new GlobalRole();
        role.setId(1);
        role.setInternal(true);
        when(mockGlobalCrudService.find(any(), any())).thenReturn(role);
        when(mockUserGlobalDBService.listUsersForRole("1")).thenReturn(Collections.emptySet());

        roleService.setUserGlobalDBService(mockUserGlobalDBService);

        testRole = getTestRole();
        testRole.setClientCode(Constants.CLIENT_INTERNAL);

        testRole = roleService.createRoleWithProvidedClient(testRole, false);
        when(globalRoleCache.get(Integer.valueOf(testRole.getUniqueIdentifier()))).thenReturn(new GlobalRole(testRole, testRole.getClientCode()));
        assertTrue(StringUtils.isNumeric(testRole.getUniqueIdentifier()));
        roleService.delete(testRole.getUniqueIdentifier());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
        verify(uasService).deleteRoleInFDS(role);
    }

    @Test
    public void createRoleWhenItsNotExistsInDB() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        Role testRole = getTestRole();
        roleService.createRoleIfNotExists(testRole);
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(
                GlobalRole.BY_CLIENT_AND_NAME,
                QueryParameter.with(CLIENT_CODE_KEY, testRole.getClientCode()).and(ROLE_NAME, testRole.getRoleName()).parameters());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void fetchRoleFromDBWhenItsAvailable() {
        Role testRole = getTestRole();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(
                GlobalRole.BY_CLIENT_AND_NAME,
                QueryParameter.with(CLIENT_CODE_KEY, testRole.getClientCode()).and(ROLE_NAME, testRole.getRoleName()).parameters())).thenReturn(getGlobalRole());
        Role role = roleService.createRoleIfNotExists(testRole);
        assertThat(role.getUniqueIdentifier(), is("4"));
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    public void createAndDeleteRoleWithProvidedClient_usingDB() {
        when(mockGlobalCrudService.findByNamedQuerySingleResult(eq(GlobalRole.BY_CLIENT_AND_NAME), anyMap())).thenReturn(null);
        Role role = new Role();
        role.setRoleName("name");
        assertFalse(roleService.doesRoleExist("ideas", role, true));
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(eq(GlobalRole.BY_CLIENT_AND_NAME), anyMap());
    }

    @Test
    void clearRolesUasUuid() {
        GlobalRole globalRole = createGlobalRole();
        GlobalRole globalRole2 = createGlobalRole();
        when(mockGlobalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_CODE, QueryParameter.with(CLIENT_CODE_KEY, CLIENT).parameters())).thenReturn(Arrays.asList(globalRole, globalRole2));

        roleService.clearRolesUasUuid(CLIENT);
        assertNull(globalRole.getUasRoleDictionaryUuid());
        assertNull(globalRole2.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService).save(Arrays.asList(globalRole, globalRole2));
    }

    @Test
    void clearRolesUasUuid_NoRoles() {
        when(mockGlobalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_CODE, QueryParameter.with(CLIENT_CODE_KEY, CLIENT).parameters())).thenReturn(null);

        roleService.clearRolesUasUuid(CLIENT);
        verify(mockGlobalCrudService, never()).save(anyList());
    }

    @Test
    public void getAllRolesSuppressesEntryNotFoundException() {
        String clientCode = "clientCode";
        EntryNotFoundException enfe = mock(EntryNotFoundException.class);
        when(roleService.getAllRolesSuppressesEntryNotFoundException(clientCode)).thenThrow(new TetrisException(ErrorCode.UNEXPECTED_ERROR, "No results found", enfe));
        assertTrue(roleService.getAllRolesSuppressesEntryNotFoundException(clientCode).isEmpty());
    }

    @Test
    public void getAllRolesSuppressesEntryNotFoundExceptionThrowsNonNotFoundExceptions() {
        assertThrows(TetrisException.class, () -> {
            String clientCode = "clientCode";
            GlobalUser globalUser = getLoggedInUser(true);
            globalUser.setClientCode(clientCode);
            when(roleService.getAllRolesSuppressesEntryNotFoundException(clientCode)).thenThrow(new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Some IOException Occurred", new IOException("Boom!")));
            roleService.getAllRolesSuppressesEntryNotFoundException(clientCode);
        });
    }

    private GlobalUser getLoggedInUser(boolean isInternal) {
        GlobalUser user = new GlobalUser();
        user.setInternal(isInternal);
        return user;
    }

    @Test
    public void testDelete_useOpenDSFalse() {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        GlobalRole globalRole = new GlobalRole();
        globalRole.setId(123);
        globalRole.setClientCode(WC_CLIENT_CODE_BLACKSTONE);
        globalRole.setRoleName("my test role");
        globalRole = globalCrudService.save(globalRole);
        when(globalRoleCache.get(globalRole.getId())).thenReturn(globalRole);
        List<LDAPUser> users = setupUsers(globalRole.getId().toString());

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        roleServiceSpy.delete(globalRole.getId().toString());

        verifyRolesRemovedForUsers(users);
        verify(globalRoleCache, Mockito.times(1)).get(globalRole.getId());
        verify(globalRoleCache, Mockito.times(2)).remove(globalRole.getId());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }


    @Test
    public void testDelete_useOpenDSFalse_roleDoesNotExist() {
        assertThrows(TetrisException.class, () -> {
            when(mockGlobalCrudService.find(GlobalRole.class, 123)).thenReturn(null);
            roleService.delete("123");
        });
    }

    private List<LDAPUser> setupUsers(String roleId) {
        List<LDAPUser> users = new ArrayList<>();

        LDAPUser user1 = new LDAPUser();
        user1.setDN("user1");
        user1.setUserId(1);
        user1.setAuthGroupRoles(Arrays.asList(new AuthGroupRoleMapping(roleId, "6")));
        user1.setPropertyRoles(Arrays.asList(new PropertyRoleMapping("someOtherRoleId", "7")));
        user1.setClient(WC_CLIENT_CODE_BLACKSTONE);
        user1.setIsCorporate(false);
        users.add(user1);

        LDAPUser user2 = new LDAPUser();
        user2.setDN("user2");
        user2.setUserId(2);
        user2.setAuthGroupRoles(Arrays.asList(new AuthGroupRoleMapping("someOtherRoleId", "6")));
        user2.setPropertyRoles(Arrays.asList(new PropertyRoleMapping(roleId, "7")));
        user2.setClient(WC_CLIENT_CODE_BLACKSTONE);
        user2.setIsCorporate(false);
        users.add(user2);

        when(mockUserGlobalDBService.listUsersForRole(roleId)).thenReturn(new HashSet<LDAPUser>(users));

        return users;
    }

    private void verifyRolesRemovedForUsers(List<LDAPUser> users) {
        ArgumentCaptor<LDAPUser> captor = ArgumentCaptor.forClass(LDAPUser.class);
        verify(mockUserService).update(eq(users.get(0).getUid()), captor.capture());
        assertEquals(1, captor.getValue().getPropertyRoles().size());
        assertEquals("someOtherRoleId", captor.getValue().getPropertyRoles().get(0).getRoleId());
        assertEquals(0, captor.getValue().getAuthGroupRoles().size());

        ArgumentCaptor<LDAPUser> captor2 = ArgumentCaptor.forClass(LDAPUser.class);
        verify(mockUserService).update(eq(users.get(1).getUid()), captor2.capture());
        assertEquals(0, captor2.getValue().getPropertyRoles().size());
        assertEquals(1, captor2.getValue().getAuthGroupRoles().size());
        assertEquals("someOtherRoleId", captor2.getValue().getAuthGroupRoles().get(0).getRoleId());

    }

    @Test
    public void findByClientAndRoleName_useOpenDSFalse() {
        String client = "SomeClient";
        String roleName = TEST_ROLE;

        GlobalRole globalRole = new GlobalRole();
        globalRole.setId(123);
        globalRole.setClientCode(client);
        globalRole.setRoleName(roleName);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_CLIENT_AND_NAME,
                QueryParameter.with("clientCode", client).and("roleName", roleName).parameters())).thenReturn(globalRole);

        Role role = roleService.findRoleByClientAndName(client, roleName);

        assertNotNull(role);
        assertEquals("123", role.getUniqueIdentifier());
        assertEquals(client, role.getClientCode());
        assertEquals(roleName, role.getRoleName());
    }


    private LDAPRole createLdapRole() {
        LDAPRole ldapRole = new LDAPRole("abc123", TEST_ROLE, "Some Description", false);
        ldapRole.setPermissions(Arrays.asList("permissions"));
        ldapRole.setViewAnnouncements(true);
        ldapRole.setIsCorporate(true);
        return ldapRole;
    }

    @Test
    public void getPropertiesForUser() throws Exception {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setUserService(mockUserService);
        when(mockUserService.getById(Integer.parseInt(USER_ID))).thenReturn(ldapUser);
        AuthGroupExploder exploder = new MockAuthGroupExploder();
        Set<String> props = roleServiceSpy.getPropertiesForUser(PlatformWorkContextTestHelper.WC_DN_BSTN_USER, exploder);
        assertEquals(4, props.size());
    }

    @Test
    public void getPropertiesForUserWithBadExploder() throws Exception {
        assertThrows(LDAPException.class, () -> {
            roleService.setUserService(mockUserService);
            when(mockUserService.getById(5003)).thenReturn(ldapUser);
            AuthGroupExploder mockExploder = mock(AuthGroupExploder.class);
            when(mockExploder.getAuthGroupPropertyIDs(isA(Integer.class)))
                    .thenThrow(new ExplosionException("Boom. Itchy trigger finger"));
            roleService.getPropertiesForUser(PlatformWorkContextTestHelper.WC_DN_REGULAR_USER, mockExploder);
        });
    }

    @Test
    public void getPermsForUser() throws Exception {
        roleServiceSpy.setGlobalCrudService(globalCrudService);
        roleServiceSpy.setUserService(mockUserService);
        when(mockUserService.getById(5003)).thenReturn(ldapUser);
        doReturn(createLdapRole().toRole()).when(roleServiceSpy).getRole(anyString());
        Set<String> perms = roleServiceSpy.getPermsForUser(PlatformWorkContextTestHelper.WC_DN_REGULAR_USER, "20",
                new MockAuthGroupExploder());
        assertTrue(perms.size() > 0, "User has permissions");
    }

    @Test
    public void getPermsForUserWithBadExploder() throws Exception {
        assertThrows(LDAPException.class, () -> {
            roleService.setUserService(mockUserService);
            when(mockUserService.getById(5003)).thenReturn(ldapUser);
            AuthGroupExploder mockExploder = mock(AuthGroupExploder.class);
            when(mockExploder.getAuthGroupPropertyIDs(isA(Integer.class)))
                    .thenThrow(new ExplosionException("Boom. Itchy trigger finger"));
            roleService.getPermsForUser(PlatformWorkContextTestHelper.WC_DN_REGULAR_USER, "20", mockExploder);
        });
    }

    @Test
    public void getPermsForAdminUser() throws Exception {
        LDAPUser ldapUsertest = ldapUser;
        List<String> roleList = new ArrayList<String>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        ldapUsertest.setRoles(roleList);
        roleService.setUserService(mockUserService);
        when(mockUserService.getById(Integer.parseInt(PlatformWorkContextTestHelper.WC_USER_ID_BSTN))).thenReturn(ldapUsertest);
        Set<String> perms = roleService.getPermsForUser(PlatformWorkContextTestHelper.WC_DN_BSTN_USER, "20",
                new MockAuthGroupExploder());
        assertEquals(1, perms.size(), "Super duper admin has permissions");
        assertTrue(perms.contains(Role.ALL_PERMS_ID), "Super duper admin has THE permission");
    }

    @Test
    public void testDeleteForClient() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");
        GlobalRole globalRole = createGlobalRole();
        when(mockGlobalCrudService.findByNamedQuery(eq(GlobalRole.BY_CLIENT_CODE), anyMapOf(String.class, Object.class))).thenReturn(Collections.singletonList(globalRole));
        roleService.deleteForClient(CLIENT);
        verify(mockGlobalCrudService).delete(Collections.singletonList(globalRole));
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void testDeleteForClient_FDSEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        GlobalRole globalRole = createGlobalRole();
        when(mockGlobalCrudService.findByNamedQuery(eq(GlobalRole.BY_CLIENT_CODE), anyMapOf(String.class, Object.class))).thenReturn(Collections.singletonList(globalRole));
        when(mockClientConfigService.getClientByCode(CLIENT)).thenReturn(client);
        Mockito.when(jobServicesBridgeExtension.getJobServicesBridge()).thenReturn(jobServicesBridge);

        roleService.deleteForClient(CLIENT);
        verify(mockGlobalCrudService).delete(Collections.singletonList(globalRole));
        verify(mockClientConfigService).getClientByCode(any());
        verify(mockJobService).startJob(any(), anyMap());
    }

    @Test
    public void testDeleteUsersFromAuthGroupRoleForClient() {
        when(mockGlobalCrudService.executeUpdateByNativeQuery("DELETE FROM User_Auth_Group_Role WHERE USER_ID IN (SELECT USER_ID FROM Users Where Client_Code = 'Client_Code')")).thenReturn(1);
        roleService.deleteUsersFromUserAuthGroupRolesForClient("Client_Code");
        verify(mockGlobalCrudService).executeUpdateByNativeQuery("DELETE FROM User_Auth_Group_Role WHERE USER_ID IN (SELECT USER_ID FROM Users Where Client_Code = 'Client_Code')");
    }

    @Test
    public void findRoles() {
        roleService.findAllGlobalRoles();
        verify(mockGlobalCrudService).findAll(GlobalRole.class);
    }

    @Test
    public void testSaveRoleRanking() {
        Role role = new Role();
        role.setRoleName("RoleTest");
        role.setClientCode(CLIENT);
        role.setDescription("Desk Clerk");
        role.setUniqueIdentifier("1");

        GlobalRole globalRole = new GlobalRole();
        globalRole.setRoleName("RoleTest");
        globalRole.setClientCode(CLIENT);
        globalRole.setRoleRanking(1);

        Set<GlobalRole> globalRoleSet = new LinkedHashSet<>();
        globalRoleSet.add(globalRole);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");

        roleService.updateRoleRanking(globalRoleSet);
        verify(mockGlobalCrudService, Mockito.times(1)).save(globalRoleSet);
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void testSaveRoleRanking_emptySet() {
        roleService.updateRoleRanking(new HashSet<>());
        verify(mockGlobalCrudService, never()).save(anySet());
        verify(mockClientService, never()).getClientByCode(any());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    public void testSaveRoleRanking_FDSEnabled() {
        roleServiceSpy.setClientService(mockClientService);
        roleServiceSpy.setJobService(mockJobService);

        Role role = new Role();
        role.setRoleName("RoleTest");
        role.setClientCode(CLIENT);
        role.setDescription("Desk Clerk");
        role.setUniqueIdentifier("1");

        GlobalRole globalRole = new GlobalRole();
        globalRole.setRoleName("RoleTest");
        globalRole.setClientCode(CLIENT);
        globalRole.setRoleRanking(1);

        Set<GlobalRole> globalRoleSet = new LinkedHashSet<>();
        globalRoleSet.add(globalRole);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(mockClientConfigService.getClientByCode(CLIENT)).thenReturn(client);
        Mockito.when(jobServicesBridgeExtension.getJobServicesBridge()).thenReturn(jobServicesBridge);

        roleService.updateRoleRanking(globalRoleSet);
        verify(mockGlobalCrudService, Mockito.times(1)).save(globalRoleSet);
        verify(mockJobService).startJob(any(), anyMap());
    }

    @Test
    public void testSaveRoleRanking_FDSEnabled_emptySet() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");

        roleService.updateRoleRanking(new HashSet<>());
        verify(mockGlobalCrudService, never()).save(anySet());
        verify(mockJobService, never()).startJob(any(), anyMap());
    }

    @Test
    void saveRole() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");
        Role role = createRole("role1", ROLE_ID);
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());

        roleService.saveRole(role);
        verify(mockGlobalCrudService).save(globalRole);
        verify(uasService, never()).saveRoleInFDS(any());
    }

    @Test
    void saveRole_internalRole() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        Role role = createRole("role1", ROLE_ID);
        role.setClientCode(Constants.CLIENT_INTERNAL);
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());

        roleService.saveRole(role);
        verify(mockGlobalCrudService).save(globalRole);
        verify(uasService, never()).saveRoleInFDS(any());
    }

    @Test
    void saveRole_FDSEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("true");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("false");
        Role role = createRole("role1", ROLE_ID);
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());

        roleService.saveRole(role);
        verify(mockGlobalCrudService).save(globalRole);
        verify(uasService).saveRoleInFDS(globalRole);
    }

    @Test
    void saveRole_FDSInternalEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName())).thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())).thenReturn("true");
        Role role = createRole("role1", ROLE_ID);
        role.setClientCode(Constants.CLIENT_INTERNAL);
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());

        roleService.saveRole(role);
        verify(mockGlobalCrudService).save(globalRole);
        verify(uasService).saveRoleInFDS(globalRole);
    }

    @Test
    void persistRole() {
        Role role = createRole("role1", ROLE_ID);
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());

        roleService.persistRole(role);
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    public void shouldAddPermissionReadWriteForMinPriceDiffForValidCase() throws LDAPException {
        Role role = new Role();
        role.setRoleName("deskClerk");
        role.setClientCode(CLIENT);
        role.setDescription("Desk Clerk");
        List<String> permissions = new ArrayList<String>();
        permissions.add("pageCode=rooms-configuration&access=readWrite&functions={roomsConfiguration:readWrite,costOfWalkSettings:readWrite}");
        role.setPermissions(permissions);
        role.setUniqueIdentifier("5");
        String result = roleService.setRWPermissionForMinPriceDiff(role.getPermissions().get(0));
        assertTrue(result.contains(TetrisPermissionKey.ROOMS_CONFIGURATION_MIN_PRICE_DIFFERENTIAL.concat(":readWrite")));
    }

    @Test
    void processRoleCreateFromFDS() {
        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());

        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.processRoleCreateFromFDS(client, globalRole.getUasRoleDictionaryUuid());

        ArgumentCaptor<GlobalRole> captor = ArgumentCaptor.forClass(GlobalRole.class);
        verify(mockGlobalCrudService).save(captor.capture());
        assertEquals(globalRole.getUasRoleDictionaryUuid(), captor.getValue().getUasRoleDictionaryUuid());
        assertEquals(globalRole.getRoleName(), captor.getValue().getRoleName());
        assertEquals(globalRole.getDescription(), captor.getValue().getDescription());
        assertEquals(globalRole.getClientCode(), captor.getValue().getClientCode());
        assertEquals(globalRole.getRoleRanking(), captor.getValue().getRoleRanking());
        assertEquals(Role.NO_PERMISSIONS, captor.getValue().getPermissions());
        assertEquals(globalRole.isCorporate(), captor.getValue().isCorporate());
        assertEquals(globalRole.isInternal(), captor.getValue().isInternal());
        assertEquals(globalRole.isViewAnnouncements(), captor.getValue().isViewAnnouncements());
    }

    @Test
    void processRoleCreateFromFDS_RoleAlreadyExists() {
        GlobalRole globalRole = createGlobalRole();
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());

        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.processRoleCreateFromFDS(client, globalRole.getUasRoleDictionaryUuid());

        ArgumentCaptor<GlobalRole> captor = ArgumentCaptor.forClass(GlobalRole.class);
        verify(mockGlobalCrudService).save(captor.capture());
        assertEquals(globalRole.getUasRoleDictionaryUuid(), captor.getValue().getUasRoleDictionaryUuid());
        assertEquals(globalRole.getRoleName(), captor.getValue().getRoleName());
        assertEquals(globalRole.getDescription(), captor.getValue().getDescription());
        assertEquals(globalRole.getClientCode(), captor.getValue().getClientCode());
        assertEquals(globalRole.getRoleRanking(), captor.getValue().getRoleRanking());
        assertEquals(globalRole.getPermissions(), captor.getValue().getPermissions());
        assertEquals(globalRole.isCorporate(), captor.getValue().isCorporate());
        assertEquals(globalRole.isInternal(), captor.getValue().isInternal());
        assertEquals(globalRole.isViewAnnouncements(), captor.getValue().isViewAnnouncements());
    }

    @Test
    void processRoleUpdateFromFDS() {
        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());

        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.processRoleUpdateFromFDS(client, globalRole.getUasRoleDictionaryUuid());

        ArgumentCaptor<GlobalRole> captor = ArgumentCaptor.forClass(GlobalRole.class);
        verify(mockGlobalCrudService).save(captor.capture());
        assertEquals(globalRole.getUasRoleDictionaryUuid(), captor.getValue().getUasRoleDictionaryUuid());
        assertEquals(globalRole.getRoleName(), captor.getValue().getRoleName());
        assertEquals(globalRole.getDescription(), captor.getValue().getDescription());
        assertEquals(globalRole.getClientCode(), captor.getValue().getClientCode());
        assertEquals(globalRole.getRoleRanking(), captor.getValue().getRoleRanking());
        assertEquals(globalRole.getPermissions(), captor.getValue().getPermissions());
        assertEquals(globalRole.isCorporate(), captor.getValue().isCorporate());
        assertEquals(globalRole.isInternal(), captor.getValue().isInternal());
        assertEquals(globalRole.isViewAnnouncements(), captor.getValue().isViewAnnouncements());
    }

    @Test
    void processRoleUpdateFromFDS_RoleDoesntExists() {
        GlobalRole globalRole = createGlobalRole();
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());

        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.processRoleUpdateFromFDS(client, globalRole.getUasRoleDictionaryUuid());

        ArgumentCaptor<GlobalRole> captor = ArgumentCaptor.forClass(GlobalRole.class);
        verify(mockGlobalCrudService).save(captor.capture());
        assertEquals(globalRole.getUasRoleDictionaryUuid(), captor.getValue().getUasRoleDictionaryUuid());
        assertEquals(globalRole.getRoleName(), captor.getValue().getRoleName());
        assertEquals(globalRole.getDescription(), captor.getValue().getDescription());
        assertEquals(globalRole.getClientCode(), captor.getValue().getClientCode());
        assertEquals(globalRole.getRoleRanking(), captor.getValue().getRoleRanking());
        assertEquals(Role.NO_PERMISSIONS, captor.getValue().getPermissions());
        assertEquals(globalRole.isCorporate(), captor.getValue().isCorporate());
        assertEquals(globalRole.isInternal(), captor.getValue().isInternal());
        assertEquals(globalRole.isViewAnnouncements(), captor.getValue().isViewAnnouncements());
    }

    @Test
    void updateGlobalRolesFromFDS_Create_External() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRolesFromFDS(clientId.toString())).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(clientId.toString());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRolesFromFDS_Update_External() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());
        when(uasService.getRolesFromFDS(clientId.toString())).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(clientId.toString());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    void updateGlobalRolesFromFDS_Create_Internal() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRolesFromFDS(Constants.CLIENT_INTERNAL_UUID)).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(Constants.CLIENT_INTERNAL_UUID);
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRolesFromFDS_Create_Internal_DifferentProductEnvironment() {
        System.setProperty("pacman.fds.g3.environment.ids", "68622f84-7aac-4463-b43d-ea39f7163a9d,78622f84-7aac-4463-b43d-ea39f7163a9d");
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("78622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRolesFromFDS(Constants.CLIENT_INTERNAL_UUID)).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(Constants.CLIENT_INTERNAL_UUID);
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRolesFromFDS_Create_Internal_MissingG3Permissions() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test_no_g3_permissions");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        //Non-G3 product environment id
        permissions.put("99922f84-7aac-4463-b43d-ea39f7163999", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRolesFromFDS(Constants.CLIENT_INTERNAL_UUID)).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(Constants.CLIENT_INTERNAL_UUID);
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService, never()).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRolesFromFDS_Update_Internal() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());
        when(uasService.getRolesFromFDS(Constants.CLIENT_INTERNAL_UUID)).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(Constants.CLIENT_INTERNAL_UUID);
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    void updateGlobalRolesFromFDS_Update_Internal_DifferentProductEnvironment() {
        System.setProperty("pacman.fds.g3.environment.ids", "68622f84-7aac-4463-b43d-ea39f7163a9d,78622f84-7aac-4463-b43d-ea39f7163a9d");
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("78622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());
        when(uasService.getRolesFromFDS(Constants.CLIENT_INTERNAL_UUID)).thenReturn(Arrays.asList(uasRole));

        roleService.updateGlobalRolesFromFDS(Constants.CLIENT_INTERNAL_UUID);
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    void updateGlobalRolesFromFDS_UnknownClient() {
        roleService.setClientService(mockClientService);

        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> roleService.updateGlobalRolesFromFDS(clientId.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error updating roles as the client doesn't exist with clientId: " + clientId, tetrisException.getBaseMessage());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService, never()).findByNamedQuerySingleResult(any(), anyMap());
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRoleFromFDS_Create_External() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRoleFromFDS(uasRoleDictionaryUuid.toString())).thenReturn(uasRole);

        roleService.updateGlobalRoleFromFDS(clientId.toString(), uasRole.getRoleId().toString());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(uasService).getRoleFromFDS(uasRoleDictionaryUuid.toString());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRoleFromFDS_Update_External() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());
        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.updateGlobalRoleFromFDS(clientId.toString(), uasRole.getRoleId().toString());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(uasService).getRoleFromFDS(globalRole.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    void updateGlobalRoleFromFDS_Create_Internal() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid).parameters())).thenReturn(null);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(uasRoleDictionaryUuid);
        uasRole.setName("test");
        uasRole.setDescription("testDescription");
        uasRole.setCorporateUser(true);
        uasRole.setViewAnnouncement(false);
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(1);
        when(uasService.getRoleFromFDS(uasRoleDictionaryUuid.toString())).thenReturn(uasRole);

        roleService.updateGlobalRoleFromFDS(Constants.CLIENT_INTERNAL_UUID, uasRole.getRoleId().toString());
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(uasService).getRoleFromFDS(uasRoleDictionaryUuid.toString());
        verify(mockGlobalCrudService).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRoleFromFDS_Update_Internal() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);
        roleService.setUasService(uasService);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(null);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);

        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.fromString(globalRole.getUasRoleDictionaryUuid()));
        uasRole.setName(globalRole.getRoleName());
        uasRole.setDescription(globalRole.getDescription());
        uasRole.setCorporateUser(globalRole.isCorporate());
        uasRole.setViewAnnouncement(globalRole.isViewAnnouncements());
        uasRole.setInternalUser(true);
        HashMap<String, Object> permissions = new HashMap<>();
        permissions.put("68622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        uasRole.setPermissions(permissions);
        uasRole.setRoleRanking(globalRole.getRoleRanking());
        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(uasRole);

        roleService.updateGlobalRoleFromFDS(Constants.CLIENT_INTERNAL_UUID, uasRole.getRoleId().toString());
        verify(mockClientService, never()).findClientByUpsClientUuid(anyString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(uasService).getRoleFromFDS(globalRole.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService).save(globalRole);
    }

    @Test
    void updateGlobalRoleFromFDS_UnknownClient() {
        roleService.setClientService(mockClientService);

        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(null);
        UUID roleId = UUID.randomUUID();

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> roleService.updateGlobalRoleFromFDS(clientId.toString(), roleId.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error updating roleId: " + roleId.toString() + ", as the client doesn't exist with clientId: " + clientId.toString(), tetrisException.getBaseMessage());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService, never()).findByNamedQuerySingleResult(any(), anyMap());
        verify(uasService, never()).getRoleFromFDS(any());
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRoleFromFDS_Update_UnknownRoleInFDS() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        GlobalRole globalRole = createGlobalRole();
        globalRole.setId(1);
        globalRole.setPermissions(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);
        when(uasService.getRoleFromFDS(globalRole.getUasRoleDictionaryUuid())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> roleService.updateGlobalRoleFromFDS(clientId.toString(), globalRole.getUasRoleDictionaryUuid()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error updating roleId: " + globalRole.getUasRoleDictionaryUuid() + " for clientId: " + client.getUpsClientUuid() + ", as the role doesn't exist in FDS", tetrisException.getBaseMessage());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters());
        verify(uasService).getRoleFromFDS(globalRole.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    void updateGlobalRoleFromFDS_Create_UnknownRoleInFDS() {
        roleService.setClientService(mockClientService);
        roleService.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        UUID clientId = UUID.randomUUID();
        client.setUpsClientUuid(clientId.toString());
        client.setCode("Hilton");
        when(mockClientService.findClientByUpsClientUuid(clientId.toString())).thenReturn(client);

        UUID uasRoleDictionaryUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters())).thenReturn(null);
        when(uasService.getRoleFromFDS(uasRoleDictionaryUuid.toString())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> roleService.updateGlobalRoleFromFDS(clientId.toString(), uasRoleDictionaryUuid.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error creating roleId: " + uasRoleDictionaryUuid.toString() + " for clientId: " + client.getUpsClientUuid() + ", as the role doesn't exist in FDS", tetrisException.getBaseMessage());
        verify(mockClientService).findClientByUpsClientUuid(clientId.toString());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", uasRoleDictionaryUuid.toString()).parameters());
        verify(uasService).getRoleFromFDS(uasRoleDictionaryUuid.toString());
        verify(mockGlobalCrudService, never()).save(any(GlobalRole.class));
    }

    @Test
    public void convertFDSPermissions_G3Only() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Parent page with function
        UUID permDefUuid1 = UUID.randomUUID();
        PermissionDefinition permDef1 = PermissionDefinition
                .builder()
                .id(permDefUuid1)
                .pageCode("information-manager")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef1);

        //Child page
        UUID permDefUuid2 = UUID.randomUUID();
        PermissionDefinition permDef2 = PermissionDefinition
                .builder()
                .id(permDefUuid2)
                .pageCode("alerts")
                .isFunction(true)
                .parentId(permDefUuid1)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef2);

        //Parent page without functions
        UUID permDefUuid3 = UUID.randomUUID();
        PermissionDefinition permDef3 = PermissionDefinition
                .builder()
                .id(permDefUuid3)
                .pageCode("investigator")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.NO_ACCESS)
                .build();
        permissionDefinitions.add(permDef3);

        //Parent page with menu excluded pages
        UUID permDefUuid4 = UUID.randomUUID();
        PermissionDefinition permDef4 = PermissionDefinition
                .builder()
                .id(permDefUuid4)
                .pageCode("pricing-config")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef4);

        //Child page that is menu excluded
        UUID permDefUuid5 = UUID.randomUUID();
        PermissionDefinition permDef5 = PermissionDefinition
                .builder()
                .id(permDefUuid5)
                .pageCode("pricing-product-list")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef5);

        //Child page with functionalities and that is menu excluded
        UUID permDefUuid6 = UUID.randomUUID();
        PermissionDefinition permDef6 = PermissionDefinition
                .builder()
                .id(permDefUuid6)
                .pageCode("pricing-configuration")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef6);

        //Child function page 1
        UUID permDefUuid7 = UUID.randomUUID();
        PermissionDefinition permDef7 = PermissionDefinition
                .builder()
                .id(permDefUuid7)
                .pageCode("pricingConfigurationDefinitionStep")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef7);

        //Child function page
        UUID permDefUuid8 = UUID.randomUUID();
        PermissionDefinition permDef8 = PermissionDefinition
                .builder()
                .id(permDefUuid8)
                .pageCode("pricingConfigurationTransientCeilingAndFloor")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef8);

        //Page without permission defined for user
        UUID permDefUuid9 = UUID.randomUUID();
        PermissionDefinition permDef9 = PermissionDefinition
                .builder()
                .id(permDefUuid9)
                .pageCode("competitor-custom-room-mapping")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef9);

        //Child function page - No Access
        UUID permDefUuid11 = UUID.randomUUID();
        PermissionDefinition permDef11 = PermissionDefinition
                .builder()
                .id(permDefUuid11)
                .pageCode("pricingConfigurationOffsets")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef11);

        //Page with functionalities that are no access
        UUID permDefUuid12 = UUID.randomUUID();
        PermissionDefinition permDef12 = PermissionDefinition
                .builder()
                .id(permDefUuid12)
                .pageCode("monitoring-dashboard")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef12);

        //Child function page 1 - No Access
        UUID permDefUuid13 = UUID.randomUUID();
        PermissionDefinition permDef13 = PermissionDefinition
                .builder()
                .id(permDefUuid13)
                .pageCode("support.jobAction")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef13);

        //Child function page 2 - No Access
        UUID permDefUuid14 = UUID.randomUUID();
        PermissionDefinition permDef14 = PermissionDefinition
                .builder()
                .id(permDefUuid14)
                .pageCode("support.schedule.stop-all")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef14);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();
        //revPlan is just added for testing purposes and should be ignored during processing
        permissionMap.put("78622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(permDefUuid1.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid2.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid3.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid4.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid5.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid6.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid7.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid8.toString(), ActionKey.readOnly.toString());
        //permDefUuid9 is not added to test that no permissions is set in this case
        //permDefUuid10 is unknown UUID that doesn't match to page code; so we just ignore it
        UUID permDefUuid10 = UUID.randomUUID();
        g3PermissionMap.put(permDefUuid10.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid11.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid12.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid13.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid14.toString(), ActionKey.noAccess.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(6, result.size());
        assertTrue(result.contains("pageCode=information-manager&access=readWrite&functions={alerts:readOnly}"));
        assertTrue(result.contains("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readOnly,pricingConfigurationTransientCeilingAndFloor:readOnly}"));
        assertTrue(result.contains("pageCode=investigator&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-config&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-product-list&access=readOnly"));
        assertFalse(result.contains("competitor-custom-room-mapping&access=noAccess"));
        assertTrue(result.contains("pageCode=monitoring-dashboard&access=readWrite"));
    }

    @Test
    public void convertFDSPermissions_G3AndUADAndSpecialEvents() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Parent page with function
        UUID permDefUuid1 = UUID.randomUUID();
        PermissionDefinition permDef1 = PermissionDefinition
                .builder()
                .id(permDefUuid1)
                .pageCode("information-manager")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef1);

        //Child page
        UUID permDefUuid2 = UUID.randomUUID();
        PermissionDefinition permDef2 = PermissionDefinition
                .builder()
                .id(permDefUuid2)
                .pageCode("alerts")
                .isFunction(true)
                .parentId(permDefUuid1)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef2);

        //Parent page without functions
        UUID permDefUuid3 = UUID.randomUUID();
        PermissionDefinition permDef3 = PermissionDefinition
                .builder()
                .id(permDefUuid3)
                .pageCode("investigator")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.NO_ACCESS)
                .build();
        permissionDefinitions.add(permDef3);

        //Parent page with menu excluded pages
        UUID permDefUuid4 = UUID.randomUUID();
        PermissionDefinition permDef4 = PermissionDefinition
                .builder()
                .id(permDefUuid4)
                .pageCode("pricing-config")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef4);

        //Child page that is menu excluded
        UUID permDefUuid5 = UUID.randomUUID();
        PermissionDefinition permDef5 = PermissionDefinition
                .builder()
                .id(permDefUuid5)
                .pageCode("pricing-product-list")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef5);

        //Child page with functionalities and that is menu excluded
        UUID permDefUuid6 = UUID.randomUUID();
        PermissionDefinition permDef6 = PermissionDefinition
                .builder()
                .id(permDefUuid6)
                .pageCode("pricing-configuration")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef6);

        //Child function page 1
        UUID permDefUuid7 = UUID.randomUUID();
        PermissionDefinition permDef7 = PermissionDefinition
                .builder()
                .id(permDefUuid7)
                .pageCode("pricingConfigurationDefinitionStep")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef7);

        //Child function page
        UUID permDefUuid8 = UUID.randomUUID();
        PermissionDefinition permDef8 = PermissionDefinition
                .builder()
                .id(permDefUuid8)
                .pageCode("pricingConfigurationTransientCeilingAndFloor")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef8);

        //Page without permission defined for user
        UUID permDefUuid9 = UUID.randomUUID();
        PermissionDefinition permDef9 = PermissionDefinition
                .builder()
                .id(permDefUuid9)
                .pageCode("competitor-custom-room-mapping")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef9);

        //Child function page - No Access
        UUID permDefUuid11 = UUID.randomUUID();
        PermissionDefinition permDef11 = PermissionDefinition
                .builder()
                .id(permDefUuid11)
                .pageCode("pricingConfigurationOffsets")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef11);

        //Page with functionalities that are no access
        UUID permDefUuid12 = UUID.randomUUID();
        PermissionDefinition permDef12 = PermissionDefinition
                .builder()
                .id(permDefUuid12)
                .pageCode("monitoring-dashboard")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef12);

        //Child function page 1 - No Access
        UUID permDefUuid13 = UUID.randomUUID();
        PermissionDefinition permDef13 = PermissionDefinition
                .builder()
                .id(permDefUuid13)
                .pageCode("support.jobAction")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef13);

        //Child function page 2 - No Access
        UUID permDefUuid14 = UUID.randomUUID();
        PermissionDefinition permDef14 = PermissionDefinition
                .builder()
                .id(permDefUuid14)
                .pageCode("support.schedule.stop-all")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef14);

        //UAD page
        UUID permDefUuid15 = UUID.randomUUID();
        PermissionDefinition permDef15 = PermissionDefinition
                .builder()
                .id(permDefUuid15)
                .pageCode("user-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef15);

        //UAD function
        UUID permDefUuid16 = UUID.randomUUID();
        PermissionDefinition permDef16 = PermissionDefinition
                .builder()
                .id(permDefUuid16)
                .pageCode("user-management-activate-deactivate")
                .isFunction(true)
                .parentId(permDefUuid15)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef16);

        //UAD page with functions
        UUID permDefUuid20 = UUID.randomUUID();
        PermissionDefinition permDef20 = PermissionDefinition
                .builder()
                .id(permDefUuid20)
                .pageCode("system-announcements")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef20);

        //Special Events page with functions
        UUID permDefUuid21 = UUID.randomUUID();
        PermissionDefinition permDef21 = PermissionDefinition
                .builder()
                .id(permDefUuid21)
                .pageCode("special-events-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef21);

        //Special Events function
        UUID permDefUuid22 = UUID.randomUUID();
        PermissionDefinition permDef22 = PermissionDefinition
                .builder()
                .id(permDefUuid22)
                .pageCode("specialEventsAddNewDelete")
                .isFunction(true)
                .parentId(permDefUuid21)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef22);

        //Special Events page without functions
        UUID permDefUuid23 = UUID.randomUUID();
        PermissionDefinition permDef23 = PermissionDefinition
                .builder()
                .id(permDefUuid23)
                .pageCode("special-event-upload")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef23);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();
        //revPlan is just added for testing purposes and should be ignored during processing
        permissionMap.put("78622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(permDefUuid1.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid2.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid3.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid4.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid5.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid6.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid7.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid8.toString(), ActionKey.readOnly.toString());
        //permDefUuid9 is not added to test that no permissions is set in this case
        //permDefUuid10 is unknown UUID that doesn't match to page code; so we just ignore it
        UUID permDefUuid10 = UUID.randomUUID();
        g3PermissionMap.put(permDefUuid10.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid11.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid12.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid13.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid14.toString(), ActionKey.noAccess.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        HashMap<String, String> uadPermissionMap = new HashMap<>();
        uadPermissionMap.put(permDefUuid15.toString(), ActionKey.readWrite.toString());
        uadPermissionMap.put(permDefUuid16.toString(), ActionKey.readOnly.toString());
        uadPermissionMap.put(permDefUuid20.toString(), ActionKey.readWrite.toString());
        permissionMap.put("643602fa-a3f6-43e9-9b8b-31a6bddf4e4a", uadPermissionMap);

        HashMap<String, String> specialEventsPermissionMap = new HashMap<>();
        specialEventsPermissionMap.put(permDefUuid21.toString(), ActionKey.readWrite.toString());
        specialEventsPermissionMap.put(permDefUuid22.toString(), ActionKey.readOnly.toString());
        specialEventsPermissionMap.put(permDefUuid23.toString(), ActionKey.readWrite.toString());
        permissionMap.put("66c7b95b-5657-4b1c-87bc-f0d212208d5a", specialEventsPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(10, result.size());
        assertTrue(result.contains("pageCode=information-manager&access=readWrite&functions={alerts:readOnly}"));
        assertTrue(result.contains("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readOnly,pricingConfigurationTransientCeilingAndFloor:readOnly}"));
        assertTrue(result.contains("pageCode=investigator&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-config&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-product-list&access=readOnly"));
        assertFalse(result.contains("competitor-custom-room-mapping&access=noAccess"));
        assertTrue(result.contains("pageCode=monitoring-dashboard&access=readWrite"));
        assertTrue(result.contains("pageCode=user-management&access=readWrite&functions={user-management-activate-deactivate:readOnly}"));
        assertTrue(result.contains("pageCode=system-announcements&access=readWrite"));
        assertTrue(result.contains("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}"));
        assertTrue(result.contains("pageCode=special-event-upload&access=readWrite"));
    }

    @Test
    public void convertFDSPermissions_G3OptixAndNavigator() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Parent page with function
        UUID permDefUuid1 = UUID.randomUUID();
        PermissionDefinition permDef1 = PermissionDefinition
                .builder()
                .id(permDefUuid1)
                .pageCode("information-manager")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef1);

        //Child page
        UUID permDefUuid2 = UUID.randomUUID();
        PermissionDefinition permDef2 = PermissionDefinition
                .builder()
                .id(permDefUuid2)
                .pageCode("alerts")
                .isFunction(true)
                .parentId(permDefUuid1)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef2);

        //Parent page without functions
        UUID permDefUuid3 = UUID.randomUUID();
        PermissionDefinition permDef3 = PermissionDefinition
                .builder()
                .id(permDefUuid3)
                .pageCode("investigator")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.NO_ACCESS)
                .build();
        permissionDefinitions.add(permDef3);

        //Parent page with menu excluded pages
        UUID permDefUuid4 = UUID.randomUUID();
        PermissionDefinition permDef4 = PermissionDefinition
                .builder()
                .id(permDefUuid4)
                .pageCode("pricing-config")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef4);

        //Child page that is menu excluded
        UUID permDefUuid5 = UUID.randomUUID();
        PermissionDefinition permDef5 = PermissionDefinition
                .builder()
                .id(permDefUuid5)
                .pageCode("pricing-product-list")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef5);

        //Child page with functionalities and that is menu excluded
        UUID permDefUuid6 = UUID.randomUUID();
        PermissionDefinition permDef6 = PermissionDefinition
                .builder()
                .id(permDefUuid6)
                .pageCode("pricing-configuration")
                .isFunction(false)
                .parentId(permDefUuid4)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef6);

        //Child function page 1
        UUID permDefUuid7 = UUID.randomUUID();
        PermissionDefinition permDef7 = PermissionDefinition
                .builder()
                .id(permDefUuid7)
                .pageCode("pricingConfigurationDefinitionStep")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef7);

        //Child function page
        UUID permDefUuid8 = UUID.randomUUID();
        PermissionDefinition permDef8 = PermissionDefinition
                .builder()
                .id(permDefUuid8)
                .pageCode("pricingConfigurationTransientCeilingAndFloor")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef8);

        //Page without permission defined for user
        UUID permDefUuid9 = UUID.randomUUID();
        PermissionDefinition permDef9 = PermissionDefinition
                .builder()
                .id(permDefUuid9)
                .pageCode("competitor-custom-room-mapping")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef9);

        //Child function page - No Access
        UUID permDefUuid11 = UUID.randomUUID();
        PermissionDefinition permDef11 = PermissionDefinition
                .builder()
                .id(permDefUuid11)
                .pageCode("pricingConfigurationOffsets")
                .isFunction(true)
                .parentId(permDefUuid6)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef11);

        //Page with functionalities that are no access
        UUID permDefUuid12 = UUID.randomUUID();
        PermissionDefinition permDef12 = PermissionDefinition
                .builder()
                .id(permDefUuid12)
                .pageCode("monitoring-dashboard")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY_READ_WRITE)
                .build();
        permissionDefinitions.add(permDef12);

        //Child function page 1 - No Access
        UUID permDefUuid13 = UUID.randomUUID();
        PermissionDefinition permDef13 = PermissionDefinition
                .builder()
                .id(permDefUuid13)
                .pageCode("support.jobAction")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef13);

        //Child function page 2 - No Access
        UUID permDefUuid14 = UUID.randomUUID();
        PermissionDefinition permDef14 = PermissionDefinition
                .builder()
                .id(permDefUuid14)
                .pageCode("support.schedule.stop-all")
                .isFunction(true)
                .parentId(permDefUuid12)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef14);

        //Optix page
        UUID permDefUuid15 = UUID.randomUUID();
        PermissionDefinition permDef15 = PermissionDefinition
                .builder()
                .id(permDefUuid15)
                .pageCode("optix-focus-analysis")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef15);

        //Optix function
        UUID permDefUuid16 = UUID.randomUUID();
        PermissionDefinition permDef16 = PermissionDefinition
                .builder()
                .id(permDefUuid16)
                .pageCode("optix-edit-templates")
                .isFunction(true)
                .parentId(permDefUuid15)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef16);

        //Navigator page
        UUID permDefUuid17 = UUID.randomUUID();
        PermissionDefinition permDef17 = PermissionDefinition
                .builder()
                .id(permDefUuid17)
                .pageCode("portfolio-navigator-manage")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef17);

        //UAD page
        UUID permDefUuid18 = UUID.randomUUID();
        PermissionDefinition permDef18 = PermissionDefinition
                .builder()
                .id(permDefUuid18)
                .pageCode("user-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef18);

        //UAD function
        UUID permDefUuid19 = UUID.randomUUID();
        PermissionDefinition permDef19 = PermissionDefinition
                .builder()
                .id(permDefUuid19)
                .pageCode("user-management-activate-deactivate")
                .isFunction(true)
                .parentId(permDefUuid18)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef19);

        //UAD page with functions
        UUID permDefUuid20 = UUID.randomUUID();
        PermissionDefinition permDef20 = PermissionDefinition
                .builder()
                .id(permDefUuid20)
                .pageCode("system-announcements")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef20);

        //Special Events page with functions
        UUID permDefUuid21 = UUID.randomUUID();
        PermissionDefinition permDef21 = PermissionDefinition
                .builder()
                .id(permDefUuid21)
                .pageCode("special-events-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef21);

        //Special Events function
        UUID permDefUuid22 = UUID.randomUUID();
        PermissionDefinition permDef22 = PermissionDefinition
                .builder()
                .id(permDefUuid22)
                .pageCode("specialEventsAddNewDelete")
                .isFunction(true)
                .parentId(permDefUuid21)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef22);

        //Special Events page without functions
        UUID permDefUuid23 = UUID.randomUUID();
        PermissionDefinition permDef23 = PermissionDefinition
                .builder()
                .id(permDefUuid23)
                .pageCode("special-event-upload")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef23);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();
        //revPlan is just added for testing purposes and should be ignored during processing
        permissionMap.put("78622f84-7aac-4463-b43d-ea39f7163a9d", new HashMap<>());
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(permDefUuid1.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid2.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid3.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid4.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid5.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid6.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid7.toString(), ActionKey.readOnly.toString());
        g3PermissionMap.put(permDefUuid8.toString(), ActionKey.readOnly.toString());
        //permDefUuid9 is not added to test that no permissions is set in this case
        //permDefUuid10 is unknown UUID that doesn't match to page code; so we just ignore it
        UUID permDefUuid10 = UUID.randomUUID();
        g3PermissionMap.put(permDefUuid10.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid11.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid12.toString(), ActionKey.readWrite.toString());
        g3PermissionMap.put(permDefUuid13.toString(), ActionKey.noAccess.toString());
        g3PermissionMap.put(permDefUuid14.toString(), ActionKey.noAccess.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        HashMap<String, String> uadPermissionMap = new HashMap<>();
        uadPermissionMap.put(permDefUuid18.toString(), ActionKey.readWrite.toString());
        uadPermissionMap.put(permDefUuid19.toString(), ActionKey.readOnly.toString());
        uadPermissionMap.put(permDefUuid20.toString(), ActionKey.readWrite.toString());
        permissionMap.put("643602fa-a3f6-43e9-9b8b-31a6bddf4e4a", uadPermissionMap);

        HashMap<String, String> optixPermissionMap = new HashMap<>();
        optixPermissionMap.put(permDefUuid15.toString(), ActionKey.readWrite.toString());
        optixPermissionMap.put(permDefUuid16.toString(), ActionKey.readOnly.toString());
        permissionMap.put("1471fb4e-7a66-47e6-a325-0b2afe48123d", optixPermissionMap);

        HashMap<String, String> navigatorPermissionMap = new HashMap<>();
        navigatorPermissionMap.put(permDefUuid17.toString(), ActionKey.readOnly.toString());
        permissionMap.put("8f1c0f61-e4de-4f60-ad09-9e0a0b60ab8e", navigatorPermissionMap);

        HashMap<String, String> specialEventsPermissionMap = new HashMap<>();
        specialEventsPermissionMap.put(permDefUuid21.toString(), ActionKey.readWrite.toString());
        specialEventsPermissionMap.put(permDefUuid22.toString(), ActionKey.readOnly.toString());
        specialEventsPermissionMap.put(permDefUuid23.toString(), ActionKey.readWrite.toString());
        permissionMap.put("66c7b95b-5657-4b1c-87bc-f0d212208d5a", specialEventsPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(12, result.size());
        assertTrue(result.contains("pageCode=information-manager&access=readWrite&functions={alerts:readOnly}"));
        assertTrue(result.contains("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readOnly,pricingConfigurationTransientCeilingAndFloor:readOnly}"));
        assertTrue(result.contains("pageCode=investigator&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-config&access=readWrite"));
        assertTrue(result.contains("pageCode=pricing-product-list&access=readOnly"));
        assertFalse(result.contains("competitor-custom-room-mapping&access=noAccess"));
        assertTrue(result.contains("pageCode=monitoring-dashboard&access=readWrite"));
        assertTrue(result.contains("pageCode=optix-focus-analysis&access=readWrite&functions={optix-edit-templates:readOnly}"));
        assertTrue(result.contains("pageCode=portfolio-navigator-manage&access=readOnly"));
        assertTrue(result.contains("pageCode=user-management&access=readWrite&functions={user-management-activate-deactivate:readOnly}"));
        assertTrue(result.contains("pageCode=system-announcements&access=readWrite"));
        assertTrue(result.contains("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}"));
        assertTrue(result.contains("pageCode=special-event-upload&access=readWrite"));
    }

    @Test
    public void convertFDSPermissions_UADOnly() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //UAD page
        UUID permDefUuid18 = UUID.randomUUID();
        PermissionDefinition permDef18 = PermissionDefinition
                .builder()
                .id(permDefUuid18)
                .pageCode("user-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef18);

        //UAD function
        UUID permDefUuid19 = UUID.randomUUID();
        PermissionDefinition permDef19 = PermissionDefinition
                .builder()
                .id(permDefUuid19)
                .pageCode("user-management-activate-deactivate")
                .isFunction(true)
                .parentId(permDefUuid18)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef19);

        //UAD page with functions
        UUID permDefUuid20 = UUID.randomUUID();
        PermissionDefinition permDef20 = PermissionDefinition
                .builder()
                .id(permDefUuid20)
                .pageCode("system-announcements")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef20);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();

        HashMap<String, String> uadPermissionMap = new HashMap<>();
        uadPermissionMap.put(permDefUuid18.toString(), ActionKey.readWrite.toString());
        uadPermissionMap.put(permDefUuid19.toString(), ActionKey.readOnly.toString());
        uadPermissionMap.put(permDefUuid20.toString(), ActionKey.readWrite.toString());
        permissionMap.put("643602fa-a3f6-43e9-9b8b-31a6bddf4e4a", uadPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(2, result.size());
        assertTrue(result.contains("pageCode=user-management&access=readWrite&functions={user-management-activate-deactivate:readOnly}"));
        assertTrue(result.contains("pageCode=system-announcements&access=readWrite"));
    }

    @Test
    public void convertFDSPermissions_OptixOnly() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Optix page
        UUID permDefUuid15 = UUID.randomUUID();
        PermissionDefinition permDef15 = PermissionDefinition
                .builder()
                .id(permDefUuid15)
                .pageCode("optix-focus-analysis")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef15);

        //Optix function
        UUID permDefUuid16 = UUID.randomUUID();
        PermissionDefinition permDef16 = PermissionDefinition
                .builder()
                .id(permDefUuid16)
                .pageCode("optix-edit-templates")
                .isFunction(true)
                .parentId(permDefUuid15)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef16);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();

        HashMap<String, String> optixPermissionMap = new HashMap<>();
        optixPermissionMap.put(permDefUuid15.toString(), ActionKey.readWrite.toString());
        optixPermissionMap.put(permDefUuid16.toString(), ActionKey.readOnly.toString());
        permissionMap.put("1471fb4e-7a66-47e6-a325-0b2afe48123d", optixPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.contains("pageCode=optix-focus-analysis&access=readWrite&functions={optix-edit-templates:readOnly}"));
    }

    @Test
    public void convertFDSPermissions_NavigatorOnly() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Navigator page
        UUID permDefUuid17 = UUID.randomUUID();
        PermissionDefinition permDef17 = PermissionDefinition
                .builder()
                .id(permDefUuid17)
                .pageCode("portfolio-navigator-manage")
                .isFunction(false)
                .parentId(null)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef17);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> navigatorPermissionMap = new HashMap<>();
        navigatorPermissionMap.put(permDefUuid17.toString(), ActionKey.readOnly.toString());
        permissionMap.put("8f1c0f61-e4de-4f60-ad09-9e0a0b60ab8e", navigatorPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.contains("pageCode=portfolio-navigator-manage&access=readOnly"));
    }

    @Test
    public void convertFDSPermissions_SpecialEventsOnly() {
        List<PermissionDefinition> permissionDefinitions = new ArrayList<>();

        //Navigator page
        //Special Events page with functions
        UUID permDefUuid21 = UUID.randomUUID();
        PermissionDefinition permDef21 = PermissionDefinition
                .builder()
                .id(permDefUuid21)
                .pageCode("special-events-management")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef21);

        //Special Events function
        UUID permDefUuid22 = UUID.randomUUID();
        PermissionDefinition permDef22 = PermissionDefinition
                .builder()
                .id(permDefUuid22)
                .pageCode("specialEventsAddNewDelete")
                .isFunction(true)
                .parentId(permDefUuid21)
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_ONLY)
                .build();
        permissionDefinitions.add(permDef22);

        //Special Events page without functions
        UUID permDefUuid23 = UUID.randomUUID();
        PermissionDefinition permDef23 = PermissionDefinition
                .builder()
                .id(permDefUuid23)
                .pageCode("special-event-upload")
                .additionalProperties(new HashMap<>())
                .accessLevel(ActionKey.READ_WRITE)
                .build();
        permissionDefinitions.add(permDef23);

        when(uasService.getPermissionDefinitions()).thenReturn(permissionDefinitions);

        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> specialEventsPermissionMap = new HashMap<>();
        specialEventsPermissionMap.put(permDefUuid21.toString(), ActionKey.readWrite.toString());
        specialEventsPermissionMap.put(permDefUuid22.toString(), ActionKey.readOnly.toString());
        specialEventsPermissionMap.put(permDefUuid23.toString(), ActionKey.readWrite.toString());
        permissionMap.put("66c7b95b-5657-4b1c-87bc-f0d212208d5a", specialEventsPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(2, result.size());
        assertTrue(result.contains("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}"));
        assertTrue(result.contains("pageCode=special-event-upload&access=readWrite"));
    }

    @Test
    public void convertFDSPermissions_nothingDefined_G3Only() {
        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.get(0).equals(Role.NO_PERMISSIONS));
    }

    @Test
    public void convertFDSPermissions_nothingDefined_G3OptixAndNavigator() {
        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);
        HashMap<String, String> optixPermissionMap = new HashMap<>();
        permissionMap.put("1471fb4e-7a66-47e6-a325-0b2afe48123d", optixPermissionMap);
        HashMap<String, String> navigatorPermissionMap = new HashMap<>();
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", navigatorPermissionMap);
        HashMap<String, String> uadPermissionMap = new HashMap<>();
        permissionMap.put("643602fa-a3f6-43e9-9b8b-31a6bddf4e4a", uadPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.get(0).equals(Role.NO_PERMISSIONS));
    }

    @Test
    public void convertFDSPermissions_noPermissions() {
        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(Role.NO_PERMISSIONS, ActionKey.noAccess.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.get(0).equals(Role.NO_PERMISSIONS));
    }

    @Test
    public void convertFDSPermissions_allPermissions_G3Only() {
        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(Role.ALL_PERMISSIONS, ActionKey.readWrite.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.get(0).equals(Role.ALL_PERMS_ID));
    }

    @Test
    public void convertFDSPermissions_allPermissions_G3OptixAndNavigator() {
        HashMap<String, Object> permissionMap = new HashMap<>();
        HashMap<String, String> g3PermissionMap = new HashMap<>();
        g3PermissionMap.put(Role.ALL_PERMISSIONS, ActionKey.readWrite.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", g3PermissionMap);
        HashMap<String, String> optixPermissionMap = new HashMap<>();
        optixPermissionMap.put(Role.ALL_PERMISSIONS, ActionKey.readWrite.toString());
        permissionMap.put("1471fb4e-7a66-47e6-a325-0b2afe48123d", optixPermissionMap);
        HashMap<String, String> navigatorPermissionMap = new HashMap<>();
        navigatorPermissionMap.put(Role.ALL_PERMISSIONS, ActionKey.readWrite.toString());
        permissionMap.put("68622f84-7aac-4463-b43d-ea39f7163a9d", navigatorPermissionMap);

        List<String> result = roleService.convertFDSPermissions(permissionMap, false);
        assertEquals(1, result.size());
        assertTrue(result.get(0).equals(Role.ALL_PERMS_ID));
    }

    @Test
    public void processRoleDeleteFromFDS() {
        GlobalRole globalRole = createGlobalRole();
        UserIndividualPropertyRole userIndividualPropertyRole = createUserIndividualPropertyRole(globalRole);
        UserAuthGroupRole userAuthGroupRole = createUserAuthGroupRole(globalRole);

        roleService.setUserGlobalDBService(mockUserGlobalDBService);

        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(globalRole);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(eq(GlobalRole.BY_IDS), anyMap())).thenReturn(globalRole);
        when(mockGlobalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_ROLES_BY_ROLE_IDS, QueryParameter.with("roleIds", Arrays.asList(globalRole.getId().toString())).parameters())).thenReturn(Arrays.asList(userIndividualPropertyRole));
        when(mockGlobalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_BY_ROLE_IDS, QueryParameter.with("roleIds", Arrays.asList(globalRole.getId().toString())).parameters())).thenReturn(Arrays.asList(userAuthGroupRole));
        roleService.processRoleDeleteFromFDS(globalRole.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService).delete(Arrays.asList(userIndividualPropertyRole));
        verify(mockGlobalCrudService).delete(Arrays.asList(userAuthGroupRole));
        verify(mockGlobalCrudService).delete(globalRole);
    }

    @Test
    public void processRoleDeleteFromFDS_RoleDoesntExist() {
        GlobalRole globalRole = createGlobalRole();

        when(mockGlobalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", globalRole.getUasRoleDictionaryUuid()).parameters())).thenReturn(null);

        roleService.processRoleDeleteFromFDS(globalRole.getUasRoleDictionaryUuid());
        verify(mockGlobalCrudService, never()).delete(any(UserIndividualPropertyRole.class));
        verify(mockGlobalCrudService, never()).delete(any(UserAuthGroupRole.class));
        verify(mockGlobalCrudService, never()).delete(any(GlobalRole.class));
    }

    @Test
    public void getRoleByUasRoleDictionaryUuid() {
        roleService.getRoleByUasRoleDictionaryUuid(ROLE_ID);
        verify(mockGlobalCrudService, times(1)).findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID, QueryParameter.with("roleId", ROLE_ID).parameters());
    }

    @Test
    public void migrateAllRolesForClientToFDS() {
        roleService.migrateAllRolesForClientToFDS(CLIENT);
        verify(uasService).migrateRolesToFDSForClientCode(CLIENT);
    }

    @Test
    public void shouldUpdateCorporateAccessForUsersWhenCorporateAccessForRoleIsUpdated() {
        GlobalRole globalRole = createGlobalRole();
        when(mockGlobalCrudService.find(GlobalRole.class, globalRole.getId())).thenReturn(globalRole);

        roleService.globalCrudService = mockGlobalCrudService;
        when(mockUserService.update(anyString(), any(LDAPUser.class))).thenAnswer(invocationOnMock -> {
            LDAPUser user = invocationOnMock.getArgument(1);
            user.setIsCorporate(true);
            return null;
        });
        roleService.setUserService(mockUserService);
        HashSet<LDAPUser> ldapUsers = new HashSet<>(setupUsers(globalRole.getId().toString()));
        when(mockUserGlobalDBService.listUsersForRole(globalRole.getId().toString())).thenReturn(ldapUsers);
        roleService.setUserGlobalDBService(mockUserGlobalDBService);


        Role role = globalRole.toRole();
        globalRole.setCorporate(true);
        roleService.update(role.getUniqueIdentifier(), role);

        verify(uasService, never()).saveRoleInFDS(any());
        for (LDAPUser user : ldapUsers) {
            assertTrue(user.getIsCorporate());
        }
    }

    @Test
    public void shouldNotUpdateCorporateAccessForUsersWhenCorporateAccessForRoleIsNotUpdated() {
        GlobalRole globalRole = createGlobalRole();
        when(mockGlobalCrudService.find(GlobalRole.class, globalRole.getId())).thenReturn(globalRole);

        roleService.globalCrudService = mockGlobalCrudService;
        HashSet<LDAPUser> ldapUsers = new HashSet<>(setupUsers(globalRole.getId().toString()));
        when(mockUserGlobalDBService.listUsersForRole(globalRole.getId().toString())).thenReturn(ldapUsers);
        roleService.setUserGlobalDBService(mockUserGlobalDBService);


        Role role = globalRole.toRole();
        roleService.update(role.getUniqueIdentifier(), role);

        verify(uasService, never()).saveRoleInFDS(any());
        for (LDAPUser user : ldapUsers) {
            assertFalse(user.getIsCorporate());
        }
    }

    private GlobalRole createGlobalRole() {
        GlobalRole globalRole = new GlobalRole();
        globalRole.setId(123);
        globalRole.setClientCode(CLIENT);
        globalRole.setRoleName(TEST_ROLE);
        globalRole.setPermissions("permissions");
        globalRole.setViewAnnouncements(false);
        globalRole.setCorporate(false);
        globalRole.setDescription("older description");
        globalRole.setUasRoleDictionaryUuid(UUID.randomUUID().toString());
        return globalRole;
    }

    private UserAuthGroupRole createUserAuthGroupRole(GlobalRole globalRole) {
        UserAuthGroupRole userAuthGroupRole = new UserAuthGroupRole();
        userAuthGroupRole.setRoleId(globalRole.getId().toString());
        return userAuthGroupRole;
    }

    private UserIndividualPropertyRole createUserIndividualPropertyRole(GlobalRole globalRole) {
        UserIndividualPropertyRole userIndividualPropertyRole = new UserIndividualPropertyRole();
        userIndividualPropertyRole.setRoleId(globalRole.getId().toString());
        return userIndividualPropertyRole;
    }

    public List<UserAuthGroupRole> getAuthGroupRoles() {
        UserAuthGroupRole userAuthGroupRole = new UserAuthGroupRole();
        userAuthGroupRole.setId(1);
        userAuthGroupRole.setAuthGroupId(11);
        userAuthGroupRole.setRoleId("1");
        userAuthGroupRole.setGlobalUser(getGlobalUser());
        return Collections.singletonList(userAuthGroupRole);
    }

    public List<UserIndividualPropertyRole> getIndividualPropertyRoles() {
        UserIndividualPropertyRole individualPropertyRoles = new UserIndividualPropertyRole();
        individualPropertyRoles.setId(2);
        individualPropertyRoles.setPropertyId(22);
        individualPropertyRoles.setRoleId("2");
        individualPropertyRoles.setGlobalUser(getGlobalUser());
        return Collections.singletonList(individualPropertyRoles);
    }

    public GlobalUser getGlobalUser() {
        GlobalUser globalUser = new GlobalUser();
        globalUser.setId(1);
        return globalUser;
    }

    public static Role createRole(String name, String roleId) {
        Role role = new Role();
        role.setUniqueIdentifier("123");
        role.setRoleName(name);
        role.setClientCode(CLIENT);
        role.setDescription("TestRoleDescription");
        role.setCorporate(true);
        role.setViewAnnouncements(false);
        role.setRoleRanking(1);
        role.setPermissions(new ArrayList<>());
        role.setUasRoleDictionaryUuid(roleId);
        return role;
    }
}
