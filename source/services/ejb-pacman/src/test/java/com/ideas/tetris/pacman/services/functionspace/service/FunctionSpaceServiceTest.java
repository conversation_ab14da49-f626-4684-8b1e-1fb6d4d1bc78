package com.ideas.tetris.pacman.services.functionspace.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.expose.external.ExternalConfigurationService;
import com.ideas.tetris.pacman.services.expose.external.IntegrationType;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBooking;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingGuestRoom;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingGuestRoomPace;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingPace;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingRevenue;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingSubBlockCode;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceEvent;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceEventRevenue;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceBookingType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceEventType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoomType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceGuestRoomCategory;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceMarketSegment;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceStatus;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FunctionSpaceServiceTest extends AbstractG3JupiterTest {
    private FunctionSpaceService service;

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private ExternalConfigurationService externalConfigurationService;

    @Mock
    private JobServiceLocal jobService;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        service = new FunctionSpaceService();
        service.tenantCrudService = tenantCrudService();
    }

    @Test
    public void getFunctionSpaceEventsAndDelete() throws Exception {
        List<FunctionSpaceEvent> events = service.getFunctionSpaceEvents();
        assertTrue(events.isEmpty());

        // create an event
        FunctionSpaceEvent event = FunctionSpaceObjectMother.buildFunctionSpaceEvent();
        event.setFunctionSpaceFunctionRoom(createRoom());
        event.setFunctionSpaceStatus(createFunctionSpaceStatus());
        event.setFunctionSpaceEventType(createFunctionSpaceEventType());
        tenantCrudService().save(event);
        tenantCrudService().flushAndClear();

        // find event
        events = service.getFunctionSpaceEvents();
        assertFalse(events.isEmpty());
        assertTrue(events.size() == 1);

        // delete event
        service.deleteFunctionSpaceEvents();

        // verify it
        events = service.getFunctionSpaceEvents();
        assertTrue(events.isEmpty());
    }

    @Test
    public void getFunctionSpaceEventTypesAndDelete() throws Exception {
        List<FunctionSpaceEventType> eventTypes = service.getFunctionSpaceEventTypes();
        assertTrue(eventTypes.isEmpty());

        // create an event type
        FunctionSpaceEventType eventType = FunctionSpaceObjectMother.buildFunctionSpaceEventType("CRP", "Goldy");
        tenantCrudService().save(eventType);
        tenantCrudService().flushAndClear();

        // find event type
        eventTypes = service.getFunctionSpaceEventTypes();
        assertFalse(eventTypes.isEmpty());
        assertTrue(eventTypes.size() == 1);

        // delete event type
        service.deleteFunctionSpaceEventTypes();

        // verify it
        eventTypes = service.getFunctionSpaceEventTypes();
        assertTrue(eventTypes.isEmpty());
    }

    @Test
    public void getFunctionSpaceMarketSegmentsAndDelete() throws Exception {
        List<FunctionSpaceMarketSegment> mktSegments = service.getFunctionSpaceMarketSegments();
        assertTrue(mktSegments.isEmpty());

        // create a market segment
        FunctionSpaceMarketSegment mktSegment = FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("New Market Segment", 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID);
        tenantCrudService().save(mktSegment);
        tenantCrudService().flushAndClear();

        // find market segment
        mktSegments = service.getFunctionSpaceMarketSegments();
        assertFalse(mktSegments.isEmpty());
        assertTrue(mktSegments.size() == 1);

        // delete market segment
        service.deleteFunctionSpaceMarketSegments();

        // verify it
        mktSegments = service.getFunctionSpaceMarketSegments();
        assertTrue(mktSegments.isEmpty());
    }

    @Test
    public void getFunctionSpaceFunctionRoomsAndDelete() throws Exception {
        List<FunctionSpaceFunctionRoom> rooms = service.getFunctionSpaceFunctionRooms();
        assertTrue(rooms.isEmpty());

        // create a room
        createRoom();
        tenantCrudService().flushAndClear();

        // find room
        rooms = service.getFunctionSpaceFunctionRooms();
        assertFalse(rooms.isEmpty());
        assertTrue(rooms.size() == 1);

        // delete room
        service.deleteFunctionSpaceFunctionRooms();

        // verify it
        rooms = service.getFunctionSpaceFunctionRooms();
        assertTrue(rooms.isEmpty());
    }

    @Test
    public void getFunctionSpaceBookingsAndDelete() throws Exception {
        List<FunctionSpaceBooking> bookings = service.getFunctionSpaceBookings();
        assertTrue(bookings.isEmpty());

        // create a booking
        FunctionSpaceBooking booking = FunctionSpaceObjectMother.buildFunctionSpaceBooking(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, null, "sc12345");
        booking.setBookingType(createBookingType());
        booking.setMarketSegment(createMktSegment());
        booking.setFunctionSpaceStatus(createFunctionSpaceStatus());
        tenantCrudService().save(booking);
        tenantCrudService().flushAndClear();

        // find booking
        bookings = service.getFunctionSpaceBookings();
        assertFalse(bookings.isEmpty());
        assertTrue(bookings.size() == 1);

        // delete booking
        service.deleteFunctionSpaceBookings();

        // verify it
        bookings = service.getFunctionSpaceBookings();
        assertTrue(bookings.isEmpty());
    }

    @Test
    public void getFunctionSpaceBookingRevenuesAndDelete() throws Exception {
        List<FunctionSpaceBookingRevenue> bookingRevenues = service.getFunctionSpaceBookingRevenues();
        assertTrue(bookingRevenues.isEmpty());

        // create a booking
        FunctionSpaceBooking booking = FunctionSpaceObjectMother.buildFunctionSpaceBooking(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, null, "sc12345");
        booking.setBookingType(createBookingType());
        booking.setMarketSegment(createMktSegment());
        booking.setFunctionSpaceStatus(createFunctionSpaceStatus());
        tenantCrudService().save(booking);
        tenantCrudService().flushAndClear();

        // create a booking revenue
        FunctionSpaceBookingRevenue bookingRevenue = FunctionSpaceObjectMother.buildFunctionSpaceBookingRevenue();
        bookingRevenue.setFunctionSpaceRevenueType(createRevenueType());
        bookingRevenue.setBookingId(booking.getId());
        tenantCrudService().save(bookingRevenue);
        tenantCrudService().flushAndClear();

        // find booking
        bookingRevenues = service.getFunctionSpaceBookingRevenues();
        assertEquals(1, bookingRevenues.size());

        // delete booking
        service.deleteFunctionSpaceBookingRevenues();

        // verify it
        bookingRevenues = service.getFunctionSpaceBookingRevenues();
        assertTrue(bookingRevenues.isEmpty());
    }

    @Test
    void getFunctionSpaceStatusesToConsiderForTentativeGroupForecasting() {
        tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, "Final-NonDeduct", true, false, false));

        tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, "Final-Deduct", true, true, false));

        tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, "Tentative", false, true, false));

        tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, "Invalid", false, true, true));

        tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, "Invalid2", true, true, true));

        tenantCrudService().flushAndClear();

        List<Integer> statuses = service.getFunctionSpaceStatusesToConsiderForTentativeGroupForecasting();

        assertEquals(2, statuses.size());
    }

    @Test
    void getFunctionSpaceStatuses() {
        inject(service, "tenantCrudService", tenantCrudService);
        Mockito.when(tenantCrudService.findByNamedQuery(eq(FunctionSpaceStatus.FIND_ALL_BY_STATUS_CODES_AND_INVENTORY_DEDUCTED), ArgumentMatchers.anyMap()))
                .thenReturn(List.of(FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID)));

        List<FunctionSpaceStatus> functionSpaceStatuses = service.getFunctionSpaceStatuses(List.of("Tentative", "Definite"), false);
        assertEquals(1, functionSpaceStatuses.size());
        verify(tenantCrudService, times(1))
                .findByNamedQuery(eq(FunctionSpaceStatus.FIND_ALL_BY_STATUS_CODES_AND_INVENTORY_DEDUCTED), ArgumentMatchers.anyMap());
    }

    @Test
    void updateFSCfgStatusAsInventoryDeductibleForHiltonForEmptyStatusList() {
        inject(service, "tenantCrudService", tenantCrudService);
        service.updateFSCfgStatusAsInventoryDeductibleForHilton(Collections.emptyList());
        verify(tenantCrudService, never()).save(Collections.emptyList());
    }

    @Test
    void updateFSCfgStatusAsInventoryDeductibleForHilton() {
        FunctionSpaceStatus functionSpaceStatus = createFunctionSpaceStatus();
        List<FunctionSpaceStatus> functionSpaceStatusList = List.of(functionSpaceStatus);
        int prevCount = tenantCrudService().findAll(FunctionSpaceStatus.class).size();

        service.updateFSCfgStatusAsInventoryDeductibleForHilton(functionSpaceStatusList);
        assertEquals(prevCount, tenantCrudService().findAll(FunctionSpaceStatus.class).size());
    }

    @Test
    void shouldDeleteFSMarketSegmentAndRelatedData() {
        FunctionSpaceStatus functionSpaceStatus = createFunctionSpaceStatus();
        FunctionSpaceRevenueType revenueType = createRevenueType();
        FunctionSpaceBooking booking = FunctionSpaceObjectMother.buildFunctionSpaceBooking(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, null, "sc12345");
        booking.setBookingType(createBookingType());
        booking.setMarketSegment(createMktSegment());
        booking.setFunctionSpaceStatus(functionSpaceStatus);
        tenantCrudService().save(booking);
        FunctionSpaceBookingSubBlockCode functionSpaceBookingSubBlockCode = new FunctionSpaceBookingSubBlockCode();
        functionSpaceBookingSubBlockCode.setId(1);
        functionSpaceBookingSubBlockCode.setSubBlockCode("SubBlockCode");
        functionSpaceBookingSubBlockCode.setFunctionSpaceBooking(booking);
        tenantCrudService().save(functionSpaceBookingSubBlockCode);
        FunctionSpaceBookingRevenue bookingRevenue = FunctionSpaceObjectMother.buildFunctionSpaceBookingRevenue();
        bookingRevenue.setFunctionSpaceRevenueType(revenueType);
        bookingRevenue.setBookingId(booking.getId());
        tenantCrudService().save(bookingRevenue);
        FunctionSpaceBookingPace functionSpaceBookingPace = FunctionSpaceObjectMother.buildFunctionSpaceBookingPace();
        functionSpaceBookingPace.setFunctionSpaceBookingId(booking.getId());
        tenantCrudService().save(functionSpaceBookingPace);
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = FunctionSpaceObjectMother.buildFunctioSpaceGuestRoomCategory();
        tenantCrudService().save(functionSpaceGuestRoomCategory);
        FunctionSpaceBookingGuestRoom functionSpaceBookingGuestRoom = FunctionSpaceObjectMother.buildFunctionSpaceBookingGuestRoom();
        functionSpaceBookingGuestRoom.setFunctionSpaceBookingId(booking.getId());
        functionSpaceBookingGuestRoom.setFunctionSpaceStatus(functionSpaceStatus);
        functionSpaceBookingGuestRoom.setFunctionSpaceGuestRoomCategory(functionSpaceGuestRoomCategory);
        tenantCrudService().save(functionSpaceBookingGuestRoom);
        FunctionSpaceBookingGuestRoomPace functionSpaceBookingGuestRoomPace = new FunctionSpaceBookingGuestRoomPace();
        functionSpaceBookingGuestRoomPace.setFunctionSpaceBookingId(booking.getId());
        functionSpaceBookingGuestRoomPace.setFunctionSpaceBookingGuestRoomID(functionSpaceBookingGuestRoom.getId());
        functionSpaceBookingGuestRoomPace.setFunctionSpaceGuestRoomCategory(functionSpaceGuestRoomCategory);
        functionSpaceBookingGuestRoomPace.setFunctionSpaceStatus(functionSpaceStatus);
        functionSpaceBookingGuestRoomPace.setGuestRoomPaceIDNGI("NGI_ID");
        functionSpaceBookingGuestRoomPace.setOccupancyDate(LocalDate.now());
        tenantCrudService().save(functionSpaceBookingGuestRoomPace);
        FunctionSpaceEvent event = FunctionSpaceObjectMother.buildFunctionSpaceEvent();
        event.setFunctionSpaceFunctionRoom(createRoom());
        event.setFunctionSpaceStatus(functionSpaceStatus);
        event.setFunctionSpaceEventType(createFunctionSpaceEventType());
        event.setFunctionSpaceBookingId(booking.getId());
        tenantCrudService().save(event);
        FunctionSpaceEventRevenue functionSpaceEventRevenue = FunctionSpaceObjectMother.buildFunctionSpaceEventRevenue();
        functionSpaceEventRevenue.setEventId(event.getId());
        functionSpaceEventRevenue.setFunctionSpaceRevenueType(revenueType);
        tenantCrudService().save(functionSpaceEventRevenue);
        tenantCrudService().flushAndClear();

        service.deleteFSMarketSegmentAndRelatedData();

        assertTrue(tenantCrudService().findAll(FunctionSpaceMarketSegment.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingPace.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingRevenue.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingSubBlockCode.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingGuestRoom.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingGuestRoomPace.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceEvent.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceEventRevenue.class).isEmpty());

    }

    @Test
    void shouldTriggerFSDataLoadJob() {
        inject(service, "externalConfigurationService", externalConfigurationService);
        inject(service, "jobService", jobService);
        // given:
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID);
        parameters.put(JobParameterKey.INTEGRATION_TYPE, IntegrationType.FUNCTION_SPACE_CLIENT.name());
        parameters.put(JobParameterKey.SENDING_SYSTEM_PROPERTY_ID, String.valueOf(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));
        parameters.put(JobParameterKey.NO_OF_YEARS, Integer.valueOf(4));

        withConfigServiceProperty(IntegrationType.FUNCTION_SPACE_CLIENT, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID);

        when(jobService.startGuaranteedNewInstance(Mockito.eq(JobName.FunctionSpaceDataLoadJob), Mockito.anyMapOf(String.class, Object.class))).thenReturn(44L);

        // when:
        service.triggerFSDataLoadJob(IntegrationType.FUNCTION_SPACE_CLIENT.name(), String.valueOf(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID), 4);

        // then:
        verify(externalConfigurationService).getProperties(IntegrationType.FUNCTION_SPACE_CLIENT.name(), String.valueOf(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));
        verify(jobService).startGuaranteedNewInstance(Mockito.eq(JobName.FunctionSpaceDataLoadJob), Mockito.eq(parameters));
    }

    private FunctionSpaceServiceTest withConfigServiceProperty(IntegrationType integrationType, int propertyId) {
        List<Property> properties = new ArrayList<>();
        Property property = new Property();
        property.setId(propertyId);
        property.setClient(new Client());
        properties.add(property);
        when(externalConfigurationService.getProperties(integrationType.name(), String.valueOf(propertyId))).thenReturn(properties);
        return this;
    }

    private FunctionSpaceRevenueType createRevenueType() {
        FunctionSpaceRevenueGroup group = createRevenueGroup();
        return tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceRevenueType(group, group.getName(), null));
    }

    private FunctionSpaceRevenueGroup createRevenueGroup() {
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup("GR", 1);
        functionSpaceRevenueGroup.setResourceType(createResourceType());
        return tenantCrudService().save(functionSpaceRevenueGroup);
    }

    private FunctionSpaceResourceType createResourceType() {
        return tenantCrudService().save(FunctionSpaceObjectMother.buildFunctionSpaceResourceType("OTHER", "Other", false, true));
    }

    private FunctionSpaceMarketSegment createMktSegment() {
        return tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("New Market Segment", 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));
    }

    private FunctionSpaceBookingType createBookingType() {
        return tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceBookingType(FunctionSpaceObjectMother.DEFAULT_ABBREVIATION, 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));
    }

    private FunctionSpaceStatus createFunctionSpaceStatus() {
        return tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceStatus(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));
    }

    private FunctionSpaceEventType createFunctionSpaceEventType() {
        return tenantCrudService().save(FunctionSpaceObjectMother.buildFunctionSpaceEventType("Abbrev", "Name"));
    }

    private FunctionSpaceFunctionRoom createRoom() {
        FunctionSpaceFunctionRoomType roomType = tenantCrudService().save(
                FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoomType());
        FunctionSpaceFunctionRoom room = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        room.setFunctionSpaceFunctionRoomType(roomType);
        tenantCrudService().save(room);
        return room;
    }

    @Test
    void shouldDeleteFunctionSpaceBookingData() {
        createAndGetFunctionSpaceBooking();
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
    }

    @Test
    void shouldDeleteFunctionSpaceBookingPaceData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        createFunctionSpaceBookingPace(booking);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingPace.class).isEmpty());
    }

    private void createFunctionSpaceBookingPace(FunctionSpaceBooking booking) {
        FunctionSpaceBookingPace functionSpaceBookingPace = FunctionSpaceObjectMother.buildFunctionSpaceBookingPace();
        functionSpaceBookingPace.setFunctionSpaceBookingId(booking.getId());
        tenantCrudService().save(functionSpaceBookingPace);
        tenantCrudService().flushAndClear();
    }

    @Test
    void shouldDeleteFunctionSpaceBookingRevenueData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        createFunctionSpaceBookingRevenue(booking);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingRevenue.class).isEmpty());
    }

    private void createFunctionSpaceBookingRevenue(FunctionSpaceBooking booking) {
        FunctionSpaceRevenueType revenueType = createRevenueType();
        FunctionSpaceBookingRevenue bookingRevenue = FunctionSpaceObjectMother.buildFunctionSpaceBookingRevenue();
        bookingRevenue.setFunctionSpaceRevenueType(revenueType);
        bookingRevenue.setBookingId(booking.getId());
        tenantCrudService().save(bookingRevenue);
        tenantCrudService().flushAndClear();
    }

    @Test
    void shouldDeleteFunctionSpaceBookingSubBlockCodeData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        createFunctionSpaceBookingSubBlockCode(booking);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingSubBlockCode.class).isEmpty());
    }

    private void createFunctionSpaceBookingSubBlockCode(FunctionSpaceBooking booking) {
        FunctionSpaceBookingSubBlockCode functionSpaceBookingSubBlockCode = new FunctionSpaceBookingSubBlockCode();
        functionSpaceBookingSubBlockCode.setId(1);
        functionSpaceBookingSubBlockCode.setSubBlockCode("SubBlockCode");
        functionSpaceBookingSubBlockCode.setFunctionSpaceBooking(booking);
        tenantCrudService().save(functionSpaceBookingSubBlockCode);
        tenantCrudService().flushAndClear();
    }

    @Test
    void shouldDeleteFunctionSpaceBookingGuestRoomData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        createFunctionSpaceBookingGuestRoom(booking);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingGuestRoom.class).isEmpty());
    }

    private FunctionSpaceBookingGuestRoom createFunctionSpaceBookingGuestRoom(FunctionSpaceBooking booking) {
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = FunctionSpaceObjectMother.buildFunctioSpaceGuestRoomCategory();
        tenantCrudService().save(functionSpaceGuestRoomCategory);
        FunctionSpaceBookingGuestRoom functionSpaceBookingGuestRoom = FunctionSpaceObjectMother.buildFunctionSpaceBookingGuestRoom();
        functionSpaceBookingGuestRoom.setFunctionSpaceBookingId(booking.getId());
        functionSpaceBookingGuestRoom.setFunctionSpaceStatus(booking.getFunctionSpaceStatus());
        functionSpaceBookingGuestRoom.setFunctionSpaceGuestRoomCategory(functionSpaceGuestRoomCategory);
        tenantCrudService().save(functionSpaceBookingGuestRoom);
        tenantCrudService().flushAndClear();
        return functionSpaceBookingGuestRoom;
    }

    @Test
    void shouldDeleteFunctionSpaceBookingGuestRoomPace() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        FunctionSpaceBookingGuestRoom functionSpaceBookingGuestRoom = createFunctionSpaceBookingGuestRoom(booking);
        createFunctionSpaceGuestRoomPace(booking, functionSpaceBookingGuestRoom);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingGuestRoom.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceBookingGuestRoomPace.class).isEmpty());
    }

    private void createFunctionSpaceGuestRoomPace(FunctionSpaceBooking booking, FunctionSpaceBookingGuestRoom functionSpaceBookingGuestRoom) {
        FunctionSpaceBookingGuestRoomPace functionSpaceBookingGuestRoomPace = new FunctionSpaceBookingGuestRoomPace();
        functionSpaceBookingGuestRoomPace.setFunctionSpaceBookingId(booking.getId());
        functionSpaceBookingGuestRoomPace.setFunctionSpaceBookingGuestRoomID(functionSpaceBookingGuestRoom.getId());
        functionSpaceBookingGuestRoomPace.setFunctionSpaceGuestRoomCategory(functionSpaceBookingGuestRoom.getFunctionSpaceGuestRoomCategory());
        functionSpaceBookingGuestRoomPace.setFunctionSpaceStatus(booking.getFunctionSpaceStatus());
        functionSpaceBookingGuestRoomPace.setGuestRoomPaceIDNGI("NGI_ID");
        functionSpaceBookingGuestRoomPace.setOccupancyDate(LocalDate.now());
        tenantCrudService().save(functionSpaceBookingGuestRoomPace);
        tenantCrudService().flushAndClear();
    }

    @Test
    void shouldDeleteFunctionSpaceEventData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        createAndGetFunctionSpaceEvent(booking);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceEvent.class).isEmpty());
    }

    private FunctionSpaceEvent createAndGetFunctionSpaceEvent(FunctionSpaceBooking booking) {
        FunctionSpaceEvent event = FunctionSpaceObjectMother.buildFunctionSpaceEvent();
        event.setFunctionSpaceFunctionRoom(createRoom());
        event.setFunctionSpaceStatus(booking.getFunctionSpaceStatus());
        event.setFunctionSpaceEventType(createFunctionSpaceEventType());
        event.setFunctionSpaceBookingId(booking.getId());
        tenantCrudService().save(event);
        tenantCrudService().flushAndClear();
        return event;
    }

    @Test
    void shouldDeleteFunctionSpaceEventRevenueData() {
        FunctionSpaceBooking booking = createAndGetFunctionSpaceBooking();
        FunctionSpaceEvent event = createAndGetFunctionSpaceEvent(booking);
        createFunctionSpaceEventRevenue(event);
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findAll(FunctionSpaceBooking.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceEvent.class).isEmpty());
        assertTrue(tenantCrudService().findAll(FunctionSpaceEventRevenue.class).isEmpty());
    }

    private void createFunctionSpaceEventRevenue(FunctionSpaceEvent event) {
        FunctionSpaceEventRevenue functionSpaceEventRevenue = FunctionSpaceObjectMother.buildFunctionSpaceEventRevenue();
        functionSpaceEventRevenue.setEventId(event.getId());
        functionSpaceEventRevenue.setFunctionSpaceRevenueType(createRevenueType());
        tenantCrudService().save(functionSpaceEventRevenue);
        tenantCrudService().flushAndClear();
    }

    private FunctionSpaceBooking createAndGetFunctionSpaceBooking() {
        FunctionSpaceStatus functionSpaceStatus = createFunctionSpaceStatus();
        FunctionSpaceBooking booking = FunctionSpaceObjectMother.buildFunctionSpaceBooking(FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID, null, "sc12345");
        booking.setBookingType(createBookingType());
        booking.setMarketSegment(createMktSegment());
        booking.setFunctionSpaceStatus(functionSpaceStatus);
        tenantCrudService().save(booking);
        return booking;
    }

    @Test
    public void shouldDeleteBookingGuestRoomNGIPaceData() {
        tenantCrudService().executeUpdateByNativeQuery("set IDENTITY_INSERT Fs_Booking_Guest_Room_NGI_Pace ON;" +
                "insert into Fs_Booking_Guest_Room_NGI_Pace (Fs_Booking_Guest_Room_NGI_Pace_ID,BookingId,RoomType,GuestRoomStatus,BookingPaceId,StayDate,ChangeDate,RoomNights,ContractedRoomNights,GuestRoomRateSingle,GuestRoomRateDouble,GuestRoomRateTriple,GuestRoomRateQuad,ContractedSingleRooms,ContractedDoubleRooms,ContractedTripleRooms,ContractedQuadRooms,BlockedSingleRooms,BlockedDoubleRooms,BlockedTripleRooms,BlockedQuadRooms,ForecastedRoomsTotal,PickupSingleRooms,PickupDoubleRooms,PickupTripleRooms,PickupQuadRooms) \n" +
                "values (1,1,'test', 'active',1,getdate(),getdate(),1,1,10,10,10,10,11,11,11,11,12,12,12,12,100,13,13,13,13)"
                + "set IDENTITY_INSERT Fs_Booking_Guest_Room_NGI_Pace OFF");
        tenantCrudService().flushAndClear();
        service.deleteFunctionSpaceRevenuesAndRelatedData();
        assertTrue(tenantCrudService().findByNativeQuery("Select * from Fs_Booking_Guest_Room_NGI_Pace").isEmpty());
    }

}