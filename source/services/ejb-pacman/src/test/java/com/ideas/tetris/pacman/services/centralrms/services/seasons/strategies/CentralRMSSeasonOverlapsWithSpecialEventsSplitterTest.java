package com.ideas.tetris.pacman.services.centralrms.services.seasons.strategies;

import com.ideas.tetris.pacman.services.centralrms.adapters.SeasonsServiceAdapter;
import com.ideas.tetris.pacman.services.centralrms.models.pricing.CrudWrapper;
import com.ideas.tetris.pacman.services.centralrms.models.pricing.SplitResultWithIgnored;
import com.ideas.tetris.pacman.services.centralrms.services.seasons.CentralRMSSeason;
import com.ideas.tetris.pacman.services.centralrms.services.seasons.core.SpecialEventSeasonSplitter;
import com.ideas.tetris.pacman.util.Season;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CentralRMSSeasonOverlapsWithSpecialEventsSplitterTest {

    @Mock
    private CentralRMSSeasonSplitterCore centralRMSSeasonSplitterCore;

    @Mock
    private SeasonsServiceAdapter seasonsServiceAdapter;

    @Mock
    private SpecialEventSeasonSplitter specialEventSeasonSplitter;

    @InjectMocks
    private CentralRMSSeasonOverlapsWithSpecialEventsSplitter splitter;

    @Test
    void split() {
        // Kind of a lengthy setup, so going to summarize here
        // We're going to split [2023-01-01, 2023-02-28] with
        //  1. Special events that spans
        //     a. [2023-01-01, 2023-01-31]
        //     b. [2023-02-05, 2023-02-10]
        //  2. Three existing seasons
        //     a. [2022-12-25, 2023-01-07]
        //     b. [2023-01-15, 2023-01-21]
        //     c. [2023-01-29, 2023-02-04]
        //  3. A System date of 2022-12-27
        //
        // This should ultimately result in the following
        //  1. Create := [[2022-12-25, 2022-12-26], [2022-12-27, 2022-12-31], [2023-02-01, 2023-02-04]]
        //  2. Update := every week of January + [2023-02-05, 2023-02-10]
        //  3. Delete := [[2022-12-25, 2023-01-07], [2023-01-29, 2023-02-04]]

        Integer defaultMetadata = 0;

        CentralRMSSeason<Integer> seasonA = new CentralRMSSeason<>(1, 1, "A", LocalDateUtils.toJodaLocalDate(LocalDate.of(2022, 12, 25)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 7)));
        CentralRMSSeason<Integer> seasonAPast = new CentralRMSSeason<>(2, null, "APast", LocalDateUtils.toJodaLocalDate(LocalDate.of(2022, 12, 25)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 12, 26)));
        CentralRMSSeason<Integer> seasonACurrentAndFuture = new CentralRMSSeason<>(2, null, "ACurrentAndFuture", LocalDateUtils.toJodaLocalDate(LocalDate.of(2022, 12, 27)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 7)));
        CentralRMSSeason<Integer> seasonA1 = new CentralRMSSeason<>(3, null, "A1", LocalDateUtils.toJodaLocalDate(LocalDate.of(2022, 12, 27)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2022, 12, 31)));
        CentralRMSSeason<Integer> seasonA2 = new CentralRMSSeason<>(3, null, "A2", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 1)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 7)));

        CentralRMSSeason<Integer> seasonB = new CentralRMSSeason<>(4, 2, "B", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 15)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 21)));

        CentralRMSSeason<Integer> seasonC = new CentralRMSSeason<>(5, 3, "C", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 29)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 2, 4)));
        CentralRMSSeason<Integer> seasonC1 = new CentralRMSSeason<>(6, null, "C1", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 29)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 1, 31)));
        CentralRMSSeason<Integer> seasonC2 = new CentralRMSSeason<>(7, null, "C2", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 2, 2)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 2, 4)));

        List<Integer> defaultConfigs = List.of(defaultMetadata);
        List<CentralRMSSeason<Integer>> seasonConfigs = List.of(seasonA, seasonB, seasonC);

        Set<String> specialEventCategories = Set.of("Festival", "Concert");
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 1, 31);

        CentralRMSSeason<Integer> specialEventSeasonA = new CentralRMSSeason<>(defaultMetadata, null, "Special Event Season A", LocalDateUtils.toJodaLocalDate(start), LocalDateUtils.toJodaLocalDate(end));
        CentralRMSSeason<Integer> specialEventSeasonA1 = new CentralRMSSeason<>(defaultMetadata, null, "Special Event Season A1", seasonA.getEndDate().plusDays(1), seasonB.getStartDate().minusDays(1));
        CentralRMSSeason<Integer> specialEventSeasonA2 = new CentralRMSSeason<>(defaultMetadata, null, "Special Event Season A2", seasonB.getEndDate().plusDays(1), seasonC.getStartDate().minusDays(1));
        CentralRMSSeason<Integer> specialEventSeasonB = new CentralRMSSeason<>(defaultMetadata, null, "Special Event Season B", LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 2, 5)), LocalDateUtils.toJodaLocalDate(LocalDate.of(2023, 2, 10)));

        CrudWrapper<CentralRMSSeason<Integer>, CentralRMSSeason<Integer>, CentralRMSSeason<Integer>> systemDateSplit = new CrudWrapper<>(
                List.of(seasonAPast),
                List.of(seasonACurrentAndFuture, seasonB, seasonC),
                List.of(seasonA)
        );

        CrudWrapper<CentralRMSSeason<Integer>, CentralRMSSeason<Integer>, CentralRMSSeason<Integer>> bulkWindowSplit = new CrudWrapper<>(
                List.of(specialEventSeasonA, specialEventSeasonB),
                Collections.emptyList(),
                Collections.emptyList()
        );
        SplitResultWithIgnored<Season> seasonSplitResultWithIgnored = new SplitResultWithIgnored<>(
                new CrudWrapper<>(List.of(seasonA2, seasonC1), List.of(seasonA1, seasonC2), List.of(seasonACurrentAndFuture, seasonC)),
                List.of(seasonB)
        );

        when(centralRMSSeasonSplitterCore.splitSeasonsBySystemDate(seasonConfigs)).thenReturn(systemDateSplit);
        when(centralRMSSeasonSplitterCore.splitBulkWindowBySpecialEvents(defaultConfigs, specialEventCategories, start, end)).thenReturn(bulkWindowSplit);
        when(specialEventSeasonSplitter.splitSeasonsByClampedSpecialEventsAndKeepIgnored(List.of(seasonACurrentAndFuture, seasonB, seasonC), specialEventCategories, start, end)).thenReturn(seasonSplitResultWithIgnored);
        when(seasonsServiceAdapter.doesAnySeasonOverlapSubject(specialEventSeasonA, List.of(seasonB, seasonA2, seasonC1))).thenReturn(true);
        when(seasonsServiceAdapter.doesAnySeasonOverlapSubject(specialEventSeasonB, List.of(seasonB, seasonA2, seasonC1))).thenReturn(false);
        when(seasonsServiceAdapter.splitSeasonIgnoreOverlapsWithSplitters(specialEventSeasonA, List.of(seasonB, seasonA2, seasonC1))).thenReturn(List.of(specialEventSeasonA1, specialEventSeasonA2));

        CrudWrapper<CentralRMSSeason<Integer>, CentralRMSSeason<Integer>, CentralRMSSeason<Integer>> result = splitter.split(defaultConfigs, seasonConfigs, specialEventCategories, start, end);

        assertEquals(List.of(seasonAPast, seasonA1, seasonC2), result.getCreate());
        assertEquals(List.of(seasonA2, specialEventSeasonA1, seasonB, specialEventSeasonA2, seasonC1, specialEventSeasonB), result.getUpdate());
        assertEquals(List.of(seasonA, seasonC), result.getDelete());
    }

}
