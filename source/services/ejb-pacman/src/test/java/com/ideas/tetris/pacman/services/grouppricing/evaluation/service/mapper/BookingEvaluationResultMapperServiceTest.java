package com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper;

import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.ngi.GroupEvaluationRequestService;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationRequest;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationRoomNight;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.GuestRoomRecommendedRateRoomClass;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.GuestRoomRecommendedRateRoomType;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.salesandcatering.*;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationObjectMother.*;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.BookingEvaluationResultMapperService.TIME_FORMAT_hh_mm_SS;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_TIME_T_MILLIS_FORMAT;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BookingEvaluationResultMapperServiceTest {

    private static final String ARRIVAL_DATE_STR = "2020-10-15";
    private LocalDate arrivalDate;
    private BookingEvaluationResult bookingEvaluationResult;
    private final Property dummyProperty = buildDummyProperty();

    @InjectMocks
    private BookingEvaluationResultMapperService evaluationResultMapperService = new BookingEvaluationResultMapperService();

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    GroupEvaluation groupEvaluationMock;

    @Mock
    FunctionSpaceRevenueGroup functionSpaceRevenueGroupMock;

    @Mock
    private GroupEvaluationRequestService groupEvaluationRequestService;

    @BeforeEach
    public void setUp() throws ParseException {
        arrivalDate = LocalDate.fromDateFields(DateUtil.parseDate(ARRIVAL_DATE_STR, DEFAULT_DATE_FORMAT));
        bookingEvaluationResult = new BookingEvaluationResult();
    }

    @Test
    public void buildBookingEvaluationResultTracker() {
        //Given
        final GroupEvaluation groupEvaluation = buildGroupEvaluationFor(TestProperty.H1.getId(), "1234");
        arrivalDate = groupEvaluation.getPreferredDate();
        //When
        final BookingEvaluationResultTracker bookingEvaluationResultTracker = evaluationResultMapperService.buildBookingEvaluationResultTracker(groupEvaluation, dummyProperty);
        //Then
        assertEquals("BSTN", bookingEvaluationResultTracker.getClientCode());
        assertEquals("H2", bookingEvaluationResultTracker.getPropertyCode());
        assertNull(bookingEvaluationResultTracker.getEvaluationId());
        assertNull(bookingEvaluationResultTracker.getCorrelationId());

        assertBookingEvaluationResultSingleValueFields(groupEvaluation, bookingEvaluationResultTracker.getBookingEvaluationResult());
    }

    @Test
    public void shouldBuildBookingEvaluationResultForSingleValueFields() {
        final GroupEvaluation groupEvaluation = buildGroupEvaluationFor(TestProperty.H1.getId(), "1234");
        arrivalDate = groupEvaluation.getPreferredDate();

        bookingEvaluationResult = evaluationResultMapperService.buildBookingEvaluationResult(groupEvaluation, dummyProperty);

        assertBookingEvaluationResultSingleValueFields(groupEvaluation, bookingEvaluationResult);
    }

    @Test
    void shouldBuildBookingEvaluationResultWithoutStatusCode() {
        final GroupEvaluation groupEvaluation = buildGroupEvaluationFor(TestProperty.H1.getId(), "1234");
        groupEvaluation.getGroupEvaluationArrivalDates().forEach(arrivalDate -> arrivalDate.setResultCode(null));
        arrivalDate = groupEvaluation.getPreferredDate();

        bookingEvaluationResult = evaluationResultMapperService.buildBookingEvaluationResult(groupEvaluation, dummyProperty);

        assertNull(bookingEvaluationResult.getStatusCode());
        assertNull(bookingEvaluationResult.getStatusCodeDescription());
    }

    private void assertBookingEvaluationResultSingleValueFields(GroupEvaluation groupEvaluation, BookingEvaluationResult bookingEvaluationResult) {
        assertEquals("Hilton-Paris", bookingEvaluationResult.getPropertyName());
        assertEquals("1234", bookingEvaluationResult.getBookingId());
        assertEquals("SampleGroupName", bookingEvaluationResult.getGroupName());
        assertNull(bookingEvaluationResult.getSalesPersonId());
        assertEquals("Scenario", bookingEvaluationResult.getMaterializationStatus());
        //assert date related fields
        assertEquals(groupEvaluation.getEvaluationDate().toString(DATE_TIME_T_MILLIS_FORMAT), bookingEvaluationResult.getEvaluationDateTime());
        assertEquals(arrivalDate.toString(DEFAULT_DATE_FORMAT), bookingEvaluationResult.getArrivalDate());
        assertEquals(arrivalDate.plusDays(groupEvaluation.getNumberOfNights()).toString(DEFAULT_DATE_FORMAT), bookingEvaluationResult.getDepartureDate());
        assertEquals(groupEvaluation.getFollowUpDate().toString(DEFAULT_DATE_FORMAT), bookingEvaluationResult.getFollowUpDate());
        //assert few numeric (Double) fields
        assertEquals(100.0, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertNull(bookingEvaluationResult.getDisplacedFunctionSpaceRevenue());
        assertNull(bookingEvaluationResult.getFunctionSpaceRecommendedRental());
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
        assertEquals(96101.0, bookingEvaluationResult.getTotalContractualRevenue());
        assertEquals(9.0, bookingEvaluationResult.getDisplacedGuestRooms());
        assertEquals(50.0, bookingEvaluationResult.getDisplacedGuestRoomRevenue());
        assertEquals(48.0, bookingEvaluationResult.getTotalNetProfit());
        assertEquals(0.06, bookingEvaluationResult.getTotalNetProfitPercent());
        assertNull(bookingEvaluationResult.getTotalUserAdjustedContractualRevenue());
        assertNull(bookingEvaluationResult.getUserAdjustedNetProfit());
        assertNull(bookingEvaluationResult.getUserAdjustedNetProfitPercent());
        assertEquals(GroupEvaluationArrivalDateResultCode.ACCEPTABLE.getId(), bookingEvaluationResult.getStatusCode());
        assertEquals(ResourceUtil.getText(GroupEvaluationArrivalDateResultCode.ACCEPTABLE.getCaption(), Locale.ENGLISH), bookingEvaluationResult.getStatusCodeDescription());
        assertEquals(88.12, bookingEvaluationResult.getAverageMAR());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayRoomTypeGuestRoomOnly() {
        //Given
        final GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY);
        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, null);
        //Then
        assertGuestRoomPerStayDayRoomClassLevel(arrivalDate, bookingEvaluationResult);
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayRoomTypeGuestRoomOnlyForHyatt() {
        //Given
        setupWorkContext();
        String evaluationRequestId = "12111";
        GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY);
        groupEvaluation.setEvaluationRequestId(evaluationRequestId);
        EvaluationRequest evaluationRequest = buildEvaluationRequest(groupEvaluation.getPreferredDate());
        when(groupEvaluationRequestService.retrieveEvaluationRequest(any(), any(), eq(evaluationRequestId)))
                .thenReturn(evaluationRequest);
        when(groupEvaluationRequestService.getEvaluationRequestArrivalDate(evaluationRequest))
                .thenReturn(evaluationRequest.getArrivalDate(false));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_SALES_AND_CATERING_ROOM_TYPE_IN_EVALUATION_RESULT))
                .thenReturn(true);

        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, evaluationRequest);

        //Then
        assertGuestRoomPerStayDayRoomClassLevelBuildUsingEvaluationRequest();
    }

    private void setupWorkContext() {
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(11033);
        workContext.setClientCode("SandBox");
        workContext.setPropertyCode("CPGP02");
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    private EvaluationRequest buildEvaluationRequest(LocalDate evaluationRequestArrivalDate) {
        EvaluationRequest evaluationRequest = new EvaluationRequest();

        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(10, evaluationRequestArrivalDate.plusDays(1), "STD"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(12, evaluationRequestArrivalDate.plusDays(1), "STD1"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(5, evaluationRequestArrivalDate.plusDays(2), "STD"));

        return evaluationRequest;
    }

    private EvaluationRoomNight buildEvaluationRoomNight(int numberOfRooms,
                                                         LocalDate startDate, String roomType) {
        EvaluationRoomNight evaluationRoomNight = new EvaluationRoomNight();
        evaluationRoomNight.setNumberOfRooms(numberOfRooms);
        evaluationRoomNight.setOccupancyDate(startDate.toString());
        evaluationRoomNight.setRoomType(roomType);
        return evaluationRoomNight;
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayRoomTypeGuestRoomAndFunctionSpace() {
        //Given
        final GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, null);
        //Then
        assertGuestRoomPerStayDayRoomClassLevel(arrivalDate, bookingEvaluationResult);
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayRoomTypeGuestRoomAndFunctionSpaceForHyatt() {
        //Given
        setupWorkContext();
        String evaluationRequestId = "12111";
        GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        groupEvaluation.setEvaluationRequestId(evaluationRequestId);
        EvaluationRequest evaluationRequest = buildEvaluationRequest(groupEvaluation.getPreferredDate());
        when(groupEvaluationRequestService.retrieveEvaluationRequest(any(), any(), eq(evaluationRequestId)))
                .thenReturn(evaluationRequest);
        when(groupEvaluationRequestService.getEvaluationRequestArrivalDate(evaluationRequest))
                .thenReturn(evaluationRequest.getArrivalDate(false));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_SALES_AND_CATERING_ROOM_TYPE_IN_EVALUATION_RESULT))
                .thenReturn(true);

        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, evaluationRequest);

        //Then
        assertGuestRoomPerStayDayRoomClassLevelBuildUsingEvaluationRequest();
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayGroupEvaluationTypeGuestRoomOnly() {
        //Given
        final GroupEvaluation groupEvaluation = prepareGroupEvaluationROHLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY);
        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, null);
        //Then
        assertGuestRoomsPerStayDayROHLevel(arrivalDate, bookingEvaluationResult);
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomsPerStayDayGroupEvaluationTypeGuestRoomAndFunctionSpace() {
        //Given
        final GroupEvaluation groupEvaluation = prepareGroupEvaluationROHLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        //When
        evaluationResultMapperService.mapGuestRoomsPerStayDay(groupEvaluation, bookingEvaluationResult, null);
        //Then
        assertGuestRoomsPerStayDayROHLevel(arrivalDate, bookingEvaluationResult);
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingRoomAsMeetingSpaceAndGroupEvaluationTypeAsFunctionSpaceOnly() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 11:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(true);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.FUNCTION_SPACE_ONLY, null);
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(2, functionSpaceRoomsPerStayDateTimeList.size());

        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(0), "ABC");
        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(1), "DEF");
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingRoomAsMeetingSpaceAndGroupEvaluationTypeAsGuestRoomAndFunctionSpace() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 11:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(true);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, null);
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(2, functionSpaceRoomsPerStayDateTimeList.size());

        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(0), "ABC");
        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(1), "DEF");
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingGroupEvaluationTypeFunctionSpaceOnly() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 11:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(true);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.FUNCTION_SPACE_ONLY, BigDecimal.valueOf(5));
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(2, functionSpaceRoomsPerStayDateTimeList.size());

        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(0), "10 Tier 1");
        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(1), "9 Tier 1");
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingGroupEvaluationTypeFunctionSpaceOnlyAndFunctionSpaceSqFeetIsFalse() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 14:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(false);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.FUNCTION_SPACE_ONLY, BigDecimal.valueOf(5));
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(2, functionSpaceRoomsPerStayDateTimeList.size());

        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(0), "0.92903 Tier 1");
        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(1), "0.836127 Tier 1");
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingGroupEvaluationTypeGuestRoomAndFunctionSpace() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 14:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(true);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, BigDecimal.valueOf(5));
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(2, functionSpaceRoomsPerStayDateTimeList.size());

        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(0), "10 Tier 1");
        assertFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, functionSpaceRoomsPerStayDateTimeList.get(1), "9 Tier 1");
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceRoomsPerStayDateTimeHavingGroupEvaluationTypeGuestRoomOnly() throws Exception {
        //Given
        final LocalDateTime startDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("16-Jul-2016 09:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));
        final LocalDateTime endDateTime = LocalDateTime.fromDateFields(DateUtil.parseDate("18-Jul-2016 11:10:11", DateUtil.DATE_FORMAT_DD_MMM_YYYY_HH_mm_ss));

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET)).thenReturn(true);

        final GroupEvaluation groupEvaluation = prepareGroupEvaluationTestDataForFunctionSpaceRoomsPerStayDateTime(startDateTime, endDateTime, GroupEvaluationType.GUEST_ROOM_ONLY, BigDecimal.valueOf(5));
        //When
        evaluationResultMapperService.mapFunctionSpaceRoomsPerStayDateTime(groupEvaluation, bookingEvaluationResult);
        //Then
        final List<BookingEvaluationFunctionSpaceRoomsPerStayDateTime> functionSpaceRoomsPerStayDateTimeList = bookingEvaluationResult.getFunctionSpaceRoomsPerStayDateTimeList();
        assertEquals(0, functionSpaceRoomsPerStayDateTimeList.size());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsGuestRoomOnlyAndGroupPricingEvaluationMethodAsROH() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY, false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        assertEquals(10.0, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertNull(bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    void buildBookingEvaluationResult_testMapUserAdjustedRoomRateHavingGroupEvaluationTypeAsGuestRoomOnlyAndGroupPricingEvaluationMethodAsROH() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY, true);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        //Then
        assertEquals(10.0, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertEquals(11.0, bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsGuestRoomAndFunctionSpaceAndGroupPricingEvaluationMethodAsROH() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        assertEquals(10.0, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertNull(bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    void buildBookingEvaluationResult_testMapUserAdjustedRoomRateHavingGroupEvaluationTypeAsGuestRoomAndFunctionSpaceAndGroupPricingEvaluationMethodAsROH() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, true);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        //Then
        assertEquals(10.0, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertEquals(11.0, bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsFunctionSpaceOnlyAndGroupPricingEvaluationMethodAsROH() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertNull(bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedRoomRateHavingGroupEvaluationTypeAsFunctionSpaceOnly() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, true);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        //Then
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertNull(bookingEvaluationResult.getUserAdjustedGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsFunctionSpaceOnlyAndGroupPricingEvaluationMethodAsROHWithUserAdjustedOutput() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateTestDataForGuestRoomRecommendedRateROH(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, true);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsFunctionSpaceOnlyAndGroupPricingEvaluationMethodAsRC() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(0, guestRoomRecommendedRateRoomClassesMap.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsFunctionSpaceOnlyAndGroupPricingEvaluationMethodAsRCBuiltUsingEvaluationRequest() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_SALES_AND_CATERING_ROOM_TYPE_IN_EVALUATION_RESULT))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(0, guestRoomRecommendedRateRoomClassesMap.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsGuestRoomAndFunctionSpaceAndGroupPricingEvaluationMethodAsRC() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClasses = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClasses.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassData(guestRoomRecommendedRateRoomClasses, false);
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsGuestRoomAndFunctionSpaceAndGroupPricingEvaluationMethodAsRCBuiltUsingEvaluationRequest() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, false);
        setupWorkContext();
        String evaluationRequestId = "12111";
        GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        groupEvaluation.setEvaluationRequestId(evaluationRequestId);
        LocalDate evaluationRequestArrivalDate = groupEvaluation.getPreferredDate();
        EvaluationRequest evaluationRequest = new EvaluationRequest();
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(10, evaluationRequestArrivalDate.plusDays(1), "STDA"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(12, evaluationRequestArrivalDate.plusDays(1), "STDB"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(12, evaluationRequestArrivalDate.plusDays(1), "STDB"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(5, evaluationRequestArrivalDate.plusDays(2), "STDC1"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(5, evaluationRequestArrivalDate.plusDays(2), "STDC2"));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_SALES_AND_CATERING_ROOM_TYPE_IN_EVALUATION_RESULT))
                .thenReturn(true);
        when(groupEvaluationRequestService.retrieveEvaluationRequest(any(), any(), eq(evaluationRequestId)))
                .thenReturn(evaluationRequest);
        when(groupEvaluationRequestService.getEvaluationRequestArrivalDate(evaluationRequest))
                .thenReturn(evaluationRequest.getArrivalDate(true));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        AccomType accomTypeA = new AccomType();
        accomTypeA.setName("A");
        AccomType accomTypeB = new AccomType();
        accomTypeB.setName("B");
        AccomType accomTypeC = new AccomType();
        accomTypeC.setName("C");

        when(groupEvaluationRequestService.getRoomType(eq("STDA"), any()))
                .thenReturn(accomTypeA);
        when(groupEvaluationRequestService.getRoomType(eq("STDB"), any()))
                .thenReturn(accomTypeB);
        when(groupEvaluationRequestService.getRoomType(eq("STDC1"), any()))
                .thenReturn(accomTypeC);
        when(groupEvaluationRequestService.getRoomType(eq("STDC2"), any()))
                .thenReturn(accomTypeC);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, evaluationRequest);

        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClasses = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClasses.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassDataBuiltUsingEvaluationRequest(guestRoomRecommendedRateRoomClasses, false);
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingUserAdjustmentsAndGroupPricingEvaluationMethodAsRC() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClassesMap.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassData(guestRoomRecommendedRateRoomClassesMap, true);
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingUserAdjustmentsAndGroupPricingEvaluationMethodAsRCBuiltUsingEvaluationRequest() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, true);
        setupWorkContext();
        String evaluationRequestId = "12111";
        GroupEvaluation groupEvaluation = prepareGroupEvaluationRCLevel(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        groupEvaluation.setEvaluationRequestId(evaluationRequestId);
        LocalDate evaluationRequestArrivalDate = groupEvaluation.getPreferredDate();
        EvaluationRequest evaluationRequest = new EvaluationRequest();
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(10, evaluationRequestArrivalDate.plusDays(1), "STDA"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(12, evaluationRequestArrivalDate.plusDays(1), "STDB"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(12, evaluationRequestArrivalDate.plusDays(1), "STDB"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(5, evaluationRequestArrivalDate.plusDays(2), "STDC1"));
        evaluationRequest.getEvaluationRoomNights().add(buildEvaluationRoomNight(5, evaluationRequestArrivalDate.plusDays(2), "STDC2"));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_SALES_AND_CATERING_ROOM_TYPE_IN_EVALUATION_RESULT))
                .thenReturn(true);
        when(groupEvaluationRequestService.retrieveEvaluationRequest(any(), any(), eq(evaluationRequestId)))
                .thenReturn(evaluationRequest);
        when(groupEvaluationRequestService.getEvaluationRequestArrivalDate(evaluationRequest))
                .thenReturn(evaluationRequest.getArrivalDate(true));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        AccomType accomTypeA = new AccomType();
        accomTypeA.setName("A");
        AccomType accomTypeB = new AccomType();
        accomTypeB.setName("B");
        AccomType accomTypeC = new AccomType();
        accomTypeC.setName("C");

        when(groupEvaluationRequestService.getRoomType(eq("STDA"), any()))
                .thenReturn(accomTypeA);
        when(groupEvaluationRequestService.getRoomType(eq("STDB"), any()))
                .thenReturn(accomTypeB);
        when(groupEvaluationRequestService.getRoomType(eq("STDC1"), any()))
                .thenReturn(accomTypeC);
        when(groupEvaluationRequestService.getRoomType(eq("STDC2"), any()))
                .thenReturn(accomTypeC);

        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, evaluationRequest);

        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClasses = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClasses.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassDataBuiltUsingEvaluationRequest(guestRoomRecommendedRateRoomClasses, true);
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupEvaluationTypeAsGuestRoomOnlyAndGroupPricingEvaluationMethodAsRC() {
        //Given
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(false);
        //When
        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);
        //Then
        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClassesMap.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassData(guestRoomRecommendedRateRoomClassesMap, false);
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingGroupPricingEvaluationMethodAsRCAndSendROHRateToggleIsTrue() {
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_ONLY, false);
        groupEvaluationArrivalDate.setSuggestedRate(BigDecimal.valueOf(125.675));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(true);

        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClassesMap.size());
        assertEquals(125.68, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassData(guestRoomRecommendedRateRoomClassesMap, false);
    }

    @Test
    void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateWithEvaluationTypeAsFunctionSpaceOnlyAndGroupPricingEvaluationMethodRCAndSendROHRateToggleEnabled() {
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.FUNCTION_SPACE_ONLY, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(true);

        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(0, guestRoomRecommendedRateRoomClassesMap.size());
        assertNull(bookingEvaluationResult.getGuestRoomRecommendedRateROH());
    }

    @Test
    public void buildBookingEvaluationResult_testMapGuestRoomRecommendedRateHavingUserAdjustmentsAndGroupPricingEvaluationMethodAsRCAndSendROHRateToggleEnabled() {
        final GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForGuestRoomRecommendedRateRCTestData(arrivalDate, GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, true);
        groupEvaluationArrivalDate.setSuggestedRate(BigDecimal.valueOf(125.675));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SEND_ROH_RATE_IN_RC_EVALUATION_RESULT_RESPONSE)).thenReturn(true);

        evaluationResultMapperService.mapGuestRoomRecommendedRate(bookingEvaluationResult, groupEvaluationArrivalDate, null);

        final List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap = bookingEvaluationResult.getGuestRoomRecommendedRateRoomClassesList();
        assertEquals(1, guestRoomRecommendedRateRoomClassesMap.size());
        assertEquals(125.68, bookingEvaluationResult.getGuestRoomRecommendedRateROH());
        assertGuestRoomRecommendedRateRoomClassData(guestRoomRecommendedRateRoomClassesMap, true);
    }

    @Test
    public void buildBookingEvaluationResult_testMapDisplacedFunctionSpaceRevenueHavingFunctionSpaceEnabledForGroupEvaluationTypeFunctionSpaceOnly() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForFunctionSpaceRecommendedRental(GroupEvaluationType.FUNCTION_SPACE_ONLY, arrivalDate);
        groupEvaluationArrivalDate.setFunctionSpaceNetRevenue(new BigDecimal(100));
        groupEvaluationArrivalDate.setFunctionSpaceNetIncrementalRevenue(new BigDecimal(20));
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(80.0, bookingEvaluationResult.getDisplacedFunctionSpaceRevenue());
        assertEquals(0.0, bookingEvaluationResult.getFunctionSpaceRecommendedRental());
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapFunctionSpaceDisplacedRevenue_DoNotMapIfEvaluationNotFunctionSpace() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForFunctionSpaceRecommendedRental(GroupEvaluationType.GUEST_ROOM_ONLY, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertNull(bookingEvaluationResult.getDisplacedFunctionSpaceRevenue());
    }

    @Test
    public void buildBookingEvaluationResult_testMapDisplacedFunctionSpaceRevenueForGroupEvaluationTypeGuestRoomAndFunctionSpace() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForFunctionSpaceRecommendedRental(GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, arrivalDate);
        groupEvaluationArrivalDate.setFunctionSpaceNetRevenue(new BigDecimal(100));
        groupEvaluationArrivalDate.setFunctionSpaceNetIncrementalRevenue(new BigDecimal(80));
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(20.0, bookingEvaluationResult.getDisplacedFunctionSpaceRevenue());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeFunctionSpaceOnlyAndHasUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.FUNCTION_SPACE_ONLY, true, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(10.0, bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeGuestRoomAndFunctionSpaceAndHasUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, true, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(10.0, bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeGuestRoomOnlyAndHasUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.GUEST_ROOM_ONLY, true, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeGuestRoomOnlyAndNoUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.GUEST_ROOM_ONLY, false, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeGuestRoomAndFunctionSpaceAndNoUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE, false, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedFunctionSpaceRentalForGroupEvaluationTypeFunctionSpaceOnlyAndNoUserAdjustedOutput() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDateForUserAdjustedFunctionSpaceRental(GroupEvaluationType.FUNCTION_SPACE_ONLY, false, arrivalDate);
        //When
        evaluationResultMapperService.mapFunctionSpaceRevenueAndRental(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertNull(bookingEvaluationResult.getUserAdjustedFunctionSpaceRental());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedNetProfit() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, true, false);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(1690.0, bookingEvaluationResult.getUserAdjustedNetProfit());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedNetProfitWithFunctionSpaceCost() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, true, false);
        Set<GroupEvaluationCost> costs = new HashSet<>();
        GroupEvaluationCost functionSpaceCost = new GroupEvaluationCost();
        functionSpaceCost.setGroupEvaluationCostType(GroupEvaluationCostType.FUNCTION_SPACE);
        functionSpaceCost.setCost(new BigDecimal(100));
        costs.add(functionSpaceCost);

        when(groupEvaluationMock.getGroupEvaluationCosts()).thenReturn(costs);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(1590.0, bookingEvaluationResult.getUserAdjustedNetProfit());
    }

    @Test
    public void buildBookingEvaluationResult_testMapUserAdjustedNetProfitPercent() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, true, false);
        groupEvaluationArrivalDate.setConferenceAndBanquetGrossRevenue(new BigDecimal(3000));
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(19.88, bookingEvaluationResult.getUserAdjustedNetProfitPercent());
    }

    @Test
    public void buildBookingEvaluationResult_testMapTotalUserAdjustedContractualRevenue() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, true, false);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(8000.0, bookingEvaluationResult.getTotalUserAdjustedContractualRevenue());
    }

    @Test
    public void buildBookingEvaluationResult_testMapTotalUserAdjustedContractualRevenueGuestRoomOnly() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, false, false);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(7000.0, bookingEvaluationResult.getTotalUserAdjustedContractualRevenue());
    }

    @Test
    public void buildBookingEvaluationResult_testMapTotalUserAdjustedContractualRevenueFunctionSpaceOnly() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, false, true, false);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(3000.0, bookingEvaluationResult.getTotalUserAdjustedContractualRevenue());
    }

    @Test
    public void buildBookingEvaluationResult_testMapTotalUserAdjustedContractualRevenueRoomTypeEvaluation() {
        //Given
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = prepareGroupEvaluationArrivalDate(false, true, true, true);
        //When
        evaluationResultMapperService.mapUserAdjustedRevenueAndProfit(bookingEvaluationResult, groupEvaluationArrivalDate);
        //Then
        assertEquals(3000.0, bookingEvaluationResult.getTotalUserAdjustedContractualRevenue());
    }

    @Test
    public void formatToDouble() {
        assertEquals(999999.99, evaluationResultMapperService.formatToDouble(new BigDecimal("999999.9912999")));
        assertEquals(123.0, evaluationResultMapperService.formatToDouble(new BigDecimal(123)));
        assertEquals(0.0, evaluationResultMapperService.formatToDouble(new BigDecimal(0)));
        assertEquals(0.0, evaluationResultMapperService.formatToDouble(null));
    }

    @Test
    public void formatToString() {
        assertEquals("999999.99", evaluationResultMapperService.formatToString(new BigDecimal("999999.9912999")));
        assertEquals("123", evaluationResultMapperService.formatToString(new BigDecimal(123)));
        assertEquals("0", evaluationResultMapperService.formatToString(new BigDecimal(0)));
        assertEquals("0.0", evaluationResultMapperService.formatToString(null));
    }

    @Test
    public void formatLocalDateTimeToString_yyyy_MM_dd_T_HH_mm_ss_SSS() {
        LocalDateTime localDateTime = LocalDateTime.parse(ARRIVAL_DATE_STR);
        assertEquals("2020-10-15T00:00:00.000", evaluationResultMapperService.formatLocalDateTimeToString(localDateTime, DATE_TIME_T_MILLIS_FORMAT));
    }

    @Test
    public void formatLocalDateTimeToString_yyyy_MM_dd() {
        LocalDateTime localDateTime = LocalDateTime.parse(ARRIVAL_DATE_STR);
        assertEquals("2020-10-15", evaluationResultMapperService.formatLocalDateTimeToString(localDateTime, DEFAULT_DATE_FORMAT));
    }

    @Test
    public void formatLocalDateTimeToString_hh_mm_SS() {
        LocalDateTime localDateTime = LocalDateTime.parse(ARRIVAL_DATE_STR);
        assertEquals("12:00:00", evaluationResultMapperService.formatLocalDateTimeToString(localDateTime, TIME_FORMAT_hh_mm_SS));
    }

    @Test
    public void formatLocalDateToString_yyyy_MM_dd() {
        LocalDate localDate = LocalDate.parse(ARRIVAL_DATE_STR);
        assertEquals("2020-10-15", evaluationResultMapperService.formatLocalDateToString(localDate));
    }

    private void assertGuestRoomRecommendedRateRoomClassDataBuiltUsingEvaluationRequest(List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap, Boolean validateUserAdjustedOutput) {
        assertEquals(BookingEvaluationResultMapperServiceTest.ARRIVAL_DATE_STR, guestRoomRecommendedRateRoomClassesMap.get(0).getOccupancyDate());
        final List<GuestRoomRecommendedRateRoomClass> guestRoomRecommendedRateRoomClasses = guestRoomRecommendedRateRoomClassesMap.get(0).getGuestRoomRecommendedRateRoomClasses();

        final GuestRoomRecommendedRateRoomClass guestRoomRecommendedRateRoomClass1 = guestRoomRecommendedRateRoomClasses.get(0);
        assertEquals("D", guestRoomRecommendedRateRoomClass1.getAccomClassName());
        assertEquals("8", guestRoomRecommendedRateRoomClass1.getGuestRoomRate());

        final List<GuestRoomRecommendedRateRoomType> guestRoomRecommendedRateRoomTypeList1 = guestRoomRecommendedRateRoomClass1.getGuestRoomRecommendedRateRoomTypeList();
        assertEquals(2, guestRoomRecommendedRateRoomTypeList1.size());

        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType1 = guestRoomRecommendedRateRoomTypeList1.get(0);
        assertEquals("STDA", guestRoomRecommendedRateRoomType1.getAccomTypeName());
        assertEquals("10", guestRoomRecommendedRateRoomType1.getGuestRoomRate());

        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType2 = guestRoomRecommendedRateRoomTypeList1.get(1);
        assertEquals("STDB", guestRoomRecommendedRateRoomType2.getAccomTypeName());
        assertEquals("8", guestRoomRecommendedRateRoomType2.getGuestRoomRate());

        final GuestRoomRecommendedRateRoomClass guestRoomRecommendedRateRoomClass2 = guestRoomRecommendedRateRoomClasses.get(1);
        assertEquals("E", guestRoomRecommendedRateRoomClass2.getAccomClassName());
        assertEquals("6", guestRoomRecommendedRateRoomClass2.getGuestRoomRate());

        final List<GuestRoomRecommendedRateRoomType> guestRoomRecommendedRateRoomTypeList2 = guestRoomRecommendedRateRoomClass2.getGuestRoomRecommendedRateRoomTypeList();
        assertEquals(2, guestRoomRecommendedRateRoomTypeList2.size());

        guestRoomRecommendedRateRoomTypeList2.sort(Comparator.comparing(GuestRoomRecommendedRateRoomType::getAccomTypeName));
        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType3 = guestRoomRecommendedRateRoomTypeList2.get(0);
        assertEquals("STDC1", guestRoomRecommendedRateRoomType3.getAccomTypeName());
        assertEquals("6", guestRoomRecommendedRateRoomType3.getGuestRoomRate());

        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType4 = guestRoomRecommendedRateRoomTypeList2.get(1);
        assertEquals("STDC2", guestRoomRecommendedRateRoomType4.getAccomTypeName());
        assertEquals("6", guestRoomRecommendedRateRoomType4.getGuestRoomRate());

        if (validateUserAdjustedOutput) {
            assertEquals("11", guestRoomRecommendedRateRoomType1.getUserAdjustedGuestRoomRate());
            assertEquals("9", guestRoomRecommendedRateRoomType2.getUserAdjustedGuestRoomRate());
            assertEquals("5", guestRoomRecommendedRateRoomType3.getUserAdjustedGuestRoomRate());
            assertEquals("5", guestRoomRecommendedRateRoomType4.getUserAdjustedGuestRoomRate());
        }
    }

    private GroupEvaluationArrivalDate prepareGroupEvaluationArrivalDate(boolean isRoomTaxIncluded, boolean isGuestRoomIncluded, boolean isFunctionSpaceIncluded, boolean isRoomTypeEvaluation) {
        when(groupEvaluationMock.isGuestRoomIncluded()).thenReturn(isGuestRoomIncluded);
        when(groupEvaluationMock.isFunctionSpaceIncluded()).thenReturn(isFunctionSpaceIncluded);
        when(groupEvaluationMock.getTotalNumberOfRooms()).thenReturn(100);
        when(groupEvaluationMock.getPerRoomServicingCost()).thenReturn(new BigDecimal(10));
        when(groupEvaluationMock.isRoomTypeEvaluation()).thenReturn(isRoomTypeEvaluation);
        when(groupEvaluationMock.isNotGroupPricingEvaluation()).thenReturn(true);
        when(functionSpaceRevenueGroupMock.getProfitPercent()).thenReturn(BigDecimal.valueOf(0.60));

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate() {
            @Override
            public BigDecimal getDisplacedConfAndBanquetRevenue() {
                return new BigDecimal(100);
            }

            @Override
            public BigDecimal getRoomDisplacedRevenue() {
                return new BigDecimal(10);
            }
        };

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setUserAdjustedOutput(true);
        groupEvaluationArrivalDate.setUserAdjustedRoomRate(new BigDecimal(50));
        groupEvaluationArrivalDate.setUserAdjustedFunctionSpaceRate(new BigDecimal(1000));

        groupEvaluationArrivalDate.setAncillaryGrossRevenue(new BigDecimal(500));
        groupEvaluationArrivalDate.setRoomNetRevenue(new BigDecimal(5000));
        groupEvaluationArrivalDate.setNetIncrementalRoomRevenue(new BigDecimal(4500));

        //displacement
        groupEvaluationArrivalDate.setDisplacedAncillaryRevenue(new BigDecimal(100));
        groupEvaluationArrivalDate.setDisplacedAncillaryProfit(new BigDecimal(10));
        groupEvaluationArrivalDate.setDisplacedConfAndBanquetProfit(new BigDecimal(20));

        //profit
        groupEvaluationArrivalDate.setRoomGrossProfit(new BigDecimal(5000));
        groupEvaluationArrivalDate.setIncrementalRoomProfit(new BigDecimal(1000));
        groupEvaluationArrivalDate.setAncillaryGrossProfit(new BigDecimal(500));
        groupEvaluationArrivalDate.setConferenceAndBanquetGrossProfit(new BigDecimal(3000));

        groupEvaluationArrivalDate.setNetIncrementalProfit(new BigDecimal(8400));
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluationMock);
        groupEvaluationArrivalDate.setCostOfWalk(BigDecimal.valueOf(200));

        if (isRoomTaxIncluded) {
            groupEvaluationArrivalDate.getGroupEvaluation().setTaxRate(BigDecimal.TEN);
        }
        when(groupEvaluationMock.getGroupEvaluationFunctionSpaceConfAndBanquets()).thenReturn(getConfAndBanq());
        return groupEvaluationArrivalDate;
    }

    private Set<GroupEvaluationFunctionSpaceConfAndBanq> getConfAndBanq() {
        List<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqs = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
            confAndBanq.setRevenue(new BigDecimal(1000));
            confAndBanq.setGroupEvaluation(groupEvaluationMock);
            confAndBanq.setCommissionPercentage(BigDecimal.ZERO);
            confAndBanq.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroupMock);
            groupEvaluationFunctionSpaceConfAndBanqs.add(confAndBanq);
        }
        return new HashSet<>(groupEvaluationFunctionSpaceConfAndBanqs);
    }

    private void assertGuestRoomRecommendedRateRoomClassData(List<BookingEvaluationGuestRoomRecommendedRateByDate> guestRoomRecommendedRateRoomClassesMap, Boolean validateUserAdjustedOutput) {
        assertEquals(BookingEvaluationResultMapperServiceTest.ARRIVAL_DATE_STR, guestRoomRecommendedRateRoomClassesMap.get(0).getOccupancyDate());
        final List<GuestRoomRecommendedRateRoomClass> guestRoomRecommendedRateRoomClasses = guestRoomRecommendedRateRoomClassesMap.get(0).getGuestRoomRecommendedRateRoomClasses();

        final GuestRoomRecommendedRateRoomClass guestRoomRecommendedRateRoomClass1 = guestRoomRecommendedRateRoomClasses.get(0);
        assertEquals("D", guestRoomRecommendedRateRoomClass1.getAccomClassName());
        assertEquals("8", guestRoomRecommendedRateRoomClass1.getGuestRoomRate());

        final List<GuestRoomRecommendedRateRoomType> guestRoomRecommendedRateRoomTypeList1 = guestRoomRecommendedRateRoomClass1.getGuestRoomRecommendedRateRoomTypeList();
        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType1 = guestRoomRecommendedRateRoomTypeList1.get(0);
        assertEquals("A", guestRoomRecommendedRateRoomType1.getAccomTypeName());
        assertEquals("10", guestRoomRecommendedRateRoomType1.getGuestRoomRate());

        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType2 = guestRoomRecommendedRateRoomTypeList1.get(1);
        assertEquals("B", guestRoomRecommendedRateRoomType2.getAccomTypeName());
        assertEquals("8", guestRoomRecommendedRateRoomType2.getGuestRoomRate());

        final GuestRoomRecommendedRateRoomClass guestRoomRecommendedRateRoomClass2 = guestRoomRecommendedRateRoomClasses.get(1);
        assertEquals("E", guestRoomRecommendedRateRoomClass2.getAccomClassName());
        assertEquals("6", guestRoomRecommendedRateRoomClass2.getGuestRoomRate());

        final List<GuestRoomRecommendedRateRoomType> guestRoomRecommendedRateRoomTypeList2 = guestRoomRecommendedRateRoomClass2.getGuestRoomRecommendedRateRoomTypeList();
        final GuestRoomRecommendedRateRoomType guestRoomRecommendedRateRoomType3 = guestRoomRecommendedRateRoomTypeList2.get(0);
        assertEquals("C", guestRoomRecommendedRateRoomType3.getAccomTypeName());
        assertEquals("6", guestRoomRecommendedRateRoomType3.getGuestRoomRate());

        if (validateUserAdjustedOutput) {
            assertEquals("11", guestRoomRecommendedRateRoomType1.getUserAdjustedGuestRoomRate());
            assertEquals("9", guestRoomRecommendedRateRoomType2.getUserAdjustedGuestRoomRate());
            assertEquals("5", guestRoomRecommendedRateRoomType3.getUserAdjustedGuestRoomRate());
        }
    }

    private void assertFunctionSpaceRoomsPerStayDateTime(LocalDateTime startDateTime, LocalDateTime endDateTime, BookingEvaluationFunctionSpaceRoomsPerStayDateTime functionSpaceRoomsPerStayDateTime1, String functionSpaceDetails) {
        final String startDateStr = startDateTime.toLocalDate().toString(DEFAULT_DATE_FORMAT);
        final String endDateStr = endDateTime.toLocalDate().toString(DEFAULT_DATE_FORMAT);
        final String startTimeStr = startDateTime.toLocalTime().toString(TIME_FORMAT_hh_mm_SS);
        final String endTimeStr = endDateTime.toLocalTime().toString(TIME_FORMAT_hh_mm_SS);
        assertEquals(startDateStr, functionSpaceRoomsPerStayDateTime1.getStartDate());
        assertEquals(startTimeStr, functionSpaceRoomsPerStayDateTime1.getStartTime());
        assertEquals(endDateStr, functionSpaceRoomsPerStayDateTime1.getEndDate());
        assertEquals(endTimeStr, functionSpaceRoomsPerStayDateTime1.getEndTime());
        assertEquals(functionSpaceDetails, functionSpaceRoomsPerStayDateTime1.getFunctionRoomName());
    }

    private void assertGuestRoomsPerStayDayROHLevel(LocalDate arrivalDate, BookingEvaluationResult evaluationResult) {
        final List<BookingEvaluationGuestRoomsPerStayDayROH> guestRoomsPerStayDayListROH = evaluationResult.getGuestRoomsPerStayDayListROH();
        assertEquals(3, guestRoomsPerStayDayListROH.size());
        final BookingEvaluationGuestRoomsPerStayDayROH guestRoomsPerStayDayROH1 = guestRoomsPerStayDayListROH.get(0);
        assertEquals(arrivalDate.toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayROH1.getDateOfStay());
        assertEquals(2, guestRoomsPerStayDayROH1.getNumberOfRooms().intValue());

        final BookingEvaluationGuestRoomsPerStayDayROH guestRoomsPerStayDayROH2 = guestRoomsPerStayDayListROH.get(1);
        assertEquals(arrivalDate.plusDays(1).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayROH2.getDateOfStay());
        assertEquals(2, guestRoomsPerStayDayROH2.getNumberOfRooms().intValue());

        final BookingEvaluationGuestRoomsPerStayDayROH guestRoomsPerStayDayROH3 = guestRoomsPerStayDayListROH.get(2);
        assertEquals(arrivalDate.plusDays(2).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayROH3.getDateOfStay());
        assertEquals(2, guestRoomsPerStayDayROH3.getNumberOfRooms().intValue());
    }

    private void assertGuestRoomPerStayDayRoomClassLevel(LocalDate arrivalDate, BookingEvaluationResult evaluationResult) {
        final List<BookingEvaluationGuestRoomsPerStayDayRoomClass> guestRoomsPerStayDayListRoomClass = evaluationResult.getGuestRoomsPerStayDayListRoomClass();
        assertEquals(3, guestRoomsPerStayDayListRoomClass.size());

        final BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass1 = guestRoomsPerStayDayListRoomClass.get(0);
        assertEquals(arrivalDate.toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass1.getDateOfStay());
        final List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList1 = guestRoomsPerStayDayRoomClass1.getGuestRoomsPerRoomType();

        assertEquals("KAES", guestRoomsPerRoomTypeList1.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList1.get(0).getNumberOfRooms());
        assertEquals("KNES", guestRoomsPerRoomTypeList1.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList1.get(1).getNumberOfRooms());


        final BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass2 = guestRoomsPerStayDayListRoomClass.get(1);
        assertEquals(arrivalDate.plusDays(1).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass2.getDateOfStay());
        final List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList2 = guestRoomsPerStayDayRoomClass2.getGuestRoomsPerRoomType();

        assertEquals("KAES", guestRoomsPerRoomTypeList2.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList2.get(0).getNumberOfRooms());
        assertEquals("KNES", guestRoomsPerRoomTypeList2.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList2.get(1).getNumberOfRooms());

        final BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass3 = guestRoomsPerStayDayListRoomClass.get(2);
        assertEquals(arrivalDate.plusDays(2).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass3.getDateOfStay());
        final List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList3 = guestRoomsPerStayDayRoomClass3.getGuestRoomsPerRoomType();

        assertEquals("KAES", guestRoomsPerRoomTypeList3.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList3.get(0).getNumberOfRooms());
        assertEquals("KNES", guestRoomsPerRoomTypeList3.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(2, guestRoomsPerRoomTypeList3.get(1).getNumberOfRooms());
    }

    private void assertGuestRoomPerStayDayRoomClassLevelBuildUsingEvaluationRequest() {
        List<BookingEvaluationGuestRoomsPerStayDayRoomClass> guestRoomsPerStayDayListRoomClass = bookingEvaluationResult.getGuestRoomsPerStayDayListRoomClass();
        assertEquals(3, guestRoomsPerStayDayListRoomClass.size());

        BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass1 = guestRoomsPerStayDayListRoomClass.get(0);
        assertEquals(arrivalDate.toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass1.getDateOfStay());
        List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList1 = guestRoomsPerStayDayRoomClass1.getGuestRoomsPerRoomType();

        assertEquals("STD", guestRoomsPerRoomTypeList1.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(10, guestRoomsPerRoomTypeList1.get(0).getNumberOfRooms());
        assertEquals("STD1", guestRoomsPerRoomTypeList1.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(12, guestRoomsPerRoomTypeList1.get(1).getNumberOfRooms());

        BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass2 = guestRoomsPerStayDayListRoomClass.get(1);
        assertEquals(arrivalDate.plusDays(1).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass2.getDateOfStay());
        List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList2 = guestRoomsPerStayDayRoomClass2.getGuestRoomsPerRoomType();

        assertEquals("STD", guestRoomsPerRoomTypeList2.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(5, guestRoomsPerRoomTypeList2.get(0).getNumberOfRooms());
        assertEquals("STD1", guestRoomsPerRoomTypeList2.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(0, guestRoomsPerRoomTypeList2.get(1).getNumberOfRooms());

        BookingEvaluationGuestRoomsPerStayDayRoomClass guestRoomsPerStayDayRoomClass3 = guestRoomsPerStayDayListRoomClass.get(2);
        assertEquals(arrivalDate.plusDays(2).toString(DEFAULT_DATE_FORMAT), guestRoomsPerStayDayRoomClass3.getDateOfStay());
        List<GuestRoomsPerRoomType> guestRoomsPerRoomTypeList3 = guestRoomsPerStayDayRoomClass3.getGuestRoomsPerRoomType();

        assertEquals("STD", guestRoomsPerRoomTypeList3.get(0).getSalesAndCateringGuestRoomTypeName());
        assertEquals(0, guestRoomsPerRoomTypeList3.get(0).getNumberOfRooms());
        assertEquals("STD1", guestRoomsPerRoomTypeList3.get(1).getSalesAndCateringGuestRoomTypeName());
        assertEquals(0, guestRoomsPerRoomTypeList3.get(1).getNumberOfRooms());
    }

    private Property buildDummyProperty() {
        Client client = new Client();
        client.setCode("BSTN");
        Property property = new Property();
        property.setCode("H2");
        property.setName("Hilton-Paris");
        property.setClient(client);
        return property;
    }

}