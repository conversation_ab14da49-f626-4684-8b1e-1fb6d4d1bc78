package com.ideas.tetris.pacman.services.groupblock;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.testdatabuilder.AccomTypeBuilder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;
import org.mockito.*;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PaceGroupBlockServiceTest extends AbstractG3JupiterTest {
    private static final MarketSegmentSummary COMP_MARKET_SEGMENT_SUMMARY = new MarketSegmentSummary();
    private static final LocalDate TODAY = LocalDate.now();
    private static final LocalDate YESTERDAY = TODAY.plusDays(-1);
    private static final LocalDate TOMORROW = TODAY.plusDays(1);
    private static final Date TODAYDATE = TODAY.toDate();
    private static final Date YESTERDAYDATE = YESTERDAY.toDate();
    private static final Date TOMORROWDATE = TOMORROW.toDate();
    private static final int accomType1 = 4;
    private static final int accomType2 = 5;
    private static final int accomType3 = 6;
    private static final String CODE = "code";
    private FileMetadata fileMetadata;
    private static final String LOST_REGRET_VALUE = "LOST/REGRET";

    @Mock
    private FileMetadataService fileMetadataService;
    @Mock
    private PaceGroupBlockUpdater paceGroupBlockUpdater;
    @Captor
    private ArgumentCaptor<List<PaceGroupBlock>> paceCaptor;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @InjectMocks
    PaceGroupBlockService paceGroupBlockService;
    @Mock
    private CrudService tenantCrudService;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        fileMetadata = new FileMetadata();
        fileMetadata.setSnapshotDt(TODAY.toDate());
        when(fileMetadataService.findByFileLocation(anyString())).thenReturn(fileMetadata);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SKIP_LOST_AND_REGRET_ENTRIES))
                .thenReturn(true);
    }

    @Test
    public void testLostRegretFilteringInSavePaceMethod_ShouldFilter() {
        GroupBlockMaster groupBlockMaster = createMaster();
        groupBlockMaster.setGroupStatusCode(LOST_REGRET_VALUE);

        int paceGroupBlocksCount = 5;
        List<PaceGroupBlock> paceGroupBlockList = createAndAssignPaceGroupBlockListWithGroupBlockMaster(groupBlockMaster, paceGroupBlocksCount);

        paceGroupBlockService.savePace(groupBlockMaster, paceGroupBlockList);

        verify(paceGroupBlockUpdater).batchUpdate(paceCaptor.capture());
        List<PaceGroupBlock> paceList = paceCaptor.getValue();

        assertEquals(0, paceList.size(),
                "All the paceGroupBlock records should be filtered as LOST/REGRET. Size must be 0");
    }

    @Test
    public void testLostRegretFilteringInSavePaceMethod_ShouldNotFilter() {
        GroupBlockMaster groupBlockMaster = createMaster();

        int paceGroupBlocksCount = 5;
        List<PaceGroupBlock> paceGroupBlockList = createAndAssignPaceGroupBlockListWithGroupBlockMaster(groupBlockMaster, paceGroupBlocksCount);

        paceGroupBlockService.savePace(groupBlockMaster, paceGroupBlockList);

        verify(paceGroupBlockUpdater).batchUpdate(paceCaptor.capture());
        List<PaceGroupBlock> paceList = paceCaptor.getValue();

        assertEquals(paceGroupBlocksCount, paceList.size(),
                "No LOST/REGRET records in this test, there should be no filtering. Size must remain the same: "
                        + paceGroupBlocksCount);
    }

    @Test
    public void testUpdatePaceFirstFeed() {
        GroupBlockMaster groupBlockMaster = createMaster();
        groupBlockMaster.setGroupBlockDetails(new ArrayList<>());
        List<GroupBlockDetail> convertedGroupBlockDetails = createExistingDetails(groupBlockMaster);

        paceGroupBlockService.save(paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(groupBlockMaster, convertedGroupBlockDetails), fileMetadata));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(2, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType1, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);

        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType2, secondPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyNewPace(secondPace);
    }

    @Test
    public void testUpdatePaceExistingGroupNoChange() {
        GroupBlockMaster existingGroupBlockMaster = createExistingMaster();
        List<GroupBlockDetail> groupBlockDetails = createExistingDetails(existingGroupBlockMaster);
        existingGroupBlockMaster.setGroupBlockDetails(groupBlockDetails);

        paceGroupBlockService.save(paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(existingGroupBlockMaster, groupBlockDetails), fileMetadata));

        assertMasterAndPacePersistence(0, 1);
    }

    @Test
    public void testUpdatePaceNewRoomType() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.addAll(createNewDetails(master));

        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType3, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);
    }

    @Test
    public void testUpdatePaceRemoveRoomType() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.remove(1);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType2, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyRemovePace(firstPace);
    }

    @Test
    public void testUpdatePaceRemoveRoomTypeAndAddOne() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.remove(1);
        groupBlocks.addAll(createNewDetails(master));
        master.setGroupBlockDetails(createExistingDetails(master));


        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(2, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType3, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);

        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType2, secondPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyRemovePace(secondPace);
    }

    @Test
    public void testUpdatePaceChangeDateToFuture() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(0).setOccupancyDate(TOMORROW);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(2, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType1, firstPace.getAccomTypeId().intValue());
        assertEquals(TOMORROWDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);

        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType1, secondPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyRemovePace(secondPace);
    }

    @Test
    public void testUpdatePaceChangeDatesToFuture() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(0).setOccupancyDate(TOMORROW);
        groupBlocks.get(1).setOccupancyDate(TOMORROW);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(4, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType1, firstPace.getAccomTypeId().intValue());
        assertEquals(TOMORROWDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);

        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType2, secondPace.getAccomTypeId().intValue());
        assertEquals(TOMORROWDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyNewPace(secondPace);

        PaceGroupBlock thirdPace = paceList.get(2);
        assertEquals(accomType1, thirdPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, thirdPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, thirdPace.getBusinessDayEndDate());
        verifyRemovePace(thirdPace);

        PaceGroupBlock fourthPace = paceList.get(3);
        assertEquals(accomType2, fourthPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, fourthPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, fourthPace.getBusinessDayEndDate());
        verifyRemovePace(fourthPace);
    }

    @Test
    public void testUpdatePaceChangeDatesToPast() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(1).setOccupancyDate(YESTERDAY);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(2, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType2, firstPace.getAccomTypeId().intValue());
        assertEquals(YESTERDAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);

        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType2, secondPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyRemovePace(secondPace);
    }

    @Test
    public void testUpdatePacePickupChange() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(1).setPickup(2);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType2, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        assertEquals(2, firstPace.getPickup().intValue());
        assertEquals(1, firstPace.getBlocks().intValue());
        assertEquals(1, firstPace.getOriginalBlocks().intValue());
        assertEquals(123.45, firstPace.getRate().doubleValue(), 0);
    }

    @Test
    public void testUpdatePaceBlocksChange() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(1).setBlocks(2);
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType2, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        assertEquals(1, firstPace.getPickup().intValue());
        assertEquals(2, firstPace.getBlocks().intValue());
        assertEquals(1, firstPace.getOriginalBlocks().intValue());
        assertEquals(123.45, firstPace.getRate().doubleValue(), 0);
    }

    @Test
    public void testUpdatePaceOriginalBlocksChange() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> groupBlocks = createExistingDetails(master);
        groupBlocks.get(1).setOriginalBlocks(2);
        List<GroupBlockDetail> existingDetails = createExistingDetails(master);
        master.setGroupBlockDetails(existingDetails);

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, groupBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType2, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        assertEquals(1, firstPace.getPickup().intValue());
        assertEquals(1, firstPace.getBlocks().intValue());
        assertEquals(2, firstPace.getOriginalBlocks().intValue());
        assertEquals(123.45, firstPace.getRate().doubleValue(), 0);
    }

    @Test
    public void testUpdatePaceRateChange() {
        GroupBlockMaster master = createExistingMaster();
        List<GroupBlockDetail> existingBlocks = createExistingDetails(master);
        existingBlocks.get(0).setRate(new BigDecimal(400.50));
        master.setGroupBlockDetails(createExistingDetails(master));

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, existingBlocks), fileMetadata)));

        List<PaceGroupBlock> paceList = assertMasterAndPacePersistence(1, 1);

        PaceGroupBlock firstPace = paceList.get(0);
        assertEquals(accomType1, firstPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        assertEquals(1, firstPace.getPickup().intValue());
        assertEquals(1, firstPace.getBlocks().intValue());
        assertEquals(1, firstPace.getOriginalBlocks().intValue());
        assertEquals(400.50, firstPace.getRate().doubleValue(), 0);
    }

    @Test
    public void testCancelInsert() {
        GroupBlockMaster master = createMaster();
        List<GroupBlockDetail> newBlocks = createExistingDetails(master);

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, newBlocks), fileMetadata)));

        verify(paceGroupBlockUpdater).batchUpdate(paceCaptor.capture());
        List<PaceGroupBlock> paceList = paceCaptor.getValue();

        assertEquals(2, paceList.size());
        PaceGroupBlock firstPace = paceList.get(0);
        PaceGroupBlock secondPace = paceList.get(1);
        assertEquals(accomType1, firstPace.getAccomTypeId().intValue());
        assertEquals(accomType2, secondPace.getAccomTypeId().intValue());
        assertEquals(TODAYDATE, firstPace.getOccupancyDate());
        assertEquals(TODAYDATE, secondPace.getOccupancyDate());
        assertEquals(YESTERDAYDATE, firstPace.getBusinessDayEndDate());
        assertEquals(YESTERDAYDATE, secondPace.getBusinessDayEndDate());
        verifyNewPace(firstPace);
        verifyNewPace(secondPace);

        master.setCancelDate(TODAYDATE);
        master.setGroupStatusCode("Cancelled");


        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, newBlocks), new FileMetadata())));

        paceCaptor.getAllValues().clear();
        verify(paceGroupBlockUpdater, times(2)).batchUpdate(paceCaptor.capture());
        List<PaceGroupBlock> secondPaceList = paceCaptor.getValue();

        assertEquals(2, secondPaceList.size());
        PaceGroupBlock updatedPace = secondPaceList.get(0);
        PaceGroupBlock updatedPace2 = secondPaceList.get(1);
        assertEquals(accomType1, updatedPace.getAccomTypeId().intValue());
        assertEquals(accomType2, updatedPace2.getAccomTypeId().intValue());
        assertEquals(0, updatedPace.getBlocks().intValue());
        assertEquals(0, updatedPace.getPickup().intValue());
        assertEquals(1, updatedPace.getOriginalBlocks().intValue());
        assertEquals(updatedPace.getRate().doubleValue(), 123.45, 0);
        assertEquals(0, updatedPace2.getBlocks().intValue());
        assertEquals(0, updatedPace2.getPickup().intValue());
        assertEquals(1, updatedPace2.getOriginalBlocks().intValue());
        assertEquals(updatedPace2.getRate().doubleValue(), 123.45, 0);
    }

    @Test
    public void testCancelInsertFirstRequest() {
        GroupBlockMaster master = createMaster();
        master.setCancelDate(TODAYDATE);
        master.setGroupStatusCode("Cancelled");
        List<GroupBlockDetail> newBlocks = createExistingDetails(master);

        paceGroupBlockService.save((paceGroupBlockService.calculatePace(getGroupBlockMasterHolder(master, newBlocks), fileMetadata)));

        verify(paceGroupBlockUpdater).batchUpdate(paceCaptor.capture());
    }


    private Answer<Collection<GroupBlockMaster>> returnSaveArgumentsPlusId() {
        return invocation -> {
            Collection<GroupBlockMaster> results = (Collection<GroupBlockMaster>) invocation.getArguments()[0];
            if (results == null) {
                return null;
            }
            int i = 1;
            for (GroupBlockMaster result : results) {
                result.setId(i);
                i++;
            }

            return results;
        };
    }

    private GroupBlockMaster createExistingMaster() {
        GroupBlockMaster existingMaster = createMaster();
        existingMaster.setId(23);
        return existingMaster;
    }

    private GroupBlockMaster createMaster() {
        GroupBlockMaster master = new GroupBlockMaster();
        master.setStartDate(new Date());
        master.setEndDate(new Date());
        master.setBookingDate(new Date());
        master.setCode("Code");
        master.setGroupStatusCode("statusCode");
        master.setCutoffDate(DateUtil.removeTimeFromDate(new Date()));
        master.setCutoffDays(2);
        master.setName("name");
        master.setPropertyId(5);
        master.setSalesPerson("salesPerson");
        master.setGroupTypeCode("GROUP");
        COMP_MARKET_SEGMENT_SUMMARY.setCode(CODE);
        COMP_MARKET_SEGMENT_SUMMARY.setId(2);
        master.setMarketSegment(COMP_MARKET_SEGMENT_SUMMARY);
        return master;
    }

    private Map<String, Object> buildGroupBlockMaster(Object statisticsCcorrelationId) {
        Map<String, Object> groupBlockMaster = new HashMap<>();

        groupBlockMaster.put("name", "name");
        groupBlockMaster.put("description", "The description");
        groupBlockMaster.put("code", "Code");
        groupBlockMaster.put("statisticsCorrelationId", statisticsCcorrelationId);

        return groupBlockMaster;
    }

    private List<GroupBlockDetail> createExistingDetails(GroupBlockMaster master) {
        ArrayList<GroupBlockDetail> groupBlocks = new ArrayList<>();
        GroupBlockDetail blockDetail = new GroupBlockDetail();
        blockDetail.setBlocks(1);
        blockDetail.setOriginalBlocks(1);
        blockDetail.setPickup(1);
        blockDetail.setRate(BigDecimal.valueOf(123.45));
        blockDetail.setAccommodationType(new AccomTypeBuilder().withId(accomType1).buildData());
        blockDetail.setOccupancyDate(TODAY);
        blockDetail.setGroupBlockMaster(master);
        groupBlocks.add(blockDetail);
        GroupBlockDetail blockDetail2 = new GroupBlockDetail();
        blockDetail2.setBlocks(1);
        blockDetail2.setOriginalBlocks(1);
        blockDetail2.setPickup(1);
        blockDetail2.setRate(BigDecimal.valueOf(123.45));
        blockDetail2.setAccommodationType(new AccomTypeBuilder().withId(accomType2).buildData());
        blockDetail2.setOccupancyDate(TODAY);
        blockDetail2.setGroupBlockMaster(master);
        groupBlocks.add(blockDetail2);
        return groupBlocks;
    }

    private void verifyNewPace(PaceGroupBlock pace) {
        assertEquals(1, pace.getPickup().intValue());
        assertEquals(1, pace.getBlocks().intValue());
        assertEquals(1, pace.getOriginalBlocks().intValue());
        org.testng.Assert.assertEquals(123.45, pace.getRate().doubleValue());
    }

    private void verifyRemovePace(PaceGroupBlock pace) {
        assertEquals(0, pace.getPickup().intValue());
        assertEquals(0, pace.getBlocks().intValue());
        assertEquals(0, pace.getOriginalBlocks().intValue());
        org.testng.Assert.assertEquals(0.0, pace.getRate().doubleValue());
    }

    private List<GroupBlockMasterHolder> getGroupBlockMasterHolder(GroupBlockMaster master, List<GroupBlockDetail> groupBlocks) {
        List<GroupBlockMasterHolder> holders = new ArrayList<>();
        GroupBlockMasterHolder holder = new GroupBlockMasterHolder();
        holder.setGroupBlockMaster(master);
        holder.setNewBlockDetails(groupBlocks);
        holders.add(holder);
        return holders;
    }

    private List<PaceGroupBlock> assertMasterAndPacePersistence(int expectedPaceSize, int numberOfMasterSaveCalls) {
        List<PaceGroupBlock> paceList;
        if (expectedPaceSize > 0) {
            verify(paceGroupBlockUpdater).batchUpdate(paceCaptor.capture());
            paceList = new ArrayList(paceCaptor.getAllValues().get(0));
        } else {
            paceList = new ArrayList<>();
        }

        assertEquals(expectedPaceSize, paceList.size(), "Expected number of pace records does not match the actual invocations");

        for (int i = numberOfMasterSaveCalls; i < paceList.size(); i++) {
            assertTrue(paceList.get(i) != null, "Pace list at index " + i + " is was expected to be a PaceGroupBlock record");
        }
        return paceList;
    }

    public ArrayList<GroupBlockDetail> createNewDetails(GroupBlockMaster master) {
        ArrayList<GroupBlockDetail> groupBlocks = new ArrayList<>();
        GroupBlockDetail blockDetail = new GroupBlockDetail();
        blockDetail.setBlocks(1);
        blockDetail.setOriginalBlocks(1);
        blockDetail.setPickup(1);
        blockDetail.setRate(BigDecimal.valueOf(123.45));
        blockDetail.setAccommodationType(new AccomTypeBuilder().withId(accomType3).buildData());
        blockDetail.setOccupancyDate(TODAY);
        blockDetail.setGroupBlockMaster(master);
        groupBlocks.add(blockDetail);
        return groupBlocks;
    }

    public List<PaceGroupBlock> createAndAssignPaceGroupBlockListWithGroupBlockMaster(GroupBlockMaster groupBlockMaster, int count) {
        List<PaceGroupBlock> paceGroupBlockList = new ArrayList<>();
        int i = 0;
        for (; i < count; i++) {
            PaceGroupBlock paceGroupBlock = new PaceGroupBlock();
            paceGroupBlock.setId(groupBlockMaster);
            paceGroupBlock.setOccupancyDate(new Date());
            paceGroupBlock.setBusinessDayEndDate(new Date());
            paceGroupBlock.setBlocks(5);
            paceGroupBlock.setPickup(1);
            paceGroupBlockList.add(paceGroupBlock);
        }
        return paceGroupBlockList;
    }

    @Test
    public void correctPaceGroupBlockPastBookingDates(){
        paceGroupBlockService.correctPaceGroupBlockPastBookingDates();
        verify(tenantCrudService).executeUpdateByNamedQuery(PaceGroupBlock.CORRECT_PACE_GROUP_BLOCK);
    }
    @Test
    public void shouldCorrectPaceGroupBlock(){
        assertTrue(paceGroupBlockService.shouldCorrectPaceGroupBlock());
        System.setProperty("correct.pace.group.block.enabled", "false");
        assertFalse(paceGroupBlockService.shouldCorrectPaceGroupBlock());
    }
}