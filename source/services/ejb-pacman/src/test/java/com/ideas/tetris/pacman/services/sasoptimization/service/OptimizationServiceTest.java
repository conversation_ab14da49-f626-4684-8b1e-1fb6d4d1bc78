package com.ideas.tetris.pacman.services.sasoptimization.service;

import com.ideas.sas.core.SASRequestQueueActions;
import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.RequestHeaderType;
import com.ideas.tetris.pacman.services.analytics.services.AnalyticsConfigService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfiguration;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.contextholder.BusinessContext;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.utility.SASSQSUtility;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.pacman.services.webrate.service.WebrateDataSchedulingService;
import com.ideas.tetris.pacman.util.file.PacmanFileUtilService;
import com.ideas.tetris.pacman.util.jaxb.optimization.request.OptimizationRequestJAXBUtil;
import com.ideas.tetris.pacman.util.jaxb.optimization.response.SimplifiedOptimizationResponseJAXBUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.verification.VerificationMode;
import org.springframework.beans.factory.annotation.Qualifier;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.ideas.g3.test.AbstractG3JupiterTest.inject;
import static org.mockito.Mockito.*;
import static org.testng.Assert.assertEquals;

/*
 * Refactor to avoid copy/paste in mocks & verifies
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class  OptimizationServiceTest {
    private static final Integer DECISION_ID = 99;

    @InjectMocks
    private OptimizationService theService;
    @InjectMocks
    private OptimizationWindowService optimizationWindowService;

    @Mock
    @Qualifier("pacmanFileUtilService")
    private PacmanFileUtilService fileUtilService;
    @Mock
    DecisionService decisionService;
    @Mock
    protected BusinessContextService businessContextService;
    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;
    @Mock
    WebrateDataSchedulingService webrateDataSchedulingService;
    @Mock
    @SimplifiedOptimizationResponseJAXBUtil.SimplifiedOptimizationResponseQualifier
    protected JAXBUtilLocal responseJaxbUtil;

    @Mock
    protected PacmanConfigParamsService configService;
    @Mock
    protected SASClientService sasClientService;
    @Mock
    @OptimizationRequestJAXBUtil.OptimizationRequestQualifier
    protected JAXBUtilLocal jaxbUtil;
    @Mock
    protected DateService dateService;
    @Mock
    protected OptimizationServiceUtilityBean optServiceUtil;
    @Mock
    protected SASNodeLocator sasNodeDeterminer;
    @Mock
    protected TaxService taxService;
    @Mock
    protected ROAPropertyAttributeService roaPropertyAttributeService;
    @Mock
    protected PricingConfigurationService pricingConfigurationService;
    @Mock
    protected AnalyticsConfigService analyticsConfigService;
    @TempDir
    File outputFolder;
    private Tax tax = new Tax();

    @BeforeEach
    public void setUp() {
        inject(theService, "optimizationWindowService", optimizationWindowService);
        PacmanWorkContextTestHelper.setup_PuneProperty_WorkContext("1234");
        tax.setRoomTaxRate(BigDecimal.ONE);
    }

    private void setupCommonMocks() {
        when(configService.getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value())).thenReturn(true);
        when(roaPropertyAttributeService.getAttributeValueByAttributeName("OPT_OUTPUT_VERSION")).thenReturn("1");
        Date date = new Date();
        when(dateService.getCaughtUpDate()).thenReturn(date);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date);
        when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(true);
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        when(taxService.findTax()).thenReturn(tax);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        when(decisionService.createOndemandDecision()).thenReturn(decision);
        when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(anyString(), anyString(), anyString())).thenReturn(null);
        when(fileUtilService.createDirectoryIfNotExist(any(File.class), anyString())).thenReturn(outputFolder);
        when(configService.getParameterValue(PreProductionConfigParamName.USE_SAS_REQUEST_QUEUE_FOR)).thenReturn(SASRequestQueueActions.NONE.name());
    }

    private void verifyCommonMocks(int executTkTimes, VerificationMode buildTkVerificationTimes) {
        verify(configService).getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value());
        verify(roaPropertyAttributeService).getAttributeValueByAttributeName("OPT_OUTPUT_VERSION");
        verify(dateService).getCaughtUpDate();
        verify(dateService).getOptimizationWindowStartDate();
        verify(syncEventAggregatorService).isAnySystemComponentDirtyFlagTrue();
        verify(configService).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        verify(configService).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        verify(businessContextService).getCurrentBusinessContext();
        verify(configService).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        verify(syncEventAggregatorService).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        verify(decisionService).createOndemandDecision();
        verify(configService).isLRAEnabled();
        verify(taxService).findTax();
        verify(webrateDataSchedulingService).getAllWebrateShoppingConfigsForProperty();
        verify(decisionService).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        verify(sasClientService,times(executTkTimes)).executeTK(isNull(), isNull(), anyString());
        verify(sasClientService, buildTkVerificationTimes).buildTKRequestWithQueue(any(), any(), any());
    }

    @Test
    public void generalHappyPath() {
        setupCommonMocks();
        theService.execute(Constants.BDE);
        verifyCommonMocks(1, Mockito.never());
    }

    @Test
    public void generalHappyPathWithUseSasRequestForSetToLog() {
        setupCommonMocks();
        when(configService.getParameterValue(PreProductionConfigParamName.USE_SAS_REQUEST_QUEUE_FOR)).thenReturn(SASRequestQueueActions.LOG.name());
       try(MockedStatic<SASSQSUtility> sassqsUtilityMockedStatic = mockStatic(SASSQSUtility.class)) {
           sassqsUtilityMockedStatic.when(() -> SASSQSUtility.useSasQueue(SASRequestQueueActions.LOG.name())).thenReturn(true);
           sassqsUtilityMockedStatic.when(() -> SASSQSUtility.shouldExecuteUsingClientService(SASRequestQueueActions.LOG.name())).thenReturn(true);
           sassqsUtilityMockedStatic.when(() -> SASSQSUtility.sendRequestToQueue(any(), anyString(), any())).thenAnswer(invocation -> null);
           theService.execute(Constants.BDE);
           verifyCommonMocks(1, times(1));
           sassqsUtilityMockedStatic.verify(times(1),() -> SASSQSUtility.sendRequestToQueue(any(),anyString(),any()));
       }

    }

    @Test
    public void generalHappyPathWithUseSasRequestForSetToProcess() {
        setupCommonMocks();
        when(configService.getParameterValue(PreProductionConfigParamName.USE_SAS_REQUEST_QUEUE_FOR)).thenReturn(SASRequestQueueActions.PROCESS.name());

        try(MockedStatic<SASSQSUtility> sassqsUtilityMockedStatic = mockStatic(SASSQSUtility.class)) {
            sassqsUtilityMockedStatic.when(() -> SASSQSUtility.useSasQueue(SASRequestQueueActions.PROCESS.name())).thenReturn(true);
            sassqsUtilityMockedStatic.when(() -> SASSQSUtility.shouldExecuteUsingClientService(SASRequestQueueActions.PROCESS.name())).thenReturn(false);
            theService.execute(Constants.BDE);
            verifyCommonMocks(0, times(1));
            sassqsUtilityMockedStatic.verify(times(1),() -> SASSQSUtility.sendRequestToQueue(any(),anyString(),any()));
        }

    }

    @Test
    public void generalHappyPathWithUseSasRequestForSetToProcessWithError() {
        setupCommonMocks();
        when(configService.getParameterValue(PreProductionConfigParamName.USE_SAS_REQUEST_QUEUE_FOR)).thenReturn(SASRequestQueueActions.PROCESS.name());
      TetrisException  tetrisException = Assertions.assertThrows(TetrisException.class,()->theService.execute(Constants.BDE));
      assertEquals(tetrisException.getBaseMessage(),"Exception occurred when preparing Optimization Request");
    }


    @Test
    public void failureBeforeDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenThrow(new TetrisException("Catch This"));
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(true);
        when(roaPropertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.OPT_OUTPUT_VERSION.getAttributeName())).thenReturn("1");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        Mockito.when(taxService.findTax()).thenReturn(tax);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        Mockito.when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, 12)).thenReturn(decision); // Seems like 12 should be constant

        try {
            theService.execute(Constants.BDE);
            Assertions.fail("We should have blown");
        } catch (TetrisException e) {
            Assertions.assertEquals(ErrorCode.OPTIMIZATION_FAILED, e.getErrorCode());
        }

        verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.never()).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(roaPropertyAttributeService, never()).getAttributeValueByAttributeName(PropertyAttributeEnum.OPT_OUTPUT_VERSION.getAttributeName());
        Mockito.verify(configService, Mockito.never()).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.never()).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.never()).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.never()).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.never()).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.never()).createOndemandDecision();
        Mockito.verify(configService, Mockito.never()).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.never()).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.never()).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        Mockito.verify(sasClientService, Mockito.never()).executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        Mockito.verify(decisionService, Mockito.never()).updateDescisionProcessStatus(DECISION_ID, 12);
    }

    @Test
    public void failureAfterDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(true);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        when(roaPropertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.OPT_OUTPUT_VERSION.getAttributeName())).thenReturn("0");
        Mockito.when(taxService.findTax()).thenReturn(tax);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, 12)).thenReturn(decision); // Seems like 12 should be constant

        try {
            theService.execute(Constants.BDE);
            Assertions.fail("We should have blown");
        } catch (TetrisException e) {
            Assertions.assertEquals(ErrorCode.OPTIMIZATION_FAILED, e.getErrorCode());
        }

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(roaPropertyAttributeService).getAttributeValueByAttributeName(PropertyAttributeEnum.OPT_OUTPUT_VERSION.getAttributeName());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(taxService).findTax();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, 12);
    }

    @Test
    public void bdeAndVariableDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.TRUE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateBDEVariable()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDP()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDPVariable()).thenReturn(new Date());
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.BDE);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.times(1)).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDE();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateBDEVariable();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDP();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDPVariable();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void bdeAndNotVariableDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.FALSE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateBDEVariable()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDP()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDPVariable()).thenReturn(new Date());
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.BDE);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.never()).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.never()).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateBDE();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDEVariable();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDP();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDPVariable();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void cdpAndVariableDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.TRUE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateBDEVariable()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDP()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDPVariable()).thenReturn(new Date());
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        when(analyticsConfigService.getNextIdpWindowOverride()).thenReturn(-1);

        theService.execute(Constants.CDP);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.times(1)).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDE();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDEVariable();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateCDP();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateCDPVariable();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void cdpAndNotVariableDecision() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.FALSE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateBDEVariable()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDP()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDPVariable()).thenReturn(new Date());
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        when(analyticsConfigService.getNextIdpWindowOverride()).thenReturn(-1);

        theService.execute(Constants.CDP);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.never()).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.never()).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateBDE();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDEVariable();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateCDP();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDPVariable();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void nonBdeCdpOperationType() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.TRUE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateBDEVariable()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDP()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowEndDateCDPVariable()).thenReturn(new Date());
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.ON_DEMAND);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.never()).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.never()).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateBDE();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateBDEVariable();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDP();
        Mockito.verify(dateService, Mockito.never()).getOptimizationWindowEndDateCDPVariable();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        Mockito.verify(sasClientService).executeTK(Mockito.isNull(), Mockito.isNull(), Mockito.anyString());
    }
    @Test
    public void onDemandOperationTypeForSmartSync() {
        LocalDate caughtUpDate = LocalDate.of(2025, 10, 10);
        Date  smartSyncEndDate = new Date(2025, Calendar.DECEMBER, 15);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);

        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        System.setProperty("pacman.integration.variableDecisionWindow.enabled", Boolean.TRUE.toString());
        Mockito.when(configService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(false);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.executeInSmartSync(Constants.ON_DEMAND, smartSyncEndDate);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.never()).isApplyVaribaleDecisionWindow();
        Mockito.verify(configService, Mockito.never()).getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value());
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowEndDateBDE();

        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        Mockito.verify(sasClientService).executeTK(Mockito.isNull(), Mockito.isNull(), Mockito.anyString());
    }

    // Does not seem to affect anything until SAS but test existed pre ActiveVos removal
    @Test
    public void decisionRateTypeBarByDay() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(true);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.BDE);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    // Does not seem to affect anything until SAS but test existed pre ActiveVos removal
    @Test
    public void decisionRateTypeBarByLOS() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(true);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        // SIGNIFICANT APIs
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.BDE);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void createDecisionOnDemand() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(false);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        // SIGNIFICANT APIs
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.ON_DEMAND);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(decisionService, Mockito.never()).createBdeDecision();
        Mockito.verify(decisionService, Mockito.never()).createCdpDecision();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void createDecisionNullType() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(false);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        // SIGNIFICANT APIs
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(null);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.never()).createOndemandDecision();
        Mockito.verify(decisionService, Mockito.times(1)).createBdeDecision();
        Mockito.verify(decisionService, Mockito.never()).createCdpDecision();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void createDecisionBdeType() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(false);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        // SIGNIFICANT APIs
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute(Constants.BDE);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.never()).createOndemandDecision();
        Mockito.verify(decisionService, Mockito.times(1)).createBdeDecision();
        Mockito.verify(decisionService, Mockito.never()).createCdpDecision();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void createDecisionCdpType() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        Mockito.when(taxService.findTax()).thenReturn(tax);
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(false);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        // SIGNIFICANT APIs
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        when(analyticsConfigService.getNextIdpWindowOverride()).thenReturn(-1);

        theService.execute(Constants.CDP);

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        Mockito.verify(taxService).findTax();
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.never()).createOndemandDecision();
        Mockito.verify(decisionService, Mockito.never()).createBdeDecision();
        Mockito.verify(decisionService, Mockito.times(1)).createCdpDecision();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void createDecisionUnexpectedType() {
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new Date());
        Mockito.when(dateService.getOptimizationWindowStartDate()).thenReturn(new Date());
        Mockito.when(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue()).thenReturn(false);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("Orbitz");
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("7");
        BusinessContext businessContext = new BusinessContext();
        businessContext.setSingleBARDecisionEnabled(true);
        businessContext.setMasterRoomClassId(77);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        Mockito.when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(optServiceUtil.createRequestHeader()).thenReturn(new RequestHeaderType());
        // SIGNIFICANT APIs
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(false);
        CPConfiguration CPConfiguration = new CPConfiguration();
        Mockito.when(pricingConfigurationService.findCPConfiguration()).thenReturn(CPConfiguration);
        Decision decision = new Decision();
        decision.setId(DECISION_ID);
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        // SIGNIFICANT APIs
        Mockito.when(configService.isLRAEnabled()).thenReturn(true);
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = new WebrateShoppingConfig();
        webrateShoppingConfig1.setRollingDaysToShop(14);
        webrateShoppingConfig1.setWebrateShoppingThreshold(2);
        webrateShoppingConfig1.setWebrateShoppingFrequency(1);
        WebrateShoppingConfig webrateShoppingConfig2 = new WebrateShoppingConfig();
        webrateShoppingConfig2.setRollingDaysToShop(30);
        webrateShoppingConfig2.setWebrateShoppingThreshold(4);
        webrateShoppingConfig2.setWebrateShoppingFrequency(7);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);
        Mockito.when(decisionService.updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false)).thenReturn(decision);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        theService.execute("GREATGOOGLYMOOGLY");

        Mockito.verify(dateService, Mockito.times(1)).getCaughtUpDate();
        Mockito.verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isAnySystemComponentDirtyFlagTrue();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        Mockito.verify(businessContextService, Mockito.times(1)).getCurrentBusinessContext();
        Mockito.verify(configService, Mockito.times(1)).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        // SIGNIFICANT APIs
        Mockito.verify(syncEventAggregatorService, Mockito.times(1)).isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC);
        Mockito.verify(decisionService, Mockito.times(1)).createOndemandDecision();
        Mockito.verify(decisionService, Mockito.never()).createBdeDecision();
        Mockito.verify(decisionService, Mockito.never()).createCdpDecision();
        // SIGNIFICANT APIs
        Mockito.verify(configService, Mockito.times(1)).isLRAEnabled();
        Mockito.verify(webrateDataSchedulingService, Mockito.times(1)).getAllWebrateShoppingConfigsForProperty();
        Mockito.verify(decisionService, Mockito.times(1)).updateDescisionProcessStatus(DECISION_ID, ProcessStatus.SUBMITTED_TO_SAS, false);
        when(sasClientService.executeTK(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
    }
}
