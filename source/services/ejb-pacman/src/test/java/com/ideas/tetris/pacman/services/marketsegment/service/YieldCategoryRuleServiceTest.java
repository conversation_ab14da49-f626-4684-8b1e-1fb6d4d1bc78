package com.ideas.tetris.pacman.services.marketsegment.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class YieldCategoryRuleServiceTest extends AbstractG3JupiterTest {

    YieldCategoryRuleService instance = new YieldCategoryRuleService();

    @BeforeEach
    public void setUp() {
        try {
            setField(instance, YieldCategoryRuleService.class.getDeclaredField("crudService"), tenantCrudService());
        } catch (NoSuchFieldException e) {
            throw new TetrisException("Could not set field");
        }
    }

    @Test
    public void testDelete() {
        Collection<YieldCategoryRule> rules = tenantCrudService().save(getYieldCategoryRules());
        List<Integer> ids = new ArrayList<Integer>();
        ids.add(rules.iterator().next().getId());

        int howMany = instance.deleteYieldCategoryRules(ids);

        Collection<YieldCategoryRule> afterList = tenantCrudService().findAll(YieldCategoryRule.class);
        assertEquals(1, howMany);
        assertEquals(2, afterList.size());
    }

    @Test
    public void test_findAll() throws Exception {
        instance.findAll();
    }

    @Test
    public void test_retrieveTransactionSummaryTestResults() throws Exception {
        instance.retrieveTransactionSummaryTestResults();
    }

    @Test
    public void test_retrieveRuleSummaryTestResults() throws Exception {
        instance.retrieveRuleSummaryTestResults();
    }

    @Test
    public void test_retrieveMarketSummaryList() throws Exception {
        instance.retrieveMarketSummaryList();
    }

    @Test
    public void test_retrieveMarketSummaryListForGroup() throws Exception {
        instance.retrieveMarketSummaryListForGroup();
    }

    @Test
    public void test_findGroupForMarketSegment() throws Exception {
        instance.findGroupForMarketSegment(Arrays.asList("abc1"));
    }

    @Test
    public void returnsAllNonDefaultYCBRWithTheGivenMktCode() {
        List<String> mktSegCodes = Arrays.asList("MS1", "MS2");
        setUpYieldCategoryRules();

        List<YieldCategoryRule> ycbrRules = instance.getYCBRRules(mktSegCodes);

        assertEquals(2, ycbrRules.size());
        assertTrue(ycbrRules.stream().anyMatch(ycbr -> ycbr.getMarketCode().equalsIgnoreCase("MS1")));
        assertTrue(ycbrRules.stream().anyMatch(ycbr -> ycbr.getMarketCode().equalsIgnoreCase("MS2")));
        assertTrue(ycbrRules.stream().noneMatch(ycbr -> ycbr.getMarketCode().equalsIgnoreCase("MS")));
        assertTrue(ycbrRules.stream().noneMatch(ycbr -> ycbr.getMarketCode().equalsIgnoreCase("MC")));
    }

    @Test
    public void returnsEmptyListIfEmptyMktCodesArePassed() {
        List<YieldCategoryRule> yieldCategoryRules = instance.getYCBRRules(new ArrayList<>());

        assertTrue(yieldCategoryRules.isEmpty());
    }

    @Test
    public void getsMktCodesByGivenMarketCodesAndRateCodes() {
        List<String> mktSegCodes = Arrays.asList("MS1", "MS2");
        List<String> rateCodes = Arrays.asList("RC1", "RC2");
        setUpYieldCategoryRules();

        List<String> result = instance.getMktSegCodes(mktSegCodes, rateCodes);

        assertEquals(3, result.size());
        assertTrue(result.stream().noneMatch(mktCodes -> mktCodes.equalsIgnoreCase("MC")));
        assertTrue(result.stream().anyMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS")));
        assertTrue(result.stream().anyMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS1")));
        assertTrue(result.stream().anyMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS2")));
    }

    @Test
    public void returnsSameMktSegCodesWhenEmptyRateCodesArePassed() {
        List<String> mktSegCodes = Arrays.asList("MS1", "MS2");
        List<String> rateCodes = new ArrayList<>();
        setUpYieldCategoryRules();

        List<String> result = instance.getMktSegCodes(mktSegCodes, rateCodes);

        assertEquals(result, mktSegCodes);
    }

    @Test
    public void returnsMktCodesWithGivenRateCodesWhenEmptyMktSegCodesArePassed() {
        List<String> mktSegCodes = new ArrayList<>();
        List<String> rateCodes = Arrays.asList("RA", "R");
        setUpYieldCategoryRules();

        List<String> result = instance.getMktSegCodes(mktSegCodes, rateCodes);

        assertTrue(result.stream().anyMatch(mktCodes -> mktCodes.equalsIgnoreCase("MC")));
        assertTrue(result.stream().anyMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS2")));
        assertTrue(result.stream().noneMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS1")));
        assertTrue(result.stream().noneMatch(mktCodes -> mktCodes.equalsIgnoreCase("MS")));
    }

    @Test
    public void returnsEmptyListWhenEmptyRateCodesAndMktSegCodesArePassed() {
        List<String> mktSegCodes = new ArrayList<>();
        List<String> rateCodes = new ArrayList<>();
        setUpYieldCategoryRules();

        List<String> result = instance.getMktSegCodes(mktSegCodes, rateCodes);
        assertTrue(result.isEmpty());
    }

    private void setUpYieldCategoryRules() {
        List<YieldCategoryRule> yieldCategoryRules = new ArrayList<>();
        yieldCategoryRules.add(new YieldCategoryRule("MS1", "RC1", "MC_TEST", null, null, 100));
        yieldCategoryRules.add(new YieldCategoryRule("MS1", null, "MC_TEST", null, null, 999));
        yieldCategoryRules.add(new YieldCategoryRule("MC", "RA", "MC_TEST", null, null, 100));
        yieldCategoryRules.add(new YieldCategoryRule("MC", null, "MC_TEST", null, null, 999));
        yieldCategoryRules.add(new YieldCategoryRule("MS2", "R", "MC_TEST", null, null, 100));
        yieldCategoryRules.add(new YieldCategoryRule("MS2", null, "MC_TEST", null, null, 999));
        yieldCategoryRules.add(new YieldCategoryRule("MS", "RC2", "MC_TEST", null, null, 100));
        yieldCategoryRules.add(new YieldCategoryRule("MS", null, "MC_TEST", null, null, 999));
        tenantCrudService().save(yieldCategoryRules);
    }

    public List<YieldCategoryRule> getYieldCategoryRules() {
        List<YieldCategoryRule> yieldCategoryRules = new ArrayList<YieldCategoryRule>();
        yieldCategoryRules.add(getYieldCategoryRuleOne());
        yieldCategoryRules.add(getYieldCategoryRuleTwo());
        yieldCategoryRules.add(getYieldCategoryRuleThree());
        return yieldCategoryRules;
    }

    private YieldCategoryRule getYieldCategoryRuleOne() {
        YieldCategoryRule yieldCategoryRule = new YieldCategoryRule();
        yieldCategoryRule.setMarketCode("abc1");
        yieldCategoryRule.setAnalyticalMarketCode("abc1_ratecode");
        yieldCategoryRule.setBookingEndDate(new LocalDate());
        yieldCategoryRule.setBookingStartDate(new LocalDate());
        yieldCategoryRule.setRank(1);
        yieldCategoryRule.setRateCode("ratecode");
        return yieldCategoryRule;
    }

    private YieldCategoryRule getYieldCategoryRuleTwo() {
        YieldCategoryRule yieldCategoryRule = getYieldCategoryRuleOne();
        yieldCategoryRule.setMarketCode("abc2");

        yieldCategoryRule.setRank(2);
        yieldCategoryRule.setRateCode("ratecode1");
        yieldCategoryRule.setAnalyticalMarketCode("abc2_ratecode1");
        return yieldCategoryRule;
    }

    private YieldCategoryRule getYieldCategoryRuleThree() {
        YieldCategoryRule yieldCategoryRule = getYieldCategoryRuleOne();
        yieldCategoryRule.setMarketCode("abc3");

        yieldCategoryRule.setRank(2);
        yieldCategoryRule.setRateCode("ratecode");
        yieldCategoryRule.setAnalyticalMarketCode("abc3_ratecode");
        return yieldCategoryRule;
    }

}
