package com.ideas.tetris.pacman.services.benefits.repository;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyPropertyGroup;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyGroupCreator;
import com.ideas.tetris.pacman.testdatabuilder.ClientBuilder;
import com.ideas.tetris.pacman.testdatabuilder.PropertyBuilder;
import com.ideas.tetris.platform.services.daoandentities.entity.Benefits;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.platform.services.Stage.TWO_WAY;
import static com.ideas.tetris.platform.services.daoandentities.entity.Status.ACTIVE;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class BenefitMeasurementRepositoryPropertyGroupTest extends AbstractG3JupiterTest {

    @InjectMocks
    BenefitsMeasurementRepository repository;

    @BeforeEach
    public void setUp() {
        repository.globalCrudService = globalCrudService();
    }

    @Test
    public void getPropertyGroupName() {
        lets()
                .createClient()
                .createPropertyGroup()
                .getAndAssertPropertyGroupName();
    }

    @Test
    public void withOnePropertyTwoMonthsHavingNoData() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .addPropertiesToPropertyGroup("PROP1")
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(0);
    }

    @Test
    public void withOnePropertyTwoMonths() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .addPropertiesToPropertyGroup("PROP1")
                .createBenefits("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .createBenefits("PROP1", 2021, 2, 1500d, 180, 1800d, 198, 100)
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(2)
                .assertMonth(2021, 1, 2000d, 200, 2500d, 220, 100,
                        11d, 10d, 1d, 10d, 11d, 10d, 1d, 10d)
                .assertMonth(2021, 2, 1500d, 180, 1800d, 198, 100,
                        11d, 10d, 1d, 10d, 11d, 10d, 1d, 10d);
    }

    @Test
    public void withTwoPropertiesOneMonth() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1")
                .addPropertiesToPropertyGroup("PROP2")
                .createBenefits("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .createBenefits("PROP2", 2021, 1, 1500d, 180, 1800d, 198, 100)
                .getBenefits(2021, 1, 2021, 1)
                .assertForMonths(1)
                .assertMonth(2021, 1, 3500d, 380, 4300d, 418, 200, 22d, 20d, 2d, 10d, 22d, 20d, 2d, 10d);
    }

    @Test
    public void withTwoPropertiesTwoMonths() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1")
                .addPropertiesToPropertyGroup("PROP2")
                .createBenefits("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .createBenefits("PROP2", 2021, 1, 1500d, 180, 1800d, 202, 100)
                .createBenefits("PROP1", 2021, 2, 2400d, 230, 2780d, 220, 100)
                .createBenefits("PROP2", 2021, 2, 1700d, 196, 2010d, 200, 100)
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(2)
                .assertMonth(2021, 1, 3500d, 380, 4300d, 422, 200, 22d, 20d, 2d, 10d, 22d, 20d, 2d, 10d)
                .assertMonth(2021, 2, 4100d, 426, 4790d, 420, 200, 22d, 20d, 2d, 10d, 22d, 20d, 2d, 10d);
    }

    @Test
    public void withOnePropertyWithTwoMonthsAndSecondPropertyWithOneMonth() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1")
                .addPropertiesToPropertyGroup("PROP2")
                .createBenefits("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .createBenefits("PROP1", 2021, 2, 2400d, 230, 2780d, 220, 100)
                .createBenefits("PROP2", 2021, 2, 1700d, 196, 2010d, 200, 100)
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(2)
                .assertMonth(2021, 1, 2000d, 200, 2500d, 220, 100, 11d, 10d, 1d, 10d, 11d, 10d, 1d, 10d)
                .assertMonth(2021, 2, 4100d, 426, 4790d, 420, 200, 22d, 20d, 2d, 10d, 22d, 20d, 2d, 10d);
    }

    @Test
    void withOnePropertyWithTwoMonthsAndSecondPropertyWithOneMonthForProfitData() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1")
                .addPropertiesToPropertyGroup("PROP2")
                .createBenefits("PROP1", 2021, 1, 2000, 200, 2500, 220, 100, 4000, 3000)
                .createBenefits("PROP1", 2021, 2, 2400, 230, 2780, 220, 100, 5000, 4000)
                .createBenefits("PROP2", 2021, 2, 1700, 196, 2010, 200, 100, 3500, 2500)
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(2)
                .assertMonthDataWithProfitData(2021, 1, 2000, 200, 2500, 220, 100, 11, 10, 1, 10, 11, 10, 1, 10, 4000, 3000, 18.181818, 15, 40, 30, 33.333333, 21.21212, 33.333333)
                .assertMonthDataWithProfitData(2021, 2, 4100, 426, 4790, 420, 200, 22, 20, 2, 10, 22, 20, 2, 10, 8500, 6500, 20.238095, 15.258215, 42.5, 32.5, 30.76923, 32.637369, 30.76923);
    }

    @Test
    void shouldNotThrowExceptionForEmptyBenefitsForTwoPropertiesInPropertygroup() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1")
                .addPropertiesToPropertyGroup("PROP2")
                .createEmptyBenefits("PROP1", 2021, 1)
                .createEmptyBenefits("PROP2", 2021, 2)
                .getBenefits(2021, 1, 2021, 2)
                .assertForMonths(2);
    }

    @Test
    void getBenefitsForPropertiesUnderPropertyGroupForProfitData() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1", "PROP2")
                .createBenefits("PROP1", 2021, 1, 2000, 200, 2500, 220, 100, 4000, 3000)
                .createBenefits("PROP1", 2021, 2, 2400, 230, 2780, 220, 100, 5000, 4000)
                .createBenefits("PROP2", 2021, 2, 1700, 196, 2010, 200, 100, 3500, 2500)
                .getBenefitsForPropertiesUnderPropertyGroup(1, 2, 2021, 2021)
                .assertMonthForPropertyUnderPropertyGroupWithProfiitData("PROP1", 2021, 1, 2000, 200, 2500, 220, 100, 4000, 3000, 18.18182, 15, 40, 30, 33.33333, 21.21212, 33.33333)
                .assertMonthForPropertyUnderPropertyGroupWithProfiitData("PROP1", 2021, 2, 2400, 230, 2780, 220, 100, 5000, 4000, 22.72727, 17.3913, 50, 40, 25, 30.68182, 25)
                .assertMonthForPropertyUnderPropertyGroupWithProfiitData("PROP2", 2021, 2, 1700, 196, 2010, 200, 100, 3500, 2500, 17.5, 12.7551, 35, 25, 40, 37.2, 40);
    }

    @Test
    void shouldNotThrowExceptionForEmptyBenefitsForForPropertiesUnderPropertyGroup() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1", "PROP2")
                .createEmptyBenefits("PROP1", 2021, 1)
                .createEmptyBenefits("PROP1", 2021, 2)
                .createBenefits("PROP2", 2021, 2, 1700, 196, 2010, 200, 100, 3500, 2500)
                .getBenefitsForPropertiesUnderPropertyGroup(1, 2, 2021, 2021)
                .assertForPropertyUnderPropertyGroup(2);
    }

    @Test
    public void getBenefitsForPropertiesUnderPropertyGroup() {
        lets()
                .createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1", "PROP2")
                .createBenefits("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .createBenefits("PROP1", 2021, 2, 2400d, 230, 2780d, 220, 100)
                .createBenefits("PROP2", 2021, 2, 1700d, 196, 2010d, 200, 100)
                .getBenefitsForPropertiesUnderPropertyGroup(1, 2, 2021, 2021)
                .assertMonthForPropertyUnderPropertyGroup("PROP1", 2021, 1, 2000d, 200, 2500d, 220, 100)
                .assertMonthForPropertyUnderPropertyGroup("PROP1", 2021, 2, 2400d, 230, 2780d, 220, 100)
                .assertMonthForPropertyUnderPropertyGroup("PROP2", 2021, 2, 1700d, 196, 2010d, 200, 100);
    }


    @Test
    public void getContextsForPropertyGroup() {
        lets().createClient()
                .createPropertyGroup()
                .createProperty("PROP1")
                .createProperty("PROP2")
                .addPropertiesToPropertyGroup("PROP1", "PROP2")
                .getContextsForPropertyGroup()
                .assertContexts("PROP1", "PROP2");
    }


    private Context lets() {
        return new Context();
    }

    private class Context {

        private Client client;
        private PropertyGroup propertyGroup;
        private HashMap<String, Property> properties = new HashMap<>();
        private Map<String, Benefits> result = new HashMap<>();
        private Map<Integer, Map<String, Benefits>> benefitsForPropertiesUnderPropertyGroup;
        private Map<Integer, String> contextsForProperties;

        Context createClient() {
            client = globalCrudService().save(ClientBuilder.buildDefaultActiveClient());
            return this;
        }

        Context createProperty(String propertyCode) {
            Property property = globalCrudService().save(PropertyBuilder.createActiveProperty(client, propertyCode, propertyCode, TWO_WAY));
            properties.put(propertyCode, property);
            return this;
        }

        Context createPropertyGroup() {
            propertyGroup = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), client.getId());
            PacmanWorkContextHelper.setPropertyGroupId(propertyGroup.getId());
            PacmanWorkContextHelper.setPropertyId(null);
            return this;
        }

        Context addPropertiesToPropertyGroup(String... propertyCodes) {
            Arrays.stream(propertyCodes).forEach(code -> {
                propertyGroup.getPropertyPropertyGroups().add(new PropertyPropertyGroup(properties.get(code), propertyGroup, ACTIVE));
            });
            globalCrudService().save(propertyGroup);
            return this;
        }


        public Context createBenefits(String code, Integer year, Integer month,
                                      double heuristicRevenue, int heuristicOccupancy,
                                      double actualRevenue, int actualOccupancy,
                                      int capacity) {
            return createBenefits(code, year, month, heuristicRevenue, heuristicOccupancy, actualRevenue, actualOccupancy, capacity, 10, 10);
        }

        public Context createBenefits(String code, Integer year, Integer month, double heuristicRevenue, int heuristicOccupancy,
                                      double actualRevenue, int actualOccupancy, int capacity, double actualProfit, double heuristicProfit) {

            Benefits benefits = getBenefits(code, year, month, heuristicRevenue, heuristicOccupancy, actualRevenue, actualOccupancy, capacity);
            double actualProPOR = actualProfit / actualOccupancy;
            double heuristicProPOR = heuristicProfit / heuristicOccupancy;
            double actualProPAR = actualProfit / capacity;
            double heuristicProPAR = heuristicProfit / capacity;

            benefits.setActualProfit(BigDecimal.valueOf(actualProfit));
            benefits.setHeuristicProfit(BigDecimal.valueOf(heuristicProfit));
            benefits.setActualProPOR(BigDecimal.valueOf(actualProPOR));
            benefits.setHeuristicProPOR(BigDecimal.valueOf(heuristicProPOR));
            benefits.setActualProPAR(BigDecimal.valueOf(actualProPAR));
            benefits.setHeuristicProPAR(BigDecimal.valueOf(heuristicProPAR));
            benefits.setBenefitProfitInPercent(BigDecimal.valueOf((actualProfit - heuristicProfit) * 100 / heuristicProfit));
            benefits.setBenefitProPORInPercent(BigDecimal.valueOf((actualProPOR - heuristicProPOR) * 100 / heuristicProPOR));
            benefits.setBenefitProPARInPercent(BigDecimal.valueOf((actualProPAR - heuristicProPAR) * 100 / heuristicProPAR));
            globalCrudService().save(benefits);
            return this;
        }

        private Context createEmptyBenefits(String code, Integer year, Integer month) {
            Benefits benefits = new Benefits();
            benefits.setPropertyId(properties.get(code).getId());
            benefits.setMonth(month);
            benefits.setYear(year);
            globalCrudService().save(benefits);
            return this;
        }

        private Benefits getBenefits(String code, Integer year, Integer month, double heuristicRevenue, int heuristicOccupancy, double actualRevenue, int actualOccupancy, int capacity) {
            Benefits benefits = new Benefits();
            benefits.setPropertyId(properties.get(code).getId());
            benefits.setMonth(month);
            benefits.setYear(year);
            benefits.setHeuristicOccupancy(heuristicOccupancy);
            benefits.setHeuristicRevenue(BigDecimal.valueOf(heuristicRevenue));
            benefits.setHeuristicAdr(BigDecimal.valueOf(heuristicRevenue / heuristicOccupancy));
            benefits.setHeuristicRevpar(BigDecimal.valueOf(heuristicRevenue / capacity));
            benefits.setActualOccupancy(actualOccupancy);
            benefits.setActualRevenue(BigDecimal.valueOf(actualRevenue));
            benefits.setActualAdr(BigDecimal.valueOf(actualRevenue / actualOccupancy));
            benefits.setActualRevpar(BigDecimal.valueOf(actualRevenue / capacity));
            benefits.setBenefitRevenue(BigDecimal.valueOf(
                    ((actualRevenue * 100 / heuristicRevenue) - 100)
            ));
            benefits.setBenefitOccupancy(BigDecimal.valueOf(
                    ((actualOccupancy * 100 / heuristicOccupancy) - 100)
            ));
            benefits.setTransientHeuristicOccupancy(0);
            benefits.setTransientHeuristicRevenue(ZERO);
            benefits.setTransientHeuristicAdr(ZERO);
            benefits.setTransientActualOccupancy(0);
            benefits.setTransientActualRevenue(ZERO);
            benefits.setTransientActualAdr(ZERO);
            benefits.setTransientBenefitRevenue(ZERO);
            benefits.setTransientBenefitOccupancy(ZERO);
            benefits.setGroupHeuristicOccupancy(0);
            benefits.setGroupHeuristicRevenue(ZERO);
            benefits.setGroupHeuristicAdr(ZERO);
            benefits.setGroupActualOccupancy(0);
            benefits.setGroupActualRevenue(ZERO);
            benefits.setGroupActualAdr(ZERO);
            benefits.setGroupBenefitRevenue(ZERO);
            benefits.setGroupBenefitOccupancy(ZERO);
            benefits.setCapacity(capacity);
            benefits.setAncillaryRevenue(new BigDecimal(11));
            benefits.setAncillaryRevenueWithoutRms(BigDecimal.TEN);
            benefits.setAncillaryRevenueGain(ONE);
            benefits.setAncillaryRevenueGainInPercent(BigDecimal.TEN);
            benefits.setAncillaryProfit(new BigDecimal(11));
            benefits.setAncillaryProfitWithoutRms(BigDecimal.TEN);
            benefits.setAncillaryProfitGain(ONE);
            benefits.setAncillaryProfitGainInPercentage(BigDecimal.TEN);
            return benefits;
        }

        public Context getBenefits(int startYear, int startMonth, int endYear, int endMonth) {
            List<Benefits> benefits = repository.getBenefits(startMonth, endMonth, startYear, endYear);
            benefits.stream().forEach(b -> this.result.put(b.getYear() + "" + b.getMonth(), b));
            return this;
        }

        public Context getBenefitsForPropertiesUnderPropertyGroup(Integer startMonthIndex, Integer endMonthIndex, Integer startYear, Integer endYear) {
            benefitsForPropertiesUnderPropertyGroup = new HashMap<>();
            Map<Integer, List<Benefits>> result = repository.getBenefitsForPropertiesUnderPropertyGroup(
                    propertyGroup.getId(), startMonthIndex, endMonthIndex, startYear, endYear
            );
            result.entrySet().stream()
                    .forEach(entry -> {
                        entry.getValue().stream()
                                .forEach(b -> benefitsForPropertiesUnderPropertyGroup.computeIfAbsent(entry.getKey(), i -> new HashMap<>())
                                        .put(b.getYear() + "" + b.getMonth(), b));
                    });
            return this;
        }

        public Context assertForMonths(int months) {
            assertNotNull(result);
            assertEquals(months, result.size());
            return this;
        }

        public Context assertMonth(int year, int month, double heuristicRevenue, int heuristicOccupancy,
                                   double actualRevenue, int actualOccupancy, int capacity, double ancillaryRevenueActual, double ancillaryRevenueWithoutRms,
                                   double ancillaryRevenueGain, double ancillaryRevenueGainPercentage, double ancillaryProfitActual, double ancillaryProfitWithoutRms,
                                   double ancillaryProfitGain, double ancillaryProfitGainPercentage) {
            String key = year + "" + month;
            assertTrue(result.containsKey(key));
            Benefits benefits = result.get(key);
            assertEquals(heuristicRevenue, benefits.getHeuristicRevenue().doubleValue());
            assertEquals(heuristicOccupancy, benefits.getHeuristicOccupancy());
            assertEquals(actualRevenue, benefits.getActualRevenue().doubleValue());
            assertEquals(actualOccupancy, benefits.getActualOccupancy());
            assertEquals(capacity, benefits.getCapacity());
            assertEquals(ancillaryRevenueActual, benefits.getAncillaryRevenue().doubleValue());
            assertEquals(ancillaryRevenueGain, benefits.getAncillaryRevenueGain().doubleValue());
            assertEquals(ancillaryRevenueWithoutRms, benefits.getAncillaryRevenueWithoutRms().doubleValue());
            assertEquals(ancillaryRevenueGainPercentage, benefits.getAncillaryRevenueGainInPercent().doubleValue());

            assertEquals(ancillaryProfitActual, benefits.getAncillaryProfit().doubleValue());
            assertEquals(ancillaryProfitGain, benefits.getAncillaryProfitGain().doubleValue());
            assertEquals(ancillaryProfitGainPercentage, benefits.getAncillaryProfitGainInPercentage().doubleValue());
            assertEquals(ancillaryProfitWithoutRms, benefits.getAncillaryProfitWithoutRms().doubleValue());
            return this;
        }

        public Context assertMonthForPropertyUnderPropertyGroup(String propertyCode, int year, int month, double heuristicRevenue, int heuristicOccupancy,
                                                                double actualRevenue, int actualOccupancy, int capacity) {
            assertNotNull(benefitsForPropertiesUnderPropertyGroup);
            assertFalse(benefitsForPropertiesUnderPropertyGroup.isEmpty());
            assertTrue(benefitsForPropertiesUnderPropertyGroup.containsKey(properties.get(propertyCode).getId()));
            Map<String, Benefits> propertyBenefits = benefitsForPropertiesUnderPropertyGroup.get(properties.get(propertyCode).getId());
            String key = year + "" + month;
            assertTrue(propertyBenefits.containsKey(key));
            Benefits benefits = propertyBenefits.get(key);
            assertEquals(heuristicRevenue, benefits.getHeuristicRevenue().doubleValue());
            assertEquals(heuristicOccupancy, benefits.getHeuristicOccupancy());
            assertEquals(actualRevenue, benefits.getActualRevenue().doubleValue());
            assertEquals(actualOccupancy, benefits.getActualOccupancy());
            assertEquals(capacity, benefits.getCapacity());
            return this;
        }

        public Context assertForPropertyUnderPropertyGroup(int propertyCount) {
            assertNotNull(benefitsForPropertiesUnderPropertyGroup);
            assertEquals(propertyCount, benefitsForPropertiesUnderPropertyGroup.size());
            return this;
        }

        public Context assertMonthDataWithProfitData(int year, int month, double heuristicRevenue, int heuristicOccupancy,
                                                     double actualRevenue, int actualOccupancy, int capacity, double ancillaryRevenueActual,
                                                     double ancillaryRevenueWithoutRms, double ancillaryRevenueGain, double ancillaryRevenueGainPercentage,
                                                     double ancillaryProfitActual, double ancillaryProfitWithoutRms, double ancillaryProfitGain,
                                                     double ancillaryProfitGainPercentage, double actualProfit, double heuristicProfit,
                                                     double actualProPOR, double heuristicProPOR, double actualProPAR, double heuristicProPAR,
                                                     double profitBenefit, double proPORBenefit, double proPARBenefit) {

            assertMonth(year, month, heuristicRevenue, heuristicOccupancy, actualRevenue, actualOccupancy, capacity,
                    ancillaryRevenueActual, ancillaryRevenueWithoutRms, ancillaryRevenueGain, ancillaryRevenueGainPercentage,
                    ancillaryProfitActual, ancillaryProfitWithoutRms, ancillaryProfitGain, ancillaryProfitGainPercentage);
            assertProfitMonthData(actualProfit, heuristicProfit, actualProPOR, heuristicProPOR, actualProPAR, heuristicProPAR,
                    profitBenefit, proPORBenefit, proPARBenefit, result.get(year + "" + month));
            return this;
        }

        public Context assertMonthForPropertyUnderPropertyGroupWithProfiitData(String propertyCode, int year, int month, double heuristicRevenue, int heuristicOccupancy,
                                                                               double actualRevenue, int actualOccupancy, int capacity, double actualProfit, double heuristicProfit,
                                                                               double actualProPOR, double heuristicProPOR, double actualProPAR, double heuristicProPAR,
                                                                               double profitBenefit, double proPORBenefit, double proPARBenefit) {
            assertMonthForPropertyUnderPropertyGroup(propertyCode, year, month, heuristicRevenue, heuristicOccupancy, actualRevenue, actualOccupancy, capacity);
            Map<String, Benefits> propertyBenefits = benefitsForPropertiesUnderPropertyGroup.get(properties.get(propertyCode).getId());
            assertProfitMonthData(actualProfit, heuristicProfit, actualProPOR, heuristicProPOR, actualProPAR, heuristicProPAR, profitBenefit, proPORBenefit, proPARBenefit, propertyBenefits.get(year + "" + month));
            return this;
        }

        private void assertProfitMonthData(double actualProfit, double heuristicProfit, double actualProPOR, double heuristicProPOR, double actualProPAR, double heuristicProPAR, double profitBenefit, double proPORBenefit, double proPARBenefit, Benefits benefits) {
            assertEquals(actualProfit, benefits.getActualProfit().doubleValue());
            assertEquals(heuristicProfit, benefits.getHeuristicProfit().doubleValue());
            assertEquals(actualProPOR, benefits.getActualProPOR().doubleValue());
            assertEquals(heuristicProPOR, benefits.getHeuristicProPOR().doubleValue());
            assertEquals(actualProPAR, benefits.getActualProPAR().doubleValue());
            assertEquals(heuristicProPAR, benefits.getHeuristicProPAR().doubleValue());
            assertEquals(profitBenefit, benefits.getBenefitProfitInPercent().doubleValue());
            assertEquals(proPORBenefit, benefits.getBenefitProPORInPercent().doubleValue());
            assertEquals(proPARBenefit, benefits.getBenefitProPARInPercent().doubleValue());
        }

        public Context getAndAssertPropertyGroupName() {
            String propertyGroupName = repository.getPropertyGroupName(propertyGroup.getId());
            assertEquals(propertyGroup.getName(), propertyGroupName);
            return this;
        }

        public Context getContextsForPropertyGroup() {
            contextsForProperties = repository.getContextsForPropertyGroup(propertyGroup.getId());
            return this;
        }

        public BenefitMeasurementRepositoryPropertyGroupTest.Context assertContexts(String... propertyCodes) {
            assertNotNull(contextsForProperties);
            Arrays.asList(propertyCodes).forEach(propertyCode -> assertTrue(contextsForProperties.containsKey(properties.get(propertyCode).getId())));
            Arrays.asList(propertyCodes).forEach(propertyCode -> {
                Property prop = properties.get(propertyCode);
                assertEquals(prop.getClient().getCode() + "," + prop.getCode(), contextsForProperties.get((prop.getId())));
            });
            return this;
        }
    }
}
