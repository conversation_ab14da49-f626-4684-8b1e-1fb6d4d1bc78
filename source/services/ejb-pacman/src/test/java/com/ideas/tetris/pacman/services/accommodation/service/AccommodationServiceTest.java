package com.ideas.tetris.pacman.services.accommodation.service;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeADR;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeDto;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomClassSummary;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.accommodation.dto.RoomTypeDto;
import com.ideas.tetris.pacman.services.accommodation.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.OldRoomClassConfigDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.centralrms.CentralRmsService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.dao.UniquePropertyCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkDefault;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkSeason;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.AccomConfigAlertComponent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.extendedstay.common.entity.ExtendedStayProductDefinition;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertCreationService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.service.AsyncInformationManagerCleanupService;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupDetailsCreator;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.opera.OperaOccupancySummary;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.outoforderoverride.entity.OutOfOrderOverride;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomSeason;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.*;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.*;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE;
import static java.util.List.of;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


public class AccommodationServiceTest extends AbstractG3JupiterTest {

    private static final String RT_K = "K";

    private static final String DELUXE = "Deluxe";

    private static final Integer ZERO = new Integer(0);

    private static final Integer ONE = new Integer(1);

    private static final Integer TWO = new Integer(2);

    private static final Integer THREE = new Integer(3);

    private static final Integer FORTY = new Integer(40);

    private InvalidateOverridesService invalidateOverridesService;

    private OverbookingOverrideService overbookingOverrideService;

    private SyncEventAggregatorService syncEventAggregatorService;

    private AccomConfigAlertComponent accomConfigAlertSyncComponent;

    private DateService dateService;

    private LDBService ldbService;

    private AlertCreationService alertCreationService;

    private PacmanConfigParamsService configParamService = null;

    private TenantProperty property;

    private AccomClass standardAccomClass;

    private AccomClass deluxeAccomClass;

    private AccomType stdKingRoom;

    private AccomType ROHRoom;

    private AccomType stdQueenRoom;

    private AccomType testQueenRoom;

    private AccomClassSharingGroup accomClassSharingGroup;

    private AccomClassInventorySharing accomClassInventorySharing;

    private InformationManagerCleanupService informationManagerCleanupService;

    private PricingConfigurationService pricingConfigurationService;

    private GroupPricingConfigurationService groupPricingConfigurationService;

    private AccomClassPriceRankService accomClassPriceRankService;

    private CloseHighestBarService closeHighestBarService;

    private AlertService alertService;

    private AccommodationMappingService accommodationMappingService;

    private CentralRmsService centralRmsService;

    private AccommodationService service;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        service = new AccommodationService();
        service.setTenantCrudService(tenantCrudService());
        property = new TenantProperty();
        property.setId(-24);
        property.setCode("NEG24");
        property.setName("Negative 24");
        property.setClientCode(TestClient.BSTN.name().toLowerCase());
        tenantCrudService().delete(TenantProperty.class, property.getId());
        property.setStatus(Status.ACTIVE);
        property = tenantCrudService().save(property);
        standardAccomClass = createStandardAccomClass();
        deluxeAccomClass = createDeluxeAccomClass();
        stdKingRoom = createNewAccomType("SKR", standardAccomClass, "Standard King Room", 100, 1, 0);
        stdQueenRoom = createNewAccomType("SDQ", standardAccomClass, "Two Queen Beds", 50, 1, 0);
        accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Standard to Deluxe Sharing");
        accomClassSharingGroup.setDescription("Standard to Deluxe Sharing Description");
        accomClassSharingGroup.setPropertyId(property.getId());
        accomClassInventorySharing = new AccomClassInventorySharing();
        accomClassInventorySharing.setAccomClassSharingGrp(accomClassSharingGroup);
        accomClassInventorySharing.setAccomClass(standardAccomClass);
        accomClassInventorySharing.setSharedAccomClass(deluxeAccomClass);
        accomClassInventorySharing.setSharedRoomCount(10);
        accomClassInventorySharing.setRank(1);
        tenantCrudService().save(new AccomClassPriceRank(deluxeAccomClass, standardAccomClass, true));
        List<AccomClassInventorySharing> accomClassInventorySharings = new ArrayList<>();
        accomClassInventorySharings.add(accomClassInventorySharing);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInventorySharings);
        InventorySharingRank inventorySharingRank = new InventorySharingRank();
        inventorySharingRank.setAccomClassSharingGrp(accomClassSharingGroup);
        inventorySharingRank.setAccomClass(standardAccomClass);
        inventorySharingRank.setRank(1);
        List<InventorySharingRank> inventorySharingRanks = new ArrayList<>();
        inventorySharingRanks.add(inventorySharingRank);
        accomClassSharingGroup.setInventorySharingRanks(inventorySharingRanks);
        accomClassSharingGroup = tenantCrudService().save(accomClassSharingGroup);
        createUnqualifiedRate();
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(property.getId());
        workContext.setPropertyCode(property.getCode());
        workContext.setClientCode("Greenbar");
        PacmanWorkContextHelper.setWorkContext(workContext);
        invalidateOverridesService = mock(InvalidateOverridesService.class, withSettings().lenient());
        service.setInvalidateOverridesService(invalidateOverridesService);
        overbookingOverrideService = mock(OverbookingOverrideService.class);
        service.setOverbookingOverrideService(overbookingOverrideService);
        syncEventAggregatorService = mock(SyncEventAggregatorService.class, withSettings().lenient());
        service.setSyncEventAggregatorService(syncEventAggregatorService);
        dateService = mock(DateService.class, withSettings().lenient());
        service.setDateService(dateService);
        ldbService = mock(LDBService.class);
        service.setLDBService(ldbService);
        alertCreationService = mock(AlertCreationService.class);
        service.setAlertCreationService(alertCreationService);
        configParamService = mock(PacmanConfigParamsService.class, withSettings().lenient());
        service.setConfigParamsService(configParamService);
        pricingConfigurationService = mock(PricingConfigurationService.class);
        service.setPricingConfigurationService(pricingConfigurationService);
        accomConfigAlertSyncComponent = new AccomConfigAlertComponent();
        alertService = new AlertService();
        accomConfigAlertSyncComponent.setAlertService(alertService);
        service.setAccomConfigSyncComponent(accomConfigAlertSyncComponent);
        groupPricingConfigurationService = mock(GroupPricingConfigurationService.class);
        service.setGroupPricingCOnfigurationService(groupPricingConfigurationService);
        informationManagerCleanupService = new InformationManagerCleanupService();
        informationManagerCleanupService.multiPropertyCrudService = multiPropertyCrudService();
        accommodationMappingService = mock(AccommodationMappingService.class);
        service.setAccommodationMappingService(accommodationMappingService);
        final AsyncInformationManagerCleanupService asyncInformationManagerCleanupService = new AsyncInformationManagerCleanupService();
        inject(asyncInformationManagerCleanupService, "informationManagerCleanupService", informationManagerCleanupService);
        inject(service, "asyncInformationManagerCleanupService", asyncInformationManagerCleanupService);
        accomClassPriceRankService = mock(AccomClassPriceRankService.class);
        service.setAccomClassPriceRankService(accomClassPriceRankService);
        centralRmsService = mock(CentralRmsService.class);
        service.setCentralRmsService(centralRmsService);
        closeHighestBarService = mock(CloseHighestBarService.class);
        inject(service, "closeHighestBarService", closeHighestBarService);
        flushAndClear();
    }

    private void createUnqualifiedRate() {
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setFileMetadataId(1);
        rateUnqualified.setName("Unassigned");
        rateUnqualified.setDescription("Unassigned");
        rateUnqualified.setStatusId(Constants.ACTIVE_STATUS_ID);
        rateUnqualified.setPropertyId(property.getId());
        rateUnqualified.setSystemDefault(0);
        Date now = new Date();
        rateUnqualified.setStartDate(DateUtil.addDaysToDate(now, -10));
        rateUnqualified.setEndDate(DateUtil.addDaysToDate(now, 10));
        rateUnqualified.setYieldable(1);
        rateUnqualified.setPriceRelative(0);
        rateUnqualified.setDerivedRateCode("0");
        rateUnqualified.setCurrency("USD");
        rateUnqualified.setIncludesPackage(0);
        rateUnqualified.setRanking(0);
        tenantCrudService().save(rateUnqualified);
    }

    private AccomClass createDeluxeAccomClass() {
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setPropertyId(property.getId());
        deluxeAccomClass.setCode("DLX");
        deluxeAccomClass.setName(DELUXE);
        deluxeAccomClass.setMasterClass(1);
        deluxeAccomClass.setDescription("Deluxe Room");
        deluxeAccomClass.setStatusId(1);
        deluxeAccomClass.setViewOrder(1);
        deluxeAccomClass.setRankOrder(2);
        deluxeAccomClass.setSystemDefault(0);
        deluxeAccomClass.setAccomTypes(new HashSet<>());
        deluxeAccomClass = tenantCrudService().save(deluxeAccomClass);
        return deluxeAccomClass;
    }

    private AccomClass createUnAssignedAccomClass() {
        AccomClass unassigned = new AccomClass();
        unassigned.setPropertyId(property.getId());
        unassigned.setCode(Constants.UNASSIGNED);
        unassigned.setName(Constants.UNASSIGNED);
        unassigned.setMasterClass(0);
        unassigned.setDescription(Constants.UNASSIGNED);
        unassigned.setStatusId(1);
        unassigned.setViewOrder(1);
        unassigned.setRankOrder(2);
        unassigned.setSystemDefault(0);
        unassigned.setAccomTypes(new HashSet<>());
        unassigned = tenantCrudService().save(unassigned);
        return unassigned;
    }

    private AccomClass createStandardAccomClass() {
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setPropertyId(property.getId());
        standardAccomClass.setCode("STD");
        standardAccomClass.setName("Standard");
        standardAccomClass.setMasterClass(0);
        standardAccomClass.setDescription("Standard Room");
        standardAccomClass.setStatusId(1);
        standardAccomClass.setViewOrder(2);
        standardAccomClass.setRankOrder(1);
        standardAccomClass.setSystemDefault(1);
        standardAccomClass.setAccomTypes(new HashSet<>());
        standardAccomClass = tenantCrudService().save(standardAccomClass);
        return standardAccomClass;
    }

    private AccomType createNewAccomType(String code, AccomClass accomClass, String name, int accomTypeCapacity, int statusId, int rohType) {
        AccomType accomType = new AccomType();
        accomType.setPropertyId(property.getId());
        accomType.setAccomTypeCode(code);
        accomType.setAccomClass(accomClass);
        accomClass.getAccomTypes().add(accomType);
        accomType.setName(name);
        accomType.setDescription(name);
        accomType.setAccomTypeCapacity(accomTypeCapacity);
        accomType.setStatusId(statusId);
        accomType.setRohType(rohType);
        accomType.setSystemDefault(0);
        accomType.setIsComponentRoom("N");
        accomType = tenantCrudService().save(accomType);
        return accomType;
    }

    @Test
    public void getAllAccomTypesWithZeroCapacity() {
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set accom_type_capacity=0 where accom_type_id = 4");
        List<String> allAccomTypesWithZeroCapacity = service.getAllAccomTypesWithZeroCapacity();
        assertEquals(1, allAccomTypesWithZeroCapacity.size());
        assertEquals(allAccomTypesWithZeroCapacity.get(0), "DLX");
    }

    @Test
    public void getAllInactiveAccomTypes() {
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set Status_ID=2,Display_Status_ID=2  where accom_type_id = 4");
        List<Integer> inactiveAccomTypes = service.getInactiveRoomTypes();
        assertEquals(1, inactiveAccomTypes.size());
        assertEquals(inactiveAccomTypes.get(0), 4);
    }

    @Test
    public void testAccomClassCapacityRatios() {
        List<AccomClass> roomClassCapacityRatio = service.getRoomClassCapacityRatio();
        assertEquals(4, roomClassCapacityRatio.size());
        AccomClass accomClass = roomClassCapacityRatio.get(0);
        assertEquals(150, accomClass.getCapacity().intValue());
        assertEquals(1.000000d, accomClass.getCapacityRatio().doubleValue(), 0.0);
        accomClass = roomClassCapacityRatio.get(1);
        assertEquals(2, accomClass.getId().intValue());
        assertEquals(120, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.727272), accomClass.getCapacityRatio());
        accomClass = roomClassCapacityRatio.get(2);
        assertEquals(3, accomClass.getId().intValue());
        assertEquals(25, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.151515), accomClass.getCapacityRatio());
        accomClass = roomClassCapacityRatio.get(3);
        assertEquals(4, accomClass.getId().intValue());
        assertEquals(20, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.121212), accomClass.getCapacityRatio());
    }

    @Test
    public void testAccomClassCapacityRatiosForSelectedNonZeroCapacityAccomTypes() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "sometestcode", "sometestcode", 0, "N");
        List<AccomClass> roomClassCapacityRatio = service.getRoomClassCapacityRatioForAccomTypes(Arrays.asList(stdKingRoom.getId(), accomType.getId()));
        assertEquals(1, roomClassCapacityRatio.size());
        assertEquals(new BigDecimal("1.00"), roomClassCapacityRatio.get(0).getCapacityRatio().setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void testAccomClassCapacityRatiosForSelectedAccomTypes() {
        createTestQueenRoomWithDeluxAccomClass();
        List<AccomClass> roomClassCapacityRatio = service.getRoomClassCapacityRatioForAccomTypes(Arrays.asList(stdKingRoom.getId(), testQueenRoom.getId()));
        assertEquals(2, roomClassCapacityRatio.size());
        assertEquals(new BigDecimal("0.72"), roomClassCapacityRatio.get(0).getCapacityRatio().setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("0.29"), roomClassCapacityRatio.get(1).getCapacityRatio().setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void accomClassCapacityForAccomTypeShouldReturnOneForSingleAccomType() {
        List<AccomClass> roomClassCapacityRatio = service.getRoomClassCapacityRatioForAccomTypes(Arrays.asList(stdKingRoom.getId()));
        assertEquals(1, roomClassCapacityRatio.size());
        assertEquals(new BigDecimal("1.00"), roomClassCapacityRatio.get(0).getCapacityRatio().setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void getAccomClassById() {
        AccomClass foundAccomClass = service.getAccomClassById(deluxeAccomClass.getId());
        assertEquals(deluxeAccomClass.getId(), foundAccomClass.getId());
        assertEquals(deluxeAccomClass.getName(), foundAccomClass.getName());
        assertEquals(deluxeAccomClass, foundAccomClass);
    }

    @Test
    public void accomClassCapacityRatioShouldOnlyConsiderNonZeroActiveAccomTypes() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "sometestcode", "sometestcode", 0, "N");
        List<AccomClass> roomClassCapacityRatio = service.getRoomClassCapacityRatio();
        assertEquals(4, roomClassCapacityRatio.size());
        AccomClass accomClass = roomClassCapacityRatio.get(0);
        assertEquals(150, accomClass.getCapacity().intValue());
        assertEquals(1.000000d, accomClass.getCapacityRatio().doubleValue(), 0.0);
        accomClass = roomClassCapacityRatio.get(1);
        assertEquals(2, accomClass.getId().intValue());
        assertEquals(120, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.727272), accomClass.getCapacityRatio());
        accomClass = roomClassCapacityRatio.get(2);
        assertEquals(3, accomClass.getId().intValue());
        assertEquals(25, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.151515), accomClass.getCapacityRatio());
        accomClass = roomClassCapacityRatio.get(3);
        assertEquals(4, accomClass.getId().intValue());
        assertEquals(20, accomClass.getCapacity().intValue());
        assertEquals(BigDecimal.valueOf(0.121212), accomClass.getCapacityRatio());
    }

    @Test
    public void getAccomTypeCodeById() {
        String foundAccomClass = service.getAccomTypeCodeById(stdQueenRoom.getId());
        assertNotNull(foundAccomClass);
        assertTrue("SDQ".equals(foundAccomClass));
    }

    @Test
    public void getAllActiveAccomClasses() {
        List<AccomClass> foundAccomClasses = service.getAllActiveAccomClasses();
        assertNotNull(foundAccomClasses);
        assertTrue(foundAccomClasses.size() == 2);
    }

    @Test
    public void getAllActiveAccomClassesWithAccomType() {
        List<AccomClass> foundAccomClasses = service.getAllActiveAccomClassesWithAccomTypes();
        assertNotNull(foundAccomClasses);
        assertTrue(foundAccomClasses.size() == 2);
    }


    @Test
    public void getAllActiveAccomClassesByPropertyId() {
        service.multiPropertyCrudService = multiPropertyCrudService();
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 6, "H2"));
        List<AccomClass> foundAccomClasses = service.getAllActiveAccomClasses(-24);
        assertNotNull(foundAccomClasses);
        assertEquals(2, foundAccomClasses.size());
    }

    @Test
    public void getAccomClassNameById() {
        var foundAccomClasses = service.getAccomClassNameById();
        assertNotNull(foundAccomClasses);
        assertEquals(2, foundAccomClasses.size());
    }


    @Test
    public void getAccomTypeNameById() {
        String name = service.getAccomTypeNameById(stdQueenRoom.getId());
        assertNotNull(name);
        assertEquals(name, "Two Queen Beds");
    }

    @Test
    public void getAllActiveNonDefaultAccomClassByViewOrder() {
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 5, "H1"));
        List<AccomClass> allActiveNonDefaultAccomClassByViewOrder = service.getAllActiveNonDefaultAccomClassByViewOrder();
        assertEquals(3, allActiveNonDefaultAccomClassByViewOrder.size());
        allActiveNonDefaultAccomClassByViewOrder.forEach(accomClass -> {
            if (accomClass.getCode().equals("DLX")) {
                assertEquals(1, accomClass.getAccomTypes().size());
                accomClass.getAccomTypes().forEach(accomType -> assertEquals(accomType.getAccomTypeCode(), "DLX"));
            }
        });
    }

    @Test
    public void getAllAccomActiveAccomTypes() {
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 5, "H1"));
        List<AccomType> activeAccomTypes = service.getAllActiveAccomTypes();
        assertEquals(of("DELUXE", "SUITE", "DOUBLE", "QUEEN", "KING"), activeAccomTypes.stream().map(type -> type.getName()).collect(toList()));
        AccomType accomTypeToDeactivate = activeAccomTypes.get(0);
        accomTypeToDeactivate.setStatusId(2);
        tenantCrudService().save(accomTypeToDeactivate);
        List<AccomType> filteredActiveAccomTypes = service.getAllActiveAccomTypes();
        assertEquals(4, filteredActiveAccomTypes.size());
    }

    @Test
    public void getAllActiveAccomTypesByPropertyId() {
        service.multiPropertyCrudService = multiPropertyCrudService();
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 6, "H2"));
        List<AccomType> activeAccomTypes = service.getAllActiveAccomTypes(-24);
        assertEquals(2, activeAccomTypes.size());
    }

    @Test
    public void getAllActiveAccomTypesWithCapacity() {
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 5, "H1"));
        List<AccomType> activeAccomTypes = service.getAllActiveAccomTypesWithCapacity();
        assertEquals(5, activeAccomTypes.size());
        AccomType accomTypeToDeactivate = activeAccomTypes.get(0);
        accomTypeToDeactivate.setStatusId(2);
        AccomType accomTypeToZeroCapacity = activeAccomTypes.get(1);
        accomTypeToZeroCapacity.setAccomTypeCapacity(0);
        tenantCrudService().save(accomTypeToDeactivate);
        tenantCrudService().save(accomTypeToZeroCapacity);
        List<AccomType> filteredActiveAccomTypes = service.getAllActiveAccomTypesWithCapacity();
        assertEquals(3, filteredActiveAccomTypes.size());
    }

    @Test
    public void getTotalAccomCapacity() {
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 5, "H1"));
        service.setTenantCrudService(tenantCrudService());
        String capacitySQL = "select     " + "SUM(Accom_Type_Capacity)    " + "from     " + "    Accom_Type AT join Accom_Class AC    " + "    on AT.Accom_Class_ID = AC.Accom_Class_ID    " + "where AC.System_Default = 0 and AT.Status_ID = 1";
        BigDecimal expected = tenantCrudService().findByNativeQuerySingleResult(capacitySQL, new HashMap<>());
        assertEquals(new Integer(expected.intValue()), service.getTotalAccomCapacity());
    }

    @Test
    public void getAccomClassSharingGroups() {
        List<AccomClassSharingGroup> accomClassSharingGroups = service.getAccomClassSharingGroups();
        assertEquals(1, accomClassSharingGroups.size());
        AccomClassSharingGroup accomClassSharingGroup = accomClassSharingGroups.get(0);
        assertEquals(accomClassSharingGroup.getName(), "Standard to Deluxe Sharing");
        assertEquals(accomClassSharingGroup.getDescription(), "Standard to Deluxe Sharing Description");
        List<AccomClassInventorySharing> foundAccomClassInventorySharings = accomClassSharingGroup.getAccomClassInventorySharings();
        assertEquals(1, foundAccomClassInventorySharings.size());
        AccomClassInventorySharing foundAccomClassInventorySharing = foundAccomClassInventorySharings.get(0);
        assertEquals(standardAccomClass, foundAccomClassInventorySharing.getAccomClass());
        assertEquals(ONE, foundAccomClassInventorySharing.getRank());
        assertEquals(new Integer(10), foundAccomClassInventorySharing.getSharedRoomCount());
        List<InventorySharingRank> foundInventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        assertEquals(1, foundInventorySharingRanks.size());
        InventorySharingRank foundInventorySharingRank = foundInventorySharingRanks.get(0);
        assertEquals(standardAccomClass, foundInventorySharingRank.getAccomClass());
        assertEquals(ONE, foundInventorySharingRank.getRank());
    }

    @Test
    public void updateAccomClassSharingGroups() {
        AccomClass suiteAccomClass = new AccomClass();
        suiteAccomClass.setPropertyId(property.getId());
        suiteAccomClass.setCode("STE");
        suiteAccomClass.setName("Suite");
        suiteAccomClass.setMasterClass(0);
        suiteAccomClass.setDescription("Suite Room");
        suiteAccomClass.setStatusId(1);
        suiteAccomClass.setViewOrder(2);
        suiteAccomClass.setRankOrder(3);
        suiteAccomClass.setSystemDefault(0);
        suiteAccomClass.setAccomTypes(new HashSet<>());
        suiteAccomClass = tenantCrudService().save(suiteAccomClass);
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        List<AccomClassSharingGroup> accomClassSharingGroups = new ArrayList<>();
        accomClassSharingGroups.add(accomClassSharingGroup);
        accomClassSharingGroup.setName("NEW NAME");
        accomClassSharingGroup.setDescription("Deluxe to Suite Sharing Description");
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setSharedAccomClass(deluxeAccomClass);
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setAccomClass(suiteAccomClass);
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setSharedRoomCount(100);
        accomClassSharingGroup.getInventorySharingRanks().get(0).setRank(2);
        service.updateAccomClassSharingGroups(accomClassSharingGroups);
        tenantCrudService().flushAndClear();
        List<AccomClassSharingGroup> foundAccomClassSharingGroups = service.getAccomClassSharingGroups();
        assertEquals(1, foundAccomClassSharingGroups.size());
        AccomClassSharingGroup accomClassSharingGroup = foundAccomClassSharingGroups.get(0);
        assertEquals(accomClassSharingGroup.getName(), "NEW NAME");
        assertEquals(accomClassSharingGroup.getDescription(), "Deluxe to Suite Sharing Description");
        List<AccomClassInventorySharing> foundAccomClassInventorySharings = accomClassSharingGroup.getAccomClassInventorySharings();
        assertEquals(1, foundAccomClassInventorySharings.size());
        AccomClassInventorySharing foundAccomClassInventorySharing = foundAccomClassInventorySharings.get(0);
        assertEquals(suiteAccomClass, foundAccomClassInventorySharing.getAccomClass());
        assertEquals(ONE, foundAccomClassInventorySharing.getRank());
        assertEquals(new Integer(100), foundAccomClassInventorySharing.getSharedRoomCount());
        List<AccomClassPriceRank> accomClassPriceRanks = tenantCrudService().findAll(AccomClassPriceRank.class);
        assertEquals(deluxeAccomClass.getCode(), accomClassPriceRanks.get(1).getLowerRankAccomClass().getCode());
        assertEquals(suiteAccomClass.getCode(), accomClassPriceRanks.get(1).getHigherRankAccomClass().getCode());
        assertFalse(accomClassPriceRanks.get(1).isUpgradeAllowed());
        List<InventorySharingRank> foundInventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        assertEquals(2, foundInventorySharingRanks.size());
        InventorySharingRank foundInventorySharingRank = foundInventorySharingRanks.get(0);
        assertEquals(suiteAccomClass, foundInventorySharingRank.getAccomClass());
        assertEquals(ONE, foundInventorySharingRank.getRank());
    }

    @Test
    public void updateAccomClassSharingGroupsRegisterSyncEvent() {
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        List<AccomClassSharingGroup> accomClassSharingGroups = new ArrayList<>();
        accomClassSharingGroups.add(accomClassSharingGroup);
        accomClassSharingGroup.setName("NEW NAME");
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setSharedRoomCount(100);
        accomClassSharingGroup.getInventorySharingRanks().get(0).setRank(TWO);
        service.updateAccomClassSharingGroups(accomClassSharingGroups);
        tenantCrudService().flushAndClear();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.INVENTORY_SHARING_CONFIG_CHANGED);
    }

    @Test
    public void hasInventoryShareRequiringSyncWithUnchangedAccomClassSharingGroup() {
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        List<AccomClassSharingGroup> accomClassSharingGroups = new ArrayList<>();
        accomClassSharingGroups.add(accomClassSharingGroup);
        service.updateAccomClassSharingGroups(accomClassSharingGroups);
    }

    @Test
    public void deleteAccomClassSharingGroup() {
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        service.deleteAccomClassSharingGroup(accomClassSharingGroup.getId());
        tenantCrudService().flushAndClear();
        assertNull(tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId()));
        assertNull(tenantCrudService().find(AccomClassInventorySharing.class, accomClassSharingGroup.getAccomClassInventorySharings().get(0).getId()));
        assertNull(tenantCrudService().find(InventorySharingRank.class, accomClassSharingGroup.getInventorySharingRanks().get(0).getId()));
    }

    @Test
    public void deleteAccomClassSharingGroupRegisterSyncEvent() {
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        service.deleteAccomClassSharingGroup(accomClassSharingGroup.getId());
        tenantCrudService().flushAndClear();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.INVENTORY_SHARING_CONFIG_CHANGED);
    }

    @Test
    public void testGetAccomClassesByViewOrder() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAccomClassesByViewOrder();
        assertEquals(2, accomClasses.size());
        AccomClass firstAccomClass = accomClasses.get(0);
        assertEquals(DELUXE, firstAccomClass.getName());
        assertEquals(ONE, firstAccomClass.getViewOrder());
        AccomClass secondAccomClass = accomClasses.get(1);
        assertEquals(secondAccomClass.getName(), "Standard");
        assertEquals(TWO, secondAccomClass.getViewOrder());
    }

    @Test
    public void testGetActiveNonDefaultAccomClasses() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), new Date())).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), new Date())).thenReturn(false);
        List<AccomClass> accomClasses = service.getActiveNonDefaultAccomClasses();
        // STD is default
        assertEquals(1, accomClasses.size());
        AccomClass firstAccomClass = accomClasses.get(0);
        assertEquals(DELUXE, firstAccomClass.getName());
        assertEquals(ONE, firstAccomClass.getViewOrder());
    }

    @Test
    public void testGetAccomClassesByRankOrder() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAccomClassesByRankOrder();
        assertEquals(2, accomClasses.size());
        AccomClass firstAccomClass = accomClasses.get(0);
        assertEquals(firstAccomClass.getName(), "Standard");
        assertEquals(ONE, firstAccomClass.getRankOrder());
        AccomClass secondAccomClass = accomClasses.get(1);
        assertEquals(DELUXE, secondAccomClass.getName());
        assertEquals(TWO, secondAccomClass.getRankOrder());
    }

    @Test
    public void getAssignedAccomClassesByRankOrder() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAssignedAccomClassesByRankOrder();
        assertEquals(1, accomClasses.size());
        AccomClass roomClass = accomClasses.get(0);
        assertEquals(DELUXE, roomClass.getName());
        assertEquals(Integer.valueOf(2), roomClass.getRankOrder());
        assertEquals(Integer.valueOf(0), roomClass.getSystemDefault());
    }

    @Test
    public void getAccomClassesByViewOrder() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAccomClassesByViewOrder();
        assertEquals(2, accomClasses.size());
        AccomClass roomClass1 = accomClasses.get(0);
        assertEquals(DELUXE, roomClass1.getName());
        assertEquals(Integer.valueOf(1), roomClass1.getViewOrder());
        AccomClass roomClass2 = accomClasses.get(1);
        assertEquals(roomClass2.getName(), "Standard");
        assertEquals(Integer.valueOf(2), roomClass2.getViewOrder());
    }

    @Test
    public void getAssignedAccomClassesByViewOrder() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAssignedAccomClassesByViewOrder();
        assertEquals(1, accomClasses.size());
        AccomClass roomClass1 = accomClasses.get(0);
        assertEquals(DELUXE, roomClass1.getName());
        assertEquals(Integer.valueOf(1), roomClass1.getViewOrder());
    }

    @Test
    public void getAssignedAccomClassesByViewOrderWithAccomTypes() {
        List<AccomClass> accomClasses = service.getAssignedAccomClassesByViewOrderWithAccomTypes();
        assertEquals(1, accomClasses.size());
        AccomClass roomClass1 = accomClasses.get(0);
        assertEquals(DELUXE, roomClass1.getName());
        assertEquals(Integer.valueOf(1), roomClass1.getViewOrder());
    }

    @Test
    public void getAllAccomClassDetails() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), null)).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), null)).thenReturn(false);
        List<AccomClass> accomClasses = service.getAllAccomClassDetails();
        assertEquals(2, accomClasses.size());
        AccomClass firstAccomClass = accomClasses.get(0);
        assertEquals(DELUXE, firstAccomClass.getName());
        assertEquals(ONE, firstAccomClass.getViewOrder());
        AccomClass secondAccomClass = accomClasses.get(1);
        assertEquals(secondAccomClass.getName(), "Standard");
        assertEquals(TWO, secondAccomClass.getViewOrder());
    }

    @Test
    public void testPersistAccomClassWithViewOrderWithSameName() {
        assertThrows(TetrisException.class, () -> {
            AccomClass newAccomClass = new AccomClass();
            newAccomClass.setPropertyId(property.getId());
            newAccomClass.setCode("STD");
            newAccomClass.setName("Standard");
            newAccomClass.setMasterClass(ZERO);
            newAccomClass.setDescription("Standard Room");
            newAccomClass.setStatusId(ONE);
            newAccomClass.setViewOrder(TWO);
            newAccomClass.setSystemDefault(ONE);
            service.persistAccomClassWithViewOrder(newAccomClass);
        });
    }

    @Test
    public void testPersistAccomClassWithViewOrder() {
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setPropertyId(property.getId());
        newAccomClass.setCode("SWT");
        newAccomClass.setName("Sweet");
        newAccomClass.setMasterClass(ZERO);
        newAccomClass.setDescription("Sweet Room");
        newAccomClass.setViewOrder(THREE);
        service.persistAccomClassWithViewOrder(newAccomClass);
        AccomClass accomClass = service.findExistingAccomClassWithName(property.getId(), newAccomClass.getName());
        assertNotNull(accomClass);
        assertNotNull(accomClass.getId());
        assertEquals(accomClass.getName(), "Sweet");
    }

    @Test
    public void testDeleteAccomClass() {
        service.deleteAccomClass(deluxeAccomClass.getId());
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(Constants.INACTIVE_STATUS_ID, accomClass.getStatusId());
        assertFalse(accomClass.getMasterClassBoolean());
    }

    @Test
    public void testUpdateAccomClassesMasterClass() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        standardAccomClass.setMasterClass(1);
        deluxeAccomClass.setMasterClass(0);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        service.updateAccomClasses(accomClasses);
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(ONE, foundStandardAccomClass.getMasterClass());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(ZERO, foundDeluxeAccomClass.getMasterClass());
        verify(invalidateOverridesService).invalidateOverrides(new ArrayList<>());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClassesUnsetMasterClass() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        standardAccomClass.setMasterClass(ZERO);
        deluxeAccomClass.setMasterClass(ZERO);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        service.updateAccomClasses(accomClasses);
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(ZERO, foundStandardAccomClass.getMasterClass());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(ZERO, foundDeluxeAccomClass.getMasterClass());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClassesWithInventSharingAndMoveOfAccomClass() {
        // Create one more Accom Class and assign room type
        testQueenRoom = createNewAccomType("QN", standardAccomClass, "Two Queen Beds", FORTY, ONE, ZERO);
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        // Share inventory
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        List<AccomClassSharingGroup> accomClassSharingGroups = new ArrayList<>();
        accomClassSharingGroups.add(accomClassSharingGroup);
        accomClassSharingGroup.setName("NEW NAME");
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setSharedRoomCount(100);
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setUseMax(true);
        accomClassSharingGroup.getInventorySharingRanks().get(0).setRank(TWO);
        service.updateAccomClassSharingGroups(accomClassSharingGroups);
        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();
        List<AccomClassSharingGroup> foundAccomClassSharingGroups = service.getAccomClassSharingGroups();
        assertEquals(1, foundAccomClassSharingGroups.size());
        AccomClassSharingGroup accomClassSharingGroup = foundAccomClassSharingGroups.get(0);
        assertEquals(accomClassSharingGroup.getName(), "NEW NAME");
        assertEquals(accomClassSharingGroup.getDescription(), "Standard to Deluxe Sharing Description");
        List<AccomClassInventorySharing> foundAccomClassInventorySharings = accomClassSharingGroup.getAccomClassInventorySharings();
        assertEquals(1, foundAccomClassInventorySharings.size());
        AccomClassInventorySharing foundAccomClassInventorySharing = foundAccomClassInventorySharings.get(0);
        assertEquals(standardAccomClass, foundAccomClassInventorySharing.getAccomClass());
        assertEquals(ONE, foundAccomClassInventorySharing.getRank());
        assertEquals(new Integer(100), foundAccomClassInventorySharing.getSharedRoomCount());
        List<InventorySharingRank> foundInventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        assertEquals(2, foundInventorySharingRanks.size());
        InventorySharingRank foundInventorySharingRank = foundInventorySharingRanks.get(0);
        assertEquals(standardAccomClass, foundInventorySharingRank.getAccomClass());
        assertEquals(ONE, foundInventorySharingRank.getRank());
        // Move queen room to Standard class
        testQueenRoom.setAccomClass(standardAccomClass);
        standardAccomClass.getAccomTypes().add(testQueenRoom);
        service.updateAccomClasses(accomClasses);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.INVENTORY_SHARING_CONFIG_CHANGED);
        accomClassSharingGroups = service.getAllAccomClassSharingGroupDetails();
        assertEquals(1, accomClassSharingGroups.size());
        assertEquals(accomClassSharingGroup.getId(), accomClassSharingGroups.get(0).getId());
        assertEquals(new Integer(100), accomClassSharingGroups.get(0).getAccomClassInventorySharings().get(0).getSharedRoomCount());
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    private void createTestQueenRoomWithDeluxAccomClass() {
        testQueenRoom = createNewAccomType("QN", deluxeAccomClass, "Two Queen Beds", FORTY, ONE, ZERO);
    }

    @Test
    public void testUpdateAccomClassesWithInventSharingAndMoveOfAccomClassForRoomsConfiguration() {
        // Create one more Accom Class and assign room type
        testQueenRoom = createNewAccomType("QN", standardAccomClass, "Two Queen Beds", FORTY, ONE, ZERO);
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        // Share inventory
        accomClassSharingGroup = tenantCrudService().find(AccomClassSharingGroup.class, accomClassSharingGroup.getId());
        List<AccomClassSharingGroup> accomClassSharingGroups = new ArrayList<>();
        accomClassSharingGroups.add(accomClassSharingGroup);
        accomClassSharingGroup.setName("NEW NAME");
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setSharedRoomCount(100);
        accomClassSharingGroup.getAccomClassInventorySharings().get(0).setUseMax(true);
        accomClassSharingGroup.getInventorySharingRanks().get(0).setRank(TWO);
        service.updateAccomClassSharingGroups(accomClassSharingGroups);
        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();
        List<AccomClassSharingGroup> foundAccomClassSharingGroups = service.getAccomClassSharingGroups();
        assertEquals(1, foundAccomClassSharingGroups.size());
        AccomClassSharingGroup accomClassSharingGroup = foundAccomClassSharingGroups.get(0);
        assertEquals(accomClassSharingGroup.getName(), "NEW NAME");
        assertEquals(accomClassSharingGroup.getDescription(), "Standard to Deluxe Sharing Description");
        List<AccomClassInventorySharing> foundAccomClassInventorySharings = accomClassSharingGroup.getAccomClassInventorySharings();
        assertEquals(1, foundAccomClassInventorySharings.size());
        AccomClassInventorySharing foundAccomClassInventorySharing = foundAccomClassInventorySharings.get(0);
        assertEquals(standardAccomClass, foundAccomClassInventorySharing.getAccomClass());
        assertEquals(ONE, foundAccomClassInventorySharing.getRank());
        assertEquals(new Integer(100), foundAccomClassInventorySharing.getSharedRoomCount());
        List<InventorySharingRank> foundInventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        assertEquals(2, foundInventorySharingRanks.size());
        InventorySharingRank foundInventorySharingRank = foundInventorySharingRanks.get(0);
        assertEquals(standardAccomClass, foundInventorySharingRank.getAccomClass());
        assertEquals(ONE, foundInventorySharingRank.getRank());
        // Move queen room to Standard class
        testQueenRoom.setAccomClass(standardAccomClass);
        standardAccomClass.getAccomTypes().add(testQueenRoom);
        service.updateAccomClasses(accomClasses);
        accomClassSharingGroups = service.getAllAccomClassSharingGroupDetails();
        assertEquals(1, accomClassSharingGroups.size());
        assertEquals(accomClassSharingGroup.getId(), accomClassSharingGroups.get(0).getId());
        assertEquals(new Integer(100), accomClassSharingGroups.get(0).getAccomClassInventorySharings().get(0).getSharedRoomCount());
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClassesResolveNoMasterClassAlert() {
        deluxeAccomClass.setMasterClass(ZERO);
        tenantCrudService().save(deluxeAccomClass);
        deluxeAccomClass.setMasterClass(ONE);
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        service.updateAccomClasses(accomClasses);
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(ZERO, foundStandardAccomClass.getMasterClass());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(ONE, foundDeluxeAccomClass.getMasterClass());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClassesMasterClass_MasterClassChanged() {
        List<AccomClass> existingAccomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        AccomClass standard = new AccomClass();
        standard.setPropertyId(property.getId());
        standard.setId(standardAccomClass.getId());
        standard.setCode("STD");
        standard.setName("Standard");
        standard.setMasterClass(1);
        standard.setDescription("Standard Room");
        standard.setStatusId(1);
        standard.setViewOrder(2);
        standard.setRankOrder(1);
        standard.setSystemDefault(1);
        standard.setAccomTypes(new HashSet<>());
        AccomClass deluxe = new AccomClass();
        deluxe.setPropertyId(property.getId());
        deluxe.setId(deluxeAccomClass.getId());
        deluxe.setCode("DLX");
        deluxe.setName(DELUXE);
        deluxe.setMasterClass(0);
        deluxe.setDescription("Deluxe Room");
        deluxe.setStatusId(1);
        deluxe.setViewOrder(1);
        deluxe.setRankOrder(2);
        deluxe.setSystemDefault(0);
        deluxe.setAccomTypes(new HashSet<>());
        List<AccomClass> accomClasses = Arrays.asList(standard, deluxe);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        service.updateAccomClasses(accomClasses, existingAccomClasses, new ArrayList<>());
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(ONE, foundStandardAccomClass.getMasterClass());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(ZERO, foundDeluxeAccomClass.getMasterClass());
        verify(invalidateOverridesService).invalidateOverrides(new ArrayList<>());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClassesMasterClass_MasterClassDeleted() {
        deluxeAccomClass.setMasterClass(0);
        standardAccomClass.setMasterClass(0);
        tenantCrudService().save(deluxeAccomClass);
        tenantCrudService().save(standardAccomClass);
        AccomClass superiorAccomClass = new AccomClass();
        superiorAccomClass.setPropertyId(property.getId());
        superiorAccomClass.setId(99);
        superiorAccomClass.setCode("SUP");
        superiorAccomClass.setName("Superior");
        superiorAccomClass.setMasterClass(1);
        superiorAccomClass.setDescription("Superior Room");
        superiorAccomClass.setStatusId(1);
        superiorAccomClass.setViewOrder(3);
        superiorAccomClass.setRankOrder(3);
        superiorAccomClass.setSystemDefault(0);
        superiorAccomClass.setAccomTypes(new HashSet<>());
        List<AccomClass> existingAccomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass, superiorAccomClass);
        AccomClass standard = new AccomClass();
        standard.setPropertyId(property.getId());
        standard.setId(standardAccomClass.getId());
        standard.setCode("STD");
        standard.setName("Standard");
        standard.setMasterClass(0);
        standard.setDescription("Standard Room");
        standard.setStatusId(1);
        standard.setViewOrder(2);
        standard.setRankOrder(1);
        standard.setSystemDefault(1);
        standard.setAccomTypes(new HashSet<>());
        AccomClass deluxe = new AccomClass();
        deluxe.setPropertyId(property.getId());
        deluxe.setId(deluxeAccomClass.getId());
        deluxe.setCode("DLX");
        deluxe.setName(DELUXE);
        deluxe.setMasterClass(1);
        deluxe.setDescription("Deluxe Room");
        deluxe.setStatusId(1);
        deluxe.setViewOrder(1);
        deluxe.setRankOrder(2);
        deluxe.setSystemDefault(0);
        deluxe.setAccomTypes(new HashSet<>());
        List<AccomClass> accomClasses = Arrays.asList(standard, deluxe);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        service.updateAccomClasses(accomClasses, existingAccomClasses, new ArrayList<>());
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(ZERO, foundStandardAccomClass.getMasterClass());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(ONE, foundDeluxeAccomClass.getMasterClass());
        verify(invalidateOverridesService).invalidateOverrides(new ArrayList<>());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClasses_HiltonUnassignedRoomTypeDoesNotRemoveOverridesEnabled() {
        AccomClass unassignedRC = new AccomClass();
        unassignedRC.setName("Unassigned");
        unassignedRC.setId(1);

        AccomType movedAccomType = new AccomType();
        movedAccomType.setName("moved rt");
        movedAccomType.setAccomTypeCode("NEW");
        movedAccomType.setAccomClass(unassignedRC);
        movedAccomType.setPropertyId(property.getId());
        movedAccomType.setSystemDefault(0);
        movedAccomType.setStatusId(1);
        movedAccomType.setRohType(0);
        movedAccomType.setIsComponentRoom("N");
        movedAccomType.setDisplayStatusId(1);
        movedAccomType.setId(1);

        AccomType stdAccomType = new AccomType();
        stdAccomType.setName("STD");
        stdAccomType.setAccomTypeCode("STD");
        stdAccomType.setAccomClass(standardAccomClass);
        stdAccomType.setPropertyId(property.getId());
        stdAccomType.setPropertyId(property.getId());
        stdAccomType.setSystemDefault(0);
        stdAccomType.setStatusId(1);
        stdAccomType.setRohType(0);
        stdAccomType.setIsComponentRoom("N");
        stdAccomType.setDisplayStatusId(1);
        stdAccomType.setId(2);

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(movedAccomType);
        accomTypes.add(stdAccomType);

        List<AccomType> existingAccomTypes = new ArrayList<>();
        existingAccomTypes.add(stdAccomType);
        existingAccomTypes.add(movedAccomType);

        standardAccomClass.setMasterClass(0);
        standardAccomClass.setAccomTypes(accomTypes);
        List<AccomClass> existingAccomClasses = Arrays.asList(standardAccomClass, unassignedRC);
        List<AccomClass> changedAccomClasses = Arrays.asList(standardAccomClass);

        // toggle on flow
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        when(configParamService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_UNASSIGNED_ROOM_TYPE_NO_OVERRIDE_REMOVAL_ENABLED)).thenReturn(true);
        service.updateAccomClasses(changedAccomClasses, existingAccomClasses, existingAccomTypes);
        verify(invalidateOverridesService).invalidateOverrides(new ArrayList<>());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testUpdateAccomClasses_HiltonUnassignedRoomTypeDoesNotRemoveOverridesDisabled() {
        AccomClass unassignedRC = new AccomClass();
        unassignedRC.setName("Unassigned");
        unassignedRC.setId(1);

        AccomType movedAccomType = new AccomType();
        movedAccomType.setName("moved rt");
        movedAccomType.setAccomTypeCode("NEW");
        movedAccomType.setAccomClass(unassignedRC);
        movedAccomType.setPropertyId(property.getId());
        movedAccomType.setSystemDefault(0);
        movedAccomType.setStatusId(1);
        movedAccomType.setRohType(0);
        movedAccomType.setIsComponentRoom("N");
        movedAccomType.setDisplayStatusId(1);
        movedAccomType.setId(1);

        AccomType stdAccomType = new AccomType();
        stdAccomType.setName("STD");
        stdAccomType.setAccomTypeCode("STD");
        stdAccomType.setAccomClass(standardAccomClass);
        stdAccomType.setPropertyId(property.getId());
        stdAccomType.setPropertyId(property.getId());
        stdAccomType.setSystemDefault(0);
        stdAccomType.setStatusId(1);
        stdAccomType.setRohType(0);
        stdAccomType.setIsComponentRoom("N");
        stdAccomType.setDisplayStatusId(1);
        stdAccomType.setId(2);

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(movedAccomType);
        accomTypes.add(stdAccomType);

        List<AccomType> existingAccomTypes = new ArrayList<>();
        existingAccomTypes.add(stdAccomType);
        existingAccomTypes.add(movedAccomType);

        standardAccomClass.setMasterClass(0);
        standardAccomClass.setAccomTypes(accomTypes);
        List<AccomClass> existingAccomClasses = Arrays.asList(standardAccomClass, unassignedRC);
        List<AccomClass> changedAccomClasses = Arrays.asList(standardAccomClass);

        // toggle off flow
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        when(configParamService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_UNASSIGNED_ROOM_TYPE_NO_OVERRIDE_REMOVAL_ENABLED)).thenReturn(false);
        service.updateAccomClasses(changedAccomClasses, existingAccomClasses, existingAccomTypes);
        verify(invalidateOverridesService).invalidateOverrides(Arrays.asList(standardAccomClass));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    void updateAccomClasses_RoomClassChangeUpdatesCentralRms() {
        List<AccomClass> existingAccomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        AccomClass standard = new AccomClass();
        standard.setPropertyId(property.getId());
        standard.setId(standardAccomClass.getId());
        standard.setCode("STD");
        standard.setName("Standard");
        standard.setMasterClass(1);
        standard.setDescription("Standard Room");
        standard.setStatusId(1);
        standard.setViewOrder(2);
        standard.setRankOrder(1);
        standard.setSystemDefault(1);
        standard.setAccomTypes(new HashSet<>());
        AccomClass deluxe = new AccomClass();
        deluxe.setPropertyId(property.getId());
        deluxe.setId(deluxeAccomClass.getId());
        deluxe.setCode("DLX");
        deluxe.setName("TEST");
        deluxe.setMasterClass(0);
        deluxe.setDescription("Deluxe Room");
        deluxe.setStatusId(1);
        deluxe.setViewOrder(1);
        deluxe.setRankOrder(2);
        deluxe.setSystemDefault(0);
        deluxe.setAccomTypes(new HashSet<>());
        List<AccomClass> accomClasses = Arrays.asList(standard, deluxe);

        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        when(configParamService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE)).thenReturn(true);

        AccommodationService spyService = spy(service);
        when(spyService.isCentralRmsAvailable()).thenReturn(true);

        spyService.updateAccomClasses(accomClasses, existingAccomClasses, new ArrayList<>());

        AccomClass updatedRoomClassName = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals("TEST", updatedRoomClassName.getName());

        verify(invalidateOverridesService).invalidateOverrides(new ArrayList<>());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());

        verify(centralRmsService).syncPricingDataOnRoomClassChange();
    }

    @Test
    void hasRoomClassNamedChange_true() {
        AccomClass existingAccomClass1 = new AccomClass();
        existingAccomClass1.setName("STE");
        AccomClass existingAccomClass2 = new AccomClass();
        existingAccomClass2.setName("DELUXE");
        AccomClass existingAccomClass3 = new AccomClass();
        existingAccomClass3.setName("STANDARD");

        AccomClass changedAccomClass1 = new AccomClass();
        changedAccomClass1.setName("STE");
        AccomClass changedAccomClass2 = new AccomClass();
        changedAccomClass2.setName("DELUXE");
        AccomClass changedAccomClass3 = new AccomClass();
        changedAccomClass3.setName("NEW");

        assertTrue(service.hasRoomClassNameChange(List.of(changedAccomClass1, changedAccomClass2, changedAccomClass3), List.of(existingAccomClass1, existingAccomClass2, existingAccomClass3)));
    }

    @Test
    void hasRoomClassNamedChange_false() {
        AccomClass existingAccomClass = new AccomClass();
        existingAccomClass.setName("STE");
        AccomClass changedAccomClass = new AccomClass();
        changedAccomClass.setName("STE");

        assertFalse(service.hasRoomClassNameChange(List.of(changedAccomClass), List.of(existingAccomClass)));
    }

    @Test
    void hasRoomClassNamedChange_falseForNoExistingRoomClasses() {
        AccomClass changedAccomClass = new AccomClass();
        changedAccomClass.setName("STE");

        assertFalse(service.hasRoomClassNameChange(List.of(changedAccomClass), new ArrayList<>()));
    }

    @Test
    void hasRoomClassNamedChange_falseForDifferenceInNumberOfRoomClasses() {
        AccomClass changedAccomClass = new AccomClass();
        changedAccomClass.setName("STE");

        AccomClass existingAccomClass1 = new AccomClass();
        existingAccomClass1.setName("STE");

        AccomClass existingAccomClass2 = new AccomClass();
        existingAccomClass2.setName("DELUXE");

        assertFalse(service.hasRoomClassNameChange(List.of(changedAccomClass), List.of(existingAccomClass1, existingAccomClass2)));
    }

    @Test
    void hasRoomClassNamedChange_falseForAddedRoomClasses() {
        AccomClass changedAccomClass1 = new AccomClass();
        changedAccomClass1.setName("STE");
        AccomClass changedAccomClass2 = new AccomClass();
        changedAccomClass2.setName("DELUXE");

        AccomClass existingAccomClass1 = new AccomClass();
        existingAccomClass1.setName("STE");

        assertFalse(service.hasRoomClassNameChange(List.of(changedAccomClass1, changedAccomClass2), List.of(existingAccomClass1)));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testRegisterSyncEventMoveAccomTypeToMasterClass() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.ACCOMMODATION_CONFIGURATION)).thenReturn(false);
        // Move queen room to deluxe class
        stdQueenRoom.setAccomClass(deluxeAccomClass);
        deluxeAccomClass.getAccomTypes().add(stdQueenRoom);
        standardAccomClass.getAccomTypes().remove(stdQueenRoom);
        service.updateAccomClasses(accomClasses);
        flushAndClear();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testRegisterSyncEventMasterClassChanged() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        deluxeAccomClass.setMasterClass(0);
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.ACCOMMODATION_CONFIGURATION)).thenReturn(false);
        service.updateAccomClasses(accomClasses);
        flushAndClear();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void testRegisterSyncEventRoomClassRankingChanged() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        deluxeAccomClass.setRankOrder(1);
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.ACCOMMODATION_CONFIGURATION)).thenReturn(false);
        service.updateAccomClasses(accomClasses);
        flushAndClear();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED_NO_CALIBRATION);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testUpdateAccomClassesMoveAccomType() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        // syncEventAggregatorService.registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        when(configParamService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(new Date());
        // Move queen room to deluxe class
        stdQueenRoom.setAccomClass(deluxeAccomClass);
        deluxeAccomClass.getAccomTypes().add(stdQueenRoom);
        standardAccomClass.getAccomTypes().remove(stdQueenRoom);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        service.updateAccomClasses(accomClasses);
        flushAndClear();
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(1, foundStandardAccomClass.getAccomTypes().size());
        assertEquals(stdKingRoom.getAccomTypeCode(), foundStandardAccomClass.getAccomTypes().iterator().next().getAccomTypeCode());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(1, foundDeluxeAccomClass.getAccomTypes().size());
        assertEquals(stdQueenRoom.getAccomTypeCode(), foundDeluxeAccomClass.getAccomTypes().iterator().next().getAccomTypeCode());
        // Assert RateUnqualifiedAccomClass was created
        List<RateUnqualifiedAccomClass> rateUnqualifiedAccomClasses = tenantCrudService().findByNamedQuery(RateUnqualifiedAccomClass.BY_PROPERTY_ID, QueryParameter.with("propertyId", property.getId()).parameters());
        assertEquals(1, rateUnqualifiedAccomClasses.size());
        assertEquals(deluxeAccomClass.getId(), rateUnqualifiedAccomClasses.get(0).getAccomClassId());
        RateUnqualifiedDefaults rateUnqualifiedDefaults = tenantCrudService().findByNamedQuerySingleResult(RateUnqualifiedDefaults.BY_RATE_UNQUALIFIED_ACCOM_CLASS, QueryParameter.with("id", rateUnqualifiedAccomClasses.get(0).getId()).parameters());
        assertNotNull(rateUnqualifiedDefaults);
        assertEquals(ONE, rateUnqualifiedDefaults.getSundayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getMondayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getTuesdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getWednesdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getThursdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getFridayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getSaturdayAvailable());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getSundayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getMondayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getTuesdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getWednesdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getThursdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getFridayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getSaturdayMinLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getSundayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getMondayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getTuesdayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getWednesdayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getThursdayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getFridayMaxLos());
        assertEquals(new Float(8), rateUnqualifiedDefaults.getSaturdayMaxLos());
        assertEquals(ZERO, rateUnqualifiedDefaults.getUserOverrideOnly());
        verify(groupPricingConfigurationService, Mockito.times(1)).handleModifiedRoomClasses();
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testUpdateAccomClassesMoveAccomType_MaxLOS() {
        List<AccomClass> accomClasses = Arrays.asList(standardAccomClass, deluxeAccomClass);
        when(dateService.getCaughtUpDate()).thenReturn(new Date());
        // Move queen room to deluxe class
        stdQueenRoom.setAccomClass(deluxeAccomClass);
        deluxeAccomClass.getAccomTypes().add(stdQueenRoom);
        standardAccomClass.getAccomTypes().remove(stdQueenRoom);
        when(configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("6");
        service.updateAccomClasses(accomClasses);
        flushAndClear();
        AccomClass foundStandardAccomClass = tenantCrudService().find(AccomClass.class, standardAccomClass.getId());
        assertEquals(1, foundStandardAccomClass.getAccomTypes().size());
        assertEquals(stdKingRoom.getAccomTypeCode(), foundStandardAccomClass.getAccomTypes().iterator().next().getAccomTypeCode());
        AccomClass foundDeluxeAccomClass = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertEquals(1, foundDeluxeAccomClass.getAccomTypes().size());
        assertEquals(stdQueenRoom.getAccomTypeCode(), foundDeluxeAccomClass.getAccomTypes().iterator().next().getAccomTypeCode());
        // Assert RateUnqualifiedAccomClass was created
        List<RateUnqualifiedAccomClass> rateUnqualifiedAccomClasses = tenantCrudService().findByNamedQuery(RateUnqualifiedAccomClass.BY_PROPERTY_ID, QueryParameter.with("propertyId", property.getId()).parameters());
        assertEquals(1, rateUnqualifiedAccomClasses.size());
        assertEquals(deluxeAccomClass.getId(), rateUnqualifiedAccomClasses.get(0).getAccomClassId());
        RateUnqualifiedDefaults rateUnqualifiedDefaults = tenantCrudService().findByNamedQuerySingleResult(RateUnqualifiedDefaults.BY_RATE_UNQUALIFIED_ACCOM_CLASS, QueryParameter.with("id", rateUnqualifiedAccomClasses.get(0).getId()).parameters());
        assertNotNull(rateUnqualifiedDefaults);
        assertEquals(ONE, rateUnqualifiedDefaults.getSundayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getMondayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getTuesdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getWednesdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getThursdayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getFridayAvailable());
        assertEquals(ONE, rateUnqualifiedDefaults.getSaturdayAvailable());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getSundayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getMondayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getTuesdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getWednesdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getThursdayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getFridayMinLos());
        assertEquals(new Float(1), rateUnqualifiedDefaults.getSaturdayMinLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getSundayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getMondayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getTuesdayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getWednesdayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getThursdayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getFridayMaxLos());
        assertEquals(new Float(6), rateUnqualifiedDefaults.getSaturdayMaxLos());
        assertEquals(ZERO, rateUnqualifiedDefaults.getUserOverrideOnly());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        verify(accommodationMappingService).deleteInactiveWebrateAccomClassMapping(property.getId());
    }

    @Test
    public void setAccomTypeAsRoh() {
        service.setAccomTypeAsRoh(stdKingRoom.getId(), Boolean.TRUE);
        verify(overbookingOverrideService).afterROHConfigurationCleanup();
        flushAndClear();
        AccomType foundStandardKingAccomType = tenantCrudService().find(AccomType.class, stdKingRoom.getId());
        assertEquals(ONE, foundStandardKingAccomType.getRohType());
        AccomType foundStandardQueenAccomType = tenantCrudService().find(AccomType.class, stdQueenRoom.getId());
        assertEquals(ZERO, foundStandardQueenAccomType.getRohType());
    }

    @Test
    public void testGetROHAccomTypes() {
        ROHRoom = new AccomType();
        ROHRoom.setPropertyId(property.getId());
        ROHRoom.setAccomTypeCode("ROHR");
        ROHRoom.setAccomClass(standardAccomClass);
        ROHRoom.setName("ROH Room");
        ROHRoom.setDescription("ROH Room");
        ROHRoom.setAccomTypeCapacity(100);
        ROHRoom.setStatusId(1);
        ROHRoom.setRohType(1);
        ROHRoom.setSystemDefault(0);
        ROHRoom.setIsComponentRoom("N");
        ROHRoom = tenantCrudService().save(ROHRoom);
        flushAndClear();
        List<AccomType> accomTypes = service.getROHAccomTypes();
        assertEquals(1, accomTypes.size());
        assertEquals(accomTypes.get(0).getAccomTypeCode(), "ROHR");
    }

    @Test
    public void getAllAccomTypeDetails() {
        // Calling this to ensure that the query executes correctly,
        // but not asserting because it's an unbounded query
        service.getAllAccomTypeDetails();
    }

    @Test
    public void getAllAccomClassSharingGroupDetails() {
        List<AccomClassSharingGroup> accomClassSharingGroups = service.getAllAccomClassSharingGroupDetails();
        assertEquals(1, accomClassSharingGroups.size());
        assertEquals(accomClassSharingGroup.getId(), accomClassSharingGroups.get(0).getId());
    }

    @Test
    public void getAllAccomClassSharingGroupDetailsWithPropertyNoResults() {
        // there should not be a property with an id of '-99999'
        List<AccomClassSharingGroup> accomClassSharingGroups = service.getAllAccomClassSharingGroupDetails(-99999);
        assertEquals(0, accomClassSharingGroups.size());
    }

    @Test
    public void getAllAccomClassSharingGroupDetailsWithProperty() {
        List<AccomClassSharingGroup> accomClassSharingGroups = service.getAllAccomClassSharingGroupDetails(property.getId());
        assertEquals(1, accomClassSharingGroups.size());
        assertEquals(accomClassSharingGroup.getId(), accomClassSharingGroups.get(0).getId());
    }

    @Test
    public void getPropertyAccomClassInventorySharingDetails() {
        List<AccomClassInventorySharing> accomClassInventorySharings = service.getPropertyAccomClassInventorySharingDetails(property.getId());
        assertEquals(1, accomClassInventorySharings.size());
    }

    @Test
    public void singleInventoryShareExistForProperty() {
        assertTrue(service.singleInventoryShareExistForProperty());
    }

    @Test
    public void getAllAccomClassSummaries() {
        when(invalidateOverridesService.hasOverrides(standardAccomClass.getId(), new Date())).thenReturn(true);
        when(invalidateOverridesService.hasOverrides(deluxeAccomClass.getId(), new Date())).thenReturn(false);
        List<AccomClassSummary> accomClassSummaries = service.getAllAccomClassSummaries();
        verifyAccomClassSummaryData(DELUXE, false, accomClassSummaries);
    }

    @Test
    public void getLowestRankAccomClassForInventoryShare() {
        AccomClass accomClass = service.getLowestRankAccomClassForInventoryShare();
        assertEquals(accomClass.getName(), "Standard");
    }

    @Test
    public void setRanks() {
        List<AccomClass> accomClasses = new ArrayList<>();
        AccomClass standard = new AccomClass();
        standard.setId(ONE);
        standard.setCode("STD");
        standard.setName("Standard");
        accomClasses.add(standard);
        AccomClass deluxe = new AccomClass();
        deluxe.setId(TWO);
        deluxe.setCode("DLX");
        deluxe.setName(DELUXE);
        accomClasses.add(deluxe);
        AccomClass premium = new AccomClass();
        premium.setId(THREE);
        premium.setCode("PRM");
        premium.setName("Premium");
        accomClasses.add(premium);
        AccomClass suite = new AccomClass();
        suite.setId(new Integer(4));
        suite.setCode("STE");
        suite.setName("Suite");
        accomClasses.add(suite);
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        List<AccomClassInventorySharing> accomClassInventorySharings = new ArrayList<>();
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInventorySharings);
        AccomClassInventorySharing accomClassInventorySharing1 = new AccomClassInventorySharing();
        accomClassInventorySharing1.setAccomClass(deluxe);
        accomClassInventorySharing1.setSharedAccomClass(standard);
        AccomClassInventorySharing accomClassInventorySharing2 = new AccomClassInventorySharing();
        accomClassInventorySharing2.setAccomClass(premium);
        accomClassInventorySharing2.setSharedAccomClass(deluxe);
        AccomClassInventorySharing accomClassInventorySharing3 = new AccomClassInventorySharing();
        accomClassInventorySharing3.setAccomClass(suite);
        accomClassInventorySharing3.setSharedAccomClass(premium);
        accomClassInventorySharings.add(accomClassInventorySharing2);
        accomClassInventorySharings.add(accomClassInventorySharing3);
        accomClassInventorySharings.add(accomClassInventorySharing1);
        service.setRankings(accomClassSharingGroup);
        // Assert that the rankings were properly set on the
        // AccomClassInventorySharing
        assertEquals(THREE, accomClassInventorySharing1.getRank());
        assertEquals(TWO, accomClassInventorySharing2.getRank());
        assertEquals(ONE, accomClassInventorySharing3.getRank());
        /*
         * Assert InventorySharingRank objects were created and ranked accordingly.
         */
        InventorySharingRank inventorySharingRank0 = accomClassSharingGroup.getInventorySharingRanks().get(0);
        assertEquals(ONE, inventorySharingRank0.getRank());
        assertEquals(inventorySharingRank0.getAccomClass().getName(), "Suite");
        InventorySharingRank inventorySharingRank1 = accomClassSharingGroup.getInventorySharingRanks().get(1);
        assertEquals(TWO, inventorySharingRank1.getRank());
        assertEquals(inventorySharingRank1.getAccomClass().getName(), "Premium");
        InventorySharingRank inventorySharingRank2 = accomClassSharingGroup.getInventorySharingRanks().get(2);
        assertEquals(THREE, inventorySharingRank2.getRank());
        assertEquals(DELUXE, inventorySharingRank2.getAccomClass().getName());
        InventorySharingRank inventorySharingRank3 = accomClassSharingGroup.getInventorySharingRanks().get(3);
        assertEquals(new Integer(4), inventorySharingRank3.getRank());
        assertEquals(inventorySharingRank3.getAccomClass().getName(), "Standard");
    }

    @Test
    public void findAccomClassInventorySharedAccomClasses() {
        Set<AccomClass> sharedWithAccomClasses = service.findAccomClassInventorySharedAccomClasses(standardAccomClass);
        assertEquals(1, sharedWithAccomClasses.size());
        assertEquals(DELUXE, sharedWithAccomClasses.iterator().next().getName());
    }

    @Test
    public void testFindExistingAccomClassWithName() {
        AccomClass accomClass = service.findExistingAccomClassWithName(property.getId(), standardAccomClass.getName());
        assertNotNull(accomClass);
        assertNotNull(accomClass.getId());
        assertEquals(standardAccomClass.getName(), accomClass.getName());
        assertNull(service.findExistingAccomClassWithName(property.getId(), "notfound"));
    }

    @Test
    public void testSetAccomClassSharingGroupDefaults_WhenInvSharingToBeDeleted() {
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Deluxe to standard");
        accomClassSharingGroup.setDescription("Deluxe to standard and standard to none");
        accomClassSharingGroup.setPropertyId(property.getId());
        List<AccomClassInventorySharing> accomClassInvSharings = new ArrayList<>();
        AccomClassInventorySharing accomClassInvSharing1 = new AccomClassInventorySharing();
        accomClassInvSharing1.setAccomClass(deluxeAccomClass);
        accomClassInvSharing1.setSharedAccomClass(standardAccomClass);
        AccomClassInventorySharing accomClassInvSharing2 = new AccomClassInventorySharing();
        accomClassInvSharing2.setAccomClass(standardAccomClass);
        accomClassInvSharing2.setSharedAccomClass(null);
        accomClassInvSharings.add(accomClassInvSharing1);
        accomClassInvSharings.add(accomClassInvSharing2);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInvSharings);
        assertEquals(2, accomClassSharingGroup.getAccomClassInventorySharings().size());
        service.setAccomClassSharingGroupDefaults(accomClassSharingGroup);
        assertEquals(1, accomClassSharingGroup.getAccomClassInventorySharings().size());
    }

    @Test
    public void testSetAccomClassSharingGroupDefaults_WhenPropertyIdNotSet() {
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Standard to deluxe");
        accomClassSharingGroup.setDescription("Standard to deluxe");
        List<AccomClassInventorySharing> accomClassInvSharings = new ArrayList<>();
        AccomClassInventorySharing accomClassInvSharing = new AccomClassInventorySharing();
        accomClassInvSharing.setAccomClass(standardAccomClass);
        accomClassInvSharing.setSharedAccomClass(deluxeAccomClass);
        accomClassInvSharings.add(accomClassInvSharing);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInvSharings);
        assertNull(accomClassSharingGroup.getPropertyId());
        service.setAccomClassSharingGroupDefaults(accomClassSharingGroup);
        assertNotNull(accomClassSharingGroup.getPropertyId());
    }

    @Test
    public void testSetAccomClassSharingGroupDefaults_WhenGroupSetToInvSharing() {
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Standard to deluxe");
        accomClassSharingGroup.setDescription("Standard to deluxe");
        accomClassSharingGroup.setPropertyId(property.getId());
        List<AccomClassInventorySharing> accomClassInvSharings = new ArrayList<>();
        AccomClassInventorySharing accomClassInvSharing = new AccomClassInventorySharing();
        accomClassInvSharing.setAccomClass(standardAccomClass);
        accomClassInvSharing.setSharedAccomClass(deluxeAccomClass);
        accomClassInvSharings.add(accomClassInvSharing);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInvSharings);
        assertNull(accomClassSharingGroup.getAccomClassInventorySharings().get(0).getAccomClassSharingGrp());
        assertNull(accomClassSharingGroup.getAccomClassInventorySharings().get(0).getRank());
        service.setAccomClassSharingGroupDefaults(accomClassSharingGroup);
        assertNotNull(accomClassSharingGroup.getAccomClassInventorySharings().get(0).getAccomClassSharingGrp());
        assertNull(accomClassSharingGroup.getAccomClassInventorySharings().get(0).getRank());
    }

    @Test
    public void testSetAccomClassSharingGroupDefaults_WhenGroupSetToInvSharingRanks() {
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Standard to deluxe");
        accomClassSharingGroup.setDescription("Standard to deluxe");
        accomClassSharingGroup.setPropertyId(property.getId());
        List<AccomClassInventorySharing> accomClassInvSharings = new ArrayList<>();
        AccomClassInventorySharing accomClassInvSharing = new AccomClassInventorySharing();
        accomClassInvSharing.setAccomClass(standardAccomClass);
        accomClassInvSharing.setSharedAccomClass(deluxeAccomClass);
        accomClassInvSharings.add(accomClassInvSharing);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInvSharings);
        List<InventorySharingRank> inventorySharingRanks = new ArrayList<>();
        InventorySharingRank invSharingRank = new InventorySharingRank();
        invSharingRank.setAccomClass(standardAccomClass);
        invSharingRank.setRank(ONE);
        inventorySharingRanks.add(invSharingRank);
        accomClassSharingGroup.setInventorySharingRanks(inventorySharingRanks);
        assertNull(accomClassSharingGroup.getInventorySharingRanks().get(0).getAccomClassSharingGrp());
        assertNotNull(accomClassSharingGroup.getInventorySharingRanks().get(0).getRank());
        service.setAccomClassSharingGroupDefaults(accomClassSharingGroup);
        assertNotNull(accomClassSharingGroup.getInventorySharingRanks().get(0).getAccomClassSharingGrp());
        assertNull(accomClassSharingGroup.getInventorySharingRanks().get(0).getRank());
    }

    @Test
    public void shouldCreateMissingAccomTypes() {
        setUpStageDataToCreateMissingOrReactivateInactiveRTs();
        Integer accomTypesCountBeforeProcessing = tenantCrudService().findByNativeQuerySingleResult("select count(*) from Accom_Type", Collections.emptyMap());
        service.createMissingAccomTypes("select 'AT' Room_Type union select 'K'");
        List<AccomType> accomTypesAfterProcessing = tenantCrudService().findAll(AccomType.class);
        assertEquals(accomTypesCountBeforeProcessing + 1, accomTypesAfterProcessing.size());
    }

    @Test
    public void testSetAccomClassSharingGroupDefaults_WhenGroupHasNullInvSharingRanks() {
        AccomClassSharingGroup accomClassSharingGroup = new AccomClassSharingGroup();
        accomClassSharingGroup.setName("Standard to deluxe");
        accomClassSharingGroup.setDescription("Standard to deluxe");
        accomClassSharingGroup.setPropertyId(property.getId());
        List<AccomClassInventorySharing> accomClassInvSharings = new ArrayList<>();
        AccomClassInventorySharing accomClassInvSharing = new AccomClassInventorySharing();
        accomClassInvSharing.setAccomClass(standardAccomClass);
        accomClassInvSharing.setSharedAccomClass(deluxeAccomClass);
        accomClassInvSharings.add(accomClassInvSharing);
        accomClassSharingGroup.setAccomClassInventorySharings(accomClassInvSharings);
        assertNull(accomClassSharingGroup.getInventorySharingRanks());
        service.setAccomClassSharingGroupDefaults(accomClassSharingGroup);
        assertNull(accomClassSharingGroup.getInventorySharingRanks());
    }

    @Test
    public void testcheckIfAllATsAreMappedToACsExcludingZeroCapacity_ONE_UNMAPPED_AT_Zero_Capacity() {
        TenantProperty property = UniquePropertyCreator.createUniqueTenantProperty(tenantCrudService());
        Integer propertyId = property.getId();
        WorkContextType workContext = PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", propertyId, property.getCode());
        PacmanThreadLocalContextHolder.setWorkContext(workContext);
        Integer masterClass = 0;
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        accomClass.setSystemDefault(1);
        tenantCrudService().save(accomClass);
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        AccomClass accomClass3 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, 1);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass);
        accomType.setAccomTypeCapacity(0);
        boolean hasAllRoomTypesMapped = service.areAllRoomTypesMappedToRoomClassesExcludingZeroCapacity();
        assertTrue(hasAllRoomTypesMapped);
    }

    @Test
    public void testcheckIfAllATsAreMappedToACsExcludingZeroCapacity_ONE_UNMAPPED_AT_Ten_Capacity() {
        TenantProperty property = UniquePropertyCreator.createUniqueTenantProperty(tenantCrudService());
        Integer propertyId = property.getId();
        WorkContextType workContext = PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", propertyId, property.getCode());
        PacmanThreadLocalContextHolder.setWorkContext(workContext);
        Integer masterClass = 0;
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        accomClass.setSystemDefault(1);
        tenantCrudService().save(accomClass);
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        AccomClass accomClass3 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, 1);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass);
        boolean hasAllRoomTypesMapped = service.areAllRoomTypesMappedToRoomClassesExcludingZeroCapacity();
        assertFalse(hasAllRoomTypesMapped);
    }

    @Test
    public void testcheckIfAllsAreMappedToACsExcludingZeroCapacity_NO_UNMAPPED_AT() {
        TenantProperty property = UniquePropertyCreator.createUniqueTenantProperty(tenantCrudService());
        Integer propertyId = property.getId();
        WorkContextType workContext = PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", propertyId, property.getCode());
        PacmanThreadLocalContextHolder.setWorkContext(workContext);
        Integer masterClass = 0;
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, masterClass);
        UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, 1);
        UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass);
        boolean hasAllRoomTypesMapped = service.areAllRoomTypesMappedToRoomClassesExcludingZeroCapacity();
        assertTrue(hasAllRoomTypesMapped);
    }

    @Test
    public void shouldNotAllowROHOverbookingForMoreThanOneInvShare() {
        // already one accom class sharing group is created in setup method
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        UniqueAccomClassSharingGroupCreator.createUniqueAccomClassSharingGroupForProperty(propertyId);
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.singleInventoryShareExist(accomClassSharingGroups);
        assertFalse(isOkForROH);
    }

    @Test
    public void shouldAllowROHOverbookingForOneInvShare() {
        // already one accom class sharing group is created in setup method
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.singleInventoryShareExist(accomClassSharingGroups);
        assertTrue(isOkForROH);
    }

    @Test
    public void shouldNotAllowROHOverbookingWhenAllRCsNotParticipateInInvShare() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        AccomType deluxRt = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), deluxeAccomClass);
        deluxeAccomClass.addAccomType(deluxRt);
        AccomClass nonEmptyRC = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomType at = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), nonEmptyRC);
        nonEmptyRC.addAccomType(at);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllRCsParticipateInInvShare(accomClassSharingGroups.get(0).getInventorySharingRanks());
        assertFalse(isOkForROH);
    }

    @Test
    public void shouldAllowROHOverbookingWhenAllRCsParticipateInInvShareButIgnoreZeroCapacityRC() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        AccomType deluxRt = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), deluxeAccomClass);
        deluxeAccomClass.addAccomType(deluxRt);
        AccomClass nonEmptyRC = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomType accomTypeWithZeroCapacity = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), nonEmptyRC);
        accomTypeWithZeroCapacity.setAccomTypeCapacity(0);
        tenantCrudService().save(accomTypeWithZeroCapacity);
        nonEmptyRC.addAccomType(accomTypeWithZeroCapacity);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllRCsParticipateInInvShare(accomClassSharingGroups.get(0).getInventorySharingRanks());
        assertTrue(isOkForROH);
    }

    @Test
    public void shouldAllowROHOverbookingWhenAllRCsParticipateInInvShare() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        AccomClass nonEmptyRC = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomType at = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), nonEmptyRC);
        nonEmptyRC.addAccomType(at);
        AccomType deluxRt = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), deluxeAccomClass);
        deluxeAccomClass.addAccomType(deluxRt);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(nonEmptyRC.getId(), accomClassSharingGroup.getId());
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllRCsParticipateInInvShare(accomClassSharingGroups.get(0).getInventorySharingRanks());
        assertTrue(isOkForROH);
    }

    @Test
    public void shouldAllowROHOverbookingWhenAllRCsParticipateInInvShareAndIgnoringEmptyRC() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        AccomType deluxRt = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), deluxeAccomClass);
        deluxeAccomClass.addAccomType(deluxRt);
        AccomClass emptyRC = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        tenantCrudService().flushAndClear();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllRCsParticipateInInvShare(accomClassSharingGroups.get(0).getInventorySharingRanks());
        assertTrue(isOkForROH);
    }

    @Test
    public void shouldAllowForROHOverbookingWhenAllRCUnitsSharedInInvShare() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        accomClassInventorySharing.setUseMax(true);
        tenantCrudService().save(accomClassInventorySharing);
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllUnitsOfRCSharedInInvShare(accomClassSharingGroups.get(0).getAccomClassInventorySharings());
        assertTrue(isOkForROH);
    }

    @Test
    public void shouldNotAllowForROHOverbookingWhenAllRCUnitsHaveNotSharedInInvShare() {
        standardAccomClass.setSystemDefault(0);
        tenantCrudService().save(standardAccomClass);
        UniquetInventorySharingRankCreator.createUniqueInventorySharingRankForAccomClassAndGroup(deluxeAccomClass.getId(), accomClassSharingGroup.getId());
        accomClassInventorySharing.setUseMax(false);
        tenantCrudService().save(accomClassInventorySharing);
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService().findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean isOkForROH = service.checkIfAllUnitsOfRCSharedInInvShare(accomClassSharingGroups.get(0).getAccomClassInventorySharings());
        assertFalse(isOkForROH);
    }

    @Test
    public void shouldReturnFailingResultsWhenHigherRankRTWithHighestRateMovedToLowerRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeLowerRank.getId() + ", " + roomTypeLowerRank2.getId() + ", " + roomTypeHigherRank2.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().getEntityManager().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertFalse(result.isEmpty());
    }

    @Test
    public void shouldReturnFailingResultsWhenLowerRankRTWithLowestRateMovedToHigherRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank.getId() + ", " + roomTypeOfHigherRank2.getId() + ", " + roomTypeOfLowerRank.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeOfLowerRank2.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertFalse(result.isEmpty());
    }

    @Test
    public void shouldNotReturnFailingResultsWhenLowerRankRTWithHighestRateMovedToHigherRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank.getId() + ", " + roomTypeOfHigherRank2.getId() + ", " + roomTypeOfLowerRank2.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeOfLowerRank.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertTrue(result.isEmpty());
    }

    @Test
    public void shouldNotReturnFailingResultsWhenHigherRankRTWithLowestRateMovedToLowerRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank2.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeOfLowerRank.getId() + ", " + roomTypeOfLowerRank2.getId() + roomTypeOfHigherRank.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertTrue(result.isEmpty());
    }

    private AccomClass createAndSetAccomClassRank(int rankOrder) {
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 0);
        accomClass.setRankOrder(rankOrder);
        tenantCrudService().save(accomClass);
        return accomClass;
    }

    @Test
    public void shouldIgnoreInactiveRTWhenHigherRankRTWithHigherRateMovedToLowerRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass roomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass roomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfHigherOrderRCWithLowestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, roomClassHighRank);
        AccomType roomTypeOfHigherOrderRCWithHigherRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, roomClassHighRank);
        AccomType roomTypeOfHigherOrderRCWithHighestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, roomClassHighRank);
        AccomType roomTypeOfLowerOrderRCWithLowestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, roomClassLowRank);
        AccomType roomTypeOfLowestOrderRCWithHighestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, roomClassLowRank);
        // lets make the roomTypeOfHigherRankWithLowestRate as inactive
        roomTypeOfHigherOrderRCWithLowestRate.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(roomTypeOfHigherOrderRCWithLowestRate);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerOrderRCWithLowestRate.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowestOrderRCWithHighestRate.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherOrderRCWithLowestRate.getId(), rateUnqualified.getId(), startDate, endDate, 100f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherOrderRCWithHigherRate.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherOrderRCWithHighestRate.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherOrderRCWithLowestRate.getId() + ", " + roomTypeOfHigherOrderRCWithHighestRate.getId() + ":" + roomClassHighRank.getRankOrder() + "|" + roomTypeOfLowerOrderRCWithLowestRate.getId() + ", " + roomTypeOfLowestOrderRCWithHighestRate.getId() + roomTypeOfHigherOrderRCWithHigherRate.getId() + ":" + roomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertTrue(result.isEmpty());
    }

    @Test
    public void shouldIgnoreInactiveRTWhenLowerRankRTWithLowerRateMovedToHigherRankRC() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfLowerOrderRCWithLowestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerOrderRCWithLowerRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerOrderRCWithHighestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfHigherRankWithLowestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfHigherRankWithHighestRate = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        // lets make the roomTypeOfHigherRankWithLowestRate as inactive
        roomTypeOfLowerOrderRCWithHighestRate.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(roomTypeOfLowerOrderRCWithHighestRate);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerOrderRCWithLowestRate.getId(), rateUnqualified.getId(), startDate, endDate, 100f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerOrderRCWithLowerRate.getId(), rateUnqualified.getId(), startDate, endDate, 200f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerOrderRCWithHighestRate.getId(), rateUnqualified.getId(), startDate, endDate, 300f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRankWithLowestRate.getId(), rateUnqualified.getId(), startDate, endDate, 600f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRankWithHighestRate.getId(), rateUnqualified.getId(), startDate, endDate, 800f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRankWithLowestRate.getId() + ", " + roomTypeOfHigherRankWithHighestRate.getId() + ", " + roomTypeOfLowerOrderRCWithLowerRate.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeOfLowerOrderRCWithLowestRate.getId() + ", " + roomTypeOfLowerOrderRCWithHighestRate.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertTrue(result.isEmpty());
    }

    @Test
    public void shouldReturnFailingResultsWhenRCRankOrderChanged() {
        when(dateService.getBusinessDate()).thenReturn(new Date());
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeOfLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        Float lowestRateForLowestRank = 10.0f;
        Float highestRateForLowestRank = 11.0f;
        Float lowestRateForHigherRank = 1000f;
        Float highestRateForHigherRank = 1100f;
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, lowestRateForLowestRank, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, highestRateForLowestRank, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, lowestRateForHigherRank, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, highestRateForHigherRank, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank.getId() + ", " + roomTypeOfHigherRank2.getId() + ":" + accomClassLowRank.getRankOrder() + "|" + roomTypeOfLowerRank.getId() + ", " + roomTypeOfLowerRank2.getId() + ":" + accomClassHighRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertFalse(result.isEmpty());
        Object[] resultRow = result.get(0);
        assertEquals(accomClassLowRank.getRankOrder(), resultRow[3]);
        assertEquals(accomClassHighRank.getRankOrder(), resultRow[4]);
        BigDecimal lowerRankMaxSunday = (BigDecimal) resultRow[5];
        assertTrue(highestRateForHigherRank.floatValue() == lowerRankMaxSunday.floatValue());
    }

    @Test
    public void shouldValidateRatesWhenRcRtMappingChanged() {
        String roomTypeIdsToMove = "1,2";
        Integer proposedRCRankForRTmove = 1;
        Integer firstRoomClassIdToChangeRankOrder = null;
        Integer proposedRankOrderForFirstRoomClassId = null;
        Integer secondRoomClassIdToChangeRankOrder = null;
        Integer proposedRankOrderForSecondRoomClassId = null;
        boolean isRoomClassRankOrderChanged = false;
        CrudService mockCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockCrudService);
        service.setDateService(dateService);
        List<Object[]> expectedObjects = new ArrayList<>();
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :proposedRankForRoomTypeIds, :systemDate," + ":firstRoomClassIdForChangingRankOrder, :proposedRankOrderForFirstRoomClass, :secondRoomClassIdForChangingRankOrder, :proposedRankOrderForSecondRoomClass, " + ":isRoomClassRankingChanged";
        when(dateService.getBusinessDate()).thenReturn(new Date());
        when(mockCrudService.<Object[]>findByNativeQuery(procedure, QueryParameter.with("changedRoomTypeIds", roomTypeIdsToMove).and("proposedRankForRoomTypeIds", proposedRCRankForRTmove).and("systemDate", DateUtil.formatDate(dateService.getBusinessDate(), "yyyy-MM-dd")).and("firstRoomClassIdForChangingRankOrder", firstRoomClassIdToChangeRankOrder).and("proposedRankOrderForFirstRoomClass", proposedRankOrderForFirstRoomClassId).and("secondRoomClassIdForChangingRankOrder", secondRoomClassIdToChangeRankOrder).and("proposedRankOrderForSecondRoomClass", proposedRankOrderForSecondRoomClassId).and("isRoomClassRankingChanged", isRoomClassRankOrderChanged).parameters())).thenReturn(expectedObjects);
        boolean isValidateRcRtMove = service.validateRatesWhenRcRtMappingChangedOrRCOrderChanged(roomTypeIdsToMove, proposedRCRankForRTmove, firstRoomClassIdToChangeRankOrder, proposedRankOrderForFirstRoomClassId, secondRoomClassIdToChangeRankOrder, proposedRankOrderForSecondRoomClassId, isRoomClassRankOrderChanged);
        assertTrue(isValidateRcRtMove);
    }

    @Test
    public void shouldValidateRatesWhenRcOrderChanged() {
        String roomTypeIdsToMove = null;
        Integer proposedRCRankForRTmove = null;
        Integer firstRoomClassIdToChangeRankOrder = 1;
        Integer proposedRankOrderForFirstRoomClassId = 2;
        Integer secondRoomClassIdToChangeRankOrder = 3;
        Integer proposedRankOrderForSecondRoomClassId = 4;
        boolean isRoomClassRankOrderChanged = true;
        CrudService mockCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockCrudService);
        service.setDateService(dateService);
        List<Object[]> expectedObjects = new ArrayList<>();
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :proposedRankForRoomTypeIds, :systemDate," + ":firstRoomClassIdForChangingRankOrder, :proposedRankOrderForFirstRoomClass, :secondRoomClassIdForChangingRankOrder, :proposedRankOrderForSecondRoomClass, " + ":isRoomClassRankingChanged";
        when(dateService.getBusinessDate()).thenReturn(new Date());
        when(mockCrudService.<Object[]>findByNativeQuery(procedure, QueryParameter.with("changedRoomTypeIds", roomTypeIdsToMove).and("proposedRankForRoomTypeIds", proposedRCRankForRTmove).and("systemDate", DateUtil.formatDate(dateService.getBusinessDate(), "yyyy-MM-dd")).and("firstRoomClassIdForChangingRankOrder", firstRoomClassIdToChangeRankOrder).and("proposedRankOrderForFirstRoomClass", proposedRankOrderForFirstRoomClassId).and("secondRoomClassIdForChangingRankOrder", secondRoomClassIdToChangeRankOrder).and("proposedRankOrderForSecondRoomClass", proposedRankOrderForSecondRoomClassId).and("isRoomClassRankingChanged", isRoomClassRankOrderChanged).parameters())).thenReturn(expectedObjects);
        boolean isValidateRcRtMove = service.validateRatesWhenRcRtMappingChangedOrRCOrderChanged(roomTypeIdsToMove, proposedRCRankForRTmove, firstRoomClassIdToChangeRankOrder, proposedRankOrderForFirstRoomClassId, secondRoomClassIdToChangeRankOrder, proposedRankOrderForSecondRoomClassId, isRoomClassRankOrderChanged);
        assertTrue(isValidateRcRtMove);
    }

    @Test
    public void shouldReturnFalseWhenGotAnyFailingScenario() {
        String roomTypeIdsToMove = null;
        Integer proposedRCRankForRTmove = null;
        Integer firstRoomClassIdToChangeRankOrder = 1;
        Integer proposedRankOrderForFirstRoomClassId = 2;
        Integer secondRoomClassIdToChangeRankOrder = 3;
        Integer proposedRankOrderForSecondRoomClassId = 4;
        boolean isRoomClassRankOrderChanged = true;
        CrudService mockCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockCrudService);
        service.setDateService(dateService);
        List<Object[]> expectedObjects = new ArrayList<Object[]>();
        expectedObjects.add(new Object[]{1, 2});
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :proposedRankForRoomTypeIds, :systemDate," + ":firstRoomClassIdForChangingRankOrder, :proposedRankOrderForFirstRoomClass, :secondRoomClassIdForChangingRankOrder, :proposedRankOrderForSecondRoomClass, " + ":isRoomClassRankingChanged";
        when(dateService.getBusinessDate()).thenReturn(new Date());
        when(mockCrudService.<Object[]>findByNativeQuery(procedure, QueryParameter.with("changedRoomTypeIds", roomTypeIdsToMove).and("proposedRankForRoomTypeIds", proposedRCRankForRTmove).and("systemDate", DateUtil.formatDate(dateService.getBusinessDate(), "yyyy-MM-dd")).and("firstRoomClassIdForChangingRankOrder", firstRoomClassIdToChangeRankOrder).and("proposedRankOrderForFirstRoomClass", proposedRankOrderForFirstRoomClassId).and("secondRoomClassIdForChangingRankOrder", secondRoomClassIdToChangeRankOrder).and("proposedRankOrderForSecondRoomClass", proposedRankOrderForSecondRoomClassId).and("isRoomClassRankingChanged", isRoomClassRankOrderChanged).parameters())).thenReturn(expectedObjects);
        boolean isValidateRcRtMove = service.validateRatesWhenRcRtMappingChangedOrRCOrderChanged(roomTypeIdsToMove, proposedRCRankForRTmove, firstRoomClassIdToChangeRankOrder, proposedRankOrderForFirstRoomClassId, secondRoomClassIdToChangeRankOrder, proposedRankOrderForSecondRoomClassId, isRoomClassRankOrderChanged);
        assertFalse(isValidateRcRtMove);
    }

    @Test
    public void shouldValidateRatesWhenRoomClassOrderChanged() {
        String roomTypeIdsToMove = null;
        Integer proposedRCRankForRTmove = null;
        Integer firstRoomClassIdToChangeRankOrder = 1;
        Integer proposedRankOrderForFirstRoomClassId = 2;
        Integer secondRoomClassIdToChangeRankOrder = 3;
        Integer proposedRankOrderForSecondRoomClassId = 4;
        boolean isRoomClassRankOrderChanged = true;
        CrudService mockCrudService = mock(CrudService.class);
        service.setTenantCrudService(mockCrudService);
        service.setDateService(dateService);
        List<Object[]> expectedObjects = new ArrayList<Object[]>();
        expectedObjects.add(new Object[]{1, 2});
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :proposedRankForRoomTypeIds, :systemDate," + ":firstRoomClassIdForChangingRankOrder, :proposedRankOrderForFirstRoomClass, :secondRoomClassIdForChangingRankOrder, :proposedRankOrderForSecondRoomClass, " + ":isRoomClassRankingChanged";
        when(dateService.getBusinessDate()).thenReturn(new Date());
        when(mockCrudService.<Object[]>findByNativeQuery(procedure, QueryParameter.with("changedRoomTypeIds", roomTypeIdsToMove).and("proposedRankForRoomTypeIds", proposedRCRankForRTmove).and("systemDate", DateUtil.formatDate(dateService.getBusinessDate(), "yyyy-MM-dd")).and("firstRoomClassIdForChangingRankOrder", firstRoomClassIdToChangeRankOrder).and("proposedRankOrderForFirstRoomClass", proposedRankOrderForFirstRoomClassId).and("secondRoomClassIdForChangingRankOrder", secondRoomClassIdToChangeRankOrder).and("proposedRankOrderForSecondRoomClass", proposedRankOrderForSecondRoomClassId).and("isRoomClassRankingChanged", isRoomClassRankOrderChanged).parameters())).thenReturn(expectedObjects);
        boolean isValidateRcRtMove = service.validateRatesWhenRoomClassOrderChanged(firstRoomClassIdToChangeRankOrder, proposedRankOrderForFirstRoomClassId, secondRoomClassIdToChangeRankOrder, proposedRankOrderForSecondRoomClassId);
        assertFalse(isValidateRcRtMove);
    }

    @Test
    public void shouldUpdateAccomCodeWithName() {
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setPropertyId(property.getId());
        newAccomClass.setCode("SWT");
        newAccomClass.setName("Sweet");
        newAccomClass.setMasterClass(ZERO);
        newAccomClass.setDescription("Sweet Room");
        newAccomClass.setViewOrder(THREE);
        service.persistAccomClassWithViewOrder(newAccomClass);
        AccomClass accomClass = service.findExistingAccomClassWithName(property.getId(), newAccomClass.getName());
        assertNotNull(accomClass);
        assertNotNull(accomClass.getId());
        assertEquals(accomClass.getName(), "Sweet");
        assertEquals(accomClass.getCode(), "Sweet");
    }

    @Test
    public void shouldExtractRtsForOrder() {
        AccomClass ac1 = new AccomClass();
        ac1.setRankOrder(1);
        AccomType at1 = new AccomType();
        at1.setId(11);
        AccomType at2 = new AccomType();
        at2.setId(12);
        Set<AccomType> rtsForAc1 = new HashSet<AccomType>();
        rtsForAc1.add(at1);
        rtsForAc1.add(at2);
        ac1.setAccomTypes(rtsForAc1);
        AccomClass ac2 = new AccomClass();
        ac2.setRankOrder(2);
        AccomType at3 = new AccomType();
        at3.setId(13);
        AccomType at4 = new AccomType();
        at4.setId(14);
        Set<AccomType> rtsForAc2 = new HashSet<AccomType>();
        rtsForAc2.add(at3);
        rtsForAc2.add(at4);
        ac2.setAccomTypes(rtsForAc2);
        List<AccomClass> roomClasses = new ArrayList<AccomClass>();
        roomClasses.add(ac1);
        roomClasses.add(ac2);
        Map<Integer, Set<Integer>> rtsWithOrder = service.extractRtsForOrder(roomClasses);
        Set<Integer> keys = rtsWithOrder.keySet();
        assertTrue(keys.contains(ac1.getRankOrder()));
        assertTrue(keys.contains(ac2.getRankOrder()));
        Set<Integer> rtIdsForAc1 = rtsWithOrder.get(keys.toArray()[0]);
        assertTrue(rtIdsForAc1.contains(at1.getId()));
        assertTrue(rtIdsForAc1.contains(at2.getId()));
        assertTrue(!rtIdsForAc1.contains(at3.getId()));
        assertTrue(!rtIdsForAc1.contains(at4.getId()));
        Set<Integer> rtIdsForAc2 = rtsWithOrder.get(keys.toArray()[1]);
        assertTrue(!rtIdsForAc2.contains(at1.getId()));
        assertTrue(!rtIdsForAc2.contains(at2.getId()));
        assertTrue(rtIdsForAc2.contains(at3.getId()));
        assertTrue(rtIdsForAc2.contains(at4.getId()));
    }

    @Test
    public void shouldIgnoreEmptyRoomClassWhileExtractRtsForOrder() {
        AccomClass ac1 = new AccomClass();
        ac1.setRankOrder(1);
        AccomType at1 = new AccomType();
        at1.setId(11);
        AccomType at2 = new AccomType();
        at2.setId(12);
        Set<AccomType> rtsForAc1 = new HashSet<AccomType>();
        rtsForAc1.add(at1);
        rtsForAc1.add(at2);
        ac1.setAccomTypes(rtsForAc1);
        AccomClass ac2 = new AccomClass();
        ac2.setRankOrder(2);
        List<AccomClass> roomClasses = new ArrayList<AccomClass>();
        roomClasses.add(ac1);
        roomClasses.add(ac2);
        Map<Integer, Set<Integer>> rtsWithOrder = service.extractRtsForOrder(roomClasses);
        Set<Integer> keys = rtsWithOrder.keySet();
        assertTrue(keys.size() == 1);
        assertTrue(keys.contains(ac1.getRankOrder()));
        assertFalse(keys.contains(ac2.getRankOrder()));
        Set<Integer> rtIdsForAc1 = rtsWithOrder.get(keys.toArray()[0]);
        assertTrue(rtIdsForAc1.contains(at1.getId()));
        assertTrue(rtIdsForAc1.contains(at2.getId()));
    }

    @Test
    public void shouldNotExtractRtsForOrderForZeroCapacityRoomClass() {
        AccomClass ac1 = new AccomClass();
        ac1.setRankOrder(1);
        List<AccomClass> roomClasses = new ArrayList<>();
        roomClasses.add(ac1);
        Map<Integer, Set<Integer>> rtsWithOrder = service.extractRtsForOrder(roomClasses);
        assertTrue(rtsWithOrder.isEmpty());
    }

    @Test
    public void shouldConvertRtsWithOrderIntoPipeSeparatorFormat() {
        HashMap<Integer, Set<Integer>> rtsWithOrder = new HashMap<>();
        Set<Integer> rts1 = new HashSet<>();
        rts1.add(11);
        rts1.add(12);
        Set<Integer> rts2 = new HashSet<>();
        rts2.add(13);
        rts2.add(14);
        rtsWithOrder.put(1, rts1);
        rtsWithOrder.put(2, rts2);
        String rtsWithOrderInPipeSeparatorFormat = service.convertRtsWithOrderIntoPipeSeparatorFormat(rtsWithOrder);
        assertEquals(rtsWithOrderInPipeSeparatorFormat, "11, 12:1|13, 14:2");
    }

    @Test
    public void shouldConvertRtsWithOrderIntoPipeSeparatorFormatForEmptyMap() {
        HashMap<Integer, Set<Integer>> rtsWithOrder = new HashMap<>();
        String rtsWithOrderInPipeSeparatorFormat = service.convertRtsWithOrderIntoPipeSeparatorFormat(rtsWithOrder);
        assertNull(rtsWithOrderInPipeSeparatorFormat);
    }

    @Test
    public void shouldReturnFalseIfCatchAnyFailingScenario() {
        HashMap<Integer, Set<Integer>> rtsWithOrder = new HashMap<>();
        Set<Integer> rts1 = new HashSet<>();
        rts1.add(11);
        rts1.add(12);
        rtsWithOrder.put(1, rts1);
        List<AccomClass> roomClasses = new ArrayList<>();
        Object[] obs = {1, 2};
        List<Object[]> objects = new ArrayList<>();
        objects.add(obs);
        AccommodationService mockAccommodationService = mock(AccommodationService.class);
        when(mockAccommodationService.extractRtsForOrder(roomClasses)).thenReturn(rtsWithOrder);
        when(mockAccommodationService.convertRtsWithOrderIntoPipeSeparatorFormat(rtsWithOrder)).thenReturn(new String());
        when(mockAccommodationService.getFailingScenariosForRtMappingChangeOrRcOrderChange(new String())).thenReturn(objects);
        when(mockAccommodationService.validateRcRtRatesAsPerOrder(roomClasses)).thenCallRealMethod();
        boolean isValidate = mockAccommodationService.validateRcRtRatesAsPerOrder(roomClasses);
        assertFalse(isValidate);
    }

    @Test
    public void shouldReturnTrueIfNotCatchAnyFailingScenario() {
        HashMap<Integer, Set<Integer>> rtsWithOrder = new HashMap<>();
        Set<Integer> rts1 = new HashSet<>();
        rts1.add(11);
        rts1.add(12);
        rtsWithOrder.put(1, rts1);
        List<AccomClass> roomClasses = new ArrayList<>();
        List<Object[]> objects = new ArrayList<>();
        AccommodationService mockAccommodationService = mock(AccommodationService.class);
        when(mockAccommodationService.extractRtsForOrder(roomClasses)).thenReturn(rtsWithOrder);
        when(mockAccommodationService.convertRtsWithOrderIntoPipeSeparatorFormat(rtsWithOrder)).thenReturn(new String());
        when(mockAccommodationService.getFailingScenariosForRtMappingChangeOrRcOrderChange(new String())).thenReturn(objects);
        when(mockAccommodationService.validateRcRtRatesAsPerOrder(roomClasses)).thenCallRealMethod();
        boolean isValidate = mockAccommodationService.validateRcRtRatesAsPerOrder(roomClasses);
        assertTrue(isValidate);
    }

    @Test
    public void shouldIgnorePastSeasonWhileGettingAnyFailingScenario() {
        when(dateService.getBusinessDate()).thenReturn(DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 10));
        Integer maxRank = tenantCrudService().findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", 5).parameters());
        int availableRankOrder = maxRank.intValue() + 1;
        tenantCrudService().getEntityManager().createNativeQuery("delete from rate_unqualified_details").executeUpdate();
        AccomClass accomClassHighRank = createAndSetAccomClassRank(availableRankOrder + 1);
        AccomClass accomClassLowRank = createAndSetAccomClassRank(availableRankOrder);
        AccomType roomTypeLowerRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeLowerRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassLowRank);
        AccomType roomTypeOfHigherRank = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        AccomType roomTypeHigherRank2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClassHighRank);
        Date startDate = new Date();
        Date endDate = DateUtil.addDaysToDate(startDate, 10);
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(startDate, endDate, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeLowerRank.getId(), rateUnqualified.getId(), startDate, endDate, 10.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeLowerRank2.getId(), rateUnqualified.getId(), startDate, endDate, 11.0f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeOfHigherRank.getId(), rateUnqualified.getId(), startDate, endDate, 1000f, 5);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(roomTypeHigherRank2.getId(), rateUnqualified.getId(), startDate, endDate, 1100f, 5);
        // set above data in this format - RT1,RT2:ORDER|RT3,RT4:ORDER
        String roomTypeIdsWithOrder = roomTypeOfHigherRank.getId() + ":" + accomClassHighRank.getRankOrder() + "|" + roomTypeLowerRank.getId() + ", " + roomTypeLowerRank2.getId() + ", " + roomTypeHigherRank2.getId() + ":" + accomClassLowRank.getRankOrder();
        tenantCrudService().flush();
        List<Object[]> result = service.getFailingScenariosForRtMappingChangeOrRcOrderChange(roomTypeIdsWithOrder);
        assertTrue(result.isEmpty());
    }

    @Test
    public void shouldValidateRatesAsPerOrder() {
        when(configParamService.getValue("pacman.Greenbar.NEG24", IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        List<AccomClass> roomClasses = new ArrayList<>();
        assertEquals(true, service.validateRatesAsPerOrder(roomClasses));
    }

    @Test
    public void shouldReturnAllAccomClassSummariesWithTheirAccomTypeSummaries() {
        CrudService crudService = mock(CrudService.class);
        service.setTenantCrudService(crudService);
        List<AccomClassSummary> accomClassSummaries = getAccomClassDummyData();
        when(crudService.<AccomClassSummary>findByNamedQuery(AccomClass.ALL_ACTIVE_ACCOM_CLASS_SUMMARY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomClassSummaries);
        when(crudService.<AccomTypeSummary>findByNamedQuery(AccomType.ALL_ACTIVE_ACCOM_TYPES_BY_ACCOM_CLASS_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomClassId", 1).parameters())).thenReturn(getAccomTypeSummaries());
        when(crudService.<AccomTypeSummary>findByNamedQuery(AccomType.ALL_ACTIVE_ACCOM_TYPES_BY_ACCOM_CLASS_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomClassId", 2).parameters())).thenReturn(Collections.emptyList());
        Map<AccomClassSummary, List<AccomTypeSummary>> allAccomClassWithTypeSummaries = service.getAllAccomClassWithTypeSummaries();
        assertEquals(1, allAccomClassWithTypeSummaries.size());
        AccomClassSummary first = allAccomClassWithTypeSummaries.keySet().iterator().next();
        assertEquals(accomClassSummaries.get(0), first);
        assertTrue(first.isAccomTypesAssigned());
    }

    @Test
    public void getAllAccomTypesByViewOrderAndName() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(new AccomType());
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE_ORDERED_BY_VIEW_ORDER_AND_NAME, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        List<AccomType> accomTypesByViewOrderAndName = service.getAllAccomTypesByViewOrderAndName();
        assertEquals(accomTypes, accomTypesByViewOrderAndName);
    }

    @Test
    void getActiveAccomTypes() {
        CrudService crudService = mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setAccomTypeCode("TEST");
        when(crudService.findByNamedQuery(eq(AccomType.ALL))).thenReturn(List.of(accomType));
        Map<Integer, String> result = service.findAccomTypeIdToAccomTypeCodeMapping();
        assertEquals(Map.of(1, "TEST"), result);
    }


    @Test
    public void createAccomType() {
        Integer initialCount = service.getAllAccomTypes().size();
        AccomType accomType = service.createAccomType("name", "code", "thisdescription", 1, 1, 1, 0, 0, "N");
        assertNotNull(accomType.getId());
        assertEquals(initialCount + 1, service.getAllAccomTypes().size());
        List<AccomType> accomList = tenantCrudService().findByNamedQuery(AccomType.BY_CODE, QueryParameter.with("code", "code").parameters());
        assertEquals(accomList.get(0).getDescription(), "thisdescription");
    }

    @Test
    public void getAllAssignedAccomTypes() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        List<AccomType> accomTypes = new ArrayList<>();
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        List<AccomType> assignedAccomTypes = service.getAllAssignedAccomTypes();
        assertEquals(accomTypes, assignedAccomTypes);
        verify(crudService).findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getAllAssignedAccomTypesIncludingInactive() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(new AccomType());
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_INCLUDING_INACTIVE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        List<AccomType> retAccomTypes = service.getAllAssignedAccomTypesIncludingInactive();
        assertEquals(accomTypes, retAccomTypes);
        verify(crudService).findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_INCLUDING_INACTIVE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getAllActiveAccomTypeADRs() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType stdAccomType = new AccomType();
        stdAccomType.setAccomTypeCode("STD");
        AccomType dlxAccomType = new AccomType();
        dlxAccomType.setAccomTypeCode("DLX");
        AccomType steAccomType = new AccomType();
        steAccomType.setAccomTypeCode("STE");
        List<AccomType> accomTypes = Arrays.asList(stdAccomType, dlxAccomType, steAccomType);
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE_NON_DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        Date caughtUpDate = new Date();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        AccomTypeCodeADR stdADR = new AccomTypeCodeADR();
        stdADR.setAccomTypeCode(stdAccomType.getAccomTypeCode());
        stdADR.setRoomRevenue(BigDecimal.ONE);
        stdADR.setRoomsSold(BigDecimal.ONE);
        AccomTypeCodeADR steADR = new AccomTypeCodeADR();
        steADR.setAccomTypeCode(steAccomType.getAccomTypeCode());
        steADR.setRoomRevenue(BigDecimal.TEN);
        steADR.setRoomsSold(BigDecimal.ONE);
        String accomTypeCodes = stdAccomType.getAccomTypeCode() + "," + dlxAccomType.getAccomTypeCode() + "," + steAccomType.getAccomTypeCode();
        List<AccomTypeCodeADR> accomTypeCodeAdrs = Arrays.asList(stdADR, steADR);
        when(configParamService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        lenient().when(crudService.<AccomTypeCodeADR>findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR, QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.START_DATE, DateUtil.addYearsToDate(caughtUpDate, -1)).and(AccomTypeCodeADR.END_DATE, caughtUpDate).and(AccomTypeCodeADR.ACCOM_TYPE_CODES, accomTypeCodes).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, 0).parameters())).thenReturn(accomTypeCodeAdrs);
        List<AccomTypeADR> accomTypeADRs = service.getAllActiveAccomTypeADRs();
        AccomTypeADR dlxAccomTypeADR = accomTypeADRs.get(0);
        assertEquals(dlxAccomType, dlxAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.ZERO, dlxAccomTypeADR.getBookingADR());
        AccomTypeADR stdAccomTypeADR = accomTypeADRs.get(1);
        assertEquals(stdAccomType, stdAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.ONE.setScale(2), stdAccomTypeADR.getBookingADR());
        AccomTypeADR steAccomTypeADR = accomTypeADRs.get(2);
        assertEquals(steAccomType, steAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.TEN.setScale(2), steAccomTypeADR.getBookingADR());
        verify(crudService).findByNamedQuery(AccomType.ALL_ACTIVE_NON_DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(dateService).getCaughtUpDate();
        verify(crudService).findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR, QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.START_DATE, DateUtil.addYearsToDate(caughtUpDate, -1)).and(AccomTypeCodeADR.END_DATE, caughtUpDate).and(AccomTypeCodeADR.ACCOM_TYPE_CODES, accomTypeCodes).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, 0).parameters());
    }

    @Test
    public void getUnassignedAccomTypeADRs() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType stdAccomType = new AccomType();
        stdAccomType.setAccomTypeCode("STD");
        AccomType dlxAccomType = new AccomType();
        dlxAccomType.setAccomTypeCode("DLX");
        AccomType steAccomType = new AccomType();
        steAccomType.setAccomTypeCode("STE");
        List<AccomType> accomTypes = Arrays.asList(stdAccomType, dlxAccomType, steAccomType);
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        Date caughtUpDate = new Date();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        AccomTypeCodeADR stdADR = new AccomTypeCodeADR();
        stdADR.setAccomTypeCode(stdAccomType.getAccomTypeCode());
        stdADR.setRoomRevenue(BigDecimal.ONE);
        stdADR.setRoomsSold(BigDecimal.ONE);
        AccomTypeCodeADR steADR = new AccomTypeCodeADR();
        steADR.setAccomTypeCode(steAccomType.getAccomTypeCode());
        steADR.setRoomRevenue(BigDecimal.TEN);
        steADR.setRoomsSold(BigDecimal.ONE);
        String accomTypeCodes = stdAccomType.getAccomTypeCode() + "," + dlxAccomType.getAccomTypeCode() + "," + steAccomType.getAccomTypeCode();
        List<AccomTypeCodeADR> accomTypeCodeAdrs = Arrays.asList(stdADR, steADR);
        when(configParamService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        lenient().when(crudService.<AccomTypeCodeADR>findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR, QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.START_DATE, DateUtil.addYearsToDate(caughtUpDate, -1)).and(AccomTypeCodeADR.END_DATE, caughtUpDate).and(AccomTypeCodeADR.ACCOM_TYPE_CODES, accomTypeCodes).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, 0).parameters())).thenReturn(accomTypeCodeAdrs);
        List<AccomTypeADR> accomTypeADRs = service.getUnassignedAccomTypeADRs();
        AccomTypeADR dlxAccomTypeADR = accomTypeADRs.get(0);
        assertEquals(dlxAccomType, dlxAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.ZERO, dlxAccomTypeADR.getBookingADR());
        AccomTypeADR stdAccomTypeADR = accomTypeADRs.get(1);
        assertEquals(stdAccomType, stdAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.ONE.setScale(2), stdAccomTypeADR.getBookingADR());
        AccomTypeADR steAccomTypeADR = accomTypeADRs.get(2);
        assertEquals(steAccomType, steAccomTypeADR.getAccomType());
        assertEquals(BigDecimal.TEN.setScale(2), steAccomTypeADR.getBookingADR());
        verify(crudService).findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(dateService).getCaughtUpDate();
        verify(crudService).findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR, QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.START_DATE, DateUtil.addYearsToDate(caughtUpDate, -1)).and(AccomTypeCodeADR.END_DATE, caughtUpDate).and(AccomTypeCodeADR.ACCOM_TYPE_CODES, accomTypeCodes).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, 0).parameters());
    }

    @Test
    public void saveAccomType() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType accomType = new AccomType();
        service.saveAccomType(accomType);
        verify(crudService).save(accomType);
    }

    @Test
    public void saveAccomTypesTest() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        Set<AccomType> accomTypes = new HashSet<>();
        AccomType accomType = new AccomType();
        accomTypes.add(accomType);
        service.saveAccomTypes(accomTypes);
        verify(crudService).save(accomTypes);
    }

    @Test
    public void isAdvancedPriceRankingEnabled() {
        when(configParamService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value())).thenReturn(true);
        assertTrue(service.isAdvancedPriceRankingEnabled());
        verify(configParamService).getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value());
    }

    @Test
    public void testDeleteAll() {
        TenantCrudServiceBean tenantCrudServiceBean = mock(TenantCrudServiceBean.class);
        inject(service, "tenantCrudService", tenantCrudServiceBean);
        service.deleteAllAccomTypes();
        verify(tenantCrudServiceBean).deleteAll(CostofWalkDefault.class);
        verify(tenantCrudServiceBean).deleteAll(OverbookingAccom.class);
        verify(tenantCrudServiceBean).deleteAll(AccomType.class);
    }

    @Test
    public void defaultMinimimumPriceDifferentials() {
        TenantCrudServiceBean tenantCrudServiceBean = mock(TenantCrudServiceBean.class);
        inject(service, "tenantCrudService", tenantCrudServiceBean);
        when(tenantCrudServiceBean.findOne(AccomClassMinPriceDiff.class)).thenReturn(null);
        service.defaultMinimimumPriceDifferentials();
        verify(tenantCrudServiceBean).executeUpdateByNativeQuery(AccommodationService.DEFAULT_MIN_PRICE_DIFFERENTIALS, QueryParameter.with("defaultMinPriceDiff", new BigDecimal("0.01")).parameters());
    }

    @Test
    public void defaultMinimimumPriceDifferentialsWithExistingAccomClassMinPriceDiff() {
        TenantCrudServiceBean tenantCrudServiceBean = mock(TenantCrudServiceBean.class);
        inject(service, "tenantCrudService", tenantCrudServiceBean);
        when(tenantCrudServiceBean.findOne(AccomClassMinPriceDiff.class)).thenReturn(new AccomClassMinPriceDiff());
        service.defaultMinimimumPriceDifferentials();
    }

    private List<AccomClassSummary> getAccomClassDummyData() {
        return new ArrayList<AccomClassSummary>() {

            {
                add(new AccomClassSummary(1, "STN"));
                add(new AccomClassSummary(2, "Not assigned"));
            }
        };
    }

    private List<AccomTypeSummary> getAccomTypeSummaries() {
        return new ArrayList<AccomTypeSummary>() {

            {
                add(new AccomTypeSummary(1, "King"));
                add(new AccomTypeSummary(2, "Queen"));
                add(new AccomTypeSummary(3, "Double"));
            }
        };
    }

    @Test
    public void testDeleteAccomClassInventorySharing() {
        AccommodationService service = new AccommodationService();
        CrudService crudService = mock(CrudService.class);
        inject(service, "tenantCrudService", crudService);
        InOrder inOrder = Mockito.inOrder(crudService);
        service.deleteAccomClassInventorySharing();
        inOrder.verify(crudService).deleteAll(AccomClassInventorySharing.class);
        inOrder.verify(crudService).deleteAll(InventorySharingRank.class);
        inOrder.verify(crudService).deleteAll(AccomClassSharingGroup.class);
    }

    @Test
    public void testGetComponentParts() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        int crAccomTypeId = 11;
        service.getComponentRoomsMapping(crAccomTypeId);
        verify(crudService).findByNamedQuery(CRAccomTypeMapping.GET_COMPONENT_ROOM_MAPPINGS, QueryParameter.with("crAccomTypeId", crAccomTypeId).parameters());
    }

    @Test
    public void testSaveOrEditAccomTypeSaveScenario() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType accomType = new AccomType();
        service.saveOrEditAccomType(accomType);
        verify(crudService).save(accomType);
        verify(crudService, times(0)).findByNamedQuery(anyString(), anyMap());
    }

    @Test
    public void testSaveOrEditAccomTypeEditScenario() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        AccomType existingAccomType = new AccomType();
        existingAccomType.setId(999);
        existingAccomType.setAccomTypeCode("OldCode");
        existingAccomType.setName("OldName");
        existingAccomType.setDescription("Old Description");
        existingAccomType.setAccomTypeCapacity(50);
        List<Object> accomTypeList = new ArrayList<>();
        accomTypeList.add(existingAccomType);
        when(crudService.findByNamedQuery(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", 999).parameters())).thenReturn(accomTypeList);
        AccomType updatedAccomType = new AccomType();
        updatedAccomType.setId(999);
        updatedAccomType.setAccomTypeCode("NewCode");
        updatedAccomType.setName("NewName");
        updatedAccomType.setDescription("New Description");
        updatedAccomType.setAccomTypeCapacity(100);
        service.saveOrEditAccomType(updatedAccomType);
        verify(crudService).findByNamedQuery(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", 999).parameters());
        verify(crudService).save(updatedAccomType);
    }

    @Test
    public void testDeleteAccomTypeById() {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        int accomTypeId = 11;
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeId).parameters());
        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType);
        service.deleteAccomTypeById(accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Accom_Type_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Accom_Type_Supplement_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CostofWalk_Default_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CostofWalk_Season_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CP_Cfg_AC_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CP_Cfg_Base_AT_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CP_Cfg_Offset_AT_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CR_Out_Of_Order_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Daily_Bar_Config_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Overbooking_Accom_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Overbooking_Accom_Season_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Rate_Qualified_Details_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from Rate_Unqualified_Details_AUD where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CP_Cfg_AC where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNativeQuery("delete from CP_Cfg_Base_AT where Accom_Type_ID = " + accomTypeId);
        verify(crudService).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType).parameters());
        verify(crudService).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES, QueryParameter.with("accomTypes", accomTypes).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(crudService).executeUpdateByNamedQuery(CRAccomTypeMapping.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(DailyBarConfig.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(ExtendedStayProductDefinition.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(RateUnqualifiedDetails.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(RateQualifiedDetails.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(CostofWalkSeason.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(CostofWalkDefault.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(OverbookingAccomSeason.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(OverbookingAccom.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(AccomType.DELETE_ACCOM_TYPE_BY_ID, QueryParameter.with("id", accomTypeId).parameters());
        verify(crudService).executeUpdateByNamedQuery(OutOfOrderOverride.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType).parameters());
    }

    @Test
    public void testdeleteCloseHighestBarForMovedAccomTypes() {
        OldRoomClassConfigDto oldRoomClassConfigDto1 = new OldRoomClassConfigDto();
        oldRoomClassConfigDto1.setAccomClassId(101);
        oldRoomClassConfigDto1.setAccomTypeId(111);
        OldRoomClassConfigDto oldRoomClassConfigDto2 = new OldRoomClassConfigDto();
        oldRoomClassConfigDto2.setAccomClassId(101);
        oldRoomClassConfigDto2.setAccomTypeId(112);
        List<OldRoomClassConfigDto> listOfMovedAccomTypes = new ArrayList<>();
        listOfMovedAccomTypes.add(oldRoomClassConfigDto1);
        listOfMovedAccomTypes.add(oldRoomClassConfigDto2);
        service.deleteCloseHighestBarForAccomTypes(listOfMovedAccomTypes);
        verify(closeHighestBarService).deleteCloseHighestBarForAccomClasses(Mockito.eq(getExpectedMap()));
    }

    @Test
    public void testAccomTableSchema() {
        List<String> countCopy;
        PacmanThreadLocalContextHolder.setWorkContext(PacmanWorkContextTestHelper.createWorkContext("11403", 2, "BSTN", 5, "H1"));
        List<String> TablesExpected = new LinkedList<>(Arrays.asList("Accom_Activity", "Accom_Type", "Accom_Type_AUD", "Accom_Type_Supplement", "Accom_Type_Supplement_AUD", "Analytic_Mkt_Accom_Los_Inv", "Business_Accom_FCST", "CostofWalk_Default", "CostofWalk_Default_AUD", "CostofWalk_Season", "CostofWalk_Season_AUD", "CP_Cfg_AC", "CP_Cfg_AC_AUD", "CP_Cfg_Base_AT", "CP_Cfg_Base_AT_Draft", "CP_Cfg_Base_AT_AUD", "CP_Cfg_Offset_AT", "CP_Cfg_Offset_AT_AUD","CP_Cfg_Offset_AT_FloorCeil_AUD", "CP_Decision_Bar_Output", "CP_Decision_Bar_Output_OVR", "CP_Pace_Decision_Bar_Output", "CP_Pace_Decision_Bar_Output_Differential", "CR_Accom_Activity", "CR_Mkt_Accom_Activity", "CR_Out_Of_Order", "CR_Out_Of_Order_AUD", "Current_Mkt_Accom_activity", "Daily_Bar_Config", "Daily_Bar_Config_AUD", "Decision_Ack_Status", "Decision_Bar_Output_OVR_Details", "Decision_COW_Value_OVR", "Decision_Dailybar_Output", "Decision_FPLOS_By_Hierarchy", "Decision_FPLOS_By_Rank", "Decision_FPLOS_By_RoomType", "Decision_LRA_FPLOS", "Decision_LRA_minLOS", "Decision_LRV_AT", "Decision_MINLOS", "Decision_Ovrbk_Accom", "Decision_Ovrbk_Accom_OVR", "Decision_Ovrbk_Accom_OVR_AUD", "Decision_Qualified_FPLOS", "Decision_Restrict_Highest_Bar", "Decision_Restrict_Highest_Bar_OVR", "ES_Product_Definition", "ES_Rate_Unqualified_Details", "ES_Rate_Unqualified_Override", "ES_Rate_Unqualified_Override_AUD", "FS_Cfg_Guest_Room_Category", "FS_Cfg_Guest_Room_Category_AUD", "Group_Block", "Grp_Evl_Arr_DT_AT", "Grp_Evl_Arr_DT_AT_AUD", "Grp_Evl_Room_Type", "Grp_Evl_Room_Type_AUD", "Grp_Prc_Cfg_Accom_Type", "Grp_Prc_Cfg_Accom_Type_AUD", "Grp_Prc_Cfg_Base_AT", "Grp_Prc_Cfg_Base_AT_AUD", "Group_Floor_OVR", "Max_Allowed_Occupancy_AT", "Max_Allowed_Occupancy_AT_AUD", "Mkt_Accom_Activity", "Occ_FCST_Org", "Occupancy_FCST", "Out_Of_Order_Override", "Out_Of_Order_Override_AUD", "Overbooking_Accom", "Overbooking_Accom_AUD", "Overbooking_Accom_Season", "Overbooking_Accom_Season_AUD", "PACE_Accom_Activity",  "PACE_Accom_Occupancy_FCST", "PACE_Accom_Occupancy_FCST_NOTIFICATION", "PACE_CR_Accom_Activity", "PACE_Dailybar_Output", "Pace_Decision_LRA_FPLOS", "Pace_Decision_LRA_minLOS", "PACE_FPLOS_By_Hierarchy", "PACE_FPLOS_By_Rank", "PACE_FPLOS_By_RoomType", "Pace_Group_Block", "PACE_LRV_AT", "PACE_MINLOS", "PACE_Ovrbk_Accom", "Pace_Ovrbk_Accom_NOTIFICATION", "PACE_Ovrbk_Accom_Upload", "PACE_Qualified_FPLOS", "Post_departure_revenue", "Product_AT", "Revenue_Stream_Detail", "Product_AT_AUD", "Rate_Qualified_Details", "Rate_Qualified_Details_AUD", "Rate_Unqualified_Details", "Rate_Unqualified_Details_AUD", "Stage_Group_Block", "Stage_Occupancy_Summary", "Stage_Transaction", "Temp_stage_occupancy_summary_PSAT_CSAT", "Trans_RollUp_NonPace", "Wash_Ind_Group_Fcst", "Wash_Ind_Group_Fcst_OVR", "Wash_Ind_Group_Fcst_OVR_AUD", "Last_Optimization_Accom_Activity", "Last_Optimization_Mkt_Accom_Activity", "Reservation_Night", "Reservation_Night_Change", "LDB_Hybrid_Accom_Type", "CP_Recommended_Floor_Ceiling", "ES_LongLos_Price_Cfg", "ES_LongLos_Price_Cfg_AUD", "ES_Recommended_Price", "Manual_Restriction_Accom_OVR", "Accom_Class_Proposed", "PACE_Manual_Restriction_Accom_OVR", "PROFIT_ADJ_FCST", "MVCR_Rate_AT"));
        List<String> countList = tenantCrudService().findByNativeQuery("Select distinct UPPER(TABLE_NAME) From INFORMATION_SCHEMA.COLUMNS Where column_name = 'Accom_Type_ID' and table_name not in (select distinct table_name from INFORMATION_SCHEMA.VIEWS);");
        countCopy = cloneList(countList);
        Function<List<String>, List<String>> toUpperCase = list -> list.stream().map(String::toUpperCase).collect(toList());
        countList.removeAll(toUpperCase.apply(TablesExpected));
        final List<String> expectedTables = toUpperCase.apply(TablesExpected);
        expectedTables.removeAll(toUpperCase.apply(countCopy));
        assertTrue(expectedTables.isEmpty(), "Failed : " + expectedTables + " Table removed from the db schema. Please c        assertTrue(countList.isEmpty(), \"Failed : \" + countList.toString() + \" Table newly added in the db schema. Please check if you need to modify 'deleteAccomTypeById' method in AccommodationService.\");\nheck if you need to modify 'deleteAccomTypeById' method in AccommodationService.");
    }

    @Test
    public void shouldGetAccomTypesByAccomClass() {
        ArrayList<Integer> accomClassIds = new ArrayList<>();
        accomClassIds.add(1);
        accomClassIds.add(2);
        Map<Integer, List<AccomType>> accomTypesByAccomClass = service.getAccomTypesByAccomClass(accomClassIds);
        Assertions.assertNotNull(accomTypesByAccomClass.get(1));
        Assertions.assertNotNull(accomTypesByAccomClass.get(2));
    }

    @Test
    public void shouldGetDisplayableAccomTypesByAccomClass() {
        AccomType dblAccomType = service.getAccomTypeById(6);
        dblAccomType.setDisplayStatusId(Constants.INACTIVE_DISPLAY_STATUS_ID);
        tenantCrudService().flushAndClear();
        ArrayList<Integer> accomClassIds = new ArrayList<>();
        accomClassIds.add(2);
        Map<Integer, List<AccomType>> accomTypesByAccomClass = service.getDisplayableAccomTypesByAccomClass(accomClassIds, Constants.ACTIVE_DISPLAY_STATUS_ID);
        Assertions.assertNotNull(accomTypesByAccomClass.get(2));
        Assertions.assertFalse(accomTypesByAccomClass.get(2).stream().map(AccomType::getAccomTypeCode).collect(toList()).contains("DBL"));
    }

    @Test
    public void getAllAssignedAccomTypesWithCapacity() throws Exception {
        CrudService crudService = Mockito.mock(CrudService.class);
        service.setTenantCrudService(crudService);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(new AccomType());
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_VALID_CAPACITY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        List<AccomType> retAccomTypes = service.getAllAssignedAccomTypesWithCapacity();
        assertSame(accomTypes, retAccomTypes);
        verify(crudService).<AccomType>findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_VALID_CAPACITY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private Map<Integer, Set<Integer>> getExpectedMap() {
        Map<Integer, Set<Integer>> expectedMap = new HashMap<>();
        Set<Integer> expectedSet = new HashSet<>();
        expectedSet.add(111);
        expectedSet.add(112);
        expectedMap.put(101, expectedSet);
        return expectedMap;
    }

    private List cloneList(List<String> tobeCloned) {
        List<String> tempList = new ArrayList<>(tobeCloned.size());
        tempList.addAll(tobeCloned);
        return tempList;
    }

    private void setUpStageDataToCreateMissingOrReactivateInactiveRTs() {
        DataLoadMetadata dlm1 = new DataLoadMetadata();
        dlm1.setIncomingFileTypeCode("CTAT");
        DataLoadMetadata savedDlmList = tenantCrudService().save(dlm1);
        OperaOccupancySummary oos1 = new OperaOccupancySummary();
        oos1.setRoomType(RT_K);
        oos1.setPhysicalRooms(10);
        oos1.setOperaDataLoadMetadata(savedDlmList);
        OperaOccupancySummary oos2 = new OperaOccupancySummary();
        oos2.setRoomType("AT");
        oos2.setPhysicalRooms(10);
        oos2.setOperaDataLoadMetadata(savedDlmList);
        tenantCrudService().save(Arrays.asList(oos1, oos2));
    }

    @Test
    public void testGetAccomTypeDtos() {
        service.multiPropertyCrudService = multiPropertyCrudService();
        multiPropertyCrudService().executeNativeUpdateOnSingleProperty(6, "update Accom_Type set Accom_Type_Capacity=0 where Accom_Type_Code='D'", null);
        List<AccomTypeDto> accomTypeDtos = service.getAccomTypeDtos(6, "A,D, STE");
        assertEquals(2, accomTypeDtos.size());
        assertEquals(accomTypeDtos.get(0).getAccomTypeCode(), "STE");
        assertEquals(accomTypeDtos.get(1).getAccomTypeCode(), "D");
        assertEquals(new BigDecimal(120), accomTypeDtos.get(0).getCapacity());
        assertEquals(BigDecimal.ZERO, accomTypeDtos.get(1).getCapacity());
    }

    @Test
    public void testFindRealPseudoRoomTypesNoAccomTypeData() {
        Set ngiPseudoRoomTypes = getPseudoRoomTypes();
        final Set realPseudoRoomTypes = service.findRealPseudoRoomTypes(ngiPseudoRoomTypes);
        assertEquals(3, realPseudoRoomTypes.size());
        assertTrue(realPseudoRoomTypes.contains("PM"));
        assertTrue(realPseudoRoomTypes.contains("PD"));
        assertTrue(realPseudoRoomTypes.contains("PX"));
    }

    @Test
    public void testFindRealPseudoRoomTypes() {
        Set ngiPseudoRoomTypes = getPseudoRoomTypes();
        List accomTypeList = new ArrayList<AccomType>();
        final AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("PM");
        accomTypeList.add(accomType);
        TenantCrudServiceBean tenantCrudServiceBean = mock(TenantCrudServiceBean.class);
        inject(service, "tenantCrudService", tenantCrudServiceBean);
        when(tenantCrudServiceBean.findByNamedQuery(AccomType.ALL_BY_CODES, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypeList", ngiPseudoRoomTypes).parameters())).thenReturn(accomTypeList);
        final Set realPseudoRoomTypes = service.findRealPseudoRoomTypes(ngiPseudoRoomTypes);
        assertEquals(2, realPseudoRoomTypes.size());
        assertFalse(realPseudoRoomTypes.contains("PM"));
        assertTrue(realPseudoRoomTypes.contains("PD"));
        assertTrue(realPseudoRoomTypes.contains("PX"));
    }

    @Test
    public void shouldFindAccomTypesWithValidCapacityAndActiveDisplayStatus() {
        standardAccomClass.setSystemDefault(0);
        AccomType zeroCapacityAccomType = createZeroCapacityAccomType();
        AccomType nzCapacityAccomType = createNonZeroCapacityAccomType();
        tenantCrudService().save(standardAccomClass);
        tenantCrudService().save(Arrays.asList(zeroCapacityAccomType, nzCapacityAccomType));
        List<AccomType> accomTypes = service.getActiveAccomTypesWithValidCapacityAndDisplayStatus();
        assertFalse(accomTypes.isEmpty());
        List<String> accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(toList());
        assertTrue(accomTypeCodes.contains(nzCapacityAccomType.getAccomTypeCode()));
        assertFalse(accomTypeCodes.contains(zeroCapacityAccomType.getAccomTypeCode()));
    }

    @Test
    public void shouldFindRoHAccomTypesWithValidCapacityAndActiveDisplayStatus() {
        AccomType rohAndZeroCapacityAccomType = createRoHAndZeroCapacityAccomType();
        AccomType rohAndNzCapacityAccomType = createRoHAndNonZeroCapacityAccomType();
        tenantCrudService().save(Arrays.asList(rohAndZeroCapacityAccomType, rohAndNzCapacityAccomType));
        List<AccomType> accomTypes = service.getROHAccomTypesWithValidCapacityAndDisplayStatus();
        assertFalse(accomTypes.isEmpty());
        List<String> accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(toList());
        assertTrue(accomTypeCodes.contains(rohAndNzCapacityAccomType.getAccomTypeCode()));
        assertFalse(accomTypeCodes.contains(rohAndZeroCapacityAccomType.getAccomTypeCode()));
    }

    @Test
    public void shouldFindAccomTypesByAccomClassAndDisplayStatus() {
        AccomType rt1AccomType = createAnAccomType("RT1");
        AccomType rt2AccomType = createAnAccomType("RT2");
        rt2AccomType.setDisplayStatusId(2);
        AccomType rt3AccomType = createAnAccomType("RT3");
        rt3AccomType.setAccomClass(deluxeAccomClass);
        tenantCrudService().save(Arrays.asList(rt1AccomType, rt2AccomType, rt3AccomType));
        List<AccomType> accomTypes = service.getAccomTypesBy(standardAccomClass.getId(), 1);
        assertFalse(accomTypes.isEmpty());
        List<String> accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(toList());
        assertTrue(accomTypeCodes.contains("RT1"));
        assertFalse(accomTypeCodes.contains("RT2"));
        assertFalse(accomTypeCodes.contains("RT3"));
    }

    private Set getPseudoRoomTypes() {
        Set<String> ngiPseudoRoomTypes = new HashSet();
        ngiPseudoRoomTypes.add("PM");
        ngiPseudoRoomTypes.add("PD");
        ngiPseudoRoomTypes.add("PX");
        return ngiPseudoRoomTypes;
    }

    @Test
    public void getAllDiscontinuedRoomTypes() {
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(5);
        PacmanWorkContextHelper.setWorkContext(workContext);
        List<AccomType> allDiscontinuedRoomTypes = service.getAllDiscontinuedRoomTypes();
        assertEquals(0, allDiscontinuedRoomTypes.size());
        AccomType at = service.getAccomTypeById(8);
        at.setDisplayStatusId(2);
        tenantCrudService().save(at);
        flushAndClear();
        allDiscontinuedRoomTypes = service.getAllDiscontinuedRoomTypes();
        assertEquals(1, allDiscontinuedRoomTypes.size());
        assertTrue(allDiscontinuedRoomTypes.get(0).getId() == 8);
    }

    private AccomType createAnAccomType(String code) {
        AccomType accomType = new AccomType();
        accomType.setPropertyId(property.getId());
        accomType.setStatusId(1);
        accomType.setAccomTypeCapacity(10);
        accomType.setAccomTypeCode(code);
        accomType.setAccomClass(standardAccomClass);
        accomType.setSystemDefault(0);
        return accomType;
    }

    private AccomType createNonZeroCapacityAccomType() {
        AccomType nzCapacityAccomType = new AccomType();
        nzCapacityAccomType.setPropertyId(property.getId());
        nzCapacityAccomType.setStatusId(1);
        nzCapacityAccomType.setAccomTypeCapacity(10);
        nzCapacityAccomType.setAccomTypeCode("NZC");
        nzCapacityAccomType.setAccomClass(standardAccomClass);
        nzCapacityAccomType.setSystemDefault(0);
        return nzCapacityAccomType;
    }

    private AccomType createZeroCapacityAccomType() {
        AccomType zeroCapacityAccomType = new AccomType();
        zeroCapacityAccomType.setPropertyId(property.getId());
        zeroCapacityAccomType.setStatusId(1);
        zeroCapacityAccomType.setAccomTypeCapacity(0);
        zeroCapacityAccomType.setAccomTypeCode("ZC");
        zeroCapacityAccomType.setDisplayStatusId(2);
        zeroCapacityAccomType.setAccomClass(standardAccomClass);
        zeroCapacityAccomType.setSystemDefault(0);
        return zeroCapacityAccomType;
    }

    private AccomType createRoHAndNonZeroCapacityAccomType() {
        AccomType rohAndNzCapacityAccomType = new AccomType();
        rohAndNzCapacityAccomType.setPropertyId(property.getId());
        rohAndNzCapacityAccomType.setStatusId(1);
        rohAndNzCapacityAccomType.setAccomTypeCapacity(10);
        rohAndNzCapacityAccomType.setAccomTypeCode("RNZC");
        rohAndNzCapacityAccomType.setRohType(1);
        rohAndNzCapacityAccomType.setAccomClass(standardAccomClass);
        rohAndNzCapacityAccomType.setSystemDefault(0);
        return rohAndNzCapacityAccomType;
    }

    private AccomType createRoHAndZeroCapacityAccomType() {
        AccomType rohAndZeroCapacityAccomType = new AccomType();
        rohAndZeroCapacityAccomType.setPropertyId(property.getId());
        rohAndZeroCapacityAccomType.setStatusId(1);
        rohAndZeroCapacityAccomType.setAccomTypeCapacity(0);
        rohAndZeroCapacityAccomType.setAccomTypeCode("RZC");
        rohAndZeroCapacityAccomType.setDisplayStatusId(2);
        rohAndZeroCapacityAccomType.setRohType(1);
        rohAndZeroCapacityAccomType.setAccomClass(standardAccomClass);
        rohAndZeroCapacityAccomType.setSystemDefault(0);
        return rohAndZeroCapacityAccomType;
    }

    @Test
    public void fetchingAccomClasseSummariesExcludingSystemDefault() {
        stdQueenRoom.setAccomClass(deluxeAccomClass);
        tenantCrudService().save(stdQueenRoom);
        deluxeAccomClass.getAccomTypes().add(stdQueenRoom);
        tenantCrudService().save(deluxeAccomClass);
        List<AccomClassSummary> accomClassSummaries = service.getAllAccomClassSummaries();
        verifyAccomClassSummaryData(DELUXE, true, accomClassSummaries);
    }

    @Test
    public void fetchingAccomClassSummariesByInvGrpIdExcludingSystemDefault() {
        InventoryGroupDetails inventoryGroupDetails1 = UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails();
        InventoryGroup inventoryGroup = inventoryGroupDetails1.getInventoryGroup();
        InventoryGroupDetails inventoryGroupDetails2 = UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails();
        inventoryGroupDetails2.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails2.getAccomClass().setSystemDefault(1);
        tenantCrudService().save(inventoryGroupDetails2);
        List<AccomClassSummary> accomClassSummaries = service.getAllAccomClassSummariesForInvGrp(inventoryGroup.getId());
        verifyAccomClassSummaryData(inventoryGroupDetails1.getAccomClass().getName(), false, accomClassSummaries);
    }

    private void verifyAccomClassSummaryData(String name, boolean isAccomTypeAssigned, List<AccomClassSummary> result) {
        assertEquals(1, result.size());
        assertEquals(name, result.get(0).getName());
        verify(invalidateOverridesService, never()).hasOverrides(anyInt(), anyObject());
        assertEquals(isAccomTypeAssigned, result.get(0).isAccomTypesAssigned());
    }

    @Test
    public void getAllAccomTypeDetailsIncludingPseudoRooms() {
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6 where accom_type_code in ('DLX','STE','DBL')");
        List<AccomType> allAccomTypeDetailsIncludingPseudoRooms = service.getAllAccomTypeDetailsIncludingPseudoRooms();
        assertEquals(1, allAccomTypeDetailsIncludingPseudoRooms.stream().filter(accomType -> accomType.getAccomTypeCode().equalsIgnoreCase("DLX")).collect(toList()).size());
        assertEquals(1, allAccomTypeDetailsIncludingPseudoRooms.stream().filter(accomType -> accomType.getAccomTypeCode().equalsIgnoreCase("STE")).collect(toList()).size());
        assertEquals(1, allAccomTypeDetailsIncludingPseudoRooms.stream().filter(accomType -> accomType.getAccomTypeCode().equalsIgnoreCase("DBL")).collect(toList()).size());
    }

    @Test
    public void testPopulatingADRInATable() {
        LocalDate arrivalDate = new LocalDate().minusDays(30);
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set Individual_Status='CO', arrival_dt = '" + arrivalDate + "',booked_accom_type_code ='DLX' ,departure_dt='" + new LocalDate().minusDays(1) + "'");
        service.populateAccomTypeADR();
        List<AccomTypeCodeADR> result = tenantCrudService().findByNativeQuery("select Accom_Type_Code as accomTypeCode,Rooms_Sold as roomsSold,Room_Revenue as roomRevenue from Accom_Type_ADR_Final where Accom_Type_Code='DLX'", new HashMap<>(), AccomTypeCodeADR.class);
        assertNotNull(result);
    }

    @Test
    public void getAccomTypeByCode() {
        AccomClass accomClass = new AccomClass(1, 1, null);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setAccomTypeCode("QUEEEN");
        accomType.setDescription("Queen Room");
        accomType.setPropertyId(5);
        accomType.setRohType(new Integer(0));
        accomType.setStatusId(new Integer(1));
        accomType.setSystemDefault(new Integer(1));
        tenantCrudService().save(accomType);

        AccomType actual = service.getAccomTypeByCode("QUEEEN");
        assertEquals(accomType.getName(), actual.getName());
        assertEquals(accomType.getPropertyId(), actual.getPropertyId());
        assertEquals(accomType.getAccomClass(), actual.getAccomClass());
        assertEquals(accomType.getDescription(), actual.getDescription());
    }

    @Test
    void getInactiveAccomClasses() {
        tenantCrudService().executeUpdateByNativeQuery("update accom_class set status_id=2 where accom_class_code in ('STE', 'Unassigned') ");
        PacmanWorkContextHelper.setPropertyId(TestProperty.H1.getId());
        List<AccomClass> inactiveAccomClasses = service.getInactiveAccomClasses();
        assertEquals(1, inactiveAccomClasses.size());
        assertEquals("STE", inactiveAccomClasses.get(0).getCode());
    }

    @Test
    void getAccomClassByCode() {
        PacmanWorkContextHelper.setPropertyId(TestProperty.H1.getId());
        AccomClass accomClass = service.getAccomClassByCode("STE");
        assertEquals("STE", accomClass.getCode());
    }

    @Test
    void deletePricingConfigurationsByAccomTypes() {
        List<AccomType> accomTypesMoved = Collections.singletonList(new AccomType());
        service.deletePricingConfigurationsByAccomTypes(accomTypesMoved);
        verify(pricingConfigurationService).deletePricingConfigurationsByRoomTypes(new HashSet<>(accomTypesMoved));
    }

    Product addProduct(AgileRatesProductTypeEnum productType, String name) {
        Product product = new Product();
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        product.setType(productType.getValue());
        product.setName(name);
        product.setDescription(name);
        product.setDependentProductId(1);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setMinDTA(0);
        product.setMinLOS(1);
        product.setSystemDefault(false);
        product.setOffsetForExtraAdult(true);
        product.setOffsetForExtraChild(true);
        product.setUpload(true);
        product.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        product.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        product.setCentrallyManaged(false);
        product.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        product.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        product.setFloorPercentage(null);
        product.setFloorType(FloorType.FIXED_RATE);
        product.setRateShoppingLOSMin(1);
        product.setRateShoppingLOSMax(1);
        product.setMaxRooms(-1);
        product.setMinRooms(-1);
        product.setUseInSmallGroupEval(false);
        product.setFreeNightEnabled(false);
        product.setFreeUpgradeEnabled(false);
        ProductCode pc = new ProductCode(2);
        product.setProductCode(pc);
        return tenantCrudService().save(product);
    }

    @Test
    public void getAllAccomClassSummariesForProduct() {
        Product product = addProduct(AgileRatesProductTypeEnum.FIXED_ABOVE_BAR, "P1");
        tenantCrudService().save(product);
        String accomTypeName = "DLX";
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomTypeName).parameters());
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setAccomType(accomType);
        productAccomType.setProduct(product);
        tenantCrudService().save(productAccomType);
        List<AccomClassSummary> allAccomClassSummariesForProduct = service.getAllAccomClassSummariesForProduct(product.getId());
        assertEquals(1, allAccomClassSummariesForProduct.size());
        AccomClassSummary acSummary = allAccomClassSummariesForProduct.get(0);
        assertEquals(accomTypeName, acSummary.getName());
    }

    @Test
    public void getAllAssignedValidAccomTypesByRankOrder() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES)).thenReturn("Q,STE");
        List<RoomTypeDto> allAssignedValidAccomTypesByRankOrder = service.getAllAssignedValidCapacityNonComponentNonPseudoAccomTypesByRankOrder();
        assertEquals("DBL", allAssignedValidAccomTypesByRankOrder.get(0).getAccomTypeCode());
        assertEquals("K", allAssignedValidAccomTypesByRankOrder.get(1).getAccomTypeCode());
        assertEquals("DLX", allAssignedValidAccomTypesByRankOrder.get(2).getAccomTypeCode());
    }

    @Test
    void getCapacityByRoomClass() {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Type_Capacity = 30 where Accom_Class_ID = 2");
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Type_Capacity = 45 where Accom_Class_ID = 3");
        Map<Integer, BigDecimal> capacityByRoomClassWithActiveRCAndRT = service.getCapacityByRoomClass();
        assertEquals(BigDecimal.valueOf(90), capacityByRoomClassWithActiveRCAndRT.get(2));
        assertEquals(BigDecimal.valueOf(45), capacityByRoomClassWithActiveRCAndRT.get(3));

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Status_ID = 2 where Accom_Class_ID = 2 ");
        Map<Integer, BigDecimal> capacityByRoomClassWithInactiveRT = service.getCapacityByRoomClass();
        assertEquals(BigDecimal.valueOf(45), capacityByRoomClassWithActiveRCAndRT.get(3));
        assertFalse(capacityByRoomClassWithInactiveRT.containsKey(2));

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Class set Status_ID = 2 where Accom_Class_ID = 3");
        Map<Integer, BigDecimal> capacityByRoomClassWithInactiveRC = service.getCapacityByRoomClass();
        assertFalse(capacityByRoomClassWithInactiveRC.containsKey(3));
        assertFalse(capacityByRoomClassWithInactiveRC.containsKey(2));
    }

    @Test
    void shouldUpdateROHGroupEvaluationExclusionFlagForAccomClass() {
        deluxeAccomClass.setExcludedForGroupEvaluation(true);
        deluxeAccomClass.setName("ABC");

        service.updateROHGroupEvaluationExclusionFlagFor(deluxeAccomClass);

        AccomClass accomClassFromDB = tenantCrudService().find(AccomClass.class, deluxeAccomClass.getId());
        assertTrue(accomClassFromDB.isExcludedForGroupEvaluation());
        assertEquals(DELUXE, accomClassFromDB.getName());
    }

    @Test
    void testGetOrCreateAccomTypesByCodes_OneExistingAccomTypeOnePseudoRoomTypeAndOneRegularRoomTypeAreNotExisting_ShouldReturnThreeAccomTypesTwoShouldBeCreated() {
        AccomClass unAssignedAccomClass = createUnAssignedAccomClass();
        Set<String> accomCodes = Set.of("SKR", "not_existing_regular", "pseudo_1");
        List<String> pseudoRoomTypes = List.of("pseudo_1");

        List<AccomType> result = service.getOrCreateAccomTypesByCodes(accomCodes, property.getId(), pseudoRoomTypes);

        assertThat(result.stream().map(AccomType::getAccomTypeCode).collect(toList()), containsInAnyOrder(accomCodes.toArray()));
        tenantCrudService().delete(unAssignedAccomClass);
    }

    @Test
    void testGetOccupancyPercentage(){
        java.time.LocalDate arrivalDate = java.time.LocalDate.now().minusDays(30);
        assertNotNull(service.getOccupancyPercentage(arrivalDate));
    }

    @Test
    void testGetOccupancyPercentageIsNull(){
        java.time.LocalDate arrivalDate = java.time.LocalDate.now().minusDays(30);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Activity set Accom_Capacity = 0, Rooms_Not_Avail_Maint = 0, Rooms_Not_Avail_Other = 0 where Occupancy_DT = '"+arrivalDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))+"'");
        assertEquals(0, service.getOccupancyPercentage(arrivalDate).compareTo(BigDecimal.valueOf(0)));
    }
}
