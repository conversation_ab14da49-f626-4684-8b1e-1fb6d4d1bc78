package com.ideas.tetris.pacman.services.investigator.historicaladr;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.AccomTypeSupplementService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class HistoricalAdrCalculatorTest extends AbstractG3JupiterTest {

    private static final String sqlUsingIndividualTrans = new StringBuilder("select Individual_Trans_ID,arrival_dt, departure_dt,Room_Revenue,Accom_Type_ID, Mkt_Seg_id, Individual_Status").append(" FROM [Individual_Trans] indTrans  where ").append(" indTrans.Arrival_DT <= :analysisDate").append(" and :analysisDate < indTrans.Departure_DT").append(" and indTrans.Individual_Status NOT IN ('XX', 'NS') order by Individual_Trans_ID,Arrival_DT,Departure_DT").toString();

    private static final String sqlUsingReservationNightView = "select * from vw_get_room_revenue_from_reservation_night where Arrival_DT <= :analysisDate and :analysisDate <  Departure_DT order by Individual_Trans_ID,Arrival_DT,Departure_DT";

    private final String CORPORATE = "2";

    private final String DOUBLE_ROOM_TYPE = "6";

    private final String QUEEN_ROOM_TYPE = "7";

    private final String CORP = "4";

    private final String DIST = "5";

    private final String DELUXE_ROOM = "5";

    HistoricalAdrCalculator historicalAdrCalculator = new HistoricalAdrCalculator();

    private DateService dateService;

    private PacmanConfigParamsService configParamsService;

    private TaxService taxService;

    private AccomTypeSupplementService accomTypeSupplementService;

    private PricingConfigurationService pricingConfigurationService;

    @BeforeEach
    public void setUp() {
        historicalAdrCalculator.tenantCrudService = tenantCrudService();
        RecurringSpecialEventQueryBuilder recurringSpecialEventQueryBuilder = new RecurringSpecialEventQueryBuilder();
        recurringSpecialEventQueryBuilder.tenantCrudService = tenantCrudService();
        recurringSpecialEventQueryBuilder.dateService = createMockDateService();
        historicalAdrCalculator.dateService = dateService;
        historicalAdrCalculator.recurringSpecialEventQueryBuilder = recurringSpecialEventQueryBuilder;
        configParamsService = mock(PacmanConfigParamsService.class);
        historicalAdrCalculator.configParamsService = configParamsService;
        taxService = mock(TaxService.class);
        historicalAdrCalculator.taxService = taxService;
        accomTypeSupplementService = mock(AccomTypeSupplementService.class);
        historicalAdrCalculator.accomTypeSupplementService = accomTypeSupplementService;
        pricingConfigurationService = mock(PricingConfigurationService.class);
        historicalAdrCalculator.pricingConfigurationService = pricingConfigurationService;
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("112.333333"), historicalAdr);
    }

    @Test
    public void shouldApplyTaxWhileCalculatingHistoricalAdrFor5STLY() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("123.566666"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenTaxIsGivenAsZero() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.ZERO);
        when(taxService.findTax()).thenReturn(tax);
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("112.333333"), historicalAdr);
    }

    @Test
    public void shouldApplySupplementsWhileCalculatingHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        addSupplementsForAccomType("7", "50.00");
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), QUEEN_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("125.000000"), historicalAdr);
    }

    private void addSupplementsForAccomType(String accomTypeId, String value) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Accom_Type_Supplement values(5, " + accomTypeId + ", 1, 2, null, null, " + value + ", " + value + ", " + value + ", " + value + ", " + value + ", " + value + ", " + value + ", 1, 1, getdate(), 1, getdate(), null, 1)");
    }

    @Test
    public void shouldApplyTaxAndSupplementsWhileCalculatingHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        addSupplementsForAccomType("7", "50.00");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), QUEEN_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("136.233333"), historicalAdr);
    }

    @Test
    public void whenSTLYIsBeforeSystemDateThenConsiderOnlyThoseSTLYWhichAreBeforeSystemDateWhileCalculatingHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "200.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "300.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "250.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(stly.plusDays(1));
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("101.666666"), historicalAdr);
    }

    @Test
    public void whenSTLYIsAfterSystemDateThenGoOneMoreYearBackToGetSTLYAndConsiderOnlyThoseSTLYWhichAreBeforeSystemDateWhileCalculatingHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        Date stlyAsOfStlyDate = DateCalculator.calculateDateForLastYear(stlyDate, true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyAsOfStlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyAsOfStlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyAsOfStlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyAsOfStlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyAsOfStlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "200.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "300.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "250.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true));
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("151.000000"), historicalAdr);
    }

    @Test
    public void shouldIgnoreShoulderNightsForOccupancyDayWhileCalculateHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer recurringSpecialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusMonths(1).plusDays(1), today.minusMonths(1).plusDays(3), recurringSpecialEventId);
        addPropertySpecialEventInstance(today.plusDays(1), today.plusDays(3), recurringSpecialEventId);
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldIgnoreShoulderNightsForSTLYDayWhileCalculateHistoricalAdrFor5STLY() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(1, stlyOneWeekPriorDate.plusDays(1), stlyOneWeekPriorDate.plusDays(1), "0", "Holiday2");
        addPropertySpecialEventInstance(stlyOneWeekPriorDate.plusDays(1), stlyOneWeekPriorDate.plusDays(1), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYByConsideringLos() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(2), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(2), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(2), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(2), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(2), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(2), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567896");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(2), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567896");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(2), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(2), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567898");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(2), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567899");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("51.000000"), historicalAdr);
    }

    @Test
    public void shouldIgnoreSTLYWithImpactForecastSpecialEventWhileCalculatingHistoricalADR() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(1, stlyOneWeekPriorDate, stlyOneWeekPriorDate, "0", "Holiday");
        addPropertySpecialEventInstance(stlyOneWeekPriorDate, stlyOneWeekPriorDate, specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "500.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "400.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("101.250000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYEvenWhenOneOfSTLYHasInformationOnlySpecialEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(0, stlyOneWeekPriorDate, stlyOneWeekPriorDate, "0", "Holiday");
        addPropertySpecialEventInstance(stlyOneWeekPriorDate, stlyOneWeekPriorDate, specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "500.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "400.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("171.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenSelectedDateHasImpactForecastOneTimeSpecialEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(1, today, today, "0", "Holiday");
        addPropertySpecialEventInstance(today, today, specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "500.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "400.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("171.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenSelectedDateHasMoreThanOneSpecialEventInstancesInFuture() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(1, today, today, "0", "Holiday");
        addPropertySpecialEventInstance(today, today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1), today.plusYears(1), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "500.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "400.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("171.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrForTheLastPastSpecialEventInstanceWhenSelectedDateHasImpactForecastRecurringEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "0", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        Integer anotherSpecialEventId = addSpecialEvent(1, today, today, "0", "OtherHoliday");
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), anotherSpecialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("25.000000"), historicalAdr);
    }

    @Test
    public void shouldSelectRecurringInstancesBeforeOccupancyDateWhileCalculatingHistoricalAdrForRecurringEvents() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer anotherSpecialEventId = addSpecialEvent(1, today, today, "0", "OtherHoliday");
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), anotherSpecialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), anotherSpecialEventId);
        Integer oneMoreSpecialEventId = addSpecialEvent(1, today, today, "0", "OneMoreHoliday");
        addPropertySpecialEventInstance(today.minusDays(3), today, oneMoreSpecialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), oneMoreSpecialEventId);
        Integer specialEventId = addSpecialEvent(1, today, today, "0", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("25.000000"), historicalAdr);
    }

    @Test
    public void shouldApplyTaxWhileCalculatingHistoricalAdrForTheLastPastSpecialEventInstanceWhenSelectedDateHasImpactForecastRecurringEvent() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("27.500000"), historicalAdr);
    }

    @Test
    public void shouldApplySupplementsWhileCalculatingHistoricalAdrForTheLastPastSpecialEventInstanceWhenSelectedDateHasImpactForecastRecurringEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("35.000000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsFirstDayOfCurrentInstance_Then_CalculateADRForFirstDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1).plusDays(5), specialEventId);
        addPropertySpecialEventInstance(today, today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("25.000000"), historicalAdr);
    }

    @Test
    public void applyTaxWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsFirstDayOfCurrentInstance_Then_CalculateADRForFirstDayOfLastInstance() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1).plusDays(5), specialEventId);
        addPropertySpecialEventInstance(today, today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("27.500000"), historicalAdr);
    }

    @Test
    public void applySupplementsWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsFirstDayOfCurrentInstance_Then_CalculateADRForFirstDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1).plusDays(5), specialEventId);
        addPropertySpecialEventInstance(today, today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("35.000000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsLastDayOfCurrentInstance_Then_CalculateADRForLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(5), today.minusYears(1), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("15.000000"), historicalAdr);
    }

    @Test
    public void applyTaxWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsLastDayOfCurrentInstance_Then_CalculateADRForLastDayOfLastInstance() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(5), today.minusYears(1), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("16.500000"), historicalAdr);
    }

    @Test
    public void applySupplementsWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsLastDayOfCurrentInstance_Then_CalculateADRForLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(5), today.minusYears(1), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(4), today.minusYears(1).plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("25.000000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsMiddleDay_Then_CalculateAdrForMiddleDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(1), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("15.000000"), historicalAdr);
    }

    @Test
    public void applyTaxWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsMiddleDay_Then_CalculateAdrForMiddleDayOfLastInstance() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(1), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("16.500000"), historicalAdr);
    }

    @Test
    public void applySupplementsWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsMiddleDay_Then_CalculateAdrForMiddleDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(1), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("25.000000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsNotFirstOrLastOrMiddleDay_Then_CalculateAdrByAverageOfOtherThanFirstAndLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("12.500000"), historicalAdr);
    }

    @Test
    public void applyTaxWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsNotFirstOrLastOrMiddleDay_Then_CalculateAdrByAverageOfOtherThanFirstAndLastDayOfLastInstance() {
        // GIVEN
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        LocalDate today = LocalDate.now();
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("13.750000"), historicalAdr);
    }

    @Test
    public void applySupplementsWhenCurrentInstanceHasDifferentOddDaysToLastInstance_And_SelectedDateIsNotFirstOrLastOrMiddleDay_Then_CalculateAdrByAverageOfOtherThanFirstAndLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        when(taxService.findTaxFor(JavaLocalDateUtils.toJodaLocalDate(today))).thenReturn(tax);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("23.750000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentOddDaysToLastInstance_And_LastInstanceHas1Day_And_SelectedDateIsNotFirstOrLastOrMiddleDay_Then_CalculateAdrByAverageOfOtherThanFirstAndLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("12.500000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasOddDays_And_LastInstanceHasEvenDays_And_SelectedDateIsMiddleDayOfCurrentInstance_Then_CalculateAdrByTheAverageOfOtherThanFirstAndLastDayOfLastInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(1), today.minusYears(1).plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(1), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        // Rate - 90/6 = 15
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        // Rate - 60/6 = 10
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        // Rate - 120/2 = 60
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(2), DOUBLE_ROOM_TYPE, CORP, "120.00000", "1234567892");
        // Rate - 100/2 = 50
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(2), DOUBLE_ROOM_TYPE, DIST, "100.00000", "1234567893");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("33.750000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasEvenDays_And_LastInstanceHasOddDays_And_SelectedDateIsNotFirstOrLastDay_Then_CalculateAdrByTheAverageOtherThanFirstAndLastDayOfLasInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(1), today.minusYears(1).plusDays(1), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        // Rate - 90/6 = 15
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        // Rate - 60/6 = 10
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        // Rate - 120/2 = 60
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(2), DOUBLE_ROOM_TYPE, CORP, "120.00000", "1234567892");
        // Rate - 100/2 = 50
        addIndividualTrans(today.minusYears(1), today.minusYears(1).plusDays(2), DOUBLE_ROOM_TYPE, DIST, "100.00000", "1234567893");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("33.750000"), historicalAdr);
    }

    @Test
    public void whenCurrentInstanceHasDifferentEvenDaysToLastInstance_And_SelectedDateIsNotFirstOrLastDay_Then_CalculateAdrByTheAverageOfOtherThanFirstAndLastDayOfInstance() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(1), today.plusDays(2), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        // Rate - 90/6 = 15
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        // Rate - 60/6 = 10
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(4), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        // Rate - 120/5 = 24
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "120.00000", "1234567892");
        // Rate - 100/5 = 20
        addIndividualTrans(today.minusYears(1).minusDays(2), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "100.00000", "1234567893");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("17.250000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrForTheLastPastSpecialEventInstanceBeforeSystemDateWhenSelectedDateHasImpactForecastRecurringEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(2).minusDays(3), today.minusYears(2).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.minusDays(3), today.plusDays(3), specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1).minusDays(3), today.plusYears(1).plusDays(3), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(today.minusYears(2).minusDays(3), today.minusYears(2).plusDays(3), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(today.minusYears(2).minusDays(3), today.minusYears(2).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(today.minusYears(1).minusDays(3), today.minusYears(1).plusDays(3), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567892");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today.minusYears(1).minusDays(3));
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("12.500000"), historicalAdr);
    }

    private DateService createMockDateService() {
        dateService = mock(DateService.class);
        return dateService;
    }

    @Test
    public void shouldCalculateHistoricalAdrForFor5STLYWhenSelectedDateHasImpactForecastRecurringEventButStartsWithSameDay() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(1, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today, today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1), today.plusYears(1), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        verify(dateService, times(1)).getCaughtUpJavaLocalDate();
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenSelectedDateHasInformationOnlyRecurringEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventId = addSpecialEvent(0, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1), specialEventId);
        addPropertySpecialEventInstance(today, today, specialEventId);
        addPropertySpecialEventInstance(today.plusYears(1), today.plusYears(1), specialEventId);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenSelectedDateHasInformationOnlyRecurringEventAndSTLYHasImpactForecastEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventIdOnToday = addSpecialEvent(0, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1), specialEventIdOnToday);
        addPropertySpecialEventInstance(today, today, specialEventIdOnToday);
        addPropertySpecialEventInstance(today.plusYears(1), today.plusYears(1), specialEventIdOnToday);
        Integer specialEventIdOnStly = addSpecialEvent(1, DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), "0", "AnotherHoliday");
        addPropertySpecialEventInstance(DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusYears(1), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusYears(1), specialEventIdOnStly);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWhenSelectedDateHasInformationOnlyRecurringEventAndSTLYHasInformationOnlyEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventIdOnToday = addSpecialEvent(0, today, today, "1", "Holiday");
        addPropertySpecialEventInstance(today.minusYears(1), today.minusYears(1), specialEventIdOnToday);
        addPropertySpecialEventInstance(today, today, specialEventIdOnToday);
        addPropertySpecialEventInstance(today.plusYears(1), today.plusYears(1), specialEventIdOnToday);
        Integer specialEventIdOnStly = addSpecialEvent(0, DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), "0", "AnotherHoliday");
        addPropertySpecialEventInstance(DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusYears(1), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusYears(1), specialEventIdOnStly);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("102.000000"), historicalAdr);
    }

    @Test
    public void shouldNotCalculateHistoricalAdrWhenAll5STLYAreImpactForecastSpecialEvent() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        Integer specialEventIdOnStly = addSpecialEvent(1, DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), "0", "StlyHoliday");
        addPropertySpecialEventInstance(DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true), specialEventIdOnStly);
        Integer specialEventIdOnStlyOneWeekPriorDate = addSpecialEvent(1, stlyOneWeekPriorDate, stlyOneWeekPriorDate, "0", "StlyOneWeekPriorHoliday");
        addPropertySpecialEventInstance(stlyOneWeekPriorDate, stlyOneWeekPriorDate, specialEventIdOnStlyOneWeekPriorDate);
        Integer specialEventIdOnStlyTwoWeekPriorDate = addSpecialEvent(1, stlyTwoWeekPriorDate, stlyTwoWeekPriorDate, "0", "StlyTwoWeekPriorHoliday");
        addPropertySpecialEventInstance(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate, specialEventIdOnStlyTwoWeekPriorDate);
        Integer specialEventIdOnStlyOneWeekAfterDate = addSpecialEvent(1, stlyOneWeekAfterDate, stlyOneWeekAfterDate, "0", "StlyOneWeekAfterHoliday");
        addPropertySpecialEventInstance(stlyOneWeekAfterDate, stlyOneWeekAfterDate, specialEventIdOnStlyOneWeekAfterDate);
        Integer specialEventIdOnStlyTwoWeekAfterDate = addSpecialEvent(1, stlyTwoWeekAfterDate, stlyTwoWeekAfterDate, "0", "StlyTwoWeekAfterHoliday");
        addPropertySpecialEventInstance(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate, specialEventIdOnStlyTwoWeekAfterDate);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertNull(historicalAdr);
    }

    private void addPropertySpecialEventInstance(LocalDate startDate, LocalDate endDate, Integer specialEventId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Property_Special_Event_Instance values(" + specialEventId + ", '" + startDate + "', '" + endDate + "', 1, 1, 1, 0, 1, 1, GETDATE(), 1, GETDATE(), 1, 0, NULL)");
    }

    private Integer addSpecialEvent(Integer impactOnForecast, LocalDate startDate, LocalDate endDate, String repeatable, String eventName) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Property_Special_Event values " + "(5, 0, " + impactOnForecast + ", '" + startDate + "', '" + endDate + "', " + repeatable + ", 1, GETDATE(), 1, '" + eventName + "', '" + eventName + "', 1, 0, 1, GETDATE(), 1, 0) ");
        PropertySpecialEvent propertySpecialEvent = tenantCrudService().findByNamedQuerySingleResult(PropertySpecialEvent.BY_NAME_AND_PROPERTY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("name", eventName).parameters());
        return propertySpecialEvent.getId();
    }

    private void addIndividualTrans(LocalDate arrivalDate, LocalDate departureDate, String accomTypeId, String mktSegId, String rateValue, String reservationId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Individual_Trans values(123, 1, 5, " + reservationId + ", 'SS', '" + arrivalDate + "', '" + departureDate + "', '" + arrivalDate + "', NULL, 'STD', " + accomTypeId + ", " + mktSegId + ", '" + rateValue + "', '20.00000', '5.00000', '10.00000', '0.00000', '135.00000', " + "'', '', 'SHHQ01', '" + rateValue + "', 126, 'IN', 101, 202, GETDATE(), " + "'0987654321', '', '16:30:58.0000000',0,Null)");
    }

    private void makeForecastGroupStraightBar(final String forecastGroupId) {
        tenantCrudService().executeUpdateByNativeQuery("update Forecast_Group set Straight_Bar = 1 where Forecast_Group_ID = " + forecastGroupId);
    }

    @Test
    public void individualTransdataShouldMatchForStlyDate() {
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusDays(7);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).minusDays(14);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).plusDays(7);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true).plusDays(14);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        String format = "yyyy-MM-dd";
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567890");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567890");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "50.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "90.00000", "1234567891");
        verifyDataMatchesFor(getFormattedAnalysisDate(stly, format));
        verifyDataMatchesFor(getFormattedAnalysisDate(stlyOneWeekPriorDate, format));
        verifyDataMatchesFor(getFormattedAnalysisDate(stlyTwoWeekPriorDate, format));
        verifyDataMatchesFor(getFormattedAnalysisDate(stlyOneWeekAfterDate, format));
        verifyDataMatchesFor(getFormattedAnalysisDate(stlyTwoWeekAfterDate, format));
    }

    @Test
    public void shouldCalculateHistoricalAdrFor5STLYWithNoDataFor1Date() {
        // GIVEN
        LocalDate today = LocalDate.now();
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        // add data for all except stlyTwoWeekPriorDate
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        // WHEN
        BigDecimal historicalAdr = historicalAdrCalculator.calculate(today, 2);
        // THEN
        assertEquals(new BigDecimal("96.666666"), historicalAdr);
    }

    @Test
    public void shouldCalculateHistoricalAdrOnForecastInvestigatorForAllForecastGroupAndAllAccomClass() {
        // GIVEN
        LocalDate today = LocalDate.now();
        commonDataToCalculateHistoricalAdr(today);
        // WHEN
        Map<LocalDate, BigDecimal> historicalAdr = historicalAdrCalculator.calculateHistoricalAdrForForecastInvestigator(today, today, -1, -1);
        // THEN
        assertEquals(new BigDecimal("118.66667"), historicalAdr.get(today));
    }

    @Test
    public void shouldCalculateHistoricalAdrOnForecastInvestigatorForAllForecastGroupAndSpecificAccomClass() {
        // GIVEN
        LocalDate today = LocalDate.now();
        commonDataToCalculateHistoricalAdr(today);
        // WHEN
        Map<LocalDate, BigDecimal> historicalAdr = historicalAdrCalculator.calculateHistoricalAdrForForecastInvestigator(today, today, 2, -1);
        // THEN
        assertEquals(new BigDecimal("125.0"), historicalAdr.get(today));
    }

    @Test
    public void shouldCalculateHistoricalAdrOnForecastInvestigatorForSpecificForecastGroupAndAllAccomClass() {
        // GIVEN
        LocalDate today = LocalDate.now();
        commonDataToCalculateHistoricalAdr(today);
        // WHEN
        Map<LocalDate, BigDecimal> historicalAdr = historicalAdrCalculator.calculateHistoricalAdrForForecastInvestigator(today, today, -1, 2);
        // THEN
        assertEquals(new BigDecimal("118.66667"), historicalAdr.get(today));
    }

    @Test
    public void shouldCalculateHistoricalAdrOnForecastInvestigatorForSpecificForecastGroupAndSpecificAccomClass() {
        // GIVEN
        LocalDate today = LocalDate.now();
        commonDataToCalculateHistoricalAdr(today);
        // WHEN
        Map<LocalDate, BigDecimal> historicalAdr = historicalAdrCalculator.calculateHistoricalAdrForForecastInvestigator(today, today, 2, 2);
        // THEN
        assertEquals(new BigDecimal("125.0"), historicalAdr.get(today));
    }


    public void commonDataToCalculateHistoricalAdr(LocalDate today) {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        addSupplementsForAccomType("6", "10.00");
        addSupplementsForAccomType("7", "50.00");
        Date stlyDate = DateCalculator.calculateDateForLastYear(DateUtil.convertLocalDateToJavaUtilDate(today), true);
        LocalDate stlyOneWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -7), true);
        LocalDate stlyTwoWeekPriorDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, -14), true);
        LocalDate stlyOneWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 7), true);
        LocalDate stlyTwoWeekAfterDate = DateUtil.convertJavaUtilDateToLocalDate(DateUtil.addDaysToDate(stlyDate, 14), true);
        LocalDate stly = DateUtil.convertJavaUtilDateToLocalDate(stlyDate, true);
        makeForecastGroupStraightBar(CORPORATE);
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "50.00000", "1234567890");
        addIndividualTrans(stly, stly.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567899");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567891");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "150.00000", "1234567892");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "80.00000", "1234567893");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, CORP, "90.00000", "1234567894");
        addIndividualTrans(stly, stly.plusDays(1), QUEEN_ROOM_TYPE, DIST, "70.00000", "1234567891");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "60.00000", "1234567895");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "200.00000", "1234567896");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "150.00000", "1234567897");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DOUBLE_ROOM_TYPE, DIST, "110.00000", "1234567898");
        addIndividualTrans(stly, stly.plusDays(1), DELUXE_ROOM, CORP, "50.00000", "1234567880");
        addIndividualTrans(stly, stly.plusDays(1), DELUXE_ROOM, CORP, "80.00000", "1234567870");
        addIndividualTrans(stlyOneWeekPriorDate, stlyOneWeekPriorDate.plusDays(1), DELUXE_ROOM, CORP, "150.00000", "1234567885");
        addIndividualTrans(stlyTwoWeekPriorDate, stlyTwoWeekPriorDate.plusDays(1), DELUXE_ROOM, CORP, "150.00000", "1234567886");
        addIndividualTrans(stlyOneWeekAfterDate, stlyOneWeekAfterDate.plusDays(1), DELUXE_ROOM, CORP, "80.00000", "1234567887");
        addIndividualTrans(stlyTwoWeekAfterDate, stlyTwoWeekAfterDate.plusDays(1), DELUXE_ROOM, CORP, "90.00000", "1234567888");
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
    }

    private String getFormattedAnalysisDate(LocalDate stlyTwoWeekAfterDate, String format) {
        return JavaLocalDateUtils.getFormattedDate(stlyTwoWeekAfterDate, format);
    }

    private void verifyDataMatchesFor(String analysisDate) {
        Map<String, Object> parameters = QueryParameter.with("analysisDate", analysisDate).parameters();
        List<Object[]> indTransData = tenantCrudService().findByNativeQuery(sqlUsingIndividualTrans, parameters);
        List<Object[]> reservationNightData = tenantCrudService().findByNativeQuery(sqlUsingReservationNightView, parameters);
        for (int i = 0; i < indTransData.size(); i++) {
            for (int j = 0; j < 6; j++) {
                assertEquals(String.valueOf(indTransData.get(i)[j]), String.valueOf(reservationNightData.get(i)[j]));
            }
        }
    }
}
