package com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util;

import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.dto.TenantChannelRestrictionAdjustmentDto;
import com.ideas.tetris.pacman.services.hiltonChannelRestrictionAdjustments.entity.TenantChannelRestrictionAdjustment;
import com.ideas.tetris.pacman.services.hiltonChannelRestrictionAdjustments.entity.TenantChannelRestrictionAdjustmentExcludedSRP;
import com.ideas.tetris.platform.services.daoandentities.entity.ChannelRestrictionAdjustment;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.ChannelRestrictionAdjustmentTestUtil.mockChannelRestrictionAdjustment;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.ChannelRestrictionAdjustmentTestUtil.mockTenantChannelRestrictionAdjustment;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.ChannelRestrictionAdjustmentTestUtil.mockTenantChannelRestrictionAdjustmentDto;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.buildAdditionalPropertyInfo;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.buildTenantEntitiesFromClientEntities;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.convertGlobalCRAToTenantCRADto;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.convertTenantCRAToDto;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.getPropertyCodesInBatch;
import static com.ideas.tetris.pacman.services.channelRestrictionAdjustment.util.SyncChannelRestrictionAdjustmentUtil.updateTenantChannelRestrictionAdjustment;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


public class SyncChannelRestrictionAdjustmentUtilTest {
    public static final String DEFAULT_SRP_GROUP_CODE = "OTA";
    private static final String DEFAULT_BRAND_CODE = "HX";
    private static final String DEFAULT_GLOBAL_AREA = "Americas";

    @Test
    public void test_buildTenantEntityFromClientEntity() throws ParseException {
        //Given
        ChannelRestrictionAdjustment channelRestrictionAdjustment = mockChannelRestrictionAdjustment(DEFAULT_BRAND_CODE, DEFAULT_GLOBAL_AREA, DEFAULT_SRP_GROUP_CODE,
                new BigDecimal(10), new BigDecimal(5), new HashSet<>(Arrays.asList("A", "B", "C")));
        PropertyAdditionalAttributes propertyAdditionalAttributes = getPropertyAdditionalAttributes();

        //WHEN
        List<TenantChannelRestrictionAdjustment> tenantChannelRestrictionAdjustments = buildTenantEntitiesFromClientEntities(Arrays.asList(channelRestrictionAdjustment), propertyAdditionalAttributes);
        TenantChannelRestrictionAdjustment tenantChannelRestrictionAdjustment = tenantChannelRestrictionAdjustments.get(0);
        //THEN
        assertNotNull(tenantChannelRestrictionAdjustments);
        assertEquals(tenantChannelRestrictionAdjustment.getSrpGroupCode(), channelRestrictionAdjustment.getSrpGroupCode());
        assertEquals(
                Arrays.asList(
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentMon(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentTue(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentWed(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentThu(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentFri(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentSat(),
                        tenantChannelRestrictionAdjustment.getPercentageAdjustmentSun()
                ),
                Arrays.asList(
                        channelRestrictionAdjustment.getPercentageAdjustmentMon(),
                        channelRestrictionAdjustment.getPercentageAdjustmentTue(),
                        channelRestrictionAdjustment.getPercentageAdjustmentWed(),
                        channelRestrictionAdjustment.getPercentageAdjustmentThu(),
                        channelRestrictionAdjustment.getPercentageAdjustmentFri(),
                        channelRestrictionAdjustment.getPercentageAdjustmentSat(),
                        channelRestrictionAdjustment.getPercentageAdjustmentSun()
                ));
        assertEquals(
                Arrays.asList(
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentMon(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentTue(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentWed(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentThu(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentFri(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentSat(),
                        tenantChannelRestrictionAdjustment.getFixedAdjustmentSun()
                ),
                Arrays.asList(
                        channelRestrictionAdjustment.getFixedAdjustmentMon(),
                        channelRestrictionAdjustment.getFixedAdjustmentTue(),
                        channelRestrictionAdjustment.getFixedAdjustmentWed(),
                        channelRestrictionAdjustment.getFixedAdjustmentThu(),
                        channelRestrictionAdjustment.getFixedAdjustmentFri(),
                        channelRestrictionAdjustment.getFixedAdjustmentSat(),
                        channelRestrictionAdjustment.getFixedAdjustmentSun()
                ));
        assertEquals(tenantChannelRestrictionAdjustment.getExcludedSRPList().size(), channelRestrictionAdjustment.getExcludedSRPList().size());
        assertEquals(propertyAdditionalAttributes.getPropertyCode(), tenantChannelRestrictionAdjustment.getPropertyCode());


    }

    private PropertyAdditionalAttributes getPropertyAdditionalAttributes() {
        PropertyAdditionalAttributes additionalAttributes = new PropertyAdditionalAttributes();
        additionalAttributes.setPropertyCode(TestProperty.H1.name());
        return additionalAttributes;
    }

    @Test
    public void test_buildAdditionalPropertyInfo() {

        //WHEN
        Map<String, PropertyAdditionalAttributes> brandAndAreaInfoMap = buildAdditionalPropertyInfo(getDefaultAdditionalInfo());

        //THEN
        assertNotNull(brandAndAreaInfoMap);
        assertNotNull(brandAndAreaInfoMap.get(TestProperty.H1.name()));
        assertEquals(DEFAULT_BRAND_CODE, brandAndAreaInfoMap.get(TestProperty.H1.name()).getBrandCode());
        assertEquals(DEFAULT_GLOBAL_AREA, brandAndAreaInfoMap.get(TestProperty.H1.name()).getGlobalArea());

    }

    @Test
    void test_getPropertyCodesInBatch() {
        List<String> propertyCodes = Arrays.asList("H1", "H2", " H3", "H4 ", "H5", "H6");
        List<String> propertyCodesInBatch = getPropertyCodesInBatch(propertyCodes);

        assertNotNull(propertyCodesInBatch);
        assertEquals(1, propertyCodesInBatch.size());
        assertEquals(propertyCodesInBatch.get(0), String.join(",", Arrays.asList("H1", "H2", "H3", "H4", "H5", "H6")));
    }

    private List<Object> getDefaultAdditionalInfo() {
        List<Object> additionalPropertyInfo = new ArrayList<>();
        String[] brandCode = new String[]{TestProperty.H1.name(), "BrAnd CoDE", DEFAULT_BRAND_CODE};
        additionalPropertyInfo.add(brandCode);

        String[] globalArea = new String[]{TestProperty.H1.name(), "GlobaL AreA", DEFAULT_GLOBAL_AREA};
        additionalPropertyInfo.add(globalArea);

        return additionalPropertyInfo;
    }

    @Test
    public void convertTenantCRAToDtoTest() {
        List tenantAdjustments = Arrays.asList(
                mockTenantChannelRestrictionAdjustment("SRP_GRP_1", new BigDecimal(5), new BigDecimal(10), Arrays.asList("SRP1", "SRP2")),
                mockTenantChannelRestrictionAdjustment("SRP_GRP_2", new BigDecimal(6), new BigDecimal(11), Arrays.asList("SRP3", "SRP4"))
        );

        Set<TenantChannelRestrictionAdjustmentDto> actual = convertTenantCRAToDto(tenantAdjustments);

        assertEquals(2, actual.size());
        actual.forEach(a -> {
            Assertions.assertTrue(a.getSrpGroupCode().equals("SRP_GRP_1") || a.getSrpGroupCode().equals("SRP_GRP_2"));
            Assertions.assertTrue(a.getSource().getFixedAdjustmentFri().equals(new BigDecimal(5)) || a.getSource().getFixedAdjustmentFri().equals(new BigDecimal(6)));
            Assertions.assertTrue(a.getSource().getPercentageAdjustmentFri().equals(new BigDecimal(10)) || a.getSource().getPercentageAdjustmentFri().equals(new BigDecimal(11)));
            assertEquals(2, a.getExcludedSRPList().size());
        });
    }

    @Test
    public void convertGlobalCRAToTenantCRADtoTest() throws ParseException {
        List tenantAdjustments = Arrays.asList(
                mockChannelRestrictionAdjustment("BrandCode_1", "GlobalArea_1", "SRP_GRP_1", new BigDecimal(5), new BigDecimal(10), new HashSet(Arrays.asList("SRP1", "SRP2"))),
                mockChannelRestrictionAdjustment("BrandCode_1", "GlobalArea_1", "SRP_GRP_2", new BigDecimal(6), new BigDecimal(11), new HashSet(Arrays.asList("SRP3", "SRP4")))
        );
        PropertyAdditionalAttributes propertyAdditionalAttributes = getPropertyAdditionalAttributes();
        Set<TenantChannelRestrictionAdjustmentDto> actual = convertGlobalCRAToTenantCRADto(tenantAdjustments, propertyAdditionalAttributes);

        assertEquals(2, actual.size());
        actual.forEach(a -> {
            Assertions.assertTrue(a.getSrpGroupCode().equals("SRP_GRP_1") || a.getSrpGroupCode().equals("SRP_GRP_2"));
            Assertions.assertTrue(a.getSource().getFixedAdjustmentFri().equals(new BigDecimal(5)) || a.getSource().getFixedAdjustmentFri().equals(new BigDecimal(6)));
            Assertions.assertTrue(a.getSource().getPercentageAdjustmentFri().equals(new BigDecimal(10)) || a.getSource().getPercentageAdjustmentFri().equals(new BigDecimal(11)));
            assertEquals(2, a.getExcludedSRPList().size());
            assertEquals(propertyAdditionalAttributes.getPropertyCode(), a.getSource().getPropertyCode());
        });
    }

    @Test
    public void updateTenantChannelRestrictionAdjustmentTest() {
        TenantChannelRestrictionAdjustmentDto old = mockTenantChannelRestrictionAdjustmentDto("SRP_GRP_1", new BigDecimal(5), new BigDecimal(10), Arrays.asList("SRP1", "SRP2"));
        TenantChannelRestrictionAdjustmentDto toBeUpdated = mockTenantChannelRestrictionAdjustmentDto("SRP_GRP_1", new BigDecimal(6), new BigDecimal(12), Arrays.asList("SRP1", "SRP3"));
        updateTenantChannelRestrictionAdjustment(old, toBeUpdated);

        assertEquals(old.getSource().getFixedAdjustmentFri(), new BigDecimal(6));
        assertEquals(old.getSource().getPercentageAdjustmentFri(), new BigDecimal(12));
        assertEquals(2, old.getExcludedSRPList().size());
        List<String> srpList = old.getExcludedSRPList().stream()
                .map(TenantChannelRestrictionAdjustmentExcludedSRP::getExcludedSRPCode)
                .collect(Collectors.toList());
        Assertions.assertTrue(srpList.contains("SRP1"));
        Assertions.assertTrue(srpList.contains("SRP3"));
        Assertions.assertFalse(srpList.contains("SRP2"));
    }
}
