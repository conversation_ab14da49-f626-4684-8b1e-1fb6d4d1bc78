package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decisionsupport.DecisionSupportService;
import com.ideas.tetris.pacman.services.decisionsupport.entity.DecisionSupport;
import com.ideas.tetris.pacman.services.rateshopping.entity.CompetitorRates;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.service.RateShoppingAdjustmentService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.dto.DCMPCGenericValuesDTO;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.ENABLE_IGNORE_WEBRATE_CHANNEL;
import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.common.constants.Constants.SYSTEM_USER_ID;
import static com.ideas.tetris.pacman.services.dateservice.dto.DateParameter.fromDate;
import static com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService.DISABLE_WEBRATE_COMPETITOR_CLASS_CODE;
import static com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService.ENABLE_WEBRATE_COMPETITOR_CLASS_CODE;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TestCompetitorRateInfoService extends AbstractG3JupiterTest {
    private static final int ACCOM_CLASS_ID = 2;
    private CompetitorRateInfoService service = new CompetitorRateInfoService();
    private WebrateShoppingDataService wsdService = new WebrateShoppingDataService();
    private TaxService taxService = mock(TaxService.class);
    private WebrateShoppingDataService webrateShoppingDataService = mock(WebrateShoppingDataService.class);
    private List<CompetitorRateInfo> actualCompetitorsRates;
    private PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
    private RateShoppingAdjustmentService rateShoppingAdjustmentService = mock(RateShoppingAdjustmentService.class);

    private AccommodationService accommodationService = mock(AccommodationService.class);

    private DecisionSupportService decisionSupportService = mock(DecisionSupportService.class);

    private DateService dateService = mock(DateService.class);

    private DynamicCMPCService dynamicCMPCService = mock(DynamicCMPCService.class);

    private WebrateChannelIgnoreService webrateChannelIgnoreService = mock(WebrateChannelIgnoreService.class);

    @BeforeEach
    public void setup() {
        actualCompetitorsRates = new ArrayList<>();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(configParamsService.getParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED.value())).thenReturn("true");
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(dateService.getCaughtUpDate()).thenReturn(convertLocalDateToJavaUtilDate(java.time.LocalDate.of(2015, 11, 16)));


        service.setConfigService(configParamsService);
        service.setCrudService(tenantCrudService());
        service.setWebrateShoppingDataService(webrateShoppingDataService);
        wsdService.setCrudService(tenantCrudService());
        service.setDecisionSupportService(decisionSupportService);
        service.setDateService(dateService);
        service.setAccommodationService(accommodationService);
        service.setDynamicCMPCService(dynamicCMPCService);
        inject(service, "webrateChannelIgnoreService", webrateChannelIgnoreService);
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setUserId("1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
    }

    @Test
    public void testGetRoomTypes() {
        List<WebrateAccomType> roomTypes = service.getRoomTypes(Arrays.asList(ACCOM_CLASS_ID));
        assertNotNull(roomTypes);
        assertEquals(2, roomTypes.size());
    }

    @Test
    public void testGetAccomClasses() {
        List<AccomClass> accomClasses = service.getAccomClassesHavingWebrateAccomTypes();
        assertNotNull(accomClasses);
    }

    @Test
    public void testGetDefaultWebrateChannel() throws Exception {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        WebrateChannel channel = service.getDefaultChannel(df.parse("2010-11-15"));
        assertNotNull(channel);
        assertEquals(3, channel.getId().intValue());
    }

    @Test
    public void testGetCompetitorWithoutRates() {
        Date rateDate = DateUtil.getDateForCurrentMonth(15);

        setUpRoomTaxRate();

        // For Channels 1 and 4, Competitors 1 and 4, and Types
        List<CompetitorRateInfo> rates = service.getCompetitorRates(rateDate, Arrays.asList(1, 4), Arrays.asList(1, 4), Arrays.asList(1, 2, 3));
        assertNotNull(rates);
        assertEquals(12, rates.size());
        String channelName = rates.get(0).getChannelName();
        String webRateAcTypeName = rates.get(0).getRoomTypeName();
        assertNotNull(channelName);
        assertNotNull(webRateAcTypeName);

        // Channels 1 , Competitor 1,4 and Types 2
        rates = service.getCompetitorRates(rateDate, Arrays.asList(1), Arrays.asList(1, 4), Arrays.asList(2));
        assertNotNull(rates);
        assertEquals(2, rates.size());
    }


    @Test
    public void testGetCompetitorRates() {
        Date rateDate = DateUtil.getDateForCurrentMonth(15);
        setUpRoomTaxRate();

        // Channels 1 , Competitor 1,2,3,4 and Types 2
        List<CompetitorRateInfo> rates = service.getCompetitorRates(rateDate, Arrays.asList(1), Arrays.asList(1, 2, 3, 4), Arrays.asList(2));
        assertNotNull(rates);
        rates.forEach(r -> {
            assertNotNull(r.getWebRateId());
        });
        assertEquals(4, rates.size());

        // Channels 1 , Competitor 1,2,3,4 and Types 2
        List<CompetitorRateInfo> ratesWithTax = service.getCompetitorRates(rateDate, Arrays.asList(1), Arrays.asList(1, 2, 3, 4), Arrays.asList(2));
        assertNotNull(ratesWithTax);
        assertEquals(4, ratesWithTax.size());

        for (int i = 0; i < 4; i++) {
            assertEquals(ratesWithTax.get(i).getRate(), rates.get(i).getRate());
        }
    }

    @Test
    public void testGetCompetitorRates_MaxWebrateGenerationDate() {
        int competitorId = 1;
        int channelId = 1;
        int accomClassId = 1;
        Date today1 = DateUtil.addDaysToDate(new Date(), 1);
        Date today3 = DateUtil.addDaysToDate(new Date(), 3);
        int los2 = 2;
        int los5 = 5;

        Date occupancyDate = DateUtil.getDateForCurrentMonth(15);
        setUpRoomTaxRate();

        UniqueWebRateCreator.createWebrate(DateUtil.sqlDate(occupancyDate), los2, DateUtil.sqlDate(today1), competitorId, channelId, accomClassId);
        UniqueWebRateCreator.createWebrate(DateUtil.sqlDate(occupancyDate), los5, DateUtil.sqlDate(today3), competitorId, channelId, accomClassId);

        List<CompetitorRateInfo> rates = service.getCompetitorRates(occupancyDate, Arrays.asList(channelId), Arrays.asList(competitorId), Arrays.asList(accomClassId));

        assertNotNull(rates);
        assertEquals(3, rates.size());
        assertEquals(new DateParameter(today3), rates.get(2).getWebrateGenerationDate());
    }

    @Test
    public void testGetCompetitorRates_CheckIfCreateNewCompetitorRateInfoRecord() {
        int competitorId = 1;
        int channelId = 1;
        int accomClassId = 1;
        Date today1 = DateUtil.addDaysToDate(new Date(), 1);
        int los2 = 2;
        Date occupancyDate = DateUtil.getDateForCurrentMonth(15);
        setUpRoomTaxRate();

        UniqueWebRateCreator.createWebrate(DateUtil.sqlDate(occupancyDate), los2, DateUtil.sqlDate(today1), competitorId, channelId, accomClassId);
        List<CompetitorRateInfo> rates1 = service.getCompetitorRates(occupancyDate, Arrays.asList(channelId), Arrays.asList(competitorId), Arrays.asList(accomClassId));
        assertNotNull(rates1);
        assertEquals(rates1.get(0).getCompetitorName(), rates1.get(1).getCompetitorName());
        assertEquals(rates1.get(0).getChannelName(), rates1.get(1).getChannelName());
        assertEquals(rates1.get(0).getRoomTypeName(), rates1.get(1).getRoomTypeName());
        assertNotEquals(rates1.get(0).getRemarks(), rates1.get(1).getRemarks());
        assertEquals(2, rates1.size());

        int los3 = 3;
        Date today3 = DateUtil.addDaysToDate(new Date(), 3);
        UniqueWebRateCreator.createWebrate(DateUtil.sqlDate(occupancyDate), los3, DateUtil.sqlDate(today3), competitorId, channelId, accomClassId);
        List<CompetitorRateInfo> rates2 = service.getCompetitorRates(occupancyDate, Arrays.asList(channelId), Arrays.asList(competitorId), Arrays.asList(accomClassId));
        assertEquals(rates2.get(1).getCompetitorName(), rates2.get(2).getCompetitorName());
        assertEquals(rates2.get(1).getChannelName(), rates2.get(2).getChannelName());
        assertEquals(rates2.get(1).getRoomTypeName(), rates2.get(2).getRoomTypeName());
        assertEquals(rates2.get(1).getRemarks(), rates2.get(2).getRemarks());
        assertEquals(rates2.get(1).getRate(), rates2.get(2).getRate());
        assertNotEquals(rates2.get(1).getWebrateGenerationDate(), rates2.get(2).getWebrateGenerationDate());
        assertEquals(3, rates2.size());

        int los4 = 4;
        UniqueWebRateCreator.createWebrate(DateUtil.sqlDate(occupancyDate), los4, DateUtil.sqlDate(today3), competitorId, channelId, accomClassId);
        List<CompetitorRateInfo> rates3 = service.getCompetitorRates(occupancyDate, Arrays.asList(channelId), Arrays.asList(competitorId), Arrays.asList(accomClassId));
        assertEquals(2, rates3.get(2).getRateByLengthOfStay().size());
        assertEquals(true, ((HashMap) rates3.get(2).getRateByLengthOfStay()).keySet().contains(los4));
        assertEquals(3, rates3.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDate() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "150.00", 1, 1, 1, "A", "1");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        //WHEN
        setUpRoomTaxRate();
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        CompetitorRateInfo santaBarbaraInnCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Santa Barbara Inn");
        assertEquals(new BigDecimal("100.00000"), santaBarbaraInnCompetitor.getRate());
        assertEquals("Santa Barbara Inn", santaBarbaraInnCompetitor.getCompetitorName());
        assertEquals("Standard 2", santaBarbaraInnCompetitor.getRoomTypeName());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        assertEquals("Standard 2", westBeachInnSantaBarbaraCompetitor.getRoomTypeName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        assertEquals("Standard 2", luxorCompetitor.getRoomTypeName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
        assertEquals("Standard 2", mgmGrandCompetitor.getRoomTypeName());
    }

    @Test
    public void getCompetitorRatesForDateRange() {
        LocalDate fromDate = new LocalDate();
        LocalDate toDate = new LocalDate().plusDays(2);

        setUpWebrateDefaultChannels();
        createWebrate(fromDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(fromDate, "210.00", 1, 2, 2, "A", "1");
        createWebrate(toDate, "300.00", 1, 1, 3, "A", "1");
        createWebrate(toDate, "310.00", 1, 2, 3, "A", "1");
        createWebrate(fromDate, "400.00", 1, 1, 4, "A", "1");
        createWebrate(fromDate, "410.00", 1, 2, 4, "A", "1");
        createWebrate(fromDate, "0.00", 1, 1, 1, "C", "1");
        createWebrate(fromDate, "300.00", 1, 1, 2, "F", "1");

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(1);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);
        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRates(fromDate.toDate(), toDate.toDate(), channelIds, competitorIds, accomTypeIds, false);
        assertEquals(4, competitorsRateForDateRange.size());
        CompetitorRateInfo firstValue = competitorsRateForDateRange.get(2);
        assertEquals(firstValue.getOccupancyDate().getMonth(), fromDate.toDate().getMonth());
        assertEquals(firstValue.getOccupancyDate().getYear(), fromDate.toDate().getYear());
        assertEquals(firstValue.getOccupancyDate().getDay(), fromDate.toDate().getDay());
        assertEquals("Expedia", firstValue.getChannelName());
        assertEquals("Luxor", firstValue.getCompetitorName());
        assertEquals("STD", firstValue.getAccomClassName());
        assertEquals("Standard", firstValue.getRoomTypeName());
        assertEquals("Test data", firstValue.getRemarks());

        CompetitorRateInfo secondValue = competitorsRateForDateRange.get(3);
        assertEquals(secondValue.getOccupancyDate().getMonth(), fromDate.toDate().getMonth());
        assertEquals(secondValue.getOccupancyDate().getYear(), fromDate.toDate().getYear());
        assertEquals(secondValue.getOccupancyDate().getDay(), fromDate.toDate().getDay());
        assertEquals("Expedia", secondValue.getChannelName());
        assertEquals("MGM Grand", secondValue.getCompetitorName());
        assertEquals("STD", secondValue.getAccomClassName());
        assertEquals("Standard", secondValue.getRoomTypeName());
        assertEquals("Test data", secondValue.getRemarks());

        CompetitorRateInfo thirdValue = competitorsRateForDateRange.get(0);
        assertEquals(thirdValue.getOccupancyDate().getMonth(), fromDate.toDate().getMonth());
        assertEquals(thirdValue.getOccupancyDate().getYear(), fromDate.toDate().getYear());
        assertEquals(thirdValue.getOccupancyDate().getDay(), fromDate.toDate().getDay());
        assertEquals("Expedia", thirdValue.getChannelName());
        assertEquals("Santa Barbara Inn", thirdValue.getCompetitorName());
        assertEquals("STD", thirdValue.getAccomClassName());
        assertEquals("Standard", thirdValue.getRoomTypeName());
        assertEquals("Test data", thirdValue.getRemarks());

        CompetitorRateInfo forthValue = competitorsRateForDateRange.get(1);
        assertEquals(forthValue.getOccupancyDate().getMonth(), fromDate.toDate().getMonth());
        assertEquals(forthValue.getOccupancyDate().getYear(), fromDate.toDate().getYear());
        assertEquals(forthValue.getOccupancyDate().getDay(), fromDate.toDate().getDay());
        assertEquals("Expedia", forthValue.getChannelName());
        assertEquals("West Beach Inn Santa Barbara", forthValue.getCompetitorName());
        assertEquals("STD", forthValue.getAccomClassName());
        assertEquals("Standard", forthValue.getRoomTypeName());
    }

    @Test
    public void shouldNotApplyTaxWhenNoCompetitorsFound() {
        //GIVEN
        LocalDate today = LocalDate.now();
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        when(taxService.findTax()).thenReturn(tax);
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(Collections.EMPTY_LIST, competitorsRateForDate);
    }

    @Test
    public void shouldGetCompetitorsRateForLos() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "150.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 1, 1, 2, "A", "2");
        createWebrate(today, "200.00", 1, 2, 2, "A", "2");
        createWebrate(today, "300.00", 1, 1, 3, "A", "2");
        createWebrate(today, "300.00", 1, 2, 3, "A", "2");
        createWebrate(today, "400.00", 1, 1, 4, "A", "2");
        createWebrate(today, "400.00", 1, 2, 4, "A", "2");
        createWebrateCompetitorsClass(4, 2, 1, 0);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 2);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        CompetitorRateInfo santaBarbaraInnCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Santa Barbara Inn");
        assertEquals(new BigDecimal("100.00000"), santaBarbaraInnCompetitor.getRate());
        assertEquals("Santa Barbara Inn", santaBarbaraInnCompetitor.getCompetitorName());
        assertEquals("Standard 2", santaBarbaraInnCompetitor.getRoomTypeName());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        assertEquals("Standard 2", westBeachInnSantaBarbaraCompetitor.getRoomTypeName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        assertEquals("Standard 2", luxorCompetitor.getRoomTypeName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
        assertEquals("Standard 2", mgmGrandCompetitor.getRoomTypeName());
    }

    @Test
    public void shouldGetCompetitorsRateForDateForBarByDayProperty() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "150.00", 1, 1, 1, "A", "1");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, -1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        CompetitorRateInfo santaBarbaraInnCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Santa Barbara Inn");
        assertEquals(new BigDecimal("100.00000"), santaBarbaraInnCompetitor.getRate());
        assertEquals("Santa Barbara Inn", santaBarbaraInnCompetitor.getCompetitorName());
        assertEquals("Standard 2", santaBarbaraInnCompetitor.getRoomTypeName());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        assertEquals("Standard 2", westBeachInnSantaBarbaraCompetitor.getRoomTypeName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        assertEquals("Standard 2", luxorCompetitor.getRoomTypeName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
        assertEquals("Standard 2", mgmGrandCompetitor.getRoomTypeName());
    }

    private CompetitorRateInfo getCompetitorRateInfoBy(List<CompetitorRateInfo> competitorsRateForDate, String competitorName) {
        return competitorsRateForDate.stream().filter(competitorRateInfo -> competitorRateInfo.getCompetitorName().equals(competitorName)).findFirst().get();
    }

    @Test
    public void shouldAvoidCompetitorsWithNStatusWhileGettingCompetitorsRateForDate() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "N", "1");
        createWebrate(today, "100.00", 1, 2, 1, "N", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(3, competitorsRateForDate.size());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
    }

    @Test
    public void shouldAvoidCompetitorsWithCStatusWhileGettingCompetitorsRateForDate() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "C", "1");
        createWebrate(today, "100.00", 1, 2, 1, "C", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(3, competitorsRateForDate.size());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
    }

    @Test
    public void shouldGetCompetitorsRateForDateOnlyForLos1() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        CompetitorRateInfo santaBarbaraInnCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Santa Barbara Inn");
        assertEquals(new BigDecimal("100.00000"), santaBarbaraInnCompetitor.getRate());
        assertEquals("Santa Barbara Inn", santaBarbaraInnCompetitor.getCompetitorName());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
    }

    @Test
    public void shouldGetCompetitorsRateForDateOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDateOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabledAllChannel() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(32, competitorsRateForDate.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDateOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabledAllChannelForIndependentProductsEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(32, competitorsRateForDate.size());
    }

    private void mockWebrateTypeProductMapping() {
        tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Type_Product (Webrate_Type_ID,Product_ID,LOS,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) values (1,1,1,1,GETDATE(),1,GETDATE())");
    }
    @Test
    public void shouldGetCompetitorsRateForDateAndRateShoppingRateTypePricingInvestigatorEnabledMinimumLos() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "2");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "2");
        createWebrate(today, "90.00", 1, 2, 1, "A", "3");
        createWebrate(today, "200.00", 1, 1, 2, "A", "4");
        createWebrate(today, "200.00", 1, 2, 2, "A", "2");
        createWebrate(today, "300.00", 1, 1, 3, "A", "4");
        createWebrate(today, "300.00", 1, 2, 3, "A", "2");
        createWebrate(today, "400.00", 1, 1, 4, "A", "4");
        createWebrate(today, "400.00", 1, 2, 4, "A", "2");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDateOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabledWithAllChannel() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 2, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 2, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(32, competitorsRateForDate.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDateForChartOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabledWithAllChannel() {
        //GIVEN
        java.time.LocalDate today = java.time.LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 2, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 2, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        // when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDateAndCompetiorChart(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
    }

    @Test
    public void shouldGetCompetitorsRateForDateForChartOnlyForLos1AndRateShoppingRateTypePricingInvestigatorEnabledWithAllChannelForIndependentProducts() {
        //GIVEN
        java.time.LocalDate today = java.time.LocalDate.now();
        setUpWebrateDefaultChannels();
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "50.00", 1, 1, 1, "A", "2");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "90.00", 1, 2, 1, "A", "2");
        createWebrate(today, "200.00", 2, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 2, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
        createWebrateCompetitorsClass(4, 2, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        //WHEN
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        // when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDateAndCompetiorChart(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
    }

    @Test
    public void shouldAvoidCompetitorsWithDemandNotEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 0, 1);
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(3, competitorsRateForDate.size());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
        CompetitorRateInfo mgmGrandCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "MGM Grand");
        assertEquals(new BigDecimal("400.00000"), mgmGrandCompetitor.getRate());
        assertEquals("MGM Grand", mgmGrandCompetitor.getCompetitorName());
    }

    @Test
    public void shouldAvoidCompetitorsWhenCMPCEnabledWithRankNotEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 0);
        createWebrateCompetitorsClass(2, 2, 1, 0);
        createWebrateCompetitorsClass(3, 2, 1, 0);
        createWebrateCompetitorsClass(4, 2, 1, 0);
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        createWebrateRankingAccomClassOvr(2, new Date(), new Date(), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));

        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(0, competitorsRateForDate.size());
    }

    @Test
    public void shouldConsiderCompetitorsWhenCMPCEnabledWithRankEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 1, 1);
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        CompetitorRateInfo westBeachInnSantaBarbaraCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "West Beach Inn Santa Barbara");
        assertEquals(new BigDecimal("200.00000"), westBeachInnSantaBarbaraCompetitor.getRate());
        assertEquals("West Beach Inn Santa Barbara", westBeachInnSantaBarbaraCompetitor.getCompetitorName());
        CompetitorRateInfo luxorCompetitor = getCompetitorRateInfoBy(competitorsRateForDate, "Luxor");
        assertEquals(new BigDecimal("300.00000"), luxorCompetitor.getRate());
        assertEquals("Luxor", luxorCompetitor.getCompetitorName());
    }

    @Test
    public void shouldConsiderOnlyCompetitorsWithRankEnabledWhenCMPCEnabled() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 1, 0);
        createWebrateCompetitorsClass(4, 2, 1, 0);
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        createWebrateRankingAccomClassOvr(2, new Date(), new Date(), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(2, competitorsRateForDate.size());
    }

    @Test
    public void shouldShowIncludedInDemandAsFalseForAllCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreIgnoredAndCompetitorOverrideIsNotSet() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandEnabledAs(DISABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds);

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsFalseForAllCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreIgnoredAndCompetitorOverrideIsNotSet_CompetitiveRankDisabled() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandAndRankEnabledAs(DISABLE_WEBRATE_COMPETITOR_CLASS_CODE, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds, false);

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueIfRateShoppingAndCompetitiveMarketPositionConstraintsColumnsChecked() {
        LocalDate givenOccupancyDate = LocalDate.parse("2020-11-19");
        List<Integer> givenChannelIDs = Arrays.asList(1);
        List<Integer> givenCompetitorIDs = Arrays.asList(1);
        List<Integer> givenAccomTypeIDs = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        createWebrate(givenOccupancyDate, "120", 1, 1, 1, "A", "1");
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 1, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandAndRankEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIDs, givenCompetitorIDs, givenAccomTypeIDs, true);
        assertTrue(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(1).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueForCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreNotIgnoredAndCompetitorOverrideIsNotSet() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        createWebrate(givenOccupancyDate, "100.00", 1, 3, 1, "A", "1");
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 3, 3);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds);

        assertTrue(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(1).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueForCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreNotIgnoredAndCompetitorOverrideIsNotSet_CompetitiveRankEnabled() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        createWebrate(givenOccupancyDate, "100.00", 1, 3, 1, "A", "1");
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 3, 3);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandAndRankEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds, true);

        assertTrue(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(1).isIncludedInDemand());
        assertTrue(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueForCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreNotIgnoredAndCompetitorOverrideIsSet() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 3, 3);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpWebrateOverrideCompetitors(givenOccupancyDate);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds);

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueForCompetitorsInRateShoppingCompetitorsSettingsConfigurationAsTheyAreNotIgnoredAndCompetitorOverrideIsSet_CompetitiveRankDisabled() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 3, 3);
        tenantCrudService().save(webrateAccomClassMapping);
        createWebrateCompetitorsClassWithDemandAndRankEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, DISABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        setUpWebrateOverrideCompetitors(givenOccupancyDate);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds, true);

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void shouldShowIncludedInDemandAsTrueForCompetitorsOnlyForNonIgnoredInRateShoppingCompetitorsSettingsConfiguration() {
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 2, 1);
        tenantCrudService().save(webrateAccomClassMapping);
        webrateAccomClassMapping = getWebrateAccomClassMapping(givenOccupancyDate, 3, 3);
        tenantCrudService().save(webrateAccomClassMapping);

        createWebrateCompetitorsClass(1, 1, DISABLE_WEBRATE_COMPETITOR_CLASS_CODE, 0);
        createWebrateCompetitorsClass(1, 2, DISABLE_WEBRATE_COMPETITOR_CLASS_CODE, 0);
        //Only one competitor class is enabled
        createWebrateCompetitorsClass(1, 3, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, 0);
        setUpRoomTaxRate();

        whenGetCompetitorRatesFor(givenOccupancyDate, givenChannelIds, givenCompetitorIds, givenAccomTypeIds);

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
        //Only one competitor class rates are included in demand
        assertTrue(actualCompetitorsRates.get(2).isIncludedInDemand());
    }

    @Test
    public void isRankDisabledFor() {
        WebrateCompetitorsAccomClass accomClass1 = new WebrateCompetitorsAccomClass();
        accomClass1.setRankingEnabled(0);
        assertTrue(service.isRankDisabledFor(accomClass1));

        WebrateCompetitorsAccomClass accomClass2 = new WebrateCompetitorsAccomClass();
        accomClass2.setRankingEnabled(1);
        assertFalse(service.isRankDisabledFor(accomClass2));
    }

    @Test
    public void getWebrateRanking() {
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassOvr();
        WebrateRanking webrateRanking = UniqueWebrateRankingCreator.createWebrateRanking();
        WebrateRankingAccomClass webrateRankingAccomClass = new WebrateRankingAccomClass();
        webrateRankingAccomClass.setProductID(1);
        webrateRankingAccomClass.setAccomClass(webrateRankingAccomClassOverride.getAccomClass());
        webrateRankingAccomClass.setWebrateRankingSunday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingMonday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingTuesday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingWednesday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingThursday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingFriday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingSaturday(webrateRanking);
        AccomClass accomClass = webrateRankingAccomClass.getAccomClass();

        Date date = new LocalDate().toDate();

        //no overrides
        assertNotNull(service.getWebrateRanking(Arrays.asList(webrateRankingAccomClass), Arrays.asList(), date, 1, accomClass));

        //overrides
        assertNotNull(service.getWebrateRanking(Arrays.asList(), Arrays.asList(webrateRankingAccomClassOverride), date, 1, accomClass));

        //no webrateRankingAccomClass for that accom class
        AccomClass newAccomClass = new AccomClass();
        accomClass.setId(99);
        assertNull(service.getWebrateRanking(Arrays.asList(webrateRankingAccomClass), Arrays.asList(webrateRankingAccomClassOverride), date, 1, newAccomClass));
    }

    @Test
    public void getValueForDayOfWeek_AccomClass() {
        WebrateRanking webrateRanking = UniqueWebrateRankingCreator.createWebrateRanking();
        WebrateRankingAccomClass webrateRankingAccomClass = new WebrateRankingAccomClass();
        webrateRankingAccomClass.setWebrateRankingSunday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingMonday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingTuesday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingWednesday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingThursday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingFriday(webrateRanking);
        webrateRankingAccomClass.setWebrateRankingSaturday(webrateRanking);
        Date date = new LocalDate().toDate();

        assertNotNull(service.getValueForDayOfWeek(webrateRankingAccomClass, date));
        assertNull(service.getValueForDayOfWeek(webrateRankingAccomClass, null));
        assertNull(service.getValueForDayOfWeek((WebrateRankingAccomClass) null, date));
    }

    @Test
    public void getValueForDayOfWeek_Override() {
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassOvr();
        Date date = new LocalDate().toDate();

        assertNotNull(service.getValueForDayOfWeek(webrateRankingAccomClassOverride, date));
        assertNull(service.getValueForDayOfWeek(webrateRankingAccomClassOverride, null));
        assertNull(service.getValueForDayOfWeek((WebrateRankingAccomClass) null, date));
    }

    @Test
    public void shouldExcludeSelfCompetitorDataTest() {
        //GIVEN
        LocalDate today = LocalDate.now();
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 1, 0);
        createWebrateCompetitorsClass(4, 2, 1, 0);
        setUpRoomTaxRate();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS)).thenReturn("5367");
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(3, competitorsRateForDate.size());
        assertFalse(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase("Santa Barbara Inn")));
    }

    @Test
    public void shouldIncludeSelfCompetitorDataTest() {
        //GIVEN
        LocalDate today = LocalDate.now();
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 1, 0);
        createWebrateCompetitorsClass(4, 2, 1, 0);
        setUpRoomTaxRate();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS)).thenReturn("0");
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        //THEN
        assertEquals(4, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase("Santa Barbara Inn")));
    }

    @Test
    void shouldNotIncludeCompetitorInDemandForIgnoredChannels(){
        LocalDate givenOccupancyDate = LocalDate.parse("2018-04-05");
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(givenOccupancyDate);
        tenantCrudService().save(getWebrateAccomClassMapping(givenOccupancyDate, 2, 1));
        when(configParamsService.getBooleanParameterValue(ENABLE_IGNORE_WEBRATE_CHANNEL)).thenReturn(true);
        when(webrateChannelIgnoreService.getDayOfWeekWiseIgnoredChannels()).thenReturn(createDayOfWeekToIgnoeredChannelMap());

        actualCompetitorsRates = service.getCompetitorRates(givenOccupancyDate.toDate(), List.of(1), List.of(1), List.of(1, 2));

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
    }

    @Test
    void shouldNotIncludeCompetitorInDemandForIgnoredCompetitor(){
        int channelId = 1;
        int accomClassId = 2;
        int competitorId = 1;
        LocalDate occupancyDate = LocalDate.parse("2018-04-05");
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(occupancyDate);
        tenantCrudService().save(getWebrateAccomClassMapping(occupancyDate, accomClassId, 1));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR)).thenReturn(true);
        when(webrateShoppingDataService.getWebrateCompChannelMappings())
                .thenReturn(createWebRateChannelMappings(occupancyDate, accomClassId, competitorId, channelId));

        actualCompetitorsRates = service.getCompetitorRates(occupancyDate.toDate(), List.of(channelId), List.of(competitorId), List.of(1, 2));

        assertFalse(actualCompetitorsRates.get(0).isIncludedInDemand());
        assertFalse(actualCompetitorsRates.get(1).isIncludedInDemand());
    }

    private List<WebrateCompChannelMapping> createWebRateChannelMappings(LocalDate occupancyDate, int accomClassId, int competitorId, int channelId) {
        WebrateCompChannelMapping webrateCompChannelMapping = new WebrateCompChannelMapping();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass.setAccomClass(tenantCrudService().find(AccomClass.class, accomClassId));
        webrateCompetitorsAccomClass.setWebrateCompetitor(tenantCrudService().find(WebrateCompetitors.class, competitorId));
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setStartDate(fromDate(occupancyDate.toDate()));
        webrateOverrideCompetitor.setEndDate(fromDate(occupancyDate.toDate()));
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webrateOverrideCompetitor);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClass);
        webrateCompChannelMapping.setWebrateOverrideCompetitorDetails(webrateOverrideCompetitorDetails);
        webrateCompChannelMapping.setWebrateChannel(tenantCrudService().find(WebrateChannel.class, channelId));
        return List.of(webrateCompChannelMapping);
    }

    private static Map<DayOfWeek, List<String>> createDayOfWeekToIgnoeredChannelMap() {
        Map<DayOfWeek, List<String>> map = new HashMap<>();
        map.put(DayOfWeek.THURSDAY, List.of("Expedia"));
        return map;
    }

    private void createActiveWebratesForLOS1(LocalDate today) {
        createWebrate(today, "100.00", 1, 1, 1, "A", "1");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "100.00", 1, 3, 1, "A", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
        createWebrate(today, "400.00", 1, 1, 4, "A", "1");
        createWebrate(today, "400.00", 1, 2, 4, "A", "1");
    }

    private void setUpWebrateOverrideCompetitors(LocalDate givenOccupancyDate) {
        WebrateOverrideCompetitor webRateOverrideCompetitor = createWebRateOverrideCompetitor(givenOccupancyDate.toDate());
        tenantCrudService().save(webRateOverrideCompetitor);
        WebrateOverrideCompetitorDetails webRateOverrideCompetitorDetails = createWebRateOverrideCompetitorDetails(1, 2, webRateOverrideCompetitor, givenOccupancyDate.toDate());
        tenantCrudService().save(webRateOverrideCompetitorDetails);
        webRateOverrideCompetitorDetails = createWebRateOverrideCompetitorDetails(1, 1, webRateOverrideCompetitor, givenOccupancyDate.toDate());
        tenantCrudService().save(webRateOverrideCompetitorDetails);
        webRateOverrideCompetitorDetails = createWebRateOverrideCompetitorDetails(1, 3, webRateOverrideCompetitor, givenOccupancyDate.toDate());
        tenantCrudService().save(webRateOverrideCompetitorDetails);
    }

    private WebrateAccomClassMapping getWebrateAccomClassMapping(LocalDate today, int accomClassId, int webrateAccomTypeId) {
        WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
        webrateAccomClassMapping.setAccomClass(tenantCrudService().find(AccomClass.class, accomClassId));
        webrateAccomClassMapping.setWebrateAccomType(tenantCrudService().find(WebrateAccomType.class, webrateAccomTypeId));
        webrateAccomClassMapping.setCreateDate(today.toDate());
        webrateAccomClassMapping.setLastUpdatedDate(today.toDate());
        webrateAccomClassMapping.setCreatedByUserId(SYSTEM_USER_ID);
        webrateAccomClassMapping.setLastUpdatedByUserId(SYSTEM_USER_ID);
        return webrateAccomClassMapping;
    }

    private WebrateOverrideCompetitorDetails createWebRateOverrideCompetitorDetails(Integer competitorId, Integer accomClassId, WebrateOverrideCompetitor webRateOverrideCompetitor, Date occupancyDate) {
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.BY_ACCOM_ID_AND_COMP_ID,
                QueryParameter.with("accomId", accomClassId).and("compId", competitorId).parameters());
        webrateOverrideCompetitorDetails.setProductID(1);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClass);
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webRateOverrideCompetitor);
        webrateOverrideCompetitorDetails.setCreateDate(occupancyDate);
        webrateOverrideCompetitorDetails.setLastUpdatedDate(occupancyDate);
        webrateOverrideCompetitorDetails.setCreatedByUserId(SYSTEM_USER_ID);
        webrateOverrideCompetitorDetails.setLastUpdatedByUserId(SYSTEM_USER_ID);
        return webrateOverrideCompetitorDetails;
    }

    private WebrateOverrideCompetitor createWebRateOverrideCompetitor(Date occupancyDate) {
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setPropertyId(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE);
        webrateOverrideCompetitor.setProductID(1);
        webrateOverrideCompetitor.setCompetitorOverrideStartDT(occupancyDate);
        webrateOverrideCompetitor.setCompetitorOverrideEndDT(occupancyDate);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnSunday(0);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnMonday(1);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnTuesday(0);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnWednesday(1);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnThursday(1);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnFriday(1);
        webrateOverrideCompetitor.setIgnoreCompetitorDataOnSaturday(0);
        webrateOverrideCompetitor.setCreateDate(occupancyDate);
        webrateOverrideCompetitor.setLastUpdatedDate(occupancyDate);
        webrateOverrideCompetitor.setCreatedByUserId(SYSTEM_USER_ID);
        webrateOverrideCompetitor.setLastUpdatedByUserId(SYSTEM_USER_ID);
        return webrateOverrideCompetitor;
    }

    private void createWebrateCompetitorsClass(int competitorId, int accomClassId, int demandEnabled, int rankingEnabled) {
        createWebrateCompetitorsClass(competitorId, accomClassId, demandEnabled, rankingEnabled, null);
    }

    private void createWebrateCompetitorsClass(int competitorId, int accomClassId, int demandEnabled, int rankingEnabled, Integer DTA) {
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Demand_Enabled = :demandEnabled, Ranking_Enabled = :rankingEnabled, DTA = :DTA " +
                        "where Webrate_Competitors_ID = :competitorId and Accom_Class_ID = :accomClassId",
                QueryParameter.with("competitorId", competitorId)
                        .and("accomClassId", accomClassId)
                        .and("demandEnabled", demandEnabled)
                        .and("rankingEnabled", rankingEnabled)
                        .and("DTA", DTA)
                        .parameters());

        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Competitors_Class values(" + competitorId + ", "
                    + accomClassId + ", " + demandEnabled + ", 1, 1, GETDATE(), 1, GETDATE(), 1, " + DTA + ")");
        }
    }


    private void createWebrateCompetitorsClassWithDTAField(int competitorId, int accomClassId, int demandEnabled, int DTA) {
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set DTA = :DTA, Demand_Enabled = :demandEnabled " +
                        "where Webrate_Competitors_ID = :competitorId and Accom_Class_ID = :accomClassId ",
                QueryParameter.with("competitorId", competitorId)
                        .and("accomClassId", accomClassId)
                        .and("demandEnabled", demandEnabled)
                        .and("DTA", DTA)
                        .parameters());
        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Competitors_Class values(" + competitorId + ", "
                    + accomClassId + ", " + demandEnabled + ", 1, 1, GETDATE(), 1, GETDATE(), 1, " + DTA + ")");
        }
    }

    private void createWebrateCompetitorsClassWithRankingEnabled(int competitorId, int accomClassId, int demandEnabled, int rankingEnabled) {
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Demand_Enabled = :demandEnabled, Ranking_Enabled = :rankingEnabled  " +
                        "where Webrate_Competitors_ID = :competitorId and Accom_Class_ID = :accomClassId ",
                QueryParameter.with("competitorId", competitorId)
                        .and("accomClassId", accomClassId)
                        .and("demandEnabled", demandEnabled)
                        .and("rankingEnabled", rankingEnabled)
                        .parameters());
        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Competitors_Class values(" + competitorId + ", "
                    + accomClassId + ", " + demandEnabled + ", " + rankingEnabled + ", 1, GETDATE(), 1, GETDATE(), 1, NULL)");
        }
    }

    private void createWebrate(LocalDate today, String rate, Integer channelId, Integer webrateAccomTypeId, Integer competitorId, String webrateStatus, String los) {
        String queryStr = " update Webrate set " +
                "Webrate_RateValue = :rate, Webrate_RateValue_Display = :rate, Webrate_Status = '" + webrateStatus + "', LOS = :los " +
                "where Occupancy_DT = :date and Webrate_Channel_ID = :channelId " +
                "and Webrate_Accom_Type_ID = :webRateAccomTypeId and Webrate_Competitors_ID = :competitorsId and LOS = :los";
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery(queryStr,
                QueryParameter.with("date", today)
                        .and("rate", rate)
                        .and("channelId", channelId)
                        .and("webRateAccomTypeId", webrateAccomTypeId)
                        .and("competitorsId", competitorId)
                        .and("los", los).parameters());
        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate values(1, GETDATE(),  " + competitorId + ", " + channelId + ", "
                    + webrateAccomTypeId + ", 1, '" + today + "', " + los + ", 'Test data', '" + webrateStatus + "', 'USD', '" + rate + "', 1, 1, 1, GETDATE(), -1,'" + rate + "')");
        }
    }

    private void createWebrate(java.time.LocalDate today, String rate, Integer channelId, Integer webrateAccomTypeId, Integer competitorId, String webrateStatus, String los) {
        String queryStr = " update Webrate set " +
                "Webrate_RateValue = :rate, Webrate_RateValue_Display = :rate, Webrate_Status = '" + webrateStatus + "', LOS = :los " +
                "where Occupancy_DT = :date and Webrate_Channel_ID = :channelId " +
                "and Webrate_Accom_Type_ID = :webRateAccomTypeId and Webrate_Competitors_ID = :competitorsId and LOS = :los";
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery(queryStr,
                QueryParameter.with("date", today)
                        .and("rate", rate)
                        .and("channelId", channelId)
                        .and("webRateAccomTypeId", webrateAccomTypeId)
                        .and("competitorsId", competitorId)
                        .and("los", los).parameters());
        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate values(1, GETDATE(),  " + competitorId + ", " + channelId + ", "
                    + webrateAccomTypeId + ", 1, '" + today + "', " + los + ", 'Test data', '" + webrateStatus + "', 'USD', '" + rate + "', 1, 1, 1, GETDATE(), -1,'" + rate + "')");
        }
    }

    private void whenGetCompetitorRatesFor(LocalDate givenOccupancyDate, List<Integer> givenChannelIds, List<Integer> givenCompetitorIds, List<Integer> givenAccomTypeIds) {
        actualCompetitorsRates = service.getCompetitorRates(givenOccupancyDate.toDate(), givenChannelIds, givenCompetitorIds, givenAccomTypeIds);
    }

    private void whenGetCompetitorRatesFor(LocalDate givenOccupancyDate, List<Integer> givenChannelIds, List<Integer> givenCompetitorIds, List<Integer> givenAccomTypeIds, boolean isCompetitiveMarketPositioningEnabled) {
        actualCompetitorsRates = service.getCompetitorRates(givenOccupancyDate.toDate(), givenOccupancyDate.toDate(), givenChannelIds, givenCompetitorIds, givenAccomTypeIds, isCompetitiveMarketPositioningEnabled);
    }

    private void setUpWebrateDefaultChannels() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate_Default_Channel");
        tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Default_Channel values(5, 1, 1, 1, 1, 1, 1, 1, 1, GETDATE(), 1, GETDATE(), 1)");
    }

    private void disableRankingEnabledForCompetitors(String csvCompId) {
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Ranking_Enabled = 0 where Webrate_Competitors_ID in ("+csvCompId+")");
    }

    private void setUpRoomTaxRate() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.ZERO);
        when(taxService.findTax()).thenReturn(tax);
    }

    private void createWebrateCompetitorsClassWithDemandEnabledAs(int isDemandEnabled) {
        createWebrateCompetitorsClass(1, 1, isDemandEnabled, 0);
        createWebrateCompetitorsClass(1, 2, isDemandEnabled, 0);
        createWebrateCompetitorsClass(1, 3, isDemandEnabled, 0);
    }

    private void createWebrateCompetitorsClassWithDemandAndRankEnabledAs(int isDemandEnabled, int isRankEnabled) {
        createWebrateCompetitorsClassWithRankingEnabled(1, 1, isDemandEnabled, isRankEnabled);
        createWebrateCompetitorsClassWithRankingEnabled(1, 2, isDemandEnabled, isRankEnabled);
        createWebrateCompetitorsClassWithRankingEnabled(1, 3, isDemandEnabled, isRankEnabled);
    }

    @Test
    @Disabled
    void shouldConsiderOnlyCompetitorsWithDemandEnabledWithWhenCMPCEnabledAndNoneRanking() {
        Date dateForSeason = DateUtil.getDateWithoutTime(22, 3, 2023);
        setUpWebrateDefaultChannels();
        LocalDate occupancyDt = LocalDate.fromDateFields(dateForSeason);
        createActiveWebratesForLOS1(occupancyDt);
        LocalDate occupancyDate2 = occupancyDt.plusDays(1);
        createActiveWebratesForLOS1(occupancyDate2);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 0, 1);
        createWebrateCompetitorsClass(3, 2, 0, 1);
        createWebrateCompetitorsClass(4, 2, 1, 0);
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        createWebrateRankingAccomClassOvr(2, dateForSeason, DateUtil.addDaysToDate(dateForSeason, 1), Map.of("Mon", 2, "Tue", 3, "Wed", 3, "Thu", 2, "Fri", 4, "Sat", 1, "Sun", 2));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        //WHEN
        List<CompetitorRateInfo> competitorsRateForDateOnSat = service.getCompetitorsRateForDate(occupancyDt, 2, 1);

        assertEquals(2, competitorsRateForDateOnSat.size());
        List<CompetitorRateInfo> competitorsRateForDateOnSun = service.getCompetitorsRateForDate(occupancyDate2, 2, 1);

        assertEquals(1, competitorsRateForDateOnSun.size());

    }

    @Test
    void shouldConsiderOnlyCompetitorsWithRankEnabledWhenCMPCEnabledAndRankEnabledInDefaultConfig() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate today = LocalDate.fromDateFields(date);
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 0, 1);
        createWebrateCompetitorsClass(4, 3, 1, 1);
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 2, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        assertEquals(2, competitorsRateForDate.size());
    }

    @Test
    void shouldConsiderOnlyCompetitorsWithRankEnabledWhenCMPCEnabledAndRateShoppingRateTypeAndDefaultChannel() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate today = LocalDate.fromDateFields(date);
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 0, 1);
        createWebrateCompetitorsClass(4, 3, 1, 1);
        mockWebrateTypeProductMapping();
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        //WHEN
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);
        when(decisionSupportService.getDecisionSupportForAnyProduct(2, date, 1, 1)).thenReturn(new DecisionSupport());
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 2, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        assertEquals(4, competitorsRateForDate.size());
    }

    @Test
    void shouldConsiderOnlyCompetitorsWithRankEnabledWhenCMPCEnabledAndNoSeasonAndInDefault() {
        Date dateWithoutTime = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate today = LocalDate.fromDateFields(dateWithoutTime);
        setUpWebrateDefaultChannels();
        createActiveWebratesForLOS1(today);
        createWebrateCompetitorsClass(1, 2, 1, 1);
        createWebrateCompetitorsClass(2, 2, 1, 1);
        createWebrateCompetitorsClass(3, 2, 0, 1);
        createWebrateCompetitorsClass(4, 2, 1, 1);
        setUpRoomTaxRate();
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 2, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorsRateForDate(today, 2, 1);
        assertEquals(3, competitorsRateForDate.size());
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDateIsWithinSeasonWithNoneRanking() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(occupancyDate, 2);
        assertNotNull(ranking);
        assertEquals("None", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDCMPCEnabledAndOccupancyIsLessThanOnBooksThreshold() {
        Date date = DateUtil.getDateWithoutTime(23, 2, 2023);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN)).thenReturn(true);
        when(accommodationService.getOccupancyPercentage(any())).thenReturn(BigDecimal.TEN);
        DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
        dto.setDowId(Byte.valueOf("5"));
        dto.setMaxPercentile(BigDecimal.valueOf(30));
        dto.setOnBooksThresholdPercent(BigDecimal.valueOf(20));
        dto.setAccomClassName("accomClass1");
        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(any(), any(), any(), any())).thenReturn(List.of(dto));

        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(occupancyDate), 2, 1);
        assertNotNull(ranking);
        assertEquals(dto.getOnBooksThresholdPercent() + Constants.REGULATOR_CONTEXT_DELIMITER + dto.getMaxPercentile(), ranking);
    }

    @Test
    void testFetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentageWhenOccupancyPercentIsNull() {
        Date date = DateUtil.getDateWithoutTime(23, 2, 2023);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN)).thenReturn(true);
        when(accommodationService.getOccupancyPercentage(any())).thenReturn(null);

        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(any(), any(), any(), any())).thenReturn(null);

        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(occupancyDate), 2, 1);
        assertNotNull(ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDCMPCEnabledAndOccupancyIsLessThanOnBooksThresholdAndToggleOff() {
        Date date = DateUtil.getDateWithoutTime(23, 2, 2023);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN)).thenReturn(false);
        when(accommodationService.getOccupancyPercentage(any())).thenReturn(BigDecimal.TEN);
        DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
        dto.setDowId(Byte.valueOf("5"));
        dto.setMaxPercentile(BigDecimal.valueOf(30));
        dto.setOnBooksThresholdPercent(BigDecimal.valueOf(20));
        dto.setAccomClassName("accomClass1");
        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(any(), any(), any(), any())).thenReturn(List.of(dto));

        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(occupancyDate), 2, 1);
        assertNotNull(ranking);
        assertEquals("None", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDCMPCEnabledAndOccupancyIsGreaterThanOnBooksThreshold() {
        Date date = DateUtil.getDateWithoutTime(23, 2, 2023);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN)).thenReturn(true);
        when(accommodationService.getOccupancyPercentage(any())).thenReturn(BigDecimal.valueOf(40));
        DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
        dto.setDowId(Byte.valueOf("5"));
        dto.setMaxPercentile(BigDecimal.valueOf(30));
        dto.setOnBooksThresholdPercent(BigDecimal.valueOf(20));
        dto.setAccomClassName("accomClass1");
        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(any(), any(), any(), any())).thenReturn(List.of(dto));

        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(occupancyDate), 2, 1);
        assertNotNull(ranking);
        assertEquals("None", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDCMPCEnabledAndConfigNotSet() {
        Date date = DateUtil.getDateWithoutTime(23, 2, 2023);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(service.getConfigService().getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN)).thenReturn(true);
        when(accommodationService.getOccupancyPercentage(any())).thenReturn(BigDecimal.valueOf(40));

        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 1, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(occupancyDate), 2, 1);
        assertNotNull(ranking);
        assertEquals("None", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDateIsWithinSeasonWithRanking() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 5, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(occupancyDate, 2);
        assertNotNull(ranking);
        assertEquals("Low Range", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDateIsWithinSeasonForDiffAccomClass() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(3, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 5, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 3, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(occupancyDate, 2);
        assertNotNull(ranking);
        assertEquals("High Range", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenDateIsNotInSeason() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(2, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 4, "Thu", 4, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(2, Map.of("Mon", 1, "Tue", 1, "Wed", 2, "Thu", 2, "Fri", 1, "Sat", 1, "Sun", 1));

        String ranking = service.getCompetitiveMarketPositionConstraints(occupancyDate.plusDays(1), 2);
        assertNotNull(ranking);
        assertEquals("Above all Competitors", ranking);
    }

    @Test
    void testGetCompetitiveMarketPositionConstraintsWhenNoSeasonAndDefaultConfig() {
        Date date = DateUtil.getDateWithoutTime(22, 2, 2023);
        LocalDate occupancyDate = LocalDate.fromDateFields(date);
        createWebrateRankingAccomClassOvr(3, date, date, Map.of("Mon", 1, "Tue", 1, "Wed", 5, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        createWebrateRankingAccomClass(3, Map.of("Mon", 1, "Tue", 1, "Wed", 3, "Thu", 1, "Fri", 1, "Sat", 1, "Sun", 1));
        String ranking = service.getCompetitiveMarketPositionConstraints(occupancyDate.plusDays(1), 2);
        assertNull(ranking);
    }

    @Test
    void shouldDisableDemandWhenOccupancyDtOutsideDTARangeCompetitorMarketPositionDisabled() {
        java.time.LocalDate givenOccupancyDate = java.time.LocalDate.of(2015, 11, 20);//systemdate(16/11/2015)
        List<Integer> givenChannelIds = Arrays.asList(1);
        List<Integer> givenCompetitorIds = Arrays.asList(1);
        List<Integer> givenAccomTypeIds = Arrays.asList(1, 2, 3);

        setUpWebrateDefaultChannels();
        createWebrate(givenOccupancyDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(givenOccupancyDate, "100.00", 1, 3, 1, "A", "1");
        createWebrate(givenOccupancyDate, "100.00", 1, 1, 1, "A", "1");

        createWebrateCompetitorsClassWithDemandAndRankEnabledAs(ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE);
        createWebrateCompetitorsClassWithDTAField(1, 2, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, 3);
        createWebrateCompetitorsClassWithDTAField(1, 3, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, 10);
        createWebrateCompetitorsClassWithDTAField(1, 1, ENABLE_WEBRATE_COMPETITOR_CLASS_CODE, 5);
        setUpRoomTaxRate();

        List<CompetitorRateInfo> competitorsRateForDate = service.getCompetitorRates(convertLocalDateToJavaUtilDate(givenOccupancyDate), givenChannelIds, givenCompetitorIds, givenAccomTypeIds);

        assertFalse(competitorsRateForDate.get(0).isIncludedInDemand());
        assertFalse(competitorsRateForDate.get(1).isIncludedInDemand());
        assertTrue(competitorsRateForDate.get(2).isIncludedInDemand());
    }


    private WebrateRankingAccomClassOverride createWebrateRankingAccomClassOvr(int accomClassId, Date startdate,
                                                                               Date endDate, Map<String, Integer> rankingForDOW) {
        WebrateRankingAccomClassOverride wrac = new WebrateRankingAccomClassOverride();
        AccomClass ac = new AccomClass();
        ac.setId(accomClassId);
        wrac.setAccomClass(ac);
        wrac.setProductID(1);
        wrac.setWebrateRankingStartDT(startdate);
        wrac.setWebrateRankingEndDT(endDate);
        wrac.setWebrateRankingOvrMonday(getRanking(rankingForDOW.get("Mon")));
        wrac.setWebrateRankingOvrTuesday(getRanking(rankingForDOW.get("Tue")));
        wrac.setWebrateRankingOvrWednesday(getRanking(rankingForDOW.get("Wed")));
        wrac.setWebrateRankingOvrThursday(getRanking(rankingForDOW.get("Thu")));
        wrac.setWebrateRankingOvrFriday(getRanking(rankingForDOW.get("Fri")));
        wrac.setWebrateRankingOvrSaturday(getRanking(rankingForDOW.get("Sat")));
        wrac.setWebrateRankingOvrSunday(getRanking(rankingForDOW.get("Sun")));
        tenantCrudService().save(wrac);
        return wrac;
    }

    private WebrateRankingAccomClass createWebrateRankingAccomClass(int accomClassId, Map<String, Integer> rankingForDOW) {
        WebrateRankingAccomClass wrac = new WebrateRankingAccomClass();
        AccomClass ac = new AccomClass();
        ac.setId(accomClassId);
        wrac.setWebrateRanking(getRanking(1));
        wrac.setAccomClass(ac);
        wrac.setProductID(1);
        wrac.setWebrateRankingMonday(getRanking(rankingForDOW.get("Mon")));
        wrac.setWebrateRankingTuesday(getRanking(rankingForDOW.get("Tue")));
        wrac.setWebrateRankingWednesday(getRanking(rankingForDOW.get("Wed")));
        wrac.setWebrateRankingThursday(getRanking(rankingForDOW.get("Thu")));
        wrac.setWebrateRankingFriday(getRanking(rankingForDOW.get("Fri")));
        wrac.setWebrateRankingSaturday(getRanking(rankingForDOW.get("Sat")));
        wrac.setWebrateRankingSunday(getRanking(rankingForDOW.get("Sun")));
        tenantCrudService().save(wrac);
        return wrac;
    }

    public WebrateRanking getRanking(Integer id) {
        WebrateRanking wr = new WebrateRanking();
        wr.setId(id);
        return wr;
    }

    @Test
    public void getCompetitorRatesForDateRangeIndToggleOff() {
        Date fromDate = new Date();
        Date toDate = DateUtil.addDaysToDate(fromDate, 2);

        setUpWebrateDefaultChannels();

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(1);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);

        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRatesIndProductToggleoff(fromDate, toDate, channelIds, competitorIds, accomTypeIds, false);
        assertEquals(4, competitorsRateForDateRange.size());
        CompetitorRateInfo firstValue = competitorsRateForDateRange.get(2);
        assertNotEquals(firstValue.getOccupancyDate().getClass(), new Date().getClass());
        assertNotEquals(firstValue.getWebrateGenerationDate().getClass(), new Date().getClass());
    }

    @Test
    public void getCompetitorRatesForDateRangeCMPCEnabled() {
        Date fromDate = new Date();
        Date toDate = DateUtil.addDaysToDate(fromDate, 2);

        setUpWebrateDefaultChannels();

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(1);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);

        List<CompetitorRateInfo> competitorsRateForDateRange1 = service.getCompetitorRatesIndProductToggleoff(fromDate, toDate, channelIds, competitorIds, accomTypeIds, true);
        assertEquals(4, competitorsRateForDateRange1.size());
        CompetitorRateInfo firstValue1 = competitorsRateForDateRange1.get(2);
        assertNotEquals(firstValue1.getOccupancyDate().getClass(), new Date().getClass());
        assertNotEquals(firstValue1.getWebrateGenerationDate().getClass(), new Date().getClass());

        disableRankingEnabledForCompetitors("3, 4, 1, 2");
        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRatesIndProductToggleoff(fromDate, toDate, channelIds, competitorIds, accomTypeIds, true);
        assertEquals(4, competitorsRateForDateRange.size());
        CompetitorRateInfo firstValue = competitorsRateForDateRange.get(2);
        assertNotEquals(firstValue.getOccupancyDate().getClass(), new Date().getClass());
        assertNotEquals(firstValue.getWebrateGenerationDate().getClass(), new Date().getClass());
    }

    @Test
    public void testIgnoreCompetitorData() {

        // create ignore competitor data for today
        WebrateOverrideCompetitorDetails overCompDetails = UniqueWebrateOverrideCompetitorDetailsCreator.createWebrateOverrideCompetitorDetails();

        // check query returning data for today
        Integer accomClassId = overCompDetails.getWebrateCompetitorsAccomClass().getAccomClass().getId();
        List<Integer> ignoreCompetitorIds = service.getIgnoreCompetitorData(java.time.LocalDate.now(), accomClassId);
        assertEquals(1, ignoreCompetitorIds.size());

        // check query returning data for today for different class
        ignoreCompetitorIds = service.getIgnoreCompetitorData(java.time.LocalDate.now().plusDays(1), ACCOM_CLASS_ID);
        assertEquals(0, ignoreCompetitorIds.size());

        // check query returning data for tomorrow.
        ignoreCompetitorIds = service.getIgnoreCompetitorData(java.time.LocalDate.now().plusDays(1), accomClassId);
        assertEquals(0, ignoreCompetitorIds.size());

        // check query returning data for tomorrow for different class
        ignoreCompetitorIds = service.getIgnoreCompetitorData(java.time.LocalDate.now().plusDays(1), ACCOM_CLASS_ID);
        assertEquals(0, ignoreCompetitorIds.size());

    }

    @Test
    void testCompetitorsRateForWhenDTAIsConfiguredSPRankingEnabled() {
        java.time.LocalDate today = java.time.LocalDate.now();
        setupForCompetitorsRateForWhenDTA(today);
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(eq(1))).thenReturn(true);
        verifyResultsOfCompetitorsRateWhenDTAAndRankingEnabled(today);
    }

    @Test
    void testCompetitorsRateForWhenDTAIsConfiguredSPRankingDisabled() {
        java.time.LocalDate today = java.time.LocalDate.now();
        setupForCompetitorsRateForWhenDTA(today);
        verifyResultsOfCompetitorsRateWhenDTA(today);
    }

    @Test
    void testCompetitorsRateWhenDTAIsConfiguredNativeQuery() {
        java.time.LocalDate today = java.time.LocalDate.now();
        setupForCompetitorsRateForWhenDTA(today);
        System.setProperty("pacman.use.optimized.stored.procedure.investigator", "false");
        verifyResultsOfCompetitorsRateWhenDTA(today);
    }

    @Test
    void testCompetitorsRateWhenDTAIsConfiguredAndRDLEnabledForAllChannels() {

        int competitorID = 1;
        String competitorName = getCompetitorName(competitorID);

        java.time.LocalDate today = java.time.LocalDate.now();
        setupForCompetitorsRateForWhenDTA(today);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RATE_SHOPPING_RATE_TYPE_FOR_PRICING_INVESTIGATOR)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(true);
        when(decisionSupportService.getDecisionSupportForAnyProduct(eq(2), any(), eq(1), eq(1))).thenReturn(any(DecisionSupport.class));
        when(webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(1)).thenReturn(true);
        List<CompetitorRateInfo> competitorsRateForDate;
        java.time.LocalDate firstOccupancyDt = getFirstOccupancyDt();
        when(dateService.getCaughtUpDate()).thenReturn(convertLocalDateToJavaUtilDate(firstOccupancyDt));
        //When DTA is null
        //test for same occupancy date
        createWebrateCompetitorsClass(1, 2, 1, 1, null);
        mockWebrateTypeProductMapping();
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt), 2, 1);
        assertEquals(24, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //test for other future date
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(today), 2, 1);
        assertEquals(24, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //When DTA is not null and within range
        createWebrateCompetitorsClass(1, 2, 1, 1, 1);
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(1)), 2, 1);
        assertEquals(24, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //When DTA is not null and outside range
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(2)), 2, 1);
        assertEquals(16, competitorsRateForDate.size());
        assertFalse(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));
    }

    @Test
    void testCompetitorsRateWhenDTAIsConfiguredAndForSingleChannel() {
        java.time.LocalDate today = java.time.LocalDate.now();
        setupForCompetitorsRateForWhenDTA(today);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RATE_SHOPPING_RATE_TYPE_FOR_PRICING_INVESTIGATOR)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS)).thenReturn(false);
        verifyResultsOfCompetitorsRateWhenDTA(today);
    }

    @Test
    void shouldReturnCompetitorRateWhenPricingScreenOptimizationEnabled() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(true);
        mockWebRateDataAndAssertCompetitorRates();
    }

    @Test
    void shouldReturnCompetitorRateInfoForAccomTypeToMultipleAccomClassesMapping() {
        mockWebRateDataAndAssertCompetitorRates();
    }

    private void mockWebRateDataAndAssertCompetitorRates() {
        LocalDate fromDate = new LocalDate();
        LocalDate toDate = new LocalDate().plusDays(2);

        setUpWebrateDefaultChannels();
        createWebrate(fromDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(fromDate, "210.00", 1, 2, 2, "A", "1");
        createWebrate(toDate, "310.00", 1, 2, 3, "A", "1");
        createWebrate(fromDate, "410.00", 1, 2, 4, "A", "1");

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(2);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);
        List<Integer> accomClassIds = Arrays.asList(2, 3);

        populateWebrateAccomClassMapping();

        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRates(fromDate.toDate(), toDate.toDate(), channelIds, competitorIds, accomTypeIds, accomClassIds, false);

        assertEquals(8, competitorsRateForDateRange.size());

        CompetitorRateInfo competitorRateInfoForRoomClassSTD = competitorsRateForDateRange.get(0);
        CompetitorRateInfo competitorRateInfoForRoomClassDLX = competitorsRateForDateRange.get(1);

        assertEquals("Standard 2", competitorRateInfoForRoomClassSTD.getRoomTypeName());
        assertEquals("Standard 2", competitorRateInfoForRoomClassDLX.getRoomTypeName());
        assertEquals("STD", competitorRateInfoForRoomClassSTD.getAccomClassName());
        assertEquals("DLX", competitorRateInfoForRoomClassDLX.getAccomClassName());
        assertEquals(new BigDecimal("100.00000"), competitorRateInfoForRoomClassSTD.getRate());
        assertEquals(new BigDecimal("100.00000"), competitorRateInfoForRoomClassDLX.getRate());
    }

    @Test
    void shouldReturnCompetitorRatesViewInfoForAccomTypeToMultipleAccomClassesMapping() {

        LocalDate fromDate = new LocalDate();
        LocalDate toDate = new LocalDate().plusDays(2);

        setUpWebrateDefaultChannels();
        createWebrate(fromDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(fromDate, "210.00", 1, 2, 2, "A", "1");
        createWebrate(toDate, "310.00", 1, 2, 3, "A", "1");
        createWebrate(fromDate, "410.00", 1, 2, 4, "A", "1");

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(2);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);
        List<Integer> accomClassIds = Arrays.asList(2, 3);
        List<Integer> productIds = Arrays.asList(1);

        populateWebrateAccomClassMapping();

        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRatesView(fromDate.toDate(),
                toDate.toDate(), channelIds, competitorIds, accomTypeIds, accomClassIds, false, productIds);

        assertEquals(8, competitorsRateForDateRange.size());

        CompetitorRateInfo competitorRateInfoForRoomClassSTD = competitorsRateForDateRange.get(0);
        CompetitorRateInfo competitorRateInfoForRoomClassDLX = competitorsRateForDateRange.get(1);

        assertEquals("Standard 2", competitorRateInfoForRoomClassSTD.getRoomTypeName());
        assertEquals("Standard 2", competitorRateInfoForRoomClassDLX.getRoomTypeName());
        assertEquals("BAR", competitorRateInfoForRoomClassSTD.getProductName());
        assertEquals("BAR", competitorRateInfoForRoomClassDLX.getProductName());
        assertEquals("STD", competitorRateInfoForRoomClassSTD.getAccomClassName());
        assertEquals("DLX", competitorRateInfoForRoomClassDLX.getAccomClassName());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassSTD.getRate());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassDLX.getRate());
    }

    @Test
    void shouldReturnCompetitorRatesViewInfoForAccomTypeToMultipleAccomClassesMappingDCMPCEnabled() {

        LocalDate fromDate = new LocalDate();
        LocalDate toDate = new LocalDate().plusDays(2);

        setUpWebrateDefaultChannels();
        createWebrate(fromDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(fromDate, "210.00", 1, 2, 2, "A", "1");
        createWebrate(toDate, "310.00", 1, 2, 3, "A", "1");
        createWebrate(fromDate, "410.00", 1, 2, 4, "A", "1");

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(2);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);
        List<Integer> accomClassIds = Arrays.asList(2, 3);
        List<Integer> productIds = Arrays.asList(1);

        populateWebrateAccomClassMapping();

        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRatesView(fromDate.toDate(),
                toDate.toDate(), channelIds, competitorIds, accomTypeIds, accomClassIds, true, productIds);

        assertEquals(8, competitorsRateForDateRange.size());

        CompetitorRateInfo competitorRateInfoForRoomClassSTD = competitorsRateForDateRange.get(0);
        CompetitorRateInfo competitorRateInfoForRoomClassDLX = competitorsRateForDateRange.get(1);

        assertEquals("Standard 2", competitorRateInfoForRoomClassSTD.getRoomTypeName());
        assertEquals("Standard 2", competitorRateInfoForRoomClassDLX.getRoomTypeName());
        assertEquals("BAR", competitorRateInfoForRoomClassSTD.getProductName());
        assertEquals("BAR", competitorRateInfoForRoomClassDLX.getProductName());
        assertEquals("STD", competitorRateInfoForRoomClassSTD.getAccomClassName());
        assertEquals("DLX", competitorRateInfoForRoomClassDLX.getAccomClassName());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassSTD.getRate());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassDLX.getRate());
    }

    @Test
    void shouldReturnCompetitorRatesViewInfoForAccomTypeToMultipleAccomClassesMappingDCMPCEnabledRankingDisabled() {

        LocalDate fromDate = new LocalDate();
        LocalDate toDate = new LocalDate().plusDays(2);

        setUpWebrateDefaultChannels();
        createWebrate(fromDate, "100.00", 1, 2, 1, "A", "1");
        createWebrate(fromDate, "210.00", 1, 2, 2, "A", "1");
        createWebrate(toDate, "310.00", 1, 2, 3, "A", "1");
        createWebrate(fromDate, "410.00", 1, 2, 4, "A", "1");

        setUpRoomTaxRate();
        List<Integer> accomTypeIds = Collections.singletonList(2);
        List<Integer> competitorIds = Arrays.asList(3, 4, 1, 2);
        List<Integer> channelIds = Collections.singletonList(1);
        List<Integer> accomClassIds = Arrays.asList(2, 3);
        List<Integer> productIds = Arrays.asList(1);

        populateWebrateAccomClassMapping();

        disableRankingEnabledForCompetitors("3, 4, 1, 2");

        List<CompetitorRateInfo> competitorsRateForDateRange = service.getCompetitorRatesView(fromDate.toDate(),
                toDate.toDate(), channelIds, competitorIds, accomTypeIds, accomClassIds, true, productIds);

        assertEquals(8, competitorsRateForDateRange.size());

        CompetitorRateInfo competitorRateInfoForRoomClassSTD = competitorsRateForDateRange.get(0);
        CompetitorRateInfo competitorRateInfoForRoomClassDLX = competitorsRateForDateRange.get(1);

        assertEquals("Standard 2", competitorRateInfoForRoomClassSTD.getRoomTypeName());
        assertEquals("Standard 2", competitorRateInfoForRoomClassDLX.getRoomTypeName());
        assertEquals("BAR", competitorRateInfoForRoomClassSTD.getProductName());
        assertEquals("BAR", competitorRateInfoForRoomClassDLX.getProductName());
        assertEquals("STD", competitorRateInfoForRoomClassSTD.getAccomClassName());
        assertEquals("DLX", competitorRateInfoForRoomClassDLX.getAccomClassName());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassSTD.getRate());
        assertEquals(new BigDecimal("100.00"), competitorRateInfoForRoomClassDLX.getRate());
    }

    @Test
    void testGetRateShoppingRateTypeForPricingInvestigatorAllChannelWithoutIgnoredChannels(){

    }

    @Test
    void testGetRateShoppingRateTypeForPricingInvestigatorAllChannelWithIgnoredChannels(){

    }

    @Test
    void getCompetitorRate_forNonRDL() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(false);
        Date currentDate = DateUtil.convertLocalDateToJavaUtilDate(java.time.LocalDate.now());
        List<CompetitorRates> competitorRates = service.getCompetitorRates(currentDate, currentDate, false, 0, 2);
        assertNotNull(competitorRates);
        assertEquals(2, competitorRates.size());
    }

    private void populateWebrateAccomClassMapping() {
        tenantCrudService().deleteAll(WebrateAccomClassMapping.class);
        for (int i = 2; i < 4; i++) {
            WebrateAccomClassMapping webrateAccomClassMappingToSave = new WebrateAccomClassMapping();
            webrateAccomClassMappingToSave.setAccomClass(tenantCrudService().find(AccomClass.class, i));
            webrateAccomClassMappingToSave.setWebrateAccomType(tenantCrudService().find(WebrateAccomType.class, 2));
            webrateAccomClassMappingToSave.setCreatedByUserId(SYSTEM_USER_ID);
            webrateAccomClassMappingToSave.setLastUpdatedByUserId(SYSTEM_USER_ID);
            tenantCrudService().save(webrateAccomClassMappingToSave);
        }
    }

    private void setupForCompetitorsRateForWhenDTA(java.time.LocalDate today) {
        setUpWebrateDefaultChannels();

        createWebrate(today, "150.00", 1, 1, 1, "A", "1");
        createWebrate(today, "100.00", 1, 2, 1, "A", "1");
        createWebrate(today, "200.00", 1, 1, 2, "A", "1");
        createWebrate(today, "200.00", 1, 2, 2, "A", "1");
        createWebrate(today, "300.00", 1, 1, 3, "A", "1");
        createWebrate(today, "300.00", 1, 2, 3, "A", "1");
    }

    private void verifyResultsOfCompetitorsRateWhenDTAAndRankingEnabled(java.time.LocalDate today) {

        int competitorID = 1;
        String competitorName = getCompetitorName(competitorID);

        List<CompetitorRateInfo> competitorsRateForDate;
        java.time.LocalDate firstOccupancyDt = getFirstOccupancyDt();
        when(dateService.getCaughtUpDate()).thenReturn(convertLocalDateToJavaUtilDate(firstOccupancyDt));
        createWebrateRankingAccomClassOvr(2, DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt), DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));
        //When DTA is null
        //test for same occupancy date
        createWebrateCompetitorsClass(1, 2, 1, 1, null);

        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt), 2, 1);
        assertEquals(3, competitorsRateForDate.size());

        createWebrateRankingAccomClassOvr(2, DateUtil.convertLocalDateToJavaUtilDate(today), DateUtil.convertLocalDateToJavaUtilDate(today), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));
        //test for other future date
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(today), 2, 1);
        assertEquals(3, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));


        //When DTA is not null and within range
        createWebrateCompetitorsClass(1, 2, 1, 1, 1);
        createWebrateRankingAccomClassOvr(2, DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt.plusDays(1)), DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt.plusDays(1)), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(1)), 2, 1);
        assertEquals(3, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //When DTA is not null and outside range
        createWebrateRankingAccomClassOvr(2, DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt.plusDays(2)), DateUtil.convertLocalDateToJavaUtilDate(firstOccupancyDt.plusDays(2)), Map.of("Mon", 2, "Tue", 4, "Wed", 2, "Thu", 4, "Fri", 3, "Sat", 2, "Sun", 2));
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(2)), 2, 1);
        assertEquals(2, competitorsRateForDate.size());
        assertFalse(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));
    }

    private void verifyResultsOfCompetitorsRateWhenDTA(java.time.LocalDate today) {

        int competitorID = 1;
        String competitorName = getCompetitorName(competitorID);

        List<CompetitorRateInfo> competitorsRateForDate;
        java.time.LocalDate firstOccupancyDt = getFirstOccupancyDt();
        when(dateService.getCaughtUpDate()).thenReturn(convertLocalDateToJavaUtilDate(firstOccupancyDt));

        //When DTA is null
        //test for same occupancy date
        createWebrateCompetitorsClass(competitorID, 2, 1, 1, null);
        mockWebrateTypeProductMapping();
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt), 2, 1);
        assertEquals(3, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //test for other future date
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(today), 2, 1);
        assertEquals(3, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //When DTA is not null and within range
        createWebrateCompetitorsClass(competitorID, 2, 1, 1, 1);

        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(1)), 2, 1);
        assertEquals(3, competitorsRateForDate.size());
        assertTrue(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

        //When DTA is not null and outside range
        competitorsRateForDate = service.getCompetitorsRateForDate(DateUtil.convertJavaToJodaLocalDate(firstOccupancyDt.plusDays(2)), 2, 1);
        assertEquals(2, competitorsRateForDate.size());
        assertFalse(competitorsRateForDate.stream().anyMatch(comp -> comp.getCompetitorName().equalsIgnoreCase(competitorName)));

    }

    private java.time.LocalDate getFirstOccupancyDt() {
        return tenantCrudService().findByNativeQuerySingleResult("select min(occupancy_DT) minDT from webrate", null, objects -> (java.sql.Date) objects[0]).toLocalDate();
    }

    private String getCompetitorName(int competitorID) {
        return tenantCrudService().findByNativeQuerySingleResult("select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_ID = :competitorID", QueryParameter.with("competitorID", competitorID).parameters(), objects -> (String) objects[0]);
    }

}
