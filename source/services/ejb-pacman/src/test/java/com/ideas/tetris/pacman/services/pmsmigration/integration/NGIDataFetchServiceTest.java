package com.ideas.tetris.pacman.services.pmsmigration.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.service.RoomTypeActivityClientAPIService;
import com.ideas.tetris.pacman.services.groupblock.service.GroupBlockClientAPIService;
import com.ideas.tetris.pacman.services.pmsmigration.dto.MktSegsRateCodesRoomTypesGroupCodesHolder;
import com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.Reservation;
import com.ideas.tetris.pacman.services.reservation.service.ReservationClientAPIService;
import com.ideas.tetris.pacman.services.reservation.service.ReservationClientAPIV2Service;
import com.ideas.tetris.pacman.services.reservationnight.ReservationNightConverter;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.hamcrest.Matchers;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.ARRIVAL_DATE;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.ARRIVAL_DATE_PLUS_3_DAYS;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.ARRIVAL_DATE_STR;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.DEPARTURE_DATE_STR;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.MIN_DEPARTURE_DATE;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.reservation;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.reservationNightBatched;
import static com.ideas.tetris.pacman.services.pmsmigration.tools.reservationdiscr.ReservationDiscrepancyTestUtil.stayDateRange;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyMapOf;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class NGIDataFetchServiceTest {

    @Mock
    private RestClient restClient;

    @Mock
    private ReservationNightConverter reservationNightConverter;

    @InjectMocks
    private NGIDataFetchService ngiDataFetchService;

    @Mock
    private ReservationClientAPIService reservationClientAPIService;

    @Mock
    private ReservationClientAPIV2Service reservationClientV2APIService;

    @Mock
    private PacmanConfigParamsService configService;

    @Mock
    private GroupBlockClientAPIService groupBlockClientAPIService;

    @Mock
    private RoomTypeActivityClientAPIService roomTypeActivityClientAPIService;

    @BeforeEach
    public void setUp() throws Exception {
        WorkContextType wc = new WorkContextType();
        wc.setClientCode(TestClient.BSTN.name());
        wc.setPropertyCode(TestProperty.H1.name());
        PacmanWorkContextHelper.setWorkContext(wc);
    }

    @AfterEach
    public void tearDown() {
        System.setProperty("pacman.pms.inbound.v2.enabled", "false");
    }


    @Test
    public void fetch() {
        FetchCriteria fetchCriteria = new FetchCriteria()
                .add("clientCode", TestClient.BSTN.name())
                .add("propertyCode", TestProperty.H1.name());

        List<Object> reservations = Collections.singletonList(reservation());

        when(reservationClientAPIService.reservationsList(anyMap(), any())).thenReturn(reservations);

        List<Reservation> fetchedReservations = ngiDataFetchService.fetchReservations(fetchCriteria);

        assertEquals(reservations, fetchedReservations);
        verify(reservationClientAPIService).reservationsList(anyMap(), any());
    }

    @Test
    public void fetchUsingV2() {
        System.setProperty("pacman.pms.inbound.v2.enabled", "true");
        FetchCriteria fetchCriteria = new FetchCriteria()
                .add("clientCode", TestClient.BSTN.name())
                .add("propertyCode", TestProperty.H1.name());
        List<Object> reservations = Collections.singletonList(reservation());
        when(reservationClientV2APIService.reservationsList(anyMap(), any())).thenReturn(reservations);

        List<Reservation> fetchedReservations = ngiDataFetchService.fetchReservations(fetchCriteria);

        assertEquals(reservations, fetchedReservations);
        verify(reservationClientV2APIService).reservationsList(anyMap(), any());
    }

    @Test
    public void getMinimumDepartureDate() {
        when(reservationClientAPIService.minimumDepartureDate(anyString(), anyString())).thenReturn(MIN_DEPARTURE_DATE);

        assertEquals(MIN_DEPARTURE_DATE, ngiDataFetchService.getMinDateToDeleteInHouseAndFutureReservaions());
        verify(reservationClientAPIService).minimumDepartureDate(anyString(), anyString());
    }

    @Test
    void getColumnNameToDelete() {
        assertEquals(NGIDataFetchService.COLUMN_NAME_TO_DELETE_FROM, ngiDataFetchService.getColumnNameToDeleteFrom());
    }

    @Test
    public void getMinimumDepartureDateUsingV2() {
        System.setProperty("pacman.pms.inbound.v2.enabled", "true");
        when(reservationClientV2APIService.minimumDepartureDate(anyString(), anyString())).thenReturn(MIN_DEPARTURE_DATE);

        assertEquals(MIN_DEPARTURE_DATE, ngiDataFetchService.getMinDateToDeleteInHouseAndFutureReservaions());
        verify(reservationClientV2APIService).minimumDepartureDate(anyString(), anyString());
    }

    @Test
    public void reservationJsonObjToReservationMapper() throws JsonProcessingException, JSONException {
        Map<String, Object> rawReservation =
                MapBuilder.with(ARRIVAL_DATE_STR, ARRIVAL_DATE)
                        .and(DEPARTURE_DATE_STR, ARRIVAL_DATE_PLUS_3_DAYS)
                        .get();
        when(reservationNightConverter.convertRoomStaysWYGIWYGStyle(anyMapOf(String.class, Object.class)))
                .thenReturn(Collections.singletonList(reservationNightBatched()));

        String jsonifiedReservation = (new ObjectMapper()).writeValueAsString(rawReservation);
        Reservation convertedReservation = ngiDataFetchService.reservationJsonObjToReservationMapper(new JSONObject(jsonifiedReservation));

        ArgumentCaptor<Map> reservationMap = ArgumentCaptor.forClass(Map.class);
        verify(reservationNightConverter).convertRoomStaysWYGIWYGStyle(reservationMap.capture());
        assertEquals(((Date) rawReservation.get(ARRIVAL_DATE_STR)).getTime(), reservationMap.getValue().get(ARRIVAL_DATE_STR));
        assertEquals(((Date) rawReservation.get(DEPARTURE_DATE_STR)).getTime(), reservationMap.getValue().get(DEPARTURE_DATE_STR));

        assertEquals(stayDateRange(ARRIVAL_DATE, ARRIVAL_DATE_PLUS_3_DAYS), convertedReservation.stayDateRange());

    }

    @Test
    public void testConfigurationJsonObjToConfigHolderMapper() throws JSONException {
        String json = "{ \"marketSegmentCodes\":[\"MS1\"], \"roomTypeCodes\":[\"RT1\"], \"rateCodes\":[\"RC1\",\"RC2\"], \"groupCodes\":[] }";

        MktSegsRateCodesRoomTypesGroupCodesHolder mappedResult = ngiDataFetchService.configurationJsonObjToConfigHolderMapper().mapResult(new JSONObject(json));

        assertThat(mappedResult.getMarketSegmentCodes(), Matchers.containsInAnyOrder("MS1"));
        assertThat(mappedResult.getRoomTypeCodes(), Matchers.containsInAnyOrder("RT1"));
        assertThat(mappedResult.getRateCodes(), Matchers.containsInAnyOrder("RC1", "RC2"));
        assertThat(mappedResult.getGroupCodes(), Matchers.containsInAnyOrder());
    }

    @Test
    public void fetchesConfigurationData() {
        MktSegsRateCodesRoomTypesGroupCodesHolder fromOccupancySummary = getMktSegsRateCodesRoomTypesGroupCodesHolder();

        Map<String, String> parameters = clientPropertyParamMap();

        when(restClient.getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES), eq(clientPropertyParamMap()), any())).thenReturn(fromOccupancySummary);
        when(roomTypeActivityClientAPIService.getUniqueRoomTypeCodes(parameters.get("clientCode"), parameters.get("propertyCode"))).thenReturn(Set.of("RT1", "RT2"));
        when(restClient.getJsonFromEndpoint(RestEndpoints.GET_ALL_GROUP_CODES_FROM_GROUP_BLOCK_MASTER, clientPropertyParamMap(), 0)).thenReturn("[\"GC2\"]");
        when(configService.getParameterValue(PreProductionConfigParamName.GROUP_THROUGH_CLIENT_API)).thenReturn(false);

        MktSegsRateCodesRoomTypesGroupCodesHolder result = ngiDataFetchService.fetchConfigurationData();

        assertThat(result.getMarketSegmentCodes(), Matchers.containsInAnyOrder("MS1", "MS2"));
        assertThat(result.getRoomTypeCodes(), Matchers.containsInAnyOrder("RT1", "RT2"));
        assertThat(result.getRateCodes(), Matchers.containsInAnyOrder("RC1", "RC2"));
        assertThat(result.getGroupCodes(), Matchers.containsInAnyOrder("GC1", "GC2"));
    }

    private MktSegsRateCodesRoomTypesGroupCodesHolder getMktSegsRateCodesRoomTypesGroupCodesHolder() {
        MktSegsRateCodesRoomTypesGroupCodesHolder fromOccupancySummary = new MktSegsRateCodesRoomTypesGroupCodesHolder();
        fromOccupancySummary.setRoomTypeCodes(Collections.singletonList("RT1"));
        fromOccupancySummary.setGroupCodes(Collections.singletonList("GC1"));
        fromOccupancySummary.setMarketSegmentCodes(Arrays.asList("MS1", "MS2"));
        fromOccupancySummary.setRateCodes(Arrays.asList("RC1", "RC2"));
        return fromOccupancySummary;
    }

    @Test
    public void fetchesConfigurationData_NGIDataThroughClientAPI() {
        MktSegsRateCodesRoomTypesGroupCodesHolder fromOccupancySummary = getMktSegsRateCodesRoomTypesGroupCodesHolder();

        when(restClient.getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES), eq(clientPropertyParamMap()), any())).thenReturn(fromOccupancySummary);
        when(configService.getParameterValue(PreProductionConfigParamName.GROUP_THROUGH_CLIENT_API)).thenReturn(true);
        when(groupBlockClientAPIService.getUniqueGroupCodes(anyString(), anyString())).thenReturn("[\"EXTERNAL_CODE\"]");
        when(roomTypeActivityClientAPIService.getUniqueRoomTypeCodes(anyString(), anyString())).thenReturn(Set.of("RT1", "RT2"));

        MktSegsRateCodesRoomTypesGroupCodesHolder result = ngiDataFetchService.fetchConfigurationData();

        assertThat(result.getMarketSegmentCodes(), Matchers.containsInAnyOrder("MS1", "MS2"));
        assertThat(result.getRoomTypeCodes(), Matchers.containsInAnyOrder("RT1", "RT2"));
        assertThat(result.getRateCodes(), Matchers.containsInAnyOrder("RC1", "RC2"));
        assertThat(result.getGroupCodes(), Matchers.containsInAnyOrder("GC1", "EXTERNAL_CODE"));
    }

    private Map<String, String> clientPropertyParamMap() {
        Map<String, String> ngiRequestParams = new HashMap<>();
        ngiRequestParams.put("clientCode", TestClient.BSTN.name());
        ngiRequestParams.put("propertyCode", TestProperty.H1.name());
        return ngiRequestParams;
    }

    @Test
    public void getMktSegsRateCodesRoomTypesGroupCodesHolders() {
        Map<String, String> parameters = clientPropertyParamMap();
        MktSegsRateCodesRoomTypesGroupCodesHolder fromOccupancySummary = getMktSegsRateCodesRoomTypesGroupCodesHolder();
        when(restClient.getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES), eq(clientPropertyParamMap()), any())).thenReturn(fromOccupancySummary);

        MktSegsRateCodesRoomTypesGroupCodesHolder mktSegsRateCodesRoomTypesGroupCodesHolder = ngiDataFetchService.getMktSegsRateCodesRoomTypesGroupCodesHolder(parameters);

        assertEquals(fromOccupancySummary, mktSegsRateCodesRoomTypesGroupCodesHolder);
    }

    @Test
    public void getMktSegsRateCodesRoomTypesGroupCodesHolderUsingV2() {
        Map<String, String> parameters = clientPropertyParamMap();
        MktSegsRateCodesRoomTypesGroupCodesHolder fromOccupancySummary = getMktSegsRateCodesRoomTypesGroupCodesHolder();
        when(configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MULTIPLE_NGI_URL_CONFIGURATIONS)).thenReturn(Boolean.TRUE);
        when(restClient.getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES_V2), eq(clientPropertyParamMap()), any())).thenReturn(fromOccupancySummary);

        MktSegsRateCodesRoomTypesGroupCodesHolder mktSegsRateCodesRoomTypesGroupCodesHolder = ngiDataFetchService.getMktSegsRateCodesRoomTypesGroupCodesHolder(parameters);

        assertEquals(fromOccupancySummary, mktSegsRateCodesRoomTypesGroupCodesHolder);
        verify(restClient).getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES_V2), eq(clientPropertyParamMap()), any());
        verify(restClient, never()).getSingleResultFromEndpoint(eq(RestEndpoints.GET_ALL_MKT_SEGS_RATE_CODES_ROOM_TYPES_AND_GROUP_CODES), eq(clientPropertyParamMap()), any());
    }

}