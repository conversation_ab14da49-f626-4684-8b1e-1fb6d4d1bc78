package com.ideas.tetris.pacman.services.manualrestrictions.control;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionMultiDayDto;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionRoomTypeIdentifier;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionTypeDto;
import com.ideas.tetris.pacman.services.manualrestrictions.control.enums.RestrictionType;
import com.ideas.tetris.pacman.services.manualrestrictions.entities.ManualRestrictionAccomTypeOverride;
import com.ideas.tetris.pacman.services.manualrestrictions.entities.ManualRestrictionPropertyOverride;
import com.ideas.tetris.pacman.services.manualrestrictions.type.AccomTypeManualRestrictionType;
import com.ideas.tetris.pacman.services.manualrestrictions.type.PropertyManualRestrictionType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.job.JobService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.manualrestrictions.entities.ManualRestrictionPropertyOverride.BY_DATERANGE_BETWEEN_AND_RESTRICTION_TYPE;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ManualRestrictionServiceTest {

    @InjectMocks
    private ManualRestrictionService manualRestrictionService;

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private AccommodationService accommodationService;

    @Mock
    private DecisionService decisionService;

    @Mock
    private JobService jobService;

    @BeforeEach
    void setUp() {
        setupWorkContext();
    }

    private void setupWorkContext() {
        WorkContextType wc = new WorkContextType();
        wc.setClientCode(TestClient.HILTON.name());
        wc.setPropertyId(TestProperty.H1.getId());
        PacmanWorkContextHelper.setWorkContext(wc);
    }

    @Test
    void testGetPropertyLevelManualRestrictions() {
        List<ManualRestrictionPropertyOverride> mockPropertyLevelOverridesList = getMockPropertyLevelOverridesList();
        when(tenantCrudService.findAll(ManualRestrictionPropertyOverride.class)).thenReturn(
                mockPropertyLevelOverridesList);
        Map<LocalDate, ManualRestrictionTypeDto> propertyLevelManualRestrictions = manualRestrictionService.getPropertyLevelManualRestrictions();
        assertEquals(1, propertyLevelManualRestrictions.size());
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getStay().getValue(), ManualRestrictionService.CLOSED);
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getArrival().getValue(), ManualRestrictionService.OPEN);
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getMinLos().getValue(), "5");
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getMaxLos().getValue(), "6");
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getMinStay().getValue(), "7");
        assertEquals(propertyLevelManualRestrictions.get(new LocalDate(new Date())).getMaxStay().getValue(), "8");
    }

    @Test
    void testGetAccomLevelManualRestrictions() {
        List<Integer> accomTypeIds = Arrays.asList(1, 2, 3, 4);
        List<ManualRestrictionAccomTypeOverride> mockAccomLevelOverridesList =
                getMockAccomLevelOverrideList(accomTypeIds);
        when(tenantCrudService.findAll(ManualRestrictionAccomTypeOverride.class)).thenReturn(
                mockAccomLevelOverridesList);
        when(accommodationService.getActiveAccomTypesWithValidCapacityAndDisplayStatus()).thenReturn(
                getMockAccomTypeList());
        Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>>
                accomLevelManualRestrictions = manualRestrictionService.getAccomLevelManualRestrictions();
        assertEquals(accomLevelManualRestrictions.size(), accomTypeIds.size());
        LocalDate localDate = new LocalDate();
        for (int i = 1; i <= accomTypeIds.size(); i++) {
            Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto> typeDtoMap =
                    accomLevelManualRestrictions.get(localDate);
            assertEquals(typeDtoMap.size(), 1);
            ManualRestrictionTypeDto manualRestrictionTypeDto =
                    typeDtoMap.get(new ManualRestrictionRoomTypeIdentifier(i, i));
            assertEquals(manualRestrictionTypeDto.getStay().getValue(), ManualRestrictionService.OPEN);
            assertEquals(manualRestrictionTypeDto.getArrival().getValue(), ManualRestrictionService.CLOSED);
            assertEquals(manualRestrictionTypeDto.getMinLos().getValue(), "5");
            assertEquals(manualRestrictionTypeDto.getMaxLos().getValue(), "6");
            assertEquals(manualRestrictionTypeDto.getMinStay().getValue(), "7");
            assertEquals(manualRestrictionTypeDto.getMaxStay().getValue(), "8");
            localDate = localDate.plusDays(i);
        }
    }

    private Map<Integer, Integer> getRoomTypeAndClassMap() {
        Map<Integer, Integer> rtCodeRcRtIdMap = new HashMap<>();
        rtCodeRcRtIdMap.put(1, 2);
        rtCodeRcRtIdMap.put(3, 4);
        rtCodeRcRtIdMap.put(4, 5);
        return rtCodeRcRtIdMap;
    }

    @Test
    void testSaveRestrictions() {
        List<Integer> accomTypeIds = Arrays.asList(1, 2, 3, 4);
        when(decisionService.createManualRestrictionDecision()).thenReturn(getMockDecision());
        manualRestrictionService.saveRestrictions(getMockPropertyLevelRestrictionsDto(),
                getMockAccomLevelRestrictions(accomTypeIds));
        Mockito.verify(tenantCrudService, Mockito.times(4)).save(ArgumentMatchers.anyList());
    }

    @Test
    void testUploadManualRestrictions() {
        manualRestrictionService.uploadManualRestrictions();
        Mockito.verify(decisionService).createManualRestrictionsUploadDecision();
        Mockito.verify(jobService).startJob(ArgumentMatchers.any(), ArgumentMatchers.anyMap());
    }

    @Test
    void testGetRestrictionTypeIdFromRestrictionType() {
        List<Integer> restrictionTypeIds = manualRestrictionService.getRestrictionTypeIdFromRestrictionType(RestrictionType.STAY);
        assertEquals(4, restrictionTypeIds.size());
        assertArrayEquals(new Object[]{1, 2, 9, 10}, restrictionTypeIds.toArray());

        restrictionTypeIds = manualRestrictionService.getRestrictionTypeIdFromRestrictionType(RestrictionType.ARRIVAL);
        assertEquals(4, restrictionTypeIds.size());
        assertArrayEquals(new Object[]{3, 4, 11, 12}, restrictionTypeIds.toArray());

        restrictionTypeIds = manualRestrictionService.getRestrictionTypeIdFromRestrictionType(RestrictionType.MinLOS);
        assertEquals(2, restrictionTypeIds.size());
        assertArrayEquals(new Object[]{5, 13}, restrictionTypeIds.toArray());
    }

    @Test
    void testRemoveRestrictionsForDateAndRestrictionType_RestrictionTypeIdEmpty() {
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));
        List<Integer> toBeRemovedRestrictionTypeIds = Arrays.asList(1, 2, 9, 10, 7, 14);
        Set<Integer> toBeRemovedAccomTypes = new HashSet<>();
        toBeRemovedAccomTypes.add(4);
        toBeRemovedAccomTypes.add(5);
        manualRestrictionService.removeRestrictionsForDateAndRestrictionType(Collections.singletonList(occupancyDate.toDate()), null, toBeRemovedAccomTypes, true, 0);
        Mockito.verify(tenantCrudService, Mockito.times(2)).executeUpdateByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(2)).findByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(2)).save(ArgumentMatchers.anyList());
    }

    @Test
    void testRemoveRestrictionsForDateAndRestrictionType_ExcludePropertyOvr() {
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));
        List<Integer> toBeRemovedRestrictionTypeIds = Arrays.asList(1, 2, 9, 10, 7, 14);
        Set<Integer> toBeRemovedAccomTypes = new HashSet<>();
        toBeRemovedAccomTypes.add(4);
        toBeRemovedAccomTypes.add(5);
        manualRestrictionService.removeRestrictionsForDateAndRestrictionType(Collections.singletonList(occupancyDate.toDate()), null, toBeRemovedAccomTypes, false, getMockDecision().getId());
        Mockito.verify(tenantCrudService, Mockito.times(1)).executeUpdateByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(1)).findByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(1)).save(ArgumentMatchers.anyList());
    }

    @Test
    void testRemoveRestrictionsForDateAndRestrictionType_NoRTSelected() {
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));

        manualRestrictionService.removeRestrictionsForDateAndRestrictionType(Collections.singletonList(occupancyDate.toDate()), null, Collections.EMPTY_SET, true, getMockDecision().getId());
        Mockito.verify(tenantCrudService, Mockito.times(1)).executeUpdateByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(1)).findByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(1)).save(ArgumentMatchers.anyList());
    }

    @Test
    void testRemoveRestrictionsForDateAndRestrictionType_NoRTSelected_ExcludePropertyOvr() {
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));

        manualRestrictionService.removeRestrictionsForDateAndRestrictionType(Collections.singletonList(occupancyDate.toDate()), null, Collections.EMPTY_SET, false, getMockDecision().getId());
        Mockito.verify(tenantCrudService, Mockito.times(0)).executeUpdateByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(0)).findByNamedQuery(ArgumentMatchers.anyString(), ArgumentMatchers.anyMap());
        Mockito.verify(tenantCrudService, Mockito.times(0)).save(ArgumentMatchers.anyList());
    }

    @Test
    void testGetAccomLevelRestrictionsFromMultiDayDto() {
        ManualRestrictionMultiDayDto mockMultiDayDto = getMockMultiDayDto();
        LocalDate occupancyDate = new LocalDate(2020, 8, 25);
        Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomLevelRestrictions = manualRestrictionService.getAccomLevelRestrictionsFromMultiDayDto(mockMultiDayDto);
        assertEquals(9, accomLevelRestrictions.size());
        assertEquals(3, accomLevelRestrictions.get(occupancyDate).size());
        assertTrue(accomLevelRestrictions.get(occupancyDate).containsKey(new ManualRestrictionRoomTypeIdentifier(2, 6)));
    }

    @Test
    void testGetPropertyLevelRestrictionsFromMultiDayDto() {
        ManualRestrictionMultiDayDto mockMultiDayDto = getMockMultiDayDto();
        LocalDate occupancyDate = new LocalDate(2020, 8, 25);
        Map<LocalDate, ManualRestrictionTypeDto> propertyLevelRestrictionsFromMultiDayDto = manualRestrictionService.getPropertyLevelRestrictionsFromMultiDayDto(mockMultiDayDto);
        assertEquals(9, propertyLevelRestrictionsFromMultiDayDto.size());
        assertEquals(PropertyManualRestrictionType.PROPERTY_STAY_OPEN_RESTRICTION.getId(), Integer.parseInt(propertyLevelRestrictionsFromMultiDayDto.get(occupancyDate).getStay().getValue()));
    }

    private ManualRestrictionMultiDayDto getMockMultiDayDto() {
        ManualRestrictionMultiDayDto multiDayDto = new ManualRestrictionMultiDayDto();
        multiDayDto.setSelectedDaysOfWeek(getSelectedDayOfWeek());
        Map<String, ManualRestrictionTypeDto> multiDayRestrictionMap = new HashMap<>();
        multiDayRestrictionMap.put(ManualRestrictionService.PROPERTY, getPropertyRestrictionTypeDto(true));
        setAccomTypeMultiDayRestrictions(multiDayRestrictionMap, "2,6");
        setAccomTypeMultiDayRestrictions(multiDayRestrictionMap, "2,7");
        setAccomTypeMultiDayRestrictions(multiDayRestrictionMap, "3,4");
        multiDayDto.setMultiDayRestrictionsMap(multiDayRestrictionMap);
        multiDayDto.setStartDate(java.time.LocalDate.of(2020, 8, 25));
        multiDayDto.setEndDate(java.time.LocalDate.of(2020, 8, 25).plusDays(14));
        multiDayDto.setSelectedRoomClass(new HashSet<>(Arrays.asList("STD", "STE", "DLX")));
        multiDayDto.setSelectedRoomTypes(new HashSet<>(Arrays.asList("DBL", "STE", "DLX", "Q", "K")));
        return multiDayDto;
    }

    private ManualRestrictionTypeDto getPropertyRestrictionTypeDto(boolean isChanged) {
        ManualRestrictionTypeDto dto = new ManualRestrictionTypeDto();
        Stream.of(PropertyManualRestrictionType.values()).forEach(type -> {
            dto.setRestrictionValueFromRestrictionTypeId(type.getId(), String.valueOf(type.getId()), isChanged, null);
        });
        return dto;
    }

    private void setAccomTypeMultiDayRestrictions(Map<String, ManualRestrictionTypeDto> multiDayRestrictionMap, String roomClassRoomTypeId) {
        multiDayRestrictionMap.put(roomClassRoomTypeId, getAccomRestrictionTypeDto(true));
    }

    private ManualRestrictionTypeDto getAccomRestrictionTypeDto(boolean isChanged) {
        ManualRestrictionTypeDto dto = new ManualRestrictionTypeDto();
        Stream.of(AccomTypeManualRestrictionType.values()).forEach(type -> {
            dto.setRestrictionValueFromRestrictionTypeId(type.getId(), "5", isChanged, null);
        });
        return dto;
    }

    private Set<DayOfWeek> getSelectedDayOfWeek() {
        Set<DayOfWeek> selectedDayOfWeek = new HashSet<>();
        selectedDayOfWeek.add(DayOfWeek.MONDAY);
        selectedDayOfWeek.add(DayOfWeek.TUESDAY);
        selectedDayOfWeek.add(DayOfWeek.THURSDAY);
        selectedDayOfWeek.add(DayOfWeek.SATURDAY);
        return selectedDayOfWeek;
    }

    private List<ManualRestrictionAccomTypeOverride> getMockAccomLevelOverrideList(List<Integer> accomTypeIds) {
        List<ManualRestrictionAccomTypeOverride> accomTypeOverrides = new ArrayList<>();
        int j = 0;
        LocalDate date = new LocalDate();
        for (Integer id : accomTypeIds) {
            date = date.plusDays(j++);
            for (int i = 1; i < 9; i++) {
                if (i == 4) {
                    continue;
                }
                ManualRestrictionAccomTypeOverride accomTypeOverride = getAccomTypeOverrideMockEntity(id, i, i, date);
                accomTypeOverrides.add(accomTypeOverride);
            }
        }
        return accomTypeOverrides;
    }

    private ManualRestrictionAccomTypeOverride getAccomTypeOverrideMockEntity(int accomTypeId, int restrictionTypeId, int losUnit, LocalDate occupancyDate) {
        ManualRestrictionAccomTypeOverride accomTypeOverride = new ManualRestrictionAccomTypeOverride();
        accomTypeOverride.setAccomTypeId(accomTypeId);
        accomTypeOverride.setOccupancyDate(occupancyDate.toDate());
        accomTypeOverride.setLosUnit(losUnit);
        accomTypeOverride.setRestrictionTypeId(restrictionTypeId);
        accomTypeOverride.setIsRestrictionPresent(true);
        accomTypeOverride.setDecisionId(11);
        return accomTypeOverride;
    }

    private List<ManualRestrictionPropertyOverride> getMockPropertyLevelOverridesList() {
        List<ManualRestrictionPropertyOverride> propertyOverrides = new ArrayList<>();
        for (int i = 1; i < 9; i++) {
            if (i == 2) {
                continue;
            }
            ManualRestrictionPropertyOverride propertyOverride = getPropertyOverrideMockEntity(i, i);
            propertyOverrides.add(propertyOverride);
        }
        return propertyOverrides;
    }

    private ManualRestrictionPropertyOverride getPropertyOverrideMockEntity(int restrictionTypeId, int losUnit) {
        ManualRestrictionPropertyOverride propertyOverride = new ManualRestrictionPropertyOverride();
        propertyOverride.setOccupancyDate(new Date());
        propertyOverride.setRestrictionTypeId(restrictionTypeId);
        propertyOverride.setLosUnit(losUnit);
        propertyOverride.setDecisionId(10);
        propertyOverride.setIsRestrictionPresent(true);
        return propertyOverride;
    }

    private AccomType getMockAccomType() {
        AccomClass accomClass = new AccomClass(1, 200, BigDecimal.ONE);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        return accomType;
    }

    private List<AccomType> getMockAccomTypeList() {
        List<AccomType> accomTypes = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            AccomClass accomClass = new AccomClass(i, 200, BigDecimal.ONE);
            AccomType accomType = new AccomType();
            accomType.setId(i);
            accomType.setAccomClass(accomClass);
            accomTypes.add(accomType);
        }
        return accomTypes;
    }

    private Map<LocalDate, ManualRestrictionTypeDto> getMockPropertyLevelRestrictionsDto() {
        Map<LocalDate, ManualRestrictionTypeDto> propertyLevelRestrictions = new HashMap<>();
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));
        for (int i = 0; i < 1; i++) {
            ManualRestrictionTypeDto dto = new ManualRestrictionTypeDto();
            int finalI = i;
            Stream.of(PropertyManualRestrictionType.values()).forEach(type -> dto.setRestrictionValueFromRestrictionTypeId(type.getId(), manualRestrictionService.getEffectiveValue(type.getId(), type.getId() + finalI, true), false, null));
            propertyLevelRestrictions.put(occupancyDate, dto);
            occupancyDate = occupancyDate.plusDays(i);
        }
        return propertyLevelRestrictions;
    }

    private Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> getMockAccomLevelRestrictions(List<Integer> accomTypeIds) {
        Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomLevelRestrictions = new HashMap<>();
        LocalDate occupancyDate = new LocalDate(DateUtil.getDate(30, 6, 2020));
        Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto> identifierMap = new HashMap<>();
        for (int i = 0; i <= accomTypeIds.size(); i++) {
            ManualRestrictionRoomTypeIdentifier identifier = new ManualRestrictionRoomTypeIdentifier(getMockAccomType().getAccomClass().getId(), i);
            ManualRestrictionTypeDto dto = new ManualRestrictionTypeDto();
            int finalI = i;
            Stream.of(AccomTypeManualRestrictionType.values()).forEach(type -> dto.setRestrictionValueFromRestrictionTypeId(type.getId(), manualRestrictionService.getEffectiveValue(type.getId(), type.getId() + finalI, true), false, null));
            identifierMap.putIfAbsent(identifier, dto);
        }
        accomLevelRestrictions.put(occupancyDate, identifierMap);
        return accomLevelRestrictions;
    }

    private Decision getMockDecision() {
        Decision decision = new Decision();
        decision.setId(12);
        return decision;
    }

    @Test
    void getPropertyLevelManualRestrictionsForDateRange() {
        var mockPropertyLevelOverridesList = getMockPropertyLevelOverridesList();
        doReturn(mockPropertyLevelOverridesList).when(tenantCrudService)
                .findByNamedQuery(eq(BY_DATERANGE_BETWEEN_AND_RESTRICTION_TYPE), anyMap());
        var result =
                manualRestrictionService.getPropertyLevelManualRestrictionsForDateRange(LocalDate.now(),
                        LocalDate.now(), RestrictionType.ARRIVAL);
        assertEquals(1, result.size());

    }
}