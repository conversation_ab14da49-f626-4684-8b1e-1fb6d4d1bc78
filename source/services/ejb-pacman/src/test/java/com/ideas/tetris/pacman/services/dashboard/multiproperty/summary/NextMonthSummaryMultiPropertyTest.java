package com.ideas.tetris.pacman.services.dashboard.multiproperty.summary;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dashboard.DashboardBaseTest;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetric2;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.MetricResponse;
import com.ideas.tetris.pacman.services.dashboard.singleproperty.summary.SummaryRequestBuilder;
import com.ideas.tetris.pacman.services.dashboard.type.MetricType2;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * On books don't change within current month when moving around caught up date,
 * since it is all based on the on books activity data (which isn't changing)
 * for these tests.
 * <p>
 * The estimates (forecasts) will change as the caught up date changes.
 */
public class NextMonthSummaryMultiPropertyTest extends DashboardBaseTest {
    private Date startDate = DateUtil.getDateForNextMonth(1);
    private Date endDate = DateUtil.getDateForNextMonth(28);

    private static final BigDecimal ADR_ONBOOKS_EXPECTED = BigDecimal.valueOf(90.00).setScale(2);
    private static final BigDecimal ADR_ESTIMATED_EXPECTED = BigDecimal.valueOf(16.86);
    private static final BigDecimal ADR_ONBOOKS_LAST_YEAR_EXPECTED = BigDecimal.valueOf(90.00).setScale(2);
    private static final BigDecimal ADR_ACTUAL_LAST_YEAR_EXPECTED = BigDecimal.valueOf(90.00).setScale(2);

    private static final BigDecimal REVPAR_ONBOOKS_EXPECTED = BigDecimal.valueOf(68.91);
    private static final BigDecimal REVPAR_ESTIMATED_EXPECTED = BigDecimal.valueOf(15.71);
    private static final BigDecimal REVPAR_ONBOOKS_LAST_YEAR_EXPECTED = BigDecimal.valueOf(68.91);
    private static final BigDecimal REVPAR_ACTUAL_LAST_YEAR_EXPECTED = BigDecimal.valueOf(68.91);

    private static final BigDecimal REVENUE_ONBOOKS_EXPECTED = BigDecimal.valueOf(1417410);
    private static final BigDecimal REVENUE_ESTIMATED_EXPECTED = BigDecimal.valueOf(323189);
    private static final BigDecimal REVENUE_ONBOOKS_LAST_YEAR_EXPECTED = BigDecimal.valueOf(1417410);
    private static final BigDecimal REVENUE_ACTUAL_LAST_YEAR_EXPECTED = BigDecimal.valueOf(1417410);

    private static final BigDecimal OCCUPANCY_ONBOOKS_EXPECTED = BigDecimal.valueOf(15749.0).setScale(1);
    private static final BigDecimal OCCUPANCY_ESTIMATED_EXPECTED = BigDecimal.valueOf(19165.0).setScale(1);
    private static final BigDecimal OCCUPANCY_ONBOOKS_LAST_YEAR_EXPECTED = BigDecimal.valueOf(15749.0).setScale(1);
    private static final BigDecimal OCCUPANCY_ACTUAL_LAST_YEAR_EXPECTED = BigDecimal.valueOf(15749.0).setScale(1);

    private static final BigDecimal OCCUPANCY_PERCENT_ONBOOKS_EXPECTED = BigDecimal.valueOf(76.56).setScale(2);
    private static final BigDecimal OCCUPANCY_PERCENT_ESTIMATED_EXPECTED = BigDecimal.valueOf(93.17);
    private static final BigDecimal OCCUPANCY_PERCENT_ONBOOKS_LAST_YEAR_EXPECTED = BigDecimal.valueOf(76.56).setScale(2);
    private static final BigDecimal OCCUPANCY_PERCENT_ACTUAL_LAST_YEAR_EXPECTED = BigDecimal.valueOf(76.56).setScale(2);

    //Values for variance
    private static final BigDecimal ADR_CURRENT_YEAR_VARIANCE = BigDecimal.valueOf(-81.2667).setScale(4);
    private static final BigDecimal ADR_LAST_YEAR_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal ADR_ONBOOKS_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal ADR_ESTIMATED_VARIANCE = BigDecimal.valueOf(-81.2667);

    private static final BigDecimal REVPAR_CURRENT_YEAR_VARIANCE = BigDecimal.valueOf(-77.2021).setScale(4);
    private static final BigDecimal REVPAR_LAST_YEAR_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal REVPAR_ONBOOKS_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal REVPAR_ESTIMATED_VARIANCE = BigDecimal.valueOf(-77.2021).setScale(4);

    private static final BigDecimal REVENUE_CURRENT_YEAR_VARIANCE = BigDecimal.valueOf(-77.1986).setScale(4);
    private static final BigDecimal REVENUE_LAST_YEAR_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal REVENUE_ONBOOKS_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal REVENUE_ESTIMATED_VARIANCE = BigDecimal.valueOf(-77.1986).setScale(4);

    private static final BigDecimal OCCUPANCY_CURRENT_YEAR_VARIANCE = BigDecimal.valueOf(21.6903).setScale(4);
    private static final BigDecimal OCCUPANCY_LAST_YEAR_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal OCCUPANCY_ONBOOKS_VARIANCE = BigDecimal.valueOf(0.0000).setScale(4);
    private static final BigDecimal OCCUPANCY_ESTIMATED_VARIANCE = BigDecimal.valueOf(21.6903).setScale(4);

    private static final BigDecimal OCCUPANCY_PERCENT_CURRENT_YEAR_VARIANCE = BigDecimal.valueOf(16.61).setScale(2);
    private static final BigDecimal OCCUPANCY_PERCENT_LAST_YEAR_VARIANCE = BigDecimal.valueOf(0.00).setScale(2);
    private static final BigDecimal OCCUPANCY_PERCENT_ONBOOKS_VARIANCE = BigDecimal.valueOf(0.00).setScale(2);
    private static final BigDecimal OCCUPANCY_PERCENT_ESTIMATED_VARIANCE = BigDecimal.valueOf(16.61).setScale(2);


    private static final int PROPERTY_GROUP_ID = 1;
    private static final String PROPERTY_LEVEL = "PROPERTY";

    @BeforeEach
    public void setUp() {
        super.setUp();
        System.setProperty("dashboardCachingEnabled", "true");
        PacmanWorkContextHelper.setPropertyGroupId(PROPERTY_GROUP_ID);
    }

    @Test
    public void testADRSummary() {
        List<MetricRequest> requests = SummaryRequestBuilder.buildRequest(MetricType2.AVERAGE_DAILY_RATE);
        List<BigDecimal> expectedValues = buildADRExpectedValues();
        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testRevPARSummary() {
        List<MetricRequest> requests = SummaryRequestBuilder.buildRequest(MetricType2.REVENUE_PER_AVAIL_ROOM);

        List<BigDecimal> expectedValues = buildRevPARExpectedValues();

        DashboardMetric2 summary = getMetricSummary2(requests);

        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testTotalRevenueSummary() {
        List<MetricRequest> requests = SummaryRequestBuilder.buildRequest(MetricType2.REVENUE);

        List<BigDecimal> expectedValues = buildRevenueExpectedValues();

        DashboardMetric2 summary = getMetricSummary2(requests);

        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testOccupancyValueSummary() {
        List<MetricRequest> requests = SummaryRequestBuilder.buildRequest(MetricType2.OCCUPANCY_FORECAST_VALUE);

        List<BigDecimal> expectedValues = buildOccupancyExpectedValues();

        DashboardMetric2 summary = getMetricSummary2(requests);

        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }


    @Test
    public void testOccupancyPercentSummaryEstimated() {
        List<MetricRequest> requests = SummaryRequestBuilder.buildRequest(MetricType2.OCCUPANCY_FORECAST_PERCENT);

        List<BigDecimal> expectedValues = buildOccupancyPercentExpectedValues();

        DashboardMetric2 summary = getMetricSummary2(requests);

        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

//Newly added test cases for variance calculation

    @Test
    public void testOccupancyPercentSummaryVariance() {

        List<MetricRequest> requests = SummaryRequestBuilder.buildRequestVariance(MetricType2.OCCUPANCY_FORECAST_PERCENT);
        List<BigDecimal> expectedValues = buildOccupancyPercentExpectedVarianceValues();

        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testOccupancyValueSummaryVariance() {

        List<MetricRequest> requests = SummaryRequestBuilder.buildRequestVariance(MetricType2.OCCUPANCY_FORECAST_VALUE);
        List<BigDecimal> expectedValues = buildOccupancyExpectedVarianceValues();

        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testTotalRevenueSummaryVariance() {

        List<MetricRequest> requests = SummaryRequestBuilder.buildRequestVariance(MetricType2.REVENUE);
        List<BigDecimal> expectedValues = buildRevenueExpectedVarianceValues();

        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testRevPARSummaryVariance() {

        List<MetricRequest> requests = SummaryRequestBuilder.buildRequestVariance(MetricType2.REVENUE_PER_AVAIL_ROOM);
        List<BigDecimal> expectedValues = buildRevPARExpectedVarianceValues();

        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testADRSummaryVariance() {

        List<MetricRequest> requests = SummaryRequestBuilder.buildRequestVariance(MetricType2.AVERAGE_DAILY_RATE);
        List<BigDecimal> expectedValues = buildADRExpectedVarianceValues();

        DashboardMetric2 summary = getMetricSummary2(requests);
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }


    @Test
    public void testAll5MetricsSummary() {
        List<MetricRequest> requests = new ArrayList<MetricRequest>();
        requests.addAll(SummaryRequestBuilder.buildRequest(MetricType2.AVERAGE_DAILY_RATE));
        requests.addAll(SummaryRequestBuilder.buildRequest(MetricType2.REVENUE_PER_AVAIL_ROOM));
        requests.addAll(SummaryRequestBuilder.buildRequest(MetricType2.REVENUE));
        requests.addAll(SummaryRequestBuilder.buildRequest(MetricType2.OCCUPANCY_FORECAST_VALUE));
        requests.addAll(SummaryRequestBuilder.buildRequest(MetricType2.OCCUPANCY_FORECAST_PERCENT));

        DashboardMetric2 summary = getMetricSummary2(requests);

        List<BigDecimal> expectedValues = new ArrayList<BigDecimal>();
        expectedValues.addAll(buildADRExpectedValues());
        expectedValues.addAll(buildRevPARExpectedValues());
        expectedValues.addAll(buildRevenueExpectedValues());
        expectedValues.addAll(buildOccupancyExpectedValues());
        expectedValues.addAll(buildOccupancyPercentExpectedValues());
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    @Test
    public void testAll5MetricsSummaryVariance() {
        List<MetricRequest> requests = new ArrayList<MetricRequest>();
        requests.addAll(SummaryRequestBuilder.buildRequestVariance(MetricType2.AVERAGE_DAILY_RATE));
        requests.addAll(SummaryRequestBuilder.buildRequestVariance(MetricType2.REVENUE_PER_AVAIL_ROOM));
        requests.addAll(SummaryRequestBuilder.buildRequestVariance(MetricType2.REVENUE));
        requests.addAll(SummaryRequestBuilder.buildRequestVariance(MetricType2.OCCUPANCY_FORECAST_VALUE));
        requests.addAll(SummaryRequestBuilder.buildRequestVariance(MetricType2.OCCUPANCY_FORECAST_PERCENT));

        DashboardMetric2 summary = getMetricSummary2(requests);

        List<BigDecimal> expectedValues = new ArrayList<BigDecimal>();
        expectedValues.addAll(buildADRExpectedVarianceValues());
        expectedValues.addAll(buildRevPARExpectedVarianceValues());
        expectedValues.addAll(buildRevenueExpectedVarianceValues());
        expectedValues.addAll(buildOccupancyExpectedVarianceValues());
        expectedValues.addAll(buildOccupancyPercentExpectedVarianceValues());
        assertMultipleMetricSummary2(requests, expectedValues, summary);
    }

    private List<BigDecimal> buildADRExpectedValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                ADR_ESTIMATED_EXPECTED,
                ADR_ONBOOKS_EXPECTED,
                ADR_ONBOOKS_LAST_YEAR_EXPECTED,
                ADR_ACTUAL_LAST_YEAR_EXPECTED);
        return expectedValues;
    }

    private List<BigDecimal> buildRevPARExpectedValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                REVPAR_ESTIMATED_EXPECTED,
                REVPAR_ONBOOKS_EXPECTED,
                REVPAR_ONBOOKS_LAST_YEAR_EXPECTED,
                REVPAR_ACTUAL_LAST_YEAR_EXPECTED);
        return expectedValues;
    }

    private List<BigDecimal> buildRevenueExpectedValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                REVENUE_ESTIMATED_EXPECTED,
                REVENUE_ONBOOKS_EXPECTED,
                REVENUE_ONBOOKS_LAST_YEAR_EXPECTED,
                REVENUE_ACTUAL_LAST_YEAR_EXPECTED);
        return expectedValues;
    }

    private List<BigDecimal> buildOccupancyExpectedValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                OCCUPANCY_ESTIMATED_EXPECTED,
                OCCUPANCY_ONBOOKS_EXPECTED,
                OCCUPANCY_ONBOOKS_LAST_YEAR_EXPECTED,
                OCCUPANCY_ACTUAL_LAST_YEAR_EXPECTED);
        return expectedValues;
    }

    private List<BigDecimal> buildOccupancyPercentExpectedValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                OCCUPANCY_PERCENT_ESTIMATED_EXPECTED,
                OCCUPANCY_PERCENT_ONBOOKS_EXPECTED,
                OCCUPANCY_PERCENT_ONBOOKS_LAST_YEAR_EXPECTED,
                OCCUPANCY_PERCENT_ACTUAL_LAST_YEAR_EXPECTED);
        return expectedValues;
    }

//for Variance

    private List<BigDecimal> buildADRExpectedVarianceValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                ADR_CURRENT_YEAR_VARIANCE,
                ADR_LAST_YEAR_VARIANCE,
                ADR_ONBOOKS_VARIANCE,
                ADR_ESTIMATED_VARIANCE);
        return expectedValues;
    }

    private List<BigDecimal> buildRevPARExpectedVarianceValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                REVPAR_CURRENT_YEAR_VARIANCE,
                REVPAR_LAST_YEAR_VARIANCE,
                REVPAR_ONBOOKS_VARIANCE,
                REVPAR_ESTIMATED_VARIANCE);
        return expectedValues;
    }

    private List<BigDecimal> buildRevenueExpectedVarianceValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                REVENUE_CURRENT_YEAR_VARIANCE,
                REVENUE_LAST_YEAR_VARIANCE,
                REVENUE_ONBOOKS_VARIANCE,
                REVENUE_ESTIMATED_VARIANCE);
        return expectedValues;
    }

    private List<BigDecimal> buildOccupancyExpectedVarianceValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                OCCUPANCY_CURRENT_YEAR_VARIANCE,
                OCCUPANCY_LAST_YEAR_VARIANCE,
                OCCUPANCY_ONBOOKS_VARIANCE,
                OCCUPANCY_ESTIMATED_VARIANCE);
        return expectedValues;
    }

    private List<BigDecimal> buildOccupancyPercentExpectedVarianceValues() {
        List<BigDecimal> expectedValues = Arrays.asList(
                OCCUPANCY_PERCENT_CURRENT_YEAR_VARIANCE,
                OCCUPANCY_PERCENT_LAST_YEAR_VARIANCE,
                OCCUPANCY_PERCENT_ONBOOKS_VARIANCE,
                OCCUPANCY_PERCENT_ESTIMATED_VARIANCE);
        return expectedValues;
    }

    private void assertMultipleMetricSummary2(List<MetricRequest> requests,
                                              List<BigDecimal> expectedValues, DashboardMetric2 summary) {
        assertHeaderInfo(requests, summary);

        assertSummaryResponses(requests, expectedValues, summary.getMetricResponses());
    }

    private void assertSummaryResponses(List<MetricRequest> requests, List<BigDecimal> expectedValues, List<MetricResponse> summaryResponses) {
        for (int i = 0; i < expectedValues.size(); i++) {
            MetricResponse summaryResponse = summaryResponses.get(i);
            assertNotNull(summaryResponse, "Value map should not be null");
            assertEquals(1, summaryResponse.getMetricValuesByGroupByType().size(), "Wrong number of entries");
            Map<String, List<?>> values = summaryResponse.getMetricValuesByGroupByType();
            assertEquals(1, values.size(), "Wrong number of values for given response");
            assertTrue(((BigDecimal) values.get(PROPERTY_LEVEL).get(0)).compareTo(BigDecimal.ZERO) >= -100, "Value " + values.get(PROPERTY_LEVEL).get(0) + " incorrect for: " + requests.get(i));
        }
    }

    private void assertHeaderInfo(List<MetricRequest> requests, DashboardMetric2 summary) {
        assertEquals(requests, summary.getMetricRequests());
        assertEquals(startDate, summary.getStartDate());
        assertEquals(endDate, summary.getEndDate());
        assertEquals(caughtUpDate, summary.getCaughtUpDate());
        assertEquals("CAUGHT_UP_DATE_IN_PAST", summary.getCaughtUpDateRelativeToMetrics());
        assertEquals(28, summary.getOccupancyDates().size(), "Wrong number of occupancy dates");
        assertEquals(requests.size(), summary.getMetricResponses().size(), "Wrong number metric reponses");
    }

    private DashboardMetric2 getMetricSummary2(
            List<MetricRequest> metricRequests) {
        return dashboardService.getMetricSummary2(metricRequests, startDate,
                endDate);
    }

}
