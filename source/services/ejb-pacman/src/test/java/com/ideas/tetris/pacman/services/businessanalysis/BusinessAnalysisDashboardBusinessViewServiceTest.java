package com.ideas.tetris.pacman.services.businessanalysis;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.budget.entity.UserForecastData;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDataDetailsDto;
import com.ideas.tetris.pacman.services.businessanalysis.repository.BusinessAnalysisDashboardBusinessViewRepository;
import com.ideas.tetris.pacman.services.dashboard.builder.ActivityBuilder;
import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.comparesEqualTo;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class BusinessAnalysisDashboardBusinessViewServiceTest extends AbstractG3JupiterTest {

    private BusinessAnalysisDashboardBusinessViewService businessAnalysisDashboardBusinessViewService;
    private BusinessAnalysisDashboardBusinessViewRepository businessAnalysisDashboardBusinessViewRepository;
    private BusinessAnalysisDashboardService businessAnalysisDashboardService;
    private AccommodationService accommodationService;
    private PacmanConfigParamsService configService;
    private ActivityBuilder activityBuilder;
    private PropertyGroupService propertyGroupService;
    private PropertyService propertyService;
    private PropertyConfigParamService propertyConfigParamService;
    private DateService dateService;
    private Date startDate;
    private Date endDate;
    private WorkContextType workContext;
    private final Integer propertyId = 5;
    private final int mktSeg5 = 5;
    private static final String TEST_BG_I = "Test BG I";
    private static final String TEST_BG_II = "Test BG II";
    private static final int TEST_BG_I_ROOMS_SOLD = 20;
    private static final int TEST_BG_II_ROOMS_SOLD = 40;
    private static final BigDecimal TEST_BG_I_ROOM_REVENUE = new BigDecimal("200.00");
    private static final BigDecimal TEST_BG_II_ROOM_REVENUE = new BigDecimal("400.00");

    @BeforeEach
    public void setUp() {
        businessAnalysisDashboardBusinessViewService = new BusinessAnalysisDashboardBusinessViewService();
        businessAnalysisDashboardService = new BusinessAnalysisDashboardService();
        businessAnalysisDashboardBusinessViewRepository = new BusinessAnalysisDashboardBusinessViewRepository();
        businessAnalysisDashboardBusinessViewRepository.setCrudService(tenantCrudService());

        accommodationService = new AccommodationService();
        accommodationService.setTenantCrudService(tenantCrudService());
        businessAnalysisDashboardService.setAccommodationService(accommodationService);

        configService = mock(PacmanConfigParamsService.class);
        businessAnalysisDashboardService.setConfigService(configService);
        inject(businessAnalysisDashboardBusinessViewService,"configParamService",configService);

        propertyService = mock(PropertyService.class);
        businessAnalysisDashboardService.setPropertyService(propertyService);

        propertyConfigParamService = mock(PropertyConfigParamService.class);
        businessAnalysisDashboardService.propertyConfigParamService = propertyConfigParamService;

        dateService = mock(DateService.class);
        businessAnalysisDashboardService.setDateService(dateService);
        businessAnalysisDashboardBusinessViewService.setDateService(dateService);

        lenient().when(dateService.getBusinessDate()).thenReturn(DateUtil.getFirstDayOfLastMonth());
        lenient().when(dateService.getAdjustedBusinessDate(anyInt())).thenReturn(DateUtil.getFirstDayOfLastMonth());
        lenient().when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getCurrentDate());

        activityBuilder = new ActivityBuilder();
        activityBuilder.setCrudService(tenantCrudService());
        activityBuilder.setMultiPropertyCrudService(multiPropertyCrudService());
        businessAnalysisDashboardService.setActivityBuilder(activityBuilder);

        propertyGroupService = new PropertyGroupService();
        propertyGroupService.setGlobalCrudService(globalCrudService());
        activityBuilder.setPropertyGroupService(propertyGroupService);

        workContext = new WorkContextType();
        workContext.setClientId(3);
        workContext.setPropertyId(propertyId);
        PacmanThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(28);

        businessAnalysisDashboardBusinessViewService.setBusinessAnalysisDashboardService(businessAnalysisDashboardService);
        businessAnalysisDashboardBusinessViewService.setBusinessAnalysisDashboardBusinessViewRepository(businessAnalysisDashboardBusinessViewRepository);
    }


    @Test
    public void test_getBusinessViewBusinessAnalysisDataDetailDtos() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailDtosbv = businessAnalysisDashboardBusinessViewService.getBusinessViewBusinessAnalysisDataDetailDtos(startDate, endDate, true, true, false, 7, "-1", "1", false);
        assertNotNull(businessAnalysisDataDetailDtosbv);
        assertEquals(1, businessAnalysisDataDetailDtosbv.size());
        assertEquals(new BigDecimal(10896), businessAnalysisDataDetailDtosbv.get(0).getOnBooks());
        assertEquals(new BigDecimal("565740.00").setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailDtosbv.get(0).getRevenue());
    }

    @Test
    public void test_getBusinessViewBusinessAnalysisDataDetailDtosExcludeMktSeg() {
        updateMarketSegmentToSetExcludeCompHouseDataDisplay(1);
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailDtosbv = businessAnalysisDashboardBusinessViewService.getBusinessViewBusinessAnalysisDataDetailDtos(startDate, endDate, true, true, false, 7, "-1", "1", true);
        assertNotNull(businessAnalysisDataDetailDtosbv);
        assertEquals(1, businessAnalysisDataDetailDtosbv.size());
        assertEquals(new BigDecimal(9753), businessAnalysisDataDetailDtosbv.get(0).getOnBooks());
        assertEquals(new BigDecimal("506520.00").setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailDtosbv.get(0).getRevenue());
        updateMarketSegmentToSetExcludeCompHouseDataDisplay(0);
    }

    @Test
    public void test_getBusinessViewByInventoryBusinessAnalysisDataDetailDtos() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        InventoryGroup inventoryGroup = createInventoryGroup();
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        when(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", businessAnalysisDashboardService.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtosListOfInv =
                businessAnalysisDashboardBusinessViewService.getBusinessViewByInventoryBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, false, false, 11, "-1", "1", false, false);
        assertNull(businessAnalysisDataDetailsDtosListOfInv.get(0).getRevenuePickUp());
        assertNull(businessAnalysisDataDetailsDtosListOfInv.get(0).getAdrPickUp());
    }

    @Test
    void test_getBusinessViewByInventoryBusinessAnalysisDataDetailDtos_withDyanmicPace() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        InventoryGroup inventoryGroup = createInventoryGroup();
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        when(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", businessAnalysisDashboardService.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtosListOfInv =
                businessAnalysisDashboardBusinessViewService.getBusinessViewByInventoryBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, false, false, 11, "-1", "1", false, true);
        assertThat(businessAnalysisDataDetailsDtosListOfInv.get(0).getRevenuePickUp(), comparesEqualTo(new BigDecimal(84510.00)));
        assertThat(businessAnalysisDataDetailsDtosListOfInv.get(0).getAdrPickUp(), is(closeTo(new BigDecimal(1.66), new BigDecimal(0.1))));
    }

    @Test
    void getPropertyBusinessAnalysisDataDetailDtosAtRoomClassForYear2019() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdateToPaceMktActivity(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = businessAnalysisDashboardBusinessViewService.getBusinessViewBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 7, "-1", "1", false);
        assertEquals(1, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(50), businessAnalysisDataDetailsDto.getYear2019OnBooks());
        assertEquals(new BigDecimal("1000.00").setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("20.00").setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019AdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertNull(businessAnalysisDataDetailsDto.getYear2019Revpar());
    }

    @Test
    public void test_getBudgetAndMyForecastData() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        final List<BusinessGroup> businessGroups = createBusinessGroupDataForBudgetAndUserForecast();
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailDtosbv = businessAnalysisDashboardBusinessViewService.getBusinessViewBusinessAnalysisDataDetailDtos(startDate, endDate, true, true, false, 7, businessGroups.get(0).getId() + ", " + businessGroups.get(1).getId(), "1", false);
        assertNotNull(businessAnalysisDataDetailDtosbv);
        assertEquals(2, businessAnalysisDataDetailDtosbv.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailDtosbv.get(0);
        BigDecimal adr = TEST_BG_I_ROOM_REVENUE.divide(new BigDecimal(TEST_BG_I_ROOMS_SOLD), RoundingMode.DOWN).setScale(2, RoundingMode.DOWN);
        assertEquals(TEST_BG_I, businessAnalysisDataDetailsDto.getName());
        assertEquals(new BigDecimal("5250"), businessAnalysisDataDetailsDto.getOnBooks());
        assertEquals(new BigDecimal("270900.00"), businessAnalysisDataDetailsDto.getRevenue());
        assertEquals(new BigDecimal(TEST_BG_I_ROOMS_SOLD), businessAnalysisDataDetailsDto.getBudgetRooms());
        assertEquals(TEST_BG_I_ROOM_REVENUE, businessAnalysisDataDetailsDto.getBudgetRevenue());
        assertEquals(adr, businessAnalysisDataDetailsDto.getBudgetAdr());
        assertEquals(new BigDecimal(TEST_BG_I_ROOMS_SOLD), businessAnalysisDataDetailsDto.getUserForecastRooms());
        assertEquals(TEST_BG_I_ROOM_REVENUE, businessAnalysisDataDetailsDto.getUserForecastRevenue());
        assertEquals(adr, businessAnalysisDataDetailsDto.getUserForecastAdr());

        businessAnalysisDataDetailsDto = businessAnalysisDataDetailDtosbv.get(1);
        adr = TEST_BG_II_ROOM_REVENUE.divide(new BigDecimal(TEST_BG_II_ROOMS_SOLD), RoundingMode.DOWN).setScale(2, RoundingMode.DOWN);
        assertEquals(TEST_BG_II, businessAnalysisDataDetailsDto.getName());
        assertEquals(new BigDecimal("5646"), businessAnalysisDataDetailsDto.getOnBooks());
        assertEquals(new BigDecimal("294840.00"), businessAnalysisDataDetailsDto.getRevenue());
        assertEquals(new BigDecimal(TEST_BG_II_ROOMS_SOLD), businessAnalysisDataDetailsDto.getBudgetRooms());
        assertEquals(TEST_BG_II_ROOM_REVENUE, businessAnalysisDataDetailsDto.getBudgetRevenue());
        assertEquals(adr, businessAnalysisDataDetailsDto.getBudgetAdr());
        assertEquals(new BigDecimal(TEST_BG_II_ROOMS_SOLD), businessAnalysisDataDetailsDto.getUserForecastRooms());
        assertEquals(TEST_BG_II_ROOM_REVENUE, businessAnalysisDataDetailsDto.getUserForecastRevenue());
        assertEquals(adr, businessAnalysisDataDetailsDto.getUserForecastAdr());
    }

    private void addOrUpdateToPaceMktActivity(String occupancyDate, String businessDate) {

        int i = tenantCrudService().findByNativeQuerySingleResult("select count(*) from PACE_Mkt_Activity where Occupancy_DT = '" + occupancyDate + "' " +
                "and Business_Day_End_DT = '" + businessDate + "'", null);
        if (i == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Mkt_Activity values(5,'" + occupancyDate + "', GETDATE(), '" + businessDate + "', 1, 50, 30, 20, 0, 0, 1000.00, 200.00, 1200.0, 1, 1, 1, GETDATE(), 0, 0)");
        }
    }

    private void updateMarketSegmentToSetExcludeCompHouseDataDisplay(int isExclude) {
        tenantCrudService().executeUpdateByNativeQuery(" UPDATE Mkt_Seg SET Exclude_CompHouse_Data_Display = " + isExclude + " WHERE Mkt_Seg_ID = " + mktSeg5 + ";");
    }

    private InventoryGroup createInventoryGroup() {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("Test_BusinessView");
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 3);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, 3));
        tenantCrudService().save(inventoryGroupDetails);
        return inventoryGroup;
    }

    private List<BusinessGroup> createBusinessGroupDataForBudgetAndUserForecast() {
        final List<BusinessGroup> businessGroups = createBusinessGroups();
        createBudgetAndUserForecastData(businessGroups);
        return businessGroups;
    }

    private List<BusinessGroup> createBusinessGroups() {
        final List<MktSeg> marketSegments = tenantCrudService().findAll(MktSeg.class);
        marketSegments.removeIf(mktSeg -> mktSeg.getCode().equals("-1"));

        final int size = marketSegments.size();
        int sizeI = size / 2;

        final List<BusinessGroup> businessGroups = new ArrayList<>();

        final BusinessGroup testBgI = new BusinessGroup();
        testBgI.setName(TEST_BG_I);
        testBgI.setDescription(TEST_BG_I);
        testBgI.setRanking(1);
        testBgI.setStatusId(1);
        testBgI.setPropertyId(5);
        testBgI.setCreatedByUserId(11403);
        testBgI.setCreateDate(LocalDateTime.now());
        testBgI.setLastUpdatedByUserId(11403);
        testBgI.setLastUpdatedDate(LocalDateTime.now());
        businessGroups.add(testBgI);

        final BusinessGroup testBgII = new BusinessGroup();
        testBgII.setName(TEST_BG_II);
        testBgII.setDescription(TEST_BG_II);
        testBgII.setRanking(1);
        testBgII.setStatusId(1);
        testBgII.setPropertyId(5);
        testBgII.setCreatedByUserId(11403);
        testBgII.setCreateDate(LocalDateTime.now());
        testBgII.setLastUpdatedByUserId(11403);
        testBgII.setLastUpdatedDate(LocalDateTime.now());
        businessGroups.add(testBgII);

        final Set<MktSegBusinessGroup> marketSegmentBusinessGroupsI = new HashSet<>(sizeI);
        final Set<MktSegBusinessGroup> marketSegmentBusinessGroupsII = new HashSet<>(size - sizeI);

        for (int i = 0; i < sizeI; i++) {
            MktSegBusinessGroup group = new MktSegBusinessGroup();
            group.setBusinessGroup(testBgI);
            group.setMktSeg(marketSegments.get(i));
            group.setRanking(1);
            group.setCreatedByUserId(testBgI.getCreatedByUserId());
            group.setCreateDate(testBgI.getCreateDate());
            group.setLastUpdatedByUserId(testBgI.getLastUpdatedByUserId());
            group.setLastUpdatedDate(testBgI.getLastUpdatedDate());
            marketSegmentBusinessGroupsI.add(group);
        }
        testBgI.setMktSegBusinessGroups(marketSegmentBusinessGroupsI);

        for (int i = sizeI; i < size; i++) {
            MktSegBusinessGroup group = new MktSegBusinessGroup();
            group.setBusinessGroup(testBgII);
            group.setMktSeg(marketSegments.get(i));
            group.setRanking(1);
            group.setCreatedByUserId(testBgII.getCreatedByUserId());
            group.setCreateDate(testBgII.getCreateDate());
            group.setLastUpdatedByUserId(testBgII.getLastUpdatedByUserId());
            group.setLastUpdatedDate(testBgII.getLastUpdatedDate());
            marketSegmentBusinessGroupsII.add(group);
        }
        testBgII.setMktSegBusinessGroups(marketSegmentBusinessGroupsII);

        tenantCrudService().save(marketSegmentBusinessGroupsI);
        tenantCrudService().save(marketSegmentBusinessGroupsII);

        return businessGroups;
    }

    private void createBudgetAndUserForecastData(final List<BusinessGroup> businessGroups) {
        final List<BudgetConfig> budgetConfigs = createBudgetAndUserForecastConfig();

        final BudgetLevel budgetLevel = budgetConfigs.get(1).getBudgetLevel();

        final List<BudgetData> budgetData = new ArrayList<>();

        final BudgetData budgetDataI = new BudgetData();
        budgetDataI.setOccupancyDate(DateUtil.convertJavaUtilDateToLocalDate(startDate));
        budgetDataI.setSegmentID(businessGroups.get(0).getId());
        budgetDataI.setRoomsSold(TEST_BG_I_ROOMS_SOLD);
        budgetDataI.setRoomRevenue(TEST_BG_I_ROOM_REVENUE);

        budgetData.add(budgetDataI);

        final BudgetData budgetDataII = new BudgetData();
        budgetDataII.setOccupancyDate(DateUtil.convertJavaUtilDateToLocalDate(endDate));
        budgetDataII.setSegmentID(businessGroups.get(1).getId());
        budgetDataII.setRoomsSold(TEST_BG_II_ROOMS_SOLD);
        budgetDataII.setRoomRevenue(TEST_BG_II_ROOM_REVENUE);

        budgetData.add(budgetDataII);

        tenantCrudService().save(budgetData);

        final List<UserForecastData> userForecastData = new ArrayList<>();

        final UserForecastData userForecastDataI = new UserForecastData();
        userForecastDataI.setBusinessGroupId(businessGroups.get(0).getId());
        userForecastDataI.setOccupancyDate(DateUtil.convertDateToLocalDate(startDate));
        userForecastDataI.setRoomsSold(TEST_BG_I_ROOMS_SOLD);
        userForecastDataI.setRoomRevenue(TEST_BG_I_ROOM_REVENUE);
        userForecastDataI.setBudgetLevel(budgetLevel);

        userForecastData.add(userForecastDataI);

        final UserForecastData userForecastDataII = new UserForecastData();
        userForecastDataII.setBusinessGroupId(businessGroups.get(1).getId());
        userForecastDataII.setOccupancyDate(DateUtil.convertDateToLocalDate(endDate));
        userForecastDataII.setRoomsSold(TEST_BG_II_ROOMS_SOLD);
        userForecastDataII.setRoomRevenue(TEST_BG_II_ROOM_REVENUE);
        userForecastDataII.setBudgetLevel(budgetLevel);

        userForecastData.add(userForecastDataII);

        tenantCrudService().save(userForecastData);
    }

    private List<BudgetConfig> createBudgetAndUserForecastConfig() {
        final BudgetLevel budgetLevel = tenantCrudService().find(BudgetLevel.class, 2);
        final List<BudgetConfig> budgetConfigs = new ArrayList<>(2);

        final BudgetConfig configI = new BudgetConfig();
        configI.setBudgetLevel(budgetLevel);
        configI.setModuleName("client.budget");
        configI.setBudgetDisplayName("Budget");
        budgetConfigs.add(configI);

        final BudgetConfig configII = new BudgetConfig();
        configII.setBudgetLevel(budgetLevel);
        configII.setModuleName("client.user.forecast");
        configII.setBudgetDisplayName("My Forecast");
        budgetConfigs.add(configII);

        tenantCrudService().save(budgetConfigs);

        return budgetConfigs;
    }
}
