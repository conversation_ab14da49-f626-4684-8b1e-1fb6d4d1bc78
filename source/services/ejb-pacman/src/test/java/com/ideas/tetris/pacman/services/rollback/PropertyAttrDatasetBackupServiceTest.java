package com.ideas.tetris.pacman.services.rollback;

import com.ideas.g3.rule.ExpectedException;
import com.ideas.sas.core.SASClientResponse;
import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

import static com.ideas.tetris.pacman.common.constants.Constants.COPY_FILE;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_QUIETLY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.DESC_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.SRC_PATH;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PropertyAttrDatasetBackupServiceTest {

    private static final Integer PROP_ID = 5;

    @RegisterExtension
    public ExpectedException expectedEx = ExpectedException.none();

    @InjectMocks
    private PropertyAttrDatasetBackupService service;

    @Mock
    protected RollbackHelper rollbackHelper;
    private SASClientResponse sasClientResponse;
    @Spy
    private SASClientService sasClientService = new SASClientService() {
        @Override
        public SASClientResponse executeFileOps(String actionUrl, HashMap<String, String> mapOfPaths) {
            if (actionUrl.equalsIgnoreCase(DELETE_FILE_QUIETLY)) {
                FileUtils.deleteQuietly(new File(mapOfPaths.get(DELETE_PATH)));
            } else if (actionUrl.equalsIgnoreCase(COPY_FILE)) {
                try {
                    FileUtils.copyFileToDirectory(new File(mapOfPaths.get(SRC_PATH)), new File(mapOfPaths.get(DESC_PATH)));
                } catch (IOException e) {
                    throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, "ERROR: Missing Property Attr Dataset" + e.getMessage(), e);
                }
            }
            return sasClientResponse;
        }
    };

    private String testTempFolderPath;
    private File backupFolder;

    @BeforeEach
    public void localSetup() {
        MockitoAnnotations.initMocks(this);
        testTempFolderPath = System.getProperty(Constants.TEMP_DIR_PROPERTY_NAME) + File.separator + "testFolder";
        Mockito.when(rollbackHelper.getAnalyticalDatasetDirectory(PROP_ID)).thenReturn(testTempFolderPath);
        backupFolder = new File(System.getProperty(Constants.TEMP_DIR_PROPERTY_NAME) + File.separator + "backup");
        FileUtils.deleteQuietly(backupFolder);
    }

    @AfterEach
    public void cleanUp() {
        FileUtils.deleteQuietly(backupFolder);
        FileUtils.deleteQuietly(new File(testTempFolderPath));
    }

    @Test
    public void stepShouldFailForMissingDatasetFile() throws Exception {
        expectedEx.expect(TetrisException.class);
        expectedEx.expectMessage("ERROR: Missing Property Attr Dataset");
        Mockito.when(rollbackHelper.getPropertyAttrDatasetBackupFolder(PROP_ID)).thenReturn(backupFolder.getAbsolutePath());
        service.backup(PROP_ID);
    }

    @Test
    public void testBackupNewFeatureDataset() throws Exception {
        createDummyDatasetFile();
        Mockito.when(rollbackHelper.getPropertyAttrDatasetBackupFolder(PROP_ID)).thenReturn(backupFolder.getAbsolutePath());
        String expedtedFilePath = backupFolder.getAbsolutePath() + File.separator + Constants.PROPERTY_ATTR_DATASET_FILENAME;
        File expectedFile = new File(expedtedFilePath);
        service.backup(PROP_ID);
        assertTrue(expectedFile.exists());
    }

    private void createDummyDatasetFile() throws IOException {
        String propertyAttrDatasetBackupPath = testTempFolderPath + File.separator + Constants.PROPERTY_ATTR_DATASET_FILENAME;
        File propertyAttrDatasetBackup = new File(propertyAttrDatasetBackupPath);
        propertyAttrDatasetBackup.getParentFile().mkdirs();
        propertyAttrDatasetBackup.createNewFile();
    }
}