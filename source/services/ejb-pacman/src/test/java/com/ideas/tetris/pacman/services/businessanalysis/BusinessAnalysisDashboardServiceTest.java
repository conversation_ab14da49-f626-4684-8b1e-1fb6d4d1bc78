package com.ideas.tetris.pacman.services.businessanalysis;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARFilterCriteria;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CompetitorInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.businessanalysis.dto.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.componentrooms.enums.PhysicalOrComponent;
import com.ideas.tetris.pacman.services.dashboard.builder.ActivityBuilder;
import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decisiondelivery.LastRoomValueDecisionService;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedClosed;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDefaults;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.pacman.testdatabuilder.BusinessGroupBuilder;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.CrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.MANUAL_RESTRICTION_ENABLED;
import static java.lang.Boolean.TRUE;
import static java.math.RoundingMode.HALF_UP;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SlowDBTest
public class BusinessAnalysisDashboardServiceTest extends AbstractG3JupiterTest {

    private static final String ACCOM_TYPE_DISPLAY_STATUS_IDS = "1";
    private static final Integer PROPERTY_ID_PUNE = 5;
    BusinessAnalysisDashboardService service;
    AccommodationService accommodationService;
    PacmanConfigParamsService configService;
    BarDecisionService barDecisionService;
    ActivityBuilder activityBuilder;
    PropertyGroupService propertyGroupService;
    PropertyService propertyService;
    LastRoomValueDecisionService lastRoomValueDecisionService;
    PricingConfigurationService pricingConfigurationService;
    PropertyConfigParamService propertyConfigParamService;
    ProductManagementService productManagementService;
    CrudService tenantCrudService = tenantCrudService();

    DateService dateService;
    Date startDate;
    Date endDate;

    WorkContextType workContext;
    FileMetadata fileMetaData;

    Integer propertyId = 5;
    String mktSeg = "2,3,4";

    @BeforeEach
    public void setUp() {
        service = new BusinessAnalysisDashboardService();
        service.setCrudService(tenantCrudService());

        accommodationService = new AccommodationService();
        accommodationService.setTenantCrudService(tenantCrudService());
        service.setAccommodationService(accommodationService);

        configService = mock(PacmanConfigParamsService.class);
        service.setConfigService(configService);

        propertyService = mock(PropertyService.class);
        service.setPropertyService(propertyService);

        barDecisionService = mock(BarDecisionService.class);
        service.setBarDecisionService(barDecisionService);

        propertyConfigParamService = mock(PropertyConfigParamService.class);
        service.propertyConfigParamService = propertyConfigParamService;

        activityBuilder = new ActivityBuilder();
        activityBuilder.setCrudService(tenantCrudService());
        activityBuilder.setMultiPropertyCrudService(multiPropertyCrudService());
        service.setActivityBuilder(activityBuilder);

        propertyGroupService = new PropertyGroupService();
        propertyGroupService.setGlobalCrudService(globalCrudService());
        activityBuilder.setPropertyGroupService(propertyGroupService);

        lastRoomValueDecisionService = new LastRoomValueDecisionService();
        lastRoomValueDecisionService.setCrudService(tenantCrudService());
        service.setLastRoomValueDecisionService(lastRoomValueDecisionService);

        workContext = new WorkContextType();
        workContext.setClientId(new Integer(3));
        workContext.setPropertyId(propertyId);
        PacmanThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(28);

        // Update the caught up date for the tests
        FileMetadata fileMetadata = tenantCrudService().findByNamedQuerySingleResult(
                FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_AND_STATUS_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with("propertyId", propertyId).and("recordTypeId", 3).and("processStatus", 13).parameters());
        fileMetadata.setSnapshotDt(DateUtil.getDateForCurrentMonth(23));
        tenantCrudService().save(fileMetadata);
        tenantCrudService().getEntityManager().flush();

        dateService = mock(DateService.class);
        service.setDateService(dateService);

        lenient().when(dateService.getBusinessDate()).thenReturn(DateUtil.getFirstDayOfLastMonth());
        lenient().when(dateService.getAdjustedBusinessDate(anyInt())).thenReturn(DateUtil.getFirstDayOfLastMonth());
        lenient().when(dateService.getCaughtUpDate()).thenReturn(DateUtil.getCurrentDate());

        lenient().when(configService.getParameterValue(eq(IPConfigParamName.BAR_BAR_DECISION))).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);

        pricingConfigurationService = new PricingConfigurationService();
        inject(pricingConfigurationService, "configParamsService", configService);
        lenient().when(configService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        lenient().when(configService.getBooleanParameterValue(MANUAL_RESTRICTION_ENABLED)).thenReturn(true);
        lenient().when(configService.getParameterValue(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN)).thenReturn(false);
        inject(pricingConfigurationService, "tenantCrudService", tenantCrudService());
        service.setPricingConfigurationService(pricingConfigurationService);
        productManagementService = mock(ProductManagementService.class);
        service.setProductManagementService(productManagementService);
        lenient().when(productManagementService.getSystemDefaultProduct()).thenReturn(getSystemDefaultProduct());
    }

    @Test
    public void rateBoundsHandlesClosedRates() {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);
        Date closedStartDate = DateUtil.addDaysToDate(today, -1);
        Date closedEndDate = DateUtil.addDaysToDate(today, 1);

        // given
        clearFloorAndCeilingOverrides(startDate, endDate);
        closeRateForDateRange("LV0", closedStartDate, closedEndDate);
        closeRateForDateRange("LV8", closedStartDate, closedEndDate);
        flushAndClear();

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (!dto.getDate().getTime().before(closedStartDate)
                    && !dto.getDate().getTime().after(closedEndDate)) {
                // rates are closed
                assertEquals("LV1", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV7", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            } else {
                // rates are not closed
                assertEquals("LV0", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV8", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            }
        }
    }

    @Test
    public void rateBoundsHandlesMinLos() {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);

        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());

        // given
        clearFloorAndCeilingOverrides(startDate, endDate);
        when(configService.getParameterValue(eq(IPConfigParamName.BAR_BAR_DECISION))).thenReturn(Constants.BAR_DECISION_VALUE_LOS);

        List<RateUnqualifiedDefaults> defaultsList = tenantCrudService().findByNamedQuery(
                RateUnqualifiedDefaults.BY_ACCOM_CLASS,
                QueryParameter.with("accomClassId", masterClass.getId()).parameters());
        for (RateUnqualifiedDefaults defaults : defaultsList) {
            defaults.setWednesdayMinLos(2.0f);
        }
        flushAndClear();

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (dto.getDayOfWeek().equalsIgnoreCase("wed")) {
                // rates are closed
                assertNull(dto.getUpperBound());
                assertNull(dto.getLowerBound());
            } else {
                // rates are not closed
                assertEquals("LV0", dto.getUpperBoundName());
                assertEquals("LV8", dto.getLowerBoundName());
            }
        }
    }

    @Test
    public void rateBoundsHandlesDayOfWeekUnavailable() {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);
        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());

        // given
        clearFloorAndCeilingOverrides(startDate, endDate);
        List<RateUnqualifiedDefaults> rateUnqualifiedDefaultsList =
                tenantCrudService().findByNamedQuery(RateUnqualifiedDefaults.BY_ACCOM_CLASS,
                        QueryParameter.with("accomClassId", masterClass.getId()).parameters());

        for (RateUnqualifiedDefaults rateUnqualifiedDefaults : rateUnqualifiedDefaultsList) {
            RateUnqualified rateUnqualified = tenantCrudService().find(RateUnqualified.class,
                    rateUnqualifiedDefaults.getRateUnqualifiedAccomClass().getRateUnqualifiedId());

            if (rateUnqualified.getName().equals("LV0") || rateUnqualified.getName().equals("LV8")) {
                rateUnqualifiedDefaults.setTuesdayAvailable(0);
                tenantCrudService().save(rateUnqualifiedDefaults);
            }
        }
        flushAndClear();

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (dto.getDayOfWeek().equalsIgnoreCase("tue")) {
                // rates are closed
                assertEquals("LV1", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV7", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            } else {
                // rates are not closed
                assertEquals("LV0", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV8", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            }
        }
    }

    @Test
    public void rateBoundsHandlesFloorAndCeiling() {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);
        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());

        Integer ceilingId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", "LV2").parameters());
        RateUnqualified ceiling = tenantCrudService().find(RateUnqualified.class, ceilingId);
        Integer floortId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", "LV7").parameters());
        RateUnqualified floor = tenantCrudService().find(RateUnqualified.class, floortId);

        // given
        createBAROutputAndOverride(today, startDate, endDate, masterClass, ceiling, floor);

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (dto.getDate().equals(today)) {
                // floor and ceiling apply
                assertEquals("LV2", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV7", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            } else {
                // no restrictions
                assertEquals("LV0", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV8", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            }
        }
    }

    private void createBAROutputAndOverride(Date today, Date startDate, Date endDate, AccomClass masterClass, RateUnqualified ceiling, RateUnqualified floor) {
        clearFloorAndCeilingOverrides(startDate, endDate);
        QueryParameter params = QueryParameter.with("arrivalDate", today)
                .and("accomClassId", masterClass.getId())
                .and("lengthOfStay", -1);
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());
        barOutput.setFloorRateUnqualified(null);
        barOutput.setOverride(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING);
        barOutput.setFloorRateUnqualified(floor);
        barOutput.setCeilingRateUnqualified(ceiling);
        tenantCrudService().save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_CEILING);
        barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
        barOutputOverride.setNewFloorRateUnqualified(floor);
        barOutputOverride.setNewCeilingRateUnqualified(ceiling);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService().save(barOutputOverride);
        flushAndClear();
    }

    @Test
    public void rateBoundsHandlesFloorCeilingOverrideAboveRateSpectrum() {
        String floorOverrideRatePlan = "LV1";
        String ceilingOverrideRatePlan = "LV0";

        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);
        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());

        Integer ceilingId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", ceilingOverrideRatePlan).parameters());
        RateUnqualified ceiling = tenantCrudService().find(RateUnqualified.class, ceilingId);
        Integer floortId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", floorOverrideRatePlan).parameters());
        RateUnqualified floor = tenantCrudService().find(RateUnqualified.class, floortId);

        updateRatePlansAsUserOverrideOnly(masterClass, floorOverrideRatePlan, ceilingOverrideRatePlan);
        // given
        createBAROutputAndOverride(today, startDate, endDate, masterClass, ceiling, floor);

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (dto.getDate().equals(today)) {
                // floor and ceiling apply
                assertEquals(ceilingOverrideRatePlan, dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals(floorOverrideRatePlan, dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            } else {
                // no restrictions
                assertEquals("LV2", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV8", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            }
        }
    }

    @Test
    public void rateBoundsHandlesFloorCeilingOverrideBelowRateSpectrum() {
        String floorOverrideRatePlan = "LV8";
        String ceilingOverrideRatePlan = "LV7";

        Date today = DateUtil.getCurrentDateWithoutTime();
        Date startDate = DateUtil.addDaysToDate(today, -5);
        Date endDate = DateUtil.addDaysToDate(today, 5);
        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());

        Integer ceilingId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", ceilingOverrideRatePlan).parameters());
        RateUnqualified ceiling = tenantCrudService().find(RateUnqualified.class, ceilingId);
        Integer floortId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", floorOverrideRatePlan).parameters());
        RateUnqualified floor = tenantCrudService().find(RateUnqualified.class, floortId);

        updateRatePlansAsUserOverrideOnly(masterClass, floorOverrideRatePlan, ceilingOverrideRatePlan);
        // given
        createBAROutputAndOverride(today, startDate, endDate, masterClass, ceiling, floor);

        // when
        long start = System.currentTimeMillis();
        List<BusinessAnalysisDailyDataDto> results = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        long end = System.currentTimeMillis();
        System.out.println("Elapsed time for service.getBusinessAnalysisDailyDataDtos(): " + (end - start) + "ms");

        // verify
        assertFalse(results.isEmpty());
        for (BusinessAnalysisDailyDataDto dto : results) {
            if (dto.getDate().equals(today)) {
                // floor and ceiling apply
                assertEquals(ceilingOverrideRatePlan, dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals(floorOverrideRatePlan, dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            } else {
                // no restrictions
                assertEquals("LV0", dto.getUpperBoundName(), "Arrival date: " + dto.getDate().getTime());
                assertEquals("LV6", dto.getLowerBoundName(), "Arrival date: " + dto.getDate().getTime());
            }
        }
    }

    private void updateRatePlansAsUserOverrideOnly(AccomClass masterClass, String floorOverrideRatePlan, String ceilingOverrideRatePlan) {
        List<RateUnqualifiedDefaults> rateUnqualifiedDefaultsList =
                tenantCrudService().findByNamedQuery(RateUnqualifiedDefaults.BY_ACCOM_CLASS,
                        QueryParameter.with("accomClassId", masterClass.getId()).parameters());

        for (RateUnqualifiedDefaults rateUnqualifiedDefaults : rateUnqualifiedDefaultsList) {
            RateUnqualified rateUnqualified = tenantCrudService().find(RateUnqualified.class,
                    rateUnqualifiedDefaults.getRateUnqualifiedAccomClass().getRateUnqualifiedId());

            if (rateUnqualified.getName().equals(ceilingOverrideRatePlan) || rateUnqualified.getName().equals(floorOverrideRatePlan)) {
                rateUnqualifiedDefaults.setUserOverrideOnly(1);
                tenantCrudService().save(rateUnqualifiedDefaults);
            }
        }
    }

    // clears any existing floor and ceiling overrides for a clean slate to test on
    private void clearFloorAndCeilingOverrides(Date startDate, Date endDate) {
        AccomClass masterClass = tenantCrudService().findByNamedQuerySingleResult(
                AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());
        QueryParameter params = QueryParameter.with("startDate", startDate)
                .and("endDate", endDate)
                .and("accomClassId", masterClass.getId())
                .and("lengthOfStay", -1);
        List<DecisionBAROutput> dboList = tenantCrudService().findByNamedQuery(
                DecisionBAROutput.BY_ARRIVALDATERANGE_AND_ACCOMCLASSID_AND_LOS, params.parameters());

        for (DecisionBAROutput barOutput : dboList) {
            if (barOutput.getFloorRateUnqualified() != null) {
                DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
                barOutputOverride.setDecision(barOutput.getDecision());
                barOutputOverride.setPropertyId(barOutput.getPropertyID());
                barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
                barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
                barOutputOverride.setUserId(1);
                barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

                barOutputOverride.setOldOverride(barOutput.getOverride());
                barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

                barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_PENDING);
                barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
                barOutputOverride.setNewFloorRateUnqualified(null);
                barOutputOverride.setNewCeilingRateUnqualified(null);
                barOutputOverride.setCreateDate(new Date());
                tenantCrudService().save(barOutputOverride);

                barOutput.setFloorRateUnqualified(null);
                barOutput.setOverride(Constants.BARDECISIONOVERRIDE_PENDING);
                barOutput.setFloorRateUnqualified(null);
                barOutput.setCeilingRateUnqualified(null);
                tenantCrudService().save(barOutput);

            }
        }
    }

    /**
     * PLEASE NOTE THAT THIS TEST IS EXTREMELY VAGUE. IT ESSENTIALLY TESTS THAT
     * THE SERVICE RETURNS DATA.
     */
    @Test
    public void getBusinessAnalysisDailyDataDtosOneMonthOfData() throws Exception {
        lenient().when(configService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION.value())).thenReturn("false");
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        assertEquals(28, dailyDataDtos.size());
        assertEquals(startDate, dailyDataDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyDataDtos.get(dailyDataDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            assertNotNull(dailyReferenceData.getDate());
            assertNotNull(dailyReferenceData.getAdr());
            assertNotNull(dailyReferenceData.getBarCode());
            assertNotNull(dailyReferenceData.getBarRate());
            assertNotNull(dailyReferenceData.getCapacity());
            assertNotNull(dailyReferenceData.getLrv());
            assertNotNull(dailyReferenceData.getOccupancyForecast());
            assertNotNull(dailyReferenceData.getOnBooks());
            assertNotNull(dailyReferenceData.getOutOfOrder());
            assertNotNull(dailyReferenceData.getOverbookings());
            assertNotNull(dailyReferenceData.getRevpar());
            assertNotNull(dailyReferenceData.getUnconstrainedRemainingDemand());
            assertNotNull(dailyReferenceData.getWash());
            assertNotNull(dailyReferenceData.getDecisionReasonTypeId());
        }
    }

    /**
     * PLEASE NOTE THAT THIS TEST IS EXTREMELY VAGUE. IT ESSENTIALLY TESTS THAT
     * THE SERVICE RETURNS DATA.
     */
    @Test
    public void getBusinessAnalysisDailyIndicatorDtosOneMonthOfData() throws Exception {
        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtos(startDate, endDate);

        assertEquals(28, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyIndicatorDto dailyIndicatorDto : dailyIndicatorDtos) {
            assertNotNull(dailyIndicatorDto.getDate());
            assertNotNull(dailyIndicatorDto.isArrivalDemandOverriden());
            assertNotNull(dailyIndicatorDto.isFloorBAROverriden());
            assertNotNull(dailyIndicatorDto.isHighestBARRestricted());
            assertNotNull(dailyIndicatorDto.isOccupancyDemandOverriden());
            assertNotNull(dailyIndicatorDto.isValueOverbookingOverriden());
            assertNotNull(dailyIndicatorDto.isLimitOverbookingOverriden());
            assertNotNull(dailyIndicatorDto.isSpecialEventInfoOnly() || dailyIndicatorDto.isSpecialEventImpactFCST());
            assertNotNull(dailyIndicatorDto.isException());
            assertNotNull(dailyIndicatorDto.isNotification());
            assertNotNull(dailyIndicatorDto.isUserBAROverriden());
            assertNotNull(dailyIndicatorDto.isWashOverriden());
            assertNotNull(dailyIndicatorDto.isGroupFloorBAROverriden());
            assertNotNull(dailyIndicatorDto.isManualRestrictionsPresent());
        }
    }

    /**
     * PLEASE NOTE THAT THIS TEST IS EXTREMELY VAGUE. IT ESSENTIALLY TESTS THAT
     * THE SERVICE RETURNS DATA .
     */
    @Test
    public void getBARByLOSDto() throws Exception {
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION)).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        Map<Date, Map<Integer, CompetitorInfo>> occupancyDateLengthOfStayCompetitors = new HashMap<Date, Map<Integer, CompetitorInfo>>();

        Map<Integer, CompetitorInfo> losCompetitorInfo = new HashMap<Integer, CompetitorInfo>();
        CompetitorInfo competitorInfo = new CompetitorInfo();
        String competitorName = "Greenbar";
        competitorInfo.setCompetitorNames(new HashSet<String>(Arrays.asList(competitorName)));
        competitorInfo.setCompetitorPrice("169.99");
        losCompetitorInfo.put(1, competitorInfo);
        occupancyDateLengthOfStayCompetitors.put(startDate, losCompetitorInfo);

        when(barDecisionService.getCompetitorInfo(Mockito.anyObject())).thenReturn(occupancyDateLengthOfStayCompetitors);

        BusinessAnalysisBARByLOSDto barByLOSDto = service.getBusinessAnalysisBARByLOSDto(startDate, -1);
        assertNotNull(barByLOSDto);

        assertEquals(competitorName, barByLOSDto.getCompetitor());
        assertEquals(competitorInfo.getCompetitorPrice(), barByLOSDto.getCompetitorRate());
    }

    private Product getSystemDefaultProduct() {
        Product barProduct = new Product();
        barProduct.setId(1);
        barProduct.setSystemDefault(true);
        return barProduct;
    }

    @Test
    public void getDailyOverviewDto() throws Exception {
        BusinessAnalysisOverviewDto hotelDailyOverviewDto = service.getDailyOverviewDto(startDate, -1);

        assertEquals("Hotel", hotelDailyOverviewDto.getName());
        assertNull(hotelDailyOverviewDto.getLrv());
        assertNull(hotelDailyOverviewDto.getRate());
        assertNull(hotelDailyOverviewDto.getRateCode());
        assertEquals(BigDecimal.ZERO, hotelDailyOverviewDto.getOverbooking());
        assertEquals(3, hotelDailyOverviewDto.getChildren().size());

        BusinessAnalysisOverviewDto standardAccomClassDto = hotelDailyOverviewDto.getChildren().get(0);
        assertEquals("STD", standardAccomClassDto.getName());
        assertNull(standardAccomClassDto.getOverbooking());
        assertNull(standardAccomClassDto.getRate());
        assertEquals("LV2", standardAccomClassDto.getRateCode());
        assertEquals(new BigDecimal(30).setScale(2), standardAccomClassDto.getLrv().setScale(2));
        assertEquals(3, standardAccomClassDto.getChildren().size());

        BusinessAnalysisOverviewDto doubleDto = standardAccomClassDto.getChildren().get(0);
        assertEquals("DOUBLE", doubleDto.getName());
        assertEquals(BigDecimal.ONE, doubleDto.getOverbooking());
        assertEquals(new BigDecimal(179).setScale(2), doubleDto.getRate().setScale(2));
        assertEquals("LV2", doubleDto.getRateCode());
        assertNull(doubleDto.getLrv());

        BusinessAnalysisOverviewDto king = standardAccomClassDto.getChildren().get(1);
        assertEquals("KING", king.getName());
        assertEquals(BigDecimal.ONE, king.getOverbooking());

        assertNull(king.getLrv());

        BusinessAnalysisOverviewDto queen = standardAccomClassDto.getChildren().get(2);
        assertEquals("QUEEN", queen.getName());
        assertEquals(BigDecimal.ONE, queen.getOverbooking());
        assertNull(queen.getLrv());

        BusinessAnalysisOverviewDto deluxeAccomClassDto = hotelDailyOverviewDto.getChildren().get(1);
        assertEquals("DLX", deluxeAccomClassDto.getName());
        assertNull(deluxeAccomClassDto.getOverbooking());
        assertNull(deluxeAccomClassDto.getRate());
        assertEquals(new BigDecimal(35).setScale(2), deluxeAccomClassDto.getLrv().setScale(2));
        assertEquals(1, deluxeAccomClassDto.getChildren().size());

        BusinessAnalysisOverviewDto suiteAccomClassDto = hotelDailyOverviewDto.getChildren().get(2);
        assertEquals("STE", suiteAccomClassDto.getName());
        assertNull(suiteAccomClassDto.getOverbooking());
        assertNull(suiteAccomClassDto.getRate());
        assertEquals(new BigDecimal(40).setScale(2), suiteAccomClassDto.getLrv().setScale(2));
        assertEquals(1, suiteAccomClassDto.getChildren().size());
    }

    private Product createTestDataForGetDailyOverviewDtoForProducts() {
        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        AccomType accomType = new AccomType();
        accomType.setId(5);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(4);
        accomType.setAccomClass(accomClass);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(product);
        productAccomType.setAccomType(accomType);
        tenantCrudService().save(productAccomType);

        Integer id = (Integer) tenantCrudService().findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(id, accomType, product, startDate);
        tenantCrudService().save(cpDecisionBAROutput);

        return product;
    }

    @Test
    public void getDailyOverviewDtoForProducts() throws Exception {
        //  when(productManagementService.getSystemDefaultProduct()).thenReturn(getSystemDefaultProduct());
        Product product = createTestDataForGetDailyOverviewDtoForProducts();
        BusinessAnalysisOverviewDto hotelDailyOverviewDto = service.getDailyOverviewDto(startDate, -1, product.getId());

        assertEquals("Hotel", hotelDailyOverviewDto.getName());
        assertNull(hotelDailyOverviewDto.getLrv());
        assertNull(hotelDailyOverviewDto.getRate());
        assertNull(hotelDailyOverviewDto.getRateCode());
        assertEquals(BigDecimal.ZERO, hotelDailyOverviewDto.getOverbooking());
        assertEquals(1, hotelDailyOverviewDto.getChildren().size());

        BusinessAnalysisOverviewDto standardAccomClassDto = hotelDailyOverviewDto.getChildren().get(0);
        assertEquals("STE", standardAccomClassDto.getName());
        assertNull(standardAccomClassDto.getOverbooking());
        assertNull(standardAccomClassDto.getRate());
        assertEquals("LV3", standardAccomClassDto.getRateCode());
        assertEquals(new BigDecimal(40).setScale(2), standardAccomClassDto.getLrv().setScale(2));
        assertEquals(1, standardAccomClassDto.getChildren().size());

        BusinessAnalysisOverviewDto doubleDto = standardAccomClassDto.getChildren().get(0);
        assertEquals("SUITE", doubleDto.getName());
        assertEquals(BigDecimal.ZERO, doubleDto.getOverbooking());
        assertEquals(new BigDecimal(169).setScale(2), doubleDto.getRate().setScale(2));
        assertEquals("LV3", doubleDto.getRateCode());
        assertNull(doubleDto.getLrv());

    }

    @Test
    public void testGetDailyOverviewDtoWithInventoryGroupFilter() throws Exception {
        InventoryGroup uniqueInventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        List<InventoryGroupDetails> groupDetails = new ArrayList<>();
        InventoryGroupDetails inventoryGroupDetails = getInventoryGroupDetails(uniqueInventoryGroup, getAccomClass("STD"));
        uniqueInventoryGroup.setInventoryGroupDetails(groupDetails);
        tenantCrudService().save(inventoryGroupDetails);

        BusinessAnalysisOverviewDto hotelDailyOverviewDto = service.getDailyOverviewDto(startDate, uniqueInventoryGroup.getId());

        assertEquals("Hotel", hotelDailyOverviewDto.getName());
        assertNull(hotelDailyOverviewDto.getLrv());
        assertNull(hotelDailyOverviewDto.getRate());
        assertNull(hotelDailyOverviewDto.getRateCode());
        assertEquals(BigDecimal.ZERO, hotelDailyOverviewDto.getOverbooking());
        assertEquals(1, hotelDailyOverviewDto.getChildren().size());

        BusinessAnalysisOverviewDto standardAccomClassDto = hotelDailyOverviewDto.getChildren().get(0);
        assertEquals("STD", standardAccomClassDto.getName());
        assertNull(standardAccomClassDto.getOverbooking());
        assertNull(standardAccomClassDto.getRate());
        assertEquals("LV2", standardAccomClassDto.getRateCode());
        assertEquals(new BigDecimal(30).setScale(2), standardAccomClassDto.getLrv().setScale(2));
        assertEquals(3, standardAccomClassDto.getChildren().size());

        BusinessAnalysisOverviewDto doubleDto = standardAccomClassDto.getChildren().get(0);
        assertEquals("DOUBLE", doubleDto.getName());
        assertEquals(BigDecimal.ONE, doubleDto.getOverbooking());
        assertEquals(new BigDecimal(179).setScale(2), doubleDto.getRate().setScale(2));
        assertEquals("LV2", doubleDto.getRateCode());
        assertNull(doubleDto.getLrv());

        BusinessAnalysisOverviewDto king = standardAccomClassDto.getChildren().get(1);
        assertEquals("KING", king.getName());
        assertEquals(BigDecimal.ONE, king.getOverbooking());

        assertNull(king.getLrv());

        BusinessAnalysisOverviewDto queen = standardAccomClassDto.getChildren().get(2);
        assertEquals("QUEEN", queen.getName());
        assertEquals(BigDecimal.ONE, queen.getOverbooking());
        assertNull(queen.getLrv());
    }

    private InventoryGroupDetails getInventoryGroupDetails(InventoryGroup uniqueInventoryGroup, AccomClass accomClass) {
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(uniqueInventoryGroup);
        inventoryGroupDetails.setAccomClass(accomClass);
        return inventoryGroupDetails;
    }

    private AccomClass getAccomClass(String accomClassCode) {
        return (AccomClass) tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", accomClassCode)
                .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getDailyOverviewDtoForContinuousPricing() throws Exception {
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        LocalDate start = LocalDate.fromDateFields(startDate);
        tenantCrudService().executeUpdateByNativeQuery("update cp_decision_bar_output set final_BAR='849.99599' where accom_type_id=4");
        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        CPDecisionBAROutput cpDecisionBAROutput = tenantCrudService().findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, start, start, Arrays.asList(accomType)));
        cpDecisionBAROutput.setFinalBAR(BigDecimal.TEN);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        BusinessAnalysisOverviewDto hotelDailyOverviewDto = service.getDailyOverviewDto(startDate, -1);

        assertEquals("Hotel", hotelDailyOverviewDto.getName());
        assertNull(hotelDailyOverviewDto.getLrv());
        assertNull(hotelDailyOverviewDto.getRate());
        assertNull(hotelDailyOverviewDto.getRateCode());
        assertEquals(BigDecimal.ZERO, hotelDailyOverviewDto.getOverbooking());
        assertEquals(3, hotelDailyOverviewDto.getChildren().size());

        BusinessAnalysisOverviewDto deluxeAccomClassDto = hotelDailyOverviewDto.getChildren().get(1);
        assertEquals("DLX", deluxeAccomClassDto.getName());
        assertNull(deluxeAccomClassDto.getOverbooking());
        assertNull(deluxeAccomClassDto.getRate());
        assertEquals(new BigDecimal(35).setScale(2), deluxeAccomClassDto.getLrv().setScale(2));
        assertEquals(1, deluxeAccomClassDto.getChildren().size());

        BusinessAnalysisOverviewDto dlxAccomTypeDto = deluxeAccomClassDto.getChildren().get(0);
        assertEquals("DELUXE", dlxAccomTypeDto.getName());
        assertEquals(BigDecimal.TEN.setScale(2), dlxAccomTypeDto.getRate().setScale(2));
    }

    private BudgetLevel getBusinessViewLevel() {
        return tenantCrudService().findByNamedQuerySingleResult(BudgetLevel.BY_LEVEL,
                QueryParameter.with("budgetLevel", "Business View").parameters());
    }

    private BigInteger addUserForecastConfig() {
        BudgetLevel budgetLevel = getBusinessViewLevel();
        if (null == budgetLevel) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Level values('Business View', GETDATE())");
            budgetLevel = getBusinessViewLevel();
        }
        tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Config values(" + budgetLevel.getId() + ", 'User Forecast', 'User Forecast', 1, GETDATE(), 1, GETDATE())");
        BigInteger budgetConfigId = tenantCrudService().findByNativeQuerySingleResult("select Budget_Config_ID from Budget_Config where Module_Name = :userForecast",
                QueryParameter.with("userForecast", "User Forecast").parameters());
        return budgetConfigId;
    }

    private BigInteger addBudgetBusinessViewData() {
        tenantCrudService().deleteAll(BudgetLevel.class);
        BudgetLevel budgetLevel = getBusinessViewLevel();
        if (null == budgetLevel) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Level values('Business View', GETDATE())");
            budgetLevel = getBusinessViewLevel();
        }
        tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Config values(" + budgetLevel.getId() + ", 'client.budget', 'Budget', 1, GETDATE(), 1, GETDATE())");
        BigInteger budgetConfigId = tenantCrudService().findByNativeQuerySingleResult("select Budget_Config_ID from Budget_Config where Module_Name = :budget",
                QueryParameter.with("budget", "client.budget").parameters());
        return budgetConfigId;
    }

    private void addUserForecastData(Integer businessGroupId, Integer roomSold, BigDecimal revenue, LocalDate occupancyDate, Integer budgetLevelId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into User_Forecast_Data values(" + businessGroupId + ", '" + occupancyDate + "', " + roomSold + ", " + revenue + ", 1, GETDATE(), 1, GETDATE(), " + budgetLevelId + ")");
    }

    private BigInteger addBudgetConfig(String budgetLevelType) {
        tenantCrudService().deleteAll(BudgetLevel.class);
        BudgetLevel budgetLevel = getBudgetLevelBusinessType();
        if (null == budgetLevel) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Level values('" + budgetLevelType + "', GETDATE())");
            budgetLevel = getBudgetLevelBusinessType();
        }
        tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Config values(" + budgetLevel.getId() + ", 'client.budget', 'Budget', 1, GETDATE(), 1, GETDATE())");
        BigInteger budgetConfigId = tenantCrudService().findByNativeQuerySingleResult("select Budget_Config_ID from Budget_Config where Module_Name = :budget",
                QueryParameter.with("budget", "client.budget").parameters());
        return budgetConfigId;
    }

    private BudgetLevel getBudgetLevelBusinessType() {
        return tenantCrudService().findByNamedQuerySingleResult(BudgetLevel.BY_LEVEL,
                QueryParameter.with("budgetLevel", "Business Type").parameters());
    }

    private BudgetLevel getBudgetLevelBusinessView() {
        return tenantCrudService().findByNamedQuerySingleResult(BudgetLevel.BY_LEVEL,
                QueryParameter.with("budgetLevel", "Business View").parameters());
    }

    private void addBudgetData(Integer businessId, Integer roomSold, BigDecimal revenue, LocalDate occupancyDate) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Budget_Data values(" + businessId + ", '" + occupancyDate + "', " + roomSold + ", " + revenue + ", 1, GETDATE(), 1, GETDATE())");
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtHotelIncludesLastYear() throws Exception {
        addBudgetConfig("Business Type");
        addUserForecastConfig();
        Integer budgetLevelId = getBudgetLevelBusinessType().getId();
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));
        BusinessGroup group1 = new BusinessGroupBuilder().withRanking(1).withPropertyId(PacmanWorkContextHelper.getPropertyId()).withDescription("TEST-BG1-DESC").withName("TEST-BG1").buildData();
        tenantCrudService().save(group1);
        addUserForecastData(group1.getId(), 11, new BigDecimal("111"), new LocalDate(startDate), budgetLevelId);
        addUserForecastData(group1.getId(), 22, new BigDecimal("222"), new LocalDate(startDate), budgetLevelId);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, false);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(propertyId, hotelDto.getId());
        assertEquals("Hilton - Pune", hotelDto.getName());
        assertEquals(new BigDecimal(10896.00).setScale(2, RoundingMode.DOWN), hotelDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(11436.00).setScale(2, RoundingMode.DOWN), hotelDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), hotelDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.12).setScale(2, RoundingMode.DOWN), hotelDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(980640.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(733315.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(78.991).setScale(2, RoundingMode.DOWN), hotelDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(59.071).setScale(2, RoundingMode.DOWN), hotelDto.getRevparForecast().setScale(2, RoundingMode.DOWN));
        assertNull(hotelDto.getLastYearSystemDemand());
        assertNull(hotelDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("30.00"), hotelDto.getBudgetRooms());
        assertEquals(new BigDecimal("300.00"), hotelDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), hotelDto.getBudgetAdr());
        assertEquals(new BigDecimal("0.02"), hotelDto.getBudgetRevpar());
        assertEquals(new BigDecimal("33.00"), hotelDto.getUserForecastRooms());
        assertEquals(new BigDecimal("333.00"), hotelDto.getUserForecastRevenue());
        assertEquals(new BigDecimal("10.09"), hotelDto.getUserForecastAdr());
        assertEquals(new BigDecimal("0.03"), hotelDto.getUserForecastRevpar());
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtHotelIncludesLastYearBudgetBusinessViews() throws Exception {
        addBudgetBusinessViewData();
        addUserForecastConfig();
        Integer budgetLevelId = getBudgetLevelBusinessView().getId();
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));
        BusinessGroup group1 = new BusinessGroupBuilder().withRanking(1).withPropertyId(PacmanWorkContextHelper.getPropertyId()).withDescription("TEST-BG1-DESC").withName("TEST-BG1").buildData();
        tenantCrudService().save(group1);
        addUserForecastData(group1.getId(), 11, new BigDecimal("111"), new LocalDate(startDate), budgetLevelId);
        addUserForecastData(group1.getId(), 22, new BigDecimal("222"), new LocalDate(startDate), budgetLevelId);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, false);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(propertyId, hotelDto.getId());
        assertEquals("Hilton - Pune", hotelDto.getName());
        assertEquals(new BigDecimal(10896.00).setScale(2, RoundingMode.DOWN), hotelDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(11436.00).setScale(2, RoundingMode.DOWN), hotelDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), hotelDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.12).setScale(2, RoundingMode.DOWN), hotelDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(980640.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(733315.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(78.991).setScale(2, RoundingMode.DOWN), hotelDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(59.071).setScale(2, RoundingMode.DOWN), hotelDto.getRevparForecast().setScale(2, RoundingMode.DOWN));
        assertNull(hotelDto.getLastYearSystemDemand());
        assertNull(hotelDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("30.00"), hotelDto.getBudgetRooms());
        assertEquals(new BigDecimal("300.00"), hotelDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), hotelDto.getBudgetAdr());
        assertEquals(new BigDecimal("0.02"), hotelDto.getBudgetRevpar());
        assertEquals(new BigDecimal("33.00"), hotelDto.getUserForecastRooms());
        assertEquals(new BigDecimal("333.00"), hotelDto.getUserForecastRevenue());
        assertEquals(new BigDecimal("10.09"), hotelDto.getUserForecastAdr());
        assertEquals(new BigDecimal("0.03"), hotelDto.getUserForecastRevpar());
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtHotelIncludesLastYearWhenDOWAdjustFOrDateFlagIsTrue() throws Exception {
        addBudgetConfig("Business Type");
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, true, false);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(propertyId, hotelDto.getId());
        assertEquals("Hilton - Pune", hotelDto.getName());
        assertEquals(new BigDecimal(10896.00).setScale(2, RoundingMode.DOWN), hotelDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(11436.00).setScale(2, RoundingMode.DOWN), hotelDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), hotelDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.12).setScale(2, RoundingMode.DOWN), hotelDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(980640.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(733315.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(78.991).setScale(2, RoundingMode.DOWN), hotelDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(59.071).setScale(2, RoundingMode.DOWN), hotelDto.getRevparForecast().setScale(2, RoundingMode.DOWN));
        assertNull(hotelDto.getLastYearSystemDemand());
        assertNull(hotelDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("30.00"), hotelDto.getBudgetRooms());
        assertEquals(new BigDecimal("300.00"), hotelDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), hotelDto.getBudgetAdr());
        assertEquals(new BigDecimal("0.02"), hotelDto.getBudgetRevpar());
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtHotelIncludesLastYearWhenDOWAdjustFOrDateFlagIsFalse() throws Exception {
        addBudgetConfig("Business Type");
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, false, false);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(propertyId, hotelDto.getId());
        assertEquals("Hilton - Pune", hotelDto.getName());
        assertEquals(new BigDecimal(10896.00).setScale(2, RoundingMode.DOWN), hotelDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(60.00).setScale(2, RoundingMode.DOWN), hotelDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(11436.00).setScale(2, RoundingMode.DOWN), hotelDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), hotelDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.12).setScale(2, RoundingMode.DOWN), hotelDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(980640.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(733315.00).setScale(2, RoundingMode.DOWN), hotelDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(78.991).setScale(2, RoundingMode.DOWN), hotelDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(59.071).setScale(2, RoundingMode.DOWN), hotelDto.getRevparForecast().setScale(2, RoundingMode.DOWN));
        assertNull(hotelDto.getLastYearSystemDemand());
        assertNull(hotelDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), hotelDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("30.00"), hotelDto.getBudgetRooms());
        assertEquals(new BigDecimal("300.00"), hotelDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), hotelDto.getBudgetAdr());
        assertEquals(new BigDecimal("0.02"), hotelDto.getBudgetRevpar());
    }

    @Test
    public void shouldCalculatePickUpForSevenDaysInPastForPropertyLevelData() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery("update pace_total_activity set Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        assertForPropertLevelDateRangePartiallyInPastAndFuture(pastDays);
        assertForForPropertLevelDateRangeInPast(pastDays);
        assertForForPropertLevelDateRangeInFuture(pastDays);
        assertForForPropertLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays);
        assertForForPropertLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays);
    }

    @Test
    public void shouldCalculatePickUpForSevenDaysInPastForPropertyLevelDataExcludeCompRooms() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery("update pace_total_activity set Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        List<BusinessAnalysisDataDetailsDto> dailyDataDtos = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false);
        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDataDetailsDto> dailyDataDtosWithoutExludedMS = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, true);

        verifyDataDetailsDto(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void shouldGivePickUpAsNullForMissingPacePointForPropertyLevelWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from pace_total_activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, false);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(dto.getOnBooks(), dto.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullForMissingPacePointForForecastGroupLevelWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from Pace_Mkt_Activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, "1", false);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(dto.getOnBooks(), dto.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullForMissingPacePointForBusinessTypesOfForecastGroupLevelWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from Pace_Mkt_Activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 7, "1", false);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(dto.getOnBooks(), dto.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullForMissingPacePointForMarketSegmentsOfBusinessTypesOfForecastGroupLevelWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from Pace_Mkt_Activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 1, 7, "1", false);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(dto.getOnBooks(), dto.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullForRoomClassLevelWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from PACE_Accom_Activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(null, dto.getOnBooksPickUp());
        //WHEN
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = 7 ;");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtoWithComponentRoom =
                service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, PhysicalOrComponent.COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        //THEN
        BusinessAnalysisDataDetailsDto dto1 = businessAnalysisDataDetailsDtoWithComponentRoom.get(0);
        assertEquals(null, dto1.getOnBooksPickUp());
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtoWithBothPhysicalOrComponent =
                service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, PhysicalOrComponent.PHYSICAL_COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        //THEN
        BusinessAnalysisDataDetailsDto dto2 = businessAnalysisDataDetailsDtoWithBothPhysicalOrComponent.get(0);
        assertEquals(null, dto2.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullForMissingPacePointForPropertyLevelAtInventoryGroupWhenThereIsNoActivityDataAvailableForBusinessDayEndDate() {
        //GIVEN
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");
        java.util.Date startDate = DateUtil.getDateForCurrentMonth(1);
        java.util.Date endDate = DateUtil.getDateForCurrentMonth(13);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from PACE_Accom_Activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );
        //WHEN
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto =
                service.getInventoryGroupBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, 7, 1, false);
        //THEN
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(null, dto.getOnBooksPickUp());
    }


    private InventoryGroup getInventoryGroup(int accomClassId, String inv_grp_Name) {
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, accomClassId);
        InventoryGroup inventoryGroup = getInventory(accomClass, inv_grp_Name);
        getInventoryGroupDetails(accomClass, inventoryGroup);
        return inventoryGroup;
    }

    private InventoryGroup getInventory(AccomClass baseAccomClass, String inv_grp_Name) {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName(inv_grp_Name);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        return inventoryGroup;
    }

    private void getInventoryGroupDetails(AccomClass accomClass, InventoryGroup inventoryGroup) {
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(accomClass);
        tenantCrudService().save(inventoryGroupDetails);
    }

    private void assertForForPropertLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(0, service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false));
    }

    private void assertForForPropertLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(150, service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false));
    }

    private void assertForForPropertLevelDateRangeInFuture(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(140, service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false));
    }

    private void assertForForPropertLevelDateRangeInPast(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(0, service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false));
    }

    private void assertForPropertLevelDateRangePartiallyInPastAndFuture(int pastDays) {
        assertPickUp(150, service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false));
    }

    @Test
    public void testRCDataForPseudoRooms() {
        Date date = DateUtil.getCurrentDate();
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6, display_status_id = 4, accom_class_id = 1, accom_type_capacity = 0 where accom_type_id in (6, 7)");//2 of 3 STD RTs
        tenantCrudService().executeUpdateByNativeQuery("update accom_activity set Room_Revenue = 300;");
        tenantCrudService().executeUpdateByNativeQuery("update pace_accom_activity set Room_Revenue = 300;");
        List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtos = service.getRoomClassBusinessAnalysisDataDetailDtos(date, date, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(3, roomClassBusinessAnalysisDataDetailDtos.size());

        BusinessAnalysisDataDetailsDto rcSTD = roomClassBusinessAnalysisDataDetailDtos.get(0);
        assertEquals("STD", rcSTD.getName());
        assertEquals(BigDecimal.valueOf(300), rcSTD.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcSTD.getLastYearRevenue().setScale(0));

        BusinessAnalysisDataDetailsDto rcDLX = roomClassBusinessAnalysisDataDetailDtos.get(1);
        assertEquals("DLX", rcDLX.getName());
        assertEquals(BigDecimal.valueOf(300), rcDLX.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcDLX.getLastYearRevenue().setScale(0));

        BusinessAnalysisDataDetailsDto rcSTE = roomClassBusinessAnalysisDataDetailDtos.get(2);
        assertEquals("STE", rcSTE.getName());
        assertEquals(BigDecimal.valueOf(300), rcSTE.getRevenue().setScale(0).setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcSTE.getLastYearRevenue().setScale(0).setScale(0));

        when(propertyConfigParamService.isIncludePseudoRevenueEnabled()).thenReturn(TRUE); //IncludePseudoRevenue
        List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue = service.getRoomClassBusinessAnalysisDataDetailDtos(date, date, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(4, roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.size());

        BusinessAnalysisDataDetailsDto rcSTDWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(1);
        assertEquals("STD", rcSTDWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(300), rcSTDWithPseudo.getRevenue().setScale(0).setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcSTDWithPseudo.getLastYearRevenue().setScale(0).setScale(0));

        BusinessAnalysisDataDetailsDto rcDLXWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(2);
        assertEquals("DLX", rcDLXWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(300), rcDLXWithPseudo.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcDLXWithPseudo.getLastYearRevenue().setScale(0));

        BusinessAnalysisDataDetailsDto rcSTEWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(3);
        assertEquals("STE", rcSTEWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(300), rcSTEWithPseudo.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rcSTEWithPseudo.getLastYearRevenue().setScale(0));

        BusinessAnalysisDataDetailsDto rcUnassignedWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(0);
        assertEquals("Unassigned", rcUnassignedWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(600), rcUnassignedWithPseudo.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(600), rcUnassignedWithPseudo.getLastYearRevenue().setScale(0));
        assertNull(rcUnassignedWithPseudo.getRevenueForecast());
        assertNull(rcUnassignedWithPseudo.getOccupancyForecast());
        assertNull(rcUnassignedWithPseudo.getAdrForecast());
        assertNull(rcUnassignedWithPseudo.getRevparForecast());
        assertNotNull(rcUnassignedWithPseudo.getLastYearOccupancyForecast());
        assertNotNull(rcUnassignedWithPseudo.getLastYearAdrForecast());
        assertNotNull(rcUnassignedWithPseudo.getLastYearRevenueForecast());
        assertNotNull(rcUnassignedWithPseudo.getLastYearRevparForecast());
    }

    @Test
    public void testRCRTDataForPseudoRooms() {
        Date date = DateUtil.getCurrentDate();
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6, display_status_id = 4, accom_class_id = 1, accom_type_capacity = 0 where accom_type_id in (6, 7)");//2 of 3 STD RTs
        tenantCrudService().executeUpdateByNativeQuery("update accom_activity set Room_Revenue = 300 where accom_type_id in (6, 7);");
        tenantCrudService().executeUpdateByNativeQuery("update pace_accom_activity set Room_Revenue = 300 where accom_type_id in (6, 7);");

        when(propertyConfigParamService.isIncludePseudoRevenueEnabled()).thenReturn(TRUE); //IncludePseudoRevenue
        List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue = service.getRoomClassBusinessAnalysisDataDetailDtos(date, date, true, 1, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(2, roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.size());

        BusinessAnalysisDataDetailsDto rtDOUBLEWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(0);
        assertEquals("DOUBLE", rtDOUBLEWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(300), rtDOUBLEWithPseudo.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rtDOUBLEWithPseudo.getLastYearRevenue().setScale(0));
        assertNull(rtDOUBLEWithPseudo.getRevenueForecast());
        assertNull(rtDOUBLEWithPseudo.getOccupancyForecast());
        assertNull(rtDOUBLEWithPseudo.getAdrForecast());
        assertNull(rtDOUBLEWithPseudo.getRevparForecast());
        assertNotNull(rtDOUBLEWithPseudo.getLastYearOccupancyForecast());
        assertNotNull(rtDOUBLEWithPseudo.getLastYearAdrForecast());
        assertNotNull(rtDOUBLEWithPseudo.getLastYearRevenueForecast());
        assertNotNull(rtDOUBLEWithPseudo.getLastYearRevparForecast());

        BusinessAnalysisDataDetailsDto rtQUEENWithPseudo = roomClassBusinessAnalysisDataDetailDtosWithPseudoRevenue.get(1);
        assertEquals("QUEEN", rtQUEENWithPseudo.getName());
        assertEquals(BigDecimal.valueOf(300), rtQUEENWithPseudo.getRevenue().setScale(0));
        assertEquals(BigDecimal.valueOf(300), rtQUEENWithPseudo.getLastYearRevenue().setScale(0));
        assertNull(rtQUEENWithPseudo.getRevenueForecast());
        assertNull(rtQUEENWithPseudo.getOccupancyForecast());
        assertNull(rtQUEENWithPseudo.getAdrForecast());
        assertNull(rtQUEENWithPseudo.getRevparForecast());
        assertNotNull(rtQUEENWithPseudo.getLastYearOccupancyForecast());
        assertNotNull(rtQUEENWithPseudo.getLastYearAdrForecast());
        assertNotNull(rtQUEENWithPseudo.getLastYearRevenueForecast());
        assertNotNull(rtQUEENWithPseudo.getLastYearRevparForecast());
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForBusinessTypeLevelData() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery("update PACE_Mkt_Activity set Rooms_Sold = Rooms_Sold - 5  where Mkt_Seg_ID = 1 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        assertForBusinessTypeLevelDateRangePartiallyInPastAndFuture(pastDays);
        assertForForBusinessTypeLevelDateRangeInPast(pastDays);
        assertForForBusinessTypeLevelDateRangeInFuture(pastDays);
        assertForForBusinessTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays);
        assertForForBusinessTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays);
    }

    private void assertForForBusinessTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false));
    }

    private void assertForForBusinessTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(75, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false));
    }

    private void assertForForBusinessTypeLevelDateRangeInFuture(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(70, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false));
    }

    private void assertForForBusinessTypeLevelDateRangeInPast(int pastDays) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false));
    }

    private void assertForBusinessTypeLevelDateRangePartiallyInPastAndFuture(int pastDays) {
        assertPickUp(75, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false));
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForBusinessTypeForecastGroupLevelData() throws Exception {
        Integer businessTypeId = new Integer(2);
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;

        tenantCrudService().executeUpdateByNativeQuery("update PACE_Mkt_Activity set Rooms_Sold = Rooms_Sold - 5  where Mkt_Seg_ID = 1 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        assertForForecastGroupLevelDateRangePartiallyInPastAndFuture(pastDays, businessTypeId);
        assertForForForecastGroupLevelDateRangeInPast(pastDays, businessTypeId);
        assertForForForecastGroupLevelDateRangeInFuture(pastDays, businessTypeId);
        assertForForForecastGroupLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, businessTypeId);
        assertForForForecastGroupLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, businessTypeId);
    }

    private void assertForForForecastGroupLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays, Integer businessTypeId) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, pastDays, "1", false));
    }

    private void assertForForForecastGroupLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays, Integer businessTypeId) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(75, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, pastDays, "1", false));
    }

    private void assertForForForecastGroupLevelDateRangeInFuture(int pastDays, Integer businessTypeId) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(70, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, pastDays, "1", false));
    }

    private void assertForForForecastGroupLevelDateRangeInPast(int pastDays, Integer businessTypeId) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, pastDays, "1", false));
    }

    private void assertForForecastGroupLevelDateRangePartiallyInPastAndFuture(int pastDays, Integer businessTypeId) {
        assertPickUp(75, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, pastDays, "1", false));
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForBusinessTypeForecastGroupMarketSegmentLevelData() throws Exception {
        Integer businessTypeId = new Integer(2);
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        int forecastGroupId = 1;
        tenantCrudService().executeUpdateByNativeQuery("update PACE_Mkt_Activity set Rooms_Sold = Rooms_Sold - 1  where Mkt_Seg_ID = 1 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        assertForMarketSegmentLevelDateRangePartiallyInPastAndFuture(pastDays, businessTypeId, forecastGroupId);
        assertForForMarketSegmentLevelDateRangeInPast(pastDays, businessTypeId, forecastGroupId);
        assertForForMarketSegmentLevelDateRangeInFuture(pastDays, businessTypeId, forecastGroupId);
        assertForForMarketSegmentLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, businessTypeId, forecastGroupId);
        assertForForMarketSegmentLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, businessTypeId, forecastGroupId);
    }

    private void assertForForMarketSegmentLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays, Integer businessTypeId, Integer forecastGroupId) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, forecastGroupId, pastDays, "1", false));
    }

    private void assertForForMarketSegmentLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays, Integer businessTypeId, Integer forecastGroupId) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(15, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, forecastGroupId, pastDays, "1", false));
    }

    private void assertForForMarketSegmentLevelDateRangeInFuture(int pastDays, Integer businessTypeId, Integer forecastGroupId) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(14, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, forecastGroupId, pastDays, "1", false));
    }

    private void assertForForMarketSegmentLevelDateRangeInPast(int pastDays, Integer businessTypeId, Integer forecastGroupId) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(0, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, forecastGroupId, pastDays, "1", false));
    }

    private void assertForMarketSegmentLevelDateRangePartiallyInPastAndFuture(int pastDays, Integer businessTypeId, Integer forecastGroupId) {
        assertPickUp(15, service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, forecastGroupId, pastDays, "1", false));
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForRoomClassLevelData() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 7  where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 5  where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = 7 ;");
        PhysicalOrComponent physicalComponentBoth = PhysicalOrComponent.PHYSICAL_COMPONENT;
        assertForRoomClassLevelDateRangePartiallyInPastAndFuture(pastDays, 180, physicalComponentBoth);
        assertForForRoomClassLevelDateRangeInPast(pastDays, 0, physicalComponentBoth);
        assertForForRoomClassLevelDateRangeInFuture(pastDays, 168, physicalComponentBoth);
        assertForForRoomClassLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, 180, physicalComponentBoth);
        assertForForRoomClassLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, 0, physicalComponentBoth);
        PhysicalOrComponent physicalOnly = PhysicalOrComponent.PHYSICAL;
        assertForRoomClassLevelDateRangePartiallyInPastAndFuture(pastDays, 105, physicalOnly);
        assertForForRoomClassLevelDateRangeInPast(pastDays, 0, physicalOnly);
        assertForForRoomClassLevelDateRangeInFuture(pastDays, 98, physicalOnly);
        assertForForRoomClassLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, 105, physicalOnly);
        assertForForRoomClassLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, 0, physicalOnly);
        PhysicalOrComponent componentOnly = PhysicalOrComponent.COMPONENT;
        assertForRoomClassLevelDateRangePartiallyInPastAndFuture(pastDays, 75, componentOnly);
        assertForForRoomClassLevelDateRangeInPast(pastDays, 0, componentOnly);
        assertForForRoomClassLevelDateRangeInFuture(pastDays, 70, componentOnly);
        assertForForRoomClassLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, 75, componentOnly);
        assertForForRoomClassLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, 0, componentOnly);
    }

    private void assertForForRoomClassLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomClassLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomClassLevelDateRangeInFuture(int pastDays, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomClassLevelDateRangeInPast(int pastDays, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForRoomClassLevelDateRangePartiallyInPastAndFuture(int pastDays, int expected, PhysicalOrComponent physicalOrComponent) {
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForRoomClassLevelDataExcludeCompRooms() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 7  where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 5  where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = 7 ;");
        PhysicalOrComponent physicalComponentBoth = PhysicalOrComponent.PHYSICAL_COMPONENT;

        List<BusinessAnalysisDataDetailsDto> dailyDataDtos = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, false, pastDays, physicalComponentBoth.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS, true);

        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDataDetailsDto> dailyDataDtosWithoutExludedMS = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, false, pastDays, physicalComponentBoth.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS, true);

        verifyDataDetailsDto(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        verifyPickupSTLYST2YData(dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForRoomClassRoomTypeLevelData() throws Exception {
        int accomClassId = 2;
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 6  where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 4  where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = 7 ;");
        PhysicalOrComponent physicalComponentBoth = PhysicalOrComponent.PHYSICAL_COMPONENT;
        assertEquals(3, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalComponentBoth.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS).size());
        assertForRoomTypeLevelDateRangePartiallyInPastAndFuture(pastDays, accomClassId, 90, physicalComponentBoth);
        assertForForRoomTypeLevelDateRangeInPast(pastDays, accomClassId, 0, physicalComponentBoth);
        assertForForRoomTypeLevelDateRangeInFuture(pastDays, accomClassId, 84, physicalComponentBoth);
        assertForForRoomTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, accomClassId, 90, physicalComponentBoth);
        assertForForRoomTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, accomClassId, 0, physicalComponentBoth);
        PhysicalOrComponent physicalOnly = PhysicalOrComponent.PHYSICAL;
        assertEquals(2, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOnly.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS).size());
        assertForRoomTypeLevelDateRangePartiallyInPastAndFuture(pastDays, accomClassId, 90, physicalOnly);
        assertForForRoomTypeLevelDateRangeInPast(pastDays, accomClassId, 0, physicalOnly);
        assertForForRoomTypeLevelDateRangeInFuture(pastDays, accomClassId, 84, physicalOnly);
        assertForForRoomTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, accomClassId, 90, physicalOnly);
        assertForForRoomTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, accomClassId, 0, physicalOnly);
        PhysicalOrComponent componentOnly = PhysicalOrComponent.COMPONENT;
        assertEquals(1, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, componentOnly.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS).size());
        assertForRoomTypeLevelDateRangePartiallyInPastAndFuture(pastDays, accomClassId, 60, componentOnly);
        assertForForRoomTypeLevelDateRangeInPast(pastDays, accomClassId, 0, componentOnly);
        assertForForRoomTypeLevelDateRangeInFuture(pastDays, accomClassId, 56, componentOnly);
        assertForForRoomTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(pastDays, accomClassId, 60, componentOnly);
        assertForForRoomTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(pastDays, accomClassId, 0, componentOnly);
    }

    @Test
    public void shouldCalculatePickupChangeForSevenDaysForRoomClassRoomTypeLevelDataExcludeCompRooms() throws Exception {
        int accomClassId = 2;
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        int pastDays = 7;
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 6  where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Rooms_Sold = Rooms_Sold - 4  where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = 7 ;");
        PhysicalOrComponent physicalComponentBoth = PhysicalOrComponent.PHYSICAL_COMPONENT;

        List<BusinessAnalysisDataDetailsDto> dailyDataDtos = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, false, accomClassId, pastDays, physicalComponentBoth.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS, false);
        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDataDetailsDto> dailyDataDtosWithoutExludedMS = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, false, accomClassId, pastDays, physicalComponentBoth.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS, true);

        verifyDataDetailsDto(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        verifyPickupSTLYST2YData(dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);

    }

    private void assertForForRoomTypeLevelStartDateInPastAndEndDateInFutureAsBusinessDayEndDate(int pastDays, Integer accomClassId, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(13);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomTypeLevelStartDateAsBusinessDayEndDateAndEndDateInFuture(int pastDays, Integer accomClassId, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(13);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomTypeLevelDateRangeInFuture(int pastDays, Integer accomClassId, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(15);
        Date endDate = DateUtil.getDateForCurrentMonth(28);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForForRoomTypeLevelDateRangeInPast(int pastDays, Integer accomClassId, int expected, PhysicalOrComponent physicalOrComponent) {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(10);
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertForRoomTypeLevelDateRangePartiallyInPastAndFuture(int pastDays, Integer accomClassId, int expected, PhysicalOrComponent physicalOrComponent) {
        assertPickUp(expected, service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, accomClassId, pastDays, physicalOrComponent.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS));
    }

    private void assertPickUp(int expected, List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto) {
        BusinessAnalysisDataDetailsDto dto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(expected, dto.getOnBooksPickUp().intValue());
    }

    private void assertPickUpRevenue(double expected, BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto) {
        assertEquals(expected, businessAnalysisDataDetailsDto.getRevenuePickUp().doubleValue());
    }

    private void assertPickUpAdr(double expected, BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto) {
        assertEquals(expected, businessAnalysisDataDetailsDto.getAdrPickUp().doubleValue());
    }

    @Test
    public void getForecastGroupBusinessAnalysisDataDetailDtosIncludesLastYear() throws Exception {
        addBudgetConfig("Business Type");
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, "1", false);

        assertEquals(2, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), transientDto.getId());
        assertEquals("Transient", transientDto.getName());
        assertEquals(new BigDecimal(9333.00).setScale(2, RoundingMode.DOWN), transientDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(48.00).setScale(2, RoundingMode.DOWN), transientDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(48.00).setScale(2, RoundingMode.DOWN), transientDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(9813.00).setScale(2, RoundingMode.DOWN), transientDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(51.84).setScale(2, RoundingMode.DOWN), transientDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(38.59).setScale(2, RoundingMode.DOWN), transientDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(483840.00).setScale(2, RoundingMode.DOWN), transientDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(378720.00).setScale(2, RoundingMode.DOWN), transientDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal("20.00"), transientDto.getBudgetRooms());
        assertEquals(new BigDecimal("200.00"), transientDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), transientDto.getBudgetAdr());
        assertNull(transientDto.getBudgetRevpar());

        BusinessAnalysisDataDetailsDto groupDto = businessAnalysisDataDetailsDto.get(1);
        assertEquals(new BigDecimal("10.00"), groupDto.getBudgetRooms());
        assertEquals(new BigDecimal("100.00"), groupDto.getBudgetRevenue());
        assertEquals(new BigDecimal("10.00"), groupDto.getBudgetAdr());
        assertNull(groupDto.getBudgetRevpar());

        // Assert last year's values
        assertNull(transientDto.getLastYearSystemDemand());
        assertNull(transientDto.getLastYearUserDemand());
    }

    @Test
    public void getForecastGroupBusinessAnalysisDataDetailDtosIncludesLastYearBudgetBusinessViews() throws Exception {

        addBudgetBusinessViewData();
        addBudgetData(1, 10, new BigDecimal("100.00"), new LocalDate(startDate));
        addBudgetData(2, 20, new BigDecimal("200.00"), new LocalDate(startDate));

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, "1", false);

        assertEquals(2, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), transientDto.getId());
        assertEquals("Transient", transientDto.getName());
        assertEquals(new BigDecimal(9333.00).setScale(2, RoundingMode.DOWN), transientDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(48.00).setScale(2, RoundingMode.DOWN), transientDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(48.00).setScale(2, RoundingMode.DOWN), transientDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(9813.00).setScale(2, RoundingMode.DOWN), transientDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(51.84).setScale(2, RoundingMode.DOWN), transientDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(38.59).setScale(2, RoundingMode.DOWN), transientDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(483840.00).setScale(2, RoundingMode.DOWN), transientDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(378720.00).setScale(2, RoundingMode.DOWN), transientDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertNull(transientDto.getBudgetRooms());
        assertNull(transientDto.getBudgetRevenue());
        assertNull(transientDto.getBudgetAdr());
        assertNull(transientDto.getBudgetRevpar());

        BusinessAnalysisDataDetailsDto groupDto = businessAnalysisDataDetailsDto.get(1);
        assertNull(groupDto.getBudgetRooms());
        assertNull(groupDto.getBudgetRevenue());
        assertNull(groupDto.getBudgetAdr());
        assertNull(groupDto.getBudgetRevpar());

        // Assert last year's values
        assertNull(transientDto.getLastYearSystemDemand());
        assertNull(transientDto.getLastYearUserDemand());
    }

    @Test
    public void getForecastGroupBusinessAnalysisDataDetailDtosForBusinessTypeIncludesLastYear() throws Exception {
        Integer businessTypeId = new Integer(2);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, 7, "1", false);

        assertEquals(7, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(1), transientDto.getId());
        assertEquals("Complementary", transientDto.getName());
        assertEquals(new BigDecimal(957.00).setScale(2), transientDto.getOnBooks().setScale(2));
        assertEquals(new BigDecimal(1017).setScale(2), transientDto.getOccupancyForecast().setScale(2));
        assertEquals(new BigDecimal(51.35).setScale(2, RoundingMode.DOWN), transientDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(37.74).setScale(2, RoundingMode.DOWN), transientDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(49140.00).setScale(2), transientDto.getRevenue().setScale(2));
        assertEquals(new BigDecimal(38385.00).setScale(2), transientDto.getRevenueForecast().setScale(2));
    }

    @Test
    public void getForecastGroupBusinessAnalysisDataDetailDtosForBusinessTypeAndForecastGroupIncludesLastYear() throws Exception {
        Integer businessTypeId = new Integer(2);
        Integer forecastGroupId = new Integer(3);

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, businessTypeId, forecastGroupId, 7, "1", false);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(6), transientDto.getId());
        assertEquals("RACK", transientDto.getName());
        assertEquals(new BigDecimal(1190.00).setScale(2, RoundingMode.DOWN), transientDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(1250.00).setScale(2), transientDto.getOccupancyForecast().setScale(2));
        assertEquals(new BigDecimal(38.67).setScale(2, RoundingMode.DOWN), transientDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(48335.00).setScale(2), transientDto.getRevenueForecast().setScale(2));

        assertNull(transientDto.getLastYearSystemDemand());
        assertNull(transientDto.getLastYearUserDemand());
    }

    private void validateTestResult(int size, List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto) {
        assertEquals(size, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), standardDto.getId());
        assertEquals("STD", standardDto.getName());

        assertEquals(new BigDecimal(6426.00).setScale(2, RoundingMode.DOWN), standardDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(6696.00).setScale(2, RoundingMode.DOWN), standardDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.0).setScale(2, RoundingMode.DOWN), standardDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(67.601).setScale(2, RoundingMode.DOWN), standardDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(578340.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(452675.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(89.871).setScale(2, RoundingMode.DOWN), standardDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(70.351).setScale(2, RoundingMode.DOWN), standardDto.getRevparForecast().setScale(2, RoundingMode.DOWN));

        assertNull(standardDto.getLastYearSystemDemand());
        assertNull(standardDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        BigDecimal revenue = new BigDecimal(600210.00).setScale(2, RoundingMode.DOWN);
        BigDecimal revenueOfOneNextDatesSet = new BigDecimal(622080.00).setScale(2, RoundingMode.DOWN);
        BigDecimal revenueLY = standardDto.getLastYearRevenue().setScale(2, RoundingMode.DOWN);
        BigDecimal revenueForecastLY = standardDto.getLastYearRevenueForecast().setScale(2, RoundingMode.DOWN);
        assertTrue(objectsAreEqual(revenue, revenueLY) || objectsAreEqual(revenueOfOneNextDatesSet, revenueLY));
        assertTrue(objectsAreEqual(revenue, revenueForecastLY) || objectsAreEqual(revenueOfOneNextDatesSet, revenueForecastLY));
        assertNotNull(standardDto.getViewOrder());
    }

    private boolean objectsAreEqual(Object obj1, Object obj2) {
        if (obj1 == null) {
            return obj2 == null;
        } else {
            return obj1.equals(obj2);
        }
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosIncludesLastYear() throws Exception {
        endDate = DateUtil.getDateForCurrentMonth(27);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        validateTestResult(3, businessAnalysisDataDetailsDto);
    }

    @Test
    public void shouldShowLYdataEvenIfSTLYdataIsNotAvailable() throws Exception {
        Date strDate = DateUtil.getDateForNextMonth(1);
        Date enDate = DateUtil.getDateForNextMonth(5);
        // deleting pace data so that STLY data gets deleted.
        tenantCrudService().executeUpdateByNativeQuery("truncate table PACE_Accom_Activity");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(strDate, enDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertNull(standardDto.getLastYearOnBooks()); // Occupancy On Books STLY
        assertNull(standardDto.getLastYearAdrOnBooks()); // ADR On Books STLY
        assertNull(standardDto.getLastYearRevenue()); // Revenue On Books STLY
        assertNotNull(standardDto.getLastYearOccupancyForecast());  // Occupancy actual LY
        assertNotNull(standardDto.getLastYearAdrForecast()); // ADR actual LY
        assertNotNull(standardDto.getLastYearRevenueForecast()); // Revenue actual LY
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosIncludesLastYear_CR_Physical_Component() throws Exception {
        endDate = DateUtil.getDateForCurrentMonth(27);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL_COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        validateTestResult(3, businessAnalysisDataDetailsDto);
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosIncludesLastYear_CR_Physical() throws Exception {
        endDate = DateUtil.getDateForCurrentMonth(27);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(3, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), standardDto.getId());
        assertEquals("STD", standardDto.getName());

        assertEquals(new BigDecimal(4365.00).setScale(2, RoundingMode.DOWN), standardDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(4545.00).setScale(2, RoundingMode.DOWN), standardDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.0).setScale(2, RoundingMode.DOWN), standardDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(67.70).setScale(2, RoundingMode.DOWN), standardDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(392850.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(307679.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(89.881).setScale(2, RoundingMode.DOWN), standardDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(70.391).setScale(2, RoundingMode.DOWN), standardDto.getRevparForecast().setScale(2, RoundingMode.DOWN));

        assertNull(standardDto.getLastYearSystemDemand());
        assertNull(standardDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        BigDecimal onBooks = new BigDecimal(4527.00).setScale(2, RoundingMode.DOWN);
        BigDecimal onBooksOfOneNextDatesSet = new BigDecimal(4689.00).setScale(2, RoundingMode.DOWN);
        BigDecimal actualOnBooksLY = standardDto.getLastYearOnBooks().setScale(2, RoundingMode.DOWN);
        assertTrue(objectsAreEqual(onBooks, actualOnBooksLY) || objectsAreEqual(onBooksOfOneNextDatesSet, actualOnBooksLY));
        BigDecimal revenue = new BigDecimal(407430.00).setScale(2, RoundingMode.DOWN);
        BigDecimal revenueOfOneNextDatesSet = new BigDecimal(422010.00).setScale(2, RoundingMode.DOWN);
        BigDecimal actualRevenueLY = standardDto.getLastYearRevenue().setScale(2, RoundingMode.DOWN);
        BigDecimal actualRevenueForecastLY = standardDto.getLastYearRevenueForecast().setScale(2, RoundingMode.DOWN);
        assertTrue(objectsAreEqual(revenue, actualRevenueLY) || objectsAreEqual(revenueOfOneNextDatesSet, actualRevenueLY));
        assertTrue(objectsAreEqual(revenue, actualRevenueForecastLY) || objectsAreEqual(revenueOfOneNextDatesSet, actualRevenueForecastLY));

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 4");
        businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        assertEquals(2, businessAnalysisDataDetailsDto.size());
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosIncludesLastYear_CR_component() throws Exception {
        endDate = DateUtil.getDateForCurrentMonth(27);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), standardDto.getId());
        assertEquals("STD", standardDto.getName());

        assertEquals(new BigDecimal(2061.00).setScale(2, RoundingMode.DOWN), standardDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getSystemDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN), standardDto.getUserDemand().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(2151.00).setScale(2, RoundingMode.DOWN), standardDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.0).setScale(2, RoundingMode.DOWN), standardDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(67.411).setScale(2, RoundingMode.DOWN), standardDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(185490.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(144996.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(89.871).setScale(2, RoundingMode.DOWN), standardDto.getRevpar().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(70.251).setScale(2, RoundingMode.DOWN), standardDto.getRevparForecast().setScale(2, RoundingMode.DOWN));

        assertNull(standardDto.getLastYearSystemDemand());
        assertNull(standardDto.getLastYearUserDemand());
        assertEquals(new BigDecimal(90).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
        BigDecimal revenue = new BigDecimal(192780.00).setScale(2, RoundingMode.DOWN);
        BigDecimal revenueOfOneNextDatesSet = new BigDecimal(200070).setScale(2, RoundingMode.DOWN);
        BigDecimal revenueLY = standardDto.getLastYearRevenue().setScale(2, RoundingMode.DOWN);
        BigDecimal revenueForecastLY = standardDto.getLastYearRevenueForecast().setScale(2, RoundingMode.DOWN);
        assertTrue(objectsAreEqual(revenue, revenueLY) || objectsAreEqual(revenueOfOneNextDatesSet, revenueLY));
        assertTrue(objectsAreEqual(revenue, revenueForecastLY) || objectsAreEqual(revenueOfOneNextDatesSet, revenueForecastLY));
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosForAccomClassIncludesLastYear() throws Exception {
        Integer accomClassId = new Integer(2);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        validateResult(3, businessAnalysisDataDetailsDto);
    }

    @Test
    public void shouldGenerateLYForecastDataIfSTLYPacePointIsMissing() {
        Date businessDate = DateCalculator.calculateDateForLastYear(DateUtil.removeTimeFromDate(dateService.getBusinessDate()), true);
        tenantCrudService().executeUpdateByNativeQuery("update PACE_Accom_Activity set Accom_Capacity=0 where Accom_Type_ID=6 and Business_Day_End_DT=:bdeDate"
                , QueryParameter.with("bdeDate", businessDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Accom_Activity where Accom_Type_ID=7 and Business_Day_End_DT=:bdeDate"
                , QueryParameter.with("bdeDate", businessDate).parameters());
        Integer accomClassId = 2;
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);

        assertEquals(3, businessAnalysisDataDetailsDto.size());
        businessAnalysisDataDetailsDto.forEach(dto -> assertNotNull(dto.getLastYearOccupancyForecast()));
    }

    @Test
    public void getRoomTypeLevelDataDetailDtosAllActive_Inactive_RoomTypesAreSelected() throws Exception {
        Integer accomClassId = new Integer(2);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set display_status_id=2 where accom_type_name='DOUBLE'");
        String accomTypeStatusIds = "1,2";
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL.getValue(), accomTypeStatusIds);
        assertEquals(3, businessAnalysisDataDetailsDto.size());
        assertTrue(businessAnalysisDataDetailsDto.stream().anyMatch(at -> at.getName().equalsIgnoreCase("DOUBLE")));
        accomTypeStatusIds = "1";
        businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL.getValue(), accomTypeStatusIds);
        assertEquals(2, businessAnalysisDataDetailsDto.size());
        assertFalse(businessAnalysisDataDetailsDto.stream().anyMatch(at -> at.getName().equalsIgnoreCase("DOUBLE")));
    }

    @Disabled
    @Test
    public void getRoomClassLevelDataDetailDtosAllActive_Inactive_RoomTypesAreSelected() throws Exception {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set display_status_id=2 where accom_type_name='DOUBLE'");
        String accomTypeStatusIds = "1,2";
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), accomTypeStatusIds);
        assertOnBooks(businessAnalysisDataDetailsDto, BigDecimal.valueOf(611100.00).setScale(2), BigDecimal.valueOf(6790));
        assertForecast(businessAnalysisDataDetailsDto, BigDecimal.valueOf(7114.0), BigDecimal.valueOf(64.37), BigDecimal.valueOf(457899.00).setScale(2), BigDecimal.valueOf(7042.0), BigDecimal.valueOf(90.00).setScale(2));
        assertLastYear(businessAnalysisDataDetailsDto, BigDecimal.valueOf(90.00).setScale(2), BigDecimal.valueOf(90.00).setScale(2), BigDecimal.valueOf(7042), BigDecimal.valueOf(633780.00).setScale(2));
        accomTypeStatusIds = "1";
        businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), accomTypeStatusIds);
        assertOnBooks(businessAnalysisDataDetailsDto, BigDecimal.valueOf(414900.00).setScale(2), BigDecimal.valueOf(4610));
        assertForecast(businessAnalysisDataDetailsDto, BigDecimal.valueOf(4826.0), BigDecimal.valueOf(64.48), BigDecimal.valueOf(311164.00).setScale(2), BigDecimal.valueOf(4779.0), BigDecimal.valueOf(90.00).setScale(2));
        assertLastYear(businessAnalysisDataDetailsDto, BigDecimal.valueOf(90.00).setScale(2), BigDecimal.valueOf(90.00).setScale(2), BigDecimal.valueOf(4779), BigDecimal.valueOf(430110.00).setScale(2));
    }

    private void assertForecast(List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtos, BigDecimal occuForecast, BigDecimal adrForecast, BigDecimal revenueForecast, BigDecimal lastYearOccuForecast, BigDecimal lastYearADRForecast) {
        final BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = roomClassBusinessAnalysisDataDetailDtos.get(0);
        assertEquals(occuForecast, businessAnalysisDataDetailsDto.getOccupancyForecast());
        assertEquals(adrForecast, businessAnalysisDataDetailsDto.getAdrForecast());
        assertEquals(revenueForecast, businessAnalysisDataDetailsDto.getRevenueForecast());
        assertEquals(lastYearOccuForecast, businessAnalysisDataDetailsDto.getLastYearOccupancyForecast());
        assertEquals(lastYearADRForecast, businessAnalysisDataDetailsDto.getLastYearAdrForecast());
    }

    private void assertOnBooks(List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtos, BigDecimal revenue, BigDecimal onBooks) {
        final BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = roomClassBusinessAnalysisDataDetailDtos.get(0);
        assertEquals(revenue, businessAnalysisDataDetailsDto.getRevenue());
        assertEquals(onBooks, businessAnalysisDataDetailsDto.getOnBooks());
    }

    private void assertLastYear(List<BusinessAnalysisDataDetailsDto> roomClassBusinessAnalysisDataDetailDtos, BigDecimal lastYearRevPAR, BigDecimal lastYearRevPARForecast, BigDecimal lastYearOnBooks, BigDecimal lastYearRevenue) {
        final BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = roomClassBusinessAnalysisDataDetailDtos.get(0);
        assertEquals(lastYearRevPAR, businessAnalysisDataDetailsDto.getLastYearRevpar());
        assertEquals(lastYearRevPARForecast, businessAnalysisDataDetailsDto.getLastYearRevparForecast());
        assertEquals(lastYearOnBooks, businessAnalysisDataDetailsDto.getLastYearOnBooks());
        assertEquals(lastYearRevenue, businessAnalysisDataDetailsDto.getLastYearRevenue());
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosForAccomClassIncludesLastYear_CR_component() throws Exception {
        Integer accomClassId = new Integer(2);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        validateResult(1, businessAnalysisDataDetailsDto);
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosForAccomClassIncludesLastYear_CR_physical_and_component() throws Exception {
        Integer accomClassId = new Integer(2);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL_COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        validateResult(3, businessAnalysisDataDetailsDto);
    }

    private void validateResult(int size, List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto) {
        assertEquals(size, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(6), standardDto.getId());
        assertEquals("DOUBLE", standardDto.getName());

        assertEquals(new BigDecimal(2180.00).setScale(2, RoundingMode.DOWN), standardDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(2288.00).setScale(2, RoundingMode.DOWN), standardDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.131).setScale(2, RoundingMode.DOWN), standardDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(196200.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(146735.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.001).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
    }

    @Test
    public void getRoomClassBusinessAnalysisDataDetailDtosForAccomClassIncludesLastYear_CR_physical() throws Exception {
        Integer accomClassId = new Integer(2);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 6");
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, accomClassId, 0, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        assertEquals(2, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto standardDto = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(7), standardDto.getId());
        assertEquals("QUEEN", standardDto.getName());

        assertEquals(new BigDecimal(2263.00).setScale(2, RoundingMode.DOWN), standardDto.getOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(2371.00).setScale(2, RoundingMode.DOWN), standardDto.getOccupancyForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(64.361).setScale(2, RoundingMode.DOWN), standardDto.getAdrForecast().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(203670.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(152603.00).setScale(2, RoundingMode.DOWN), standardDto.getRevenueForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(90.00).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90.001).setScale(2, RoundingMode.DOWN), standardDto.getLastYearAdrForecast().setScale(2, RoundingMode.DOWN));
    }


    @Test
    public void convertBigDecimalToLong() throws Exception {
        assertNull(service.convertBigDecimalToLong(null));
        assertEquals(new Long(0), service.convertBigDecimalToLong(BigDecimal.ZERO));
    }

    @Test
    public void testHighestScoreNotification() throws Exception {
        InfoMgrStatusEntity alertStatus = tenantCrudService().find(InfoMgrStatusEntity.class, 1);
        InfoMgrTypeEntity alertType = tenantCrudService().find(InfoMgrTypeEntity.class, 17);

        Calendar cal = Calendar.getInstance();

        Date today = DateUtil.getCurrentDate();
        cal.setTime(today);
        cal.set(Calendar.DAY_OF_MONTH, 1);

        InfoMgrExcepNotifEntity instance = new InfoMgrExcepNotifEntity();

        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        instance.setAlertStatus(alertStatus);
        instance.setSubType(objExceptionSubTypeEntity);
        instance.setAlertType(alertType);
        instance.setStatusId(1);
        instance.setCreatedBy("testing");
        instance.setScore(20);
        instance.setDescription("A test user exception instance");
        instance.setDetails("with some but not many details");
        instance.setPropertyId(workContext.getPropertyId());
        instance.setOccupancyDate(cal.getTime());
        instance.setLastModificationDate(new Date());
        instance = tenantCrudService().save(instance);

        BusinessAnalysisExceptionDto dto = service.getBusinessAnalysisNotificationDto(startDate);

        assertNotNull(dto);
        assertEquals(instance.getId(), dto.getId());
        assertEquals(instance.getDescription(), dto.getDescription());
        assertEquals(instance.getDetails(), dto.getDetail());
        assertEquals(new Integer(instance.getScore()), dto.getScore());
    }

    @Test
    public void testHighestScoreException() throws Exception {
        InfoMgrStatusEntity alertStatus = tenantCrudService().find(InfoMgrStatusEntity.class, 1);
        InfoMgrTypeEntity alertType = tenantCrudService().find(InfoMgrTypeEntity.class, 18);

        Calendar cal = Calendar.getInstance();

        Date today = DateUtil.getCurrentDate();
        cal.setTime(today);
        cal.set(Calendar.DAY_OF_MONTH, 1);

        InfoMgrExcepNotifEntity instance = new InfoMgrExcepNotifEntity();
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        instance.setAlertStatus(alertStatus);
        instance.setSubType(objExceptionSubTypeEntity);
        instance.setAlertType(alertType);
        instance.setStatusId(1);
        instance.setCreatedBy("testing");
        instance.setScore(20);
        instance.setDescription("A test user exception instance");
        instance.setDetails("with some but not many details");
        instance.setPropertyId(workContext.getPropertyId());
        instance.setOccupancyDate(cal.getTime());
        instance.setLastModificationDate(new Date());
        instance = tenantCrudService().save(instance);

        BusinessAnalysisExceptionDto dto = service.getBusinessAnalysisExceptionDto(startDate);

        assertNotNull(dto);
        assertEquals(instance.getId(), dto.getId());
        assertEquals(instance.getDescription(), dto.getDescription());
        assertEquals(instance.getDetails(), dto.getDetail());
        assertEquals(new Integer(instance.getScore()), dto.getScore()); //
    }

    @Test
    public void dontHateTheRateLevels() throws Exception {
        List<String> rateLevels = service.getOrderedRateLevels();
        assertNotNull(rateLevels);
        assertTrue(rateLevels.size() > 0);
    }

    @Test
    public void getBusinessAnalysisSpecialEventDtos() throws Exception {
        List<BusinessAnalysisSpecialEventDto> specialEventDtos = service.getBusinessAnalysisSpecialEventDtos(DateUtil.getDateWithoutTime(27, 2, 2009));
        assertNotNull(specialEventDtos);

        assertEquals(1, specialEventDtos.size());

        assertEquals("Hoteliers Convention", specialEventDtos.get(0).getEventName());
        // Update special event as information only event
        PropertySpecialEvent propertySpecialEvent = tenantCrudService().find(PropertySpecialEvent.class, 3);
        propertySpecialEvent.setImpactOnForcast(3);
        propertySpecialEvent = tenantCrudService().save(propertySpecialEvent);
        tenantCrudService().getEntityManager().flush();
        specialEventDtos = service.getBusinessAnalysisSpecialEventDtos(DateUtil.getDateWithoutTime(27, 2, 2009));
        assertTrue(specialEventDtos.get(0).getInformationUseOnly());
    }

    @ParameterizedTest
    @MethodSource("eventNameProvider")
    public void testGetBusinessAnalysisSpecialEventDtosForEventNameWithInstanceName(String eventInstanceName, String expectedEventNameWithInstanceName) {
        // given
        Date eventStartDate = DateUtil.toDate("2012-12-20");
        Date eventEndDate = DateUtil.toDate("2012-12-27");
        int specialEventId = 7;

        // when
        PropertySpecialEventInstance propertySpecialEventInstance = getSpecialEventInstance(specialEventId, eventStartDate, eventEndDate);
        propertySpecialEventInstance.setEventInstanceName(eventInstanceName);
        tenantCrudService().save(propertySpecialEventInstance);
        List<BusinessAnalysisSpecialEventDto> specialEventDtos = service.getBusinessAnalysisSpecialEventDtos(eventStartDate);

        // then
        assertNotNull(specialEventDtos);
        assertEquals(1, specialEventDtos.size());
        assertEquals(expectedEventNameWithInstanceName, specialEventDtos.get(0).getEventName());
    }

    public static Stream<Arguments> eventNameProvider() {
        return Stream.of(
                arguments(" ", "Boy Scout Convention-Info Only"),
                arguments("Demo Instance", "Boy Scout Convention-Info Only - Demo Instance")
        );
    }

    private PropertySpecialEventInstance getSpecialEventInstance(int specialEventId, Date eventStartDate, Date eventEndDate) {
        List<PropertySpecialEvent> storedPropertySpecialEvents = tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY,
                MapBuilder.with("propertyId", PROPERTY_ID_PUNE).and("specialEventId", specialEventId).get());

        assertEquals(1, storedPropertySpecialEvents.size());
        PropertySpecialEvent storedPropertySpecialEvent = storedPropertySpecialEvents.get(0);

        assertNotNull(storedPropertySpecialEvent.getPropertySpecialEventIntances());
        PropertySpecialEventInstance propertySpecialEventInstance = storedPropertySpecialEvent.getPropertySpecialEventIntances().stream()
                .filter(storedPropertySpecialEventInstance -> storedPropertySpecialEventInstance.getStartDate().equals(eventStartDate) && storedPropertySpecialEventInstance.getEndDate().equals(eventEndDate))
                .findFirst()
                .orElse(null);

        assertNotNull(propertySpecialEventInstance);

        return propertySpecialEventInstance;
    }

    @Test
    public void getBusinessAnalysisRateOfDayDto() throws Exception {
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION)).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        BusinessAnalysisRateOfDayDto rateDayDto = service.getBusinessAnalysisRateOfDayDto(startDate, -1);
        assertEquals("LV2", rateDayDto.getLevel());
    }

    private Product createTestDataForProductAndWebrate() {
        Product product = createTestDataForGetDailyOverviewDtoForProducts();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(2);

        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setId(4);

        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass.setAccomClass(accomClass);
        webrateCompetitorsAccomClass.setWebrateCompetitor(webrateCompetitors);
        webrateCompetitorsAccomClass.setProductID(product.getId());
        webrateCompetitorsAccomClass.setDemandEnabled(1);
        webrateCompetitorsAccomClass.setRankingEnabled(1);
        tenantCrudService().save(webrateCompetitorsAccomClass);

        return product;
    }

    @Test
    public void getBusinessAnalysisRateOfDayDtoRateForProducts() throws Exception {
        when(configService.getParameterValue((IPConfigParamName.BAR_BAR_DECISION))).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        AccomType accomType = new AccomType();
        accomType.setId(5);
        Product product = createTestDataForProductAndWebrate();
        BusinessAnalysisRateOfDayDto rateOfDayDto = service.getBusinessAnalysisRateOfDayDto(startDate, -1, product, accomType);
        assertEquals(BigDecimal.valueOf(10), rateOfDayDto.getRate());
    }

    @Test
    void getBusinessAnalysisRateOfDayDtoRateForPrimaryProduct() {
        when(configService.getParameterValue((IPConfigParamName.BAR_BAR_DECISION))).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        final AccomClass masterClass = accommodationService.findMasterClass(propertyId);
        tenantCrudService.deleteAll(CPDecisionBAROutput.class);
        AccomType accomType = getAccomType(4, "DLX");
        final Product barProduct = getSystemDefaultProduct();
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(1, accomType, barProduct, LocalDate.now().toDate());
        tenantCrudService.save(cpDecisionBAROutput);

        final Map<Date, Map<Integer, CompetitorInfo>> competitorInfoMap = prepareCompetitorInfoMap(LocalDate.now().toDate(), "150", "A");
        when(barDecisionService.getCompetitorInfo(any(BARFilterCriteria.class))).thenReturn(competitorInfoMap);

        BusinessAnalysisRateOfDayDto rateOfDayDto = service.getBusinessAnalysisRateOfDayDto(LocalDate.now().toDate(), -1, barProduct, null);
        assertEquals(BigDecimal.TEN, rateOfDayDto.getRate());
        assertEquals("150", rateOfDayDto.getCompetitorRate());

        ArgumentCaptor<BARFilterCriteria> barFilterCriteriaArgumentCaptor = ArgumentCaptor.forClass(BARFilterCriteria.class);
        verify(barDecisionService).getCompetitorInfo(barFilterCriteriaArgumentCaptor.capture());
        final BARFilterCriteria barFilterCriteria = barFilterCriteriaArgumentCaptor.getValue();
        assertEquals(masterClass.getId(), barFilterCriteria.getAccomClassId());
        assertEquals(barProduct.getId(), barFilterCriteria.getSelectedProductId());
    }

    @Test
    void getBusinessAnalysisRateOfDayDtoRateForIndependentProduct() {
        when(configService.getParameterValue((IPConfigParamName.BAR_BAR_DECISION))).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        final AccomClass masterClass = accommodationService.findMasterClass(propertyId);
        tenantCrudService.deleteAll(CPDecisionBAROutput.class);
        AccomType accomType = getAccomType(6, "DBL");
        AccomClass accomClass = getAccomClass(2);
        accomType.setAccomClass(accomClass);
        final Product indProduct = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("IND1"));
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(1, accomType, indProduct, LocalDate.now().toDate());
        tenantCrudService.save(cpDecisionBAROutput);

        final Map<Date, Map<Integer, CompetitorInfo>> competitorInfoMap = prepareCompetitorInfoMap(LocalDate.now().toDate(), "150", "A");
        when(barDecisionService.getCompetitorInfo(any(BARFilterCriteria.class))).thenReturn(competitorInfoMap);

        BusinessAnalysisRateOfDayDto rateOfDayDto = service.getBusinessAnalysisRateOfDayDto(LocalDate.now().toDate(), -1, indProduct, accomType);
        assertEquals(BigDecimal.TEN, rateOfDayDto.getRate());
        assertEquals("150", rateOfDayDto.getCompetitorRate());

        ArgumentCaptor<BARFilterCriteria> barFilterCriteriaArgumentCaptor = ArgumentCaptor.forClass(BARFilterCriteria.class);
        verify(barDecisionService).getCompetitorInfo(barFilterCriteriaArgumentCaptor.capture());
        final BARFilterCriteria barFilterCriteria = barFilterCriteriaArgumentCaptor.getValue();
        assertNotEquals(masterClass.getId(), barFilterCriteria.getAccomClassId());
        assertEquals(accomClass.getId(), barFilterCriteria.getAccomClassId());
        assertEquals(indProduct.getId(), barFilterCriteria.getSelectedProductId());
    }

    private Map<Date, Map<Integer, CompetitorInfo>> prepareCompetitorInfoMap(Date occupancyDate, String competitorPrice, String webrateStatus) {
        Map<Date, Map<Integer, CompetitorInfo>> competitorInfoMap = new HashMap<>();
        Map<Integer, CompetitorInfo> competitorInfoMapByLOS = new HashMap<>();
        competitorInfoMapByLOS.put(1, createCompetitor(occupancyDate, competitorPrice, webrateStatus));
        competitorInfoMap.put(occupancyDate, competitorInfoMapByLOS);
        return competitorInfoMap;
    }

    private CompetitorInfo createCompetitor(Date occupancyDate, String competitorPrice, String webrateStatus) {
        CompetitorInfo competitorInfo = new CompetitorInfo();
        competitorInfo.setLengthOfStay(1);
        competitorInfo.setCompetitorPrice(competitorPrice);
        competitorInfo.setArrivalDate(occupancyDate);
        competitorInfo.setWebrateStatus(webrateStatus);
        return competitorInfo;
    }

    @Test
    public void getRoomTypeRateDtos() throws Exception {
        assertEquals("DELUXE", service.getRoomTypeRateDtos(startDate, "LV2").get(0).getRoomType());
    }

    @Test
    public void getRoomTypeRateDtosWhenDisplayStatusIDIsNot1() throws Exception {
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set display_status_id = 2 where accom_type_name = 'DELUXE'");
        assertNull(service.getRoomTypeRateDtos(startDate, "LV2"));
    }

    @Test
    public void filterEmptyAccomTypesRateDtos() throws Exception {
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set Accom_Type_Capacity=0 where accom_type_name = 'DELUXE'");
        assertNull(service.getRoomTypeRateDtos(startDate, "LV2"));
    }

    @Test
    public void filterEmptyAccomTypesRateDtosForCp() throws Exception {
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set Accom_Type_Capacity=0 where accom_type_name = 'DELUXE'");
        assertNull(service.getRoomTypeRateDtos(startDate, "LV2"));
    }

    @Test
    public void getRoomTypeRateDtosForContinuousPricing() throws Exception {
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        LocalDate start = LocalDate.fromDateFields(startDate);

        CPDecisionBAROutput cpDecisionBAROutput = tenantCrudService().findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, start, start, Arrays.asList(accomType)));
        cpDecisionBAROutput.setFinalBAR(BigDecimal.TEN);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        assertEquals(BigDecimal.TEN, service.getRoomTypeRateDtos(startDate, null).get(0).getRate().setScale(0));
    }

    @Test
    public void getRoomTypeRateDtosForContinuousPricingWhenAccomTypeDisplayStatusIDISNot1() throws Exception {
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        accomType.setDisplayStatusId(2);
        tenantCrudService().save(accomType);
        LocalDate start = LocalDate.fromDateFields(startDate);

        CPDecisionBAROutput cpDecisionBAROutput = tenantCrudService().findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, start, start, Arrays.asList(accomType)));
        cpDecisionBAROutput.setFinalBAR(BigDecimal.TEN);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        assertNull(service.getRoomTypeRateDtos(startDate, null));
    }

    @Test
    public void getSRPDetailsForMarketSegmentV2() throws Exception {
        Date startDate = DateUtil.getDateWithoutTime(10, 7, 2011);
        Date endDate = DateUtil.getDateWithoutTime(12, 7, 2011);
        addPaceTotalActivity(new LocalDate(2011, 8, 10), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 11), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 12), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));

        List<BusinessAnalysisDataDetailsDto> dataDetailsDto =
                service.getSRPDetailsForMarketSegment(startDate, endDate, 1, false, false, false, 7, false);
        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = dataDetailsDto.get(0);

        assertEquals("SHHQO1", businessAnalysisDataDetailsDto.getName());
        assertEquals(BigDecimal.ZERO, businessAnalysisDataDetailsDto.getOnBooksPickUp());
    }

    @Test
    public void shouldGivePickUpAsNullWhileGettingSRPDetailsForMarketSegmentV2() throws Exception {
        Date startDate = DateUtil.getDateWithoutTime(10, 7, 2011);
        Date endDate = DateUtil.getDateWithoutTime(12, 7, 2011);
        addPaceTotalActivity(new LocalDate(2011, 8, 10), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 11), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 12), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));

        tenantCrudService().executeUpdateByNativeQuery(
                "delete from pace_total_activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );

        List<BusinessAnalysisDataDetailsDto> dataDetailsDto =
                service.getSRPDetailsForMarketSegment(startDate, endDate, 1, false, false, false, 7, false);
        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = dataDetailsDto.get(0);

        assertEquals("SHHQO1", businessAnalysisDataDetailsDto.getName());
        assertEquals(null, businessAnalysisDataDetailsDto.getOnBooksPickUp());
    }

    @Test
    public void shouldRateValueAsRevenueWhileGettingSRPDetailsForMarketSegmentV2_Total_Rate_EnableON() throws Exception {
        Date startDate = DateUtil.getDateWithoutTime(10, 7, 2011);
        Date endDate = DateUtil.getDateWithoutTime(12, 7, 2011);
        addPaceTotalActivity(new LocalDate(2011, 8, 10), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 11), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 12), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));

        tenantCrudService().executeUpdateByNativeQuery(
                "delete from pace_total_activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );

        addReservationNight(DateUtil.convertDateToLocalDate(startDate));

        when(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(1);
        List<BusinessAnalysisDataDetailsDto> dataDetailsDto =
                service.getSRPDetailsForMarketSegment(startDate, endDate, 1, false, false, false, 7, false);
        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = dataDetailsDto.get(1);

        assertEquals("TESTCODE", businessAnalysisDataDetailsDto.getName());
        assertEquals(new BigDecimal("800.00000"), businessAnalysisDataDetailsDto.getRevenue());
    }

    @Test
    public void shouldRateValueAsRevenueWhileGettingSRPDetailsForMarketSegmentV2_Post_Departure_Adjustment_EnableON() throws Exception {
        Date startDate = DateUtil.getDateWithoutTime(10, 7, 2011);
        Date endDate = DateUtil.getDateWithoutTime(12, 7, 2011);
        addPaceTotalActivity(new LocalDate(2011, 8, 10), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 11), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));
        addPaceTotalActivity(new LocalDate(2011, 8, 12), LocalDate.fromDateFields(DateUtil.getFirstDayOfLastMonth()).minusDays(7));

        tenantCrudService().executeUpdateByNativeQuery(
                "delete from pace_total_activity where Business_Day_End_DT = '" + service.calculateBusinessDateForPastDays(7) + "' "
        );

        addReservationNight(DateUtil.convertDateToLocalDate(startDate));
        populatePostDepData(DateUtil.convertDateToLocalDate(startDate));

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);
        List<BusinessAnalysisDataDetailsDto> dataDetailsDto =
                service.getSRPDetailsForMarketSegment(startDate, endDate, 1, false, false, false, 7, false);
        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = dataDetailsDto.get(1);

        assertEquals("TESTCODE", businessAnalysisDataDetailsDto.getName());
        assertEquals(new BigDecimal("2"), businessAnalysisDataDetailsDto.getOnBooks());
        assertEquals(new BigDecimal("1500.00000"), businessAnalysisDataDetailsDto.getRevenue());
    }


    private void addPaceTotalActivity(LocalDate occupancyDate, LocalDate businessDate) {
        tenantCrudService().executeUpdateByNativeQuery("insert into pace_total_activity values" +
                        "(5,:occupancyDate, :businessDate, :businessDate, 448, 517, 5, 5, 502, 507, 12,  4, 10000.00, 2000.00, 12000.00, 1, 1, 1, getdate()," + null + ")",
                QueryParameter.with("occupancyDate", occupancyDate)
                        .and("businessDate", businessDate).parameters());
    }

    private void addReservationNight(LocalDate startDate) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" INSERT INTO [Reservation_Night]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM],[Occupancy_DT]) ");
        insertQuery.append("  VALUES (2, 5,1001,'SS','" + startDate.toString() + "' ");
        insertQuery.append(" ,'" + startDate.plusDays(2).toString() + "','" + startDate.minusDays(1).toString() + "',NULL,'QN',4,'1',500,0,0,0,0,500,NULL,NULL,'TESTCODE',400,NULL,'GT',0,1,GETDATE(),NULL,NULL,NULL,'" + startDate + "')");

        insertQuery.append(" INSERT INTO [Reservation_Night]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM],[Occupancy_DT]) ");
        insertQuery.append("  VALUES (2, 5,1001,'SS','" + startDate.toString() + "' ");
        insertQuery.append(" ,'" + startDate.plusDays(2).toString() + "','" + startDate.minusDays(1).toString() + "',NULL,'QN',4,'1',500,0,0,0,0,500,NULL,NULL,'TESTCODE',400,NULL,'GT',0,1,GETDATE(),NULL,NULL,NULL,'" + startDate.plusDays(1).toString() + "')");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

    }

    private void populatePostDepData(LocalDate startDate) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" INSERT INTO [post_departure_revenue]");
        insertQuery.append(" ([Reservation_Identifier],[occupancy_dt]");
        insertQuery.append(" ,[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[market_code],[Rate_Code],[Rate_Value])");
        insertQuery.append("  VALUES (1001,'" + startDate.plusDays(2).toString() + "' ");
        insertQuery.append(" ,4,'1',500,0,0,500,'BART','TESTCODE',400)");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

    }

    @Test
    public void loadArrivalDemand() throws Exception {
        BusinessAnalysisBARByLOSDto barByLosDto = new BusinessAnalysisBARByLOSDto();

        service.loadArrivalDemand(barByLosDto, propertyId, startDate);

        assertNotNull(barByLosDto.getLos1ArrivalDemand());
        assertNotNull(barByLosDto.getLos2ArrivalDemand());
        assertNotNull(barByLosDto.getLos3ArrivalDemand());
        assertNotNull(barByLosDto.getLos4ArrivalDemand());
        assertNotNull(barByLosDto.getLos5ArrivalDemand());
        assertNotNull(barByLosDto.getLos6ArrivalDemand());
        assertNotNull(barByLosDto.getLos7ArrivalDemand());
        assertNotNull(barByLosDto.getLos8ArrivalDemand());
    }

    @Test
    public void loadArrivalDemandForAccomClass() throws Exception {
        BusinessAnalysisBARByLOSDto barByLosDto = new BusinessAnalysisBARByLOSDto();

        AccomClass masterAccomClass = accommodationService.findMasterClass(propertyId);

        service.loadArrivalDemand(barByLosDto, propertyId, masterAccomClass, startDate);

        assertNotNull(barByLosDto.getLos1ArrivalDemand());
    }

    @Test
    public void loadLengthOfStayBARRate() throws Exception {
        BusinessAnalysisBARByLOSDto barByLosDto = new BusinessAnalysisBARByLOSDto();

        AccomClass masterAccomClass = accommodationService.findMasterClass(propertyId);

        service.loadLengthOfStayBARRate(barByLosDto, masterAccomClass, startDate, false);

        assertNotNull(barByLosDto.getLos1Rate());
        assertNotNull(barByLosDto.getLos2Rate());
        assertNotNull(barByLosDto.getLos3Rate());
        assertNotNull(barByLosDto.getLos4Rate());
        assertNotNull(barByLosDto.getLos5Rate());
        assertNotNull(barByLosDto.getLos6Rate());
        assertNotNull(barByLosDto.getLos7Rate());
        assertNotNull(barByLosDto.getLos8Rate());
    }

    @Test
    public void loadBARRateCodeByArrivalByRC() throws Exception {
        BusinessAnalysisBARByLOSDto barByLosDto = new BusinessAnalysisBARByLOSDto();

        AccomClass masterAccomClass = accommodationService.findMasterClass(propertyId);

        service.loadBARRateCodeByArrivalByRC(barByLosDto, masterAccomClass, startDate, false);

        assertNotNull(barByLosDto.getLos1Level());
        assertNotNull(barByLosDto.getLos2Level());
        assertNotNull(barByLosDto.getLos3Level());
        assertNotNull(barByLosDto.getLos4Level());
        assertNotNull(barByLosDto.getLos5Level());
        assertNotNull(barByLosDto.getLos6Level());
        assertNotNull(barByLosDto.getLos7Level());
        assertNotNull(barByLosDto.getLos8Level());
    }

    @Test
    public void parseDate() throws Exception {
        assertThrows(TetrisException.class, () -> {
            service.parseDate("ABC");
        });
    }

    @Test
    public void calculateBusinessDateForPastDays() {
        LocalDate businessDate = new LocalDate();
        int pastDays = 7;
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());

        assertEquals(businessDate.minusDays(pastDays), service.calculateBusinessDateForPastDays(pastDays));
    }

    @Test
    public void calculateSRPBusinessDateForPastDays() {
        LocalDate businessDate = new LocalDate();
        int pastDays = 7;
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());

        assertEquals(businessDate.minusDays(pastDays - 1), service.calculateSRPBusinessDateForPastDays(pastDays));
    }

    @Test
    public void getRateBoundsForDate_NoMasterClass() throws Exception {
        CrudServiceBean mockCrudService = mock(CrudServiceBean.class);
        LocalDate startDate = new LocalDate(2016, 11, 1);
        LocalDate endDate = new LocalDate(2016, 11, 30);

        service.setCrudService(mockCrudService);
        lenient().when(mockCrudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(null);

        Map<Date, RateBounds> retResults = service.getRateBoundsForDate(propertyId, startDate.toDate(), endDate.toDate(), new AccomClass());

        assertTrue(retResults.isEmpty());
    }

    @Test
    public void TestIsPhysicalCapacityEnabled() throws Exception {

        when(configService.isEnablePhysicalCapacityConsideration()).thenReturn(true);
        assertEquals(service.isPhysicalCapacityEnabled(), 1);

        when(configService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        assertEquals(service.isPhysicalCapacityEnabled(), 0);
    }

    private void closeRateForDateRange(String rateName, Date closedStartDate, Date closedEndDate) {
        Date today = DateUtil.getCurrentDate();
        AccomClass masterAccomClass = accommodationService.findMasterClass(propertyId);
        Integer rateToCloseId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", rateName).parameters());
        RateUnqualified rateToClose = tenantCrudService().find(RateUnqualified.class, rateToCloseId);

        RateUnqualifiedAccomClass rateUnqualifiedAccomClass = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualifiedAccomClass.GET_DETAILS_BY_RATE_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with("id", rateToClose.getId())
                        .and("accomClassId", masterAccomClass.getId()).parameters());

        RateUnqualifiedClosed closed = new RateUnqualifiedClosed();
        closed.setRateUnqualifiedAccomClass(rateUnqualifiedAccomClass);
        closed.setRateUnqualifiedClosedStartDate(new DateParameter(closedStartDate));
        closed.setRateUnqualifiedClosedEndDate(new DateParameter(closedEndDate));
        closed.setNotes("For testing");
        closed.setCreateDate(today);
        closed.setCreatedByUserId(1);
        closed.setLastUpdatedDate(today);
        closed.setLastUpdatedByUserId(1);
        tenantCrudService().save(closed);
    }

    @Test
    public void getBusinessAnalysisDailyDataDtos_CPEnabled() throws Exception {
        updateTransientPricingBaseAccomType();
        when(propertyConfigParamService.isCP()).thenReturn(true);
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        assertEquals(28, dailyDataDtos.size());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            assertNotNull(dailyReferenceData.getDate());
            assertNotNull(dailyReferenceData.getAdr());
            assertEquals("", dailyReferenceData.getBarCode());
            assertNull(dailyReferenceData.getBarRate());
            assertNotNull(dailyReferenceData.getCapacity());
            assertNotNull(dailyReferenceData.getLrv());
            assertNotNull(dailyReferenceData.getOccupancyForecast());
            assertNotNull(dailyReferenceData.getOnBooks());
            assertNotNull(dailyReferenceData.getOutOfOrder());
            assertNotNull(dailyReferenceData.getOverbookings());
            assertNotNull(dailyReferenceData.getRevpar());
            assertNotNull(dailyReferenceData.getUnconstrainedRemainingDemand());
            assertNotNull(dailyReferenceData.getWash());
            assertNotNull(dailyReferenceData.getDecisionReasonTypeId());
            assertNotNull(dailyReferenceData.getUpperBound());
            assertNotNull(dailyReferenceData.getLowerBound());
        }
    }

    @Test
    public void getBusinessAnalysisDailyDataDtosPhysicalCapacity_CPEnabled() throws Exception {
        updateTransientPricingBaseAccomType();
        when(configService.isEnablePhysicalCapacityConsideration()).thenReturn(true);
        when(propertyConfigParamService.isCP()).thenReturn(true);
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        assertEquals(28, dailyDataDtos.size());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            assertNotNull(dailyReferenceData.getDate());
            assertNotNull(dailyReferenceData.getAdr());
            assertEquals("", dailyReferenceData.getBarCode());
            assertNull(dailyReferenceData.getBarRate());
            assertNotNull(dailyReferenceData.getCapacity());
            assertNotNull(dailyReferenceData.getLrv());
            assertNotNull(dailyReferenceData.getOccupancyForecast());
            assertNotNull(dailyReferenceData.getOnBooks());
            assertNotNull(dailyReferenceData.getOnBooksPerc());
            assertNotNull(dailyReferenceData.getOutOfOrder());
            assertNotNull(dailyReferenceData.getOverbookings());
            assertNotNull(dailyReferenceData.getRevpar());
            assertNotNull(dailyReferenceData.getUnconstrainedRemainingDemand());
            assertNotNull(dailyReferenceData.getWash());
            assertNotNull(dailyReferenceData.getDecisionReasonTypeId());
            assertNotNull(dailyReferenceData.getUpperBound());
            assertNotNull(dailyReferenceData.getLowerBound());

            BigDecimal expectedOccupancyForecastPercent = (dailyReferenceData.getOccupancyForecast()
                    .divide(new BigDecimal(dailyReferenceData.getCapacity()), 4, HALF_UP))
                    .multiply(BigDecimal.valueOf(100)).setScale(2, HALF_UP);

            assertEquals(expectedOccupancyForecastPercent, dailyReferenceData.getOccupancyForecastPerc());
        }
    }

    @Test
    public void getBusinessAnalysisDailyDataDtos_CPDisabled_CompRoomsFilter_True() throws Exception {
        updateTransientPricingBaseAccomType();
        when(propertyConfigParamService.isCP()).thenReturn(false);
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtos(startDate, endDate, false);
        assertEquals(28, dailyDataDtos.size());

        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDailyDataDto> dailyDataDtosWithoutExludedMS = service.getBusinessAnalysisDailyDataDtos(startDate, endDate, true);
        assertEquals(28, dailyDataDtosWithoutExludedMS.size());

        verifyDetails(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void getBusinessAnalysisDailyDataDtos_CPEnabled_CompRoomsFilter_True() throws Exception {
        updateTransientPricingBaseAccomType();
        when(propertyConfigParamService.isCP()).thenReturn(true);
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtos(startDate, endDate, true);
        assertEquals(28, dailyDataDtos.size());

        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDailyDataDto> dailyDataDtosWithoutExludedMS = service.getBusinessAnalysisDailyDataDtos(startDate, endDate, true);
        assertEquals(28, dailyDataDtosWithoutExludedMS.size());

        verifyDetails(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void testComputeOnBooksPercentageForNullData() throws Exception {
        Object[] row = new Object[16];
        assertEquals(BigDecimal.ZERO, service.computeOnBooksPercentage(row, false));
    }

    @Test
    public void testComputeOnBooksPercentageForZeroValueData() throws Exception {
        Object[] row = new Object[16];
        Arrays.fill(row, BigDecimal.ZERO);
        assertEquals(BigDecimal.ZERO, service.computeOnBooksPercentage(row, true));
    }

    @Test
    public void shouldComputeValidOnBooksPercentage() throws Exception {
        Object[] row = new Object[16];
        Arrays.fill(row, BigDecimal.ONE);
        assertEquals(BigDecimal.valueOf(100.000000).setScale(6), service.computeOnBooksPercentage(row, true));
    }

    @Test
    public void shouldComputeValidOnBooksPercentageWhenPhysicalCapacityConsiderationNotEnabled() throws Exception {
        Object[] row = new Object[16];
        Arrays.fill(row, BigDecimal.ONE);
        row[2] = new BigDecimal("400");
        row[7] = new BigDecimal("2");
        row[3] = new BigDecimal("375");
        assertEquals(BigDecimal.valueOf(94.22).setScale(6), service.computeOnBooksPercentage(row, false));
    }

    @Test
    public void test_getDailyOverviewDto_For_RateName() {
        RateUnqualified rateUnqualified = (RateUnqualified) tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES,
                QueryParameter.with("names", Collections.singletonList("LV2")).parameters());
        rateUnqualified.setName("LV 2");
        tenantCrudService().save(rateUnqualified);
        BusinessAnalysisOverviewDto hotelDailyOverviewDto = service.getDailyOverviewDto(startDate, -1);

        assertEquals("Hotel", hotelDailyOverviewDto.getName());
        assertNull(hotelDailyOverviewDto.getLrv());
        assertNull(hotelDailyOverviewDto.getRate());
        assertNull(hotelDailyOverviewDto.getRateCode());
        assertEquals(BigDecimal.ZERO, hotelDailyOverviewDto.getOverbooking());
        assertEquals(3, hotelDailyOverviewDto.getChildren().size());

        BusinessAnalysisOverviewDto standardAccomClassDto = hotelDailyOverviewDto.getChildren().get(0);
        assertEquals("STD", standardAccomClassDto.getName());
        assertNull(standardAccomClassDto.getOverbooking());
        assertNull(standardAccomClassDto.getRate());
        assertEquals("LV 2", standardAccomClassDto.getRateCode());

    }

    @Test
    public void shouldGetBusinessAnalysisDailyDataDtosByInventoryGroup() {
        //GIVEN
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        InventoryGroup inventoryGroup = new InventoryGroup();
        AccomClass baseAccomClass = new AccomClass();
        baseAccomClass.setId(1);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        when(mockCrudService.find(InventoryGroup.class, 1)).thenReturn(inventoryGroup);
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("inventoryGroupId", 1);
        parameters.put("propertyId", propertyId);
        parameters.put("startDate", startDate);
        parameters.put("endDate", endDate);
        parameters.put("isPhysicalCapacityEnabled", 0);
        ArgumentCaptor<String> queryArgumentCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<RowMapper> rowMapperArgumentCaptor = ArgumentCaptor.forClass(RowMapper.class);
        //WHEN
        service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate, endDate, 1, false);
        //THEN
        verify(mockCrudService).findByNativeQuery(queryArgumentCaptor.capture(),
                mapArgumentCaptor.capture(), rowMapperArgumentCaptor.capture());
        String query = queryArgumentCaptor.getValue();
        assertEquals("exec dbo.usp_rdd_load_basic_data_by_inventory_group :propertyId,:startDate,:endDate,:isPhysicalCapacityEnabled,:inventoryGroupId,:compRoomsFilter", query);
        assertEquals(1, mapArgumentCaptor.getValue().get("inventoryGroupId"));
        assertEquals(propertyId, mapArgumentCaptor.getValue().get("propertyId"));
        assertEquals(startDate, mapArgumentCaptor.getValue().get("startDate"));
        assertEquals(endDate, mapArgumentCaptor.getValue().get("endDate"));
        assertEquals(0, mapArgumentCaptor.getValue().get("isPhysicalCapacityEnabled"));
    }

    @Test
    public void shouldGetBusinessAnalysisDailyDataDtosForInventoryGroup() {
        final CrudService crudService = tenantCrudService();
        final LocalDate caughtUpLocalDate = getCaughtUpLocalDate();
        final LocalDate startDate = caughtUpLocalDate.plusDays(1);
        InventoryGroup inventoryGroup = createTestDataForInventoryGroup(crudService, caughtUpLocalDate, startDate);
        service.setCrudService(crudService);
        final List<BusinessAnalysisDailyDataDto> badByInventoryGroup = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate.toDate(), startDate.toDate(),
                inventoryGroup.getId(), false);
        final BusinessAnalysisDailyDataDto businessAnalysisDailyDataDto = badByInventoryGroup.get(0);
        assertNotNull(badByInventoryGroup);
        assertEquals(BigDecimal.valueOf(155.0), businessAnalysisDailyDataDto.getOccupancyForecast());
        assertEquals(BigDecimal.valueOf(47.69), businessAnalysisDailyDataDto.getOccupancyForecastPerc());
    }

    @Test
    public void shouldGetBusinessAnalysisDailyDataDtosForInventoryGroup_CPDisabled_CompRoomsFilter_True() {
        final CrudService crudService = tenantCrudService();
        final LocalDate caughtUpLocalDate = getCaughtUpLocalDate();
        final LocalDate startDate = caughtUpLocalDate.plusDays(1);
        InventoryGroup inventoryGroup = createTestDataForInventoryGroup(crudService, caughtUpLocalDate, startDate);
        service.setCrudService(crudService);
        when(propertyConfigParamService.isCP()).thenReturn(false);
        final List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate.toDate(), startDate.toDate(),
                inventoryGroup.getId(), false);

        updateMarketSegmentToExcluded(1);
        final List<BusinessAnalysisDailyDataDto> dailyDataDtosWithoutExludedMS = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate.toDate(), startDate.toDate(),
                inventoryGroup.getId(), true);
        verifyDetails(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void shouldGetBusinessAnalysisDailyDataDtosForInventoryGroup_CPEnabled_CompRoomsFilter_True() {
        final CrudService crudService = tenantCrudService();
        final LocalDate caughtUpLocalDate = getCaughtUpLocalDate();
        final LocalDate startDate = caughtUpLocalDate.plusDays(1);
        InventoryGroup inventoryGroup = createTestDataForInventoryGroup(crudService, caughtUpLocalDate, startDate);
        service.setCrudService(crudService);
        when(propertyConfigParamService.isCP()).thenReturn(true);
        final List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate.toDate(), startDate.toDate(),
                inventoryGroup.getId(), false);

        updateMarketSegmentToExcluded(1);
        final List<BusinessAnalysisDailyDataDto> dailyDataDtosWithoutExludedMS = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(startDate.toDate(), startDate.toDate(),
                inventoryGroup.getId(), true);
        verifyDetails(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    void verifyOutOfOrderRoomsCountForInventoryGroup_CP() {
        final java.time.LocalDate caughtUpLocalDate = JavaLocalDateUtils.toJavaLocalDate(getCaughtUpLocalDate());
        final java.time.LocalDate startDate = caughtUpLocalDate.plusDays(1);
        InventoryGroup inventoryGroup = createTestDataForInventoryGroup(tenantCrudService, JavaLocalDateUtils.toJodaLocalDate(caughtUpLocalDate), JavaLocalDateUtils.toJodaLocalDate(startDate));
        service.setCrudService(tenantCrudService);

        when(propertyConfigParamService.isCP()).thenReturn(true);
        updateAccomTypeToInactive(7);

        final List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(startDate), inventoryGroup.getId(), false);

        final BusinessAnalysisDailyDataDto businessAnalysisDailyDataDto = dailyDataDtos.get(0);
        assertNotNull(dailyDataDtos);
        assertEquals(4, businessAnalysisDailyDataDto.getOutOfOrder());
    }

    @Test
    void verifyOutOfOrderRoomsCountForInventoryGroup_NonCP() {
        final java.time.LocalDate caughtUpLocalDate = JavaLocalDateUtils.toJavaLocalDate(getCaughtUpLocalDate());
        final java.time.LocalDate startDate = caughtUpLocalDate.plusDays(1);
        InventoryGroup inventoryGroup = createTestDataForInventoryGroup(tenantCrudService, JavaLocalDateUtils.toJodaLocalDate(caughtUpLocalDate), JavaLocalDateUtils.toJodaLocalDate(startDate));
        service.setCrudService(tenantCrudService);

        when(propertyConfigParamService.isCP()).thenReturn(false);
        updateAccomTypeToInactive(8);

        final List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosByInventoryGroup(JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(startDate), inventoryGroup.getId(), false);

        final BusinessAnalysisDailyDataDto businessAnalysisDailyDataDto = dailyDataDtos.get(0);
        assertNotNull(dailyDataDtos);
        assertEquals(4, businessAnalysisDailyDataDto.getOutOfOrder());
    }

    private void updateAccomTypeToInactive(int accomTypeId) {
        tenantCrudService.executeUpdateByNativeQuery("update accom_type set Status_ID=2, Display_Status_ID=2 where accom_type_id="+accomTypeId);
    }


    private InventoryGroup createTestDataForInventoryGroup(CrudService crudService, LocalDate caughtUpLocalDate, LocalDate startDate) {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("INV_1");
        AccomClass baseAccomClass = crudService.find(AccomClass.class, 2);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        crudService.save(inventoryGroup);
        crudService.executeUpdateByNativeQuery("delete from Occupancy_FCST where occupancy_dt='" + startDate + "'");
        crudService.executeUpdateByNativeQuery("insert into Inventory_Group_Details values(" + inventoryGroup.getId() + ",2,1,GETDATE(),1,GETDATE())");
        final String queryStr = "insert into Occupancy_FCST(Decision_ID,Property_ID,MKT_SEG_ID,Accom_Type_ID,Occupancy_DT," +
                "Occupancy_NBR,Revenue,Month_ID,Year_ID)" +
                " values((select MAX(decision_id) from Decision),5,1,7,'" + startDate + "',155,40008.9800,6,5)";
        crudService.executeUpdateByNativeQuery(queryStr);
        return inventoryGroup;
    }


    private LocalDate getCaughtUpLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(5,3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    @Test
    public void shouldGetRoomTypeRateDtosForByInventoryGroupForCotinuousPricing() {
        //GIVEN
        LocalDate date = LocalDate.now();
        InventoryGroup inventoryGroup = createInventoryGroupDetails();
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("100.00"), 6);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("200.00"), 7);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("300.00"), 8);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("400.00"), 4);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("500.00"), 5);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        //WHEN
        List<BusinessAnalysisRoomTypeRateDto> roomTypeRateDtosByInventoryGroup =
                service.getRoomTypeRateDtosByInventoryGroup(date.toDate(), "LV0", inventoryGroup.getId());
        //THEN
        assertEquals(3, roomTypeRateDtosByInventoryGroup.size());
        assertEquals(new BigDecimal("100.00000"), roomTypeRateDtosByInventoryGroup.get(0).getRate());
        assertEquals(new BigDecimal("200.00000"), roomTypeRateDtosByInventoryGroup.get(1).getRate());
        assertEquals(new BigDecimal("300.00000"), roomTypeRateDtosByInventoryGroup.get(2).getRate());
    }

    @Test
    public void shouldGetRoomTypeRateDtosForByInventoryGroupForContinuousPricingWhenARoomTypeDoesNotHaveDisplayStatusID1() {
        //GIVEN
        LocalDate date = LocalDate.now();
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set display_status_id = 2 where accom_type_id = 6"); //DOUBLE
        InventoryGroup inventoryGroup = createInventoryGroupDetails();
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("100.00"), 6);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("200.00"), 7);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("300.00"), 8);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("400.00"), 4);
        updateFinalBARInCpDecisionBarOutput(date, new BigDecimal("500.00"), 5);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        //WHEN
        List<BusinessAnalysisRoomTypeRateDto> roomTypeRateDtosByInventoryGroup =
                service.getRoomTypeRateDtosByInventoryGroup(date.toDate(), "LV0", inventoryGroup.getId());
        //THEN
        assertEquals(2, roomTypeRateDtosByInventoryGroup.size());
        assertEquals(new BigDecimal("200.00000"), roomTypeRateDtosByInventoryGroup.get(0).getRate());
        assertEquals("QUEEN", roomTypeRateDtosByInventoryGroup.get(0).getRoomType());
        assertEquals(new BigDecimal("300.00000"), roomTypeRateDtosByInventoryGroup.get(1).getRate());
        assertEquals("KING", roomTypeRateDtosByInventoryGroup.get(1).getRoomType());
    }

    @Test
    public void filterEmptyAccomTypesRateDtosByInventoryGroup() {
        //GIVEN
        LocalDate date = LocalDate.now();
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set Accom_Type_Capacity=0 where accom_type_id = 6"); //DOUBLE
        InventoryGroup inventoryGroup = createInventoryGroupDetails();
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        //WHEN
        List<BusinessAnalysisRoomTypeRateDto> roomTypeRateDtosByInventoryGroup =
                service.getRoomTypeRateDtosByInventoryGroup(date.toDate(), "LV0", inventoryGroup.getId());
        //THEN
        assertNull(roomTypeRateDtosByInventoryGroup);
    }

    @Test
    public void filterEmptyAccomTypesRateDtosByInventoryGroupForCP() {
        //GIVEN
        LocalDate date = LocalDate.now();
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set Accom_Type_Capacity=0 where accom_type_id = 6"); //DOUBLE
        InventoryGroup inventoryGroup = createInventoryGroupDetails();
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        //WHEN
        List<BusinessAnalysisRoomTypeRateDto> roomTypeRateDtosByInventoryGroup =
                service.getRoomTypeRateDtosByInventoryGroup(date.toDate(), "LV0", inventoryGroup.getId());
        //THEN
        assertEquals(2, roomTypeRateDtosByInventoryGroup.size());
        assertEquals("QUEEN", roomTypeRateDtosByInventoryGroup.get(0).getRoomType());
        assertEquals("KING", roomTypeRateDtosByInventoryGroup.get(1).getRoomType());
    }

    private void updateFinalBARInCpDecisionBarOutput(LocalDate startDate, BigDecimal savedRateUnqualified, Integer accomTypeId) {
        tenantCrudService().executeUpdateByNativeQuery("update cp_decision_bar_output set Final_BAR = " + savedRateUnqualified
                + " where Arrival_DT = '" + startDate + "' and Accom_Type_ID = " + accomTypeId);
    }

    private InventoryGroup createInventoryGroupDetails() {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("INV_GRP");
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, 2));
        InventoryGroupDetails savedInventoryGroupDetails = tenantCrudService().save(inventoryGroupDetails);
        inventoryGroup.setInventoryGroupDetails(Arrays.asList(savedInventoryGroupDetails));
        return inventoryGroup;
    }

    //Forecast Group ID = 10 for Mkt Seg id in(1 - Transient, 14 - Group)
    @Test
    public void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypes() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 10, 20, "1", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosTransient.size());
        assertEquals(new BigDecimal(957), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getOnBooks());
        assertEquals(new BigDecimal(49140.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getRevenue());
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, 10, 20, "1", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosGroup.size());
        assertEquals(new BigDecimal(1563), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getOnBooks());
        assertEquals(new BigDecimal(81900.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getRevenue());
    }

    @Test
    void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypes_DisconMS_Selected() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id =1");
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 10, 20, "1,3", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosTransient.size());
        assertEquals(new BigDecimal(957), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getOnBooks());
        assertEquals(new BigDecimal(49140.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getRevenue());
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, 10, 20, "1,3", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosGroup.size());
        assertEquals(new BigDecimal(1563), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getOnBooks());
        assertEquals(new BigDecimal(81900.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getRevenue());
    }

    @Test
    void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypes_DisconMS_Not_Selected() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (1,14)");
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 10, 20, "1", false);
        assertNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, 10, 20, "1", false);
        assertNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
    }


    @Test
    public void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypesInventoryGroup() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group_Details];");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group];");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group] VALUES('test','test',3,11403,GETDATE(),11403,GETDATE(), NULL);");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group_Details] VALUES((select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'),3,11403,GETDATE(), 11403,GETDATE())");
        BigInteger inventoryGroupId = tenantCrudService().findByNativeQuerySingleResult("select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'", null);
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 2, 10, 20, "1", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosTransient.size());
        assertEquals(new BigDecimal(173), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getOnBooks());
        assertEquals(new BigDecimal(8820.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getRevenue());
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 1, 10, 20, "1", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosGroup.size());
        assertEquals(new BigDecimal(294), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getOnBooks());
        assertEquals(new BigDecimal(15390.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getRevenue());
    }

    @Test
    void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypesInventoryGroup_DisconMS_Selected() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group_Details];");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group];");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group] VALUES('test','test',3,11403,GETDATE(),11403,GETDATE(), NULL);");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group_Details] VALUES((select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'),3,11403,GETDATE(), 11403,GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (1)");
        BigInteger inventoryGroupId = tenantCrudService().findByNativeQuerySingleResult("select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'", null);
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 2, 10, 20, "1,3", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosTransient.size());
        assertEquals(new BigDecimal(173), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getOnBooks());
        assertEquals(new BigDecimal(8820.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosTransient.get(0).getRevenue());
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 1, 10, 20, "1,3", false);
        assertNotNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
        assertEquals(1, forecastGroupBusinessAnalysisDataDetailDtosGroup.size());
        assertEquals(new BigDecimal(294), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getOnBooks());
        assertEquals(new BigDecimal(15390.00).setScale(2), forecastGroupBusinessAnalysisDataDetailDtosGroup.get(0).getRevenue());
    }

    @Test
    void checkMarketSegmentLevelDataForTransientAndGroupBusinessTypesInventoryGroup_DisconMS__Not_Selected() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Forecast_Group_ID = 10 where Forecast_Group_ID = 1");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group_Details];");
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Inventory_Group];");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group] VALUES('test','test',3,11403,GETDATE(),11403,GETDATE(), NULL);");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Inventory_Group_Details] VALUES((select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'),3,11403,GETDATE(), 11403,GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (1,14)");
        BigInteger inventoryGroupId = tenantCrudService().findByNativeQuerySingleResult("select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'test'", null);
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosTransient =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 2, 10, 20, "1", false);
        assertNull(forecastGroupBusinessAnalysisDataDetailDtosTransient);
        List<BusinessAnalysisDataDetailsDto> forecastGroupBusinessAnalysisDataDetailDtosGroup = service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroupId.intValue(), startDate, endDate, false, 1, 10, 20, "1", false);
        assertNull(forecastGroupBusinessAnalysisDataDetailDtosGroup);
    }

    @Test
    public void validateRevenueAndAdrPickupForPropertyLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery("update pace_total_activity set Room_Revenue = Room_Revenue -100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-1.12, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassACLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100 , Rooms_Sold = Rooms_Sold -10 where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, 7, PhysicalOrComponent.PHYSICAL.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        assertPickUpRevenue(3000, badDetailDtoList.get(0));
        assertPickUpAdr(-3.7, badDetailDtoList.get(0));

    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassATLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10   where accom_type_id = 6 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 7 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, pastDays, PhysicalOrComponent.PHYSICAL_COMPONENT.getValue(), ACCOM_TYPE_DISPLAY_STATUS_IDS);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-5.91, badDetailDtoList.get(0));

    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassInvGrpLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 4 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getInventoryGroupBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, 7, 1, false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.45, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassInvGrpACLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 4 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, 7, 0, inventoryGroup.getId(), "1,2");
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.45, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassInvGrpACLevelExcludeCompRooms() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 4 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> dailyDataDtos =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, false, false, 7, 0, inventoryGroup.getId(), "1,2", false);

        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDataDetailsDto> dailyDataDtosWithoutExludedMS =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, false, false, 7, 0, inventoryGroup.getId(), "1,2", true);

        verifyDataDetailsDto(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassInvGrpATLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 20, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 4 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, 3, 7, 0, inventoryGroup.getId(), "1,2");
        assertPickUpRevenue(300, badDetailDtoList.get(0));
        assertPickUpAdr(-7.09, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForRoomClassInvGrpATLevelExcludeCompRoom() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_Accom_Activity set Room_Revenue = Room_Revenue - 20, Rooms_Sold = Rooms_Sold -10  where accom_type_id = 4 and business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> dailyDataDtos =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, false, false, 3, 7, 0, inventoryGroup.getId(), "1,2", false);
        updateMarketSegmentToExcluded(1);
        List<BusinessAnalysisDataDetailsDto> dailyDataDtosWithoutExludedMS =
                service.getRoomClassBusinessAnalysisDataDetailDtosForInvGroup(startDate, endDate, false, false, false, 3, 7, 0, inventoryGroup.getId(), "1,2", true);

        verifyDataDetailsDto(dailyDataDtos, dailyDataDtosWithoutExludedMS);
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false);
        assertPickUpRevenue(12000, badDetailDtoList.get(0));
        assertPickUpAdr(-6.17, badDetailDtoList.get(0));
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevel_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1", false);
        assertPickUpRevenue(9000, badDetailDtoList.get(0));
        assertPickUpAdr(-6.36, badDetailDtoList.get(0));
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevel_DisconMS_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, pastDays, "1,3", false);
        assertPickUpRevenue(12000, badDetailDtoList.get(0));
        assertPickUpAdr(-6.17, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevelInvGroup() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update Mkt_Accom_Activity set Room_Revenue = Room_Revenue + 5, Rooms_Sold = Rooms_Sold + 5 where Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getInventoryViewForecastGroupBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevelInvGroup_DisconMS_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update Mkt_Accom_Activity set Room_Revenue = Room_Revenue + 5, Rooms_Sold = Rooms_Sold + 5 where Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getInventoryViewForecastGroupBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, pastDays, "1,3", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    void validateUserForecastForForecastGroupBusinessTypeLevel() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        when(configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_BUSINESS_TYPE_FOR_USER_FORECAST)).thenReturn(true);
        when(dateService.getBusinessDate()).thenReturn(businessDate);

        Integer budgetLevelId = addUserForecastConfigForBusinessType();
        addUserForecastData(1, 11, BigDecimal.valueOf(111), DateUtil.convertDateToLocalDate(startDate), budgetLevelId);
        addUserForecastData(2, 22, BigDecimal.valueOf(222), DateUtil.convertDateToLocalDate(startDate), budgetLevelId);

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, true, false, 7, "1", false);

        BusinessAnalysisDataDetailsDto dto1 = badDetailDtoList.stream().filter(dto -> dto.getName().equalsIgnoreCase("GROUP")).findFirst().get();
        BusinessAnalysisDataDetailsDto dto2 = badDetailDtoList.stream().filter(dto -> dto.getName().equalsIgnoreCase("TRANSIENT")).findFirst().get();
        assertEquals(11.00, dto1.getUserForecastRooms().doubleValue());
        assertEquals(111.00, dto1.getUserForecastRevenue().doubleValue());
        assertEquals(22.00, dto2.getUserForecastRooms().doubleValue());
        assertEquals(222.00, dto2.getUserForecastRevenue().doubleValue() );
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupBusinessTypeLevelInvGroup_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update Mkt_Accom_Activity set Room_Revenue = Room_Revenue + 5, Rooms_Sold = Rooms_Sold + 5 where Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getInventoryViewForecastGroupBusinessAnalysisDataDetailDtos(inventoryGroup.getId(), startDate, endDate, false, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupFGLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, pastDays, "1", false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-4.5, badDetailDtoList.get(0));

    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupFGLevel_DisconMS_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        setMktSegStatusDiscontinued(Arrays.asList(14));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, pastDays, "1,3", false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-4.5, badDetailDtoList.get(0));

    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupFGLevel_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        setMktSegStatusDiscontinued(Arrays.asList(14));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, pastDays, "1", false);
        assertNull(badDetailDtoList);

    }

    private void updateMarketSegmentToExcludeCompRooms(StringBuilder insertQuery, int isExclude) {
        insertQuery.append(" Update Mkt_Seg set Exclude_CompHouse_Data_Display=+ isExclude + where Mkt_Seg_ID = 5 ");
    }

    @Test
    void testValidateRevenueAndAdrPickupForForecastGroupFGLevel_ExcludeMktSeg() {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        StringBuilder insertQuery = new StringBuilder();
        updateMarketSegmentToExcludeCompRooms(insertQuery, 1);
        setMktSegStatusDiscontinued(Arrays.asList(14));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 1, pastDays, "1", false);
        assertNull(badDetailDtoList);
        updateMarketSegmentToExcludeCompRooms(insertQuery, 0);
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupFGLevelInvGroup() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 5, Rooms_Sold = Rooms_Sold -1 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroup.getId(), startDate, endDate, false, 2, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupFGLevelInvGroup_DisconMS_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 5, Rooms_Sold = Rooms_Sold -1 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroup.getId(), startDate, endDate, false, 2, pastDays, "1,3", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupFGLevelInvGroup_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 5, Rooms_Sold = Rooms_Sold -1 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroup.getId(), startDate, endDate, false, 2, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupMSLevel() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 2, pastDays, "1", false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.6, badDetailDtoList.get(0));
    }

    @Test
    public void testValidateRevenueAndAdrPickupForForecastGroupMSLevelExcludeMktSeg() throws Exception {
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;
        StringBuilder insertQuery = new StringBuilder();
        updateMarketSegmentToExcludeCompRooms(insertQuery, 1);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 2, pastDays, "1", false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.6, badDetailDtoList.get(0));
        updateMarketSegmentToExcludeCompRooms(insertQuery, 0);
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupMSLevel_DisconMS_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 2, pastDays, "1,3", false);
        assertEquals(2, badDetailDtoList.size());
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.6, badDetailDtoList.get(0));
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupMSLevel_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        int pastDays = 7;

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, 2, 2, pastDays, "1", false);
        assertPickUpRevenue(1500, badDetailDtoList.get(0));
        assertPickUpAdr(-6.32, badDetailDtoList.get(0));
    }

    @Test
    public void validateRevenueAndAdrPickupForForecastGroupMSLevelInvGroup() throws Exception {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");
        int pastDays = 7;

        //When
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroup.getId(), startDate, endDate, false, 2, 2, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    void validateRevenueAndAdrPickupForForecastGroupMSLevelInvGroup_DisconMS_Not_Selected() {
        // Given
        Date businessDate = DateUtil.getDateForCurrentMonth(20);
        InventoryGroup inventoryGroup = getInventoryGroup(3, "INV_GRP_forecast");
        int pastDays = 7;

        //When
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        tenantCrudService().executeUpdateByNativeQuery(" update pace_mkt_activity set Room_Revenue = Room_Revenue - 100, Rooms_Sold = Rooms_Sold -10 where business_day_end_dt" +
                        " = :businessDT and Occupancy_DT between :startDate and :endDate ",
                QueryParameter.with("businessDT", service.calculateBusinessDateForPastDays(pastDays).toDate()).and("startDate", startDate).and("endDate", endDate).parameters());

        //Then
        List<BusinessAnalysisDataDetailsDto> badDetailDtoList =
                service.getForecastGroupBusinessAnalysisDataDetailDtosForInventoryGroup(inventoryGroup.getId(), startDate, endDate, false, 2, 2, pastDays, "1", false);
        assertNull(badDetailDtoList.get(0).getRevenuePickUp());
        assertNull(badDetailDtoList.get(0).getAdrPickUp());
    }

    @Test
    public void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtHotelIncludesLastYear() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update Total_activity SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_Total_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, false);
        assertEquals(1, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);

        assertEquals(propertyId, hotelDto.getId());
        assertEquals("Hilton - Pune", hotelDto.getName());
        assertEquals(new BigDecimal(1.61).setScale(2, RoundingMode.DOWN), hotelDto.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(18.21092).setScale(2, RoundingMode.DOWN), hotelDto.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(17640.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(208260.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(1.4209).setScale(2, RoundingMode.DOWN), hotelDto.getProPAROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(16.776220).setScale(2, RoundingMode.DOWN), hotelDto.getProPARForecast().setScale(2, RoundingMode.DOWN));
    }

    @Test
    public void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtBusinessTypeIncludesLastYear() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update MKT_ACCOM_ACTIVITY SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_MKT_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, 7, "1", false);
        assertEquals(2, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);

        assertEquals(2, hotelDto.getId());
        assertEquals("Transient", hotelDto.getName());
        assertEquals(new BigDecimal(75.60271).setScale(2, RoundingMode.DOWN), hotelDto.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(74.10578).setScale(2, RoundingMode.DOWN), hotelDto.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(705600.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(727200.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitForecast().setScale(2, RoundingMode.DOWN));

        hotelDto = businessAnalysisDataDetailsDto.get(1);

        assertEquals(1, hotelDto.getId());
        assertEquals("Group", hotelDto.getName());
        assertEquals(new BigDecimal(56.42995).setScale(2, RoundingMode.DOWN), hotelDto.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(56.00740).setScale(2, RoundingMode.DOWN), hotelDto.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(88200.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90900.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitForecast().setScale(2, RoundingMode.DOWN));

    }

    @Test
    void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtBusinessTypeIncludesLastYear_DisconMS_Not_Selected() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update MKT_ACCOM_ACTIVITY SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_MKT_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, true, false, false, 7, "1", false);
        assertEquals(2, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto hotelDto = businessAnalysisDataDetailsDto.get(0);

        assertEquals(2, hotelDto.getId());
        assertEquals("Transient", hotelDto.getName());
        assertEquals(new BigDecimal(77.67504).setScale(2, RoundingMode.DOWN), hotelDto.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(76.03514).setScale(2, RoundingMode.DOWN), hotelDto.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(529200.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(545400.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitForecast().setScale(2, RoundingMode.DOWN));

        hotelDto = businessAnalysisDataDetailsDto.get(1);

        assertEquals(1, hotelDto.getId());
        assertEquals("Group", hotelDto.getName());
        assertEquals(new BigDecimal(56.42995).setScale(2, RoundingMode.DOWN), hotelDto.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(56.00740).setScale(2, RoundingMode.DOWN), hotelDto.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(88200.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90900.00).setScale(2, RoundingMode.DOWN), hotelDto.getProfitForecast().setScale(2, RoundingMode.DOWN));
    }

    @Test
    public void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtForecastGroupIncludesLastYear() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update MKT_ACCOM_ACTIVITY SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_MKT_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        Integer businessTypeId = new Integer(2);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, 7, "1", false);

        assertEquals(7, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDTO = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(1), transientDTO.getId());
        assertEquals("Complementary", transientDTO.getName());

        assertEquals(new BigDecimal(92.16301).setScale(2, RoundingMode.DOWN), transientDTO.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(89.38054).setScale(2, RoundingMode.DOWN), transientDTO.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(88200.00).setScale(2, RoundingMode.DOWN), transientDTO.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90900.00).setScale(2, RoundingMode.DOWN), transientDTO.getProfitForecast().setScale(2, RoundingMode.DOWN));

    }

    @Test
    void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtForecastGroupIncludesLastYear_DisconMS_Not_Selected() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update MKT_ACCOM_ACTIVITY SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        setMktSegStatusDiscontinued(Arrays.asList(11, 4));
        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_MKT_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        Integer businessTypeId = new Integer(2);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, businessTypeId, 7, "1", false);

        assertEquals(6, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto transientDTO = businessAnalysisDataDetailsDto.get(1);
        assertEquals(new Integer(2), transientDTO.getId());
        assertEquals("Corporate", transientDTO.getName());

        assertEquals(new BigDecimal(77.16536).setScale(2, RoundingMode.DOWN), transientDTO.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(75.56110).setScale(2, RoundingMode.DOWN), transientDTO.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(88200.00).setScale(2, RoundingMode.DOWN), transientDTO.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(90900.00).setScale(2, RoundingMode.DOWN), transientDTO.getProfitForecast().setScale(2, RoundingMode.DOWN));

    }

    @Test
    public void getPropertyBusinessAnalysisProfitMetricsDataDetailDtosAtRoomClassIncludesLastYear() {
        Date businessDate = DateUtil.getFirstDayOfLastMonth();
        Date startDateLy = DateCalculator.calculateDateForLastYear(startDate, true);
        Date endDateLy = DateCalculator.calculateDateForLastYear(endDate, true);
        when(configService.getParameterValue(eq(PreProductionConfigParamName.SHOW_PROFIT_METRICS_AT_BUSINESS_ANALYSIS_DATA_DETAILS_SCREEN))).thenReturn(true);
        String addTotalProfit = "Update ACCOM_ACTIVITY SET Total_Profit =630 where Occupancy_Dt between :startDate and :endDate ";
        String addForecastProfit = "Update Occupancy_FCST SET Profit =720 where Occupancy_Dt between :startDate and :endDate ";

        Date businessDtLy = DateCalculator.calculateDateForLastYear(businessDate, true);
        String addPaceTotalProfit = "Update Pace_Accom_activity SET Total_Profit =510 where Occupancy_Dt between :startDate and :endDate and Business_Day_End_DT=:businessDTLy";
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addForecastProfit, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).parameters());
        tenantCrudService().executeUpdateByNativeQuery(addPaceTotalProfit, QueryParameter.with("startDate", startDateLy).and("endDate", endDateLy).and("businessDTLy", businessDtLy).parameters());

        String accomTypeStatusIds = "1,2";
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDto = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, true, 0, PhysicalOrComponent.PHYSICAL.getValue(), accomTypeStatusIds);


        //   assertEquals(7, businessAnalysisDataDetailsDto.size());

        BusinessAnalysisDataDetailsDto accomClassDTO = businessAnalysisDataDetailsDto.get(0);
        assertEquals(new Integer(2), accomClassDTO.getId());
        assertEquals("STD", accomClassDTO.getName());

        assertEquals(new BigDecimal(7.793).setScale(2, RoundingMode.DOWN), accomClassDTO.getProPOROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(22.24066).setScale(2, RoundingMode.DOWN), accomClassDTO.getProPORForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(52920.00).setScale(2, RoundingMode.DOWN), accomClassDTO.getProfitOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(158220.00).setScale(2, RoundingMode.DOWN), accomClassDTO.getProfitForecast().setScale(2, RoundingMode.DOWN));

        assertEquals(new BigDecimal(7.786).setScale(2, RoundingMode.DOWN), accomClassDTO.getProPAROnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(23.281341).setScale(2, RoundingMode.DOWN), accomClassDTO.getProPARForecast().setScale(2, RoundingMode.DOWN));

    }

    @Test
    public void getPricingAccomClass() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(25);
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        CrudService crudService = mock(CrudService.class);
        service.setCrudService(crudService);
        when(crudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", 25).parameters())).thenReturn(pricingAccomClass);

        PricingAccomClass result = service.getPricingAccomClass(accomClass);

        assertEquals(pricingAccomClass, result);
        assertEquals(pricingAccomClass.getAccomClass(), accomClass);
    }

    @Test
    void testGroupForecastGroupOverrides() {
        tenantCrudService().executeUpdateByNativeQuery("delete from GFF_FG_OVR");
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        insertOccupancyForecastGroupOverrides(startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), 12.0);
        insertOccupancyForecastGroupOverrides(endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), 13.0);
        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, 1, -1, 5);
        assertEquals(2, dailyIndicatorDtos.size());
        assertTrue(dailyIndicatorDtos.get(0).isGroupOccupancyForecastOverridden());
        assertTrue(dailyIndicatorDtos.get(1).isGroupOccupancyForecastOverridden());
    }

    private void insertOccupancyForecastGroupOverrides(java.time.LocalDate date, double override) {
        String insertQuery = "INSERT INTO GFF_FG_OVR\n" +
                "           (Decision_ID,Property_ID,Forecast_Group_ID,Occupancy_DT,FG_Occupancy_NBR_OVR,   " +
                " Expiration_DT,Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM ) VALUES\n" +
                " (" + 20 + "," + propertyId + "," + 1 + ",'" + date + "',"
                + override + "," + "NULL, 1, 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP)";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);
    }


    private void updateTransientPricingBaseAccomType() {
        tenantCrudService().executeUpdateByNativeQuery("Update CP_Cfg_Base_AT Set Sunday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Monday_Ceil_Rate_w_Tax = Monday_Ceil_Rate,Tuesday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Wednesday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Thursday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Friday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Saturday_Ceil_Rate_w_Tax = Sunday_Ceil_Rate,Sunday_Floor_Rate_w_Tax = Sunday_Floor_Rate,Monday_Floor_Rate_w_Tax = Monday_Floor_Rate,Tuesday_Floor_Rate_w_Tax = Tuesday_Floor_Rate,Wednesday_Floor_Rate_w_Tax = Wednesday_Floor_Rate,Thursday_Floor_Rate_w_Tax = Thursday_Floor_Rate,Friday_Floor_Rate_w_Tax = Friday_Floor_Rate,Saturday_Floor_Rate_w_Tax = Saturday_Floor_Rate");
    }

    private void setMktSegStatusDiscontinued(List<Integer> mktSegIds) {
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (:mktSegIds)"
                , QueryParameter.with("mktSegIds", mktSegIds).parameters());
    }

    private void updateMarketSegmentToExcluded(int value) {
        tenantCrudService().executeUpdateByNativeQuery(" Update Mkt_Seg set Exclude_CompHouse_Data_Display=" + value + " where Mkt_Seg_ID in (" + mktSeg + ") ");
    }

    private void verifyDetails(List<BusinessAnalysisDailyDataDto> dailyDataDtos1, List<BusinessAnalysisDailyDataDto> dailyDataDtos2) {
        for (int i = 0; i < dailyDataDtos1.size(); i++) {
            BusinessAnalysisDailyDataDto dailyDataDtos = dailyDataDtos1.get(i);
            BusinessAnalysisDailyDataDto dailyDataDtosWithoutExludedMS = dailyDataDtos2.get(i);
            assertTrue(dailyDataDtos.getOnBooks() >= dailyDataDtosWithoutExludedMS.getOnBooks());
            assertTrue(dailyDataDtos.getGroups() >= dailyDataDtosWithoutExludedMS.getGroups());
            assertTrue(dailyDataDtos.getAdr().compareTo(dailyDataDtosWithoutExludedMS.getAdr()) <= 0);
            assertTrue(dailyDataDtos.getRevpar().compareTo(dailyDataDtosWithoutExludedMS.getRevpar()) >= 0);
            assertTrue(dailyDataDtos.getOccupancyForecast().compareTo(dailyDataDtosWithoutExludedMS.getOccupancyForecast()) >= 0);
        }
    }

    private void verifyDataDetailsDto(List<BusinessAnalysisDataDetailsDto> dailyDataDtos1, List<BusinessAnalysisDataDetailsDto> dailyDataDtos2) {
        for (int i = 0; i < dailyDataDtos1.size(); i++) {
            BusinessAnalysisDataDetailsDto dailyDataDtos = dailyDataDtos1.get(i);
            BusinessAnalysisDataDetailsDto dailyDataDtosWithoutExludedMS = dailyDataDtos2.get(i);
            assertTrue(dailyDataDtos.getOnBooks().compareTo(dailyDataDtosWithoutExludedMS.getOnBooks()) >= 0);
            assertTrue(dailyDataDtos.getRevenue().compareTo(dailyDataDtosWithoutExludedMS.getRevenue()) >= 0);
            assertTrue(dailyDataDtos.getAdrOnBooks().compareTo(dailyDataDtosWithoutExludedMS.getAdrOnBooks()) <= 0);
            assertTrue(dailyDataDtos.getRevpar().compareTo(dailyDataDtosWithoutExludedMS.getRevpar()) >= 0);
            assertTrue(dailyDataDtos.getOccupancyForecast().compareTo(dailyDataDtosWithoutExludedMS.getOccupancyForecast()) >= 0);
        }
    }

    private void verifyPickupSTLYST2YData(List<BusinessAnalysisDataDetailsDto> dailyDataDetailsDtos) {
        for (int i = 0; i < dailyDataDetailsDtos.size(); i++) {
            BusinessAnalysisDataDetailsDto dailyDataDtos = dailyDataDetailsDtos.get(i);
            assertNull(dailyDataDtos.getAdrPickUp());
            assertNull(dailyDataDtos.getOnBooksPickUp());
            assertNull(dailyDataDtos.getRevenuePickUp());
            assertNull(dailyDataDtos.getLastYearOnBooks());
            assertNull(dailyDataDtos.getLastYearRevenue());
            assertNull(dailyDataDtos.getLastYearAdrOnBooks());
            assertNull(dailyDataDtos.getLastYearRevpar());
            assertNull(dailyDataDtos.getLast2YearsOnBooks());
            assertNull(dailyDataDtos.getLast2YearsRevenue());
            assertNull(dailyDataDtos.getLast2YearsAdrOnBooks());
            assertNull(dailyDataDtos.getLast2YearsRevpar());
        }
    }

    @Test
    void getPropertyBusinessAnalysisDataDetailDtosAtHotelForYear2019() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdateToPaceTotalActivity(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 7, false);
        assertEquals(1, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(250), businessAnalysisDataDetailsDto.getYear2019OnBooks());
        assertEquals(new BigDecimal(10000.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(40.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019AdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(20.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revpar().setScale(2, RoundingMode.DOWN));

    }

    @Test
    void getPropertyBusinessAnalysisDataDetailDtosYear2019VerifyRevpar() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdateToPaceTotalActivityForRevpar(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = service.getPropertyBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 7, false);
        assertEquals(1, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(600), businessAnalysisDataDetailsDto.getCapacity());
        assertEquals(new BigDecimal(0.22).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revpar().setScale(2, RoundingMode.DOWN));
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtBusinessTypeForYear2019() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdateToPaceMktActivity(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = service.getForecastGroupBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 7, "1", false);
        assertEquals(2, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(50), businessAnalysisDataDetailsDto.getYear2019OnBooks());
        assertEquals(new BigDecimal(1000.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(20.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019AdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertNull(businessAnalysisDataDetailsDto.getYear2019Revpar());

    }

    @Test
    void getPropertyBusinessAnalysisDataDetailDtosAtRoomClassForYear2019() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdatePaceAccomActivity(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 7, 0, "1", false);
        assertEquals(3, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(40), businessAnalysisDataDetailsDto.getYear2019OnBooks());
        assertEquals(new BigDecimal(1000.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(25.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019AdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(20.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revpar().setScale(2, RoundingMode.DOWN));
    }

    @Test
    public void getPropertyBusinessAnalysisDataDetailDtosAtRoomTypeForYear2019() {
        startDate = DateUtil.getCurrentDateWithoutTime();
        endDate = DateUtil.getCurrentDateWithoutTime();

        Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(startDate, endDate, DateUtil.getFirstDayOfLastMonth(), true);
        addOrUpdatePaceAccomActivity(new LocalDate(dates.get(0).get("startDate")).toString(), new LocalDate(dates.get(0).get("businessDate")).toString());

        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = service.getRoomClassBusinessAnalysisDataDetailDtos(startDate, endDate, false, false, true, 2, 7, 0, "1", false);
        assertEquals(3, businessAnalysisDataDetailsDtos.size());

        BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto = businessAnalysisDataDetailsDtos.get(0);
        assertEquals(new BigDecimal(40), businessAnalysisDataDetailsDto.getYear2019OnBooks());
        assertEquals(new BigDecimal(1000.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revenue().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(25.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019AdrOnBooks().setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(20.00).setScale(2, RoundingMode.DOWN), businessAnalysisDataDetailsDto.getYear2019Revpar().setScale(2, RoundingMode.DOWN));
    }

    private void addOrUpdateToPaceTotalActivity(String occupancyDate, String businessDate) {

        int i = tenantCrudService().findByNativeQuerySingleResult("select count(*) from pace_total_activity where Occupancy_DT = '" + occupancyDate + "' " +
                "and Business_Day_End_DT = '" + businessDate + "'", null);
        if (i == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Total_Activity values(5,'" + occupancyDate + "', GETDATE(), '" + businessDate + "', 500, 250, 0, 0, 300, 200, 0, 0, 10000.00, 2000.00, 12000.0, 1, 1, 1, GETDATE(), null)");
        }
    }

    private void addOrUpdateToPaceTotalActivityForRevpar(String occupancyDate, String businessDate) {

        int i = tenantCrudService().findByNativeQuerySingleResult("select count(*) from pace_total_activity where Occupancy_DT = '" + occupancyDate + "' " +
                "and Business_Day_End_DT = '" + businessDate + "'", null);
        if (i == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Total_Activity values(5,'" + occupancyDate + "', GETDATE(), '" + businessDate + "', 600, 1, 0, 0, 0, 0, 0, 0, 134.10, 0, 134.10, 1, 1, 1, GETDATE(), null)");
        }
    }

    private void addOrUpdateToPaceMktActivity(String occupancyDate, String businessDate) {

        int i = tenantCrudService().findByNativeQuerySingleResult("select count(*) from PACE_Mkt_Activity where Occupancy_DT = '" + occupancyDate + "' " +
                "and Business_Day_End_DT = '" + businessDate + "'", null);
        if (i == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Mkt_Activity values(5,'" + occupancyDate + "', GETDATE(), '" + businessDate + "', 1, 50, 30, 20, 0, 0, 1000.00, 200.00, 1200.0, 1, 1, 1, GETDATE(), 0, 0)");
        }
    }

    private void addOrUpdatePaceAccomActivity(String occupancyDate, String businessDate) {
        int i = tenantCrudService().findByNativeQuerySingleResult("select count(*) from PACE_Accom_Activity where Occupancy_DT = '" + occupancyDate + "' " +
                "and Business_Day_End_DT = '" + businessDate + "' and Accom_Type_ID = 6 ", null);
        if (i == 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Accom_Activity values(5,'" + occupancyDate + "', GETDATE(), '" + businessDate + "', 6, 50, 40, 0, 0, 25, 15, 0, 0, 1000.00, 200.00, 1200.0, 1, 1, 1, GETDATE(), null)");
        }
    }

    @Test
    public void getRoomTypeRateDtosForProducts() throws Exception {

        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        AccomType accomType = new AccomType();
        accomType.setId(5);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(4);
        accomType.setAccomClass(accomClass);

        Integer id = (Integer) tenantCrudService().findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(id, accomType, product, startDate);
        tenantCrudService().save(cpDecisionBAROutput);

        assertEquals("SUITE", service.getRoomTypeRateDtosForProducts(startDate, product, accomType).get(0).getRoomType());
        assertEquals(BigDecimal.TEN, service.getRoomTypeRateDtosForProducts(startDate, product, accomType).get(0).getRate().setScale(0));
    }

    @Test
    public void getBusinessAnalysisDailyDataDtosForCpProducts() throws Exception {
        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        AccomType accomType = new AccomType();
        accomType.setId(5);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(4);
        accomType.setAccomClass(accomClass);

        Integer id = (Integer) tenantCrudService().findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        List<CPDecisionBAROutput> cpDecisionBAROutputs = Arrays.asList(getCpDecisionBAROutput(id, accomType, product, startDate), getCpDecisionBAROutput(id, accomType, product, endDate));
        tenantCrudService().save(cpDecisionBAROutputs);

        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosForCpProducts(startDate, endDate, true, product, accomType);
        assertEquals(2, dailyDataDtos.size());
        assertEquals(startDate, dailyDataDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyDataDtos.get(dailyDataDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            verifyBusinessAnalysisDailyDataDto(dailyReferenceData);
        }
    }

    private void verifyBusinessAnalysisDailyDataDto(BusinessAnalysisDailyDataDto dailyReferenceData) {
        assertNotNull(dailyReferenceData.getDate());
        assertNotNull(dailyReferenceData.getAdr());
        assertNotNull(dailyReferenceData.getBarCode());
        assertEquals(BigDecimal.TEN, dailyReferenceData.getBarRate().setScale(0));
        assertNotNull(dailyReferenceData.getCapacity());
        assertNotNull(dailyReferenceData.getLrv());
        assertNotNull(dailyReferenceData.getOccupancyForecast());
        assertNotNull(dailyReferenceData.getOnBooks());
        assertNotNull(dailyReferenceData.getOutOfOrder());
        assertNotNull(dailyReferenceData.getOverbookings());
        assertNotNull(dailyReferenceData.getRevpar());
        assertNotNull(dailyReferenceData.getUnconstrainedRemainingDemand());
        assertNotNull(dailyReferenceData.getWash());
        assertNotNull(dailyReferenceData.getDecisionReasonTypeId());
    }

    private CPDecisionBAROutput getCpDecisionBAROutput(Integer id, AccomType accomType,
                                                       Product product, Date date) {
        return getCpDecisionBAROutput(id, accomType, product, date, DecisionReasonType.ALL_IS_WELL.getId(),
                DecisionOverrideType.NONE, null, null, null);
    }

    private CPDecisionBAROutput getCpDecisionBAROutput(Integer decisionId, AccomType accomType, Product product,
                                                       Date date, int reasonTypeId, DecisionOverrideType overrideType,
                                                       BigDecimal userOverride, BigDecimal floorOverride, BigDecimal ceilOverride) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setPropertyId(propertyId);
        cpDecisionBAROutput.setDecisionId(decisionId);
        cpDecisionBAROutput.setDecisionReasonTypeId(reasonTypeId);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setArrivalDate(LocalDate.fromDateFields(date));
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOverrideType(overrideType);
        cpDecisionBAROutput.setOptimalBAR(BigDecimal.TEN);
        cpDecisionBAROutput.setFinalBAR(BigDecimal.TEN);
        cpDecisionBAROutput.setSpecificOverride(userOverride);
        cpDecisionBAROutput.setFloorOverride(floorOverride);
        cpDecisionBAROutput.setCeilingOverride(ceilOverride);
        return cpDecisionBAROutput;
    }

    @Test
    public void getFloorAndCeilingForIndependentProduct() {
        updateTransientPricingBaseAccomType();
        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(2);

        Product independentProduct = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        AccomType accomType = new AccomType();
        accomType.setId(4);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomType.setAccomClass(accomClass);

        inserDataIntoCP_Cfg_Base_AT(accomType.getId(), independentProduct.getId());

        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosForCpProducts(startDate, endDate, true, independentProduct, accomType);
        assertEquals(2, dailyDataDtos.size());
        assertEquals(startDate, dailyDataDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyDataDtos.get(dailyDataDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            assertNotNull(dailyReferenceData.getUpperBound());
            assertNotNull(dailyReferenceData.getLowerBound());
        }
    }

    private void inserDataIntoCP_Cfg_Base_AT(int accomTypeId, int productId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + accomTypeId + " , null, null,  80.00,120.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "     170.00,210.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00," + productId + ")");
    }

    @Test
    public void getFloorAndCeilingForLinkedProducts() {
        updateTransientPricingBaseAccomType();
        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(2);
        Product linkedProduct = tenantCrudService().save(ProductBuilder.createAgileRateProduct("Linked"));
        Product linkedOptimizedProduct = tenantCrudService().save(ProductBuilder.createAgileRateProduct("LinkedOptimized"));
        AccomType accomType = new AccomType();
        accomType.setId(4);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomType.setAccomClass(accomClass);

        verifyLinkedProducts(linkedProduct, accomType);
        verifyLinkedProducts(linkedOptimizedProduct, accomType);
    }

    private void verifyLinkedProducts(Product linkedProduct, AccomType accomType) {
        List<BusinessAnalysisDailyDataDto> dailyDataDtos = service.getBusinessAnalysisDailyDataDtosForCpProducts(startDate, endDate, true, linkedProduct, accomType);
        assertEquals(2, dailyDataDtos.size());
        assertEquals(startDate, dailyDataDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyDataDtos.get(dailyDataDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyDataDto dailyReferenceData : dailyDataDtos) {
            assertNull(dailyReferenceData.getUpperBound());
            assertNull(dailyReferenceData.getLowerBound());
        }
    }

    @Test
    public void getBusinessAnalysisDailyIndicatorDtosForCpProductsWithUserSpecificOverride() throws Exception {
        startDate = DateUtil.getDateForCurrentMonth(1);
        endDate = DateUtil.getDateForCurrentMonth(2);
        Product independentProduct = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));

        insertIntoCPBarOutputOverRide(1, independentProduct.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "USER", 259.00000, 459.00000, null, null, null, null, 11);
        insertIntoCPBarOutputOverRide(1, independentProduct.getId(), 4, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "USER", 259.00000, 459.00000, null, null, null, null, 19);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, independentProduct.getId(), -1, 4);

        assertEquals(2, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyIndicatorDto dailyIndicatorDto : dailyIndicatorDtos) {
            assertNotNull(dailyIndicatorDto.getDate());
            assertTrue(dailyIndicatorDto.isUserBAROverriden());
            assertFalse(dailyIndicatorDto.isFloorBAROverriden());
            assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
        }
    }

    @Test
    public void getBusinessAnalysisDailyIndicatorDtosForCpProductsWithFloorCeilingOverride() throws Exception {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date secondDate = DateUtil.getDateForCurrentMonth(2);
        Date endDate = DateUtil.getDateForCurrentMonth(3);

        Product independentProduct = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));

        insertIntoCPBarOutputOverRide(1, independentProduct.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", 259.00000, null, null, 129.00000, null, null, 21);
        insertIntoCPBarOutputOverRide(1, independentProduct.getId(), 4, LocalDate.fromDateFields(secondDate), -1, 11403, "NONE", "CEIL", 259.00000, null, null, null, null, 899.00000, 25);
        insertIntoCPBarOutputOverRide(1, independentProduct.getId(), 4, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "FLOORANDCEIL", 259.00000, null, null, 129.00000, null, 899.00000, 29);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, independentProduct.getId(), -1, 4);

        assertEquals(3, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());

        for (BusinessAnalysisDailyIndicatorDto dailyIndicatorDto : dailyIndicatorDtos) {
            assertNotNull(dailyIndicatorDto.getDate());
            Date dailyIndicatorDtoDate = dailyIndicatorDto.getDate().getTime();
            if (dailyIndicatorDtoDate.compareTo(startDate) == 0) {
                assertFalse(dailyIndicatorDto.isUserBAROverriden());
                assertTrue(dailyIndicatorDto.isFloorBAROverriden());
                assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
            } else if (dailyIndicatorDtoDate.compareTo(secondDate) == 0) {
                assertFalse(dailyIndicatorDto.isUserBAROverriden());
                assertFalse(dailyIndicatorDto.isFloorBAROverriden());
                assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
            } else if (dailyIndicatorDtoDate.compareTo(endDate) == 0) {
                assertFalse(dailyIndicatorDto.isUserBAROverriden());
                assertTrue(dailyIndicatorDto.isFloorBAROverriden());
                assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
            }
        }
    }

    private void insertIntoCPBarOutputOverRide(int decisionId, int productId, int accomTypeId, LocalDate arrivalDate, int los,
                                               int userId, String oldOverride, String newOverride, Double oldBar, Double newBar, Double oldFloorRate, Double newFloorRate, Double oldCeilRate, Double newCeilRate, int addSecToCreateDate) {
        String insertQuery = "INSERT INTO CP_Decision_Bar_Output_OVR\n" +
                "           (Property_ID       ,    Decision_ID   ,    Product_ID   ,    Accom_Type_ID  ,   " +
                " Arrival_DT     ,    LOS    ,   User_ID    ,    Old_Override     ,     New_Override    ," +
                "    Old_BAR   ,   New_BAR    ,    Old_Floor_Rate  ,    New_Floor_Rate  ,    Old_Ceil_Rate  ,  New_Ceil_Rate ,  CreateDate) VALUES\n" +
                "           (" + propertyId + "," + decisionId + "," + productId + "," + accomTypeId + ",'"
                + arrivalDate + "'," + los + "," + userId + ",'" + oldOverride + "','" + newOverride + "',"
                + oldBar + "," + newBar + "," + oldFloorRate + "," + newFloorRate + "," + oldCeilRate + "," + newCeilRate + "," + "DATEADD(second," + addSecToCreateDate + ", GETDATE()) )";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithUserSpecificOverrideWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);

        Product product = getSystemDefaultProduct();
        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        insertIntoCPBarOutputOverRide(1, product.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "USER", 259.00000, 299.00000, null, null, null, null, 31);
        insertIntoCPBarOutputOverRide(1, product.getId(), 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "USER", 259.00000, 299.00000, null, null, null, null, 39);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, product.getId(), inv1.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, product.getId(), inv2.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertUserSpecificOverrideIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, product.getId(), -1, -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertUserSpecificOverrideIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithFloorOverrideWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);

        Product barProduct = getSystemDefaultProduct();

        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        insertIntoCPBarOutputOverRide(1, barProduct.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", null, null, null, 129.00000, null, null, 41);
        insertIntoCPBarOutputOverRide(1, barProduct.getId(), 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", null, null, null, 159.00000, null, null, 49);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), inv1.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), inv2.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertFloorOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), -1, -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertFloorOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithCeilOverrideWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);

        Product barProduct = getSystemDefaultProduct();

        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        Integer barProductId = barProduct.getId();
        insertIntoCPBarOutputOverRide(1, barProductId, 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 129.00000, 51);
        insertIntoCPBarOutputOverRide(1, barProductId, 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 159.00000, 59);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProductId, inv1.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProductId, inv2.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProductId, -1, -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithFloorAndCeilOverrideWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);

        Product barProduct = getSystemDefaultProduct();

        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        insertIntoCPBarOutputOverRide(1, barProduct.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", null, null, null, 129.00000, null, 159.00000, 62);
        insertIntoCPBarOutputOverRide(1, barProduct.getId(), 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", null, null, null, 149.00000, null, 199.00000, 69);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), inv1.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), inv2.getId(), -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertFloorAndCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, startDate, barProduct.getId(), -1, -1);
        assertEquals(1, dailyIndicatorDtos.size());
        assertFloorAndCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithFloorCeilOverridesWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date secondDate = DateUtil.getDateForCurrentMonth(2);
        Date thirdDate = DateUtil.getDateForCurrentMonth(3);
        Date forthDate = DateUtil.getDateForCurrentMonth(4);
        Date endDate = DateUtil.getDateForCurrentMonth(5);

        Product barProduct = getSystemDefaultProduct();
        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        Integer barProductId = barProduct.getId();
        insertIntoCPBarOutputOverRide(1, barProductId, 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", null, null, null, 129.00000, null, 199.00000, 71);
        insertIntoCPBarOutputOverRide(1, barProductId, 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", null, null, null, 159.00000, null, null, 75);
        insertIntoCPBarOutputOverRide(1, barProductId, 4, LocalDate.fromDateFields(secondDate), -1, 11403, "NONE", "FLOOR", null, null, null, 129.00000, null, null, 79);
        insertIntoCPBarOutputOverRide(1, barProductId, 5, LocalDate.fromDateFields(secondDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 299.00000, 83);
        insertIntoCPBarOutputOverRide(1, barProductId, 5, LocalDate.fromDateFields(thirdDate), -1, 11403, "NONE", "FLOOR", null, null, null, 159.00000, null, null, 87);
        insertIntoCPBarOutputOverRide(1, barProductId, 6, LocalDate.fromDateFields(thirdDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 299.00000, 91);
        insertIntoCPBarOutputOverRide(1, barProductId, 6, LocalDate.fromDateFields(forthDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 299.00000, 95);
        insertIntoCPBarOutputOverRide(1, barProductId, 6, LocalDate.fromDateFields(forthDate), -1, 11403, "CEIL", "FLOOR", null, null, null, 159.00000, 299.00000, null, 99);
        insertIntoCPBarOutputOverRide(1, barProductId, 6, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "FLOOR", null, null, null, 129.00000, null, null, 103);
        insertIntoCPBarOutputOverRide(1, barProductId, 6, LocalDate.fromDateFields(endDate), -1, 11403, "FLOOR", "FLOORANDCEIL", null, null, 129.00000, 159.00000, null, 299.00000, 109);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, barProductId, inv1.getId(), -1);
        assertEquals(5, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertOverrideNotInInventoryGrp(secondDate, dailyIndicatorDtos.get(1));
        assertCeilOverrideInInventoryGrp(thirdDate, dailyIndicatorDtos.get(2));
        assertFloorOverrideInInventoryGrp(forthDate, dailyIndicatorDtos.get(3));
        assertFloorAndCeilOverrideInInventoryGrp(endDate, dailyIndicatorDtos.get(4));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, barProductId, inv2.getId(), -1);
        assertEquals(5, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());
        assertFloorAndCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertFloorAndCeilOverrideInInventoryGrp(secondDate, dailyIndicatorDtos.get(1));
        assertFloorOverrideInInventoryGrp(thirdDate, dailyIndicatorDtos.get(2));
        assertOverrideNotInInventoryGrp(forthDate, dailyIndicatorDtos.get(3));
        assertOverrideNotInInventoryGrp(endDate, dailyIndicatorDtos.get(4));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, barProductId, -1, -1);
        assertEquals(5, dailyIndicatorDtos.size());
        assertEquals(startDate, dailyIndicatorDtos.get(0).getDate().getTime());
        assertEquals(endDate, dailyIndicatorDtos.get(dailyIndicatorDtos.size() - 1).getDate().getTime());
        assertFloorAndCeilOverrideInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertFloorAndCeilOverrideInInventoryGrp(secondDate, dailyIndicatorDtos.get(1));
        assertFloorAndCeilOverrideInInventoryGrp(thirdDate, dailyIndicatorDtos.get(2));
        assertFloorOverrideInInventoryGrp(forthDate, dailyIndicatorDtos.get(3));
        assertFloorAndCeilOverrideInInventoryGrp(endDate, dailyIndicatorDtos.get(4));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithUserSpecificAndFloorCeilOverridesWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);

        Product product = getSystemDefaultProduct();
        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        insertIntoCPBarOutputOverRide(1, product.getId(), 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "USER", 259.00000, 299.00000, null, null, null, null, 43);
        insertIntoCPBarOutputOverRide(1, product.getId(), 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", null, null, null, 129.00000, null, 199.00000, 47);
        insertIntoCPBarOutputOverRide(1, product.getId(), 4, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "FLOOR", null, null, null, 159.00000, null, null, 53);
        insertIntoCPBarOutputOverRide(1, product.getId(), 5, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "CEIL", null, null, null, null, null, 299.00000, 57);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), inv1.getId(), -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertOverrideNotInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertOverrideNotInInventoryGrp(endDate, dailyIndicatorDtos.get(1));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), inv2.getId(), -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertUserSpecificFloorCeilOverridesIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertFloorAndCeilOverrideInInventoryGrp(endDate, dailyIndicatorDtos.get(1));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), -1, -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertUserSpecificFloorCeilOverridesIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertFloorAndCeilOverrideInInventoryGrp(endDate, dailyIndicatorDtos.get(1));
    }

    @Test
    void getBusinessAnalysisDailyIndicatorDtosForCPWithGroupFloorOverrideWithMultiInventoryGroups() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);

        Product product = getSystemDefaultProduct();
        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);
        AccomClass steAccomClass = getAccomClass(4);

        AccomType dlxAccomType = getAccomType(4, "DLX");
        dlxAccomType.setAccomClass(getAccomClass(3));

        AccomType steAccomType = getAccomType(5, "STE");
        steAccomType.setAccomClass(steAccomClass);

        AccomType stdAccomType = getAccomType(6, "DBL");
        stdAccomType.setAccomClass(getAccomClass(2));

        InventoryGroup inv1 = getInventory(stdAccomClass, "STD");
        getInventoryGroupDetails(stdAccomClass, inv1);

        InventoryGroup inv2 = getInventory(dlxAccomClass, "DLXSTE");
        getInventoryGroupDetails(dlxAccomClass, inv2);
        getInventoryGroupDetails(steAccomClass, inv2);

        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output_OVR");

        CPDecisionBAROutput cpDecisionBAROutput1 = getCpDecisionBAROutput(1, steAccomType, product, startDate, DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.GPFLOOR, null, BigDecimal.valueOf(199.00), null);
        cpDecisionBAROutput1.setFinalBAR(BigDecimal.valueOf(199.00));
        CPDecisionBAROutput cpDecisionBAROutput2 = getCpDecisionBAROutput(1, dlxAccomType, product, endDate, DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.GPFLOORANDCEIL, null, BigDecimal.valueOf(299.00), BigDecimal.valueOf(499.00));
        cpDecisionBAROutput2.setFinalBAR(BigDecimal.valueOf(299.00));
        CPDecisionBAROutput cpDecisionBAROutput3 = getCpDecisionBAROutput(1, stdAccomType, product, startDate, DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.GPFLOOR, null, BigDecimal.valueOf(159.00), null);
        cpDecisionBAROutput3.setFinalBAR(BigDecimal.valueOf(159.00));
        tenantCrudService.save(List.of(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        insertIntoCPBarOutputOverRide(1, product.getId(), steAccomType.getId(), LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "GPFLOOR", null, null, null, 199.00, null, null, 32);
        insertIntoCPBarOutputOverRide(1, product.getId(), dlxAccomType.getId(), LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "GPFLOORANDCEIl", null, null, null, 299.00, null, 499.00, 38);
        insertIntoCPBarOutputOverRide(1, product.getId(), stdAccomType.getId(), LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "GPFLOOR", null, null, null, 159.00, null, null, 44);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), inv1.getId(), -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertGroupFloorOverrideIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertOverrideNotInInventoryGrp(endDate, dailyIndicatorDtos.get(1));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), inv2.getId(), -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertGroupFloorOverrideIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertGroupFloorAndCeilingOverrideIndicatorsInInventoryGrp(endDate, dailyIndicatorDtos.get(1));

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), -1, -1);
        assertEquals(2, dailyIndicatorDtos.size());
        assertGroupFloorOverrideIndicatorsInInventoryGrp(startDate, dailyIndicatorDtos.get(0));
        assertGroupFloorAndCeilingOverrideIndicatorsInInventoryGrp(endDate, dailyIndicatorDtos.get(1));

    }


    private void assertOverrideNotInInventoryGrp(Date startDate, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(startDate, dailyIndicatorDto.getDate().getTime());
        assertFalse(dailyIndicatorDto.isUserBAROverriden());
        assertFalse(dailyIndicatorDto.isFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
        assertFalse(dailyIndicatorDto.isGroupFloorBAROverriden());
    }

    private void assertFloorOverrideInInventoryGrp(Date startDate, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(startDate, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
        assertFalse(dailyIndicatorDto.isUserBAROverriden());
    }

    private void assertCeilOverrideInInventoryGrp(Date startDate, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(startDate, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
        assertFalse(dailyIndicatorDto.isFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isUserBAROverriden());
    }

    private void assertFloorAndCeilOverrideInInventoryGrp(Date startDate, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(startDate, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isFloorBAROverriden());
        assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
        assertFalse(dailyIndicatorDto.isUserBAROverriden());
    }

    private void assertUserSpecificOverrideIndicatorsInInventoryGrp(Date date, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(date, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isUserBAROverriden());
        assertFalse(dailyIndicatorDto.isFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
    }

    private void assertUserSpecificFloorCeilOverridesIndicatorsInInventoryGrp(Date date, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(date, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isUserBAROverriden());
        assertTrue(dailyIndicatorDto.isFloorBAROverriden());
        assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
    }

    private void assertGroupFloorOverrideIndicatorsInInventoryGrp(Date date, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(date, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isGroupFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isCeilingBAROverriden());
    }

    private void assertGroupFloorAndCeilingOverrideIndicatorsInInventoryGrp(Date date, BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        assertEquals(date, dailyIndicatorDto.getDate().getTime());
        assertTrue(dailyIndicatorDto.isGroupFloorBAROverriden());
        assertFalse(dailyIndicatorDto.isFloorBAROverriden());
        assertTrue(dailyIndicatorDto.isCeilingBAROverriden());
    }

    @Test
    void testOverriddenAccomClassDataForBarProduct() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = getSystemDefaultProduct();
        AccomType accomType = getAccomType(5, "SUITE");
        accomType.setAccomClass(getAccomClass(4));

        AccomType accomType1 = getAccomType(6, "DOUBLE");
        AccomClass accomClass1 = getAccomClass(2);
        accomType1.setAccomClass(accomClass1);

        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        CPDecisionBAROutput cpDecisionBAROutput1 = getCpDecisionBAROutput(1, accomType, product, startDate,
                DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.USER, BigDecimal.valueOf(144.00), null, null);

        CPDecisionBAROutput cpDecisionBAROutput2 = getCpDecisionBAROutput(1, accomType1, product, startDate,
                DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.CEIL, null, null, BigDecimal.valueOf(88.00));
        CPDecisionBAROutput cpDecisionBAROutput3 = getCpDecisionBAROutput(1, accomType1, product, endDate,
                DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.FLOOR, null, BigDecimal.valueOf(92.00), null);

        CPDecisionBAROutput cpDecisionBAROutput4 = getCpDecisionBAROutput(1, accomType, product, endDate,
                DecisionReasonType.ALL_IS_WELL.getId(), DecisionOverrideType.FLOORANDCEIL, null, BigDecimal.valueOf(87.00), BigDecimal.valueOf(201.00));
        tenantCrudService.save(List.of(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4));

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(startDate, endDate, product, true, -1, -1);

        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(startDate);
        assertEquals("STE-144.00", acOverrideSummary.getUserOverriddenAccomClasses());
        assertEquals("STD-88.00", acOverrideSummary.getCeilOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(endDate);
        assertEquals("STD-92.00\nSTE-87.00", acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STE-201.00", acOverrideSummary.getCeilOverriddenAccomClasses());
    }

    @Test
    void testOverriddenAccomClassesForIndependentProduct() {
        Date firstDate = DateUtil.getDateForCurrentMonth(1);
        Date secondDate = DateUtil.getDateForCurrentMonth(3);
        Product product = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("Idp1"));

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType1 = getAccomType(6, "DOUBLE");
        AccomType stdAccomType2 = getAccomType(7, "QUEEN");
        AccomClass stdAccomClass = getAccomClass(2);
        stdAccomType1.setAccomClass(stdAccomClass);
        stdAccomType2.setAccomClass(stdAccomClass);

        Integer maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        int decisionReasonTypeId = DecisionReasonType.ALL_IS_WELL.getId();

        tenantCrudService.deleteAll(CPDecisionBAROutput.class);
        CPDecisionBAROutput userOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, product, firstDate,
                decisionReasonTypeId, DecisionOverrideType.USER, BigDecimal.valueOf(144.00), null, null);
        CPDecisionBAROutput floorOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, product, firstDate,
                decisionReasonTypeId, DecisionOverrideType.CEIL, null, null, BigDecimal.valueOf(88.00));
        CPDecisionBAROutput gpFloorOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType2, product, firstDate,
                decisionReasonTypeId, DecisionOverrideType.GPFLOOR, null, BigDecimal.valueOf(99.00), null);
        CPDecisionBAROutput floorOverrideForEndDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, product, secondDate,
                decisionReasonTypeId, DecisionOverrideType.FLOOR, null, BigDecimal.valueOf(92.00), null);

        CPDecisionBAROutput floorAndCeillOverrideForEndDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, product, secondDate,
                decisionReasonTypeId, DecisionOverrideType.FLOORANDCEIL, null, BigDecimal.valueOf(87.00), BigDecimal.valueOf(201.00));

        tenantCrudService.save(List.of(userOverrideForStartDate, gpFloorOverrideForStartDate,
                floorOverrideForStartDate, floorOverrideForEndDate, floorAndCeillOverrideForEndDate));

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(
                firstDate, secondDate, product, true, -1, -1);

        assertNotNull(overrideData);
        assertEquals(3, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(firstDate);
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertEquals("STE-144.00", acOverrideSummary.getUserOverriddenAccomClasses());
        assertEquals("STD-88.00", acOverrideSummary.getCeilOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(secondDate);
        assertEquals("STD-92.00\nSTE-87.00", acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STE-201.00", acOverrideSummary.getCeilOverriddenAccomClasses());
    }

    @Test
    void testOverriddenAccomClassDataForGroupFloorForBarProduct() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = getSystemDefaultProduct();

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType = getAccomType(6, "DOUBLE");
        stdAccomType.setAccomClass(getAccomClass(2));

        addGroupFloorOverridesData(startDate, endDate, product, steAccomType, stdAccomType);

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(startDate, endDate, product, true, -1, -1);

        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(startDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STD-89.00\nSTE-99.00", acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertEquals("STD-199.00", acOverrideSummary.getCeilOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(endDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STE-87.00", acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertEquals("STE-201.00", acOverrideSummary.getCeilOverriddenAccomClasses());
    }

    private CPDecisionBAROutput getCpDecisionBAROutput(Date startDate, Product product, AccomType steAccomType,
                                                       DecisionOverrideType overrideType,
                                                       Double floorOverride, Double ceilOverride) {

        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(1, steAccomType, product, startDate,
                DecisionReasonType.ALL_IS_WELL.getId(), overrideType, null,
                BigDecimal.valueOf(floorOverride), BigDecimal.valueOf(ceilOverride));
        cpDecisionBAROutput.setFinalBAR(BigDecimal.valueOf(floorOverride));
        insertIntoCPBarOutputOverRide(1, product.getId(), steAccomType.getId(), LocalDate.fromDateFields(startDate),
                -1, 11403, "NONE", overrideType.name(), null, null, null,
                floorOverride, null, ceilOverride, 15);
        return cpDecisionBAROutput;
    }


    @Test
    void testLRVGreaterThanPriceAccomClasses() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("Idp1"));

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType1 = getAccomType(6, "DOUBLE");
        AccomClass stdAccomClass = getAccomClass(2);
        stdAccomType1.setAccomClass(stdAccomClass);

        Integer maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID,
                QueryParameter.with("propertyId", propertyId).parameters());

        int decisionReasonType = DecisionReasonType.LRV_GT_BAR.getId();
        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        CPDecisionBAROutput lrvGreaterThanPriceForStartDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, product, startDate,
                decisionReasonType, DecisionOverrideType.NONE, null, null, null);

        CPDecisionBAROutput lrvLessThanPriceForStartDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, product, startDate);

        CPDecisionBAROutput lrvGreaterThanPriceStdForEndDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, product, endDate,
                decisionReasonType, DecisionOverrideType.NONE, null, null, null);

        CPDecisionBAROutput lrvGreaterThanPriceSteForEndDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, product, endDate,
                decisionReasonType, DecisionOverrideType.FLOORANDCEIL, null, BigDecimal.valueOf(87.00), BigDecimal.valueOf(201.00));

        tenantCrudService.save(Arrays.asList(lrvGreaterThanPriceForStartDate, lrvLessThanPriceForStartDate,
                lrvGreaterThanPriceStdForEndDate, lrvGreaterThanPriceSteForEndDate));

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(
                startDate, endDate, product, true, -1, -1);

        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto summary1 = overrideData.get(startDate);
        assertEquals("STE", summary1.getLrvGreaterThanPriceAccomClasses());

        BusinessAnalysisIndicatorAccomClassDto summary2 = overrideData.get(endDate);
        assertEquals("STD\nSTE", summary2.getLrvGreaterThanPriceAccomClasses());
    }

    @Test
    void testOverriddenAccomClassesForNonCPProperty() {

        Date arrivalDate = DateUtil.getDateWithoutTime(17, 2, 2023);
        Date startDate = DateUtil.addDaysToDate(arrivalDate, -1);
        Date endDate = DateUtil.addDaysToDate(arrivalDate, 1);
        createTestDataForDecisionBarOutputOverrides(arrivalDate);

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> accomClassOverrideData = service.getAccomClassOverrideData(startDate, endDate, getSystemDefaultProduct(), false, -1, -1);

        BusinessAnalysisIndicatorAccomClassDto summary = accomClassOverrideData.get(arrivalDate);
        assertEquals("DLX-1008.00", summary.getUserOverriddenAccomClasses());
        assertEquals("STD-119.00", summary.getFloorOverriddenAccomClasses());

        BusinessAnalysisIndicatorAccomClassDto summary1 = accomClassOverrideData.get(DateUtil.addDaysToDate(arrivalDate, 1));
        assertEquals("STD\nDLX", summary1.getLrvGreaterThanPriceAccomClasses());
        assertNull(summary1.getUserOverriddenAccomClasses());
        assertNull(summary1.getFloorOverriddenAccomClasses());
        assertEquals("STD-129.00\nDLX-648.00", summary1.getCeilOverriddenAccomClasses());

        BusinessAnalysisIndicatorAccomClassDto summary2 = accomClassOverrideData.get(DateUtil.addDaysToDate(arrivalDate, -1));
        assertNull(summary2.getUserOverriddenAccomClasses());
        assertEquals("DLX", summary2.getLrvGreaterThanPriceAccomClasses());
        assertEquals("STD-119.00", summary2.getFloorOverriddenAccomClasses());
        assertEquals("STD-129.00\nDLX-729.00", summary2.getCeilOverriddenAccomClasses());
    }

    private void createTestDataForDecisionBarOutputOverrides(Date today) {
        AccomClass stdAccomClass = getAccomClass(2);
        AccomClass dlxAccomClass = getAccomClass(3);

        Integer decisionId = tenantCrudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", propertyId).parameters());
        Decision decision = tenantCrudService.find(Decision.class, decisionId);

        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR");
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output");
        createBAROutputAndOverrideForAccomClass(decision, today, stdAccomClass, Constants.BARDECISIONOVERRIDE_FLOOR, DecisionReasonType.ALL_IS_WELL.getId());
        createBAROutputAndOverrideForAccomClass(decision, today, dlxAccomClass, Constants.BARDECISIONOVERRIDE_USER,
                DecisionReasonType.ALL_IS_WELL.getId());
        createBAROutputAndOverrideForAccomClass(decision, DateUtil.addDaysToDate(today, -1), stdAccomClass,
                Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, DecisionReasonType.ALL_IS_WELL.getId());
        createBAROutputAndOverrideForAccomClass(decision, DateUtil.addDaysToDate(today, -1), dlxAccomClass,
                Constants.BARDECISIONOVERRIDE_CEILING, DecisionReasonType.LRV_GT_BAR.getId());
        createBAROutputAndOverrideForAccomClass(decision, DateUtil.addDaysToDate(today, 1), stdAccomClass,
                Constants.BARDECISIONOVERRIDE_CEILING, DecisionReasonType.LRV_GT_BAR.getId());
        createBAROutputAndOverrideForAccomClass(decision, DateUtil.addDaysToDate(today, 1), dlxAccomClass,
                Constants.BARDECISIONOVERRIDE_CEILING, DecisionReasonType.LRV_GT_BAR.getId());
    }

    private void createBAROutputAndOverrideForAccomClass(Decision decision, Date arrivalDate, AccomClass accomClass, String override,
                                                         int decisionReasonTypeId) {

        RateUnqualified user = tenantCrudService.find(RateUnqualified.class, 8);
        RateUnqualified ceiling = tenantCrudService.find(RateUnqualified.class, 11);
        RateUnqualified floor = tenantCrudService.find(RateUnqualified.class, 12);
        DecisionBAROutput barOutput = new DecisionBAROutput();
        barOutput.setPropertyID(propertyId);
        barOutput.setDecision(decision);
        barOutput.setArrivalDate(arrivalDate);
        barOutput.setAccomClassId(accomClass.getId());
        barOutput.setCreateDate(new Date());
        barOutput.setLengthOfStay(-1);
        barOutput.setRateUnqualified(user);
        barOutput.setFloorRateUnqualified(null);
        barOutput.setOverride(override);
        barOutput.setFloorRateUnqualified(floor);
        barOutput.setCeilingRateUnqualified(ceiling);
        barOutput.setReasonTypeId(decisionReasonTypeId);
        tenantCrudService.save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(override);
        barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
        barOutputOverride.setNewFloorRateUnqualified(floor);
        barOutputOverride.setNewCeilingRateUnqualified(ceiling);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService.save(barOutputOverride);
    }

    AccomType getAccomType(int accomTypeId, String accomTypeName) {
        AccomType accomType = new AccomType();
        accomType.setId(accomTypeId);
        accomType.setName(accomTypeName);
        return accomType;
    }

    AccomClass getAccomClass(int accomClassId) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(accomClassId);
        return accomClass;
    }

    @Test
    void assertBADIndictorsForBARProductWithCPEnabled() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);

        Product barProduct = getSystemDefaultProduct();
        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        Integer productId = barProduct.getId();
        insertIntoCPBarOutputOverRide(1, productId, 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", 259.00000, null, null, 129.00000, null, null, 21);
        insertIntoCPBarOutputOverRide(1, productId, 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", 259.00000, null, null, null, null, 899.00000, 25);
        insertIntoCPBarOutputOverRide(1, productId, 4, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "USER", 259.00000, null, null, null, null, 899.00000, 30);
        insertIntoCPBarOutputOverRide(1, productId, 5, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "CEIL", 259.00000, null, null, null, null, 899.00000, 35);

        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB)).thenReturn(false);
        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtos(startDate, endDate, -1);

        assertEquals(2, dailyIndicatorDtos.size());
        assertOverrideIndicators(dailyIndicatorDtos.get(0), false, true, true, false);
        assertOverrideIndicators(dailyIndicatorDtos.get(1), true, false, true, false);
    }

    @Test
    void assertBADIndictorsWhenIndependentProductWithAllOverrides() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date secondDate = DateUtil.getDateForCurrentMonth(2);
        Date endDate = DateUtil.getDateForCurrentMonth(3);

        Product independentProduct = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("IND1"));

        Integer productId = independentProduct.getId();
        insertIntoCPBarOutputOverRide(1, productId, 4, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOOR", 259.00000, null, null, 129.00000, null, null, 21);
        insertIntoCPBarOutputOverRide(1, productId, 5, LocalDate.fromDateFields(startDate), -1, 11403, "NONE", "FLOORANDCEIL", 259.00000, null, null, null, null, 899.00000, 25);
        insertIntoCPBarOutputOverRide(1, productId, 5, LocalDate.fromDateFields(secondDate), -1, 11403, "NONE", "USER", 259.00000, null, null, null, null, 899.00000, 30);
        insertIntoCPBarOutputOverRide(1, productId, 4, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "GPFLOOR", 259.00000, null, null, null, null, 899.00000, 35);
        insertIntoCPBarOutputOverRide(1, productId, 5, LocalDate.fromDateFields(endDate), -1, 11403, "NONE", "CEIL", 259.00000, null, null, null, null, 899.00000, 40);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, productId, -1, 5);

        assertEquals(3, dailyIndicatorDtos.size());

        assertOverrideIndicators(dailyIndicatorDtos.get(0), false, true, true, false);
        assertOverrideIndicators(dailyIndicatorDtos.get(1), true, false, false, false);
        assertOverrideIndicators(dailyIndicatorDtos.get(2), false, false, true, false);

    }

    @Test
    @Disabled
    @Tag("businessAnalysisDashboard-flaky")
    void assertBADIndictorsWhenIndependentProductWithGPFloorOverride() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("Idp1"));

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType = getAccomType(6, "DOUBLE");
        stdAccomType.setAccomClass(getAccomClass(2));

        addGroupFloorOverridesData(startDate, endDate, product, steAccomType, stdAccomType);

        List<BusinessAnalysisDailyIndicatorDto> dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), -1, 6);

        assertEquals(2, dailyIndicatorDtos.size());
        assertOverrideIndicators(dailyIndicatorDtos.get(0), false, false, true, true);
        assertOverrideIndicators(dailyIndicatorDtos.get(1), false, false, false, false);

        dailyIndicatorDtos = service.getBusinessAnalysisDailyIndicatorDtosForContinuousPricing(startDate, endDate, product.getId(), -1, 5);

        assertEquals(2, dailyIndicatorDtos.size());
        assertOverrideIndicators(dailyIndicatorDtos.get(0), false, false, false, true);
        assertOverrideIndicators(dailyIndicatorDtos.get(1), false, false, true, true);

    }

    private static void assertOverrideIndicators(BusinessAnalysisDailyIndicatorDto dailyIndicatorDto, boolean userOvr, boolean floorOvr,
                                                 boolean ceilOvr, boolean grpOvr) {
        assertEquals(userOvr, dailyIndicatorDto.isUserBAROverriden());
        assertEquals(floorOvr, dailyIndicatorDto.isFloorBAROverriden());
        assertEquals(ceilOvr, dailyIndicatorDto.isCeilingBAROverriden());
        assertEquals(grpOvr, dailyIndicatorDto.isGroupFloorBAROverriden());
    }

    @Test
    void testOverriddenAccomClassesForIndependentProductForAccomType() {
        Date firstDate = DateUtil.getDateForCurrentMonth(1);
        Date secondDate = DateUtil.getDateForCurrentMonth(2);
        Product independentProduct = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("Idp1"));

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType1 = getAccomType(6, "DOUBLE");
        AccomType stdAccomType2 = getAccomType(7, "QUEEN");
        AccomClass stdAccomClass = getAccomClass(2);
        stdAccomType1.setAccomClass(stdAccomClass);
        stdAccomType2.setAccomClass(stdAccomClass);

        Integer maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        int decisionReasonTypeId = DecisionReasonType.ALL_IS_WELL.getId();

        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        CPDecisionBAROutput userOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, independentProduct, firstDate,
                decisionReasonTypeId, DecisionOverrideType.USER, BigDecimal.valueOf(144.00), null, null);
        CPDecisionBAROutput floorOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, independentProduct, firstDate,
                decisionReasonTypeId, DecisionOverrideType.CEIL, null, null, BigDecimal.valueOf(88.00));
        CPDecisionBAROutput gpFloorOverrideForStartDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType2, independentProduct, firstDate,
                decisionReasonTypeId, DecisionOverrideType.GPFLOOR, null, BigDecimal.valueOf(99.00), null);
        CPDecisionBAROutput floorOverrideForEndDate = getCpDecisionBAROutput(maxDecisionId, stdAccomType1, independentProduct, secondDate,
                decisionReasonTypeId, DecisionOverrideType.FLOOR, null, BigDecimal.valueOf(92.00), null);

        CPDecisionBAROutput floorAndCeillOverrideForEndDate = getCpDecisionBAROutput(maxDecisionId, steAccomType, independentProduct, secondDate,
                decisionReasonTypeId, DecisionOverrideType.FLOORANDCEIL, null, BigDecimal.valueOf(87.00), BigDecimal.valueOf(201.00));

        tenantCrudService.save(List.of(userOverrideForStartDate, gpFloorOverrideForStartDate,
                floorOverrideForStartDate, floorOverrideForEndDate, floorAndCeillOverrideForEndDate));

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(
                firstDate, secondDate, independentProduct, true, -1, 6);

        asserOverridesForStdAccomType(firstDate, secondDate, overrideData);

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideDataForSteAccomType = service.getAccomClassOverrideData(
                firstDate, secondDate, independentProduct, true, -1, 5);
        asserOverridesForSteAccomType(firstDate, secondDate, overrideDataForSteAccomType);

    }

    private static void asserOverridesForStdAccomType(Date firstDate, Date secondDate, Map<Date,
            BusinessAnalysisIndicatorAccomClassDto> overrideData) {
        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(firstDate);
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertEquals("STD-88.00", acOverrideSummary.getCeilOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(secondDate);
        assertNull(acOverrideSummary.getCeilOverriddenAccomClasses());
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertEquals("STD-92.00", acOverrideSummary.getFloorOverriddenAccomClasses());
    }

    private static void asserOverridesForSteAccomType(Date firstDate, Date secondDate, Map<Date,
            BusinessAnalysisIndicatorAccomClassDto> overrideData) {
        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(firstDate);
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getCeilOverriddenAccomClasses());
        assertEquals("STE-144.00", acOverrideSummary.getUserOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(secondDate);
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertEquals("STE-87.00", acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STE-201.00", acOverrideSummary.getCeilOverriddenAccomClasses());
    }

    @Test
    void testOverriddenACDataForGPFloorForIndependentProductWithAccomType() {
        Date startDate = DateUtil.getDateForCurrentMonth(1);
        Date endDate = DateUtil.getDateForCurrentMonth(2);
        Product product = tenantCrudService.save(ProductBuilder.createIndependentProductProduct("Idp1"));

        AccomType steAccomType = getAccomType(5, "SUITE");
        steAccomType.setAccomClass(getAccomClass(4));

        AccomType stdAccomType = getAccomType(6, "DOUBLE");
        stdAccomType.setAccomClass(getAccomClass(2));

        addGroupFloorOverridesData(startDate, endDate, product, steAccomType, stdAccomType);

        Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData = service.getAccomClassOverrideData(startDate, endDate, product, true, -1, 6);

        assertGPFloorStdAccomType(startDate, endDate, overrideData);

        overrideData = service.getAccomClassOverrideData(startDate, endDate, product, true, -1, 5);

        assertGPFloorSteAccomType(startDate, endDate, overrideData);
    }

    private void addGroupFloorOverridesData(Date startDate, Date endDate, Product product, AccomType steAccomType, AccomType stdAccomType) {
        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output");
        tenantCrudService().executeUpdateByNativeQuery("delete from CP_Decision_Bar_Output_OVR");
        CPDecisionBAROutput cpDecisionBAROutput1 = getCpDecisionBAROutput(startDate, product, steAccomType,
                DecisionOverrideType.GPFLOOR, 99.00, 0.00);
        CPDecisionBAROutput cpDecisionBAROutput2 = getCpDecisionBAROutput(startDate, product, stdAccomType,
                DecisionOverrideType.GPFLOORANDCEIL, 89.00, 199.00);

        CPDecisionBAROutput cpDecisionBAROutput3 = getCpDecisionBAROutput(endDate, product, steAccomType,
                DecisionOverrideType.GPFLOORANDCEIL, 87.00, 201.00);
        tenantCrudService.save(List.of(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));
    }

    private static void assertGPFloorStdAccomType(Date startDate, Date endDate, Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData) {
        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(startDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STD-89.00", acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertEquals("STD-199.00", acOverrideSummary.getCeilOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(endDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getCeilOverriddenAccomClasses());
        assertNull(acOverrideSummary.getGroupFloorOverriddenAccomClasses());
    }

    private static void assertGPFloorSteAccomType(Date startDate, Date endDate, Map<Date, BusinessAnalysisIndicatorAccomClassDto> overrideData) {
        assertNotNull(overrideData);
        assertEquals(2, overrideData.size());
        BusinessAnalysisIndicatorAccomClassDto acOverrideSummary = overrideData.get(startDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertNull(acOverrideSummary.getCeilOverriddenAccomClasses());
        assertEquals("STE-99.00", acOverrideSummary.getGroupFloorOverriddenAccomClasses());

        acOverrideSummary = overrideData.get(endDate);
        assertNull(acOverrideSummary.getUserOverriddenAccomClasses());
        assertNull(acOverrideSummary.getFloorOverriddenAccomClasses());
        assertEquals("STE-87.00", acOverrideSummary.getGroupFloorOverriddenAccomClasses());
        assertEquals("STE-201.00", acOverrideSummary.getCeilOverriddenAccomClasses());

    }

    private Integer addUserForecastConfigForBusinessType() {
        BudgetLevel budgetLevel = getBudgetLevelBusinessType();
        if (null == budgetLevel) {
            budgetLevel = createBudgetLevel("Business Type");
        }
        BudgetConfig budgetConfig = createBudgetConfig(budgetLevel);
        return budgetLevel.getId();
    }

    private BudgetLevel createBudgetLevel(String level) {
        BudgetLevel budgetLevel = new BudgetLevel();
        budgetLevel.setBudgetLevel(level);
        budgetLevel.setCreatedDTTM(new Date());
        return tenantCrudService().save(budgetLevel);
    }

    private BudgetConfig createBudgetConfig(BudgetLevel budgetLevel) {
        BudgetConfig budgetConfig = new BudgetConfig();
        budgetConfig.setModuleName("client.user.forecast");
        BudgetConfig byExampleSingleResult = tenantCrudService.findByExampleSingleResult(budgetConfig);
        if(byExampleSingleResult == null) {
            budgetConfig.setBudgetLevel(budgetLevel);
            budgetConfig.setBudgetDisplayName("User Forecast");
            return tenantCrudService.save(budgetConfig);
        }
        return byExampleSingleResult;
    }


}
