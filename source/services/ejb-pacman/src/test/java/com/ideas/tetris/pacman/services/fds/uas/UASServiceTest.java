package com.ideas.tetris.pacman.services.fds.uas;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttribute;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttributeValue;
import com.ideas.tetris.pacman.services.fds.producers.FDSRestTemplateProvider;
import com.ideas.tetris.pacman.services.fds.uas.model.UASAuthGroup;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRole;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRoleSearch;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRuleDetails;
import com.ideas.tetris.pacman.services.fds.uis.UISEntitlementService;
import com.ideas.tetris.pacman.services.fds.uis.model.entitlement.EntitlementV2;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rules.*;
import com.ideas.tetris.pacman.services.security.*;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroupPropertyMapping;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class UASServiceTest {

    @Mock
    private FDSRestTemplateProvider fdsRestTemplateProvider;

    @Mock
    private RestTemplate uasRestTemplate;

    @Mock
    private ClientPropertyCacheService cacheService;

    @Mock
    private RoleService roleService;

    @Mock
    private AuthorizationService authorizationService;

    @Mock
    private PropertyService propertyService;

    @Mock
    private RulesService rulesService;

    @Mock
    private UISEntitlementService uisEntitlementService;

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private CrudService crudService;

    @Mock
    private UserService userService;

    @InjectMocks
    UASService service;

    @BeforeEach
    void setup() {
        when(fdsRestTemplateProvider.uasRestTemplate()).thenReturn(uasRestTemplate);
    }

    private static final String CLIENT_CODE = "Hilton";
    private static final String INTERNAL_CLIENT_CODE = "ideas";
    private static final String INTERNAL_CLIENT_UUID = "cce74be1-e839-4c7b-be38-64307e29785e";
    private static final String INTERNAL_ALL_AUTH_GROUPS_UUID = "f9d34d8b-b099-4148-9d0b-b5272942861e";
    private static final String INTERNAL_ALL_PERMISSIONS_ROLE_UUID = "fc69a60f-0418-453e-873f-e266ed05d61f";
    private static final String CLIENT_ID = "083019b2-04cc-4aed-bf25-5b2397004dff";
    private static final String ROLE_ID = "183019b2-04cc-4aed-bf25-5b2397004dff";
    private static final String G3_PRODUCT_ENVIRONMENT_ID = "68622f84-7aac-4463-b43d-ea39f7163a9d";
    private static final String UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID = "643602fa-a3f6-43e9-9b8b-31a6bddf4e4a";
    private static final String OPTIX_PRODUCT_ENVIRONMENT_ID = "1471fb4e-7a66-47e6-a325-0b2afe48123d";
    private static final String NAVIGATOR_PRODUCT_ENVIRONMENT_ID = "8f1c0f61-e4de-4f60-ad09-9e0a0b60ab8e";
    private static final String SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID = "66c7b95b-5657-4b1c-87bc-f0d212208d5a";
    private static final String READ_WRITE_ACCESS = "readWrite";
    private static final String READ_ONLY_ACCESS = "readOnly";
    private static final String NO_ACCESS = "noAccess";

    private Client client;
    private Client client2;
    private Client internalClient;

    @BeforeEach
    void setUp() {
        //setup client for role migration
        client = new Client();
        client.setId(1);
        client.setUpsClientUuid(CLIENT_ID);
        client.setCode(CLIENT_CODE);

        client2 = new Client();
        client2.setId(2);
        client2.setUpsClientUuid(null);
        client2.setCode(CLIENT_CODE);

        internalClient = new Client();
        internalClient.setId(3);
        internalClient.setUpsClientUuid(INTERNAL_CLIENT_UUID);
        internalClient.setCode(INTERNAL_CLIENT_CODE);
    }

    @Test
    void savePermissionDefinitions() {
        List<PermissionDefinition> definitions = new ArrayList<>();
        definitions.add(PermissionDefinition
                .builder()
                .id(UUID.randomUUID())
                .build());
        PermissionDefinition[] definitionsArray = new PermissionDefinition[definitions.size()];
        definitionsArray = definitions.toArray(definitionsArray);
        when(uasRestTemplate.exchange("/v1/permissions", HttpMethod.POST, new HttpEntity<>(definitions), PermissionDefinition[].class)).thenReturn(ResponseEntity.ok(definitionsArray));
        service.savePermissionDefinitions(definitions);
    }

    @Test
    void savePermissionDefinitionsFailed() {
        List<PermissionDefinition> definitions = new ArrayList<>();
        definitions.add(PermissionDefinition
                .builder()
                .id(UUID.randomUUID())
                .build());
        when(uasRestTemplate.exchange("/v1/permissions", HttpMethod.POST, new HttpEntity<>(definitions), PermissionDefinition[].class)).thenReturn(ResponseEntity.badRequest().build());
        List<PermissionDefinition> permissionDefinitions = service.savePermissionDefinitions(definitions);
        assertTrue(permissionDefinitions.isEmpty());
    }

    @Test
    void getPermissionDefinitions() {
        List<PermissionDefinition> definitions = new ArrayList<>();
        definitions.add(PermissionDefinition
                .builder()
                .id(UUID.randomUUID())
                .build());
        PermissionDefinition[] definitionsArray = new PermissionDefinition[definitions.size()];
        definitionsArray = definitions.toArray(definitionsArray);
        when(uasRestTemplate.getForEntity("/v1/permissions/byProductEnvironment/68622f84-7aac-4463-b43d-ea39f7163a9d", PermissionDefinition[].class)).thenReturn(ResponseEntity.ok(definitionsArray));
        List<PermissionDefinition> permissionDefinitions = service.getPermissionDefinitions();
        assertEquals(definitions, permissionDefinitions);
    }

    @Test
    void getPermissionDefinitionsFailed() {
        List<PermissionDefinition> definitions = new ArrayList<>();
        definitions.add(PermissionDefinition
                .builder()
                .id(UUID.randomUUID())
                .build());
        when(uasRestTemplate.getForEntity("/v1/permissions/g3", PermissionDefinition[].class)).thenReturn(ResponseEntity.badRequest().build());
        List<PermissionDefinition> permissionDefinitions = service.getPermissionDefinitions();
        assertTrue(permissionDefinitions.isEmpty());
    }

    @Test
    void migrateRoleAssignmentsToFDSForClient() {
        GlobalUser user = new GlobalUser();
        user.setClientCode(CLIENT_CODE);
        String userUUID = UUID.randomUUID().toString();
        user.setCognitoUserId(userUUID);
        user.setId(1);

        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);
        List<EntitlementV2> userEntitlements = new ArrayList<>();
        EntitlementV2 bothEntitlement = new EntitlementV2("role1", "authGroup1", Arrays.asList("property1"));
        userEntitlements.add(bothEntitlement);
        EntitlementV2 authGroupEntitlement = new EntitlementV2("role2", "authGroup2");
        userEntitlements.add(authGroupEntitlement);
        EntitlementV2 propertyEntitlement = new EntitlementV2("role3", Arrays.asList("property3"));
        userEntitlements.add(propertyEntitlement);
        EntitlementV2 roleEntitlement = new EntitlementV2("role4");
        userEntitlements.add(roleEntitlement);
        Mockito.when(roleService.getUserRolesForFDS(user.getId(), client.getUpsClientUuid())).thenReturn(userEntitlements);
        Mockito.when(userService.pullCurrentEnvironmentRoleEntitlementsFromExistingUser(userUUID, client.getUpsClientUuid())).thenReturn(new ArrayList<>());

        service.migrateRoleAssignmentsToFDSForClient(List.of(user));

        verify(userService).pullCurrentEnvironmentRoleEntitlementsFromExistingUser(userUUID, client.getUpsClientUuid());
        verify(cacheService).getClient(CLIENT_CODE);
        verify(roleService).getUserRolesForFDS(user.getId(), client.getUpsClientUuid());
        verify(uisEntitlementService).addRoleAssignments(user.getCognitoUserId(), userEntitlements);
    }

    @Test
    void migrateRoleAssignmentsToFDSForInternalClient() {
        GlobalUser user = new GlobalUser();
        user.setClientCode(INTERNAL_CLIENT_CODE);
        String userUUID = UUID.randomUUID().toString();
        user.setCognitoUserId(userUUID);
        user.setId(1);

        Mockito.when(cacheService.getClient(INTERNAL_CLIENT_CODE)).thenReturn(internalClient);
        List<EntitlementV2> userEntitlements = new ArrayList<>();
        EntitlementV2 bothEntitlement = new EntitlementV2("role1", "authGroup1", Arrays.asList("property1"));
        userEntitlements.add(bothEntitlement);
        EntitlementV2 authGroupEntitlement = new EntitlementV2("role2", "authGroup2");
        userEntitlements.add(authGroupEntitlement);
        EntitlementV2 propertyEntitlement = new EntitlementV2("role3", Arrays.asList("property3"));
        userEntitlements.add(propertyEntitlement);
        EntitlementV2 roleEntitlement = new EntitlementV2("role4");
        userEntitlements.add(roleEntitlement);
        Mockito.when(roleService.getUserRolesForFDS(user.getId(), internalClient.getUpsClientUuid())).thenReturn(userEntitlements);
        Mockito.when(userService.pullCurrentEnvironmentRoleEntitlementsFromExistingUser(userUUID, internalClient.getUpsClientUuid())).thenReturn(new ArrayList<>());

        service.migrateRoleAssignmentsToFDSForClient(List.of(user));

        verify(userService).pullCurrentEnvironmentRoleEntitlementsFromExistingUser(userUUID, internalClient.getUpsClientUuid());
        verify(roleService).getUserRolesForFDS(user.getId(), internalClient.getUpsClientUuid());
        verify(uisEntitlementService).addRoleAssignments(user.getCognitoUserId(), userEntitlements);
    }

    @Test
    void migrateRoleAssignmentsToFDSForClient_CognitoUserIdIsNull() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client2);

        GlobalUser user = new GlobalUser();
        user.setEmail("<EMAIL>");
        user.setClientCode(CLIENT_CODE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.migrateRoleAssignmentsToFDSForClient(List.of(user)));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertTrue(tetrisException.getBaseMessage().startsWith("The Cognito User UUID is null; The user for email: " + user.getEmail() + " needs to exist in FDS"));

        verify(cacheService).getClient(CLIENT_CODE);
        verify(uasRestTemplate, never()).delete("/v2/authorities/{clientId}/g3", CLIENT_ID);
    }

    @Test
    void migrateRoleAssignmentsToFDSForClient_ClientUUIDisNull() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client2);

        GlobalUser user = new GlobalUser();
        user.setClientCode(CLIENT_CODE);
        user.setCognitoUserId(UUID.randomUUID().toString());

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.migrateRoleAssignmentsToFDSForClient(List.of(user)));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertTrue(tetrisException.getBaseMessage().startsWith("The UPS Client UUID is null; The client for clientCode: " + client.getCode() + " needs to exist in UPS"));

        verify(cacheService).getClient(CLIENT_CODE);
        verify(uasRestTemplate, never()).delete("/v2/authorities/{clientId}/g3", CLIENT_ID);
    }

    @Test
    void deleteRolesInFDSForClientUUIDAndProductEnvironmentUUID() {
        Set<Role> roles = new HashSet<>();
        Role role1 = new Role();
        String roleUUID1 = UUID.randomUUID().toString();
        role1.setUasRoleDictionaryUuid(roleUUID1);
        roles.add(role1);
        Role role2 = new Role();
        String roleUUID2 = UUID.randomUUID().toString();
        role2.setUasRoleDictionaryUuid(roleUUID2);
        roles.add(role2);
        Role role3 = new Role();
        role3.setUasRoleDictionaryUuid(null);
        roles.add(role3);
        when(roleService.getAllRoles(CLIENT_CODE)).thenReturn(roles);

        service.deleteRolesInFDSForClientAndProductEnvironmentUUID(CLIENT_CODE, G3_PRODUCT_ENVIRONMENT_ID);
        verify(uasRestTemplate).delete("/v2/roles/{roleId}/byProductEnvironment/{productEnvironmentId}", roleUUID1, G3_PRODUCT_ENVIRONMENT_ID);
        verify(uasRestTemplate).delete("/v2/roles/{roleId}/byProductEnvironment/{productEnvironmentId}", roleUUID2, G3_PRODUCT_ENVIRONMENT_ID);
        verify(uasRestTemplate, times(2)).delete(anyString(), anyString(), anyString());
    }

    @Test
    void deleteRoleInFDS() {
        GlobalRole globalRole = new GlobalRole();
        globalRole.setClientCode(CLIENT_CODE);
        UUID uuid = UUID.randomUUID();
        globalRole.setUasRoleDictionaryUuid(uuid.toString());

        service.deleteRoleInFDS(globalRole);
        verify(uasRestTemplate).delete("/v2/roles/{roleId}", uuid.toString());
    }

    @Test
    void getRoleFromFDS() {
        String roleId = UUID.randomUUID().toString();

        service.getRoleFromFDS(roleId);
        verify(uasRestTemplate).getForObject("/v2/roles/{roleId}", UASRole.class, roleId);
    }

    @Test
    void getRolesFromFDS() {
        String clientId = UUID.randomUUID().toString();
        HashMap<String, Object> body = new HashMap<>();
        body.put("anyClientIdIn", Arrays.asList(clientId));
        body.put("numItems", 100);

        UASRoleSearch uasRoleSearch = new UASRoleSearch();
        UASRole uasRole = new UASRole();
        uasRole.setRoleId(UUID.randomUUID());
        uasRole.setName("test");
        uasRoleSearch.setItems(Arrays.asList(uasRole));
        when(fdsRestTemplateProvider.uasRestTemplate().postForObject("/v2/roles/searches", body, UASRoleSearch.class)).thenReturn(uasRoleSearch);

        service.getRolesFromFDS(clientId);
        verify(uasRestTemplate).postForObject("/v2/roles/searches", body, UASRoleSearch.class);
    }

    @Test
    void migrateRolesToFDSForClient() {
        Role role1 = createRole("role1", ROLE_ID, CLIENT_CODE);
        Set<Role> rolesSet = new HashSet<>(Arrays.asList(role1));
        Mockito.when(roleService.getRoleFromGlobal(CLIENT_CODE)).thenReturn(rolesSet);

        HashMap<String, Object> body = createRoleMap("role1", CLIENT_ID, false, UUID.fromString(ROLE_ID));
        UASRole uasRole1 = new UASRole();
        uasRole1.setName("role1");
        uasRole1.setRoleId(UUID.fromString(ROLE_ID));

        Mockito.when(uasRestTemplate.postForObject("/v2/roles/migrate", body, UASRole.class)).thenReturn(uasRole1);
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);
        service.migrateRolesToFDSForClientCode(CLIENT_CODE);

        verify(cacheService).getClient(CLIENT_CODE);
        verify(uasRestTemplate, never()).delete("/v2/roles/{clientId}/{productEnvironmentId}", CLIENT_ID, G3_PRODUCT_ENVIRONMENT_ID);
        verify(roleService).getRoleFromGlobal(CLIENT_CODE);
        verify(uasRestTemplate).postForObject("/v2/roles/migrate", body, UASRole.class);
        verify(roleService).persistRole(role1);
    }

    @Test
    void migrateRolesToFDSForClient_Internal() {
        Role role1 = createRole("role1", ROLE_ID, Constants.CLIENT_INTERNAL);
        Set<Role> rolesSet = new HashSet<>(Arrays.asList(role1));
        Mockito.when(roleService.getRoleFromGlobal(Constants.CLIENT_INTERNAL)).thenReturn(rolesSet);

        HashMap<String, Object> body = createRoleMap("role1", Constants.CLIENT_INTERNAL_UUID, true, UUID.fromString(ROLE_ID));
        UASRole uasRole1 = new UASRole();
        uasRole1.setName("role1");
        uasRole1.setRoleId(UUID.fromString(ROLE_ID));

        Mockito.when(uasRestTemplate.postForObject("/v2/roles/migrate", body, UASRole.class)).thenReturn(uasRole1);
        service.migrateRolesToFDSForClientCode(Constants.CLIENT_INTERNAL);

        verify(cacheService, never()).getClient(Constants.CLIENT_INTERNAL);
        verify(uasRestTemplate, never()).delete("/v2/roles/{clientId}/{productEnvironmentId}", Constants.CLIENT_INTERNAL_UUID, G3_PRODUCT_ENVIRONMENT_ID);
        verify(roleService).getRoleFromGlobal(Constants.CLIENT_INTERNAL);
        verify(uasRestTemplate).postForObject("/v2/roles/migrate", body, UASRole.class);
        verify(roleService).persistRole(role1);
    }

    @Test
    void migrateRolesToFDSForClient_ClientUUIDisNull() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client2);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.migrateRolesToFDSForClientCode(CLIENT_CODE));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertTrue(tetrisException.getBaseMessage().startsWith("The UPS Client UUID is null; The client for clientCode: " + client.getCode() + " needs to exist in UPS"));

        verify(cacheService).getClient(CLIENT_CODE);
        verify(uasRestTemplate, never()).delete("/v2/roles/{clientId}/{productEnvironmentId}", CLIENT_ID, G3_PRODUCT_ENVIRONMENT_ID);
        verify(roleService, never()).getRoleFromGlobal(CLIENT_CODE);
        verify(uasRestTemplate, never()).postForObject(anyString(), any(), any());
        verify(roleService, never()).persistRole(any());
    }

    @Test
    void saveRoleInFDS() {
        GlobalRole globalRole = createGlobalRole("role1", ROLE_ID);

        HashMap<String, Object> body = createRoleMap("role1", CLIENT_ID, false, UUID.fromString(ROLE_ID));
        UASRole uasRole1 = new UASRole();
        uasRole1.setName("role1");
        uasRole1.setRoleId(UUID.fromString(ROLE_ID));

        Mockito.when(uasRestTemplate.postForObject("/v2/roles/migrate", body, UASRole.class)).thenReturn(uasRole1);
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        service.saveRoleInFDS(globalRole);
        verify(cacheService).getClient(CLIENT_CODE);
        verify(uasRestTemplate).postForObject("/v2/roles/migrate", body, UASRole.class);
    }

    @Test
    void saveRoleInFDS_Internal() {
        GlobalRole globalRole = createGlobalRole("role1", ROLE_ID);
        globalRole.setClientCode(Constants.CLIENT_INTERNAL);

        HashMap<String, Object> body = createRoleMap("role1", Constants.CLIENT_INTERNAL_UUID, true, UUID.fromString(ROLE_ID));
        UASRole uasRole1 = new UASRole();
        uasRole1.setName("role1");
        uasRole1.setRoleId(UUID.fromString(ROLE_ID));

        Mockito.when(uasRestTemplate.postForObject("/v2/roles/migrate", body, UASRole.class)).thenReturn(uasRole1);

        service.saveRoleInFDS(globalRole);
        verify(cacheService, never()).getClient(CLIENT_CODE);
        verify(uasRestTemplate).postForObject("/v2/roles/migrate", body, UASRole.class);
    }

    public static Role createRole(String name, String roleId, String clientCode) {
        Role role = new Role();
        role.setUniqueIdentifier("123");
        role.setRoleName(name);
        role.setClientCode(clientCode);
        role.setDescription("TestRoleDescription");
        role.setCorporate(true);
        role.setViewAnnouncements(false);
        role.setRoleRanking(1);
        role.setPermissions(new ArrayList<>());
        role.setUasRoleDictionaryUuid(roleId);
        return role;
    }

    public static GlobalRole createGlobalRole(String name, String roleId) {
        GlobalRole role = new GlobalRole();
        role.setId(123);
        role.setRoleName(name);
        role.setClientCode(CLIENT_CODE);
        role.setDescription("TestRoleDescription");
        role.setCorporate(true);
        role.setViewAnnouncements(false);
        role.setRoleRanking(1);
        role.setPermissions(null);
        role.setUasRoleDictionaryUuid(roleId);
        return role;
    }

    public static HashMap<String, Object> createRoleMap(String name, String clientUUID, boolean isInternal, UUID roleId) {
        HashMap<String, Object> roleBody = new HashMap<>();
        roleBody.put("roleId", roleId);
        roleBody.put("clientId", UUID.fromString(clientUUID));
        roleBody.put("name", name);
        roleBody.put("description", "TestRoleDescription");
        roleBody.put("productId", "g3");
        roleBody.put("corporateUser", true);
        roleBody.put("internalUser", isInternal);
        roleBody.put("viewAnnouncement", false);
        roleBody.put("roleRanking", 1);
        HashMap<String, HashMap<String, String>> permissionsMap = new HashMap<>();
        roleBody.put("permissions", permissionsMap);

        return roleBody;
    }

    @Test
    void createRole_setIdTrue() {
        UUID clientId = UUID.randomUUID();
        Role role = createRole("TestRoleName", ROLE_ID, CLIENT_CODE);

        HashMap<String, HashMap<String, String>> expectedPermissionsMap = new HashMap<>();

        HashMap<String, Object> result = service.createRole(clientId, role);
        Assertions.assertEquals(UUID.fromString(ROLE_ID), result.get("roleId"));
        Assertions.assertEquals(clientId, result.get("clientId"));
        Assertions.assertEquals("TestRoleName", result.get("name"));
        Assertions.assertEquals("TestRoleDescription", result.get("description"));
        Assertions.assertEquals(true, result.get("corporateUser"));
        Assertions.assertEquals(false, result.get("internalUser"));
        Assertions.assertEquals(false, result.get("viewAnnouncement"));
        Assertions.assertEquals(1, result.get("roleRanking"));
        Assertions.assertEquals(expectedPermissionsMap, result.get("permissions"));
    }

    @Test
    void createRole_setIdFalse() {
        UUID clientId = UUID.randomUUID();
        Role role = createRole("TestRoleName", ROLE_ID, CLIENT_CODE);
        role.setClientCode("ideas");
        role.setUasRoleDictionaryUuid(null);

        HashMap<String, HashMap<String, String>> expectedPermissionsMap = new HashMap<>();

        HashMap<String, Object> result = service.createRole(clientId, role);
        assertNull(result.get("roleId"));
        Assertions.assertEquals(clientId, result.get("clientId"));
        Assertions.assertEquals("TestRoleName", result.get("name"));
        Assertions.assertEquals("TestRoleDescription", result.get("description"));
        Assertions.assertEquals(true, result.get("corporateUser"));
        Assertions.assertEquals(true, result.get("internalUser"));
        Assertions.assertEquals(false, result.get("viewAnnouncement"));
        Assertions.assertEquals(1, result.get("roleRanking"));
        Assertions.assertEquals(expectedPermissionsMap, result.get("permissions"));
    }

    @Test
    void flattenPermissions_G3Only() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");
        //valid page code with no functionalities
        permissionList.add("pageCode=booking-pace-report&access=readOnly");
        //valid page code with invalid functionalities
        permissionList.add("pageCode=information-manager&access=readOnly&functions={alerts:readOnly,invalidFunction:readOnly,exceptions:readOnly}");
        //invalid page code
        permissionList.add("pageCode=unknown-page&access=readWrite");
        //nested pages
        permissionList.add("pageCode=agile-rates&access=readWrite&functions={agileRatesDefinition:readWrite,agileRatesDefaults:readWrite,agileRatesSeasons:noAccess,agileRatesRestrictions:readOnly}");
        permissionList.add("pageCode=pricing-config&access=readWrite");
        permissionList.add("pageCode=pricing-product-list&access=readWrite");
        permissionList.add("pageCode=pricing-advanced-settings&access=readWrite");
        permissionList.add("pageCode=pricing-config-centrally-managed&access=readWrite");
        permissionList.add("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}");
        permissionList.add("pageCode=reset-property&access=readWrite");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(1, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(22, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("booking-pace-report"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("information-manager"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("alerts"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("exceptions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agile-rates"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefinition"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefaults"));
        assertEquals(NO_ACCESS, g3Result.get("agileRatesSeasons"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("agileRatesRestrictions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-product-list"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-advanced-settings"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-config-centrally-managed"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-configuration"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationDefinitionStep"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationTransientCeilingAndFloor"));
        assertNull(g3Result.get("invalidFunction"));
        assertNull(g3Result.get("unknown-page"));
    }

    @Test
    void flattenPermissions_G3AndUADAndSpecialEvents() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");
        //valid page code with no functionalities
        permissionList.add("pageCode=booking-pace-report&access=readOnly");
        //valid page code with invalid functionalities
        permissionList.add("pageCode=information-manager&access=readOnly&functions={alerts:readOnly,invalidFunction:readOnly,exceptions:readOnly}");
        //invalid page code
        permissionList.add("pageCode=unknown-page&access=readWrite");
        //nested pages
        permissionList.add("pageCode=agile-rates&access=readWrite&functions={agileRatesDefinition:readWrite,agileRatesDefaults:readWrite,agileRatesSeasons:noAccess,agileRatesRestrictions:readOnly}");
        permissionList.add("pageCode=pricing-config&access=readWrite");
        permissionList.add("pageCode=pricing-product-list&access=readWrite");
        permissionList.add("pageCode=pricing-advanced-settings&access=readWrite");
        permissionList.add("pageCode=pricing-config-centrally-managed&access=readWrite");
        permissionList.add("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}");
        permissionList.add("pageCode=reset-property&access=readWrite");
        //UAD pages
        permissionList.add("pageCode=authorization-group-management&access=readOnly");
        permissionList.add("pageCode=role-management&access=readWrite&functions={role-ranking:readOnly}");
        permissionList.add("pageCode=user-management&access=readOnly&functions={user-management-activate-deactivate:readOnly,user-management-save:readOnly}");
        permissionList.add("pageCode=property-attribute-assignments&access=readWrite");
        permissionList.add("pageCode=property-attributes&access=readWrite");
        permissionList.add("pageCode=system-announcements&access=readWrite");
        //Special Events pages
        permissionList.add("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}");
        permissionList.add("pageCode=special-event-upload&access=readWrite");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(3, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(22, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("booking-pace-report"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("information-manager"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("alerts"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("exceptions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agile-rates"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefinition"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefaults"));
        assertEquals(NO_ACCESS, g3Result.get("agileRatesSeasons"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("agileRatesRestrictions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-product-list"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-advanced-settings"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-config-centrally-managed"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-configuration"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationDefinitionStep"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationTransientCeilingAndFloor"));
        assertNull(g3Result.get("invalidFunction"));
        assertNull(g3Result.get("unknown-page"));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(9, uadResult.size());
        assertEquals(READ_ONLY_ACCESS, uadResult.get("authorization-group-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("role-ranking"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-activate-deactivate"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-save"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attributes"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attribute-assignments"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("system-announcements"));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(3, specialEventsResult.size());
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get("special-events-management"));
        assertEquals(READ_ONLY_ACCESS, specialEventsResult.get("specialEventsAddNewDelete"));
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get("special-event-upload"));
    }

    @Test
    void flattenPermissions_G3AndUAD_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");
        //valid page code with no functionalities
        permissionList.add("pageCode=booking-pace-report&access=readOnly");
        //valid page code with invalid functionalities
        permissionList.add("pageCode=information-manager&access=readOnly&functions={alerts:readOnly,invalidFunction:readOnly,exceptions:readOnly}");
        //invalid page code
        permissionList.add("pageCode=unknown-page&access=readWrite");
        //nested pages
        permissionList.add("pageCode=agile-rates&access=readWrite&functions={agileRatesDefinition:readWrite,agileRatesDefaults:readWrite,agileRatesSeasons:noAccess,agileRatesRestrictions:readOnly}");
        permissionList.add("pageCode=pricing-config&access=readWrite");
        permissionList.add("pageCode=pricing-product-list&access=readWrite");
        permissionList.add("pageCode=pricing-advanced-settings&access=readWrite");
        permissionList.add("pageCode=pricing-config-centrally-managed&access=readWrite");
        permissionList.add("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}");
        permissionList.add("pageCode=reset-property&access=readWrite");
        //UAD pages
        permissionList.add("pageCode=authorization-group-management&access=readOnly");
        permissionList.add("pageCode=role-management&access=readWrite&functions={role-ranking:readOnly}");
        permissionList.add("pageCode=user-management&access=readOnly&functions={user-management-activate-deactivate:readOnly,user-management-save:readOnly}");
        permissionList.add("pageCode=property-attribute-assignments&access=readWrite");
        permissionList.add("pageCode=property-attributes&access=readWrite");
        permissionList.add("pageCode=system-announcements&access=readWrite");
        //Special Events pages
        permissionList.add("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}");
        permissionList.add("pageCode=special-event-upload&access=readWrite");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(2, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(25, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("booking-pace-report"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("information-manager"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("alerts"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("exceptions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agile-rates"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefinition"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefaults"));
        assertEquals(NO_ACCESS, g3Result.get("agileRatesSeasons"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("agileRatesRestrictions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-product-list"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-advanced-settings"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-config-centrally-managed"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-configuration"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationDefinitionStep"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationTransientCeilingAndFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("special-events-management"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("specialEventsAddNewDelete"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("special-event-upload"));
        assertNull(g3Result.get("invalidFunction"));
        assertNull(g3Result.get("unknown-page"));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(9, uadResult.size());
        assertEquals(READ_ONLY_ACCESS, uadResult.get("authorization-group-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("role-ranking"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-activate-deactivate"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-save"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attributes"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attribute-assignments"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("system-announcements"));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_G3OptixNavigatorAndSpecialEvents() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");
        //valid page code with no functionalities
        permissionList.add("pageCode=booking-pace-report&access=readOnly");
        //valid page code with invalid functionalities
        permissionList.add("pageCode=information-manager&access=readOnly&functions={alerts:readOnly,invalidFunction:readOnly,exceptions:readOnly}");
        //invalid page code
        permissionList.add("pageCode=unknown-page&access=readWrite");
        //nested pages
        permissionList.add("pageCode=agile-rates&access=readWrite&functions={agileRatesDefinition:readWrite,agileRatesDefaults:readWrite,agileRatesSeasons:noAccess,agileRatesRestrictions:readOnly}");
        permissionList.add("pageCode=pricing-config&access=readWrite");
        permissionList.add("pageCode=pricing-product-list&access=readWrite");
        permissionList.add("pageCode=pricing-advanced-settings&access=readWrite");
        permissionList.add("pageCode=pricing-config-centrally-managed&access=readWrite");
        permissionList.add("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}");
        permissionList.add("pageCode=reset-property&access=readWrite");
        //Optix data feed
        permissionList.add("pageCode=optix-data-feed&access=readOnly");
        //Optix pages
        permissionList.add("pageCode=optix&access=readWrite");
        permissionList.add("pageCode=optix-property-hierarchy&access=readWrite");
        permissionList.add("pageCode=optix-configuration&access=readOnly");
        permissionList.add("pageCode=optix-focus-analysis&access=readWrite&functions={optix-edit-templates:readOnly,optix-share-templates:readWrite}");
        //Navigator pages
        permissionList.add("pageCode=portfolio-navigator&access=readWrite");
        permissionList.add("pageCode=portfolio-navigator-manage&access=readOnly");
        permissionList.add("pageCode=portfolio-navigator-ceiling-floor&access=readOnly");
        //UAD pages
        permissionList.add("pageCode=authorization-group-management&access=readOnly");
        permissionList.add("pageCode=role-management&access=readWrite&functions={role-ranking:readWrite}");
        permissionList.add("pageCode=user-management&access=readOnly&functions={user-management-activate-deactivate:readOnly,user-management-save:readOnly}");
        permissionList.add("pageCode=property-attribute-assignments&access=readWrite");
        permissionList.add("pageCode=property-attributes&access=readWrite");
        permissionList.add("pageCode=system-announcements&access=readWrite");
        //Special Events pages
        permissionList.add("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}");
        permissionList.add("pageCode=special-event-upload&access=readWrite");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(5, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(23, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("booking-pace-report"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("information-manager"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("alerts"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("exceptions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agile-rates"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefinition"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefaults"));
        assertEquals(NO_ACCESS, g3Result.get("agileRatesSeasons"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("agileRatesRestrictions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-product-list"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-advanced-settings"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-config-centrally-managed"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-configuration"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationDefinitionStep"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationTransientCeilingAndFloor"));
        assertNull(g3Result.get("invalidFunction"));
        assertNull(g3Result.get("unknown-page"));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(6, optixResult.size());
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-property-hierarchy"));
        assertEquals(READ_ONLY_ACCESS, optixResult.get("optix-configuration"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-focus-analysis"));
        assertEquals(READ_ONLY_ACCESS, optixResult.get("optix-edit-templates"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-share-templates"));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(3, navigatorResult.size());
        assertEquals(READ_WRITE_ACCESS, navigatorResult.get("portfolio-navigator"));
        assertEquals(READ_ONLY_ACCESS, navigatorResult.get("portfolio-navigator-manage"));
        assertEquals(READ_ONLY_ACCESS, navigatorResult.get("portfolio-navigator-ceiling-floor"));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(9, uadResult.size());
        assertEquals(READ_ONLY_ACCESS, uadResult.get("authorization-group-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-ranking"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-activate-deactivate"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-save"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attributes"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attribute-assignments"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("system-announcements"));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(3, specialEventsResult.size());
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get("special-events-management"));
        assertEquals(READ_ONLY_ACCESS, specialEventsResult.get("specialEventsAddNewDelete"));
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get("special-event-upload"));
    }

    @Test
    void flattenPermissions_G3OptixNavigator_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");
        //valid page code with no functionalities
        permissionList.add("pageCode=booking-pace-report&access=readOnly");
        //valid page code with invalid functionalities
        permissionList.add("pageCode=information-manager&access=readOnly&functions={alerts:readOnly,invalidFunction:readOnly,exceptions:readOnly}");
        //invalid page code
        permissionList.add("pageCode=unknown-page&access=readWrite");
        //nested pages
        permissionList.add("pageCode=agile-rates&access=readWrite&functions={agileRatesDefinition:readWrite,agileRatesDefaults:readWrite,agileRatesSeasons:noAccess,agileRatesRestrictions:readOnly}");
        permissionList.add("pageCode=pricing-config&access=readWrite");
        permissionList.add("pageCode=pricing-product-list&access=readWrite");
        permissionList.add("pageCode=pricing-advanced-settings&access=readWrite");
        permissionList.add("pageCode=pricing-config-centrally-managed&access=readWrite");
        permissionList.add("pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationDefinitionStep:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}");
        permissionList.add("pageCode=reset-property&access=readWrite");
        //Optix data feed
        permissionList.add("pageCode=optix-data-feed&access=readOnly");
        //Optix pages
        permissionList.add("pageCode=optix&access=readWrite");
        permissionList.add("pageCode=optix-property-hierarchy&access=readWrite");
        permissionList.add("pageCode=optix-configuration&access=readOnly");
        permissionList.add("pageCode=optix-focus-analysis&access=readWrite&functions={optix-edit-templates:readOnly,optix-share-templates:readWrite}");
        //Navigator pages
        permissionList.add("pageCode=portfolio-navigator&access=readWrite");
        permissionList.add("pageCode=portfolio-navigator-manage&access=readOnly");
        permissionList.add("pageCode=portfolio-navigator-ceiling-floor&access=readOnly");
        //UAD pages
        permissionList.add("pageCode=authorization-group-management&access=readOnly");
        permissionList.add("pageCode=role-management&access=readWrite&functions={role-ranking:readWrite}");
        permissionList.add("pageCode=user-management&access=readOnly&functions={user-management-activate-deactivate:readOnly,user-management-save:readOnly}");
        permissionList.add("pageCode=property-attribute-assignments&access=readWrite");
        permissionList.add("pageCode=property-attributes&access=readWrite");
        permissionList.add("pageCode=system-announcements&access=readWrite");
        //Special Events pages
        permissionList.add("pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readOnly}");
        permissionList.add("pageCode=special-event-upload&access=readWrite");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(4, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(26, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("booking-pace-report"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("information-manager"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("alerts"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("exceptions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agile-rates"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefinition"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("agileRatesDefaults"));
        assertEquals(NO_ACCESS, g3Result.get("agileRatesSeasons"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("agileRatesRestrictions"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-product-list"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-advanced-settings"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-config-centrally-managed"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricing-configuration"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationDefinitionStep"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("pricingConfigurationTransientCeilingAndFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("special-events-management"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("specialEventsAddNewDelete"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("special-event-upload"));
        assertNull(g3Result.get("invalidFunction"));
        assertNull(g3Result.get("unknown-page"));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(6, optixResult.size());
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-property-hierarchy"));
        assertEquals(READ_ONLY_ACCESS, optixResult.get("optix-configuration"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-focus-analysis"));
        assertEquals(READ_ONLY_ACCESS, optixResult.get("optix-edit-templates"));
        assertEquals(READ_WRITE_ACCESS, optixResult.get("optix-share-templates"));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(3, navigatorResult.size());
        assertEquals(READ_WRITE_ACCESS, navigatorResult.get("portfolio-navigator"));
        assertEquals(READ_ONLY_ACCESS, navigatorResult.get("portfolio-navigator-manage"));
        assertEquals(READ_ONLY_ACCESS, navigatorResult.get("portfolio-navigator-ceiling-floor"));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(9, uadResult.size());
        assertEquals(READ_ONLY_ACCESS, uadResult.get("authorization-group-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-management"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("role-ranking"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-activate-deactivate"));
        assertEquals(READ_ONLY_ACCESS, uadResult.get("user-management-save"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attributes"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("property-attribute-assignments"));
        assertEquals(READ_WRITE_ACCESS, uadResult.get("system-announcements"));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_NoPermissions_G3Only_ExternalRole() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(3, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3Result.size());
        assertEquals(NO_ACCESS, g3Result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, specialEventsResult.size());
        assertEquals(NO_ACCESS, specialEventsResult.get(Role.NO_PERMISSIONS));
    }

    @Test
    void flattenPermissions_NoPermissions_G3Only_ExternalRole_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(2, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3Result.size());
        assertEquals(NO_ACCESS, g3Result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_NoPermissions_G3OptixNavigatorAndSpecialEvents_ExternalRole() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(5, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(NO_ACCESS, g3result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(NO_ACCESS, optixResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(NO_ACCESS, navigatorResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, specialEventsResult.size());
        assertEquals(NO_ACCESS, specialEventsResult.get(Role.NO_PERMISSIONS));
    }

    @Test
    void flattenPermissions_NoPermissions_G3OptixNavigatorAndSpecialEvents_ExternalRole_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(4, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(NO_ACCESS, g3result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(NO_ACCESS, optixResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(NO_ACCESS, navigatorResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_AllPermissions_G3Only_ExternalRole() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.ALL_PERMS_ID);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(3, allPermissions.size());
        HashMap<String, String> result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, result.size());
        assertEquals(READ_WRITE_ACCESS, result.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(READ_WRITE_ACCESS, uadResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, specialEventsResult.size());
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get(Role.ALL_PERMISSIONS));
    }

    @Test
    void flattenPermissions_AllPermissions_G3Only_ExternalRole_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.ALL_PERMS_ID);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(2, allPermissions.size());
        HashMap<String, String> result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, result.size());
        assertEquals(READ_WRITE_ACCESS, result.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(READ_WRITE_ACCESS, uadResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_AllPermissions_G3OptixNavigatorAndSpecialEvents_ExternalRole() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.ALL_PERMS_ID);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(5, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(READ_WRITE_ACCESS, g3result.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(READ_WRITE_ACCESS, optixResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(READ_WRITE_ACCESS, navigatorResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(READ_WRITE_ACCESS, uadResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, specialEventsResult.size());
        assertEquals(READ_WRITE_ACCESS, specialEventsResult.get(Role.ALL_PERMISSIONS));
    }

    @Test
    void flattenPermissions_AllPermissions_G3OptixAndNavigator_ExternalRole_SpecialEventsDisabled() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.ALL_PERMS_ID);

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        System.setProperty("pacman.fds.specialevents.product.enabled", "false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("true");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("true");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(4, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(READ_WRITE_ACCESS, g3result.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(READ_WRITE_ACCESS, optixResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(READ_WRITE_ACCESS, navigatorResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(READ_WRITE_ACCESS, uadResult.get(Role.ALL_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_NoPermissions_G3OptixNavigatorAndSpecialEvents_InternalRole() {
        System.setProperty("pacman.fds.specialevents.product.enabled", "true");

        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        role.setClientCode(Constants.CLIENT_INTERNAL);
        role.setPermissions(permissionList);

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(5, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(NO_ACCESS, g3result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(NO_ACCESS, optixResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(NO_ACCESS, navigatorResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, specialEventsResult.size());
        assertEquals(NO_ACCESS, specialEventsResult.get(Role.NO_PERMISSIONS));
    }

    @Test
    void flattenPermissions_NoPermissions_G3OptixAndNavigator_InternalRole_SpecialEventsDisabled() {
        System.setProperty("pacman.fds.specialevents.product.enabled", "false");

        List<String> permissionList = new ArrayList<>();
        //valid page code with functionalities
        permissionList.add(Role.NO_PERMISSIONS);

        Role role = new Role();
        role.setClientCode(Constants.CLIENT_INTERNAL);
        role.setPermissions(permissionList);

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(4, allPermissions.size());
        HashMap<String, String> g3result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, g3result.size());
        assertEquals(NO_ACCESS, g3result.get(Role.NO_PERMISSIONS));
        HashMap<String, String> optixResult = allPermissions.get(OPTIX_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, optixResult.size());
        assertEquals(NO_ACCESS, optixResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> navigatorResult = allPermissions.get(NAVIGATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, navigatorResult.size());
        assertEquals(NO_ACCESS, navigatorResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> uadResult = allPermissions.get(UNIVERSAL_ADMINISTRATOR_PRODUCT_ENVIRONMENT_ID);
        assertEquals(1, uadResult.size());
        assertEquals(NO_ACCESS, uadResult.get(Role.NO_PERMISSIONS));
        HashMap<String, String> specialEventsResult = allPermissions.get(SPECIAL_EVENTS_PRODUCT_ENVIRONMENT_ID);
        assertNull(specialEventsResult);
    }

    @Test
    void flattenPermissions_invalidPermissions() {
        List<String> permissionList = new ArrayList<>();
        Role role = new Role();
        role.setPermissions(permissionList);

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(0, allPermissions.size());

        role.setPermissions(null);
        HashMap<String, HashMap<String, String>> allPermissions2 = service.flattenPermissions(role);
        assertEquals(0, allPermissions2.size());
    }

    @Test
    void flattenPermissions_OnePermissions_G3Only() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with invalid functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess}");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        HashMap<String, HashMap<String, String>> allPermissions = service.flattenPermissions(role);
        assertEquals(1, allPermissions.size());
        HashMap<String, String> g3Result = allPermissions.get(G3_PRODUCT_ENVIRONMENT_ID);
        assertEquals(6, g3Result.size());
        assertEquals(READ_WRITE_ACCESS, g3Result.get("group-pricing-configuration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationGeneralConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationCeilingFloor"));
        assertEquals(READ_WRITE_ACCESS, g3Result.get("groupPricingConfigurationRateConfiguration"));
        assertEquals(READ_ONLY_ACCESS, g3Result.get("groupPricingConfigurationConferenceBanquet"));
        assertEquals(NO_ACCESS, g3Result.get("groupPricingConfigurationAncillary"));
    }

    @Test
    void flattenPermissions_invalidFunctionality_MissingClosingBracket() {
        List<String> permissionList = new ArrayList<>();
        //valid page code with invalid functionalities
        permissionList.add("pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess");

        Role role = new Role();
        String clientCode = "Hilton";
        role.setClientCode(clientCode);
        role.setPermissions(permissionList);

        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.OPTIX_ENABLED.getParameterName(), clientCode)).thenReturn("false");
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.getParameterName(), clientCode)).thenReturn("false");

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.flattenPermissions(role));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("The functions string is invalid for PageCode: groupPricingConfigurationCeilingFloor and Permissions: pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readOnly,groupPricingConfigurationCeilingFloor:readOnly,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readOnly,groupPricingConfigurationAncillary:noAccess", tetrisException.getBaseMessage());
    }

    @Test
    void migrateAuthGroupsToFDSForClientCode() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        AuthorizationGroup authorizationGroup = createAuthGroup("Test1", client.getId(), property.getId());
        Mockito.when(authorizationService.getAuthGroupsByClientId(client.getId())).thenReturn(Arrays.asList(authorizationGroup));
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));
        UASAuthGroup[] uasAuthGroups = new UASAuthGroup[1];
        UASAuthGroup authGroup = new UASAuthGroup();
        authGroup.setName("All Properties");
        authGroup.setDescription("All available properties");
        uasAuthGroups[0] = authGroup;
        Mockito.when(fdsRestTemplateProvider.uasRestTemplate()).thenReturn(uasRestTemplate);
        Mockito.when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid())).thenReturn(uasAuthGroups);

        HashMap<String, Object> body = createAuthGroupMap("Test1", client.getUpsClientUuid(), Arrays.asList(property.getUpsId()));

        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        UUID uasAuthGroupUuid = UUID.randomUUID();
        uasAuthGroup.setAuthGroupId(uasAuthGroupUuid);
        Mockito.when(uasRestTemplate.postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class)).thenReturn(uasAuthGroup);

        service.migrateAuthGroupsToFDSForClientCode(CLIENT_CODE);

        verify(cacheService).getClient(CLIENT_CODE);
        verify(authorizationService).getAuthGroupsByClientId(client.getId());
        verify(uasRestTemplate).postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class);
        verify(authorizationService).saveAuthGroup(authorizationGroup);
    }

    @Test
    void migrateAuthGroupsToFDSForClientCode_WithNonExistingAllPropertiesAuthGroup() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        AuthorizationGroup authorizationGroup = createAuthGroup("Test1", client.getId(), property.getId());
        Mockito.when(authorizationService.getAuthGroupsByClientId(client.getId())).thenReturn(Arrays.asList(authorizationGroup));
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));
        UASAuthGroup[] uasAuthGroups = new UASAuthGroup[1];
        UASAuthGroup authGroup = new UASAuthGroup();
        uasAuthGroups[0] = authGroup;
        Mockito.when(fdsRestTemplateProvider.uasRestTemplate()).thenReturn(uasRestTemplate);
        Mockito.when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid())).thenReturn(uasAuthGroups);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Mockito.when(uasRestTemplate.exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class)).thenReturn(ResponseEntity.ok(uasAuthGroups[0]));

        HashMap<String, Object> body = createAuthGroupMap("Test1", client.getUpsClientUuid(), Arrays.asList(property.getUpsId()));

        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        UUID uasAuthGroupUuid = UUID.randomUUID();
        uasAuthGroup.setAuthGroupId(uasAuthGroupUuid);
        Mockito.when(uasRestTemplate.postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class)).thenReturn(uasAuthGroup);

        service.migrateAuthGroupsToFDSForClientCode(CLIENT_CODE);

        verify(cacheService).getClient(CLIENT_CODE);
        verify(authorizationService).getAuthGroupsByClientId(client.getId());
        verify(uasRestTemplate).postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class);
        verify(authorizationService).saveAuthGroup(authorizationGroup);
        verify(uasRestTemplate).getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid());
        verify(uasRestTemplate).exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class);
    }

    @Test
    void migrateAuthGroupsToFDSForClientCode_NoAuthGroupsInG3_WithNonExistingAllPropertiesAuthGroup() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        Mockito.when(authorizationService.getAuthGroupsByClientId(client.getId())).thenReturn(new ArrayList<>());
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));
        UASAuthGroup[] uasAuthGroups = new UASAuthGroup[1];
        UASAuthGroup authGroup = new UASAuthGroup();
        uasAuthGroups[0] = authGroup;
        Mockito.when(fdsRestTemplateProvider.uasRestTemplate()).thenReturn(uasRestTemplate);
        Mockito.when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid())).thenReturn(new UASAuthGroup[0]);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Mockito.when(uasRestTemplate.exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class)).thenReturn(ResponseEntity.ok(uasAuthGroups[0]));

        service.migrateAuthGroupsToFDSForClientCode(CLIENT_CODE);

        verify(cacheService).getClient(CLIENT_CODE);
        verify(authorizationService).getAuthGroupsByClientId(client.getId());
        verify(uasRestTemplate, never()).postForObject(anyString(), any(), any());
        verify(authorizationService, never()).saveAuthGroup(any(AuthorizationGroup.class));
        verify(uasRestTemplate).getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid());
        verify(uasRestTemplate).exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class);
    }

    @Test
    void migrateAuthGroupsToFDSForClientCode_PostFails() {
        Mockito.when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        AuthorizationGroup authorizationGroup = createAuthGroup("Test1", client.getId(), property.getId());
        Mockito.when(authorizationService.getAuthGroupsByClientId(client.getId())).thenReturn(Arrays.asList(authorizationGroup));
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));

        HashMap<String, Object> body = createAuthGroupMap("Test1", client.getUpsClientUuid(), Arrays.asList(property.getUpsId()));

        Mockito.when(uasRestTemplate.postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class)).thenReturn(null);

        UASAuthGroup[] uasAuthGroups = new UASAuthGroup[1];
        UASAuthGroup authGroup = new UASAuthGroup();
        uasAuthGroups[0] = authGroup;
        Mockito.when(fdsRestTemplateProvider.uasRestTemplate()).thenReturn(uasRestTemplate);
        Mockito.when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid())).thenReturn(new UASAuthGroup[0]);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Mockito.when(uasRestTemplate.exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class)).thenReturn(ResponseEntity.ok(uasAuthGroups[0]));

        service.migrateAuthGroupsToFDSForClientCode(CLIENT_CODE);

        verify(cacheService).getClient(CLIENT_CODE);
        verify(authorizationService).getAuthGroupsByClientId(client.getId());
        verify(uasRestTemplate).postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class);
        verify(authorizationService, never()).saveAuthGroup(authorizationGroup);
        verify(uasRestTemplate).getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid());
        verify(uasRestTemplate).exchange("/v1/lambdas/addClient/" + client.getUpsClientUuid(), HttpMethod.POST, new HttpEntity<>(headers), UASAuthGroup.class);
    }

    @Test
    void migrateAuthGroupToFDS() {
        Mockito.when(cacheService.getClient(client.getId())).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        AuthorizationGroup authorizationGroup = createAuthGroup("Test1", client.getId(), property.getId());
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));

        HashMap<String, Object> body = createAuthGroupMap("Test1", client.getUpsClientUuid(), Arrays.asList(property.getUpsId()));

        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        UUID uasAuthGroupUuid = UUID.randomUUID();
        uasAuthGroup.setAuthGroupId(uasAuthGroupUuid);
        Mockito.when(uasRestTemplate.postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class)).thenReturn(uasAuthGroup);

        service.migrateAuthGroupToFDS(authorizationGroup);

        verify(cacheService).getClient(client.getId());
        verify(uasRestTemplate).postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class);
        verify(authorizationService).saveAuthGroup(authorizationGroup);
    }

    @Test
    void migrateAuthGroupToFDS_postFails() {
        Mockito.when(cacheService.getClient(client.getId())).thenReturn(client);

        Property property = new Property();
        property.setId(123);
        property.setClient(client);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        AuthorizationGroup authorizationGroup = createAuthGroup("Test1", client.getId(), property.getId());
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(property.getId()))).thenReturn(Arrays.asList(property));

        HashMap<String, Object> body = createAuthGroupMap("Test1", client.getUpsClientUuid(), Arrays.asList(property.getUpsId()));

        Mockito.when(uasRestTemplate.postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class)).thenReturn(null);

        service.migrateAuthGroupToFDS(authorizationGroup);

        verify(cacheService).getClient(client.getId());
        verify(uasRestTemplate).postForObject("/v1/authGroup/migrate", body, UASAuthGroup.class);
        verify(authorizationService, never()).saveAuthGroup(authorizationGroup);
    }

    private AuthorizationGroup createAuthGroup(String name, Integer clientId, Integer propertyId) {
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        authorizationGroup.setClientId(clientId);
        authorizationGroup.setName(name);
        authorizationGroup.setDescription("TestRoleDescription");
        authorizationGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setPropertyId(propertyId);
        authorizationGroupPropertyMapping.setAuthorizationGroup(authorizationGroup);
        authorizationGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)));

        return authorizationGroup;
    }

    public static HashMap<String, Object> createAuthGroupMap(String name, String clientUUID, List<String> propertyIds) {
        HashMap<String, Object> authGroupBody = new HashMap<>();
        authGroupBody.put("clientId", UUID.fromString(clientUUID));
        authGroupBody.put("name", name);
        authGroupBody.put("description", "TestRoleDescription");
        authGroupBody.put("status", 1);
        authGroupBody.put("rule", new HashMap<>());
        authGroupBody.put("propertyList", propertyIds);

        return authGroupBody;
    }

    @Test
    void createAuthGroup_setUUID() {
        UUID clientUuid = UUID.randomUUID();
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        UUID authGroupUuid = UUID.randomUUID();
        authorizationGroup.setUasAuthGroupUuid(authGroupUuid.toString());
        String authGroupName = "Test1";
        authorizationGroup.setName(authGroupName);
        Integer status = 1;
        authorizationGroup.setStatusId(status);
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setAuthorizationGroup(authorizationGroup);
        Integer propertyId = 123456;
        authorizationGroupPropertyMapping.setPropertyId(propertyId);
        authorizationGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)));

        Property property = new Property();
        property.setId(propertyId);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(propertyId))).thenReturn(Arrays.asList(property));

        HashMap<String, Object> result = service.createAuthGroup(clientUuid, authorizationGroup);
        assertEquals(authGroupUuid, result.get("authGroupId"));
        assertEquals(clientUuid, result.get("clientId"));
        assertEquals(authGroupName, result.get("name"));
        assertEquals(status, result.get("status"));
        assertEquals(new HashMap<>(), result.get("rule"));
        assertEquals(Arrays.asList(upsId.toString()), result.get("propertyList"));
    }

    @Test
    void createAuthGroup_dontSetUUID() {
        UUID clientUuid = UUID.randomUUID();
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        String authGroupName = "Test1";
        authorizationGroup.setName(authGroupName);
        Integer status = 1;
        authorizationGroup.setStatusId(status);
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setAuthorizationGroup(authorizationGroup);
        Integer propertyId = 123456;
        authorizationGroupPropertyMapping.setPropertyId(propertyId);
        authorizationGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)));

        Property property = new Property();
        property.setId(propertyId);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        Mockito.when(propertyService.findPropertyByIds(Arrays.asList(propertyId))).thenReturn(Arrays.asList(property));

        HashMap<String, Object> result = service.createAuthGroup(clientUuid, authorizationGroup);
        assertNull(result.get("authGroupId"));
        assertEquals(clientUuid, result.get("clientId"));
        assertEquals(authGroupName, result.get("name"));
        assertEquals(status, result.get("status"));
        assertEquals(new HashMap<>(), result.get("rule"));
        assertEquals(Arrays.asList(upsId.toString()), result.get("propertyList"));
    }

    @Test
    void createRuleMap() {
        Integer ruleId = 1;
        Rule rule = new Rule();
        rule.setId(ruleId);

        RuleAttributeValueMapping ruleAttributeValueMapping = new RuleAttributeValueMapping();
        ruleAttributeValueMapping.setRule(rule);
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        ClientAttribute clientAttribute = new ClientAttribute();
        UUID upsCustomAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(upsCustomAttributeUuid.toString());
        clientAttributeValue.setClientAttribute(clientAttribute);
        String attributeValue = "Test";
        clientAttributeValue.setClientAttributeValue(attributeValue);
        ruleAttributeValueMapping.setClientAttributeValue(clientAttributeValue);
        ConditionType conditionType = new ConditionType();
        conditionType.setId(1);
        ruleAttributeValueMapping.setConditionType(conditionType);
        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(1);
        ruleAttributeValueMapping.setConjunctionType(conjunctionType);
        ruleAttributeValueMapping.setRanking(1);
        rule.setRuleAttributeValueMappings(new HashSet<>(Arrays.asList(ruleAttributeValueMapping)));

        when(rulesService.getRule(ruleId)).thenReturn(rule);

        Map<Integer, UASRuleDetails> result = service.createRuleMap(ruleId);
        assertEquals(1, result.size());
        UASRuleDetails resultRuleDetails = result.get(1);
        assertEquals("EQUALS", resultRuleDetails.getConditionType());
        assertEquals("NONE", resultRuleDetails.getConjunctionType());
        assertEquals(upsCustomAttributeUuid, resultRuleDetails.getCustomAttributeId());
        assertEquals(1, resultRuleDetails.getCustomAttributeValues().size());
        List<String> resultCustomAttributeValues = resultRuleDetails.getCustomAttributeValues();
        assertEquals(attributeValue, resultCustomAttributeValues.get(0));
    }

    @Test
    void createRuleMap_noRuleFound() {
        Integer ruleId = 1;
        when(rulesService.getRule(ruleId)).thenReturn(null);

        Map<Integer, UASRuleDetails> result = service.createRuleMap(ruleId);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    void createRuleMap_clientAttributeUUIDisNull() {
        Integer ruleId = 1;
        Rule rule = new Rule();
        rule.setId(ruleId);

        RuleAttributeValueMapping ruleAttributeValueMapping = new RuleAttributeValueMapping();
        ruleAttributeValueMapping.setRule(rule);
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setId(1);
        clientAttribute.setUpsCustomAttributeUuid(null);
        clientAttributeValue.setClientAttribute(clientAttribute);
        String attributeValue = "Test";
        clientAttributeValue.setClientAttributeValue(attributeValue);
        ruleAttributeValueMapping.setClientAttributeValue(clientAttributeValue);
        ConditionType conditionType = new ConditionType();
        conditionType.setId(1);
        ruleAttributeValueMapping.setConditionType(conditionType);
        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(1);
        ruleAttributeValueMapping.setConjunctionType(conjunctionType);
        ruleAttributeValueMapping.setRanking(1);
        rule.setRuleAttributeValueMappings(new HashSet<>(Arrays.asList(ruleAttributeValueMapping)));

        when(rulesService.getRule(ruleId)).thenReturn(rule);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.createRuleMap(ruleId));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error creating Rule map as UpsCustomAttributeUuid is null for clientAttributeId: 1", tetrisException.getBaseMessage());
    }

    @Test
    void getConjunctionTypeName() {
        assertEquals("Invalid Id", service.getConjunctionTypeName(0));
        assertEquals("NONE", service.getConjunctionTypeName(1));
        assertEquals("AND", service.getConjunctionTypeName(2));
        assertEquals("OR", service.getConjunctionTypeName(3));
        assertEquals("Invalid Id", service.getConjunctionTypeName(4));
    }

    @Test
    void getConditionTypeName() {
        assertEquals("Invalid Id", service.getConditionTypeName(0));
        assertEquals("EQUALS", service.getConditionTypeName(1));
        assertEquals("NOT_EQUALS", service.getConditionTypeName(2));
        assertEquals("LESS_THAN", service.getConditionTypeName(3));
        assertEquals("GREATER_THAN", service.getConditionTypeName(4));
        assertEquals("LESS_THAN_OR_EQUAL_TO", service.getConditionTypeName(5));
        assertEquals("GREATER_THAN_OR_EQUAL_TO", service.getConditionTypeName(6));
        assertEquals("Invalid Id", service.getConditionTypeName(7));
    }

    @Test
    void getPropertyUUIDList_valid() {
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setPropertyId(123);

        Property property = new Property();
        property.setId(123);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        when(propertyService.findPropertyByIds(Arrays.asList(123))).thenReturn(Arrays.asList(property));

        List<String> result = service.getPropertyUUIDList(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)), 1);
        assertEquals(1, result.size());
        assertEquals(upsId.toString(), result.get(0));
    }

    @Test
    void getPropertyUUIDList_missingUpsId() {
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setPropertyId(123);

        Property property = new Property();
        property.setId(123);
        when(propertyService.findPropertyByIds(Arrays.asList(123))).thenReturn(Arrays.asList(property));

        Integer clientId = 1;
        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.getPropertyUUIDList(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)), clientId));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("One or more properties for G3 ClientId: " + clientId + " have a null UPS_ID", tetrisException.getBaseMessage());
    }

    @Test
    void getPropertyUUIDList_noPropertiesFound() {
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
        authorizationGroupPropertyMapping.setPropertyId(123);

        Property property = new Property();
        property.setId(123);
        UUID upsId = UUID.randomUUID();
        property.setUpsId(upsId.toString());
        when(propertyService.findPropertyByIds(Arrays.asList(123))).thenReturn(new ArrayList<>());

        Integer clientId = 1;
        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.getPropertyUUIDList(new HashSet<>(Arrays.asList(authorizationGroupPropertyMapping)), clientId));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("No properties found for property Ids: [123]", tetrisException.getBaseMessage());
    }

    @Test
    void getPropertyUUIDList_emptylist() {
        List<String> result = service.getPropertyUUIDList(new HashSet<>(), 1);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    void getListOfProperties() {
        Property property1 = new Property();
        property1.setId(123);
        Property property2 = new Property();
        property2.setId(124);
        Property property3 = new Property();
        property3.setId(125);
        Property property4 = new Property();
        property4.setId(126);
        Property property5 = new Property();
        property5.setId(127);
        when(propertyService.findPropertyByIds(Arrays.asList(123, 124))).thenReturn(Arrays.asList(property1, property2));
        when(propertyService.findPropertyByIds(Arrays.asList(125, 126))).thenReturn(Arrays.asList(property3, property4));
        when(propertyService.findPropertyByIds(Arrays.asList(127))).thenReturn(Arrays.asList(property5));

        List<Property> result = service.getListOfProperties(Arrays.asList(123, 124, 125, 126, 127), 2);

        assertEquals(5, result.size());
        assertEquals(property1, result.get(0));
        assertEquals(property2, result.get(1));
        assertEquals(property3, result.get(2));
        assertEquals(property4, result.get(3));
        assertEquals(property5, result.get(4));
    }

    @Test
    void deleteAuthGroupInFDS() {
        UUID authGroupUUID = UUID.randomUUID();
        service.deleteAuthGroupInFDS(authGroupUUID.toString());

        verify(uasRestTemplate).delete("/v1/authGroup/{authGroupId}", authGroupUUID.toString());
    }

    @Test
    void getAuthGroupFromFDS() {
        UUID authGroupUUID = UUID.randomUUID();
        service.getAuthGroupFromFDS(authGroupUUID.toString());

        verify(uasRestTemplate).getForObject("/v1/authGroup/byAuthGroupId/{authGroupId}", UASAuthGroup.class, authGroupUUID.toString());
    }

    @Test
    void testGetAuthGroupsFromFDSByClient() {
        UASAuthGroup[] authGroups = new UASAuthGroup[1];
        authGroups[0] = new UASAuthGroup();
        when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid())).thenReturn(authGroups);
        List<UASAuthGroup> authGroupsFromFDSByClient = service.getAuthGroupsFromFDSByClient(client.getUpsClientUuid());
        verify(uasRestTemplate).getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client.getUpsClientUuid());
        assertEquals(1, authGroupsFromFDSByClient.size());

    }

    @Test
    void testGetAuthGroupsFromFDSByClientServerErrorOrInvalidUUID() {
        when(uasRestTemplate.getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client2.getUpsClientUuid())).thenThrow(new RestClientException("Server Error"));
        List<UASAuthGroup> authGroupsFromFDSByClient = service.getAuthGroupsFromFDSByClient(client2.getUpsClientUuid());
        verify(uasRestTemplate, times(2)).getForObject("/v1/authGroup/{clientId}", UASAuthGroup[].class, client2.getUpsClientUuid());
        assertTrue(authGroupsFromFDSByClient.isEmpty());
    }

    @Test
    void testGetActiveUsersByClient() {
        GlobalUser user = new GlobalUser();
        user.setId(111);
        when(crudService.findByNamedQuery(GlobalUser.ALL_ACTIVE_NON_INTEGRATION_USERS_BY_CLIENT_CODE_AND_COGNITO_USER_ID_NOT_NULL_AND_FDS_USER_NOT_MIGRATED, QueryParameter.with("clientCode", CLIENT_CODE).parameters())).thenReturn(List.of(user));
        List<GlobalUser> allActiveUsersByClient = service.getAllActiveUsersNotMigratedToFDSByClient(CLIENT_CODE);
        assertEquals(1, allActiveUsersByClient.size());
        assertEquals(user, allActiveUsersByClient.get(0));
    }

    @Test
    void syncRoleInFDS() {
        /*
         * Test begins with the following role assignments
         * role1 = authGroup1UUID
         * role2 = property1UUID
         * role3 = authGroup2UUID, property2UUID
         * The test ends with the following assignments
         * role1 = authGroup2UUID
         * role2 = property2UUID
         * role3 = authGroup1UUID, property1UUID
         * */
        String userId = UUID.randomUUID().toString();
        GlobalUser user = new GlobalUser();
        user.setId(1);
        user.setCognitoUserId(userId);
        user.setClientCode(CLIENT_CODE);
        when(userService.getGlobalUserByUISId(userId)).thenReturn(Optional.of(user));

        //Setup roles
        String role1UUID = UUID.randomUUID().toString();
        String role2UUID = UUID.randomUUID().toString();
        String role3UUID = UUID.randomUUID().toString();
        Role role1 = new Role();
        role1.setUniqueIdentifier("1");
        role1.setUasRoleDictionaryUuid(role1UUID);
        Role role2 = new Role();
        role2.setUniqueIdentifier("2");
        role2.setUasRoleDictionaryUuid(role2UUID);
        Role role3 = new Role();
        role3.setUniqueIdentifier("3");
        role3.setUasRoleDictionaryUuid(role3UUID);
        when(roleService.getAllRoles(CLIENT_CODE)).thenReturn(Set.of(role1, role2, role3));
        when(cacheService.getClient(CLIENT_CODE)).thenReturn(client);

        //Setup auth groups
        String authGroup1UUID = UUID.randomUUID().toString();
        String authGroup2UUID = UUID.randomUUID().toString();
        String property1UUID = UUID.randomUUID().toString();
        String property2UUID = UUID.randomUUID().toString();

        EntitlementV2 finalRole1 = new EntitlementV2(role1UUID, authGroup2UUID);
        EntitlementV2 finalRole2 = new EntitlementV2(role2UUID, List.of(property2UUID));
        EntitlementV2 finalRole3 = new EntitlementV2(role3UUID, authGroup1UUID, List.of(property1UUID));
        List<EntitlementV2> finalRoleList = new ArrayList<>();
        finalRoleList.add(finalRole1);
        finalRoleList.add(finalRole2);
        finalRoleList.add(finalRole3);
        when(roleService.getUserRolesForFDS(user.getId(), client.getUpsClientUuid())).thenReturn(finalRoleList);

        EntitlementV2 startingRole1 = new EntitlementV2(role1UUID, authGroup1UUID);
        EntitlementV2 startingRole2 = new EntitlementV2(role2UUID, List.of(property1UUID));
        EntitlementV2 startingRole3 = new EntitlementV2(role3UUID, authGroup2UUID, List.of(property2UUID));
        List<EntitlementV2> startingRoleList = new ArrayList<>();
        startingRoleList.add(startingRole1);
        startingRoleList.add(startingRole2);
        startingRoleList.add(startingRole3);
        when(userService.pullCurrentEnvironmentRoleEntitlementsFromExistingUser(user.getCognitoUserId(), client.getUpsClientUuid())).thenReturn(startingRoleList);

        ArgumentCaptor<List<EntitlementV2>> entitlementCaptor = ArgumentCaptor.forClass(List.class);
        service.syncRoleInFDS(userId);

        //Verify remove and add were called
        verify(uisEntitlementService).removeRoleAssignments(eq(userId), entitlementCaptor.capture());
        verify(uisEntitlementService).addRoleAssignments(eq(userId), entitlementCaptor.capture());

        //Building entitlements to compare with
        List<String> expectedEntitlementsToRemove = List.of(
                new EntitlementV2(role1UUID, authGroup1UUID).toString(),
                new EntitlementV2(role2UUID, List.of(property1UUID)).toString(),
                new EntitlementV2(role3UUID, authGroup2UUID, List.of(property2UUID)).toString());

        List<String> expectedEntitlementsToAdd = List.of(
                new EntitlementV2(role1UUID, authGroup2UUID).toString(),
                new EntitlementV2(role2UUID, List.of(property2UUID)).toString(),
                new EntitlementV2(role3UUID, authGroup1UUID, List.of(property1UUID)).toString()
        );

        //Grab all captured values and ensure remove and add are the correct values
        List<List<EntitlementV2>> allCapturedValues = entitlementCaptor
                .getAllValues();
        List<String> entitlementsToRemove = allCapturedValues
                .get(0)
                .stream()
                .map(EntitlementV2::toString)
                .collect(Collectors.toList());
        List<String> entitlementsToAdd = allCapturedValues
                .get(1)
                .stream()
                .map(EntitlementV2::toString)
                .collect(Collectors.toList());
        assertTrue(expectedEntitlementsToRemove.containsAll(entitlementsToRemove));
        assertTrue(expectedEntitlementsToAdd.containsAll(entitlementsToAdd));
    }

    @Test
    void syncRoleInFDSInternalUser() {
        /*
         * Test begins with the following role assignments
         * role1 = internalAllAuthGroupUUID
         * The test ends with the following assignments
         * role2 = internalAllAuthGroupUUID
         * */
        String userId = UUID.randomUUID().toString();
        GlobalUser user = new GlobalUser();
        user.setId(1);
        user.setCognitoUserId(userId);
        user.setClientCode(INTERNAL_CLIENT_CODE);
        when(userService.getGlobalUserByUISId(userId)).thenReturn(Optional.of(user));

        //Setup roles
        String role1UUID = UUID.randomUUID().toString();
        String role2UUID = UUID.randomUUID().toString();

        EntitlementV2 finalRole = new EntitlementV2(role2UUID, INTERNAL_ALL_AUTH_GROUPS_UUID);
        List<EntitlementV2> finalRoleList = new ArrayList<>();
        finalRoleList.add(finalRole);
        when(roleService.getUserRolesForFDS(user.getId(), internalClient.getUpsClientUuid())).thenReturn(finalRoleList);

        EntitlementV2 startingRole = new EntitlementV2(role1UUID, INTERNAL_ALL_AUTH_GROUPS_UUID);
        List<EntitlementV2> startingRoleList = new ArrayList<>();
        startingRoleList.add(startingRole);
        when(userService.pullCurrentEnvironmentRoleEntitlementsFromExistingUser(user.getCognitoUserId(), internalClient.getUpsClientUuid())).thenReturn(startingRoleList);

        ArgumentCaptor<List<EntitlementV2>> entitlementCaptor = ArgumentCaptor.forClass(List.class);
        service.syncRoleInFDS(userId);

        //Verify remove and add were called
        verify(uisEntitlementService).removeRoleAssignments(eq(userId), entitlementCaptor.capture());
        verify(uisEntitlementService).addRoleAssignments(eq(userId), entitlementCaptor.capture());

        //Building entitlements to compare with
        List<String> expectedEntitlementsToRemove = List.of(
                new EntitlementV2(role1UUID, INTERNAL_ALL_AUTH_GROUPS_UUID).toString());

        List<String> expectedEntitlementsToAdd = List.of(
                new EntitlementV2(role2UUID, INTERNAL_ALL_AUTH_GROUPS_UUID).toString()
        );

        //Grab all captured values and ensure remove and add are the correct values
        List<List<EntitlementV2>> allCapturedValues = entitlementCaptor
                .getAllValues();
        List<String> entitlementsToRemove = allCapturedValues
                .get(0)
                .stream()
                .map(EntitlementV2::toString)
                .collect(Collectors.toList());
        List<String> entitlementsToAdd = allCapturedValues
                .get(1)
                .stream()
                .map(EntitlementV2::toString)
                .collect(Collectors.toList());
        assertTrue(expectedEntitlementsToRemove.containsAll(entitlementsToRemove));
        assertTrue(expectedEntitlementsToAdd.containsAll(entitlementsToAdd));
    }
}
