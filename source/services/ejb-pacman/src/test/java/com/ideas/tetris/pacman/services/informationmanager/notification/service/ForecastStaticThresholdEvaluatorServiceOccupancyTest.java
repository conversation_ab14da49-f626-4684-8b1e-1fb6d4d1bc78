package com.ideas.tetris.pacman.services.informationmanager.notification.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertEvaluationException;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.informationmanager.notification.AbstractForecastStaticThresholdEvaluatorTestSupport;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MockitoSettings(strictness = Strictness.LENIENT)
public class ForecastStaticThresholdEvaluatorServiceOccupancyTest extends AbstractForecastStaticThresholdEvaluatorTestSupport {

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtProperty() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(4);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.PROPERTY, MetricType.ROOMS, "Property", false, thresholdValue, thresholdConstraintGreaterThanOrEqualTo);
        setUpTestDataForProperty();
        tenantCrudService().flush();
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(126, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    void testOccupancyForecastNotifAtPropertyLevlWithDifferentMktSegInConsecutiveBDE() {
        thresholdValue = new BigDecimal(4);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.PROPERTY, MetricType.ROOMS, "Property", false, thresholdValue, thresholdConstraintGreaterThanOrEqualTo);
        setUpTestDataForPropertyForDifferentMktSeg();
        tenantCrudService().flush();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertEquals(1, list.size());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
        assertEquals("25.00000", list.get(0).getCurrentOptimizationValue());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtPropertyForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.PROPERTY, MetricType.ROOMS, PROPERTY_CODE, thresholdConstraintGreaterThanOrEqualTo, BigDecimal.valueOf(4)).doSetupForProperty().evaluateNotification().verifyGeneratedNotificationWithExpectation(63, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtPropertyForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.PROPERTY, MetricType.ROOMS, PROPERTY_CODE, thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(50)).doSetupForProperty().evaluateNotification().verifyGeneratedNotificationWithExpectation(15, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluteExceptionForOccupancyForecastAtRoomClass() throws SecurityException, NoSuchMethodException, IllegalArgumentException, IllegalAccessException, InvocationTargetException, AlertEvaluationException {
        thresholdValue = new BigDecimal(50);
        AccomClass objAccomClass = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "DLX").and("propertyId", PROPERTY_ID5).parameters());
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.ROOM_CLASS, MetricType.ROOMS, objAccomClass.getCode(), false, thresholdValue, thresholdConstraintLessThanOrEqualTo);
        setUpTestDataForRoomTypeAndRoomClass();
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(32, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtRoomClassForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.ROOM_CLASS, MetricType.CURRENCY, ROOM_CODE, thresholdConstraintGreaterThanOrEqualTo, BigDecimal.ONE).doSetupForRoomClassAndRoomType().evaluateNotification().verifyGeneratedNotificationWithExpectation(190, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtRoomClassForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.ROOM_CLASS, MetricType.CURRENCY, ROOM_CODE, thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(50)).doSetupForRoomClassAndRoomType().evaluateNotification().verifyGeneratedNotificationWithExpectation(16, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtRoomType() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(10);
        AccomType objAccomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.ROOM_TYPE, MetricType.ROOMS, objAccomType.getAccomTypeCode(), false, thresholdValue, thresholdConstraintGreaterThanOrEqualTo);
        setUpTestDataForRoomTypeAndRoomClass();
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(38, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtRoomTypeForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.ROOM_TYPE, MetricType.CURRENCY, ROOM_CODE, thresholdConstraintGreaterThanOrEqualTo, BigDecimal.TEN).doSetupForRoomClassAndRoomType().evaluateNotification().verifyGeneratedNotificationWithExpectation(19, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtRoomTypeForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.ROOM_CLASS, MetricType.CURRENCY, ROOM_CODE, thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(50)).doSetupForRoomClassAndRoomType().evaluateNotification().verifyGeneratedNotificationWithExpectation(16, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtMasterClass() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(20);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.ROOM_CLASS, MetricType.ROOMS, SubLevelType.MASTER_CLASS.getCode(), true, thresholdValue, thresholdConstraintLessThanOrEqualTo);
        setUpTestDataForRoomTypeAndRoomClass();
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(22, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtForecastGroup() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(26);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.FORECAST_GROUP, MetricType.ROOMS, getForecastGroup().getName(), false, thresholdValue, thresholdConstraintLessThanOrEqualTo);
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(20, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtForecastGroupForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.FORECAST_GROUP, MetricType.CURRENCY, getForecastGroup().getName(), thresholdConstraintGreaterThanOrEqualTo, BigDecimal.valueOf(4)).evaluateNotification().verifyGeneratedNotificationWithExpectation(63, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtForecastGroupForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.FORECAST_GROUP, MetricType.CURRENCY, getForecastGroup().getName(), thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(26)).evaluateNotification().verifyGeneratedNotificationWithExpectation(10, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessType() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(87);
        // thresholdValue.compareTo(another);
        Integer businessTypeId = ((BusinessType) tenantCrudService().findByNamedQuerySingleResult(BusinessType.BY_NAME, QueryParameter.with("name", "Transient").parameters())).getId();
        setUptestDataForAlertForBusinessType(businessTypeId);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.BUSINESS_TYPE, MetricType.ROOMS, "Transient", false, thresholdValue, thresholdConstraintGreaterThanOrEqualTo);
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(162, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessTypeForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.BUSINESS_TYPE, MetricType.CURRENCY, BUSINESS_TYPE_TRANSIENT, thresholdConstraintGreaterThanOrEqualTo, BigDecimal.valueOf(2)).doSetupForBusinessType().evaluateNotification().verifyGeneratedNotificationWithExpectation(3520, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessTypeForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.BUSINESS_TYPE, MetricType.CURRENCY, BUSINESS_TYPE_TRANSIENT, thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(1000)).doSetupForBusinessType().evaluateNotification().verifyGeneratedNotificationWithExpectation(13, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessGroup() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(51);
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.PROPERTY_BUSINESS_VIEW, MetricType.ROOMS, getBusinessGroup().getName(), false, thresholdValue, thresholdConstraintLessThanOrEqualTo);
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(20, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessGroupForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.PROPERTY_BUSINESS_VIEW, MetricType.CURRENCY, getBusinessGroup().getName(), thresholdConstraintGreaterThanOrEqualTo, BigDecimal.valueOf(4)).evaluateNotification().verifyGeneratedNotificationWithExpectation(125, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtBusinessGroupForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.PROPERTY_BUSINESS_VIEW, MetricType.CURRENCY, getBusinessGroup().getName(), thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(50)).evaluateNotification().verifyGeneratedNotificationWithExpectation(10, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtMarketSegment() throws AlertEvaluationException {
        thresholdValue = new BigDecimal(50);
        MktSeg objMktSeg = getMktSeg();
        setUpExceptionConfiguration(AlertType.ForecastAsOfLastNightlyOptimization, ExceptionSubType.OCCUPANCY, LevelType.MARKET_SEGMENT, MetricType.ROOMS, objMktSeg.getCode(), false, thresholdValue, thresholdConstraintGreaterThanOrEqualTo);
        instance.evaluateAll();
        instance.evaluateAll();
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertTrue(list.size() > 0);
        assertEquals(20, list.get(0).getScore());
        assertEquals(AlertType.ForecastAsOfLastNightlyOptimization.toString(), list.get(0).getAlertType().getName());
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtMarketSegmentForConditionGreaterThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.MARKET_SEGMENT, MetricType.CURRENCY, getMktSeg().getCode(), thresholdConstraintGreaterThanOrEqualTo, BigDecimal.valueOf(4)).evaluateNotification().verifyGeneratedNotificationWithExpectation(125, AlertType.ForecastAsOfLastOptimization);
    }

    @Test
    public void testEvaluateExceptionForOccupancyForecastAtMarketSegmentForConditionLessThanOrEqualTo_LastOptimization() throws AlertEvaluationException {
        withGivenDataAndConfiguration(AlertType.ForecastAsOfLastOptimization, LevelType.MARKET_SEGMENT, MetricType.CURRENCY, getMktSeg().getCode(), thresholdConstraintLessThanOrEqualTo, BigDecimal.valueOf(50)).evaluateNotification().verifyGeneratedNotificationWithExpectation(10, AlertType.ForecastAsOfLastOptimization);
    }

    private MktSeg getMktSeg() {
        String marketSegCode = setUptestDataForAlertForMarketSegment();
        return tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_PROPERTY_ID_AND_CODE, QueryParameter.with("code", marketSegCode).and("propertyId", PROPERTY_ID5).parameters());
    }

    private ForecastStaticThresholdEvaluatorServiceOccupancyTest withGivenDataAndConfiguration(final AlertType alertType, final LevelType levelType, final MetricType metricType, final String subLevelTypeCode, final String comparatorOperator, final BigDecimal thresholdValue) {
        this.thresholdValue = thresholdValue;
        setUpExceptionConfiguration(alertType, ExceptionSubType.OCCUPANCY, levelType, metricType, subLevelTypeCode, false, thresholdValue, comparatorOperator);
        return this;
    }

    private ForecastStaticThresholdEvaluatorServiceOccupancyTest doSetupForProperty() {
        setUpTestDataForProperty();
        return this;
    }

    private ForecastStaticThresholdEvaluatorServiceOccupancyTest evaluateNotification() {
        instance.evaluateAll();
        infoMgrExcepNotifEntities = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        return this;
    }

    private ForecastStaticThresholdEvaluatorServiceOccupancyTest doSetupForRoomClassAndRoomType() {
        setUpTestDataForRoomTypeAndRoomClass();
        return this;
    }

    private ForecastStaticThresholdEvaluatorServiceOccupancyTest doSetupForBusinessType() {
        setUptestDataForAlertForBusinessType(2);
        return this;
    }

    protected BusinessGroup getBusinessGroup() {
        String BusinessGroupName = setUptestDataForAlertForBusinessGroup();
        return tenantCrudService().findByNamedQuerySingleResult(BusinessGroup.BY_NAME, QueryParameter.with("name", BusinessGroupName).and("propertyId", PROPERTY_ID5).parameters());
    }

    private ForecastGroup getForecastGroup() {
        String forecast_group_name = setUptestDataForAlertForForecastGroup();
        return tenantCrudService().findByNamedQuerySingleResult(ForecastGroup.BY_PROPERTY_AND_NAME, QueryParameter.with("name", forecast_group_name).and("propertyId", PROPERTY_ID5).parameters());
    }
}
