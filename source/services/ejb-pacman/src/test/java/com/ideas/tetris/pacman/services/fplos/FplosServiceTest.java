package com.ideas.tetris.pacman.services.fplos;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.recommendation.compression.GzipCompressionHelper;
import com.ideas.recommendation.model.QualifiedRates;
import com.ideas.recommendation.model.RateDetails;
import com.ideas.recommendation.model.RecommendationContext;
import com.ideas.recommendation.model.s3.RecommendationRequest;
import com.ideas.recommendation.model.s3.RecommendationResponse;
import com.ideas.recommendation.model.s3.S3Context;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.activity.service.AccomActivityRepository;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileProductRestrictionAssociation;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.ChannelRestrictionAdjustmentCurrencyCorrectionService;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.ChannelRestrictionAdjustmentService;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.entity.ChannelSrpGroupIncludedSrpMapping;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.repository.ChannelSrpGroupIncludedSrpMappingRepository;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.LastRoomValueDecisionService;
import com.ideas.tetris.pacman.services.decisionmigration.DecisionMigrationStatusService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.fplos.entity.DecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.PaceDecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.hiltonChannelRestrictionAdjustments.entity.TenantChannelRestrictionAdjustment;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.limittotal.service.LimitTotalService;
import com.ideas.tetris.pacman.services.linkedsrp.repository.RateQualifiedFixedRepository;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.IRateAdjustment;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedAdjustmentService;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixed;
import com.ideas.tetris.pacman.services.servicingcostbylos.entity.ServicingCostByLOSForRestriction;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified;
import com.ideas.tetris.pacman.util.compression.GzipCompressionUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.ChannelRestrictionAdjustment;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQBytesMessage;
import org.apache.activemq.util.ByteSequence;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.core.JmsTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.getCurrentTimestamp;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@ExtendWith({MockitoExtension.class})
@Slf4j
public class FplosServiceTest extends AbstractG3JupiterTest {
    @Mock
    private AccommodationService accommodationService;
    @Mock
    private LastRoomValueDecisionService lastRoomValueDecisionService;
    @Mock
    private DecisionService decisionService;
    @Mock
    private DecisionMigrationStatusService decisionMigrationStatusService;
    @Mock
    private DecisionQualifiedFplosRepository decisionQualifiedFplosRepository;
    @Mock
    private PaceDecisionQualifiedFplosRepository paceDecisionQualifiedFplosRepository;
    @Mock
    private RateQualifiedService rateQualifiedService;

    @Mock
    public CrudService crudService;

    @Mock
    private AccomActivityRepository accomActivityRepository;
    @Mock
    private RateQualifiedAdjustmentService rateQualifiedAdjustmentService;
    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ChannelSrpGroupIncludedSrpMappingRepository channelSrpGroupIncludedSrpMappingRepository;

    @Mock
    private ChannelRestrictionAdjustmentService channelRestrictionAdjustmentService;

    @Mock
    private ChannelRestrictionAdjustmentCurrencyCorrectionService channelRestrictionAdjustmentCurrencyCorrectionService;

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private PacmanConfigParamsService configService;

    @Mock
    private LimitTotalService limitTotalService;

    @Mock
    private FPLOSQualifiedRecommendationService fplosQualifiedRecommendationService;

    @Mock
    private CloseHighestBarService closeHighestBarService;

    @Mock
    private HospitalityRoomsService hospitalityRoomsService;

    @Mock
    @Qualifier("jmsTemplateForFplosBroker")
    private JmsTemplate jmsTemplateForFplosBroker;

    @Mock
    private ServicingCostByLOSService servicingCostByLOSService;

    @Mock
    private RateQualifiedFixedRepository rateQualifiedFixedRepository;

    @Mock
    private DateService dateService;

    @Mock
    private FileMetadataService fileMetadataService;

    @InjectMocks
    FplosService fplosService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void load() {
        LocalDate startDate = LocalDate.of(2023, 01, 01);
        LocalDate endDate = LocalDate.of(2024, 01, 01);
        LocalDate endDatePlus7 = LocalDate.of(2024, 01, 8);
        AccomClass accomClass = createAccomClass();

        var decision = new Decision();
        decision.setId(1);
        PacmanWorkContextHelper.setWorkContext(createWorkContext("Hilton"));
        when(accommodationService.getAllAccomTypes()).thenReturn(createAccomTypes(5, accomClass));
        when(rateQualifiedService.fetchAllRatesByPropertyId()).thenReturn(createRates());
        when(lastRoomValueDecisionService.getLastRoomValueDecisions(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createLastRoomValue());
        when(accommodationService.findAll()).thenReturn(List.of(accomClass));
        when(decisionService.createQualifiedFPLOSDecision()).thenReturn(decision);
        when(rateQualifiedService.getRateQualifiedDetails(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());
        when(accomActivityRepository.findByDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createAccomActivities());
        when(rateQualifiedAdjustmentService.getRateQualifiedAdjustments(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7), false)).thenReturn(createRateAdjustments());
        when(fplosQualifiedRecommendationService.getDefaultAccomTypeId()).thenReturn(1);
        when(closeHighestBarService.getClosedHighestBarsForDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());

        when(productManagementService.search(any())).thenReturn(createCPDecisionBarOutputs());
        when(channelSrpGroupIncludedSrpMappingRepository.getSRPGrpIncludedSRPMappings()).thenReturn(getChannelSrpGroupIncludedSrpMapping());
        when(agileRatesConfigurationService.getAllProductRestrictionAssociations()).thenReturn(createProductAssociationList());
        when(channelRestrictionAdjustmentCurrencyCorrectionService.getTenantChannelRestrictionAdjustments()).thenReturn(createTenantChannelRestrictionsList());
        when(configService.getBooleanParameterValue(PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT)).thenReturn(false);
        when(jmsTemplateForFplosBroker.sendAndReceive(anyString(), any())).thenReturn(getMessage());
        when(configService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(limitTotalService.getSRPsAtTotalLevel()).thenReturn(new ArrayList<String>());
        when(hospitalityRoomsService.getAllTypesOfZeroCapacityRTsRequiringPricingAndRestrictions()).thenReturn(new ArrayList<String>());
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.DIFF_ENABLED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(decisionService.getLastDecisionIdByDecisionTypeAndProcessStatusId(anyInt(), anyInt())).thenReturn(999);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);

        fplosService.load(
                convertLocalDateToJavaUtilDate(startDate),
                convertLocalDateToJavaUtilDate(endDate),
                new Date(),
                true,
                false,
                null);

        verify(decisionService).createQualifiedFPLOSDecision();
        verify(jmsTemplateForFplosBroker).sendAndReceive(anyString(), any()); //FIXME: add some validations on the input for the queue name and the structure of the recommendation request
        verify(decisionService).updateDescisionProcessStatus(decision.getId(), ProcessStatus.COMMITTED, true);
        verify(decisionService, never()).updateDescisionProcessStatus(anyInt(), eq(ProcessStatus.FAILED), anyBoolean());
        verify(configService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED);
    }

    private static DecisionQualifiedFPLOS getDecisionQualifiedFPLOS(int decisionId, Integer rateId, Integer daysToAdd, Integer propertyId, Integer accomTypeId) {
        var decisionQualifiedFPLOS = new DecisionQualifiedFPLOS();
        // Set necessary properties for each instance
        decisionQualifiedFPLOS.setDecisionId(decisionId);
        decisionQualifiedFPLOS.setPropertyId(propertyId);
        decisionQualifiedFPLOS.setAccomTypeId(accomTypeId);
        decisionQualifiedFPLOS.setArrivalDate(LocalDateUtils.toDate(LocalDate.now().plusDays(daysToAdd)));
        decisionQualifiedFPLOS.setRateQualifiedId(rateId);
        decisionQualifiedFPLOS.setFplos("YYYYYYY");
        decisionQualifiedFPLOS.setCreateDate(new Date());
        return decisionQualifiedFPLOS;
    }

    private static PaceDecisionQualifiedFPLOS getPaceDecisionQualifiedFPLOS(int decisionId, Integer rateId, Integer i) {
        var paceDecisionQualifiedFPLOS = new PaceDecisionQualifiedFPLOS();
        // Set necessary properties for each instance
        paceDecisionQualifiedFPLOS.setDecisionId(decisionId);
        paceDecisionQualifiedFPLOS.setPropertyId(5);
        paceDecisionQualifiedFPLOS.setAccomTypeId(5);
        paceDecisionQualifiedFPLOS.setArrivalDate(LocalDateUtils.toDate(LocalDate.now().plusDays(i)));
        paceDecisionQualifiedFPLOS.setRateQualifiedId(rateId);
        paceDecisionQualifiedFPLOS.setFplos("YYYYYYY");
        paceDecisionQualifiedFPLOS.setCreateDate(new Date());
        return paceDecisionQualifiedFPLOS;
    }

    private List<TenantChannelRestrictionAdjustment> createTenantChannelRestrictionsList() {
        var channelRestriction = new TenantChannelRestrictionAdjustment();
        channelRestriction.setId(Integer.valueOf(RandomStringUtils.randomNumeric(3)));
        channelRestriction.setSrpGroupCode(RandomStringUtils.randomAlphabetic(7));

        channelRestriction.setFixedAdjustmentSun(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentSun(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentMon(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentMon(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentTue(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentTue(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentWed(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentWed(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentThu(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentThu(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentFri(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentFri(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentSat(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentSat(BigDecimal.ONE);

        channelRestriction.setStartDate(new Date());
        channelRestriction.setEndDate(new Date());

        return List.of(channelRestriction);
    }

    private List<ChannelRestrictionAdjustment> createChannelRestrictionsList() {
        var channelRestriction = new ChannelRestrictionAdjustment();
        channelRestriction.setId(Integer.valueOf(RandomStringUtils.randomNumeric(3)));
        channelRestriction.setBrandName(RandomStringUtils.randomAlphabetic(7));
        channelRestriction.setGlobalRegion(RandomStringUtils.randomAlphabetic(7));
        channelRestriction.setSrpGroupCode(RandomStringUtils.randomAlphabetic(7));

        channelRestriction.setFixedAdjustmentSun(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentSun(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentMon(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentMon(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentTue(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentTue(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentWed(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentWed(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentThu(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentThu(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentFri(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentFri(BigDecimal.ONE);

        channelRestriction.setFixedAdjustmentSat(BigDecimal.ONE);
        channelRestriction.setPercentageAdjustmentSat(BigDecimal.ONE);

        channelRestriction.setStartDate(new Date());
        channelRestriction.setEndDate(new Date());

        return List.of(channelRestriction);
    }

    private List<AgileProductRestrictionAssociation> createProductAssociationList() {
        var productRestriction = new AgileProductRestrictionAssociation();
        final Product product = new Product();
        product.setId(1);
        productRestriction.setProduct(product);
        final RateQualified rateQualified = new RateQualified();
        rateQualified.setId(27);
        productRestriction.setRateQualified(rateQualified);

        return List.of(productRestriction);
    }

    private List<ChannelSrpGroupIncludedSrpMapping> getChannelSrpGroupIncludedSrpMapping() {
        var mapping = new ChannelSrpGroupIncludedSrpMapping();
        mapping.setId(Integer.valueOf(RandomStringUtils.randomNumeric(3)));
        mapping.setSrpGroupCode(RandomStringUtils.randomAlphabetic(3));
        mapping.setIncludedSrpCode("Y");
        mapping.setCreatedDate(LocalDateTime.now());
        mapping.setLastUpdatedDate(LocalDateTime.now());
        mapping.setChannelRestrictionAdjustmentId(Integer.valueOf(RandomStringUtils.randomNumeric(3)));
        return List.of(mapping);
    }

    private List<CPDecisionBAROutput> createCPDecisionBarOutputs() {
        var decision = new CPDecisionBAROutput();
        final var product = new Product();
        product.setId(1);
        decision.setProduct(product);
        final var accomType = new AccomType();
        accomType.setId(1);
        decision.setAccomType(accomType);
        decision.setArrivalDate(org.joda.time.LocalDate.now());
        decision.setFinalBAR(BigDecimal.TEN);
        return List.of(decision);
    }

    @Test
    public void qualifiedRate() {
        RateQualified qualifiedRate = new RateQualified();
        qualifiedRate.setId(1);
        qualifiedRate.setName("2");
        qualifiedRate.setYieldable(3);
        qualifiedRate.setStatusId(1);
        qualifiedRate.setRateQualifiedTypeId(4);

        QualifiedRates.QualifiedRate qr = fplosService.qualifiedRate(qualifiedRate);

        assertEquals(1, qr.getRateQualifiedID());
        assertEquals("2", qr.getRateCodeName());
        assertEquals(3, qr.getYieldable());
        assertEquals(1, qr.getStatusID());
        assertEquals(4, qr.getRateQualifiedTypeId());
    }

    @Test
    public void adjustments() {
        final var adjustmentCalendar = new RateQualifiedAdjustment();
        adjustmentCalendar.setRateQualifiedId(1);
        adjustmentCalendar.setStartDate(new Date());
        adjustmentCalendar.setEndDate(new Date());
        adjustmentCalendar.setPostingRuleTypeId(2);
        adjustmentCalendar.setNetValue(3.0f);
        adjustmentCalendar.setNetValueTypeId(4);
        adjustmentCalendar.setAdjustmentType("type");

        final var rateAdjustmentCalendar = fplosService.rateAdjustmentCalendars(adjustmentCalendar);
        assertNotNull(rateAdjustmentCalendar);
        assertEquals("type", rateAdjustmentCalendar.getAdjustmentType());
    }

    @Test
    public void accomActivity() {
        var accomActivity = new AccomActivity();

        accomActivity.setAccomTypeId(0);
        accomActivity.setOccupancyDate(new Date());
        accomActivity.setAccomCapacity(new BigDecimal(5));
        accomActivity.setRoomsSold(new BigDecimal(6));
        accomActivity.setRoomsNotAvailableOther(new BigDecimal(7));
        accomActivity.setRoomsNotAvailableMaintenance(new BigDecimal(8));

        var aa = fplosService.accomActivity(accomActivity);

        assertEquals(0, aa.getAccomTypeID());
        assertNotNull(aa.getOccupancyDT());
        assertEquals(new BigDecimal(5), aa.getAccomCapacity());
        assertEquals(new BigDecimal(6), aa.getRoomsSold());
        assertEquals(new BigDecimal(7), aa.getRoomsNotAvailableOther());
        assertEquals(new BigDecimal(8), aa.getRoomsNotAvailableMaintenance());
    }

    @Test
    public void convertToString() {
        var localDate = LocalDate.of(2020, 1, 1);
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        assertEquals("2020-01-01", fplosService.convertToString(date));
    }

    @Test
    public void convertToLocalDateViaInstant() {
        var localDate = LocalDate.of(2020, 1, 1);
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Assertions.assertNotNull(fplosService.convertToLocalDateViaInstant(date));
    }

    @Test
    public void update() {
        fplosService.crudService = tenantCrudService();
        crudService.truncateTable(DecisionQualifiedFPLOS.TABLE_NAME);

        when(dateService.getCaughtUpDate()).thenReturn(LocalDateUtils.toDate(LocalDate.now()));

        Decision decision = new Decision();
        decision.setId(1);

        var rateQualified1 = UniqueRateQualified.createRateQualifiedByDateNameAndStatus(JavaLocalDateUtils.toDate(LocalDate.now()),
                JavaLocalDateUtils.toDate(LocalDate.now().plusYears(1)), 5, "R1", Status.ACTIVE.getId());

        var rateQualified2 = UniqueRateQualified.createRateQualifiedByDateNameAndStatus(JavaLocalDateUtils.toDate(LocalDate.now()),
                JavaLocalDateUtils.toDate(LocalDate.now().plusYears(1)), 5, "R2", Status.INACTIVE.getId());

        rateQualified1 = fplosService.crudService.save(rateQualified1);
        rateQualified2 = fplosService.crudService.save(rateQualified2);

        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 4);
        accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        tenantCrudService().save(accomClass);
        tenantCrudService().flushAndClear();
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClass);

        var recommendationResponse = new RecommendationResponse(new RecommendationContext(), new ConcurrentHashMap<>(), new S3Context(), null);
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1));
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1));
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), new RecommendationResponse.Fplos("NNNNNNN", 1));
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1));
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1));
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1));
        compress(recommendationResponse);

        // Should delete inactive rates from Decision_Qualified_FPLOS where Arrival_DT is greater than the optimization window start date
        var decisionQualifiedFplosList = new ArrayList<DecisionQualifiedFPLOS>();
        for (int i = 1; i <= 10; i++) {
            decisionQualifiedFplosList.add(getDecisionQualifiedFPLOS(1, rateQualified2.getId(), i, 5, accomType.getId()));
        }
        fplosService.crudService.save(decisionQualifiedFplosList);
        Assertions.assertEquals(10, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());

        fplosService.update(decision.getId(), recommendationResponse, List.of(rateQualified1, rateQualified2));

        Assertions.assertEquals(6, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());
        Assertions.assertEquals(16, fplosService.crudService.findAll(PaceDecisionQualifiedFPLOS.class).size());

        recommendationResponse.getRatesMap().clear();
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 1)); // update value
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 04), accomType.getId(), new RecommendationResponse.Fplos("NNNNNNN", 1)); // new decision
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete inactive rate
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete inactive rate
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), null); // delete inactive rate
        compress(recommendationResponse);
        decision.setId(2);

        //should delete inactive rates from Decision_Qualified_FPLOS where Arrival_DT is greater than the optimization window start date
        decisionQualifiedFplosList.clear();
        for (int i = 1; i <= 10; i++) {
            decisionQualifiedFplosList.add(getDecisionQualifiedFPLOS(2, rateQualified2.getId(), i, 5, accomType.getId()));
        }
        fplosService.crudService.save(decisionQualifiedFplosList);
        Assertions.assertEquals(16, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());

        fplosService.update(decision.getId(), recommendationResponse, List.of(rateQualified1, rateQualified2));

        tenantCrudService().flushAndClear();

        var fplosList = fplosService.crudService.findAll(DecisionQualifiedFPLOS.class);

        Assertions.assertEquals(2, fplosList.size());

        Assertions.assertEquals("YYYYYYY", getDecision(fplosList, 2023, 1, 3, rateQualified1.getId()).getFplos());
        Assertions.assertEquals("NNNNNNN", getDecision(fplosList, 2023, 1, 4, rateQualified1.getId()).getFplos());

        Assertions.assertEquals(2, getDecision(fplosList, 2023, 1, 3, rateQualified1.getId()).getDecisionId());
        Assertions.assertEquals(2, getDecision(fplosList, 2023, 1, 4, rateQualified1.getId()).getDecisionId());
        Assertions.assertEquals(28, fplosService.crudService.findAll(PaceDecisionQualifiedFPLOS.class).size());


        recommendationResponse.getRatesMap().clear();
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 3)); // update value
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 04), accomType.getId(), new RecommendationResponse.Fplos("NNNNNNN", 3)); // new decision
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete inactive rate
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete inactive rate
        recommendationResponse.add(rateQualified2.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), null); // delete inactive rate
        compress(recommendationResponse);
        decision.setId(3);

        //should delete inactive rates from Decision_Qualified_FPLOS where Arrival_DT is greater than the optimization window start date
        decisionQualifiedFplosList.clear();
        for (int i = 1; i <= 10; i++) {
            decisionQualifiedFplosList.add(getDecisionQualifiedFPLOS(3, rateQualified2.getId(), i, 5, accomType.getId()));
        }
        fplosService.crudService.save(decisionQualifiedFplosList);
        Assertions.assertEquals(12, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());

        fplosService.update(decision.getId(), recommendationResponse, List.of(rateQualified1, rateQualified2));

        tenantCrudService().flushAndClear();

        fplosList = fplosService.crudService.findAll(DecisionQualifiedFPLOS.class);

        Assertions.assertEquals(2, fplosList.size());

        Assertions.assertEquals("YYYYYYY", getDecision(fplosList, 2023, 1, 3, rateQualified1.getId()).getFplos());
        Assertions.assertEquals("NNNNNNN", getDecision(fplosList, 2023, 1, 4, rateQualified1.getId()).getFplos());

        Assertions.assertEquals(2, getDecision(fplosList, 2023, 1, 3, rateQualified1.getId()).getDecisionId());
        Assertions.assertEquals(2, getDecision(fplosList, 2023, 1, 4, rateQualified1.getId()).getDecisionId());
        Assertions.assertEquals(38, fplosService.crudService.findAll(PaceDecisionQualifiedFPLOS.class).size());


        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 4)); // re-add
        recommendationResponse.add(rateQualified1.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 4));  // re-add
        compress(recommendationResponse);
        decision.setId(4);

        //should delete inactive rates from Decision_Qualified_FPLOS where Arrival_DT is greater than the optimization window start date
        decisionQualifiedFplosList.clear();
        for (int i = 1; i <= 10; i++) {
            decisionQualifiedFplosList.add(getDecisionQualifiedFPLOS(4, rateQualified2.getId(), i, 5, accomType.getId()));
        }
        fplosService.crudService.save(decisionQualifiedFplosList);
        Assertions.assertEquals(12, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());

        fplosService.update(decision.getId(), recommendationResponse, List.of(rateQualified1, rateQualified2));

        var list = fplosService.crudService.findAll(PaceDecisionQualifiedFPLOS.class).stream()
                .filter(paceDecisionQualifiedFPLOS -> paceDecisionQualifiedFPLOS.getDecisionId() == 4)
                .collect(Collectors.toList());

        Assert.assertEquals(12, list.size());

        var rateQualified3 = UniqueRateQualified.createRateQualifiedByDateNameAndYieldable(JavaLocalDateUtils.toDate(LocalDate.now()),
                JavaLocalDateUtils.toDate(LocalDate.now().plusYears(1)), 5, "R3", 1);
        var rateQualified4 = UniqueRateQualified.createRateQualifiedByDateNameAndYieldable(JavaLocalDateUtils.toDate(LocalDate.now()),
                JavaLocalDateUtils.toDate(LocalDate.now().plusYears(1)), 5, "R4", 0);

        recommendationResponse.getRatesMap().clear();
        recommendationResponse.add(rateQualified3.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified3.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete this
        recommendationResponse.add(rateQualified3.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), new RecommendationResponse.Fplos("YYYYYYY", 3)); // update value
        recommendationResponse.add(rateQualified3.getId(), LocalDate.of(2023, 01, 04), accomType.getId(), new RecommendationResponse.Fplos("NNNNNNN", 3)); // new decision
        recommendationResponse.add(rateQualified4.getId(), LocalDate.of(2023, 01, 01), accomType.getId(), null); // delete non yieldable rate
        recommendationResponse.add(rateQualified4.getId(), LocalDate.of(2023, 01, 02), accomType.getId(), null); // delete non yieldable rate
        recommendationResponse.add(rateQualified4.getId(), LocalDate.of(2023, 01, 03), accomType.getId(), null); // delete non yieldable rate
        compress(recommendationResponse);
        decision.setId(5);

        //should delete non-yieldable rates from Decision_Qualified_FPLOS where Arrival_DT is greater than the optimization window start date
        decisionQualifiedFplosList.clear();
        for (int i = 1; i <= 10; i++) {
            decisionQualifiedFplosList.add(getDecisionQualifiedFPLOS(5, rateQualified4.getId(), i, 5, accomType.getId()));
        }
        fplosService.crudService.save(decisionQualifiedFplosList);
        Assertions.assertEquals(14, fplosService.crudService.findAll(DecisionQualifiedFPLOS.class).size());

        fplosService.update(decision.getId(), recommendationResponse, List.of(rateQualified3, rateQualified4));

        fplosList = fplosService.crudService.findAll(DecisionQualifiedFPLOS.class);
        Assertions.assertEquals(6, fplosList.size());

        Assertions.assertEquals("YYYYYYY", getDecision(fplosList, 2023, 1, 3, rateQualified3.getId()).getFplos());
        Assertions.assertEquals("NNNNNNN", getDecision(fplosList, 2023, 1, 4, rateQualified3.getId()).getFplos());

        Assertions.assertEquals(5, getDecision(fplosList, 2023, 1, 3, rateQualified3.getId()).getDecisionId());
        Assertions.assertEquals(5, getDecision(fplosList, 2023, 1, 4, rateQualified3.getId()).getDecisionId());
        Assertions.assertEquals(62, fplosService.crudService.findAll(PaceDecisionQualifiedFPLOS.class).size());
    }

    public static RecommendationResponse compress(RecommendationResponse recommendationResponse) {
        var iterator = recommendationResponse.getMap().entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            var key = entry.getKey();
            var value = entry.getValue();

            byte[] compressedBytes = GzipCompressionHelper.compressToBytes(value);
            recommendationResponse.getCompressed().put(key, compressedBytes);

            value.getDatesMap().clear();

            iterator.remove(); // Safely remove the current entry
        }

        return recommendationResponse;
    }

    private static DecisionQualifiedFPLOS getDecision(List<DecisionQualifiedFPLOS> fplosList, int year, int month, int day, int rateId) {
        return fplosList.stream()
                .filter(d -> isEquals(d, year, month, day, rateId))
                .findFirst()
                .get();
    }

    private static boolean isEquals(DecisionQualifiedFPLOS d, int year, int month, int day, int rateId) {
        return day == d.getArrivalDate().getDay() + 1 &&
                month == d.getArrivalDate().getMonth() + 1 &&
                year == d.getArrivalDate().getYear() + 1900 &&
                rateId == d.getRateQualifiedId();
    }

    @Test
    public void shouldBuildRecommendationRequestHilton() {
        LocalDate startDate = LocalDate.of(2023, 01, 01);
        LocalDate endDate = LocalDate.of(2024, 01, 01);
        LocalDate endDatePlus7 = LocalDate.of(2024, 01, 8);
        AccomClass accomClass = createAccomClass();

        PacmanWorkContextHelper.setWorkContext(createWorkContext("Hilton"));
        when(accommodationService.getAllAccomTypes()).thenReturn(createAccomTypes(5, accomClass));
        when(lastRoomValueDecisionService.getLastRoomValueDecisions(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createLastRoomValue());
        when(accommodationService.findAll()).thenReturn(List.of(accomClass));
        when(rateQualifiedService.getRateQualifiedFixedDetails(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());
        when(accomActivityRepository.findByDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createAccomActivities());
        when(rateQualifiedAdjustmentService.getRateQualifiedAdjustments(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7), true)).thenReturn(createRateAdjustments());
        when(fplosQualifiedRecommendationService.getDefaultAccomTypeId()).thenReturn(1);
        when(closeHighestBarService.getClosedHighestBarsForDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());

        when(productManagementService.search(any())).thenReturn(createCPDecisionBarOutputs());
        when(channelSrpGroupIncludedSrpMappingRepository.getSRPGrpIncludedSRPMappings()).thenReturn(getChannelSrpGroupIncludedSrpMapping());
        when(agileRatesConfigurationService.getAllProductRestrictionAssociations()).thenReturn(createProductAssociationList());
        when(channelRestrictionAdjustmentCurrencyCorrectionService.getTenantChannelRestrictionAdjustments()).thenReturn(createTenantChannelRestrictionsList());
        when(servicingCostByLOSService.getServicingCostForAllRatesByLOS()).thenReturn(getServicingCostByLosForRestrictions());
        when(configService.getBooleanParameterValue(PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.DIFF_ENABLED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(true);

        var decision = new Decision();
        decision.setId(1);
        final RecommendationRequest recommendationRequest = fplosService.buildRecommendationRequest(
                convertLocalDateToJavaUtilDate(startDate),
                convertLocalDateToJavaUtilDate(endDate),
                new Date(),
                new ArrayList<>(),
                new ArrayList<>(),
                decision,
                999,
                null);

        Assertions.assertNotNull(recommendationRequest);
        Assertions.assertEquals("LV0", recommendationRequest.getSpecialRatePlanName());
        Assertions.assertEquals(true, recommendationRequest.getSrpFplosAtTotalLevel());
        Assertions.assertEquals(1, recommendationRequest.getDefaultAccomTypeId());
        Assertions.assertEquals(999, recommendationRequest.getLastSuccessfulDecisionId());
        assertServicingCostData(recommendationRequest);
    }

    @Test
    public void shouldBuildRecommendationRequest() {
        LocalDate startDate = LocalDate.of(2023, 01, 01);
        LocalDate endDate = LocalDate.of(2024, 01, 01);
        LocalDate endDatePlus7 = LocalDate.of(2024, 01, 8);
        AccomClass accomClass = createAccomClass();

        PacmanWorkContextHelper.setWorkContext(createWorkContext("Hyatt"));
        when(accommodationService.getAllAccomTypes()).thenReturn(createAccomTypes(5, accomClass));
        when(lastRoomValueDecisionService.getLastRoomValueDecisions(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createLastRoomValue());
        when(accommodationService.findAll()).thenReturn(List.of(accomClass));
        when(rateQualifiedService.getRateQualifiedDetails(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());
        when(accomActivityRepository.findByDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(createAccomActivities());
        when(rateQualifiedAdjustmentService.getRateQualifiedAdjustments(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7), false)).thenReturn(createRateAdjustments());
        when(fplosQualifiedRecommendationService.getDefaultAccomTypeId()).thenReturn(1);
        when(closeHighestBarService.getClosedHighestBarsForDateRange(convertLocalDateToJavaUtilDate(startDate), convertLocalDateToJavaUtilDate(endDatePlus7))).thenReturn(new ArrayList<>());

        when(productManagementService.search(any())).thenReturn(createCPDecisionBarOutputs());
        when(channelSrpGroupIncludedSrpMappingRepository.getSRPGrpIncludedSRPMappings()).thenReturn(getChannelSrpGroupIncludedSrpMapping());
        when(agileRatesConfigurationService.getAllProductRestrictionAssociations()).thenReturn(createProductAssociationList());
        when(channelRestrictionAdjustmentCurrencyCorrectionService.getTenantChannelRestrictionAdjustments()).thenReturn(createTenantChannelRestrictionsList());
        when(configService.getBooleanParameterValue(PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT)).thenReturn(false);
        when(configService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.DIFF_ENABLED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(false);
        when(limitTotalService.getSRPsAtTotalLevel()).thenReturn(new ArrayList<String>());
        when(hospitalityRoomsService.getAllTypesOfZeroCapacityRTsRequiringPricingAndRestrictions()).thenReturn(new ArrayList<String>());
        when(configService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);

        var decision = new Decision();
        decision.setId(1);
        final RecommendationRequest recommendationRequest = fplosService.buildRecommendationRequest(
                convertLocalDateToJavaUtilDate(startDate),
                convertLocalDateToJavaUtilDate(endDate),
                new Date(),
                new ArrayList<>(),
                new ArrayList<>(),
                decision,
                998,
                null);

        Assertions.assertNotNull(recommendationRequest);
        Assertions.assertNull(recommendationRequest.getSpecialRatePlanName());
        Assertions.assertEquals(true, recommendationRequest.getSrpFplosAtTotalLevel());
        Assertions.assertEquals(1, recommendationRequest.getDefaultAccomTypeId());
        Assertions.assertEquals(998, recommendationRequest.getLastSuccessfulDecisionId());
        Assertions.assertNull(recommendationRequest.getServicingCosts());
    }

    @Test
    public void shouldParseRateDetails() {
        final RateQualifiedDetails rateQualifiedDetails = createRateQualifiedDetails();

        final RateDetails.RateDetail rateDetail = fplosService.rateDetail(rateQualifiedDetails);
        Assertions.assertNotNull(rateDetail);
        Assertions.assertNotNull(rateDetail.getRateQualifiedID());
        Assertions.assertNotNull(rateDetail.getAccomTypeID());
        Assertions.assertNotNull(rateDetail.getStartDateDT());
        Assertions.assertNotNull(rateDetail.getEndDateDT());
        Assertions.assertNotNull(rateDetail.getSunday());
        Assertions.assertNotNull(rateDetail.getMonday());
        Assertions.assertNotNull(rateDetail.getTuesday());
        Assertions.assertNotNull(rateDetail.getWednesday());
        Assertions.assertNotNull(rateDetail.getThursday());
        Assertions.assertNotNull(rateDetail.getFriday());
        Assertions.assertNotNull(rateDetail.getSaturday());
    }

    private List<IRateAdjustment> createRateAdjustments() {
        RateQualifiedAdjustment rateAdjustmentCalendar = new RateQualifiedAdjustment();
        rateAdjustmentCalendar.setStartDate(new Date());
        rateAdjustmentCalendar.setEndDate(new Date());
        rateAdjustmentCalendar.setPostingRuleTypeId(1);
        rateAdjustmentCalendar.setNetValue(2F);
        rateAdjustmentCalendar.setNetValueTypeId(3);
        rateAdjustmentCalendar.setAdjustmentType("Adj");
        return List.of(rateAdjustmentCalendar);
    }

    private List<AccomActivity> createAccomActivities() {
        AccomActivity accomActivity = new AccomActivity();
        accomActivity.setPropertyId(5);
        accomActivity.setOccupancyDate(new Date());
        accomActivity.setAccomCapacity(new BigDecimal(3));
        accomActivity.setRoomsSold(new BigDecimal(4));
        accomActivity.setRoomsNotAvailableMaintenance(new BigDecimal(5));
        accomActivity.setRoomsNotAvailableOther(new BigDecimal(6));
        accomActivity.setArrivals(new BigDecimal(7));
        accomActivity.setDepartures(new BigDecimal(8));
        accomActivity.setCancellations(new BigDecimal(9));
        accomActivity.setNoShows(new BigDecimal(10));
        accomActivity.setRoomRevenue(new BigDecimal(5));
        accomActivity.setFoodRevenue(new BigDecimal(5));
        accomActivity.setTotalRevenue(new BigDecimal(5));
        accomActivity.setTotalProfit(new BigDecimal(5));
        return List.of(accomActivity);
    }

    private static ActiveMQBytesMessage getMessage() {
        return new ActiveMQBytesMessage() {
            @SneakyThrows
            @Override
            public ByteSequence getContent() {
                var s = "{\"recommendationContext\":{\"clientCode\":\"\",\"propertyCode\":\"\",\"fiscalDate\":\"\",\"fileName\":\"response\"},\"datesMap\":{\"2023-03-28\":{\"ratesMap\":{\"9999\":{\"accomTypesMap\":{\"1\":[\"NYYYYYY\"]}}}}}}\n";
                return new ByteSequence(GzipCompressionUtil.compressValue(s));
            }
        };
    }

    public static WorkContextType createWorkContext(final String clientCode) {
        WorkContextType workContextType = new WorkContextType();
        workContextType.setPropertyCode("123");
        workContextType.setPropertyId(456);
        workContextType.setClientCode(clientCode);
        return workContextType;
    }

    private AccomClass createAccomClass() {
        AccomClass accomClass = new AccomClass(1, 1, BigDecimal.ONE);
        accomClass.setMasterClass(1);
        return accomClass;
    }

    private RateQualifiedDetails createRateQualifiedDetails() {
        final RateQualifiedDetails rateQualifiedDetails = new RateQualifiedDetails();
        rateQualifiedDetails.setRateQualifiedId(1);
        rateQualifiedDetails.setAccomTypeId(1);
        rateQualifiedDetails.setStartDate(new Date());
        rateQualifiedDetails.setEndDate(new Date());
        rateQualifiedDetails.setSunday(new BigDecimal("1"));
        rateQualifiedDetails.setMonday(new BigDecimal("1"));
        rateQualifiedDetails.setTuesday(new BigDecimal("1"));
        rateQualifiedDetails.setWednesday(new BigDecimal("1"));
        rateQualifiedDetails.setThursday(new BigDecimal("1"));
        rateQualifiedDetails.setFriday(new BigDecimal("1"));
        rateQualifiedDetails.setSaturday(new BigDecimal("1"));

        return rateQualifiedDetails;
    }

    private List<RateQualifiedDetails> createRateDetails() {
        return List.of(createRateQualifiedDetails());
    }

    private List<RateQualified> createRates() {
        return List.of(new RateQualified());
    }

    private List<AccomType> createAccomTypes(int propertyID, AccomClass accomClass) {
        AccomType accomType = new AccomType();
        accomType.setPropertyId(propertyID);
        accomType.setAccomClass(accomClass);
        accomType.setName("accomTypeName");
        accomType.setDescription("Test Accom Type creation");
        accomType.setAccomTypeCapacity(1);

        accomType.setAccomTypeCode("");
        accomType.setSystemDefault(0);
        accomType.setStatusId(1);
        accomType.setIsComponentRoom("false");
        accomType.setCreateDate(getCurrentTimestamp());

        return List.of(accomType);
    }

    private List<LastRoomValue> createLastRoomValue() {
        return List.of(new LastRoomValue());
    }


    @Test
    void validateFixedRateQualifiedId() {
        RateQualifiedFixed rateQualifiedFixed = getRateQualifiedFixed();

        FplosService fplosService = new FplosService();
        QualifiedRates.QualifiedRate qualifiedRate = fplosService.qualifiedRate(rateQualifiedFixed);

        assertEquals(11, qualifiedRate.getRateQualifiedID());
        assertEquals(3, qualifiedRate.getRateQualifiedTypeId());
    }

    private static RateQualifiedFixed getRateQualifiedFixed() {
        RateQualifiedFixed rateQualifiedFixed = new RateQualifiedFixed();
        rateQualifiedFixed.setId(1L);
        rateQualifiedFixed.setRateQualifiedTypeId(3);
        rateQualifiedFixed.setRateQualifiedId(11);
        return rateQualifiedFixed;
    }

    private List<ServicingCostByLOSForRestriction> getServicingCostByLosForRestrictions() {
        var servicingCostByLOSForRestriction = new ServicingCostByLOSForRestriction(1L, 1L, List.of(20.0, 30.0, 40.0, 50.0, 60.0, 70.0));
        return List.of(servicingCostByLOSForRestriction);
    }

    private static void assertServicingCostData(RecommendationRequest recommendationRequest) {
        var servicingCost = new ArrayList<>(recommendationRequest.getServicingCosts().getList()).get(0);
        Assertions.assertEquals(true, recommendationRequest.getProfitOptimizationEnabled());
        Assertions.assertEquals(1, servicingCost.getRateQualifiedID());
        Assertions.assertEquals(1, servicingCost.getAccomTypeID());
        Assertions.assertEquals(List.of(20.0, 30.0, 40.0, 50.0, 60.0, 70.0), servicingCost.getServicingCostsByLos());
    }

    @Test
    public void testCheckAndReloadSemaphoreSemaphoreDoesNotNeedReload() {
        // turn on semaphore
        System.setProperty("pacman.fplos.use.semaphore", "true");

        // set system property to 5
        System.setProperty("pacman.fplos.batch.jobs.at.once", "5");

        // Simulate semaphore not needing reload
        FplosService.semaphore = new Semaphore(5);

        // reload semaphore
        FplosService.checkAndReloadSemaphore();

        // Assert that semaphore has same value
        assertEquals(5, FplosService.semaphore.availablePermits());
    }

    @Test
    public void testLoadSemaphoreReleaseOnFailure() throws InterruptedException {
        var decision = new Decision();
        decision.setId(1);
        when(decisionService.createQualifiedFPLOSDecision()).thenReturn(decision);

        // turn on semaphore
        System.setProperty("pacman.fplos.use.semaphore", "true");

        // set system property to 16
        System.setProperty("pacman.fplos.batch.jobs.at.once", "16");

        // test data
        Date startDate = new Date();
        Date endDate = new Date();
        Date fiscalDate = new Date();
        boolean enableFplosService = true;
        boolean async = false;
        Long jobExecutionId = 1L;

        assertThrows(RuntimeException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, enableFplosService, async, jobExecutionId);
        });

        assertEquals(16, FplosService.semaphore.availablePermits());
    }

    @Test
    public void testLoadSemaphoreNotAcquiredWithZeroPermits() {
        // set system property to 0
        System.setProperty("pacman.fplos.batch.jobs.at.once", "0");
        // set time to 0 seconds
        System.setProperty("pacman.receive.timeout", "0");
        // turn on semaphore
        System.setProperty("pacman.fplos.use.semaphore", "true");

        // test data
        Date startDate = new Date();
        Date endDate = new Date();
        Date fiscalDate = new Date();
        Long jobExecutionId = 1L;

        assertThrows(InterruptedException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, true, true, jobExecutionId);
        });

        assertThrows(InterruptedException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, true, false, jobExecutionId);
        });
    }

    @Test
    public void testLoadSemaphoreNotAcquiredAfterAcquiringPermits() {

        // set system property to 5
        System.setProperty("pacman.fplos.batch.jobs.at.once", "5");
        // set time to 0 seconds
        System.setProperty("pacman.receive.timeout", "0");
        // turn on semaphore
        System.setProperty("pacman.fplos.use.semaphore", "true");

        FplosService.checkAndReloadSemaphore();

        try {
            FplosService.semaphore.tryAcquire(5000, TimeUnit.MILLISECONDS);
            log.info("Permits available: " + FplosService.semaphore.availablePermits());
            FplosService.semaphore.tryAcquire(5000, TimeUnit.MILLISECONDS);
            log.info("Permits available: " + FplosService.semaphore.availablePermits());
            FplosService.semaphore.tryAcquire(5000, TimeUnit.MILLISECONDS);
            log.info("Permits available: " + FplosService.semaphore.availablePermits());
            FplosService.semaphore.tryAcquire(5000, TimeUnit.MILLISECONDS);
            log.info("Permits available: " + FplosService.semaphore.availablePermits());
            FplosService.semaphore.tryAcquire(5000, TimeUnit.MILLISECONDS);
            log.info("Permits available: " + FplosService.semaphore.availablePermits());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // test data
        Date startDate = new Date();
        Date endDate = new Date();
        Date fiscalDate = new Date();
        Long jobExecutionId = 1L;

        assertThrows(InterruptedException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, true, true, jobExecutionId);
        });

        assertThrows(InterruptedException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, true, false, jobExecutionId);
        });
    }

    @Test
    public void testLoadSemaphoreReleaseAfterRefresh() {

        // set system property to 1
        System.setProperty("pacman.fplos.batch.jobs.at.once", "1");
        // set time to 0 seconds
        System.setProperty("pacman.receive.timeout", "0");
        // turn on semaphore
        System.setProperty("pacman.fplos.use.semaphore", "true");

        // reload
        FplosService.checkAndReloadSemaphore();

        // set system property to 0
        System.setProperty("pacman.fplos.batch.jobs.at.once", "0");

        // test data
        Date startDate = new Date();
        Date endDate = new Date();
        Date fiscalDate = new Date();
        Long jobExecutionId = 1L;

        assertThrows(InterruptedException.class, () -> {
            fplosService.load(startDate, endDate, fiscalDate, true, true, jobExecutionId);
        });

        assertEquals(0, FplosService.semaphore.availablePermits());
    }

    @Test
    void fetchFplosDecisionsFromService() {
        var startDate = convertLocalDateToJavaUtilDate(LocalDate.of(2025, 1, 27));
        var endDate = convertLocalDateToJavaUtilDate(LocalDate.of(2025, 1, 28));
        var lastUploadDate = convertLocalDateToJavaUtilDate(LocalDate.of(2025, 1, 25));

        when(decisionService.getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED)).thenReturn(2);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED)).thenReturn(false);
        when(rateQualifiedService.getAllYieldableActiveRateQualifiedIds()).thenReturn(Set.of(1, 2));
        when(decisionService.getLastUploadedDecisionIdByDecisionTypeAndProcessStatusId(lastUploadDate, DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED)).thenReturn(1);
        when(jmsTemplateForFplosBroker.sendAndReceive(anyString(), any())).thenReturn(getMessage());

        fplosService.fetchFplosDecisionsFromService(startDate, endDate, lastUploadDate);

        verify(decisionService, times(1)).getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED);
        verify(configService, times(1)).getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED);
        verify(rateQualifiedService, times(1)).getAllYieldableActiveRateQualifiedIds();
        verify(decisionService, times(1)).getLastUploadedDecisionIdByDecisionTypeAndProcessStatusId(lastUploadDate, DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED);
        verify(jmsTemplateForFplosBroker, times(1)).sendAndReceive(anyString(), any());
    }


    @Test
    void deleteFuturePaceFplosDecisionsFromS3() {
        when(decisionService.getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED)).thenReturn(2);
        when(fileMetadataService.getLatestSnapShotDate()).thenReturn(new Date());
        when(jmsTemplateForFplosBroker.sendAndReceive(anyString(), any())).thenReturn(getPaceCleanUpResponse());

        fplosService.deleteFuturePaceFplosDecisionsFromS3(CLIENT_CODE, PROPERTY_CODE);

        verify(decisionService, times(1)).getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED);
        verify(jmsTemplateForFplosBroker, times(1)).sendAndReceive(anyString(), any());
    }

    @Test
    void retrievePace() {
        var startDate = convertLocalDateToJavaUtilDate(LocalDate.of(2025, 1, 27));
        var endDate = convertLocalDateToJavaUtilDate(LocalDate.of(2025, 1, 28));

        when(decisionService.getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED)).thenReturn(2);
        when(jmsTemplateForFplosBroker.sendAndReceive(anyString(), any())).thenReturn(getMessage());

        fplosService.retrievePace(startDate, endDate, List.of(1, 2), List.of(3, 4));

        verify(decisionService, times(1)).getLastDecisionIdByDecisionTypeAndProcessStatusId(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.COMMITTED);
        verify(jmsTemplateForFplosBroker, times(1)).sendAndReceive(anyString(), any());
    }

    private static ActiveMQBytesMessage getPaceCleanUpResponse() {
        return new ActiveMQBytesMessage() {
            @SneakyThrows
            @Override
            public ByteSequence getContent() {
                var s = "{\"success\":\"true\"}";
                return new ByteSequence(GzipCompressionUtil.compressValue(s));
            }
        };
    }
}