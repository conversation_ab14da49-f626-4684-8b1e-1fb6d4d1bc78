package com.ideas.tetris.pacman.services.reportsquery.performancecomparison;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Created by idnrar on 28-11-2014.
 */
public class PerformanceComparisonRoomClassLevelDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private String dow;
    private int propertyID = 6;
    private int recordTypeId = 3;
    private int processStatusId = 13;
    private int accomClass1 = 6;
    private int accomClass2 = 7;
    private int isRolling = 0;
    private int paceDays = 20;
    private int paceDaysFormRollingMonth = 90;
    private LocalDate START_OF_MONTH_MINUS_TWO;
    private LocalDate START_OF_MONTH;
    private int START_OF_MONTH_MINUS_TWO_DIFF;
    private int START_OF_MONTH_DIFF;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        createTestData();
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        START_OF_MONTH_MINUS_TWO = DateUtil.getStartOfTheMonth(startDate.toDate(), -2);
        START_OF_MONTH = DateUtil.getStartOfTheMonth(startDate.toDate(), 0);
        START_OF_MONTH_MINUS_TWO_DIFF = Days.daysBetween(START_OF_MONTH_MINUS_TWO, startDate).getDays() * -1;
        START_OF_MONTH_DIFF = Days.daysBetween(START_OF_MONTH, startDate).getDays() * -1;

        int decisionIdAsOnBusinessDateMinusEleven = retrieveDecisionIDForGivenBusinessDate(11);
        int decisionIdAsOnBusinessDateMinusTwelve = retrieveDecisionIDForGivenBusinessDate(12);
        int decisionIdAsOnBusinessDateMinusThirteen = retrieveDecisionIDForGivenBusinessDate(13);
        int decisionIdAsOnBusinessDateMinusFourteen = retrieveDecisionIDForGivenBusinessDate(14);

        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQuery, decisionIdAsOnBusinessDateMinusEleven, 6, 7.39, 10.72, 11.85, 14.97);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQuery, decisionIdAsOnBusinessDateMinusTwelve, 5, 2.53, 3.72, 4.67, 5.65);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQuery, decisionIdAsOnBusinessDateMinusThirteen, 4, 4.53, 5.82, 1.56, 4.28);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQuery, decisionIdAsOnBusinessDateMinusFourteen, 3, 8.78, 9.89, 10.56, 7.28);

        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDate(insertQuery, 6, 11, 46, 47, 48, 49);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDate(insertQuery, 5, 12, 56, 57, 58, 59);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDate(insertQuery, 4, 13, 66, 67, 68, 69);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDate(insertQuery, 3, 14, 76, 77, 78, 79);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private int retrieveDecisionIDForGivenBusinessDate(int bizDate) {
        List secondDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(bizDate).toString() + "' " +
                " and Decision_Type_ID=1");
        if (secondDecisionIdList == null || secondDecisionIdList.isEmpty()) {
            return UniqueDecisionCreator.createDecisionFor(propertyID, startDate.minusDays(bizDate).toDate(), 1).getId();
        }
        return Integer.valueOf(secondDecisionIdList.get(0) + "");

    }


    private void populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDate(StringBuilder insertQuery, int occDate, int bizDate, int roomSold1, int roomSold2, int roomSold3, int roomSold4) {
        insertQuery.append(" update PACE_Accom_Activity set");
        insertQuery.append(" Accom_Capacity=47,Rooms_Sold=" + roomSold1 + ",Room_Revenue=4140.00000,Total_Revenue=4370.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1,Arrivals=43,");
        insertQuery.append(" Departures=44,Cancellations=0,No_Shows=0 where Accom_Type_ID=9 and Occupancy_DT='" + startDate.plusDays(occDate).toString() + "'");
        insertQuery.append(" and Business_Day_End_DT='" + startDate.minusDays(bizDate).toString() + "'");

        insertQuery.append(" update PACE_Accom_Activity set");
        insertQuery.append(" Accom_Capacity=48,Rooms_Sold=" + roomSold2 + ",Room_Revenue=4230.00000,Total_Revenue=4465.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1,Arrivals=44,");
        insertQuery.append(" Departures=45,Cancellations=0,No_Shows=0 where Accom_Type_ID=10 and Occupancy_DT='" + startDate.plusDays(occDate).toString() + "'");
        insertQuery.append(" and Business_Day_End_DT='" + startDate.minusDays(bizDate).toString() + "'");

        insertQuery.append(" update PACE_Accom_Activity set");
        insertQuery.append(" Accom_Capacity=50,Rooms_Sold=" + roomSold3 + ",Room_Revenue=4410.00000,Total_Revenue=4655.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1,Arrivals=46,");
        insertQuery.append(" Departures=47,Cancellations=0,No_Shows=0 where Accom_Type_ID=11 and Occupancy_DT='" + startDate.plusDays(occDate).toString() + "'");
        insertQuery.append(" and Business_Day_End_DT='" + startDate.minusDays(bizDate).toString() + "'");

        insertQuery.append(" update PACE_Accom_Activity set");
        insertQuery.append(" Accom_Capacity=51,Rooms_Sold=" + roomSold4 + ",Room_Revenue=4500.00000,Total_Revenue=4750.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1,Arrivals=47,");
        insertQuery.append(" Departures=48,Cancellations=1,No_Shows=0 where Accom_Type_ID=12 and Occupancy_DT='" + startDate.plusDays(occDate).toString() + "'");
        insertQuery.append(" and Business_Day_End_DT='" + startDate.minusDays(bizDate).toString() + "'");
    }

    private void populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(StringBuilder insertQuery, int decisionIdAsOnGivenBusiness,
                                                                                        int occDate, Double occNbr1, Double occNbr2, Double occNbr3, Double occNbr4) {
        insertQuery.append(" INSERT INTO [PACE_Accom_Occupancy_FCST]([Decision_ID],[Property_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + decisionIdAsOnGivenBusiness + ",6,9,'" + startDate.plusDays(occDate).toString() + "'," + occNbr1 + ",45.4567,GETDATE()),");
        insertQuery.append(" (" + decisionIdAsOnGivenBusiness + ",6,10,'" + startDate.plusDays(occDate).toString() + "'," + occNbr2 + ",33.4237,GETDATE()),");
        insertQuery.append(" (" + decisionIdAsOnGivenBusiness + ",6,11,'" + startDate.plusDays(occDate).toString() + "'," + occNbr3 + ",35.4587,GETDATE()),");
        insertQuery.append(" (" + decisionIdAsOnGivenBusiness + ",6,12,'" + startDate.plusDays(occDate).toString() + "'," + occNbr4 + ",24.4897,GETDATE())");
    }

    private LocalDate getLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + "," + recordTypeId + "," + processStatusId + ")");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    @Test
    public void shouldValidatePerformanceComparisonRoomClassLevelForMasterClassForStaticDate() {
        int numberOfRecords = 21;
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDays + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass2 + "," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY+2'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Master Class with Static Dates", reportData, numberOfRecords,
                "17", "104", "144", "14.44", 17, "8460.00", "8460.00", "81.35", "58.75", "66.85", "4.63");
        final int analysisDaysAheadOfBusinessDate = 7;
        assertEquals(analysisDaysAheadOfBusinessDate, reportData.stream().filter(row -> analysisFieldsAreNull(row, analysisDaysAheadOfBusinessDate)).count());
        final int comparisionDaysAheadOfBusinessDate = 5;
        assertEquals(comparisionDaysAheadOfBusinessDate, reportData.stream().filter(row -> comparisionFieldsAreNull(row, comparisionDaysAheadOfBusinessDate)).count());
    }

    private boolean comparisionFieldsAreNull(Object[] row, int daysAheadOfBusinessDate) {
        return (Integer.valueOf(row[0].toString()) < daysAheadOfBusinessDate) && row[2] == null && row[5] == null && row[7] == null;
    }

    private boolean analysisFieldsAreNull(Object[] row, int daysAheadOfBusinessDate) {
        return (Integer.valueOf(row[0].toString()) < daysAheadOfBusinessDate) && row[1] == null && row[3] == null && row[4] == null && row[6] == null && row[8] == null && row[9] == null;
    }

    @Test
    public void shouldValidatePerformanceComparisonRoomClassLevelForMasterClassForRollingDate() {
        int numberOfRecords = 21;
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDays + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass2 + "," + isRolling + ",'TODAY+5','TODAY+6','TODAY+3','TODAY+4'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Master Class with Rolling Dates", reportData, numberOfRecords,
                "17", "104", "144", "14.44", 17, "8460.00", "8460.00", "81.35", "58.75", "66.85", "4.63");
    }

    @Test
    public void shouldValidatePerformanceComparisonRoomClassLevelForNonMasterClassForStaticDate() {
        int numberOfRecords = 21;
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDays + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass1 + "," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY+2'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Static Dates", reportData, numberOfRecords,
                "17", "316", "436", "47.06", 17, "26100.00", "26100.00", "82.59", "59.86", "210.81", "4.48");
    }

    @Test
    public void shouldValidatePerformanceComparisonRoomClassLevelForNonMasterClassForRollingDate() {
        int numberOfRecords = 21;
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDays + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass1 + "," + isRolling + ",'TODAY+5','TODAY+6','TODAY+3','TODAY+4'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Rolling Dates", reportData, numberOfRecords,
                "17", "316", "436", "47.06", 17, "26100.00", "26100.00", "82.59", "59.86", "210.81", "4.48");
    }

    @Test
    public void shouldValidatePerformanceComparisonReportRoomClassForSOMMinusAndEOMPlusAccomClass2() {
        int numberOfRecords = 91;
        isRolling = 1;
        deleteOlderDataForDaysToArrivalDay(80);
        int systemDateDiffSOMMinusTwo = (int) Math.abs(START_OF_MONTH_MINUS_TWO_DIFF) + 80;
        StringBuilder insertQueryForMonthData = new StringBuilder();
        int decisionIdAsOnBusinessDateSOMMinus2 = retrieveDecisionIDForGivenBusinessDate(systemDateDiffSOMMinusTwo);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDateForRollingMonthOptions(insertQueryForMonthData, (int) START_OF_MONTH_MINUS_TWO_DIFF, systemDateDiffSOMMinusTwo, 10, 15, 25, 26);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQueryForMonthData, decisionIdAsOnBusinessDateSOMMinus2, (int) START_OF_MONTH_MINUS_TWO_DIFF, 5.73, 4.28, 5.62, 6.24);
        tenantCrudService().executeUpdateByNativeQuery(insertQueryForMonthData.toString());

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDaysFormRollingMonth + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass2 + "," + isRolling + ",'START_OF_MONTH-2','END_OF_MONTH+1','LY_START_OF_MONTH+10','LY_START_OF_MONTH+13'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Rolling Dates", reportData, numberOfRecords, "80", "15", "15", "4.28", 80, "4230.00", "4230.00", "282.00", "282.00", "33.42", "7.81");
    }


    @Test
    public void shouldValidatePerformanceComparisonReportRoomClassForSOMAndEOMPlusAccomClass2() {
        int numberOfRecords = 91;
        isRolling = 1;
        deleteOlderDataForDaysToArrivalDay(80);
        int systemDateDiffSOMMinusTwo = (int) Math.abs(START_OF_MONTH_DIFF) + 80;
        StringBuilder insertQueryForMonthData = new StringBuilder();
        int decisionIdAsOnBusinessDateSOMMinus2 = retrieveDecisionIDForGivenBusinessDate(systemDateDiffSOMMinusTwo);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDateForRollingMonthOptions(insertQueryForMonthData, (int) START_OF_MONTH_DIFF, systemDateDiffSOMMinusTwo, 15, 16, 17, 18);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQueryForMonthData, decisionIdAsOnBusinessDateSOMMinus2, (int) START_OF_MONTH_DIFF, 4.46, 5.12, 4.77, 3.58);
        tenantCrudService().executeUpdateByNativeQuery(insertQueryForMonthData.toString());

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDaysFormRollingMonth + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass2 + "," + isRolling + ",'START_OF_MONTH','END_OF_MONTH+1','LY_START_OF_MONTH+12','LY_START_OF_MONTH+13'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Rolling Dates", reportData, numberOfRecords, "80", "16", "16", "5.12", 80, "4230.00", "4230.00", "264.38", "264.38", "33.42", "6.53");
    }

    @Test
    public void shouldValidatePerformanceComparisonReportRoomClassForSOMAndEOMPlusAccomClass1() {
        int numberOfRecords = 91;
        isRolling = 1;
        deleteOlderDataForDaysToArrivalDay(79);
        int systemDateDiffSOMMinusTwo = (int) Math.abs(START_OF_MONTH_DIFF) + 79;
        StringBuilder insertQueryForMonthData = new StringBuilder();
        int decisionIdAsOnBusinessDateSOMMinus2 = retrieveDecisionIDForGivenBusinessDate(systemDateDiffSOMMinusTwo);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDateForRollingMonthOptions(insertQueryForMonthData, (int) START_OF_MONTH_DIFF, systemDateDiffSOMMinusTwo, 11, 22, 33, 44);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQueryForMonthData, decisionIdAsOnBusinessDateSOMMinus2, (int) START_OF_MONTH_DIFF, 6.45, 3.23, 4.68, 5.67);
        tenantCrudService().executeUpdateByNativeQuery(insertQueryForMonthData.toString());

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDaysFormRollingMonth + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass1 + "," + isRolling + ",'START_OF_MONTH','END_OF_MONTH+1','LY_START_OF_MONTH+12','LY_START_OF_MONTH+13'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Rolling Dates", reportData, numberOfRecords, "79", "88", "88", "16.80", 79, "15157.00", "15157.00", "172.24", "172.24", "105.41", "6.27");
    }

    @Test
    public void shouldValidatePerformanceComparisonReportRoomClassForSOMMinusAndEOMPlusAccomClass1() {
        int numberOfRecords = 91;
        isRolling = 1;
        deleteOlderDataForDaysToArrivalDay(79);
        int systemDateDiffSOMMinusTwo = (int) Math.abs(START_OF_MONTH_MINUS_TWO_DIFF) + 79;
        StringBuilder insertQueryForMonthData = new StringBuilder();
        int decisionIdAsOnBusinessDateSOMMinus2 = retrieveDecisionIDForGivenBusinessDate(systemDateDiffSOMMinusTwo);
        populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDateForRollingMonthOptions(insertQueryForMonthData, (int) START_OF_MONTH_MINUS_TWO_DIFF, systemDateDiffSOMMinusTwo, 21, 22, 23, 24);
        populateForecastPaceForAccomTypeForGivenBusinessDateForGivenDecisionId(insertQueryForMonthData, decisionIdAsOnBusinessDateSOMMinus2, (int) START_OF_MONTH_MINUS_TWO_DIFF, 4.31, 2.78, 3.67, 4.82);
        tenantCrudService().executeUpdateByNativeQuery(insertQueryForMonthData.toString());

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec usp_performance_comparision_report_rc " + propertyID + "," + paceDaysFormRollingMonth + ",'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(3).toString() + "','" + startDate.plusDays(4).toString() + "'," + accomClass1 + "," + isRolling + ",'START_OF_MONTH-2','END_OF_MONTH+1','LY_START_OF_MONTH+10','LY_START_OF_MONTH+13'");
        assertPerformanceComparisonAtRoomClassLevel("Performance Comparison Report at Room Class level For Non Master Class with Rolling Dates", reportData, numberOfRecords, "79", "68", "68", "12.80", 79, "15157.00", "15157.00", "222.90", "222.90", "105.41", "8.23");
    }

    private void deleteOlderDataForDaysToArrivalDay(int daysToArrival) {
        String deleteQuery = "delete from PACE_Accom_Activity where DATEDIFF(day,Business_Day_End_DT,Occupancy_DT) = " + daysToArrival;
        tenantCrudService().executeUpdateByNativeQuery(deleteQuery);
    }

    private void assertPerformanceComparisonAtRoomClassLevel(String Level, List<Object[]> reportData, int numberOfRecords, String pacePoint, String onBooksAnalysisPeriod, String onBooksComparisonPeriod, String OccupancyForecastAnalysisPeriod, int daysToArrivals, String analysisRoomRevenue, String comparisonRoomRevenue, String analysisADR, String comparisonADR, String forecastRevenue, String forecastADR) {
        assertEquals(numberOfRecords, (reportData.size()), Level + " - Number Of Records ");
        assertEquals(pacePoint, (reportData.get(daysToArrivals)[0].toString()), Level + " - Pace Point ");
        assertEquals(onBooksAnalysisPeriod, (reportData.get(daysToArrivals)[1].toString()), Level + " - Occupancy On Books - Analysis Period ");
        assertEquals(onBooksComparisonPeriod, (reportData.get(daysToArrivals)[2].toString()), Level + " - Occupancy On Books - Comparison Period ");
        assertEquals(OccupancyForecastAnalysisPeriod, (reportData.get(daysToArrivals)[3].toString()), Level + " - Occupancy Forecast - Analysis Period ");
        assertEquals(analysisRoomRevenue, (reportData.get(daysToArrivals)[4].toString()), Level + " - Room Revenue - Analysis Period ");
        assertEquals(comparisonRoomRevenue, (reportData.get(daysToArrivals)[5].toString()), Level + " - Room Revenue - Comparison Period ");
        assertEquals(analysisADR, (reportData.get(daysToArrivals)[6].toString()), Level + " - ADR - Analysis Period ");
        assertEquals(comparisonADR, (reportData.get(daysToArrivals)[7].toString()), Level + " - ADR - Comparison Period ");
        assertEquals(forecastRevenue, (reportData.get(daysToArrivals)[8].toString()), Level + " - Forecast Revenue - Analysis Period ");
        assertEquals(forecastADR, (reportData.get(daysToArrivals)[9].toString()), Level + " - Forecast ADR - Analysis Period ");
    }

    private void populateActivityPaceForAccomTypeForGivenOccupancyDateAndGivenBusinessDateForRollingMonthOptions(StringBuilder insertQuery, int occDate, int bizDate, int roomSold1, int roomSold2, int roomSold3, int roomSold4) {

        insertQuery.append("INSERT INTO [PACE_Accom_Activity]" +
                "([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Accom_Type_ID]" +
                ",[Accom_Capacity],[Rooms_Sold],[Rooms_Not_Avail_Maint],[Rooms_Not_Avail_Other],[Arrivals]" +
                ",[Departures],[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue]" +
                ",[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])" +
                "VALUES(" + propertyID + ",'" + startDate.plusDays(occDate).toString() + "',GETDATE(),'" + startDate.minusDays(bizDate).toString() + "',9" +
                ",48," + roomSold1 + ",0,0,41" +
                ",51,0,0,5310.00000,0,5310.00000" +
                ",1,1,1,GETDATE())");

        insertQuery.append("INSERT INTO [PACE_Accom_Activity]" +
                "([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Accom_Type_ID]" +
                ",[Accom_Capacity],[Rooms_Sold],[Rooms_Not_Avail_Maint],[Rooms_Not_Avail_Other],[Arrivals]" +
                ",[Departures],[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue]" +
                ",[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])" +
                "VALUES(" + propertyID + ",'" + startDate.plusDays(occDate).toString() + "',GETDATE(),'" + startDate.minusDays(bizDate).toString() + "',10" +
                ",48," + roomSold2 + ",0,0,45" +
                ",46,0,0,4230.00000,0,4230.00000" +
                ",1,1,1,GETDATE())");

        insertQuery.append("INSERT INTO [PACE_Accom_Activity]" +
                "([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Accom_Type_ID]" +
                ",[Accom_Capacity],[Rooms_Sold],[Rooms_Not_Avail_Maint],[Rooms_Not_Avail_Other],[Arrivals]" +
                ",[Departures],[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue]" +
                ",[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])" +
                "VALUES(" + propertyID + ",'" + startDate.plusDays(occDate).toString() + "',GETDATE(),'" + startDate.minusDays(bizDate).toString() + "',11" +
                ",48," + roomSold3 + ",0,0,33" +
                ",44,0,0,3414.00000,0,3414.00000" +
                ",1,1,1,GETDATE())");

        insertQuery.append("INSERT INTO [PACE_Accom_Activity]" +
                "([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Accom_Type_ID]" +
                ",[Accom_Capacity],[Rooms_Sold],[Rooms_Not_Avail_Maint],[Rooms_Not_Avail_Other],[Arrivals]" +
                ",[Departures],[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue]" +
                ",[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])" +
                "VALUES(" + propertyID + ",'" + startDate.plusDays(occDate).toString() + "',GETDATE(),'" + startDate.minusDays(bizDate).toString() + "',12" +
                ",48," + roomSold4 + ",0,0,33" +
                ",44,0,0,6433.00000,0,6433.00000" +
                ",1,1,1,GETDATE())");
    }

}
