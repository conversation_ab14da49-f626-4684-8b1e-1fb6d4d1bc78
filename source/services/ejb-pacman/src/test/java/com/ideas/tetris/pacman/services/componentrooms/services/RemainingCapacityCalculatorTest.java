package com.ideas.tetris.pacman.services.componentrooms.services;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.pacman.services.componentrooms.dto.ComponentRoomsConfiguration;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomActivity;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRMappingRoomNumbers;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRMktSegAccomActivity;
import com.ideas.tetris.pacman.services.componentrooms.entity.CROutOfOrder;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRTotalActivity;
import com.ideas.tetris.pacman.services.componentrooms.entity.PaceCRAccomActivity;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.Query;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

@MockitoSettings(strictness = Strictness.LENIENT)
public class RemainingCapacityCalculatorTest extends AbstractG3JupiterTest {

    private RemainingCapacityCalculator processor;
    private CrudService tenantCrudService;
    private CrudService globalCrudService;
    private ComponentRoomService componentRoomService;
    private ComponentRoomPhysicalRoomResolver componentRoomPhysicalRoomResolver;
    DateService mockDateService;
    Date businessDate;
    SimpleDateFormat sdf;

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private AlertService alertService;

    @BeforeEach
    public void setUp() {
        mockDateService = DateService.createTestInstance();
        mockDateService.setCrudService(tenantCrudService());
        mockDateService.setPropertyGroupService(new PropertyGroupService());
        mockDateService.setMultiPropertyCrudService(multiPropertyCrudService());
        businessDate = mockDateService.getBusinessDate();
        sdf = new SimpleDateFormat("yyyy-MM-dd");
        tenantCrudService = tenantCrudService();
        globalCrudService = globalCrudService();
        componentRoomService = new ComponentRoomService();
        componentRoomPhysicalRoomResolver = new ComponentRoomPhysicalRoomResolver();
        Mockito.when(alertService.getAlertType(Mockito.any())).thenReturn(new InfoMgrTypeEntity() {{
            setName(ComponentRoomService.CR_OOO_NOT_UPDATED_ALERT);
        }});
        Mockito.when(alertService.findExistingAlert(Mockito.any())).thenReturn(new InfoMgrInstanceEntity());
        Mockito.when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADJUST_MKT_ACCOM_ACTIVITY_COMPONENT_ROOMS_SOLD)).thenReturn(false);
        inject(componentRoomService, "tenantCrudService", tenantCrudService);
        inject(componentRoomService, "globalCrudService", globalCrudService);
        inject(componentRoomService, "configParamsService", configParamsService);
        inject(componentRoomService, "alertService", alertService);
        inject(componentRoomService, "componentRoomPhysicalRoomResolver", componentRoomPhysicalRoomResolver);
        inject(componentRoomPhysicalRoomResolver, "tenantCrudService", tenantCrudService);

        processor = new RemainingCapacityCalculator();
        inject(processor, "tenantCrudService", tenantCrudService);
        inject(processor, "globalCrudService", globalCrudService);
        inject(processor, "configParamsService", configParamsService);
        inject(processor, "componentRoomService", componentRoomService);
        flushAndClear();
        tenantCrudService().flushAndClear();
    }


    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithOnlyPhysical() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 10);
        AccomType cr1 = createAccomType("CR1", 10);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithOnlyPhysicalAndQuantity2() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 10);
        AccomType cr1 = createAccomType("CR1", 5);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithOnlyPhysicalWithMultipleCRAndQuantity2() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 20);
        AccomType p2 = createAccomType("P2", 20);
        AccomType p3 = createAccomType("P3", 10);
        AccomType cr3 = createAccomType("CR3", 5);
        AccomType cr1 = createAccomType("CR1", 10);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);
        tenantCrudService.save(p3);
        tenantCrudService.save(cr3);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, p3);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);
        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithOnlyPhysicalWithMultipleCRDifferentQuantityDiamondScenario() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 20);
        AccomType p2 = createAccomType("P2", 20);
        AccomType p3 = createAccomType("P3", 10);
        AccomType cr2 = createAccomType("CR2", 10);
        AccomType cr1 = createAccomType("CR1", 20);
        AccomType cr3 = createAccomType("CR3", 10);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);
        tenantCrudService.save(p3);
        tenantCrudService.save(cr2);
        tenantCrudService.save(cr3);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p2);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p3);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithCommonPhysicalInMultipleCRAndCrossImpactQuantity2() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 10);
        AccomType cr1 = createAccomType("CR1", 5);
        AccomType p3 = createAccomType("P3", 10);
        AccomType cr2 = createAccomType("CR2", 5);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);
        tenantCrudService.save(p3);
        tenantCrudService.save(cr2);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);


        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p1);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p3);
        crAccomTypeMapping.setCpAccomTypeQuantity(2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithNestedCR() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 10);
        AccomType p3 = createAccomType("P3", 10);
        AccomType p4 = createAccomType("P4", 10);
        AccomType cr1 = createAccomType("CR1", 10);
        AccomType cr2 = createAccomType("CR2", 10);
        AccomType cr3 = createAccomType("CR3", 10);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(p3);
        tenantCrudService.save(p4);
        tenantCrudService.save(cr1);
        tenantCrudService.save(cr2);
        tenantCrudService.save(cr3);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p3);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p4);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingFullShareWithNestedMultipleAndNestedCR() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 10);
        AccomType p3 = createAccomType("P3", 10);
        AccomType p4 = createAccomType("P4", 10);
        AccomType p5 = createAccomType("P5", 10);
        AccomType p6 = createAccomType("P6", 10);
        AccomType cr1 = createAccomType("CR1", 10);
        AccomType cr2 = createAccomType("CR2", 10);
        AccomType cr3 = createAccomType("CR3", 10);
        AccomType cr4 = createAccomType("CR4", 10);
        AccomType cr5 = createAccomType("CR5", 10);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(p3);
        tenantCrudService.save(p4);
        tenantCrudService.save(p4);
        tenantCrudService.save(p5);
        tenantCrudService.save(p6);
        tenantCrudService.save(cr1);
        tenantCrudService.save(cr2);
        tenantCrudService.save(cr3);
        tenantCrudService.save(cr4);
        tenantCrudService.save(cr5);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p3);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p4);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr3, cr2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr4, cr1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr4, p6);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr5, cr4);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr5, cr2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr5, p5);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        return crAccomTypeMappings;
    }

    private AccomType createAccomType(String accomTypeCode, int capacity) {
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        accomType.setPropertyId(TestProperty.H1.getId());
        accomType.setAccomTypeCode(accomTypeCode);
        accomType.setName(accomTypeCode);
        accomType.setDescription(accomTypeCode);
        accomType.setAccomTypeCapacity(capacity);
        if (accomTypeCode.startsWith("C")) {
            accomType.setIsComponentRoom("Y");
        }
        return accomType;
    }

    private CRAccomTypeMapping getCrAccomTypeMapping(AccomType componentRoomType, AccomType physicalRoomType) {
        CRAccomTypeMapping crAccomTypeMapping = new CRAccomTypeMapping();
        crAccomTypeMapping.setCrAccomType(componentRoomType);
        crAccomTypeMapping.setCpAccomType(physicalRoomType);
        crAccomTypeMapping.setCpAccomTypeQuantity(1);
        crAccomTypeMapping.setPropertyId(TestProperty.H1.getId());
        return crAccomTypeMapping;
    }

    private void populateAccomActivity(Map<AccomType, Integer> accomTypeCodeRoomSoldMap) {
        StringBuilder insertSql = new StringBuilder("");
        Query qryAccom1 = tenantCrudService().getEntityManager().createNativeQuery("update Accom_Activity SET SnapShot_DTTM =" + sdf.format(DateUtil.addDaysToDate(businessDate, -1)).trim());
        qryAccom1.executeUpdate();
        accomTypeCodeRoomSoldMap.keySet().stream().forEach(accomType -> {
            insertSql.setLength(0);
            insertSql.append("INSERT INTO Accom_Activity(Property_ID, Occupancy_DT,SnapShot_DTTM,Accom_type_id, Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue, File_Metadata_ID,Last_Updated_DTTM,createDate)");
            insertSql.append(" VALUES(5,'" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "',convert(date, GETDATE())," + accomType.getId() + " ," + accomType.getAccomTypeCapacity() + "," + accomTypeCodeRoomSoldMap.get(accomType) + ",0,0,0,0,0,0,10000,2000,12000,3,GETDATE(),GETDATE())");
            Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery(insertSql.toString());
            qryAccom.executeUpdate();
        });
        correctIndividualTrans();
        tenantCrudService.flushAndClear();
    }

    private void populatePaceAccomActivity(Map<AccomType, Integer> accomTypeCodeRoomSoldMap) {
        StringBuilder insertSql = new StringBuilder("");
        accomTypeCodeRoomSoldMap.keySet().stream().forEach(accomType -> {
            insertSql.setLength(0);
            insertSql.append("INSERT INTO PACE_Accom_Activity(Property_ID, Occupancy_DT,SnapShot_DTTM,Accom_type_id, Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue, File_Metadata_ID,Last_Updated_DTTM, Business_Day_End_DT, month_id, Year_id)");
            insertSql.append(" VALUES(5,'" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "',convert(date, GETDATE())," + accomType.getId() + " ," + accomType.getAccomTypeCapacity() + "," + accomTypeCodeRoomSoldMap.get(accomType) + ",0,0,0,0,0,0,10000,2000,12000,3,GETDATE(),'" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "',5,11)");
            Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery(insertSql.toString());
            qryAccom.executeUpdate();
        });
        correctIndividualTrans();
        tenantCrudService.flushAndClear();
    }

    private void updateAccomActivityForOutOfOrder(Map<AccomType, Integer> accomTypeOutOfOrderMap) {
        StringBuilder insertSql = new StringBuilder("");
        Query qryAccom1 = tenantCrudService().getEntityManager().createNativeQuery("update Accom_Activity SET SnapShot_DTTM =" + sdf.format(DateUtil.addDaysToDate(businessDate, -1)).trim());
        qryAccom1.executeUpdate();
        accomTypeOutOfOrderMap.keySet().stream().forEach(accomType -> {
            int roomsNotAvailableOther = new Double(accomTypeOutOfOrderMap.get(accomType) * 1.0 / 2).intValue(),
                    roomsNotAvailableMaintenance = accomTypeOutOfOrderMap.get(accomType) - roomsNotAvailableOther;
            insertSql.setLength(0);
            insertSql.append(" UPDATE Accom_Activity SET Rooms_Not_Avail_Maint = " + roomsNotAvailableOther + ", Rooms_Not_Avail_Other = " + roomsNotAvailableMaintenance + " ");
            insertSql.append(" WHERE accom_type_id ='" + accomType.getId() + "' AND Occupancy_DT = '" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "' AND File_metaData_ID = (Select Max(File_metaData_ID) From Accom_Activity)");

            Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery(insertSql.toString());
            qryAccom.executeUpdate();
        });
        correctIndividualTrans();
        tenantCrudService.flushAndClear();
    }

    private AccomType getAccomType(String accomTypeCode) {
        AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.ALL_BY_CODES, QueryParameter.with("accomTypeList", Arrays.asList(accomTypeCode)).
                and("propertyId", 5).parameters());
        return accomType;
    }

    private List<CRAccomTypeMapping> pushComponentRoomsMappingPartialShareWithOnlyPhysicalWithMultipleCR() {
        List<CRAccomTypeMapping> crAccomTypeMappings = new ArrayList<>();
        CRAccomTypeMapping crAccomTypeMapping = null;
        AccomType p1 = createAccomType("P1", 10);
        AccomType p2 = createAccomType("P2", 5);
        AccomType p3 = createAccomType("P3", 10);
        AccomType cr1 = createAccomType("CR1", 5);
        AccomType cr2 = createAccomType("CR3", 3);

        tenantCrudService.save(p1);
        tenantCrudService.save(p2);
        tenantCrudService.save(cr1);
        tenantCrudService.save(p3);
        tenantCrudService.save(cr2);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr1, p2);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, cr1);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);

        crAccomTypeMapping = getCrAccomTypeMapping(cr2, p3);
        crAccomTypeMappings.add(crAccomTypeMapping);
        tenantCrudService.save(crAccomTypeMappings);
        return crAccomTypeMappings;
    }

    private Set<CRMappingRoomNumbers> getCrMappingRoomNumbers(List<String> roomNumbers, CRAccomTypeMapping crAccomTypeMapping) {
        Set<CRMappingRoomNumbers> roomNumbersSet = new LinkedHashSet<>();
        for (String roomNumber : roomNumbers) {
            CRMappingRoomNumbers crMappingRoomNumber = new CRMappingRoomNumbers();
            crMappingRoomNumber.setCpRoomNumber(roomNumber);
            crMappingRoomNumber.setCrAccomTypeMapping(crAccomTypeMapping);
            crMappingRoomNumber.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            roomNumbersSet.add(crMappingRoomNumber);
        }
        return roomNumbersSet;
    }

    private void correctIndividualTrans() {
        tenantCrudService.executeUpdateByNativeQuery(" UPDATE Individual_Trans set Cancellation_DT = NULL ");
        tenantCrudService.executeUpdateByNativeQuery(" UPDATE File_Metadata SET Past_Window_Size = 45, Future_Window_Size = 365, SnapShot_DT = convert(date, getdate()) ");
    }

    private void populateTransData(int accom_type_Id, String roomNumber) {
        LocalDate startDate = new LocalDate();
        int propertyID = 5;
        String reservationNumber = accom_type_Id + roomNumber;
        if (StringUtils.isEmpty(roomNumber)) {
            reservationNumber = UUID.randomUUID().toString();
        }

        StringBuilder insertQuery = new StringBuilder("");
        insertQuery.append(" INSERT INTO [individual_trans]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM]) ");
        insertQuery.append("  VALUES (3," + propertyID + ",'" + reservationNumber + "','SS','" + startDate.minusDays(3).toString() + "' ");
        insertQuery.append(" ,'" + startDate.plusDays(4).toString() + "','" + startDate.minusDays(1).toString() + "',NULL,'QN'," + accom_type_Id + ",'1',500,0,0,0,0,500,NULL,NULL,'SHHQO1',400,'" + roomNumber + "','GT',0,1,GETDATE(),NULL,NULL,NULL)");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    @Test //scenario1
    public void correctedSoldsForMktAccomActivity() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "2"}};
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);

        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(2, crMktAccomActivities.size(), "crMktAccomActivities should have 2 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
//            put("CR1", 2);
            put(mktSegIdAccomTypIdSolds[0][1], 2);
            put(mktSegIdAccomTypIdSolds[1][1], 1);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }


    @Test
    public void correctedSoldsForMktAccomActivityShouldNotUpdateMktActivity() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "2"}};
        int addDays = 1;
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, addDays, 3);
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(2, crMktAccomActivities.size(), "crMktAccomActivities should have 2 elements");
        Integer marketSegId = Integer.valueOf(mktSegIdAccomTypIdSolds[0][0]);
        Integer accomTypeId = Integer.valueOf(mktSegIdAccomTypIdSolds[0][1]);
        List<MktSegAccomActivity> mktAccmActivities = tenantCrudService.findByNamedQuery(MktSegAccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_ID_AND_MKT_SEG_ID_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("occupancyDate", DateUtil.addDaysToDate(businessDate, addDays)).and("accomTypeId", accomTypeId).and("mktSegId", marketSegId).parameters());
        assertEquals(4, mktAccmActivities.get(0).getRoomsSold().intValue());
    }

    @Test //scenario1
    public void correctedSoldsForMktAccomActivityMultipleMarketSegment() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "2"},
                {"2", getAccomType("P1").getId().toString(), "5"},
                {"2", getAccomType("P2").getId().toString(), "7"},
                {"2", getAccomType("CR1").getId().toString(), "4"},
        };
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(4, crMktAccomActivities.size(), "crMktAccomActivities should have 4 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[0][1], 2);
            put(mktSegIdAccomTypIdSolds[1][1], 1);
        }});
        expectedMsRtRoomSolds.put(2, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[3][1], 1);
            put(mktSegIdAccomTypIdSolds[4][1], 3);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }

    @Test //scenario1
    public void correctedSoldsForMktAccomActivityMultipleMarketSegmentComposingOf2rooms() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysicalAndQuantity2();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "1"},
                {"2", getAccomType("P1").getId().toString(), "5"},
                {"2", getAccomType("P2").getId().toString(), "7"},
                {"2", getAccomType("CR1").getId().toString(), "2"},
        };
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(4, crMktAccomActivities.size(), "crMktAccomActivities should have 4 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[0][1], 2);
            put(mktSegIdAccomTypIdSolds[1][1], 1);
        }});
        expectedMsRtRoomSolds.put(2, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[3][1], 1);
            put(mktSegIdAccomTypIdSolds[4][1], 3);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }

    @Test // scenario4 when other component room reduces the capacity of common room type which cause the impact
    public void correctedSoldsForMktGrpTestForWithOnlyCommonPhysicalInMultipleCRAndCrossImpactQuantity2() throws Exception {
        pushComponentRoomsMappingFullShareWithCommonPhysicalInMultipleCRAndCrossImpactQuantity2();
        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "11"},
                {"1", getAccomType("P2").getId().toString(), "5"},
                {"1", getAccomType("P3").getId().toString(), "8"},
                {"1", getAccomType("CR1").getId().toString(), "2"},
                {"1", getAccomType("CR2").getId().toString(), "3"},
        };
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);

        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(3, crMktAccomActivities.size(), "crMktAccomActivities should have 3 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[0][1], 1);
            put(mktSegIdAccomTypIdSolds[1][1], 1);
            put(mktSegIdAccomTypIdSolds[2][1], 2);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }

    @Test
    public void correctedSoldsForMktAccomActivityForMultipleOccupancyWithDiffrentFileMetadataId() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();
        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "2"}};
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 2, 2);

        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(2, crMktAccomActivities.size(), "crMktAccomActivities should have 2 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
            put(mktSegIdAccomTypIdSolds[0][1], 2);
            put(mktSegIdAccomTypIdSolds[1][1], 1);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }

    @Disabled
    @Test //scenario7
    public void correctedSoldsForMktGrpTestForFullSharedWithMultipleAndNestedCR() throws Exception {
        pushComponentRoomsMappingFullShareWithNestedMultipleAndNestedCR();
        String[][] mktSegIdAccomTypIdSolds = {
                {"1", getAccomType("P1").getId().toString(), "5"},
                {"1", getAccomType("P2").getId().toString(), "5"},
                {"1", getAccomType("P3").getId().toString(), "4"},
                {"1", getAccomType("P4").getId().toString(), "4"},
                {"1", getAccomType("P5").getId().toString(), "2"},
                {"1", getAccomType("P6").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "1"},
                {"1", getAccomType("CR2").getId().toString(), "1"},
                {"1", getAccomType("CR3").getId().toString(), "1"},
                {"1", getAccomType("CR4").getId().toString(), "1"},
                {"1", getAccomType("CR5").getId().toString(), "1"},
        };
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);

        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();
        Map<String, String> fileMetaDataDateMap = componentRoomService.getStartEndDateForMaxFileMetaDataFromAccomActivity();
        Date startDate = new LocalDate(fileMetaDataDateMap.get(Constants.START_DATE)).toDate();
        Date endDate = new LocalDate(fileMetaDataDateMap.get(Constants.END_DATE)).toDate();
        LinkedHashSet<MktSegAccomActivity> crMktAccomActivities = processor.calculateSoldsForMktAccomActivity(componentRoomsConfigurations, startDate, endDate);
        assertEquals(6, crMktAccomActivities.size(), "crMktAccomActivities should have 6 elements");
        Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds = new HashMap<>();
        expectedMsRtRoomSolds.put(1, new HashMap<String, Integer>() {{
            put("P1", 1);
            put("P2", 1);
            put("P3", 1);
            put("P4", 1);
            put("P5", 1);
            put("P6", 1);
        }});
        assertMktRoomSolds(crMktAccomActivities, expectedMsRtRoomSolds);
    }

    private void assertMktRoomSolds(LinkedHashSet<MktSegAccomActivity> mktAccomActivities, Map<Integer, Map<String, Integer>> expectedMsRtRoomSolds) {
        mktAccomActivities.forEach(crMktAccomActivity -> {
            String accomTypId = crMktAccomActivity.getAccomTypeId().toString();
            Integer mkSgId = crMktAccomActivity.getMktSegId();
            assertEquals(expectedMsRtRoomSolds.get(mkSgId).get(accomTypId).intValue(), crMktAccomActivity.getRoomsSold().intValue(), "Solds should match for the MS: " + mkSgId + " AccTypeID: " + accomTypId);
        });
    }

    private void populateMktAccomActivity(String[][] mktSegIdAccomTypIdSolds, int addDays, int fileMetaDataId) {
        StringBuilder insertSql = new StringBuilder("");
        Query qryAccom1 = tenantCrudService().getEntityManager().createNativeQuery("update Accom_Activity SET SnapShot_DTTM =" + sdf.format(DateUtil.addDaysToDate(businessDate, -1)).trim());
        qryAccom1.executeUpdate();
        Arrays.stream(mktSegIdAccomTypIdSolds).forEach((msRTSolds) -> {
            insertSql.setLength(0);
            insertSql.append("INSERT INTO Mkt_Accom_Activity(Property_ID, Occupancy_DT,SnapShot_DTTM, mkt_seg_id, Accom_type_id, Rooms_Sold,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue, File_Metadata_ID,Last_Updated_DTTM,createDate)");
            insertSql.append(" VALUES(5,'" + sdf.format(DateUtil.addDaysToDate(businessDate, addDays)) + "',convert(date, GETDATE())," + msRTSolds[0] + "," + msRTSolds[1] + "," + msRTSolds[2] + ",0,0,0,0,10000,2000,12000," + fileMetaDataId + ",GETDATE(),GETDATE())");
            Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery(insertSql.toString());
            qryAccom.executeUpdate();
        });
        correctIndividualTrans();
    }

    // calculation for out of order
    @Test // scenario1
    public void calculateOutOfOrderTestSimpleFullShareScenario() throws ParseException {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 4);
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 4);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
    }


    @Test// scenario1
    public void calculateOutOfOrderTestSimpleFullShareScenarioQuantity2() throws ParseException {

        pushComponentRoomsMappingFullShareWithOnlyPhysicalAndQuantity2();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 2);
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 4);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
    }


    @Test// scenario3
    public void calculateOutOfOrderTestNestedHybridFullShareScenario() throws ParseException {

        pushComponentRoomsMappingFullShareWithOnlyPhysicalWithMultipleCRAndQuantity2();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 10);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 9);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 3);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 10);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 9);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 4);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 5);
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 3);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
    }

    @Test // scenario4
    public void calculateOutOfOrderTestForFullSharedWithOnlyCommonPhysicalInMultipleCRQuantity2() throws Exception {

        pushComponentRoomsMappingFullShareWithCommonPhysicalInMultipleCRAndCrossImpactQuantity2();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 6);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 8);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR2"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 6);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 5);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 8);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 3);
        expectedOutOfOrder.put("CR2_" + getAccomType("CR2").getId(), 4);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
    }

    @Test //scenario5
    public void calculatedOutOfOrderTestForFullSharedWithNestedCR() throws Exception {

        pushComponentRoomsMappingFullShareWithNestedCR();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P4"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR2"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 3);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 5);
        expectedOutOfOrder.put("P4_" + getAccomType("P4").getId(), 4);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 3);
        expectedOutOfOrder.put("CR2_" + getAccomType("CR2").getId(), 5);
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 5);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));

    }

    @Test //scenario5
    public void calculatedOutOfOrderTestForFullSharedWithNestedCRWithUserOverride() throws Exception {

        pushComponentRoomsMappingFullShareWithNestedCR();

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P4"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR2"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);

        accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("CR2"), 6);
        populateCROOOByUser(accomTypeOutOfOrderMap);

        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 3);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 5);
        expectedOutOfOrder.put("P4_" + getAccomType("P4").getId(), 4);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 3);
        expectedOutOfOrder.put("CR2_" + getAccomType("CR2").getId(), 6);
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 6);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));

    }

    @Test //scenario3
    public void calculatedOutOfOrderTestForHybridSharedWithNestedCRWithOutOverride() throws Exception {
        //contains full partial and (one partial and one full shared participant)
        List<CRAccomTypeMapping> crAccomTypeMappings = pushComponentRoomsMappingPartialShareWithOnlyPhysicalWithMultipleCR();

        Set<CRMappingRoomNumbers> roomNumberses = getCrMappingRoomNumbers(Arrays.asList("P126", "P128", "P129", "P130", "P131"), crAccomTypeMappings.get(0));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        roomNumberses = getCrMappingRoomNumbers(Arrays.asList("CR126", "CR128", "CR129"), crAccomTypeMappings.get(2));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        roomNumberses = getCrMappingRoomNumbers(Arrays.asList("P3126", "P3128", "P3129"), crAccomTypeMappings.get(3));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        AccomType accomType = getAccomType("CR3");
        populateTransData(accomType.getId(), "CR3126");

        accomType = getAccomType("CR1");
        populateTransData(accomType.getId(), "CR130");
        populateTransData(accomType.getId(), "CR127");

        accomType = getAccomType("P1");
        populateTransData(accomType.getId(), "P126");
        populateTransData(accomType.getId(), "P127");

        accomType = getAccomType("P3");
        populateTransData(accomType.getId(), "P3126");
        populateTransData(accomType.getId(), "P3128");

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(businessDate));

        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 5);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 3);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 3);
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 1);
        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
        assertCROutOfOrderIsOverriden(new String[][]{{"CR3", "1"}});
    }

    @Test //scenario3
    public void calculatedOutOfOrderTestForHybridSharedWithNestedCRWithOverride() throws Exception {
        //contains full partial and (one partial and one full shared participant)
        List<CRAccomTypeMapping> crAccomTypeMappings = pushComponentRoomsMappingPartialShareWithOnlyPhysicalWithMultipleCR();

        Set<CRMappingRoomNumbers> roomNumberses = getCrMappingRoomNumbers(Arrays.asList("P126", "P128", "P129", "P130", "P131"), crAccomTypeMappings.get(0));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        roomNumberses = getCrMappingRoomNumbers(Arrays.asList("CR126", "CR128", "CR129"), crAccomTypeMappings.get(2));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        roomNumberses = getCrMappingRoomNumbers(Arrays.asList("P3126", "P3128", "P3129"), crAccomTypeMappings.get(3));
        tenantCrudService().save(roomNumberses);
        tenantCrudService().flushAndClear();

        AccomType accomType = getAccomType("CR3");
        populateTransData(accomType.getId(), "CR3126");

        accomType = getAccomType("CR1");
        populateTransData(accomType.getId(), "CR130");
        populateTransData(accomType.getId(), "CR127");

        accomType = getAccomType("P1");
        populateTransData(accomType.getId(), "P126");
        populateTransData(accomType.getId(), "P127");

        accomType = getAccomType("P3");
        populateTransData(accomType.getId(), "P3126");
        populateTransData(accomType.getId(), "P3128");

        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 3);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 0);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 1);
        populateCROOOByUser(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(businessDate));
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 5);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 3);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 3);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 5);
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 3); // OOO can not be  greater than Capacity.
        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
        assertCROutOfOrderIsOverriden(new String[][]{{"CR1", "5"}, {"CR3", "3"}});
    }

    private void assertCROutOfOrderIsOverriden(String[][] expected) {
        List<CROutOfOrder> crOutOfOrders = tenantCrudService.findAll(CROutOfOrder.class);
        Map<String, Integer> accomTypeCodeOOO = crOutOfOrders.stream().collect(Collectors.toMap(cr -> cr.getAccomType().getAccomTypeCode(), CROutOfOrder::getOutOfOrder));
        for (int i = 0; i < expected.length; i++) {
            assertEquals(Integer.parseInt(expected[i][1]), accomTypeCodeOOO.get(expected[i][0]).intValue(), "OOO vlaue not matching in CROutOfOrder table for: " + expected[i][0]);
        }
    }

    @Test //scenario4
    public void calculateOutOfOrderTestForFullSharedWithOnlyPhysicalWithMultipleCRDifferentQuantityDiamondScenario() throws Exception {

        pushComponentRoomsMappingFullShareWithOnlyPhysicalWithMultipleCRDifferentQuantityDiamondScenario();
        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();

        accomTypeOutOfOrderMap.put(getAccomType("CR3"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("CR2"), 0);
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 5);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 4);
        accomTypeOutOfOrderMap.put(getAccomType("P3"), 2);

        populateAccomActivityOutOfOrder(accomTypeOutOfOrderMap);
        Map<String, Map<Integer, Integer>> outOfOrderMap = processor.calculateOutOfOrder(sdf.format(DateUtil.addDaysToDate(businessDate, 1)), sdf.format(DateUtil.addDaysToDate(businessDate, 1)), LocalDate.now().toString());
        assertEquals(1, outOfOrderMap.size(), "activityDtos should have 1 elements");

        Map<String, Integer> expectedOutOfOrder = new HashMap<>();
        expectedOutOfOrder.put("CR3_" + getAccomType("CR3").getId(), 3);
        expectedOutOfOrder.put("CR2_" + getAccomType("CR2").getId(), 2);
        expectedOutOfOrder.put("CR1_" + getAccomType("CR1").getId(), 5);
        expectedOutOfOrder.put("P1_" + getAccomType("P1").getId(), 5);
        expectedOutOfOrder.put("P2_" + getAccomType("P2").getId(), 4);
        expectedOutOfOrder.put("P3_" + getAccomType("P3").getId(), 2);

        assertOutOfOrder(expectedOutOfOrder, outOfOrderMap.get(sdf.format(DateUtil.addDaysToDate(businessDate, 1))));
    }

    private void assertOutOfOrder(Map<String, Integer> expectedOutOfOrderMap, Map<Integer, Integer> actualOutOfOrder) {
        expectedOutOfOrderMap.keySet().stream().forEach(accomTypeCode_accomTypeId -> {
            String[] codeAndId = accomTypeCode_accomTypeId.split("_");
            assertEquals(expectedOutOfOrderMap.get(accomTypeCode_accomTypeId), actualOutOfOrder.get(Integer.parseInt(codeAndId[1])), "Out Of Order should match for the " + codeAndId[0]);
        });
    }

    private void populateAccomActivityOutOfOrder(Map<AccomType, Integer> accomTypeOutOfOrderMap) {
        StringBuilder insertSql = new StringBuilder("");
        accomTypeOutOfOrderMap.keySet().stream().forEach(accomType -> {
            insertSql.setLength(0);

            int roomsNotAvailableOther = new Double(accomTypeOutOfOrderMap.get(accomType) * 1.0 / 2).intValue(),
                    roomsNotAvailableMaintainance = accomTypeOutOfOrderMap.get(accomType) - roomsNotAvailableOther;

            insertSql.append("INSERT INTO Accom_Activity(Property_ID, Occupancy_DT,SnapShot_DTTM,Accom_type_id, Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue, File_Metadata_ID,Last_Updated_DTTM,createDate)");
            insertSql.append(" VALUES(5,'" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "',convert(date, GETDATE())," + accomType.getId() + " ," + accomType.getAccomTypeCapacity() + "," + 0 + "," + roomsNotAvailableOther + "," + roomsNotAvailableMaintainance + ",0,0,0,0,10000,2000,12000,3,GETDATE(),GETDATE())");

            Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery(insertSql.toString());
            qryAccom.executeUpdate();
        });
        correctIndividualTrans();
    }

    @Test
    public void calculateAndSaveRemainingCapacityForBDETest() throws Exception {

        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        Map<AccomType, Integer> accomTypeRoomSoldMap = new HashMap<>();
        accomTypeRoomSoldMap.put(getAccomType("P1"), 4);
        accomTypeRoomSoldMap.put(getAccomType("P2"), 3);
        accomTypeRoomSoldMap.put(getAccomType("CR1"), 2);

        populateAccomActivity(accomTypeRoomSoldMap);
        processor.calculateAndSaveRemainingCapacity(true);
        List<CRAccomActivity> accomList = tenantCrudService.findByNamedQuery(CRAccomActivity.ALL);
        List<PaceCRAccomActivity> paceAccomList = tenantCrudService.findByNamedQuery(PaceCRAccomActivity.ALL);
        assertEquals(3, accomList.size(), "There should be 3 crAccomactivity ");
        assertEquals(3, paceAccomList.size(), "There should be 3 PaceCrAccomactivity ");

    }

    @Test
    public void calculateAndSaveRemainingCapacityForCDPTest() throws Exception {

        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        Map<AccomType, Integer> accomTypeRoomSoldMap = new HashMap<>();
        accomTypeRoomSoldMap.put(getAccomType("P1"), 4);
        accomTypeRoomSoldMap.put(getAccomType("P2"), 3);
        accomTypeRoomSoldMap.put(getAccomType("CR1"), 2);

        populateAccomActivity(accomTypeRoomSoldMap);
        processor.calculateAndSaveRemainingCapacity(false);
        List<CRAccomActivity> accomList = tenantCrudService.findByNamedQuery(CRAccomActivity.ALL);
        List<PaceCRAccomActivity> paceAccomList = tenantCrudService.findByNamedQuery(PaceCRAccomActivity.ALL);
        assertEquals(3, accomList.size(), "There should be 3 crAccomactivity ");
        assertEquals(0, paceAccomList.size(), "There should be 3 PaceCrAccomactivity ");
    }

    @Test
    public void calculateOOOAndSaveRemainingCapacityForBDETest() throws Exception {

        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        Map<AccomType, Integer> accomTypeRoomSoldMap = new HashMap<>();
        accomTypeRoomSoldMap.put(getAccomType("P1"), 4);
        accomTypeRoomSoldMap.put(getAccomType("P2"), 3);
        accomTypeRoomSoldMap.put(getAccomType("CR1"), 2);

        populateAccomActivity(accomTypeRoomSoldMap);
        populatePaceAccomActivity(accomTypeRoomSoldMap);
        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 1);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 2);

        updateAccomActivityForOutOfOrder(accomTypeOutOfOrderMap);
        processor.calculateAndSaveOutOfOrder(true);

        tenantCrudService.flushAndClear();
        List<AccomActivity> accomActivities = getAccomActivities(Arrays.asList("CR1", "P1", "P2"));

        assertEquals(3, accomActivities.size(), "There should be 3 Accomactivity ");
        Map<Integer, Integer> outOfOrderMap = accomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertOutOfOrder(outOfOrderMap);

        String sql = getPaceAccomActivitySelectSql();
        List<PaceAccomActivity> paceAccomActivities = tenantCrudService.findByNativeQuery(sql, null, PaceAccomActivity.class);
        assertEquals(3, paceAccomActivities.size(), "There should be 3 PaceAccomactivity ");
        outOfOrderMap = paceAccomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertEquals(0, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 0");
        assertEquals(2, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 2");
        calculateOOOForUserOverrideBDETest();

    }

    private void calculateOOOForUserOverrideBDETest() throws Exception {
        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 9);
        populateCROOOByUser(accomTypeOutOfOrderMap);
        processor.calculateAndSaveOutOfOrder(true);

        tenantCrudService.flushAndClear();
        List<AccomActivity> accomActivities = getAccomActivities(Arrays.asList("CR1", "P1", "P2"));
        assertEquals(3, accomActivities.size(), "There should be 3 Accomactivity ");
        Map<Integer, Integer> outOfOrderMap = accomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertOutOfOrderForOverride(outOfOrderMap);

        String sql = getPaceAccomActivitySelectSql();
        List<PaceAccomActivity> paceAccomActivities = tenantCrudService.findByNativeQuery(sql, null, PaceAccomActivity.class);
        assertEquals(3, paceAccomActivities.size(), "There should be 3 PaceAccomactivity ");
        outOfOrderMap = paceAccomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertEquals(0, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 0");
        assertEquals(9, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 2");
    }

    @Test
    public void calculateOOOAndSaveRemainingCapacityForCDPTest() throws Exception {

        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        Map<AccomType, Integer> accomTypeRoomSoldMap = new HashMap<>();
        accomTypeRoomSoldMap.put(getAccomType("P1"), 4);
        accomTypeRoomSoldMap.put(getAccomType("P2"), 3);
        accomTypeRoomSoldMap.put(getAccomType("CR1"), 2);

        populateAccomActivity(accomTypeRoomSoldMap);
        populatePaceAccomActivity(accomTypeRoomSoldMap);
        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("P1"), 1);
        accomTypeOutOfOrderMap.put(getAccomType("P2"), 2);

        updateAccomActivityForOutOfOrder(accomTypeOutOfOrderMap);
        processor.calculateAndSaveOutOfOrder(false);

        tenantCrudService.flushAndClear();
        List<AccomActivity> accomActivities = getAccomActivities(Arrays.asList("CR1", "P1", "P2"));

        Map<Integer, Integer> outOfOrderMap = accomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertOutOfOrder(outOfOrderMap);

        String sql = getPaceAccomActivitySelectSql();
        List<PaceAccomActivity> paceAccomActivities = tenantCrudService.findByNativeQuery(sql, null, PaceAccomActivity.class);
        outOfOrderMap = paceAccomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertEquals(0, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 0");
        calculateOOOForUserOverrideCDPTest();
    }

    private void calculateOOOForUserOverrideCDPTest() throws Exception {
        Map<AccomType, Integer> accomTypeOutOfOrderMap = new HashMap<>();
        accomTypeOutOfOrderMap.put(getAccomType("CR1"), 9);
        populateCROOOByUser(accomTypeOutOfOrderMap);
        processor.calculateAndSaveOutOfOrder(false);

        tenantCrudService.flushAndClear();
        List<AccomActivity> accomActivities = getAccomActivities(Arrays.asList("CR1", "P1", "P2"));

        assertEquals(3, accomActivities.size(), "There should be 3 Accomactivity ");
        Map<Integer, Integer> outOfOrderMap = accomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertOutOfOrderForOverride(outOfOrderMap);

        String sql = getPaceAccomActivitySelectSql();
        List<PaceAccomActivity> paceAccomActivities = tenantCrudService.findByNativeQuery(sql, null, PaceAccomActivity.class);
        assertEquals(3, paceAccomActivities.size(), "There should be 3 PaceAccomactivity ");
        outOfOrderMap = paceAccomActivities.stream().collect(Collectors.toMap(accomActivity -> accomActivity.getAccomTypeId(), accomActivity -> accomActivity.getRoomsNotAvailableTotal().intValue()));
        assertEquals(0, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 0");
        assertEquals(0, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 0");
    }

    private List<AccomActivity> getAccomActivities(List<String> accomTypeCodeList) {
        return tenantCrudService.findByNamedQuery(AccomActivity.ACTIVE_BY_DATE_RANGE_AND_ACCOM_TYPE_CODE,
                QueryParameter.with("startDate", sdf.format(DateUtil.addDaysToDate(businessDate, 1))).and("endDate", sdf.format(DateUtil.addDaysToDate(businessDate, 1))).
                        and("accomTypeCode", accomTypeCodeList).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private void assertOutOfOrder(Map<Integer, Integer> outOfOrderMap) {
        assertEquals(1, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 1");
        assertEquals(2, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 2");
        assertEquals(2, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 2");
    }

    private void assertOutOfOrderForOverride(Map<Integer, Integer> outOfOrderMap) {
        assertEquals(1, outOfOrderMap.get(getAccomType("P1").getId()).intValue(), "OutOfOrder For P1 should be 1");
        assertEquals(2, outOfOrderMap.get(getAccomType("P2").getId()).intValue(), "OutOfOrder For P2 should be 2");
        assertEquals(9, outOfOrderMap.get(getAccomType("CR1").getId()).intValue(), "OutOfOrder For CR1 should be 2");
    }

    private String getPaceAccomActivitySelectSql() {
        return " SELECT * FROM PACE_Accom_Activity " +
                " WHERE Occupancy_DT = '" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "'" +
                " AND Accom_Type_ID IN (" + getAccomType("P1").getId() + "," + getAccomType("P2").getId() + "," + getAccomType("CR1").getId() + ") " +
                " AND File_Metadata_ID = (Select MAX(File_Metadata_ID) FROM PACE_Accom_Activity) ";
    }

    private void populateCROOOByUser(Map<AccomType, Integer> accomTypeOutOfOrderMap) {
        accomTypeOutOfOrderMap.keySet().stream().forEach(accomType -> {
            CROutOfOrder crOutOfOrder = createCROOOEntity(accomType, accomTypeOutOfOrderMap.get(accomType));
            tenantCrudService().save(crOutOfOrder);
        });
        tenantCrudService().flushAndClear();
    }

    private CROutOfOrder createCROOOEntity(AccomType accomType, Integer oooQuantity) {
        CROutOfOrder crOutOfOrder = new CROutOfOrder();
        crOutOfOrder.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        crOutOfOrder.setOccupancyDate(DateUtil.addDaysToDate(businessDate, 1));
        crOutOfOrder.setAccomType(accomType);
        crOutOfOrder.setOutOfOrder(oooQuantity);

        return crOutOfOrder;
    }

    @Test
    public void calculateAndSaveSoldsForMktAccomActivityTest() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "2"}};
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);
        processor.calculateAndSaveSoldsForMktAccomActivity();
        List<CRMktSegAccomActivity> accomList = tenantCrudService.findByNamedQuery(CRMktSegAccomActivity.ALL);
        assertEquals(2, accomList.size(), "There should be 2 CRMktSegAccomactivity ");
    }

    @Test
    public void whenZeroSoldOnCRThenNoCorrectionForMktAccomActivity() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();

        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "0"}};
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, 1, 3);
        processor.calculateAndSaveSoldsForMktAccomActivity();
        List<CRMktSegAccomActivity> accomList = tenantCrudService.findByNamedQuery(CRMktSegAccomActivity.ALL);
        assertEquals(0, accomList.size(), "There should be 2 CRMktSegAccomactivity ");
    }


    @Test
    public void calculateAndSaveSoldsForTotalActivityTest() throws Exception {
        pushComponentRoomsMappingFullShareWithOnlyPhysical();
        Map<AccomType, Integer> accomTypeRoomSoldMap = new HashMap<>();
        accomTypeRoomSoldMap.put(getAccomType("P1"), 4);
        accomTypeRoomSoldMap.put(getAccomType("P2"), 3);
        accomTypeRoomSoldMap.put(getAccomType("CR1"), 2);
        accomTypeRoomSoldMap.put(getAccomType("Q"), 25); // doesn't have any CR configuration
        accomTypeRoomSoldMap.put(getAccomType("K"), 25);// doesn't have any CR configuration
        accomTypeRoomSoldMap.put(getAccomType("DLX"), 25);// doesn't have any CR configuration
        accomTypeRoomSoldMap.put(getAccomType("STE"), 25);// doesn't have any CR configuration
        tenantCrudService.executeUpdateByNativeQuery(" delete from Accom_Activity where occupancy_dt = '" + sdf.format(DateUtil.addDaysToDate(businessDate, 1)) + "'");
        populateAccomActivity(accomTypeRoomSoldMap);
        processor.calculateAndSaveRemainingCapacity(false);
        processor.calculateAndSaveSoldsForTotalActivity();
        List<CRTotalActivity> crtotalActivity = tenantCrudService.findByNamedQuery(CRTotalActivity.ALL);
        assertEquals(crtotalActivity.size(), 1, "There should be 1 CRTotalActivity ");
        assertEquals(105, crtotalActivity.get(0).getRoomsSold().intValue(), "Rool Solds for CRTotalActivity not matching");
    }

    @Test
    public void calculateComponentRoomsSold_param_ADJUST_MKT_ACCOM_ACTIVITY_AND_REMOVE_AMS_FROM_INTEGRATIONS_true() {
        // given
        Mockito.when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADJUST_MKT_ACCOM_ACTIVITY_COMPONENT_ROOMS_SOLD)).thenReturn(true);
        Mockito.when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.REMOVE_AMS_FROM_INTEGRATIONS)).thenReturn(true);
        // Existing solds: 4(P1), 3(P2), 1(CR1)
        // Since 1(CR1) = 2(P1) + 2(P2), after calculation, we should see an extra 2 rooms sold for each of the physical room types.
        pushComponentRoomsMappingFullShareWithOnlyPhysicalAndQuantity2();
        String[][] mktSegIdAccomTypIdSolds = {{"1", getAccomType("P1").getId().toString(), "4"},
                {"1", getAccomType("P2").getId().toString(), "3"},
                {"1", getAccomType("CR1").getId().toString(), "1"}};
        Integer mktSegId = 1;
        Integer accomTypeIdP1 = getAccomType("P1").getId();
        Integer accomTypeIdP2 = getAccomType("P2").getId();
        int addDays = 1;
        populateMktAccomActivity(mktSegIdAccomTypIdSolds, addDays, 3);

        // when
        processor.calculateAndSaveSoldsForMktAccomActivity();

        // then
        List<MktSegAccomActivity> mktAccomActivityP1 = tenantCrudService.findByNamedQuery(MktSegAccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_ID_AND_MKT_SEG_ID_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("occupancyDate", DateUtil.addDaysToDate(businessDate, addDays)).and("accomTypeId", accomTypeIdP1).and("mktSegId", mktSegId).parameters());
        List<MktSegAccomActivity> mktAccomActivityP2 = tenantCrudService.findByNamedQuery(MktSegAccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_ID_AND_MKT_SEG_ID_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("occupancyDate", DateUtil.addDaysToDate(businessDate, addDays)).and("accomTypeId", accomTypeIdP2).and("mktSegId", mktSegId).parameters());
        assertEquals(6, mktAccomActivityP1.get(0).getRoomsSold().intValue());
        assertEquals(5, mktAccomActivityP2.get(0).getRoomsSold().intValue());
    }
}
