package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.budget.BudgetService;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.datafeed.dto.ComponentRoomMappingDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupBlockDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupMasterDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.*;
import com.ideas.tetris.pacman.services.datafeed.entity.PricingSensitivityCoefficient;
import com.ideas.tetris.pacman.services.datafeed.entity.PropertyLevelData;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demand360.entity.Demand360BookingSummaryPace;
import com.ideas.tetris.pacman.services.demand360.entity.Demand360MarketSegmentDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.pacealert.dto.PropertyOnBooksPaceAlertDTO;
import com.ideas.tetris.pacman.services.pacealert.entity.PropertyOnBooksPaceAlert;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.str.dto.STRDailyDTO;
import com.ideas.tetris.pacman.services.str.dto.STRMonthlyDTO;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

public class OptixDatafeedServiceTest extends AbstractG3JupiterTest {

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    PropertyConfigParamService propertyConfigParamService;
    @Mock
    DateService dateService;

    @Mock
    DatafeedEndpointService endpointService;

    @Mock
    CrudService mockTenantCrudService;

    private CrudService tenantService = tenantCrudService();
    private DatafeedService datafeedService = new DatafeedService();
    private OptixDatafeedService optixDatafeedService = new OptixDatafeedService();
    private ReservationNightDatafeedService reservationNightDatafeedService = new ReservationNightDatafeedService();

    SimpleDateFormat simpleDateFormat;

    @Mock
    BudgetService budgetService;


    ComponentRoomService componentRoomService = new ComponentRoomService();

    Date startDate = DateUtil.getDate(10, 6, 2018, 0, 0, 0);
    Date endDate = DateUtil.getDate(11, 6, 2018, 0, 0, 0);
    Date lastSuccessDate = DateUtil.getDate(1, 6, 2018);
    DatafeedRequest datafeedRequest = new DatafeedRequest(0, 5, startDate, endDate, lastSuccessDate);

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H1);
        inject(datafeedService, "tenantCrudService", tenantService);
        inject(datafeedService, "propertyConfigParamService", propertyConfigParamService);
        inject(optixDatafeedService, "tenantCrudService", tenantService);
        inject(optixDatafeedService, "budgetService", budgetService);
        inject(datafeedService, "optixDatafeedService", optixDatafeedService);
        inject(optixDatafeedService, "endpointService", endpointService);
        inject(optixDatafeedService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(optixDatafeedService, "componentRoomService", componentRoomService);
        inject(componentRoomService, "tenantCrudService", tenantService);
        inject(datafeedService, "reservationNightDatafeedService", reservationNightDatafeedService);
        inject(reservationNightDatafeedService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(reservationNightDatafeedService, "tenantCrudService", tenantService);
        inject(reservationNightDatafeedService, "endpointService", endpointService);
        inject(datafeedService, "dateService", dateService);
        lenient().when(optixDatafeedService.pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.DATAFEED_HISTORY_DATA_OFFSET.value())).thenReturn(365);
        lenient().when(reservationNightDatafeedService.pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.DATAFEED_HISTORY_DATA_OFFSET.value())).thenReturn(365);

        String pattern = "yyyy-MM-dd";
        simpleDateFormat = new SimpleDateFormat(pattern);
        prepareDataForOptixDatafeed();
        populatePaceWebrateData();
    }

    @Test
    public void testReservationNight_flowTest() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);
        List<Object> result = datafeedService.get("ReservationNightDTO", datafeedRequest);
        assertEquals(3, result.size());
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(false);
        result = datafeedService.get("ReservationNightDTO", datafeedRequest);
        assertEquals(2, result.size());
    }

    @Test
    public void testReservationNight() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);

        List<Object> result = datafeedService.get("ReservationNightDTO", datafeedRequest);

        ReservationNightDTO reservationNight = (ReservationNightDTO) result.get(0);
        assertEquals("SS", reservationNight.getIndividualStatus(), "Individual Status");
        assertEquals("12345678901", reservationNight.getReservationIdentifier(), "Reservation Identifier");
        assertEquals("2018-07-10", simpleDateFormat.format(reservationNight.getArrivalDate()), "Arrival Date");
        assertEquals("2018-07-11", simpleDateFormat.format(reservationNight.getDepartureDate()), "Departure Date");
        assertEquals("2017-08-12", simpleDateFormat.format(reservationNight.getBookingDate()), "Booking Date");
        assertEquals("SXBL", reservationNight.getBookedAccomTypeCode(), "Booked Room Type Code");
        assertEquals("DLX", reservationNight.getAccomTypeCode(), "Room Type Code");
        assertEquals("1108", reservationNight.getRoomNumber(), "Room Number");
        assertEquals("IN", reservationNight.getBookingType(), "Booking type");
        assertEquals("CONV", reservationNight.getMarketSegCode(), "Market Segment Code");
        assertEquals("100.00", reservationNight.getRoomRevenue().toString(), "Inventory Data - Room Revenue");
        assertEquals("140.00", reservationNight.getFoodRevenue().toString(), "Inventory Data – Food Revenue");
        assertEquals("240.00", reservationNight.getTotalRevenue().toString(), "Inventory Data – Total Revenue");
        assertEquals("SHHQO1", reservationNight.getRateCode(), "Inventory Data – Rate Code");
        assertEquals("25.00", reservationNight.getRateValue().toString(), "Inventory Data – Rate Value");
        assertEquals("10", reservationNight.getNumberChildren().toString(), "Children");
        assertEquals("25", reservationNight.getNumberAdults().toString(), "Adults");

        reservationNight = (ReservationNightDTO) result.get(2);
        assertEquals("SS", reservationNight.getIndividualStatus(), "Individual Status");
        assertEquals("12345678901", reservationNight.getReservationIdentifier(), "Reservation Identifier");
        assertEquals("2018-07-10", simpleDateFormat.format(reservationNight.getArrivalDate()), "Arrival Date");
        assertEquals("2018-07-12", simpleDateFormat.format(reservationNight.getDepartureDate()), "Departure Date");
        assertEquals("2017-08-12", simpleDateFormat.format(reservationNight.getBookingDate()), "Booking Date");
        assertEquals("SXBL", reservationNight.getBookedAccomTypeCode(), "Booked Room Type Code");
        assertEquals("DLX", reservationNight.getAccomTypeCode(), "Room Type Code");
        assertEquals("1108", reservationNight.getRoomNumber(), "Room Number");
        assertEquals("IN", reservationNight.getBookingType(), "Booking type");
        assertEquals("CONV", reservationNight.getMarketSegCode(), "Market Segment Code");
        assertEquals("1111.00", reservationNight.getRoomRevenue().toString(), "Inventory Data - Room Revenue");
        assertEquals("222.00", reservationNight.getFoodRevenue().toString(), "Inventory Data – Food Revenue");
        assertEquals("1366.00", reservationNight.getTotalRevenue().toString(), "Inventory Data – Total Revenue");
        assertEquals("SHHQO1", reservationNight.getRateCode(), "Inventory Data – Rate Code");
        assertEquals("25.00", reservationNight.getRateValue().toString(), "Inventory Data – Rate Value");
        assertEquals("10", reservationNight.getNumberChildren().toString(), "Children");
        assertEquals("25", reservationNight.getNumberAdults().toString(), "Adults");
        assertEquals("2018-07-12", simpleDateFormat.format(reservationNight.getOccupancyDate()), "Occupancy Date");

    }

    @Test
    public void testReservationChangeNight_flowTest() {
        prepareReservationNightData();
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);
        List<Object> result = datafeedService.get("ReservationNightChangeDTO", datafeedRequest);
        assertEquals(2, result.size());
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(false);
        result = datafeedService.get("ReservationNightChangeDTO", datafeedRequest);
        assertEquals(1, result.size());
    }

    @Test
    public void testReservationChangeNight() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(true);

        prepareReservationNightData();
        List<Object> result = datafeedService.get("ReservationNightChangeDTO", datafeedRequest);
        deleteReservationNightData();

        ReservationNightChangeDTO reservationNight = (ReservationNightChangeDTO) result.get(0);
        assertEquals("SS", reservationNight.getIndividualStatus(), "Individual Status");
        assertEquals("12345678901", reservationNight.getReservationIdentifier(), "Reservation Identifier");
        assertEquals("2018-07-10", simpleDateFormat.format(reservationNight.getArrivalDate()), "Arrival Date");
        assertEquals("2018-07-11", simpleDateFormat.format(reservationNight.getDepartureDate()), "Departure Date");
        assertEquals("2017-08-12", simpleDateFormat.format(reservationNight.getBookingDate()), "Booking Date");
        assertEquals("SXBL", reservationNight.getBookedAccomTypeCode(), "Booked Room Type Code");
        assertEquals("DLX", reservationNight.getAccomTypeCode(), "Room Type Code");
        assertEquals("1108", reservationNight.getRoomNumber(), "Room Number");
        assertEquals("IN", reservationNight.getBookingType(), "Booking type");
        assertEquals("CONV", reservationNight.getMarketSegCode(), "Market Segment Code");
        assertEquals("100.00", reservationNight.getRoomRevenue().toString(), "Inventory Data - Room Revenue");
        assertEquals("140.00", reservationNight.getFoodRevenue().toString(), "Inventory Data – Food Revenue");
        assertEquals("240.00", reservationNight.getTotalRevenue().toString(), "Inventory Data – Total Revenue");
        assertEquals("SHHQO1", reservationNight.getRateCode(), "Inventory Data – Rate Code");
        assertEquals("25.00", reservationNight.getRateValue().toString(), "Inventory Data – Rate Value");
        assertEquals("10", reservationNight.getNumberChildren().toString(), "Children");
        assertEquals("25", reservationNight.getNumberAdults().toString(), "Adults");

        reservationNight = (ReservationNightChangeDTO) result.get(1);
        assertEquals("SS", reservationNight.getIndividualStatus(), "Individual Status");
        assertEquals("12345678901", reservationNight.getReservationIdentifier(), "Reservation Identifier");
        assertEquals("2018-07-10", simpleDateFormat.format(reservationNight.getArrivalDate()), "Arrival Date");
        assertEquals("2018-07-11", simpleDateFormat.format(reservationNight.getDepartureDate()), "Departure Date");
        assertEquals("2017-08-12", simpleDateFormat.format(reservationNight.getBookingDate()), "Booking Date");
        assertEquals("SXBL", reservationNight.getBookedAccomTypeCode(), "Booked Room Type Code");
        assertEquals("DLX", reservationNight.getAccomTypeCode(), "Room Type Code");
        assertEquals("1108", reservationNight.getRoomNumber(), "Room Number");
        assertEquals("IN", reservationNight.getBookingType(), "Booking type");
        assertEquals("CONV", reservationNight.getMarketSegCode(), "Market Segment Code");
        assertEquals("1111.00", reservationNight.getRoomRevenue().toString(), "Inventory Data - Room Revenue");
        assertEquals("222.00", reservationNight.getFoodRevenue().toString(), "Inventory Data – Food Revenue");
        assertEquals("1366.00", reservationNight.getTotalRevenue().toString(), "Inventory Data – Total Revenue");
        assertEquals("SHHQO1", reservationNight.getRateCode(), "Inventory Data – Rate Code");
        assertEquals("25.00", reservationNight.getRateValue().toString(), "Inventory Data – Rate Value");
        assertEquals("10", reservationNight.getNumberChildren().toString(), "Children");
        assertEquals("25", reservationNight.getNumberAdults().toString(), "Adults");
        assertEquals("2018-07-12", simpleDateFormat.format(reservationNight.getOccupancyDate()), "Occupancy Date");


    }

    @Test
    public void testOccupancyFCST() {
        List<Object> result = datafeedService.get("OccupancyFCSTDTO", datafeedRequest);
        OccupancyFCSTDTO occupancyFCST = (OccupancyFCSTDTO) result.get(1);
        assertEquals("BART", occupancyFCST.getMarketCode(), "Market code");
        assertEquals("STE", occupancyFCST.getAccomTypeCode(), "Room Type Code");
        assertEquals("2018-07-11", simpleDateFormat.format(occupancyFCST.getOccupancyDate()), "Occupancy Date");
        assertEquals("155.00", occupancyFCST.getOccupancyNumber().toString(), "Occupancy Forecast");
        assertEquals("40008.98", occupancyFCST.getRevenue().toString(), "Revenue Forecast");
    }

    @Test
    public void testBudgetData_Business_Type_Level() {
        prepareBudgetDataAtBusinessTypeLevel();
        when(optixDatafeedService.budgetService.getBudgetConfig()).thenReturn(getBudgetConfig(true));
        List<Object> result = datafeedService.get("BudgetDataDTO", datafeedRequest);
        BudgetDataDTO budgetData = (BudgetDataDTO) result.get(0);
        assertEquals("Group", budgetData.getBusinessTypeName(), "Business Type Name");
        assertNull(budgetData.getBusinessViewCode(), "Business View Code");
        assertEquals("2018-07-10", simpleDateFormat.format(budgetData.getOccupancyDate()), "Occupancy Date");
        assertEquals("20", String.valueOf(budgetData.getRoomsSold()), "Budgeted Rooms Sold");
        assertEquals("100.00", budgetData.getRoomRevenue().toString(), "Budgeted Room Revenue");
        budgetData = (BudgetDataDTO) result.get(3);
        assertEquals("Transient", budgetData.getBusinessTypeName(), "Business Type Name");
        assertNull(budgetData.getBusinessViewCode(), "Business View Code");
        assertEquals("2018-07-11", simpleDateFormat.format(budgetData.getOccupancyDate()), "Occupancy Date");
        assertEquals("45", String.valueOf(budgetData.getRoomsSold()), "Budgeted Rooms Sold");
        assertEquals("333.00", budgetData.getRoomRevenue().toString(), "Budgeted Room Revenue");
    }

    @Test
    public void testBudgetData_Business_View_Level() {
        prepareBudgetDataAtBusinessViewLevel();
        when(optixDatafeedService.budgetService.getBudgetConfig()).thenReturn(getBudgetConfig(false));
        List<Object> result = datafeedService.get("BudgetDataDTO", datafeedRequest);

        BudgetDataDTO budgetData = (BudgetDataDTO) result.get(0);
        assertNull(budgetData.getBusinessTypeName(), "Business Type Name");
        assertEquals("Corporate Groups", budgetData.getBusinessViewCode(), "Business View Code");
        assertEquals("2018-07-10", simpleDateFormat.format(budgetData.getOccupancyDate()), "Occupancy Date");
        assertEquals("20", String.valueOf(budgetData.getRoomsSold()), "Budgeted Rooms Sold");
        assertEquals("100.00", budgetData.getRoomRevenue().toString(), "Budgeted Room Revenue");
        budgetData = (BudgetDataDTO) result.get(3);
        assertNull(budgetData.getBusinessTypeName(), "Business Type Name");
        assertEquals("Complimentary and House Use", budgetData.getBusinessViewCode(), "Business View Code");
        assertEquals("2018-07-10", simpleDateFormat.format(budgetData.getOccupancyDate()), "Occupancy Date");
        assertEquals("60", String.valueOf(budgetData.getRoomsSold()), "Budgeted Rooms Sold");
        assertEquals("600.00", budgetData.getRoomRevenue().toString(), "Budgeted Room Revenue");
    }

    @Test
    public void testForecastGroupInfo() {
        List<Object> result = datafeedService.get("ForecastGroupDTO", datafeedRequest);
        ForecastGroupDTO forecastGroupDTO = (ForecastGroupDTO) result.get(0);
        assertEquals("Complement", forecastGroupDTO.getForecastGroupCode(), "Forecast Group Code");
        assertEquals("NOFCST_WASH", forecastGroupDTO.getForecastTypeName(), "Forecast Type Name");
        assertEquals("NOFCST_WASH", forecastGroupDTO.getForecastTypeDescription(), "Forecast Type Description");
        assertEquals("No", forecastGroupDTO.getStraightBARIndicator(), "Straight Bar Indicator");
        assertEquals("No", forecastGroupDTO.getLinkedtoBARIndicator(), "Linked To Bar Indicator");
        forecastGroupDTO = (ForecastGroupDTO) result.get(4);
        assertEquals("Wash", forecastGroupDTO.getForecastGroupCode(), "Forecast Group Code");
        assertEquals("NOFCST", forecastGroupDTO.getForecastTypeName(), "Forecast Type Name");
        assertEquals("NOFCST", forecastGroupDTO.getForecastTypeDescription(), "Forecast Type Description");
        assertEquals("No", forecastGroupDTO.getStraightBARIndicator(), "Straight Bar Indicator");
        assertEquals("No", forecastGroupDTO.getLinkedtoBARIndicator(), "Linked To Bar Indicator");
    }

    @Test
    public void testgetMarketAccomActivity() {
        prepareMarketAccomActivity();
        List<Object> result = datafeedService.get("MarketAccomActivityDTO", datafeedRequest);
        MarketAccomActivityDTO marketAccomActivityDTO = (MarketAccomActivityDTO) result.get(0);
        assertEquals("2018-07-10", simpleDateFormat.format(marketAccomActivityDTO.getOccupancyDate()), "Occupancy Date");
        assertEquals("BART", marketAccomActivityDTO.getMarketSegmentCode(), "Market Segment Code");
        assertEquals("DLX", marketAccomActivityDTO.getRoomTypeCode(), "Room Type Code");
        assertEquals("7", String.valueOf(marketAccomActivityDTO.getRoomsSold()), "Inventory Data - Room Solds");
        assertEquals("5", String.valueOf(marketAccomActivityDTO.getArrivals()), "Inventory Data – Arrivals");
        assertEquals("4", String.valueOf(marketAccomActivityDTO.getDepartures()), "Inventory Data – Departures");
        assertEquals("0", String.valueOf(marketAccomActivityDTO.getCancellations()), "Inventory Data – Cancellations");
        assertEquals("0", String.valueOf(marketAccomActivityDTO.getNoShows()), "Inventory Data – No Shows");
        assertEquals("360.00", marketAccomActivityDTO.getRoomRevenue().toString(), "Inventory Data - Room Revenue");
        assertEquals("20.00", marketAccomActivityDTO.getFoodRevenue().toString(), "Inventory Data - Food Revenue");
        assertEquals("380.00", marketAccomActivityDTO.getTotalRevenue().toString(), "Inventory Data - Total Revenue");
        assertEquals("0.00", marketAccomActivityDTO.getPseudoRoomRevenue().toString(), "Inventory Data - Pseudo Revenue");
    }

    @Test
    public void testGroupMaster() {
        List<Object> result = datafeedService.get("GroupMasterDTO", datafeedRequest);
        GroupMasterDTO groupMaster = (GroupMasterDTO) result.get(0);
        assertEquals("OPTIX_TEST_CODE_1", groupMaster.getCode(), "Group Code");
        assertEquals("Test Group Name1", groupMaster.getName(), "Group Name");
        assertEquals("Test Group Description1", groupMaster.getDescription(), "Group Description");
        assertEquals("DEFINITE", groupMaster.getGroupStatusCode(), "Group Status Code");
        assertEquals("TRANS", groupMaster.getGroupTypeCode(), "Group Type Code");
        assertEquals("CONV", groupMaster.getMarketSegmentCode(), "Market Segment Code");
        assertEquals("2019-07-07", simpleDateFormat.format(groupMaster.getStartDate()), "Start Date");
        assertEquals("2019-07-10", simpleDateFormat.format(groupMaster.getEndDate()), "End Date");
        assertEquals("2018-07-10", simpleDateFormat.format(groupMaster.getBookingDate()), "Booking Date");
        assertEquals("2018-07-10", simpleDateFormat.format(groupMaster.getCutoffDate()), "Cut Off Date");
        assertEquals("0", groupMaster.getCutoffDays().toString(), "Cut Off Days");
    }

    @Test
    public void verifyGroupsWithOccupancyDateIncludedInDatafeed() {
        assertEquals(2, datafeedService.get("GroupMasterDTO", datafeedRequest).size());
        addGroupWithBookinDateNotInDateRage();
        addGroupBlockWithOccupancydateInDateRage();
        assertEquals(3, datafeedService.get("GroupMasterDTO", datafeedRequest).size());
    }

    private void addGroupBlockWithOccupancydateInDateRage() {
        String query = " insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate, IsPeakNight) " +
                "values ((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_3'),'2018-07-11',5,9,8,11,75.00,1)";
        tenantService.executeUpdateByNativeQuery(query);
    }

    private void addGroupWithBookinDateNotInDateRage() {
        String query = " insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT,Cut_Off_date,Cut_Off_days) "
                + "values(000005, 'OPTIX_TEST_CODE_3', 'Test Group Name3', 'Test Group Description3', 5,'DEFINITE','TRANS','2019-09-07','2019-09-10','2017-07-11','2018-07-11',0)";
        tenantService.executeUpdateByNativeQuery(query);
    }

    @Test
    public void testGroupBlock() {
        List<Object> result = datafeedService.get("GroupBlockDTO", datafeedRequest);
        GroupBlockDTO dto = (GroupBlockDTO) result.get(0);
        assertEquals("OPTIX_TEST_CODE_1", dto.getGroupCode(), "Group Code");
        assertEquals("Q", dto.getAccommodationType(), "Accom Type");
        assertEquals(new Integer(5), dto.getBlocks(), "Group Blocks");
        assertEquals(new Integer(6), dto.getOriginalBlocks(), "Original Blocks");
        assertEquals(BigDecimalUtil.round(new BigDecimal(55), 5), dto.getRate(), "Group rate");
        assertEquals(new Integer(4), dto.getPickup(), "Pickup");
        assertEquals(new Integer(1), dto.getPeakNight(), "Is Peak Night");
        assertEquals("2018-07-10", simpleDateFormat.format(dto.getOccupancyDate()), "Occupancy Date");
    }

    @Test
    public void testPaceGroupBlock() {
        List<Object> result = datafeedService.get("PaceGroupBlockDTO", datafeedRequest);
        assertEquals(2, result.size());

        PaceGroupBlockDTO row1 = (PaceGroupBlockDTO) result.get(0);

        assertEquals("OPTIX_TEST_CODE_1", row1.getGroupCode(), "Group Code");
        assertEquals("Q", row1.getAccommodationType(), "Accom Type");
        assertEquals(new Integer(5), row1.getBlocks(), "Blocks");
        assertEquals(new Integer(4), row1.getPickup(), "Pickup");
        assertEquals(new Integer(6), row1.getOriginalBlocks(), "Original Blocks");
        assertEquals(BigDecimalUtil.round(new BigDecimal(55.88888), 5), row1.getRate(), "rate");
        assertEquals("2018-07-10", simpleDateFormat.format(row1.getOccupancyDate()), "Occupancy Date");
        assertEquals("2018-07-10", simpleDateFormat.format(row1.getBusinessDayEndDate()), "Business End Date");

        PaceGroupBlockDTO row2 = (PaceGroupBlockDTO) result.get(1);

        assertEquals("OPTIX_TEST_CODE_2", row2.getGroupCode(), "Group Code");
        assertEquals("STE", row2.getAccommodationType(), "Accom Type");
        assertEquals(new Integer(9), row2.getBlocks(), "Blocks");
        assertEquals(new Integer(8), row2.getPickup(), "Pickup");
        assertEquals(new Integer(11), row2.getOriginalBlocks(), "Original Blocks");
        assertEquals(BigDecimalUtil.round(new BigDecimal(75.12345), 5), row2.getRate(), "rate");
        assertEquals("2018-07-11", simpleDateFormat.format(row2.getOccupancyDate()), "Occupancy Date");
        assertEquals("2018-07-11", simpleDateFormat.format(row2.getBusinessDayEndDate()), "Business End Date");

    }

    @Test
    public void testPaceGroupMaster() {
        preparePaceGroupMasterData();
        List<Object> result = datafeedService.get("PaceGroupMasterDTO", datafeedRequest);
        assertEquals(1, result.size());

        PaceGroupMasterDTO row1 = (PaceGroupMasterDTO) result.get(0);
        assertEquals("2017-10-02", simpleDateFormat.format(row1.getBusinessDayEndDate()), "Business Day End Date");
        assertEquals("297950", row1.getGroupCode(), "Group Code");
        assertEquals("0917LUCIAN_Luciani Wedding Block", row1.getGroupName(), "Group Name");
        assertEquals("Luciani Wedding Block", row1.getGroupDescription(), "Group Description");
        assertEquals("CXL", row1.getGroupStatusCode(), "Group_Status_Code");
        assertEquals("GROUP", row1.getGroupTypeCode(), "Group_Type_Code");
        assertEquals("GT", row1.getMarketSegmentCode(), "Market_Segment_Code");
        assertEquals("2017-09-15", simpleDateFormat.format(row1.getStartDate()), "Start Date");
        assertEquals("2017-09-18", simpleDateFormat.format(row1.getEndDate()), "End Date");
        assertEquals("2018-07-10", simpleDateFormat.format(row1.getBookingDate()), "Booking Date");
        assertEquals("2018-07-10", simpleDateFormat.format(row1.getCancelDate()), "Cancel Date");
        assertEquals("2016-06-12", simpleDateFormat.format(row1.getCutOffDate()), "Cut off Date");
        assertEquals(10, row1.getCutOffDays(), "Cut off days");

    }

    @Test
    public void testLRVPace() {
        prepareLRVPaceData();
        List<Object> result = datafeedService.get("LRVPaceDTO", datafeedRequest);
        assertEquals(1, result.size());

        LRVPaceDTO row1 = (LRVPaceDTO) result.get(0);
        assertEquals("2010-01-01", simpleDateFormat.format(row1.getBusinessDayEndDate()), "Business Day End Date");
        assertEquals("2018-07-10", simpleDateFormat.format(row1.getOccupancyDate()), "Occupancy Date");
        assertEquals("STD", row1.getRoomClassCode(), "Room Class Code");
        assertEquals(BigDecimalUtil.round(new BigDecimal(12.34000), 2), row1.getLrv(), "LRV");
    }


    @Test
    public void testWebrate() {
        populateWebrateData();
        List<Object> result = datafeedService.get("WebrateDTO", datafeedRequest);
        deleteWebrateData();
        assertEquals(5, result.size());
        WebrateDTO row1 = (WebrateDTO) result.get(0);
        assertEquals("RateView", row1.getWebrateSourceProperty(), "Webrate Source Property");
        assertEquals("2018-07-11", simpleDateFormat.format(row1.getWebrateGenerationDate()), "Webrate GenerationDate");
        assertEquals("Luxor", row1.getCompetitorsName(), "Competitors Name");
        assertEquals("Expedia", row1.getWebrateChannel(), "Webrate Channel");
        assertEquals("Delux", row1.getWebrateRoomType(), "Webrate Room Type");
        assertEquals("rate1", row1.getWebrateTypeName(), "Webrate Type Name");
        assertEquals("2018-07-12", simpleDateFormat.format(row1.getOccupancyDate()), "Occupancy Date");
        assertEquals(1, row1.getLos(), "Los");
        assertEquals("A", row1.getWebrateStatus(), "Webrate Status");
        assertEquals("USD", row1.getWebrateCurrency(), "Webrate Currency");
        assertEquals(BigDecimalUtil.round(new BigDecimal(172.00000), 5), row1.getWebrateRateValue(), "Webrate Rate Value");
        assertEquals("1", row1.getWebrateRank(), "Webrate Rank");
        assertEquals("1", row1.getWebrateRating(), "Webrate Rating");
        assertEquals(BigDecimalUtil.round(new BigDecimal(1.00000), 5), row1.getWebrateRateValueMax(), "Webrate Rate Value Max");
        assertEquals(BigDecimalUtil.round(new BigDecimal(172.00000), 5), row1.getWebrateRateValueDisplay(), "Webrate Rate Value Display");

        WebrateDTO row2 = (WebrateDTO) result.get(1);
        assertEquals("RateView", row2.getWebrateSourceProperty(), "Webrate Source Property");
        assertEquals("2018-07-12", simpleDateFormat.format(row2.getWebrateGenerationDate()), "Webrate GenerationDate");
        assertEquals("Luxor", row2.getCompetitorsName(), "Competitors Name");
        assertEquals("Expedia", row2.getWebrateChannel(), "Webrate Channel");
        assertEquals("Delux", row2.getWebrateRoomType(), "Webrate Room Type");
        assertEquals("rate1", row2.getWebrateTypeName(), "Webrate Type Name");
        assertEquals("2018-07-13", simpleDateFormat.format(row2.getOccupancyDate()), "Occupancy Date");
        assertEquals(1, row2.getLos(), "Los");
        assertEquals("A", row2.getWebrateStatus(), "Webrate Status");
        assertEquals("USD", row2.getWebrateCurrency(), "Webrate Currency");
        assertEquals(BigDecimalUtil.round(new BigDecimal(192.00000), 5), row2.getWebrateRateValue(), "Webrate Rate Value");
        assertEquals("2", row2.getWebrateRank(), "Webrate Rank");
        assertEquals("2", row2.getWebrateRating(), "Webrate Rating");
        assertEquals(BigDecimalUtil.round(new BigDecimal(2.00000), 5), row2.getWebrateRateValueMax(), "Webrate Rate Value Max");
        assertEquals(BigDecimalUtil.round(new BigDecimal(192.00000), 5), row2.getWebrateRateValueDisplay(), "Webrate Rate Value Display");
    }

    @Test
    public void testSTRDailyFile() {
        populateSTRDailyData();
        List<Object> result = datafeedService.get("STRDailyDTO", datafeedRequest);
        deleteSTRDailyData();
        assertEquals(2, result.size());
        STRDailyDTO row1 = (STRDailyDTO) result.get(0);

        assertEquals(550, row1.getCompetitiveSetAvailable(), "Competitive Set Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(25637.00), 2), row1.getCompetitiveSetRevenue(), "Competitive Set Revenue");
        assertEquals(283, row1.getCompetitiveSetSold(), "Competitive Set Sold");
        assertEquals(120, row1.getPropertyAvailable(), "Property Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(7087), 2), row1.getPropertyRevenue(), "Property Revenue");
        assertEquals(92, row1.getPropertySold(), "Property Sold");
        assertEquals("10-Jul-2018", row1.getOccupancyDate(), "Occupancy Date");

        STRDailyDTO row2 = (STRDailyDTO) result.get(1);

        assertEquals(650, row2.getCompetitiveSetAvailable(), "Competitive Set Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(35637.00), 2), row2.getCompetitiveSetRevenue(), "Competitive Set Revenue");
        assertEquals(383, row2.getCompetitiveSetSold(), "Competitive Set Sold");
        assertEquals(220, row2.getPropertyAvailable(), "Property Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(8087.00), 2), row2.getPropertyRevenue(), "Property Revenue");
        assertEquals(102, row2.getPropertySold(), "Property Sold");
        assertEquals("11-Jul-2018", row2.getOccupancyDate(), "Occupancy Date");

    }

    @Test
    public void testSTRMonthlyFile() {
        List<Object> result = datafeedService.get("STRMonthlyDTO", datafeedRequest);
        assertEquals(3, result.size());
        STRMonthlyDTO row1 = (STRMonthlyDTO) result.get(0);

        assertEquals(225, row1.getCompetitiveSetAvailable(), "Competitive Set Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(22487.00), 2), row1.getCompetitiveSetRevenue(), "Competitive Set Revenue");
        assertEquals(211, row1.getCompetitiveSetSold(), "Competitive Set Sold");
        assertEquals(200, row1.getPropertyAvailable(), "Property Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(19944), 2), row1.getPropertyRevenue(), "Property Revenue");
        assertEquals(185, row1.getPropertySold(), "Property Sold");

        STRMonthlyDTO row2 = (STRMonthlyDTO) result.get(1);

        assertEquals(225, row2.getCompetitiveSetAvailable(), "Competitive Set Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(20638.00), 2), row2.getCompetitiveSetRevenue(), "Competitive Set Revenue");
        assertEquals(198, row2.getCompetitiveSetSold(), "Competitive Set Sold");
        assertEquals(200, row2.getPropertyAvailable(), "Property Available");
        assertEquals(BigDecimalUtil.round(new BigDecimal(17225.00), 2), row2.getPropertyRevenue(), "Property Revenue");
        assertEquals(161, row2.getPropertySold(), "Property Sold");

    }

    @Test
    public void testD360CapacityFile() {
        populateD360CapacityData();
        List<Object> result = datafeedService.get("D360CompCapacityDTO", datafeedRequest);
        deleteD360CapacityData();
        assertEquals(2, result.size());

        D360CompCapacityDTO row1 = (D360CompCapacityDTO) result.get(0);
        assertEquals("10-Jul-2018", row1.getOccupancyDate(), "Occupancy Date");
        assertEquals(5555, row1.getCapacity(), "Comp Capacity");

        D360CompCapacityDTO row2 = (D360CompCapacityDTO) result.get(1);
        assertEquals("11-Jul-2018", row2.getOccupancyDate(), "Occupancy Date");
        assertEquals(5556, row2.getCapacity(), "Comp Capacity");
    }

    @Test
    public void testD360BookingSummaryFile() {
        populateD360BookingSummaryData();
        List<Object> result = datafeedService.get("D360BookingSummaryDTO", datafeedRequest);
        deleteD360BookingSummaryData();
        assertEquals(2, result.size());

        D360BookingSummaryDTO row1 = (D360BookingSummaryDTO) result.get(0);
        assertEquals("10-Jul-2018", row1.getOccupancyDate(), "Occupancy Date");
        assertEquals("10-Jul-2018", row1.getCaptureDate(), "Capture Date");
        assertEquals(11, row1.getArrivals(), "Arrivals");
        assertEquals(12, row1.getMktArrivals(), "Mkt Arrivals");
        assertEquals(BigDecimalUtil.round(new BigDecimal(38.10), 2), row1.getMktRoomRevenue(), "Mkt Room Revenue");
        assertEquals(3, row1.getMktRoomSold(), "Mkt Rooms Sold");
        assertEquals(1, row1.getMarketSegmentDetailCode(), "Mkt Segment Detail Code");

        D360BookingSummaryDTO row2 = (D360BookingSummaryDTO) result.get(1);
        assertEquals("11-Jul-2018", row2.getOccupancyDate(), "Occupancy Date");
        assertEquals("11-Jul-2018", row2.getCaptureDate(), "Capture Date");
        assertEquals(13, row2.getArrivals(), "Arrivals");
        assertEquals(14, row2.getMktArrivals(), "Mkt Arrivals");
        assertEquals(BigDecimalUtil.round(new BigDecimal(48.10), 2), row2.getMktRoomRevenue(), "Mkt Room Revenue");
        assertEquals(33, row2.getMktRoomSold(), "Mkt Rooms Sold");
        assertEquals(1, row2.getMarketSegmentDetailCode(), "Mkt Segment Detail Code");
    }

    @Test
    public void testPaceWebrate() {

        Date startDate = DateUtil.getDate(23, 4, 2015, 0, 0, 0);
        DatafeedRequest datafeedRequest = new DatafeedRequest(0, 5, startDate, endDate, lastSuccessDate);

        List<Object> result = datafeedService.get("PaceWebrateDTO", datafeedRequest);
        assertEquals(4, result.size());

        PaceWebrateDTO row1 = (PaceWebrateDTO) result.get(0);

        assertEquals("RateView", row1.getWebrateSourceProperty(), "Webrate Source Property");
        assertEquals("2015-02-22", simpleDateFormat.format(row1.getFirstWebrateGenerationDate()), "First Webrate GenerationDate");
        assertEquals("2015-02-22", simpleDateFormat.format(row1.getWebrateGenerationDate()), "Webrate GenerationDate");
        assertEquals(1, row1.getCount(), "Count");
        assertEquals("Santa Barbara Inn", row1.getCompetitorsName(), "Competitors Name");
        assertEquals("Orbitz", row1.getWebrateChannel(), "Webrate Channel");
        assertEquals("Delux", row1.getWebrateRoomType(), "Webrate Room Type");
        assertEquals("rate1", row1.getWebrateTypeName(), "Webrate Type Name");
        assertEquals("2016-04-22", simpleDateFormat.format(row1.getOccupancyDate()), "Occupancy Date");
        assertEquals(1, row1.getLos(), "Los");
        assertEquals("A", row1.getWebrateStatus(), "Webrate Status");
        assertEquals("USD", row1.getWebrateCurrency(), "Webrate Currency");
        assertEquals(BigDecimalUtil.round(new BigDecimal(120.00000), 5), row1.getWebrateRateValue(), "Webrate Rate Value");
        assertEquals("", row1.getWebrateRank(), "Webrate Rank");
        assertEquals("lowest", row1.getWebrateRating(), "Webrate Rating");
        assertEquals(BigDecimalUtil.round(new BigDecimal(120.00000), 5), row1.getWebrateRateValueDisplay(), "Webrate Rate Value Display");

    }

    @Test
    public void testComponentRoomMapping() {

        populateComponentRoomMappingDTOData();
        List<Object> result = datafeedService.get("ComponentRoomMappingDTO", datafeedRequest);
        assertEquals(3, result.size());

        assertEquals("DLX", ((ComponentRoomMappingDTO) result.get(0)).getCrRoomTypeCode());
        assertEquals("STE", ((ComponentRoomMappingDTO) result.get(0)).getCpRoomTypeCode());
        assertEquals(9, ((ComponentRoomMappingDTO) result.get(0)).getCpRoomTypeQuantity());

        assertEquals("Q", ((ComponentRoomMappingDTO) result.get(1)).getCrRoomTypeCode());
        assertEquals("DBL", ((ComponentRoomMappingDTO) result.get(1)).getCpRoomTypeCode());
        assertEquals(10, ((ComponentRoomMappingDTO) result.get(1)).getCpRoomTypeQuantity());

        assertEquals("STE", ((ComponentRoomMappingDTO) result.get(2)).getCrRoomTypeCode());
        assertEquals("K", ((ComponentRoomMappingDTO) result.get(2)).getCpRoomTypeCode());
        assertEquals(21, ((ComponentRoomMappingDTO) result.get(2)).getCpRoomTypeQuantity());

    }

    @Test
    void test_getPropertyOnBooksPaceAlerts() {
        inject(optixDatafeedService, "tenantCrudService", mockTenantCrudService);
        Date DATE = new GregorianCalendar(2025, Calendar.APRIL, 7).getTime();
        when(mockTenantCrudService.findByNamedQuery(PropertyOnBooksPaceAlert.FIND_ALL_PROPERTY_ON_BOOKS_PACE_ALERTS_ORDER_BY_OCCUPANCY_DATE)).thenReturn(Collections.singletonList(mockPropertyOnBooksPaceAlert(DATE)));
        List<PropertyOnBooksPaceAlertDTO> result = optixDatafeedService.getPropertyOnBooksPaceAlerts();
        assertEquals(1, result.size());
        assertEquals(DATE, result.get(0).getOccupancyDate());
        assertEquals(0, result.get(0).getPaceSeverityScore());
        assertEquals(5, result.get(0).getActualOnBooks());
        assertEquals(BigDecimal.TEN, result.get(0).getExpectedOnBooks());
        assertNull(result.get(0).getExpectedOnBooksLowerBound());
        assertNull(result.get(0).getExpectedOnBooksUpperBound());
        assertEquals(DATE, result.get(0).getCreateDateDTTM());
        verify(mockTenantCrudService).findByNamedQuery(PropertyOnBooksPaceAlert.FIND_ALL_PROPERTY_ON_BOOKS_PACE_ALERTS_ORDER_BY_OCCUPANCY_DATE);
    }

    @Test
    void test_getPropertyLevelData() {
        inject(optixDatafeedService, "tenantCrudService", mockTenantCrudService);
        Date DATE = new GregorianCalendar(2025, Calendar.APRIL, 7).getTime();
        Date LAST_YEAR_DATE = new GregorianCalendar(2024, Calendar.APRIL, 8).getTime();
        DatafeedRequest datafeedRequest = new DatafeedRequest();
        datafeedRequest.setStartDate(DATE);
        datafeedRequest.setEndDate(DATE);
        when(mockTenantCrudService.findByNamedQuery(PropertyLevelData.FIND_BY_DATES_BETWEEN,
                QueryParameter.with("startDate", datafeedRequest.getStartDate())
                        .and("endDate", datafeedRequest.getEndDate())
                        .and("propertyId", 5)
                        .and("isSpecialEventInstanceNameEnabled", 1).parameters()))
                .thenReturn(List.of(mockPropertyLevelData(DATE, LAST_YEAR_DATE)));
        List<PropertyLevelDataDTO> result = optixDatafeedService.getPropertyLevelData(datafeedRequest, 1, false);
        assertEquals(1, result.size());
        PropertyLevelDataDTO resultDTO = result.get(0);
        assertEquals(DATE, resultDTO.getOccupancyDate());
        assertEquals(LAST_YEAR_DATE, resultDTO.getComparisonDateLastYear());
        assertEquals(5, resultDTO.getPropertyOverbooking());
        assertEquals("SE_ThisYear", resultDTO.getSpecialEventNameThisYear());
        assertEquals("SE_LastYear", resultDTO.getSpecialEventNameLastYear());
        assertEquals(5, resultDTO.getBudgetRoomSold());
        assertEquals(BigDecimal.TEN, resultDTO.getBudgetRoomRevenue());
        verify(mockTenantCrudService).findByNamedQuery(PropertyLevelData.FIND_BY_DATES_BETWEEN,
                QueryParameter.with("startDate", datafeedRequest.getStartDate())
                        .and("endDate", datafeedRequest.getEndDate())
                        .and("propertyId", 5)
                        .and("isSpecialEventInstanceNameEnabled", 1).parameters());
    }

    @Test
    void getPricingSensitivityCfgs() {
        insertPricingSensitivityCoefficientDate();
        List<Object> result = datafeedService.get("PricingSensitivityCoefficient", datafeedRequest);

        assertEquals(2, result.size());
        assertPricingSensitivityCfgs((PricingSensitivityCoefficient) result.get(0), "STD", "Complement", BigDecimal.valueOf(100));
        assertPricingSensitivityCfgs((PricingSensitivityCoefficient) result.get(1), "DLX", "Corporate", BigDecimal.valueOf(512));
    }

    private void assertPricingSensitivityCfgs(PricingSensitivityCoefficient result, String roomClassCode, String forecastGroupCode, BigDecimal psCoeff) {
        assertEquals(result.getRoomClassCode(), roomClassCode);
        assertEquals(result.getForecastGroupCode(), forecastGroupCode);
        assertEquals(result.getPsCoefficient(), psCoeff.setScale(5));
    }

    private void insertPricingSensitivityCoefficientDate() {
        String insertQuery = "SET IDENTITY_INSERT [dbo].Pricing_Sensitivity ON " +
                "insert into Pricing_Sensitivity(Pricing_Sensitivity_ID, Forecast_Group_ID, Accom_Class_ID, Occupancy_DT, PS_Coefficient) values " +
                "(1, 1, 2,'2024-05-01', 100) " +
                "insert into Pricing_Sensitivity(Pricing_Sensitivity_ID, Forecast_Group_ID, Accom_Class_ID, Occupancy_DT, PS_Coefficient) values " +
                "(2, 2, 3,'2025-05-01', 512) " +
                "SET IDENTITY_INSERT [dbo].Pricing_Sensitivity OFF";
        tenantService.executeUpdateByNativeQuery(insertQuery);
    }

    private PropertyLevelData mockPropertyLevelData(Date date, Date lastYearDate) {
        PropertyLevelData propertyLevelData = new PropertyLevelData();
        propertyLevelData.setOccupancyDate(date);
        propertyLevelData.setComparisonDateLastYear(lastYearDate);
        propertyLevelData.setPropertyOverbooking(5);
        propertyLevelData.setSpecialEventNameThisYear("SE_ThisYear");
        propertyLevelData.setSpecialEventNameLastYear("SE_LastYear");
        propertyLevelData.setBudgetRoomSold(5);
        propertyLevelData.setBudgetRoomRevenue(BigDecimal.TEN);
        return propertyLevelData;
    }

    private PropertyOnBooksPaceAlert mockPropertyOnBooksPaceAlert(Date date) {
        PropertyOnBooksPaceAlert propertyOnBooksPaceAlert = new PropertyOnBooksPaceAlert();
        propertyOnBooksPaceAlert.setId(1);
        propertyOnBooksPaceAlert.setPropertyId(5);
        propertyOnBooksPaceAlert.setOccupancyDate(date);
        propertyOnBooksPaceAlert.setPaceSeverityScore(0);
        propertyOnBooksPaceAlert.setActualOnBooks(5);
        propertyOnBooksPaceAlert.setExpectedOnBooks(BigDecimal.TEN);
        propertyOnBooksPaceAlert.setExpectedOnBooksLowerBound(null);
        propertyOnBooksPaceAlert.setExpectedOnBooksUpperBound(null);
        propertyOnBooksPaceAlert.setCreateDateDTTM(date);
        return propertyOnBooksPaceAlert;
    }

    private void populateComponentRoomMappingDTOData() {
        StringBuilder query = new StringBuilder();
        query.append("INSERT [dbo].[CR_Accom_Type_Mapping] ( [Property_ID], [CR_Accom_Type_ID], [CP_Accom_Type_ID], [CP_Accom_Type_Quantity]) VALUES (000005, 4, 5, 9);");
        query.append("INSERT [dbo].[CR_Accom_Type_Mapping] ( [Property_ID], [CR_Accom_Type_ID], [CP_Accom_Type_ID], [CP_Accom_Type_Quantity]) VALUES (000005, 7, 6, 10);");
        query.append("INSERT [dbo].[CR_Accom_Type_Mapping] ( [Property_ID], [CR_Accom_Type_ID], [CP_Accom_Type_ID], [CP_Accom_Type_Quantity]) VALUES (000005, 5, 8, 21);");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void populatePaceWebrateData() {

        StringBuilder query = new StringBuilder();
        query.append("delete from PACE_Webrate \n");
        query.append("update  Webrate_Accom_Class_Mapping set Accom_Class_ID =3 where Webrate_Accom_Type_ID=2 \n");
        query.append("insert into PACE_Webrate_Differential values(1,'2015-02-22','2015-02-22',1, 1, 3, 3, 1, '2016-04-22', " + 1 + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME(),120.00000)\n");
        query.append("insert into PACE_Webrate_Differential values(1,'2015-02-23','2015-02-23',1, 2, 3, 3, 1, '2016-04-23', " + 1 + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME(),120.00000)\n");
        query.append("insert into PACE_Webrate_Differential values(1,'2015-02-24','2015-02-24',1, 1, 3, 2, 1, '2016-04-24', " + 1 + ",  'A', 'USD', 115.00000,'','lowest',SYSDATETIME(),115.00000)\n");
        query.append("insert into PACE_Webrate_Differential values(1,'2015-02-25','2015-02-25',1, 2, 3, 2, 1, '2016-04-25', " + 1 + ",  'A', 'USD', 75.00000,'','lowest',SYSDATETIME(),75.00000)\n");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void populateWebrateData() {
        StringBuilder query = new StringBuilder();
        query.append("INSERT INTO [dbo].[Webrate] VALUES (1,'2018-07-11',3,1,3,1,'2018-07-12',1,'Test_Remark','A','USD',172.00000,1,1,1,SYSDATETIME(),1.00000,172.00000)\n");
        query.append("INSERT INTO [dbo].[Webrate] VALUES (1,'2018-07-12',3,1,3,1,'2018-07-13',1,'Test_Remark','A','USD',192.00000,1,2,2,SYSDATETIME(),2.00000,192.00000)\n");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void deleteWebrateData() {
        StringBuilder query = new StringBuilder();
        query.append("delete  from [dbo].[Webrate] where Webrate_Remark like 'Test_Remark'");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void populateSTRDailyData() {
        StringBuilder query = new StringBuilder();
        query.append("INSERT [dbo].[STR_Daily] ([Property_ID],[Chain_ID], [Mgmt_ID], [Owner_ID], [Hotel_Name], [Occupancy_DT], [Property_Avail], [Property_Sold], [Property_Rev], [Comp_Set_Avail], [Comp_Set_Sold], [Comp_Set_Rev], [Created_DTTM]) VALUES (000005, 7777, 0, 0, N'Sonesta Select Tucson Airport', CAST(N'2018-07-10' AS Date), 120, 92, CAST(7087.00 AS Numeric(19, 2)), 550, 283, CAST(25637.00 AS Numeric(19, 2)), CAST(N'2020-08-25T09:43:52.883' AS DateTime));\n");
        query.append("INSERT [dbo].[STR_Daily] ([Property_ID],[Chain_ID], [Mgmt_ID], [Owner_ID], [Hotel_Name], [Occupancy_DT], [Property_Avail], [Property_Sold], [Property_Rev], [Comp_Set_Avail], [Comp_Set_Sold], [Comp_Set_Rev], [Created_DTTM]) VALUES (000005, 7777, 0, 0, N'Sonesta Select Tucson Airport', CAST(N'2018-07-11' AS Date), 220, 102, CAST(8087.00 AS Numeric(19, 2)), 650, 383, CAST(35637.00 AS Numeric(19, 2)), CAST(N'2020-08-25T09:43:52.883' AS DateTime))");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void deleteSTRDailyData() {
        StringBuilder query = new StringBuilder();
        query.append("Delete from [dbo].[STR_Daily] where chain_id=7777 ;");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void populateD360CapacityData() {
        StringBuilder query = new StringBuilder();
        query.append("INSERT [dbo].[D360_MKT_Hist_Capacity] ([D360_Comp_Set_ID], [Occupancy_DT], [Capacity], [Property_ID], [Created_DTTM], [Last_Updated_DTTM]) VALUES ( 99999, CAST(N'2018-07-10' AS Date), 5555, 5, CAST(N'2015-11-17T20:43:11.690' AS DateTime), CAST(N'2015-11-17T20:43:11.690' AS DateTime));\n");
        query.append("INSERT [dbo].[D360_MKT_Hist_Capacity] ([D360_Comp_Set_ID], [Occupancy_DT], [Capacity], [Property_ID], [Created_DTTM], [Last_Updated_DTTM]) VALUES ( 99999, CAST(N'2018-07-11' AS Date), 5556, 5, CAST(N'2015-11-17T20:43:11.690' AS DateTime), CAST(N'2015-11-17T20:43:11.690' AS DateTime));\n");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void deleteD360CapacityData() {
        StringBuilder query = new StringBuilder();
        query.append("Delete from [dbo].[D360_MKT_Hist_Capacity] where D360_Comp_Set_ID=99999 ;");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void populateD360BookingSummaryData() {
        StringBuilder query = new StringBuilder();
        query.append("INSERT into [dbo].[D360_Booking_Summary_Pace] ([D360_Comp_Set_ID], [D360_MKT_Seg_Detail_ID], [Capture_DT], [Occupancy_DT], [Arrivals], [Rooms_Sold], [Room_Revenue], [Mkt_Arrivals], [Mkt_Rooms_Sold], [Mkt_Room_Revenue], [Property_ID], [Created_DTTM], [Last_Updated_DTTM]) VALUES (9999, 2, CAST(N'2018-07-10' AS Date), CAST(N'2018-07-10' AS Date), 11, 0, CAST(0.00 AS Numeric(19, 2)), 12, 3, CAST(38.10 AS Numeric(19, 2)), 5, CAST(N'2020-02-21T00:58:14.747' AS DateTime), CAST(N'2020-02-21T00:58:14.747' AS DateTime));\n");
        query.append("INSERT into [dbo].[D360_Booking_Summary_Pace] ([D360_Comp_Set_ID], [D360_MKT_Seg_Detail_ID], [Capture_DT], [Occupancy_DT], [Arrivals], [Rooms_Sold], [Room_Revenue], [Mkt_Arrivals], [Mkt_Rooms_Sold], [Mkt_Room_Revenue], [Property_ID], [Created_DTTM], [Last_Updated_DTTM]) VALUES (9999, 2, CAST(N'2018-07-11' AS Date), CAST(N'2018-07-11' AS Date), 13, 0, CAST(0.00 AS Numeric(19, 2)), 14, 33, CAST(48.10 AS Numeric(19, 2)), 5, CAST(N'2020-02-21T00:58:14.747' AS DateTime), CAST(N'2020-02-21T00:58:14.747' AS DateTime));\n");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void deleteD360BookingSummaryData() {
        StringBuilder query = new StringBuilder();
        query.append("Delete from [dbo].[D360_Booking_Summary_Pace] where D360_Comp_Set_ID=9999 ;");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void insertGroupBlock(StringBuilder insertQuery) {
        insertQuery.append(" insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate, IsPeakNight) " +
                "values((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_1'),'2018-07-10',7,5,4,6,55.00,1)");
        insertQuery.append(" insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate, IsPeakNight) " +
                "values ((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_2'),'2018-07-11',5,9,8,11,75.00,1)");
    }

    private void insertPaceGroupBlock(StringBuilder insertQuery) {
        insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " +
                "values((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_1'),'2018-07-10',7,'2018-07-10',5,4,6,55.88888)");
        insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " +
                "values ((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_2'),'2018-07-11',5,'2018-07-11',9,8,11,75.12345)");
        insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " +
                "values ((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_2'),'2018-07-09',5,'2018-07-11',9,8,11,75.12345)");
        insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " +
                "values ((select Group_ID from Group_Master where Group_Code='OPTIX_TEST_CODE_2'),'2018-07-12',5,'2018-07-11',9,8,11,75.12345)");
    }

    private void prepareDataForOptixDatafeed() {
        StringBuilder insertQuery = new StringBuilder();
        insertReservationNight(insertQuery);
        insertPostDepartureRevenue(insertQuery);
        insertOccupancyFCST(insertQuery);
        insertGroupMaster(insertQuery);
        insertGroupBlock(insertQuery);
        insertPaceGroupBlock(insertQuery);
        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void insertGroupMaster(StringBuilder insertQuery) {
        insertQuery.append(" insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT,Cut_Off_date,Cut_Off_days) " +
                "values(000005, 'OPTIX_TEST_CODE_1', 'Test Group Name1', 'Test Group Description1', 7,'DEFINITE','TRANS','2019-07-07','2019-07-10','2018-07-10','2018-07-10',0)");
        insertQuery.append(" insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT,Cut_Off_date,Cut_Off_days) "
                + "values(000005, 'OPTIX_TEST_CODE_2', 'Test Group Name2', 'Test Group Description2', 5,'DEFINITE','TRANS','2019-09-07','2019-09-10','2018-07-11','2018-07-11',0)");
    }

    private void prepareReservationNightData() {
        StringBuilder insertQuery = new StringBuilder();

        insertQuery.append("INSERT INTO [dbo].[Reservation_Night_Change]\n" +
                "([Reservation_Identifier],[Occupancy_DT],[File_Metadata_ID],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue],[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue]\n" +
                ",[Total_Revenue],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children],[Number_Adults],[Confirmation_No],[Change_Type],[Change_DTTM])\n" +
                " VALUES(12345678901,'2018-07-10',7387283,'SS','2018-07-10','2018-07-11','2017-08-12','2018-10-10','SXBL',4,7,100.00,140.00\n" +
                ",2.00000,0.00000,0.00000,240.00,'SHHQO1',25.00,1108,'IN',10,25,123,1,'2017-10-01 03:18:43.000')");

        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void deleteReservationNightData() {
        StringBuilder deleteQuery = new StringBuilder();

        deleteQuery.append("DELETE FROM Reservation_Night_Change where Occupancy_DT = '2018-07-10'");

        tenantService.executeUpdateByNativeQuery(deleteQuery.toString());
    }

    private void prepareBudgetDataAtBusinessTypeLevel() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" delete from Budget_Data");
        insertQuery.append(" insert into Budget_Data values(1, '2018-07-10',20, 100.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values(2, '2018-07-10',40, 300.00, 1, GETDATE(), 1, GETDATE())");

        insertQuery.append(" insert into Budget_Data values(1, '2018-07-11',25, 115.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values(2, '2018-07-11',45, 333.00, 1, GETDATE(), 1, GETDATE())");

        System.out.println(insertQuery.toString());
        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void prepareBudgetDataAtBusinessViewLevel() {
        StringBuilder insertQuery = new StringBuilder();

        insertQuery.append(" delete from Business_Group");
        insertQuery.append(" insert into Business_Group Values('Corporate Groups','Corporate Groups',1,1,11403,11403,GETDATE(),GETDATE(),'000005')");
        insertQuery.append(" insert into Business_Group Values('Leisure Groups','Leisure Groups',1,1,11403,11403,GETDATE(),GETDATE(),'000005')");
        insertQuery.append(" insert into Business_Group Values('Transient Blocks','Blocks & Allotments',1,1,11403,11403,GETDATE(),GETDATE(),'000005')");
        insertQuery.append(" insert into Business_Group Values('Complimentary and House Use','Comp & House Use',1,1,11403,11403,GETDATE(),GETDATE(),'000005')");
        insertQuery.append(" insert into Business_Group Values('Transient','Transient',1,1,11403,11403,GETDATE(),GETDATE(),'000005')");

        insertQuery.append(" delete from Budget_Data");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Corporate Groups'), '2018-07-10',20, 100.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Leisure Groups'), '2018-07-10',40, 300.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Transient Blocks'), '2018-07-10',50, 450.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Complimentary and House Use'), '2018-07-10',60, 600.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Transient'), '2018-07-10',70, 750.00, 1, GETDATE(), 1, GETDATE())");

        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Corporate Groups'), '2018-07-11',25, 150.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Leisure Groups'), '2018-07-11',45, 350.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Transient Blocks'), '2018-07-11',55, 490.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Complimentary and House Use'), '2018-07-11',65, 650.00, 1, GETDATE(), 1, GETDATE())");
        insertQuery.append(" insert into Budget_Data values((select Business_Group_ID from Business_Group where Business_Group_Name='Transient'), '2018-07-11',75, 790.00, 1, GETDATE(), 1, GETDATE())");

        System.out.println(insertQuery.toString());
        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private BudgetConfig getBudgetConfig(boolean isBusinessTypeLevel) {
        BudgetLevel budgetLevel = new BudgetLevel();
        if (isBusinessTypeLevel) {
            budgetLevel.setId(1);
            budgetLevel.setBudgetLevel("Business Type");
        } else {
            budgetLevel.setId(2);
            budgetLevel.setBudgetLevel("Business View");
        }

        BudgetConfig budgetConfig = new BudgetConfig();
        budgetConfig.setBudgetLevel(budgetLevel);
        budgetConfig.setBudgetDisplayName("Budget");
        budgetConfig.setId(1);
        budgetConfig.setModuleName("client.budget");

        return budgetConfig;
    }

    private void insertOccupancyFCST(StringBuilder insertQuery) {
        insertQuery.append(" insert into Occupancy_FCST(Decision_ID,Property_ID,MKT_SEG_ID,Accom_Type_ID,Occupancy_DT," +
                "Occupancy_NBR,Revenue,Month_ID,Year_ID)" +
                " values((select MAX(decision_id) from Decision),5,1,4,'2018-07-10',155,40008.9800,6,5)");

        insertQuery.append(" insert into Occupancy_FCST(Decision_ID,Property_ID,MKT_SEG_ID,Accom_Type_ID,Occupancy_DT," +
                "Occupancy_NBR,Revenue,Month_ID,Year_ID)" +
                " values((select MAX(decision_id) from Decision),5,1,5,'2018-07-11',155,40008.9800,6,5)");
    }

    private void insertReservationNight(StringBuilder insertQuery) {
        insertQuery.append(" insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT]," +
                "[Booking_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code]," +
                "[Rate_Value],[Number_Children],[Number_Adults],[CreateDate_DTTM],[Occupancy_DT]) " +
                "values(1, 000005, '12345678901','SS','2018-07-10','2018-07-11','2017-08-12','SXBL', 4, 7, 1108, 'IN', 100.00, 140.00, 240.00,'SHHQO1',25.00,10,25,'2012-09-18 12:57:25.957', '2018-07-10')");

        insertQuery.append(" insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT]," +
                "[Booking_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code]," +
                "[Rate_Value],[Number_Children],[Number_Adults],[CreateDate_DTTM],[Occupancy_DT]) " +
                "values(1, 000005, '12345678901','XX','2018-07-11','2018-07-12','2017-08-12','KXTD', 4, 7, 421, 'IN', 100.00, 140.00, 240.00,'SHHQO2',30.00,15,40,'2012-09-18 12:57:25.957', '2018-07-11')");

    }

    private void insertPostDepartureRevenue(StringBuilder insertQuery) {
        insertQuery.append("insert into post_departure_revenue (reservation_identifier, occupancy_dt, mkt_seg_id, accom_type_id, room_revenue, food_revenue, other_revenue,total_revenue,market_code,rate_code,rate_value) " +
                "values ('12345678901', '2018-07-12', 7, 4, 1111, 222, 33, 1366, null,'SHHQO1',25) ");
    }

    private void prepareMarketAccomActivity() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" delete from Mkt_Accom_Activity");
        insertQuery.append(" insert into Mkt_Accom_Activity([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID],[Rooms_Sold],[Arrivals],[Departures]," +
                "[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM],[CreateDate],[Pseudo_Room_Revenue])" +
                "values(5,'2018-07-10','2019-01-18 00:00:00.000',(select Mkt_Seg_ID from Mkt_Seg where  MKt_Seg_Code='BART'),(select Accom_Type_ID from Accom_Type where  Accom_Type_Code='DLX')," +
                "7,5,4,0,0,360.00000,20.00000,380.00000,1,'2020-07-17 00:00:00.000','2020-03-19 21:56:19.407',0.00000)");

        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void preparePaceGroupMasterData() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append("INSERT [dbo].[Pace_Group_Master] ([Group_ID], [Business_Day_End_DT], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days], [File_Metadata_ID]) VALUES (49, CAST(N'2017-10-02' AS Date), N'297950', N'0917LUCIAN_Luciani Wedding Block', N'Luciani Wedding Block', NULL, NULL, N'CXL', N'GROUP', 20, CAST(N'2017-09-15' AS Date), CAST(N'2017-09-18' AS Date), CAST(N'2018-07-10' AS Date), NULL, CAST(N'2018-07-10' AS Date), NULL, NULL, CAST(N'2016-06-12' AS Date), 10, 2)");
        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void prepareLRVPaceData() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append("INSERT [dbo].[PACE_LRV] ([Decision_ID], [Property_ID], [Accom_Class_ID], [Occupancy_DT], [LRV]) VALUES ((select MAX(decision_id) from Decision), 5, 2,'2018-07-10', 12.34)");
        tenantService.executeUpdateByNativeQuery(insertQuery.toString());
    }

    @Test
    public void testGetWebrateNullLastSuccessfulDate() {

        // Create request with specific start date
        Date startDate = DateUtil.getDate(10, 6, 2018, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, null, null);

        // Call the method under test
        List<WebrateDTO> result = optixDatafeedService.getWebrateStreaming(request).collect(Collectors.toList());

        // Verify results
        assertEquals(10, result.size());

    }


    @Test
    public void testGetWebrateStreamingWithLastSuccessfulDate() {
        // Create request with specific start and last success dates
        Date startDate = DateUtil.getDate(10, 6, 2018, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, null, new Date());

        // Call the method under test
        Stream<WebrateDTO> resultStream = optixDatafeedService.getWebrateStreaming(request);
        List<WebrateDTO> result = resultStream.collect(Collectors.toList());

        // Verify results (only records after lastSuccessDate should be returned)
        assertEquals(0, result.size());

    }

    @Test
    public void testGetGroupBlockStreamingWithLastSuccessfulDateFirstRunTrue() {
        // Create request with specific start and last success dates
        Date startDate = DateUtil.getDate(10, 6, 2022, 0, 0, 0);
        Date endDate = DateUtil.getDate(10, 6, 2025, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, endDate, new Date());

        // Call the method under test
        Stream<GroupBlockDTO> resultStream = optixDatafeedService.getGroupBlockStreaming(request, true);
        List<GroupBlockDTO> result = resultStream.collect(Collectors.toList());

        // Verify results (only records after lastSuccessDate should be returned)
        assertEquals(0, result.size());
    }

    private void updateLastUpdatedTime() {
        var groupMaster = tenantCrudService().findAll(GroupBlockMaster.class);
        groupMaster.stream().map(group -> {
                    group.setLastUpdatedDate(java.time.LocalDateTime.now());
                    return group;
                }
        );
        tenantCrudService().save(groupMaster);
    }


    @Test
    public void testGetGroupBlockStreamingWithLastSuccessfulDateFirstRunFalse() {
        // Create request with specific start and last success dates
        Date startDate = DateUtil.getDate(10, 6, 2022, 0, 0, 0);
        Date endDate = DateUtil.getDate(10, 6, 2026, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, endDate, new Date());

        // Call the method under test
        Stream<GroupBlockDTO> resultStream = optixDatafeedService.getGroupBlockStreaming(request, false);
        List<GroupBlockDTO> result = resultStream.collect(Collectors.toList());

        // Verify results (only records after lastSuccessDate should be returned)
        assertEquals(0, result.size());

    }

    @Test
    void getBookingSummaryDemand360Streaming() {
        var demand360MarketSegmentDetail = new Demand360MarketSegmentDetail();
        demand360MarketSegmentDetail.setId(2);
        demand360MarketSegmentDetail.setMarktetSegmentName("Test Market Segment");
        demand360MarketSegmentDetail.setMarketSegmentDetailCode(3);
        demand360MarketSegmentDetail.setMarketSegmentGroup("Test Group");
        tenantCrudService().save(demand360MarketSegmentDetail);

        var occupancyDate = LocalDate.now().minusMonths(1);
        var demand360BookingSummaryPace = new Demand360BookingSummaryPace();
        demand360BookingSummaryPace.setOccupancyDate(occupancyDate);
        demand360BookingSummaryPace.setArrivals(10);
        demand360BookingSummaryPace.setRoomsSold(20);
        demand360BookingSummaryPace.setRoomRevenue(BigDecimal.valueOf(1000));
        demand360BookingSummaryPace.setMktArrivals(15);
        demand360BookingSummaryPace.setMktRoomSold(25);
        demand360BookingSummaryPace.setMktRoomRevenue(BigDecimal.valueOf(1500));
        demand360BookingSummaryPace.setCaptureDate(occupancyDate.minusYears(1));
        demand360BookingSummaryPace.setCompSetId(9999);
        demand360BookingSummaryPace.setDemand360MarketSegmentDetail(demand360MarketSegmentDetail);
        demand360BookingSummaryPace.setPropertyId(5);
        tenantCrudService().save(demand360BookingSummaryPace);

        var startDate = occupancyDate.minusYears(1).toDate();
        var endDate = occupancyDate.toDate();
        var lastSuccessfulDate = startDate;
        var datafeedRequest = new DatafeedRequest(0, 10, startDate, endDate, lastSuccessfulDate);


        // last successful date is in the past, so it should return the data
        var stream = optixDatafeedService.getBookingSummaryDemand360Streaming(datafeedRequest, false);
        var list = stream.collect(Collectors.toList());
        assertEquals(1, list.size(), "One record should be returned as the last successful date is in the past");
        // Verify the content of the returned record
        var demand360BookingSummary = list.get(0);
        assertEquals(10, demand360BookingSummary.getArrivals());
        assertEquals(20, demand360BookingSummary.getRoomsSold());
        assertEquals(BigDecimal.valueOf(1000), demand360BookingSummary.getRoomRevenue());
        assertEquals(15, demand360BookingSummary.getMktArrivals());
        assertEquals(25, demand360BookingSummary.getMktRoomSold());
        assertEquals(BigDecimal.valueOf(1500), demand360BookingSummary.getMktRoomRevenue());

        datafeedRequest = new DatafeedRequest(0, 10, startDate, endDate, new Date());
        stream = optixDatafeedService.getBookingSummaryDemand360Streaming(datafeedRequest, false);
        assertEquals(0, stream.count(), "No records should be returned as the last successful date is in the future");
    }

    @Test
    public void testGetD360FirstRunTrue() {

        // Create request with specific start date
        Date startDate = DateUtil.getDate(10, 6, 2010, 0, 0, 0);
        Date endDate = DateUtil.getDate(10, 6, 2126, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, endDate, new Date(0));

        // Call the method under test
        var result = optixDatafeedService.getHotelCapacityDemand360Streaming(request, true).collect(Collectors.toList());

        // Verify results
        assertEquals(0, result.size());

    }

    @Test
    public void testGetD360FirstRunFalse() {

        // Create request with specific start and last success dates
        Date startDate = DateUtil.getDate(10, 6, 2010, 0, 0, 0);
        Date endDate = DateUtil.getDate(10, 6, 2126, 0, 0, 0);
        DatafeedRequest request = new DatafeedRequest(0, 10, startDate, endDate, new Date());

        // Call the method under test
        var result = optixDatafeedService.getHotelCapacityDemand360Streaming(request, false).collect(Collectors.toList());

        // Verify results (only records after lastSuccessDate should be returned)
        assertEquals(0, result.size());

    }

}

