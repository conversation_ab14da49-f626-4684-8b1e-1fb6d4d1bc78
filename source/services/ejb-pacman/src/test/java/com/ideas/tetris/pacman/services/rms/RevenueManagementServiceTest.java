package com.ideas.tetris.pacman.services.rms;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.HeatMapService;
import com.ideas.tetris.pacman.services.bestavailablerate.OverrideService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.Response;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyDateCapacity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.RoomTypeOccupancyDateCapacityDetails;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDataDetailsDto;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetric2;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.MetricResponse;
import com.ideas.tetris.pacman.services.dashboard.builder.ActivityBuilder;
import com.ideas.tetris.pacman.services.dashboard.type.GroupByType;
import com.ideas.tetris.pacman.services.dashboard.type.MetricType2;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demand360.Demand360Service;
import com.ideas.tetris.pacman.services.demand360.entity.Demand360Summary;
import com.ideas.tetris.pacman.services.eventaggregator.PropertyState;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.dto.*;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.dto.heatMapConfiguration.HeatMapConfiguration;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.dto.heatMapConfiguration.HeatMapConfigurations;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.BarRate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColors;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfig;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfigType;
import com.ideas.tetris.pacman.services.rms.dto.*;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.login.LoginRestService;
import com.ideas.tetris.pacman.services.security.login.vo.AuthorizedUserVO;
import com.ideas.tetris.pacman.services.security.login.vo.ModulePermissionVO;
import com.ideas.tetris.pacman.services.security.login.vo.PropertyVO;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.str.entity.STRDaily;
import com.ideas.tetris.pacman.services.str.service.STRService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ParameterType;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ideas.tetris.pacman.services.dashboard.util.DateCalculator.calculateDateForLastYear;
import static com.ideas.tetris.pacman.services.security.login.mapper.PropertyDetailsMapper.HIGH_OCC_CUT_OFF;
import static com.ideas.tetris.pacman.services.security.login.mapper.PropertyDetailsMapper.LOW_OCC_CUT_OFF;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJodaLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.toDate;
import static java.util.Collections.emptySet;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.util.CollectionUtils.isEmpty;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RevenueManagementServiceTest {
    public static final double OCC_FCST_PERCENT = 158.96;
    public static final double ADR_FCST = 12.25;
    public static final int SCALE_5 = 5;

    @Spy
    @InjectMocks
    RevenueManagementService revenueManagementService;

    @Mock
    AuthorizationService authorizationService;

    @Mock
    LoginRestService loginRestService;

    @Mock
    CrudService globalCrudService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private ActivityBuilder activityBuilder;

    @Mock
    private DateService dateService;

    @Mock
    private GroupEvaluationService groupEvaluationService;

    @Mock
    private GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    private SpecialEventService specialEventService;

    @Mock
    private HeatMapService heatMapService;

    @Mock
    private Demand360Service demand360Service;

    @Mock
    private STRService strService;

    @Mock
    private DashboardService dashboardService;

    @Mock
    private BusinessAnalysisDashboardService businessAnalysisDashboardService;

    @Mock
    private OverrideService overrideService;

    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @Test
    void shouldBeAbleToGetClients(){
        doReturn(Arrays.asList(createClient())).when(authorizationService).retrieveClientByUser();
        List<Client> clients = revenueManagementService.getClients();
        assertEquals(1, clients.size());
        assertEquals(1, clients.get(0).getId());
        assertEquals("BSTN", clients.get(0).getCode());
        assertEquals("BlackStone", clients.get(0).getName());
        assertEquals("UUIDForClient12345", clients.get(0).getUniqueClientId());
    }

    @Test
    void shouldGetConfigParameterValueWhenValidParameterName(){
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        String parameterName = "pacman.feature.ParameterName";
        ConfigParameter configParameter = getConfigParameter(ParameterType.VALUE_BOOLEAN);
        when(pacmanConfigParamsService.getConfigParameterByName(parameterName)).thenReturn(configParameter);
        when(pacmanConfigParamsService.getBooleanParameterValue(parameterName,
                PacmanWorkContextHelper.getClientCode(),PacmanWorkContextHelper.getPropertyCode())).thenReturn(true);

        ConfigurationParameterDetails parameterValue = revenueManagementService.getConfigurationParameterValue(parameterName);

        assertNotNull(parameterValue);
        assertTrue(Boolean.parseBoolean(parameterValue.getValue()));
    }

    @Test
    void shouldThrowExceptionWhenParameterNameIsInvalid() {
        String invalidParameterName = "invalid.ParameterName";
        String type = "BOOLEAN";
        when(pacmanConfigParamsService.getConfigParameterByName(invalidParameterName)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            revenueManagementService.getConfigurationParameterValue(invalidParameterName);
        });

        assertEquals("Parameter name specified is invalid", exception.getMessage());
    }

    @Test
    void shouldGetOccupanciesByStartDateAndEndDate(){
        //GIVEN
        Date startDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-04");
        Date caughtUpDate = toDate("2017-10-01");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //OccupancyPercentage
        Map<Date, BigDecimal> occupancyPercentage = new HashMap<>();
        occupancyPercentage.put(toDate("2017-10-01"), new BigDecimal("78.0000000"));
        occupancyPercentage.put(toDate("2017-10-02"), new BigDecimal("80.5000000"));
        occupancyPercentage.put(toDate("2017-10-03"), new BigDecimal("82.6900000"));
        occupancyPercentage.put(toDate("2017-10-04"), new BigDecimal("84.0000000"));
        ArgumentCaptor<MetricRequest> argumentCaptorForMetricRequest = ArgumentCaptor.forClass(MetricRequest.class);
        doReturn(occupancyPercentage).when(activityBuilder).buildMetric(any(MetricRequest.class), eq(startDate), eq(endDate), eq(caughtUpDate));
        //Capacities
        List<OccupancyDateCapacity> capacities = new ArrayList<>();
        capacities.add(createCapacity(LocalDate.of(2017, 10, 01), 300, 350));
        capacities.add(createCapacity(LocalDate.of(2017, 10, 02), 200, 250));
        capacities.add(createCapacity(LocalDate.of(2017, 10, 03), 410, 420));
        capacities.add(createCapacity(LocalDate.of(2017, 10, 04), 150, 350));
        doReturn(capacities).when(groupEvaluationService).getCapacitiesForDateRange(toJodaLocalDate(LocalDateUtils.toJavaLocalDate(startDate)),
                toJodaLocalDate(LocalDateUtils.toJavaLocalDate(endDate)));
        //Final Price
        Map<Date, BigDecimal> finalPriceByDate = new HashMap<>();
        finalPriceByDate.put(toDate("2017-10-01"), new BigDecimal("100.0000000"));
        finalPriceByDate.put(toDate("2017-10-02"), new BigDecimal("120.0000000"));
        finalPriceByDate.put(toDate("2017-10-03"), new BigDecimal("140.5500000"));
        finalPriceByDate.put(toDate("2017-10-04"), new BigDecimal("200.0000000"));
        doReturn(finalPriceByDate).when(groupEvaluationService).getFinalPriceForROHForDateRange(startDate, endDate);
        //HeatMap Configurations
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigs = createHeatMapRangeAndColorsConfigWithDefaultAndSeasons();
        doReturn(heatMapRangeAndColorsConfigs).when(heatMapService).getSeasonHeatMapRangeAndColorsConfig(DateUtil.convertDateToLocalDate(startDate),
                DateUtil.convertDateToLocalDate(endDate), false);
        //WHEN
        Occupancy occupancy = revenueManagementService.getOccupanciesBy(startDate, endDate, OccupancyType.PROPERTY);
        //THEN
        verify(activityBuilder).buildMetric(argumentCaptorForMetricRequest.capture(), eq(startDate), eq(endDate), eq(caughtUpDate));
        MetricRequest actualMetricRequest = argumentCaptorForMetricRequest.getValue();
        assertEquals(MetricType2.OCCUPANCY_FORECAST_PERCENT, actualMetricRequest.getMetricType());
        assertOccupancyDetailsFor(toDate("2017-10-01"), new BigDecimal("78.00000"), 350, 300, new BigDecimal("100.00000"), occupancy.getDetails().get(0));
        assertOccupancyDetailsFor(toDate("2017-10-02"), new BigDecimal("80.50000"), 250, 200, new BigDecimal("120.00000"), occupancy.getDetails().get(1));
        assertOccupancyDetailsFor(toDate("2017-10-03"), new BigDecimal("82.69000"), 420, 410, new BigDecimal("140.55000"), occupancy.getDetails().get(2));
        assertOccupancyDetailsFor(toDate("2017-10-04"), new BigDecimal("84.00000"), 350, 150, new BigDecimal("200.00000"), occupancy.getDetails().get(3));
        assertHeatMapConfigurations(occupancy.getHeatMapConfigurations());
        verify(groupPricingConfigurationService, never()).getAccomTypesConfiguredForRoomClassEvaluations();
        verify(groupEvaluationService, never()).getFinalPriceForRoomTypesForDateRange(any(), any(), any());
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(any(),any(),eq(false));
    }

    @Test
    void shouldGetOccupanciesByStartDateAndEndDateForRC(){
        //GIVEN
        Date startDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-04");
        Date caughtUpDate = toDate("2017-10-01");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //OccupancyPercentage
        Map<Date, BigDecimal> occupancyPercentage = new HashMap<>();
        occupancyPercentage.put(toDate("2017-10-01"), new BigDecimal("78.00"));
        occupancyPercentage.put(toDate("2017-10-02"), new BigDecimal("80.50"));
        occupancyPercentage.put(toDate("2017-10-03"), new BigDecimal("82.69"));
        occupancyPercentage.put(toDate("2017-10-04"), new BigDecimal("84.00"));
        ArgumentCaptor<MetricRequest> argumentCaptorForMetricRequest = ArgumentCaptor.forClass(MetricRequest.class);
        doReturn(occupancyPercentage).when(activityBuilder).buildMetric(any(MetricRequest.class), eq(startDate), eq(endDate), eq(caughtUpDate));
        //Capacities
        List<AccomType> accomTypes = new ArrayList<>();
        AccomType stdRtAccomType = createAccomTypeBy(1, "STD_RT");
        AccomType dlxRtAccomType = createAccomTypeBy(2, "DLX_RT");
        accomTypes.add(stdRtAccomType);
        accomTypes.add(dlxRtAccomType);
        doReturn(accomTypes).when(groupPricingConfigurationService).getAccomTypesConfiguredForRoomClassEvaluations();
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> roomTypeCapacitiesForDateRangeDetails = new HashMap<>();
        List<RoomTypeOccupancyDateCapacityDetails> stdRtCapacities = new ArrayList<>();
        List<RoomTypeOccupancyDateCapacityDetails> dlxRtCapacities = new ArrayList<>();
        stdRtCapacities.add(createRoomTypeOccupancyCapacityDetails(stdRtAccomType, LocalDate.of(2017, 10, 01), 120, 100));
        stdRtCapacities.add(createRoomTypeOccupancyCapacityDetails(stdRtAccomType, LocalDate.of(2017, 10, 02), 140, 130));
        stdRtCapacities.add(createRoomTypeOccupancyCapacityDetails(stdRtAccomType, LocalDate.of(2017, 10, 03), 110, 100));
        stdRtCapacities.add(createRoomTypeOccupancyCapacityDetails(stdRtAccomType, LocalDate.of(2017, 10, 04), 130, 100));
        dlxRtCapacities.add(createRoomTypeOccupancyCapacityDetails(dlxRtAccomType, LocalDate.of(2017, 10, 01), 150, 120));
        dlxRtCapacities.add(createRoomTypeOccupancyCapacityDetails(dlxRtAccomType, LocalDate.of(2017, 10, 02), 140, 110));
        dlxRtCapacities.add(createRoomTypeOccupancyCapacityDetails(dlxRtAccomType, LocalDate.of(2017, 10, 03), 150, 130));
        dlxRtCapacities.add(createRoomTypeOccupancyCapacityDetails(dlxRtAccomType, LocalDate.of(2017, 10, 04), 140, 130));
        roomTypeCapacitiesForDateRangeDetails.put(stdRtAccomType, stdRtCapacities);
        roomTypeCapacitiesForDateRangeDetails.put(dlxRtAccomType, dlxRtCapacities);
        doReturn(roomTypeCapacitiesForDateRangeDetails).when(groupEvaluationService).getRoomTypeCapacitiesForDateRangeDetails(accomTypes,
                toJodaLocalDate(LocalDate.of(2017, 10, 01)),
                toJodaLocalDate(LocalDate.of(2017, 10, 04)));
        //Final Price
        List<BarRate> barRates = new ArrayList<>();
        barRates.add(new BarRate(toDate("2017-10-01"), stdRtAccomType.getId(), new BigDecimal("150.5000000")));
        barRates.add(new BarRate(toDate("2017-10-02"), stdRtAccomType.getId(), new BigDecimal("250.5000000")));
        barRates.add(new BarRate(toDate("2017-10-03"), stdRtAccomType.getId(), new BigDecimal("350.5000000")));
        barRates.add(new BarRate(toDate("2017-10-04"), stdRtAccomType.getId(), new BigDecimal("450.5000000")));
        barRates.add(new BarRate(toDate("2017-10-01"), dlxRtAccomType.getId(), new BigDecimal("550.5000000")));
        barRates.add(new BarRate(toDate("2017-10-02"), dlxRtAccomType.getId(), new BigDecimal("650.5000000")));
        barRates.add(new BarRate(toDate("2017-10-03"), dlxRtAccomType.getId(), new BigDecimal("750.5000000")));
        barRates.add(new BarRate(toDate("2017-10-04"), dlxRtAccomType.getId(), new BigDecimal("850.5000000")));
        doReturn(barRates).when(groupEvaluationService).getFinalPriceForRoomTypesForDateRange(startDate, endDate, Arrays.asList(stdRtAccomType.getId(), dlxRtAccomType.getId()));
        //HeatMap Configurations
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigs = createHeatMapRangeAndColorsConfigWithDefaultAndSeasons();
        doReturn(heatMapRangeAndColorsConfigs).when(heatMapService).getSeasonHeatMapRangeAndColorsConfig(DateUtil.convertDateToLocalDate(startDate),
                DateUtil.convertDateToLocalDate(endDate), false);
        //WHEN
        Occupancy occupancy = revenueManagementService.getOccupanciesBy(startDate, endDate, OccupancyType.ROOM_TYPE);
        //THEN
        verify(activityBuilder).buildMetric(argumentCaptorForMetricRequest.capture(), eq(startDate), eq(endDate), eq(caughtUpDate));
        MetricRequest actualMetricRequest = argumentCaptorForMetricRequest.getValue();
        assertEquals(MetricType2.OCCUPANCY_FORECAST_PERCENT, actualMetricRequest.getMetricType());
        OccupancyDetails occupancyDetailsDay1 = occupancy.getDetails().get(0);
        OccupancyDetails occupancyDetailsDay2 = occupancy.getDetails().get(1);
        OccupancyDetails occupancyDetailsDay3 = occupancy.getDetails().get(2);
        OccupancyDetails occupancyDetailsDay4 = occupancy.getDetails().get(3);
        assertOccupancyDetailsFor(toDate("2017-10-01"), new BigDecimal("78.00000"), occupancyDetailsDay1);
        assertOccupancyDetailsFor(toDate("2017-10-02"), new BigDecimal("80.50000"), occupancyDetailsDay2);
        assertOccupancyDetailsFor(toDate("2017-10-03"), new BigDecimal("82.69000"), occupancyDetailsDay3);
        assertOccupancyDetailsFor(toDate("2017-10-04"), new BigDecimal("84.00000"), occupancyDetailsDay4);
        assertOccupancyDetailsForRoomType(stdRtAccomType, 120, 100, new BigDecimal("150.50000"), occupancyDetailsDay1.getRoomTypes().get(0));
        assertOccupancyDetailsForRoomType(dlxRtAccomType, 150, 120, new BigDecimal("550.50000"), occupancyDetailsDay1.getRoomTypes().get(1));
        assertOccupancyDetailsForRoomType(stdRtAccomType, 140, 130, new BigDecimal("250.50000"), occupancyDetailsDay2.getRoomTypes().get(0));
        assertOccupancyDetailsForRoomType(dlxRtAccomType, 140, 110, new BigDecimal("650.50000"), occupancyDetailsDay2.getRoomTypes().get(1));
        assertOccupancyDetailsForRoomType(stdRtAccomType, 110, 100, new BigDecimal("350.50000"), occupancyDetailsDay3.getRoomTypes().get(0));
        assertOccupancyDetailsForRoomType(dlxRtAccomType, 150, 130, new BigDecimal("750.50000"), occupancyDetailsDay3.getRoomTypes().get(1));
        assertOccupancyDetailsForRoomType(stdRtAccomType, 130, 100, new BigDecimal("450.50000"), occupancyDetailsDay4.getRoomTypes().get(0));
        assertOccupancyDetailsForRoomType(dlxRtAccomType, 140, 130, new BigDecimal("850.50000"), occupancyDetailsDay4.getRoomTypes().get(1));
        assertHeatMapConfigurations(occupancy.getHeatMapConfigurations());
        verify(groupEvaluationService, never()).getCapacitiesForDateRange(any(), any());
        verify(groupEvaluationService, never()).getFinalPriceForROHForDateRange(any(), any());
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(any(),any(),eq(false));
    }

    @Test
    void shouldGetSpecialEventsInOccupanciesByStartDateAndEndDate(){
        //GIVEN
        Date startDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-04");
        Date caughtUpDate = toDate("2017-10-01");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //Special Events
        List<PropertySpecialEventInstance> specialEventsForDateRange = new ArrayList<>();
        specialEventsForDateRange.add(createSpecialEventsBy("Diwali", toDate("2017-10-02"), toDate("2017-10-03")));
        specialEventsForDateRange.add(createSpecialEventsBy("Dasara", toDate("2017-10-03"), toDate("2017-10-04")));
        doReturn(specialEventsForDateRange).when(specialEventService).getAllSpecialEventInstanceByDateRange(startDate, endDate);
        //WHEN
        Occupancy occupancy = revenueManagementService.getOccupanciesBy(startDate, endDate, OccupancyType.PROPERTY);
        //THEN
        OccupancyDetails occupancyDetailsDay1 = occupancy.getDetails().get(0);
        assertEquals(toDate("2017-10-01"), occupancyDetailsDay1.getDate());
        assertEquals(Collections.EMPTY_LIST, occupancyDetailsDay1.getSpecialEvents());

        OccupancyDetails occupancyDetailsDay2 = occupancy.getDetails().get(1);
        assertEquals(toDate("2017-10-02"), occupancyDetailsDay2.getDate());
        assertSpecialEvent("Diwali", toDate("2017-10-02"), toDate("2017-10-03"),
                occupancyDetailsDay2.getSpecialEvents().get(0));

        OccupancyDetails occupancyDetailsDay3 = occupancy.getDetails().get(2);
        assertEquals(toDate("2017-10-03"), occupancyDetailsDay3.getDate());
        assertSpecialEvent("Diwali", toDate("2017-10-02"), toDate("2017-10-03"),
                occupancyDetailsDay3.getSpecialEvents().get(0));
        assertSpecialEvent("Dasara", toDate("2017-10-03"), toDate("2017-10-04"),
                occupancyDetailsDay3.getSpecialEvents().get(1));

        OccupancyDetails occupancyDetailsDay4 = occupancy.getDetails().get(3);
        assertEquals(toDate("2017-10-04"), occupancyDetailsDay4.getDate());
        assertSpecialEvent("Dasara", toDate("2017-10-03"), toDate("2017-10-04"),
                occupancyDetailsDay4.getSpecialEvents().get(0));
    }

    private static PropertySpecialEventInstance createSpecialEventsBy(String eventName, Date startDate, Date endDate) {
        PropertySpecialEventInstance specialEventInstance = new PropertySpecialEventInstance();
        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        propertySpecialEvent.setName(eventName);
        specialEventInstance.setPropertySpecialEvent(propertySpecialEvent);
        specialEventInstance.setStartDate(startDate);
        specialEventInstance.setEndDate(endDate);
        return specialEventInstance;
    }

    private static void assertOccupancyDetailsForRoomType(AccomType expectedRtAccomType, int expectedPhysicalCapacity, int expectedAvailableRoomToSell,
                                                          BigDecimal expectedFinalBar, RoomTypeOccupancyDetails roomTypeOccupancyDetails) {
        assertEquals(expectedRtAccomType.getId(), roomTypeOccupancyDetails.getRoomTypeId());
        assertEquals(expectedPhysicalCapacity, roomTypeOccupancyDetails.getPhysicalCapacity());
        assertEquals(expectedAvailableRoomToSell, roomTypeOccupancyDetails.getAvailableRoomsToSell());
        assertEquals(expectedFinalBar, roomTypeOccupancyDetails.getFinalBar());
    }

    private static RoomTypeOccupancyDateCapacityDetails createRoomTypeOccupancyCapacityDetails(AccomType accomType, LocalDate occupancyDate, int physicalCapacity, int remainingCapacity) {
        RoomTypeOccupancyDateCapacityDetails roomTypeOccupancyCapacityDetails = new RoomTypeOccupancyDateCapacityDetails();
        roomTypeOccupancyCapacityDetails.setAccomTypeId(accomType.getId());
        roomTypeOccupancyCapacityDetails.setOccupancyDate(toJodaLocalDate(occupancyDate));
        roomTypeOccupancyCapacityDetails.setCapacity(physicalCapacity);
        roomTypeOccupancyCapacityDetails.setRemainingCapacity(remainingCapacity);
        return roomTypeOccupancyCapacityDetails;
    }

    private static AccomType createAccomTypeBy(int id, String name) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(name);
        return accomType;
    }

    @Test
    void shouldGetEndDateAsLastDayOfMonthWhenStartDateIsPresentButEndDateIsNot(){
        //GIVEN
        Date startDate = toDate("2017-10-05");
        Date expectedEndDate = toDate("2017-10-31");
        Date caughtUpDate = toDate("2017-10-05");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //WHEN
        revenueManagementService.getOccupanciesBy(startDate, null, OccupancyType.PROPERTY);
        //THEN
        verify(activityBuilder).buildMetric(any(MetricRequest.class), eq(startDate), eq(expectedEndDate), eq(caughtUpDate));
        verify(groupEvaluationService).getCapacitiesForDateRange(toJodaLocalDate(LocalDateUtils.toJavaLocalDate(startDate)),
                toJodaLocalDate(LocalDateUtils.toJavaLocalDate(expectedEndDate)));
        verify(groupEvaluationService).getFinalPriceForROHForDateRange(startDate, expectedEndDate);
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(eq(DateUtil.convertDateToLocalDate(startDate))
                ,eq(DateUtil.convertDateToLocalDate(expectedEndDate)),eq(false));
    }

    @Test
    void shouldGetStartDateAsFirstDayOfMonthWhenEndDateIsPresentButStartDateIsNot(){
        //GIVEN
        Date expectedStartDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-15");
        Date caughtUpDate = toDate("2017-10-05");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //WHEN
        revenueManagementService.getOccupanciesBy(null, endDate, OccupancyType.PROPERTY);
        //THEN
        verify(activityBuilder).buildMetric(any(MetricRequest.class), eq(expectedStartDate), eq(endDate), eq(caughtUpDate));
        verify(groupEvaluationService).getCapacitiesForDateRange(toJodaLocalDate(LocalDateUtils.toJavaLocalDate(expectedStartDate)),
                toJodaLocalDate(LocalDateUtils.toJavaLocalDate(endDate)));
        verify(groupEvaluationService).getFinalPriceForROHForDateRange(expectedStartDate, endDate);
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(eq(DateUtil.convertDateToLocalDate(expectedStartDate))
                ,eq(DateUtil.convertDateToLocalDate(endDate)),eq(false));
    }

    @Test
    void shouldGetOccupanciesForStartDateAsFirstDayAndEndDateAsLastDayOfMonthOfCaughtDateWhenBothAreNotPresent(){
        //GIVEN
        Date expectedStartDate = toDate("2017-10-01");
        Date expectedEndDate = toDate("2017-10-31");
        Date caughtUpDate = toDate("2017-10-05");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //WHEN
        revenueManagementService.getOccupanciesBy(null, null, OccupancyType.PROPERTY);
        //THEN
        verify(activityBuilder).buildMetric(any(MetricRequest.class), eq(expectedStartDate), eq(expectedEndDate), eq(caughtUpDate));
        verify(groupEvaluationService).getCapacitiesForDateRange(toJodaLocalDate(LocalDateUtils.toJavaLocalDate(expectedStartDate)),
                toJodaLocalDate(LocalDateUtils.toJavaLocalDate(expectedEndDate)));
        verify(groupEvaluationService).getFinalPriceForROHForDateRange(expectedStartDate, expectedEndDate);
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(eq(DateUtil.convertDateToLocalDate(expectedStartDate))
                ,eq(DateUtil.convertDateToLocalDate(expectedEndDate)),eq(false));
    }

    @Test
    void shouldGetAnEmptyListForSeasonsInHeatMapConfigurationsWhenNoSeasonsAreConfigured(){
        //GIVEN
        Date startDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-04");
        Date caughtUpDate = toDate("2017-10-01");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //HeatMap Configurations
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigsWithoutSeasons = singletonList(createHeatMapRangeAndColorsConfigWithDefaultAndSeasons().get(0));
        doReturn(heatMapRangeAndColorsConfigsWithoutSeasons).when(heatMapService).getSeasonHeatMapRangeAndColorsConfig(DateUtil.convertDateToLocalDate(startDate),
                DateUtil.convertDateToLocalDate(endDate), false);
        //WHEN
        Occupancy occupancy = revenueManagementService.getOccupanciesBy(startDate, endDate, OccupancyType.PROPERTY);
        //THEN
        HeatMapConfigurations heatMapConfigurations = occupancy.getHeatMapConfigurations();
        assertNotNull(heatMapConfigurations);
        assertEquals(8,heatMapConfigurations.getDefaults().size());
        assertAllDefaultConfigurations(heatMapConfigurations.getDefaults());
        assertTrue(isEmpty(heatMapConfigurations.getSeasons()));
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(any(),any(),eq(false));
    }

    @Test
    void shouldGetDefaultConfigsFromCustomizedConfigsForHeatMapConfigurations(){
        //GIVEN
        Date startDate = toDate("2017-10-01");
        Date endDate = toDate("2017-10-04");
        Date caughtUpDate = toDate("2017-10-01");
        doReturn(caughtUpDate).when(dateService).getCaughtUpDate();
        //HeatMap Configurations
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigsWithDefaultAndSeasons = new ArrayList<>();
        heatMapRangeAndColorsConfigsWithDefaultAndSeasons.addAll(createHeatMapRangeAndColorsConfigWithDefaultAndSeasons());
        heatMapRangeAndColorsConfigsWithDefaultAndSeasons.add(createCustomizedRangeAndColorsConfiguration());
        doReturn(heatMapRangeAndColorsConfigsWithDefaultAndSeasons).when(heatMapService).getSeasonHeatMapRangeAndColorsConfig(DateUtil.convertDateToLocalDate(startDate),
                DateUtil.convertDateToLocalDate(endDate), false);
        //WHEN
        Occupancy occupancy = revenueManagementService.getOccupanciesBy(startDate, endDate, OccupancyType.PROPERTY);
        //THEN
        HeatMapConfigurations heatMapConfigurations = occupancy.getHeatMapConfigurations();
        assertNotNull(heatMapConfigurations);
        assertEquals(8,heatMapConfigurations.getDefaults().size());
        assertDefaultConfigurationsFromCustomized(heatMapConfigurations.getDefaults());
        verify(heatMapService).getSeasonHeatMapRangeAndColorsConfig(any(),any(),eq(false));
    }

    @Test
    void shouldGetHotelCapacity() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2021, 7, 5));
        OccupancyDateCapacity occupancyCapacity = new OccupancyDateCapacity();
        occupancyCapacity.setOccupancyDate(DateUtil.convertDateToLocalDate(occupancyDate));
        occupancyCapacity.setAvailableCapacity(50);
        occupancyCapacity.setPhysicalCapacity(70);
        when(groupEvaluationService.getCapacities(occupancyDate, occupancyDate)).thenReturn(singletonList(occupancyCapacity));

        BigDecimal hotelCapacity = revenueManagementService.getHotelCapacity(occupancyDate);

        assertEquals(BigDecimal.valueOf(70), hotelCapacity);
    }

    @Test
    void shouldReturnNullWhenHotelCapacityIsNotAvailable() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2052, 7, 5));
        when(groupEvaluationService.getCapacities(occupancyDate, occupancyDate)).thenReturn(null);

        BigDecimal hotelCapacity = revenueManagementService.getHotelCapacity(occupancyDate);

        assertNull(hotelCapacity);
    }

    @Test
    void shouldGetDemand360GroupSummary() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 7, 5));
        Demand360Summary groupSummary = new Demand360Summary(11529L,"Group", occupancyDate, BigDecimal.valueOf(2), BigDecimal.valueOf(506.90),
                BigDecimal.valueOf(53), BigDecimal.valueOf(18876.35), BigDecimal.valueOf(1155));
        Demand360Summary otherSummary = new Demand360Summary(11530L,"Retail", occupancyDate, BigDecimal.valueOf(34), BigDecimal.valueOf(16133.07),
                BigDecimal.valueOf(235), BigDecimal.valueOf(134599.52), BigDecimal.valueOf(1155));
        when(demand360Service.getDemand360Summary(occupancyDate, occupancyDate)).thenReturn(List.of(otherSummary, groupSummary));

        Demand360Summary demand360GroupSummary = revenueManagementService.getDemand360GroupSummary(occupancyDate);

        assertEquals("Group", demand360GroupSummary.getMarketSegmentName());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(2), demand360GroupSummary.getRoomsSold()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(506.90), demand360GroupSummary.getRoomRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(53), demand360GroupSummary.getMarketRoomsSold()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(18876.35), demand360GroupSummary.getMarketRoomRevenue()));
    }

    @Test
    void shouldReturnNullWhenDemand360GroupSummaryIsNotFound() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2052, 7, SCALE_5));
        when(demand360Service.getDemand360Summary(occupancyDate, occupancyDate)).thenReturn(emptyList());

        assertNull(revenueManagementService.getDemand360GroupSummary(occupancyDate));
    }

    @Test
    void shouldGetDemand360GroupBlockCommitted() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 7, 5));
        Demand360Summary thisYearSummary = new Demand360Summary(11529L,"Group", occupancyDate, BigDecimal.valueOf(30),
                BigDecimal.valueOf(506.90), BigDecimal.valueOf(53), BigDecimal.valueOf(18876.35), BigDecimal.valueOf(1155));
        Demand360Summary lastYearSummary = new Demand360Summary(10345L,"Group", DateUtil.addYearsToDate(occupancyDate, -1), BigDecimal.valueOf(43),
                BigDecimal.valueOf(429.00), BigDecimal.valueOf(80), BigDecimal.valueOf(28166.43), BigDecimal.valueOf(1155));
        BigDecimal thisYearHotelCapacity = BigDecimal.valueOf(190);
        BigDecimal lastYearHotelCapacity = BigDecimal.valueOf(180);

        Demand360GroupBlockCommitted demand360GroupBlockCommitted = revenueManagementService.getDemand360GroupBlockCommitted(thisYearSummary, lastYearSummary, thisYearHotelCapacity, lastYearHotelCapacity);

        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(53.75000), demand360GroupBlockCommitted.getLastYearHotel()),
                "Last year Hotel Group Block was expected to be 53.75000 but was " + demand360GroupBlockCommitted.getLastYearHotel());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(38.16558), demand360GroupBlockCommitted.getLastYearMSDiff()),
                "Last year Group Block MS Difference was expected to be 38.16558 but was " + demand360GroupBlockCommitted.getLastYearMSDiff());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(56.60377), demand360GroupBlockCommitted.getThisYearHotel()),
                "This year Hotel Group Block was expected to be 56.60377 but was " + demand360GroupBlockCommitted.getThisYearHotel());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(40.15355), demand360GroupBlockCommitted.getThisYearMSDiff()),
                "Last year Group Block MS Difference was expected to be 0.66075 but was " + demand360GroupBlockCommitted.getThisYearMSDiff());
    }

    @Test
    void shouldGetDemand360GroupBlockCommittedWhenMarketCapacityIsNotFound() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 7, 5));
        Demand360Summary thisYearSummary = new Demand360Summary(11529L,"Group", occupancyDate, BigDecimal.valueOf(30),
                BigDecimal.valueOf(506.90), BigDecimal.valueOf(53), BigDecimal.valueOf(18876.35), null);
        Demand360Summary lastYearSummary = new Demand360Summary(10345L,"Group", DateUtil.addYearsToDate(occupancyDate, -1), BigDecimal.valueOf(43),
                BigDecimal.valueOf(429.00), BigDecimal.valueOf(80), BigDecimal.valueOf(28166.43), BigDecimal.valueOf(1155));
        BigDecimal thisYearHotelCapacity = BigDecimal.valueOf(190);
        BigDecimal lastYearHotelCapacity = BigDecimal.valueOf(180);

        Demand360GroupBlockCommitted demand360GroupBlockCommitted = revenueManagementService.getDemand360GroupBlockCommitted(thisYearSummary, lastYearSummary, thisYearHotelCapacity, lastYearHotelCapacity);

        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(53.75000), demand360GroupBlockCommitted.getLastYearHotel()),
                "Last year Hotel Group Block was expected to be 23.88889 but was " + demand360GroupBlockCommitted.getLastYearHotel());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(38.16558), demand360GroupBlockCommitted.getLastYearMSDiff()),
                "Last year Group Block MS Difference was expected to be -8.30447 but was " + demand360GroupBlockCommitted.getLastYearMSDiff());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(56.60377), demand360GroupBlockCommitted.getThisYearHotel()),
                "This year Hotel Group Block was expected to be 15.78947 but was " + demand360GroupBlockCommitted.getThisYearHotel());
        assertNull(demand360GroupBlockCommitted.getThisYearMSDiff());
    }

    @Test
    void shouldGetDemand360GroupBlockCommittedWhenD360AndCapacityAreNotFound() {
        Demand360GroupBlockCommitted demand360GroupBlockCommitted = revenueManagementService.getDemand360GroupBlockCommitted(
                null, null, null, null);

        assertNull(demand360GroupBlockCommitted.getLastYearHotel());
        assertNull(demand360GroupBlockCommitted.getLastYearMSDiff());
        assertNull(demand360GroupBlockCommitted.getThisYearHotel());
        assertNull(demand360GroupBlockCommitted.getThisYearMSDiff());
    }

    @Test
    void shouldGetDemand360GroupADR() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 7, 5));
        Demand360Summary lastYearSummary = new Demand360Summary(10345L,"Group", DateUtil.addYearsToDate(occupancyDate, -1), BigDecimal.valueOf(43),
                BigDecimal.valueOf(429.00), BigDecimal.valueOf(80), BigDecimal.valueOf(28166.43), BigDecimal.valueOf(1155));
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(
                occupancyDate, occupancyDate, false, 7, false))
                .thenReturn(List.of(
                        createBusinessAnalysisDataDetailsDto("GROUP", BigDecimal.TEN),
                        createBusinessAnalysisDataDetailsDto("TRANSIENT", BigDecimal.ONE)));

        Demand360GroupADR demand360GroupADR = revenueManagementService.getDemand360GroupADR(lastYearSummary, BigDecimal.TEN);

        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(9.98), demand360GroupADR.getLastYearHotel()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(352.08), demand360GroupADR.getLastYearMarket()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(10.00), demand360GroupADR.getThisYearHotelForecast()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(0.02), demand360GroupADR.getForecastVariance()));
    }

    @Test
    void shouldGetADRForecastWhenPresent() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 8, 19));
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(occupancyDate, occupancyDate, false, 7, false))
                .thenReturn(List.of(
                        createBusinessAnalysisDataDetailsDto("GROUP", BigDecimal.TEN),
                        createBusinessAnalysisDataDetailsDto("TRANSIENT", BigDecimal.ONE)));

        BigDecimal adrForecast = revenueManagementService.getADRForecast(occupancyDate);

        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, adrForecast));
    }

    @Test
    void shouldGetNullADRForecastWhenNotPresent() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 8, 19));
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(occupancyDate, occupancyDate, false, 7, false))
                .thenReturn(List.of(
                        createBusinessAnalysisDataDetailsDto("TRANSIENT", BigDecimal.ONE)));

        BigDecimal adrForecast = revenueManagementService.getADRForecast(occupancyDate);

        assertNull(adrForecast);
    }

    @Test
    void shouldGetNullBusinessAnalysisDeshboardDataIsNotPresent() {
        Date occupancyDate = JavaLocalDateUtils.toDate(LocalDate.of(2022, 8, 19));
        when(businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(occupancyDate, occupancyDate, false, 7, false))
                .thenReturn(null);

        BigDecimal adrForecast = revenueManagementService.getADRForecast(occupancyDate);

        assertNull(adrForecast);
    }

    private static BusinessAnalysisDataDetailsDto createBusinessAnalysisDataDetailsDto(String name, BigDecimal adrForecast) {
        BusinessAnalysisDataDetailsDto groupDto = new BusinessAnalysisDataDetailsDto();
        groupDto.setName(name);
        groupDto.setAdrForecast(adrForecast);
        return groupDto;
    }

    @Test
    void shouldGetDemand360GroupADRWithD360SummaryIsNotFound() {
        Demand360GroupADR demand360GroupADR = revenueManagementService.getDemand360GroupADR(null, null);

        assertNull(demand360GroupADR.getLastYearHotel());
        assertNull(demand360GroupADR.getLastYearMarket());
        assertNull(demand360GroupADR.getThisYearHotelForecast());
        assertNull(demand360GroupADR.getForecastVariance());
    }

    @Test
    void shouldGetSTRDetails() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2024, 8, 8));
        STRDaily lastYearSTRDaily = buildSTRDaily();
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyBy(DateUtil.convertJavaUtilDateToLocalDate(calculateDateForLastYear(arrivalDate, true))))
                .thenReturn(lastYearSTRDaily);
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertOccupancyDetails(strDetails.getTotalOccupancy());
        assertADRDdetails(strDetails.getADR());
    }

    @Test
    void shouldGetSTRDetailsForDateRange() {
        LocalDate startDate = LocalDate.of(2025, 5, 10);
        LocalDate endDate = LocalDate.of(2025, 5, 12);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2025, 5, 30));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2023, 5, 1));
        when(strService.getSTRDailyForDateRange(startDate.minusWeeks(52), endDate.minusWeeks(52)))
                .thenReturn(List.of(buildSTRDaily(), buildSTRDaily(), buildSTRDaily()));
        when(dashboardService.getMetrics2(anyList(), any(Date.class), any(Date.class))).thenReturn(buildMetricResponseForDateRange());

        STRDetails strDetails = revenueManagementService.getSTRDetails(startDate, endDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertOccupancyDetails(strDetails.getTotalOccupancy());
        assertADRDdetails(strDetails.getADR());
    }

    @Test
    void shouldGetEmptyObjectForDateRangeWhenNoSTRDetailsAvailable() {
        LocalDate startDate = LocalDate.of(2025, 5, 10);
        LocalDate endDate = LocalDate.of(2025, 5, 12);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(null);
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(null);
        when(dashboardService.getMetrics2(anyList(), any(Date.class), any(Date.class))).thenReturn(getEmptyMetricResponse());

        STRDetails strDetails = revenueManagementService.getSTRDetails(startDate, endDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
    }

    @Test
    void shouldGetSTRDetailsWhenDateRangeInFutureForExtendedStay() {
        LocalDate startDate = LocalDate.of(2028, 5, 10);
        LocalDate endDate = LocalDate.of(2028, 5, 12);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyForDateRange(LocalDate.of(2024,5,15), LocalDate.of(2024,5,17)))
                .thenReturn(List.of(buildSTRDaily(), buildSTRDaily(), buildSTRDaily()));
        when(dashboardService.getMetrics2(anyList(), any(Date.class), any(Date.class))).thenReturn(buildMetricResponseForDateRange());

        STRDetails strDetails = revenueManagementService.getSTRDetails(startDate, endDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertOccupancyDetails(strDetails.getTotalOccupancy());
        assertADRDdetails(strDetails.getADR());
    }

    @Test
    void shouldGetSTRDetailsForDateWhenPropertyAvailableIsNull() {
        LocalDate startDate = LocalDate.of(2028, 5, 10);
        LocalDate endDate = LocalDate.of(2028, 5, 12);
        List<STRDaily> strDailyList = List.of(buildSTRDaily(), buildSTRDaily(), buildSTRDaily());
        strDailyList.forEach(str -> str.setPropertyAvailable(null));
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyForDateRange(startDate.minusWeeks(52), endDate.minusWeeks(52)))
                .thenReturn(List.of(buildSTRDaily(), buildSTRDaily(), buildSTRDaily()));
        when(dashboardService.getMetrics2(anyList(), any(Date.class), any(Date.class))).thenReturn(buildMetricResponseForDateRange());

        STRDetails strDetails = revenueManagementService.getSTRDetails(startDate, endDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertNull(strDetails.getTotalOccupancy().getLastYearHotel());
    }

    @Test
    void shouldGetEmptyObjectWhenNoSTRDetailsAvailable() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2024, 8, 8));
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(null);
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(null);
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getEmptyMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
    }

    @Test
    void shouldGetSTRDetailsWhenArrivalDateInFutureForExtendedStay() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2028, 8, 22));
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyBy(LocalDate.of(2023,8,29)))
                .thenReturn(buildSTRDaily());
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertOccupancyDetails(strDetails.getTotalOccupancy());
        assertADRDdetails(strDetails.getADR());
    }

    @Test
    void shouldGetSTRDetailsWhenPropertyAvailableIsNull() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2024, 8, 22));
        STRDaily strDaily = buildSTRDaily();
        strDaily.setPropertyAvailable(null);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyBy(DateUtil.convertJavaUtilDateToLocalDate(calculateDateForLastYear(arrivalDate, true)))).thenReturn(strDaily);
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertNull(strDetails.getTotalOccupancy().getLastYearHotel());
    }

    @Test
    void shouldGetSTRDetailsWhenPropertyAvailableIsZero() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2024, 8, 22));
        STRDaily strDaily = buildSTRDaily();
        strDaily.setPropertyAvailable(0);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(strService.getSTRDailyBy(DateUtil.convertJavaUtilDateToLocalDate(calculateDateForLastYear(arrivalDate, true))))
                .thenReturn(strDaily);
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertNull(strDetails.getTotalOccupancy().getLastYearHotel());
    }

    @Test
    void shouldGetNullSTRDetailsWhenDataForArrivalDateNotFound() {
        Date arrivalDate = JavaLocalDateUtils.toDate(LocalDate.of(2024, 8, 22));
        when(strService.getSTRDailyBy(DateUtil.convertJavaUtilDateToLocalDate(calculateDateForLastYear(arrivalDate, true))))
                .thenReturn(null);
        when(strService.getMaximumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2024,8,15));
        when(strService.getMinimumOccupancyDateInSTRDaily()).thenReturn(LocalDate.of(2022,9,2));
        when(dashboardService.getMetrics2(anyList(),eq(arrivalDate),eq(arrivalDate))).thenReturn(getEmptyMetricResponse());

        STRDetails strDetails = revenueManagementService.getStrDetails(arrivalDate);

        assertNotNull(strDetails);
        assertNotNull(strDetails.getTotalOccupancy());
        assertNotNull(strDetails.getADR());
        assertNull(strDetails.getTotalOccupancy().getLastYearHotel());
        assertNull(strDetails.getTotalOccupancy().getLastYearCompetitor());
        assertNull(strDetails.getTotalOccupancy().getThisYearHotelForecast());
        assertNull(strDetails.getTotalOccupancy().getForecastVariance());
        assertNull(strDetails.getADR().getLastYearHotel());
        assertNull(strDetails.getADR().getLastYearMarket());
        assertNull(strDetails.getADR().getThisYearHotelForecast());
        assertNull(strDetails.getADR().getForecastVariance());
    }

    @Test
    public void shouldGetProperties(){
        AuthorizedUserVO authorizedUser = new AuthorizedUserVO();
        //Authorized Properties
        Map<Integer, PropertyVO> authorizedProperties = new HashMap<>();
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        com.ideas.tetris.platform.services.daoandentities.entity.Property h2Property = getPropertyBy(6, "H2", "Hilton Baner", "12346");
        authorizedProperties.put(5, new PropertyVO(h1Property, "2024-12-07", 100, HIGH_OCC_CUT_OFF, LOW_OCC_CUT_OFF, true));
        authorizedProperties.put(6, new PropertyVO(h2Property));
        authorizedUser.setAssociatedPropertiesObjectMap(authorizedProperties);
        //Module wise Permissions
        Map<Integer, Set<ModulePermissionVO>> propertiesModulesPermissionsMap = new HashMap<>();
        Set<ModulePermissionVO> modulePermissions = new HashSet<>();
        modulePermissions.add(new ModulePermissionVO("GroupPricingEvaluation", "WRITE"));
        propertiesModulesPermissionsMap.put(5, modulePermissions);
        authorizedUser.setPropertiesModulesPermissionsMap(propertiesModulesPermissionsMap);
        com.ideas.tetris.platform.services.daoandentities.entity.Client client = new com.ideas.tetris.platform.services.daoandentities.entity.Client();
        client.setId(2);
        client.setCode("BSTN");
        when(loginRestService.getAuthorizedPropertiesWithPermissions(true, client)).thenReturn(authorizedUser);
        when(globalCrudService.findByNamedQuery(com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_IDS,
                QueryParameter.with("propertyIdList", Set.of(5,6)).parameters())).thenReturn(Arrays.asList(h1Property, h2Property));
        String clientUUId = "BSTN_1234";
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Client.BY_UPS_CLIENT_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Client.CLIENT_UPS_ID_CONSTANT, clientUUId).parameters())).thenReturn(client);
        doReturn("BSTN").when(revenueManagementService).getClientCode();
        doReturn(false).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), "BSTN", "H1");
        //WHEN
        List<Property> properties = revenueManagementService.getProperties(clientUUId);
        //THEN
        assertEquals("H1", properties.get(0).getCode());
        assertEquals("Hilton Pune", properties.get(0).getName());
        assertEquals("12345", properties.get(0).getUpsId());
        assertEquals("GroupPricingEvaluation", properties.get(0).getModulePermission().getModuleName());
        assertEquals("WRITE", properties.get(0).getModulePermission().getModulePermission());

        assertEquals("H2", properties.get(1).getCode());
        assertEquals("Hilton Baner", properties.get(1).getName());
        assertEquals("12346", properties.get(1).getUpsId());
        assertNull(properties.get(1).getModulePermission());
    }

    @Test
    public void shouldGetPropertiesWhenFunctionSpaceEnabledWithGuestRoomOnly(){
        AuthorizedUserVO authorizedUser = new AuthorizedUserVO();
        //Authorized Properties
        Map<Integer, PropertyVO> authorizedProperties = new HashMap<>();
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        com.ideas.tetris.platform.services.daoandentities.entity.Property h2Property = getPropertyBy(6, "H2", "Hilton Baner", "12346");
        authorizedProperties.put(5, new PropertyVO(h1Property, "2024-12-07", 100, HIGH_OCC_CUT_OFF, LOW_OCC_CUT_OFF, true));
        authorizedProperties.put(6, new PropertyVO(h2Property));
        authorizedUser.setAssociatedPropertiesObjectMap(authorizedProperties);
        //Module wise Permissions
        Map<Integer, Set<ModulePermissionVO>> propertiesModulesPermissionsMap = new HashMap<>();
        Set<ModulePermissionVO> modulePermissions = new HashSet<>();
        modulePermissions.add(new ModulePermissionVO("GroupPricingEvaluation", "NO_ACCESS"));
        modulePermissions.add(new ModulePermissionVO("Evaluations", "WRITE"));
        propertiesModulesPermissionsMap.put(5, modulePermissions);
        authorizedUser.setPropertiesModulesPermissionsMap(propertiesModulesPermissionsMap);
        com.ideas.tetris.platform.services.daoandentities.entity.Client client = new com.ideas.tetris.platform.services.daoandentities.entity.Client();
        client.setId(2);
        client.setCode("BSTN");
        when(loginRestService.getAuthorizedPropertiesWithPermissions(true, client)).thenReturn(authorizedUser);
        when(globalCrudService.findByNamedQuery(com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_IDS,
                QueryParameter.with("propertyIdList", Set.of(5,6)).parameters())).thenReturn(Arrays.asList(h1Property, h2Property));
        String clientUUId = "BSTN_1234";
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Client.BY_UPS_CLIENT_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Client.CLIENT_UPS_ID_CONSTANT, clientUUId).parameters())).thenReturn(client);
        doReturn("BSTN").when(revenueManagementService).getClientCode();
        doReturn(true).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), "Hilton", "H1");
        doReturn(true).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value(), "Hilton", "H1");
        //WHEN
        List<Property> properties = revenueManagementService.getProperties(clientUUId);
        //THEN
        assertEquals("H1", properties.get(0).getCode());
        assertEquals("Hilton Pune", properties.get(0).getName());
        assertEquals("12345", properties.get(0).getUpsId());
        assertEquals("GroupPricingEvaluation", properties.get(0).getModulePermission().getModuleName());
        assertEquals("WRITE", properties.get(0).getModulePermission().getModulePermission());

        assertEquals("H2", properties.get(1).getCode());
        assertEquals("Hilton Baner", properties.get(1).getName());
        assertEquals("12346", properties.get(1).getUpsId());
        assertNull(properties.get(1).getModulePermission());
    }

    @Test
    public void shouldGetGroupPricingPermissionByProperty(){
        //GIVEN
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_UNIFIED_PROPERTY_ID, "12345").parameters()))
                .thenReturn(h1Property);
        Map<Integer, Set<ModulePermissionVO>> groupPricingPermissionForProperty = new HashMap<>();
        groupPricingPermissionForProperty.put(5, Set.of(new ModulePermissionVO("GroupPricingEvaluation", "WRITE")));
        doReturn(groupPricingPermissionForProperty).when(loginRestService).getGroupPricingPermissionForProperty(h1Property);
        doReturn("BSTN").when(revenueManagementService).getClientCode();
        doReturn(false).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), "BSTN", "H1");
        //WHEN
        Property groupPricingPermissionByProperty = revenueManagementService.getGroupPricingPermissionByProperty("12345");
        //THEN
        assertEquals("H1", groupPricingPermissionByProperty.getCode());
        assertEquals("Hilton Pune", groupPricingPermissionByProperty.getName());
        assertEquals("12345", groupPricingPermissionByProperty.getUpsId());
        assertEquals("GroupPricingEvaluation", groupPricingPermissionByProperty.getModulePermission().getModuleName());
        assertEquals("WRITE", groupPricingPermissionByProperty.getModulePermission().getModulePermission());
    }

    @Test
    public void shouldGetGroupPricingPermissionByPropertyWhenFunctionSpaceEnabledWithGuestRoomOnly(){
        //GIVEN
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_UNIFIED_PROPERTY_ID, "12345").parameters()))
                .thenReturn(h1Property);
        Map<Integer, Set<ModulePermissionVO>> groupPricingPermissionForProperty = new HashMap<>();
        groupPricingPermissionForProperty.put(5, Set.of(new ModulePermissionVO("GroupPricingEvaluation", "NO_ACCESS"), new ModulePermissionVO("Evaluations", "WRITE")));
        doReturn(groupPricingPermissionForProperty).when(loginRestService).getGroupPricingPermissionForProperty(h1Property);
        doReturn("BSTN").when(revenueManagementService).getClientCode();
        doReturn(true).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), "Hilton", "H1");
        doReturn(true).when(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value(), "Hilton", "H1");
        //WHEN
        Property groupPricingPermissionByProperty = revenueManagementService.getGroupPricingPermissionByProperty("12345");
        //THEN
        assertEquals("H1", groupPricingPermissionByProperty.getCode());
        assertEquals("Hilton Pune", groupPricingPermissionByProperty.getName());
        assertEquals("12345", groupPricingPermissionByProperty.getUpsId());
        assertEquals("GroupPricingEvaluation", groupPricingPermissionByProperty.getModulePermission().getModuleName());
        assertEquals("WRITE", groupPricingPermissionByProperty.getModulePermission().getModulePermission());
    }

    @Test
    public void shouldGetGroupPricingPermissionByPropertyForNoAccess(){
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_UNIFIED_PROPERTY_ID, "12345").parameters()))
                .thenReturn(h1Property);
        Map<Integer, Set<ModulePermissionVO>> groupPricingPermissionForProperty = new HashMap<>();
        groupPricingPermissionForProperty.put(5, null);
        doReturn(groupPricingPermissionForProperty).when(loginRestService).getGroupPricingPermissionForProperty(h1Property);
        Property groupPricingPermissionByProperty = revenueManagementService.getGroupPricingPermissionByProperty("12345");
        assertEquals("H1", groupPricingPermissionByProperty.getCode());
        assertEquals("Hilton Pune", groupPricingPermissionByProperty.getName());
        assertEquals("12345", groupPricingPermissionByProperty.getUpsId());
        assertEquals("GroupPricingEvaluation", groupPricingPermissionByProperty.getModulePermission().getModuleName());
        assertEquals("NO_ACCESS", groupPricingPermissionByProperty.getModulePermission().getModulePermission());

    }

    @Test
    public void shouldGetMeetingPackagePricingPermissionByProperty(){
        //GIVEN
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_UNIFIED_PROPERTY_ID, "12345").parameters()))
                .thenReturn(h1Property);
        Map<Integer, Set<ModulePermissionVO>> meetingPackagePricingPermissionForProperty = new HashMap<>();
        meetingPackagePricingPermissionForProperty.put(5, Set.of(new ModulePermissionVO("MeetingPackagePricing", "WRITE")));
        doReturn(meetingPackagePricingPermissionForProperty).when(loginRestService).getMeetingPackagePricingPermissionForProperty(h1Property);
        //WHEN
        Set<ModulePermissionVO> meetingPackagePricingPermissionByProperty = revenueManagementService.getMeetingPackagePricingPermissionByProperty("12345");
        //THEN
        ModulePermissionVO modulePermissionVO = meetingPackagePricingPermissionByProperty.stream().filter(mpp -> mpp.getModuleName().equals("MeetingPackagePricing")).findFirst().get();
        assertEquals("MeetingPackagePricing", modulePermissionVO.getModuleName());
        assertEquals("WRITE", modulePermissionVO.getModulePermission());
    }

    @Test
    public void shouldGetMeetingPackagePricingPermissionByPropertyForNoAccess(){
        //GIVEN
        com.ideas.tetris.platform.services.daoandentities.entity.Property h1Property = getPropertyBy(5, "H1", "Hilton Pune", "12345");
        when(globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_UNIFIED_PROPERTY_ID, "12345").parameters()))
                .thenReturn(h1Property);
        Map<Integer, Set<ModulePermissionVO>> meetingPackagePricingPermissionForProperty = new HashMap<>();
        meetingPackagePricingPermissionForProperty.put(5, null);
        doReturn(meetingPackagePricingPermissionForProperty).when(loginRestService).getMeetingPackagePricingPermissionForProperty(h1Property);
        //WHEN
        Set<ModulePermissionVO> meetingPackagePricingPermissionByProperty = revenueManagementService.getMeetingPackagePricingPermissionByProperty("12345");
        //THEN
        ModulePermissionVO modulePermissionVO = meetingPackagePricingPermissionByProperty.stream().filter(mpp -> mpp.getModuleName().equals("MeetingPackagePricing")).findFirst().get();
        assertEquals("MeetingPackagePricing", modulePermissionVO.getModuleName());
        assertEquals("NO_ACCESS", modulePermissionVO.getModulePermission());
    }

    @Test
    void verifyPerformManualBarUpload(){
        Response expectedResponse = new Response("Success");
        when(overrideService.performManualBarUpload()).thenReturn(expectedResponse);

        Response response = revenueManagementService.performManualBarUpload();

        assertEquals(expectedResponse, response);
    }

    @Test
    void verifySyncAll(){
        revenueManagementService.syncAll();

        verify(syncEventAggregatorService).syncAll();

    }

    @Test
    void verifySyncAllAndUploadDecisions(){
        revenueManagementService.syncAllAndUploadDecisions();

        verify(syncEventAggregatorService).syncAllAndUploadDecisions();
    }

    @Test
    void verifyGetPropertyStateInformation(){
        PropertyState aexpectedPropertyState = new PropertyState();
        when(syncEventAggregatorService.getPropertyStateInformation()).thenReturn(aexpectedPropertyState);

        PropertyState propertyStateInformation = revenueManagementService.getPropertyStateInformation();

        assertEquals(aexpectedPropertyState, propertyStateInformation);
    }


    private static com.ideas.tetris.platform.services.daoandentities.entity.Property getPropertyBy(int id, String code, String name, String upsId) {
        com.ideas.tetris.platform.services.daoandentities.entity.Property property = new com.ideas.tetris.platform.services.daoandentities.entity.Property();
        property.setId(id);
        property.setCode(code);
        property.setName(name);
        property.setUpsId(upsId);
        com.ideas.tetris.platform.services.daoandentities.entity.Client client = new com.ideas.tetris.platform.services.daoandentities.entity.Client();
        client.setCode("Hilton");
        client.setUpsClientUuid("999888");
        property.setClient(client);
        return property;
    }

    private void assertADRDdetails(ADRDetails adr) {
        assertEquals(BigDecimal.valueOf(165.38462).setScale(SCALE_5,RoundingMode.HALF_UP),adr.getLastYearHotel());
        assertEquals(BigDecimal.valueOf(163.17073).setScale(SCALE_5,RoundingMode.HALF_UP),adr.getLastYearMarket());
        assertEquals(BigDecimal.valueOf(12.25).setScale(SCALE_5,RoundingMode.HALF_UP),adr.getThisYearHotelForecast());
        assertEquals(BigDecimal.valueOf(-153.13462).setScale(SCALE_5,RoundingMode.HALF_UP),adr.getForecastVariance());
    }

    private void assertOccupancyDetails(STROccupancyDetails totalOccupancy) {
        assertEquals(BigDecimal.valueOf(91).setScale(SCALE_5, RoundingMode.HALF_UP),totalOccupancy.getLastYearHotel());
        assertEquals(BigDecimal.valueOf(98.4).setScale(SCALE_5,RoundingMode.HALF_UP),totalOccupancy.getLastYearCompetitor());
        assertEquals(BigDecimal.valueOf(158.96).setScale(SCALE_5,RoundingMode.HALF_UP),totalOccupancy.getThisYearHotelForecast());
        assertEquals(BigDecimal.valueOf(67.96).setScale(SCALE_5,RoundingMode.HALF_UP),totalOccupancy.getForecastVariance());
    }

    private STRDaily buildSTRDaily() {
        STRDaily strDaily = new STRDaily();
        strDaily.setPropertyAvailable(200);
        strDaily.setPropertySold(182);
        strDaily.setPropertyRevenue(BigDecimal.valueOf(30100.00));
        strDaily.setCompetitiveSetAvailable(250);
        strDaily.setCompetitiveSetSold(246);
        strDaily.setCompetitiveSetRevenue(BigDecimal.valueOf(40140.00));
        return strDaily;
    }

    private static void assertOccupancyDetailsFor(Date expectedDate, BigDecimal expectedOccupancyPercentage, int expectedPhysicalCapacity, int expectedAvailableRoomsToSell, BigDecimal expectedFinalPrice, OccupancyDetails occupancyDetails) {
        assertEquals(expectedDate, occupancyDetails.getDate());
        assertEquals(expectedOccupancyPercentage, occupancyDetails.getOccupancyPercentage());
        assertEquals(expectedPhysicalCapacity, occupancyDetails.getPhysicalCapacity());
        assertEquals(expectedAvailableRoomsToSell, occupancyDetails.getAvailableRoomsToSell());
        assertEquals(expectedFinalPrice, occupancyDetails.getFinalBar());
    }

    private static void assertOccupancyDetailsFor(Date expectedDate, BigDecimal expectedOccupancyPercentage, OccupancyDetails occupancyDetails) {
        assertEquals(expectedDate, occupancyDetails.getDate());
        assertEquals(expectedOccupancyPercentage, occupancyDetails.getOccupancyPercentage());
    }

    private static void assertSpecialEvent(String expectedSpecialEventName,
                                           Date expectedStartDate, Date expectedEndDate,
                                           SpecialEvent specialEvent) {
        assertEquals(expectedSpecialEventName, specialEvent.getEventName());
        assertEquals(expectedStartDate, specialEvent.getStartDate());
        assertEquals(expectedEndDate, specialEvent.getEndDate());
    }

    private void assertHeatMapConfigurations(HeatMapConfigurations heatMapConfigurations) {
        assertEquals(8, heatMapConfigurations.getDefaults().size());
        assertEquals(8, heatMapConfigurations.getSeasons().size());
        assertAllDefaultConfigurations(heatMapConfigurations.getDefaults());
        assertAllSeasonsConfigurations(heatMapConfigurations.getSeasons());
    }

    private void assertAllSeasonsConfigurations(List<HeatMapConfiguration> seasons) {
        assertSeasonalConfiguration(seasons.get(0), 0, 30, "2A44FD", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(1), 31, 45, "3794FE", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(2), 46, 48, "00FFFF", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(3), 49, 60, "70FFA9", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(4), 61, 70, "A7FF68", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(SCALE_5), 71, 80, "E4FF33", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(6), 81, 90, "F89B00", "2017-10-01", "2017-11-30");
        assertSeasonalConfiguration(seasons.get(7), 91, 100, "FF0000", "2017-10-01", "2017-11-30");
    }

    private void assertAllDefaultConfigurations(List<HeatMapConfiguration> defaults) {
        assertDefaultConfiguration(defaults.get(0), 0, 30, "2A44FD");
        assertDefaultConfiguration(defaults.get(1), 31, 40, "3794FE");
        assertDefaultConfiguration(defaults.get(2), 41, 50, "00FFFF");
        assertDefaultConfiguration(defaults.get(3), 51, 60, "70FFA9");
        assertDefaultConfiguration(defaults.get(4), 61, 70, "FF0099");
        assertDefaultConfiguration(defaults.get(SCALE_5), 71, 80, "E4FF33");
        assertDefaultConfiguration(defaults.get(6), 81, 90, "F89B00");
        assertDefaultConfiguration(defaults.get(7), 91, 100, "FF0000");
    }

    private void assertDefaultConfigurationsFromCustomized(List<HeatMapConfiguration> defaults) {
        assertDefaultConfiguration(defaults.get(0), 0, 30, "70FFA9");
        assertDefaultConfiguration(defaults.get(1), 31, 35, "3794FE");
        assertDefaultConfiguration(defaults.get(2), 36, 38, "00FFFF");
        assertDefaultConfiguration(defaults.get(3), 39, 58, "70FFA9");
        assertDefaultConfiguration(defaults.get(4), 59, 70, "E4FF33");
        assertDefaultConfiguration(defaults.get(SCALE_5), 71, 80, "FF0099");
        assertDefaultConfiguration(defaults.get(6), 81, 90, "F89B00");
        assertDefaultConfiguration(defaults.get(7), 91, 100, "FF0000");
    }

    private void assertDefaultConfiguration(HeatMapConfiguration config, int expectedFrom, int expectedTo, String expectedColor) {
        assertEquals(expectedFrom, config.getRangeFrom());
        assertEquals(expectedTo, config.getRangeTo());
        assertEquals(expectedColor, config.getColorCode());
    }

    private void assertSeasonalConfiguration(HeatMapConfiguration config, int expectedFrom, int expectedTo, String expectedColor, String expectedStartDate, String expectedEndDate) {
        assertEquals(expectedStartDate, config.getStartDate());
        assertEquals(expectedEndDate, config.getEndDate());
        assertEquals(expectedFrom, config.getRangeFrom());
        assertEquals(expectedTo, config.getRangeTo());
        assertEquals(expectedColor, config.getColorCode());
    }

    private static OccupancyDateCapacity createCapacity(LocalDate date, int availableCapacity, int physicalCapacity) {
        OccupancyDateCapacity capacity = new OccupancyDateCapacity();
        capacity.setOccupancyDate(LocalDateUtils.toJodaLocalDate(date));
        capacity.setAvailableCapacity(availableCapacity);
        capacity.setPhysicalCapacity(physicalCapacity);
        return capacity;
    }

    private ConfigParameter getConfigParameter(String type) {
        ConfigParameter parameter = new ConfigParameter();
        parameter.setId(1);
        parameter.setName("pacman.feature.ParameterName");
        ParameterType parameterType = new ParameterType();
        parameterType.setType(type);
        parameter.setParameterType(parameterType);
        return parameter;
    }

    private com.ideas.tetris.platform.services.daoandentities.entity.Client createClient() {
        com.ideas.tetris.platform.services.daoandentities.entity.Client client = new com.ideas.tetris.platform.services.daoandentities.entity.Client();
        client.setId(1);
        client.setCode("BSTN");
        client.setName("BlackStone");
        client.setUpsClientUuid("UUIDForClient12345");
        return client;
    }

    private List<HeatMapRangeAndColorsConfig> createHeatMapRangeAndColorsConfigWithDefaultAndSeasons() {
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = new HeatMapRangeAndColorsConfig();
        heatMapRangeAndColorsConfig.setId(1);
        HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType = new HeatMapRangeAndColorsConfigType();
        heatMapRangeAndColorsConfigType.setConfigType("DEFAULT");
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColorsConfigType(heatMapRangeAndColorsConfigType);
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColors(createDefaultRangeAndColors());

        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfigSeasons = new HeatMapRangeAndColorsConfig();
        heatMapRangeAndColorsConfigSeasons.setId(2);
        heatMapRangeAndColorsConfigSeasons.setStartDate(String.valueOf(toDate("2017-10-01")));
        heatMapRangeAndColorsConfigSeasons.setEndDate(String.valueOf(toDate("2017-11-30")));
        HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType2 = new HeatMapRangeAndColorsConfigType();
        heatMapRangeAndColorsConfigType2.setConfigType("SEASONAL");
        heatMapRangeAndColorsConfigSeasons.setHeatMapRangeAndColorsConfigType(heatMapRangeAndColorsConfigType2);
        heatMapRangeAndColorsConfigSeasons.setHeatMapRangeAndColors(createSeasonsRangeAndColors());
        return List.of(heatMapRangeAndColorsConfig, heatMapRangeAndColorsConfigSeasons);
    }

    private HeatMapRangeAndColorsConfig createCustomizedRangeAndColorsConfiguration() {
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = new HeatMapRangeAndColorsConfig();
        heatMapRangeAndColorsConfig.setId(3);
        HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType = new HeatMapRangeAndColorsConfigType();
        heatMapRangeAndColorsConfigType.setConfigType("CUSTOMIZE");
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColorsConfigType(heatMapRangeAndColorsConfigType);
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColors(createCustomizedRangeAndColors());
        return heatMapRangeAndColorsConfig;
    }

    private Set<HeatMapRangeAndColors> createDefaultRangeAndColors() {
        return Set.of(
                createHeatMapRangeAndColors(1, 0, 30, "2A44FD"),
                createHeatMapRangeAndColors(1, 31, 40, "3794FE"),
                createHeatMapRangeAndColors(1, 41, 50, "00FFFF"),
                createHeatMapRangeAndColors(1, 51, 60, "70FFA9"),
                createHeatMapRangeAndColors(1, 61, 70, "FF0099"),
                createHeatMapRangeAndColors(1, 71, 80, "E4FF33"),
                createHeatMapRangeAndColors(1, 81, 90, "F89B00"),
                createHeatMapRangeAndColors(1, 91, 100, "FF0000")
        );
    }

    private Set<HeatMapRangeAndColors> createSeasonsRangeAndColors() {
        return Set.of(
                createHeatMapRangeAndColors(2, 0, 30, "2A44FD"),
                createHeatMapRangeAndColors(2, 31, 45, "3794FE"),
                createHeatMapRangeAndColors(2, 46, 48, "00FFFF"),
                createHeatMapRangeAndColors(2, 49, 60, "70FFA9"),
                createHeatMapRangeAndColors(2, 61, 70, "A7FF68"),
                createHeatMapRangeAndColors(2, 71, 80, "E4FF33"),
                createHeatMapRangeAndColors(2, 81, 90, "F89B00"),
                createHeatMapRangeAndColors(2, 91, 100, "FF0000")
        );
    }

    private Set<HeatMapRangeAndColors> createCustomizedRangeAndColors() {
        return Set.of(
                createHeatMapRangeAndColors(3, 0, 30, "70FFA9"),
                createHeatMapRangeAndColors(3, 31, 35, "3794FE"),
                createHeatMapRangeAndColors(3, 36, 38, "00FFFF"),
                createHeatMapRangeAndColors(3, 39, 58, "70FFA9"),
                createHeatMapRangeAndColors(3, 59, 70, "E4FF33"),
                createHeatMapRangeAndColors(3, 71, 80, "FF0099"),
                createHeatMapRangeAndColors(3, 81, 90, "F89B00"),
                createHeatMapRangeAndColors(3, 91, 100, "FF0000")
        );
    }

    private HeatMapRangeAndColors createHeatMapRangeAndColors(int configId, int from, int to, String colorCode) {
        HeatMapRangeAndColors heatMapRangeAndColors = new HeatMapRangeAndColors();
        heatMapRangeAndColors.setHeatMapRangeAndColorsConfigId(configId);
        heatMapRangeAndColors.setRangeFrom(from);
        heatMapRangeAndColors.setRangeTo(to);
        heatMapRangeAndColors.setOccupancyForecastColorCode(colorCode);
        return heatMapRangeAndColors;
    }

    private DashboardMetric2 getMetricResponse() {
        DashboardMetric2 response = new DashboardMetric2();
        MetricResponse response1 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType1 = new HashMap<>();
        metricValueByGroupByType1.put(GroupByType.PROPERTY.name(),List.of(BigDecimal.valueOf(OCC_FCST_PERCENT)));
        response1.setMetricValuesByGroupByType(metricValueByGroupByType1);
        MetricResponse response2 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType2 = new HashMap<>();
        metricValueByGroupByType2.put(GroupByType.PROPERTY.name(),List.of(BigDecimal.valueOf(ADR_FCST)));
        response2.setMetricValuesByGroupByType(metricValueByGroupByType2);
        response.setMetricResponses(List.of(response1,response2));
        return response;
    }

    private DashboardMetric2 buildMetricResponseForDateRange() {
        DashboardMetric2 response = new DashboardMetric2();
        MetricResponse response1 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType1 = new HashMap<>();
        BigDecimal occForecastPercent = BigDecimal.valueOf(OCC_FCST_PERCENT);
        metricValueByGroupByType1.put(GroupByType.PROPERTY.name(),List.of(occForecastPercent, occForecastPercent, occForecastPercent));
        response1.setMetricValuesByGroupByType(metricValueByGroupByType1);
        MetricResponse response2 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType2 = new HashMap<>();
        BigDecimal adrForecast = BigDecimal.valueOf(ADR_FCST);
        metricValueByGroupByType2.put(GroupByType.PROPERTY.name(),List.of(adrForecast, adrForecast, adrForecast));
        response2.setMetricValuesByGroupByType(metricValueByGroupByType2);
        response.setMetricResponses(List.of(response1,response2));
        return response;
    }

    private DashboardMetric2 getEmptyMetricResponse() {
        DashboardMetric2 response = new DashboardMetric2();
        MetricResponse response1 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType1 = new HashMap<>();
        metricValueByGroupByType1.put(GroupByType.PROPERTY.name(),List.of(BigDecimal.valueOf(Integer.MIN_VALUE)));
        response1.setMetricValuesByGroupByType(metricValueByGroupByType1);
        MetricResponse response2 = new MetricResponse();
        Map<String, List<?>> metricValueByGroupByType2 = new HashMap<>();
        metricValueByGroupByType2.put(GroupByType.PROPERTY.name(),List.of(BigDecimal.valueOf(Integer.MIN_VALUE)));
        response2.setMetricValuesByGroupByType(metricValueByGroupByType2);
        response.setMetricResponses(List.of(response1,response2));
        return response;
    }

    @Test
    public void shouldBeAbleToGetPropertiesByPagination(){
        List<Integer> propertyIds = Arrays.asList(5,6,9,10,11);
        revenueManagementService.getPropertiesByPagination(propertyIds, 2);
        verify(globalCrudService).findByNamedQuery(com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_IDS,
                QueryParameter.with("propertyIdList", Arrays.asList(5,6)).parameters());
        verify(globalCrudService).findByNamedQuery(com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_IDS,
                QueryParameter.with("propertyIdList", Arrays.asList(9,10)).parameters());
        verify(globalCrudService).findByNamedQuery(com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_IDS,
                QueryParameter.with("propertyIdList", Arrays.asList(11)).parameters());
    }

    @Test
    public void shouldCallForPaginationWhenPropertyIdsMoreThan2100(){
        Set<Integer> properties = IntStream.range(1, 3500)
                .boxed()
                .collect(Collectors.toSet());
        revenueManagementService.getGlobalPropertyByIdMap(properties);
        verify(revenueManagementService).getPropertiesByPagination(eq(new ArrayList<>(properties)), eq(Constants.PAGE_SIZE));
    }

    @Test
    public void shouldCallForPaginationWhenPropertyIdsEqualTo2100(){
        Set<Integer> properties = IntStream.range(1, 2101)
                .boxed()
                .collect(Collectors.toSet());
        revenueManagementService.getGlobalPropertyByIdMap(properties);
        verify(revenueManagementService).getPropertiesByPagination(eq(new ArrayList<>(properties)), eq(Constants.PAGE_SIZE));
    }

    @Test
    public void shouldNotCallForPaginationWhenPropertyIdsLessThan2100(){
        Set<Integer> properties = IntStream.range(1, 2099)
                .boxed()
                .collect(Collectors.toSet());
        revenueManagementService.getGlobalPropertyByIdMap(properties);
        verify(revenueManagementService).getPropertiesByIds(eq(properties));
    }

    @Test
    void shouldReturnEmptyListWhenPropertyIdsLisIsEmpty() {
        Map<Integer, com.ideas.tetris.platform.services.daoandentities.entity.Property> result =
                revenueManagementService.getGlobalPropertyByIdMap(emptySet());
        assertTrue(isEmpty(result));
        verify(globalCrudService, never()).findByNamedQuery(anyString(), anyMap());
    }
}