package com.ideas.tetris.pacman.services.functionspace.dataload.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.WorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBooking;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingSubBlockCode;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceMarketSegment;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceStatus;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.POPULATE_FS_BOOKING_BLOCK_CODE_USING_SUB_BLOCK_CODES;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

class FunctionSpaceSubBlockCodeServiceTest extends AbstractG3JupiterTest {

    private static final String BLOCK_CODE = "BlockCode";

    private FunctionSpaceSubBlockCodeService functionSpaceSubBlockCodeService;
    private CrudService tenantCrudService;

    @BeforeEach
    public void setup() {
        tenantCrudService = tenantCrudService();
        functionSpaceSubBlockCodeService = new FunctionSpaceSubBlockCodeService();
        functionSpaceSubBlockCodeService.setTenantCrudService(tenantCrudService);
    }

    @Test
    public void shouldInsertSubBlockCodesWhenNewBookingIsReceived() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        functionSpaceBooking.setId(null);
        List<String> subBlockCodes = List.of("BlockCode1", "BlockCode2");

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, subBlockCodes);

        List<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodes = sortFSBookingSubBlockCodesBySubBlockCode(functionSpaceBooking);
        assertEquals(2, functionSpaceBookingSubBlockCodes.size());
        assertEquals("BlockCode1", functionSpaceBookingSubBlockCodes.get(0).getSubBlockCode());
        assertEquals("BlockCode2", functionSpaceBookingSubBlockCodes.get(1).getSubBlockCode());
    }

    @Test
    public void shouldNotInsertNullSubBlockCodesWhenNewBookingIsReceived() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        List<String> subBlockCodes = new ArrayList<>();
        subBlockCodes.add("BlockCode1");
        subBlockCodes.add("BlockCode2");
        subBlockCodes.add(null);

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, subBlockCodes);

        List<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodes = sortFSBookingSubBlockCodesBySubBlockCode(functionSpaceBooking);
        assertEquals(2, functionSpaceBookingSubBlockCodes.size());
        assertEquals("BlockCode1", functionSpaceBookingSubBlockCodes.get(0).getSubBlockCode());
        assertEquals("BlockCode2", functionSpaceBookingSubBlockCodes.get(1).getSubBlockCode());
    }

    @Test
    public void shouldInsertNewSubBlockCodesWhenBookingUpdateIsReceived() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode1 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode1");
        fsBookingSubBlockCode1.setId(1);
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode2 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode2");
        fsBookingSubBlockCode2.setId(2);
        Set<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodeSet = new HashSet<>();
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode1);
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode2);
        functionSpaceBooking.setFunctionSpaceBookingSubBlockCodeSet(functionSpaceBookingSubBlockCodeSet);
        List<String> subBlockCodes = List.of("BlockCode1", "BlockCode2", "BlockCode3");

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, subBlockCodes);

        List<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodes = sortFSBookingSubBlockCodesBySubBlockCode(functionSpaceBooking);
        assertEquals(3, functionSpaceBookingSubBlockCodes.size());
        assertEquals("BlockCode1", functionSpaceBookingSubBlockCodes.get(0).getSubBlockCode());
        assertEquals(1, functionSpaceBookingSubBlockCodes.get(0).getId());
        assertEquals("BlockCode2", functionSpaceBookingSubBlockCodes.get(1).getSubBlockCode());
        assertEquals(2, functionSpaceBookingSubBlockCodes.get(1).getId());
        assertEquals("BlockCode3", functionSpaceBookingSubBlockCodes.get(2).getSubBlockCode());
        assertNull(functionSpaceBookingSubBlockCodes.get(2).getId());
    }

    @Test
    public void shouldDeleteSubBlockCodesWhenBookingUpdateHasLessSubBlockCodes() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode1 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode1");
        fsBookingSubBlockCode1.setId(1);
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode2 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode2");
        Set<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodeSet = new HashSet<>();
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode1);
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode2);
        functionSpaceBooking.setFunctionSpaceBookingSubBlockCodeSet(functionSpaceBookingSubBlockCodeSet);
        List<String> subBlockCodes = List.of("BlockCode1");

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, subBlockCodes);

        assertEquals(1, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().size());
        assertEquals("BlockCode1", functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().iterator().next().getSubBlockCode());
        assertEquals(1, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().iterator().next().getId());
    }

    @Test
    public void shouldDeleteSubBlockCodesWhenBookingUpdateHasNoSubBlockCodes() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode1 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode1");
        fsBookingSubBlockCode1.setId(1);
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode2 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode2");
        Set<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodeSet = new HashSet<>();
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode1);
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode2);
        functionSpaceBooking.setFunctionSpaceBookingSubBlockCodeSet(functionSpaceBookingSubBlockCodeSet);

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, null);

        assertEquals(0, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().size());
    }

    @Test
    public void shouldNotChangeExistingSubBlockSetFromBookingEntityWhenBookingUpdateIsReceived() {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking("Booking1");
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode1 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode1");
        fsBookingSubBlockCode1.setId(1);
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode2 = buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "BlockCode2");
        Set<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodeSet = new HashSet<>();
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode1);
        functionSpaceBookingSubBlockCodeSet.add(fsBookingSubBlockCode2);
        functionSpaceBooking.setFunctionSpaceBookingSubBlockCodeSet(functionSpaceBookingSubBlockCodeSet);
        List<String> subBlockCodes = List.of("BlockCode1");

        functionSpaceSubBlockCodeService.handleSubBlockCodes(functionSpaceBooking, subBlockCodes);

        assertEquals(functionSpaceBookingSubBlockCodeSet, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet());
        assertEquals(1, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().size());
        assertEquals("BlockCode1", functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().iterator().next().getSubBlockCode());
        assertEquals(1, functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet().iterator().next().getId());
    }

    @Test
    public void shouldUpdateBookingsWhenSingleSubBlockCodeMatchSingleGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B1", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM2");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B1", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals("GM1", functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldUpdateHistoricalSyncBookingsWhenSingleSubBlockCodeMatchSingleGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B1", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM2");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, true);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B1", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals("GM1", functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldUpdateBookingsWhenNewSingleSubBlockCodeMatchSingleGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B1", "GM3", functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM2");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B1", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals("GM1", functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldNotUpdateBookingsWhenOldBlockCodeMatchSubBlockCodeAndGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B1", "GM3", functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM3");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B1", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals("GM3", functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldUpdateBookingsWhenOldBlockCodeMatchSubBlockCodeButNotGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B1", "GM4", functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM4");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B1", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals("GM1", functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldUpdateBookingsWhenDifferentBookingsSubBlockCodeMatchSameGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking1 = createFunctionSpaceBooking("B2", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        FunctionSpaceBooking functionSpaceBooking2 = createFunctionSpaceBooking("B3", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking1, "GM3");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking1, "GM4");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking2, "GM3");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking2, "GM5");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking1);
        tenantCrudService.refresh(functionSpaceBooking2);
        assertEquals("B2", functionSpaceBooking1.getSalesCateringIdentifier());
        assertEquals("GM3", functionSpaceBooking1.getBlockCode());
        assertEquals("B3", functionSpaceBooking2.getSalesCateringIdentifier());
        assertEquals("GM3", functionSpaceBooking2.getBlockCode());
    }

    @Test
    public void shouldNotUpdateBookingsWhenMultipleSubBlockCodesMatchDifferentGroupCodes() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B4", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        functionSpaceBooking.setBlockCode(BLOCK_CODE);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM6");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM7");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B4", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals(BLOCK_CODE, functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldNotUpdateBookingsWhenItFallsOutsideMappingWindow() {
        Date currentDt = new Date();
        int fsBookingMappingWindow = SystemConfig.fsBookingSubBlockCodeMappingWindow() - 2;
        Date lastModifiedDt = DateUtil.addDaysToDate(currentDt, fsBookingMappingWindow);
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B4", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, lastModifiedDt);
        functionSpaceBooking.setBlockCode(BLOCK_CODE);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM2");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B4", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals(BLOCK_CODE, functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldNotUseGroupMasterRecordsWhenItFallsOutsideMappingWindow() {
        Date currentDt = new Date();
        int grpMasterMappingWindow = SystemConfig.grpMasterSubBlockCodeMappingWindow() - 2;
        Date lastUpdatedDTTM = DateUtil.addDaysToDate(currentDt, grpMasterMappingWindow);
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B4", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        functionSpaceBooking.setBlockCode(BLOCK_CODE);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM1");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM3");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(lastUpdatedDTTM, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B4", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals(BLOCK_CODE, functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldNotUpdateBookingsWhenSubBlockCodeMatchDifferentGroupMasterWithSameGroupCode() {
        Date currentDt = new Date();
        FunctionSpaceStatus functionSpaceStatus = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceStatus(WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceMarketSegment functionSpaceMarketSegment = tenantCrudService.save(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("MS", null, WorkContextHelper.getCurrent().getPropertyId()));
        FunctionSpaceBooking functionSpaceBooking = createFunctionSpaceBooking("B5", BLOCK_CODE, functionSpaceStatus, functionSpaceMarketSegment, currentDt);
        functionSpaceBooking.setBlockCode(BLOCK_CODE);
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM8");
        createFunctionSpaceBookingSubBlockCode(functionSpaceBooking, "GM8");
        populateGroupMasterDetails(currentDt);

        functionSpaceSubBlockCodeService.performSubBlockCodeMapping(currentDt, false);

        tenantCrudService.refresh(functionSpaceBooking);
        assertEquals("B5", functionSpaceBooking.getSalesCateringIdentifier());
        assertEquals(BLOCK_CODE, functionSpaceBooking.getBlockCode());
    }

    @Test
    public void shouldReturnTrueWhenPopulateFSBookingBlockCodeUsingSubBlockCodesToggleIsTrue() {
        PacmanConfigParamsService configParamsService = Mockito.mock(PacmanConfigParamsService.class);
        inject(functionSpaceSubBlockCodeService, "configParamsService", configParamsService);
        when(configParamsService.getBooleanParameterValue(POPULATE_FS_BOOKING_BLOCK_CODE_USING_SUB_BLOCK_CODES)).thenReturn(true);

        assertTrue(functionSpaceSubBlockCodeService.shouldPopulateFSBookingBlockCodeUsingSubBlockCodes());
    }

    private void populateGroupMasterDetails(Date businessDayEndDt) {
        List<MarketSegmentSummary> marketSegmentSummaries = tenantCrudService.findAll(MarketSegmentSummary.class);
        MarketSegmentSummary mktSegSummary1 = marketSegmentSummaries.get(0);
        MarketSegmentSummary mktSegSummary2 = marketSegmentSummaries.get(1);
        FileMetadata fileMetadata = tenantCrudService.findOne(FileMetadata.class);

        GroupBlockMaster groupMaster1 = createGroupMaster("GM1", mktSegSummary1);
        GroupBlockMaster groupMaster2 = createGroupMaster("GM3", mktSegSummary1);
        GroupBlockMaster groupMaster3 = createGroupMaster("GM6", mktSegSummary1);
        GroupBlockMaster groupMaster4 = createGroupMaster("GM7", mktSegSummary1);
        GroupBlockMaster groupMaster5 = createGroupMaster("GM8", mktSegSummary1);
        GroupBlockMaster groupMaster6 = createGroupMaster("GM8", mktSegSummary2);

        String insertQuery = createPaceGroupMasterInsertQuery(groupMaster1, fileMetadata, businessDayEndDt) +
                createPaceGroupMasterInsertQuery(groupMaster2, fileMetadata, businessDayEndDt) +
                createPaceGroupMasterInsertQuery(groupMaster3, fileMetadata, businessDayEndDt) +
                createPaceGroupMasterInsertQuery(groupMaster4, fileMetadata, businessDayEndDt) +
                createPaceGroupMasterInsertQuery(groupMaster5, fileMetadata, businessDayEndDt) +
                createPaceGroupMasterInsertQuery(groupMaster6, fileMetadata, businessDayEndDt);
        tenantCrudService.executeUpdateByNativeQuery(insertQuery);
    }

    private FunctionSpaceBooking createFunctionSpaceBooking(String salesAndCateringIdentifier, String blockCode, FunctionSpaceStatus functionSpaceStatus, FunctionSpaceMarketSegment functionSpaceMarketSegment, Date lastUpdatedDTTM) {
        FunctionSpaceBooking functionSpaceBooking = FunctionSpaceObjectMother.buildFunctionSpaceBooking(WorkContextHelper.getCurrent().getPropertyId(), null, salesAndCateringIdentifier);
        functionSpaceBooking.setFunctionSpaceStatus(functionSpaceStatus);
        functionSpaceBooking.setMarketSegment(functionSpaceMarketSegment);
        functionSpaceBooking.setBookingType(null);
        functionSpaceBooking.setBlockCode(blockCode);
        functionSpaceBooking.setLastUpdatedDate(lastUpdatedDTTM);
        return tenantCrudService.save(functionSpaceBooking);
    }

    private GroupBlockMaster createGroupMaster(String groupCode, MarketSegmentSummary mktSegSummary) {
        GroupBlockMaster groupMaster = new GroupBlockMaster();
        groupMaster.setPropertyId(WorkContextHelper.getCurrent().getPropertyId());
        groupMaster.setCode(groupCode);
        groupMaster.setName(groupCode);
        groupMaster.setDescription("TEST_GROUP_DESCRIPTION");
        groupMaster.setGroupTypeCode("TRANS");
        groupMaster.setGroupStatusCode("groupStatusCode");
        groupMaster.setMarketSegment(mktSegSummary);
        groupMaster.setStartDate(DateUtil.getCurrentDateWithoutTime());
        groupMaster.setEndDate(DateUtil.addYearsToDate(DateUtil.getCurrentDateWithoutTime(), 2));
        groupMaster.setBookingDate(DateUtil.getCurrentDateWithoutTime());
        return tenantCrudService.save(groupMaster);
    }

    private String createPaceGroupMasterInsertQuery(GroupBlockMaster groupMaster, FileMetadata fileMetadata, Date businessDayEndDt) {
        return ("insert into Pace_Group_Master values" +
                "(" + groupMaster.getId() + ",'" + DateUtil.formatDate(businessDayEndDt, "yyyy-MM-dd") + "','" + groupMaster.getCode() + "','" + groupMaster.getCode() + "','" + groupMaster.getCode() + "',NULL,NULL,'DEFINITE','TRANS'," + groupMaster.getMarketSegment().getId() + "," +
                "getdate(),getdate(),getdate(),'INDV',NULL,NULL,'Testcase',getdate(),0," + fileMetadata.getId() + ");");
    }

    private void createFunctionSpaceBookingSubBlockCode(FunctionSpaceBooking functionSpaceBooking, String subBlockCode) {
        tenantCrudService.save(buildFunctionSpaceBookingSubBlockCode(functionSpaceBooking, subBlockCode));
    }

    private FunctionSpaceBookingSubBlockCode buildFunctionSpaceBookingSubBlockCode(FunctionSpaceBooking functionSpaceBooking, String subBlockCode) {
        FunctionSpaceBookingSubBlockCode fsBookingSubBlockCode = new FunctionSpaceBookingSubBlockCode();
        fsBookingSubBlockCode.setFunctionSpaceBooking(functionSpaceBooking);
        fsBookingSubBlockCode.setSubBlockCode(subBlockCode);
        return fsBookingSubBlockCode;
    }

    private List<FunctionSpaceBookingSubBlockCode> sortFSBookingSubBlockCodesBySubBlockCode(FunctionSpaceBooking functionSpaceBooking) {
        List<FunctionSpaceBookingSubBlockCode> functionSpaceBookingSubBlockCodeSet = new ArrayList<>(functionSpaceBooking.getFunctionSpaceBookingSubBlockCodeSet());
        functionSpaceBookingSubBlockCodeSet.sort(Comparator.comparing(FunctionSpaceBookingSubBlockCode::getSubBlockCode));
        return functionSpaceBookingSubBlockCodeSet;
    }
}