package com.ideas.tetris.pacman.services.reportsquery.pickUpAndChange;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPPaceDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OptimalBarType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.cpmigration.CPPaceBarOutputQueryConstant.INSERT_MIGRATION_CP_PACE_BAR_OUTPUT_DIFFERENTIAL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Disabled("slow")
public class PickUpAndChangeHotelLevelDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private Date javaStartDate;
    private String dow;
    private final int propertyID = 6;
    private final int recordTypeId = 13;
    private final int processStatusId = 13;
    private int isRolling = 0;
    private final int comp1 = 5;
    private final int comp2 = 6;
    private final int comp3 = 7;
    private final int masterAccomClassId = 7;
    private final int webrate_accom_type_id1 = 4;
    private final int webrate_accom_type_id2 = 5;
    private final String startDateOfWebRate = LocalDate.now().toString();
    private final String endDateOfWebRate = LocalDate.now().plusDays(1).toString();
    private String comp1WebRateValue;
    private String comp2WebRateValue;
    private String comp3WebRateValue;
    private int accomClassID = 0;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        startDate = getLocalDate();
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();

        int firstDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven();
        int secondDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight();

        populateForecastPaceForMarketSegmentForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForMarketSegmentForSecondPacePoint(insertQuery, secondDecisionId);
        populateForecastPaceForAccomTypeForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForAccomTypeForSecondPacePoint(insertQuery, secondDecisionId);
        populateActivityPaceForTotalLevel(insertQuery);
        populateActivityPaceForMarketSegment(insertQuery);
        populateActivityPaceForAccomType(insertQuery);
        populateSpecialEventData(insertQuery);
        populateOOODataForTotalLevel(insertQuery);
        populateWebRatePace(insertQuery);
        populateOverbookingPace(insertQuery, firstDecisionId, secondDecisionId);
        populateLRVPace(insertQuery, firstDecisionId, secondDecisionId);
        updateTotalActivityData(insertQuery);
        UpdateOccupancyForecastForGivenOccupancyDate(insertQuery);
        updateBarData(insertQuery, firstDecisionId);
        UpdateBarRateDetails(insertQuery);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        comp1WebRateValue = getWebRateValue(comp1, startDate.plusDays(6));
        comp2WebRateValue = getWebRateValue(comp2, startDate.plusDays(6));
        comp3WebRateValue = getWebRateValue(comp3, startDate.plusDays(6));
    }

    private String getWebRateValue(int compId, LocalDate occupancyDate) {
        double generatedWebRateValue = Math.round((Double.parseDouble("0." + (compId * 5 *
                4 * occupancyDate.getDayOfMonth())) * 100) + 100.0);
        return String.format("%.5f", generatedWebRateValue);
    }

    private void UpdateBarRateDetails(StringBuilder insertQuery) {
        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2303.00000,Monday=2303.00000,Tuesday=2303.00000, ");
        insertQuery.append(" Wednesday=2303.00000,Thursday=2303.00000,Friday=2303.00000,Saturday=2303.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=19and accom_type_id=10 ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2450.00000,Monday=2450.00000,Tuesday=2450.00000, ");
        insertQuery.append(" Wednesday=2450.00000,Thursday=2450.00000,Friday=2450.00000,Saturday=2450.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=18and accom_type_id=10 ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2548.00000,Monday=2548.00000,Tuesday=2548.00000, ");
        insertQuery.append(" Wednesday=2548.00000,Thursday=2548.00000,Friday=2548.00000,Saturday=2548.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=17and accom_type_id=10 ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2744.00000,Monday=2744.00000,Tuesday=2744.00000, ");
        insertQuery.append(" Wednesday=2744.00000,Thursday=2744.00000,Friday=2744.00000,Saturday=2744.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=16and accom_type_id=10 ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2940.00000,Monday=2940.00000,Tuesday=2940.00000, ");
        insertQuery.append(" Wednesday=2940.00000,Thursday=2940.00000,Friday=2940.00000,Saturday=2940.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=15and accom_type_id=10 ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3136.00000,Monday=3136.00000,Tuesday=3136.00000, ");
        insertQuery.append(" Wednesday=3136.00000,Thursday=3136.00000,Friday=3136.00000,Saturday=3136.00000 ");
        insertQuery.append(" where Start_Date_DT<='").append(startDate.plusDays(6)).append("' and End_Date_DT>='").append(startDate.plusDays(6)).append("' and Rate_Unqualified_ID=14and accom_type_id=10 ");
    }

    private void updateBarData(StringBuilder insertQuery, int firstDecisionId) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        insertQuery.append("    UPDATE Decision_Bar_Output " +
                "    SET Rate_Unqualified_ID =  " +
                "    CASE " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 2 AND Accom_Class_ID = 6 THEN 14 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 4 AND Accom_Class_ID = 6 THEN 15 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 3 AND Accom_Class_ID = 6 THEN 15 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 5 AND Accom_Class_ID = 6 THEN 16 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 6 AND Accom_Class_ID = 6 THEN 16 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 8 AND Accom_Class_ID = 6 THEN 17 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 7 AND Accom_Class_ID = 6 THEN 17 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 1 AND Accom_Class_ID = 6 THEN 18 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 2 AND Accom_Class_ID = 7 THEN 14 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 4 AND Accom_Class_ID = 7 THEN 15 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 3 AND Accom_Class_ID = 7 THEN 15 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 5 AND Accom_Class_ID = 7 THEN 16 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 7 AND Accom_Class_ID = 7 THEN 17 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 6 AND Accom_Class_ID = 7 THEN 17 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 8 AND Accom_Class_ID = 7 THEN 18 " +
                "        WHEN Arrival_DT = '" + localDate.plusDays(6) + "' AND los = 1 AND Accom_Class_ID = 7 THEN 19 " +
                "    END " +
                "WHERE " +
                "    Arrival_DT = '" + localDate.plusDays(6) + "' " +
                "    AND los IN (1, 2, 3, 4, 5, 6, 7, 8) " +
                "    AND Accom_Class_ID IN (6, 7) ");

        insertQuery.append(" UPDATE PACE_Bar_Output " +
                "SET  " +
                "    Rate_Unqualified_ID =  " +
                "        CASE " +
                "            WHEN los = 1 AND Accom_Class_ID = 6 THEN 15 " +
                "            WHEN los = 2 AND Accom_Class_ID = 6 THEN 13 " +
                "            WHEN los = 3 AND Accom_Class_ID = 6 THEN 14 " +
                "            WHEN los = 4 AND Accom_Class_ID = 6 THEN 14 " +
                "            WHEN los = 5 AND Accom_Class_ID = 6 THEN 13 " +
                "            WHEN los = 6 AND Accom_Class_ID = 6 THEN 13 " +
                "            WHEN los = 7 AND Accom_Class_ID = 6 THEN 14 " +
                "            WHEN los = 8 AND Accom_Class_ID = 6 THEN 14 " +
                "            WHEN los = 1 AND Accom_Class_ID = 7 THEN 16 " +
                "            WHEN los = 2 AND Accom_Class_ID = 7 THEN 13 " +
                "            WHEN los = 3 AND Accom_Class_ID = 7 THEN 14 " +
                "            WHEN los = 4 AND Accom_Class_ID = 7 THEN 14 " +
                "            WHEN los = 5 AND Accom_Class_ID = 7 THEN 13 " +
                "            WHEN los = 6 AND Accom_Class_ID = 7 THEN 14 " +
                "            WHEN los = 7 AND Accom_Class_ID = 7 THEN 14 " +
                "            WHEN los = 8 AND Accom_Class_ID = 7 THEN 15 " +
                "        END, " +
                "    Derived_Unqualified_Value =  " +
                "        CASE " +
                "            WHEN los = 1 AND Accom_Class_ID = 6 THEN 104.00000 " +
                "            WHEN los = 2 AND Accom_Class_ID = 6 THEN 100.00000 " +
                "            WHEN los = 3 AND Accom_Class_ID = 6 THEN 101.00000 " +
                "            WHEN los = 4 AND Accom_Class_ID = 6 THEN 101.00000 " +
                "            WHEN los = 5 AND Accom_Class_ID = 6 THEN 102.00000 " +
                "            WHEN los = 6 AND Accom_Class_ID = 6 THEN 102.00000 " +
                "            WHEN los = 7 AND Accom_Class_ID = 6 THEN 103.00000 " +
                "            WHEN los = 8 AND Accom_Class_ID = 6 THEN 103.00000 " +
                "            WHEN los = 1 AND Accom_Class_ID = 7 THEN 106.00000 " +
                "            WHEN los = 2 AND Accom_Class_ID = 7 THEN 101.00000 " +
                "            WHEN los = 3 AND Accom_Class_ID = 7 THEN 102.00000 " +
                "            WHEN los = 4 AND Accom_Class_ID = 7 THEN 102.00000 " +
                "            WHEN los = 5 AND Accom_Class_ID = 7 THEN 103.00000 " +
                "            WHEN los = 6 AND Accom_Class_ID = 7 THEN 104.00000 " +
                "            WHEN los = 7 AND Accom_Class_ID = 7 THEN 104.00000 " +
                "            WHEN los = 8 AND Accom_Class_ID = 7 THEN 105.00000 " +
                "        END " +
                "WHERE " +
                "    Arrival_DT = '" + localDate.plusDays(6) + "' AND Decision_ID = " + firstDecisionId + " ");
    }

    private void UpdateOccupancyForecastForGivenOccupancyDate(StringBuilder updateQuery) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        updateQuery.append(" UPDATE Occupancy_FCST " +
                "SET  " +
                "    Occupancy_NBR =  " +
                "        CASE " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 7 THEN 11.11111 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 7 THEN 11.55555 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 7 THEN 11.94449 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 7 THEN 12.99999 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 8 THEN 11.14459 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 8 THEN 11.11111 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 8 THEN 12.14459 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 8 THEN 12.99999 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 9 THEN 11.94449 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 9 THEN 12.55555 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 9 THEN 12.11111 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 9 THEN 12.55555 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 10 THEN 12.94449 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 10 THEN 12.14459 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 10 THEN 12.11111 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 10 THEN 13.55555 " +
                "        END, " +
                "    Revenue =  " +
                "        CASE " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 7 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 7 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 7 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 7 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 8 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 8 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 8 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 8 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 9 THEN 189.00000 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 9 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 9 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 9 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 9 AND MKT_SEG_ID = 10 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 10 AND MKT_SEG_ID = 10 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 11 AND MKT_SEG_ID = 10 THEN 190.00000 " +
                "            WHEN Accom_Type_ID = 12 AND MKT_SEG_ID = 10 THEN 191.00000 " +
                "        END " +
                "WHERE " +
                "    Occupancy_DT = '" + localDate.plusDays(6) + "' ");
    }

    private void updateTotalActivityData(StringBuilder insertQuery) {
        insertQuery.append(" UPDATE [Total_Activity]");
        insertQuery.append(" SET [Property_ID] = 6,[Occupancy_DT] = '").append(startDate.plusDays(6)).append("',[SnapShot_DTTM] = GETDATE()");
        insertQuery.append(" ,[Total_Accom_Capacity] = 295,[Rooms_Sold] = 155,[Rooms_Not_Avail_Maint] = 2");
        insertQuery.append(" ,[Rooms_Not_Avail_Other] = 1,[Arrivals] = 151,[Departures] = 147,[Cancellations] = 0");
        insertQuery.append(" ,[No_Shows] = 0,[Room_Revenue] = 13950.00000,[Food_Revenue] = 775.00000,[Total_Revenue] = 14725.00000");
        insertQuery.append(" ,[File_Metadata_ID] = 1,[Last_Updated_DTTM] = GETDATE(),[CreateDate] = GETDATE()");
        insertQuery.append(" WHERE Occupancy_DT='").append(startDate.plusDays(6)).append("'");
    }

    private void populateLRVPace(StringBuilder insertQuery, int firstDecisionId, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_LRV]([Decision_ID],[Property_ID],[Accom_Class_ID] ");
        insertQuery.append(",[Occupancy_DT],[LRV],[CreateDate_DTTM],[Delta_Value],[Ceiling_Value]) ");
        insertQuery.append(" VALUES(").append(firstDecisionId).append(",6,7,'").append(startDate.plusDays(6)).append("',17.78,GETDATE(),Null,Null), ");
        insertQuery.append(" (").append(secondDecisionId).append(",6,7,'").append(startDate.plusDays(6)).append("',15.87,GETDATE(),Null,Null) ");
        insertQuery.append(" update Decision_LRV set LRV=72.00000 where Occupancy_DT='").append(startDate.plusDays(6)).append("' and Accom_Class_ID=7");
    }

    private void populateOverbookingPace(StringBuilder insertQuery, int firstDecisionId, int secondDecisionId) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        insertQuery.append("  UPDATE PACE_Ovrbk_Accom " +
                "    SET Overbooking_Decision = " +
                "    CASE " +
                "        WHEN Decision_ID = " + secondDecisionId + " AND Accom_Type_ID = 9 THEN 3 " +
                "        WHEN Decision_ID = " + secondDecisionId + " AND Accom_Type_ID = 10 THEN 6 " +
                "        WHEN Decision_ID = " + secondDecisionId + " AND Accom_Type_ID = 11 THEN 8 " +
                "        WHEN Decision_ID = " + secondDecisionId + " AND Accom_Type_ID = 12 THEN 10 " +
                "        WHEN Decision_ID = " + firstDecisionId + " AND Accom_Type_ID = 9 THEN 12 " +
                "        WHEN Decision_ID = " + firstDecisionId + " AND Accom_Type_ID = 10 THEN 13 " +
                "        WHEN Decision_ID = " + firstDecisionId + " AND Accom_Type_ID = 11 THEN 14 " +
                "        WHEN Decision_ID = " + firstDecisionId + " AND Accom_Type_ID = 12 THEN 15 " +
                "    END " +
                "    WHERE " +
                "    (Decision_ID = " + secondDecisionId + " OR Decision_ID = " + firstDecisionId + " " +
                "    AND Accom_Type_ID IN (9, 10, 11, 12) " +
                "    AND Occupancy_DT = '" + localDate.plusDays(6) +
                "')");
        insertQuery.append(" update PACE_Ovrbk_Property set Overbooking_Decision=9 where Decision_ID=").append(secondDecisionId).append(" and Occupancy_DT='").append(startDate.plusDays(6)).append("' ");
        insertQuery.append(" update PACE_Ovrbk_Property set Overbooking_Decision=7 where Decision_ID=").append(firstDecisionId).append(" and Occupancy_DT='").append(startDate.plusDays(6)).append("' ");
        insertQuery.append(" update Decision_Ovrbk_Property set Overbooking_Decision=5 where Occupancy_DT='").append(startDate.plusDays(6)).append("'");
    }

    private void populateWebRatePace(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Webrate]([Webrate_Source_Property_ID],[Webrate_GenerationDate]");
        insertQuery.append(" ,[Webrate_Competitors_ID],[Webrate_Channel_ID],[Webrate_Accom_Type_ID]");
        insertQuery.append(" ,[Webrate_Type_ID],[Occupancy_DT],[LOS],[Webrate_Status],[Webrate_Currency]");
        insertQuery.append(" ,[Webrate_RateValue],[Webrate_Rank],[Webrate_Rating],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',5,5,4,1,'").append(startDate.plusDays(6)).append("',1,'C','USD',97.8762,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',6,5,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',96.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',7,5,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',95.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',5,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',99.8762,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',6,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',94.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(8)).append(" 01:27:00.000',7,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',93.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',5,5,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',91.8762,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',6,5,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',91.8762,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',7,5,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',85.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',5,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',71.8762,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',6,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',73.9742,' ',20,GETDATE()),");
        insertQuery.append(" (2,'").append(startDate.minusDays(11)).append(" 01:27:00.000',7,6,4,1,'").append(startDate.plusDays(6)).append("',1,'A','USD',73.9742,' ',20,GETDATE())");
        insertQuery.append(" update Webrate_Accom_Class_Mapping set Accom_Class_ID=7 where Webrate_Accom_Type_ID=4");

        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        insertQuery.append(" UPDATE Webrate " +
                " SET Webrate_RateValue = " +
                "    CASE " +
                "        WHEN Webrate_Competitors_ID = 5 AND Webrate_Channel_ID = 5 THEN 111.00000 " +
                "        WHEN Webrate_Competitors_ID = 5 AND Webrate_Channel_ID = 6 THEN 113.00000 " +
                "        WHEN Webrate_Competitors_ID = 5 AND Webrate_Channel_ID = 7 THEN 115.00000 " +
                "        WHEN Webrate_Competitors_ID = 6 AND Webrate_Channel_ID = 5 THEN 113.00000 " +
                "        WHEN Webrate_Competitors_ID = 6 AND Webrate_Channel_ID = 6 THEN 116.00000 " +
                "        WHEN Webrate_Competitors_ID = 6 AND Webrate_Channel_ID = 7 THEN 118.00000 " +
                "        WHEN Webrate_Competitors_ID = 7 AND Webrate_Channel_ID = 5 THEN 115.00000 " +
                "        WHEN Webrate_Competitors_ID = 7 AND Webrate_Channel_ID = 6 THEN 118.00000 " +
                "        WHEN Webrate_Competitors_ID = 7 AND Webrate_Channel_ID = 7 THEN 122.00000 " +
                "    END" +
                " WHERE Occupancy_DT = '" + localDate.plusDays(6) + "' AND Webrate_Competitors_ID IN (5, 6, 7) AND Webrate_Channel_ID IN (5, 6, 7) ");
    }

    private void populatePaceWebrateDifferential() {
        String insertQuery = "INSERT INTO [PACE_Webrate_Differential]([Webrate_Source_Property_ID],[First_Webrate_GenerationDate],[Webrate_GenerationDate],[Webrate_count],[Webrate_Competitors_ID],[Webrate_Channel_ID],[Webrate_Accom_Type_ID]," +
                "[Webrate_Type_ID],[Occupancy_DT],[LOS],[Webrate_Status],[Webrate_Currency],[Webrate_RateValue],[Webrate_Rank],[Webrate_Rating],[Last_Updated_DTTM],[Webrate_RateValue_Display])" +
                "values (2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(8) + "',1,5,5,4,1,'" + startDate.plusDays(6) + "',1,'C','USD',97.8762,' ',20,GETDATE(),97.8762)," +
                "(2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(8) + " 22:59:59.999',1,6,5,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',96.9742,' ',20,GETDATE(),96.9742)," +
                "(2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(8) + " 22:59:59.999',1,7,5,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',95.9742,' ',20,GETDATE(),95.9742)," +
                "(2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(8) + " 22:59:59.999',1,5,6,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',97.8762,' ',20,GETDATE(),97.8762)," +
                "(2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(7) + " 22:59:59.999',2,6,6,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',95.9742,' ',20,GETDATE(),95.9742)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(11) + " 22:59:59.999',1,5,5,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',91.8762,' ',20,GETDATE(),91.8762)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(11) + " 22:59:59.999',1,6,5,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',91.8762,' ',20,GETDATE(),91.8762)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(11) + " 22:59:59.999',1,7,5,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',85.9742,' ',20,GETDATE(),85.9742)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(11) + " 22:59:59.999',1,5,6,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',71.8762,' ',20,GETDATE(),71.8762)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(10) + " 22:59:59.999',2,6,6,4,1,'" + startDate.plusDays(6) + "',1,'A','USD',73.9742,' ',20,GETDATE(),73.9742)";

        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

    }

    private void populateOOODataForTotalLevel(StringBuilder insertQuery) {
        insertQuery.append(" update Total_Activity set Rooms_Not_Avail_Maint=2, Rooms_Not_Avail_Other=1 where Occupancy_DT='").append(startDate.plusDays(6)).append("'");
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSix() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(6) + "') as theDayName");
        dow = dowList.get(0).toString();
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight() {
        List secondDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(8) + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(secondDecisionIdList.get(0) + "");
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven() {
        List firstDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11) + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(firstDecisionIdList.get(0) + "");
    }

    private void populateActivityPaceForAccomType(StringBuilder insertQuery) {
        insertQuery.append(" update PACE_Accom_Activity set");
        insertQuery.append(" Rooms_Sold=91,Room_Revenue=8080.00000,Food_Revenue=340.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=4,Arrivals=76,");
        insertQuery.append(" Departures=45,Cancellations=5,No_Shows=1 where Occupancy_DT='").append(startDate.plusDays(6)).append("'");
        insertQuery.append(" and Business_Day_End_DT='").append(startDate.minusDays(11)).append("'");
    }

    private void populateActivityPaceForTotalLevel(StringBuilder insertQuery) {
        insertQuery.append(" update PACE_Total_Activity set");
        insertQuery.append(" Rooms_Sold=91,Room_Revenue=8080.00000,Food_Revenue=340.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=2,Rooms_Not_Avail_Other=3,Arrivals=76,");
        insertQuery.append(" Departures=45,Cancellations=5,No_Shows=1 ");
        insertQuery.append(" where Occupancy_DT='").append(startDate.plusDays(6)).append("'");
        insertQuery.append(" and Business_Day_End_DT='").append(startDate.minusDays(11)).append("'");
        insertQuery.append(" update PACE_Total_Activity set");
        insertQuery.append(" Rooms_Sold=155,Room_Revenue=13950.00000,Food_Revenue=775.00000,");
        insertQuery.append(" Rooms_Not_Avail_Maint=0,Rooms_Not_Avail_Other=0,Arrivals=76,");
        insertQuery.append(" Departures=45,Cancellations=5,No_Shows=1 ");
        insertQuery.append(" where Occupancy_DT='").append(startDate.plusDays(6)).append("'");
        insertQuery.append(" and Business_Day_End_DT='").append(startDate.minusDays(8)).append("'");
    }

    private void populateSpecialEventData(StringBuilder insertQuery) {
        java.time.LocalDate startDate = getJavaLocalDate();
        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'").append(startDate.plusDays(6)).append("','").append(startDate.plusDays(6)).append("',0,1,GETDATE(),1,'SP_PickUPReport','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'").append(startDate.plusDays(6)).append("','").append(startDate.plusDays(6)).append("',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");

        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,3,'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,1,GETDATE(),1,'PickUp Change Report','',1,0,1,GETDATE(),0)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID],[Special_Event_Instance_Name])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1,'Test Instance')");
    }

    private void populateActivityPaceForMarketSegment(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'").append(startDate.plusDays(6)).append("',GETDATE()");
        insertQuery.append(" ,'").append(startDate.minusDays(6)).append("',7,25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
    }

    private void populateForecastPaceForAccomTypeForSecondPacePoint(StringBuilder insertQuery, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Accom_Occupancy_FCST]([Decision_ID],[Property_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (").append(secondDecisionId).append(",6,9,'").append(startDate.plusDays(6)).append("',3.32,27.4567,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,10,'").append(startDate.plusDays(6)).append("',6.72,27.4567,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,11,'").append(startDate.plusDays(6)).append("',3.82,17.4567,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,12,'").append(startDate.plusDays(6)).append("',5.92,37.4567,GETDATE())");
    }

    private void populateForecastPaceForAccomTypeForFirstPacePoint(StringBuilder insertQuery, int firstDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Accom_Occupancy_FCST]([Decision_ID],[Property_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (").append(firstDecisionId).append(",6,9,'").append(startDate.plusDays(6)).append("',2.52,17.4567,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,10,'").append(startDate.plusDays(6)).append("',3.52,37.4567,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,11,'").append(startDate.plusDays(6)).append("',4.52,27.4567,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,12,'").append(startDate.plusDays(6)).append("',5.52,17.4567,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForSecondPacePoint(StringBuilder insertQuery, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (").append(secondDecisionId).append(",6,7,'").append(startDate.plusDays(6)).append("',2.52,17.10556,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,8,'").append(startDate.plusDays(6)).append("',3.35,27.30656,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,9,'").append(startDate.plusDays(6)).append("',4.56,37.70456,GETDATE()),");
        insertQuery.append(" (").append(secondDecisionId).append(",6,10,'").append(startDate.plusDays(6)).append("',5.45,17.40956,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForFirstPacePoint(StringBuilder insertQuery, int firstDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (").append(firstDecisionId).append(",6,7,'").append(startDate.plusDays(6)).append("',1.25,47.10456,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,8,'").append(startDate.plusDays(6)).append("',2.25,57.10456,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,9,'").append(startDate.plusDays(6)).append("',3.25,67.10456,GETDATE()),");
        insertQuery.append(" (").append(firstDecisionId).append(",6,10,'").append(startDate.plusDays(6)).append("',4.25,77.10456,GETDATE())");
    }

    private LocalDate getLocalDate() {
        return getLocalDate(6);
    }

    private String getCaughtUpDate(final Integer propertyId) {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyId + ",3,13)");
        return caughtUpDates.get(0).toString();
    }

    private LocalDate getLocalDate(final Integer propertyId) {
        return LocalDate.parse(getCaughtUpDate(propertyId));
    }

    private Date getJavaLocalDate(final Integer propertyId) {
        return DateUtil.toDate(getCaughtUpDate(propertyId));
    }

    private java.time.LocalDate getJavaLocalDate() {
        return java.time.LocalDate.parse(getCaughtUpDate(propertyID));
    }

    @Test
    public void shouldValidatePickUpAndChangeForRollingAndStaticDates() {
        //setup
        createTestData();
        insertDifferentialRows();
        //tests
        shouldValidatePickUpReportHotelLevelFunctionForStaticDate();
        shouldValidatePickUpReportHotelLevelFunctionForRollingDate();
        shouldValidateChangeReportHotelLevelFunctionForStaticDates();
        shouldValidateChangeReportHotelLevelFunctionForRollingDates();
        shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates();
        shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDates();
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinussEleven() {
        List firstDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11) + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(firstDecisionIdList.get(0) + "");
    }

    private void modifyDecisionTableData() {
        List decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11) + "' " +
                " and Decision_Type_ID=1");
        StringBuilder query = new StringBuilder();
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(").append(propertyID).append(",'").append(startDate.minusDays(11)).append("','").append(new LocalDate()).append("',").append("'").append(new LocalDate()).append("','").append(new LocalDate()).append("',1,'").append(new LocalDate()).append("','").append(new LocalDate()).append("',2,'").append(new LocalDate()).append("')");
        }
        query.append("insert into decision values(").append(propertyID).append(",'").append(startDate.minusDays(11)).append("','").append(new LocalDate()).append("',").append("'").append(new LocalDate()).append("','").append(new LocalDate()).append("',1,'").append(new LocalDate()).append("','").append(new LocalDate()).append("',2,'").append(new LocalDate()).append("')");
        if (!"".equals(query.toString())) {
            tenantCrudService().executeUpdateByNativeQuery(query.toString());
        }

    }

    private void modifyPaceBarOutputData(int accomClassId, Integer rateUnqualifiedId, boolean isCP) {
        List<Integer> decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11) + "' " +
                " and Decision_Type_ID=1");
        StringBuilder query = new StringBuilder();
        if (isCP) {
            query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionIdList.get(0)).append(",1,1,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',-1,1,1,'NONE',null,null,null,getdate(),29,null,null,null)");
            query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionIdList.get(1)).append(",1,1,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',-1,1,1,'NONE',null,null,null,getdate(),30,null,null,null)");

        } else {
            query.append(" insert into pace_bar_output values(").append(decisionIdList.get(0)).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',").append(rateUnqualifiedId).append(",103,-1,'NONE',null,1,1,1,getdate(),null) ");
            query.append(" insert into pace_bar_output values(").append(decisionIdList.get(1)).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',").append(rateUnqualifiedId).append(",204,-1,'NONE',null,1,1,1,getdate(),null) ");
        }
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    @Test
    public void testToverifyDuplicateDataWhenMultipleDecisionsPresent() {
        modifyDataForTests();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today','Today','Today','false',0,0");
        verifyResultsForDuplicateDataTest(reportDataByStaticDates);
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today-11','Today+1','Today+1','false',0,0");
        verifyResultsForDuplicateDataTest(reportDataByRollingDates);
    }

    private void modifyDataForTests() {
        int accomClassId = getAccomClassId();
        Integer accomTypeId = populateAccomTypeAndClassTable(accomClassId);
        setUpTables(accomTypeId);
        Integer rateUnqualifiedID = Integer.valueOf(tenantCrudService().findByNativeQuery("select Rate_Unqualified_ID from Rate_Unqualified").get(0) + "");
        StringBuilder query = new StringBuilder();
        updateRateUnqualifiedDetails(query, rateUnqualifiedID, accomTypeId);
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
        modifyDecisionTableData();
        modifyPaceBarOutputData(accomClassId, rateUnqualifiedID, false);
    }

    @Test
    public void testToverifyDuplicateDataWhenMultipleDecisionsPresentForCP() {
        modifyDataForTests();
        insertDifferentialRows();

        isRolling = 0;
        List<Object[]> reportDataByStaticDatesForStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today','Today','Today',1,1,1");
        verifyResultsForDuplicateDataTest(reportDataByStaticDatesForStaticDates);
        List<Object[]> reportDataByStaticDatesForStaticDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today','Today','Today',1,1,1");
        verifyResultsForDuplicateDataTest(reportDataByStaticDatesForStaticDatesDiff);

        isRolling = 1;
        List<Object[]> reportDataByStaticDatesForRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today-11','Today+1','Today+1',1,1,1");
        verifyResultsForDuplicateDataTest(reportDataByStaticDatesForRollingDates);
        List<Object[]> reportDataByStaticDatesForRollingDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(1) + "','" + startDate.plusDays(1) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null," + isRolling + ",'Today-11','Today+1','Today+1',1,1,1");
        verifyResultsForDuplicateDataTest(reportDataByStaticDatesForRollingDatesDiff);
    }

    private void verifyResultsForDuplicateDataTest(List<Object[]> reportDataDates) {
        assertEquals(1, reportDataDates.size(), "Expected records count not matched");
        assertEquals(startDate.plusDays(1).toString(), (reportDataDates.get(0)[0].toString()), "Arrival Date is not matching");
    }


    @Test
    public void showCompetitorRateForPropertyWhenCompactWebratePaceEnabled() {
        createTestData();
        populatePaceWebrateDifferential();
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'','','','false',1,1");
        String level = "Compact Webrate Pace property level";
        assertCompitetorRate(reportDataByStaticDates, level);
    }

    @Test
    public void showCompetitorRateForPropertyWithRateShoppingRateType() {
        createTestData();
        populatePaceWebrateDifferential();
        mockWebrateTypeProductMapping();
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'','','','false',1,1");
        String level = "Compact Webrate Pace property level";
        assertCompitetorRate(reportDataByStaticDates, level);
    }

    private void assertCompitetorRate(List<Object[]> reportDataByStaticDates, String level) {
        assertEquals(comp1WebRateValue, (reportDataByStaticDates.get(0)[55].toString()), level + " - comp1 rate ");
        assertEquals("Four Point", (reportDataByStaticDates.get(0)[56].toString()), level + " - comp1 name ");
        assertEquals(comp2WebRateValue, (reportDataByStaticDates.get(0)[57].toString()), level + " - comp2 rate ");
        assertEquals("Days Inn", (reportDataByStaticDates.get(0)[58].toString()), level + " - comp2 name ");
        assertEquals(comp3WebRateValue, (reportDataByStaticDates.get(0)[59].toString()), level + " - comp3 rate ");
        assertEquals("Holiday Inn", (reportDataByStaticDates.get(0)[60].toString()), level + " - comp3 name ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp1WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[85].toString()), level + " - comp1 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp2WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[86].toString()), level + " - comp2 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp3WebRateValue) - 85.9742)), convertToBigDecimal(reportDataByStaticDates.get(0)[87].toString()), level + " - comp3 change ");
    }

    private void assertCompitetorRate_rc(List<Object[]> reportDataByStaticDates, String level) {
        assertEquals(comp1WebRateValue, (reportDataByStaticDates.get(0)[67].toString()), level + " - comp1 rate ");
        assertEquals("Four Point", (reportDataByStaticDates.get(0)[68].toString()), level + " - comp1 name ");
        assertEquals(comp2WebRateValue, (reportDataByStaticDates.get(0)[69].toString()), level + " - comp2 rate ");
        assertEquals("Days Inn", (reportDataByStaticDates.get(0)[70].toString()), level + " - comp2 name ");
        assertEquals(comp3WebRateValue, (reportDataByStaticDates.get(0)[71].toString()), level + " - comp3 rate ");
        assertEquals("Holiday Inn", (reportDataByStaticDates.get(0)[72].toString()), level + " - comp3 name ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp1WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[97].toString()), level + " - comp1 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp2WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[98].toString()), level + " - comp2 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp3WebRateValue) - 85.9742)), convertToBigDecimal(reportDataByStaticDates.get(0)[99].toString()), level + " - comp3 change ");
    }

    private BigDecimal convertToBigDecimal(String numberStr) {
        return BigDecimal.valueOf(Double.parseDouble(numberStr)).setScale(5, RoundingMode.HALF_UP);
    }

    private void mockWebrateTypeProductMapping() {
        tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Type_Product (Webrate_Type_ID,Product_ID,LOS,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) values (1,1,1,1,GETDATE(),1,GETDATE())");
    }

    @Test
    public void shouldValidatePickUpReportHotelLevelFunctionForPastAnalysisStartDate() {
        javaStartDate = getJavaLocalDate(6);
        createTestData();
        insertDifferentialRows();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + DateUtil.getDateAsString(javaStartDate, -100) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',0");
        assertNotEquals("0.00", (reportDataByStaticDates.get(0)[6].toString()), "[shouldValidatePickUpReportHotelLevelFunctionForPastAnalysisStartDate]  Rooms Sold Pickup ");
    }

    @Test
    public void showCompetitorRateForRoomClassWhenCompactWebratePaceEnabled() {
        createTestData();
        populatePaceWebrateDifferential();
        insertDifferentialRows();
        String Level = "Compact Webrate Pace Room class level";
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',1,1,1");
        assertCompitetorRate(reportDataByStaticDates, Level);
        List<Object[]> reportDataByStaticDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',1,1,1");
        assertCompitetorRate(reportDataByStaticDatesDiff, Level);
    }

    @Test
    public void showCompetitorRateForNonMasterRoomClassWhenCompactWebratePaceEnabled() {
        createTestData();
        int accomClassId = getAccomClassId();
        populatePaceWebrateDifferential();
        setAsNonMaster(accomClassId);
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_rc " + propertyID + ",'" + accomClassId + "'," + comp1 + "," + comp2 + "," + comp3 + ",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + isRolling + ",'TODAY','TODAY','TODAY','false',0,1,1");
        String Level = "Compact Webrate Pace Room class level";
        assertCompitetorRate_rc(reportDataByStaticDates, Level);
    }

    private void setAsNonMaster(int accomClassId) {
        int nonMasterAccomClassId = getNonMasterAccomClassId();
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Class set Master_Class = 0 where Accom_Class_ID = " + accomClassId);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Class set Master_Class = 1 where Accom_Class_ID = " + nonMasterAccomClassId);
    }


    private void shouldValidatePickUpReportHotelLevelFunctionForStaticDate() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.minusDays(8) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',0");
        assertPickUpReportDataAtHotelLevel("[shouldValidatePickUpReportHotelLevelFunctionForStaticDate] " + "Pickup Report at Hotel level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    private void shouldValidatePickUpReportHotelLevelFunctionForRollingDate() {
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.minusDays(8) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6',0");
        assertPickUpReportDataAtHotelLevel("[shouldValidatePickUpReportHotelLevelFunctionForRollingDate] " + "Pickup Report at Hotel level with Rolling Dates", reportDataByRollingDates, "SP_PickUPReport");
    }

    private void shouldValidateChangeReportHotelLevelFunctionForStaticDates() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today','false',0,0");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevelFunctionForStaticDates] " + "Change Report at Hotel level with Static Dates", reportDataByStaticDates, "NonCP", "SP_PickUPReport");
    }

    private void shouldValidateChangeReportHotelLevelFunctionForRollingDates() {
        isRolling = 0;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,1,'Today-11','Today+6','Today+6','false',0,0");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevelFunctionForRollingDates] " + "Change Report at Hotel level with Rolling Dates", reportDataByRollingDates, "NonCP", "SP_PickUPReport");
    }

    private void shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByStaticDates, "CP", "SP_PickUPReport");
        List<Object[]> reportDataByStaticDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByStaticDatesDiff, "CP", "SP_PickUPReport");
    }

    private void shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDates() {
        isRolling = 0;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_cp " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,1,'Today-11','Today+6','Today+6',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByRollingDates, "CP", "SP_PickUPReport");
        List<Object[]> reportDataByRollingDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_cp_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,1,'Today-11','Today+6','Today+6',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByRollingDatesDiff, "CP", "SP_PickUPReport");
    }

    private void assertPickUpReportDataAtHotelLevel(String Level, List<Object[]> reportDataByStaticDates, String expectedSpecialEventName) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), Level + " - Arrival Date ");
        assertEquals("6", (reportDataByStaticDates.get(0)[2].toString()), Level + " - Property ID ");
        assertEquals("155", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Rooms Sold ");
        assertEquals(expectedSpecialEventName, (reportDataByStaticDates.get(0)[4].toString()), Level + " - Special Event ");
        assertEquals("3", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Out Of Order ");
        assertEquals("64.00", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms Sold Pickup ");
        assertEquals("194.93", (reportDataByStaticDates.get(0)[7].toString()), Level + " - Occupancy Forecast ");
        assertEquals("66.76", (reportDataByStaticDates.get(0)[8].toString()), Level + " - Occupancy Percentage ");
        assertEquals("66.08", (reportDataByStaticDates.get(0)[9].toString()), Level + " - Occupancy Percentage as per Physical Capacity");
        assertEquals("3.70", (reportDataByStaticDates.get(0)[10].toString()), Level + " - Occupancy Pickup ");
        assertEquals("1.16", (reportDataByStaticDates.get(0)[11].toString()), Level + " - Occupancy Percentage Pickup ");
        assertEquals("1.25", (reportDataByStaticDates.get(0)[12].toString()), Level + " - Occupancy Percentage Pickup as per Physical Capacity ");
        assertEquals("3035.00", (reportDataByStaticDates.get(0)[13].toString()), Level + " - Revenue ");
        assertEquals("15.57", (reportDataByStaticDates.get(0)[14].toString()), Level + " - ADR ");
        assertEquals("10.39", (reportDataByStaticDates.get(0)[15].toString()), Level + " - RevPAR ");
        assertEquals("0.03", (reportDataByStaticDates.get(0)[16].toString()), Level + " - RevPAR Pickup ");
        assertEquals("10.00", (reportDataByStaticDates.get(0)[17].toString()), Level + " - Revenue Pickup ");
        assertEquals("-0.66", (reportDataByStaticDates.get(0)[18].toString()), Level + " - ADR Pickup ");
        assertEquals("13950.00", (reportDataByStaticDates.get(0)[19].toString()), Level + " - Booked Revenue ");
        assertEquals("90.00", (reportDataByStaticDates.get(0)[20].toString()), Level + " - Booked ADR ");
        assertEquals("47.77", (reportDataByStaticDates.get(0)[21].toString()), Level + " - Booked RevPAR ");
        assertEquals("1.21", (reportDataByStaticDates.get(0)[22].toString()), Level + " - Booked ADR Pickup ");
        assertEquals("19.43", (reportDataByStaticDates.get(0)[23].toString()), Level + " - Booked RevPAR Pickup ");
        assertEquals("5870.00", (reportDataByStaticDates.get(0)[24].toString()), Level + " - Booked Revenue Pickup ");
    }


    private void assertChangeReportDataAtHotelLevel(String level, List<Object[]> reportDataByStaticDates, String barType, String expectedSpecialEventName) {
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[0].toString()), level + " - Arrival Date ");
        assertEquals(dow, (reportDataByStaticDates.get(0)[1].toString()), level + " - DOW ");
        assertEquals("3", (reportDataByStaticDates.get(0)[2].toString()), level + " - Out Of order ");
        assertEquals(expectedSpecialEventName, (reportDataByStaticDates.get(0)[3].toString()), level + " - Special Event ");
        assertEquals("6", (reportDataByStaticDates.get(0)[4].toString()), level + " - Property ID ");
        assertEquals("155", (reportDataByStaticDates.get(0)[5].toString()), level + " - Room Sold Current ");
        assertEquals("64.00", (reportDataByStaticDates.get(0)[6].toString()), level + " - Room Sold Change ");
        assertEquals("194.93386", (reportDataByStaticDates.get(0)[7].toString()), level + " - Occupancy Forecast Current ");
        assertEquals("178.85", (reportDataByStaticDates.get(0)[8].toString()), level + " - Occupancy Forecast Change ");
        assertEquals("66.758100", (reportDataByStaticDates.get(0)[9].toString()), level + " - Occupancy forecast Percentage Current ");
        assertEquals("61.21", (reportDataByStaticDates.get(0)[10].toString()), level + " - occupancy forecast Percentage Change ");
        assertEquals("13950.00000", (reportDataByStaticDates.get(0)[11].toString()), level + " - Booked room revenue current ");
        assertEquals("5870.00", (reportDataByStaticDates.get(0)[12].toString()), level + " - Booked room revenue change ");
        assertEquals("3035.00000", (reportDataByStaticDates.get(0)[13].toString()), level + " - Forecasted room revenue current ");
        assertEquals("2935.17", (reportDataByStaticDates.get(0)[14].toString()), level + " - Forecasted room revenue change ");
        assertEquals("90.00000", (reportDataByStaticDates.get(0)[15].toString()), level + " - Booked adr current ");
        assertEquals("1.21", (reportDataByStaticDates.get(0)[16].toString()), level + " - Booked adr change ");
        assertEquals("15.570000", (reportDataByStaticDates.get(0)[17].toString()), level + " - Estimated adr current ");
        assertEquals("9.36", (reportDataByStaticDates.get(0)[18].toString()), level + " - Estimated adr change ");
        assertEquals("47.77397", (reportDataByStaticDates.get(0)[19].toString()), level + " - Booked revpar current ");
        assertEquals("19.91", (reportDataByStaticDates.get(0)[20].toString()), level + " - Booked revpar change ");
        assertEquals("10.390000", (reportDataByStaticDates.get(0)[21].toString()), level + " - Estimated revpar current ");
        assertEquals("10.05", (reportDataByStaticDates.get(0)[22].toString()), level + " - Estimated revpar change ");
        assertEquals("Deluxe", (reportDataByStaticDates.get(0)[23].toString()), level + " - Master classname ");
        assertEquals("72.00000", (reportDataByStaticDates.get(0)[24].toString()), level + " - LRV ");
        assertEquals("54.22", (reportDataByStaticDates.get(0)[25].toString()), level + " - LRV change ");
        assertEquals("5", (reportDataByStaticDates.get(0)[26].toString()), level + " - Overbooking current ");
        assertEquals("-2.00", (reportDataByStaticDates.get(0)[27].toString()), level + " - Overbooking change ");
        if (barType.equals("NonCP")) {
            assertEquals("2303.0", (reportDataByStaticDates.get(0)[28].toString()), level + " - bar_los1 ");
            assertEquals("3136.0", (reportDataByStaticDates.get(0)[29].toString()), level + " - bar_los2 ");
            assertEquals("2940.0", (reportDataByStaticDates.get(0)[30].toString()), level + " - bar_los3 ");
            assertEquals("2940.0", (reportDataByStaticDates.get(0)[31].toString()), level + " - bar_los4 ");
            assertEquals("2744.0", (reportDataByStaticDates.get(0)[32].toString()), level + " - bar_los5 ");
            assertEquals("2548.0", (reportDataByStaticDates.get(0)[33].toString()), level + " - bar_los6 ");
            assertEquals("2548.0", (reportDataByStaticDates.get(0)[34].toString()), level + " - bar_los7 ");
            assertEquals("2450.0", (reportDataByStaticDates.get(0)[35].toString()), level + " - bar_los8 ");
//        assertEquals(level+" - bar_by_day ","0.0",(reportDataByStaticDates.get(0)[36].toString()));
            assertEquals("BAR6", (reportDataByStaticDates.get(0)[37].toString()), level + " - Ratecode_los1 ");
            assertEquals("BAR1", (reportDataByStaticDates.get(0)[38].toString()), level + " - Ratecode_los2 ");
            assertEquals("BAR2", (reportDataByStaticDates.get(0)[39].toString()), level + " - Ratecode_los3 ");
            assertEquals("BAR2", (reportDataByStaticDates.get(0)[40].toString()), level + " - Ratecode_los4 ");
            assertEquals("BAR3", (reportDataByStaticDates.get(0)[41].toString()), level + " - Ratecode_los5 ");
            assertEquals("BAR4", (reportDataByStaticDates.get(0)[42].toString()), level + " - Ratecode_los6 ");
            assertEquals("BAR4", (reportDataByStaticDates.get(0)[43].toString()), level + " - Ratecode_los7 ");
            assertEquals("BAR5", (reportDataByStaticDates.get(0)[44].toString()), level + " - Ratecode_los8 ");
//        assertEquals(level+" - Ratecode_los_all ","0.00",(reportDataByStaticDates.get(0)[45].toString()));
            assertEquals("2197.00", (reportDataByStaticDates.get(0)[46].toString()), level + " - bar_los1_change ");
            assertEquals("3035.00", (reportDataByStaticDates.get(0)[47].toString()), level + " - bar_los2_change ");
            assertEquals("2838.00", (reportDataByStaticDates.get(0)[48].toString()), level + " - bar_los3_change ");
            assertEquals("2838.00", (reportDataByStaticDates.get(0)[49].toString()), level + " - bar_los4_change ");
            assertEquals("2641.00", (reportDataByStaticDates.get(0)[50].toString()), level + " - bar_los5_change ");
            assertEquals("2444.00", (reportDataByStaticDates.get(0)[51].toString()), level + " - bar_los6_change ");
            assertEquals("2444.00", (reportDataByStaticDates.get(0)[52].toString()), level + " - bar_los7_change ");
            assertEquals("2345.00", (reportDataByStaticDates.get(0)[53].toString()), level + " - bar_los8_change ");
        }
//        assertEquals(level+" - bar_by_day_change ","0.00",(reportDataByStaticDates.get(0)[54].toString()));
        assertEquals(comp1WebRateValue, (reportDataByStaticDates.get(0)[55].toString()), level + " - comp1 rate ");
        assertEquals("Four Point", (reportDataByStaticDates.get(0)[56].toString()), level + " - comp1 name ");
        assertEquals(comp2WebRateValue, (reportDataByStaticDates.get(0)[57].toString()), level + " - comp2 rate ");
        assertEquals("Days Inn", (reportDataByStaticDates.get(0)[58].toString()), level + " - comp2 name ");
        assertEquals(comp3WebRateValue, (reportDataByStaticDates.get(0)[59].toString()), level + " - comp3 rate ");
        assertEquals("Holiday Inn", (reportDataByStaticDates.get(0)[60].toString()), level + " - comp3 name ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp1WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[85].toString()), level + " - comp1 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp2WebRateValue) - 91.8762)), convertToBigDecimal(reportDataByStaticDates.get(0)[86].toString()), level + " - comp2 change ");
        assertEquals(convertToBigDecimal(String.valueOf(Double.parseDouble(comp3WebRateValue) - 85.9742)), convertToBigDecimal(reportDataByStaticDates.get(0)[87].toString()), level + " - comp3 change ");
        assertEquals("66.079200", (reportDataByStaticDates.get(0)[100].toString()), level + " - occupancy forecast percentage current without ooo ");
        assertEquals("60.63", (reportDataByStaticDates.get(0)[101].toString()), level + " - occupancy forecast percentage change without ooo ");
    }

    @Test
    public void shouldValidatePickUpAndChangeReportHotelLevelFunctionWhenActivityDateIsBeyondAnalysisDate() {
        int accomClassId = getAccomClassId();
        Integer accomTypeId = populateAccomTypeAndClassTable(accomClassId);
        setUpTables(accomTypeId);

        shouldValidatePickUpReportHotelLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidatePickUpReportHotelLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate();

        //Change Report Tests
        setUpChangeReportTables(accomClassId, accomTypeId);
        insertDifferentialRows();
        shouldValidateChangeReportHotelLevelFunctionForStaticDatesWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeReportHotelLevelFunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDatesWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate();
    }

    private void shouldValidateChangeReportHotelLevelFunctionForStaticDatesWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1) + "','" + startDate.toString() + "','" + startDate.plusDays(3) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today','false',0,0");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevelFunctionForStaticDatesWhenActivityDateIsBeyondAnalysisDate", reportData, false);
    }

    private void shouldValidateChangeReportHotelLevelFunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,'Today+1','Today','Today+3','false',0,0");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevelFunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate", reportData, false);
    }

    private void shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDatesWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1) + "','" + startDate.toString() + "','" + startDate.plusDays(3) + "',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDatesWhenActivityDateIsBeyondAnalysisDate", reportData, true);
        List<Object[]> reportDataDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1) + "','" + startDate.toString() + "','" + startDate.plusDays(3) + "',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDatesWhenActivityDateIsBeyondAnalysisDate", reportDataDiff, true);
    }

    private void shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_cp " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,'Today+1','Today','Today+3',0,0,1");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate", reportData, true);
        List<Object[]> reportDataDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_cp_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,'Today+1','Today','Today+3',0,0,1");
        assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_FunctionForRollingDatesWhenActivityDateIsBeyondAnalysisDate", reportDataDiff, true);
    }

    private void assertChangeReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate(String message, List<Object[]> reportData, boolean isCP) {
        assertEquals(4, reportData.size(), message);
        assertEquals("0.00", reportData.get(0)[6].toString(), message);
        assertEquals("1.00", reportData.get(1)[6].toString(), message);
        assertEquals("2.00", reportData.get(2)[6].toString(), message);
        assertEquals("1.00", reportData.get(3)[6].toString(), message);
        assertEquals("0.00", reportData.get(0)[8].toString(), message);
        assertEquals("9.68", reportData.get(1)[8].toString(), message);
        assertEquals("5.28", reportData.get(2)[8].toString(), message);
        assertEquals("7.18", reportData.get(3)[8].toString(), message);
        assertTrue(reportData.get(1)[10].toString().startsWith("17.8"), message);
        assertTrue(reportData.get(2)[10].toString().startsWith("8.98"), message);
        assertTrue(reportData.get(3)[10].toString().startsWith("12.48"), message);
        assertTrue(reportData.get(0)[12].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[12].toString().startsWith("810.0"), message);
        assertTrue(reportData.get(2)[12].toString().startsWith("900.0"), message);
        assertTrue(reportData.get(3)[12].toString().startsWith("810.0"), message);
        assertTrue(reportData.get(0)[14].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[14].toString().startsWith("163.54"), message);
        assertTrue(reportData.get(2)[14].toString().startsWith("161.54"), message);
        assertTrue(reportData.get(3)[14].toString().startsWith("172.54"), message);
        assertTrue(reportData.get(0)[16].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[16].toString().startsWith("13.33"), message);
        assertTrue(reportData.get(2)[16].toString().startsWith("13.09"), message);
        assertTrue(reportData.get(3)[16].toString().startsWith("12.63"), message);
        assertTrue(reportData.get(0)[18].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[18].toString().startsWith("6.42"), message);
        assertTrue(reportData.get(2)[18].toString().startsWith("11.66"), message);
        assertTrue(reportData.get(3)[18].toString().startsWith("12.70"), message);
        assertTrue(reportData.get(0)[20].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[20].toString().startsWith("13.55"), message);
        assertTrue(reportData.get(2)[20].toString().startsWith("13.2"), message);
        assertTrue(reportData.get(3)[20].toString().startsWith("12.8"), message);
        assertTrue(reportData.get(0)[22].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[22].toString().startsWith("3.02"), message);
        assertTrue(reportData.get(2)[22].toString().startsWith("2.87"), message);
        assertTrue(reportData.get(3)[22].toString().startsWith("3.02"), message);
        assertTrue(reportData.get(0)[25].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[25].toString().startsWith("-7.0"), message);
        assertTrue(reportData.get(2)[25].toString().startsWith("21.0"), message);
        assertTrue(reportData.get(3)[25].toString().startsWith("6.0"), message);
        assertTrue(reportData.get(0)[27].toString().startsWith("0.00"), message);
        assertTrue(reportData.get(1)[27].toString().startsWith("-10.00"), message);
        assertTrue(reportData.get(2)[27].toString().startsWith("-10.0"), message);
        assertTrue(reportData.get(3)[27].toString().startsWith("-10.0"), message);
        assertTrue(reportData.get(0)[46].toString().startsWith("0.00"), message);
        if (isCP) {
            assertTrue(reportData.get(1)[46].toString().startsWith("-10.0"), message);
            assertTrue(reportData.get(2)[46].toString().startsWith("-10.0"), message);
            assertTrue(reportData.get(3)[46].toString().startsWith("-10.0"), message);
        } else {
            assertTrue(reportData.get(1)[46].toString().startsWith("12597.00"), message);
            assertTrue(reportData.get(2)[46].toString().startsWith("12695.00"), message);
            assertTrue(reportData.get(3)[46].toString().startsWith("12793.00"), message);
        }
        for (int i = 47; i < 55; i++) {
            for (int j = 0; j < 4; j++) {
                assertEquals("0.00", reportData.get(j)[i].toString(), message);
            }
        }
    }

    private void setUpTables(Integer accomTypeId) {
        StringBuilder query = new StringBuilder();
        populateTotalActivityTable(query);
        populatePaceTotalActivityTable(query);
        updateOccupancyForecastData(query, accomTypeId);
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
        updateDecisionTable();
        updatePaceAccomOccupancyFCSTData(accomTypeId);
    }

    private void setUpChangeReportTables(int accomClassId, Integer accomTypeId) {
        Integer rateUnqualifiedId = Integer.valueOf(tenantCrudService().findByNativeQuery("select Rate_Unqualified_ID from Rate_Unqualified").get(0) + "");
        StringBuilder query = new StringBuilder();
        updateRateUnqualifiedDetails(query, rateUnqualifiedId, accomTypeId);
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
        updateDecisionBarOutputData(accomClassId, rateUnqualifiedId);
        updatePaceBarOutputData(accomClassId, rateUnqualifiedId);
        updateCPDecisionBarOutputData(accomTypeId);
        Integer cfgAccomTypeId = updateCPCfgAccomClassData(accomClassId);
        updateCPPaceBarOutputData(cfgAccomTypeId);
        updateDecisionLrvData(accomClassId);
        updatePaceLrvData(accomClassId);
        updateOverbookingDecisionData();
        updatePaceOverbookingDecisionData();
    }

    private Integer updateCPCfgAccomClassData(int accomClassId) {
        List accomTypeId = tenantCrudService().findByNativeQuery("select Accom_Type_Id from CP_Cfg_AC where Accom_Class_Id = " + accomClassId);
        if (null == accomTypeId || accomTypeId.isEmpty()) {
            String query = "insert into CP_Cfg_AC values(6," + accomClassId + ",0,1,getdate(),1,getdate(),null,null";
            tenantCrudService().executeUpdateByNativeQuery(query);
            tenantCrudService().flushAndClear();
        }
        return Integer.valueOf(tenantCrudService().findByNativeQuery("select Accom_Type_Id from CP_Cfg_AC where Accom_Class_Id = " + accomClassId).get(0) + "");
    }

    private void updateCPPaceBarOutputData(Integer cfgAccomTypeId) {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionId).append(",1,1,").append(cfgAccomTypeId).append(",'").append(startDate.plusDays(1)).append("',1,1,1,'NONE',null,null,null,getdate(),29,null,null,null)");
        query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionId).append(",1,1,").append(cfgAccomTypeId).append(",'").append(startDate.plusDays(2)).append("',1,1,1,'NONE',null,null,null,getdate(),30,null,null,null)");
        query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionId).append(",1,1,").append(cfgAccomTypeId).append(",'").append(startDate.plusDays(3)).append("',1,1,1,'NONE',null,null,null,getdate(),31,null,null,null)");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionId).append(",1,1,").append(cfgAccomTypeId).append(",'").append(startDate.plusDays(2)).append("',1,1,1,'NONE',null,null,null,getdate(),32,null,null,null)");
        query.append("insert into cp_pace_decision_bar_output values(6,").append(decisionId).append(",1,1,").append(cfgAccomTypeId).append(",'").append(startDate.plusDays(3)).append("',1,1,1,'NONE',null,null,null,getdate(),33,null,null,null)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateCPDecisionBarOutputData(int accomTypeId) {
        ArrayList<CPDecisionBAROutput> cpDecisionBAROutputs = new ArrayList<>();
        cpDecisionBAROutputs.add(createCpDecisionBarOutput(accomTypeId, startDate, BigDecimal.valueOf(18)));
        cpDecisionBAROutputs.add(createCpDecisionBarOutput(accomTypeId, startDate.plusDays(1), BigDecimal.valueOf(19)));
        cpDecisionBAROutputs.add(createCpDecisionBarOutput(accomTypeId, startDate.plusDays(2), BigDecimal.valueOf(20)));
        cpDecisionBAROutputs.add(createCpDecisionBarOutput(accomTypeId, startDate.plusDays(3), BigDecimal.valueOf(21)));
        tenantCrudService().save(cpDecisionBAROutputs);
    }

    private void updatePaceOverbookingDecisionData() {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append("insert into pace_ovrbk_property values (").append(decisionId).append(",6,'").append(startDate.plusDays(1)).append("',29,25,getdate(),null)");
        query.append("insert into pace_ovrbk_property values (").append(decisionId).append(",6,'").append(startDate.plusDays(2)).append("',30,26,getdate(),null)");
        query.append("insert into pace_ovrbk_property values (").append(decisionId).append(",6,'").append(startDate.plusDays(3)).append("',31,27,getdate(),null)");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append("insert into pace_ovrbk_property values (").append(decisionId).append(",6,'").append(startDate.plusDays(2)).append("',32,26,getdate(),null)");
        query.append("insert into pace_ovrbk_property values (").append(decisionId).append(",6,'").append(startDate.plusDays(3)).append("',33,27,getdate(),null)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateOverbookingDecisionData() {
        String query = "update Decision_Ovrbk_Property set Overbooking_Decision=18 where Occupancy_DT='" + startDate.toString() + "'" +
                "update Decision_Ovrbk_Property set Overbooking_Decision=19 where Occupancy_DT='" + startDate.plusDays(1) + "'" +
                "update Decision_Ovrbk_Property set Overbooking_Decision=20 where Occupancy_DT='" + startDate.plusDays(2) + "'" +
                "update Decision_Ovrbk_Property set Overbooking_Decision=21 where Occupancy_DT='" + startDate.plusDays(3) + "'";
        tenantCrudService().executeUpdateByNativeQuery(query);
    }

    private void updatePaceLrvData(int accomClassId) {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append("insert into pace_lrv values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',89,getdate(),null,null)");
        query.append("insert into pace_lrv values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(2)).append("',56,getdate(),null,null)");
        query.append("insert into pace_lrv values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(3)).append("',80,getdate(),null,null)");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append("insert into pace_lrv values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(2)).append("',67,getdate(),null,null)");
        query.append("insert into pace_lrv values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(3)).append("',89,getdate(),null,null)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateDecisionLrvData(int accomClassId) {
        String query = " update Decision_LRV set LRV=79.00000 where Occupancy_DT='" + startDate.toString() + "' and Accom_Class_ID=" + accomClassId +
                " update Decision_LRV set LRV=82.00000 where Occupancy_DT='" + startDate.plusDays(1) + "' and Accom_Class_ID=" + accomClassId +
                " update Decision_LRV set LRV=77.00000 where Occupancy_DT='" + startDate.plusDays(2) + "' and Accom_Class_ID=" + accomClassId +
                " update Decision_LRV set LRV=86.00000 where Occupancy_DT='" + startDate.plusDays(3) + "' and Accom_Class_ID=" + accomClassId;
        tenantCrudService().executeUpdateByNativeQuery(query);
    }

    private void updatePaceBarOutputData(int accomClassId, Integer rateUnqualifiedId) {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append("insert into pace_bar_output values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(1)).append("',").append(rateUnqualifiedId).append(",103,1,'NONE',null,1,1,1,getdate(),null)");
        query.append("insert into pace_bar_output values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(2)).append("',").append(rateUnqualifiedId).append(",105,1,'NONE',null,1,1,1,getdate(),null)");
        query.append("insert into pace_bar_output values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(3)).append("',").append(rateUnqualifiedId).append(",107,1,'NONE',null,1,1,1,getdate(),null)");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append("insert into pace_bar_output values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(2)).append("',").append(rateUnqualifiedId).append(",204,1,'NONE',null,1,1,1,getdate(),null)");
        query.append("insert into pace_bar_output values(").append(decisionId).append(",6,").append(accomClassId).append(",'").append(startDate.plusDays(3)).append("',").append(rateUnqualifiedId).append(",206,1,'NONE',null,1,1,1,getdate(),null)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateDecisionBarOutputData(int accomClassId, Integer rateUnqualifiedId) {
        tenantCrudService().executeUpdateByNativeQuery("truncate table decision_bar_output");
        tenantCrudService().flushAndClear();
        String query = "insert into decision_bar_output values(1,6," + accomClassId + ",'" + startDate.toString() + "'," + rateUnqualifiedId + ",1,'NONE',null,1,null,null,getdate(),null)" +
                "insert into decision_bar_output values(1,6," + accomClassId + ",'" + startDate.plusDays(1) + "'," + rateUnqualifiedId + ",1,'NONE',null,1,null,null,getdate(),null)" +
                "insert into decision_bar_output values(1,6," + accomClassId + ",'" + startDate.plusDays(2) + "'," + rateUnqualifiedId + ",1,'NONE',null,1,null,null,getdate(),null)" +
                "insert into decision_bar_output values(1,6," + accomClassId + ",'" + startDate.plusDays(3) + "'," + rateUnqualifiedId + ",1,'NONE',null,1,null,null,getdate(),null)";
        tenantCrudService().executeUpdateByNativeQuery(query);
    }

    private void updateRateUnqualifiedDetails(StringBuilder query, Integer rateUnqualifiedID, Integer accomTypeId) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        query.append("truncate table Rate_Unqualified_Details ");
        query.append(" insert into Rate_Unqualified_Details values(" + rateUnqualifiedID + "," + accomTypeId + ",'" + localDate.toString() + "','" + localDate + "',12600.00000, 12600.00000, 12600.00000, 12600.00000, 12600.00000, 12600.00000, 12600.00000, 1, getdate(), 1, getdate())" +
                "insert into Rate_Unqualified_Details values(" + rateUnqualifiedID + "," + accomTypeId + ",'" + localDate.plusDays(1) + "','" + localDate.plusDays(1) + "',12700.00000, 12700.00000, 12700.00000, 12700.00000, 12700.00000, 12700.00000, 12700.00000, 1, getdate(), 1, getdate())" +
                "insert into Rate_Unqualified_Details values(" + rateUnqualifiedID + "," + accomTypeId + ",'" + localDate.plusDays(2) + "','" + localDate.plusDays(2) + "',12800.00000, 12800.00000, 12800.00000, 12800.00000, 12800.00000, 12800.00000, 12800.00000, 1, getdate(), 1, getdate())" +
                "insert into Rate_Unqualified_Details values(" + rateUnqualifiedID + "," + accomTypeId + ",'" + localDate.plusDays(3) + "','" + localDate.plusDays(3) + "',12900.00000, 12900.00000, 12900.00000, 12900.00000, 12900.00000, 12900.00000, 12900, 1, getdate(), 1, getdate()) ");
    }

    private void updatePaceAccomOccupancyFCSTData(Integer accomTypeID) {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO [PACE_Accom_Occupancy_FCST]([Decision_ID],[Property_ID],[Accom_Type_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (").append(decisionId).append(",6,").append(accomTypeID).append(",'").append(startDate.plusDays(1)).append("',3.32,27.4567,GETDATE()),");
        query.append(" (").append(decisionId).append(",6,").append(accomTypeID).append(",'").append(startDate.plusDays(2)).append("',6.72,27.4567,GETDATE()),");
        query.append(" (").append(decisionId).append(",6,").append(accomTypeID).append(",'").append(startDate.plusDays(3)).append("',3.82,17.4567,GETDATE())");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append(" INSERT INTO [PACE_Accom_Occupancy_FCST]([Decision_ID],[Property_ID],[Accom_Type_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (").append(decisionId).append(",6,").append(accomTypeID).append(",'").append(startDate.plusDays(2)).append("',2.52,17.4567,GETDATE()),");
        query.append(" (").append(decisionId).append(",6,").append(accomTypeID).append(",'").append(startDate.plusDays(3)).append("',3.52,37.4567,GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateDecisionTable() {
        List decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(1) + "' " +
                " and Decision_Type_ID=1");
        StringBuilder query = new StringBuilder();
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(").append(propertyID).append(",'").append(startDate.plusDays(1)).append("','").append(new LocalDate()).append("',").append("'").append(new LocalDate()).append("','").append(new LocalDate()).append("',1,'").append(new LocalDate()).append("','").append(new LocalDate()).append("',2,'").append(new LocalDate()).append("')");
        }
        decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(2) + "' " +
                " and Decision_Type_ID=1");
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(").append(propertyID).append(",'").append(startDate.plusDays(2)).append("','").append(new LocalDate()).append("',").append("'").append(new LocalDate()).append("','").append(new LocalDate()).append("',1,'").append(new LocalDate()).append("','").append(new LocalDate()).append("',2,'").append(new LocalDate()).append("')");
        }
        if (!"".equals(query.toString())) {
            tenantCrudService().executeUpdateByNativeQuery(query.toString());
        }
    }

    private Integer populateAccomTypeAndClassTable(int accomClassId) {
        List accomTypeId = tenantCrudService().findByNativeQuery("select Accom_Type_Id from Accom_Type where Accom_Class_Id = " + accomClassId);
        if (null == accomTypeId || accomTypeId.isEmpty()) {
            String query = "insert into Accom_Type values(6,'test','test','',100," + accomClassId + ",0,1,getdate(),0,1,getdate(),1,'N',1,0, NULL)";
            tenantCrudService().executeUpdateByNativeQuery(query);
            tenantCrudService().flushAndClear();
        }
        return Integer.valueOf(tenantCrudService().findByNativeQuery("select Accom_Type_Id from Accom_Type where Accom_Class_Id = " + accomClassId).get(0) + "");

    }

    private void updateOccupancyForecastData(StringBuilder query, Integer accomTypeId) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        query.append(" truncate table Occupancy_FCST");
        query.append(" INSERT INTO Occupancy_FCST" +
                " VALUES (1,6,7," + accomTypeId + ",'" + localDate.toString() + "',10,175,4,5,getDate(), 5)" +
                " INSERT INTO Occupancy_FCST" +
                " VALUES (1,6,7," + accomTypeId + ",'" + localDate.plusDays(1) + "',13,191,4,5,getDate(), 5)" +
                " INSERT INTO Occupancy_FCST" +
                " VALUES (1,6,7," + accomTypeId + ",'" + localDate.plusDays(2) + "',12,189,4,5,getDate(), 5)" +
                " INSERT INTO Occupancy_FCST" +
                " VALUES (1,6,7," + accomTypeId + ",'" + localDate.plusDays(3) + "',11,190,4,5,getDate(), 5)");
    }

    private int getAccomClassId() {
        if (accomClassID == 0) {
            accomClassID = Integer.valueOf(tenantCrudService().findByNativeQuery("select Accom_Class_Id from Accom_Class where Master_Class = 1").get(0) + "");
        }
        return accomClassID;
    }

    private int getNonMasterAccomClassId() {
        return Integer.valueOf(tenantCrudService().findByNativeQuery("select MAX(Accom_Class_Id) from Accom_Class where Master_Class = 0").get(0) + "");
    }

    private void populateTotalActivityTable(StringBuilder query) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        query.append(" UPDATE [Total_Activity]" +
                " SET [Property_ID] = 6,[Occupancy_DT] = '" + localDate.toString() + "',[SnapShot_DTTM] = GETDATE()" +
                " ,[Total_Accom_Capacity] = 55,[Rooms_Sold] = 54,[Rooms_Not_Avail_Maint] = 1" +
                " ,[Rooms_Not_Avail_Other] = 1,[Arrivals] = 51,[Departures] = 52,[Cancellations] = 0" +
                " ,[No_Shows] = 0,[Room_Revenue] = 4860.00000,[Food_Revenue] = 270.00000,[Total_Revenue] = 5130.00000" +
                " ,[File_Metadata_ID] = 1,[Last_Updated_DTTM] = GETDATE(),[CreateDate] = GETDATE()" +
                " WHERE Occupancy_DT='" + startDate + "'" +
                " UPDATE [Total_Activity]" +
                " SET [Property_ID] = 6,[Occupancy_DT] = '" + localDate.plusDays(1) + "',[SnapShot_DTTM] = GETDATE()" +
                " ,[Total_Accom_Capacity] = 56,[Rooms_Sold] = 55,[Rooms_Not_Avail_Maint] = 1" +
                " ,[Rooms_Not_Avail_Other] = 1,[Arrivals] = 51,[Departures] = 52,[Cancellations] = 0" +
                " ,[No_Shows] = 0,[Room_Revenue] = 4950.00000,[Food_Revenue] = 275.00000,[Total_Revenue] = 5225.00000" +
                " ,[File_Metadata_ID] = 1,[Last_Updated_DTTM] = GETDATE(),[CreateDate] = GETDATE()" +
                " WHERE Occupancy_DT='" + localDate.plusDays(1) + "'" +
                " UPDATE [Total_Activity]" +
                " SET [Property_ID] = 6,[Occupancy_DT] = '" + localDate.plusDays(2) + "',[SnapShot_DTTM] = GETDATE()" +
                " ,[Total_Accom_Capacity] = 58,[Rooms_Sold] = 57,[Rooms_Not_Avail_Maint] = 1" +
                " ,[Rooms_Not_Avail_Other] = 1,[Arrivals] = 51,[Departures] = 52,[Cancellations] = 0" +
                " ,[No_Shows] = 0,[Room_Revenue] = 5130.00000,[Food_Revenue] = 285.00000,[Total_Revenue] = 5415.00000" +
                " ,[File_Metadata_ID] = 1,[Last_Updated_DTTM] = GETDATE(),[CreateDate] = GETDATE()" +
                " WHERE Occupancy_DT='" + localDate.plusDays(2) + "'" +
                " UPDATE [Total_Activity]" +
                " SET [Property_ID] = 6,[Occupancy_DT] = '" + localDate.plusDays(3) + "',[SnapShot_DTTM] = GETDATE()" +
                " ,[Total_Accom_Capacity] = 59,[Rooms_Sold] = 58,[Rooms_Not_Avail_Maint] = 1" +
                " ,[Rooms_Not_Avail_Other] = 1,[Arrivals] = 51,[Departures] = 52,[Cancellations] = 0" +
                " ,[No_Shows] = 0,[Room_Revenue] = 5220.00000,[Food_Revenue] = 290.00000,[Total_Revenue] = 5510.00000" +
                " ,[File_Metadata_ID] = 1,[Last_Updated_DTTM] = GETDATE(),[CreateDate] = GETDATE()" +
                " WHERE Occupancy_DT='" + localDate.plusDays(3) + "'");

    }

    private void populatePaceTotalActivityTable(StringBuilder query) {
        java.time.LocalDate localDate = LocalDateUtils.toJavaTimeLocalDate(startDate);

        query.append(" update PACE_Total_Activity set" +
                " Total_Accom_Capacity=55,Rooms_Sold=54,Room_Revenue=4140.00000," +
                " Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1" +
                " where Occupancy_DT='" + localDate.plusDays(1) + "'" +
                " and Business_Day_End_DT='" + localDate.plusDays(1) + "'" +
                " update PACE_Total_Activity set" +
                " Total_Accom_Capacity=56,Rooms_Sold=55,Room_Revenue=4230.00000," +
                " Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1" +
                " where Occupancy_DT='" + localDate.plusDays(2) + "'" +
                " and Business_Day_End_DT='" + localDate.plusDays(1) + "'" +
                " update PACE_Total_Activity set" +
                " Total_Accom_Capacity=58,Rooms_Sold=57,Room_Revenue=4410.00000," +
                " Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1" +
                " where Occupancy_DT='" + localDate.plusDays(3) + "'" +
                " and Business_Day_End_DT='" + localDate.plusDays(1) + "'" +
                " update PACE_Total_Activity set" +
                " Total_Accom_Capacity=59,Rooms_Sold=58,Room_Revenue=4500.00000," +
                " Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1" +
                " where Occupancy_DT='" + localDate.plusDays(2) + "'" +
                " and Business_Day_End_DT='" + localDate.plusDays(2) + "'" +
                " update PACE_Total_Activity set" +
                " Total_Accom_Capacity=60,Rooms_Sold=59,Room_Revenue=5140.00000," +
                " Rooms_Not_Avail_Maint=1,Rooms_Not_Avail_Other=1" +
                " where Occupancy_DT='" + localDate.plusDays(3) + "'" +
                " and Business_Day_End_DT='" + localDate.plusDays(2) + "'");
    }

    private void shouldValidatePickUpReportHotelLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1) + "','" + startDate.plusDays(2) + "','" + startDate.toString() + "','" + startDate.plusDays(3) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',0");
        assertPickUpReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportHotelLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate", reportData);
    }

    private void shouldValidatePickUpReportHotelLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.minusDays(8) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3',0");
        assertPickUpReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportHotelLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate", reportData);
    }

    private void assertPickUpReportDataAtHotelLevelWhenActivityDateIsBeyondAnalysisDate(String message, List<Object[]> reportData) {
        assertEquals(4, reportData.size(), message);
        assertTrue(reportData.get(0)[6].toString().startsWith("0"), message);
        assertTrue(reportData.get(1)[6].toString().startsWith("1"), message);
        assertTrue(reportData.get(2)[6].toString().startsWith("3"), message);
        assertTrue(reportData.get(3)[6].toString().startsWith("2"), message);
        assertTrue(reportData.get(0)[10].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[10].toString().startsWith("9.68"), message);
        assertTrue(reportData.get(2)[10].toString().startsWith("-4.2"), message);
        assertTrue(reportData.get(3)[10].toString().startsWith("-0.3"), message);
        assertTrue(reportData.get(0)[11].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[11].toString().startsWith("17.81"), message);
        assertTrue(reportData.get(2)[11].toString().startsWith("-8.02"), message);
        assertTrue(reportData.get(3)[11].toString().startsWith("-0.75"), message);
        assertTrue(reportData.get(0)[12].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[12].toString().startsWith("17.18"), message);
        assertTrue(reportData.get(2)[12].toString().startsWith("-7.73"), message);
        assertTrue(reportData.get(3)[12].toString().startsWith("-0.72"), message);
        assertTrue(reportData.get(0)[16].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[16].toString().startsWith("3.02"), message);
        assertTrue(reportData.get(2)[16].toString().startsWith("-0.20"), message);
        assertTrue(reportData.get(3)[16].toString().startsWith("0.33"), message);
        assertTrue(reportData.get(0)[17].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[17].toString().startsWith("163.54"), message);
        assertTrue(reportData.get(2)[17].toString().startsWith("-10.0"), message);
        assertTrue(reportData.get(3)[17].toString().startsWith("20.0"), message);
        assertTrue(reportData.get(0)[18].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[18].toString().startsWith("6.42"), message);
        assertTrue(reportData.get(2)[18].toString().startsWith("2.84"), message);
        assertTrue(reportData.get(3)[18].toString().startsWith("6.07"), message);
        assertTrue(reportData.get(0)[22].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[22].toString().startsWith("13.33"), message);
        assertTrue(reportData.get(2)[22].toString().startsWith("0.68"), message);
        assertTrue(reportData.get(3)[22].toString().startsWith("9.75"), message);
        assertTrue(reportData.get(0)[23].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[23].toString().startsWith("13.55"), message);
        assertTrue(reportData.get(2)[23].toString().startsWith("0.61"), message);
        assertTrue(reportData.get(3)[23].toString().startsWith("9.87"), message);
        assertTrue(reportData.get(0)[24].toString().startsWith("0.0"), message);
        assertTrue(reportData.get(1)[24].toString().startsWith("810.0"), message);
        assertTrue(reportData.get(2)[24].toString().startsWith("270.0"), message);
        assertTrue(reportData.get(3)[24].toString().startsWith("730.0"), message);
    }

    private CPDecisionBAROutput createCpDecisionBarOutput(int accomTypeId, LocalDate startDate, BigDecimal finalBar) {
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setPropertyId(6);
        output.setDecisionId(1);
        output.setProduct(tenantCrudService().find(Product.class, 1));
        output.setDecisionReasonTypeId(1);
        output.setAccomType(tenantCrudService().find(AccomType.class, accomTypeId));
        output.setArrivalDate(startDate);
        output.setLengthOfStay(1);
        output.setOptimalBAR(BigDecimal.ONE);
        output.setPrettyBAR(BigDecimal.ONE);
        output.setOverrideType(DecisionOverrideType.NONE);
        output.setFloorOverride(null);
        output.setCeilingOverride(null);
        output.setSpecificOverride(null);
        output.setFinalBAR(finalBar);
        output.setRoomsOnlyBAR(null);
        output.setPreviousBAR(null);
        output.setOptimalBarType(OptimalBarType.PRICE);
        return output;
    }

    @Test
    public void webRateCompetitorsRateValueSingleAccomTypeTest() {
        createTestData();
        double webRateValue = 99.9;

        createWebRateData(startDateOfWebRate, endDateOfWebRate, webrate_accom_type_id1, webRateValue);

        List<Object> ufn_get_web_competitors_rate = tenantCrudService().findByNativeQuery("select * from ufn_get_web_competitors_rate(" +
                propertyID + "," + comp1 + "," + masterAccomClassId + ",'" + startDateOfWebRate + "','" + startDateOfWebRate + "');");

        assertEquals(1, ufn_get_web_competitors_rate.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) ufn_get_web_competitors_rate.get(0))[3]).doubleValue(), 0.01);
        assertEquals(webrate_accom_type_id1, ((Integer) ((Object[]) ufn_get_web_competitors_rate.get(0))[5]).intValue());

        List<Object> result = assertWebRateDifferentialAsOfBusinessDate(webRateValue);
        assertEquals(1, result.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) result.get(0))[3]).doubleValue(), 0.01);
    }

    @Test
    public void webRateCompetitorsRateValueSingleAccomTypeWithDateRangeTest() {
        createTestData();
        double webRateValue = 98.9;

        createWebRateData(startDateOfWebRate, endDateOfWebRate, webrate_accom_type_id1, webRateValue);

        List<Object> ufn_get_web_competitors_rate = tenantCrudService().findByNativeQuery("select * from ufn_get_web_competitors_rate(" +
                propertyID + "," + comp1 + "," + masterAccomClassId + ",'" + startDateOfWebRate + "','" + endDateOfWebRate + "');");

        assertEquals(2, ufn_get_web_competitors_rate.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) ufn_get_web_competitors_rate.get(0))[3]).doubleValue(), 0.01);
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) ufn_get_web_competitors_rate.get(1))[3]).doubleValue(), 0.01);
        assertEquals(webrate_accom_type_id1, ((Integer) ((Object[]) ufn_get_web_competitors_rate.get(0))[5]).intValue());
        assertEquals(webrate_accom_type_id1, ((Integer) ((Object[]) ufn_get_web_competitors_rate.get(1))[5]).intValue());
    }

    @Test
    public void webRateCompetitorsRateValueMultipleAccomTypeTest() {
        createTestData();
        createAccomTypeTestData(startDateOfWebRate, endDateOfWebRate);
        double webRateValue = 97.9;

        createWebRateData(startDateOfWebRate, endDateOfWebRate, webrate_accom_type_id2, webRateValue);
        updateWebRateData(startDateOfWebRate, endDateOfWebRate);

        List<Object> ufn_get_web_competitors_rate = tenantCrudService().findByNativeQuery("select * from ufn_get_web_competitors_rate(" +
                propertyID + "," + comp1 + "," + masterAccomClassId + ",'" + startDateOfWebRate + "','" + startDateOfWebRate + "');");

        assertEquals(1, ufn_get_web_competitors_rate.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) ufn_get_web_competitors_rate.get(0))[3]).doubleValue(), 0.01);
        assertEquals(webrate_accom_type_id2, ((Integer) ((Object[]) ufn_get_web_competitors_rate.get(0))[5]).intValue());

        List<Object> result = assertWebRateDifferentialAsOfBusinessDate(webRateValue);
        assertEquals(1, result.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) result.get(0))[3]).doubleValue(), 0.01);
    }

    @Test
    public void webRateCompetitorsRateValueMultipleAccomTypeWithDateRangeTest() {
        createTestData();
        createAccomTypeTestData(startDateOfWebRate, endDateOfWebRate);
        double webRateValue = 97.9;

        createWebRateData(startDateOfWebRate, endDateOfWebRate, webrate_accom_type_id2, webRateValue);
        updateWebRateData(startDateOfWebRate, endDateOfWebRate);

        List<Object> result = tenantCrudService().findByNativeQuery("select * from ufn_get_web_competitors_rate(" +
                propertyID + "," + comp1 + "," + masterAccomClassId + ",'" + startDateOfWebRate + "','" + endDateOfWebRate + "');");

        assertEquals(2, result.size());
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) result.get(0))[3]).doubleValue(), 0.01);
        assertEquals(webRateValue, ((BigDecimal) ((Object[]) result.get(1))[3]).doubleValue(), 0.01);
        assertEquals(webrate_accom_type_id2, ((Integer) ((Object[]) result.get(0))[5]).intValue());
        assertEquals(webrate_accom_type_id2, ((Integer) ((Object[]) result.get(1))[5]).intValue());
    }

    private List<Object> assertWebRateDifferentialAsOfBusinessDate(double webrateRateValue) {
        String occupancyDT = startDate.plusDays(6).toString();
        populatePaceWebrateDifferential(4);
        updatePaceDifferential(webrateRateValue, occupancyDT);
        updatePaceWebRateDifferentialData(startDate.minusDays(8).toString(), startDate.plusDays(6).toString());

        List<Object> result = tenantCrudService().findByNativeQuery("select * from ufn_get_webrate_differential_asof_businessdate(" +
                propertyID + "," + masterAccomClassId + "," + comp1 + ",'" + startDate.minusDays(8) + "','" + startDate.plusDays(6) + "','" + startDate.toString() + "');");
        return result;
    }

    private void updatePaceDifferential(double webrateRateValue, String occupancydate) {
        String updatePaceWebrateQuery = "update PACE_Webrate_Differential set Webrate_RateValue=" + webrateRateValue + ", Webrate_RateValue_Display = " + webrateRateValue + " where Occupancy_dt = '" + occupancydate + "' " +
                "and webrate_competitors_ID=" + 5 + " and Webrate_Channel_ID=" + 5 + " and Webrate_Accom_Type_ID in (" + 4 + ") ";
        tenantCrudService().executeUpdateByNativeQuery(updatePaceWebrateQuery);

    }

    private void populatePaceWebrateDifferential(int webrateAccomTypeID) {
        String insertQuery = "INSERT INTO [PACE_Webrate_Differential]([Webrate_Source_Property_ID],[First_Webrate_GenerationDate],[Webrate_GenerationDate],[Webrate_count],[Webrate_Competitors_ID],[Webrate_Channel_ID],[Webrate_Accom_Type_ID]," +
                "[Webrate_Type_ID],[Occupancy_DT],[LOS],[Webrate_Status],[Webrate_Currency],[Webrate_RateValue],[Webrate_Rank],[Webrate_Rating],[Last_Updated_DTTM],[Webrate_RateValue_Display])" +
                "values (2,'" + startDate.minusDays(8) + "','" + startDate.minusDays(8) + "',1,5,5," + webrateAccomTypeID + ",1,'" + startDate.plusDays(6) + "',1,'C','USD',97.8762,' ',20,GETDATE(),91.8762)," +
                "(2,'" + startDate.minusDays(11) + "','" + startDate.minusDays(11) + " 22:59:59.999',1,5,5," + webrateAccomTypeID + ",1,'" + startDate.plusDays(6) + "',1,'A','USD',91.8762,' ',20,GETDATE(),91.8762)";

        tenantCrudService().executeUpdateByNativeQuery(insertQuery);
    }

    private void updateWebRateData(String startDate, String endDate) {
        String test = "update Webrate set Webrate_GenerationDate = GETDATE() where Occupancy_DT between '" + startDate + "' and '" + endDate + "' " +
                "and webrate_competitors_ID=5 and Webrate_Channel_ID=5 and Webrate_Accom_Type_ID in (5,4) and los = 1";

        tenantCrudService().executeUpdateByNativeQuery(test);
    }

    private void updatePaceWebRateDifferentialData(String startDate, String endDate) {
        String test = "update PACE_Webrate_Differential set Webrate_GenerationDate = GETDATE() where Occupancy_DT between '" + startDate + "' and '" + endDate + "' " +
                "and webrate_competitors_ID=5 and Webrate_Channel_ID=5 and Webrate_Accom_Type_ID in (5,4) and los = 1";

        tenantCrudService().executeUpdateByNativeQuery(test);
    }

    private void createAccomTypeTestData(String startDate, String endDate) {
        tenantCrudService().executeUpdateByNativeQuery("SET IDENTITY_INSERT [Webrate_Accom_Type] ON  INSERT INTO Webrate_Accom_Type(Webrate_Accom_Type_ID,Property_ID,Webrate_Accom_Name,Webrate_Accom_Alias,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)\n" +
                "     VALUES(5,6,'test_2','test_2',1,getdate(),1,getdate()) SET IDENTITY_INSERT [Webrate_Accom_Type] OFF");
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Accom_Class_Mapping set Accom_Class_ID =" +
                masterAccomClassId);
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Webrate_Accom_Class_Mapping" +
                "           (Webrate_Accom_Type_ID,Accom_Class_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)" +
                "     VALUES(5," + masterAccomClassId + ",1,getdate(),1,getdate())");

        tenantCrudService().executeUpdateByNativeQuery("insert into Webrate values(2,GetDate(), 5, 5,5, 1, '" + startDate + "'," + 1 + ",'TestData','" + 'A' + "','USD', '100.00', 1, 1, 1, getdate(), -1, '100.00')");
        tenantCrudService().executeUpdateByNativeQuery("insert into Webrate values(2,GetDate(), 5, 5,5, 1, '" + endDate + "'," + 1 + ",'TestData','" + 'A' + "','USD', '101.00', 1, 1, 1, getdate(), -1, '101.00')");
    }

    private void createWebRateData(final String startDate, final String endDate, int webRateAccomTypeId, double webRateValue) {
        String qry = "update Webrate set Webrate_Status = '" + "A" + "', Webrate_RateValue = " + webRateValue + ", Webrate_RateValue_Display = " + webRateValue + " where webrate_accom_type_id = " + webRateAccomTypeId + " " +
                "and Occupancy_DT between '" + startDate + "' and '" + endDate + "' " +
                "and Webrate_Channel_ID = (select Channel_ID from Vw_Webrate_Channel where Occupancy_DT = Webrate.Occupancy_DT) " +
                "and Webrate_Competitors_ID =" + 5 + "";
        tenantCrudService().executeUpdateByNativeQuery(qry);
    }

    @Test
    public void shouldValidatePickUpReportHotelLevelFunctionForStaticDateAndSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        createTestData();
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), eventInstanceName);

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.minusDays(8) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY',0");
        assertPickUpReportDataAtHotelLevel("[shouldValidatePickUpReportHotelLevelFunctionForStaticDate] " + "Pickup Report at Hotel level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport - Test Instance");
    }

    private void updateSpecialEventInstanceNameOfTestSpecialEventData(String eventStartDate, String eventEndDate, String specialEventInstanceName) {
        List<PropertySpecialEvent> propertySpecialEvents = getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(eventStartDate, eventEndDate);
        PropertySpecialEvent propertySpecialEvent = propertySpecialEvents.get(0);
        PropertySpecialEventInstance propertySpecialEventInstance = propertySpecialEvent.getPropertySpecialEventIntances().iterator().next();
        propertySpecialEventInstance.setEventInstanceName(specialEventInstanceName);
        tenantCrudService().save(propertySpecialEvent);
    }

    private List<PropertySpecialEvent> getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(String eventStartDate, String eventEndDate) {
        String selectQuery = "select Property_Special_Event_ID from [Property_Special_Event] WHERE [Start_DTTM] = :Start_DTTM and [End_DTTM] = :End_DTTM and [Special_Event_Name] = :Special_Event_Name";
        List<Object> specialEventsIds = tenantCrudService().findByNativeQuery(selectQuery,
                MapBuilder.with("Start_DTTM", eventStartDate)
                        .and("End_DTTM", eventEndDate)
                        .and("Special_Event_Name", "SP_PickUPReport")
                        .get());

        Integer specialEventId = (Integer) specialEventsIds.get(0);
        return tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY, MapBuilder.with("propertyId", propertyID).and("specialEventId", specialEventId).get());
    }

    @Test
    public void shouldValidateChangeReportHotelLevelFunctionForStaticDatesAndSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        createTestData();
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), eventInstanceName);

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today','false',0,0");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevelFunctionForStaticDates] " + "Change Report at Hotel level with Static Dates", reportDataByStaticDates, "NonCP", "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport - Test Instance");
    }

    @Test
    public void shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDatesAndSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        createTestData();
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), eventInstanceName);
        insertDifferentialRows();

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByStaticDates, "CP", "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport - Test Instance");
        List<Object[]> reportDataByStaticDatesDiff = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_property_CP_diff " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11) + "','" + startDate.plusDays(6) + "','" + startDate.plusDays(6) + "'," + comp1 + "," + comp2 + "," + comp3 + ",null,null,null,null,null,null,null,null,null,null,null,null,0,'Today','Today','Today',0,0,1");
        assertChangeReportDataAtHotelLevel("[shouldValidateChangeReportHotelLevel_CPFunction_nonBarData_ForStaticDates] " + "Change Report (CP) at Hotel level with Static Dates", reportDataByStaticDatesDiff, "CP", "SP_PickUPReport - Test Instance");
    }

    private void insertDifferentialRows() {
        List<CPPaceDecisionBAROutput> paceData = tenantCrudService().findAll(CPPaceDecisionBAROutput.class);
        Set<Date> arrivalDates = paceData.stream()
                .map(CPPaceDecisionBAROutput::getArrivalDate).map(LocalDate::toDate).collect(Collectors.toSet());
        arrivalDates.forEach(arrivalDate -> {
            Map<String, Object> queryParams = QueryParameter.with("arrivalDate", arrivalDate)
                    .parameters();
            tenantCrudService().executeUpdateByNativeQuery(INSERT_MIGRATION_CP_PACE_BAR_OUTPUT_DIFFERENTIAL, queryParams);
        });
    }
}
