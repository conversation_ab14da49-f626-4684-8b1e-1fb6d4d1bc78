package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomTypeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.service.RoomTypeVendorMappingService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputKey;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.g3.data.TestProperty.H2;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext.createOutputs;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext.generateRangeOfDatesList;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createAdultPackage;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createChildPackage;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createMockCpDecisionContext;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createProductRateOffsets;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createSetPercentPackage;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.precisionTwoValueOf;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaToJodaLocalDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class CPRecommendationServiceTest extends AbstractCPServiceTest {
    private static final List<String> NO_ROOMS = Collections.emptyList();
    private static final List<String> ROOM_A_ROOM_B = Arrays.asList("A", "B");
    private static final String RT_STE = "STE";
    private CPRecommendationService service = new CPRecommendationService();

    @Captor
    ArgumentCaptor<List<DecisionDailybarOutput>> upgradedDecisions;

    @Override
    CPBarDecisionService getCPBarDecisionService() {
        return service;
    }

    @Override
    protected boolean isAgileRatesEnabled() {
        return false;
    }

    @Override
    protected boolean isPerPersonEnabled() {
        return false;
    }

    @Override
    protected boolean isIndependentProductsEnabled() {
        return false;
    }

    @Test
    public void recommendBARsChangesFinaBARDueToSyncFlagChangesEvenThoughFinalBARWouldNotChangeDueToMinimumIncrement() {
        //Enable a full refresh
        service.updateEnableFullRefresh(true);

        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("30.00"), new BigDecimal("30.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        DecisionDailybarOutput decisionDailybarOutput = findDecisionDailybarOutputs().get(0);

        assertEquals(new BigDecimal("19.87"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendBARsDoesNotChangeFinalBARDueToMinimumIncrement() {
        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("30.00"), new BigDecimal("30.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        DecisionDailybarOutput decisionDailybarOutput = findDecisionDailybarOutputs().get(0);

        assertEquals(new BigDecimal("30.00"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendBARsShouldNotGenerateDecisionsForZeroCapacityRTs() {
        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Type_Capacity=0 where Accom_Type_Code='D'");
        when(hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms()).thenReturn(Collections.singletonList("D"));
        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);
        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("30.00"), new BigDecimal("30.00"), BigDecimal.ZERO, BigDecimal.ZERO);
        addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        assertEquals(1, findDecisionDailybarOutputs().size());

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        assertEquals(0, findDecisionDailybarOutputs().size());
    }

    @Test
    public void recommendFinalBARs_WithNoCPDecisionOutputs() {
        // Attempt to create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextNextDecisionID, startDate, endDate);

        // Verify no decisions got created
        assertEquals(0, findDecisionDailybarOutputs().size());
    }

    @Test
    public void recommendFinalBARs_NewContinuousPricingDecision() {
        BigDecimal optimalBAR = new BigDecimal("100.25");

        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(optimalBAR, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
        assertNull(decisionDailybarOutput.getTripleRate());
        assertNull(decisionDailybarOutput.getQuadRate());
        assertNull(decisionDailybarOutput.getQuintRate());
        assertNull(decisionDailybarOutput.getOneChildRate());
        assertNull(decisionDailybarOutput.getTwoChildRate());
        assertNull(decisionDailybarOutput.getThreeChildRate());
        assertNull(decisionDailybarOutput.getFourChildRate());
        assertNull(decisionDailybarOutput.getFiveChildRate());
        assertNull(decisionDailybarOutput.getChildAgeOneRate());
        assertNull(decisionDailybarOutput.getChildAgeTwoRate());
        assertNull(decisionDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_NewContinuousPricingDecisionWithPPPConfigurationsWithPPPNotEnabled() {
        reset(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value())).thenReturn(false);

        OccupantBucketEntity childAgeBucket1 = new OccupantBucketEntity();
        childAgeBucket1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        tenantCrudService().save(childAgeBucket1);

        OccupantBucketEntity childAgeBucket2 = new OccupantBucketEntity();
        childAgeBucket2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        tenantCrudService().save(childAgeBucket2);

        OccupantBucketEntity childAgeBucket3 = new OccupantBucketEntity();
        childAgeBucket3.setOccupancyType(OccupancyType.CHILD_BUCKET_3);
        tenantCrudService().save(childAgeBucket3);

        BigDecimal optimalBAR = new BigDecimal("100.25");

        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.THREE_ADULTS, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.FOUR_ADULTS, OffsetMethod.FIXED_OFFSET, new BigDecimal("11.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.FIVE_ADULTS, OffsetMethod.FIXED_OFFSET, new BigDecimal("12.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.ONE_CHILD, OffsetMethod.FIXED_OFFSET, new BigDecimal("1.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.TWO_CHILDREN, OffsetMethod.FIXED_OFFSET, new BigDecimal("2.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.THREE_CHILDREN, OffsetMethod.FIXED_OFFSET, new BigDecimal("3.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.FOUR_CHILDREN, OffsetMethod.FIXED_OFFSET, new BigDecimal("4.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.FIVE_CHILDREN, OffsetMethod.FIXED_OFFSET, new BigDecimal("5.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_OFFSET, new BigDecimal("20.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_OFFSET, new BigDecimal("21.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_OFFSET, new BigDecimal("22.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(optimalBAR, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
        assertNull(decisionDailybarOutput.getTripleRate());
        assertNull(decisionDailybarOutput.getQuadRate());
        assertNull(decisionDailybarOutput.getQuintRate());
        assertNull(decisionDailybarOutput.getOneChildRate());
        assertNull(decisionDailybarOutput.getTwoChildRate());
        assertNull(decisionDailybarOutput.getThreeChildRate());
        assertNull(decisionDailybarOutput.getFourChildRate());
        assertNull(decisionDailybarOutput.getFiveChildRate());
        assertNull(decisionDailybarOutput.getChildAgeOneRate());
        assertNull(decisionDailybarOutput.getChildAgeTwoRate());
        assertNull(decisionDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_NewContinuousPricingDecisionWithChildAgeBuckets() {
        reset(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value())).thenReturn(true);

        OccupantBucketEntity childAgeBucket1 = new OccupantBucketEntity();
        childAgeBucket1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        tenantCrudService().save(childAgeBucket1);

        OccupantBucketEntity childAgeBucket2 = new OccupantBucketEntity();
        childAgeBucket2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        tenantCrudService().save(childAgeBucket2);

        OccupantBucketEntity childAgeBucket3 = new OccupantBucketEntity();
        childAgeBucket3.setOccupancyType(OccupancyType.CHILD_BUCKET_3);
        tenantCrudService().save(childAgeBucket3);

        BigDecimal optimalBAR = new BigDecimal("100.25");

        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_OFFSET, new BigDecimal("20.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_OFFSET, new BigDecimal("21.00"));
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_OFFSET, new BigDecimal("22.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(optimalBAR, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
        assertNull(decisionDailybarOutput.getTripleRate());
        assertNull(decisionDailybarOutput.getQuadRate());
        assertNull(decisionDailybarOutput.getQuintRate());
        assertNull(decisionDailybarOutput.getOneChildRate());
        assertNull(decisionDailybarOutput.getTwoChildRate());
        assertNull(decisionDailybarOutput.getThreeChildRate());
        assertNull(decisionDailybarOutput.getFourChildRate());
        assertNull(decisionDailybarOutput.getFiveChildRate());
        assertBigDecimalEquals(new BigDecimal("20.00"), decisionDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("21.00"), decisionDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("22.00"), decisionDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_CreateNewContinuousPricingDecisionWithOffsets() {
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("100.25"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("1.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("1.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_CreateNewContinuousPricingDecisionWithSeasonOnlyOffsets() {
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(RT_QUEEN, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("100.25"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("100.25"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("10.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_UnchangingDecisionDoesNotChangeDailybarOutput() {
        BigDecimal optimalBAR = new BigDecimal("100.25");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN, false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, optimalBAR, optimalBAR, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the decision id didn't change
        assertEquals(previousDecisionId, decisionDailybarOutputs.get(0).getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangingDecisionDoesChangeDailybarOutput() {
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal dailyBARExisting = new BigDecimal("100.00");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20.00"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the decision id didn't change
        assertEquals(previousDecisionId, decisionDailybarOutputs.get(0).getDecisionId());

        // Verify DecisionDailybarOutput did not change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(dailyBARExisting, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendFinalBARs_ChangingPriceDecisionDoesNotChangeDailybarOutputDueToMinimumIncrement() {
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the decision id didn't change
        assertEquals(previousDecisionId, decisionDailybarOutputs.get(0).getDecisionId());

        // Verify Decision Value did not change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(dailyBARExisting, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendFinalBARs_ChangingPriceDecisionDoesChangeDailybarOutputDueToCeiling() {
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal dailyBARExisting = new BigDecimal("105.25");
        BigDecimal ceilingOverride = new BigDecimal("102.00");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record - Pretty BAR is the ceiling
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR, ceilingOverride, null, ceilingOverride, null);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the decision id didn't change
        assertEquals(nextDecisionId, decisionDailybarOutputs.get(0).getDecisionId());

        // Verify Decision Value did not change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(ceilingOverride, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendFinalBARs_ChangingPriceDecisionDoesChangeDailybarOutputDueToFloor() {
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal dailyBARExisting = new BigDecimal("105.25");
        BigDecimal floorOverride = new BigDecimal("107.00");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record - Pretty BAR is the floor
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR, floorOverride, null, null, floorOverride);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the decision id didn't change
        assertEquals(nextDecisionId, decisionDailybarOutputs.get(0).getDecisionId());

        // Verify Decision Value did not change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(floorOverride, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
    }

    @Test
    public void recommendFinalBARs_UserSpecifiedPriceChangesDecisionRegardlessOfMinimumIncrement() {
        BigDecimal userSpecifiedOverride = new BigDecimal("101.00");
        BigDecimal dailybarOutputBAR = new BigDecimal("105.25");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, userSpecifiedOverride, userSpecifiedOverride, userSpecifiedOverride);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailybarOutputBAR, dailybarOutputBAR.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify daily bar results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(userSpecifiedOverride, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("101.00"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));

        // Verify the Pace record
        assertEquals(1, findCountPaceDailybarOutput(decisionDailybarOutput.getDecisionId()).intValue());
        List<PaceDailyBarOutput> PaceDailybarOutputs = findPaceDailyBarOutputs();
        PaceDailyBarOutput paceDailyBarOutput = PaceDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, paceDailyBarOutput.getDecisionId());
        assertEquals(nextDecisionId, paceDailyBarOutput.getDecisionId());
        assertEquals(startDate, paceDailyBarOutput.getOccupancyDate());
        assertEquals(RT_QUEEN, paceDailyBarOutput.getAccomType().getAccomTypeCode());
        assertEquals(userSpecifiedOverride, BigDecimalUtil.round(paceDailyBarOutput.getSingleRate(), 2));
        assertEquals(userSpecifiedOverride, BigDecimalUtil.round(paceDailyBarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.round(paceDailyBarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.round(paceDailyBarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_UserSpecifiedPriceChangesDecisionOnlyWhenPriceActuallyChanges() {
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal userSpecifiedOverride = new BigDecimal("101.00");
        BigDecimal dailybarOutputBAR = new BigDecimal("105.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR, optimalBAR, userSpecifiedOverride);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailybarOutputBAR, dailybarOutputBAR.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Make call to recommendation service again
        nextDecisionId = Integer.valueOf(nextDecisionId.intValue() + 1);
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Shouldn't have any Decision_Dailybar_Output records for latest decision
        assertEquals(0, findCountDecisionDailybarOutput(nextDecisionId).intValue());
    }

    @Test
    public void recommendFinalBARs_ChangingPriceDecisionChangeDailybarOutputDueToMinimumIncrement() {
        BigDecimal optimalBAR = new BigDecimal("115.25");
        BigDecimal dailybarOutputBAR = new BigDecimal("105.25");

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailybarOutputBAR, dailybarOutputBAR.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(optimalBAR, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutputs.get(0).getDecisionId());
    }

    @Test
    public void recommendFinalBARs_DoNotChangeDailybarOutputDueToAccomClassHierarchy() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("128.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("101.00"), new BigDecimal("116.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("98.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("99.00"));


        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("98.00"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("99.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangeDailybarOutputDueToAccomClassHierarchy() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("115.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("115.00"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangeDailybarOutputDueToAccomClassHierarchyLinearPath() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);
        updatePricingAccomClassData(RT_KING, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        //Add extra accom classes to create an advanced path.
        AccomClass imperial = new AccomClass(null, 100, new BigDecimal("50.0"));
        imperial.setName("Imperial");
        imperial.setCode("IMP");
        imperial.setPropertyId(H2.getId());
        imperial.setStatusId(Status.ACTIVE.getId());
        imperial.setSystemDefault(0);
        AccomClass superImperial = new AccomClass(null, 101, new BigDecimal("50.0"));
        superImperial.setName("Super Imperial");
        superImperial.setCode("SP_IMP");
        superImperial.setPropertyId(H2.getId());
        superImperial.setSystemDefault(0);
        superImperial.setStatusId(Status.ACTIVE.getId());
        AccomClass deluxe = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "Deluxe").and("propertyId", H2.getId()).parameters());
        AccomClass stn = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "STN").and("propertyId", H2.getId()).parameters());

        tenantCrudService().save(imperial);
        tenantCrudService().save(superImperial);

        tenantCrudService().flush();

        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        priceRank2.setLowerRankAccomClass(deluxe);
        priceRank2.setHigherRankAccomClass(imperial);

        AccomClassPriceRank priceRank3 = new AccomClassPriceRank();
        priceRank3.setLowerRankAccomClass(imperial);
        priceRank3.setHigherRankAccomClass(superImperial);

        tenantCrudService().save(priceRank2);
        tenantCrudService().save(priceRank3);

        AccomType k = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "k").parameters());
        AccomType d = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "D").parameters());
        AccomType qn = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "QN").parameters());
        AccomType ste = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "STE").parameters());

        d.setAccomClass(stn);
        ste.setAccomClass(deluxe);
        k.setAccomClass(superImperial);
        qn.setAccomClass(imperial);
        tenantCrudService().save(Arrays.asList(k, d, qn, ste));

        PricingAccomClass imperialPricingAccomClass = new PricingAccomClass();
        imperialPricingAccomClass.setAccomClass(imperial);
        imperialPricingAccomClass.setAccomType(qn);
        imperialPricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass superImperialPricingAccomClass = new PricingAccomClass();
        superImperialPricingAccomClass.setAccomClass(superImperial);
        superImperialPricingAccomClass.setAccomType(k);
        superImperialPricingAccomClass.setPropertyId(H2.getId());

        tenantCrudService().save(imperialPricingAccomClass);
        tenantCrudService().save(superImperialPricingAccomClass);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_KING, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);


        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_KING, startDate, new BigDecimal("114.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("115.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput1 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_KING)).findFirst().get();
        assertEquals(new BigDecimal("114.00"), BigDecimalUtil.round(decisionDailybarOutput1.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("115.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("116.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput4 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput4.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput1.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput3.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput4.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangeDailybarOutputDueToAccomClassHierarchyNonLinearPath() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);
        updatePricingAccomClassData(RT_KING, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        //Add extra accom classes to create an advanced path.
        AccomClass imperial = new AccomClass(null, 100, new BigDecimal("50.0"));
        imperial.setName("Imperial");
        imperial.setCode("IMP");
        imperial.setPropertyId(H2.getId());
        imperial.setStatusId(Status.ACTIVE.getId());
        imperial.setSystemDefault(0);
        AccomClass superImperial = new AccomClass(null, 101, new BigDecimal("50.0"));
        superImperial.setName("Super Imperial");
        superImperial.setCode("SP_IMP");
        superImperial.setPropertyId(H2.getId());
        superImperial.setSystemDefault(0);
        superImperial.setStatusId(Status.ACTIVE.getId());
        AccomClass deluxe = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "Deluxe").and("propertyId", H2.getId()).parameters());
        AccomClass stn = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "STN").and("propertyId", H2.getId()).parameters());

        tenantCrudService().save(imperial);
        tenantCrudService().save(superImperial);

        tenantCrudService().deleteAll(AccomClassPriceRank.class);
        tenantCrudService().flush();

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        priceRank1.setLowerRankAccomClass(stn);
        priceRank1.setHigherRankAccomClass(deluxe);

        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        priceRank2.setLowerRankAccomClass(stn);
        priceRank2.setHigherRankAccomClass(imperial);

        AccomClassPriceRank priceRank3 = new AccomClassPriceRank();
        priceRank3.setLowerRankAccomClass(imperial);
        priceRank3.setHigherRankAccomClass(superImperial);

        AccomClassPriceRank priceRank4 = new AccomClassPriceRank();
        priceRank4.setLowerRankAccomClass(deluxe);
        priceRank4.setHigherRankAccomClass(superImperial);

        tenantCrudService().save(priceRank1);
        tenantCrudService().save(priceRank2);
        tenantCrudService().save(priceRank3);
        tenantCrudService().save(priceRank4);

        AccomType k = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "k").parameters());
        AccomType d = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "D").parameters());
        AccomType qn = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "QN").parameters());
        AccomType ste = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "STE").parameters());

        d.setAccomClass(stn);
        ste.setAccomClass(deluxe);
        k.setAccomClass(superImperial);
        qn.setAccomClass(imperial);
        tenantCrudService().save(Arrays.asList(k, d, qn, ste));

        PricingAccomClass imperialPricingAccomClass = new PricingAccomClass();
        imperialPricingAccomClass.setAccomClass(imperial);
        imperialPricingAccomClass.setAccomType(qn);
        imperialPricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass superImperialPricingAccomClass = new PricingAccomClass();
        superImperialPricingAccomClass.setAccomClass(superImperial);
        superImperialPricingAccomClass.setAccomType(k);
        superImperialPricingAccomClass.setPropertyId(H2.getId());

        tenantCrudService().save(imperialPricingAccomClass);
        tenantCrudService().save(superImperialPricingAccomClass);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_KING, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);


        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_KING, startDate, new BigDecimal("114.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("115.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // The decision for suite did not meet the minimum change difference, but since not acceoting the decision would violate the hierarcy, we must accept the decision for suite as well.
        DecisionDailybarOutput decisionDailybarOutput1 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_KING)).findFirst().get();
        assertEquals(new BigDecimal("114.00"), BigDecimalUtil.round(decisionDailybarOutput1.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("115.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("116.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput4 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput4.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput1.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput3.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput4.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangeDailybarOutputDueToAccomClassHierarchyLinearPath_ManyToOneAccomTypesToAccomClass() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);
        updatePricingAccomClassData(RT_KING, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        //Add extra accom classes to create an advanced path.
        AccomClass imperial = new AccomClass(null, 100, new BigDecimal("50.0"));
        imperial.setName("Imperial");
        imperial.setCode("IMP");
        imperial.setPropertyId(H2.getId());
        imperial.setStatusId(Status.ACTIVE.getId());
        imperial.setSystemDefault(0);

        AccomClass deluxe = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "Deluxe").and("propertyId", H2.getId()).parameters());
        AccomClass stn = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "STN").and("propertyId", H2.getId()).parameters());

        tenantCrudService().save(imperial);

        tenantCrudService().deleteAll(AccomClassPriceRank.class);
        tenantCrudService().flush();

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        priceRank1.setLowerRankAccomClass(stn);
        priceRank1.setHigherRankAccomClass(deluxe);

        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        priceRank2.setLowerRankAccomClass(deluxe);
        priceRank2.setHigherRankAccomClass(imperial);

        tenantCrudService().save(priceRank1);
        tenantCrudService().save(priceRank2);

        AccomType k = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "k").parameters());
        AccomType d = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "D").parameters());
        AccomType qn = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "QN").parameters());
        AccomType ste = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "STE").parameters());

        k.setAccomClass(stn);
        d.setAccomClass(deluxe);
        ste.setAccomClass(deluxe);
        qn.setAccomClass(imperial);
        tenantCrudService().save(Arrays.asList(k, d, qn, ste));

        tenantCrudService().deleteAll(PricingAccomClass.class);

        PricingAccomClass suitePricingAccomClass = new PricingAccomClass();
        suitePricingAccomClass.setAccomClass(deluxe);
        suitePricingAccomClass.setAccomType(ste);
        suitePricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass queenPricingAccomClass = new PricingAccomClass();
        queenPricingAccomClass.setAccomClass(imperial);
        queenPricingAccomClass.setAccomType(qn);
        queenPricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass kingPricingAccomClass = new PricingAccomClass();
        kingPricingAccomClass.setAccomClass(stn);
        kingPricingAccomClass.setAccomType(k);
        kingPricingAccomClass.setPropertyId(H2.getId());

        tenantCrudService().save(suitePricingAccomClass);
        tenantCrudService().save(kingPricingAccomClass);
        tenantCrudService().save(queenPricingAccomClass);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_KING, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);


        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_KING, startDate, new BigDecimal("114.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("115.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // Verify Decision Values changed
        DecisionDailybarOutput decisionDailybarOutput1 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_KING)).findFirst().get();
        assertEquals(new BigDecimal("114.00"), BigDecimalUtil.round(decisionDailybarOutput1.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("115.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("116.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput4 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput4.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput1.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput3.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput4.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_DoNotChangeDailybarOutput_AccomClassHierarchyLinearPathIsValid_ManyToOneAccomTypesToAccomClass() {

        //Add extra accom classes to create an advanced path.
        AccomClass imperial = new AccomClass(null, 100, new BigDecimal("50.0"));
        imperial.setName("Imperial");
        imperial.setCode("IMP");
        imperial.setPropertyId(H2.getId());
        imperial.setStatusId(Status.ACTIVE.getId());
        imperial.setSystemDefault(0);

        AccomClass deluxe = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "Deluxe").and("propertyId", H2.getId()).parameters());
        AccomClass stn = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "STN").and("propertyId", H2.getId()).parameters());

        tenantCrudService().save(imperial);

        tenantCrudService().deleteAll(AccomClassPriceRank.class);
        tenantCrudService().flush();

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        priceRank1.setLowerRankAccomClass(stn);
        priceRank1.setHigherRankAccomClass(deluxe);

        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        priceRank2.setLowerRankAccomClass(deluxe);
        priceRank2.setHigherRankAccomClass(imperial);

        tenantCrudService().save(priceRank1);
        tenantCrudService().save(priceRank2);

        AccomType k = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "k").parameters());
        AccomType d = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "D").parameters());
        AccomType qn = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "QN").parameters());
        AccomType ste = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "STE").parameters());

        k.setAccomClass(stn);
        d.setAccomClass(deluxe);
        ste.setAccomClass(deluxe);
        qn.setAccomClass(imperial);
        tenantCrudService().save(Arrays.asList(k, d, qn, ste));

        tenantCrudService().deleteAll(PricingAccomClass.class);

        PricingAccomClass suitePricingAccomClass = new PricingAccomClass();
        suitePricingAccomClass.setAccomClass(deluxe);
        suitePricingAccomClass.setAccomType(ste);
        suitePricingAccomClass.setMinimumIncrementMethod(MinimumIncrementMethod.FIXED_OFFSET);
        suitePricingAccomClass.setMinimumIncrementValue(new BigDecimal("50.00"));
        suitePricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass queenPricingAccomClass = new PricingAccomClass();
        queenPricingAccomClass.setAccomClass(imperial);
        queenPricingAccomClass.setAccomType(qn);
        queenPricingAccomClass.setMinimumIncrementMethod(MinimumIncrementMethod.FIXED_OFFSET);
        queenPricingAccomClass.setMinimumIncrementValue(new BigDecimal("1.00"));
        queenPricingAccomClass.setPropertyId(H2.getId());

        PricingAccomClass kingPricingAccomClass = new PricingAccomClass();
        kingPricingAccomClass.setAccomClass(stn);
        kingPricingAccomClass.setAccomType(k);
        kingPricingAccomClass.setMinimumIncrementMethod(MinimumIncrementMethod.FIXED_OFFSET);
        kingPricingAccomClass.setMinimumIncrementValue(new BigDecimal("1.00"));
        kingPricingAccomClass.setPropertyId(H2.getId());

        tenantCrudService().save(suitePricingAccomClass);
        tenantCrudService().save(kingPricingAccomClass);
        tenantCrudService().save(queenPricingAccomClass);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_KING, startDate, new BigDecimal("105.00"), new BigDecimal("109.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("115.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);


        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_KING, startDate, new BigDecimal("109.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("115.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(4, decisionDailybarOutputs.size());

        // The decisions for DOUBLE and SUITE did not meet the minimum change values. Since they also do not violate the accom class hierarcy by not accepting them, we have not accepted these decisions.
        DecisionDailybarOutput decisionDailybarOutput1 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_KING)).findFirst().get();
        assertEquals(new BigDecimal("109.00"), BigDecimalUtil.round(decisionDailybarOutput1.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("110.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput4 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("110.00"), BigDecimalUtil.round(decisionDailybarOutput4.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput1.getDecisionId());
        assertEquals(previousDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput3.getDecisionId());
        assertEquals(previousDecisionId, decisionDailybarOutput4.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_ChangeDailybarOutputDueToAccomClassHierarchy_hasTwoRTsInSameRoomClass() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("101.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("115.00"));
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("115.00"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("116.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("117.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        // Verify the decision id did increment
        assertEquals(nextDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(nextDecisionId, decisionDailybarOutput3.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_DontChangeDailybarOutputDueToMinChangeIncrement_hasTwoRTsInSameRoomClass() {

        updatePricingAccomClassData(RT_QUEEN, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);
        updatePricingAccomClassData(RT_SUITE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("50"), false);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, new BigDecimal("100.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("101.00"), new BigDecimal("105.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_SUITE, startDate, new BigDecimal("110.00"), new BigDecimal("115.00"), BigDecimal.TEN, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, new BigDecimal("115.00"));
        addCPDecisionBAROutput(RT_DOUBLE, startDate, new BigDecimal("116.00"));

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_SUITE, startDate, new BigDecimal("117.00"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify Decision Value did change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_QUEEN)).findFirst().get();
        assertEquals(new BigDecimal("100.00"), BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput2 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_DOUBLE)).findFirst().get();
        assertEquals(new BigDecimal("101.00"), BigDecimalUtil.round(decisionDailybarOutput2.getSingleRate(), 2));

        DecisionDailybarOutput decisionDailybarOutput3 = decisionDailybarOutputs.stream().filter(output -> output.getAccomType().getAccomTypeCode().equals(RT_SUITE)).findFirst().get();
        assertEquals(new BigDecimal("110.00"), BigDecimalUtil.round(decisionDailybarOutput3.getSingleRate(), 2));

        // Verify the decision id did not increment
        assertEquals(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(previousDecisionId, decisionDailybarOutput2.getDecisionId());
        assertEquals(previousDecisionId, decisionDailybarOutput3.getDecisionId());
    }

    @Test
    public void recommendFinalBARs_CreateNewContinuousPricingDecisionWithPercentOffsets() {
        setRoundingRule(9, 8, 7);

        String accomTypeCode = "STE";
        BigDecimal optimalBAR = new BigDecimal("100.00");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Add offsets
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal(110));
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal(20));
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal(20));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(optimalBAR, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("209.87"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("41.97"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("41.97"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_createNewContinuousPricingDecisionWithPercentOffsetsValidatesRounding() {
        setTax(new BigDecimal(10));
        setRoundingRule(null, 4, 0);

        String accomTypeCode = "STE";

        // Add a CPDecisionBAROutput record
        BigDecimal prettyBar = new BigDecimal("1232.40");
        addCPDecisionBAROutput(accomTypeCode, startDate, prettyBar);

        // Add offsets
        BigDecimal offsetPercent = new BigDecimal("2.2");
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, offsetPercent);

        // Add supplements
        addSupplement(accomTypeCode, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplement(accomTypeCode, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplement(accomTypeCode, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplement(accomTypeCode, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(prettyBar, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("1267.40"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("45.43"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("50.43"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_createNewContinuousPricingDecisionWithPercentOffsetsAndPercentSupplement_ValidatesRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);

        setTax(new BigDecimal(10));
        setRoundingRule(null, 4, 0);

        String accomTypeCode = "STE";

        // Add a CPDecisionBAROutput record
        BigDecimal prettyBar = new BigDecimal("1232.40");
        addCPDecisionBAROutput(accomTypeCode, startDate, prettyBar);

        // Add offsets
        BigDecimal offsetPercent = new BigDecimal("2.2");
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, offsetPercent);

        // Add supplements
        addSupplementPercent(accomTypeCode, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(accomTypeCode, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(accomTypeCode, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(accomTypeCode, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(prettyBar, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("1322.40"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("213.78"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("275.34"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_createNewContinuousPricingDecisionWithPercentOffsets_FixedAndPercentSupplement_ValidatesRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);

        setTax(new BigDecimal(10));
        setRoundingRule(null, 4, 0);

        String accomTypeCode = "STE";

        // Add a CPDecisionBAROutput record
        BigDecimal prettyBar = new BigDecimal("1232.40");
        addCPDecisionBAROutput(accomTypeCode, startDate, prettyBar);

        // Add offsets
        BigDecimal offsetPercent = new BigDecimal("2.2");
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, offsetPercent);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, offsetPercent);

        // Add supplements
        addSupplementPercent(accomTypeCode, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(accomTypeCode, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplement(accomTypeCode, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplement(accomTypeCode, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertEquals(prettyBar, BigDecimalUtil.round(decisionDailybarOutput.getSingleRate(), 2));
        assertEquals(new BigDecimal("1322.40"), BigDecimalUtil.round(decisionDailybarOutput.getDoubleRate(), 2));
        assertEquals(new BigDecimal("44.09"), BigDecimalUtil.round(decisionDailybarOutput.getAdultRate(), 2));
        assertEquals(new BigDecimal("49.09"), BigDecimalUtil.round(decisionDailybarOutput.getChildRate(), 2));
    }

    @Test
    public void recommendFinalBARs_CreateNewContinuousPricingDecisionWithPercentOffsetsRoundsDouble() {
        String accomTypeCode = "STE";
        BigDecimal optimalBAR = new BigDecimal("100");

        setRoundingRule(null, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Add offsets
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal(5.5));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(optimalBAR, decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("105.99"), decisionDailybarOutput.getDoubleRate());
    }

    @Test
    public void recommendFinalBARs_UploadOnlyOneRoomTypesChangedDecisionWhenOnlyOnceSatisfiesMinIncrement() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        BigDecimal optimalBAR_QN = new BigDecimal("115.25");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("116.00");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        updatePricingAccomClassData(accomTypeCodeOne, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, startDate, optimalBAR_STE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCodeOne, startDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify Decision Value did  change
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(optimalBAR_QN, decisionDailybarOutput.getSingleRate());

        DecisionDailybarOutput decisionDailybarOutput_STE = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(dailybarOutputBAR_STE, decisionDailybarOutput_STE.getSingleRate());
    }

    @Test
    public void recommendFinalBARs_DoNotUploadAllRoomTypesUnChangedDecisionWhenOnlyOneSatisfiesMinIncrement() {
        String qnCode = "QN";
        String steCode = "STE";
        BigDecimal optimalBAR_QN = new BigDecimal("115.25");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("117.00");

        updatePricingAccomClassData(qnCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        updatePricingAccomClassData(steCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(qnCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(steCode, startDate, optimalBAR_STE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(qnCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(steCode, startDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Verify decision updated rows are only for one room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(1, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_RoomClassLevel_MinimumIncrement_FixedOffset_AppliedCorrectly_All_RTMatchCriteria() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        BigDecimal optimalBAR_QN = new BigDecimal("105.25");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("137.00");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        updatePricingAccomClassData(accomTypeCodeOne, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, startDate, optimalBAR_STE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCodeOne, startDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);
        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Verify decision updated rows are for both room types.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(2, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_RoomClassLevel_MinimumIncrement_PercentOffset_AppliedCorrectly_All_RTMatchCriteria() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        //QN doe not satisfy minimum increment
        BigDecimal optimalBAR_QN = new BigDecimal("210.00");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("200.00");

        //STE  satisfy minimum increment
        BigDecimal optimalBAR_STE = new BigDecimal("200.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("200.00");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.PERCENTAGE, new BigDecimal("5"), false);
        updatePricingAccomClassData(accomTypeCodeOne, MinimumIncrementMethod.PERCENTAGE, new BigDecimal("2"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, nextToStartDate, optimalBAR_STE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCodeOne, nextToStartDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);
        // Create Decision_Dailybar_Output records

        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();

        // Verify decision updated rows are only for one room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(1, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_RoomClassLevel_MinimumIncrement_FixedOffset_AppliedCorrectly_OneRTDoesNotMatchCriteria() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        BigDecimal optimalBAR_QN = new BigDecimal("115.25");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("136.00");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        updatePricingAccomClassData(accomTypeCodeOne, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, nextToStartDate, optimalBAR_STE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCodeOne, nextToStartDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Verify decision updated rows are only for one room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);

        assertEquals(1, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_PartialUpload_3RT_2ChngMorethanMinIncre_1ChngGreatThanZer0_1Same_Upload2RT() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        String accomTypeCodeTwo = "k";
        BigDecimal optimalBAR_QN = new BigDecimal("115.25");
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");
        BigDecimal dailybarOutputBAR_STE = new BigDecimal("127.00");
        BigDecimal optimalBAR_k = new BigDecimal("110.00");
        BigDecimal dailybarOutputBAR_k = new BigDecimal("110.00");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);
        updatePricingAccomClassData(accomTypeCodeOne, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, startDate, optimalBAR_STE);
        addCPDecisionBAROutput(accomTypeCodeTwo, startDate, optimalBAR_k);

        // Add a DecisionDailybarOutput record ( These are as per default offsets in H2 DB.
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCodeOne, startDate, dailybarOutputBAR_STE, dailybarOutputBAR_STE, BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(accomTypeCodeTwo, startDate, dailybarOutputBAR_k, dailybarOutputBAR_k, BigDecimal.ZERO, BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Verify decision updated rows are only for two room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(2, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_validate_NonbaseRT_MinimumIncrementCritetia_IsPickedFromBaseRTMinIncrement_Setting_Positive() {
        String accomTypeChildNonBase = "QN";
        String accomTypeParentBase = "D";
        String accomTypeSecondBase = "STE";
        //new decision as on current processing  satisfy min increment for base RT D.
        BigDecimal optimalBAR_QN = new BigDecimal("95.00");
        // Existing DailybarOutputs  previous processing
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.00");

        //Lets set Minimum increment for D
        updatePricingAccomClassData(accomTypeParentBase, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        //Lets set minimum increment for STE
        updatePricingAccomClassData(accomTypeSecondBase, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeChildNonBase, startDate, optimalBAR_QN);

        addOffset(accomTypeChildNonBase, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(accomTypeChildNonBase, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a DecisionDailybarOutput record ( These are as per default offsets in H2 DB.
        addDecisionDailybarOutput(accomTypeChildNonBase, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeChildNonBase, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(optimalBAR_QN, decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("95.00"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_validate_NonbaseRT_MinimumIncrementCritetia_IsPickedFromBaseRTMinIncrement_Setting_Negative() {
        String accomTypeChildNonBase = "QN";
        String accomTypeParentBase = "D";
        String accomTypeSecondBase = "STE";

        //new decision as on current processing - not satisfy min increment for base RT D.
        BigDecimal optimalBAR_QN = new BigDecimal("96.00");
        // Existing DailybarOutputs  previous processing
        BigDecimal dailybarOutputBAR_QN = new BigDecimal("105.00");

        //Lets set Minimum increment for D
        updatePricingAccomClassData(accomTypeParentBase, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        //Lets set minimum increment for STE
        updatePricingAccomClassData(accomTypeSecondBase, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Set some offsets
        addOffset(accomTypeChildNonBase, startDate, endDate, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(accomTypeChildNonBase, startDate, endDate, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(accomTypeChildNonBase, startDate, endDate, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeChildNonBase, startDate, optimalBAR_QN);

        // Add a DecisionDailybarOutput record ( These are as per default offsets in H2 DB.
        addDecisionDailybarOutput(accomTypeChildNonBase, startDate, dailybarOutputBAR_QN, dailybarOutputBAR_QN.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, nextToStartDate);

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);

        assertEquals(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeChildNonBase, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(dailybarOutputBAR_QN, decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(dailybarOutputBAR_QN.add(BigDecimal.TEN), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_PartialUpload_SameRT_MultipleOccupancyDates_OnlyOneDayHasChanged() {
        String accomTypeCode = "QN";

        // today's optimization decisions for two days
        BigDecimal optimalBAR_QN_today = new BigDecimal("95.00");
        BigDecimal optimalBAR_QN_tomorrow = new BigDecimal("116.25");

        // Existing DailybarOutputs for two Days
        BigDecimal dailybarOutputBAR_QN_today = new BigDecimal("105.25");
        BigDecimal dailybarOutputBAR_QN_tomorrow = new BigDecimal("116.25");

        // Use mockito to take care of the config parameter service requirements
        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN_today);
        addCPDecisionBAROutput(accomTypeCode, nextToStartDate, optimalBAR_QN_tomorrow);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN_today, dailybarOutputBAR_QN_today.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCode, nextToStartDate, dailybarOutputBAR_QN_tomorrow, dailybarOutputBAR_QN_tomorrow.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Verify decision updated rows are only for two room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(1, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_PartialUpload_SameRT_MultipleOccupancyDates_BothDaysHaveChanged() {
        String accomTypeCode = "QN";

        // today's optimization decisions for two days
        BigDecimal optimalBAR_QN_today = new BigDecimal("95.00");
        BigDecimal optimalBAR_QN_tomorrow = new BigDecimal("126.25");

        // Existing DailybarOutputs for two Days
        BigDecimal dailybarOutputBAR_QN_today = new BigDecimal("105.25");
        BigDecimal dailybarOutputBAR_QN_tomorrow = new BigDecimal("116.25");

        // Use mockito to take care of the config parameter service requirements
        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN_today);
        addCPDecisionBAROutput(accomTypeCode, nextToStartDate, optimalBAR_QN_tomorrow);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN_today, dailybarOutputBAR_QN_today.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCode, nextToStartDate, dailybarOutputBAR_QN_tomorrow, dailybarOutputBAR_QN_tomorrow.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, nextToStartDate);

        // Verify decision updated rows are only for two room type.
        int updatedDecisionCount = findCountDecisionDailybarOutput(nextDecisionId);
        assertEquals(2, updatedDecisionCount);
    }

    @Test
    public void recommendFinalBARs_ValidateDecisionChangesWhenOnlyDoubleOccupancyChanged() {
        String accomTypeCode = "STE";
        BigDecimal optimalBAR = new BigDecimal("100.00");
        BigDecimal dailyBarExisting = new BigDecimal("100.00");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);
        // Add offsets
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("11.00"));
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_PRICE, BigDecimal.TEN);
        addOffset(accomTypeCode, null, null, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_PRICE, BigDecimal.TEN);

        // Add a daily bar output
        addDecisionDailybarOutput(accomTypeCode, startDate, dailyBarExisting, dailyBarExisting.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(optimalBAR, decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("111.00"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.TEN, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_validatePaceDataIsNotAddedWhenDecisionRemainsSame() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal dailybarOutputBAR = new BigDecimal("105.25");

        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR, dailybarOutputBAR.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);

        // Verify the Pace record
        assertEquals(1, findCountPaceDailybarOutput(decisionDailybarOutput.getDecisionId()).intValue());

        // Rereun Recommendation with new decision ID
        service.recommendFinalBARs(nextNextDecisionID, startDate, endDate);

        // Verify the Pace does not have new entry
        int countOfRowsFirstDecision = findCountPaceDailybarOutput(nextDecisionId);
        assertEquals(1, countOfRowsFirstDecision);
        int countOfRowsSecondDecision = findCountPaceDailybarOutput(nextNextDecisionID);
        assertEquals(0, countOfRowsSecondDecision);
    }

    @Test
    public void recommendFinalBARs_validatePaceDataIsAddedWhenDecisionChanges() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("100.25");
        BigDecimal dailybarOutputBAR = new BigDecimal("105.25");

        // Use mockito to take care of the config parameter service requirements
        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("1"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR, dailybarOutputBAR.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);

        // Verify the Pace record
        assertEquals(1, findCountPaceDailybarOutput(decisionDailybarOutput.getDecisionId()).intValue());

        // Rereun Recommendation with updatedDecisions
        BigDecimal optimalBARNew = new BigDecimal("102.25");
        updateCPDecisionBAROutput(accomTypeCode, startDate, optimalBARNew);
        service.recommendFinalBARs(nextNextDecisionID, startDate, endDate);

        // Verify the Pace does  have new entry
        int countOfRowsFirstDecision = findCountPaceDailybarOutput(nextDecisionId);
        assertEquals(1, countOfRowsFirstDecision);
        int countOfRowsSecondDecision = findCountPaceDailybarOutput(nextNextDecisionID);
        assertEquals(1, countOfRowsSecondDecision);
    }

    @Test
    public void recommendFinalBARs_validateProductIDValueEqualToOneForCPInNonPaceTable() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("100.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(1, decisionDailybarOutput.getProduct().getId().intValue());
    }

    @Test
    public void recommendFinalBARs_validateProductIDValueEqualToOneForCPInPaceTable() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("100.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Run Recommendation service.
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the Pace DailybarOutputs
        List<PaceDailyBarOutput> paceDailyBarOutputs = findPaceDailyBarOutputs();
        assertEquals(1, paceDailyBarOutputs.size());

        // Verify the results
        PaceDailyBarOutput paceDailyBarOutput = paceDailyBarOutputs.get(0);
        assertEquals(1, paceDailyBarOutput.getProduct().getId().intValue());
    }

    @Test
    public void recommendFinalBARs_shouldNotGenerateDailyBarOutputWhenAccomTypeCapacityIsZero() {
        String accomTypeCode = "QN";
        String accomTypeCodeOne = "STE";
        BigDecimal optimalBAR_QN = new BigDecimal("115.25");
        BigDecimal optimalBAR_STE = new BigDecimal("117.00");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN);
        addCPDecisionBAROutput(accomTypeCodeOne, nextToStartDate, optimalBAR_STE);

        //UpdateAccom tpe Capacity for one room type to Zero "STE"
        updateAccomTypeCapacityToZero(accomTypeCodeOne);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());
        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
    }

    @Test
    public void recommendFinalBARs_createDoublePrettyPricedDecision() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("129.99");

        // Set Pricing Rule
        setRoundingRule(9, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);

        // Add an offset (make sure there are no other offsets)
        tenantCrudService().deleteAll(CPConfigOffsetAccomType.class);
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal(5));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(new BigDecimal("129.99"), decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("139.99"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_createDoublePrettyPricedDecisionWithTax() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("129.99");
        setTax(new BigDecimal("6.25"));
        setRoundingRule(9, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal(25));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(new BigDecimal("129.99"), decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("159.99"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_createDoublePrettyPricedDecisionWithTaxPercentage() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("129.99");
        setTax(new BigDecimal("6.25"));
        setRoundingRule(9, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR);
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal(25));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(new BigDecimal("129.99"), decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("159.99"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_createDoubleWithSpecificOverride() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("129.99");
        BigDecimal override = new BigDecimal("139.99");
        setTax(new BigDecimal("6.25"));
        setRoundingRule(9, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR, optimalBAR, override);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(new BigDecimal("139.99"), decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("139.99"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_createDoubleWithSpecificOverrideAndOffset() {
        String accomTypeCode = "QN";
        BigDecimal optimalBAR = new BigDecimal("129.99");
        BigDecimal override = new BigDecimal("139.99");
        setTax(new BigDecimal("6.25"));
        setRoundingRule(9, 9, 9);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR, optimalBAR, override);
        addOffset(accomTypeCode, null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal(10));

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the results
        DecisionDailybarOutput decisionDailybarOutput = decisionDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, decisionDailybarOutput.getDecisionId());
        assertEquals(startDate, decisionDailybarOutput.getOccupancyDate());
        assertEquals(accomTypeCode, decisionDailybarOutput.getAccomType().getAccomTypeCode());
        assertBigDecimalEquals(new BigDecimal("139.99"), decisionDailybarOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("149.99"), decisionDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, decisionDailybarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_noOffsetsTaxSupplementsRounding() {
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("100"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("100"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("100"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("0"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("0"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsNoTaxSupplementsRounding() {
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("10"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, new BigDecimal("7"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, new BigDecimal("5"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("20"));
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, new BigDecimal("8"));
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, new BigDecimal("6"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("110"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("7"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("5"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("100"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("8"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("6"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsOnBaseAndNoOffsetsOnNonBaseWillStillMatchBaseWhenBaseRTOnly() {
        String baseRT = "D";
        String nonBaseRT = "QN";

        reset(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("10"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, new BigDecimal("7"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, new BigDecimal("5"));

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, null, null);

        // Add the Base RT
        CPDecisionBAROutput baseRTDecisionOutput = addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Query the DB for the final state of the CPDecisionBAROutputs
        List<CPDecisionBAROutput> foundCPDecisionBarOutputs = tenantCrudService().findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(baseRTDecisionOutput.getProduct(), startDate, endDate));

        // BaseRT/non-baseRT should match on specific override, rooms-only bar, and final bar values
        CPDecisionBAROutput nonBaseRTDecisionOutput = foundCPDecisionBarOutputs.get(0);
        baseRTDecisionOutput = foundCPDecisionBarOutputs.get(1);
        assertEquals(baseRTDecisionOutput.getSpecificOverride(), nonBaseRTDecisionOutput.getSpecificOverride());
        assertEquals(baseRTDecisionOutput.getRoomsOnlyBAR(), nonBaseRTDecisionOutput.getRoomsOnlyBAR());
        assertEquals(baseRTDecisionOutput.getFinalBAR(), nonBaseRTDecisionOutput.getFinalBAR());

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the non-BaseRT = baseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(baseRTOutput.getSingleRate(), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(baseRTOutput.getDoubleRate(), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(baseRTOutput.getAdultRate(), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(baseRTOutput.getChildRate(), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsSupplementsNoRounding() {
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementFor(baseRT, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("120"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("110"), new BigDecimal("130"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("120"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("130"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("140"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsPercentSupplementsNoRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);

        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("110"), new BigDecimal("115.5"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("28"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("34"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("115.5"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("132"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("29.5"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("36"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsSupplementsTaxNoRounding() {
        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementFor(baseRT, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("120"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("110"), new BigDecimal("130"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("120"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.73"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("130"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("140.73"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsPercentSupplementsTaxNoRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("110"), new BigDecimal("115.5"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.8"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("28.95"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.02"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("115.5"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("132.8"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.45"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("37.02"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_fixedOffsetsPercentSupplementsTaxNoRounding_ChildAgeBucketsEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        java.time.LocalDate now = java.time.LocalDate.now();

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(15.0));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(10.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(15.0));

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(now), new BigDecimal("110"), new BigDecimal("115.5"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.8"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("28.95"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.02"), baseRTOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.36"), baseRTOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10.73"), baseRTOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.09"), baseRTOutput.getChildAgeThreeRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("115.5"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("132.8"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.45"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("37.02"), nonBaseRTOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.36"), nonBaseRTOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("10.73"), nonBaseRTOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.09"), nonBaseRTOutput.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_priceExcludedOffsetsIgnoreTaxAndRounding() {
        setTax(new BigDecimal("7.25"));
        setRoundingRule(null, 8, 9);

        String baseRT = "k";
        String nonBaseRT = "QN";

        addPricingAccomClass(baseRT, true);

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementFor(baseRT, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("120"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("120"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("120"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.73"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("120"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.73"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("30.73"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_priceExcludedFixedOffsetsPercentSupplement_IgnoreTaxAndRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        setTax(new BigDecimal("7.25"));
        setRoundingRule(null, 8, 9);

        String baseRT = "k";
        String nonBaseRT = "QN";

        addPricingAccomClass(baseRT, true);

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), convertJavaToJodaLocalDate(java.time.LocalDate.now()), OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.8"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("28.95"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.02"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("105"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.8"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("28.95"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("35.02"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsNoTaxSupplementsRounding() {
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal("10"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal("7"));
        addOffset(baseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal("5"));

        // Add offsets for non-BaseRT
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, new BigDecimal("20"));
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, new BigDecimal("8"));
        addOffset(nonBaseRT, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, new BigDecimal("6"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, BigDecimalUtil.ONE_HUNDRED, null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("100"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("110"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("7.7"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("5.5"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("100"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("120"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("9.6"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("7.2"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsTaxSupplements() {
        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        // Add supplements for BaseRT
        addSupplementFor(baseRT, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));

        // Add supplements for non-BaseRT
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("119.89"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("105"), new BigDecimal("124.89"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("119.89"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125.24"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.64"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.64"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("124.89"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.51"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.92"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.92"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsTax() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("105"), new BigDecimal("110.25"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsTax_ChildAgeBucketsEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        setTax(new BigDecimal("7.25"));

        String baseRT = "k";
        String nonBaseRT = "QN";

        java.time.LocalDate now = java.time.LocalDate.now();

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));

        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(nonBaseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(now), new BigDecimal("105"), new BigDecimal("110.25"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutput.getChildAgeThreeRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.69"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("23.41"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("29.24"), nonBaseRTOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.93"), nonBaseRTOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.87"), nonBaseRTOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("17.8"), nonBaseRTOutput.getChildAgeThreeRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsTax_ChildAgeBucketsEnabled_PaceTable() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        enableChildAgeBuckets();

        setTax(new BigDecimal("7.25"));

        String baseRT = "k";

        java.time.LocalDate now = java.time.LocalDate.now();

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_1, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(5.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_2, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(10.0));
        addOffset(baseRT, convertJavaToJodaLocalDate(now), convertJavaToJodaLocalDate(now), OccupancyType.CHILD_BUCKET_3, OffsetMethod.PERCENTAGE, BigDecimal.valueOf(15.0));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(now), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(1, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.9"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.30"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.85"), baseRTOutput.getChildRate());
        assertBigDecimalEquals(new BigDecimal("5.65"), baseRTOutput.getChildAgeOneRate());
        assertBigDecimalEquals(new BigDecimal("11.31"), baseRTOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(new BigDecimal("16.95"), baseRTOutput.getChildAgeThreeRate());

        // Verify the Pace record
        assertEquals(1, findCountPaceDailybarOutput(baseRTOutput.getDecisionId()).intValue());
        List<PaceDailyBarOutput> paceDailybarOutputs = findPaceDailyBarOutputs();
        PaceDailyBarOutput paceDailyBarOutput = paceDailybarOutputs.get(0);
        assertNotSame(previousDecisionId, paceDailyBarOutput.getDecisionId());
        assertEquals(nextDecisionId, paceDailyBarOutput.getDecisionId());
        assertEquals(startDate, paceDailyBarOutput.getOccupancyDate());
        assertEquals(RT_KING, paceDailyBarOutput.getAccomType().getAccomTypeCode());
        assertEquals(baseRTOutput.getSingleRate(), paceDailyBarOutput.getSingleRate());
        assertEquals(baseRTOutput.getDoubleRate(), paceDailyBarOutput.getDoubleRate());
        assertEquals(baseRTOutput.getAdultRate(), paceDailyBarOutput.getAdultRate());
        assertEquals(baseRTOutput.getChildRate(), paceDailyBarOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsSupplementsWithRounding() {
        setRoundingRule(null, 8, 9);

        String baseRT = "k";
        String nonBaseRT = "QN";
        //setup component room mapping
        AccomType componentRT = findAccomType(RT_STE);
        setupComponentRoomDataForPriceAsSumOfPart(componentRT);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(false);
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));
        addSupplementFor(baseRT, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(componentRT.getAccomTypeCode(), new BigDecimal("5"));
        addSupplementFor(componentRT.getAccomTypeCode(), new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("119.89"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("105"), new BigDecimal("124.89"), null);

        // Add the component RT
        addCPDecisionBAROutput(componentRT.getAccomTypeCode(), LocalDate.now(), new BigDecimal("55"), new BigDecimal("74.89"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("119.89"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("124.89"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.24"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.24"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("124.89"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("129.89"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.49"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.49"), nonBaseRTOutput.getChildRate());

        // Verify the component RT values
        DecisionDailybarOutput componentRTOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("74.89"), componentRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("77.89"), componentRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.89"), componentRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("22.89"), componentRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsWithRounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        setRoundingRule(null, 8, 9);

        String baseRT = "k";
        String nonBaseRT = "QN";
        //setup component room mapping
        AccomType componentRT = findAccomType(RT_STE);
        setupComponentRoomDataForPriceAsSumOfPart(componentRT);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(false);
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(componentRT.getAccomTypeCode(), new BigDecimal("5"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105.89"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("105"), new BigDecimal("110.89"), null);

        // Add the component RT
        addCPDecisionBAROutput(componentRT.getAccomTypeCode(), convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("55"), new BigDecimal("57.89"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105.89"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("116.89"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.05"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.62"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.89"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.89"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.99"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("28.81"), nonBaseRTOutput.getChildRate());

        // Verify the component RT values
        DecisionDailybarOutput componentRTOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("57.89"), componentRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("63.89"), componentRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("12.05"), componentRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("15.10"), componentRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsSupplementsWithRounding_ComponentRoomsPriceAsSumOfPartEnabled() {
        setRoundingRule(null, 8, 9);
        String baseRT = "k";
        String nonBaseRT = "QN";
        AccomType componentRT = findAccomType(RT_STE);
        setupComponentRoomDataForPriceAsSumOfPart(componentRT);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(true);
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));
        addSupplementFor(baseRT, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(componentRT.getAccomTypeCode(), new BigDecimal("5"));
        addSupplementFor(componentRT.getAccomTypeCode(), new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("119.89"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("105"), new BigDecimal("124.89"), null);

        // Add the component RT
        addCPDecisionBAROutput(componentRT.getAccomTypeCode(), LocalDate.now(), new BigDecimal("55"), new BigDecimal("74.89"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("119.89"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("124.89"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.24"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.24"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("124.89"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("129.89"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.49"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.49"), nonBaseRTOutput.getChildRate());

        // Verify the component RT values
        DecisionDailybarOutput componentRTOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("614.89"), componentRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("639.89"), componentRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("51.24"), componentRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("51.24"), componentRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplementsWithRounding_ComponentRoomsPriceAsSumOfPartEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        setRoundingRule(null, 8, 9);
        String baseRT = "k";
        String nonBaseRT = "QN";
        AccomType componentRT = findAccomType(RT_STE);
        setupComponentRoomDataForPriceAsSumOfPart(componentRT);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(true);
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        addOccupancyTypeOffsetsFor(componentRT.getAccomTypeCode(), new BigDecimal("5"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(componentRT.getAccomTypeCode(), OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105.89"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("105"), new BigDecimal("110.89"), null);

        // Add the component RT
        addCPDecisionBAROutput(componentRT.getAccomTypeCode(), convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("55"), new BigDecimal("57.89"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());
        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105.89"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("116.89"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.05"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.62"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.89"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.89"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.99"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("28.81"), nonBaseRTOutput.getChildRate());

        // Verify the component RT values
        DecisionDailybarOutput componentRTOutput = decisionDailybarOutputs.get(2);
        assertBigDecimalEquals(new BigDecimal("544.89"), componentRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("599.89"), componentRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("112.97"), componentRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("141.55"), componentRTOutput.getChildRate());
    }

    private void addOccupancyTypeOffsetsFor(String roomTypeCode, BigDecimal offsetValue) {
        addOffset(roomTypeCode, LocalDate.now(), LocalDate.now(), OccupancyType.DOUBLE, OffsetMethod.PERCENTAGE, offsetValue);
        addOffset(roomTypeCode, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_ADULT, OffsetMethod.PERCENTAGE, offsetValue);
        addOffset(roomTypeCode, LocalDate.now(), LocalDate.now(), OccupancyType.EXTRA_CHILD, OffsetMethod.PERCENTAGE, offsetValue);
    }

    private void addSupplementFor(String roomTypeCode, BigDecimal supplementValue) {
        addSupplement(roomTypeCode, OccupancyType.SINGLE, supplementValue);
        addSupplement(roomTypeCode, OccupancyType.DOUBLE, supplementValue);
        addSupplement(roomTypeCode, OccupancyType.EXTRA_ADULT, supplementValue);
        addSupplement(roomTypeCode, OccupancyType.EXTRA_CHILD, supplementValue);
    }

    private void setupComponentRoomDataForPriceAsSumOfPart(AccomType componentRT) {
        componentRT.setIsComponentRoom("Y");
        tenantCrudService().save(componentRT);
        AccomType kingAccomType = findAccomType(RT_KING);
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(componentRT, kingAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(componentRT, queenAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_STE, null, null, true);
    }

    @Test
    public void recommendFinalBARs_percentOffsetsSupplementsNoRoundingRules() {
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        // Add supplements for BaseRT
        addSupplementFor(baseRT, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));

        // Add supplements for non-BaseRT
        addSupplementFor(nonBaseRT, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, LocalDate.now(), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("120"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, LocalDate.now(), new BigDecimal("105"), new BigDecimal("125"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("120"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("125"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.25"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.25"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("125"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("130.25"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("25.51"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("25.51"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARs_percentOffsetsPercentSupplements_NoRoundingRules_NoTax() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        String baseRT = "k";
        String nonBaseRT = "QN";

        // Add occupancy type offsets for BaseRT
        addOccupancyTypeOffsetsFor(baseRT, new BigDecimal("5"));

        // Add supplements for BaseRT
        addSupplementPercent(baseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(baseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(baseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add offsets for non-BaseRT
        addOccupancyTypeOffsetsFor(nonBaseRT, new BigDecimal("5"));

        // Add supplements for non-BaseRT
        addSupplementPercent(nonBaseRT, OccupancyType.SINGLE, new BigDecimal("5"));
        addSupplementPercent(nonBaseRT, OccupancyType.DOUBLE, new BigDecimal("10"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_ADULT, new BigDecimal("15"));
        addSupplementPercent(nonBaseRT, OccupancyType.EXTRA_CHILD, new BigDecimal("20"));

        // Add the Base RT
        addCPDecisionBAROutput(baseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), BigDecimalUtil.ONE_HUNDRED, new BigDecimal("105"), null);

        // Add the non-Base RT
        addCPDecisionBAROutput(nonBaseRT, convertJavaToJodaLocalDate(java.time.LocalDate.now()), new BigDecimal("105"), new BigDecimal("110.25"), null);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the BaseRT values
        DecisionDailybarOutput baseRTOutput = decisionDailybarOutputs.get(0);
        assertBigDecimalEquals(new BigDecimal("105"), baseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("115.5"), baseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("21.79"), baseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("27.3"), baseRTOutput.getChildRate());

        // Verify the non-BaseRT values
        DecisionDailybarOutput nonBaseRTOutput = decisionDailybarOutputs.get(1);
        assertBigDecimalEquals(new BigDecimal("110.25"), nonBaseRTOutput.getSingleRate());
        assertBigDecimalEquals(new BigDecimal("121.28"), nonBaseRTOutput.getDoubleRate());
        assertBigDecimalEquals(new BigDecimal("22.87"), nonBaseRTOutput.getAdultRate());
        assertBigDecimalEquals(new BigDecimal("28.66"), nonBaseRTOutput.getChildRate());
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyEnabled_ensureFinalBarsEqualAcrossRoomTypes_offsetsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the rates are the same by accom class.
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getDoubleRate(), decisionDailybarOutputs.get(1).getDoubleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyDisabled_ensureFinalBarsNotEqualAcrossRoomTypes_offsetsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.TEN);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);
        addCPConfigOffsetAccomType(RT_DOUBLE, OccupancyType.EXTRA_CHILD, OffsetMethod.FIXED_OFFSET, BigDecimal.ONE);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the double, extra adult and extra child rates vary between the two room types..
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getDoubleRate(), decisionDailybarOutputs.get(1).getDoubleRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyEnabled_ensureFinalBarsAreEqualAcrossRoomTypes_supplementsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(RT_STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);
        addCPDecisionBAROutput(RT_STE, startDate, new BigDecimal("510.25"));
        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_STE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        addSupplement(RT_STE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);
        addTransientPricingBaseAccomType(RT_STE, new BigDecimal("300"), new BigDecimal("800.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());

        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the double, extra adult and extra child rates are equal for the two room types..
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getDoubleRate(), decisionDailybarOutputs.get(1).getDoubleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));

        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1.00).setScale(2), decisionDailybarOutputs.get(2).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1.00).setScale(2), decisionDailybarOutputs.get(2).getChildRate().setScale(2));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyEnabled_ensureFinalBarsAreEqualAcrossRoomTypes_percentSupplementsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(RT_STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);
        addCPDecisionBAROutput(RT_STE, startDate, new BigDecimal("510.25"));
        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_STE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        addSupplementPercent(RT_STE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplementPercent(RT_STE, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplementPercent(RT_STE, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplementPercent(RT_STE, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplementPercent(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);
        addTransientPricingBaseAccomType(RT_STE, new BigDecimal("300"), new BigDecimal("800.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());

        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the double, extra adult and extra child rates are equal for the two room types..
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getDoubleRate(), decisionDailybarOutputs.get(1).getDoubleRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));

        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(5.05).setScale(2), decisionDailybarOutputs.get(2).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(5.05).setScale(2), decisionDailybarOutputs.get(2).getChildRate().setScale(2));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyEnabledAndComonentRoomPriceAsSumOfPartEnabled_EnsureFinalBarsAreSumOfTheirParts() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(RT_STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);
        addCPDecisionBAROutput(RT_STE, startDate, new BigDecimal("510.25"));
        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_STE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        addSupplement(RT_STE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);
        addTransientPricingBaseAccomType(RT_STE, new BigDecimal("300"), new BigDecimal("800.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());

        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the double, extra adult and extra child rates are sum of part..
        assertEquals(BigDecimal.valueOf(1530.75).setScale(2), decisionDailybarOutputs.get(0).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1530.75).setScale(2), decisionDailybarOutputs.get(0).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(0).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(0).getChildRate().setScale(2));

        assertEquals(BigDecimal.valueOf(1020.50).setScale(2), decisionDailybarOutputs.get(1).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1020.50).setScale(2), decisionDailybarOutputs.get(1).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(1).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(1).getChildRate().setScale(2));

        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1.00).setScale(2), decisionDailybarOutputs.get(2).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1.00).setScale(2), decisionDailybarOutputs.get(2).getChildRate().setScale(2));
    }

    @Test
    public void recommendFinalBARsChanging_EnsureFinalBarsAreSumOfTheirParts_MinimumPriceChangeDidNotMetForOneRT() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");
        BigDecimal optimalBARForSTE = new BigDecimal("512.25");
        BigDecimal dailyBARExistingForSTE = new BigDecimal("510.25");//minimumPriceChange Does Not Met. Should use this rate to calculate ComponentRoomRate
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(RT_STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN, true);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN, false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBarOutput(startDate, RT_QUEEN, optimalBAR);
        addCPDecisionBarOutput(startDate, RT_STE, optimalBARForSTE);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);
        addDecisionDailybarOutput(RT_STE, startDate, dailyBARExistingForSTE, dailyBARExistingForSTE, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        addSupplement(RT_STE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_STE, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);
        addTransientPricingBaseAccomType(RT_STE, new BigDecimal("300"), new BigDecimal("800.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        decisionDailybarOutputs = decisionDailybarOutputs.stream().sorted((d1, d2) -> d2.getAccomType().compareTo(d1.getAccomType())).collect(Collectors.toList());

        assertEquals(3, decisionDailybarOutputs.size());

        // Verify the double, extra adult and extra child rates are sum of part..
        assertEquals(BigDecimal.valueOf(1530.75).setScale(2), decisionDailybarOutputs.get(0).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1530.75).setScale(2), decisionDailybarOutputs.get(0).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(0).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(0).getChildRate().setScale(2));

        assertEquals(BigDecimal.valueOf(1020.50).setScale(2), decisionDailybarOutputs.get(1).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(1020.50).setScale(2), decisionDailybarOutputs.get(1).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(1).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(10.00).setScale(2), decisionDailybarOutputs.get(1).getChildRate().setScale(2));

        //Verify that decisions are not changed
        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getSingleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(510.25).setScale(2), decisionDailybarOutputs.get(2).getDoubleRate().setScale(2));
        assertEquals(BigDecimal.valueOf(0.00).setScale(2), decisionDailybarOutputs.get(2).getAdultRate().setScale(2));
        assertEquals(BigDecimal.valueOf(0.00).setScale(2), decisionDailybarOutputs.get(2).getChildRate().setScale(2));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyDisabled_ensureFinalBarsNotEqualAcrossRoomTypes_supplementsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the extra adult and extra child rates vary between the two room types..
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));
    }

    @Test
    public void recommendFinalBARsChanging_baseRoomTypeOnlyDisabled_ensureFinalBarsNotEqualAcrossRoomTypes_percentSupplementsNotEqual() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        BigDecimal optimalBAR = new BigDecimal("110.25");
        BigDecimal dailyBARExisting = new BigDecimal("115.25");

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_QUEEN, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_QUEEN, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplementPercent(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.DOUBLE, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_ADULT, BigDecimal.ONE);
        addSupplementPercent(RT_QUEEN, OccupancyType.EXTRA_CHILD, BigDecimal.ONE);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(RT_DOUBLE, startDate, optimalBAR);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(RT_DOUBLE, startDate, dailyBARExisting, dailyBARExisting, BigDecimal.ZERO, BigDecimal.ZERO);

        addSupplementPercent(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.DOUBLE, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        addSupplementPercent(RT_DOUBLE, OccupancyType.EXTRA_CHILD, BigDecimal.TEN);

        addTransientPricingBaseAccomType(RT_QUEEN, new BigDecimal("100.00"), new BigDecimal("500.00"), BigDecimal.ZERO);

        // Create Decision_Dailybar_Output records
        service.recommendFinalBARs(nextDecisionId, startDate, endDate);
        tenantCrudService().flushAndClear();

        // Get the DecisionDailybarOutputs
        List<DecisionDailybarOutput> decisionDailybarOutputs = findDecisionDailybarOutputs();
        assertEquals(2, decisionDailybarOutputs.size());

        // Verify the extra adult and extra child rates vary between the two room types..
        assertTrue(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getSingleRate(), decisionDailybarOutputs.get(1).getSingleRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getAdultRate(), decisionDailybarOutputs.get(1).getAdultRate()));
        assertFalse(BigDecimalUtil.equals(decisionDailybarOutputs.get(0).getChildRate(), decisionDailybarOutputs.get(1).getChildRate()));
    }


    @Test
    public void deleteDecisionsOfZeroCapacityAccomTypes_NoZeroCapacityRoomTypeFound() {
        CrudService tenantCrudService = mock(CrudService.class);
        inject(service, "tenantCrudService", tenantCrudService);
        when(hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms()).thenReturn(NO_ROOMS);

        service.deleteDecisionsOfZeroCapacityAccomTypes();

        verify(tenantCrudService, times(0))
                .executeUpdateByNamedQuery(eq(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES), anyMap());
    }

    @Test
    public void deleteDecisionsOfZeroCapacityAccomTypes_ZeroCapacityRoomTypesFound() {
        CrudService tenantCrudService = mock(CrudService.class);
        inject(service, "tenantCrudService", tenantCrudService);
        Date caughtUpDate = DateUtil.getCurrentDate();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms()).thenReturn(ROOM_A_ROOM_B);

        service.deleteDecisionsOfZeroCapacityAccomTypes();

        verify(tenantCrudService)
                .executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES,
                        MapBuilder.with("caughtUpDate", new LocalDate(caughtUpDate))
                                .and("accomTypeCodes", ROOM_A_ROOM_B).get());
    }

    @Test
    public void deleteFutureDecisionsForNonUploadableProducts() {
        LocalDate today = LocalDate.now();
        CrudService crudService = mock(CrudService.class);
        inject(service, "tenantCrudService", crudService);
        when(dateService.getCaughtUpLocalDate()).thenReturn(today);
        Product product = new Product();

        service.deleteFutureDecisionsForNonUploadableProducts(product);

        verify(crudService, never()).executeUpdateByNamedQuery(anyString(), anyMap());

        Product product1 = new Product();
        product1.setId(250);
        product1.setUpload(true);

        service.deleteFutureDecisionsForNonUploadableProducts(product1);

        verify(crudService, never()).executeUpdateByNamedQuery(anyString(), anyMap());

        product1 = new Product();
        product1.setId(250);
        product1.setUpload(false);

        service.deleteFutureDecisionsForNonUploadableProducts(product1);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", today);
        parameters.put("nonUploadableProductId", 250);
        verify(crudService).executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_NON_UPLOADABLE_AGILE_PRODUCTS, parameters);
        parameters = new HashMap<>();
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        parameters.put("product", product1);
        parameters.put("arrivalDate", today);
        verify(crudService).executeUpdateByNamedQuery(CPDecisionBAROutput.DELETE_BY_PRODUCT, parameters);
    }

    @Test
    public void deleteFutureDecisionDailyBarOutput() {
        LocalDate today = LocalDate.now();
        CrudService crudService = mock(CrudService.class);
        inject(service, "tenantCrudService", crudService);
        when(dateService.getCaughtUpLocalDate()).thenReturn(today);
        Product product = new Product();
        product.setId(250);

        service.deleteFutureDecisionDailyBarOutput(product, today);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", today);
        parameters.put("nonUploadableProductId", 250);
        verify(crudService).executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_NON_UPLOADABLE_AGILE_PRODUCTS, parameters);
    }

    @Test
    public void getAdjustedRate_PercentageOffset() {
        //Zero Rate; Positive adjustment Value
        assertEquals(BigDecimal.ZERO, service.getAdjustedRate(BigDecimal.ZERO, new BigDecimal(10), AgileRatesOffsetMethod.PERCENTAGE));

        //Valid Rate; Zero adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustedRate(BigDecimal.TEN, BigDecimal.ZERO, AgileRatesOffsetMethod.PERCENTAGE));

        //Valid Rate; adjustment value below parent
        assertEquals(new BigDecimal("9.00"), service.getAdjustedRate(BigDecimal.TEN, BigDecimal.valueOf(-10), AgileRatesOffsetMethod.PERCENTAGE));

        //Valid Rate; adjustment value above parent
        assertEquals(new BigDecimal("11.00"), service.getAdjustedRate(BigDecimal.TEN, BigDecimal.TEN, AgileRatesOffsetMethod.PERCENTAGE));

        //Valid Rate; adjustment value below parent
        assertEquals(new BigDecimal("126.31"), service.getAdjustedRate(BigDecimal.valueOf(157.89), BigDecimal.valueOf(-20), AgileRatesOffsetMethod.PERCENTAGE));

        //Valid Rate; adjustment value above parent
        assertEquals(new BigDecimal("189.47"), service.getAdjustedRate(BigDecimal.valueOf(157.89), BigDecimal.valueOf(20), AgileRatesOffsetMethod.PERCENTAGE));

        //Null rate
        assertNull(service.getAdjustedRate(null, BigDecimal.valueOf(20), AgileRatesOffsetMethod.PERCENTAGE));

        //Null adjustment
        assertEquals(new BigDecimal("157.89"), service.getAdjustedRate(BigDecimal.valueOf(157.89), null, AgileRatesOffsetMethod.PERCENTAGE));
    }

    @Test
    public void getAdjustedRate_FixedOffset() {
        //Zero Rate; Positive adjustment Value
        assertEquals(new BigDecimal("10.00"), service.getAdjustedRate(BigDecimal.ZERO, new BigDecimal("10.00"), AgileRatesOffsetMethod.FIXED));

        //Valid Rate; Zero adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustedRate(BigDecimal.TEN, BigDecimal.ZERO, AgileRatesOffsetMethod.FIXED));

        //Valid Rate; adjustment value below parent
        assertEquals(new BigDecimal("0.00"), service.getAdjustedRate(BigDecimal.TEN, BigDecimal.valueOf(-10), AgileRatesOffsetMethod.FIXED));

        //Valid Rate; adjustment value above parent
        assertEquals(new BigDecimal("20.00"), service.getAdjustedRate(BigDecimal.TEN, BigDecimal.TEN, AgileRatesOffsetMethod.FIXED));

        //Valid Rate; adjustment value below parent
        assertEquals(new BigDecimal("137.89"), service.getAdjustedRate(BigDecimal.valueOf(157.89), BigDecimal.valueOf(-20), AgileRatesOffsetMethod.FIXED));

        //Valid Rate; adjustment value above parent
        assertEquals(new BigDecimal("177.89"), service.getAdjustedRate(BigDecimal.valueOf(157.89), BigDecimal.valueOf(20), AgileRatesOffsetMethod.FIXED));

        //Null rate
        assertNull(service.getAdjustedRate(null, BigDecimal.valueOf(20), AgileRatesOffsetMethod.FIXED));

        //Null adjustment
        assertEquals(new BigDecimal("157.89"), service.getAdjustedRate(BigDecimal.valueOf(157.89), null, AgileRatesOffsetMethod.FIXED));

        //Null AgileRatesOffsetMethod
        assertEquals(new BigDecimal("1.00"), service.getAdjustedRate(BigDecimal.TEN, BigDecimal.valueOf(-9), null));
    }

    @Test
    public void getRate_Percentage() {
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setCode(Product.BAR);
        product.setSystemDefault(true);
        cpDecisionBAROutput.setProduct(product);

        assertEquals(BigDecimal.valueOf(10.00), service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(11.00), service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(12.00), service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(12.00), service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
        assertNull(service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRate(cpDecisionBAROutput, null, null, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 4, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getRate_Fixed() {
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setCode(Product.BAR);
        product.setSystemDefault(true);
        cpDecisionBAROutput.setProduct(product);

        assertEquals(BigDecimal.TEN, service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.TEN, service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.TEN, service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.TEN, service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
        assertNull(service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRate(cpDecisionBAROutput, null, null, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRate(cpDecisionBAROutput, null, OccupancyType.FIVE_CHILDREN, 4, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getRateWithoutPackages_Percentage() {
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        assertEquals(BigDecimal.valueOf(10.00), service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(11.00), service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(12.00), service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(12.00), service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
        assertNull(service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRateWithoutPackages(null, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 4, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getRateWithoutPackages_Fixed() {
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        assertEquals(BigDecimal.TEN, service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.TEN, service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.TEN, service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.TEN, service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
        assertNull(service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRateWithoutPackages(null, 5, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertNull(service.getRateWithoutPackages(OccupancyType.FIVE_CHILDREN, 4, OccupancyType.getChildQuantities(), cpConfigMergedOffset, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getPackagesRate_Percentage() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                false,
                false);

        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(products.get(products.keySet().iterator().next()).get(1));

        assertEquals(BigDecimal.valueOf(10.60).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(11.66).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(12.72).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(14.64), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getPackagesRate_Fixed() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.FIXED;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.FIXED;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                false,
                false);

        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(products.get(products.keySet().iterator().next()).get(1));

        assertEquals(BigDecimal.valueOf(16.00).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(17.00).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(18.00).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(34.00).setScale(2), service.getPackagesRate(cpDecisionBAROutput, mockCpDecisionContext, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void getPackagesRate_BAR() {
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigMergedOffset.setOffsetValue(BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setCode(Product.BAR);
        product.setSystemDefault(true);
        cpDecisionBAROutput.setProduct(product);

        assertEquals(BigDecimal.valueOf(10.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(11.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(11.00), OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(12.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(12.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
        assertEquals(BigDecimal.valueOf(10.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(10.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(12.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(12.00), OccupancyType.CHILD_BUCKET_3));
        assertEquals(BigDecimal.valueOf(12.00), service.getPackagesRate(cpDecisionBAROutput, null, BigDecimal.valueOf(12.00), OccupancyType.FIVE_CHILDREN));
    }

    @Test
    public void isPerPersonPricingEnabled_False() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                false,
                false);

        assertFalse(service.isPerPersonPricingEnabled(mockCpDecisionContext));
    }

    @Test
    public void isPerPersonPricingEnabled_True() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                true,
                true,
                false);

        assertTrue(service.isPerPersonPricingEnabled(mockCpDecisionContext));
    }

    @Test
    public void isChildAgeBucketsEnabled_False() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                false,
                false);

        assertFalse(service.isChildAgeBucketsEnabled(mockCpDecisionContext));
    }

    @Test
    public void isChildAgeBucketsEnabled_True() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                true,
                false);

        assertTrue(service.isChildAgeBucketsEnabled(mockCpDecisionContext));
    }

    @Test
    public void isHiltonSendByAdjustment_isHiltonSendAdjustmentEnabled_False() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                false,
                false,
                false,
                false);

        DecisionDailybarOutput decisionDailybarOutput = new DecisionDailybarOutput();
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput.setProduct(barProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput));

        DecisionDailybarOutput decisionDailybarOutput2 = new DecisionDailybarOutput();
        Product agileProduct = new Product();
        agileProduct.setSystemDefault(false);
        agileProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput2.setProduct(agileProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput2));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput2));

        DecisionDailybarOutput decisionDailybarOutput3 = new DecisionDailybarOutput();
        Product agileProductAdjustment = new Product();
        agileProductAdjustment.setSystemDefault(false);
        agileProductAdjustment.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        decisionDailybarOutput3.setProduct(agileProductAdjustment);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput3));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput3));
    }

    @Test
    public void isHiltonSendByAdjustment_isHiltonSendAdjustmentEnabled_True() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                true,
                false,
                false,
                false,
                false);

        DecisionDailybarOutput decisionDailybarOutput = new DecisionDailybarOutput();
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput.setProduct(barProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput));

        DecisionDailybarOutput decisionDailybarOutput2 = new DecisionDailybarOutput();
        Product agileProductPrice = new Product();
        agileProductPrice.setSystemDefault(false);
        agileProductPrice.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        agileProductPrice.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput2.setProduct(agileProductPrice);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput2));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput2));

        DecisionDailybarOutput decisionDailybarOutput3 = new DecisionDailybarOutput();
        Product agileProductAdjustment = new Product();
        agileProductAdjustment.setSystemDefault(false);
        agileProductAdjustment.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        agileProductAdjustment.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        decisionDailybarOutput3.setProduct(agileProductAdjustment);

        assertTrue(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput3));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput3));
    }

    @Test
    public void isHiltonSendByAdjustment_isHiltonUpdateValuesSentForExtraAdultExtraChild_False() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                false,
                true,
                false,
                false,
                false);

        DecisionDailybarOutput decisionDailybarOutput = new DecisionDailybarOutput();
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput.setProduct(barProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput));

        DecisionDailybarOutput decisionDailybarOutput2 = new DecisionDailybarOutput();
        Product agileProduct = new Product();
        agileProduct.setSystemDefault(false);
        agileProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput2.setProduct(agileProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput2));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput2));

        DecisionDailybarOutput decisionDailybarOutput3 = new DecisionDailybarOutput();
        Product agileProductAdjustment = new Product();
        agileProductAdjustment.setSystemDefault(false);
        agileProductAdjustment.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        decisionDailybarOutput3.setProduct(agileProductAdjustment);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput3));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput3));
    }

    @Test
    public void isHiltonSendByAdjustment_isHiltonUpdateValuesSentForExtraAdultExtraChild_True() {
        boolean adjustForExtraAdultExtraChild = true;
        double productRateOffsetValue = 0;
        AgileRatesOffsetMethod productRateOffsetMethod = AgileRatesOffsetMethod.FIXED;
        double adultPackageValue = 7;
        AgileRatesOffsetMethod adultPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        double childPackageValue = 4;
        AgileRatesOffsetMethod childPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;
        final double setPackageValue = 2;
        final AgileRatesOffsetMethod setPackageOffsetMethod = AgileRatesOffsetMethod.PERCENTAGE;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultChildSetPercentForAllProducts = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefault())
                    .forEach(p -> {
                        p.setOffsetForExtraAdult(adjustForExtraAdultExtraChild);
                        p.setOffsetForExtraChild(adjustForExtraAdultExtraChild);
                        productPackagesWhereAdultChildSetPercentForAllProducts.put(
                                p,
                                Arrays.asList(
                                        createAdultPackage(precisionTwoValueOf(adultPackageValue), adultPackageOffsetMethod),
                                        createChildPackage(precisionTwoValueOf(childPackageValue), childPackageOffsetMethod),
                                        createSetPercentPackage(precisionTwoValueOf(setPackageValue), setPackageOffsetMethod)));
                    });
        });

        AccomType baseAccomType = context.getBaseAccomType();

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        CPDecisionContext mockCpDecisionContext = createMockCpDecisionContext(
                OccupancyType.SINGLE,
                products,
                productPackagesWhereAdultChildSetPercentForAllProducts,
                pricingRules,
                baseAccomType,
                generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                0,
                new HashMap<>(),
                new HashMap<>(),
                createProductRateOffsets(
                        baseAccomType.getAccomClass().getId(),
                        outputs,
                        productRateOffsetMethod,
                        productRateOffsetValue),
                true,
                true,
                false,
                false,
                false);

        DecisionDailybarOutput decisionDailybarOutput = new DecisionDailybarOutput();
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput.setProduct(barProduct);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput));

        DecisionDailybarOutput decisionDailybarOutput2 = new DecisionDailybarOutput();
        Product agileProductPrice = new Product();
        agileProductPrice.setSystemDefault(false);
        agileProductPrice.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        agileProductPrice.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        decisionDailybarOutput2.setProduct(agileProductPrice);

        assertFalse(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput2));
        assertFalse(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput2));

        DecisionDailybarOutput decisionDailybarOutput3 = new DecisionDailybarOutput();
        Product agileProductAdjustment = new Product();
        agileProductAdjustment.setSystemDefault(false);
        agileProductAdjustment.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        agileProductAdjustment.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        decisionDailybarOutput3.setProduct(agileProductAdjustment);

        assertTrue(service.isHiltonSendByAdjustment(mockCpDecisionContext, decisionDailybarOutput3));
        assertTrue(service.isHiltonUpdateValuesSentForExtraAdultExtraChild(mockCpDecisionContext, decisionDailybarOutput3));
    }

    @Test
    public void getAdjustment_adults() {
        //maxOccupancyType == null
        assertNull(service.getAdjustment(null, 5, OccupancyType.getNonBaseAdults(), OccupancyType.FOUR_ADULTS, BigDecimal.TEN));

        //(maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)
        assertNull(service.getAdjustment(OccupancyType.FOUR_ADULTS, 4, OccupancyType.getNonBaseAdults(), OccupancyType.FIVE_ADULTS, BigDecimal.TEN));

        //(maxOccupantQuantity == -1
        assertNull(service.getAdjustment(OccupancyType.EXTRA_ADULT, 5, OccupancyType.getNonBaseAdults(), OccupancyType.FIVE_ADULTS, BigDecimal.TEN));

        //occupancyType index is less than to the max, then return adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_ADULTS, 5, OccupancyType.getNonBaseAdults(), OccupancyType.THREE_ADULTS, BigDecimal.TEN));
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_ADULTS, 5, OccupancyType.getNonBaseAdults(), OccupancyType.FOUR_ADULTS, BigDecimal.TEN));

        //occupancyType index is equal to the max, then return adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_ADULTS, 5, OccupancyType.getNonBaseAdults(), OccupancyType.FIVE_CHILDREN, BigDecimal.TEN));

        //occupancyType index is greater than the max, then return null
        assertNull(service.getAdjustment(OccupancyType.FOUR_ADULTS, 5, OccupancyType.getNonBaseAdults(), OccupancyType.FIVE_ADULTS, BigDecimal.TEN));
    }

    @Test
    public void getAdjustment_children() {
        //maxOccupancyType == null
        assertNull(service.getAdjustment(null, 5, OccupancyType.getChildQuantities(), OccupancyType.FOUR_CHILDREN, BigDecimal.TEN));

        //(maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)
        assertNull(service.getAdjustment(OccupancyType.FOUR_CHILDREN, 4, OccupancyType.getChildQuantities(), OccupancyType.FIVE_CHILDREN, BigDecimal.TEN));

        //(maxOccupantQuantity == -1
        assertNull(service.getAdjustment(OccupancyType.EXTRA_CHILD, 5, OccupancyType.getChildQuantities(), OccupancyType.FIVE_CHILDREN, BigDecimal.TEN));

        //occupancyType index is less than to the max, then return adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.ONE_CHILD, BigDecimal.TEN));
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.TWO_CHILDREN, BigDecimal.TEN));
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.THREE_CHILDREN, BigDecimal.TEN));
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.FOUR_CHILDREN, BigDecimal.TEN));

        //occupancyType index is equal to the max, then return adjustment value
        assertEquals(BigDecimal.TEN, service.getAdjustment(OccupancyType.FIVE_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.FIVE_CHILDREN, BigDecimal.TEN));

        //occupancyType index is greater than the max, then return null
        assertNull(service.getAdjustment(OccupancyType.FOUR_CHILDREN, 5, OccupancyType.getChildQuantities(), OccupancyType.FIVE_CHILDREN, BigDecimal.TEN));
    }

    @Test
    public void getBarDailyBarDecisionChildRate() {
        LocalDate localDate = new LocalDate();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        Product product = new Product();
        product.setName("product");
        product.setId(1);
        Product linkProduct = new Product();
        linkProduct.setName("linkProduct");
        linkProduct.setId(2);
        linkProduct.setDependentProductId(1);
        DecisionDailybarOutput currentDailybarOutput = new DecisionDailybarOutput();
        currentDailybarOutput.setAccomType(accomType);
        currentDailybarOutput.setOccupancyDate(localDate);
        currentDailybarOutput.setProduct(linkProduct);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = new ArrayList<>();
        DecisionDailybarOutput barDecisionDailyBarOutput = new DecisionDailybarOutput();
        barDecisionDailyBarOutput.setAccomType(accomType);
        barDecisionDailyBarOutput.setOccupancyDate(localDate);
        barDecisionDailyBarOutput.setProduct(product);
        barDecisionDailyBarOutput.setChildAgeOneRate(BigDecimal.valueOf(10.00));
        barDecisionDailyBarOutput.setChildAgeTwoRate(BigDecimal.valueOf(11.00));
        barDecisionDailyBarOutput.setChildAgeThreeRate(BigDecimal.valueOf(12.00));
        barDecisionDailybarOutputs.add(barDecisionDailyBarOutput);

        assertNull(service.getBarDailyBarDecisionChildRate(currentDailybarOutput, new ArrayList<>(), OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(10.00), service.getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1));
        assertEquals(BigDecimal.valueOf(11.00), service.getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2));
        assertEquals(BigDecimal.valueOf(12.00), service.getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3));
    }

    @Test
    public void populationNonHiltonCrsTest() {
        // setting of toggle's value
        when(pacmanConfigParamsService.getBooleanParameterValue(NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED)).thenReturn(false);

        String accomTypeCode = "QN";

        // today's optimization decisions for two days
        BigDecimal optimalBAR_QN_today = new BigDecimal("95.00");
        BigDecimal optimalBAR_QN_tomorrow = new BigDecimal("126.25");

        // Existing DailybarOutputs for two Days
        BigDecimal dailybarOutputBAR_QN_today = new BigDecimal("105.25");
        BigDecimal dailybarOutputBAR_QN_tomorrow = new BigDecimal("116.25");

        // Use mockito to take care of the config parameter service requirements
        updatePricingAccomClassData(accomTypeCode, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Add a CPDecisionBAROutput record
        addCPDecisionBAROutput(accomTypeCode, startDate, optimalBAR_QN_today);
        addCPDecisionBAROutput(accomTypeCode, nextToStartDate, optimalBAR_QN_tomorrow);

        // Add a DecisionDailybarOutput record
        addDecisionDailybarOutput(accomTypeCode, startDate, dailybarOutputBAR_QN_today, dailybarOutputBAR_QN_today.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);
        addDecisionDailybarOutput(accomTypeCode, nextToStartDate, dailybarOutputBAR_QN_tomorrow, dailybarOutputBAR_QN_tomorrow.add(BigDecimal.TEN), BigDecimal.TEN, BigDecimal.TEN);

        service.recommendFinalBARs(nextDecisionId, startDate, nextToStartDate);

        // Verify decision updated rows are only for two room type.
        int decisionDailybarOutputNonHiltonCRSCount = findCountDecisionDailybarOutputNonHiltonCRS(nextDecisionId);
        int paceDailybarOutputNonHiltonCRSCount = findCountPaceDailybarOutputNonHiltonCRS(nextDecisionId);

        //verify that nothing was populated as toggle is false
        assertEquals(0, decisionDailybarOutputNonHiltonCRSCount);
        assertEquals(0, paceDailybarOutputNonHiltonCRSCount);
    }

    @Test
    void handle_free_night_calculate_even_when_sync_disabled() {
        // Given
//        CPRecommendationService service = mock(CPRecommendationService.class);
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setFreeNightEnabled(true);
        fn.setOffsetForExtraAdult(true);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);
        Integer decisionId = 100;
        RateUnqualified rateUnqualified = mock(RateUnqualified.class);
        TableBatch tableBatchNonHiltonCRS = null;
        CPBarDecisionChange cpBarDecisionChange = mock(CPBarDecisionChange.class);
        List<CPDecisionBAROutput> productOutputsForComparison = new ArrayList<>();
        Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs = new HashMap<>();

        // When
        when(cpBarDecisionChange.isUploadable()).thenReturn(true);
        when(cpBarDecisionChange.isUpdateRequired()).thenReturn(false);
        when(cpBarDecisionChange.getCPDecisionBAROutput()).thenReturn(cpDecisionBAROutput);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        when(cpBarDecisionChange.getPreviousDailybarOutput()).thenReturn(currentDailybarOutput);

        service.handleCPBarDecisionChange(decisionId, rateUnqualified, context, tableBatch,
                tableBatchNonHiltonCRS, cpBarDecisionChange, productOutputsForComparison,
                barDecisionDailybarOutputs, previousDecisionDailybarOutputs, bar);
        verify(service.tenantCrudService, times(1)).detach(Mockito.any(DecisionDailybarOutput.class));
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_checked_child_unchecked_ppp_off() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox checked
        Extra Child adjustment checkbox not checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(true);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_unchecked_child_unchecked_ppp_off() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox not checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_unchecked_child_unchecked_ppp_on() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox not checked
        PPP is on
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(barDailybarOutput.getOneChildRate(), currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(barDailybarOutput.getTwoChildRate(), currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(barDailybarOutput.getThreeChildRate(), currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFourChildRate(), currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFiveChildRate(), currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_unchecked_child_checked_ppp_off() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_unchecked_child_checked_ppp_on() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox checked
        PPP is on
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_free_night_adult_unchecked_child_checked_ppp_on_child_bucket_not_present() {
        /*
        Free night eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox checked
        PPP is on
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        barDailybarOutput.setChildAgeTwoRate(null);
        barDailybarOutput.setChildAgeThreeRate(null);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.valueOf(-100));
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getFiveChildRate());

        assertNotNull(currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(BigDecimal.ZERO, currentDailybarOutput.getChildAgeOneRate());
        assertNull(currentDailybarOutput.getChildAgeTwoRate());
        assertNull(currentDailybarOutput.getChildAgeThreeRate());
    }


    @Test
    void test_handleFreeNightProduct_non_free_night_adult_checked_child_unchecked_ppp_off() {
        /*
        Free night not eligible
        Extra Adult adjustment checkbox checked
        Extra Child adjustment checkbox not checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(true);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_non_free_night_adult_unchecked_child_unchecked_ppp_off() {
        /*
        Free night not eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox not checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_non_free_night_adult_unchecked_child_unchecked_ppp_on() {
        /*
        Free night not eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox not checked
        PPP is on
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(false);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(barDailybarOutput.getOneChildRate(), currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(barDailybarOutput.getTwoChildRate(), currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(barDailybarOutput.getThreeChildRate(), currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFourChildRate(), currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFiveChildRate(), currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_non_free_night_adult_unchecked_child_checked_ppp_off() {
        /*
        Free night not eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox checked
        PPP is off
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(false);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertNull(currentDailybarOutput.getOneChildRate());
        assertNull(currentDailybarOutput.getTwoChildRate());
        assertNull(currentDailybarOutput.getThreeChildRate());
        assertNull(currentDailybarOutput.getFourChildRate());
        assertNull(currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeNightProduct_non_free_night_adult_unchecked_child_checked_ppp_on() {
        /*
        Free night not eligible
        Extra Adult adjustment checkbox not checked
        Extra Child adjustment checkbox checked
        PPP is on
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, new HashMap<>());

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(barDailybarOutput.getOneChildRate(), currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(barDailybarOutput.getTwoChildRate(), currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(barDailybarOutput.getThreeChildRate(), currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFourChildRate(), currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFiveChildRate(), currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }


    @Test
    void test_handleFreeNightProduct_non_free_night_parent_decision_missing() {
        /*
        when bar decision is not changed, verify that decision is fetched from the previous bar decision list
         */

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        Product bar = getProduct(1, "bar", null);
        Product fn = getProduct(2, "fn", 1);
        fn.setOffsetForExtraAdult(false);
        fn.setOffsetForExtraChild(true);
        AccomType std = getAccomType(5, "STD");
        DecisionDailybarOutput currentDailybarOutput = getDecisionDailyBarOutput(fn, std, startDate);
        DecisionDailybarOutput barDailybarOutput = getDecisionDailyBarOutput(bar, std, startDate);
        addRatesForBar(barDailybarOutput);
        CPDecisionBAROutput cpDecisionBAROutput = getCPDecisionBAROutput(fn, std, startDate, BigDecimal.ZERO);
        Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs = new HashMap<>();
        DecisionDailybarOutputKey decisionDailybarOutputKey = new DecisionDailybarOutputKey(bar.getId(),
                barDailybarOutput.getOccupancyDate(), barDailybarOutput.getAccomType().getId());
        previousDecisionDailybarOutputs.put(decisionDailybarOutputKey, barDailybarOutput);
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = Collections.emptyList();//List.of(barDailybarOutput);
        TableBatch tableBatch = new TableBatch("procedure", "table");
        CPDecisionContext context = mock(CPDecisionContext.class);

        // When
        when(context.isPerPersonPricingEnabled()).thenReturn(true);
        service.handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs, tableBatch, fn, context, previousDecisionDailybarOutputs);

        // Then
        assertBigDecimalEquals(barDailybarOutput.getSingleRate(), currentDailybarOutput.getSingleRate());
        assertBigDecimalEquals(barDailybarOutput.getDoubleRate(), currentDailybarOutput.getDoubleRate());
        assertBigDecimalEquals(barDailybarOutput.getTripleRate(), currentDailybarOutput.getTripleRate());
        assertBigDecimalEquals(barDailybarOutput.getQuadRate(), currentDailybarOutput.getQuadRate());
        assertNull(currentDailybarOutput.getQuintRate());

        assertBigDecimalEquals(barDailybarOutput.getAdultRate(), currentDailybarOutput.getAdultRate());
        assertBigDecimalEquals(barDailybarOutput.getChildRate(), currentDailybarOutput.getChildRate());

        assertBigDecimalEquals(barDailybarOutput.getOneChildRate(), currentDailybarOutput.getOneChildRate());
        assertBigDecimalEquals(barDailybarOutput.getTwoChildRate(), currentDailybarOutput.getTwoChildRate());
        assertBigDecimalEquals(barDailybarOutput.getThreeChildRate(), currentDailybarOutput.getThreeChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFourChildRate(), currentDailybarOutput.getFourChildRate());
        assertBigDecimalEquals(barDailybarOutput.getFiveChildRate(), currentDailybarOutput.getFiveChildRate());

        assertBigDecimalEquals(barDailybarOutput.getChildAgeOneRate(), currentDailybarOutput.getChildAgeOneRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeTwoRate());
        assertBigDecimalEquals(barDailybarOutput.getChildAgeThreeRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    @Test
    void test_handleFreeUpgrade_free_night_toggle_off() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(false);

        //then
        service.handleFreeUpgrade(startDate, endDate);
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
    }

    @Test
    void test_handleFreeUpgrade_no_free_upgrade_products() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        when(context.getProductsInHierarchicalOrder()).thenReturn(getProductsInHierarchicalOrder());

        //then
        service.handleFreeUpgrade(startDate, endDate);
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
    }

    @Test
    void test_handleFreeUpgrade_external_system_incorrect() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.externalSystemHelper = externalSystemHelper;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productsInHierarchicalOrder = getProductsInHierarchicalOrder();
        updateFreeUpgradeFlag(productsInHierarchicalOrder, 2);
        when(context.getProductsInHierarchicalOrder()).thenReturn(productsInHierarchicalOrder);
        when(externalSystemHelper.isHilstar()).thenReturn(false);
        when(externalSystemHelper.isPCRS()).thenReturn(false);

        //then
        TetrisException thrownException = assertThrows(TetrisException.class, () -> {
            service.handleFreeUpgrade(startDate, endDate);
        });
        assertEquals(ErrorCode.UNEXPECTED_ERROR, thrownException.getErrorCode());
        assertEquals("External system must be HCRS/PCRS since there is a free upgrade product present", thrownException.getBaseMessage());
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
    }

    @Test
    void test_handleFreeUpgrade_hcrs_no_vendor_mapping() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.externalSystemHelper = externalSystemHelper;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productsInHierarchicalOrder = getProductsInHierarchicalOrder();
        updateFreeUpgradeFlag(productsInHierarchicalOrder, 2);
        when(context.getProductsInHierarchicalOrder()).thenReturn(productsInHierarchicalOrder);
        when(externalSystemHelper.isHilstar()).thenReturn(true);
        when(externalSystemHelper.isPCRS()).thenReturn(false);
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.HILSTAR.getCode())).thenReturn(Collections.emptyList());

        //then
        TetrisException thrownException = assertThrows(TetrisException.class, () -> service.handleFreeUpgrade(startDate, endDate));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, thrownException.getErrorCode());
        assertEquals("Vendor mapping should be configured for at least one accom type for free upgrade to work. None found for HILSTAR",
                thrownException.getBaseMessage());
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
    }

    @Test
    void test_handleFreeUpgrade_pcrs_no_vendor_mapping() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.externalSystemHelper = externalSystemHelper;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productsInHierarchicalOrder = getProductsInHierarchicalOrder();
        updateFreeUpgradeFlag(productsInHierarchicalOrder, 2);
        when(context.getProductsInHierarchicalOrder()).thenReturn(productsInHierarchicalOrder);
        when(externalSystemHelper.isHilstar()).thenReturn(false);
        when(externalSystemHelper.isPCRS()).thenReturn(true);
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(Collections.emptyList());

        //then
        TetrisException thrownException = assertThrows(TetrisException.class, () -> service.handleFreeUpgrade(startDate, endDate));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, thrownException.getErrorCode());
        assertEquals("Vendor mapping should be configured for at least one accom type for free upgrade to work. None found for PCRS", thrownException.getBaseMessage());
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
    }

    @Test
    void test_handleFreeUpgrade_vendor_mapping_present() {
        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.externalSystemHelper = externalSystemHelper;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productsInHierarchicalOrder = getProductsInHierarchicalOrder();
        updateFreeUpgradeFlag(productsInHierarchicalOrder, 2);
        when(context.getProductsInHierarchicalOrder()).thenReturn(productsInHierarchicalOrder);
        when(externalSystemHelper.isHilstar()).thenReturn(false);
        when(externalSystemHelper.isPCRS()).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeVendorMappings = getAccomTypeVendorMappings();
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeVendorMappings);
        List<Object> decisionDailybarOutputs = getDecisionDailybarOutputs();
        Map<String, Object> params = QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters();
        when(service.tenantCrudService.findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN, params)).thenReturn(decisionDailybarOutputs);
        DecisionDailybarOutput stdDecision = (DecisionDailybarOutput) decisionDailybarOutputs.get(0);
        DecisionDailybarOutput supDecision = (DecisionDailybarOutput) decisionDailybarOutputs.get(1);
        supDecision.setSingleRate(stdDecision.getSingleRate());
        supDecision.setDoubleRate(stdDecision.getDoubleRate());

        //then
        service.handleFreeUpgrade(startDate, endDate);
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(service.tenantCrudService, times(1)).save(List.of(supDecision));
    }

    @Test
    void test_handleFreeUpgrade_vendor_mapping_present_multiple() {
        // Accom types: A B C D
        // Mapping present: A->B, B->C, C->D

        // Given
        CPRecommendationService service = new CPRecommendationService();
        service.tenantCrudService = mock(CrudService.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        CPDecisionContext context = mock(CPDecisionContext.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.externalSystemHelper = externalSystemHelper;

        // when
        when(pricingConfigurationService.getCPDecisionContext(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(context);
        when(context.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productsInHierarchicalOrder = getProductsInHierarchicalOrder();
        updateFreeUpgradeFlag(productsInHierarchicalOrder, 2);
        when(context.getProductsInHierarchicalOrder()).thenReturn(productsInHierarchicalOrder);
        when(externalSystemHelper.isHilstar()).thenReturn(false);
        when(externalSystemHelper.isPCRS()).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeVendorMappings = getAccomTypeVendorMappingsMultiple();
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeVendorMappings);
        List<Object> decisionDailybarOutputs = getDecisionDailybarOutputsMultiple();
        Map<String, Object> params = QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters();
        when(service.tenantCrudService.findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN, params)).thenReturn(decisionDailybarOutputs);
        DecisionDailybarOutput updatedDecisionB = getUpdatedDecision((DecisionDailybarOutput) decisionDailybarOutputs.get(0),
                (DecisionDailybarOutput) decisionDailybarOutputs.get(1));
        DecisionDailybarOutput updatedDecisionC = getUpdatedDecision((DecisionDailybarOutput) decisionDailybarOutputs.get(1),
                (DecisionDailybarOutput) decisionDailybarOutputs.get(2));
        DecisionDailybarOutput updatedDecisionD = getUpdatedDecision((DecisionDailybarOutput) decisionDailybarOutputs.get(2),
                (DecisionDailybarOutput) decisionDailybarOutputs.get(3));
        List<DecisionDailybarOutput> updatedDecisions = List.of(updatedDecisionB, updatedDecisionC, updatedDecisionD);

        //then
        service.handleFreeUpgrade(startDate, endDate);
        verify(pricingConfigurationService, times(1)).getCPDecisionContext(startDate, endDate);
        verify(context, times(1)).getProductsInHierarchicalOrder();
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(service.tenantCrudService, times(1)).save(upgradedDecisions.capture());
        List<DecisionDailybarOutput> capturedValue = upgradedDecisions.getValue();
        verifyRatesMatch(updatedDecisions, capturedValue);
    }

    private void verifyRatesMatch(List<DecisionDailybarOutput> updatedDecisions, List<DecisionDailybarOutput> capturedValue) {
        Map<String, DecisionDailybarOutput> expected = updatedDecisions.stream().collect(Collectors.toMap(d -> d.getAccomType().getAccomTypeCode(), Function.identity()));
        Map<String, DecisionDailybarOutput> actual = capturedValue.stream().collect(Collectors.toMap(d -> d.getAccomType().getAccomTypeCode(), Function.identity()));
        expected.forEach((at, expectedDecision) -> {
            DecisionDailybarOutput capturedDecision = actual.get(at);
            assertEquals(expectedDecision.getSingleRate(), capturedDecision.getSingleRate());
            assertEquals(expectedDecision.getDoubleRate(), capturedDecision.getDoubleRate());
        });
    }

    private DecisionDailybarOutput getUpdatedDecision(DecisionDailybarOutput source, DecisionDailybarOutput target) {
        DecisionDailybarOutput updated = new DecisionDailybarOutput();
        updated.setProduct(target.getProduct());
        updated.setAccomType(target.getAccomType());
        updated.setOccupancyDate(target.getOccupancyDate());
        updated.setSingleRate(source.getSingleRate());
        updated.setDoubleRate(source.getDoubleRate());
        return updated;
    }

    private List<Object> getDecisionDailybarOutputs() {
        List<Object> decisions = new ArrayList<>();
        Product fn1 = getProduct(2, "fn", 1, true, true);
        Product fn2 = getProduct(3, "fn", 1, true, true);
        AccomType std = getAccomType(5, "STD");
        AccomType sup = getAccomType(8, "SUP");
        DecisionDailybarOutput d1 = createDecisionDailybarOutput(fn1, std, startDate, BigDecimal.valueOf(100), BigDecimal.valueOf(200));
        DecisionDailybarOutput d2 = createDecisionDailybarOutput(fn1, sup, startDate, BigDecimal.valueOf(120), BigDecimal.valueOf(240));
        decisions.add(d1);
        decisions.add(d2);
        return decisions;
    }

    private List<Object> getDecisionDailybarOutputsMultiple() {
        List<Object> decisions = new ArrayList<>();
        Product fn1 = getProduct(2, "fn", 1, true, true);
        AccomType atA = getAccomType(5, "A");
        AccomType atB = getAccomType(6, "B");
        AccomType atC = getAccomType(7, "C");
        AccomType atD = getAccomType(8, "D");
        DecisionDailybarOutput d1 = createDecisionDailybarOutput(fn1, atA, startDate, BigDecimal.valueOf(100), BigDecimal.valueOf(200));
        DecisionDailybarOutput d2 = createDecisionDailybarOutput(fn1, atB, startDate, BigDecimal.valueOf(120), BigDecimal.valueOf(220));
        DecisionDailybarOutput d3 = createDecisionDailybarOutput(fn1, atC, startDate, BigDecimal.valueOf(140), BigDecimal.valueOf(240));
        DecisionDailybarOutput d4 = createDecisionDailybarOutput(fn1, atD, startDate, BigDecimal.valueOf(160), BigDecimal.valueOf(260));
        decisions.add(d1);
        decisions.add(d2);
        decisions.add(d3);
        decisions.add(d4);
        return decisions;
    }

    private DecisionDailybarOutput createDecisionDailybarOutput(Product product, AccomType accomType, LocalDate occupancyDate,
                                                                BigDecimal singleRate, BigDecimal doubleRate) {
        DecisionDailybarOutput d = new DecisionDailybarOutput();
        d.setProduct(product);
        d.setAccomType(accomType);
        d.setOccupancyDate(occupancyDate);
        d.setSingleRate(singleRate);
        d.setDoubleRate(doubleRate);
        return d;
    }

    private List<AccomTypeVendorMapping> getAccomTypeVendorMappings() {
        AccomTypeVendorMapping m1 = new AccomTypeVendorMapping("STD", "PCRS", "SUP");
        return List.of(m1);
    }

    private List<AccomTypeVendorMapping> getAccomTypeVendorMappingsMultiple() {
        AccomTypeVendorMapping m1 = new AccomTypeVendorMapping("A", "PCRS", "B");
        AccomTypeVendorMapping m2 = new AccomTypeVendorMapping("B", "PCRS", "C");
        AccomTypeVendorMapping m3 = new AccomTypeVendorMapping("C", "PCRS", "D");
        return List.of(m1, m2, m3);
    }

    private CPDecisionBAROutput getCPDecisionBAROutput(Product product, AccomType accomType, LocalDate arrivalDate, BigDecimal optimalBar) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        cpDecisionBAROutput.setOptimalBAR(optimalBar);
        return cpDecisionBAROutput;
    }

    private void addRatesForBar(DecisionDailybarOutput b) {
        b.setSingleRate(BigDecimal.valueOf(130.45));
        b.setDoubleRate(BigDecimal.valueOf(150.45));
        b.setTripleRate(BigDecimal.valueOf(170.45));
        b.setQuadRate(BigDecimal.valueOf(190.45));
        b.setQuintRate(null);
        b.setChildRate(BigDecimal.valueOf(100.45));
        b.setAdultRate(BigDecimal.valueOf(120.45));
        b.setOneChildRate(BigDecimal.valueOf(10.45));
        b.setTwoChildRate(BigDecimal.valueOf(20.45));
        b.setThreeChildRate(BigDecimal.valueOf(30.45));
        b.setFourChildRate(BigDecimal.valueOf(40.45));
        b.setFiveChildRate(BigDecimal.valueOf(50.45));
        b.setChildAgeOneRate(BigDecimal.valueOf(10.45));
        b.setChildAgeTwoRate(BigDecimal.valueOf(20.45));
        b.setChildAgeThreeRate(BigDecimal.valueOf(30.45));
    }

    private AccomType getAccomType(int id, String name) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(name);
        accomType.setAccomTypeCode(name);
        return accomType;
    }

    private Product getProduct(int id, String name, Integer dependentProductId) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        product.setDependentProductId(dependentProductId);
        return product;
    }

    private Product getProduct(int id, String name, Integer dependentProductId, boolean isFreeNightEnabled, boolean isFreeUpgradeEnabled) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        product.setDependentProductId(dependentProductId);
        product.setFreeNightEnabled(isFreeNightEnabled);
        product.setFreeUpgradeEnabled(isFreeUpgradeEnabled);
        return product;
    }

    private DecisionDailybarOutput getDecisionDailyBarOutput(Product product, AccomType accomType, LocalDate occupancyDate) {
        DecisionDailybarOutput decisionDailybarOutput = new DecisionDailybarOutput();
        decisionDailybarOutput.setRateUnqualified(new RateUnqualified());
        decisionDailybarOutput.setProduct(product);
        decisionDailybarOutput.setAccomType(accomType);
        decisionDailybarOutput.setOccupancyDate(occupancyDate);
        return decisionDailybarOutput;
    }

    private Map<Product, List<Product>> getProductsInHierarchicalOrder() {
        Product bar = getProduct(1, "bar", null, false, false);
        Product fn1 = getProduct(2, "fn", 1, true, false);
        Product fn2 = getProduct(3, "fn", 1, true, false);
        Map<Product, List<Product>> productListMap = new HashMap<>();
        productListMap.put(bar, List.of(bar, fn1, fn2));
        return productListMap;
    }

    private void updateFreeUpgradeFlag(Map<Product, List<Product>> productListMap, int id) {
        List<Product> productList = productListMap.get(getProduct(1, "bar", null, false, false));
        productList.forEach(p -> {
            if (p.getId() == id) {
                p.setFreeUpgradeEnabled(true);
            }
        });
    }
}