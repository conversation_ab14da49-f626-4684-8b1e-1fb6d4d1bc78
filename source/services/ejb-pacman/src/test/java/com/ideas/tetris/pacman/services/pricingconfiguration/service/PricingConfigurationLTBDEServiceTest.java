package com.ideas.tetris.pacman.services.pricingconfiguration.service;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfiguration;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJodaLocalDate;
import static java.time.LocalDate.now;
import static java.time.LocalDate.parse;
import static java.util.List.of;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

public class PricingConfigurationLTBDEServiceTest {

    private static java.time.LocalDate systemDate = parse("2023-01-01");

    @Mock
    private TenantCrudServiceBean crudService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private DateService dateService;

    @InjectMocks
    private PricingConfigurationLTBDEService service;

    @BeforeEach
    public void setup() {
        initMocks(this);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);
    }

    @Test
    public void shouldNotEnableLTBDEIfToggleIsOff() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);

        service.enabledLTBDEIfApplicable();
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfApplicable(now().plusDays(1000), now());
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(), systemDate);
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(new HashSet<>());
        verify(crudService, never()).save(any(CPConfiguration.class));
    }

    @Test
    public void shouldNotEnableLTBDEIfNoSmallGroupProductIsConfiguredForProperty() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of());

        service.enabledLTBDEIfApplicable();
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfApplicable(now().plusDays(1000), now());
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(), systemDate);
        verify(crudService, never()).save(any(CPConfiguration.class));

        service.enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(new HashSet<>());
        verify(crudService, never()).save(any(CPConfiguration.class));
    }

    @Test
    public void shouldEnableLTBDEWhenChangedRoomClassIsAttachedWithActiveSmallGroup() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        Product smallGroupProduct = getSmallGroupProduct();
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(eq(ProductAccomType.BY_PRODUCTS), anyMap())).thenReturn(of(getProductAccomType()));

        Set<Integer> roomTypeIds = new HashSet<>();
        roomTypeIds.add(15);
        service.enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(roomTypeIds);
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableLTBDEWhenChangedRoomClassIsNotAttachedWithActiveSmallGroup() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        Product smallGroupProduct = getSmallGroupProduct();
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(eq(ProductAccomType.BY_PRODUCTS), anyMap())).thenReturn(of(getProductAccomType()));

        Set<Integer> roomTypeIds = new HashSet<>();
        roomTypeIds.add(5);
        service.enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(roomTypeIds);
        verify(crudService, never()).save(any(CPConfiguration.class));
    }

    @Test
    public void shouldEnableLTBDEOnMeetingAllApplicableConditions() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        Product smallGroupProduct = getSmallGroupProduct();
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEIfApplicable();
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldEnableLTBDEForSeasonsIfEndDateIsInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        Product smallGroupProduct = getSmallGroupProduct();
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        service.enabledLTBDEIfApplicable(now().plusDays(366), now());
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableLTBDEForSeasonsIfEndDateIsInOptimizationWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        Product smallGroupProduct = getSmallGroupProduct();
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        service.enabledLTBDEIfApplicable(now().plusDays(364), now());
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldEnableLTBDEForCeilingFloorIfUpdatedDefaultCeilingFloorIsPassed() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType pricingBaseAccomType = getPricingBaseAccomType(accomType, new BigDecimal(80.98), new BigDecimal(101.88));

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(pricingBaseAccomType));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomType(accomType, new BigDecimal(81.98), new BigDecimal(102.88))), systemDate);
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableLTBDEForCeilingFloorIfSameDefaultCeilingFloorIsPassed() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType defaultCeilingFloor = getPricingBaseAccomType(accomType, new BigDecimal(80.98), new BigDecimal(101.88));
        PricingBaseAccomType seasonCeilingFloor = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(82.98), new BigDecimal(103.88), "2023-04-05", "2023-04-09");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(defaultCeilingFloor, seasonCeilingFloor));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomType(accomType, new BigDecimal(80.98), new BigDecimal(101.88))), systemDate);
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldEnableLTBDEForCeilingFloorIfUpdatedSeasonCeilingFloorIsPassed() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType pricingBaseAccomType = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(101.88), "2028-01-01", "2028-01-04");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(pricingBaseAccomType));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(102.88), "2028-01-01", "2028-01-04")), systemDate);
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldEnableLTBDEForCeilingFloorOnCreatingNewSeason() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType defaultCeilingFloor = getPricingBaseAccomType(accomType, new BigDecimal(80.98), new BigDecimal(101.88));
        PricingBaseAccomType existingSeason = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(101.88), "2023-01-01", "2023-01-04");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(defaultCeilingFloor, existingSeason));

        PricingBaseAccomType newSeason = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(102.88),
                "2028-01-05", "2028-01-08");
        newSeason.setId(null);
        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(newSeason), systemDate);
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableLTBDEForCeilingFloorIfSeasonBeingCreatedOrModifiedIsWithinOptimizationWindow() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType defaultCeilingFloor = getPricingBaseAccomType(accomType, new BigDecimal("80.98"), new BigDecimal("101.88"));
        PricingBaseAccomType existingSeason = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal("80.98"), new BigDecimal("101.88"), "2023-01-01", "2023-01-04");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(defaultCeilingFloor, existingSeason));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomTypeForSeason(accomType,
                new BigDecimal("80.98"), new BigDecimal("102.88"), "2023-01-05", "2023-01-08")), systemDate);
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldEnableLTBDEForCeilingFloorIfSeasonBeingModifiedIsBeyondOptimizationWindow() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType defaultCeilingFloor = getPricingBaseAccomType(accomType, new BigDecimal("80.98"), new BigDecimal("101.88"));
        PricingBaseAccomType existingSeason = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal("80.98"), new BigDecimal("101.88"), "2027-01-01", "2027-01-04");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(defaultCeilingFloor, existingSeason));

        //season dates modified to be within optimization window. Still, the LTBDE should be triggered since the original season was beyond optimization window.
        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomTypeForSeason(accomType,
                new BigDecimal("80.98"), new BigDecimal("102.88"), "2023-01-05", "2023-01-08")), systemDate);
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableLTBDEForCeilingFloorIfSameSeasonCeilingFloorIsPassed() {
        Product smallGroupProduct = getSmallGroupProduct();
        AccomType accomType = accomType();
        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomType);
        Product barProduct = getBarProduct();
        PricingBaseAccomType pricingBaseAccomType = getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(101.88), "2023-01-01", "2023-01-03");

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
        when(crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("productId", barProduct.getId()).parameters()))
                .thenReturn(of(pricingBaseAccomType));

        service.enabledLTBDEIfApplicableForCeilingFloorChange(of(getPricingBaseAccomTypeForSeason(accomType, new BigDecimal(80.98), new BigDecimal(101.88), "2023-01-01", "2023-01-03")), systemDate);
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    public void shouldNotEnableOverrideForExtendedWindowIfToggleIsOff() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(false);
        service.enableOverrideForExtendedWindowIfApplicable(parse("2024-03-01"), parse("2023-01-01"));
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }

    @Test
    public void shouldNotEnableOverrideForExtendedWindowIfToggleIsOff_MultipleArrivalDates() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(false);
        service.enableOverrideForExtendedWindowIfApplicable(of(parse("2024-03-01"), parse("2023-10-01")), parse("2023-01-01"));
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }

    @Test
    public void shouldNotEnableOverrideForExtendedWindowIfDateIsInStandardWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        service.enableOverrideForExtendedWindowIfApplicable(parse("2023-03-01"), parse("2023-01-01"));
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }

    @Test
    public void shouldNotEnableOverrideForExtendedWindowIfAllDatesAreInStandardWindow_MultipleArrivalDates() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        service.enableOverrideForExtendedWindowIfApplicable(of(parse("2023-03-01"), parse("2023-10-01")), parse("2023-01-01"));
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }


    @Test
    public void shouldEnableOverrideForExtendedWindowIfDateIsInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(new CPConfiguration());

        service.enableOverrideForExtendedWindowIfApplicable(parse("2024-03-01"), parse("2023-01-01"));
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }

    @Test
    public void shouldEnableOverrideForExtendedWindowIfAtLeastOneDateIsInExtendedWindow_MultipleArrivalDates() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(new CPConfiguration());

        service.enableOverrideForExtendedWindowIfApplicable(of(parse("2023-01-01"), parse("2024-03-01")), parse("2023-01-01"));
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableOverrideForExtendedWindow()));
    }

    @Test
    public void shouldReturnFalseIfLTBDEToggleIsOff_isDateInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(false);

        assertFalse(service.isDateInExtendedWindowForOverrides(parse("2023-01-01"), parse("2022-01-01")));
    }

    @Test
    public void shouldReturnFalseIfDateIsInOptimizationWindow_isDateInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        assertFalse(service.isDateInExtendedWindowForOverrides(parse("2022-06-01"), parse("2022-01-01")));
    }

    @Test
    public void shouldReturnTrueIfDateIsBeyondOptimizationWindow_isDateInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        assertTrue(service.isDateInExtendedWindowForOverrides(parse("2023-06-01"), parse("2022-01-01")));
    }

    @Test
    public void shouldReturnTrueIfDateIsExactlyAtOptimizationWindow_isDateInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        assertTrue(service.isDateInExtendedWindowForOverrides(parse("2023-01-01"), parse("2022-01-01")));
    }

    @Test
    public void shouldReturnFalseIfDateIsOneDayLessThanOptimizationWindow_isDateInExtendedWindow() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);

        assertFalse(service.isDateInExtendedWindowForOverrides(parse("2022-12-31"), parse("2022-01-01")));
    }

    @Test
    void shouldNotEnableLTBDEIfThereAreNoSupplementsToSave() {
        service.enabledLTBDEIfApplicableForSupplementChange(Collections.emptyList(), parse("2024-01-01"));

        verify(configParamsService, times(0)).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE);
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEWhenLTBDEToggleIsDisabled() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);

        service.enabledLTBDEIfApplicableForSupplementChange(of(new AccomTypeSupplement()), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenThereAreNoActiveSmallGroupProductsAvailable() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of());

        service.enabledLTBDEIfApplicableForSupplementChange(of(new AccomTypeSupplement()), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLBDEFlagForPricingWhenNewDefaultSupplementIsAdded() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(3);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEFlagForPricingWhenSupplementSeasonIsUpdatedButSeasonIsNotBeyondOptimizedWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenSupplementSeasonIsUpdatedWithStartDateOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        populateSupplementValues(updatedAccomTypeSupplement, BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        populateSupplementValues(existingAccomTypeSupplement, BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenSupplementSeasonIsUpdatedWithEndDateOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        populateSupplementValues(updatedAccomTypeSupplement, BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        populateSupplementValues(existingAccomTypeSupplement, BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-03")));
        existingAccomTypeSupplement.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenSupplementSeasonIsUpdatedWithSupplementValueOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setMondaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setTuesdaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setWednesdaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setThursdaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setFridaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setSaturdaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenSupplementSeasonIsUpdatedWithOffsetMethodOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        populateSupplementValues(updatedAccomTypeSupplement, BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.PERCENTAGE);
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        populateSupplementValues(existingAccomTypeSupplement, BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBEForPricingFlagWhenSupplementSeasonIsAddedAndIsNotBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2023-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenSupplementSeasonNameIsChangedAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setName("Season updated");
        populateSupplementValues(updatedAccomTypeSupplement, new BigDecimal("0.0"));
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setName("SeasonName");
        populateSupplementValues(existingAccomTypeSupplement, new BigDecimal("0.0"));
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters()))
                .thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBEForPricingFlagWhenSupplementSeasonIsAddedAndIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBEForPricingFlagWhenSupplementSeasonIsDeletedAndIsNotBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setDeleted(true);
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setDeleted(false);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(existingAccomTypeSupplement), parse("2023-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBEForPricingFlagWhenSupplementSeasonIsDeletedAndIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setDeleted(true);
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setDeleted(false);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingWhenNonBaseRoomTypeIsAssociatedWithAnyGroupProduct() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenNonBaseRoomTypeIsNotAssociatedWithAnyGroupProduct() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsAssociatedWithAnyGroupProduct() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndAnyOtherRoomTypesOfSameRoomClassOfBaseRoomType() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndButAnyOtherRoomTypesOfSameRoomClassOfBaseRoomTypeIsAssociated() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setId(1001);
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForStandard2 = accomType();
        accomTypeForStandard2.setAccomClass(standardAccomClass);
        accomTypeForStandard2.setId(201);

        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard, accomTypeForStandard2));

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard2);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenSupplementChangesMadeInNonPrimaryPricedProduct() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(5);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(2);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForSupplementChange(of(updatedAccomTypeSupplement), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenSeasonISDeletedForNonPrimaryProduct() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setDeleted(true);
        updatedAccomTypeSupplement.setProductID(7);


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setDeleted(false);
        existingAccomTypeSupplement.setProductID(7);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingWhenNonBaseRoomTypeIsAssociatedWithAnyGroupProduct_WhileDeletingSeason() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenNonBaseRoomTypeIsNotAssociatedWithAnyGroupProduct_WhileDeletingSeason() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsAssociatedWithAnyGroupProduct_WhileDeletingSeason() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));


        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndAnyOtherRoomTypesOfSameRoomClassOfBaseRoomType_WhileDeletingSeason() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndButAnyOtherRoomTypesOfSameRoomClassOfBaseRoomTypeIsAssociated_WhileDeletingSeason() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setId(1001);
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForStandard2 = accomType();
        accomTypeForStandard2.setAccomClass(standardAccomClass);
        accomTypeForStandard2.setId(201);

        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard, accomTypeForStandard2));

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setProductID(1);
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard2);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeSupplement));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-01-01"), parse("2022-01-01"));
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenLTBDEForPricingConfigToggleIsDisabled_WhileDeleting() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setDeleted(true);
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setDeleted(false);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenThereAreNoSmallGroupProductsAreConfigured_WhileDeleting() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        AccomTypeSupplement updatedAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        updatedAccomTypeSupplement.setAccomType(accomTypeForStandard);
        updatedAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        updatedAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeSupplement.setDeleted(true);
        updatedAccomTypeSupplement.setProductID(1);

        AccomTypeSupplement existingAccomTypeSupplement = new AccomTypeSupplement();
        updatedAccomTypeSupplement.setId(1);
        existingAccomTypeSupplement.setAccomType(accomTypeForStandard);
        existingAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        existingAccomTypeSupplement.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeSupplement.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeSupplement.setDeleted(false);
        existingAccomTypeSupplement.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForSupplementDeleteIfApplicable(of(updatedAccomTypeSupplement), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEIfThereAreNoOffsetsToSave() {
        service.enabledLTBDEIfApplicableForOffsetChange(Collections.emptyList(), parse("2024-01-01"));

        verify(configParamsService, times(0)).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE);
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEWhenLTBDEToggleIsDisabled_for_offset_change() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);

        service.enabledLTBDEIfApplicableForOffsetChange(of(new CPConfigOffsetAccomType()), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenThereAreNoActiveSmallGroupProductsAvailable_for_offset_change() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of());

        service.enabledLTBDEIfApplicableForOffsetChange(of(new CPConfigOffsetAccomType()), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLBDEFlagForPricingWhenNewDefaultOffsetIsAdded() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(3);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEFlagForPricingWhenOffsetSeasonIsUpdatedButSeasonIsNotBeyondOptimizedWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenOffsetSeasonIsUpdatedWithStartDateOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        populateOffsetValues(updatedAccomTypeOffset, BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        populateOffsetValues(existingAccomTypeOffset, BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenOffsetSeasonIsUpdatedWithEndDateOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        populateOffsetValues(updatedAccomTypeOffset, BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        populateOffsetValues(existingAccomTypeOffset, BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-03")));
        existingAccomTypeOffset.setProductID(1);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenOffsetSeasonIsUpdatedWithOffsetValueOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setMondayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setTuesdayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setWednesdayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setThursdayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setFridayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setSaturdayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEFlagForPricingWhenOffsetSeasonIsUpdatedWithOffsetMethodOnlyAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        populateOffsetValues(updatedAccomTypeOffset, BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        populateOffsetValues(existingAccomTypeOffset, BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBEForPricingFlagWhenOffsetSeasonIsAddedAndIsNotBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2023-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenOffsetSeasonNameIsChangedAndSeasonIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setName("Season updated");
        populateOffsetValues(updatedAccomTypeOffset, new BigDecimal("0.0"));
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setName("SeasonName");
        populateOffsetValues(existingAccomTypeOffset, new BigDecimal("0.0"));
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters()))
                .thenReturn(of(existingAccomTypeOffset));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBEForPricingFlagWhenOffsetSeasonIsAddedAndIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBEForPricingFlagWhenOffsetSeasonIsDeletedAndIsNotBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setDeleted(true);
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setDeleted(false);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(existingAccomTypeOffset), parse("2023-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBEForPricingFlagWhenOffsetSeasonIsDeletedAndIsBeyondOptimizationWindow() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setDeleted(true);
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setDeleted(false);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingWhenNonBaseRoomTypeIsAssociatedWithAnyGroupProduct_for_offset_change() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenNonBaseRoomTypeIsNotAssociatedWithAnyGroupProduct_for_offset_change() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsAssociatedWithAnyGroupProduct_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndAnyOtherRoomTypesOfSameRoomClassOfBaseRoomType_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndButAnyOtherRoomTypesOfSameRoomClassOfBaseRoomTypeIsAssociated_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setId(1001);
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForStandard2 = accomType();
        accomTypeForStandard2.setAccomClass(standardAccomClass);
        accomTypeForStandard2.setId(201);

        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard, accomTypeForStandard2));

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard2);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenOffsetChangesMadeInNonPrimaryPricedProduct() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);


        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(5);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(2);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEIfApplicableForOffsetChange(of(updatedAccomTypeOffset), parse("2024-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenSeasonISDeletedForNonPrimaryProduct_for_offset_change() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setDeleted(true);
        updatedAccomTypeOffset.setProductID(7);


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setDeleted(false);
        existingAccomTypeOffset.setProductID(7);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingWhenNonBaseRoomTypeIsAssociatedWithAnyGroupProduct_WhileDeletingSeason_for_offset_change() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenNonBaseRoomTypeIsNotAssociatedWithAnyGroupProduct_WhileDeletingSeason_for_offset_change() {

        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(null);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsAssociatedWithAnyGroupProduct_WhileDeletingSeason_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));


        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndAnyOtherRoomTypesOfSameRoomClassOfBaseRoomType_WhileDeletingSeason_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));

        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);


        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForDeluxe);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-01-01"), parse("2022-01-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldEnableLTBDEForPricingFlagWhenBaseRoomTypeIsNotAssociatedWithAnyGroupProductAndButAnyOtherRoomTypesOfSameRoomClassOfBaseRoomTypeIsAssociated_WhileDeletingSeason_for_offset_change() {
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setId(1001);
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        AccomType accomTypeForStandard2 = accomType();
        accomTypeForStandard2.setAccomClass(standardAccomClass);
        accomTypeForStandard2.setId(201);

        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard, accomTypeForStandard2));

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setProductID(1);
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-01")));

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard2);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standardAccomClass);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(of(existingAccomTypeOffset));
        when(crudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeForStandard.getId()).parameters())).thenReturn(pricingAccomClass);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());


        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-01-01"), parse("2022-01-01"));
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenLTBDEForPricingConfigToggleIsDisabled_WhileDeleting_for_offset_change() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setDeleted(true);
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setDeleted(false);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));

    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenThereAreNoSmallGroupProductsAreConfigured_WhileDeleting_for_offset_change() {
        AccomType accomTypeForDeluxe = accomType();
        AccomClass deluxeAccomClass = new AccomClass();
        deluxeAccomClass.setName("DELUXE");
        deluxeAccomClass.setAccomTypes(Set.of(accomTypeForDeluxe));
        AccomType accomTypeForStandard = new AccomType();
        AccomClass standardAccomClass = new AccomClass();
        standardAccomClass.setName("STANDARD");
        standardAccomClass.setAccomTypes(Set.of(accomTypeForStandard));
        accomTypeForStandard.setAccomClass(standardAccomClass);
        accomTypeForStandard.setId(101);

        accomTypeForDeluxe.setAccomClass(deluxeAccomClass);
        accomTypeForDeluxe.setId(201);

        CPConfigOffsetAccomType updatedAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        updatedAccomTypeOffset.setAccomType(accomTypeForStandard);
        updatedAccomTypeOffset.setSundayOffsetValue(BigDecimal.ONE);
        updatedAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        updatedAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        updatedAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        updatedAccomTypeOffset.setDeleted(true);
        updatedAccomTypeOffset.setProductID(1);

        CPConfigOffsetAccomType existingAccomTypeOffset = new CPConfigOffsetAccomType();
        updatedAccomTypeOffset.setId(1);
        existingAccomTypeOffset.setAccomType(accomTypeForStandard);
        existingAccomTypeOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        existingAccomTypeOffset.setStartDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-01-02")));
        existingAccomTypeOffset.setEndDate(JavaLocalDateUtils.toJodaLocalDate(parse("2024-02-02")));
        existingAccomTypeOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        existingAccomTypeOffset.setDeleted(false);
        existingAccomTypeOffset.setProductID(1);


        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        ProductAccomType productAccomType = getProductAccomType(smallGroupProduct, accomTypeForStandard);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", Set.of(smallGroupProduct)).parameters())).thenReturn(of(productAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", getBarProduct().getId()).parameters())).thenReturn(Collections.emptyList());
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(getBarProduct());

        service.enabledLTBDEForOffsetDeleteIfApplicable(of(updatedAccomTypeOffset), parse("2024-02-02"), parse("2022-12-01"));

        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldCallToFetchOptimizationWindowBDEDays() {
        service.getOptimizationWindowDays();
        verify(configParamsService).getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName());
    }

    @Test
    void shouldReturnTrueIfIndirectConfigChangeToggleIsEnabledAndThereAreSmallGroupProductsAvailable() {
        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_INDIRECT_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));

        assertTrue(service.isLTBDEOnIndirectConfigChangedEnabled());
    }

    @Test
    void shouldReturnFalseIfIndirectConfigChangeToggleIsDisabledAndThereAreSomeSmallGroupProductsAvaialble() {
        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_INDIRECT_CONFIG_CHANGE)).thenReturn(false);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));

        assertFalse(service.isLTBDEOnIndirectConfigChangedEnabled());
    }

    @Test
    void shouldReturnFalseIfIndirectConfigChangeToggleIsEnabledAndThereAreNoSmallGroupProductsAvaialble() {
        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_INDIRECT_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(Collections.emptyList());

        assertFalse(service.isLTBDEOnIndirectConfigChangedEnabled());
    }

    @Test
    void shouldCAllToEnableLTBDEFlagForPricingIfLTBDEIndirectConfigChangeToggleIsEnabled() {
        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        smallGroupProduct.setActive(true);
        smallGroupProduct.setStatus(TenantStatusEnum.ACTIVE);
        smallGroupProduct.setId(5);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_INDIRECT_CONFIG_CHANGE)).thenReturn(true);
        when(crudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters())).thenReturn(of(smallGroupProduct));
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));
        service.enableLTBDEOnIndirectConfigChangedIfApplicable();
        verify(crudService).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Test
    void shouldNotCAllToEnableLTBDEFlagForPricingIfLTBDEIndirectConfigChangeToggleIsDisabled() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_INDIRECT_CONFIG_CHANGE)).thenReturn(false);
        when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

        service.enableLTBDEOnIndirectConfigChangedIfApplicable();
        verify(crudService, never()).save(argThat((CPConfiguration arg) -> arg.isEnableLTBDEForPricing()));
    }

    @Nested
    class IsLTBDEForPricingEnabled {
        @Test
        void shouldReturnDisabledWhenCpConfigIsNull() {
            when(crudService.findOne(CPConfiguration.class)).thenReturn(null);

            boolean result = service.isLTBDEFlagForPricingEnabledInCPConfig();

            assertFalse(result);
        }

        @Test
        void shouldReturnDisabledWhenCpConfigLTBDEForPricingIsDisabled() {
            when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(false));

            boolean result = service.isLTBDEFlagForPricingEnabledInCPConfig();

            assertFalse(result);
        }

        @Test
        void shouldReturnEnabledWhenCpConfigLTBDEForPricingIsEnabled() {
            when(crudService.findOne(CPConfiguration.class)).thenReturn(getCPConfig(true));

            boolean result = service.isLTBDEFlagForPricingEnabledInCPConfig();

            assertTrue(result);
        }
    }

    private PricingBaseAccomType getPricingBaseAccomType(AccomType accomType, BigDecimal floorRate, BigDecimal ceilingRate) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(accomType);
        pricingBaseAccomType.setSundayFloorRate(floorRate);
        pricingBaseAccomType.setSundayCeilingRate(ceilingRate);
        pricingBaseAccomType.setId(1);
        return pricingBaseAccomType;
    }

    private ProductAccomType getProductAccomType(Product smallGroupProduct, AccomType accomType) {
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(smallGroupProduct);
        productAccomType.setAccomType(accomType);
        return productAccomType;
    }

    private PricingBaseAccomType getPricingBaseAccomTypeForSeason(AccomType accomType, BigDecimal floorRate, BigDecimal ceilingRate, String seasonStartDate, String seasonEndDate) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(accomType);
        pricingBaseAccomType.setSundayFloorRate(floorRate);
        pricingBaseAccomType.setSundayCeilingRate(ceilingRate);
        pricingBaseAccomType.setStartDate(toJodaLocalDate(parse(seasonStartDate)));
        pricingBaseAccomType.setEndDate(toJodaLocalDate(parse(seasonEndDate)));
        pricingBaseAccomType.setId(2);
        return pricingBaseAccomType;
    }

    private Product getBarProduct() {
        Product product = new Product();
        product.setActive(true);
        product.setSystemDefault(true);
        product.setId(1);
        return product;
    }

    private AccomType accomType() {
        AccomType accomType = new AccomType();
        accomType.setAccomClass(new AccomClass());
        return accomType;
    }

    private Product getSmallGroupProduct() {
        Product product = new Product();
        product.setActive(true);
        product.setCode(Product.GROUP_PRODUCT_CODE);
        product.setId(2);
        return product;
    }

    private ProductAccomType getProductAccomType() {
        ProductAccomType product = new ProductAccomType();
        product.setId(2);
        product.setAccomType(new AccomType());
        product.getAccomType().setId(15);
        return product;
    }

    private CPConfiguration getCPConfig(boolean enableLTBDEForPricing) {
        CPConfiguration cpConfiguration = new CPConfiguration();
        cpConfiguration.setEnableLTBDEForPricing(enableLTBDEForPricing);
        return cpConfiguration;
    }

    private void populateSupplementValues(AccomTypeSupplement accomTypeSupplement, BigDecimal value) {
        accomTypeSupplement.setSundaySupplementValue(value);
        accomTypeSupplement.setMondaySupplementValue(value);
        accomTypeSupplement.setTuesdaySupplementValue(value);
        accomTypeSupplement.setWednesdaySupplementValue(value);
        accomTypeSupplement.setThursdaySupplementValue(value);
        accomTypeSupplement.setFridaySupplementValue(value);
        accomTypeSupplement.setSaturdaySupplementValue(value);
    }

    private void populateOffsetValues(CPConfigOffsetAccomType accomTypeOffset, BigDecimal value) {
        accomTypeOffset.setSundayOffsetValueWithTax(value);
        accomTypeOffset.setMondayOffsetValueWithTax(value);
        accomTypeOffset.setTuesdayOffsetValueWithTax(value);
        accomTypeOffset.setWednesdayOffsetValueWithTax(value);
        accomTypeOffset.setThursdayOffsetValueWithTax(value);
        accomTypeOffset.setFridayOffsetValueWithTax(value);
        accomTypeOffset.setSaturdayOffsetValueWithTax(value);
    }

}