package com.ideas.tetris.pacman.services.saspopulation.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.xml.schema.ISASRequest;
import com.ideas.tetris.pacman.common.xml.schema.operapopulation.request.v1.OperaPopulationRequestDataType;
import com.ideas.tetris.pacman.common.xml.schema.operapopulation.request.v1.RequestHeaderType;
import com.ideas.tetris.pacman.common.xml.schema.operapopulation.request.v1.SASRequest;
import com.ideas.tetris.pacman.common.xml.schema.operapopulation.response.v1.SASResponse;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.pacman.services.opera.DataLoadSas;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.UnsupportedEncodingException;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.BOOKED_VS_ORIGINAL_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.DECREASE_RESERVATION_NIGHT_DB_CALL;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.EXTENDED_LOSDATA_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.OPTIMIZE_ORG_MKT_ACCOM_INVENTORY_POPULATION;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.SKIP_UPDATE_FILE_METADATA_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaPopulationServiceTest extends AbstractG3JupiterTest {
    static final Integer DB_PORT_NUMBER = 123;
    static final String DB_SERVER_NAME = "def";
    static final String DB_SERVER_INST = "abc";
    static final Integer FILE_METADATA_ID = 913;
    public static final int OVERRIDE_MAX_HISTORY_LOS_OFFSET = 1000;

    @Mock
    protected PacmanConfigParamsService configService;
    @Mock
    protected DataSourceCacheBean dataSourceCacheBean;
    @Mock
    protected SASClientService sasClientService;
    @Mock
    protected SASNodeLocator sasNodeDeterminer;
    @Mock
    protected JAXBUtilLocal jaxbUtil;
    @Mock
    DataSourceCacheBean dataSourceCache;
    @Mock
    private BookedDataService bookedDataService;
    @Mock
    private DataLoadSas dataLoadSas;

    @InjectMocks
    OperaPopulationService operaPopulationService;

    @BeforeEach
    public void setUp() {
        DBLoc dbLoc = new DBLoc();
        dbLoc.setDbName("TestName");
        dbLoc.setPortNumber(1234);
        dbLoc.setServerName("TestServerName");
        dbLoc.setServerInst("TestServerInstance");


        DBLoc globalDbLoc = new DBLoc();
        globalDbLoc.setDbName(Constants.GLOBAL_DATABASE_NAME);
        globalDbLoc.setPortNumber(DB_PORT_NUMBER);
        globalDbLoc.setServerName(DB_SERVER_NAME);
        globalDbLoc.setServerInst(DB_SERVER_INST);

        Mockito.when(dataSourceCacheBean.getDBLoc(5)).thenReturn(dbLoc);
        Mockito.when(dataSourceCacheBean.getGlobalDBLoc()).thenReturn(globalDbLoc);

        when(sasClientService.executeMacroSynchronously(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString())).thenReturn(null);
        Mockito.when(sasNodeDeterminer.determineSasAnalyticsDataSetLocationForSASNodes(
                Mockito.anyInt())).thenReturn("c:/sas/8");

        Mockito.when(jaxbUtil.marshall(Mockito.any(Object.class))).thenReturn("abc");
        when(dataSourceCache.getDBLoc(5)).thenReturn(dbLoc);
        when(dataSourceCache.getGlobalDBLoc()).thenReturn(globalDbLoc);
        when(configService.getParameterValue(IPConfigParamName.CORE_HISTORICAL_DATA_AVAILABILITY_DAYS.value())).thenReturn("365");
        when(configService.getParameterValue(BOOKED_VS_ORIGINAL_ENABLED.value())).thenReturn("false");
        when(configService.getParameterValue(EXTENDED_LOSDATA_ENABLED.value())).thenReturn("false");
    }

    @Test
    public void testExecute_EnabledAmsAutoDetectSufficientPace() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(true);
        when(bookedDataService.shouldSkipStayedDataPopulation()).thenReturn(true);
        when(configService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        operaPopulationService.execute(1);

        ArgumentCaptor<ISASRequest> marshallInputCaptor = ArgumentCaptor.forClass(ISASRequest.class);
        verify(jaxbUtil, times(1)).marshall(marshallInputCaptor.capture());
        SASRequest marshallInput = (SASRequest) marshallInputCaptor.getValue();
        RequestHeaderType header = marshallInput.getRequestHeader();
        OperaPopulationRequestDataType request = marshallInput.getOperaPopulationRequest();
        assertEquals(OperaPopulationService.OPERATION_NAME, header.getOperationName());
        assertEquals("true", request.getAutoDetectSufficientBookedRTPace());
        assertEquals("true", request.getSkipStayedRTPopulation());
        assertFalse(request.isLimitedDataBuildEnabled());
    }

    @Test
    public void testExecute_excludeZeroSummary() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(true);
        when(bookedDataService.shouldSkipStayedDataPopulation()).thenReturn(true);
        when(configService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        when(configService.getParameterValue(BOOKED_VS_ORIGINAL_ENABLED.value())).thenReturn("false");
        operaPopulationService.execute(1, true);

        ArgumentCaptor<ISASRequest> marshallInputCaptor = ArgumentCaptor.forClass(ISASRequest.class);
        verify(jaxbUtil, times(1)).marshall(marshallInputCaptor.capture());
        SASRequest marshallInput = (SASRequest) marshallInputCaptor.getValue();
        RequestHeaderType header = marshallInput.getRequestHeader();
        OperaPopulationRequestDataType request = marshallInput.getOperaPopulationRequest();
        assertEquals(OperaPopulationService.OPERATION_NAME, header.getOperationName());
        assertEquals("true", request.getAutoDetectSufficientBookedRTPace());
        assertEquals("true", request.getSkipStayedRTPopulation());
        assertEquals("false", request.getBookedVsOriginalEnabled());
        //assertTrue(request.isPropertyHiltonIpp());
        assertTrue(request.isExcludeZeroSummary());
        assertFalse(request.isLimitedDataBuildEnabled());
    }

    @Test
    void testExecute_excludeZeroSummaryForAmsRebuild() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(true);
        when(bookedDataService.shouldSkipStayedDataPopulation()).thenReturn(true);
        when(configService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        operaPopulationService.executeWithOverrideLosOffset(1, true, OVERRIDE_MAX_HISTORY_LOS_OFFSET);

        ArgumentCaptor<ISASRequest> marshallInputCaptor = ArgumentCaptor.forClass(ISASRequest.class);
        verify(jaxbUtil, times(1)).marshall(marshallInputCaptor.capture());
        SASRequest marshallInput = (SASRequest) marshallInputCaptor.getValue();
        RequestHeaderType header = marshallInput.getRequestHeader();
        OperaPopulationRequestDataType request = marshallInput.getOperaPopulationRequest();
        assertEquals(OperaPopulationService.OPERATION_NAME, header.getOperationName());
        assertEquals("true", request.getAutoDetectSufficientBookedRTPace());
        assertEquals("true", request.getSkipStayedRTPopulation());
        assertEquals(OVERRIDE_MAX_HISTORY_LOS_OFFSET, request.getOverrideMaxHistoryLosOffset());
        assertTrue(request.isExcludeZeroSummary());
        assertFalse(request.isLimitedDataBuildEnabled());
    }

    @Test
    public void testExecute_DisabledAmsAutoDetectSufficientPace() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(false);
        when(bookedDataService.shouldSkipStayedDataPopulation()).thenReturn(false);
        when(configService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(SKIP_UPDATE_FILE_METADATA_ID)).thenReturn(true);
        operaPopulationService.execute(1);

        ArgumentCaptor<ISASRequest> marshallInputCaptor = ArgumentCaptor.forClass(ISASRequest.class);
        verify(jaxbUtil, times(1)).marshall(marshallInputCaptor.capture());
        SASRequest marshallInput = (SASRequest) marshallInputCaptor.getValue();
        RequestHeaderType header = marshallInput.getRequestHeader();
        OperaPopulationRequestDataType request = marshallInput.getOperaPopulationRequest();
        assertEquals(OperaPopulationService.OPERATION_NAME, header.getOperationName());
        assertEquals("false", request.getAutoDetectSufficientBookedRTPace());
        assertEquals("false", request.getSkipStayedRTPopulation());
        assertTrue(request.isSkipUpdateFileMetadataId());
        assertTrue(request.isLimitedDataBuildEnabled());
    }

    @Test
    public void testGetSASRequest() {
        when(configService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value())).thenReturn("PA,L2SUIT,P2001,PS,SUTOPR,PM,P2000");
        when(configService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.STOP_POPULATING_PARTITIONS_TABLE.getParameterName())).thenReturn(false);
        when(configService.getBooleanParameterValue(OPTIMIZE_ORG_MKT_ACCOM_INVENTORY_POPULATION)).thenReturn(false);
        when(configService.getBooleanParameterValue(DECREASE_RESERVATION_NIGHT_DB_CALL)).thenReturn(false);
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals(Constants.BDE, operaPopulationRequestDataType.getOperationType());
        assertEquals("'PA|L2SUIT|P2001|PS|SUTOPR|PM|P2000'", operaPopulationRequestDataType.getPseudoRooms());
        assertTrue(operaPopulationRequestDataType.isLimitedDataBuildEnabled());
        assertFalse(operaPopulationRequestDataType.isStopPopulatingPartitionsTable());
    }

    @Test
    public void testGetSASRequestWhenPseudoRoomNotConfigured() {
        when(configService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value())).thenReturn(null);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.STOP_POPULATING_PARTITIONS_TABLE.getParameterName())).thenReturn(true);
        when(configService.getBooleanParameterValue(OPTIMIZE_ORG_MKT_ACCOM_INVENTORY_POPULATION)).thenReturn(true);
        when(configService.getBooleanParameterValue(DECREASE_RESERVATION_NIGHT_DB_CALL)).thenReturn(true);
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals(Constants.BDE, operaPopulationRequestDataType.getOperationType());
        assertEquals("", operaPopulationRequestDataType.getPseudoRooms());
        assertTrue(operaPopulationRequestDataType.isStopPopulatingPartitionsTable());
    }

    @Test
    public void testGetSASRequestWhenPseudoRoomIsEmptyString() {
        when(configService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value())).thenReturn("");
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals(Constants.BDE, operaPopulationRequestDataType.getOperationType());
        assertEquals("", operaPopulationRequestDataType.getPseudoRooms());
    }

    @Test
    public void testGetSASRequestWhenPseudoRoomContainsAccentCharacters() throws UnsupportedEncodingException {
        final String stringWithUmlauts = new String("Frühstück".getBytes("Windows-1252"));
        when(configService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value())).thenReturn(stringWithUmlauts);
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals(Constants.BDE, operaPopulationRequestDataType.getOperationType());
        assertEquals("'" + (new String(stringWithUmlauts.getBytes("UTF-8"))) + "'", operaPopulationRequestDataType.getPseudoRooms());
    }

    @Test
    public void testGetStoredProc_Default() {
        assertTrue(operaPopulationService.getStoredProc().contains(Constants.IDEAS_SAS_STORED_PROC_DEFAULT_VALUE));
    }

    @Test
    public void testGetStoredProc() {
        System.setProperty(Constants.IDEAS_SAS_STORED_PROC_PROPERTY, "Test");
        assertTrue(operaPopulationService.getStoredProc().contains("Test"));
    }

    @Test
    public void getRequestMapWhenNewPopulationIsTrue() {
        assertTrue(operaPopulationService.getRequestMap().contains("operadailypopulation.map"));
    }

    @Test
    public void getOperationNameWhenNewPopulationIsTrue() {
        assertEquals(OperaPopulationService.OPERATION_NAME, operaPopulationService.getOperationName());
    }

    @Test
    public void test_getSASRequest_autoDetectFalse() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(false);
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals("false", operaPopulationRequestDataType.getAutoDetectSufficientBookedRTPace());
        verify(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE);
    }

    @Test
    public void test_getSASRequest_autoDetectTrue() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE)).thenReturn(true);
        SASRequest request = (SASRequest) operaPopulationService.getSASRequest(getOperaPopulationRequestDataType(), getDecision());
        OperaPopulationRequestDataType operaPopulationRequestDataType = request.getOperaPopulationRequest();
        assertEquals("true", operaPopulationRequestDataType.getAutoDetectSufficientBookedRTPace());
        verify(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE);
    }

    @Test
    public void test_executeSync() {
        SASResponse response = operaPopulationService.executeSync(FILE_METADATA_ID);
        verify(sasClientService).executeMacroSynchronously(any(), any(), any(), any());
    }


    private Decision getDecision() {
        Decision decision = new Decision();
        decision.setId(12);
        return decision;

    }

    private OperaPopulationRequestDataType getOperaPopulationRequestDataType() {
        return new OperaPopulationRequestDataType();
    }
}
