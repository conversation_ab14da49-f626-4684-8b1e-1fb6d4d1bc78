package com.ideas.tetris.pacman.services.minlos;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.minlos.entity.MinlosDecisions;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class MinlosDecisionServiceTest extends AbstractG3JupiterTest {

    private LocalDate date;
    @Mock
    EntityManager entityManager;
    @Mock
    Query query;
    @Mock
    DateService dateService;
    @Mock
    DecisionService decisionService;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @InjectMocks
    MinlosRecommendationService minlosService;
    @InjectMocks
    MinlosDecisionService minlosDecisionService;

    @BeforeEach
    public void setUp() {
        date = new LocalDate(2014, 3, 11);
        tenantCrudService().getEntityManager().clear();
        setWorkContextProperty(TestProperty.H2);
        minlosService.setCrudService(tenantCrudService());
        minlosService.dateService = dateService;
        minlosDecisionService.setCrudService(tenantCrudService());
        minlosDecisionService.dateService = dateService;
        minlosService.decisionService = decisionService;
        minlosDecisionService.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    @Test
    public void testMinlosDifferential() {
        firstMinlosGeneration("'D'", "'D'", "'2014-03-15'", "'2014-03-16'");
        secondMinlosGeneration();
        LocalDateTime lastUploadedDate = getLastUploadedDate();
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));
        assertTrue(minlosDecisions.size() == 1);
        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        assertEquals("2014-03-16", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(1, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferential_DisabledRateCode() {
        firstMinlosGeneration("'D'", "'D'", "'2014-03-15'", "'2014-03-16'");
        secondMinlosGeneration();
        LocalDateTime lastUploadedDate = getLastUploadedDate();
        disableRateCode("BAR0");
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));
        assertTrue(minlosDecisions.isEmpty());
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferentialNonYieldableRateCodeFiltered() {
        firstMinlosGeneration("'D'", "'D'", "'2014-03-15'", "'2014-03-16'");
        secondMinlosGeneration();
        setRateCodeToNonYieldable("BAR0");
        LocalDateTime lastUploadedDate = getLastUploadedDate();
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));
        assertTrue(minlosDecisions.isEmpty());

        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosAlwaysToBeGenerated() {
        generateMinlos();
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(null);
        assertFalse(minlosDecisions.isEmpty());
    }

    @Test
    public void testMinlosAlwaysToBeGenerated_RateCodesDeleted() {
        generateMinlos();
        disableRateCode("BAR0");
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(null);
        assertTrue(minlosDecisions.isEmpty());
    }

    private void disableRateCode(String rateCodeName) {
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Qualified set Status_ID=2 where rate_code_name =:rateCodeName",
                QueryParameter.with("rateCodeName", rateCodeName).parameters());
    }

    @Test
    public void testMinlosFull() {
        firstMinlosGeneration("'D'", "'D'", "'2014-03-15'", "'2014-03-16'");
        secondMinlosGeneration();
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(null);
        assertTrue(minlosDecisions.size() == 2);

        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        if ("2014-03-16".equals(minlosDecision.getArrivalDate())) {
            assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
            assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
            assertEquals(1, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        }
        if ("2014-03-15".equals(minlosDecision.getArrivalDate())) {
            assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
            assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
            assertEquals(6, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        }
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosFullNoDecisionsForNonYieldableRateCode() {
        firstMinlosGeneration("'D'", "'D'", "'2014-03-15'", "'2014-03-16'");
        secondMinlosGeneration();
        setRateCodeToNonYieldable("BAR0");
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getMinLOSDecisions(null);

        assertTrue(minlosDecisions.isEmpty());

        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferentialForRateCodeRoomType() {
        //Given
        final String occupancyDate = "'2014-03-16'";
        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'NYNNNYY'", "'BAR0'");
        final String secondOccupancyDate = "'2014-03-15'";
        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NNNNNNY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'NNYNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'NNNNYYY'", "'BAR0'");
        firstMinlosGeneration("'D'", "'QN'", occupancyDate, occupancyDate);
        updateDecisionQualifiedFplos(secondOccupancyDate, "10", "'NNNNNYY'");
        secondMinlosGeneration();
        LocalDateTime lastUploadedDate = getLastUploadedDate();

        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertTrue(minlosDecisions.size() == 6);
        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        assertEquals("2014-03-15", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(7, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionsSecondRoomType = minlosDecisions.get(1);
        assertEquals("2014-03-15", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(3, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionThirdRoomType = minlosDecisions.get(2);
        assertEquals("2014-03-15", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(6, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        minlosDecision = minlosDecisions.get(3);
        assertEquals("2014-03-16", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(1, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        minlosDecisionsSecondRoomType = minlosDecisions.get(4);
        assertEquals("2014-03-16", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(4, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        minlosDecisionThirdRoomType = minlosDecisions.get(5);
        assertEquals("2014-03-16", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(2, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferentialForRateCodeRoomTypeNonYieldableRateCodeFiltered() {
        //Given
        final String occupancyDate = "'2014-03-16'";
        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'NYNNNYY'", "'BAR0'");
        final String secondOccupancyDate = "'2014-03-15'";
        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NNNNNNY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'NNYNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'NNNNYYY'", "'BAR0'");
        firstMinlosGeneration("'D'", "'QN'", occupancyDate, occupancyDate);
        updateDecisionQualifiedFplos(secondOccupancyDate, "10", "'NNNNNYY'");
        secondMinlosGeneration();
        setRateCodeToNonYieldable("BAR0");
        LocalDateTime lastUploadedDate = getLastUploadedDate();

        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertTrue(minlosDecisions.isEmpty());

        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferentialForMultipleRateCodeRoomTypeChanges() {
        //Given
        final String occupancyDate = "'2014-03-16'";
        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'NYNNNYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'YNNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", occupancyDate, "'NYNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'D'", occupancyDate, "'NNYNNYY'", "'SRP8'");

        final String secondOccupancyDate = "'2014-03-15'";
        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NNNNNNY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'NNYNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'NNNNYYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NYNNNNY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'YNYNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'YNNNYYY'", "'SRP8'");

        firstMinlosGeneration("'D'", "'QN'", occupancyDate, occupancyDate);

        updateDecisionQualifiedFplos(secondOccupancyDate, "10", "'NNNNNYY'");
        secondMinlosGeneration();

        LocalDateTime lastUploadedDate = getLastUploadedDate();

        disableRateCode("SRP8");

        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertEquals(6, minlosDecisions.size());
        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        assertEquals("2014-03-15", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(7, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionsSecondRoomType = minlosDecisions.get(1);
        assertEquals("2014-03-15", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(3, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionThirdRoomType = minlosDecisions.get(2);
        assertEquals("2014-03-15", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(6, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testMinlosDifferentialForMultipleRateCodeRoomTypeChanges_optimizeSQL() {
        generateDifferetialMinLOSData();

        LocalDateTime lastUploadedDate = getLastUploadedDate();

        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertEquals(12, minlosDecisions.size());
        verifyMinLOSDecision(minlosDecisions);
    }

    @Test
    public void testMinlosDifferentialForMultipleRateCodeRoomTypeChanges_optimizeSQL_DisabledSRPs() {
        generateDifferetialMinLOSData();

        LocalDateTime lastUploadedDate = getLastUploadedDate();
        disableRateCode("SRP8");
        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertEquals(6, minlosDecisions.size());
        verifyBarDecisions(minlosDecisions);
    }

    private void verifyMinLOSDecision(List<MinlosDecisions> minlosDecisions) {
        verifyBarDecisions(minlosDecisions);
        MinlosDecisions minlosDecision;
        MinlosDecisions minlosDecisionsSecondRoomType;
        MinlosDecisions minlosDecisionThirdRoomType;
        minlosDecision = minlosDecisions.get(9);
        assertEquals("2014-03-16", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("SRP8", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(1, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        minlosDecisionsSecondRoomType = minlosDecisions.get(10);
        assertEquals("2014-03-16", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("SRP8", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(2, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        minlosDecisionThirdRoomType = minlosDecisions.get(11);
        assertEquals("2014-03-16", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("SRP8", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(1, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    private void verifyBarDecisions(List<MinlosDecisions> minlosDecisions) {
        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        assertEquals("2014-03-15", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(7, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionsSecondRoomType = minlosDecisions.get(1);
        assertEquals("2014-03-15", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(3, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionThirdRoomType = minlosDecisions.get(2);
        assertEquals("2014-03-15", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(6, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
    }

    private LocalDateTime getLastUploadedDate() {
        String uploadDateStr = "2014-03-12 02:30";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime lastUploadedDate = LocalDateTime.parse(uploadDateStr, formatter);
        Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant());
        return lastUploadedDate;
    }

    private void generateDifferetialMinLOSData() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_MIN_LOS_DECSION_SQL)).thenReturn(true);

        //Given
        final String occupancyDate = "'2014-03-16'";
        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'NYNNNYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'YNNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", occupancyDate, "'NYNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'D'", occupancyDate, "'NNYNNYY'", "'SRP8'");

        final String secondOccupancyDate = "'2014-03-15'";
        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NNNNNNY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'NNYNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'NNNNYYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NYNNNNY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'YNYNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'YNNNYYY'", "'SRP8'");

        firstMinlosGeneration("'D'", "'QN'", occupancyDate, occupancyDate);

        updateDecisionQualifiedFplos(secondOccupancyDate, "10", "'NNNNNYY'");
        secondMinlosGeneration();
    }

    @Test
    public void testMinlosDifferentialForMultipleRateCodeRoomTypeChangesNonYieldableRateCodeFiltered() {
        //Given
        final String occupancyDate = "'2014-03-16'";
        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'NYNNNYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'STE'", occupancyDate, "'YNNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", occupancyDate, "'NYNNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'D'", occupancyDate, "'NNYNNYY'", "'SRP8'");

        final String secondOccupancyDate = "'2014-03-15'";
        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NNNNNNY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'NNYNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'NNNNYYY'", "'BAR0'");

        addRecordToDecisionQualifiedFplos("'D'", secondOccupancyDate, "'NYNNNNY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'QN'", secondOccupancyDate, "'YNYNNYY'", "'SRP8'");
        addRecordToDecisionQualifiedFplos("'STE'", secondOccupancyDate, "'YNNNYYY'", "'SRP8'");

        firstMinlosGeneration("'D'", "'QN'", occupancyDate, occupancyDate);

        updateDecisionQualifiedFplos(secondOccupancyDate, "10", "'NNNNNYY'");
        secondMinlosGeneration();
        setRateCodeToNonYieldable("SRP8");
        LocalDateTime lastUploadedDate = getLastUploadedDate();

        //When
        List<MinlosDecisions> minlosDecisions = minlosDecisionService.getDifferentialMinLosDecisionForRateCode(Date.from(lastUploadedDate.atZone(ZoneId.systemDefault()).toInstant()));

        //Then
        assertTrue(minlosDecisions.size() == 6);
        MinlosDecisions minlosDecision = minlosDecisions.get(0);
        assertEquals("2014-03-15", minlosDecision.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecision.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("D", minlosDecision.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(7, minlosDecision.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionsSecondRoomType = minlosDecisions.get(1);
        assertEquals("2014-03-15", minlosDecisionsSecondRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionsSecondRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("QN", minlosDecisionsSecondRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(3, minlosDecisionsSecondRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        MinlosDecisions minlosDecisionThirdRoomType = minlosDecisions.get(2);
        assertEquals("2014-03-15", minlosDecisionThirdRoomType.getArrivalDate().toString(), "Arrival date is not correct for differential of minlos");
        assertEquals("BAR0", minlosDecisionThirdRoomType.getRatePlan().toString(), "Rateplan is not correct for differential of minlos");
        assertEquals("STE", minlosDecisionThirdRoomType.getRoomType().toString(), "RoomType is not correct for differential of minlos");
        assertEquals(6, minlosDecisionThirdRoomType.getMinlos(), "Minlos is not correct for differential of minlos");
        verify(dateService, atLeast(1)).getDecisionUploadWindowEndDate();
    }

    @Test
    public void testGetTotalNumberOfRecords() {
        deleteAllDecisionsInMinlos();
        insertValuesIntoMinlos();
        int noOfRecords = minlosDecisionService.getTotalNumberOfRecordsAfterDate(new java.util.Date());
        assertEquals(3, noOfRecords);
    }

    @Test
    public void testGetTotalNumberOfRecordsFromPace() {
        deleteAllDecisionsInPaceMinlos();
        insertValuesIntoPaceMinlos();
        int noOfRecords = minlosDecisionService.getTotalNumberOfRecordsFromPaceAfterDate(new java.util.Date());
        assertEquals(3, noOfRecords);
    }

    @Test
    public void testDeleteMinlosDecisionsAfterDate() {
        int CHUNK_SIZE = 2;
        deleteAllDecisionsInMinlos();
        insertValuesIntoMinlos();
        int noOfRecords = minlosDecisionService.deleteMinlosDecisionsAfterDate(CHUNK_SIZE, new java.util.Date());
        assertEquals(CHUNK_SIZE, noOfRecords);
    }

    @Test
    public void testdeleteMinlosDecisionsAfterDateFromPace() {
        int CHUNK_SIZE = 2;
        deleteAllDecisionsInPaceMinlos();
        insertValuesIntoPaceMinlos();
        int noOfRecords = minlosDecisionService.deleteMinlosDecisionsFromPaceAfterDate(CHUNK_SIZE, new java.util.Date());
        assertEquals(CHUNK_SIZE, noOfRecords);
    }

    @Test
    public void shouldNotDeleteDecisionsBeforeDate() {
        int CHUNK_SIZE = 4;
        deleteAllDecisionsInMinlos();
        insertValuesIntoMinlos();
        int noOfRecords = minlosDecisionService.deleteMinlosDecisionsAfterDate(CHUNK_SIZE, new java.util.Date());
        assertEquals(3, noOfRecords);
    }

    @Test
    public void shouldNotDeleteDecisionsInPaceBeforeDate() {
        int CHUNK_SIZE = 4;
        deleteAllDecisionsInPaceMinlos();
        insertValuesIntoPaceMinlos();
        int noOfRecords = minlosDecisionService.deleteMinlosDecisionsFromPaceAfterDate(CHUNK_SIZE, new java.util.Date());
        assertEquals(3, noOfRecords);
    }

    public void insertValuesIntoQualifiedFplos() {
        tenantCrudService().getEntityManager().createNativeQuery("insert into Decision_Qualified_FPLOS (Decision_ID, Property_ID, Accom_Type_ID, Rate_Qualified_ID, Arrival_DT, FPLOS) " +
                "VALUES (161, 6, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), CONVERT(date, SYSDATETIME()), N'NNNNNNN')").executeUpdate();
    }

    public void insertValuesIntoMinlos() {
        insertValuesIntoQualifiedFplos();
        tenantCrudService().getEntityManager().createNativeQuery("insert into Decision_MINLOS (Decision_Qualified_FPLOS_ID, Decision_ID, Accom_Type_ID, Rate_Qualified_ID, Arrival_DT, MINLOS)" +
                "VALUES ((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, " +
                "(select max(Accom_Type_ID) from accom_type), 2, CONVERT(date, SYSDATETIME()), 1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, 1, CONVERT(date, SYSDATETIME())), 1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, -1, CONVERT(date, SYSDATETIME())), 1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, 2, CONVERT(date, SYSDATETIME())), 1);").executeUpdate();
    }

    public void insertValuesIntoPaceMinlos() {
        insertValuesIntoQualifiedFplos();
        tenantCrudService().getEntityManager().createNativeQuery("insert into PACE_MINLOS (Decision_Qualified_FPLOS_ID, Decision_ID, Accom_Type_ID, Rate_Qualified_ID, Arrival_DT, MINLOS) " +
                "VALUES ((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, CONVERT(date, SYSDATETIME()), 1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, 1, CONVERT(date, SYSDATETIME())),1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, -1, CONVERT(date, SYSDATETIME())), 1)," +
                "((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 6, (select max(Accom_Type_ID) from accom_type), 2, DATEADD(day, 2, CONVERT(date, SYSDATETIME())), 1);").executeUpdate();
    }

    public void deleteAllDecisionsInMinlos() {
        tenantCrudService().getEntityManager().createNativeQuery("delete from Decision_MINLOS").executeUpdate();
        tenantCrudService().getEntityManager().createNativeQuery("delete from Decision_Qualified_FPLOS").executeUpdate();
    }

    public void deleteAllDecisionsInPaceMinlos() {
        tenantCrudService().getEntityManager().createNativeQuery("delete from PACE_MINLOS").executeUpdate();
        tenantCrudService().getEntityManager().createNativeQuery("delete from Decision_Qualified_FPLOS").executeUpdate();
    }

    private void setRateCodeToNonYieldable(String rateCode) {
        RateQualified rateQualified = (RateQualified) tenantCrudService().findByNamedQuerySingleResult(RateQualified.GET_RATE_QUALIFIED_BY_NAMES,
                QueryParameter.with("names", Collections.singletonList(rateCode)).parameters());
        rateQualified.setYieldable(0);
        tenantCrudService().save(rateQualified);
        tenantCrudService().flushAndClear();
    }

    private void secondMinlosGeneration() {
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Decision VALUES(6,CURRENT_TIMESTAMP ,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,19,CURRENT_TIMESTAMP,'2014-03-12 04:30:00.000',1,CURRENT_TIMESTAMP) ");
        List decisionId = tenantCrudService().findByNativeQuery("select max(decision_id) from decision where decision_type_id = 19");
        Decision dec = new Decision();
        dec.setId(Integer.parseInt(decisionId.get(0).toString()));
        String endDateStr = "2014-03-12 04:30";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);
        dec.setEndDate(Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant()));

        updateDecisionQualifiedFplos("'2014-03-16'", "12", "'YNNYNYY'");
        when(decisionService.createMINLOSDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(Constants.OPERA);
        minlosService.createMinlosDecisions();
    }

    private void updateDecisionQualifiedFplos(final String occupancyDate, final String accomTypeId, final String fplos) {
        tenantCrudService().executeUpdateByNativeQuery("update Decision_Qualified_FPLOS set	FPLOS =" + fplos + " where Arrival_DT=" + occupancyDate + " and Accom_Type_ID=" + accomTypeId);
    }

    private void firstMinlosGeneration(final String accomType, final String secondAccomType, final String occupancyDate, final String secondOccupancyDate) {
        Decision dec = new Decision();
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Decision VALUES(6,CURRENT_TIMESTAMP ,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,19,CURRENT_TIMESTAMP,'2014-03-12 02:30:00.000',1,CURRENT_TIMESTAMP) ");
        List decisionId = tenantCrudService().findByNativeQuery("select max(decision_id) from decision where decision_type_id = 19");
        addRecordToDecisionQualifiedFplos(accomType, occupancyDate, "'NNNNNYY'", "'BAR0'");
        addRecordToDecisionQualifiedFplos(secondAccomType, secondOccupancyDate, "'NNNYNYY'", "'BAR0'");
        dec.setId(Integer.parseInt(decisionId.get(0).toString()));
        String endDateStr = "2014-03-12 02:30";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);


        dec.setEndDate(Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant()));
        dec.setDecisionTypeId(19);
        when(decisionService.createMINLOSDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        java.util.Date uploadEndDate = (date.plusDays(20)).toDate();
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(uploadEndDate);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(Constants.OPERA);
        minlosService.createMinlosDecisions();
    }

    private void addRecordToDecisionQualifiedFplos(String accomType, String occupancyDate, final String fplos, final String rateCode) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select Accom_Type_ID from accom_type where accom_type_code=" + accomType + "), (select Rate_Qualified_ID from Rate_Qualified where rate_code_name =" + rateCode + "),	" + occupancyDate + ",	" + fplos + ",CURRENT_TIMESTAMP) ");
    }

    private void generateMinlos() {
        Decision dec = new Decision();
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Decision VALUES(6,CURRENT_TIMESTAMP ,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,19,CURRENT_TIMESTAMP,'2014-03-12',1,CURRENT_TIMESTAMP) ");
        List decisionId = tenantCrudService().findByNativeQuery("select max(decision_id) from decision where decision_type_id = 19");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select Accom_Type_ID from accom_type where accom_type_code='D'), (select Rate_Qualified_ID from Rate_Qualified where rate_code_name ='BAR0'),	'2014-03-15',	'NNNNNYY',CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select Accom_Type_ID from accom_type where accom_type_code='D'), (select Rate_Qualified_ID from Rate_Qualified where rate_code_name ='BAR0'),	'2014-03-16',	'NNNYNYY',CURRENT_TIMESTAMP) ");
        dec.setId(Integer.parseInt(decisionId.get(0).toString()));
        dec.setEndDate(new LocalDate(2014, 3, 12).toDate());
        dec.setDecisionTypeId(19);
        when(decisionService.createMINLOSDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(Constants.OPERA);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())).thenReturn(true);
        minlosService.createMinlosDecisions();
    }
}