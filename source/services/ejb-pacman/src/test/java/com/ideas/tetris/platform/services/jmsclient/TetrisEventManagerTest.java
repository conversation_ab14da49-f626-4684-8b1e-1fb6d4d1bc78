package com.ideas.tetris.platform.services.jmsclient;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.event.MetaData;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.common.event.TetrisEventType;
import com.ideas.tetris.platform.common.event.publisher.EjbPacmanEventPublisher;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.mdb.TopicMessageProducer;
import com.ideas.tetris.platform.services.Stage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.TOPIC_NAME_NEW;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TetrisEventManagerTest {
    private static final int ONE = 1;
    private static final int PROPERTY_ID = ONE;
    private static final String CLIENT_CODE = "testClient";
    private static final String PROPERTY_CODE = "testProperty";
    private static final Date DATE = new Date();
    private static final String USER_ID = "testUser";
    private static final String NOTE = "testNoter";
    private static final boolean INCLUDE_WEB_RATES = true;
    private static final boolean SRP_FPLOS_AT_TOTAL_LEVEL = true;
    private static final String SFDC_CASE_NUMBER = "121212";
    public static final String CORRELATION_ID = "123-abc";
    public static final Long JOB_INSTANCE_ID = 1919L;
    public static final Long PROBLEM_ID = 1920L;
    @InjectMocks
    TetrisEventManager tetrisEventManager;
    @Mock
    EjbPacmanEventPublisher publisher;

    @AfterEach
    public void teardown() {
        PlatformThreadLocalContextHolder.cleanupThread();
    }

    @Test
    public void testPropertyConfiguredEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        TetrisEvent te = tetrisEventManager.buildPropertyConfiguredEvent(PROPERTY_ID, USER_ID);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.PROPERTY_CONFIGURED);
        assertEquals(te.getMetaDataByName(MetaData.INITIATED_BY).getValue(), USER_ID);
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
    }

    @Test
    public void testRollbackInitiatedEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        TetrisEvent te = tetrisEventManager.buildRollbackInitiatedEvent(PROPERTY_ID, USER_ID);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.ROLLBACK_INITIATED);
        assertEquals(te.getMetaDataByName(MetaData.INITIATED_BY).getValue(), USER_ID);
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
    }

    @Test
    public void testPropertyAddedEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        mdList.add(new MetaData(MetaData.PROPERTY_CODE, PROPERTY_CODE));
        mdList.add(new MetaData(MetaData.CLIENT_CODE, CLIENT_CODE));
        TetrisEvent te = tetrisEventManager.buildPropertyAddedEvent(PROPERTY_ID, PROPERTY_CODE, CLIENT_CODE, USER_ID);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.PROPERTY_ADDED);
        assertEquals(te.getMetaDataByName(MetaData.INITIATED_BY).getValue(), USER_ID);
        assertEquals(te.getMetaDataByName(MetaData.PROPERTY_CODE).getValue(), PROPERTY_CODE);
        assertEquals(te.getMetaDataByName(MetaData.CLIENT_CODE).getValue(), CLIENT_CODE);
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
    }

    @Test
    public void testTwoWayStageEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        mdList.add(new MetaData(MetaData.NOTE, NOTE));
        mdList.add(new MetaData(MetaData.NEW_STAGE, Stage.TWO_WAY.getCode()));
        mdList.add(new MetaData(MetaData.DECISION_STAGE_CHANGE_FLAG, "true"));
        mdList.add(new MetaData(MetaData.SRP_FPLOS_AT_TOTAL_LEVEL, String.valueOf(SRP_FPLOS_AT_TOTAL_LEVEL)));
        TetrisEvent te = tetrisEventManager.buildPropertyStageChangedEvent(PROPERTY_ID, USER_ID, Stage.ONE_WAY,
                Stage.TWO_WAY, NOTE, true, SRP_FPLOS_AT_TOTAL_LEVEL);
        assertStageChanges(te, Stage.TWO_WAY, Boolean.TRUE, Boolean.TRUE);
    }

    @Test
    public void testOneWayStageEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        mdList.add(new MetaData(MetaData.NOTE, NOTE));
        mdList.add(new MetaData(MetaData.NEW_STAGE, Stage.ONE_WAY.getCode()));
        mdList.add(new MetaData(MetaData.DECISION_STAGE_CHANGE_FLAG, "true"));
        mdList.add(new MetaData(MetaData.SRP_FPLOS_AT_TOTAL_LEVEL, String.valueOf(SRP_FPLOS_AT_TOTAL_LEVEL)));
        TetrisEvent te = tetrisEventManager.buildPropertyStageChangedEvent(PROPERTY_ID, USER_ID, Stage.POPULATION,
                Stage.ONE_WAY, NOTE, true, SRP_FPLOS_AT_TOTAL_LEVEL);
        assertStageChanges(te, Stage.ONE_WAY, Boolean.TRUE, Boolean.TRUE);
    }

    @Test
    public void testPopulationStageEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        mdList.add(new MetaData(MetaData.NOTE, NOTE));
        mdList.add(new MetaData(MetaData.NEW_STAGE, Stage.POPULATION.getCode()));
        TetrisEvent te = tetrisEventManager.buildPropertyStageChangedEvent(PROPERTY_ID, USER_ID, Stage.CATCHUP,
                Stage.POPULATION, NOTE);
        assertStageChanges(te, Stage.POPULATION, null, null);
    }

    @Test
    public void testCatchupStageEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        mdList.add(new MetaData(MetaData.NOTE, NOTE));
        mdList.add(new MetaData(MetaData.NEW_STAGE, Stage.CATCHUP.getCode()));
        TetrisEvent te = tetrisEventManager.buildPropertyStageChangedEvent(PROPERTY_ID, USER_ID, Stage.DATA_CAPTURE,
                Stage.CATCHUP, NOTE);
        assertStageChanges(te, Stage.CATCHUP, null, null);
    }

    @Test
    public void testCatchupInitiatedEvent() {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        mdList.add(new MetaData(MetaData.EXTRACT_START_DATE, dateFormat.format(DATE)));
        mdList.add(new MetaData(MetaData.EXTRACT_END_DATE, dateFormat.format(DATE)));
        mdList.add(new MetaData(MetaData.INCLUDE_WEB_RATES, new Boolean(INCLUDE_WEB_RATES).toString()));
        mdList.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        TetrisEvent te = tetrisEventManager.buildCatchupInitiatedEvent(PROPERTY_ID, DATE, DATE, INCLUDE_WEB_RATES,
                USER_ID);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.CATCHUP_INITIATED);
        assertEquals(te.getMetaDataByName(MetaData.EXTRACT_START_DATE).getValue(), dateFormat.format(DATE));
        assertEquals(te.getMetaDataByName(MetaData.EXTRACT_END_DATE).getValue(), dateFormat.format(DATE));
        assertEquals(te.getMetaDataByName(MetaData.INCLUDE_WEB_RATES).getValue(),
                new Boolean(INCLUDE_WEB_RATES).toString());
        assertEquals(te.getMetaDataByName(MetaData.INITIATED_BY).getValue(), USER_ID);
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
    }

    @Test
    public void testDeletePropertyEventStarted() {
        ArrayList<MetaData> expectedMetaData = new ArrayList<MetaData>();
        expectedMetaData.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        expectedMetaData.add(new MetaData(MetaData.SFDC_CASE_NUMBER, SFDC_CASE_NUMBER));
        expectedMetaData.add(new MetaData(MetaData.PROPERTY_CODE, PROPERTY_CODE));
        expectedMetaData.add(new MetaData(MetaData.CLIENT_CODE, CLIENT_CODE));

        TetrisEvent te = tetrisEventManager.buildDeletePropertyEvent(PROPERTY_ID, PROPERTY_CODE, CLIENT_CODE, USER_ID,
                SFDC_CASE_NUMBER);

        assertEquals(TetrisEventType.DELETE_PROPERTY, te.getType());
        assertEquals(PROPERTY_ID, te.getPropertyId().intValue());
        assertNotNull(te.getDate());
        assertEquals(expectedMetaData, te.getMetaData());
    }

    @Test
    public void testDeletePropertyEventCompleted() {
        ArrayList<MetaData> expectedMetaData = new ArrayList<MetaData>();
        expectedMetaData.add(new MetaData(MetaData.INITIATED_BY, USER_ID));
        expectedMetaData.add(new MetaData(MetaData.SFDC_CASE_NUMBER, SFDC_CASE_NUMBER));
        expectedMetaData.add(new MetaData(MetaData.PROPERTY_CODE, PROPERTY_CODE));
        expectedMetaData.add(new MetaData(MetaData.CLIENT_CODE, CLIENT_CODE));

        TetrisEvent te = tetrisEventManager.buildDeletePropertyEvent(PROPERTY_ID, PROPERTY_CODE, CLIENT_CODE, USER_ID,
                SFDC_CASE_NUMBER);

        assertEquals(TetrisEventType.DELETE_PROPERTY, te.getType());
        assertEquals(PROPERTY_ID, te.getPropertyId().intValue());
        assertNotNull(te.getDate());
        assertEquals(expectedMetaData, te.getMetaData());
    }

    @Test
    public void testCapcityChangeEvent() {
        String newCapacity = "10";
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, newCapacity));
        TetrisEvent te = tetrisEventManager.buildCapacityChangeEvent(PROPERTY_ID, newCapacity);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.CAPACITY_CHANGED);
        assertEquals(te.getMetaDataByName(MetaData.NEW_CAPACITY).getValue(), newCapacity);
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
    }

    @Test
    public void testCreateCDPProcessingStartedEvent_noContext() {
        TetrisEvent tetrisEvent = tetrisEventManager.createCDPProcessingStartedEvent(PROPERTY_ID, CORRELATION_ID);
        List<MetaData> metaDataList = tetrisEvent.getMetaData();
        assertEquals(TetrisEventType.CDP_PROCESSING_STARTED, tetrisEvent.getType());
        assertEquals((Integer) PROPERTY_ID, tetrisEvent.getPropertyId());
        assertEquals(CORRELATION_ID, metaDataList.get(0).getValue());
    }

    @Test
    public void testCreateCDPProcessingStartedEvent_jobContext() {
        setUpJobContext();
        TetrisEvent tetrisEvent = tetrisEventManager.createCDPProcessingStartedEvent(PROPERTY_ID, CORRELATION_ID);
        List<MetaData> metaDataList = tetrisEvent.getMetaData();
        basicCDPAsserts(tetrisEvent, metaDataList);
        assertEquals(JOB_INSTANCE_ID.toString(), metaDataList.get(1).getValue());
    }

    @Test
    public void testRaiseDecisionUploadEvent() {
    }

    @Test
    public void testRaiseEndPoint() {
        String propertyId = "6";
        String type = TetrisEventType.BDE_INITIATED.name();
        TopicMessageProducer topicMessageProducer = Mockito.mock(TopicMessageProducer.class);
        TetrisEventManager temSpy = Mockito.spy(tetrisEventManager);
        Mockito.when(temSpy.getTopicMessageProducer()).thenReturn(topicMessageProducer);

        String response = temSpy.raise(propertyId, type);

        assertEquals("success", response);

        ArgumentCaptor<TetrisEvent> eventCaptor = ArgumentCaptor.forClass(TetrisEvent.class);
        verify(topicMessageProducer).sendTopicMessage(eq(TOPIC_NAME_NEW), eventCaptor.capture());
        TetrisEvent event = eventCaptor.getValue();
        assertEquals(6, event.getPropertyId().intValue());
        assertEquals(TetrisEventType.BDE_INITIATED, event.getType());
        assertNotNull(event.getDate());

        List<MetaData> metaData = event.getMetaData();
        assertContains(MetaData.INITIATED_BY, "REST", metaData);
        assertContains(MetaData.INPUT_IDENTIFIER, metaData);
    }

    @Test
    public void testRaiseEndPointFailure() {
        String propertyId = "6";
        String type = TetrisEventType.BDE_INITIATED.name();

        String response = tetrisEventManager.raise(propertyId, type);
        assertEquals("failure", response);
    }

    @Test
    public void firesEventAfterBDECompleted() {
        TetrisEvent tetrisEvent = new TetrisEvent();
        tetrisEvent.setPropertyId(6);
        tetrisEvent.setType(TetrisEventType.BDE_COMPLETED);

        TopicMessageProducer topicMessageProducer = Mockito.mock(TopicMessageProducer.class);
        TetrisEventManager temSpy = Mockito.spy(tetrisEventManager);
        Mockito.when(temSpy.getTopicMessageProducer()).thenReturn(topicMessageProducer);

        temSpy.raiseEvent(tetrisEvent);

        verify(publisher).publishTetrisEvent(tetrisEvent);
    }

    @Test
    public void firesEventAfterCDPCompleted() {
        TetrisEvent tetrisEvent = new TetrisEvent();
        tetrisEvent.setPropertyId(6);
        tetrisEvent.setType(TetrisEventType.CDP_COMPLETED);

        TopicMessageProducer topicMessageProducer = Mockito.mock(TopicMessageProducer.class);
        TetrisEventManager temSpy = Mockito.spy(tetrisEventManager);
        Mockito.when(temSpy.getTopicMessageProducer()).thenReturn(topicMessageProducer);

        temSpy.raiseEvent(tetrisEvent);

        verify(publisher).publishTetrisEvent(tetrisEvent);
    }

    @Test
    public void doesntFireEventForOtherTypes() {
        for (TetrisEventType type : TetrisEventType.values()) {
            if (!(TetrisEventType.BDE_COMPLETED.equals(type) || TetrisEventType.CDP_COMPLETED.equals(type))) {
                TetrisEvent tetrisEvent = new TetrisEvent();
                tetrisEvent.setPropertyId(6);
                tetrisEvent.setType(type);

                TopicMessageProducer topicMessageProducer = Mockito.mock(TopicMessageProducer.class);
                TetrisEventManager temSpy = Mockito.spy(tetrisEventManager);
                Mockito.when(temSpy.getTopicMessageProducer()).thenReturn(topicMessageProducer);

                temSpy.raiseEvent(tetrisEvent);

                verify(publisher, never()).publishTetrisEvent(tetrisEvent);
            }
        }
    }

    @Test
    public void createCDPDecisionsUploadedEvent() {
        Integer propertyId = 5;
        String decisionIdentifier = "theDecisionIdentifier";
        String inputIdentifier = "theInputIdentifier";

        JobStepContext jobStepContext = new JobStepContext();
        jobStepContext.setJobInstanceId(123L);

        PlatformThreadLocalContextHolder.put(Constants.JOB_STEP_CONTEXT_KEY, jobStepContext);

        TetrisEvent event = tetrisEventManager.createCDPDecisionsUploadedEvent(propertyId, decisionIdentifier,
                inputIdentifier);

        assertNotNull(event);
        assertEquals(TetrisEventType.CDP_DECISIONS_UPLOADED, event.getType());
        assertEquals(propertyId, event.getPropertyId());
        assertNotNull(event.getDate());

        assertEquals(inputIdentifier, event.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue());
        assertEquals(decisionIdentifier, event.getMetaDataByName(MetaData.DECISIONS_IDENTIFIER).getValue());
        assertEquals("123", event.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue());

    }

    @Test
    public void createCDPDecisionsUploadedEventNoJobInstanceId() {
        Integer propertyId = 5;
        String decisionIdentifier = "theDecisionIdentifier";
        String inputIdentifier = "theInputIdentifier";

        JobStepContext jobStepContext = new JobStepContext();

        PlatformThreadLocalContextHolder.put(Constants.JOB_STEP_CONTEXT_KEY, jobStepContext);

        TetrisEvent event = tetrisEventManager.createCDPDecisionsUploadedEvent(propertyId, decisionIdentifier,
                inputIdentifier);

        assertNotNull(event);
        assertEquals(TetrisEventType.CDP_DECISIONS_UPLOADED, event.getType());
        assertEquals(propertyId, event.getPropertyId());
        assertNotNull(event.getDate());

        assertEquals(inputIdentifier, event.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue());
        assertEquals(decisionIdentifier, event.getMetaDataByName(MetaData.DECISIONS_IDENTIFIER).getValue());
        assertNull(event.getMetaDataByName(MetaData.JOB_INSTANCE_ID));

    }

    @Test
    public void createCDPDecisionsUploadedEventNoJobStepContext() {
        Integer propertyId = 5;
        String decisionIdentifier = "theDecisionIdentifier";
        String inputIdentifier = "theInputIdentifier";

        TetrisEvent event = tetrisEventManager.createCDPDecisionsUploadedEvent(propertyId, decisionIdentifier,
                inputIdentifier);

        assertNotNull(event);
        assertEquals(TetrisEventType.CDP_DECISIONS_UPLOADED, event.getType());
        assertEquals(propertyId, event.getPropertyId());
        assertNotNull(event.getDate());

        assertEquals(inputIdentifier, event.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue());
        assertEquals(decisionIdentifier, event.getMetaDataByName(MetaData.DECISIONS_IDENTIFIER).getValue());
        assertNull(event.getMetaDataByName(MetaData.JOB_INSTANCE_ID));

    }

    @Test
    public void createScheduledReportsInitiatedEvent() {
        String inputIdentifier = "theInputIdentifier";
        setUpJobContext();

		ArrayList<MetaData> mdList = new ArrayList<MetaData>();
		mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
		TetrisEvent te = tetrisEventManager.createScheduledReportsInitiatedEvent(PROPERTY_ID, inputIdentifier);
		assertNotNull(te);

		assertEquals(te.getType(), TetrisEventType.SCHEDULED_REPORTS_INITIATED);
		assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
		assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
		assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(),JOB_INSTANCE_ID.toString());
    }

    @Test
    public void createScheduledReportsCompletedEvent() {
        String inputIdentifier = "theInputIdentifier";

		ArrayList<MetaData> mdList = new ArrayList<MetaData>();
		mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
		TetrisEvent te = tetrisEventManager.createScheduledReportsCompletedEvent(PROPERTY_ID, inputIdentifier);
		assertNotNull(te);

		assertEquals(te.getType(), TetrisEventType.SCHEDULED_REPORTS_COMPLETED);
		assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
		assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
    }

    @Test
    public void test_createBDECompletedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";
        String type2FileName = "type2FileName";

        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
        TetrisEvent te = tetrisEventManager.createBDECompletedEvent(propertyId, inputIdentifier, type2FileName);
        assertNotNull(te);

        assertEquals(te.getType(), TetrisEventType.BDE_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));

        te = tetrisEventManager.createBDECompletedEvent(propertyId, null, type2FileName);
        assertNotNull(te);

        assertEquals(te.getType(), TetrisEventType.BDE_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.TYPE_2_FILE_NAME).getValue(), type2FileName);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));

        setUpJobContext();
        te = tetrisEventManager.createBDECompletedEvent(propertyId, null, type2FileName);
        assertNotNull(te);

        assertEquals(te.getType(), TetrisEventType.BDE_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.TYPE_2_FILE_NAME).getValue(), type2FileName);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createPacmanExtractGeneratedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";
        String pacmanExtractFile = "type2FileName";
        setUpJobContext();
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
        TetrisEvent te = tetrisEventManager.createPacmanExtractGeneratedEvent(propertyId, inputIdentifier, pacmanExtractFile);
        assertNotNull(te);

        assertEquals(te.getType(), TetrisEventType.PACMAN_EXTRACT_GENERATED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.TYPE_2_FILE_NAME).getValue(), pacmanExtractFile);
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createCDPProcessingStartedEvent() {
        Integer propertyId = 5;
        String correlationId = "correlationId";

        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createCDPProcessingStartedEvent(correlationId, propertyId);
        assertNotNull(te);

        assertEquals(te.getType(), TetrisEventType.CDP_PROCESSING_STARTED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), correlationId);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));

        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createCDPCompletedEvent() {
        int propertyId = 5;
        String correlationId = "correlationId";

        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createCDPCompletedEvent(propertyId, correlationId);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.CDP_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), correlationId);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue(), JOB_INSTANCE_ID.toString());

    }

    @Test
    public void test_createBDEDecisionsUploadedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";
        String decisionsIdentifier = "decisionsIdentifier";


        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createBDEDecisionsUploadedEvent(propertyId, decisionsIdentifier, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.BDE_DECISIONS_UPLOADED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertEquals(te.getMetaDataByName(MetaData.DECISIONS_IDENTIFIER).getValue(), decisionsIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());

    }

    @Test
    public void test_createBDEErrorEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";

        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createBDEErrorEvent(propertyId, inputIdentifier, PROBLEM_ID);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.BDE_ERROR);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertEquals(te.getMetaDataByName(MetaData.PROBLEM_ID).getValue(), PROBLEM_ID.toString());
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());

    }

    @Test
    public void test_createBDEInitiatedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";
        String fileSize = "fileSize";
        String stage = "stage";
        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createBDEInitiatedEvent(propertyId, inputIdentifier, fileSize, stage);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.BDE_INITIATED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertEquals(te.getMetaDataByName(MetaData.FILE_SIZE).getValue(), fileSize);
        assertEquals(te.getMetaDataByName(MetaData.STAGE).getValue(), stage);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());

    }

    @Test
    public void test_createCDPDecisionsUploadedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";
        String decisionsIdentifier = "decisionsIdentifier";

        setUpJobContext();

        TetrisEvent te = tetrisEventManager.createCDPDecisionsUploadedEvent(propertyId, decisionsIdentifier, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.CDP_DECISIONS_UPLOADED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertEquals(te.getMetaDataByName(MetaData.DECISIONS_IDENTIFIER).getValue(), decisionsIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createScheduledReportsCompletedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";

        TetrisEvent te = tetrisEventManager.createScheduledReportsCompletedEvent(propertyId, null);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.SCHEDULED_REPORTS_COMPLETED);
        assertNull(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertNull(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID));

        setUpJobContext();
        te = tetrisEventManager.createScheduledReportsCompletedEvent(propertyId, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.SCHEDULED_REPORTS_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createFunctionSpaceCompletedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";

        TetrisEvent te = tetrisEventManager.createFunctionSpaceCompletedEvent(propertyId, null);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.FUNCTION_SPACE_DATA_LOAD_COMPLETED);
        assertNull(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertNull(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID));

        setUpJobContext();
        te = tetrisEventManager.createFunctionSpaceCompletedEvent(propertyId, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.FUNCTION_SPACE_DATA_LOAD_COMPLETED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());
    }

    @Test
    public void test_createFunctionSpaceInitiatedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";

        setUpJobContext();
        TetrisEvent te = tetrisEventManager.createFunctionSpaceInitiatedEvent(propertyId, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.FUNCTION_SPACE_DATA_LOAD_INITIATED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());

    }

    @Test
    public void test_createBDEProcessingStartedEvent() {
        Integer propertyId = 5;
        String inputIdentifier = "theInputIdentifier";

        setUpJobContext();
        TetrisEvent te = tetrisEventManager.createBDEProcessingStartedEvent(propertyId, inputIdentifier);
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.BDE_PROCESSING_STARTED);
        assertEquals(te.getMetaDataByName(MetaData.INPUT_IDENTIFIER).getValue(), inputIdentifier);
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
        assertEquals(te.getMetaDataByName(MetaData.JOB_INSTANCE_ID).getValue().toString(), JOB_INSTANCE_ID.toString());

    }

    private void assertContains(String name, List<MetaData> list) {
        assertContains(name, null, list);
    }

    private void assertContains(String name, String value, List<MetaData> list) {
        for (MetaData metaData : list) {
            if (metaData.getName().equals(name)) {
                if ((value == null) || metaData.getValue().equals(value)) {
                    return;
                }
            }
        }

        fail("Didn't find expected name/value in metadata: " + name + "/" + value);
    }

    private void basicCDPAsserts(TetrisEvent tetrisEvent, List<MetaData> metaDataList) {
        assertEquals(TetrisEventType.CDP_PROCESSING_STARTED, tetrisEvent.getType());
        assertEquals((Integer) PROPERTY_ID, tetrisEvent.getPropertyId());
        assertEquals(CORRELATION_ID, metaDataList.get(0).getValue());
    }

    private void setUpJobContext() {
        JobStepContext jobStepContext = new JobStepContext();
        jobStepContext.setJobInstanceId(JOB_INSTANCE_ID);
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
    }

    private void assertStageChanges(TetrisEvent te, Stage stage, Boolean dscf, Boolean sfatl) {
        assertNotNull(te);
        assertEquals(te.getType(), TetrisEventType.STAGE_CHANGED);
        assertEquals(te.getMetaDataByName(MetaData.INITIATED_BY).getValue(), USER_ID);
        assertEquals(te.getMetaDataByName(MetaData.NOTE).getValue(), NOTE);
        assertEquals(te.getMetaDataByName(MetaData.NEW_STAGE).getValue(), stage.getCode());
        if (dscf == null) {
            assertNull(te.getMetaDataByName(MetaData.DECISION_STAGE_CHANGE_FLAG));
        } else {
            assertEquals(te.getMetaDataByName(MetaData.DECISION_STAGE_CHANGE_FLAG).getValue(), dscf.toString());
        }
        if (sfatl == null) {
            assertNull(te.getMetaDataByName(MetaData.SRP_FPLOS_AT_TOTAL_LEVEL));
        } else {
            assertEquals(te.getMetaDataByName(MetaData.SRP_FPLOS_AT_TOTAL_LEVEL).getValue(), sfatl.toString());
        }
        assertEquals(Long.valueOf(te.getPropertyId()), Long.valueOf(PROPERTY_ID));
        assertNull(te.getMetaDataByName(MetaData.ERROR_MESSAGE));
    }
}
