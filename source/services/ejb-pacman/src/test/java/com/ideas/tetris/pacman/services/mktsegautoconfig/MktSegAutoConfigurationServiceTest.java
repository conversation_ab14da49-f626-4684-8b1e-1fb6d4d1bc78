package com.ideas.tetris.pacman.services.mktsegautoconfig;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentAttributeSet;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.job.JobService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.*;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.FILE_NOT_PRESENT;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.FILE_PROCESSING_ERROR;
import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
class MktSegAutoConfigurationServiceTest extends AbstractG3JupiterTest {

    public static final long JOB_EXECUTION_ID = 123L;
    public static final long JOB_INSTANCE_ID = 234L;
    public static final long JOB_TIMEOUT = 120000L;
    public static final String MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_5.csv";
    public static final String MKT_SEG_AUTO_CONFIG_TEST_INVALID_FILE_PATH = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_4.csv";
    public static final String MKT_SEG_AUTO_CONFIG_WITH_COMMA_TEST_FILE_PATH = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_5_with_commas.csv";
    public static final String MKT_SEG_AUTO_CONFIG_WITH_ACCENTS_AND_ANSI_ENCODING_TEST_FILE_PATH = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_with_accents.csv";
    public static final String MKT_SEG_AUTO_CONFIG_EMPTY_TEST_FILE_PATH = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_5_empty.csv";

    @Spy
    @InjectMocks
    MktSegAutoConfigurationService mktSegAutoConfigurationService;

    @Mock
    JobService jobService;

    @Mock
    JobMonitorService jobMonitorService;

    @Mock
    AnalyticalMarketSegmentService analyticalMarketSegmentService;

    @Mock
    DateService dateService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Captor
    ArgumentCaptor<Map<String, Object>> jobParamtersCaptor;

    @BeforeEach
    public void setUp() {
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH);
        inject(mktSegAutoConfigurationService, "tenantCrudService", tenantCrudService());
    }

    @Test
    public void startMarketSegmentAutoConfigurationJob() {
        List<String> straightBarRateCodes = new ArrayList<>();

        ArgumentCaptor<JobName> jobCaptor = ArgumentCaptor.forClass(JobName.class);
        when(jobService.startGuaranteedNewInstance(jobCaptor.capture(), anyMap())).thenReturn(JOB_EXECUTION_ID);
        when(jobMonitorService.getJobInstanceId(JOB_EXECUTION_ID)).thenReturn(JOB_INSTANCE_ID);

        JobView jobView = mktSegAutoConfigurationService.startMarketSegmentAutoConfigurationJob(straightBarRateCodes);

        verify(jobService).startGuaranteedNewInstance(jobCaptor.capture(), anyMap());
        JobName jobName = jobCaptor.getValue();
        assertEquals("MktSegAutoConfigurationJob", jobName.name());
        verify(jobMonitorService).getJobViewWaitUntilCompletedOrAbandoned(JOB_INSTANCE_ID, false, JOB_TIMEOUT);
    }

    @Test
    public void saveAutoConfigurationOutput() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH);
        mktSegAutoConfigurationService.saveAutoConfigurationOutput();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(7, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(0), "MS1", "RC1", "MS1_USB",
                EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(1), "MS2", "RC2", "MS2_QY",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(2), "MS2", "RC3", "MS2_QY",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(3), "MS2", "RC4", "MS2_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(4), "MS2", null, "MS2_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.DEFAULT);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(5), "MS3", null, "MS3",
                AnalyticalMarketSegmentAttribute.GROUP, RateCodeTypeEnum.ALL);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(6), "MS4", null, "MS4",
                AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED, RateCodeTypeEnum.ALL);

        List<MarketSegmentMaster> marketSegmentMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(7, marketSegmentMasters.size());

        assertMarketSegmentMaster(marketSegmentMasters.get(0), "MS1_USB", EQUAL_TO_BAR,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(1), "MS2_QY", AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(2), "MS2_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(3), "MS3", AnalyticalMarketSegmentAttribute.GROUP,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(4), "MS2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(5), "MS2_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
        assertMarketSegmentMaster(marketSegmentMasters.get(6), "MS4", AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
    }

    @Test
    void saveAutoConfigurationOutputWithRateCodesContainingCommas() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_WITH_COMMA_TEST_FILE_PATH);

        mktSegAutoConfigurationService.saveAutoConfigurationOutput();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(7, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(0), "MS1", "RC1", "MS1_USB",
                EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(1), "MS2", "RC2", "MS2_QY",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(2), "MS2", "RC3,INC", "MS2_QY",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS);
    }

    @Test
    void saveAutoConfigurationOutputWithAccentedCharactersAndANSIEncodedCSVFile() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_WITH_ACCENTS_AND_ANSI_ENCODING_TEST_FILE_PATH);

        mktSegAutoConfigurationService.saveAutoConfigurationOutput();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(3, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(0), "Affaires Individuel", null, "Affaires Individuel_DEF",
                EQUAL_TO_BAR, RateCodeTypeEnum.DEFAULT);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(1), "Affaires Soir�e Etape", null, "Affaires Soir�e Etape",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.ALL);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(2), "Soci�t�s n�goci�", null, "Soci�t�s n�goci�",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.ALL);
    }

    public void assertAnalyticalMarketSegment(AnalyticalMarketSegment ams, String marketCode, String rateCode, String mappedMarketCode,
                                              AnalyticalMarketSegmentAttribute attribute, RateCodeTypeEnum rateCodeType) {
        assertEquals(marketCode, ams.getMarketCode());
        if (rateCode == null) {
            assertNull(ams.getRateCode());
        } else {
            assertEquals(rateCode, ams.getRateCode());
        }
        assertEquals(mappedMarketCode, ams.getMappedMarketCode());
        assertEquals(attribute, ams.getAttribute());
        assertEquals(rateCodeType, ams.getRateCodeType());
    }

    private void assertMarketSegmentMaster(MarketSegmentMaster master, String mappedMarketCode, AnalyticalMarketSegmentAttribute attribute,
                                           MarketSegmentAttributeSet.MarketSegmentForecastActivityType forecastActivityType) {
        assertEquals(mappedMarketCode, master.getCode());
        assertEquals(mappedMarketCode, master.getName());
        assertEquals(mappedMarketCode, master.getDescription());
        assertEquals(attribute.getBusinessType().getId(), master.getBusinessTypeId());
        assertEquals(attribute.getYieldType().getId(), master.getYieldTypeId());
        assertEquals(forecastActivityType.getId(), master.getForecastActivityTypeId());
        assertEquals(attribute.getQualified() ? 1 : 0, master.getQualified());
        assertEquals(attribute.getFenced() ? 1 : 0, master.getFenced());
        assertEquals(attribute.getPackaged() ? 1 : 0, master.getPackageValue());
        assertEquals(attribute.getLinkType().getId(), master.getLink());
        assertEquals(attribute.getPricedByBar() ? 1 : 0, master.getPriceByBar());
        assertEquals(attribute.getBlock() ? 100 : 0, master.getBookingBlockPc());
    }

    @Test
    public void assignGroupMarketSegments() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        amsSummary.setMarketCode("LGS");
        amsSummary.setMappedCode("LGS");

        when(analyticalMarketSegmentService.getUnassignedGroupMarketSegments()).thenReturn(Collections.singletonList(amsSummary));
        mktSegAutoConfigurationService.assignGroupMarketSegments();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(1, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(0), "LGS", null, "LGS",
                AnalyticalMarketSegmentAttribute.GROUP, RateCodeTypeEnum.ALL);
        List<MarketSegmentMaster> marketSegmentMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(1, marketSegmentMasters.size());
        assertMarketSegmentMaster(marketSegmentMasters.get(0), "LGS",
                AnalyticalMarketSegmentAttribute.GROUP, MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
    }

    @Test
    public void assignIndividualMarketSegments() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        amsSummary.setMarketCode("LYO");
        amsSummary.setMappedCode("LYO");

        when(analyticalMarketSegmentService.getUnassignedIndividualMarketSegments()).thenReturn(Collections.singletonList(amsSummary));
        mktSegAutoConfigurationService.assignIndividualMarketSegments();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(1, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(0), "LYO", null, "LYO",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.ALL);
        List<MarketSegmentMaster> marketSegmentMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(1, marketSegmentMasters.size());
        assertMarketSegmentMaster(marketSegmentMasters.get(0), "LYO",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
    }

    @Test
    public void assignSharedMarketSegmentsWhenOneIsUnattributed() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        AnalyticalMarketSegment defAMS = new AnalyticalMarketSegment();
        defAMS.setMarketCode("MS1");
        defAMS.setMappedMarketCode("MS1_DEF");
        defAMS.setAttribute(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE);
        defAMS.setRateCodeType(RateCodeTypeEnum.DEFAULT);
        defAMS.setRank(RateCodeTypeEnum.DEFAULT.getRank());
        tenantCrudService().save(defAMS);
        tenantCrudService().flushAndClear();
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        amsSummary.setMarketCode("MS1");
        amsSummary.setMappedCode("MS1");
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.singletonList(amsSummary));
        when(analyticalMarketSegmentService.getRateCodes(anyList())).thenReturn(createRateCodeSummaries());
        mktSegAutoConfigurationService.assignSharedMarketSegments();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals(3, analyticalMarketSegments.size());
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(1), "MS1", "RC1", "MS1_DEF",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, RateCodeTypeEnum.EQUALS);
        assertAnalyticalMarketSegment(analyticalMarketSegments.get(2), "MS1", "RC2", "MS1_DEF",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE, RateCodeTypeEnum.EQUALS);
        List<MarketSegmentMaster> marketSegmentMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(1, marketSegmentMasters.size());
        assertMarketSegmentMaster(marketSegmentMasters.get(0), "MS1_DEF",
                AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE,
                MarketSegmentAttributeSet.MarketSegmentForecastActivityType.DEMAND_AND_WASH);
    }

    @Test
    public void assignSharedMarketSegmentsWhenNoneAreUnattributed() {
        tenantCrudService().deleteAll(AnalyticalMarketSegment.class);
        tenantCrudService().deleteAll(MarketSegmentMaster.class);
        tenantCrudService().flushAndClear();
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(emptyList());
        mktSegAutoConfigurationService.assignSharedMarketSegments();

        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(analyticalMarketSegments.isEmpty());
        List<MarketSegmentMaster> marketSegmentMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertTrue(marketSegmentMasters.isEmpty());
    }

    @Test
    void getUnassignedSharedMarketSegmentsWhenAllAreAttributed() {
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(emptyList());
        List<AnalyticalMarketSegmentSummary> result = mktSegAutoConfigurationService.getUnassignedSharedMarketSegments();
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnassignedSharedMarketSegmentsWhenOneIsUnattributed() {
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        amsSummary.setMarketCode("MS1");
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.singletonList(amsSummary));
        when(analyticalMarketSegmentService.getRateCodes(anyList())).thenReturn(createRateCodeSummaries());

        List<AnalyticalMarketSegmentSummary> result = mktSegAutoConfigurationService.getUnassignedSharedMarketSegments();

        assertEquals(2, result.size());
    }

    @Test
    void validateMSAttributionsForCorrectAttributions() {
        assertDoesNotThrow(() -> mktSegAutoConfigurationService.validateMSAttributions(MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH));
    }

    @Test
    void validateMSAttributionsShouldThrowExceptionWhenOutputIsEmpty() {
        TetrisException exception = assertThrows(TetrisException.class, () ->
                mktSegAutoConfigurationService.validateMSAttributions(MKT_SEG_AUTO_CONFIG_EMPTY_TEST_FILE_PATH));

        assertEquals(ErrorCode.FILE_PROCESSING_ERROR, exception.getErrorCode());
        assertEquals("No market segment mappings found", exception.getBaseMessage());
    }

    @Test
    void validateMSAttributionsShouldNotThrowExceptionWhenAccentedCharactersArePresentAndFileHasANSIEncoding() {
        assertDoesNotThrow(() -> mktSegAutoConfigurationService.validateMSAttributions(MKT_SEG_AUTO_CONFIG_WITH_ACCENTS_AND_ANSI_ENCODING_TEST_FILE_PATH));
    }

    @Test
    void addAMSCompositionChange() {
        TenantCrudServiceBean tenantCrudService = mock(TenantCrudServiceBean.class);
        inject(mktSegAutoConfigurationService, "tenantCrudService", tenantCrudService);
        when(tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_DEFAULTS)).thenReturn(List.of(
                new AnalyticalMarketSegment("MS1", "RC1", "MS1_USB", EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS),
                new AnalyticalMarketSegment("MS2", "RC2", "MS2_DEF", QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.DEFAULT)));

        mktSegAutoConfigurationService.addAMSCompositionChange();

        verify(analyticalMarketSegmentService, times(2)).createAMSCompositionChangeRecord(anyString(), anyString());
    }

    @Test
    void allRateCodesAreAttributedReturnsFalseWhenSharedMSAreRemaining() {
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        amsSummary.setMarketCode("MS1");
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(Collections.singletonList(amsSummary));
        when(analyticalMarketSegmentService.getRateCodes(anyList())).thenReturn(createRateCodeSummaries());
        assertFalse(mktSegAutoConfigurationService.allRateCodesAreAttributed());
    }

    @Test
    void allRateCodesAreAttributed() {
        when(analyticalMarketSegmentService.getUnassignedSharedMarketSegments()).thenReturn(emptyList());
        when(analyticalMarketSegmentService.getRateCodes(anyList())).thenReturn(createRateCodeSummaries());
        assertTrue(mktSegAutoConfigurationService.allRateCodesAreAttributed());
    }

    @Test
    void isMktSegAutoConfigurationJobInProgress() {
        when(jobService.isJobActive(eq(JobName.MktSegAutoConfigurationJob), anyMap())).thenReturn(true);
        assertTrue(mktSegAutoConfigurationService.isMktSegAutoConfigurationJobInProgress());
        verify(jobService).isJobActive(eq(JobName.MktSegAutoConfigurationJob), anyMap());
    }

    @Test
    void startAMSDataPopulationJob() {
        PacmanWorkContextHelper.setPropertyId(5);
        Date businessDate = new Date();
        when(dateService.getBusinessDate()).thenReturn(businessDate);

        mktSegAutoConfigurationService.startAMSDataPopulationJob();

        verify(jobService).startGuaranteedNewInstance(eq(JobName.AMSDataLoadJob), jobParamtersCaptor.capture());
        assertTrue(jobParamtersCaptor.getValue().containsValue(businessDate));
        assertTrue(jobParamtersCaptor.getValue().containsValue(5));
    }

    @Test
    void shouldThrowExceptionWhenValidatingAMSRecordWithEqualToBarAttributeANdNoRateCode() throws IOException {
        CSVParser parser = CSVFormat.DEFAULT.builder()
                .setHeader("property_id", "market_code", "rate_code", "mapped_market_code", "attribute", "predict_forecast_type_id")
                .setSkipHeaderRecord(true)
                .build()
                .parse(new StringReader("property_id,market_code,rate_code,mapped_market_code,attribute,predict_forecast_type_id\n" +
                        "994364,TREV,A-RACK,TREV_USB,EQUAL_TO_BAR,1\n" +
                        "994364,CORP,,CORP,EQUAL_TO_BAR,1\n"));

        TetrisException expectedException = assertThrows(TetrisException.class,
                () -> mktSegAutoConfigurationService.validateAMSRecord().accept(parser.getRecords().get(1)));

        assertEquals(FILE_PROCESSING_ERROR, expectedException.getErrorCode());
        assertEquals("Market code CORP has been attributed as EQUAL_TO_BAR but does not have a rate code", expectedException.getBaseMessage());
    }

    @Test
    void validateAMSMacroOutputShouldThrowExceptionWhenFileIsNotFound() {
        String amsMacroOutputPath = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_0.csv";
        TetrisException expectedException = assertThrows(TetrisException.class,
                () -> mktSegAutoConfigurationService.validateAMSMacroOutput(amsMacroOutputPath));
        assertEquals(FILE_NOT_PRESENT, expectedException.getErrorCode());
    }

    @Test
    void validateAMSMacroOutputShouldBeSuccessfulWhenNoErrorIsFound() {
        String amsMacroOutputPath = "src/test/resources/mktsegautoconfiguration/analytic_mkt_seg_proposed_5.csv";
        assertDoesNotThrow(() -> mktSegAutoConfigurationService.validateAMSMacroOutput(amsMacroOutputPath));
    }

    @Test
    void shouldGetValidMktSegAutoConfigMacroOutputFilePath(){
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH);

        String filePath = mktSegAutoConfigurationService.getAMSMacroOutputExcelPath();

        assertEquals(MKT_SEG_AUTO_CONFIG_TEST_FILE_PATH, filePath);
        verify(mktSegAutoConfigurationService,atLeastOnce()).buildAMSMacroExcelOutputPath();
    }

    @Test
    void shouldThrowExceptionWhenMktSegConfigOutputFileIsNotPresent(){
        when(mktSegAutoConfigurationService.buildAMSMacroExcelOutputPath()).thenReturn(MKT_SEG_AUTO_CONFIG_TEST_INVALID_FILE_PATH);

        TetrisException expectedException = assertThrows(TetrisException.class, () -> mktSegAutoConfigurationService.getAMSMacroOutputExcelPath());

        assertEquals(FILE_NOT_PRESENT, expectedException.getErrorCode());
        assertEquals("SAS macro output for Market Segment Auto Config was not present", expectedException.getBaseMessage());
        verify(mktSegAutoConfigurationService, atLeastOnce()).buildAMSMacroExcelOutputPath();
    }

    private List<RateCodeSummary> createRateCodeSummaries() {
        List<RateCodeSummary> rateCodeSummaries = new ArrayList<>();
        RateCodeSummary rateCode1 = new RateCodeSummary();
        rateCode1.setMarketCode("MS1");
        rateCode1.setRateCode("RC1");
        rateCodeSummaries.add(rateCode1);
        RateCodeSummary rateCode2 = new RateCodeSummary();
        rateCode2.setMarketCode("MS1");
        rateCode2.setRateCode("RC2");
        rateCodeSummaries.add(rateCode2);
        RateCodeSummary rateCode3 = new RateCodeSummary();
        rateCode3.setMarketCode("MS2");
        rateCode3.setRateCode("RC2");
        rateCodeSummaries.add(rateCode3);
        return rateCodeSummaries;
    }
}