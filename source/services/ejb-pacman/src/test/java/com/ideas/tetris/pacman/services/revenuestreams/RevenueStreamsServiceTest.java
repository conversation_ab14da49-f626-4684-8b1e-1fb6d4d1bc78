package com.ideas.tetris.pacman.services.revenuestreams;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.RoomTypeCache;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.revenuestreams.dto.RevenueStreamDto;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStream;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStreamCost;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStreamDetail;
import com.ideas.tetris.pacman.services.revenuestreams.repository.RevenueStreamRepository;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class RevenueStreamsServiceTest extends AbstractG3JupiterTest {

    private static final String ROOM_TYPE_1 = "RT_1";

    private static final String ROOM_TYPE_2 = "RT_2";

    private static final String DIVISION_CODE = "500";

    private static final String DIVISION_NAME = "casino";

    private static final String DIVISION_CODE_2 = "600";

    private static final String DIVISION_NAME_2 = "banquet";

    public static final int PROPERTY_ID = 5;

    public static final String DLX = "DLX";


    public static final LocalDate stayDate = LocalDate.of(2019, 1, 01);
    public static final String rateCode = "AAA";
    public static final String groupCode = "zpeqkmaka";
    public static final String mktSegCode = "RACK";
    public static final BigDecimal actualValue = new BigDecimal("222.00");
    public static final BigDecimal estimateValue = new BigDecimal("111.00");
    public static final String playerTier = "41";
    public static final Integer roomNightCount = 1;
    public static final Integer los = 1;
    public static final String accomTypeCode = DLX;


    @InjectMocks
    RevenueStreamsService revenueStreamsService;

    @Mock
    private RevenueStreamRepository repository;

    @Mock
    AccommodationService accommodationService;

    @Mock
    RoomTypeCache roomTypeCache;

    @Mock
    AnalyticalMarketSegmentService analyticalMarketSegmentService;

    @Mock
    JpaJdbcUtil jpaJdbcUtil;

    @BeforeEach
    public void setUp() {
        revenueStreamsService.tenantCrudService = tenantCrudService();
        RevenueStreamsJdbcBatchable<RevenueStreamDetail> revenueStreamsBatching = new RevenueStreamsJdbcBatchable<>();
        revenueStreamsBatching.setTenantCrudService(tenantCrudService());
        revenueStreamsBatching.setJpaJdbcUtil(jpaJdbcUtil);
        inject(revenueStreamsService, "revenueStreamsBatching", revenueStreamsBatching);
        lenient().when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
        tenantCrudService().deleteAll(RevenueStreamDetail.class);
        tenantCrudService().deleteAll(RevenueStreamCost.class);
        tenantCrudService().deleteAll(RevenueStream.class);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldSaveRevenueStreamDetails(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
    }

    private void mockRoomTypeCache(String accomTypeCode) {
        AccomType at = new AccomType();
        at.setId(4);
        lenient().when(roomTypeCache.get(PROPERTY_ID, accomTypeCode)).thenReturn(at);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldNotSaveRevenueStreamDetailForNewRevenueStream(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        assertThrows(TetrisException.class, () -> {
            mockRoomTypeCache(accomTypeCode);
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap("255", rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertEquals(0, revenueStreamsService.getRevenueStreamDetails().size());
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldSaveRevenueStreamDetailsWithNullValues(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldSaveDetails_PlayerTier_NA(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
    }

    @Test
    public void shouldSaveDetails_PlayerTier_NULL() {
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldUpdateAccomTypeIds_WhenAccomTypeCodeIsExisting(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 1, 01);
        final String rateCode = "AAA";
        final String groupCode = "CHHUCG";
        final String mktSegCode = "RACK";
        final BigDecimal estimateValue = new BigDecimal("111.00");
        final BigDecimal actualValue = new BigDecimal("222.00");
        final String playerTier = "41";
        final Integer roomNightCount = 1;
        final Integer los = 1;
        final String accomTypeCode = DLX;
        final Integer accomTypeId = 4;
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final List<RevenueStream> revenueStreams = tenantCrudService().findAll(RevenueStream.class);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreams.stream().forEach(value -> {
            revenueStreamsService.updateAccomTypeIdsRevenueStreamDetails(value.getId().toString(), stayDate.toString());
        });
        tenantCrudService().flushAndClear();
        assertEquals(accomTypeId, getRevenueStreamDetail().getAccomTypeId());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldNotUpdateAccomTypeIds_WhenAccomTypeCodeIsNew(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 1, 01);
        final String rateCode = "AAA";
        final String groupCode = "CHHUCG";
        final String mktSegCode = "RACK";
        final BigDecimal estimateValue = new BigDecimal("111.00");
        final BigDecimal actualValue = new BigDecimal("222.00");
        final String playerTier = "41";
        final Integer roomNightCount = 1;
        final Integer los = 1;
        final String accomTypeCode = "zpeqkmaka";
        final Integer accomTypeId = null;
        mockRoomTypeCache(DLX);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.updateAccomTypeIdsRevenueStreamDetails("1", "2019-01-01");
        tenantCrudService().flushAndClear();
        assertEquals(accomTypeId, getRevenueStreamDetail().getAccomTypeId());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldUpdateGroupIds_WhenGroupBlockCodeIsExisting(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 1, 01);
        final String rateCode = "AAA";
        final String groupCode = "CHHUCG";
        final String mktSegCode = "RACK";
        final BigDecimal estimateValue = new BigDecimal("111.00");
        final BigDecimal actualValue = new BigDecimal("222.00");
        final String playerTier = "41";
        final Integer roomNightCount = 1;
        final Integer los = 1;
        final String accomTypeCode = DLX;
        final Integer groupId = 11;
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final List<RevenueStream> revenueStreams = tenantCrudService().findAll(RevenueStream.class);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreams.stream().forEach(value -> {
            revenueStreamsService.updateGroupIdsRevenueStreamDetails(value.getId().toString(), stayDate.toString());
        });
        tenantCrudService().flushAndClear();
        assertEquals(groupId, getRevenueStreamDetail().getGroupId());
    }

    @Test
    public void shouldThrowException_WhenAccomTypeCodeNull() {
        assertThrows(NullPointerException.class, () -> {
            final LocalDate stayDate = LocalDate.of(2019, 1, 01);
            final String rateCode = "AAA";
            final String groupCode = "zpeqkmaka";
            final String mktSegCode = "RACK";
            final BigDecimal estimateValue = new BigDecimal("111.00");
            final BigDecimal actualValue = new BigDecimal("222.00");
            final String playerTier = "41";
            final Integer roomNightCount = 1;
            final Integer los = 1;
            final String accomTypeCode = null;
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, null);
        });
    }

    @Test
    public void shouldThrowException_WhenMktSegCodeNull() {
        assertThrows(NullPointerException.class, () -> {
            final LocalDate stayDate = LocalDate.of(2019, 1, 01);
            final String rateCode = "AAA";
            final String groupCode = "zpeqkmaka";
            final String mktSegCode = null;
            final BigDecimal estimateValue = new BigDecimal("111.00");
            final BigDecimal actualValue = new BigDecimal("222.00");
            final String playerTier = "41";
            final Integer roomNightCount = 1;
            final Integer los = 1;
            final String accomTypeCode = DLX;
            mockRoomTypeCache(accomTypeCode);
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, null);
        });
    }

    @Test
    public void shouldThrowException_WhenEstimateValueNull() {
        final BigDecimal estimateValue = null;
        assertThrows(NullPointerException.class, () -> {
            mockRoomTypeCache(accomTypeCode);
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, null);
        });
    }

    @Test
    public void shouldThrowException_WhenActualValueNull() {
        assertThrows(NullPointerException.class, () -> {
            final BigDecimal actualValue = null;
            final Integer groupId = null;
            mockRoomTypeCache(accomTypeCode);
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, null);
        });
    }

    @Test
    public void shouldThrowException_WhenRoomNightCountNull() {
        assertThrows(NullPointerException.class, () -> {
            final Integer roomNightCount = null;
            mockRoomTypeCache(accomTypeCode);
            final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
            revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
            final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
            revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
            assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, null);
        });
    }

    @Test
    public void saveRevenueStream_And_RevenueStreamCost() {
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        assertRevenueStreamAndRevenueStreamCostData();
    }

    @Test
    public void updateRevenueStreamsWithJustGamingAsFalseAndMarginUpdated() {
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        assertRevenueStreamAndRevenueStreamCostData();
        final List<Map<String, Object>> updatedRevenueStreamMapList = updateRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(updatedRevenueStreamMapList);
        assertUpdatedRevenueStreamAndRevenueStreamCostData();
    }

    @Test
    public void insertRevenueStreamsForNewDateRange() {
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        assertRevenueStreamAndRevenueStreamCostData();
        final List<Map<String, Object>> insertedRevenueStreamMapList = insertRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(insertedRevenueStreamMapList);
        assertInsertedRevenueStreamAndRevenueStreamCostData();
    }

    @SneakyThrows
    @Test
    public void getMinimumOccupancyDTForLatestUpdateDt() {
        mockRoomTypeCache(accomTypeCode);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        Date minimumOccupancyDT = revenueStreamsService.getMinimumOccupanyDtForLatestUpdatedDt();
        assertNotNull(minimumOccupancyDT);
        Date expectedMinimumDate = DateUtil.parseDate("2019-01-01", DateUtil.DEFAULT_DATE_FORMAT);
        assertEquals(expectedMinimumDate, minimumOccupancyDT);
    }

    private void assertRevenueStream(String rateCode, String playerTier, BigDecimal estimateValue, BigDecimal actualValue, LocalDate stayDate, Integer roomNightCount, Integer los, String mktSegCode, String accomTypeCode, String groupCode) {
        assertRevenueStreamDetails(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
    }

    private RevenueStreamDetail getRevenueStreamDetail() {
        final List<RevenueStreamDetail> revenueStreamDetails = revenueStreamsService.getRevenueStreamDetails();
        assertEquals(1, revenueStreamDetails.size());
        revenueStreamDetails.sort(Comparator.comparing(RevenueStreamDetail::getAccomTypeId));
        return revenueStreamDetails.get(0);
    }

    private void assertUpdatedRevenueStreamAndRevenueStreamCostData() {
        List<RevenueStream> revenueStreams = revenueStreamsService.getRevenueStreams();
        List<RevenueStreamCost> revenueStreamCosts = revenueStreamsService.getRevenueStreamCosts();
        revenueStreams.sort(Comparator.comparing(RevenueStream::getId));
        revenueStreamCosts.sort(Comparator.comparing(RevenueStreamCost::getId));
        assertEquals(5, revenueStreamCosts.size());
        assertEquals(2, revenueStreams.size());
        final RevenueStreamCost revenueCost5 = revenueStreamCosts.get(4);
        final LocalDate effectiveDate5 = LocalDate.of(2025, 8, 01);
        assertValues(revenueCost5, revenueStreams.get(1).getId(), effectiveDate5, effectiveDate5.plusYears(100), false, BigDecimal.valueOf(0.82));
    }

    private void assertInsertedRevenueStreamAndRevenueStreamCostData() {
        List<RevenueStream> revenueStreams = revenueStreamsService.getRevenueStreams();
        List<RevenueStreamCost> revenueStreamCost = revenueStreamsService.getRevenueStreamCosts();
        revenueStreams.sort(Comparator.comparing(RevenueStream::getId));
        revenueStreamCost.sort(Comparator.comparing(RevenueStreamCost::getId));
        assertEquals(6, revenueStreamCost.size());
        assertEquals(2, revenueStreams.size());
        final RevenueStreamCost revenueCost5 = revenueStreamCost.get(5);
        final LocalDate effectiveDate6 = LocalDate.of(2025, 8, 01);
        assertValues(revenueCost5, revenueStreams.get(0).getId(), effectiveDate6, effectiveDate6.plusYears(100), false, BigDecimal.valueOf(0.25));
    }

    private void assertRevenueStreamAndRevenueStreamCostData() {
        List<RevenueStream> revenueStreams = revenueStreamsService.getRevenueStreams();
        List<RevenueStreamCost> revenueStreamCosts = revenueStreamsService.getRevenueStreamCosts();
        revenueStreams.sort(Comparator.comparing(RevenueStream::getId));
        revenueStreamCosts.sort(Comparator.comparing(RevenueStreamCost::getId));
        assertEquals(5, revenueStreamCosts.size());
        assertEquals(2, revenueStreams.size());
        final RevenueStreamCost revenueCost5 = revenueStreamCosts.get(4);
        final LocalDate effectiveDate5 = LocalDate.of(2025, 8, 01);
        assertValues(revenueCost5, revenueStreams.get(1).getId(), effectiveDate5, effectiveDate5.plusYears(100), true, BigDecimal.valueOf(0.45));
    }

    private List<Map<String, Object>> buildRevenueStreamAndRevenueStreamCostData() {
        final LocalDate effectiveDate1 = LocalDate.of(2019, 1, 01);
        final Map<String, Object> revenueStreamMap1 = buildRevenueStreamMap(DIVISION_CODE, DIVISION_NAME, "true", "0.65", effectiveDate1.toString());
        final LocalDate effectiveDate2 = LocalDate.of(2019, 5, 01);
        final Map<String, Object> revenueStreamMap2 = buildRevenueStreamMap(DIVISION_CODE, DIVISION_NAME, "true", "0.80", effectiveDate2.toString());
        final LocalDate effectiveDate3 = LocalDate.of(2019, 8, 01);
        final Map<String, Object> revenueStreamMap3 = buildRevenueStreamMap(DIVISION_CODE, DIVISION_NAME, "true", "0.45", effectiveDate3.toString());
        final LocalDate effectiveDate4 = LocalDate.of(2020, 5, 01);
        final Map<String, Object> revenueStreamMap4 = buildRevenueStreamMap(DIVISION_CODE_2, DIVISION_NAME_2, "true", "0.30", effectiveDate4.toString());
        final LocalDate effectiveDate5 = LocalDate.of(2025, 8, 01);
        final Map<String, Object> revenueStreamMap5 = buildRevenueStreamMap(DIVISION_CODE_2, DIVISION_NAME_2, "true", "0.45", effectiveDate5.toString());
        return asList(revenueStreamMap1, revenueStreamMap2, revenueStreamMap3, revenueStreamMap4, revenueStreamMap5);
    }

    private List<Map<String, Object>> updateRevenueStreamAndRevenueStreamCostData() {
        final LocalDate effectiveDate3 = LocalDate.of(2019, 8, 01);
        final Map<String, Object> revenueStreamMap3 = buildRevenueStreamMap(DIVISION_CODE, DIVISION_NAME, "true", "0.36", effectiveDate3.toString());
        final LocalDate effectiveDate5 = LocalDate.of(2025, 8, 01);
        final Map<String, Object> revenueStreamMap5 = buildRevenueStreamMap(DIVISION_CODE_2, DIVISION_NAME_2, "false", "0.82", effectiveDate5.toString());
        return asList(revenueStreamMap3, revenueStreamMap5);
    }

    private List<Map<String, Object>> insertRevenueStreamAndRevenueStreamCostData() {
        final LocalDate effectiveDate6 = LocalDate.of(2025, 8, 01);
        final Map<String, Object> revenueStreamMap6 = buildRevenueStreamMap(DIVISION_CODE, DIVISION_NAME, "false", "0.25", effectiveDate6.toString());
        return asList(revenueStreamMap6);
    }

    private AccomType getAccomType(String rtName, int roomTypeId) {
        AccomType accomType = new AccomType();
        accomType.setName(rtName);
        accomType.setAccomTypeCode(rtName);
        accomType.setId(roomTypeId);
        return accomType;
    }

    private Map<String, Object> buildRevenueStreamDetailsMap(String divisionCode, String rateCode, String playerTier, BigDecimal estimateValue, BigDecimal actualValue, LocalDate stayDate, Integer roomNightCount, Integer los, String mktSegCode, String accomTypeCode, String groupCode) {
        Map<String, Object> revenueMap = new HashMap<>();
        revenueMap.put("stayDate", stayDate);
        revenueMap.put("divisionCode", divisionCode);
        revenueMap.put("details", Arrays.asList(buildDetailsMap(rateCode, groupCode, mktSegCode, estimateValue, actualValue, playerTier, roomNightCount, los, accomTypeCode)));
        return revenueMap;
    }

    private Map buildDetailsMap(String rateCode, String groupCode, String mktSegCode, BigDecimal estimateValue, BigDecimal actualValue, String playerTier, Integer roomNightCount, Integer los, String accomTypeCode) {
        Map details = new HashMap();
        Timestamp lastUpdatedDTTM = null;
        try {
            lastUpdatedDTTM = new Timestamp(new SimpleDateFormat("dd/MM/yyyy").parse("13/08/2020").getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        details.put("rateCode", rateCode);
        details.put("playerTier", playerTier);
        details.put("estimateValue", estimateValue);
        details.put("actualValue", actualValue);
        details.put("actualFixedMargin", null);
        details.put("estimatedFixedMargin", null);
        details.put("roomNightCount", roomNightCount);
        details.put("los", los);
        details.put("marketCode", mktSegCode);
        details.put("roomType", accomTypeCode);
        details.put("blockCode", groupCode);
        details.put("createdDttm", lastUpdatedDTTM);
        details.put("lastUpdatedDttm", lastUpdatedDTTM);
        return details;
    }

    private Map<String, Object> buildRevenueStreamMap(String divisionCode, String divisionName, String gaming, String margin, String effectiveDate) {
        Map<String, Object> revenueMap = new HashMap<>();
        revenueMap.put("divisionCode", divisionCode);
        revenueMap.put("divisionName", divisionName);
        revenueMap.put("gaming", gaming);
        revenueMap.put("margin", margin);
        revenueMap.put("effectiveDate", effectiveDate);
        return revenueMap;
    }

    private void assertValues(RevenueStreamCost revenueCost, Integer revenueId, LocalDate startDate, LocalDate endDate, boolean isGaming, BigDecimal percentageCost) {
        assertEquals(revenueId, revenueCost.getRevenueStream().getId());
        assertEquals(startDate.toString(), LocalDateUtils.fromDate(revenueCost.getStartDate()).toString());
        assertEquals(endDate.toString(), LocalDateUtils.fromDate(revenueCost.getEndDate()).toString());
        assertEquals(isGaming, revenueCost.getRevenueStream().isGaming());
        assertEquals(BigDecimal.ONE.subtract(percentageCost).multiply(new BigDecimal(100)), revenueCost.getPercentageCost());
    }

    private void assertRevenueStreamDetails(String rateCode, String playerTier, BigDecimal estimateValue, BigDecimal actualValue, LocalDate stayDate, Integer roomNightCount, Integer los, String mktSegCode, String accomTypeCode, String groupCode) {
        RevenueStreamDetail revenueStreamDetail = getRevenueStreamDetail();
        SimpleDateFormat df = new SimpleDateFormat("yyy-MM-dd");
        assertEquals(rateCode, revenueStreamDetail.getRateCode());
        assertEquals(estimateValue, revenueStreamDetail.getEstimateValue());
        assertEquals(actualValue, revenueStreamDetail.getActualValue());
        assertEquals(BigDecimal.ZERO.setScale(2), revenueStreamDetail.getActualFixedCost());
        assertEquals(BigDecimal.ZERO.setScale(2), revenueStreamDetail.getEstimateFixedCost());
        assertEquals(playerTier, revenueStreamDetail.getTier());
        assertEquals(groupCode, revenueStreamDetail.getBlockCode());
        assertEquals(roomNightCount, revenueStreamDetail.getRoomNightCount());
        assertEquals(los, revenueStreamDetail.getLos());
        assertEquals(mktSegCode, revenueStreamDetail.getMktSegCode());
        assertEquals(accomTypeCode, revenueStreamDetail.getAccomTypeCode());
        assertEquals(stayDate.toString(), df.format(revenueStreamDetail.getOccupancyDt()));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldUpdateMktSegIds_WhenMappingISPresentInYieldCategoryTable(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 1, 01);
        final String rateCode = "BLACK";
        final String groupCode = "CHHUCG";
        final String mktSegCode = "COMP";
        final BigDecimal estimateValue = new BigDecimal("111.00");
        final BigDecimal actualValue = new BigDecimal("222.00");
        final String playerTier = "41";
        final Integer roomNightCount = 1;
        final Integer los = 1;
        final String accomTypeCode = DLX;
        final Integer mktSegId = 2;
        mockRoomTypeCache(accomTypeCode);
        addRecordInYieldCategoryRuleTable();
        addRecordInAnalyticalMarketSegmentTable();
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        assertRevenueStream(rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.updateMktSegIdsRevenueStreamDetails(1, 4);
    }

    private void addRecordInYieldCategoryRuleTable() {
        YieldCategoryRule yieldCategoryRule = new YieldCategoryRule();
        yieldCategoryRule.setMarketCode("COMP");
        yieldCategoryRule.setRateCode("BLACK");
        yieldCategoryRule.setAnalyticalMarketCode("COMP");
        yieldCategoryRule.setBookingStartDate(new org.joda.time.LocalDate(18000101));
        yieldCategoryRule.setBookingEndDate(new org.joda.time.LocalDate(29991231));
        yieldCategoryRule.setRank(10);
        tenantCrudService().save(yieldCategoryRule);
    }

    private void addRecordInAnalyticalMarketSegmentTable() {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setMarketCode("COMP");
        analyticalMarketSegment.setRateCode("BLACK");
        analyticalMarketSegment.setMappedMarketCode("COMP");
        analyticalMarketSegment.setAttribute(AnalyticalMarketSegmentAttribute.GROUP);
        analyticalMarketSegment.setCreatedByUserId(19124);
        analyticalMarketSegment.setLastUpdatedByUserId(19124);
        analyticalMarketSegment.setCreateDate(new Date());
        analyticalMarketSegment.setLastUpdatedDate(new Date());
        analyticalMarketSegment.setRateCodeType(RateCodeTypeEnum.ALL);
        analyticalMarketSegment.setRank(10);
        analyticalMarketSegment.setComplimentary(false);
        tenantCrudService().save(analyticalMarketSegment);
    }

    @Test
    public void testFetchRevenueStreamsIds() {
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        assertEquals(2, revenueStreamsService.fetchRevenueStreamIds().size());
    }

    @Test
    public void testDistinctOccupancyDates() {
        final LocalDate stayDate = LocalDate.of(2019, 12, 01);
        final String rateCode = "AAA";
        final String groupCode = "CHHUCG";
        final String mktSegCode = "RACK";
        final BigDecimal estimateValue = new BigDecimal("111.00");
        final BigDecimal actualValue = new BigDecimal("222.00");
        final String playerTier = "41";
        final Integer roomNightCount = 1;
        final Integer los = 1;
        final String accomTypeCode = DLX;
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        final Map<String, Object> revenueStreamDetailsMap = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap));
        final Map<String, Object> revenueStreamDetailsMap2 = buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(1), roomNightCount, los, mktSegCode, accomTypeCode, groupCode);
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap2));
        final Set<String> dates = revenueStreamsService.fetchDistinctOccupancyDates();
        assertEquals(2, dates.size());
    }

    @Test
    public void updateWithNullValuesForColumn() {
        testDistinctOccupancyDates();
        tenantCrudService().executeUpdateByNativeQuery("update Revenue_Stream_Detail set Mkt_Seg_Id = 1");
        Integer totalRec = revenueStreamsService.updateWithNullValuesForColumn("Mkt_Seg_Id", 4500);
        assertEquals(Integer.valueOf(2), totalRec);
        totalRec = revenueStreamsService.updateWithNullValuesForColumn("Mkt_Seg_Id", 4500);
        assertEquals(Integer.valueOf(0), totalRec);
    }

    @Test
    public void testUpdateNullAccomTypeIdsWithActualValues() {
        testDistinctOccupancyDates();
        List<Object[]> streams = tenantCrudService().findByNativeQuery("select * from Revenue_Stream");
        assertEquals(2, streams.size());
        if (Objects.nonNull(streams)) {
            Object[] objects = streams.get(0);
            int revenueStreamId = Integer.parseInt(objects[0].toString());
            int totalRec = revenueStreamsService.updateNullAccomTypeIdsWithActualValues(4500, revenueStreamId);
            assertEquals(2, totalRec);
            totalRec = revenueStreamsService.updateNullAccomTypeIdsWithActualValues(4500, revenueStreamId);
            assertEquals(0, totalRec);
        }
    }

    @Test
    public void testUpdateNullGroupIdsWithActualValues() {
        testDistinctOccupancyDates();
        List<Object[]> streams = tenantCrudService().findByNativeQuery("select * from Revenue_Stream");
        assertEquals(2, streams.size());
        if (Objects.nonNull(streams)) {
            Object[] objects = streams.get(0);
            int revenueStreamId = Integer.parseInt(objects[0].toString());
            int totalRec = revenueStreamsService.updateNullGroupIdsWithActualValues(4500, revenueStreamId);
            assertEquals(2, totalRec);
            totalRec = revenueStreamsService.updateNullGroupIdsWithActualValues(4500, revenueStreamId);
            assertEquals(0, totalRec);
        }
    }

    @Test
    public void shouldNotInvokeCacheIfItsPresentInList_updateAccomType() {
        String accomTypeCode = "ABC";
        RevenueStreamDetail detail = new RevenueStreamDetail();
        Map<String, Integer> accomTypes = new HashMap<>();
        detail.setAccomTypeCode(accomTypeCode);
        when(roomTypeCache.get(PROPERTY_ID, accomTypeCode)).thenReturn(null);
        revenueStreamsService.updateAccomType(PROPERTY_ID, detail, accomTypes);
        revenueStreamsService.updateAccomType(PROPERTY_ID, detail, accomTypes);
        verify(roomTypeCache).get(PROPERTY_ID, accomTypeCode);
    }

    @Test
    public void shouldNotHitCacheIfItsPresentInList_updateGroupId() {
        String groupCode = "ABC";
        RevenueStreamDetail detail = new RevenueStreamDetail();
        Map<String, Integer> groupIds = new HashMap<>();
        detail.setBlockCode(groupCode);
        revenueStreamsService.updateGroupId(detail, groupIds);
        assertNull(detail.getGroupId());
        groupIds.put(groupCode, 12);
        revenueStreamsService.updateGroupId(detail, groupIds);
        assertEquals(12, detail.getGroupId().intValue());
    }

    @Test
    public void shouldNotHitCacheIfBlockCodeIsNull_updateGroupId() {
        String groupCode = null;
        RevenueStreamDetail detail = new RevenueStreamDetail();
        Map<String, Integer> groupIds = new HashMap<>();
        detail.setBlockCode(groupCode);
        revenueStreamsService.updateGroupId(detail, groupIds);
        assertNull(detail.getGroupId());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldSaveRevenueSteamDetailDataAndShouldOverrideExistingRevenueValues(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 01, 01);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForDiffDates());
        final Map<String, Object> revenueStreamDetailsMap2 = buildRevenueStreamDetailsMap(DIVISION_CODE, "AAA", "40", new BigDecimal("111.00"), new BigDecimal("222.00"), stayDate.plusDays(1), 1, 1, "RACK", DLX, "CHHUCG");
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap2));
        final Set<String> dates = revenueStreamsService.fetchRevenueStreamIds();
        assertEquals(2, dates.size());
    }

    private List createRevenueStreamDetailsRecordsForDiffDates() {
        List revenueStreamDetailsList = new ArrayList<>();
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(1), roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(2), roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(3), roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(4), roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        return revenueStreamDetailsList;
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldSaveRevenuesteamDetailDataAndShouldOverrideAllExistingValues(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final LocalDate stayDate = LocalDate.of(2019, 12, 01);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForSameOccDate());
        final Map<String, Object> revenueStreamDetailsMap2 = buildRevenueStreamDetailsMap(DIVISION_CODE, "AAA", "40", new BigDecimal("111.00"), new BigDecimal("222.00"), stayDate, 1, 1, "RACK", DLX, "CHHUCG");
        revenueStreamsService.saveRevenueStreamsDetails(asList(revenueStreamDetailsMap2));
        final Set<String> dates = revenueStreamsService.fetchRevenueStreamIds();
        assertEquals(2, dates.size());
    }

    private List createRevenueStreamDetailsRecordsForSameOccDate() {
        List revenueStreamDetailsList = new ArrayList<>();
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, 1, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, 2, mktSegCode, accomTypeCode, groupCode));
        revenueStreamDetailsList.add(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, 3, mktSegCode, accomTypeCode, groupCode));
        return revenueStreamDetailsList;
    }

    @SneakyThrows
    @Test
    public void testLeastOccupancyDates() {
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForDiffDates());
        Timestamp lastUpdatedDTTM = new Timestamp(new SimpleDateFormat("dd/MM/yyyy").parse("13/08/2020").getTime());
        final Date occupancyDTTM = revenueStreamsService.fetchLeastOccupancyDTTM(lastUpdatedDTTM);
        assertEquals("2019-01-01 00:00:00.0", occupancyDTTM.toString());
    }

    @SneakyThrows
    @Test
    public void testFetchOccupancyDates() {
        final LocalDate stayDate = LocalDate.of(2019, 12, 01);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForDiffDates());
        Timestamp lastUpdatedDTTM = new Timestamp(new SimpleDateFormat("dd/MM/yyyy").parse("13/08/2020").getTime());
        final Date leastOccupancyDTTM = revenueStreamsService.fetchLeastOccupancyDTTM(lastUpdatedDTTM);
        final Set<String> dates = revenueStreamsService.fetchOccupancyDates(leastOccupancyDTTM, DateUtil.getCurrentTimestamp());
        assertEquals(5, dates.size());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldUpdateWithZeroValueRevenuesteamDetailData(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForSameOccDate());
        final List<Map<String, Object>> revenueSteamsDetail = Arrays.asList(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate, roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        final Integer count = revenueStreamsService.saveRevenueStreamsDetails(revenueSteamsDetail);
        assertEquals(2, count);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void shouldNotUpdateWithZeroValueRevenuesteamDetailData(boolean shouldEnableTableVariableBatching) {
        setTableBatchSystemProperty(shouldEnableTableVariableBatching);
        final List<Map<String, Object>> revenueStreamMapList = buildRevenueStreamAndRevenueStreamCostData();
        revenueStreamsService.saveRevenueStreams(revenueStreamMapList);
        revenueStreamsService.saveRevenueStreamsDetails(createRevenueStreamDetailsRecordsForSameOccDate());
        final List<Map<String, Object>> revenueSteamsDetail = Arrays.asList(buildRevenueStreamDetailsMap(DIVISION_CODE, rateCode, playerTier, estimateValue, actualValue, stayDate.plusDays(1), roomNightCount, los, mktSegCode, accomTypeCode, groupCode));
        final Integer count = revenueStreamsService.saveRevenueStreamsDetails(revenueSteamsDetail);
        assertEquals(0, count);
    }

    private void setTableBatchSystemProperty(boolean propertyValue) {
        System.setProperty("pacman.revenue.stream.details.tablebatch.enabled", propertyValue ? "true" : "false");
    }

    @Test
    void getRevenueStream(){
        var revenueStreams = tenantCrudService().findAll(RevenueStream.class);
        when(repository.findAll()).thenReturn(revenueStreams);
        List<RevenueStreamDto> result = revenueStreamsService.getRevenueStream();
        assertEquals(0, result.size());
    }
}
