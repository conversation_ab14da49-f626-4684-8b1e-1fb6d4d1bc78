package com.ideas.tetris.pacman.services.qualifiedrate.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.rule.ExpectedException;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.recommendation.common.RecommendationCommonConstants;
import com.ideas.recommendation.compression.GzipCompressionHelper;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileProductRestrictionAssociation;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.product.*;
import com.ideas.tetris.pacman.services.qualifiedrate.dto.*;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.UniqueRateQualifiedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixed;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified.createRateQualifiedByDateAndName;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.initMocks;

@MockitoSettings(strictness = Strictness.LENIENT)
public class TestQualifiedRateService extends AbstractG3JupiterTest {
    private static final Date DEFAULT_START_DATE = DateUtil.getDateWithoutTime(1, Calendar.MAY, 2024);
    private static final Date DEFAULT_END_DATE = DateUtil.getDateWithoutTime(8, Calendar.MAY, 2024);
    private static final Object[][] RATE_QUALIFIED = {
            {"RATE_CODE", "START_DATE", "END_DATE"},
            {"R1", "2016-01-05", "2016-04-24"},
            {"R2", "2016-09-06", "2016-09-30"},
            {"R3", "2016-05-01", "2016-12-25"},
            {"R4", "2016-08-10", "2017-03-10"}
    };
    private static final Object[][][] RATE_QUALIFIED_DETAILS_WITH_MATCHING_RATE_CODES = {
            {
                    {"RATE_CODE", "SEASON_START_DATE", "SEASON_END_DATE"},
                    {"R1", "2016-01-10", "2016-02-15"},
                    {"R1", "2016-02-20", "2016-04-05"},
                    {"R2", "2016-09-09", "2016-09-25"},
                    {"R4", "2016-09-16", "2017-02-01"}
            },
            {
                    {"NEXT_RATE_HEADER_START_DATE", "NEXT_RATE_HEADER_END_DATE", "CAUGHT_UP_DATE", "MATCHING_RATE_CODES"},
                    {"2016-01-03", "2016-01-04", "2016-01-02", ""},
                    {"2016-01-12", "2016-10-05", "2016-01-02", "R1,R2,R4"},
                    {"2016-03-30", "2016-08-08", "2016-03-25", "R1"},
                    {"2016-04-20", "2016-09-08", "2016-03-25", ""},
                    {"2016-04-20", "2016-09-17", "2016-04-20", "R2,R4"},
                    {"2016-09-10", "2016-09-17", "2016-09-26", "R4"}
            }
    };
    @RegisterExtension
    public ExpectedException expectedException = ExpectedException.none();
    @Mock
    private DateService dateService;
    @Mock
    private ExternalSystemHelper externalSystemHelper;
    @Mock
    private RestClient restClient;
    @InjectMocks
    private RateQualifiedService rateQualifiedService = new RateQualifiedService();
    @Mock
    private PacmanConfigParamsService configService;

    @BeforeEach
    public void setUp() {
        initMocks(this);
        rateQualifiedService.setCrudService(tenantCrudService());
        rateQualifiedService.setDateService(dateService);
        rateQualifiedService.setSeasonService(new SeasonService());
        inject(rateQualifiedService, "pacmanConfigParamsService", configService);
    }

    @Test
    public void shouldCreateFileMetaData() {
        FileMetadata fileMetadata = rateQualifiedService.createFileMetadata();
        assertNotNull(fileMetadata.getId());
        assertEquals(fileMetadata.getRecordTypeId(), Constants.RATE_PLANS_BY_USER);
        assertEquals(fileMetadata.getFileLocation(), Constants.FILE_LOCATION);
        assertEquals(fileMetadata.getPropertyId(), PacmanWorkContextHelper.getPropertyId());
        assertNotNull(fileMetadata.getFileName());
        assertEquals(fileMetadata.getProcessStatusId(), Constants.PROCESS_STATUS_SUCCESSFUL);
        assertTrue(fileMetadata.getSnapshotDt().compareTo(DateUtil.getCurrentDateWithoutTime()) == 0);
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldFetchAllActiveRateHeaders() {
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(DateUtil.getCurrentDateWithoutTime(),
                DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5), PacmanWorkContextHelper.getPropertyId());
        List<RateHeader> rateHeaders = rateQualifiedService.fetchAllActiveRateHeaders();
        assertNotNull(rateHeaders);
        assertFalse(rateHeaders.isEmpty());
        assertEquals(rateHeaders.get(0).getRatePlanName(), rateQualified.getName());
        assertEquals(rateHeaders.get(0).getRatePlanDescription(), rateQualified.getDescription());
        assertTrue(rateHeaders.get(0).getStartDate().toDate().compareTo(rateQualified.getStartDate()) == 0);
        assertTrue(rateHeaders.get(0).getEndDate().toDate().compareTo(rateQualified.getEndDate()) == 0);
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldSaveRateQualifiedSeasonDetail() {
        Date startDate = DateUtil.getCurrentDateWithoutTime();
        Date endDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(startDate, endDate,
                PacmanWorkContextHelper.getPropertyId());
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        SeasonDetail seasonDetail = new SeasonDetail();
        BigDecimal rateValue = applyDecimalFormatToBigDecimal(new BigDecimal(1.1));
        seasonDetail.setSunday(rateValue);
        seasonDetail.setMonday(rateValue);
        seasonDetail.setTuesday(rateValue);
        seasonDetail.setWednesday(rateValue);
        seasonDetail.setThursday(rateValue);
        seasonDetail.setFriday(rateValue);
        seasonDetail.setSaturday(rateValue);
        seasonDetail.setAccomTypeId(accomType.getId());

        seasonDetail = rateQualifiedService.saveSeasonDetail(seasonDetail, rateQualified.getId(),
                LocalDateUtils.fromDate(startDate), LocalDateUtils.fromDate(endDate));

        assertNotNull(seasonDetail.getId());

        RateQualifiedDetails rateQualifiedDetails = tenantCrudService().find(RateQualifiedDetails.class, seasonDetail.getId());
        assertTrue(rateQualifiedDetails.getMonday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getTuesday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getWednesday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getThursday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getFriday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getSaturday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getSunday().compareTo(rateValue) == 0);
        assertTrue(rateQualifiedDetails.getStartDate().compareTo(startDate) == 0);
        assertTrue(rateQualifiedDetails.getEndDate().compareTo(endDate) == 0);
    }

    @Test
    public void shouldFetchRateQualifiedIds() {
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(DateUtil.getCurrentDateWithoutTime(),
                DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5), PacmanWorkContextHelper.getPropertyId());
        List<Integer> allRateQualifiedIds = rateQualifiedService.getAllRateQualifiedIds();
        assertNotNull(allRateQualifiedIds);
        assertTrue(allRateQualifiedIds.contains(rateQualified.getId()));
    }

    @Test
    public void fetchAllTotalLevelRates() {
        insertIntoRateQualified();
        List<Integer> limitTotalrateIds = rateQualifiedService.fetchAllTotalLevelRates();
        assertEquals(0, limitTotalrateIds.size());
        insertIntoLimitTotalRateQualified();
        limitTotalrateIds = rateQualifiedService.fetchAllTotalLevelRates();
        assertEquals(1, limitTotalrateIds.size());
    }

    private void insertIntoLimitTotalRateQualified() {
        tenantCrudService().executeUpdateByNativeQuery("INSERT into [Limit_Total_Rate_Qualified] (Limit_Total_Rate_Qualified_ID) select Rate_qualified_id from Rate_Qualified");
    }

    private void insertIntoRateQualified() {
        tenantCrudService().executeUpdateByNativeQuery(" SET IDENTITY_INSERT Rate_Qualified ON " +
                " insert into Rate_Qualified (Rate_qualified_id,File_Metadata_ID,Property_ID,Rate_Code_Name,Rate_Code_Description,Remarks,Rate_Code_Currency,Start_Date_DT,End_Date_DT," +
                " Yieldable,Price_Relative,Reference_Rate_Code,Includes_Package,Last_Updated_DTTM,Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID," +
                " Rate_Qualified_Type_Id,Managed_In_G3)" +
                " values(1,3,5,'BLOCK','BLOCK',null,'USD',GETDATE(),GETDATE(),1,0,'None',1,GETDATE(),1,1,GETDATE(),1,3,0)" +
                " SET IDENTITY_INSERT Rate_Qualified OFF");
    }

    @Test
    public void shouldUpdateRateQualifiedSeasonDetail() {
        Date startDate = DateUtil.getCurrentDateWithoutTime();
        Date endDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(startDate, endDate,
                PacmanWorkContextHelper.getPropertyId());
        RateQualifiedDetails rateQualifiedDetails = UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate, endDate,
                PacmanWorkContextHelper.getPropertyId(), rateQualified.getId());

        SeasonDetail seasonDetail = new SeasonDetail();
        BigDecimal rateValue = applyDecimalFormatToBigDecimal(new BigDecimal(3.1));
        seasonDetail.setSunday(rateValue);
        seasonDetail.setMonday(rateValue);
        seasonDetail.setTuesday(rateValue);
        seasonDetail.setWednesday(rateValue);
        seasonDetail.setThursday(rateValue);
        seasonDetail.setFriday(rateValue);
        seasonDetail.setSaturday(rateValue);
        seasonDetail.setAccomTypeId(rateQualifiedDetails.getAccomTypeId());
        seasonDetail.setId(rateQualifiedDetails.getId());

        SeasonDetail updatedSeasonDetail = rateQualifiedService.saveSeasonDetail(seasonDetail, rateQualified.getId(),
                LocalDateUtils.fromDate(startDate), LocalDateUtils.fromDate(endDate));
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getSunday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getMonday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getTuesday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getWednesday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getThursday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getFriday()) == 0);
        assertTrue(rateValue.compareTo(updatedSeasonDetail.getSaturday()) == 0);
    }

    private BigDecimal applyDecimalFormatToBigDecimal(BigDecimal bd) {
        DecimalFormat df = new DecimalFormat(".###");
        String str = df.format(bd);
        return new BigDecimal(str);
    }

    @Test
    public void shouldSetValuesFromDtoToEntity() {
        Integer accomTypeId = 1;
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setAccomTypeId(accomTypeId);
        BigDecimal rateValue = new BigDecimal(1.1);
        seasonDetail.setMonday(rateValue);
        seasonDetail.setTuesday(rateValue);
        seasonDetail.setWednesday(rateValue);
        seasonDetail.setThursday(rateValue);
        seasonDetail.setFriday(rateValue);
        seasonDetail.setSaturday(rateValue);
        seasonDetail.setSunday(rateValue);

        RateQualifiedDetails rateQualifiedDetails = new RateQualifiedDetails();
        rateQualifiedService.setValuesFromDtoToEntity(seasonDetail, rateQualifiedDetails);
        assertEquals(accomTypeId, rateQualifiedDetails.getAccomTypeId());
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldFetchSeasonsForRateHeader() {
        Date currentDate = DateUtil.getCurrentDate();
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(currentDate,
                DateUtil.addDaysToDate(currentDate, 10), PacmanWorkContextHelper.getPropertyId());
        RateQualifiedDetails rateQualifiedDetails = UniqueRateQualifiedDetails.createRateQualifiedDetails(DateUtil.addDaysToDate(currentDate, 1),
                DateUtil.addDaysToDate(currentDate, 5), PacmanWorkContextHelper.getPropertyId(), rateQualified.getId());

        RateHeader rateHeader = new RateHeader();
        rateHeader.setId(rateQualified.getId());
        rateHeader.setRatePlanName(rateQualified.getName());
        rateHeader.setRatePlanDescription(rateQualified.getDescription());
        rateHeader.setStartDate(LocalDateUtils.fromDate(rateQualified.getStartDate()));
        rateHeader.setEndDate(LocalDateUtils.fromDate(rateQualified.getEndDate()));

        List<QualifiedRateSeason> seasons = rateQualifiedService.fetchSeasonsForRateHeader(rateHeader);
        assertNotNull(seasons);
        assertFalse(seasons.isEmpty());
        assertTrue(seasons.get(0).getStartDate().compareTo(LocalDateUtils.fromDate(rateQualifiedDetails.getStartDate())) == 0);
        assertTrue(seasons.get(0).getEndDate().compareTo(LocalDateUtils.fromDate(rateQualifiedDetails.getEndDate())) == 0);
        assertNotNull(seasons.get(0).getRoomClasses());
        assertFalse(seasons.get(0).getRoomClasses().isEmpty());
        SeasonDetail seasonDetail = seasons.get(0).getRoomClasses().get(0).getSeasonDetails().get(0);
        assertEquals(seasonDetail.getAccomTypeId(), rateQualifiedDetails.getAccomTypeId());
        assertTrue(seasonDetail.getMonday().compareTo(rateQualifiedDetails.getMonday()) == 0);
        assertTrue(seasonDetail.getTuesday().compareTo(rateQualifiedDetails.getTuesday()) == 0);
        assertTrue(seasonDetail.getWednesday().compareTo(rateQualifiedDetails.getWednesday()) == 0);
        assertTrue(seasonDetail.getThursday().compareTo(rateQualifiedDetails.getThursday()) == 0);
        assertTrue(seasonDetail.getFriday().compareTo(rateQualifiedDetails.getFriday()) == 0);
        assertTrue(seasonDetail.getSaturday().compareTo(rateQualifiedDetails.getSaturday()) == 0);
        assertTrue(seasonDetail.getSunday().compareTo(rateQualifiedDetails.getSunday()) == 0);
    }

    @Disabled
    @Test
    public void shouldFetchSeasonsForRateHeaderForMoreThanOneSeason() {
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date expectedEndDateSeason2 = DateUtil.addDaysToDate(currentDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(currentDate,
                expectedEndDateSeason2, propertyId);
//		AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, 0);
        AccomClass accomClass1 = tenantCrudService().find(AccomClass.class, 2);
        AccomType accomType1 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass1);
        AccomType accomType2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass1);
//		AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(propertyId, 0);
        AccomClass accomClass2 = tenantCrudService().find(AccomClass.class, 3);
        AccomType accomType3 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass2);
        AccomType accomType4 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(propertyId, accomClass2);
        Date expectedStartDateSeason1 = DateUtil.addDaysToDate(currentDate, 1);
        Date expectedEndDateSeason1 = DateUtil.addDaysToDate(currentDate, 10);
        RateQualifiedDetails rateQualifiedDetails1Season1 = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(expectedStartDateSeason1,
                expectedEndDateSeason1, propertyId, rateQualified.getId(), accomType1.getId(), 1.1);
        RateQualifiedDetails rateQualifiedDetails2Season1 = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(expectedStartDateSeason1,
                expectedEndDateSeason1, propertyId, rateQualified.getId(), accomType2.getId(), 1.1);

        Date expectedStartDateSeason2 = DateUtil.addDaysToDate(currentDate, 11);
        RateQualifiedDetails rateQualifiedDetails3Season2 = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(expectedStartDateSeason2,
                expectedEndDateSeason2, propertyId, rateQualified.getId(), accomType3.getId(), 1.1);
        RateQualifiedDetails rateQualifiedDetails4Season2 = UniqueRateQualifiedDetails.createRateQualifiedDetailsForAccomType(expectedStartDateSeason2,
                expectedEndDateSeason2, propertyId, rateQualified.getId(), accomType4.getId(), 1.1);

        RateHeader rateHeader = new RateHeader();
        rateHeader.setId(rateQualified.getId());
        rateHeader.setRatePlanName(rateQualified.getName());
        rateHeader.setRatePlanDescription(rateQualified.getDescription());
        rateHeader.setStartDate(LocalDateUtils.fromDate(rateQualified.getStartDate()));
        rateHeader.setEndDate(LocalDateUtils.fromDate(rateQualified.getEndDate()));

        List<QualifiedRateSeason> seasons = rateQualifiedService.fetchSeasonsForRateHeader(rateHeader);
        assertTrue(seasons.size() == 2);
        QualifiedRateSeason season1 = seasons.get(0);
        QualifiedRateSeason season2 = seasons.get(1);
        assertTrue(expectedStartDateSeason1.compareTo(season1.getStartDate().toDate()) == 0);
        assertTrue(expectedEndDateSeason1.compareTo(season1.getEndDate().toDate()) == 0);
        assertTrue(expectedStartDateSeason2.compareTo(season2.getStartDate().toDate()) == 0);
        assertTrue(expectedEndDateSeason2.compareTo(season2.getEndDate().toDate()) == 0);
        assertTrue(season1.getRoomClasses().size() == 3);
        assertTrue(season2.getRoomClasses().size() == 3);
        RoomClass roomClass1Season1 = season1.getRoomClasses().get(0);
        assertEquals(accomClass1.getName(), roomClass1Season1.getRoomClassName());
        assertEquals(accomClass1.getRankOrder(), roomClass1Season1.getRoomClassOrder());
        RoomClass roomClass2Season1 = season1.getRoomClasses().get(1);
        assertEquals(accomClass2.getName(), roomClass2Season1.getRoomClassName());
        assertEquals(accomClass2.getRankOrder(), roomClass2Season1.getRoomClassOrder());
        assertTrue(roomClass1Season1.getSeasonDetails().size() == 5);
        assertTrue(roomClass2Season1.getSeasonDetails().size() == 5);
        assertSeasonDetailWithRateQualifiedDetail(rateQualifiedDetails1Season1, roomClass1Season1.getSeasonDetails().get(0));
        assertSeasonDetailWithRateQualifiedDetail(rateQualifiedDetails2Season1, roomClass1Season1.getSeasonDetails().get(1));
        assertEquals(accomType3.getId(), roomClass2Season1.getSeasonDetails().get(0).getAccomTypeId());
        assertEquals(accomType3.getName(), roomClass2Season1.getSeasonDetails().get(0).getAccomTypeName());
        assertSeasonRatesNullable(roomClass2Season1.getSeasonDetails().get(0));
        assertEquals(accomType4.getId(), roomClass2Season1.getSeasonDetails().get(1).getAccomTypeId());
        assertEquals(accomType4.getName(), roomClass2Season1.getSeasonDetails().get(1).getAccomTypeName());
        assertSeasonRatesNullable(roomClass2Season1.getSeasonDetails().get(1));
        //assertion of season2 rate details
        RoomClass roomClass1Season2 = season2.getRoomClasses().get(0);
        RoomClass roomClass2Season2 = season2.getRoomClasses().get(1);
        assertEquals(accomClass1.getName(), roomClass1Season2.getRoomClassName());
        assertEquals(accomClass1.getRankOrder(), roomClass1Season2.getRoomClassOrder());
        assertEquals(accomClass2.getName(), roomClass2Season2.getRoomClassName());
        assertEquals(accomClass2.getRankOrder(), roomClass2Season2.getRoomClassOrder());
        assertTrue(roomClass1Season2.getSeasonDetails().size() == 2);
        assertTrue(roomClass2Season2.getSeasonDetails().size() == 2);
        assertSeasonDetailWithRateQualifiedDetail(rateQualifiedDetails3Season2, roomClass2Season2.getSeasonDetails().get(0));
        assertSeasonDetailWithRateQualifiedDetail(rateQualifiedDetails4Season2, roomClass2Season2.getSeasonDetails().get(1));
        assertEquals(accomType3.getId(), roomClass1Season2.getSeasonDetails().get(0).getAccomTypeId());
        assertEquals(accomType3.getName(), roomClass1Season2.getSeasonDetails().get(0).getAccomTypeName());
        assertSeasonRatesNullable(roomClass1Season2.getSeasonDetails().get(0));
        assertEquals(accomType4.getId(), roomClass1Season2.getSeasonDetails().get(1).getAccomTypeId());
        assertEquals(accomType4.getName(), roomClass1Season2.getSeasonDetails().get(1).getAccomTypeName());
        assertSeasonRatesNullable(roomClass1Season2.getSeasonDetails().get(1));
    }

    @Test
    public void shouldFetchSeasonForRateQualifiedWithinDateRange() {
        Date season1StartDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date season1EndDate = DateUtil.addDaysToDate(season1StartDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(season1StartDate, season1EndDate, propertyId);
        RateQualifiedDetails rateQualifiedDetails = UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate,
                propertyId, rateQualified.getId());
        Date season2StartDate = DateUtil.addDaysToDate(season1EndDate, 1);
        Date season2EndDate = DateUtil.addDaysToDate(season1EndDate, 20);
        RateQualifiedDetails rateQualifiedDetails2 = UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate,
                propertyId, rateQualified.getId());
        List<QualifiedRateSeason> seasons = rateQualifiedService.fetchSeasonForRateQualifiedWithinDateRange(rateQualified.getId(),
                LocalDateUtils.fromDate(season1StartDate), LocalDateUtils.fromDate(season1EndDate));
        assertTrue(seasons.size() == 1);
        assertTrue(seasons.get(0).getStartDate().toDate().compareTo(season1StartDate) == 0);
        assertTrue(seasons.get(0).getEndDate().toDate().compareTo(season1EndDate) == 0);
    }

    private void assertSeasonRatesNullable(SeasonDetail seasonDetail) {
        assertNull(seasonDetail.getMonday());
        assertNull(seasonDetail.getTuesday());
        assertNull(seasonDetail.getWednesday());
        assertNull(seasonDetail.getThursday());
        assertNull(seasonDetail.getFriday());
        assertNull(seasonDetail.getSaturday());
    }

    private void assertSeasonDetailWithRateQualifiedDetail(
            RateQualifiedDetails rateQualifiedDetails,
            SeasonDetail seasonDetail) {
        assertEquals(rateQualifiedDetails.getAccomTypeId(), seasonDetail.getAccomTypeId());
        assertEquals(rateQualifiedDetails.getId(), seasonDetail.getId());
        assertTrue(rateQualifiedDetails.getMonday().compareTo(seasonDetail.getMonday()) == 0);
        assertTrue(rateQualifiedDetails.getTuesday().compareTo(seasonDetail.getTuesday()) == 0);
        assertTrue(rateQualifiedDetails.getWednesday().compareTo(seasonDetail.getWednesday()) == 0);
        assertTrue(rateQualifiedDetails.getThursday().compareTo(seasonDetail.getThursday()) == 0);
        assertTrue(rateQualifiedDetails.getFriday().compareTo(seasonDetail.getFriday()) == 0);
        assertTrue(rateQualifiedDetails.getSaturday().compareTo(seasonDetail.getSaturday()) == 0);
    }

    @Test
    public void shouldReturnAccomTypeActive() {
        Object[] row = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1};
        boolean isActive = rateQualifiedService.isAccomTypeActive(row);
        assertTrue(isActive);

        row[20] = 0;
        isActive = rateQualifiedService.isAccomTypeActive(row);
        assertFalse(isActive);
    }

    @Test
    public void shouldExtractSeasonDetailFromDbValues() {
        Integer rateQualifiedId = 1;
        Integer accomTypeId = 9;
        Integer rateQualifiedDetailsId = 1;
        String accomTypeName = "TestAccomType379118975";
        String startDateStr = "2014-04-08";
        String endDateStr = "2014-04-18";
        DecimalFormat df = new DecimalFormat(".###");
        String str = df.format(new BigDecimal(1.1));
        BigDecimal rateValue = new BigDecimal(str);
        String roomClassName = "XYZ-702777742";
        Integer roomClassOrder = 1;
        Integer accomTypeDisplayStatusId = 1;
        Object[] row = {accomTypeId, accomTypeName, startDateStr, endDateStr, rateQualifiedDetailsId, rateQualifiedId, accomTypeId, startDateStr, endDateStr,
                rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, 1, "2014-04-08 12:07:31.663", 1, "2014-04-08 12:07:31.663",
                1, roomClassName, roomClassOrder, accomTypeDisplayStatusId};

        SeasonDetail seasonDetail = rateQualifiedService.extractSeasonDetailFromDbValues(row, true, rateQualifiedId, true);
        assertEquals(accomTypeId, seasonDetail.getAccomTypeId());
        assertEquals(accomTypeName, seasonDetail.getAccomTypeName());
        assertEquals(rateQualifiedDetailsId, seasonDetail.getId());
        assertTrue(seasonDetail.isAccomTypeDisplayActive());
        assertTrue(rateValue.compareTo(seasonDetail.getMonday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getTuesday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getWednesday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getThursday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getFriday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getSaturday()) == 0);
        assertTrue(rateValue.compareTo(seasonDetail.getSunday()) == 0);
    }

    @Test
    public void shouldSkipDowWhenRatesNotDefinedForRateQualified() {
        Integer rateQualifiedId = null;
        Integer accomTypeId = 9;
        String accomTypeName = "TestAccomType379118975";
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        Object[] row = generateSeasonDetailRow(rateQualifiedId, accomTypeId, accomTypeName, roomClassName, roomClassOrder);

        SeasonDetail seasonDetail = rateQualifiedService.extractSeasonDetailFromDbValues(row, true, rateQualifiedId, true);

        assertEquals(accomTypeId, seasonDetail.getAccomTypeId());
        assertEquals(accomTypeName, seasonDetail.getAccomTypeName());
        assertNull(seasonDetail.getId());
        assertNull(seasonDetail.getMonday());
        assertNull(seasonDetail.getTuesday());
        assertNull(seasonDetail.getWednesday());
        assertNull(seasonDetail.getThursday());
        assertNull(seasonDetail.getFriday());
        assertNull(seasonDetail.getSaturday());
        assertNull(seasonDetail.getSunday());
    }

    @Test
    public void shouldExtractSeasonFromDbValues() throws ParseException {
        List<QualifiedRateSeason> seasons = new ArrayList<QualifiedRateSeason>();
        Map<String, QualifiedRateSeason> seasonStartDateEndDateMap = new HashMap<String, QualifiedRateSeason>();

        Integer rateQualifiedId = null;
        BigDecimal rateValue = null;
        Integer accomTypeId = 9;
        Integer rateQualifiedDetailsId = null;
        String accomTypeName = "TestAccomType379118975";
        String roomClassName = "XYZ-702777742";
        Integer roomClassOrder = 1;
        Date startDate = DateUtil.getDate(8, 4, 2014);
        Date endDate = DateUtil.getDate(18, 4, 2014);
        Object[] row = {accomTypeId, accomTypeName, startDate, endDate, rateQualifiedDetailsId, rateQualifiedId, accomTypeId, startDate, endDate,
                rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, "2014-04-08 12:07:31.663", 1, roomClassName, roomClassOrder};

        QualifiedRateSeason season = rateQualifiedService.extractSeasonFromDbValues(seasons, seasonStartDateEndDateMap, row);
        assertTrue(season.getStartDate().compareTo(LocalDateUtils.fromDate(startDate)) == 0);
        assertTrue(season.getEndDate().compareTo(LocalDateUtils.fromDate(endDate)) == 0);
    }

    @Test
    public void shouldDeleteRateHeaderToDb() {
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDaysToDate(startDate, 30);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(startDate, endDate, PacmanWorkContextHelper.getPropertyId());
        RateHeader rateHeader = new RateHeader();
        rateHeader.setRatePlanName("LV0");
        rateHeader.setRatePlanDescription("LV0");
        rateHeader.setStartDate(LocalDateUtils.fromDate(startDate));
        rateHeader.setEndDate(LocalDateUtils.fromDate(endDate));
        rateHeader.setId(rateQualified.getId());
        rateHeader.setDeleted(true);
        rateQualifiedService.deleteRateHeader(rateHeader);
        RateQualified rateQualifiedAfterDelete = tenantCrudService().find(RateQualified.class, rateHeader.getId());
        assertEquals(Constants.INACTIVE_STATUS_ID, rateQualifiedAfterDelete.getStatusId());
    }

    @Test
    public void shouldBuildRateQualifiedEntity() {
        RateHeader rateHeader = new RateHeader();
        RateQualified rateQualified = rateQualifiedService.getRateQualifiedEntity(rateHeader);
        assertNotNull(rateQualified);
    }

    @Test
    public void shouldBuildRateQualifiedEntityFromDb() {
        RateHeader rateHeader = new RateHeader();
        rateHeader.setId(1);
        CrudService mockCrudService = mock(CrudService.class);
        RateQualified rateQualified = new RateQualified();
        rateQualified.setId(1);
        when(mockCrudService.find(RateQualified.class, rateHeader.getId())).thenReturn(rateQualified);
        rateQualifiedService.setCrudService(mockCrudService);
        RateQualified returnedRateQualified = rateQualifiedService.getRateQualifiedEntity(rateHeader);
        Mockito.verify(mockCrudService).find(RateQualified.class, rateHeader.getId());
        assertEquals(returnedRateQualified.getId(), rateHeader.getId());
    }

    @Test
    public void shouldGetSeasonLatestEndDateEarlierStartDateForRateHeader() {
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date rateHeaderEndDate = DateUtil.addDaysToDate(currentDate, 30);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(currentDate,
                rateHeaderEndDate, propertyId);
        Date season1StartDate = DateUtil.addDaysToDate(currentDate, 5);
        Date season1EndDate = DateUtil.addDaysToDate(currentDate, 10);
        Date season2StartDate = DateUtil.addDaysToDate(currentDate, 15);
        Date season2EndDate = DateUtil.addDaysToDate(currentDate, 20);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate, propertyId, rateQualified.getId());
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate, propertyId, rateQualified.getId());
        RateHeader rateHeader = new RateHeader();
        rateHeader.setId(rateQualified.getId());
        tenantCrudService().flush();
        Object[] dates = rateQualifiedService.getSeasonLatestEndDateEarlierStartDateForRateHeader(rateHeader);
        assertTrue(season2EndDate.compareTo((Date) dates[0]) == 0);
        assertTrue(season1StartDate.compareTo((Date) dates[1]) == 0);
    }

    @Test
    public void seasonEndDateCanNotBeBeforeBusinessDate() {
        expectedException.expect(TetrisException.class);
        expectedException.expectMessage("Season end date can not be before system date");
        QualifiedRateSeason season = new QualifiedRateSeason();
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Date businessDate = DateUtil.addDaysToDate(currentDate, 1);
        season.setEndDate(LocalDateUtils.fromDate(currentDate));
        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getBusinessDate()).thenReturn(businessDate);
        rateQualifiedService.setDateService(mockDateService);
        rateQualifiedService.validateSeasonEndDate(season);
        verify(mockDateService).getBusinessDate();
    }

    @Test
    public void shouldReturnTrueIfPastSeason() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Date businessDate = DateUtil.addDaysToDate(currentDate, 1);
        season.setEndDate(LocalDateUtils.fromDate(currentDate));
        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getBusinessDate()).thenReturn(businessDate);
        rateQualifiedService.setDateService(mockDateService);
        boolean isPastSeason = rateQualifiedService.isPastSeason(season);
        verify(mockDateService).getBusinessDate();
        assertTrue(isPastSeason);
    }

    @Test
    public void shouldReturnFalseIfNotAPastSeason() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        Date endDate = DateUtil.getCurrentDateWithoutTime();
        Date businessDate = DateUtil.addDaysToDate(endDate, -1);
        season.setEndDate(LocalDateUtils.fromDate(endDate));
        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getBusinessDate()).thenReturn(businessDate);
        rateQualifiedService.setDateService(mockDateService);
        boolean isPastSeason = rateQualifiedService.isPastSeason(season);
        verify(mockDateService).getBusinessDate();
        assertFalse(isPastSeason);
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldReturnFalseIfSeasonEndDateEqualsSystemDate() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        Date endDate = DateUtil.getCurrentDateWithoutTime();
        season.setEndDate(LocalDateUtils.fromDate(endDate));
        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getBusinessDate()).thenReturn(endDate);
        rateQualifiedService.setDateService(mockDateService);
        boolean isPastSeason = rateQualifiedService.isPastSeason(season);
        verify(mockDateService).getBusinessDate();
        assertFalse(isPastSeason);
    }

    @Test
    public void shouldCreateAndExtractRoomClassFromDbValuesIfNotExistInMap() {
        Object[] row = generateSeasonDetailRow(null, 9, null, "STD", 1);
        SeasonDetail localSeasonDetail = new SeasonDetail();
        localSeasonDetail.setId(1);
        Map<String, RoomClass> roomClassDetailsMap = new HashMap<String, RoomClass>();
        QualifiedRateSeason season = new QualifiedRateSeason();
        rateQualifiedService.extractRoomClassFromDbValues(row, localSeasonDetail, roomClassDetailsMap, season);
        String expectedRoomClassName = "STD";
        Integer expectedRoomClassOrder = 1;
        RoomClass roomClass = roomClassDetailsMap.get(expectedRoomClassName);
        assertEquals(expectedRoomClassName, roomClass.getRoomClassName());
        assertEquals(expectedRoomClassOrder, roomClass.getRoomClassOrder());
        assertEquals(localSeasonDetail.getId(), season.getRoomClasses().get(0).getSeasonDetails().get(0).getId());
    }

    @Test
    public void shouldExtractRoomClassFromDbValues() {
        Object[] row = generateSeasonDetailRow(null, 9, null, "STD", 1);
        SeasonDetail seasonDetail1 = new SeasonDetail();
        seasonDetail1.setId(1);
        RoomClass roomClass = new RoomClass();
        roomClass.setRoomClassName("STD");
        roomClass.setRoomClassOrder(1);
        roomClass.addSeasonDetails(seasonDetail1);
        Map<String, RoomClass> roomClassDetailsMap = new HashMap<String, RoomClass>();
        roomClassDetailsMap.put("STD", roomClass);
        QualifiedRateSeason season = null;
        SeasonDetail seasonDetail2 = new SeasonDetail();
        seasonDetail2.setId(2);
        rateQualifiedService.extractRoomClassFromDbValues(row, seasonDetail2, roomClassDetailsMap, season);
        String expectedRoomClassName = "STD";
        Integer expectedRoomClassOrder = 1;
        RoomClass roomClassFromMap = roomClassDetailsMap.get(expectedRoomClassName);
        assertEquals(expectedRoomClassName, roomClassFromMap.getRoomClassName());
        assertEquals(expectedRoomClassOrder, roomClassFromMap.getRoomClassOrder());
        assertNull(season);
        assertEquals(seasonDetail1.getId(), roomClassFromMap.getSeasonDetails().get(0).getId());
        assertEquals(seasonDetail2.getId(), roomClassFromMap.getSeasonDetails().get(1).getId());
    }

    private Object[] generateSeasonDetailRow(Integer rateQualifiedId, Integer accomTypeId, String accomTypeName,
                                             String roomClassName, Integer roomClassOrder) {
        BigDecimal rateValue = null;
        Integer rateQualifiedDetailsId = null;
        String startDateStr = "2014-04-08";
        String endDateStr = "2014-04-18";
        Object[] row = {accomTypeId, accomTypeName, startDateStr, endDateStr, rateQualifiedDetailsId, rateQualifiedId, accomTypeId, startDateStr, endDateStr,
                rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, rateValue, 1, "2014-04-08 12:07:31.663", 1, "2014-04-08 12:07:31.663",
                1, roomClassName, roomClassOrder, 1};
        return row;
    }

    @Test
    public void shouldBuildRateQualifiedDetailsEntityFromDb() {
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setId(1);
        CrudService mockCrudService = mock(CrudService.class);
        rateQualifiedService.setCrudService(mockCrudService);
        when(mockCrudService.find(RateQualifiedDetails.class, seasonDetail.getId())).thenReturn(new RateQualifiedDetails());
        rateQualifiedService.buildRateQualifiedDetailsEntity(seasonDetail);
        verify(mockCrudService).find(RateQualifiedDetails.class, seasonDetail.getId());
    }

    @Test
    public void shouldBuildRateQualifiedDetailsEntity() {
        SeasonDetail seasonDetail = new SeasonDetail();
        RateQualifiedDetails rateQualifiedDetails = rateQualifiedService.buildRateQualifiedDetailsEntity(seasonDetail);
        assertNotNull(rateQualifiedDetails);
    }

    @Test
    public void shouldBuildNewRoomClass() {
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        RoomClass roomClass = rateQualifiedService.buildNewRoomClass(roomClassName, roomClassOrder);
        assertEquals(roomClassName, roomClass.getRoomClassName());
        assertEquals(roomClassOrder, roomClass.getRoomClassOrder());
        assertNull(roomClass.getSeasonDetails());
    }

    @Test
    public void shouldAddRoomClassToSeason() {
        Map<QualifiedRateSeason, Map<String, RoomClass>> seasonRoomClassMap = new HashMap<QualifiedRateSeason, Map<String, RoomClass>>();
        SeasonDetail expectedSeasonDetail = generateSeasonDetailDto("STD1", new BigDecimal(1.1));
        QualifiedRateSeason season = new QualifiedRateSeason();
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        RoomClass expectedRoomClass = new RoomClass();
        expectedRoomClass.setRoomClassName(roomClassName);
        expectedRoomClass.setRoomClassOrder(roomClassOrder);
        expectedRoomClass.addSeasonDetails(expectedSeasonDetail);
        rateQualifiedService.addRoomClassToSeason(seasonRoomClassMap, expectedSeasonDetail, season, roomClassName, roomClassOrder);
        assertTrue(seasonRoomClassMap.size() == 1);
        assertEquals(expectedRoomClass, seasonRoomClassMap.get(season).get("STD"));
        assertEquals(season.getRoomClasses().get(0).getRoomClassName(), roomClassName);
        assertEquals(season.getRoomClasses().get(0).getRoomClassOrder(), roomClassOrder);
        SeasonDetail actualSeasonDetail = season.getRoomClasses().get(0).getSeasonDetails().get(0);
        assertEquals(expectedSeasonDetail, actualSeasonDetail);
    }

    private SeasonDetail generateSeasonDetailDto(String accomTypeName, BigDecimal rateValue) {
        SeasonDetail localSeasonDetail = buildSeasonDetailWith(1, rateValue);
        localSeasonDetail.setAccomTypeId(2);
        localSeasonDetail.setAccomTypeActive(true);
        localSeasonDetail.setAccomTypeName("STD1");
        return localSeasonDetail;
    }

    @Test
    public void shouldUpdateRoomClassToSeason() {
        Map<QualifiedRateSeason, Map<String, RoomClass>> seasonRoomClassMap = new HashMap<QualifiedRateSeason, Map<String, RoomClass>>();
        SeasonDetail seasonDetail = generateSeasonDetailDto("STD1", new BigDecimal(1.1));
        QualifiedRateSeason season = new QualifiedRateSeason();
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        RoomClass expectedRoomClass = new RoomClass();
        expectedRoomClass.setRoomClassName(roomClassName);
        expectedRoomClass.setRoomClassOrder(roomClassOrder);
        season.addRoomClasses(expectedRoomClass);
        Map<String, RoomClass> roomClassMap = new HashMap<String, RoomClass>();
        rateQualifiedService.updateRoomClassToSeason(seasonRoomClassMap, seasonDetail, season, roomClassName, roomClassOrder, roomClassMap);
        assertEquals(roomClassName, roomClassMap.get(roomClassName).getRoomClassName());
        assertEquals(roomClassOrder, roomClassMap.get(roomClassName).getRoomClassOrder());
        assertEquals(roomClassName, seasonRoomClassMap.get(season).get(roomClassName).getRoomClassName());
        assertEquals(roomClassOrder, seasonRoomClassMap.get(season).get(roomClassName).getRoomClassOrder());
    }

    @Test
    public void shouldFindRoomClassInMap() {
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        RoomClass expectedRoomClass = new RoomClass();
        expectedRoomClass.setRoomClassName(roomClassName);
        expectedRoomClass.setRoomClassOrder(roomClassOrder);
        Map<String, RoomClass> roomClassMap = new HashMap<String, RoomClass>();
        roomClassMap.put(roomClassName, expectedRoomClass);
        RoomClass actualRoomClass = rateQualifiedService.findOrBuildRoomClass(roomClassName, roomClassOrder, roomClassMap);
        assertEquals(expectedRoomClass.getRoomClassName(), actualRoomClass.getRoomClassName());
        assertEquals(expectedRoomClass.getRoomClassOrder(), actualRoomClass.getRoomClassOrder());
    }

    @Test
    public void shouldBuildRoomClassIfNotFoundInMap() {
        String roomClassName = "STD";
        Integer roomClassOrder = 1;
        Map<String, RoomClass> roomClassMap = new HashMap<String, RoomClass>();
        RoomClass roomClass = rateQualifiedService.findOrBuildRoomClass(roomClassName, roomClassOrder, roomClassMap);
        RoomClass expectedRoomClass = new RoomClass(1, roomClassName, roomClassOrder);
        assertEquals(expectedRoomClass, roomClass);
    }

    @Test
    public void shouldAddRoomClassToSeasonIfNotExist() {
        SeasonDetail seasonDetail = generateSeasonDetailDto("STD1", new BigDecimal(1.1));
        QualifiedRateSeason season = new QualifiedRateSeason();
        RoomClass roomClass1 = new RoomClass();
        roomClass1.setRoomClassName("STD");
        roomClass1.setRoomClassOrder(1);
        season.addRoomClasses(roomClass1);
        RoomClass roomClass2 = new RoomClass();
        roomClass2.setRoomClassName("DLX");
        roomClass2.setRoomClassOrder(2);
        assertTrue(season.getRoomClasses().size() == 1);
        rateQualifiedService.addOrReplaceRoomClassToSeason(seasonDetail, season, roomClass2);
        assertTrue(season.getRoomClasses().size() == 2);
        assertEquals(roomClass2.getRoomClassName(), season.getRoomClasses().get(1).getRoomClassName());
        assertEquals(roomClass2.getRoomClassOrder(), season.getRoomClasses().get(1).getRoomClassOrder());
        SeasonDetail actualSeasonDetail = season.getRoomClasses().get(1).getSeasonDetails().get(0);
        assertSeasonDetailObject(seasonDetail, actualSeasonDetail);
    }

    @Test
    public void shouldReplaceRoomClassToSeasonIfExist() {
        SeasonDetail seasonDetail1 = generateSeasonDetailDto("STD1", new BigDecimal(1.1));
        SeasonDetail seasonDetail2 = generateSeasonDetailDto("STD2", new BigDecimal(1.2));
        QualifiedRateSeason season = new QualifiedRateSeason();
        RoomClass roomClass1 = new RoomClass();
        roomClass1.setRoomClassName("STD");
        roomClass1.setRoomClassOrder(1);
        roomClass1.addSeasonDetails(seasonDetail1);
        season.addRoomClasses(roomClass1);
        assertTrue(season.getRoomClasses().get(0).getSeasonDetails().size() == 1);
        rateQualifiedService.addOrReplaceRoomClassToSeason(seasonDetail2, season, roomClass1);
        assertTrue(season.getRoomClasses().size() == 1);
        assertEquals(roomClass1.getRoomClassName(), season.getRoomClasses().get(0).getRoomClassName());
        assertEquals(roomClass1.getRoomClassOrder(), season.getRoomClasses().get(0).getRoomClassOrder());
        assertTrue(season.getRoomClasses().get(0).getSeasonDetails().size() == 2);
        SeasonDetail expectedSeasonDetail1 = season.getRoomClasses().get(0).getSeasonDetails().get(0);
        SeasonDetail expectedSeasonDetail2 = season.getRoomClasses().get(0).getSeasonDetails().get(1);
        assertSeasonDetailObject(expectedSeasonDetail1, seasonDetail1);
        assertSeasonDetailObject(expectedSeasonDetail2, seasonDetail2);
    }

    private void assertSeasonDetailObject(SeasonDetail expectedSeasonDetail, SeasonDetail actualSeasonDetail) {
        assertEquals(expectedSeasonDetail.getAccomTypeId(), actualSeasonDetail.getAccomTypeId());
        assertTrue(expectedSeasonDetail.getFriday().compareTo(actualSeasonDetail.getFriday()) == 0);
        assertTrue(expectedSeasonDetail.getMonday().compareTo(actualSeasonDetail.getMonday()) == 0);
        assertTrue(expectedSeasonDetail.getTuesday().compareTo(actualSeasonDetail.getTuesday()) == 0);
        assertTrue(expectedSeasonDetail.getWednesday().compareTo(actualSeasonDetail.getWednesday()) == 0);
        assertTrue(expectedSeasonDetail.getThursday().compareTo(actualSeasonDetail.getThursday()) == 0);
        assertTrue(expectedSeasonDetail.getSaturday().compareTo(actualSeasonDetail.getSaturday()) == 0);
    }

    @Test
    public void shouldFetchSeasonWithNullRoomClassAndRoomTypes() {
        CrudService mockCrudService = mock(CrudService.class);
        rateQualifiedService.setCrudService(mockCrudService);
        List<RoomClass> roomClasses = new ArrayList<RoomClass>();
        RoomClass roomClass = new RoomClass();
        roomClass.setId(1);
        roomClass.setRoomClassName("STD");
        roomClass.setRoomClassOrder(1);
        roomClasses.add(roomClass);
        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setAccomTypeId(11);
        seasonDetail.setAccomTypeName("STD1");
        seasonDetails.add(seasonDetail);
        when(mockCrudService.<RoomClass>findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_TO_ROOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(roomClasses);
        when(mockCrudService.<SeasonDetail>findByNamedQuery(AccomType.BY_ACCOM_CLASS_PROPERTY_ID_TO_SEASON_DETAIL,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", roomClass.getId()).parameters())).thenReturn(seasonDetails);
        QualifiedRateSeason season = rateQualifiedService.fetchEmptySeason();
        verify(mockCrudService).findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_TO_ROOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(mockCrudService).findByNamedQuery(AccomType.BY_ACCOM_CLASS_PROPERTY_ID_TO_SEASON_DETAIL,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", roomClass.getId()).parameters());
        assertNull(season.getStartDate());
        assertNull(season.getEndDate());
        assertEquals("STD", season.getRoomClasses().get(0).getRoomClassName());
        assertTrue(1 == season.getRoomClasses().get(0).getRoomClassOrder());
        assertTrue(11 == season.getRoomClasses().get(0).getSeasonDetails().get(0).getAccomTypeId());
        assertEquals("STD1", season.getRoomClasses().get(0).getSeasonDetails().get(0).getAccomTypeName());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getFriday());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getTuesday());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getThursday());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getSaturday());
        assertNull(season.getRoomClasses().get(0).getSeasonDetails().get(0).getSunday());
    }

    @Test
    public void shouldSplitDeletedSeasonWhenSystemDateIsBetweenStartDateAndEndDate() {
        QualifiedRateSeason season1 = new QualifiedRateSeason();
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5);
        season1.setStartDate(startDate);
        season1.setEndDate(endDate);
        RoomClass roomClass = new RoomClass();
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setMonday(new BigDecimal(1.1));
        seasonDetail.setId(1);
        roomClass.addSeasonDetails(seasonDetail);
        season1.addRoomClasses(roomClass);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForDeleteSeason(season1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(startDate) == 0);
        LocalDate expectedEndDateOfPastSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(expectedCaughtUpDate, -1));
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(expectedEndDateOfPastSeason) == 0);
        assertFalse(splittedSeasons.get(0).isDeleted());
        LocalDate expectedStartDateOfDeletedSeason = LocalDateUtils.fromDate(expectedCaughtUpDate);
        assertTrue(splittedSeasons.get(1).getStartDate().compareTo(expectedStartDateOfDeletedSeason) == 0);
        assertTrue(splittedSeasons.get(1).getEndDate().compareTo(endDate) == 0);
        assertTrue(splittedSeasons.get(1).isDeleted());
        assertNotNull(splittedSeasons.get(0).getRoomClasses().get(0).getSeasonDetails().get(0).getId());
        assertNull(splittedSeasons.get(1).getRoomClasses().get(0).getSeasonDetails().get(0).getId());
    }

    @Test
    public void shouldReturnSameSeasonWhileSplitWhenDeletedSeasonIsInFuture() {
        QualifiedRateSeason season1 = new QualifiedRateSeason();
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addMonthsToDate(DateUtil.getCurrentDateWithoutTime(), 3));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        season1.setStartDate(startDate);
        season1.setEndDate(endDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForDeleteSeason(season1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 1);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(startDate) == 0);
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(endDate) == 0);
        assertTrue(splittedSeasons.get(0).isDeleted());
    }

    @Test
    public void shouldReturnNothingWhileSplitWhenDeletedSeasonIsInPast() {
        QualifiedRateSeason season1 = new QualifiedRateSeason();
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 11);
        season1.setStartDate(startDate);
        season1.setEndDate(endDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForDeleteSeason(season1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.isEmpty());
    }

    @Test
    public void shouldReturnSameSeasonWhileSplitWhenDeletedSeasonHasSameStartDateAsSystemDate() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addMonthsToDate(DateUtil.getCurrentDateWithoutTime(), 3));
        Date expectedCaughtUpDate = DateUtil.getCurrentDateWithoutTime();
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForDeleteSeason(season);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 1);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(startDate) == 0);
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(endDate) == 0);
        assertTrue(splittedSeasons.get(0).isDeleted());
    }

    @Test
    public void shouldSplitDeletedSeasonWhenDeletedSeasonHasSameEndDateAsSystemDate() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        RoomClass roomClass = new RoomClass();
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setMonday(new BigDecimal(1.1));
        seasonDetail.setId(1);
        roomClass.addSeasonDetails(seasonDetail);
        season.addRoomClasses(roomClass);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForDeleteSeason(season);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(startDate) == 0);
        LocalDate expectedEndDateOfPastSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(expectedCaughtUpDate, -1));
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(expectedEndDateOfPastSeason) == 0);
        assertFalse(splittedSeasons.get(0).isDeleted());
        LocalDate expectedStartDateOfDeletedSeason = LocalDateUtils.fromDate(expectedCaughtUpDate);
        assertTrue(splittedSeasons.get(1).getStartDate().compareTo(expectedStartDateOfDeletedSeason) == 0);
        assertTrue(splittedSeasons.get(1).getEndDate().compareTo(endDate) == 0);
        assertTrue(splittedSeasons.get(1).isDeleted());
        assertNotNull(splittedSeasons.get(0).getRoomClasses().get(0).getSeasonDetails().get(0).getId());
        assertNull(splittedSeasons.get(1).getRoomClasses().get(0).getSeasonDetails().get(0).getId());
    }

    @Test
    public void shouldReturnNothingWhileSplitSeasonWhenUpdatedSeasonIsInPast() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        LocalDate seasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate seasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 11);
        season.setStartDate(seasonStartDate);
        season.setEndDate(seasonEndDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        List<QualifiedRateSeason> seasonsFromDb = new ArrayList<QualifiedRateSeason>();
        seasonsFromDb.add(season);
        rateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForUpdateSeason(season, 1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.isEmpty());
    }

    @Test
    public void shouldReturnSameSeasonWhileSplitWhenUpdatedSeasonIsInFuture() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        LocalDate seasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate seasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        season.setStartDate(seasonStartDate);
        season.setEndDate(seasonEndDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        List<QualifiedRateSeason> seasonsFromDb = new ArrayList<QualifiedRateSeason>();
        seasonsFromDb.add(season);
        RateQualifiedService mockRateQualifiedService = spy(new RateQualifiedService());
        doReturn(seasonsFromDb).when(mockRateQualifiedService).fetchSeasonForRateQualifiedWithinDateRange(1, seasonStartDate, seasonEndDate);

        mockRateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mock(DateService.class);
        mockRateQualifiedService.setDateService(mockDateService);
        when(mockDateService.getCaughtUpDate()).thenReturn(expectedCaughtUpDate);

        List<QualifiedRateSeason> splittedSeasons = mockRateQualifiedService.applySplitForUpdateSeason(season, 1);

        verify(mockDateService, times(1)).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 1);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(seasonStartDate) == 0);
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(seasonEndDate) == 0);
    }

    @Test
    public void shouldSplitUpdatedSeasonWhenSystemDateIsBetweenStartDateAndEndDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 30));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        QualifiedRateSeason expectedOldSeason = generateSeason(startDate, endDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonForUpdate = generateSeason(startDate, endDate, new BigDecimal(2.1), true);
        RateQualifiedService mockRateQualifiedService = spy(new RateQualifiedService());
        List<QualifiedRateSeason> expectedSeasons = new ArrayList<QualifiedRateSeason>();
        expectedSeasons.add(expectedOldSeason);
        doReturn(expectedSeasons).when(mockRateQualifiedService).fetchSeasonForRateQualifiedWithinDateRange(1, startDate, endDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        mockRateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mock(DateService.class);
        mockRateQualifiedService.setDateService(mockDateService);
        when(mockDateService.getCaughtUpDate()).thenReturn(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = mockRateQualifiedService.applySplitForUpdateSeason(seasonForUpdate, 1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        assertOldSeason(startDate, expectedCaughtUpDate, splittedSeasons.get(0));
        assertNewSeason(endDate, expectedCaughtUpDate, splittedSeasons.get(1));
    }

    @Test
    public void shouldReturnSameSeasonWhileSplitWhenUpdatedSeasonHasSameStartDateAsSystemDate() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        LocalDate seasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate seasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.getCurrentDateWithoutTime();
        season.setStartDate(seasonStartDate);
        season.setEndDate(seasonEndDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        List<QualifiedRateSeason> seasonsFromDb = new ArrayList<QualifiedRateSeason>();
        seasonsFromDb.add(season);
        RateQualifiedService mockRateQualifiedService = spy(new RateQualifiedService());
        doReturn(seasonsFromDb).when(mockRateQualifiedService).fetchSeasonForRateQualifiedWithinDateRange(1, seasonStartDate, seasonEndDate);
        mockRateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mock(DateService.class);
        mockRateQualifiedService.setDateService(mockDateService);
        when(mockDateService.getCaughtUpDate()).thenReturn(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = mockRateQualifiedService.applySplitForUpdateSeason(season, 1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 1);
        assertTrue(splittedSeasons.get(0).getStartDate().compareTo(seasonStartDate) == 0);
        assertTrue(splittedSeasons.get(0).getEndDate().compareTo(seasonEndDate) == 0);
    }

    @Test
    public void shouldSplitUpdatedSeasonWhenUpdatedSeasonHasSameEndDateAsSystemDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 30));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        QualifiedRateSeason expectedOldSeason = generateSeason(startDate, endDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonForUpdate = generateSeason(startDate, endDate, new BigDecimal(2.1), true);
        RateQualifiedService mockRateQualifiedService = spy(new RateQualifiedService());
        List<QualifiedRateSeason> expectedSeasons = new ArrayList<QualifiedRateSeason>();
        expectedSeasons.add(expectedOldSeason);
        doReturn(expectedSeasons).when(mockRateQualifiedService).fetchSeasonForRateQualifiedWithinDateRange(1, startDate, endDate);
        SeasonService seasonService = new SeasonService();
        seasonService.setTenantCrudService(tenantCrudService());
        mockRateQualifiedService.setSeasonService(seasonService);
        DateService mockDateService = mock(DateService.class);
        mockRateQualifiedService.setDateService(mockDateService);
        when(mockDateService.getCaughtUpDate()).thenReturn(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = mockRateQualifiedService.applySplitForUpdateSeason(seasonForUpdate, 1);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        assertOldSeason(startDate, expectedCaughtUpDate, splittedSeasons.get(0));
        assertNewSeason(endDate, expectedCaughtUpDate, splittedSeasons.get(1));
    }

    private void assertNewSeason(LocalDate endDate, Date expectedCaughtUpDate,
                                 QualifiedRateSeason newSeason) {
        LocalDate caughtUpDate = LocalDateUtils.fromDate(expectedCaughtUpDate);
        assertTrue(newSeason.getStartDate().compareTo(caughtUpDate) == 0);
        assertTrue(newSeason.getEndDate().compareTo(endDate) == 0);
        verifyRateValueOfNewSeason(newSeason);
        verifyNewSeasonDetailsIdIsNull(newSeason);
        assertFalse(newSeason.isDeleted());
    }

    private void assertOldSeason(LocalDate startDate,
                                 Date expectedCaughtUpDate, QualifiedRateSeason oldSeason) {
        assertTrue(oldSeason.getStartDate().compareTo(startDate) == 0);
        LocalDate expectedEndDateOfPastSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(expectedCaughtUpDate, -1));
        assertTrue(oldSeason.getEndDate().compareTo(expectedEndDateOfPastSeason) == 0);
        verifyExistingSeasonDetailsIdIsNotNull(oldSeason);
        verifyRateValueOfExistingSeason(oldSeason);
        assertFalse(oldSeason.isDeleted());
    }

    private QualifiedRateSeason generateSeason(LocalDate startDate, LocalDate endDate, BigDecimal value, boolean withId) {
        QualifiedRateSeason season = new QualifiedRateSeason();
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        RoomClass roomClass = new RoomClass();
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setMonday(value);
        seasonDetail.setTuesday(value);
        seasonDetail.setWednesday(value);
        seasonDetail.setThursday(value);
        seasonDetail.setFriday(value);
        seasonDetail.setSaturday(value);
        seasonDetail.setSunday(value);
        if (withId) {
            seasonDetail.setId(1);
        }
        roomClass.addSeasonDetails(seasonDetail);
        season.addRoomClasses(roomClass);
        return season;
    }

    @Test
    public void shouldFetchAllActiveNonEmptyRoomClasses() {
        UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        List<RoomClass> roomClasses = rateQualifiedService.fetchAllActiveNonEmptyRoomClasses();
        assertTrue(roomClasses.size() == 3);
    }

    @Test
    public void shouldReturnFalseWhenCaughtUpDateIsAfterSeasonToSplitEndDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 11);
        QualifiedRateSeason seasonToSplit = new QualifiedRateSeason();
        seasonToSplit.setStartDate(startDate);
        seasonToSplit.setEndDate(endDate);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        assertFalse(rateQualifiedService.isSplitRequired(seasonToSplit));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void shouldReturnFalseWhenCaughtUpDateIsSameAsSeasonToSplitStartDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.getCurrentDateWithoutTime();
        QualifiedRateSeason seasonToSplit = new QualifiedRateSeason();
        seasonToSplit.setStartDate(startDate);
        seasonToSplit.setEndDate(endDate);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        assertFalse(rateQualifiedService.isSplitRequired(seasonToSplit));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void shouldReturnFalseWhenCaughtUpDateIsBeforeSeasonToSplitStartDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason seasonToSplit = new QualifiedRateSeason();
        seasonToSplit.setStartDate(startDate);
        seasonToSplit.setEndDate(endDate);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        assertFalse(rateQualifiedService.isSplitRequired(seasonToSplit));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void shouldReturnTrueWhenCaughtUpDateIsBetweenSeasonToSplitStartDateAndEndDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5);
        QualifiedRateSeason seasonToSplit = new QualifiedRateSeason();
        seasonToSplit.setStartDate(startDate);
        seasonToSplit.setEndDate(endDate);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        assertTrue(rateQualifiedService.isSplitRequired(seasonToSplit));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void shouldReturnTrueWhenCaughtUpDateIsSameAsSeasonToSplitEndDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10);
        QualifiedRateSeason seasonToSplit = new QualifiedRateSeason();
        seasonToSplit.setStartDate(startDate);
        seasonToSplit.setEndDate(endDate);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        assertTrue(rateQualifiedService.isSplitRequired(seasonToSplit));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void shouldSplitWhenExistingSeasonIsCompletelyOverlappedByNewSeason() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(startDate, endDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(1);
        assertTrue(existingSeasonAfterSplit.isDeleted());
        assertFalse(newSeasonAfterSplit.isDeleted());
        verifySeasonStartDateEndDateRemainsAsItIs(existingSeasonAfterSplit, existingSeason);
        verifySeasonStartDateEndDateRemainsAsItIs(newSeasonAfterSplit, seasonToAdd);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
    }

    @Test
    public void shouldSplitWhenExistingSeasonIsOverlappedByNewSeasonWithSameStartDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(startDate, endDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(1);
        assertTrue(existingSeasonAfterSplit.isDeleted());
        assertFalse(newSeasonAfterSplit.isDeleted());
        verifySeasonStartDateEndDateRemainsAsItIs(existingSeasonAfterSplit, existingSeason);
        verifySeasonStartDateEndDateRemainsAsItIs(newSeasonAfterSplit, seasonToAdd);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
    }

    @Test
    public void shouldSplitWhenExistingSeasonIsOverlappedByNewSeasonWithSameEndDate() {
        LocalDate startDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -5));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(startDate, endDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(1);
        assertTrue(existingSeasonAfterSplit.isDeleted());
        assertFalse(newSeasonAfterSplit.isDeleted());
        verifySeasonStartDateEndDateRemainsAsItIs(existingSeasonAfterSplit, existingSeason);
        verifySeasonStartDateEndDateRemainsAsItIs(newSeasonAfterSplit, seasonToAdd);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
    }

    @Test
    public void shouldSplitWhenMoreThanOneExistingSeasonsAreOverlappedByNewSeason() {
        LocalDate startDateOfExistingSeason1 = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDateOfExistingSeason1 = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfExistingSeason2 = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate endDateOfExistingSeason2 = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 20));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 25));
        QualifiedRateSeason existingSeason1 = generateSeason(startDateOfExistingSeason1, endDateOfExistingSeason1, new BigDecimal(1.1), false);
        QualifiedRateSeason existingSeason2 = generateSeason(startDateOfExistingSeason2, endDateOfExistingSeason2, new BigDecimal(2.1), false);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(3.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason1);
        existingSeasons.add(existingSeason2);
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 3);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason existingFirstSeasonAfterSplit = splittedSeasons.get(1);
        QualifiedRateSeason existingSecondSeasonAfterSplit = splittedSeasons.get(2);
        assertTrue(existingFirstSeasonAfterSplit.isDeleted());
        assertTrue(existingSecondSeasonAfterSplit.isDeleted());
        assertFalse(newSeasonAfterSplit.isDeleted());
        assertSeasonDates(existingFirstSeasonAfterSplit, existingSeason1);
        assertSeasonDates(existingSecondSeasonAfterSplit, existingSeason2);
        assertSeasonDates(newSeasonAfterSplit, seasonToAdd);
        assertTrue(existingFirstSeasonAfterSplit.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday().compareTo(new BigDecimal(1.1)) == 0);
        assertTrue(existingSecondSeasonAfterSplit.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday().compareTo(new BigDecimal(2.1)) == 0);
        assertTrue(newSeasonAfterSplit.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday().compareTo(new BigDecimal(3.1)) == 0);
    }

    private void assertSeasonDates(QualifiedRateSeason expectedSeason, QualifiedRateSeason actualSeason) {
        assertTrue(expectedSeason.getStartDate().isEqual(actualSeason.getStartDate()));
        assertTrue(expectedSeason.getEndDate().isEqual(actualSeason.getEndDate()));
    }

    @Test
    public void shouldSplitWhenStartOfExistingSeasonIsOverlappedByNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertForExistingSeasonStartDateOverlappedByNewSeason(endDateOfNewSeason, existingSeason, seasonToAdd, splittedSeasons);
    }

    private void assertForExistingSeasonStartDateOverlappedByNewSeason(
            LocalDate endDateOfNewSeason, QualifiedRateSeason existingSeason,
            QualifiedRateSeason seasonToAdd,
            List<QualifiedRateSeason> splittedSeasons) {
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason splittedNewSeason = splittedSeasons.get(0);
        QualifiedRateSeason splittedExistingSeason = splittedSeasons.get(1);
        assertFalse(splittedExistingSeason.isDeleted());
        assertFalse(splittedNewSeason.isDeleted());
        LocalDate expectedStartDateExistingSeason = endDateOfNewSeason.plusDays(1);
        assertTrue(expectedStartDateExistingSeason.isEqual(splittedExistingSeason.getStartDate()));
        assertTrue(existingSeason.getEndDate().isEqual(splittedExistingSeason.getEndDate()));
        verifySeasonStartDateEndDateRemainsAsItIs(splittedNewSeason, seasonToAdd);
        verifyRateValueOfExistingSeason(splittedExistingSeason);
        verifyRateValueOfNewSeason(splittedNewSeason);
        verifyExistingSeasonDetailsIdIsNotNull(splittedExistingSeason);
        verifyNewSeasonDetailsIdIsNull(splittedNewSeason);
    }

    @Test
    public void shouldSplitWhenStartOfExistingSeasonIsOverlappedByEndOfNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertForExistingSeasonStartDateOverlappedByNewSeason(endDateOfNewSeason, existingSeason, seasonToAdd, splittedSeasons);
    }

    @Test
    public void shouldSplitWhenStartOfExistingSeasonIsOverlappedByStartOfNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason splittedNewSeason = splittedSeasons.get(0);
        QualifiedRateSeason splittedExistingSeason = splittedSeasons.get(1);
        verifyAllSplittedSeasonsAreNotMarkedForDeletion(splittedSeasons);
        verifySeasonStartDateEndDateRemainsAsItIs(seasonToAdd, splittedNewSeason);
        verifySplittedExistingSeasonStartsOneDayAfterNewSeason(splittedExistingSeason, endDateOfNewSeason, existingSeasonEndDate);
        verifyRateValueOfExistingSeason(splittedExistingSeason);
        verifyRateValueOfNewSeason(splittedNewSeason);
        verifyExistingSeasonDetailsIdIsNotNull(splittedExistingSeason);
        verifyNewSeasonDetailsIdIsNull(splittedNewSeason);
    }

    @Test
    public void fetchSeasonForRateQualifiedWithinDateRange() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = rateQualifiedService.fetchSeasonForRateQualifiedWithinDateRangeAndRateIds(Arrays.asList(1), new Date(1000, 7, 1), new Date(3017, 7, 1));
        assertNotNull(massRestrictionUploadDTOS);
    }

    @Test
    public void fetchSeasonForRateQualifiedWithinDateRangeWithLimit() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = rateQualifiedService.fetchSeasonForRateQualifiedWithinDateRangeAndRateIds(10001, Arrays.asList(1), new Date(1000, 7, 1), new Date(3017, 7, 1));
        assertNotNull(massRestrictionUploadDTOS);
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldFetchSeasonForRateQualifiedWithinDateRangeAndRateIds() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date season1StartDate = DateUtil.getCurrentDateWithoutTime();
        Date season1EndDate = DateUtil.addDaysToDate(season1StartDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(season1StartDate, season1EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate,
                propertyId, rateQualified.getId());
        Date season2StartDate = DateUtil.addDaysToDate(season1EndDate, 1);
        Date season2EndDate = DateUtil.addDaysToDate(season1EndDate, 5);
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(season2StartDate, season2EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate,
                propertyId, rateQualified2.getId());


        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = rateQualifiedService.fetchSeasonForRateQualifiedWithinDateRangeAndRateIds(Collections.singletonList(rateQualified.getId()),
                DateUtil.addDaysToDate(season1StartDate, 1), DateUtil.addDaysToDate(season1EndDate, 10));
        assertEquals(massRestrictionUploadDTOS.size(), 1);
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderStartDate().compareTo(season1StartDate) == 0);
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderEndDate().compareTo(season1EndDate) == 0);
        assertEquals(massRestrictionUploadDTOS.get(0).getRateQualifiedId(), rateQualified.getId());
    }

    @Test
    @Tag("qualifiedrate-flaky")
    public void shouldFetchActiveSeasonForRateQualifiedWithinDateRange() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date season1StartDate = DateUtil.getCurrentDateWithoutTime();
        Date season1EndDate = DateUtil.addDaysToDate(season1StartDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(season1StartDate, season1EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate,
                propertyId, rateQualified.getId());
        Date season2StartDate = DateUtil.addDaysToDate(season1EndDate, 1);
        Date season2EndDate = DateUtil.addDaysToDate(season1EndDate, 5);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate,
                propertyId, rateQualified.getId());

        List<QualifiedRateSeason> qualifiedRateSeasons = rateQualifiedService.fetchActiveSeasonsWithinDateRange
                (rateQualified.getId(), new LocalDate(season1StartDate).plusDays(1), new LocalDate(season1EndDate).plusDays(23));
        assertEquals(qualifiedRateSeasons.size(), 2);
        assertTrue(qualifiedRateSeasons.get(0).getStartDate().toDate().compareTo(season1StartDate) == 0);
        assertTrue(qualifiedRateSeasons.get(0).getEndDate().toDate().compareTo(season1EndDate) == 0);
        assertTrue(qualifiedRateSeasons.get(1).getStartDate().toDate().compareTo(season2StartDate) == 0);
        assertTrue(qualifiedRateSeasons.get(1).getEndDate().toDate().compareTo(season2EndDate) == 0);
    }

    @Test
    public void shouldFetchAllSeasonForRateQualifiedWithLimit() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        Date season1StartDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date season1EndDate = DateUtil.addDaysToDate(season1StartDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(season1StartDate, season1EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate,
                propertyId, rateQualified.getId());
        Date season2StartDate = DateUtil.addDaysToDate(season1EndDate, 1);
        Date season2EndDate = DateUtil.addDaysToDate(season1EndDate, 5);
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(season2StartDate, season2EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate,
                propertyId, rateQualified2.getId());
        Date season3StartDate = DateUtil.addDaysToDate(season1StartDate, -20);
        Date season3EndDate = DateUtil.addDaysToDate(season1StartDate, -10);
        RateQualified rateQualified3 = UniqueRateQualified.createRateQualifiedByDate(season3StartDate, season3EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season3StartDate, season3EndDate,
                propertyId, rateQualified3.getId());
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(season1StartDate));

        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = rateQualifiedService.fetchAllSeasonForRateQualified(10001);
        massRestrictionUploadDTOS.sort(Comparator.comparing(MassRestrictionUploadDTO::getSeasonStartDate));
        assertEquals(massRestrictionUploadDTOS.size(), 2);
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderStartDate().compareTo(season1StartDate) == 0);
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderEndDate().compareTo(season1EndDate) == 0);
        assertEquals(massRestrictionUploadDTOS.get(0).getRateQualifiedId(), rateQualified.getId());
        assertTrue(massRestrictionUploadDTOS.get(1).getRateHeaderStartDate().compareTo(season2StartDate) == 0);
        assertTrue(massRestrictionUploadDTOS.get(1).getRateHeaderEndDate().compareTo(season2EndDate) == 0);
        assertEquals(massRestrictionUploadDTOS.get(1).getRateQualifiedId(), rateQualified2.getId());
    }

    @Test
    public void shouldFetchAllSeasonForRateQualified() {
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate());
        Date season1StartDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date season1EndDate = DateUtil.addDaysToDate(season1StartDate, 20);
        RateQualified rateQualified = UniqueRateQualified.createRateQualifiedByDate(season1StartDate, season1EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season1StartDate, season1EndDate,
                propertyId, rateQualified.getId());
        Date season2StartDate = DateUtil.addDaysToDate(season1EndDate, 1);
        Date season2EndDate = DateUtil.addDaysToDate(season1EndDate, 5);
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(season2StartDate, season2EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season2StartDate, season2EndDate,
                propertyId, rateQualified2.getId());
        Date season3StartDate = DateUtil.addDaysToDate(season1StartDate, -20);
        Date season3EndDate = DateUtil.addDaysToDate(season1StartDate, -10);
        RateQualified rateQualified3 = UniqueRateQualified.createRateQualifiedByDate(season3StartDate, season3EndDate, propertyId);
        UniqueRateQualifiedDetails.createRateQualifiedDetails(season3StartDate, season3EndDate,
                propertyId, rateQualified3.getId());
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(season1StartDate));

        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = rateQualifiedService.fetchAllSeasonForRateQualified();
        assertEquals(massRestrictionUploadDTOS.size(), 2);
        massRestrictionUploadDTOS.sort(Comparator.comparing(MassRestrictionUploadDTO::getSeasonStartDate));
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderStartDate().compareTo(season1StartDate) == 0);
        assertTrue(massRestrictionUploadDTOS.get(0).getRateHeaderEndDate().compareTo(season1EndDate) == 0);
        assertEquals(massRestrictionUploadDTOS.get(0).getRateQualifiedId(), rateQualified.getId());
        assertTrue(massRestrictionUploadDTOS.get(1).getRateHeaderStartDate().compareTo(season2StartDate) == 0);
        assertTrue(massRestrictionUploadDTOS.get(1).getRateHeaderEndDate().compareTo(season2EndDate) == 0);
        assertEquals(massRestrictionUploadDTOS.get(1).getRateQualifiedId(), rateQualified2.getId());
    }

    private void verifyExistingSeasonDetailsIdIsNotNull(QualifiedRateSeason splittedExistingSeason) {
        assertNotNull(splittedExistingSeason.getRoomClasses().get(0).getSeasonDetails().get(0).getId());
    }

    private void verifyNewSeasonDetailsIdIsNull(
            QualifiedRateSeason splittedNewSeason) {
        assertNull(splittedNewSeason.getRoomClasses().get(0).getSeasonDetails().get(0).getId());
    }

    private void verifyRateValueOfNewSeason(QualifiedRateSeason splittedNewSeason) {
        assertTrue(splittedNewSeason.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday().compareTo(new BigDecimal(2.1)) == 0);
    }

    private void verifyRateValueOfExistingSeason(
            QualifiedRateSeason splittedExistingSeason) {
        assertTrue(splittedExistingSeason.getRoomClasses().get(0).getSeasonDetails().get(0).getMonday().compareTo(new BigDecimal(1.1)) == 0);
    }

    private void verifySplittedExistingSeasonStartsOneDayAfterNewSeason(QualifiedRateSeason splittedExistingSeason, LocalDate endDateOfNewSeason,
                                                                        LocalDate existingSeasonEndDate) {
        assertTrue(splittedExistingSeason.getStartDate().isEqual(endDateOfNewSeason.plusDays(1)));
        assertTrue(splittedExistingSeason.getEndDate().isEqual(existingSeasonEndDate));
    }

    @Test
    public void shouldSplitWhenNewSeasonIsOverlappedByExistingSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 3);
        QualifiedRateSeason splittedExistingSeasonBeforeNewSeason = splittedSeasons.get(0);
        QualifiedRateSeason splittedNewSeason = splittedSeasons.get(1);
        QualifiedRateSeason splittedExistingSeasonAfterNewSeason = splittedSeasons.get(2);
        verifyAllSplittedSeasonsAreNotMarkedForDeletion(splittedSeasons);
        verifyExistingSeasonShouldEndOneDayBeforeNewSeasonStarts(startDateOfNewSeason, existingSeasonStartDate, splittedExistingSeasonBeforeNewSeason);
        verifySeasonStartDateEndDateRemainsAsItIs(seasonToAdd, splittedNewSeason);
        splittedExistingSeasonAfterNewSeasonStartsOneDayAfterNewSeason(existingSeasonEndDate, endDateOfNewSeason, splittedExistingSeasonAfterNewSeason);
        verifyExistingSeasonDetailsIdIsNotNull(splittedExistingSeasonBeforeNewSeason);
        verifyNewSeasonDetailsIdIsNull(splittedNewSeason);
        verifyNewSeasonDetailsIdIsNull(splittedExistingSeasonAfterNewSeason);
    }

    private void splittedExistingSeasonAfterNewSeasonStartsOneDayAfterNewSeason(
            LocalDate existingSeasonEndDate, LocalDate endDateOfNewSeason,
            QualifiedRateSeason splittedExistingSeasonAfterNewSeason) {
        assertTrue(splittedExistingSeasonAfterNewSeason.getStartDate().isEqual(endDateOfNewSeason.plusDays(1)));
        assertTrue(splittedExistingSeasonAfterNewSeason.getEndDate().isEqual(existingSeasonEndDate));
    }

    private void verifyAllSplittedSeasonsAreNotMarkedForDeletion(List<QualifiedRateSeason> splittedSeasons) {
        for (QualifiedRateSeason splitSeason : splittedSeasons) {
            assertFalse(splitSeason.isDeleted());
        }
    }

    private void verifyExistingSeasonShouldEndOneDayBeforeNewSeasonStarts(LocalDate startDateOfNewSeason, LocalDate startDateOfExistingSeason,
                                                                          QualifiedRateSeason splittedExistingSeasonBeforeNewSeason) {
        assertTrue(splittedExistingSeasonBeforeNewSeason.getStartDate().isEqual(startDateOfExistingSeason));
        assertTrue(splittedExistingSeasonBeforeNewSeason.getEndDate().isEqual(startDateOfNewSeason.minusDays(1)));
    }

    private void verifySeasonStartDateEndDateRemainsAsItIs(QualifiedRateSeason seasonBeforeSplit, QualifiedRateSeason seasonAfterSplit) {
        assertTrue(seasonAfterSplit.getStartDate().isEqual(seasonBeforeSplit.getStartDate()));
        assertTrue(seasonAfterSplit.getEndDate().isEqual(seasonBeforeSplit.getEndDate()));
    }

    @Test
    public void shouldSplitWhenEndOfExistingSeasonIsOverlappedByNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(1);
        verifyAllSplittedSeasonsAreNotMarkedForDeletion(splittedSeasons);
        verifyExistingSeasonShouldEndOneDayBeforeNewSeasonStarts(startDateOfNewSeason, existingSeasonStartDate, existingSeasonAfterSplit);
        verifySeasonStartDateEndDateRemainsAsItIs(seasonToAdd, newSeasonAfterSplit);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
    }

    @Test
    public void shouldSplitWhenEndOfExistingSeasonIsOverlappedByStartOfNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(1);
        verifyAllSplittedSeasonsAreNotMarkedForDeletion(splittedSeasons);
        verifyExistingSeasonShouldEndOneDayBeforeNewSeasonStarts(startDateOfNewSeason, existingSeasonStartDate, existingSeasonAfterSplit);
        verifySeasonStartDateEndDateRemainsAsItIs(seasonToAdd, newSeasonAfterSplit);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
    }

    @Test
    public void shouldSplitWhenEndOfExistingSeasonIsOverlappedByEndOfNewSeason() {
        LocalDate existingSeasonStartDate = LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime());
        LocalDate existingSeasonEndDate = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        LocalDate startDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 5));
        LocalDate endDateOfNewSeason = LocalDateUtils.fromDate(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 15));
        Date expectedCaughtUpDate = DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), -1);
        QualifiedRateSeason existingSeason = generateSeason(existingSeasonStartDate, existingSeasonEndDate, new BigDecimal(1.1), true);
        QualifiedRateSeason seasonToAdd = generateSeason(startDateOfNewSeason, endDateOfNewSeason, new BigDecimal(2.1), false);
        List<QualifiedRateSeason> existingSeasons = new ArrayList<QualifiedRateSeason>();
        existingSeasons.add(existingSeason);
        rateQualifiedService.setSeasonService(new SeasonService());
        DateService mockDateService = mockCaughtUpDate(expectedCaughtUpDate);
        List<QualifiedRateSeason> splittedSeasons = rateQualifiedService.applySplitForAddSeason(existingSeasons, seasonToAdd);
        verify(mockDateService).getCaughtUpDate();
        assertTrue(splittedSeasons.size() == 2);
        QualifiedRateSeason existingSeasonAfterSplit = splittedSeasons.get(0);
        QualifiedRateSeason newSeasonAfterSplit = splittedSeasons.get(1);
        verifyAllSplittedSeasonsAreNotMarkedForDeletion(splittedSeasons);
        verifyExistingSeasonShouldEndOneDayBeforeNewSeasonStarts(startDateOfNewSeason, existingSeasonStartDate, existingSeasonAfterSplit);
        verifySeasonStartDateEndDateRemainsAsItIs(seasonToAdd, newSeasonAfterSplit);
        verifyRateValueOfNewSeason(newSeasonAfterSplit);
        verifyRateValueOfExistingSeason(existingSeasonAfterSplit);
        verifyNewSeasonDetailsIdIsNull(newSeasonAfterSplit);
        verifyExistingSeasonDetailsIdIsNotNull(existingSeasonAfterSplit);
    }

    private DateService mockCaughtUpDate(Date expectedCaughtUpDate) {
        DateService mockDateService = mock(DateService.class);
        rateQualifiedService.setDateService(mockDateService);
        when(mockDateService.getCaughtUpDate()).thenReturn(expectedCaughtUpDate);
        return mockDateService;
    }

    @Test
    public void shouldSetDaysOfWeekToMinusOneWhenTheStartDateAndEndDateRangeIsLessThanSevenDays() {
        final BigDecimal expectedRate = new BigDecimal(2.1);
        SeasonDetail seasonDetail = buildSeasonDetailWith(1, expectedRate);

        rateQualifiedService.clearOutOfRangeDayOfWeekValues(seasonDetail, LocalDateUtils.fromDate(DateUtil.getDate(1, 4, 2014)), LocalDateUtils.fromDate(DateUtil.getDate(3, 4, 2014)));

        BigDecimal expectedNoRate = new BigDecimal(-1);
        assertEquals(expectedNoRate.intValue(), seasonDetail.getSunday().intValue());
        assertEquals(expectedNoRate.intValue(), seasonDetail.getMonday().intValue());
        assertEquals(expectedNoRate.intValue(), seasonDetail.getTuesday().intValue());
        assertEquals(expectedNoRate.intValue(), seasonDetail.getWednesday().intValue());
        assertEquals(expectedRate, seasonDetail.getThursday());
        assertEquals(expectedRate, seasonDetail.getFriday());
        assertEquals(expectedRate, seasonDetail.getSaturday());
    }

    @Test
    public void shouldSortSeasonsKeepingTheSeasonsToInsertAtTheEndOfTheListWhenSortMethodCalledOnTheCollection() {
        QualifiedRateSeason seasonToUpdate = buildQualifiedRateSeasonWith(1, new BigDecimal(100), LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()), LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()).plusDays(15));
        QualifiedRateSeason seasonToAdd = buildQualifiedRateSeasonWith(null, new BigDecimal(200), LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()), LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()).plusDays(10));
        QualifiedRateSeason seasonToDelete = buildQualifiedRateSeasonWith(2, null, LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()).plusDays(20), LocalDateUtils.fromDate(DateUtil.getCurrentDateWithoutTime()).plusDays(25));
        List<QualifiedRateSeason> seasonsToBeSorted = new ArrayList<QualifiedRateSeason>();
        seasonsToBeSorted.add(seasonToAdd);
        seasonsToBeSorted.add(seasonToUpdate);
        seasonsToBeSorted.add(seasonToDelete);

        Collections.sort(seasonsToBeSorted);

        assertTrue(seasonsToBeSorted.get(0) == seasonToUpdate);
        assertTrue(seasonsToBeSorted.get(1) == seasonToDelete);
        assertTrue(seasonsToBeSorted.get(2) == seasonToAdd);
    }

    @Test
    public void testDeletePastRateForTestBench() {
        when(dateService.getCaughtUpDate()).thenReturn(new Date());
        rateQualifiedService.addPastRateForVaadinTestBench(5);
        List<RateQualified> pastRate = tenantCrudService().findByNamedQuery(RateQualified.BY_PROPERTY_AND_RATE_CODE_NAME,
                QueryParameter.with("propertyId", 5).and("rateCodeName", "PastRate").parameters());
        assertNotNull(pastRate);
        assertEquals(1, pastRate.size());
        rateQualifiedService.deletePastRateForVaadinTestBench(5);
        pastRate = tenantCrudService().findByNamedQuery(RateQualified.BY_PROPERTY_AND_RATE_CODE_NAME,
                QueryParameter.with("propertyId", 5).and("rateCodeName", "PastRate").parameters());
        assertNotNull(pastRate);
        assertEquals(0, pastRate.size());
    }

    private QualifiedRateSeason buildQualifiedRateSeasonWith(Integer seasonDetailId, BigDecimal rateValueForDOWs, LocalDate seasonStartDate, LocalDate seasonEndDate) {
        QualifiedRateSeason qualifiedRateSeason = new QualifiedRateSeason();
        qualifiedRateSeason.setStartDate(seasonStartDate);
        qualifiedRateSeason.setEndDate(seasonEndDate);
        RoomClass roomClass = buildRoomClassWith(seasonDetailId, rateValueForDOWs);
        qualifiedRateSeason.addRoomClasses(roomClass);
        return qualifiedRateSeason;
    }

    private RoomClass buildRoomClassWith(Integer seasonDetailIdToUpdate, BigDecimal rateValueForDOWsForSeasonToUpdate) {
        RoomClass roomClass = new RoomClass();
        SeasonDetail seasonDetail = buildSeasonDetailWith(seasonDetailIdToUpdate, rateValueForDOWsForSeasonToUpdate);
        roomClass.addSeasonDetails(seasonDetail);
        return roomClass;
    }

    private SeasonDetail buildSeasonDetailWith(Integer seasonDetailId, BigDecimal rateValueForDOWs) {
        SeasonDetail seasonDetail = new SeasonDetail();
        seasonDetail.setId(seasonDetailId);
        seasonDetail.setMonday(rateValueForDOWs);
        seasonDetail.setTuesday(rateValueForDOWs);
        seasonDetail.setWednesday(rateValueForDOWs);
        seasonDetail.setThursday(rateValueForDOWs);
        seasonDetail.setFriday(rateValueForDOWs);
        seasonDetail.setSaturday(rateValueForDOWs);
        seasonDetail.setSunday(rateValueForDOWs);
        return seasonDetail;
    }

    @Test
    public void testDerivedQualifiedRatesWhenRatesAreActive() {
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date rateHeaderEndDate = DateUtil.addDaysToDate(currentDate, 30);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_VALUE_TYPE);
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        //create fixed non-yieldable rate
        RateQualified rateQualified3 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_FIXED_TYPE, 1, 0);
        //create fixed yieldable rate
        UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_FIXED_TYPE, 1, 1);
        UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_FIXED_TYPE);

        List<RateQualified> rateQualifieds = rateQualifiedService.fetchG3ManagedRates();
        assertEquals(3, rateQualifieds.size());
        assertEquals(rateQualified1.getName(), rateQualifieds.get(0).getName());
        assertEquals(rateQualified2.getName(), rateQualifieds.get(1).getName());
        assertEquals(rateQualified3.getName(), rateQualifieds.get(2).getName());
    }

    @Test
    public void fetchDerivedActiveAndYieldableRates() {
        LocalDate start = LocalDate.now();
        LocalDate end = LocalDate.now().plusMonths(1);
        RateQualified derived = createRateQualifiedByDateAndName(start.toDate(), end.toDate(), PacmanWorkContextHelper.getPropertyId(), "QRD");
        derived.setRateQualifiedTypeId(1);
        derived.setYieldable(1);
        derived.setStatusId(1);

        RateQualified fixed = createRateQualifiedByDateAndName(start.toDate(), end.toDate(), PacmanWorkContextHelper.getPropertyId(), "QRF");
        fixed.setRateQualifiedTypeId(3);
        fixed.setYieldable(1);
        fixed.setStatusId(1);

        List<RateQualified> derivedRates = rateQualifiedService.fetchDerivedActiveAndYieldableRates();
        assertNotNull(derivedRates);
        assertEquals(1, derivedRates.size());
        assertEquals("QRD", derivedRates.get(0).getName());
    }

    @Test
    public void testDerivedQualifiedRatesWhenRateIsDeleted() {
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date rateHeaderEndDate = DateUtil.addDaysToDate(currentDate, 30);

        //setting status to deleted/closed
        UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_VALUE_TYPE, 2);
        //create fixed non-yieldable deleted rate
        UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_FIXED_TYPE, 2, 0);

        UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_VALUE_TYPE, 2);
        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        //create fixed non-yieldable active rate
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_FIXED_TYPE, 1, 0);

        List<RateQualified> rateQualifieds = rateQualifiedService.fetchG3ManagedRates();
        assertEquals(2, rateQualifieds.size());
        assertEquals(rateQualified1.getName(), rateQualifieds.get(0).getName());
        assertEquals(rateQualified2.getName(), rateQualifieds.get(1).getName());
    }

    @Test
    public void fetchYieldableQualifiedRates() {

        RateQualified rateQualified = buildRateQualified(DateUtil.getCurrentDateWithoutTime(), new LocalDate(DateUtil.getCurrentDateWithoutTime()).plusDays(10).toDate());
        rateQualified.setYieldable(1);
        rateQualified.setFileMetadataId(3);
        rateQualified.setIncludesPackage(34);
        rateQualified.setPriceRelative(3);
        rateQualified.setPropertyId(5);
        rateQualified.setStatusId(1);
        rateQualified.setRateQualifiedTypeId(3);
        rateQualified.setReferenceRateCode("0");
        tenantCrudService().save(rateQualified);

        Integer yeildableRatesCount = rateQualifiedService.getYieldableQualifiedRatesCount();
        assertEquals(1, yeildableRatesCount, 0);
    }

    public RateQualified buildRateQualified(Date currentDateWithoutTime, Date endDate) {
        RateQualified rateQualified = new RateQualified();
        rateQualified.setEndDate(endDate);
        rateQualified.setStartDate(currentDateWithoutTime);
        rateQualified.setName("validRateName");
        rateQualified.setDescription("validRateName");
        return rateQualified;
    }

    @Test
    public void shouldFindRateQualifiedIdsMatchingDateRangeOfSourceRateHeader() {
        List<RateQualified> rateQualifiedList = createRateQualifiedList();
        Map<String, Integer> rateQualifiedCodeToIdMapping = createRateQualifiedCodeToIdMapping(rateQualifiedList);
        Map<Integer, String> rateQualifiedIdToCodeMapping = createRateQualifiedIdToCodeMapping(rateQualifiedList);

        createRateQualifiedDetails(RATE_QUALIFIED_DETAILS_WITH_MATCHING_RATE_CODES[0], rateQualifiedCodeToIdMapping);
        assertMatchingRateCodes(RATE_QUALIFIED_DETAILS_WITH_MATCHING_RATE_CODES[1], rateQualifiedIdToCodeMapping);
    }

    @Test
    public void shouldUpdateManagedInG3ToFalse() {
        rateQualifiedService.multiPropertyCrudService = multiPropertyCrudService();
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date rateHeaderEndDate = DateUtil.addDaysToDate(currentDate, 30);

        RateQualified rateQualified1 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified1.setManagedInG3(true);
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(currentDate, rateHeaderEndDate, propertyId, QUALIFIED_RATE_PLAN_VALUE_TYPE);
        rateQualified2.setManagedInG3(true);
        tenantCrudService().save(rateQualified1);
        tenantCrudService().save(rateQualified2);

        rateQualifiedService.updateToNotManagedInG3(5);

        List<Integer> result = tenantCrudService().findByNativeQuery("select [Managed_In_G3] from Rate_Qualified");
        assertEquals(2, result.size());
        assertEquals(0, result.get(0).intValue());
        assertEquals(0, result.get(1).intValue());
    }

    @Test
    public void shouldDeactivateAllDerivedRates() {
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        LocalDate dayAfterTomorrow = tomorrow.plusDays(1);
        LocalDate endDate = today.plusMonths(3);

        RateQualified fixedRate = UniqueRateQualified.createRateQualifiedByDate(today.toDate(), endDate.toDate(), PacmanWorkContextHelper.getPropertyId(), QUALIFIED_RATE_PLAN_FIXED_TYPE);
        tenantCrudService().detach(fixedRate);

        RateQualified linkedRateManagedInG3 = UniqueRateQualified.createRateQualifiedByDate(tomorrow.toDate(), endDate.toDate(), PacmanWorkContextHelper.getPropertyId(), QUALIFIED_RATE_PLAN_VALUE_TYPE);
        linkedRateManagedInG3.setManagedInG3(true);
        tenantCrudService().detach(linkedRateManagedInG3);

        tenantCrudService().save(linkedRateManagedInG3);
        RateQualified linkedRateNotManagedInG3 = UniqueRateQualified.createRateQualifiedByDate(dayAfterTomorrow.toDate(), endDate.toDate(), PacmanWorkContextHelper.getPropertyId(), QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService().detach(linkedRateNotManagedInG3);

        rateQualifiedService.deactivateAllDerivedRates();

        List<RateQualified> allRates = tenantCrudService().findAll(RateQualified.class);
        allRates.sort(Comparator.comparing(RateQualified::getStartDate));


        assertEquals(new Integer(1), allRates.get(0).getStatusId());
        assertEquals(new Integer(1), allRates.get(1).getStatusId());
        assertEquals(new Integer(2), allRates.get(2).getStatusId());
    }


    @Test
    public void shouldFetchActiveRateHeader() {
        RateQualified rateQualified1 = createRateQualifiedByDateAndName(LocalDate.now().toDate(),
                LocalDate.now().plusYears(1).toDate(), 5, "R1");
        RateQualified rateQualified2 = createRateQualifiedByDateAndName(LocalDate.now().toDate(),
                LocalDate.now().plusYears(1).toDate(), 5, "R2");
        tenantCrudService().flushAndClear();
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);
        final List<RateHeader> rateHeaders = rateQualifiedService.fetchActiveRateHeaders();
        assertEquals(2, rateHeaders.size());
        assertEquals(rateQualified1.getId(), rateHeaders.get(0).getId());
        assertEquals(rateQualified2.getId(), rateHeaders.get(1).getId());
    }

    @Test
    public void shouldFetchActiveRateHeadersExceptForAgileProductLinkedRates() {
        RateQualified rateQualified1 = createRateQualifiedByDateAndName(LocalDate.now().toDate(),
                LocalDate.now().plusYears(1).toDate(), 5, "R1");
        RateQualified rateQualified2 = createRateQualifiedByDateAndName(LocalDate.now().toDate(),
                LocalDate.now().plusYears(1).toDate(), 5, "R2");
        final Product agile_product = createProductEntity("Agile Product");
        createAgileProductRestrictionAssociation(agile_product, rateQualified1);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);
        final List<RateHeader> rateHeaders = rateQualifiedService.fetchActiveRateHeaders();
        assertEquals(1, rateHeaders.size());
        assertEquals(rateQualified2.getId(), rateHeaders.get(0).getId());
    }

    private Product createProductEntity(String name) {
        Product product = createProduct(name, TenantStatusEnum.ACTIVE);
        return tenantCrudService().save(product);
    }

    private AgileProductRestrictionAssociation createAgileProductRestrictionAssociation(Product activeProduct, RateQualified rateQualified) {
        AgileProductRestrictionAssociation agileProductRestrictionAssociation = new AgileProductRestrictionAssociation();
        agileProductRestrictionAssociation.setRateQualified(rateQualified);
        agileProductRestrictionAssociation.setProduct(activeProduct);
        agileProductRestrictionAssociation.setYieldable(1);
        return tenantCrudService().save(agileProductRestrictionAssociation);
    }

    private Product createProduct(String name, TenantStatusEnum status) {
        Product arProduct = new Product();
        arProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        arProduct.setName(name);
        arProduct.setType("type");
        arProduct.setDescription("description");
        arProduct.setStatus(status);
        arProduct.setMinDTA(0);
        arProduct.setMinLOS(1);
        arProduct.setMaxLOS(2);
        arProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        arProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        arProduct.setCentrallyManaged(false);
        arProduct.setFloorPercentage(null);
        arProduct.setFloorType(FloorType.FIXED_RATE);
        arProduct.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        arProduct.setRateShoppingLOSMin(1);
        arProduct.setRateShoppingLOSMax(1);
        ProductCode pc = new ProductCode(2);
        arProduct.setProductCode(pc);
        arProduct.setMinRooms(-1);
        arProduct.setMaxRooms(-1);
        arProduct.setUseInSmallGroupEval(false);
        arProduct.setFreeNightEnabled(false);
        arProduct.setFreeUpgradeEnabled(false);
        tenantCrudService().save(arProduct);
        return arProduct;
    }

    private void assertMatchingRateCodes(Object[][] matchingRateCodeDetails, Map<Integer, String> rateQualifiedIdToCodeMapping) {
        for (int j = 1; j < matchingRateCodeDetails.length; j++) {
            LocalDate nextRateHeaderStartDate = LocalDate.parse((String) matchingRateCodeDetails[j][0]);
            LocalDate nextRateHeaderEndDate = LocalDate.parse((String) matchingRateCodeDetails[j][1]);
            LocalDate caughtUpDate = LocalDate.parse((String) matchingRateCodeDetails[j][2]);

            when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpDate);

            String expectedMatchingRateCodes = (String) matchingRateCodeDetails[j][3];
            List<Integer> matchingRateQualifiedIds = rateQualifiedService.findRateQualifiedIdsMatchingDateRangeOfSourceRateHeader(nextRateHeaderStartDate, nextRateHeaderEndDate);

            String actualMatchingRateCodes = matchingRateQualifiedIds.stream().sorted().map(rateQualifiedIdToCodeMapping::get).collect(Collectors.joining(","));
            assertEquals(expectedMatchingRateCodes, actualMatchingRateCodes, "SCENARIO " + j + ": FAILED");
        }
    }

    private void createRateQualifiedDetails(Object[][] rawRateQualifiedDetails, Map<String, Integer> rateQualifiedCodeToIdMapping) {
        for (int j = 1; j < rawRateQualifiedDetails.length; j++) {
            String rateCode = (String) rawRateQualifiedDetails[j][0];
            Integer rateQualifiedId = rateQualifiedCodeToIdMapping.get(rateCode);

            LocalDate startDate = LocalDate.parse((String) rawRateQualifiedDetails[j][1]);
            LocalDate endDate = LocalDate.parse((String) rawRateQualifiedDetails[j][2]);

            UniqueRateQualifiedDetails.createRateQualifiedDetails(startDate.toDate(), endDate.toDate(), TestProperty.H1.getId(), rateQualifiedId);
        }
    }

    private List<RateQualified> createRateQualifiedList() {
        List<RateQualified> rateQualifiedList = new ArrayList<>();

        Object[][] rawRateQualified = RATE_QUALIFIED;
        for (int i = 1; i < rawRateQualified.length; i++) {
            String rateName = (String) rawRateQualified[i][0];
            LocalDate startDate = LocalDate.parse((String) rawRateQualified[i][1]);
            LocalDate endDate = LocalDate.parse((String) rawRateQualified[i][2]);

            RateQualified rateQualified = createRateQualifiedByDateAndName(startDate.toDate(), endDate.toDate(), TestProperty.H1.getId(), rateName);
            rateQualifiedList.add(rateQualified);
        }

        return rateQualifiedList;
    }

    private Map<String, Integer> createRateQualifiedCodeToIdMapping(List<RateQualified> rateQualifiedList) {
        return rateQualifiedList.stream().collect(Collectors.toMap(RateQualified::getName, RateQualified::getId));
    }

    private Map<Integer, String> createRateQualifiedIdToCodeMapping(List<RateQualified> rateQualifiedList) {
        return rateQualifiedList.stream().collect(Collectors.toMap(RateQualified::getId, RateQualified::getName));
    }

    @Test
    public void loadRateDetailsV2() {
        when(configService.getIntegerParameterValue(PreProductionConfigParamName.RATE_ID_SIZE_TO_LOAD_FOR_RATE_DETAILS.value())).thenReturn(1);

        var rateQualified = tenantCrudService().save(getDetachedRateQualified(DEFAULT_START_DATE, DEFAULT_END_DATE, 5, 1, 1, 1, "Test rate", 1));
        var rateQualifiedFixed = tenantCrudService().save(createFixedRate(rateQualified));
        var fixedRateDetails = getRateQualifiedDetailsObj(rateQualifiedFixed.getId(), 0, 0, 0, 0, 0, 0, 0, 4);
        fixedRateDetails.setRateQualifiedId(rateQualified.getId());
        rateQualifiedFixed.setRateQualifiedId(rateQualified.getId());
        fixedRateDetails.setRateQualifiedFixed(rateQualifiedFixed);

        tenantCrudService().save(fixedRateDetails);


        var r = tenantCrudService().findAll(RateQualifiedFixedDetails.class);

        var map = rateQualifiedService.loadRateDetailsV2Map(
                DateUtil.getDateWithoutTime(1, Calendar.MAY, 2024),
                DateUtil.getDateWithoutTime(8, Calendar.MAY, 2024),
                List.of(rateQualified));


        var rateDetail = map.get(rateQualifiedFixed.getRateQualifiedId().intValue());
        try {
            var rateDetailsCompressedV2 = GzipCompressionHelper.decompressValue(rateDetail, RecommendationCommonConstants.objectMapper(), RateQualifiedService.RateDetailsCompressedV2.class);
            var detail = rateDetailsCompressedV2.getValue().get(0);
            assertEquals(detail.getRateQualifiedID(), rateQualifiedFixed.getRateQualifiedId());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private RateQualifiedFixedDetails getRateQualifiedDetailsObj(Long parentId, double sunday, double monday,
                                                                 double tuesday, double wednesday,
                                                                 double thursday, double friday, double saturday, Integer accomType) {

        RateQualifiedFixedDetails details = new RateQualifiedFixedDetails();
        details.setRateQualifiedFixedId(parentId);
        details.setAccomTypeId(accomType);
        details.setStartDate(DEFAULT_START_DATE);
        details.setEndDate(DEFAULT_END_DATE);
        details.setSunday(BigDecimal.valueOf(sunday));
        details.setMonday(BigDecimal.valueOf(monday));
        details.setTuesday(BigDecimal.valueOf(tuesday));
        details.setWednesday(BigDecimal.valueOf(wednesday));
        details.setThursday(BigDecimal.valueOf(thursday));
        details.setFriday(BigDecimal.valueOf(friday));
        details.setSaturday(BigDecimal.valueOf(saturday));
        return details;
    }

    private static RateQualified getDetachedRateQualified(Date startDate,
                                                          Date endDate, Integer propertyId, Integer ratePlanType, Integer statusId, Integer isYieldable, String rateName, int fileMetadatId) {
        Random r = new Random();
        RateQualified rateQualified = new RateQualified();
        rateQualified.setPropertyId(propertyId);
        rateQualified.setCurrency("Test currency");
        rateQualified.setDescription("Test description");
        rateQualified.setName(rateName == null ? "Test name" + r.nextInt() : rateName);
        rateQualified.setPriceRelative(500);
        rateQualified.setRemarks("remarks");
        rateQualified.setFileMetadataId(fileMetadatId);
        rateQualified.setIncludesPackage(1);
        rateQualified.setLastUpdatedDate(new Timestamp(0));
        rateQualified.setStartDate(startDate);
        rateQualified.setEndDate(endDate);
        rateQualified.setYieldable(isYieldable);
        rateQualified.setStatusId(statusId);
        rateQualified.setReferenceRateCode("None");
        rateQualified.setRateQualifiedTypeId(ratePlanType);
        return rateQualified;
    }

    private RateQualifiedFixed createFixedRate(RateQualified rateQualified) {


        RateQualifiedFixed rateQualifiedFixed = new RateQualifiedFixed();
        rateQualifiedFixed.setName("SRP1");
        rateQualifiedFixed.setRateQualifiedId(rateQualified.getId());
        rateQualifiedFixed.setReferenceRateCode("LV0");
        rateQualifiedFixed.setFileMetadataId(1);
        rateQualifiedFixed.setPropertyId(5);
        rateQualifiedFixed.setStatusId(1);
        rateQualifiedFixed.setYieldable(1);
        rateQualifiedFixed.setDescription("SRP1");
        rateQualifiedFixed.setCurrency("");
        rateQualifiedFixed.setPriceRelative(0);
        rateQualifiedFixed.setIncludesPackage(0);
        rateQualifiedFixed.setRemarks(null);
        rateQualifiedFixed.setRateQualifiedTypeId(Constants.QUALIFIED_RATE_PLAN_FIXED_TYPE);
        rateQualifiedFixed.setManagedInG3(1);
        rateQualifiedFixed.setStartDate(DEFAULT_START_DATE);
        rateQualifiedFixed.setEndDate(DEFAULT_END_DATE);

        return rateQualifiedFixed;
    }
}
