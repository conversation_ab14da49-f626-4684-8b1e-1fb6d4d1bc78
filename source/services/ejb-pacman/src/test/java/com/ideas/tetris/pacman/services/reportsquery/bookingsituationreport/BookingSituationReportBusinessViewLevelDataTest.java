package com.ideas.tetris.pacman.services.reportsquery.bookingsituationreport;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Created by idnrar on 10-10-2014.
 */
public class BookingSituationReportBusinessViewLevelDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private String dow1;
    private String dow2;
    private String dow3;
    private int propertyID = 6;
    private int recordTypeId = 3;
    private int processStatusId = 13;
    private int isRolling = 0;
    private int businessGroupId1;
    private int businessGroupId2;
    private int mktSeg1 = 7;
    private int mktSeg2 = 8;
    private int mktSeg3 = 9;
    private int mktSeg4 = 10;
    private String businessView1 = "BVTest1";
    private String businessView2 = "BVTest2";

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        createTestData();
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSeven();
        retrieveDayOfWeekForDateEqualToSystemDatePlusFive();

        populateActivityPaceForMarketSegment(insertQuery);
        populateActivityPaceForMarketSegmentWithPastData(insertQuery);
        populateActivityNonPaceForMarketSegmentWithPastData(insertQuery);
        populateBusinessViewData(insertQuery);
        populateSpecialEventData(insertQuery);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

        retrieveFirstBusinessGroupId();
        retrieveSecondBusinessGroupId();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSix() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(6) + "') as theDayName");
        dow1 = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSeven() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(7) + "') as theDayName");
        dow2 = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusFive() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(5) + "') as theDayName");
        dow3 = dowList.get(0).toString();
    }

    private void populateBusinessViewData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Business_Group]([Business_Group_Name],[Business_Group_Description],[Ranking] ");
        insertQuery.append(" ,[Status_ID],[Created_by_User_ID],[Last_Updated_by_User_ID] ");
        insertQuery.append(" ,[Last_Updated_DTTM],[Created_DTTM],[Property_ID]) ");
        insertQuery.append(" VALUES ('" + businessView1 + "','For Unit Test',1,1,1,1,GETDATE(),GETDATE()," + propertyID + "), ");
        insertQuery.append(" ('" + businessView2 + "','For Unit Test',2,1,1,1,GETDATE(),GETDATE()," + propertyID + ") ");
        insertQuery.append(" INSERT INTO [Mkt_Seg_Business_Group]([Business_Group_ID],[Mkt_Seg_ID],[Ranking],[Created_By_User_ID], ");
        insertQuery.append(" [Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM]) ");
        insertQuery.append(" VALUES ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView1 + "' ), ");
        insertQuery.append(" " + mktSeg1 + ",1,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView1 + "' ), ");
        insertQuery.append(" " + mktSeg2 + ",2,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ), ");
        insertQuery.append(" " + mktSeg3 + ",3,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ), ");
        insertQuery.append(" " + mktSeg4 + ",4,1,GETDATE(),1,GETDATE()) ");
    }

    private void retrieveFirstBusinessGroupId() {
        List FirstBusinessGroup = tenantCrudService().findByNativeQuery(" select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView1 + "' ");
        businessGroupId1 = (Integer) FirstBusinessGroup.get(0);
    }

    private void retrieveSecondBusinessGroupId() {
        List SecondBusinessGroup = tenantCrudService().findByNativeQuery(" select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ");
        businessGroupId2 = (Integer) SecondBusinessGroup.get(0);
    }

    private void populateSpecialEventData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,1,GETDATE(),1,'BSR_BV1','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");

        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(9).toString() + "',0,1,GETDATE(),1,'BSR_BV2','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(9).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");
    }

    private void populateActivityPaceForMarketSegment(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(7).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(14).toString() + "'," + mktSeg1 + ",39,14,33,4");
        insertQuery.append(" ,3,1256.16567,205.86843");
        insertQuery.append(" ,1527.28533,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(14).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,5,4563.56767,175.87333");
        insertQuery.append(" ,2357.47781,1,1,1,GETDATE())");


        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg3 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(7).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(14).toString() + "'," + mktSeg3 + ",34,13,32,1");
        insertQuery.append(" ,2,2345.56367,123.87343");
        insertQuery.append(" ,1526.25782,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(14).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,2,3567.68267,245.65243");
        insertQuery.append(" ,3276.35793,1,1,1,GETDATE())");
    }

    private void populateActivityNonPaceForMarketSegmentWithPastData(StringBuilder insertQuery) {

        insertQuery.append(" DELETE FROM [Mkt_Accom_Activity]");
        insertQuery.append(" WHERE Occupancy_DT = '" + startDate.plusDays(5).toString() + "'");
        insertQuery.append(" AND Mkt_Seg_ID IN (" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + ")");

        insertQuery.append(" DELETE FROM [Mkt_Accom_Activity]");
        insertQuery.append(" WHERE Occupancy_DT = '" + startDate.plusDays(8).toString() + "'");
        insertQuery.append(" AND Mkt_Seg_ID IN (" + mktSeg1 + "," + mktSeg2 + "," + mktSeg3 + "," + mktSeg4 + ")");

        insertQuery.append(" INSERT INTO [Mkt_Accom_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(5).toString() + "',GETDATE()");
        insertQuery.append(" ," + mktSeg1 + ",9,25,21,18,2,1");
        insertQuery.append(" ,1126.13567,226.76343");
        insertQuery.append(" ,1126.24563,1,GETDATE())");

        insertQuery.append(" INSERT INTO [Mkt_Accom_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(5).toString() + "',GETDATE()");
        insertQuery.append(" ," + mktSeg3 + ",9,30,21,18,2,1");
        insertQuery.append(" ,1127.13567,227.76343");
        insertQuery.append(" ,1127.24563,1,GETDATE())");

        insertQuery.append(" INSERT INTO [Mkt_Accom_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(8).toString() + "',GETDATE()");
        insertQuery.append(" ," + mktSeg2 + ",9,27,19,17,3,1");
        insertQuery.append(" ,1128.13567,228.76343");
        insertQuery.append(" ,1128.24563,1,GETDATE())");

        insertQuery.append(" INSERT INTO [Mkt_Accom_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(8).toString() + "',GETDATE()");
        insertQuery.append(" ," + mktSeg4 + ",9,30,21,18,2,1");
        insertQuery.append(" ,1129.13567,229.76343");
        insertQuery.append(" ,1129.24563,1,GETDATE())");
    }

    private void populateActivityPaceForMarketSegmentWithPastData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(6).toString() + "'," + mktSeg2 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(6).toString() + "'," + mktSeg4 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(7).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(6).toString() + "'," + mktSeg1 + ",39,14,33,4");
        insertQuery.append(" ,3,1256.16567,205.86843");
        insertQuery.append(" ,1527.28533,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(7).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(6).toString() + "'," + mktSeg3 + ",40,12,16,1");
        insertQuery.append(" ,5,4563.56767,175.87333");
        insertQuery.append(" ,2357.47781,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(8).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(8).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg3 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg2 + ",34,13,32,1");
        insertQuery.append(" ,2,2345.56367,123.87343");
        insertQuery.append(" ,1526.25782,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,2,3567.68267,245.65243");
        insertQuery.append(" ,3276.35793,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg1 + ",34,13,32,1");
        insertQuery.append(" ,2,2345.56367,123.87343");
        insertQuery.append(" ,1526.25782,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(8).toString() + "'," + mktSeg3 + ",39,13,13,4");
        insertQuery.append(" ,2,3567.68267,245.65243");
        insertQuery.append(" ,3276.35793,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(9).toString() + "'," + mktSeg2 + ",34,13,32,1");
        insertQuery.append(" ,2,2345.56367,123.87343");
        insertQuery.append(" ,1526.25782,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(9).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(9).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,2,3567.68267,245.65243");
        insertQuery.append(" ,3276.35793,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(9).toString() + "'," + mktSeg1 + ",34,13,32,1");
        insertQuery.append(" ,2,2345.56367,123.87343");
        insertQuery.append(" ,1526.25782,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(10).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.plusDays(9).toString() + "'," + mktSeg3 + ",39,13,13,4");
        insertQuery.append(" ,2,3567.68267,245.65243");
        insertQuery.append(" ,3276.35793,1,1,1,GETDATE())");
    }

    private LocalDate getLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + "," + recordTypeId + "," + processStatusId + ")");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForStaticDate() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(11).toString() + "'," +
                "'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.minusDays(14).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");
        assertBookingSituationReportDataAtBusinessViewLevel("Booking Situation at Business View level with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForRollingDate() {
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(11).toString() + "'," +
                "'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.minusDays(14).toString() + "'," +
                "" + isRolling + ",'TODAY+6','TODAY+7','TODAY-11','TODAY+9','TODAY+10','TODAY-14' ");
        assertBookingSituationReportDataAtBusinessViewLevel("Booking Situation at Business View level with Rolling Dates", reportDataByRollingDates);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForStaticDateWithPastData() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.plusDays(6).toString() + "'," +
                "'" + startDate.plusDays(8).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.plusDays(8).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");
        assertBookingSituationReportDataAtBusinessViewLevelWithPastData("Booking Situation at Business View level with Static Dates and past data ", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForStaticDateWithPastDataForComparisonDates() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(5).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.plusDays(6).toString() + "'," +
                "'" + startDate.plusDays(8).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.plusDays(9).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");
        assertBookingSituationReportDataAtBusinessViewLevelWithPastDataForComparisonDates("Booking Situation at Business View level with Static Dates and past data for comparison dates ", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionWhenRoomsSoldAndRevenueAndADRIs0() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "'," +
                "'" + startDate.toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(30).toString() + "'," +
                "'" + startDate.plusDays(2).toString() + "','" + startDate.plusDays(9).toString() + "','" + startDate.minusDays(29).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");
        for (Object[] record : reportDataByStaticDates) {
            assertTrue("0".equals(record[4].toString()));
            assertTrue("0".equals(record[5].toString()));
            assertTrue("0".equals(record[6].toString()));
            assertTrue("0.00000".equals(record[7].toString()));
            assertTrue("0.00000".equals(record[8].toString()));
            assertTrue("0.00000".equals(record[9].toString()));
            assertTrue("0.00000".equals(record[10].toString()));
            assertTrue("0.00000".equals(record[11].toString()));
            assertTrue("0.00000".equals(record[12].toString()));
        }
    }

    private void assertBookingSituationReportDataAtBusinessViewLevel(String Level, List<Object[]> reportDataByStaticDates) {

        validateFirstComparisonForFirstBusinessView(Level, reportDataByStaticDates);
        validateFirstComparisonForSecondBusinessView(Level, reportDataByStaticDates);
        validateSecondComparisonForFirstBusinessView(Level, reportDataByStaticDates);
        validateSecondComparisonForSecondBusinessView(Level, reportDataByStaticDates);

    }

    private void validateSecondComparisonForSecondBusinessView(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(3)[0].toString()), Level + " - DOW For Second Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(3)[1].toString()), Level + " - Business Group Name For Second Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(3)[2].toString()), Level + " - Second Occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(3)[3].toString()), Level + " - Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[4].toString()), Level + " - Rooms sold for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[5].toString()), Level + " - Rooms sold for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("0", (reportDataByStaticDates.get(3)[6].toString()), Level + " - Rooms sold variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("3756.48", String.format("%.2f", reportDataByStaticDates.get(3)[7]), Level + " - Rooms Revenue for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(3)[8]), Level + " - Rooms Revenue for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("188.80", String.format("%.2f", reportDataByStaticDates.get(3)[9]), Level + " - Rooms Revenue variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("96.32", String.format("%.2f", reportDataByStaticDates.get(3)[10]), Level + " - ADR for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(3)[11]), Level + " - ADR for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("4.84", String.format("%.2f", reportDataByStaticDates.get(3)[12]), Level + " - ADR variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
    }

    private void validateSecondComparisonForFirstBusinessView(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW For Second Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(2)[1].toString()), Level + " - Business Group Name For Second Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(2)[2].toString()), Level + " - Second Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(2)[3].toString()), Level + " - Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(2)[4].toString()), Level + " - Rooms sold for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(2)[5].toString()), Level + " - Rooms sold for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(2)[6].toString()), Level + " - Rooms sold variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3125.44", String.format("%.2f", reportDataByStaticDates.get(2)[7]), Level + " - Rooms Revenue for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("4563.57", String.format("%.2f", reportDataByStaticDates.get(2)[8]), Level + " - Rooms Revenue for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1438.13", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Rooms Revenue variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("89.30", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - ADR for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("130.39", String.format("%.2f", reportDataByStaticDates.get(2)[11]), Level + " - ADR for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-41.09", String.format("%.2f", reportDataByStaticDates.get(2)[12]), Level + " - ADR variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForFirstBusinessView(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(0)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(0)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-14", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1256.17", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-131.03", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("32.21", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("12.80", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(0)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(0)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForSecondBusinessView(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(1)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(1)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(1)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("25", (reportDataByStaticDates.get(1)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("34", (reportDataByStaticDates.get(1)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-9", (reportDataByStaticDates.get(1)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-1220.43", String.format("%.2f", reportDataByStaticDates.get(1)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(1)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-23.98", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(1)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(1)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
    }

    private void assertBookingSituationReportDataAtBusinessViewLevelWithPastData(String Level, List<Object[]> reportDataByStaticDates) {

        validateFirstComparisonForFirstBusinessViewWithPastData(Level, reportDataByStaticDates);
        validateFirstComparisonForSecondBusinessViewWithPastData(Level, reportDataByStaticDates);
        validateSecondComparisonForFirstBusinessViewWithPastData(Level, reportDataByStaticDates);
        validateSecondComparisonForSecondBusinessViewWithPastData(Level, reportDataByStaticDates);
        validateThirdComparisonForFirstBusinessViewWithPastData(Level, reportDataByStaticDates);
        validateThirdComparisonForSecondBusinessViewWithPastData(Level, reportDataByStaticDates);
    }

    private void validateFirstComparisonForFirstBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow3, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(0)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(5).toString(), (reportDataByStaticDates.get(0)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(0)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1126.14", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.05", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("0.04", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForSecondBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow3, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(1)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(5).toString(), (reportDataByStaticDates.get(1)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(1)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("30", (reportDataByStaticDates.get(1)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(1)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-9", (reportDataByStaticDates.get(1)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1127.14", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3756.48", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2629.34", String.format("%.2f", reportDataByStaticDates.get(1)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("37.57", String.format("%.2f", reportDataByStaticDates.get(1)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("96.32", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-58.75", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateSecondComparisonForFirstBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(2)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(2)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(2)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(2)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("34", (reportDataByStaticDates.get(2)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-9", (reportDataByStaticDates.get(2)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(2)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(2)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1220.43", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(2)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-23.98", String.format("%.2f", reportDataByStaticDates.get(2)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(2)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(2)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateSecondComparisonForSecondBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(3)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(3)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(3)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(3)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(3)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-4", (reportDataByStaticDates.get(3)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3125.44", String.format("%.2f", reportDataByStaticDates.get(3)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(3)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-442.25", String.format("%.2f", reportDataByStaticDates.get(3)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("89.30", String.format("%.2f", reportDataByStaticDates.get(3)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(3)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2.18", String.format("%.2f", reportDataByStaticDates.get(3)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(3)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(3)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateThirdComparisonForFirstBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(4)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(4)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(4)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(4)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(4)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("34", (reportDataByStaticDates.get(4)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("5", (reportDataByStaticDates.get(4)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1256.17", String.format("%.2f", reportDataByStaticDates.get(4)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(4)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1089.40", String.format("%.2f", reportDataByStaticDates.get(4)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("32.21", String.format("%.2f", reportDataByStaticDates.get(4)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(4)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-36.78", String.format("%.2f", reportDataByStaticDates.get(4)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateThirdComparisonForSecondBusinessViewWithPastData(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(5)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(5)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(5)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(5)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("40", (reportDataByStaticDates.get(5)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(5)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1", (reportDataByStaticDates.get(5)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("4563.57", String.format("%.2f", reportDataByStaticDates.get(5)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(5)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("995.89", String.format("%.2f", reportDataByStaticDates.get(5)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("114.09", String.format("%.2f", reportDataByStaticDates.get(5)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(5)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("22.61", String.format("%.2f", reportDataByStaticDates.get(5)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void assertBookingSituationReportDataAtBusinessViewLevelWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {

        validateFirstComparisonForFirstBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
        validateFirstComparisonForSecondBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
        validateSecondComparisonForFirstBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
        validateSecondComparisonForSecondBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
        validateThirdComparisonForFirstBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
        validateThirdComparisonForSecondBusinessViewWithPastDataForComparisonDates(Level, reportDataByStaticDates);
    }

    private void validateFirstComparisonForFirstBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow3, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(0)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(5).toString(), (reportDataByStaticDates.get(0)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(0)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("27", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1126.14", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1128.14", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.05", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("41.78", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3.26", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForSecondBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow3, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(1)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(5).toString(), (reportDataByStaticDates.get(1)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(1)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("30", (reportDataByStaticDates.get(1)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("30", (reportDataByStaticDates.get(1)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(1)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1127.14", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1129.14", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2.00", String.format("%.2f", reportDataByStaticDates.get(1)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("37.57", String.format("%.2f", reportDataByStaticDates.get(1)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("37.64", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-0.07", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateSecondComparisonForFirstBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(2)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(2)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(2)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(2)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("34", (reportDataByStaticDates.get(2)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-9", (reportDataByStaticDates.get(2)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(2)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(2)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1220.43", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(2)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-23.98", String.format("%.2f", reportDataByStaticDates.get(2)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(2)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(2)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateSecondComparisonForSecondBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(3)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(3)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(3)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(3)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(3)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-4", (reportDataByStaticDates.get(3)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3125.44", String.format("%.2f", reportDataByStaticDates.get(3)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(3)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-442.25", String.format("%.2f", reportDataByStaticDates.get(3)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("89.30", String.format("%.2f", reportDataByStaticDates.get(3)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(3)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-2.18", String.format("%.2f", reportDataByStaticDates.get(3)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(3)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(3)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateThirdComparisonForFirstBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(4)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(4)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(4)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(4)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(4)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("34", (reportDataByStaticDates.get(4)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("5", (reportDataByStaticDates.get(4)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1256.17", String.format("%.2f", reportDataByStaticDates.get(4)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(4)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1089.40", String.format("%.2f", reportDataByStaticDates.get(4)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("32.21", String.format("%.2f", reportDataByStaticDates.get(4)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(4)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-36.78", String.format("%.2f", reportDataByStaticDates.get(4)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateThirdComparisonForSecondBusinessViewWithPastDataForComparisonDates(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(5)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(5)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(5)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(5)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("40", (reportDataByStaticDates.get(5)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(5)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1", (reportDataByStaticDates.get(5)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("4563.57", String.format("%.2f", reportDataByStaticDates.get(5)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(5)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("995.89", String.format("%.2f", reportDataByStaticDates.get(5)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("114.09", String.format("%.2f", reportDataByStaticDates.get(5)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(5)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("22.61", String.format("%.2f", reportDataByStaticDates.get(5)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    @Test
    public void shouldValidateBookingSituationReportForecastGroupLevelFunctionForRollingDateOfSOMAndEOM_ForBusinessView1() {
        isRolling = 1;
        LocalDate analysisStartDate = startDate.withDayOfMonth(1);
        LocalDate analysisEndDate = startDate.dayOfMonth().withMaximumValue();
        LocalDate analysisAsOfDate = startDate.minusDays(40);
        LocalDate comparisonStartDate = startDate.minusYears(1).withDayOfMonth(1);
        LocalDate comparisonEndDate = startDate.minusYears(1).dayOfMonth().withMaximumValue();
        LocalDate comparisonAsOfDate = startDate.minusDays(364 + 40);

        //analysis period
        populateMonthsData(analysisStartDate, analysisEndDate, analysisAsOfDate, mktSeg1);
        populateMonthsData(analysisStartDate, analysisEndDate, analysisAsOfDate, mktSeg2);

        //comparison period
        populateMonthsData(comparisonStartDate, comparisonEndDate, comparisonAsOfDate, mktSeg1);
        populateMonthsData(comparisonStartDate, comparisonEndDate, comparisonAsOfDate, mktSeg2);

        String queryStr = "exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "'," +
                "'" + analysisStartDate.toString() + "','" + analysisEndDate.toString() + "','" + analysisAsOfDate.toString() + "'," +
                "'" + comparisonStartDate.toString() + "','" + comparisonEndDate.toString() + "','" + comparisonAsOfDate.toString() + "'," +
                +isRolling + ",'START_OF_MONTH','END_OF_MONTH','LAST_UPDATED-39','LY_START_OF_MONTH','LY_END_OF_MONTH','LY_LAST_UPDATED-39' ";
        List<Object[]> reportData = tenantCrudService().findByNativeQuery(queryStr);

        int daysInAnalysisPeriod = Days.daysBetween(analysisStartDate, analysisEndDate).getDays() + 1;
        int daysInComparisonPeriod = Days.daysBetween(comparisonStartDate, comparisonEndDate).getDays() + 1;
        assertBookingSituationReportDataBusinessViewLevelForStartAndEndOfMonth("Booking Situation Report at BVTest1 with Rolling Dates For start and end of month", reportData,
                analysisStartDate, comparisonStartDate, daysInAnalysisPeriod, daysInComparisonPeriod, "BVTest1");
    }

    @Test
    public void shouldValidateBookingSituationReportForecastGroupLevelFunctionForRollingDateOfSOMAndEOM_ForBusinessView2() {
        isRolling = 1;
        LocalDate analysisStartDate = startDate.withDayOfMonth(1);
        LocalDate analysisEndDate = startDate.dayOfMonth().withMaximumValue();
        LocalDate analysisAsOfDate = startDate.minusDays(40);
        LocalDate comparisonStartDate = startDate.minusYears(1).withDayOfMonth(1);
        LocalDate comparisonEndDate = startDate.minusYears(1).dayOfMonth().withMaximumValue();
        LocalDate comparisonAsOfDate = startDate.minusDays(364 + 40);

        //analysis period
        populateMonthsData(analysisStartDate, analysisEndDate, analysisAsOfDate, mktSeg3);
        populateMonthsData(analysisStartDate, analysisEndDate, analysisAsOfDate, mktSeg4);

        //comparison period
        populateMonthsData(comparisonStartDate, comparisonEndDate, comparisonAsOfDate, mktSeg3);
        populateMonthsData(comparisonStartDate, comparisonEndDate, comparisonAsOfDate, mktSeg4);

        String queryStr = "exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId2 + "'," +
                "'" + analysisStartDate.toString() + "','" + analysisEndDate.toString() + "','" + analysisAsOfDate.toString() + "'," +
                "'" + comparisonStartDate.toString() + "','" + comparisonEndDate.toString() + "','" + comparisonAsOfDate.toString() + "'," +
                +isRolling + ",'START_OF_MONTH','END_OF_MONTH','LAST_UPDATED-39','LY_START_OF_MONTH','LY_END_OF_MONTH','LY_LAST_UPDATED-39' ";
        List<Object[]> reportData = tenantCrudService().findByNativeQuery(queryStr);

        int daysInAnalysisPeriod = Days.daysBetween(analysisStartDate, analysisEndDate).getDays() + 1;
        int daysInComparisonPeriod = Days.daysBetween(comparisonStartDate, comparisonEndDate).getDays() + 1;
        assertBookingSituationReportDataBusinessViewLevelForStartAndEndOfMonth("Booking Situation Report at BVTest1 with Rolling Dates For start and end of month", reportData,
                analysisStartDate, comparisonStartDate, daysInAnalysisPeriod, daysInComparisonPeriod, "BVTest2");
    }

    private int retrieveDayOfWeekIndexForDate(LocalDate date) {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATEPART(dw,'" + date + "') -1 as theDayIndex");
        return (int) dowList.get(0);
    }

    private void assertBookingSituationReportDataBusinessViewLevelForStartAndEndOfMonth(String Level, List<Object[]> reportData, LocalDate analysisStartOfDate, LocalDate comparisonStartOfDate, int daysInAnalysisPeriod, int daysInComparisonPeriod, String businessViewName) {
        assertEquals(daysInAnalysisPeriod, reportData.size());
        int dayIndex = retrieveDayOfWeekIndexForDate(analysisStartOfDate);
        int index = 0;
        for (; index < reportData.size(); index++, dayIndex++) {
            boolean isComparisonDataPresent = index < daysInComparisonPeriod;
            assertEquals(businessViewName, reportData.get(index)[1].toString(), Level + " " + index + " - Business View Name");
            assertEquals(analysisStartOfDate.plusDays(index).toString(), (reportData.get(index)[2].toString()), Level + " " + index + " - Occupancy Date ");
            assertEquals("50", (reportData.get(index)[4].toString()), Level + " " + index + " - Rooms sold for occupancy Date ");
            assertEquals("2250.27134", (reportData.get(index)[7].toString()), Level + " " + index + " - Rooms Revenue for occupancy Date ");
            assertEquals("45.01", String.format("%.2f", reportData.get(index)[10]), Level + " " + index + " - ADR for occupancy Date ");
            if (isComparisonDataPresent) {
                assertEquals(reportData.get(index)[0].toString(), DOW.getDOWBasedOnIndex(dayIndex), Level + " " + index + " - DOW ");
                assertEquals(comparisonStartOfDate.plusDays(index).toString(), (reportData.get(index)[3].toString()), Level + " " + index + " - Comparison Date ");
                assertEquals("50", (reportData.get(index)[5].toString()), Level + " " + index + " - Rooms sold for Comparison Date ");
                assertEquals("0", (reportData.get(index)[6].toString()), Level + " " + index + " - Rooms sold variance between occupancy date and Comparison Date ");
                assertEquals("2250.27134", (reportData.get(index)[8].toString()), Level + " " + index + " - Rooms Revenue for Comparison Date ");
                assertEquals("0.00", String.format("%.2f", reportData.get(index)[9]), Level + " " + index + " - Rooms Revenue variance between occupancy date and Comparison Date ");
                assertEquals("45.01", String.format("%.2f", reportData.get(index)[11]), Level + " " + index + " - ADR for Comparison Date ");
                assertEquals("0.00", String.format("%.2f", reportData.get(index)[12]), Level + " " + index + " - ADR variance between occupancy date and Comparison Date ");
            }
        }
    }

    private void populateMonthsData(LocalDate startDate, LocalDate endDate, LocalDate asOfDate, int mktSegId) {
        int noOfDaysInThisMonth = startDate.dayOfMonth().getMaximumValue();
        StringBuilder insertQuery = new StringBuilder();

        String Rooms_Sold, Room_Revenue, Food_Revenue, Total_Revenue, Arrivals, Departures, Cancellations, No_Shows;

        Rooms_Sold = "25";
        Room_Revenue = "1125.13567";
        Food_Revenue = "225.76343";
        Total_Revenue = "1125.24563";
        Arrivals = "21";
        Departures = "18";
        Cancellations = "2";
        No_Shows = "1";

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]")
                .append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]")
                .append(",[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]")
                .append(",[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM]) VALUES \n");

        for (int day = 0; day < noOfDaysInThisMonth; day++) {

            insertQuery.append("(").append(propertyID)
                    .append(",'").append(startDate.plusDays(day).toString()).append("'")
                    .append(",GETDATE()")
                    .append(",'").append(asOfDate.toString()).append("'")
                    .append(",").append(mktSegId)
                    .append(",").append(Rooms_Sold)
                    .append(",").append(Arrivals)
                    .append(",").append(Departures)
                    .append(",").append(Cancellations)
                    .append(",").append(No_Shows)
                    .append(",").append(Room_Revenue)
                    .append(",").append(Food_Revenue)
                    .append(",").append(Total_Revenue)
                    .append(",").append(1)
                    .append(",").append(1)
                    .append(",").append(1)
                    .append(",GETDATE()),\n");
        }
        insertQuery.deleteCharAt(insertQuery.lastIndexOf(","));
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForStaticDateWithPastSoldDetails() {

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(11).toString() + "'," +
                "'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.minusDays(14).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");
        assertBookingSituationReportDataAtBusinessViewLevelWithPastSoldDetails("Booking Situation at Business View level with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForRollingDateWithPastSoldDetails() {

        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(11).toString() + "'," +
                "'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.minusDays(14).toString() + "'," +
                "" + isRolling + ",'TODAY+6','TODAY+7','TODAY-11','TODAY+9','TODAY+10','TODAY-14' ");
        assertBookingSituationReportDataAtBusinessViewLevelWithPastSoldDetails("Booking Situation at Business View level with Rolling Dates", reportDataByRollingDates);
    }

    private void assertBookingSituationReportDataAtBusinessViewLevelWithPastSoldDetails(String Level, List<Object[]> reportDataByStaticDates) {
        validateFirstComparisonForFirstBusinessViewWithPastSoldDetails(Level, reportDataByStaticDates);
        validateFirstComparisonForSecondBusinessViewWithPastSoldDetails(Level, reportDataByStaticDates);
        validateSecondComparisonForFirstBusinessViewWithPastSoldDetails(Level, reportDataByStaticDates);
        validateSecondComparisonForSecondBusinessViewWithPastSoldDetails(Level, reportDataByStaticDates);

    }

    private void validateSecondComparisonForSecondBusinessViewWithPastSoldDetails(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(3)[0].toString()), Level + " - DOW For Second Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(3)[1].toString()), Level + " - Business Group Name For Second Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(3)[2].toString()), Level + " - Second Occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(3)[3].toString()), Level + " - Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[4].toString()), Level + " - Rooms sold for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("39", (reportDataByStaticDates.get(3)[5].toString()), Level + " - Rooms sold for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("0", (reportDataByStaticDates.get(3)[6].toString()), Level + " - Rooms sold variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("3756.48", String.format("%.2f", reportDataByStaticDates.get(3)[7]), Level + " - Rooms Revenue for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("3567.68", String.format("%.2f", reportDataByStaticDates.get(3)[8]), Level + " - Rooms Revenue for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("188.80", String.format("%.2f", reportDataByStaticDates.get(3)[9]), Level + " - Rooms Revenue variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("96.32", String.format("%.2f", reportDataByStaticDates.get(3)[10]), Level + " - ADR for Second occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("91.48", String.format("%.2f", reportDataByStaticDates.get(3)[11]), Level + " - ADR for Second Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("4.84", String.format("%.2f", reportDataByStaticDates.get(3)[12]), Level + " - ADR variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
    }

    private void validateSecondComparisonForFirstBusinessViewWithPastSoldDetails(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow2, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW For Second Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(2)[1].toString()), Level + " - Business Group Name For Second Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(2)[2].toString()), Level + " - Second Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(10).toString(), (reportDataByStaticDates.get(2)[3].toString()), Level + " - Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(2)[4].toString()), Level + " - Rooms sold for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(2)[5].toString()), Level + " - Rooms sold for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("0", (reportDataByStaticDates.get(2)[6].toString()), Level + " - Rooms sold variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("3125.44", String.format("%.2f", reportDataByStaticDates.get(2)[7]), Level + " - Rooms Revenue for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("4563.57", String.format("%.2f", reportDataByStaticDates.get(2)[8]), Level + " - Rooms Revenue for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-1438.13", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Rooms Revenue variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("89.30", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - ADR for Second occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("130.39", String.format("%.2f", reportDataByStaticDates.get(2)[11]), Level + " - ADR for Second Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-41.09", String.format("%.2f", reportDataByStaticDates.get(2)[12]), Level + " - ADR variance between Second occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForFirstBusinessViewWithPastSoldDetails(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(0)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(0)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-14", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1256.17", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-131.03", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("32.21", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("12.80", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(0)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(0)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

    private void validateFirstComparisonForSecondBusinessViewWithPastSoldDetails(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BVTest2", (reportDataByStaticDates.get(1)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(1)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(1)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("25", (reportDataByStaticDates.get(1)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("34", (reportDataByStaticDates.get(1)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-9", (reportDataByStaticDates.get(1)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("2345.56", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-1220.43", String.format("%.2f", reportDataByStaticDates.get(1)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(1)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("68.99", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("-23.98", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BSR_BV1", (reportDataByStaticDates.get(1)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId2 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(1)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId2 + " ");
    }

    @Test
    public void shouldValidateBookingSituationReportBusinessViewLevelFunctionForSpecialEventNameWithInstanceName() {
        List<PropertySpecialEvent> propertySpecialEvents = getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), "BSR_BV1");
        PropertySpecialEvent propertySpecialEvent = propertySpecialEvents.get(0);
        PropertySpecialEventInstance propertySpecialEventInstance = propertySpecialEvent.getPropertySpecialEventIntances().iterator().next();
        propertySpecialEventInstance.setEventInstanceName("Test Instance");
        tenantCrudService().save(propertySpecialEvent);

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " + propertyID + ",'" + businessGroupId1 + "," + businessGroupId2 + "'," +
                "'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(7).toString() + "','" + startDate.minusDays(11).toString() + "'," +
                "'" + startDate.plusDays(9).toString() + "','" + startDate.plusDays(10).toString() + "','" + startDate.minusDays(14).toString() + "'," +
                "" + isRolling + ",'','','','','','' ");

        validateDataForSpecialEventNameWithInstanceName("Booking Situation at Business View level with Static Dates", reportDataByStaticDates);
    }

    private List<PropertySpecialEvent> getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(String eventStartDate, String eventEndDate, String specialEventName) {
        String selectQuery = "select Property_Special_Event_ID from [Property_Special_Event] WHERE [Start_DTTM] = :Start_DTTM and [End_DTTM] = :End_DTTM and [Special_Event_Name] = :Special_Event_Name";
        List<Object> specialEventsIds = tenantCrudService().findByNativeQuery(selectQuery,
                MapBuilder.with("Start_DTTM", eventStartDate)
                        .and("End_DTTM", eventEndDate)
                        .and("Special_Event_Name", specialEventName)
                        .get());

        Integer specialEventId = (Integer) specialEventsIds.get(0);
        return tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY, MapBuilder.with("propertyId", 6).and("specialEventId", specialEventId).get());
    }

    private void validateDataForSpecialEventNameWithInstanceName(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(dow1, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BVTest1", (reportDataByStaticDates.get(0)[1].toString()), Level + " - Business Group Name For First Comparison of Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[2].toString()), Level + " - First Occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals(startDate.plusDays(9).toString(), (reportDataByStaticDates.get(0)[3].toString()), Level + " - First Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("25", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Rooms sold for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("39", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Rooms sold for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-14", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Rooms sold variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1125.14", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Rooms Revenue for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("1256.17", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Rooms Revenue for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("-131.03", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Rooms Revenue variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("45.01", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - ADR for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("32.21", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - ADR for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("12.80", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - ADR variance between first occupancy date and Comparison Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV1 - Test Instance", (reportDataByStaticDates.get(0)[13].toString()), Level + " - Special Event for first occupancy Date with Business Group ID as " + businessGroupId1 + " ");
        assertEquals("BSR_BV2", (reportDataByStaticDates.get(0)[14].toString()), Level + " - Special Event for first Comparison Date with Business Group ID as " + businessGroupId1 + " ");
    }

}
