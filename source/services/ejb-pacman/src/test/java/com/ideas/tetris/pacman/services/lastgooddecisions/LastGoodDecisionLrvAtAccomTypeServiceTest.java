package com.ideas.tetris.pacman.services.lastgooddecisions;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisiondelivery.LastRoomValueDecisionService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_BDE;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsServiceTest.COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsServiceTest.ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsServiceTest.QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_TIME_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LastGoodDecisionLrvAtAccomTypeServiceTest extends AbstractG3JupiterTest {

    private static final String DECISION_LRV_COLUMNS = "Decision_LRV_AT_ID,Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM";

    private static final String PACE_LRV_COLUMNS = "PACE_LRV_AT_ID,Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM";

    private static final String LRV_AT_ROOM_TYPE = "select id,decisionId,propertyID,accomTypeID,occupancyDate,(value+(value * :taxFactor)) as value,  createDate " +
            " from LastRoomValueAccomType as lrv where propertyID=:propertyId and occupancyDate >= :startDate and occupancyDate <= :endDate";

    public static final String LRV_AT_ROOM_TYPE_FOR_HTNG = "select dat.Decision_LRV_AT_ID,dat.Decision_ID,dat.Property_ID,dat.Occupancy_DT,\n" +
            "(dat.LRV +(dat.LRV * :taxFactor)) as value ,dac.Accom_Class_ID, dat.Accom_Type_ID , \n" +
            "(dac.Delta_Value + (dac.Delta_Value * :taxFactor)) as deltaValue,\n" +
            "dac.Ceiling_Value,dat.CreateDate_DTTM from Decision_LRV_AT dat inner join Accom_Type at\n" +
            "on dat.Property_ID = at.Property_ID and dat.Accom_Type_ID = at.Accom_Type_ID\n" +
            "inner join Decision_LRV dac on dat.Property_ID = dac.Property_ID \n" +
            "and dat.Occupancy_DT = dac.Occupancy_DT and at.Accom_Class_ID = dac.Accom_Class_ID\n" +
            "where dat.Property_ID = :propertyId and dat.Occupancy_DT >= :startDate and dat.Occupancy_DT <= :endDate";

    @Mock
    protected PacmanConfigParamsService configService;

    @Mock
    DateService dateService;

    @InjectMocks
    LastGoodDecisionLrvAtAccomTypeService lastGoodDecisionLrvAtAccomTypeService;

    LastRoomValueDecisionService lastRoomValueDecisionService;

    private int propertyId;

    private String NonPaceTable = "Decision_LRV_AT";

    private String PaceTable = "Pace_LRV_AT";

    public static final String YYYY_MM_DD = "YYYY-MM-dd";

    public static final LocalDate NOW = new LocalDate("2018-12-15");

    public static final String START_DATE = DateUtil.formatDate(NOW.toDate(), YYYY_MM_DD);

    public static final String END_DATE = DateUtil.formatDate(NOW.plusDays(5).toDate(), YYYY_MM_DD);

    private static final String FIFTH_DAY = DateUtil.formatDate(NOW.plusDays(4).toDate(), DATE_TIME_FORMAT);

    private static final String FOURTH_DAY = DateUtil.formatDate(NOW.plusDays(3).toDate(), DATE_TIME_FORMAT);

    private static final String THIRD_DAY = DateUtil.formatDate(NOW.plusDays(2).toDate(), DATE_TIME_FORMAT);

    private static final String SECOND_DAY = DateUtil.formatDate(NOW.plusDays(1).toDate(), DATE_TIME_FORMAT);

    private static final String FIRST_DAY = DateUtil.formatDate(NOW.toDate(), DATE_TIME_FORMAT);

    public static final int ONE_MINUTE = 1000 * 60;

    private static final String INSERT_QUERY_COLUMNS = " (Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM) values( ";

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        propertyId = TestProperty.H2.getId();
        lastRoomValueDecisionService = new LastRoomValueDecisionService();
        lastRoomValueDecisionService.setCrudService(tenantCrudService());
        lastGoodDecisionLrvAtAccomTypeService.crudService = tenantCrudService();
        lastGoodDecisionLrvAtAccomTypeService.dateService = dateService;

        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(configService);
        lastRoomValueDecisionService.setConfigParameterNameService(configParameterNameService);
    }

    @Test
    public void shouldSendAsOfDecisions() {
        List<Integer> roomTypeIds = Arrays.asList(9, 10);
        final Date today = new Date();
        final Date businessDate = today;
        cleanUpTables(roomTypeIds, true);
        // 1st time decision created
        final LocalDateTime firstDecisionCreationTime = LocalDateTime.now();
        final Decision decision1st = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BDE, firstDecisionCreationTime.toDate());
        createDecision(businessDate, decision1st.getId(), BigDecimal.valueOf(10), roomTypeIds);
        LocalDateTime firstDecisionUploadedTime = firstDecisionCreationTime.plusMinutes(4);
        // 2nd time decision created n uploaded
        final LocalDateTime secondDecisionCreationTime = firstDecisionUploadedTime.plusHours(2);
        final Decision decision2nd = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BDE, secondDecisionCreationTime.toDate());
        roomTypeIds = Arrays.asList(10);
        cleanUpTables(roomTypeIds, false);
        createDecision(businessDate, decision2nd.getId(), BigDecimal.valueOf(20), roomTypeIds);
        // check first differential
        List<LastRoomValueAccomType> lrvAtATDecisions = lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions("hbsi", businessDate, businessDate, false);
        compareResults(lrvAtATDecisions, 10, 20);
        // Give call for asOFDate decisions and verify differential data after that
        LocalDateTime asOfDecisionCreationTime = secondDecisionCreationTime.plusHours(2);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(LocalDate.fromDateFields(businessDate).toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(LocalDate.fromDateFields(businessDate).toDate());
        final Decision newDecisionIDForAsOF = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BDE, asOfDecisionCreationTime.toDate());
        lastGoodDecisionLrvAtAccomTypeService.createLastGoodDecisions(decision1st.getId(), newDecisionIDForAsOF.getId());
        lrvAtATDecisions = lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions("hbsi", businessDate, businessDate, false);
        compareResults(lrvAtATDecisions, 10, 10);
        // 3rd Day - regular differential call after AsOfDate
        final Decision decision3rd = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BDE, asOfDecisionCreationTime.plusHours(4).toDate());
        cleanUpTables(roomTypeIds, false);
        createDecision(businessDate, decision3rd.getId(), BigDecimal.valueOf(30), roomTypeIds);
        lrvAtATDecisions = lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions("hbsi", businessDate, businessDate, false);
        compareResults(lrvAtATDecisions, 10, 30);
    }

    @Test
    public void testIfAsOfDecisionsGeneratedAsPerRequiredDates() throws Exception {
        cleanPaceAndNonPaceTables();
        Date arrivalStartDate = LocalDate.parse(START_DATE).toDate();
        Date arrivalEndDate = LocalDate.parse(END_DATE).toDate();
        final Date businessDate = new Date();
        final Decision decisionId4 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -4), DECISION_TYPE_BDE);
        final Decision decisionId3 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -3), DECISION_TYPE_BDE);
        final Decision decisionId2 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -2), DECISION_TYPE_BDE);
        final Decision decisionId1 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -1), DECISION_TYPE_BDE);
        final Decision businessDateDecision = UniqueDecisionCreator.createDecisionFor(propertyId, businessDate, DECISION_TYPE_BDE, businessDate);
        prepareDataForValidatingLRV(decisionId4, decisionId3, decisionId2, decisionId1, businessDateDecision);
        final Date businessDate1 = new Date(businessDate.getTime() + ONE_MINUTE);
        Decision newDecisionId = UniqueDecisionCreator.createDecisionFor(propertyId, businessDate, DECISION_TYPE_BDE, businessDate1);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(arrivalStartDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(arrivalEndDate);
        lastGoodDecisionLrvAtAccomTypeService.createLastGoodDecisions(decisionId2.getId(), newDecisionId.getId());
        List<LastRoomValueAccomType> lastRoomValues = lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions("hbsi", arrivalStartDate, arrivalEndDate, false);
        lastRoomValues = lastRoomValues.stream().sorted(Comparator.comparing(LastRoomValueAccomType::getOccupancyDate)).collect(Collectors.toList());
        Assertions.assertEquals(6, lastRoomValues.size());
        Assertions.assertEquals(START_DATE.split(" ")[0], lastRoomValues.get(0).getOccupancyDate().toString());
        Assertions.assertEquals(SECOND_DAY.split(" ")[0], lastRoomValues.get(1).getOccupancyDate().toString());
        Assertions.assertEquals(THIRD_DAY.split(" ")[0], lastRoomValues.get(2).getOccupancyDate().toString());
        Assertions.assertEquals(FOURTH_DAY.split(" ")[0], lastRoomValues.get(3).getOccupancyDate().toString());
        Assertions.assertEquals(FIFTH_DAY.split(" ")[0], lastRoomValues.get(4).getOccupancyDate().toString());
        Assertions.assertEquals(END_DATE.split(" ")[0], lastRoomValues.get(5).getOccupancyDate().toString());
        Assertions.assertEquals(new BigDecimal("17.000000"), lastRoomValues.get(0).getValue());
        Assertions.assertEquals(new BigDecimal("16.000000"), lastRoomValues.get(1).getValue());
        Assertions.assertEquals(new BigDecimal("15.000000"), lastRoomValues.get(2).getValue());
        Assertions.assertEquals(new BigDecimal("12.000000"), lastRoomValues.get(3).getValue());
        Assertions.assertEquals(new BigDecimal("12.000000"), lastRoomValues.get(4).getValue());
        Assertions.assertEquals(new BigDecimal("10.000000"), lastRoomValues.get(5).getValue());
    }

    private void prepareDataForValidatingLRV(Decision decisionId4, Decision decisionId3, Decision decisionId2, Decision decisionId1, Decision businessDateDecision) {
        populatePaceDecisionDailyBarData(decisionId2, decisionId3, decisionId4);
        populatePaceAndNonPaceData(decisionId1, decisionId3, businessDateDecision);
    }

    private void populatePaceAndNonPaceData(Decision decisionId1, Decision decisionId3, Decision businessDateDecision) {
        List<String> tableList = Arrays.asList(PaceTable, NonPaceTable);
        tableList.stream().forEach(tableName -> {
            insertRecordForTable(decisionId1, decisionId3, businessDateDecision, tableName);
        });
    }

    private void insertRecordForTable(Decision decisionId1, Decision decisionId3, Decision businessDateDecision, String tableName) {
        StringBuilder sixthDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        sixthDayQuery1.append(decisionId3.getId() + "," + propertyId + ",9,'" + END_DATE + "',10,'" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(sixthDayQuery1.toString());
        StringBuilder fifthDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        fifthDayQuery1.append(decisionId1.getId() + "," + propertyId + ",10,'" + FIFTH_DAY + "',11,'" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fifthDayQuery1.toString());
        StringBuilder fourthDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        fourthDayQuery1.append(decisionId3.getId() + "," + propertyId + ",9,'" + FOURTH_DAY + "',12,'" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fourthDayQuery1.toString());
        StringBuilder thirdDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        thirdDayQuery1.append(decisionId1.getId() + "," + propertyId + ",10,'" + THIRD_DAY + "',114,'" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery1.toString());
        StringBuilder secondDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        secondDayQuery1.append(decisionId1.getId() + "," + propertyId + ",9,'" + SECOND_DAY + "',121,'" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(secondDayQuery1.toString());
        StringBuilder firstDayQuery1 = new StringBuilder("insert into " + tableName + INSERT_QUERY_COLUMNS);
        firstDayQuery1.append(businessDateDecision.getId() + "," + propertyId + ",10,'" + FIRST_DAY + "',176,'" + DateUtil.formatDate(businessDateDecision.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(firstDayQuery1.toString());
    }

    private void populatePaceDecisionDailyBarData(Decision decisionId2, Decision decisionId3, Decision decisionId4) {
        StringBuilder sixthDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        sixthDayQuery1.append(decisionId4.getId() + "," + propertyId + ",9,'" + END_DATE + "',11,'" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(sixthDayQuery1.toString());
        StringBuilder fifthDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        fifthDayQuery1.append(decisionId2.getId() + "," + propertyId + ",10,'" + FIFTH_DAY + "',12,'" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fifthDayQuery1.toString());
        StringBuilder fourthDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        fourthDayQuery1.append(decisionId4.getId() + "," + propertyId + ",9,'" + FOURTH_DAY + "',13,'" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fourthDayQuery1.toString());
        StringBuilder thirdDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        thirdDayQuery1.append(decisionId3.getId() + "," + propertyId + ",10,'" + THIRD_DAY + "',15,'" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery1.toString());
        StringBuilder thirdDayQuery2 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        thirdDayQuery2.append(decisionId4.getId() + "," + propertyId + ",10,'" + THIRD_DAY + "',14,'" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery2.toString());
        StringBuilder secondDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        secondDayQuery1.append(decisionId2.getId() + "," + propertyId + ",9,'" + SECOND_DAY + "',16,'" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(secondDayQuery1.toString());
        StringBuilder firstDayQuery1 = new StringBuilder("insert into " + PaceTable + INSERT_QUERY_COLUMNS);
        firstDayQuery1.append(decisionId2.getId() + "," + propertyId + ",10,'" + FIRST_DAY + "',17,'" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(firstDayQuery1.toString());
    }

    private void cleanPaceAndNonPaceTables() {
        tenantCrudService().executeUpdateByNativeQuery("delete from " + NonPaceTable);
        tenantCrudService().executeUpdateByNativeQuery("delete from " + PaceTable);
    }

    void compareResults(List<LastRoomValueAccomType> lrvAtATDecisions, int at9LRV, int at10LRV) {
        assertEquals(2, lrvAtATDecisions.size());
        List<LastRoomValueAccomType> at9 = lrvAtATDecisions.stream().filter(at -> at.getAccomTypeID() == 9).collect(Collectors.toList());
        List<LastRoomValueAccomType> at10 = lrvAtATDecisions.stream().filter(at -> at.getAccomTypeID() == 10).collect(Collectors.toList());
        assertTrue(BigDecimal.valueOf(at9LRV).compareTo(at9.get(0).getValue()) == 0);
        assertTrue(BigDecimal.valueOf(at10LRV).compareTo(at10.get(0).getValue()) == 0);
    }

    private void createDecision(Date businessDate, Integer decisionId, BigDecimal lrv, List<Integer> roomTypeIds) {
        final String arrivalDateStr = "'" + DateUtil.formatDate(businessDate, "yyyy-MM-dd") + "'";
        roomTypeIds.forEach(rtId -> {
            tenantCrudService().executeUpdateByNativeQuery("insert into Decision_LRV_AT(Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM) " + " values(" + decisionId + "," + propertyId + "," + rtId + "," + arrivalDateStr + "," + lrv + ",getDate())");
            tenantCrudService().executeUpdateByNativeQuery("insert into Pace_LRV_AT(Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM) " + " values(" + decisionId + "," + propertyId + "," + rtId + "," + arrivalDateStr + "," + lrv + ",getDate())");
        });
    }

    private void cleanUpTables(List<Integer> roomTypeIds, boolean deletePaceData) {
        final CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery("delete from Decision_LRV_AT where Accom_Type_ID in (:roomTypeIds)", QueryParameter.with("roomTypeIds", roomTypeIds).parameters());
        if (deletePaceData) {
            crudService.executeUpdateByNativeQuery("delete from Pace_LRV_AT where Accom_Type_ID in (:roomTypeIds)", QueryParameter.with("roomTypeIds", roomTypeIds).parameters());
        }
    }

    @Test
    public void test_If_LRV_Query_Is_In_Sync_With_Last_Good_Decision_Delivery_Job() {
        assertEquals(LRV_AT_ROOM_TYPE, tenantCrudService().getEntityManager().createNamedQuery(LastRoomValueAccomType.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AT_ROOM_TYPE_WITHTAX).unwrap(org.hibernate.Query.class).getQueryString(), String.format(ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED, "LRVAtRoomType", "LastGoodDecisionLrvAtAccomTypeService"));
    }

    @Test
    public void test_If_LRV_Query_Is_In_Sync_With_Last_Good_Decision_Delivery_Job_For_HTNG() {
        assertEquals(LRV_AT_ROOM_TYPE_FOR_HTNG, tenantCrudService().getEntityManager().createNamedQuery(LastRoomValueAccomType.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AT_ROOM_TYPE_WITH_TAX).unwrap(org.hibernate.Query.class).getQueryString(), String.format(ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED, "LRVAtRoomType", "LastGoodDecisionLrvAtAccomTypeService"));
    }

    @Test
    public void test_If_Decision_LRV_AT_Property_Table_Columns_Are_In_Sync_With_Last_Good_Decision_Delivery_Job() {
        final Object columns = tenantCrudService().findByNativeQuery(String.format(QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE, "Decision_LRV_AT")).get(0);
        assertEquals(DECISION_LRV_COLUMNS, String.valueOf(columns), String.format(COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB, "Decision_LRV_AT", "LastGoodDecisionLrvAtAccomTypeService"));
    }

    @Test
    public void test_If_Pace_LRV_AT_Property_Table_Columns_Are_In_Sync_With_Last_Good_Decision_Delivery_Job() {
        final Object columns = tenantCrudService().findByNativeQuery(String.format(QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE, "Pace_LRV_AT")).get(0);
        assertEquals(PACE_LRV_COLUMNS, String.valueOf(columns), String.format(COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB, "Pace_LRV_AT", "LastGoodDecisionLrvAtAccomTypeService"));
    }
}
