package com.ideas.tetris.pacman.services.meetingpackagepricing.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.meetingpackagepricing.MeetingPackageBarDecisionBuilder;
import com.ideas.tetris.pacman.services.meetingpackagepricing.dto.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.dto.MeetingPackageDecisionOverrideHistory;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.MeetingPackageBarDecisionOverrideRepository;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.MeetingPackagePaceBarDecisionRepository;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.MeetingPackageProductRateOffsetOverrideRepository;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.MeetingProductBarDecisionRepository;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackagePaceSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackageSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.mapper.MeetingPackagePricingMapper;
import com.ideas.tetris.pacman.services.meetingpackagepricing.transformer.MeetingPackagePricingOverrideTransformer;
import com.ideas.tetris.pacman.services.ngi.dto.meetingroompricing.MeetingPackagePricing;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductBuilder;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.DecisionUploadType;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum.ACTIVE;
import static com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.DatesWithFilter.CHANGES_SINCE;
import static com.ideas.tetris.platform.common.time.LocalDateUtils.toDate;
import static java.math.BigDecimal.*;
import static java.time.LocalDate.of;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MeetingPackageDecisionServiceTest {

    private static final String CLIENT_BSTN = "BSTN";
    private static final String PROPERTY_H1 = "H1";

    @InjectMocks
    private MeetingPackageDecisionService meetingPackageDecisionService;

    @Mock
    private MeetingProductBarDecisionRepository meetingProductBarDecisionRepository;

    @Mock
    private MeetingPackagePaceBarDecisionRepository meetingPackagePaceBarDecisionRepository;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private SpecialEventService specialEventService;

    @Mock
    private DateService dateService;

    @Mock
    private MeetingPackageBarDecisionOverrideRepository meetingPackageBarDecisionOverrideRepository;

    @Mock
    private MeetingPackageProductRateOffsetOverrideRepository meetingPackageProductRateOffsetOverrideRepository;
    @Mock
    private MeetingPackagePricingOverrideTransformer meetingPackagePricingOverrideTransformer;

    @Mock
    private DecisionService decisionService;

    @Mock
    private MeetingPackageProductService meetingPackageProductService;

    @Mock
    private MeetingProductMeetingPackageElementService meetingProductMeetingPackageElementService;

    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @BeforeEach
    public void setUp() {
        PacmanWorkContextHelper.setWorkContext(CLIENT_BSTN, 5);
        PacmanWorkContextHelper.setUserId("1");
        PacmanWorkContextHelper.setPropertyCode(PROPERTY_H1);
    }

    @Test
    void shouldGetMeetingPackagePricings() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("MP_Product", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertEquals(TEN, meetingPackagePricings.get(0).getAmount());
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldGetMeetingProductBarDifferentialDecisionsForUploadByOccupancyDateRange() {
        Date startDate = getDate("2025-02-26");
        Date endDate = getDate("2025-02-27");
        Date lastUploadDate = getDate("2025-02-25");
        Date occupancyDate1 = getDate("2025-02-26");
        Date occupancyDate2 = getDate("2025-02-27");
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackagePaceBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackagePaceBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDifferentialDecisionsForUploadByOccupancyDateRange(startDate, endDate, lastUploadDate))
                .thenReturn(List.of(meetingPackagePaceBarDecision1, meetingPackagePaceBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, lastUploadDate, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("MP_Product", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertEquals(TEN, meetingPackagePricings.get(0).getAmount());
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldReturnEmptyListWhenDateRangeIsIncorrect() {
        Date now = new Date();
        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(now, DateUtil.addDaysToDate(now, 1), null, null);
        assertEquals(0, meetingPackagePricings.size());
        verify(configParamsService, never()).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
    }

    @Test
    void shouldGetMeetingPackagePricingsWhenUsePackageIDToggleEnabled() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setPackageId("PackageID-1");
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(true);

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("PackageID-1", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, meetingPackagePricings.get(0).getAmount()));
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldThrowExceptionWhileGettingMeetingPackagePricingsWhenPackageIdMissingAndUsePackageIDToggleEnabled() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(true);

        TetrisException thrownException = assertThrows(TetrisException.class, () -> meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId));
        assertEquals("Package ID is missing for the meeting package product: MP_Product", thrownException.getBaseMessage());
    }

    @Test
    void shouldBuildSearchCriteriaCorrectly() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(10, 20);

        MeetingPackageSearchCriteria searchCriteria = meetingPackageDecisionService.buildSearchCriteria(startDate, endDate, meetingRoomIds, productIds);

        assertNotNull(searchCriteria);
        assertEquals(startDate, searchCriteria.getStartDate());
        assertEquals(endDate, searchCriteria.getEndDate());
        assertEquals(meetingRoomIds, searchCriteria.getMeetingRoomIds());
        assertEquals(productIds, searchCriteria.getProductIds());
    }

    @Test
    void shouldHandleNullValuesInSearchCriteria() {
        MeetingPackageSearchCriteria searchCriteria = meetingPackageDecisionService.buildSearchCriteria(null, null, null, null);

        assertNotNull(searchCriteria);
        assertNull(searchCriteria.getStartDate());
        assertNull(searchCriteria.getEndDate());
        assertNull(searchCriteria.getMeetingRoomIds());
        assertNull(searchCriteria.getProductIds());
    }

    @Test
    void shouldReturnMeetingPackageBarDecisionsWhenDataIsAvailable() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertEquals(valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
    }

    @Test
    void shouldGetMeetingPackageBarDecisionsChangesSince() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-30");
        List<Integer> meetingRoomIds = List.of(1, 2);
        List<Integer> productIds = singletonList(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        MeetingPackageElement meetingPackageElement = new MeetingPackageElement();
        meetingPackageElement.setName("Audio Setup");
        meetingPackageElement.setId(1);
        meetingPackageElement.setOffsetValue(valueOf(5.00));
        Date changesSinceDate = toDate(LocalDateTime.of(2025, 4, 9, 0, 0, 0, 0));
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(singletonList(createMeetingPackagePaceDecision(startDate, meetingPackageProduct, meetingRoom)));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(Map.of("event1", List.of(createSpecialEventForDate())));
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(createBaseMeetingRoom());

        var result = meetingPackageDecisionService.getMeetingPackageBarDecisionsChangesSince(startDate, endDate, meetingRoomIds, productIds, CHANGES_SINCE.getParam(), changesSinceDate);

        assertEquals(1, result.size());
        MeetingRoomPrice meetingRoomPrice = result.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(result);
        assertEquals(valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
    }

    @Test
    void shouldReturnTotalPackagePriceSameAsFinalBarWhenNoPackageElementsAssociatedWithProduct() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = List.of(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertNull(meetingRoomPrice.getAdjustmentValue());
        assertEquals(BigDecimal.valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
    }

    @Test
    void shouldFetchParentProductDecisionsIfMissingInInitialResults() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(100);
        MeetingPackageProduct linkedProduct = createMeetingPackageProduct();
        linkedProduct.setId(100);
        linkedProduct.setType("LINKED");
        linkedProduct.setDependentProductId(200);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(1);
        room.setName("Room A");
        MeetingPackageBarDecision linkedDecision = createMeetingPackageBarDecision(startDate, linkedProduct, room);
        MeetingPackageProduct parentProduct = createMeetingPackageProduct();
        parentProduct.setId(200);
        parentProduct.setType("INDEPENDENT");
        MeetingPackageBarDecision baseDecision = createMeetingPackageBarDecision(startDate, parentProduct, room);
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(linkedDecision))
                .thenReturn(List.of(baseDecision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(2)).searchByFilterCriteria(any());
        assertEquals("MP_Product",results.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0).getBaseProduct());
        assertEquals(TEN,results.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0).getBaseProductPrice());
    }

    @Test
    void shouldUseExistingParentProductDecisionsIfAlreadyPresent() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(100);
        MeetingPackageProduct linkedProduct = createMeetingPackageProduct();
        linkedProduct.setId(100);
        linkedProduct.setType("LINKED");
        linkedProduct.setDependentProductId(200);
        MeetingPackageProduct baseProduct = createMeetingPackageProduct();
        baseProduct.setId(200);
        baseProduct.setType("INDEPENDENT");
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(1);
        room.setName("Room A");
        MeetingPackageBarDecision linkedDecision = createMeetingPackageBarDecision(startDate, linkedProduct, room);
        MeetingPackageBarDecision baseDecision = createMeetingPackageBarDecision(startDate, baseProduct, room);
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(linkedDecision, baseDecision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(1)).searchByFilterCriteria(any());
    }


    @Test
    void shouldNotCallForParentProductDecisionsWhenNoLinkedProducts() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(1);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(1);
        product.setType("INDEPENDENT");
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(startDate, product, createFunctionSpaceFunctionRoom());
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(decision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(1)).searchByFilterCriteria(any());
    }


    private void assertMeetingPackagePricingDetails(List<MeetingPackagePricingPerDay> meetingPackagePricePerDay) {
        assertEquals(1, meetingPackagePricePerDay.size());
        assertEquals(getDate("2025-04-10"), meetingPackagePricePerDay.get(0).getDate());
        assertEquals(1, meetingPackagePricePerDay.get(0).getSpecialEvents().size());
        assertEquals(1, meetingPackagePricePerDay.get(0).getPriceDetails().size());
        MeetingPackagePriceDetail meetingPackagePriceDetail = meetingPackagePricePerDay.get(0).getPriceDetails().get(0);
        assertEquals(1, meetingPackagePriceDetail.getProductId());
        assertEquals("MP_Product", meetingPackagePriceDetail.getProductName());
        MeetingRoomPrice meetingRoomPrice = meetingPackagePriceDetail.getMeetingRoomPrices().get(0);
        assertEquals(1, meetingRoomPrice.getMeetingRoomId());
        assertEquals("Meeting_Room", meetingRoomPrice.getMeetingRoomName());
        assertEquals(TEN, meetingRoomPrice.getMeetingRoomPrice());
        assertTrue(meetingRoomPrice.getBaseMeetingRoom());
    }

    private PropertySpecialEvent createSpecialEventForDate() {
        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        Date startDate = getDate("2025-04-01");
        Date endDate = getDate("2025-04-31");
        propertySpecialEvent.setStartDate(startDate);
        propertySpecialEvent.setEndDate(endDate);
        return propertySpecialEvent;
    }


    @Test
    void shouldReturnEmptyListWhenDecisionsNotPresentForSelectedCriteria() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(10, 20);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(emptyList());
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(Collections.emptyMap());

        try (MockedStatic<MeetingPackagePricingMapper> mockedMapper = mockStatic(MeetingPackagePricingMapper.class)) {
            mockedMapper.when(() -> MeetingPackagePricingMapper.toPricingPerDay(eq(emptyList()), any(), eq(Collections.emptyMap()), eq(emptyList()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(emptyList())))
                    .thenReturn(emptyList());

            List<MeetingPackagePricingPerDay> result = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, meetingRoomIds);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void shouldReturnEmptyListWhenSpecialEventsAreEmpty() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(1);
        List<MeetingPackageBarDecision> mockDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        mockDecisions.add(meetingPackageBarDecision);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(mockDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(Collections.emptyMap());
        try (MockedStatic<MeetingPackagePricingMapper> mockedMapper = mockStatic(MeetingPackagePricingMapper.class)) {
            mockedMapper.when(() -> MeetingPackagePricingMapper.toPricingPerDay(eq(mockDecisions), any(), eq(Collections.emptyMap()), eq(emptyList()), eq(Collections.emptyMap()), eq(Collections.emptyMap()),eq(Collections.emptyMap()), eq(emptyList())))
                    .thenReturn(emptyList());

            List<MeetingPackagePricingPerDay> result = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void shouldReturnAdjustmentValueForLinkedProductWhenOverrideExists() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = List.of(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        ProductRateOffset productRateOffset = getProductRateOffset();
        LocalDate occupancyDate = DateUtil.convertJavaUtilDateToLocalDate(startDate);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        when(meetingPackageProductService.getProductRateOffsetsByProductAndOccupancyDate(occupancyDate, meetingPackageProduct.getId()))
                .thenReturn(productRateOffset);
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOccupancyDate(startDate);
        override.setProduct(meetingPackageProduct);
        override.setOffsetValue(TEN);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(
                anySet(), anySet(), eq(ACTIVE)))
                .thenReturn(List.of(override));

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);
        MeetingPackagePriceDetail priceDetail = meetingPackagePricePerDay.get(0).getPriceDetails().get(0);
        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertEquals(override.getOffsetValue(), meetingRoomPrice.getAdjustmentValue());
        assertEquals(valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
        assertEquals(valueOf(100.00), priceDetail.getAdjustment());
    }

    private ProductRateOffset getProductRateOffset() {
        ProductRateOffset productRateOffset = new ProductRateOffset();
        productRateOffset.setCeilingRate(valueOf(100.0));
        productRateOffset.setFloorRate(valueOf(100.0));
        productRateOffset.setMpProductId(1);
        return productRateOffset;
    }

    @Test
    void shouldGetActiveOverridesForLinkedProducts() throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ArgumentCaptor<Set<Integer>> productIdsCaptor = ArgumentCaptor.forClass(Set.class);
        ArgumentCaptor<Set<Date>> occupancyDatesCaptor = ArgumentCaptor.forClass(Set.class);
        var room1 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Room 1");
        var room2 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Room 2");
        var mpp1 = MeetingPackageProductBuilder.createIndependentMPProduct("IP1", ACTIVE);
        mpp1.setId(10);
        var mpp2 = MeetingPackageProductBuilder.createLinkedMPProduct("LP1", mpp1.getId());
        mpp2.setId(11);
        mpp2.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        MeetingPackageBarDecision decision0 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp1, room1, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision1 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room1, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision2 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room2, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision3 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room1, dateFormat.parse("2022-09-02"), TEN);

        meetingPackageDecisionService.getActiveOverridesForLinkedProducts(List.of(decision0, decision1, decision2, decision3));

        verify(meetingPackageProductRateOffsetOverrideRepository).findActiveOverridesForProductOccupancyPairs(
                productIdsCaptor.capture(), occupancyDatesCaptor.capture(), eq(ACTIVE));
        assertEquals(1, productIdsCaptor.getValue().size());
        assertEquals(mpp2.getId(), productIdsCaptor.getValue().iterator().next());
        assertEquals(2, occupancyDatesCaptor.getValue().size());
        assertEquals(Set.of(dateFormat.parse("2022-09-01"), dateFormat.parse("2022-09-02")), occupancyDatesCaptor.getValue());
    }

    @Test
    void shouldGetActiveOverridesForLinkedProductsWhenNoDecisionsArePassed() {
        var result = meetingPackageDecisionService.getActiveOverridesForLinkedProducts(emptyList());

        verify(meetingPackageProductRateOffsetOverrideRepository, never()).findActiveOverridesForProductOccupancyPairs(anySet(), anySet(), any());
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForBDE() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(21, 2, 2024);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadEndDate("BDE")).thenReturn(endDate);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(4);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialData("BDE");

        assertEquals(4, resultCount);
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForManualUpload() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(21, 6, 2025);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(endDate);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(4);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialDetailsForManualUpload();

        assertEquals(4, resultCount);
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForCDP() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(21, 2, 2024);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadEndDate("CDP")).thenReturn(endDate);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(4);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialData("CDP");

        assertEquals(4, resultCount);
    }

    @Test
    void shouldReturnTrueWhenMeetingPackagePricingDecisionTypeConfiguredAsDifferential() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.DIFFERENTIAL.getConfigParamValue());

        assertTrue(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnTrueWhenMeetingPackagePricingDecisionTypeConfiguredAsFull() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.FULL.getConfigParamValue());

        assertTrue(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnFalseWhenMeetingPackagePricingDecisionTypeNotConfigured() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.NONE.getConfigParamValue());

        assertFalse(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnPricingOverrideDetailWhenOverridesArePresent() {
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = new MeetingPackageBarDecisionOverride();
        meetingPackageBarDecisionOverride.setId(1L);
        PricingOverrideDetail pricingOverrideDetail = new PricingOverrideDetail();
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageBarDecisionOverrideBy(1L))
                .thenReturn(meetingPackageBarDecisionOverride);
        when(meetingPackagePricingOverrideTransformer.convert(meetingPackageBarDecisionOverride)).thenReturn(pricingOverrideDetail);

        PricingOverrideDetail overrideDetail = meetingPackageDecisionService.getPricingOverrideDetails(1L).get();

        assertEquals(pricingOverrideDetail, overrideDetail);
        verify(meetingPackageBarDecisionOverrideRepository).getMeetingPackageBarDecisionOverrideBy(1L);
        verify(meetingPackagePricingOverrideTransformer).convert(meetingPackageBarDecisionOverride);
    }

    @Test
    void shouldReturnNullWhenOverridesAreAbsent() {
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageBarDecisionOverrideBy(1L))
                .thenReturn(null);

        Optional<PricingOverrideDetail> overrideDetail = meetingPackageDecisionService.getPricingOverrideDetails(1L);

        assertFalse(overrideDetail.isPresent());
        verify(meetingPackageBarDecisionOverrideRepository).getMeetingPackageBarDecisionOverrideBy(1L);
        verify(meetingPackagePricingOverrideTransformer, never()).convert(any());
    }

    @Test
    void shouldSavePricingOverrideDetailsForSpecificOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, ONE, occupancyDate);
        pricingOverride.setSpecificOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertEquals(ONE, overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getUserSpecifiedRate());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSavePricingOverrideDetailsForFloorOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOOR, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getFloorRate());
        assertEquals(DecisionOverrideType.FLOOR, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSavePricingOverrideDetailsForCeilOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, ONE, occupancyDate);
        pricingOverride.setCeilingOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.CEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getCeilRate());
        assertEquals(DecisionOverrideType.CEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }


    @Test
    void shouldCreateNewPricingOverrideDetailsWhenExistingOverrideIsUpdated() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(12, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(decisionService, times(1)).createMeetingPackagePricingOverrideDecision();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldDeactivateOverrideWhenLinkedProductAndAdjustmentIsNul() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);

    }

    @Test
    void shouldSetExistingActiveOverridesToInactiveWhenRemovingOverrides() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageProductRateOffsetOverride meetingPackageHistory = new MeetingPackageProductRateOffsetOverride();
        meetingPackageHistory.setStatus(ACTIVE);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveByProductAndOccupancyDate(meetingPackageProduct, occupancyDate)).thenReturn(
                List.of(meetingPackageHistory)
        );

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);

    }

    @Test
    void shouldSavePricingOverrideDetailsForFloorAndCeilingOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(14);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(14, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(1, overrides.get(0).getUserId());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(14, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSaveRemovedPricingOverrideDetails() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.USER);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingPricingOverrideDetails() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(120), occupancyDate);

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingAndAddSpecificPricingOverrideDetails() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(120), occupancyDate);
        pricingOverride.setSpecificOverride(valueOf(16));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(valueOf(16), overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }


    private static Decision createNewDecision(int decisionId) {
        Decision overrideDecision = new Decision();
        overrideDecision.setId(decisionId);
        return overrideDecision;
    }

    @Test
    public void shouldGetEmptyListWhenNoOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(emptyList());

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertTrue(actualOverrideHistory.isEmpty());
    }

    @Test
    public void shouldGeOverrideHistoryWhenSpecificOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.USER.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(valueOf(30.00));
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertEquals(30.0, overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.USER.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(10.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertEquals(10.0, overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.FLOOR.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(valueOf(10.00));
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.FLOOR.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(valueOf(10.00));
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenCeilingOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.CEIL.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(valueOf(30.00));
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenCeilingOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.CEIL.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(valueOf(30.00));
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorAndCeilingOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.FLOORANDCEIL.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(valueOf(10.00));
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(valueOf(30.00));
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorAndCeilingOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.FLOORANDCEIL.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(null);
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(valueOf(10.00));
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(valueOf(30.00));
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    void shouldCreateLastGoodDecisionsWhenLastGoodDecisionAndNewDecisionIdAreKnown() {
        Date startDate = toDate(of(2024, 7, 22));
        Date endDate = toDate(of(2025, 7, 22));
        Integer newDecisionId = 398;
        Integer lastGoodDecisionId = 392;
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(endDate);
        when(meetingProductBarDecisionRepository.updateMeetingPackageBarDecisionWithLastKnownGoodDecisions(
                startDate, endDate, newDecisionId, lastGoodDecisionId)).thenReturn(5);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(
                startDate, endDate)).thenReturn(3);

        meetingPackageDecisionService.createLastGoodDecisions(lastGoodDecisionId, newDecisionId);

        verify(meetingProductBarDecisionRepository, times(1))
                .updateMeetingPackageBarDecisionWithLastKnownGoodDecisions(startDate, endDate, newDecisionId, lastGoodDecisionId);
        verify(meetingPackagePaceBarDecisionRepository, times(1))
                .insertPaceMeetingPackageUploadDifferentialData(startDate, endDate);
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingSpecificOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setSpecificOverride(valueOf(34));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(34), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldNotModifyMeetingRoomPriceWhenFloorOverrideIsLessThanMeetingRoomPrice() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(20));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(23.45), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingFloorOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(34));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(34), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldNotModifyMeetingRoomPriceWhenCeilingOverrideIsGreaterThanMeetingRoomPrice() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setCeilingOverride(valueOf(35));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(23.45), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingCeilingOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(null, 2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setCeilingOverride(TEN);

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(TEN, pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void verifyThatMeetingPackageLinkedProductChangeSyncGetsTriggeredWhenAdjustmentOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(1L, 2, 3, valueOf(120), occupancyDate);
        pricingOverride.setAdjustment(valueOf(30));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrideDetails(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRateOffsetOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToSave = overridesCaptor.getValue();
        assertNotNull(rateOffsetOverridesToSave);
        assertEquals(1, rateOffsetOverridesToSave.size());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldGetAdjustmentOverrideHistoryWhenActiveAdjustmentOverride() {
        Integer productId = 101;
        Date occupancyDate = new Date();
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        override.setOffsetValue(new BigDecimal("30.0"));
        override.setStatus(ACTIVE);
        override.setLastUpdatedByUserId(1);
        override.setLastUpdatedDate(new Date());
        List<MeetingPackageProductRateOffsetOverride> overrides = List.of(override);
        when(meetingPackageProductRateOffsetOverrideRepository.findAllAdjustmentsByProductAndOccupancyDate(productId, occupancyDate))
                .thenReturn(overrides);
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getLastUpdatedDate())))
                .thenReturn("26-May-2025 10:31:02 MST");
        User user = new User();
        user.setId(1);
        user.setName("SSO User");
        MeetingPackageDecisionService spyService = spy(meetingPackageDecisionService);
        doReturn(List.of(user)).when(spyService).getUsers(overrides);

        List<MeetingPackageAdjustmentOverrideHistory> result =
                spyService.getMeetingPackageAdjustmentOverrideHistory(productId, occupancyDate);

        assertEquals(1, result.size());
        MeetingPackageAdjustmentOverrideHistory overrideHistory = result.get(0);
        assertEquals("Fixed", overrideHistory.getOffsetType());
        assertEquals(valueOf(30.00).setScale(2), overrideHistory.getAdjustedOffset());
        assertEquals("SSO User", overrideHistory.getUpdatedBy());
        assertFalse(overrideHistory.isRemoved());
        assertNotNull(overrideHistory.getUpdatedOn());
    }

    @Test
    void shouldGetAdjustmentOverrideHistoryWhenOverridesAddedAndRemoved() {
        Integer productId = 101;
        Date occupancyDate = new Date();
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        override.setOffsetValue(new BigDecimal("25.5"));
        override.setStatus(TenantStatusEnum.DELETED);
        override.setLastUpdatedByUserId(1);
        override.setCreatedByUserId(2);
        override.setLastUpdatedDate(new Date());
        override.setCreateDate(new Date());
        List<MeetingPackageProductRateOffsetOverride> overrides = List.of(override);
        when(meetingPackageProductRateOffsetOverrideRepository.findAllAdjustmentsByProductAndOccupancyDate(productId, occupancyDate))
                .thenReturn(overrides);
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getLastUpdatedDate())))
                .thenReturn("26-May-2025 10:35:02 MST");
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getCreateDate())))
                .thenReturn("25-May-2025 10:31:02 MST");
        User user1 = new User();
        user1.setId(1);
        user1.setName("Remover");
        User user2 = new User();
        user2.setId(2);
        user2.setName("Creator");
        MeetingPackageDecisionService spyService = spy(meetingPackageDecisionService);
        doReturn(List.of(user1, user2)).when(spyService).getUsers(overrides);

        List<MeetingPackageAdjustmentOverrideHistory> result =
                spyService.getMeetingPackageAdjustmentOverrideHistory(productId, occupancyDate);

        assertEquals(2, result.size());
        MeetingPackageAdjustmentOverrideHistory removed = result.get(1);
        MeetingPackageAdjustmentOverrideHistory created = result.get(0);
        assertEquals("Fixed", removed.getOffsetType());
        assertEquals(valueOf(25.50).setScale(2), removed.getAdjustedOffset());
        assertTrue(removed.isRemoved());
        assertEquals("Remover", removed.getUpdatedBy());
        assertEquals("Fixed", created.getOffsetType());
        assertEquals(valueOf(25.50).setScale(2), created.getAdjustedOffset());
        assertFalse(created.isRemoved());
        assertEquals("Creator", created.getUpdatedBy());
    }

    @Test
    public void shouldCreateSingleEntryWhenOnlyOneDecisionExists() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1));

        assertEquals(1, result.size());
        assertEquals(10, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldGetHigherDecisionIdWhenKeysAreDuplicate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date);
        MeetingPackagePaceBarDecision d2 = createDecision(20, 1, 1, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(20, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldIgnoreLowerDecisionIdWhenKeysAreDuplicate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(20, 1, 1, date);
        MeetingPackagePaceBarDecision d2 = createDecision(10, 1, 1, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(20, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldCreateTwoEntriesForDifferentProductsSameRoomAndDate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 2, 1, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldCreateTwoEntriesForDifferentRoomsSameProductAndDate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 1, 2, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldCreateTwoEntriesForSameProductAndRoomButDifferentDates() {
        Date date1 = getDate("2025-07-01");
        Date date2 = getDate("2025-07-02");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date1);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 1, 1, date2);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldKeepFirstEntryWhenExactDuplicatesExist() {
        Date date = getDate("2025-02-25");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date);
        MeetingPackagePaceBarDecision d2 = createDecision(10, 1, 1, date);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(10, result.values().iterator().next().getDecisionId());
    }


    private static Date getDate(String dateString) {
        try {
            return DateUtil.parseDate(dateString, DateUtil.DEFAULT_DATE_FORMAT);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private static PricingOverride getPricingOverride(Long id, Integer productId, Integer meetingRoomId,
                                                      BigDecimal meetingRoomPrice, Date occupancyDate) {
        PricingOverride pricingOverrideDetail = new PricingOverride();
        pricingOverrideDetail.setProductId(productId);
        pricingOverrideDetail.setMeetingRoomId(meetingRoomId);
        pricingOverrideDetail.setMeetingRoomPrice(meetingRoomPrice);
        pricingOverrideDetail.setStartDate(LocalDateUtils.toJavaLocalDate(occupancyDate));
        pricingOverrideDetail.setEndDate(LocalDateUtils.toJavaLocalDate(occupancyDate));
        return pricingOverrideDetail;
    }

    private static MeetingPackageBarDecisionOverride createMeetingPackageBarDecisionOverride(Date occupancyDate,
                                                                                             MeetingPackageProduct meetingPackageProduct,
                                                                                             FunctionSpaceFunctionRoom functionSpaceFunctionRoom,
                                                                                             BigDecimal oldBar, BigDecimal newBar) {
        MeetingPackageBarDecisionOverride meetingPackageBarDecision = new MeetingPackageBarDecisionOverride();
        meetingPackageBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackageBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackageBarDecision.setOccupancyDate(occupancyDate);
        meetingPackageBarDecision.setOldBar(oldBar);
        meetingPackageBarDecision.setNewBar(newBar);
        return meetingPackageBarDecision;
    }

    private static MeetingPackageBarDecision createMeetingPackageBarDecision(Date occupancyDate,
                                                                             MeetingPackageProduct meetingPackageProduct,
                                                                             FunctionSpaceFunctionRoom functionSpaceFunctionRoom) {
        MeetingPackageBarDecision meetingPackageBarDecision = new MeetingPackageBarDecision();
        meetingPackageBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackageBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackageBarDecision.setOccupancyDate(occupancyDate);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setTotalPackagePrice(BigDecimal.valueOf(15.00));
        return meetingPackageBarDecision;
    }

    private static MeetingPackagePaceBarDecision createMeetingPackagePaceBarDecision(Date occupancyDate,
                                                                             MeetingPackageProduct meetingPackageProduct,
                                                                             FunctionSpaceFunctionRoom functionSpaceFunctionRoom) {
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = new MeetingPackagePaceBarDecision();
        meetingPackagePaceBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackagePaceBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackagePaceBarDecision.setOccupancyDate(occupancyDate);
        meetingPackagePaceBarDecision.setFinalBar(TEN);
        meetingPackagePaceBarDecision.setTotalPackagePrice(BigDecimal.valueOf(15.00));
        return meetingPackagePaceBarDecision;
    }

    private static MeetingPackagePaceBarDecision createMeetingPackagePaceDecision(Date occupancyDate,
                                                                                  MeetingPackageProduct meetingPackageProduct,
                                                                                  FunctionSpaceFunctionRoom meetingRoom) {
        MeetingPackagePaceBarDecision paceBarDecision = new MeetingPackagePaceBarDecision();
        paceBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        paceBarDecision.setFunctionSpaceFunctionRoom(meetingRoom);
        paceBarDecision.setOccupancyDate(occupancyDate);
        paceBarDecision.setFinalBar(TEN);
        paceBarDecision.setTotalPackagePrice(BigDecimal.valueOf(15.00));
        return paceBarDecision;
    }

    private static MeetingPackageProduct createMeetingPackageProduct() {
        MeetingPackageProduct meetingPackageProduct = new MeetingPackageProduct();
        meetingPackageProduct.setName("MP_Product");
        return meetingPackageProduct;
    }

    private static FunctionSpaceFunctionRoom createFunctionSpaceFunctionRoom() {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = new FunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setSalesCateringIdentifier("Meeting_Room");
        return functionSpaceFunctionRoom;
    }

    private BaseMeetingRoomDTO createBaseMeetingRoom() {
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        return baseMeetingRoom;
    }

    private MeetingPackageProduct createProduct(int id) {
        MeetingPackageProduct product = new MeetingPackageProduct();
        product.setId(id);
        return product;
    }

    private FunctionSpaceFunctionRoom createRoom(int id) {
        FunctionSpaceFunctionRoom room = new FunctionSpaceFunctionRoom();
        room.setId(id);
        room.setName("Room_" + id);
        return room;
    }

    private MeetingPackagePaceBarDecision createDecision(int decisionId, int productId, int roomId, Date date) {
        MeetingPackagePaceBarDecision decision = new MeetingPackagePaceBarDecision();
        decision.setDecisionId(decisionId);
        decision.setOccupancyDate(date);
        decision.setMeetingPackageProduct(createProduct(productId));
        decision.setFunctionSpaceFunctionRoom(createRoom(roomId));
        return decision;
    }
}