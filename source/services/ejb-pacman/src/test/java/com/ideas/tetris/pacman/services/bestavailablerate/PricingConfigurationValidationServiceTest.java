package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesHierarchyValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.PricingAccomClassRankDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedCeilingAndFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedCeilingAndFloorPK;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffsetPK;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.GroupPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomTypeDraft;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationOffsetService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PricingConfigurationValidationServiceTest {
    @Mock
    CrudService crudService;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    PricingConfigurationOffsetService offsetService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    AccomClassPriceRankService accomClassPriceRankService;

    @Mock
    DateService dateService;

    @Mock
    AgileRatesHierarchyValidationService agileRatesHierarchyValidationService;

    @Mock
    AgileRatesConfigurationService agileRatesConfigurationService;

    @InjectMocks
    @Spy
    PricingConfigurationValidationService service = new PricingConfigurationValidationService();

    private PricingAccomClass lowestPricingAccomClass;
    private AccomType lowestBaseAccomType1;
    private AccomType lowestAccomType2;
    private PricingAccomClass middlePricingAccomClass;
    private AccomType middleBaseAccomType1;
    private AccomType middleAccomType2;
    private PricingAccomClass highestPricingAccomClass;
    private AccomType highestBaseAccomType1;
    private AccomType highestAccomType2;
    private List<PricingAccomClass> pricingAccomClasses;

    private AccomClassPriceRank upperAccomClassPriceRank;
    private List<AccomClassPriceRank> accomClassPriceRanks;

    private BigDecimal positive100 = new BigDecimal(100);
    private BigDecimal negative100 = new BigDecimal(-100);
    private BigDecimal negative99 = new BigDecimal(-99);
    private BigDecimal zero = new BigDecimal(0);
    private BigDecimal min = new BigDecimal(0.01);
    private Set<String> expectedRoomClassesWithOffsetViolation = new HashSet<>();
    private Set<String> expectedRoomClassesWithPriceExcludedOffsetViolation = new HashSet<>();

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        //Setup basic Room and General Pricing Configuration
        int lowestAccomClassId = 123;
        int middleAccomClassId = 456;
        int highestAccomClassId = 789;
        int lowestAccomType1Id = 1;
        int lowestAccomType2Id = 2;
        int middleAccomType1Id = 3;
        int middleAccomType2Id = 4;
        int highestAccomType1Id = 5;
        int highestAccomType2Id = 6;

        lowestPricingAccomClass = new PricingAccomClass();
        AccomClass lowestAccomClass = new AccomClass();
        lowestAccomClass.setId(lowestAccomClassId);
        lowestBaseAccomType1 = new AccomType();
        lowestBaseAccomType1.setAccomClass(lowestAccomClass);
        lowestBaseAccomType1.setId(lowestAccomType1Id);
        lowestBaseAccomType1.setName("DELUXE");
        lowestAccomType2 = new AccomType();
        lowestAccomType2.setAccomClass(lowestAccomClass);
        lowestAccomType2.setId(lowestAccomType2Id);
        lowestAccomType2.setName("STANDARD");
        lowestPricingAccomClass.setAccomClass(lowestAccomClass);
        lowestPricingAccomClass.setAccomType(lowestBaseAccomType1);
        lowestAccomClass.setAccomTypes(new HashSet<>(asList(lowestBaseAccomType1, lowestAccomType2)));
        middlePricingAccomClass = new PricingAccomClass();
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(middleAccomClassId);
        middleBaseAccomType1 = new AccomType();
        middleBaseAccomType1.setAccomClass(middleAccomClass);
        middleBaseAccomType1.setId(middleAccomType1Id);
        middleBaseAccomType1.setName("QUEEN");
        middleAccomType2 = new AccomType();
        middleAccomType2.setAccomClass(middleAccomClass);
        middleAccomType2.setId(middleAccomType2Id);
        middleAccomType2.setName("KING");
        middlePricingAccomClass.setAccomClass(middleAccomClass);
        middlePricingAccomClass.setAccomType(middleBaseAccomType1);
        middleAccomClass.setAccomTypes(new HashSet<>(asList(middleBaseAccomType1, middleAccomType2)));
        highestPricingAccomClass = new PricingAccomClass();
        AccomClass highestAccomClass = new AccomClass();
        highestAccomClass.setId(highestAccomClassId);
        highestBaseAccomType1 = new AccomType();
        highestBaseAccomType1.setAccomClass(highestAccomClass);
        highestBaseAccomType1.setId(highestAccomType1Id);
        highestAccomType2 = new AccomType();
        highestAccomType2.setAccomClass(highestAccomClass);
        highestAccomType2.setId(highestAccomType2Id);
        highestPricingAccomClass.setAccomClass(highestAccomClass);
        highestPricingAccomClass.setAccomType(highestBaseAccomType1);
        highestAccomClass.setAccomTypes(new HashSet<>(asList(highestBaseAccomType1, highestAccomType2)));
        pricingAccomClasses = asList(lowestPricingAccomClass, middlePricingAccomClass, highestPricingAccomClass);

        lowestAccomClass.setName("lowest");
        middleAccomClass.setName("middle");
        highestAccomClass.setName("highest");

        AccomClassPriceRank lowerAccomClassPriceRank = new AccomClassPriceRank();
        lowerAccomClassPriceRank.setLowerRankAccomClass(lowestAccomClass);
        lowerAccomClassPriceRank.setHigherRankAccomClass(middleAccomClass);

        upperAccomClassPriceRank = new AccomClassPriceRank();
        upperAccomClassPriceRank.setLowerRankAccomClass(middleAccomClass);
        upperAccomClassPriceRank.setHigherRankAccomClass(highestAccomClass);
        accomClassPriceRanks = asList(lowerAccomClassPriceRank, upperAccomClassPriceRank);

        expectedRoomClassesWithOffsetViolation.add("KING");
        expectedRoomClassesWithOffsetViolation.add("DELUXE");

        expectedRoomClassesWithPriceExcludedOffsetViolation.add("DELUXE");
        when(dateService.getCaughtUpLocalDate()).thenReturn(LocalDate.now());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor() {

        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Offsets
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setProductID(1);
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setProductID(1);
        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setProductID(1);

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);

        //We spy this because it's tested on its own.
        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);
        doReturn(true).when(service).isValidDefaultRoomClassConfig(middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false, highestBaseConfig, new HashSet<>(singletonList(highestConfigOffsetAccomType)), false);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

    }

    @Test
    public void validateSaveDefaultCeilingAndFloorByPricingRuleType() {

        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Offsets
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setProductID(1);
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setProductID(1);
        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setProductID(1);

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);


        //We spy this because it's tested on its own.
        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);
        doReturn(true).when(service).isValidDefaultRoomClassConfig(middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false, highestBaseConfig, new HashSet<>(singletonList(highestConfigOffsetAccomType)), false);


        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloorByPricingRuleType(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_OnlyOneRoomType_higherClass() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.TEN);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(110));

        classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }


    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_OnlyOneRoomType_lowerClass() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);


        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The middle room class will not be on the input as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(250));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        middlePricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(301));

        classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_higherClass_OffsetsNotYetConfigured() {

        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));

        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = singletonList(middleConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRate(BigDecimal.valueOf(50));

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        middleConfigOffsetAccomType.setTuesdayOffsetValue(BigDecimal.TEN);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_lowerClass_OffsetsNotYetConfigured() {
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));

        //Setup Offsets
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = singletonList(highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The middle room class will not be on the input as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setThursdayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setFridayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setSaturdayCeilingRate(BigDecimal.valueOf(300));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        middlePricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_multipleRoomTypes_higherClass() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);


        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.TEN);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(110));

        classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloorByPricingRuleTypeForOffestDefaultAndMinRCDefault() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        List<CPConfigOffsetAccomType> offsets = getOffsets();
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        TransientPricingBaseAccomType middleBaseConfig = getTransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        when(service.getDailyBarPricingRuleType()).thenReturn(2);

        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = service.getPricingAccomClassesForProperty();
        List<AccomClassMinPriceDiff> minRCDefaults = getMinRCDefaults(pricingAccomClassRankDtos);

        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(minRCDefaults);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloorByPricingRuleType(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        classesInViolation = service.validateSaveDefaultCeilingAndFloorByPricingRuleType(basePricingAccomTypes);
        assertEquals(0, classesInViolation.size());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloorByPricingRuleTypeForOffestDefaultAndMinRCSeason() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        List<CPConfigOffsetAccomType> offsets = getOffsets();
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        TransientPricingBaseAccomType middleBaseConfig = getTransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = service.getPricingAccomClassesForProperty();
        List<AccomClassMinPriceDiff> minRCDefaults = getMinRCDefaults(pricingAccomClassRankDtos);
        List<AccomClassMinPriceDiffSeason> minRCSeaons = getMinRCSeasons(pricingAccomClassRankDtos);

        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(minRCDefaults);
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(minRCSeaons);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloorByPricingRuleType(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(200));

        classesInViolation = service.validateSaveDefaultCeilingAndFloorByPricingRuleType(basePricingAccomTypes);
        assertEquals(1, classesInViolation.size());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_multipleRoomTypes_lowerClass() {
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(asList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setAccomType(middleAccomType2);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The middle room class will not be on the input as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(250));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        middlePricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(301));

        classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    /**
     * Validating the below methods here.
     * 1. validateSaveSeasonCeilingAndFloor
     * 2. validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType
     */
    @Test
    public void validateSaveSeasonCeilingAndFloor_priceExcludedClass_higherClass() {
        Product product = new Product();
        product.setCode(Product.BAR);
        List<ProductAccomType> productAccomTypeList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        accomType1.setName("QUEEN");
        accomType1.setId(3);
        accomType2.setId(5);
        accomTypeList.add(accomType1);
        accomTypeList.add(accomType2);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(asList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.findProductRoomTypesByProduct(product)).thenReturn(productAccomTypeList);

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK middleConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigStartDate = new CPConfigMergedOffset();
        middleConfigStartDate.setOffsetValue(BigDecimal.TEN);
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        middleConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedOffsetPK middleConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigMiddleDate = new CPConfigMergedOffset();
        middleConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        middleConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedOffsetPK middleConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigEndDate = new CPConfigMergedOffset();
        middleConfigEndDate.setOffsetValue(BigDecimal.TEN);
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyEndDate, middleConfigEndDate);

        //The higher room class will not be in the offsets as it is price excluded, but it should be considered

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(80));
        middleBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(80));

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        //Offsets grabbed because we're price excluded
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigMiddleDate.setOffsetValue(BigDecimal.valueOf(110));

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Offsets: [</b>Default<b>]</b> <BR/><b>Minimum Pricing Difference: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(201));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(201));
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Minimum Pricing Difference: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());
    }

    /**
     * Validating the below methods here.
     * 1. validateSaveSeasonCeilingAndFloor
     * 2. validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType
     */
    @Test
    public void validateSaveSeasonCeilingAndFloor_priceExcludedClass_lowerClass() {
        Product product = new Product();
        product.setId(1);
        ProductAccomType productAccomType1 = new ProductAccomType();
        ProductAccomType productAccomType2 = new ProductAccomType();
        List<ProductAccomType> productAccomTypeList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        accomType1.setName("QUEEN");
        accomType1.setId(3);
        accomType2.setId(5);
        productAccomType1.setProduct(product);
        productAccomType1.setAccomType(accomType1);
        productAccomType2.setProduct(product);
        productAccomType2.setAccomType(accomType2);
        accomTypeList.add(accomType1);
        accomTypeList.add(accomType2);
        productAccomTypeList.add(productAccomType1);
        productAccomTypeList.add(productAccomType2);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.findProductRoomTypesByProduct(product)).thenReturn(productAccomTypeList);

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);

        //The higher room class will not be in the offsets as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(200));

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        middlePricingAccomClass.setPriceExcluded(true);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        //Offsets grabbed because we're price excluded
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleAccomType2);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);


        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, middleConfigOffsetAccomType2);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Minimum Pricing Difference: [</b>Seasons<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Minimum Pricing Difference: [</b>Seasons<b>]</b> <BR/>", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonCeilingAndFloorWhenAllRoomTypesNotConfigured() {
        Product product = new Product();
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        product.setId(1);
        ProductAccomType productAccomType1 = new ProductAccomType();
        List<ProductAccomType> productAccomTypeList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        accomType1.setName("QUEEN");
        accomType1.setId(3);
        accomType2.setName("STD");
        accomType2.setId(5);
        productAccomType1.setProduct(product);
        productAccomType1.setAccomType(accomType1);
        accomTypeList.add(accomType1);
        accomTypeList.add(accomType2);
        productAccomTypeList.add(productAccomType1);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(singletonList(upperAccomClassPriceRank));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.findProductRoomTypesByProduct(product)).thenReturn(productAccomTypeList);

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(200));
        highestBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(200));

        middlePricingAccomClass.setPriceExcluded(true);

        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleAccomType2);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);


        List<CPConfigOffsetAccomType> offsets = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, middleConfigOffsetAccomType2);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(0, classesInViolation.size());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(0, classesInViolation.size());
    }

    /**
     * Validating the below methods here.
     * 1. validateSaveSeasonCeilingAndFloor
     * 2. validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType
     */
    @Test
    public void validateSaveSeasonCeilingAndFloor_priceExcludedClass_higherClass_offsetsNotYetConfigured() {
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);
        Product product = new Product();
        product.setCode(Product.BAR);
        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK middleConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigStartDate = new CPConfigMergedOffset();
        middleConfigStartDate.setOffsetValue(BigDecimal.TEN);
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        middleConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedOffsetPK middleConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigMiddleDate = new CPConfigMergedOffset();
        middleConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        middleConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedOffsetPK middleConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigEndDate = new CPConfigMergedOffset();
        middleConfigEndDate.setOffsetValue(BigDecimal.TEN);
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyEndDate, middleConfigEndDate);

        //The higher room class will not be in the offsets as it is price excluded, but it should be considered

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRate(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRate(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRate(BigDecimal.valueOf(50));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(middleBaseConfig);

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        highestPricingAccomClass.setPriceExcluded(true);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        //Offsets empty because they are not yet configured
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(Collections.emptyList());

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());


    }

    /**
     * Validating the below methods here.
     * 1. validateSaveSeasonCeilingAndFloor
     * 2. validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType
     */
    @Test
    public void validateSaveSeasonCeilingAndFloor_priceExcludedClass_lowerClass_offsetsNotYetConfigured() {
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);

        //The higher room class will not be in the offsets as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setThursdayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setFridayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setSaturdayCeilingRate(BigDecimal.valueOf(200));
        highestBaseConfig.setSundayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setMondayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setTuesdayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setWednesdayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setThursdayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setFridayFloorRate(BigDecimal.valueOf(100));
        highestBaseConfig.setSaturdayFloorRate(BigDecimal.valueOf(100));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        //The higher room class will not be on the input as it is price excluded, but it should be considered
        middlePricingAccomClass.setPriceExcluded(true);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        //Offsets empty because they are not yet configured
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(Collections.emptyList());


        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_true_if_no_offsets_configured() {
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setProductID(1);
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setSundayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setMondayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setTuesdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setWednesdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setThursdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setFridayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setSaturdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setSundayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setMondayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setTuesdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setWednesdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setThursdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setFridayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setSaturdayFloorRate(BigDecimal.ZERO);

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setMondayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setTuesdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setWednesdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setThursdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setFridayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setSaturdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setSundayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setMondayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setTuesdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setWednesdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setThursdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setFridayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setSaturdayFloorRate(BigDecimal.ZERO);

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setMondayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setTuesdayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setWednesdayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setThursdayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setFridayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setSaturdayCeilingRate(BigDecimal.ZERO);
        highestBaseConfig.setSundayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setMondayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setTuesdayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setWednesdayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setThursdayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setFridayFloorRate(BigDecimal.ZERO);
        highestBaseConfig.setSaturdayFloorRate(BigDecimal.ZERO);

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);

        // No Offsets data configured
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(Collections.emptyList());

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveSeasonCeilingAndFloor() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(accomClassPriceRanks);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setProductID(1);
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(150));

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK lowestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigStartDate = new CPConfigMergedOffset();
        lowestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        lowestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedOffsetPK lowestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigMiddleDate = new CPConfigMergedOffset();
        lowestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        lowestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedOffsetPK lowestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigEndDate = new CPConfigMergedOffset();
        lowestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        lowestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedOffsetPK middleConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigStartDate = new CPConfigMergedOffset();
        middleConfigStartDate.setOffsetValue(BigDecimal.TEN);
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        middleConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedOffsetPK middleConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigMiddleDate = new CPConfigMergedOffset();
        middleConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        middleConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedOffsetPK middleConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigEndDate = new CPConfigMergedOffset();
        middleConfigEndDate.setOffsetValue(BigDecimal.TEN);
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);


        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(291)); //291 + 10 (offset) is 301

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        //Equal is alright
        basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig); //Reset
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(290)); //290 + 10 (offset) is 300

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        //Test Negative numbers
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200)); //Reset
        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(-101)); //200 - 101 (offset) is 99

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());

        //Percentages
        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(40)); //40% of 200 is 80
        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(54)); //54% of 200 is 108
        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(0));
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(100));

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(51));
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonCeilingAndFloorByDailyPricingRuleType() {
        Product product = new Product();
        product.setId(1);
        ProductAccomType productAccomType1 = new ProductAccomType();
        ProductAccomType productAccomType2 = new ProductAccomType();
        ProductAccomType productAccomType3 = new ProductAccomType();
        List<ProductAccomType> productAccomTypeList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        AccomType accomType3 = new AccomType();
        accomType1.setName("DELUXE");
        accomType1.setId(1);
        accomType2.setName("QUEEN");
        accomType2.setId(3);
        accomType3.setId(5);
        productAccomType1.setProduct(product);
        productAccomType1.setAccomType(accomType1);
        productAccomType2.setProduct(product);
        productAccomType2.setAccomType(accomType2);
        productAccomType3.setProduct(product);
        productAccomType3.setAccomType(accomType3);
        accomTypeList.add(accomType1);
        accomTypeList.add(accomType2);
        accomTypeList.add(accomType3);
        productAccomTypeList.add(productAccomType1);
        productAccomTypeList.add(productAccomType2);
        productAccomTypeList.add(productAccomType3);

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(accomClassPriceRanks);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.findProductRoomTypesByProduct(product)).thenReturn(productAccomTypeList);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setProductID(1);
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(150));

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK lowestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigStartDate = new CPConfigMergedOffset();
        lowestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        lowestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedOffsetPK lowestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigMiddleDate = new CPConfigMergedOffset();
        lowestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        lowestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedOffsetPK lowestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigEndDate = new CPConfigMergedOffset();
        lowestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        lowestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedOffsetPK middleConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigStartDate = new CPConfigMergedOffset();
        middleConfigStartDate.setOffsetValue(BigDecimal.TEN);
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        middleConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedOffsetPK middleConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigMiddleDate = new CPConfigMergedOffset();
        middleConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        middleConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedOffsetPK middleConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigEndDate = new CPConfigMergedOffset();
        middleConfigEndDate.setOffsetValue(BigDecimal.TEN);
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);


        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        when(agileRatesConfigurationService.findProductById(1)).thenReturn(product);
        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(291)); //291 + 10 (offset) is 301

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Offsets: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(301));
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/>", classesInViolation.iterator().next());

        //Equal is alright
        basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig); //Reset
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(290)); //290 + 10 (offset) is 300

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        //Test Negative numbers
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200)); //Reset
        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(-101)); //200 - 101 (offset) is 99

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        //  assertEquals(1, classesInViolation.size());
        //  assertEquals("lowest - middle<BR/><b>Offsets: [</b>Default<b>]</b> <BR/><b>Minimum Pricing Difference: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle<BR/><b>Minimum Pricing Difference: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        //Percentages
        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(40)); //40% of 200 is 80
        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(54)); //54% of 200 is 108
        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Offsets: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(0, classesInViolation.size());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(0));
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(100));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Offsets: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/>", classesInViolation.iterator().next());

        middleConfigEndDate.setOffsetValue(BigDecimal.valueOf(51));
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());


        middleConfigEndDate.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest<BR/><b>Offsets: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        lowestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        classesInViolation = service.validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(basePricingAccomTypes, startDate, endDate);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle<BR/><b>Minimum Pricing Difference: [</b>Default<b>]</b> <BR/>", classesInViolation.iterator().next());

    }

    @Test
    public void validateSaveSeasonCeilingAndFloor_true_if_no_offsets_configured() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);

        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setProductID(1);
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(100));
        lowestBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(100));
        lowestBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(100));

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(200));
        middleBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(200));
        middleBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(200));

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setMondayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRate(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRate(BigDecimal.valueOf(300));

        List<PricingBaseAccomType> basePricingAccomTypes = asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig);

        //Setup Offsets Config
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = getAccomClassMinPriceDiffSeasonList();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasonList);
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(basePricingAccomTypes, startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

    }

    @Test
    public void isValidDefaultRoomClassConfig() {
        TransientPricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomType();
        lowerClassBaseConfig.setProductID(1);
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        TransientPricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomType();
        higherClassBaseConfig.setProductID(1);
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Monday----
        //Negative Values properly handled
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Tuesday----
        //Negative Values properly handled
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Wednesday----
        //Negative Values properly handled
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Thursday----
        //Negative Values properly handled
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Friday----
        //Negative Values properly handled
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Saturday----
        //Negative Values properly handled
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Floor is invalid; lower config floor > higher config floor
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(76));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Percentage offset
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(51));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));
    }

    @Test
    public void isValidDefaultRoomClassConfig_WithDraftValues() {
        PricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomTypeDraft();
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        PricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomTypeDraft();
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));
    }

    @Test
    public void isValidDefaultRoomClassConfig_PercentileOffsets() {
        TransientPricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomType();
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        TransientPricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomType();
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-10));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20)); //All negatives mean the base of 100 is the highest
        lowerOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 60
        higherOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        lowerOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Lower accom class > higher accom class is invalid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(51));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

    }

    @Test
    public void roomsConfigurationIgnoreRankingsWithoutPricingAccomClasses() {
        //   when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_ROOMS_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(accomClassPriceRanks);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(new ArrayList<PricingAccomClass>());

        assertTrue(service.getPricingAccomClassesForProperty().isEmpty());
    }

    @Test
    public void getDayOfWeek() {
        LocalDate date = new LocalDate();
        assertEquals(date.getDayOfWeek(), service.getDayOfWeek(date));
    }

    @Test
    public void validateSaveOffsetLowerThanTransientFloor() {

        List<CPConfigOffsetAccomType> offsetsToSave = asList(getLessThanFloorFixedAccomType(), getLessThanFloorPercentageAccomType(),
                getEqualToFloorFixedAccomType(), getLessThanFloorFixedAccomType2());

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validateSaveOffsetLowerThanTransientFloor(offsetsToSave, Arrays.asList(getTransientConfig1(), getTransientConfig2(), getTransientConfig3()));
        assertEquals(expectedRoomClassesWithOffsetViolation, roomClassesWithOffsetViolation);
    }

    @Test
    public void validateSaveOffsetLowerThanTransientSeasonFloor() {
        List<CPConfigOffsetAccomType> offsetsToSave = asList(getLessThanFloorFixedAccomType(), getLessThanFloorPercentageAccomType(),
                getEqualToFloorFixedAccomType(), getLessThanFloorFixedAccomType2());

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validateSaveOffsetLowerThanTransientSeasonFloor(offsetsToSave, Arrays.asList(getTransientConfig1(), getTransientConfig2(), getTransientConfig3(), getTransientConfig4()), new LocalDate());
        assertEquals(expectedRoomClassesWithOffsetViolation, roomClassesWithOffsetViolation);
    }

    @Test
    public void validatePriceExcludedOffsets() {

        List<CPConfigOffsetAccomType> offsetsToSave = asList(getZeroFixedAccomType(), getAboveZeroFixedAccomType());

        Set<String> roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.SINGLE);
        assertEquals(expectedRoomClassesWithPriceExcludedOffsetViolation, roomClassesWithOffsetViolation);

        roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.SINGLE);
        assertEquals(expectedRoomClassesWithPriceExcludedOffsetViolation, roomClassesWithOffsetViolation);

        offsetsToSave.forEach(offset -> {
            offset.setSundayOffsetValue(BigDecimal.TEN);
            offset.setMondayOffsetValue(BigDecimal.TEN);
            offset.setTuesdayOffsetValue(BigDecimal.TEN);
            offset.setWednesdayOffsetValue(BigDecimal.TEN);
            offset.setThursdayOffsetValue(BigDecimal.TEN);
            offset.setFridayOffsetValue(BigDecimal.TEN);
            offset.setSaturdayOffsetValue(BigDecimal.TEN);
        });

        roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.SINGLE);
        assertEquals(expectedRoomClassesWithPriceExcludedOffsetViolation, roomClassesWithOffsetViolation);

        offsetsToSave.forEach(offset -> {
            offset.setSundayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setMondayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setTuesdayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setWednesdayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setThursdayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setFridayOffsetValueWithTax(BigDecimal.ZERO);
            offset.setSaturdayOffsetValueWithTax(BigDecimal.ZERO);
        });

        roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.SINGLE);
        assertEquals(expectedRoomClassesWithPriceExcludedOffsetViolation, roomClassesWithOffsetViolation);

        offsetsToSave.forEach(offset -> {
            offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
            offset.setMondayOffsetValueWithTax(BigDecimal.TEN);
            offset.setTuesdayOffsetValueWithTax(BigDecimal.TEN);
            offset.setWednesdayOffsetValueWithTax(BigDecimal.TEN);
            offset.setThursdayOffsetValueWithTax(BigDecimal.TEN);
            offset.setFridayOffsetValueWithTax(BigDecimal.TEN);
            offset.setSaturdayOffsetValueWithTax(BigDecimal.TEN);
        });

        roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.SINGLE);
        assertEquals(new HashSet<>(), roomClassesWithOffsetViolation);
    }

    @Test
    public void validatePriceExcludedOffsetsPPP() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value())).thenReturn(true);

        CPConfigOffsetAccomType zeroFixedAccomType = getZeroFixedAccomType();
        zeroFixedAccomType.setOccupancyType(OccupancyType.DOUBLE);

        CPConfigOffsetAccomType aboveZeroFixedAccomType = getAboveZeroFixedAccomType();
        aboveZeroFixedAccomType.setOccupancyType(OccupancyType.DOUBLE);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(zeroFixedAccomType, aboveZeroFixedAccomType);

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validatePriceExcludedOffsets(offsetsToSave, Arrays.asList(getPricingAccomClass1(), getPricingAccomClass2()), OccupancyType.DOUBLE);
        assertEquals(expectedRoomClassesWithPriceExcludedOffsetViolation, roomClassesWithOffsetViolation);
    }

    @Test
    public void validateSaveFloorAboveZeroWithOffset_Transient() {
        List<PricingBaseAccomType> baseAccomTypes = asList(getTransientConfig1(), getTransientConfig3());
        LocalDate caughtUpDate = LocalDate.now();
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpDate);

        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY_EXCLUDING_PAST_DATA,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("systemDate", caughtUpDate)
                        .and("productID", 1).parameters()))
                .thenReturn(asList(getLessThanFloorFixedAccomType(), getEqualToFloorFixedAccomType()));

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validateSaveFloorAboveZeroWithOffset(baseAccomTypes);
        assertEquals("DELUXE", roomClassesWithOffsetViolation.iterator().next());
    }

    @Test
    public void validateSaveFloorAboveZeroWithOffset_Group() {
        List<PricingBaseAccomType> baseAccomTypes = asList(getGroupConfig1(), getGroupConfig3());
        LocalDate caughtUpDate = LocalDate.now();
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpDate);

        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY_EXCLUDING_PAST_DATA,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("systemDate", caughtUpDate)
                        .and("productID", 1).parameters()))
                .thenReturn(asList(getLessThanFloorFixedAccomType(), getEqualToFloorFixedAccomType()));

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validateSaveFloorAboveZeroWithOffset(baseAccomTypes);
        assertEquals("DELUXE", roomClassesWithOffsetViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setStartDate(null);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, lowestConfigOffsetAccomType2, middleConfigOffsetAccomType1, middleConfigOffsetAccomType2, highestConfigOffsetAccomType1, highestConfigOffsetAccomType2);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(11));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        //Equal is alright
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Test Negative numbers
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-11));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-6));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());

        //Percentages
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(40)); //40% of 20 is 8
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(60)); //60% of 20 is 12
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(20));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleType() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setStartDate(null);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, lowestConfigOffsetAccomType2, middleConfigOffsetAccomType1, middleConfigOffsetAccomType2, highestConfigOffsetAccomType1, highestConfigOffsetAccomType2);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        String expectedClassesInViolation1 = "middle - highest<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";
        String expectedClassesInViolation2 = "lowest - middle<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation1, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(0, classesInViolation.size());

        //Equal is alright
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Test Negative numbers
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-11));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-6));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation2, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation2, classesInViolation.iterator().next());

        //Percentages
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(40)); //40% of 20 is 8
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(60)); //60% of 20 is 12
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation1, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(0, classesInViolation.size());

        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(20));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation1, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(19));
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation1, classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets_notEveryDaySet() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        //Minimal inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(null);
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(null);

        List<CPConfigOffsetAccomType> offsetsToSave = singletonList(lowestConfigOffsetAccomType1);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(11));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleType_notEveryDaySet() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        //Minimal inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(null);
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(null);

        List<CPConfigOffsetAccomType> offsetsToSave = singletonList(lowestConfigOffsetAccomType1);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        String expectedClassesInViolation = "lowest - middle<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(11));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        lowestConfigEndDate.setCeilingRate(BigDecimal.valueOf(31));
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation, classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets_OnlyBaseRoomTypeOffsetExists() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));


        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.DOUBLE); //There is no SINGLE offset as this is a base room type
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, middleConfigOffsetAccomType1);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(11));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRule_OnlyBaseRoomTypeOffsetExists() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.DOUBLE); //There is no SINGLE offset as this is a base room type
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, middleConfigOffsetAccomType1);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        String expectedClassesInViolation = "lowest - middle<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = getAccomClassMinPriceDiffDefaultList();
        when(crudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffDefaultList);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(50));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation, classesInViolation.iterator().next());

        lowestConfigEndDate.setCeilingRate(BigDecimal.valueOf(21));
        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInViolation, classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets_true_if_no_ceiling_or_floor_configured() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setStartDate(null);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, lowestConfigOffsetAccomType2, middleConfigOffsetAccomType1, middleConfigOffsetAccomType2, highestConfigOffsetAccomType1, highestConfigOffsetAccomType2);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleType_true_if_no_ceiling_or_floor_configured() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setProductID(1);
        middleConfigOffsetAccomType2.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setStartDate(null);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        middleConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValue(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValue(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValue(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setStartDate(null);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType2.setMondayOffsetValue(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setTuesdayOffsetValue(BigDecimal.valueOf(6));
        highestConfigOffsetAccomType2.setWednesdayOffsetValue(BigDecimal.valueOf(6));

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType1, lowestConfigOffsetAccomType2, middleConfigOffsetAccomType1, middleConfigOffsetAccomType2, highestConfigOffsetAccomType1, highestConfigOffsetAccomType2);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveSeasonOffsets_multiplePriceExcludedClasses() {

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestConfigOffsetAccomType.setStartDate(null);
        lowestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType.setStartDate(null);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, lowestConfigOffsetAccomType2, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //The higher and middle room class will not be returned as ceiling as it is price excluded, but it should be considered
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        lowestPricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());

        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100)); //Reset
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleTypeByDailyPricingBarRule_multiplePriceExcludedClasses() {

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestConfigOffsetAccomType.setStartDate(null);
        lowestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestConfigOffsetAccomType2.setStartDate(null);
        lowestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType.setStartDate(null);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, lowestConfigOffsetAccomType2, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //The higher and middle room class will not be returned as ceiling as it is price excluded, but it should be considered
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        lowestPricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        String expectedClassesInVoilation1 = "lowest - middle<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";
        String expectedClassesInVoilation2 = "middle - highest<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(210));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation1, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation1, classesInViolation.iterator().next());

        lowestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100)); //Reset
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation2, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(301));
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);

        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation2, classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets_multiplePriceExcludedClasses_OnlyOverridingMiddleOffset() {

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);


        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, lowestConfigOffsetAccomType2, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //The higher and middle room class will not be returned as ceiling as it is price excluded, but it should be considered

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        lowestPricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        //Offsets grabbed because price excluded classes aren't being saved
        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType.setProductID(1);
        lowestOriginalConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType2.setProductID(1);
        lowestOriginalConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));

        CPConfigOffsetAccomType middleOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleOriginalConfigOffsetAccomType.setProductID(1);
        middleOriginalConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType.setProductID(1);
        highestOriginalConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(300));

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType2.setProductID(1);
        highestOriginalConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(350));

        List<CPConfigOffsetAccomType> offsets = asList(lowestOriginalConfigOffsetAccomType, lowestOriginalConfigOffsetAccomType2, middleOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType2);

        when(offsetService.retrieveOffsetConfig(false, 1)).thenReturn(offsets);

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(110));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(5)); //Reset
        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleTypeByDailyPricingBarRuleType_multiplePriceExcludedClasses_OnlyOverridingMiddleOffset() {

        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType lowestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType2.setProductID(1);
        lowestConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, lowestConfigOffsetAccomType2, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //The higher and middle room class will not be returned as ceiling as it is price excluded, but it should be considered

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(200));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        lowestPricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        //Offsets grabbed because price excluded classes aren't being saved
        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType.setProductID(1);
        lowestOriginalConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        lowestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType2.setProductID(1);
        lowestOriginalConfigOffsetAccomType2.setAccomType(lowestAccomType2);
        lowestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        lowestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));

        CPConfigOffsetAccomType middleOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleOriginalConfigOffsetAccomType.setProductID(1);
        middleOriginalConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType.setProductID(1);
        highestOriginalConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(300));
        highestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(300));

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType2.setProductID(1);
        highestOriginalConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(350));
        highestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(350));

        List<CPConfigOffsetAccomType> offsets = asList(lowestOriginalConfigOffsetAccomType, lowestOriginalConfigOffsetAccomType2, middleOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType2);

        when(offsetService.retrieveOffsetConfig(false, 1)).thenReturn(offsets);

        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        String expectedClassesInVoilation1 = "middle - highest<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";
        String expectedClassesInVoilation2 = "lowest - middle<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(110));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation1, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(199));
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation1, classesInViolation.iterator().next());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(5)); //Reset

        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210)); //Reset
        lowestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(210)); //Reset

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(300));
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation2, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation2, classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveSeasonOffsets_multiplePriceExcludedClasses_ceilingAndFloorConfigNotSet() {

        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //No ceiling and floor config saved yet
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        middlePricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        //Offsets grabbed because price excluded classes aren't being saved
        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType.setProductID(1);
        lowestOriginalConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType middleOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleOriginalConfigOffsetAccomType.setProductID(1);
        middleOriginalConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType.setProductID(1);
        highestOriginalConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType2.setProductID(1);
        highestOriginalConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));

        List<CPConfigOffsetAccomType> offsets = asList(lowestOriginalConfigOffsetAccomType, middleOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType2);

        when(offsetService.retrieveOffsetConfig(false, 1)).thenReturn(offsets);

        Set<String> classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());


        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveSeasonOffsets(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());

    }

    @Test
    public void validateSaveSeasonOffsetsByDailyPricingBarRuleType_multiplePriceExcludedClasses_ceilingAndFloorConfigNotSet() {

        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setProductID(1);
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setProductID(1);
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setProductID(1);
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //No ceiling and floor config saved yet
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        middlePricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        //Offsets grabbed because price excluded classes aren't being saved
        CPConfigOffsetAccomType lowestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestOriginalConfigOffsetAccomType.setProductID(1);
        lowestOriginalConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        lowestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(55));
        lowestOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType middleOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleOriginalConfigOffsetAccomType.setProductID(1);
        middleOriginalConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(95));
        middleOriginalConfigOffsetAccomType.setStartDate(null);

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType.setProductID(1);
        highestOriginalConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestOriginalConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));

        CPConfigOffsetAccomType highestOriginalConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestOriginalConfigOffsetAccomType2.setProductID(1);
        highestOriginalConfigOffsetAccomType2.setAccomType(highestBaseAccomType1);
        highestOriginalConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestOriginalConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestOriginalConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestOriginalConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));

        List<CPConfigOffsetAccomType> offsets = asList(lowestOriginalConfigOffsetAccomType, middleOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType, highestOriginalConfigOffsetAccomType2);

        when(offsetService.retrieveOffsetConfig(false, 1)).thenReturn(offsets);
        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);
        String expectedClassesInVoilation = "middle - highest<BR/><b>Transient Pricing: [</b>Default<b>]</b> <BR/>";

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        Set<String> classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());


        when(service.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());


        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(210));

        when(service.getDailyBarPricingRuleType()).thenReturn(2);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());

        when(service.getDailyBarPricingRuleType()).thenReturn(3);
        classesInViolation = service.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals(expectedClassesInVoilation, classesInViolation.iterator().next());

    }

    @Test
    public void testFloorOffsetCalculation() {
        assertFalse(service.floorOffsetCalculation(positive100, negative99));

        assertTrue(service.floorOffsetCalculation(positive100, negative100));
    }

    @Test
    public void testOffsetValidationForDefaults() {

        assertTrue(service.offsetValidation(null, false));

        assertTrue(service.offsetValidation(zero, false));

        assertFalse(service.offsetValidation(min, false));
    }

    @Test
    public void testOffsetValidationForSeasons() {

        assertFalse(service.offsetValidation(null, true));

        assertTrue(service.offsetValidation(zero, true));

        assertFalse(service.offsetValidation(min, true));
    }

    @Test
    public void validateSaveDefaultOffsets() {
        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType);

        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);

        //We spy this because it's tested on its own.
        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);
        //This shouldn't be called, but I stub it to prove the point and make sure the tests fails
        doReturn(true).when(service).isValidDefaultRoomClassConfig(middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false, highestBaseConfig, new HashSet<>(singletonList(highestConfigOffsetAccomType)), false);

        //See what happens when we exclude something in the middle of the list
        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, new ArrayList<>(), pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveDefaultOffsetsPPP() {
        //Setup Configuration
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value())).thenReturn(true);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType, highestConfigOffsetAccomType);

        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);

        //We spy this because it's tested on its own.
        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);
        //This shouldn't be called, but I stub it to prove the point and make sure the tests fails
        doReturn(true).when(service).isValidDefaultRoomClassConfig(middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false, highestBaseConfig, new HashSet<>(singletonList(highestConfigOffsetAccomType)), false);

        //See what happens when we exclude something in the middle of the list
        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, new ArrayList<>(), pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig), OccupancyType.DOUBLE);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void validateSaveDefaultOffsets_OnlyBaseRoomTypeOffsetExists() {
        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE); //There is no SINGLE offset as this is a base room type
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setSundayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setMondayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setTuesdayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setWednesdayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setThursdayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setFridayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setSaturdayOffsetValue(BigDecimal.ZERO);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType);

        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setSundayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setMondayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setTuesdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setWednesdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setThursdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setFridayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setSaturdayCeilingRate(BigDecimal.ZERO);
        lowestBaseConfig.setSundayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setMondayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setTuesdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setWednesdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setThursdayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setFridayFloorRate(BigDecimal.ZERO);
        lowestBaseConfig.setSaturdayFloorRate(BigDecimal.ZERO);
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setMondayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setTuesdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setWednesdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setThursdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setFridayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setSaturdayCeilingRate(BigDecimal.ZERO);
        middleBaseConfig.setSundayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setMondayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setTuesdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setWednesdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setThursdayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setFridayFloorRate(BigDecimal.ZERO);
        middleBaseConfig.setSaturdayFloorRate(BigDecimal.ZERO);

        //We spy this because it's tested on its own.
        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);

        //See what happens when we exclude something in the middle of the list
        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, accomClassPriceRanks, pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        doReturn(false).when(service).isValidDefaultRoomClassConfig(anyObject(), anyObject(), eq(false), eq(middleBaseConfig), eq(new HashSet<>(singletonList(middleConfigOffsetAccomType))), eq(false));

        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, accomClassPriceRanks, pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig), OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());

        //Switching Highest and Lowest as a base room type
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);

        doReturn(true).when(service).isValidDefaultRoomClassConfig(lowestBaseConfig, new HashSet<>(singletonList(lowestConfigOffsetAccomType)), false, middleBaseConfig, new HashSet<>(singletonList(middleConfigOffsetAccomType)), false);

        //See what happens when we exclude something in the middle of the list
        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, accomClassPriceRanks, pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        //Prove out that it returns the accom classes in conflict
        doReturn(false).when(service).isValidDefaultRoomClassConfig(eq(lowestBaseConfig), eq(new HashSet<>(singletonList(lowestConfigOffsetAccomType))), eq(false), anyObject(), anyObject(), eq(false));

        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, accomClassPriceRanks, pricingAccomClasses, Arrays.asList(lowestBaseConfig, middleBaseConfig), OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("lowest - middle", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultOffsets_priceExcludedClass_multipleRoomTypes_higherClass() {
        //Setup Offset Inputs
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));

        //The higher room class will not be returned as ceiling as it is price excluded, but it should be considered

        highestPricingAccomClass.setPriceExcluded(true);

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.TEN);

        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), singletonList(middleBaseConfig), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(110));

        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), singletonList(middleBaseConfig), OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }


    @Test
    public void validateSaveDefaultOffsets_priceExcludedClass_multipleRoomTypes_lowerClass() {
        //Setup Offset Inputs
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setAccomType(middleAccomType2);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        middleConfigOffsetAccomType2.setStartDate(null);

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, middleConfigOffsetAccomType2, highestConfigOffsetAccomType);

        //The middle room class will not be returned as ceiling as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(250));

        middlePricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), singletonList(highestBaseConfig), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(350));

        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), singletonList(highestBaseConfig), OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultOffsets_multiplePriceExcludedClasses() {
        //Setup Offset Inputs
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType.setStartDate(null);
        middleConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(100));
        middleConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(100));

        CPConfigOffsetAccomType middleConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType2.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        middleConfigOffsetAccomType2.setStartDate(null);
        middleConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(150));
        middleConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(150));

        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(middleConfigOffsetAccomType, middleConfigOffsetAccomType2, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);

        //The higher and middle room class will not be returned as ceiling as it is price excluded, but it should be considered

        middlePricingAccomClass.setPriceExcluded(true);
        highestPricingAccomClass.setPriceExcluded(true);

        middleConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.TEN);

        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), Collections.emptyList(), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        middleConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(210));

        classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, Arrays.asList(upperAccomClassPriceRank), Arrays.<PricingAccomClass>asList(middlePricingAccomClass, highestPricingAccomClass), Collections.emptyList(), OccupancyType.SINGLE);
        assertEquals(1, classesInViolation.size());
        assertEquals("middle - highest", classesInViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultOffsets_true_if_no_ceiling_or_floor_configured() {
        //Setup Configuration
        List<PricingAccomClass> pricingAccomClasses = asList(lowestPricingAccomClass, middlePricingAccomClass);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsetsToSave = asList(lowestConfigOffsetAccomType, middleConfigOffsetAccomType);

        // No Ceiling or Floor data configured
        Set<String> classesInViolation = service.validateSaveDefaultOffsets(offsetsToSave, new ArrayList<>(), pricingAccomClasses, Collections.emptyList(), OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void isValidDefaultRoomClassConfig_TaxInclusive() {
        TransientPricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomType();
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        TransientPricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomType();
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Monday----
        //Negative Values properly handled
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setMondayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Tuesday----
        //Negative Values properly handled
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Wednesday----
        //Negative Values properly handled
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Thursday----
        //Negative Values properly handled
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Friday----
        //Negative Values properly handled
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setFridayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //----Saturday----
        //Negative Values properly handled
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Floor is invalid; lower config floor > higher config floor
        lowerOffset1.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        lowerOffset2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(0));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(76));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Percentage offset
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerOffset1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(51));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));
    }

    @Test
    public void isValidDefaultRoomClassConfig_TaxInclusive_DraftValues() {
        PricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomTypeDraft();
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(10));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(10));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        PricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomTypeDraft();
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-30));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //All negatives mean the base of 100 is the highest
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 90
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20));

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));
    }

    @Test
    public void isValidDefaultRoomClassConfig_PercentileOffsets_TaxInclusive() {
        TransientPricingBaseAccomType lowerClassBaseConfig = new TransientPricingBaseAccomType();
        lowerClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowerClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        lowerClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));

        CPConfigOffsetAccomType lowerOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType lowerOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> lowerClassAccomTypes = new HashSet<>(asList(lowerOffset1, lowerOffset2));

        TransientPricingBaseAccomType higherClassBaseConfig = new TransientPricingBaseAccomType();
        higherClassBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(150));
        higherClassBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(75));
        higherClassBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(75));

        CPConfigOffsetAccomType higherOffset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset2 = new CPConfigOffsetAccomType();

        Set<CPConfigOffsetAccomType> higherClassAccomTypes = new HashSet<>(asList(higherOffset1, higherOffset2));

        //----Sunday----

        //Negative Values properly handled
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-10));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(-20)); //All negatives mean the base of 100 is the highest
        lowerOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(-60)); //This makes the lowest 60
        higherOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Normal Case
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        lowerOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(20));
        lowerOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(0));
        higherOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);
        higherOffset2.setSundayOffsetValueWithTax(BigDecimal.valueOf(10));
        higherOffset2.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Equal Values Are Valid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(50));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertTrue(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));

        //Lower accom class > higher accom class is invalid
        lowerOffset1.setSundayOffsetValueWithTax(BigDecimal.valueOf(51));
        lowerOffset1.setOffsetMethod(OffsetMethod.PERCENTAGE);

        assertFalse(service.isValidDefaultRoomClassConfig(lowerClassBaseConfig, lowerClassAccomTypes, false, higherClassBaseConfig, higherClassAccomTypes, false));
    }

    @Test
    public void validateSaveFloorAboveZeroWithOffset_Transient_TaxInclusive() {
        List<PricingBaseAccomType> baseAccomTypes = asList(getTransientConfig1(), getTransientConfig3());
        LocalDate caughtUpDate = LocalDate.now();
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpDate);

        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY_EXCLUDING_PAST_DATA,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("systemDate", caughtUpDate)
                        .and("productID", 1)
                        .parameters()))
                .thenReturn(asList(getLessThanFloorFixedAccomType(), getEqualToFloorFixedAccomType()));

        Set<String> roomClassesWithOffsetViolation;
        roomClassesWithOffsetViolation = service.validateSaveFloorAboveZeroWithOffset(baseAccomTypes);

        assertEquals("DELUXE", roomClassesWithOffsetViolation.iterator().next());
    }

    @Test
    public void validateSaveDefaultCeilingAndFloor_priceExcludedClass_lowerClass_OffsetsNotYetConfigured_TaxInclusive() {
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(asList(middlePricingAccomClass, highestPricingAccomClass));

        //Setup Offsets
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setProductID(1);
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setStartDate(null);

        List<CPConfigOffsetAccomType> offsets = singletonList(highestConfigOffsetAccomType);
        when(offsetService.retrieveOffsetConfig(true, 1)).thenReturn(offsets);

        //The middle room class will not be on the input as it is price excluded, but it should be considered

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(300));

        List<PricingBaseAccomType> basePricingAccomTypes = singletonList(highestBaseConfig);

        middlePricingAccomClass.setPriceExcluded(true);

        Set<String> classesInViolation = service.validateSaveDefaultCeilingAndFloor(basePricingAccomTypes);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void getAllPricingBaseAccomTypes() {
        service.getAllPricingBaseAccomTypes();
        verify(crudService, times(1)).findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void testValidationOfSaveSeasonCeilingAndFloorWithIndependentProducts() {
        // validations should work generally the same - it just has a chance to null out if the mapping doesnt work
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(accomClassPriceRanks);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        //Setup Base Inputs
        TransientPricingBaseAccomType lowestBaseConfig = new TransientPricingBaseAccomType();
        lowestBaseConfig.setProductID(1);
        lowestBaseConfig.setAccomType(lowestBaseAccomType1);
        lowestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        lowestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        lowestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));

        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setProductID(1);
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(200));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(150));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(150));

        TransientPricingBaseAccomType highestBaseConfig = new TransientPricingBaseAccomType();
        highestBaseConfig.setProductID(1);
        highestBaseConfig.setAccomType(highestBaseAccomType1);
        highestBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(300));
        highestBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(250));
        highestBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(250));

        // setup offsets so that price rank validations will run
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate)).thenReturn(offsetConfigForBaseOccupancyType);

        CPConfigMergedOffsetPK lowestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigStartDate = new CPConfigMergedOffset();
        lowestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        lowestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedOffsetPK lowestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigMiddleDate = new CPConfigMergedOffset();
        lowestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        lowestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedOffsetPK lowestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, lowestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset lowestConfigEndDate = new CPConfigMergedOffset();
        lowestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        lowestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedOffsetPK middleConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigStartDate = new CPConfigMergedOffset();
        middleConfigStartDate.setOffsetValue(BigDecimal.TEN);
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        middleConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedOffsetPK middleConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigMiddleDate = new CPConfigMergedOffset();
        middleConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        middleConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedOffsetPK middleConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, middleBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset middleConfigEndDate = new CPConfigMergedOffset();
        middleConfigEndDate.setOffsetValue(BigDecimal.TEN);
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        middleConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedOffsetPK highestConfigKeyStartDate = new CPConfigMergedOffsetPK(startDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigStartDate = new CPConfigMergedOffset();
        highestConfigStartDate.setOffsetValue(BigDecimal.TEN);
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        highestConfigStartDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedOffsetPK highestConfigKeyMiddleDate = new CPConfigMergedOffsetPK(middleDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigMiddleDate = new CPConfigMergedOffset();
        highestConfigMiddleDate.setOffsetValue(BigDecimal.TEN);
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        highestConfigMiddleDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedOffsetPK highestConfigKeyEndDate = new CPConfigMergedOffsetPK(endDate, 1, highestBaseAccomType1.getId(), OccupancyType.SINGLE);
        CPConfigMergedOffset highestConfigEndDate = new CPConfigMergedOffset();
        highestConfigEndDate.setOffsetValue(BigDecimal.TEN);
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        highestConfigEndDate.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offsetConfigForBaseOccupancyType.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        Set<String> classesInViolation = service.validateSaveSeasonCeilingAndFloor(asList(lowestBaseConfig, middleBaseConfig, highestBaseConfig), startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(asList(lowestBaseConfig, middleBaseConfig), startDate, endDate);
        assertTrue(classesInViolation.isEmpty());

        classesInViolation = service.validateSaveSeasonCeilingAndFloor(asList(middleBaseConfig, highestBaseConfig), startDate, endDate);
        assertTrue(classesInViolation.isEmpty());
    }

    @Test
    public void testGetAllPricingBaseAccomTypesByProduct() {
        service.getAllPricingBaseAccomTypesByProductId(1);
        verify(crudService, times(1)).findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_ID,
                QueryParameter.with("productID", 1).parameters());
    }

    @Test
    public void testValidateSaveSeasonOffsetsWithIndependentProducts() {
        LocalDate startDate = new LocalDate();
        LocalDate middleDate = startDate.plusDays(1);
        LocalDate endDate = startDate.plusDays(2);

        //Setup Offset Inputs
        CPConfigOffsetAccomType lowestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        lowestConfigOffsetAccomType1.setProductID(1);
        lowestConfigOffsetAccomType1.setAccomType(lowestBaseAccomType1);
        lowestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        lowestConfigOffsetAccomType1.setStartDate(null);
        lowestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        lowestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        lowestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType middleConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType1.setProductID(1);
        middleConfigOffsetAccomType1.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType1.setStartDate(null);
        middleConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        middleConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        middleConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        CPConfigOffsetAccomType highestConfigOffsetAccomType1 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType1.setProductID(1);
        highestConfigOffsetAccomType1.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType1.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType1.setStartDate(null);
        highestConfigOffsetAccomType1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType1.setMondayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(5));
        highestConfigOffsetAccomType1.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(5));

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = new HashMap<>();
        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false)).thenReturn(ceilingAndFloorConfigForDates);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigStartDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigStartDate.setFloorRate(BigDecimal.TEN);
        lowestConfigStartDate.setId(lowestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyStartDate, lowestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigMiddleDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setFloorRate(BigDecimal.TEN);
        lowestConfigMiddleDate.setId(lowestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyMiddleDate, lowestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK lowestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, lowestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor lowestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        lowestConfigEndDate.setCeilingRate(BigDecimal.TEN);
        lowestConfigEndDate.setFloorRate(BigDecimal.TEN);
        lowestConfigEndDate.setId(lowestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(lowestConfigKeyEndDate, lowestConfigEndDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigStartDate = new CPConfigMergedCeilingAndFloor();
        middleConfigStartDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigStartDate.setId(middleConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyStartDate, middleConfigStartDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        middleConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigMiddleDate.setId(middleConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyMiddleDate, middleConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK middleConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, middleBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor middleConfigEndDate = new CPConfigMergedCeilingAndFloor();
        middleConfigEndDate.setCeilingRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setFloorRate(BigDecimal.valueOf(20));
        middleConfigEndDate.setId(middleConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(middleConfigKeyEndDate, middleConfigEndDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyStartDate = new CPConfigMergedCeilingAndFloorPK(1, startDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigStartDate = new CPConfigMergedCeilingAndFloor();
        highestConfigStartDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigStartDate.setId(highestConfigKeyStartDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyStartDate, highestConfigStartDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyMiddleDate = new CPConfigMergedCeilingAndFloorPK(1, middleDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigMiddleDate = new CPConfigMergedCeilingAndFloor();
        highestConfigMiddleDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigMiddleDate.setId(highestConfigKeyMiddleDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyMiddleDate, highestConfigMiddleDate);

        CPConfigMergedCeilingAndFloorPK highestConfigKeyEndDate = new CPConfigMergedCeilingAndFloorPK(1, endDate, highestBaseAccomType1.getId());
        CPConfigMergedCeilingAndFloor highestConfigEndDate = new CPConfigMergedCeilingAndFloor();
        highestConfigEndDate.setCeilingRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setFloorRate(BigDecimal.valueOf(30));
        highestConfigEndDate.setId(highestConfigKeyEndDate);
        ceilingAndFloorConfigForDates.put(highestConfigKeyEndDate, highestConfigEndDate);

        //Stubbed for consistent testing
        doReturn(DateTimeConstants.MONDAY).when(service).getDayOfWeek(startDate);
        doReturn(DateTimeConstants.TUESDAY).when(service).getDayOfWeek(startDate.plusDays(1));
        doReturn(DateTimeConstants.WEDNESDAY).when(service).getDayOfWeek(startDate.plusDays(2));

        // set independent product toggle = true
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        // middle rc has both lower and higher ranked rc and no rcs in violation, so we should get back nothing
        Set<String> classesInViolation = service.validateSaveSeasonOffsets(
                new ArrayList<>(Arrays.asList(lowestConfigOffsetAccomType1, middleConfigOffsetAccomType1, highestConfigOffsetAccomType1)),
                startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        // middle rc is missing a higher ranked rc, and validations still work as expected
        classesInViolation = service.validateSaveSeasonOffsets(
                new ArrayList<>(Arrays.asList(lowestConfigOffsetAccomType1, middleConfigOffsetAccomType1)),
                startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        // middle rc is missing a lower ranked rc, and validations still work as expected
        classesInViolation = service.validateSaveSeasonOffsets(
                new ArrayList<>(Arrays.asList(middleConfigOffsetAccomType1, highestConfigOffsetAccomType1)),
                startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());

        // middle rc is missing both a higher and lower ranked rc, and validations still work as expected
        classesInViolation = service.validateSaveSeasonOffsets(
                new ArrayList<>(Arrays.asList(middleConfigOffsetAccomType1)),
                startDate, endDate, accomClassPriceRanks, pricingAccomClasses, OccupancyType.SINGLE);
        assertTrue(classesInViolation.isEmpty());
    }

    private CPConfigOffsetAccomType getZeroFixedAccomType() {
        CPConfigOffsetAccomType zeroFixedAccomType = new CPConfigOffsetAccomType();
        zeroFixedAccomType.setAccomType(lowestBaseAccomType1);
        zeroFixedAccomType.setOccupancyType(OccupancyType.SINGLE);
        zeroFixedAccomType.setStartDate(null);
        zeroFixedAccomType.setSundayOffsetValue(null);
        zeroFixedAccomType.setMondayOffsetValue(zero);
        zeroFixedAccomType.setTuesdayOffsetValue(zero);
        zeroFixedAccomType.setWednesdayOffsetValue(zero);
        zeroFixedAccomType.setThursdayOffsetValue(zero);
        zeroFixedAccomType.setFridayOffsetValue(zero);
        zeroFixedAccomType.setSaturdayOffsetValue(zero);
        zeroFixedAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        return zeroFixedAccomType;
    }

    private CPConfigOffsetAccomType getAboveZeroFixedAccomType() {
        CPConfigOffsetAccomType aboveZeroFixedAccomType = new CPConfigOffsetAccomType();
        aboveZeroFixedAccomType.setAccomType(middleBaseAccomType1);
        aboveZeroFixedAccomType.setOccupancyType(OccupancyType.SINGLE);
        aboveZeroFixedAccomType.setStartDate(null);
        aboveZeroFixedAccomType.setSundayOffsetValue(null);
        aboveZeroFixedAccomType.setMondayOffsetValue(negative99);
        aboveZeroFixedAccomType.setTuesdayOffsetValue(negative99);
        aboveZeroFixedAccomType.setWednesdayOffsetValue(negative99);
        aboveZeroFixedAccomType.setThursdayOffsetValue(negative99);
        aboveZeroFixedAccomType.setFridayOffsetValue(negative99);
        aboveZeroFixedAccomType.setSaturdayOffsetValue(negative99);
        aboveZeroFixedAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        return aboveZeroFixedAccomType;
    }

    private PricingAccomClass getPricingAccomClass1() {
        lowestPricingAccomClass.setPriceExcluded(true);
        return lowestPricingAccomClass;
    }

    private PricingAccomClass getPricingAccomClass2() {
        middlePricingAccomClass.setPriceExcluded(false);
        return middlePricingAccomClass;
    }

    private CPConfigOffsetAccomType getLessThanFloorFixedAccomType() {
        CPConfigOffsetAccomType lessThanFloorFixedAccomType = new CPConfigOffsetAccomType();
        lessThanFloorFixedAccomType.setAccomType(lowestBaseAccomType1);
        lessThanFloorFixedAccomType.setOccupancyType(OccupancyType.SINGLE);
        lessThanFloorFixedAccomType.setStartDate(null);

        lessThanFloorFixedAccomType.setSundayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setMondayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setTuesdayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setWednesdayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setThursdayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setFridayOffsetValue(negative100);
        lessThanFloorFixedAccomType.setSaturdayOffsetValue(negative100);

        lessThanFloorFixedAccomType.setSundayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setMondayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setTuesdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setWednesdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setThursdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setFridayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType.setSaturdayOffsetValueWithTax(negative100);

        lessThanFloorFixedAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        return lessThanFloorFixedAccomType;
    }

    private CPConfigOffsetAccomType getLessThanFloorPercentageAccomType() {
        CPConfigOffsetAccomType lessThanFloorPercentageAccomType = new CPConfigOffsetAccomType();
        lessThanFloorPercentageAccomType.setAccomType(lowestAccomType2);
        lessThanFloorPercentageAccomType.setOccupancyType(OccupancyType.SINGLE);
        lessThanFloorPercentageAccomType.setStartDate(null);
        lessThanFloorPercentageAccomType.setSundayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setMondayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setTuesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setWednesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setThursdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setFridayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setSaturdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setOffsetMethod(OffsetMethod.PERCENTAGE);

        lessThanFloorPercentageAccomType.setSundayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setMondayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setTuesdayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setWednesdayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setThursdayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setFridayOffsetValueWithTax(negative100);
        lessThanFloorPercentageAccomType.setSaturdayOffsetValueWithTax(negative100);

        return lessThanFloorPercentageAccomType;
    }

    private CPConfigOffsetAccomType getEqualToFloorFixedAccomType() {
        CPConfigOffsetAccomType equalToFloorFixedAccomType = new CPConfigOffsetAccomType();
        equalToFloorFixedAccomType.setAccomType(middleBaseAccomType1);
        equalToFloorFixedAccomType.setOccupancyType(OccupancyType.SINGLE);
        equalToFloorFixedAccomType.setStartDate(null);
        equalToFloorFixedAccomType.setSundayOffsetValue(null);
        equalToFloorFixedAccomType.setMondayOffsetValue(negative99);
        equalToFloorFixedAccomType.setTuesdayOffsetValue(negative99);
        equalToFloorFixedAccomType.setWednesdayOffsetValue(negative99);
        equalToFloorFixedAccomType.setThursdayOffsetValue(negative99);
        equalToFloorFixedAccomType.setFridayOffsetValue(negative99);
        equalToFloorFixedAccomType.setSaturdayOffsetValue(negative99);

        equalToFloorFixedAccomType.setSundayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setMondayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setTuesdayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setWednesdayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setThursdayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setFridayOffsetValueWithTax(negative99);
        equalToFloorFixedAccomType.setSaturdayOffsetValueWithTax(negative99);

        equalToFloorFixedAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        return equalToFloorFixedAccomType;
    }


    private CPConfigOffsetAccomType getLessThanFloorFixedAccomType2() {
        CPConfigOffsetAccomType lessThanFloorFixedAccomType2 = new CPConfigOffsetAccomType();
        lessThanFloorFixedAccomType2.setAccomType(middleAccomType2);
        lessThanFloorFixedAccomType2.setOccupancyType(OccupancyType.SINGLE);
        lessThanFloorFixedAccomType2.setStartDate(null);
        lessThanFloorFixedAccomType2.setSundayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setMondayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setTuesdayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setWednesdayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setThursdayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setFridayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setSaturdayOffsetValue(negative100);
        lessThanFloorFixedAccomType2.setSundayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setMondayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setTuesdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setWednesdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setThursdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setFridayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setSaturdayOffsetValueWithTax(negative100);
        lessThanFloorFixedAccomType2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        return lessThanFloorFixedAccomType2;
    }

    private TransientPricingBaseAccomType getTransientConfig1() {
        TransientPricingBaseAccomType transientConfig1 = new TransientPricingBaseAccomType();
        transientConfig1.setProductID(1);
        transientConfig1.setAccomType(lowestBaseAccomType1);
        transientConfig1.setEndDate(new LocalDate().plusDays(4));

        transientConfig1.setSundayFloorRate(positive100);
        transientConfig1.setMondayFloorRate(positive100);
        transientConfig1.setTuesdayFloorRate(positive100);
        transientConfig1.setWednesdayFloorRate(positive100);
        transientConfig1.setThursdayFloorRate(positive100);
        transientConfig1.setFridayFloorRate(positive100);
        transientConfig1.setSaturdayFloorRate(positive100);

        transientConfig1.setSundayFloorRateWithTax(positive100);
        transientConfig1.setMondayFloorRateWithTax(positive100);
        transientConfig1.setTuesdayFloorRateWithTax(positive100);
        transientConfig1.setWednesdayFloorRateWithTax(positive100);
        transientConfig1.setThursdayFloorRateWithTax(positive100);
        transientConfig1.setFridayFloorRateWithTax(positive100);
        transientConfig1.setSaturdayFloorRateWithTax(positive100);

        return transientConfig1;
    }

    private TransientPricingBaseAccomType getTransientConfig2() {
        TransientPricingBaseAccomType transientConfig2 = new TransientPricingBaseAccomType();
        transientConfig2.setProductID(1);
        transientConfig2.setAccomType(lowestAccomType2);
        transientConfig2.setEndDate(new LocalDate().plusDays(1));
        transientConfig2.setSundayFloorRate(positive100);
        transientConfig2.setMondayFloorRate(positive100);
        transientConfig2.setTuesdayFloorRate(positive100);
        transientConfig2.setWednesdayFloorRate(positive100);
        transientConfig2.setThursdayFloorRate(positive100);
        transientConfig2.setFridayFloorRate(positive100);
        transientConfig2.setSaturdayFloorRate(positive100);

        return transientConfig2;
    }

    private TransientPricingBaseAccomType getTransientConfig3() {
        TransientPricingBaseAccomType transientConfig3 = new TransientPricingBaseAccomType();
        transientConfig3.setProductID(1);
        transientConfig3.setAccomType(middleBaseAccomType1);
        transientConfig3.setEndDate(new LocalDate().plusDays(2));

        transientConfig3.setSundayFloorRate(positive100);
        transientConfig3.setMondayFloorRate(positive100);
        transientConfig3.setTuesdayFloorRate(positive100);
        transientConfig3.setWednesdayFloorRate(positive100);
        transientConfig3.setThursdayFloorRate(positive100);
        transientConfig3.setFridayFloorRate(positive100);
        transientConfig3.setSaturdayFloorRate(positive100);

        transientConfig3.setSundayFloorRateWithTax(positive100);
        transientConfig3.setMondayFloorRateWithTax(positive100);
        transientConfig3.setTuesdayFloorRateWithTax(positive100);
        transientConfig3.setWednesdayFloorRateWithTax(positive100);
        transientConfig3.setThursdayFloorRateWithTax(positive100);
        transientConfig3.setFridayFloorRateWithTax(positive100);
        transientConfig3.setSaturdayFloorRateWithTax(positive100);

        return transientConfig3;
    }

    private TransientPricingBaseAccomType getTransientConfig4() {
        TransientPricingBaseAccomType transientConfig4 = new TransientPricingBaseAccomType();
        transientConfig4.setAccomType(middleAccomType2);
        transientConfig4.setEndDate(new LocalDate().minusDays(1));
        transientConfig4.setSundayFloorRate(positive100);
        transientConfig4.setMondayFloorRate(positive100);
        transientConfig4.setTuesdayFloorRate(positive100);
        transientConfig4.setWednesdayFloorRate(positive100);
        transientConfig4.setThursdayFloorRate(positive100);
        transientConfig4.setFridayFloorRate(positive100);
        transientConfig4.setSaturdayFloorRate(positive100);

        return transientConfig4;
    }

    private GroupPricingBaseAccomType getGroupConfig1() {
        GroupPricingBaseAccomType groupConfig1 = new GroupPricingBaseAccomType();
        groupConfig1.setProductID(1);
        groupConfig1.setAccomType(lowestBaseAccomType1);
        groupConfig1.setEndDate(new LocalDate().plusDays(1));

        groupConfig1.setSundayFloorRate(positive100);
        groupConfig1.setMondayFloorRate(positive100);
        groupConfig1.setTuesdayFloorRate(positive100);
        groupConfig1.setWednesdayFloorRate(positive100);
        groupConfig1.setThursdayFloorRate(positive100);
        groupConfig1.setFridayFloorRate(positive100);
        groupConfig1.setSaturdayFloorRate(positive100);

        groupConfig1.setSundayFloorRateWithTax(positive100);
        groupConfig1.setMondayFloorRateWithTax(positive100);
        groupConfig1.setTuesdayFloorRateWithTax(positive100);
        groupConfig1.setWednesdayFloorRateWithTax(positive100);
        groupConfig1.setThursdayFloorRateWithTax(positive100);
        groupConfig1.setFridayFloorRateWithTax(positive100);
        groupConfig1.setSaturdayFloorRateWithTax(positive100);

        return groupConfig1;
    }

    private GroupPricingBaseAccomType getGroupConfig2() {
        GroupPricingBaseAccomType groupConfig2 = new GroupPricingBaseAccomType();
        groupConfig2.setProductID(1);
        groupConfig2.setAccomType(lowestAccomType2);
        groupConfig2.setEndDate(new LocalDate().plusDays(2));
        groupConfig2.setSundayFloorRate(positive100);
        groupConfig2.setMondayFloorRate(positive100);
        groupConfig2.setTuesdayFloorRate(positive100);
        groupConfig2.setWednesdayFloorRate(positive100);
        groupConfig2.setThursdayFloorRate(positive100);
        groupConfig2.setFridayFloorRate(positive100);
        groupConfig2.setSaturdayFloorRate(positive100);

        groupConfig2.setSundayFloorRateWithTax(positive100);
        groupConfig2.setMondayFloorRateWithTax(positive100);
        groupConfig2.setTuesdayFloorRateWithTax(positive100);
        groupConfig2.setWednesdayFloorRateWithTax(positive100);
        groupConfig2.setThursdayFloorRateWithTax(positive100);
        groupConfig2.setFridayFloorRateWithTax(positive100);
        groupConfig2.setSaturdayFloorRateWithTax(positive100);

        return groupConfig2;
    }

    private GroupPricingBaseAccomType getGroupConfig3() {
        GroupPricingBaseAccomType groupConfig3 = new GroupPricingBaseAccomType();
        groupConfig3.setProductID(1);
        groupConfig3.setAccomType(middleBaseAccomType1);
        groupConfig3.setEndDate(new LocalDate().plusDays(3));

        groupConfig3.setSundayFloorRate(positive100);
        groupConfig3.setMondayFloorRate(positive100);
        groupConfig3.setTuesdayFloorRate(positive100);
        groupConfig3.setWednesdayFloorRate(positive100);
        groupConfig3.setThursdayFloorRate(positive100);
        groupConfig3.setFridayFloorRate(positive100);
        groupConfig3.setSaturdayFloorRate(positive100);


        groupConfig3.setSundayFloorRateWithTax(positive100);
        groupConfig3.setMondayFloorRateWithTax(positive100);
        groupConfig3.setTuesdayFloorRateWithTax(positive100);
        groupConfig3.setWednesdayFloorRateWithTax(positive100);
        groupConfig3.setThursdayFloorRateWithTax(positive100);
        groupConfig3.setFridayFloorRateWithTax(positive100);
        groupConfig3.setSaturdayFloorRateWithTax(positive100);

        return groupConfig3;
    }

    private GroupPricingBaseAccomType getGroupConfig4() {
        GroupPricingBaseAccomType groupConfig4 = new GroupPricingBaseAccomType();
        groupConfig4.setAccomType(middleAccomType2);
        groupConfig4.setEndDate(new LocalDate().minusDays(1));

        groupConfig4.setSundayFloorRate(positive100);
        groupConfig4.setMondayFloorRate(positive100);
        groupConfig4.setTuesdayFloorRate(positive100);
        groupConfig4.setWednesdayFloorRate(positive100);
        groupConfig4.setThursdayFloorRate(positive100);
        groupConfig4.setFridayFloorRate(positive100);
        groupConfig4.setSaturdayFloorRate(positive100);

        groupConfig4.setSundayFloorRateWithTax(positive100);
        groupConfig4.setMondayFloorRateWithTax(positive100);
        groupConfig4.setTuesdayFloorRateWithTax(positive100);
        groupConfig4.setWednesdayFloorRateWithTax(positive100);
        groupConfig4.setThursdayFloorRateWithTax(positive100);
        groupConfig4.setFridayFloorRateWithTax(positive100);
        groupConfig4.setSaturdayFloorRateWithTax(positive100);

        return groupConfig4;
    }

    private GroupPricingBaseAccomType getGroupConfig5() {
        GroupPricingBaseAccomType groupConfig5 = new GroupPricingBaseAccomType();
        groupConfig5.setAccomType(lowestBaseAccomType1);
        groupConfig5.setStartDate(new LocalDate().plusDays(1));
        groupConfig5.setEndDate(new LocalDate().plusDays(10));

        groupConfig5.setSundayFloorRate(positive100);
        groupConfig5.setMondayFloorRate(positive100);
        groupConfig5.setTuesdayFloorRate(positive100);
        groupConfig5.setWednesdayFloorRate(positive100);
        groupConfig5.setThursdayFloorRate(positive100);
        groupConfig5.setFridayFloorRate(positive100);
        groupConfig5.setSaturdayFloorRate(positive100);

        groupConfig5.setSundayFloorRateWithTax(positive100);
        groupConfig5.setMondayFloorRateWithTax(positive100);
        groupConfig5.setTuesdayFloorRateWithTax(positive100);
        groupConfig5.setWednesdayFloorRateWithTax(positive100);
        groupConfig5.setThursdayFloorRateWithTax(positive100);
        groupConfig5.setFridayFloorRateWithTax(positive100);
        groupConfig5.setSaturdayFloorRateWithTax(positive100);

        return groupConfig5;
    }

    private CPConfigOffsetAccomType getSeasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig() {
        CPConfigOffsetAccomType seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig = new CPConfigOffsetAccomType();
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setAccomType(lowestBaseAccomType1);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setOccupancyType(OccupancyType.SINGLE);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setStartDate(new LocalDate().plusDays(2));
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setEndDate(new LocalDate().plusDays(12));
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setSundayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setMondayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setTuesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setWednesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setThursdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setFridayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setSaturdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setSundayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setMondayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setTuesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setWednesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setThursdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setFridayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig.setSaturdayOffsetValueWithTax(negative100);

        return seasonLessThanFloorFixedAccomTypeStartDateAfterStartOfConfig;
    }

    private CPConfigOffsetAccomType getSeasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig() {
        CPConfigOffsetAccomType seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig = new CPConfigOffsetAccomType();
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setAccomType(lowestBaseAccomType1);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setOccupancyType(OccupancyType.SINGLE);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setStartDate(new LocalDate().minusDays(2));
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setEndDate(new LocalDate().plusDays(4));

        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setSundayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setMondayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setTuesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setWednesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setThursdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setFridayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setSaturdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setSundayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setMondayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setTuesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setWednesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setThursdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setFridayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig.setSaturdayOffsetValueWithTax(negative100);

        return seasonLessThanFloorFixedAccomTypeEndDateBeforeEndOfConfig;
    }

    private CPConfigOffsetAccomType getSeasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig() {
        CPConfigOffsetAccomType seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig = new CPConfigOffsetAccomType();
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setAccomType(lowestBaseAccomType1);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setOccupancyType(OccupancyType.SINGLE);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setStartDate(new LocalDate().minusDays(2));
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setEndDate(new LocalDate().plusDays(2));

        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setSundayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setMondayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setTuesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setWednesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setThursdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setFridayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setSaturdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setSundayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setMondayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setTuesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setWednesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setThursdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setFridayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig.setSaturdayOffsetValueWithTax(negative100);

        return seasonLessThanFloorFixedAccomTypeStartAndEndDateOverlapEntireConfig;
    }

    private CPConfigOffsetAccomType getSeasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig() {
        CPConfigOffsetAccomType seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig = new CPConfigOffsetAccomType();
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setAccomType(lowestBaseAccomType1);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setOccupancyType(OccupancyType.SINGLE);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setStartDate(new LocalDate().plusDays(12));
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setEndDate(new LocalDate().plusDays(17));

        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setSundayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setMondayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setTuesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setWednesdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setThursdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setFridayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setSaturdayOffsetValue(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setSundayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setMondayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setTuesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setWednesdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setThursdayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setFridayOffsetValueWithTax(negative100);
        seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig.setSaturdayOffsetValueWithTax(negative100);

        return seasonLessThanFloorFixedAccomTypeStartAndEndDateOutsideConfig;
    }

    private List<AccomClassMinPriceDiff> getAccomClassMinPriceDiffDefaultList() {
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaultList = new ArrayList<AccomClassMinPriceDiff>();
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = service.getPricingAccomClassesForProperty();
        if (!pricingAccomClassRankDtos.isEmpty()) {
            accomClassPriceRank.setHigherRankAccomClass(pricingAccomClassRankDtos.get(0).getHigherPricingAccomClass().getAccomClass());
            accomClassPriceRank.setLowerRankAccomClass(pricingAccomClassRankDtos.get(0).getLowerPricingAccomClass().getAccomClass());
            accomClassMinPriceDiff.setAccomClassPriceRank(accomClassPriceRank);
            accomClassMinPriceDiff.setMondayDiffWithTax(BigDecimal.valueOf(2));
            accomClassMinPriceDiff.setTuesdayDiffWithTax(BigDecimal.valueOf(4));
            accomClassMinPriceDiff.setWednesdayDiffWithTax(BigDecimal.valueOf(6));
            accomClassMinPriceDiff.setThursdayDiffWithTax(BigDecimal.valueOf(8));
            accomClassMinPriceDiff.setFridayDiffWithTax(BigDecimal.valueOf(10));
            accomClassMinPriceDiff.setSaturdayDiffWithTax(BigDecimal.valueOf(12));
            accomClassMinPriceDiff.setSundayDiffWithTax(BigDecimal.valueOf(14));
            accomClassMinPriceDiffDefaultList.add(accomClassMinPriceDiff);
        }
        return accomClassMinPriceDiffDefaultList;
    }

    public List<AccomClassMinPriceDiffSeason> getAccomClassMinPriceDiffSeasonList() {
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonList = new ArrayList<AccomClassMinPriceDiffSeason>();

        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = service.getPricingAccomClassesForProperty();
        if (!pricingAccomClassRankDtos.isEmpty()) {
            accomClassPriceRank.setHigherRankAccomClass(pricingAccomClassRankDtos.get(0).getHigherPricingAccomClass().getAccomClass());
            accomClassPriceRank.setLowerRankAccomClass(pricingAccomClassRankDtos.get(0).getLowerPricingAccomClass().getAccomClass());

            AccomClassMinPriceDiffSeason accomClassMinPriceDiffSeason = new AccomClassMinPriceDiffSeason();
            accomClassMinPriceDiffSeason.setAccomClassPriceRank(accomClassPriceRank);
            accomClassMinPriceDiffSeason.setMondayDiffWithTax(BigDecimal.valueOf(1));
            accomClassMinPriceDiffSeason.setTuesdayDiffWithTax(BigDecimal.valueOf(3));
            accomClassMinPriceDiffSeason.setWednesdayDiffWithTax(BigDecimal.valueOf(5));
            accomClassMinPriceDiffSeason.setThursdayDiffWithTax(BigDecimal.valueOf(7));
            accomClassMinPriceDiffSeason.setFridayDiffWithTax(BigDecimal.valueOf(9));
            accomClassMinPriceDiffSeason.setSaturdayDiffWithTax(BigDecimal.valueOf(11));
            accomClassMinPriceDiffSeason.setSundayDiffWithTax(BigDecimal.valueOf(13));
            accomClassMinPriceDiffSeason.setStartDate(LocalDate.now());
            accomClassMinPriceDiffSeason.setEndDate(LocalDate.now());
            accomClassMinPriceDiffSeasonList.add(accomClassMinPriceDiffSeason);
        }
        return accomClassMinPriceDiffSeasonList;

    }

    private List<CPConfigOffsetAccomType> getOffsets() {
        //Setup Offsets
        CPConfigOffsetAccomType middleConfigOffsetAccomType = new CPConfigOffsetAccomType();
        middleConfigOffsetAccomType.setAccomType(middleBaseAccomType1);
        middleConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        middleConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(highestBaseAccomType1);
        highestConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(200));
        highestConfigOffsetAccomType.setStartDate(null);
        CPConfigOffsetAccomType highestConfigOffsetAccomType2 = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType2.setAccomType(highestAccomType2);
        highestConfigOffsetAccomType2.setOccupancyType(OccupancyType.SINGLE);
        highestConfigOffsetAccomType2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        highestConfigOffsetAccomType2.setSundayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setMondayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setThursdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setFridayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(250));
        highestConfigOffsetAccomType2.setStartDate(null);

        return asList(middleConfigOffsetAccomType, highestConfigOffsetAccomType, highestConfigOffsetAccomType2);
    }

    private TransientPricingBaseAccomType getTransientPricingBaseAccomType() {
        TransientPricingBaseAccomType middleBaseConfig = new TransientPricingBaseAccomType();
        middleBaseConfig.setAccomType(middleBaseAccomType1);
        middleBaseConfig.setSundayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setWednesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setThursdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setFridayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSaturdayCeilingRateWithTax(BigDecimal.valueOf(100));
        middleBaseConfig.setSundayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setMondayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setTuesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setWednesdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setThursdayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setFridayFloorRateWithTax(BigDecimal.valueOf(50));
        middleBaseConfig.setSaturdayFloorRateWithTax(BigDecimal.valueOf(50));
        return middleBaseConfig;
    }

    private List<AccomClassMinPriceDiff> getMinRCDefaults(List<PricingAccomClassRankDto> pricingAccomClassRankDtos) {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        accomClassMinPriceDiff.setId(1);
        accomClassMinPriceDiff.setSundayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setMondayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setTuesdayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setWednesdayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setThursdayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setFridayDiffWithTax(BigDecimal.TEN);
        accomClassMinPriceDiff.setSaturdayDiffWithTax(BigDecimal.TEN);

        accomClassMinPriceDiff.setAccomClassPriceRank(new AccomClassPriceRank());
        accomClassMinPriceDiff.getAccomClassPriceRank().setLowerRankAccomClass(pricingAccomClassRankDtos.get(0).getLowerPricingAccomClass().getAccomClass());
        accomClassMinPriceDiff.getAccomClassPriceRank().setHigherRankAccomClass(pricingAccomClassRankDtos.get(0).getHigherPricingAccomClass().getAccomClass());

        //accomClassMinPriceDiff.set
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffDefaults = new ArrayList<>();
        accomClassMinPriceDiffDefaults.add(accomClassMinPriceDiff);
        return accomClassMinPriceDiffDefaults;
    }

    private List<AccomClassMinPriceDiffSeason> getMinRCSeasons(List<PricingAccomClassRankDto> pricingAccomClassRankDtos) {
        AccomClassMinPriceDiffSeason accomClassMinPriceDiffSeason = new AccomClassMinPriceDiffSeason();
        accomClassMinPriceDiffSeason.setId(1);
        accomClassMinPriceDiffSeason.setStartDate(LocalDate.now());
        accomClassMinPriceDiffSeason.setEndDate(LocalDate.now());
        accomClassMinPriceDiffSeason.setSundayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setMondayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setTuesdayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setWednesdayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setThursdayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setFridayDiffWithTax(BigDecimal.valueOf(20));
        accomClassMinPriceDiffSeason.setSaturdayDiffWithTax(BigDecimal.valueOf(20));

        accomClassMinPriceDiffSeason.setAccomClassPriceRank(new AccomClassPriceRank());
        accomClassMinPriceDiffSeason.getAccomClassPriceRank().setLowerRankAccomClass(pricingAccomClassRankDtos.get(0).getLowerPricingAccomClass().getAccomClass());
        accomClassMinPriceDiffSeason.getAccomClassPriceRank().setHigherRankAccomClass(pricingAccomClassRankDtos.get(0).getHigherPricingAccomClass().getAccomClass());

        //accomClassMinPriceDiff.set
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = new ArrayList<>();
        accomClassMinPriceDiffSeasons.add(accomClassMinPriceDiffSeason);
        return accomClassMinPriceDiffSeasons;
    }

    @Test
    public void isHierarchyInvalidAccordingToOffsetsValuesSingleBaseOccupancyTypeTest1() {
        when(pricingConfigurationService.getPricingAccomClasses(null)).thenReturn(Arrays.asList(lowestPricingAccomClass, middlePricingAccomClass));
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(100), BigDecimal.valueOf(50), BigDecimal.valueOf(20));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(120), BigDecimal.valueOf(70), BigDecimal.valueOf(20));
        CPConfigOffsetAccomType offset1ForEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.DOUBLE, BigDecimal.TEN);
        List<CPConfigOffsetAccomType> offsetsOfEditingProduct = Arrays.asList(offset1ForEditingProduct);
        assertFalse(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, offsetsOfEditingProduct, Collections.emptyList(), Collections.emptySet(), BigDecimal.ZERO, true));
    }

    @Test
    public void isHierarchyInvalidAccordingToOffsetsValuesSingleBaseOccupancyTypeTest2() {
        when(pricingConfigurationService.getPricingAccomClasses(null)).thenReturn(Arrays.asList(lowestPricingAccomClass, middlePricingAccomClass));
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(100), BigDecimal.valueOf(50), BigDecimal.valueOf(20));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(120), BigDecimal.valueOf(70), BigDecimal.valueOf(20));
        CPConfigOffsetAccomType offset1ForEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.DOUBLE, BigDecimal.TEN);
        List<CPConfigOffsetAccomType> offsetsOfEditingProduct = Arrays.asList(offset1ForEditingProduct);
        assertFalse(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, offsetsOfEditingProduct, Collections.emptyList(), Collections.emptySet(), BigDecimal.TEN, true));
    }

    @Test
    public void isHierarchyInvalidAccordingToOffsetsValuesDoubleBaseOccupancyTypeTest1() {
        when(pricingConfigurationService.getPricingAccomClasses(null)).thenReturn(Arrays.asList(lowestPricingAccomClass, middlePricingAccomClass));
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(100), BigDecimal.valueOf(50), BigDecimal.valueOf(20));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(120), BigDecimal.valueOf(70), BigDecimal.valueOf(20));
        CPConfigOffsetAccomType offsetForEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        List<CPConfigOffsetAccomType> offsetsOfEditingProduct = Arrays.asList(offsetForEditingProduct);
        assertFalse(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, offsetsOfEditingProduct, Collections.emptyList(), Collections.emptySet(), BigDecimal.TEN, true));
    }

    @Test
    public void isHierarchyInvalidAccordingToOffsetsValuesDoubleBaseOccupancyTypeTest2() {
        when(agileRatesHierarchyValidationService.isHierarchyInvalidAccordingToOffsetsValues(any(), any(), any(), any(), any(), any(), anyBoolean(), any()))
                .thenReturn(true);
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(100), BigDecimal.valueOf(50), BigDecimal.valueOf(20));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(120), BigDecimal.valueOf(70), BigDecimal.valueOf(20));
        CPConfigOffsetAccomType offsetForEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        List<CPConfigOffsetAccomType> offsetsOfEditingProduct = Arrays.asList(offsetForEditingProduct);
        assertTrue(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, offsetsOfEditingProduct, Collections.emptyList(), Collections.emptySet(), BigDecimal.valueOf(30), true));
    }

    @Test
    public void isHierarchyInvalidAccordingToOffsetsValuesDoubleBaseOccupancyTypeTest3() {
        when(agileRatesHierarchyValidationService.isHierarchyInvalidAccordingToOffsetsValues(any(), any(), any(), any(), any(), any(), anyBoolean(), any()))
                .thenReturn(true);
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(100), BigDecimal.valueOf(50), BigDecimal.valueOf(20));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = prepareFloorCeilingValues(BigDecimal.valueOf(120), BigDecimal.valueOf(70), BigDecimal.valueOf(20));
        CPConfigOffsetAccomType offsetForEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.EXTRA_ADULT, BigDecimal.TEN);
        CPConfigOffsetAccomType offsetForNonEditingProduct =
                prepareOffset(lowestBaseAccomType1, OccupancyType.EXTRA_ADULT, BigDecimal.valueOf(25));
        List<CPConfigOffsetAccomType> offsetsOfEditingProduct = Arrays.asList(offsetForEditingProduct);
        assertTrue(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, offsetsOfEditingProduct, Arrays.asList(offsetForNonEditingProduct), Collections.emptySet(), BigDecimal.valueOf(30), true));
    }

    @Test
    public void isHierarchyValidForPriceExcluded() {
        when(agileRatesHierarchyValidationService.isHierarchyInvalidAccordingToOffsetsValues(any(), any(), any(), any(), any(), any(), anyBoolean(), any()))
                .thenReturn(true);
        List<PricingBaseAccomType> ceilingFloorValuesOfEditingProduct = List.of(createPricingBaseAccomType(BigDecimal.valueOf(100), BigDecimal.valueOf(100), middleBaseAccomType1));
        List<PricingBaseAccomType> ceilingFloorValuesOfNonEditingProduct = List.of(createPricingBaseAccomType(BigDecimal.valueOf(110), BigDecimal.valueOf(110), middleBaseAccomType1));
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setAccomClass(middleBaseAccomType1.getAccomClass());
        CPConfigOffsetAccomType offsetForEditingProduct =
                prepareOffset(middleAccomType2, OccupancyType.SINGLE, BigDecimal.valueOf(110));
        CPConfigOffsetAccomType offsetForNonEditingProduct =
                prepareOffset(middleAccomType2, OccupancyType.SINGLE, BigDecimal.valueOf(120));
        assertTrue(service.isHierarchyInvalidAccordingToOffsetsValues(ceilingFloorValuesOfEditingProduct, ceilingFloorValuesOfNonEditingProduct, List.of(offsetForEditingProduct),
                List.of(offsetForNonEditingProduct), Set.of(middlePricingAccomClass.getAccomClass()), BigDecimal.valueOf(30), true));
    }

    private PricingBaseAccomType createPricingBaseAccomType(BigDecimal ceiling, BigDecimal floor, AccomType accomType) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(accomType);
        pricingBaseAccomType.setSundayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setMondayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setTuesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setWednesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setThursdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setFridayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSaturdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSundayFloorRateWithTax(floor);
        pricingBaseAccomType.setMondayFloorRateWithTax(floor);
        pricingBaseAccomType.setTuesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setWednesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setThursdayFloorRateWithTax(floor);
        pricingBaseAccomType.setFridayFloorRateWithTax(floor);
        pricingBaseAccomType.setSaturdayFloorRateWithTax(floor);
        return pricingBaseAccomType;
    }

    private List<PricingBaseAccomType> prepareFloorCeilingValues(BigDecimal initCeilingValue, BigDecimal initFloorValue, BigDecimal step) {
        AccomType[] types = {lowestBaseAccomType1, middleBaseAccomType1};
        List<PricingBaseAccomType> result = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            PricingBaseAccomType pricingBaseAccomType = createPricingBaseAccomType(
                    initCeilingValue.add(step.multiply(BigDecimal.valueOf(i))), initFloorValue.add(step.multiply(BigDecimal.valueOf(i))), types[i]);
            result.add(pricingBaseAccomType);
        }
        return result;
    }

    private CPConfigOffsetAccomType prepareOffset(AccomType acoomType, OccupancyType occupancyType, BigDecimal offsetValue) {
        CPConfigOffsetAccomType highestConfigOffsetAccomType = new CPConfigOffsetAccomType();
        highestConfigOffsetAccomType.setAccomType(acoomType);
        highestConfigOffsetAccomType.setOccupancyType(occupancyType);
        highestConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        highestConfigOffsetAccomType.setSundayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setMondayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setTuesdayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setWednesdayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setThursdayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setFridayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setSaturdayOffsetValueWithTax(offsetValue);
        highestConfigOffsetAccomType.setStartDate(null);
        return highestConfigOffsetAccomType;
    }

    @Test
    public void getInvalidHierarchiesEditingCeilingFloorTest_valid() {
        Product ip1 = createIndependentProduct(2, "IP1");
        Product ip2 = createIndependentProduct(3, "IP2");
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));

        PricingBaseAccomType editingPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null, ip1.getId());
        PricingBaseAccomType editingPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null, ip1.getId());
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"), ip1.getId());
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"), ip1.getId());

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null, ip2.getId());
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null, ip2.getId());

        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(ip1);
        hierarchy.setToProduct(ip2);

        when(agileRatesConfigurationService.findImpactedProductHierarchies(ip1.getId())).thenReturn(List.of(hierarchy));
        when(agileRatesConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.getAllImpactedProductsWithinHierarchies(List.of(hierarchy), ip1)).thenReturn(List.of(ip2));
        when(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(List.of(ip2.getId()))).thenReturn(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2));
        when(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(anyMap(), anyMap(), anyMap(), anyMap(),
                anySet(), any(), anySet(), any(Predicate.class))).thenReturn(true);

        assertTrue(CollectionUtils.isEmpty(service.getInvalidHierarchiesEditingCeilingFloor(ip1, List.of(editingPricingBaseAccomType1, editingPricingBaseAccomType2),
                List.of(selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2))));
    }

    @Test
    public void getInvalidHierarchiesEditingCeilingFloorTest_invalid() {
        Product ip1 = createIndependentProduct(2, "IP1");
        Product ip2 = createIndependentProduct(3, "IP2");
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));

        PricingBaseAccomType editingPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null, ip1.getId());
        PricingBaseAccomType editingPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null, ip1.getId());
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"), ip1.getId());
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"), ip1.getId());

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null, ip2.getId());
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null, ip2.getId());

        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(ip1);
        hierarchy.setToProduct(ip2);

        when(agileRatesConfigurationService.findImpactedProductHierarchies(ip1.getId())).thenReturn(List.of(hierarchy));
        when(agileRatesConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.getAllImpactedProductsWithinHierarchies(List.of(hierarchy), ip1)).thenReturn(List.of(ip2));
        when(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(List.of(ip2.getId()))).thenReturn(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2));
        when(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(anyMap(), anyMap(), anyMap(), anyMap(),
                anySet(), any(), anySet(), any(Predicate.class))).thenReturn(false);

        assertFalse(CollectionUtils.isEmpty(service.getInvalidHierarchiesEditingCeilingFloor(ip1, List.of(editingPricingBaseAccomType1, editingPricingBaseAccomType2),
                List.of(selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2))));
    }

    @Test
    public void getInvalidHierarchiesEditingOffsetsTest_valid() {
        Product ip1 = createIndependentProduct(2, "IP1");
        Product ip2 = createIndependentProduct(3, "IP2");
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null, ip1.getId());
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null, ip1.getId());

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null, ip2.getId());
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null, ip2.getId());

        CPConfigOffsetAccomType editingCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(10), OccupancyType.SINGLE, null, null, ip1.getId());
        CPConfigOffsetAccomType editingCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(10), OccupancyType.SINGLE, null, null, ip1.getId());
        CPConfigOffsetAccomType selectedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(15), OccupancyType.SINGLE, LocalDate.parse("2023-08-01"), LocalDate.parse("2023-09-01"), ip1.getId());

        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(ip1);
        hierarchy.setToProduct(ip2);

        when(agileRatesConfigurationService.findImpactedProductHierarchies(ip1.getId())).thenReturn(List.of(hierarchy));
        when(agileRatesConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.getAllImpactedProductsWithinHierarchies(List.of(hierarchy), ip1)).thenReturn(List.of(ip2));
        when(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(List.of(ip2.getId(), ip1.getId())))
                .thenReturn(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2, selectedPricingBaseAccomType1, selectedPricingBaseAccomType2));
        when(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(anyMap(), anyMap(), anyMap(), anyMap(),
                anySet(), any(), anySet(), any(Predicate.class))).thenReturn(true);

        assertTrue(CollectionUtils.isEmpty(service.getInvalidHierarchiesEditingOffsets(ip1, List.of(editingCPConfigOffsetAccomType1, editingCPConfigOffsetAccomType2),
                List.of(selectedSeasonalCPConfigOffsetAccomType2))));
    }

    @Test
    public void getInvalidHierarchiesEditingOffsetsTest_invalid() {
        Product ip1 = createIndependentProduct(2, "IP1");
        Product ip2 = createIndependentProduct(3, "IP2");
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null, ip1.getId());
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null, ip1.getId());

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null, ip2.getId());
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null, ip2.getId());

        CPConfigOffsetAccomType editingCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(10), OccupancyType.SINGLE, null, null, ip1.getId());
        CPConfigOffsetAccomType editingCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(10), OccupancyType.SINGLE, null, null, ip1.getId());
        CPConfigOffsetAccomType selectedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(15), OccupancyType.SINGLE, LocalDate.parse("2023-08-01"), LocalDate.parse("2023-09-01"), ip1.getId());

        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(ip1);
        hierarchy.setToProduct(ip2);

        when(agileRatesConfigurationService.findImpactedProductHierarchies(ip1.getId())).thenReturn(List.of(hierarchy));
        when(agileRatesConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(agileRatesConfigurationService.getAllImpactedProductsWithinHierarchies(List.of(hierarchy), ip1)).thenReturn(List.of(ip2));
        when(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(List.of(ip2.getId(), ip1.getId())))
                .thenReturn(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2, selectedPricingBaseAccomType1, selectedPricingBaseAccomType2));
        when(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(anyMap(), anyMap(), anyMap(), anyMap(),
                anySet(), any(), anySet(), any(Predicate.class))).thenReturn(false);

        assertFalse(CollectionUtils.isEmpty(service.getInvalidHierarchiesEditingOffsets(ip1, List.of(editingCPConfigOffsetAccomType1, editingCPConfigOffsetAccomType2),
                List.of(selectedSeasonalCPConfigOffsetAccomType2))));
    }

    private PricingBaseAccomType createPricingBaseAccomType(BigDecimal ceiling, BigDecimal floor, AccomType accomType, LocalDate startDate, LocalDate endDate, Integer productId) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(accomType);
        pricingBaseAccomType.setSundayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setMondayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setTuesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setWednesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setThursdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setFridayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSaturdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSundayFloorRateWithTax(floor);
        pricingBaseAccomType.setMondayFloorRateWithTax(floor);
        pricingBaseAccomType.setTuesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setWednesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setThursdayFloorRateWithTax(floor);
        pricingBaseAccomType.setFridayFloorRateWithTax(floor);
        pricingBaseAccomType.setSaturdayFloorRateWithTax(floor);
        pricingBaseAccomType.setStartDate(startDate);
        pricingBaseAccomType.setEndDate(endDate);
        pricingBaseAccomType.setProductID(productId);
        return pricingBaseAccomType;
    }

    private AccomType createAccomType(Integer id, String name, AccomClass accomClass) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(name);
        accomType.setAccomClass(accomClass);

        return accomType;
    }

    private AccomClass createAccomClass(Integer id, String name) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(id);
        accomClass.setName(name);
        return accomClass;
    }

    private PricingAccomClass createPricingAccomClass(Integer id, AccomType accomType, AccomClass accomClass) {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setId(id);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);
        return pricingAccomClass;
    }

    private CPConfigOffsetAccomType createCPConfigOffsetAccomType(OffsetMethod offsetMethod, AccomType accomType,
                                                                  BigDecimal offsetValue, OccupancyType occupancyType, LocalDate startDate, LocalDate endDate, Integer productId) {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setOffsetMethod(offsetMethod);
        cpConfigOffsetAccomType.setAccomType(accomType);
        cpConfigOffsetAccomType.setEndDate(endDate);
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setMondayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setOccupancyType(occupancyType);
        cpConfigOffsetAccomType.setSaturdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setStartDate(startDate);
        cpConfigOffsetAccomType.setSundayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setThursdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setTuesdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setWednesdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setProductID(productId);
        return cpConfigOffsetAccomType;
    }

    private Product createIndependentProduct(Integer id, String name) {
        Product product = new Product();
        product.setId(id);
        product.setSystemDefault(false);
        product.setCode("INDEPENDENT");
        product.setName(name);
        return product;
    }
}