package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class OverrideServiceSpecificAndFloorRateOfTheDayDataTest extends AbstractG3JupiterTest {

    private static final int ACCOM_CLASS_ID_FOR_STD = 2;
    private static final Integer rateLV1 = 5;
    private static final Integer rateLV2 = 6;
    private static final Integer rateLV3 = 7;
    private static final Integer rateLV4 = 8;
    private static final Integer rateLV5 = 9;
    private static final Integer rateLV6 = 10;
    private static final Integer rateLV7 = 11;
    private static final BigDecimal BD2548_734 = new BigDecimal(2548.73438);
    private static final BigDecimal BD2744_734 = new BigDecimal(2744.73438);
    private static final BigDecimal BD2940_734 = new BigDecimal(2940.73438);
    private static final BigDecimal BD3457_734 = new BigDecimal(3457.73438);
    private static final BigDecimal BD4940_734 = new BigDecimal(4940.73438);
    private static final BigDecimal BD5236_734 = new BigDecimal(5236.73438);
    private static final BigDecimal BD5336_734 = new BigDecimal(5336.73438);
    private static final BigDecimal LV1ValueForMasterClass = new BigDecimal(3795.73438);
    private static final BigDecimal LV2ValueForMasterClass = new BigDecimal(3457.73438);
    private static final BigDecimal LV3ValueForMasterClass = new BigDecimal(3136.73438);
    private static final BigDecimal LV4ValueForMasterClass = new BigDecimal(2940.73438);
    private static final BigDecimal LV5ValueForMasterClass = new BigDecimal(2744.73438);
    private static final BigDecimal LV6ValueForMasterClass = new BigDecimal(2548.73438);
    private static final BigDecimal LV7ValueForMasterClass = new BigDecimal(2450.73438);
    private static final BigDecimal LV1ValueForNonMasterClass = new BigDecimal(5476.73438);
    private static final BigDecimal LV2ValueForNonMasterClass = new BigDecimal(5336.73438);
    private static final BigDecimal LV3ValueForNonMasterClass = new BigDecimal(5236.73438);
    private static final BigDecimal LV4ValueForNonMasterClass = new BigDecimal(4940.73438);
    private static final BigDecimal LV5ValueForNonMasterClass = new BigDecimal(4744.73438);
    private static final BigDecimal LV6ValueForNonMasterClass = new BigDecimal(4548.73438);
    private static final BigDecimal LV7ValueForNonMasterClass = new BigDecimal(4450.73438);
    private static final BigDecimal NoRateValue = new BigDecimal(0.00);
    private OverrideService service = new OverrideService();
    private DateService dateService;
    private BusinessContextService businessContextService = new BusinessContextService();
    private PriceService priceService = new PriceService();
    private PacmanConfigParamsService configParamsService;
    private MasterClassOverrideHelperBean masterClassOverrideHelper;
    private LocalDate startDate;
    private int decisionReasonTypeIdForAllIsWell = 1;
    private String overrideTypeCeiling = Constants.BARDECISIONOVERRIDE_CEILING;
    private String overrideTypeFloor = "Floor";
    private String overrideTypeFloorAndCeiling = Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING;
    private String overrideTypeNone = "None";
    private String overrideTypeUser = "User";
    private String overrideTypePending = "Pending";
    private int accomTypeIdSix = 6;
    private int accomTypeIdSeven = 7;
    private int accomTypeIdEight = 8;
    private int accomTypeIdFour = 4;
    private int losBarByDay = -1;
    private int masterClassId = 3;
    private int propertyIdForH1 = 5;

    @BeforeEach
    public void setUp() {
        configParamsService = mock(PacmanConfigParamsService.class);
        dateService = new DateService() {

            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getBusinessDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getWebRateShoppingDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getUnqualifiedRateCaughtUpDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getBARDisplayWindowEndDate() {
                return DateUtil.getLastDayOfCurrentMonth();
            }

            @Override
            public Date getBARDisplayWindowStartDate() {
                return getCaughtUpDate();
            }
        };
        service.setCrudService(tenantCrudService());
        businessContextService.setConfigService(configParamsService);
        businessContextService.setCrudService(tenantCrudService());
        service.setBusinessContextService(businessContextService);
        service.configService = configParamsService;
        DecisionService decisionService = DecisionService.createTestInstance();
        RateDeterminator rateDeterminator = new RateDeterminator();

        priceService.setCrudService(tenantCrudService());
        try {
            decisionService.setDateServiceLocal(dateService);
            decisionService.setCrudService(tenantCrudService());
            service.setDecisionService(decisionService);

            priceService.rateDeterminator = rateDeterminator;
            service.priceService = priceService;

        } catch (Exception e) {
            fail("couldn't inject dateService and crudService into DecisionService.");
        }


        rateDeterminator.setCrudService(tenantCrudService());
        service.setRateDeterminator(rateDeterminator);

        masterClassOverrideHelper = new MasterClassOverrideHelperBean();
        masterClassOverrideHelper.setCrudService(tenantCrudService());
        masterClassOverrideHelper.setBusinessContextService(businessContextService);
        service.setMasterClassHelper(masterClassOverrideHelper);

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
    }


    @SuppressWarnings("unchecked")
    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsGreaterThanOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setUserOverrideForGivenDate(3, rateLV6, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveMultiDaySpecificOverrideWhenSystemDecisionIsGreaterThanOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setMultiDaySpecificOverrideForGivenDate(3, 4, 5, rateLV6, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2  ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2   ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2   ", masterClassId, startDate.plusDays(5).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousCeilingOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setCeilingOverrideForGivenDate(3, rateLV5, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeUser, rateLV4, null, rateLV5, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue, LV5ValueForMasterClass, NoRateValue);
    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousFloorOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setFloorOverrideForGivenDate(3, rateLV5, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypeUser, rateLV4, null, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousSpecificOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setUserOverrideForGivenDate(3, rateLV7, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV7, losBarByDay, overrideTypeUser, null, overrideTypeUser, rateLV4, null, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV7ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousCeilingAndFloorOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setCeilingAndFloorOverrideForGivenDate(3, rateLV4, rateLV5, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeUser, rateLV4, null, rateLV4, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue);
    }

    @Test
    public void testSaveSingleOverrideWhenSpecificOverrideIsRemove() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setUserOverrideForGivenDate(3, rateLV6, masterClassId);
        removeOverrideForGivenDate(3, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testMultiDayOverrideSaveSingleOverrideWhenSpecificOverrideIsRemove() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setMultiDaySpecificOverrideForGivenDate(3, 4, 5, Integer.valueOf(rateLV6), masterClassId);
        removeMultiDayOverrideForGivenDate(3, 4, 5, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1  ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, LV7ValueForMasterClass, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 2  ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2  ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2", masterClassId, startDate.plusDays(5).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, LV7ValueForMasterClass, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsLessThanOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveMultiDayFloorOverrideWhenSystemDecisionIsLessThanOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setMultiDayFloorOverrideForGivenDate(3, 4, 5, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);

        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setFloorOverrideForGivenDate(3, rateLV7, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV7, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV5, rateLV7, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV7, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV5ValueForMasterClass, LV7ValueForMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousCeilingOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setCeilingOverrideForGivenDate(3, rateLV2, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeFloor, rateLV4, rateLV4, rateLV2, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, LV2ValueForMasterClass, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousFloorOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setFloorOverrideForGivenDate(3, rateLV5, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousSpecificOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setUserOverrideForGivenDate(3, rateLV7, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV7, losBarByDay, overrideTypeUser, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV7ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousCeilingAndFloorOverride() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setCeilingAndFloorOverrideForGivenDate(3, rateLV4, rateLV5, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeFloor, rateLV4, rateLV4, rateLV4, null);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(0, ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString());
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue);
    }

    @Test
    public void testSaveSingleOverrideWhenFloorOverrideIsRemove() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        removeOverrideForGivenDate(3, masterClassId);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, overrideTypePending, rateLV4, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypePending, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data ", decisionBarOutputOVRId, accomTypeIdFour, LV4ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testMultiDayOverrideSaveSingleOverrideWhenFloorOverrideIsRemove() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionFalse();
        setMultiDayFloorOverrideForGivenDate(3, 4, 5, Integer.valueOf(rateLV5), masterClassId);
        removeMultiDayOverrideForGivenDate(3, 4, 5, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data ", masterClassId, startDate.plusDays(3).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV5ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);

        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 2  ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2  ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV5ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    /*Enable Single Bar Decision True Tests*/

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsGreaterThanOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, rateLV6, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveMultiDaySpecificOverrideWhenSystemDecisionIsGreaterThanOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDaySpecificOverrideForGivenDate(3, 4, 5, rateLV6, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying for Master class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1 for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2  for Master Class  ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Master Class    ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2  for Master Class   ", masterClassId, startDate.plusDays(5).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 for Master Class   ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying for Non_master class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 for Non Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 for Non Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1 for Non Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Non Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2 for Non Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, rateLV6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Non Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeUser, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2  for Non Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 for Non Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousCeilingOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, rateLV5, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeUser, rateLV4, null, rateLV5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue, LV5ValueForMasterClass, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeUser, rateLV4, null, rateLV5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, NoRateValue, LV5ValueForNonMasterClass, NoRateValue);
    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousSpecificOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, rateLV7, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV7, losBarByDay, overrideTypeUser, null, overrideTypeUser, rateLV4, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class   ", decisionBarOutputOVRId, accomTypeIdFour, LV7ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class    ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class    ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV7, losBarByDay, overrideTypeUser, null, overrideTypeUser, rateLV4, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class    ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV7ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveSpecificOverrideWhenSystemDecisionIsLessThanOverridesAndPreviousCeilingAndFloorOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingAndFloorOverrideForGivenDate(3, rateLV4, rateLV5, masterClassId);
        setUserOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeUser, rateLV4, null, rateLV4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeUser, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeUser, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeUser, rateLV4, null, rateLV4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeUser, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, NoRateValue);
    }

    @Test
    public void testSaveSingleOverrideWhenSpecificOverrideIsRemoveForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, rateLV6, masterClassId);
        removeOverrideForGivenDate(3, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV6ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsLessThanOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveMultiDayFloorOverrideWhenSystemDecisionIsLessThanOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayFloorOverrideForGivenDate(3, 4, 5, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying For Master Class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 for Master Class", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1 for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2 for Master Class ", masterClassId, startDate.plusDays(5).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Master Class ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 for Master Class ", masterClassId, startDate.plusDays(5).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 for Master Class ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
        //Verifying For Non-Master Class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 1 for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 1 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data for Date 2 for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, rateLV7, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV7, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV5, rateLV7, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV7, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV5ValueForMasterClass, LV7ValueForMasterClass, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV7, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeNone, null, overrideTypeFloor, rateLV5, rateLV7, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, LV5ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV7, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV5ValueForNonMasterClass, LV7ValueForNonMasterClass, NoRateValue, NoRateValue);

    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousCeilingOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, rateLV2, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeFloor, rateLV4, rateLV4, rateLV2, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, NoRateValue, LV4ValueForMasterClass, LV4ValueForMasterClass, LV2ValueForMasterClass, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeCeiling, null, overrideTypeFloor, rateLV4, rateLV4, rateLV2, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, NoRateValue, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, LV2ValueForNonMasterClass, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousFloorOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, rateLV5, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class   ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypeFloor, rateLV4, rateLV4, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, NoRateValue);
    }

    @Test
    public void testSaveFloorOverrideWhenSystemDecisionIsGreaterThanOverridesAndPreviousCeilingAndFloorOverrideForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingAndFloorOverrideForGivenDate(3, rateLV4, rateLV5, masterClassId);
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeFloor, rateLV4, rateLV4, rateLV4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloorAndCeiling, rateLV5, overrideTypeFloor, rateLV4, rateLV4, rateLV4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypeFloor, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue);

    }

    @Test
    public void testSaveSingleOverrideWhenFloorOverrideIsRemoveForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, rateLV4, masterClassId);
        removeOverrideForGivenDate(3, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decision For Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, overrideTypePending, rateLV4, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV4, LV4ValueForMasterClass, losBarByDay, overrideTypePending, rateLV4, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRId, accomTypeIdFour, LV4ValueForMasterClass, LV4ValueForMasterClass, LV4ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, losBarByDay, overrideTypeFloor, rateLV4, overrideTypePending, rateLV4, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV4, LV4ValueForNonMasterClass, losBarByDay, overrideTypePending, rateLV4, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non-Master Class ", decisionBarOutputOVRId, accomTypeIdSix, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, LV4ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    @Test
    public void testMultiDayOverrideSaveSingleOverrideWhenSpecificOverrideIsRemoveForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDaySpecificOverrideForGivenDate(3, 4, 5, Integer.valueOf(rateLV6), masterClassId);
        removeMultiDayOverrideForGivenDate(3, 4, 5, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying for Master Class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 1 for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 for Master Class    ", masterClassId, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class   ", masterClassId, startDate.plusDays(3).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Master Class    ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, LV7ValueForMasterClass, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 2 for Master Class    ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2  for Master Class   ", masterClassId, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 for Master Class  ", masterClassId, startDate.plusDays(5).toString(), rateLV6, LV6ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  for Master Class   ", decisionBarOutputOVRId, accomTypeIdFour, LV6ValueForMasterClass, LV7ValueForMasterClass, LV6ValueForMasterClass, NoRateValue, LV6ValueForMasterClass, NoRateValue);
        //Verifying for Non-Master Class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 1 for Non-Master Class    ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV6ValueForNonMasterClass, LV7ValueForNonMasterClass, LV6ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue);
        //Verifying Decisions For The Second Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing override for Date 2 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV6, losBarByDay, overrideTypeUser, null, overrideTypePending, rateLV6, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 for Non-Master Class ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV6, LV6ValueForNonMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2 for Non-Master Class   ", decisionBarOutputOVRId, accomTypeIdSix, LV6ValueForNonMasterClass, LV7ValueForNonMasterClass, LV6ValueForNonMasterClass, NoRateValue, LV6ValueForNonMasterClass, NoRateValue);
    }

    @Test
    public void testMultiDayOverrideSaveSingleOverrideWhenFloorOverrideIsRemoveForEnableSingleBarDecisionTrue() throws Exception {
        setWorkContextProperty(TestProperty.H1);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayFloorOverrideForGivenDate(3, 4, 5, Integer.valueOf(rateLV5), masterClassId);
        removeMultiDayOverrideForGivenDate(3, 4, 5, masterClassId);
        tenantCrudService().flushAndClear();
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 1 for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data  for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        int decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Master Class   ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV5ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 2 for Master Class   ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2  for Master Class  ", masterClassId, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2  for Master Class ", masterClassId, startDate.plusDays(5).toString(), rateLV5, LV5ValueForMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, masterClassId);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  for Master Class  ", decisionBarOutputOVRId, accomTypeIdFour, LV5ValueForMasterClass, LV5ValueForMasterClass, LV5ValueForMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decision For Non-Master Class
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 1 for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1  for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(3).toString(), rateLV5, LV5ValueForNonMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 1 for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);
        //Verifying Decisions For The First Date
        assertDecisionBarOut("Verifying Decision Bar out Put data after removing  override for Date 2  for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 for Non-Master Class   ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV5, losBarByDay, overrideTypeFloor, rateLV5, overrideTypePending, rateLV5, null, null, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Date 2 for Non-Master Class  ", ACCOM_CLASS_ID_FOR_STD, startDate.plusDays(5).toString(), rateLV5, LV5ValueForNonMasterClass, losBarByDay, overrideTypePending, null, null);
        decisionBarOutputOVRId = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STD);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Date 2  for Non-Master Class  ", decisionBarOutputOVRId, accomTypeIdSix, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, LV5ValueForNonMasterClass, NoRateValue, NoRateValue, NoRateValue);

    }

    private void removeMultiDayOverrideForGivenDate(int dateNumber1, int dateNumber2, int dateNumber3, int accomClassId) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        override1.setAccomClassId(accomClassId);
        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setLengthOfStay(-1);
        override1.setRemove(true);
        override2.setAccomClassId(accomClassId);
        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setLengthOfStay(-1);
        override2.setRemove(true);
        override3.setAccomClassId(accomClassId);
        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setLengthOfStay(-1);
        override3.setRemove(true);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }

    private void setMultiDayFloorOverrideForGivenDate(int dateNumber1, int dateNumber2, int dateNumber3, int specificRateUnqualifiedId, int accomClassId) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setFloorRateUnqualifiedId(specificRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override2.setFloorRateUnqualifiedId(specificRateUnqualifiedId);
        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setAccomClassId(accomClassId);
        override3.setFloorRateUnqualifiedId(specificRateUnqualifiedId);
        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }


    private void setMultiDaySpecificOverrideForGivenDate(int dateNumber1, int dateNumber2, int dateNumber3, int specificRateUnqualifiedId, int accomClassId) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setSpecificRateUnqualifiedId(specificRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override2.setSpecificRateUnqualifiedId(specificRateUnqualifiedId);
        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setAccomClassId(accomClassId);
        override3.setSpecificRateUnqualifiedId(specificRateUnqualifiedId);
        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }

    private void setCeilingOverrideForGivenDate(int dateNumber, int ceilingRateUnqualifiedId, int accomClassId) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(override));
    }

    private void setCeilingAndFloorOverrideForGivenDate(int dateNumber, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(override));
    }


    private void removeOverrideForGivenDate(int dateNumber, int accomClassId) {
        BAROverride override = new BAROverride();
        override.setAccomClassId(accomClassId);
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setLengthOfStay(-1);
        override.setRemove(true);
        service.saveBAROverrides(Arrays.asList(override));
    }

    private void setFloorOverrideForGivenDate(int dateNumber, int floorRateUnqualifiedId, int accomClassId) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(override));
    }

    private void setUserOverrideForGivenDate(int dateNumber, int userRateUnqualifiedId, int accomClassId) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setSpecificRateUnqualifiedId(userRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(override));
    }

    private void assertDecisionBarOut(String Level, int accomClassId, String arrivalDate, Integer rateUnqualifiedId, int los,
                                      String overrideType, Integer floorRateUnqualifiedId, int decisionReasonTypeId, Integer ceilingRateUnqualifiedId) {
        List<Object[]> decisionBarOutPutData = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " ");

        assertEquals(accomClassId, (decisionBarOutPutData.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (decisionBarOutPutData.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(rateUnqualifiedId, (decisionBarOutPutData.get(0)[5]), Level + " - Rate Unqualified Id ");
        assertEquals(los, (decisionBarOutPutData.get(0)[6]), Level + " - LOS ");
        assertEquals(overrideType, (decisionBarOutPutData.get(0)[7].toString()), Level + " - Override Type ");
        if (decisionBarOutPutData.get(0)[8] != null) {
            assertEquals(floorRateUnqualifiedId, (decisionBarOutPutData.get(0)[8]), Level + " - Floor Rate Unqualified Id ");
        }
        if (decisionBarOutPutData.get(0)[9] != null) {
            assertEquals(decisionReasonTypeId, (decisionBarOutPutData.get(0)[9]), Level + " - Decision Reason Type Id ");
        }
        if (decisionBarOutPutData.get(0)[13] != null) {
            assertEquals(ceilingRateUnqualifiedId, (decisionBarOutPutData.get(0)[13]), Level + " - Ceiling Rate Unqualified Id ");
        }
    }

    private void assertDecisionBarOutOvr(String Level, int accomClassId, String arrivalDate, Integer oldRateUnqualifiedId, int los,
                                         String oldOverrideType, Integer oldFloorRateUnqualifiedId, String newOverrideType, Integer newRateUnqualifiedId,
                                         Integer newFloorRateUnqualifiedId, Integer oldCeilingRateUnqualifiedId, Integer newCeilingRateUnqualifiedId) {
        List<Object[]> decisionBarOutPutDataOvr = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output_ovr where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " order by Decision_ID desc ");
        assertEquals(accomClassId, (decisionBarOutPutDataOvr.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (decisionBarOutPutDataOvr.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(oldRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[6]), Level + " - Old Rate Unqualified Id ");
        assertEquals(los, (decisionBarOutPutDataOvr.get(0)[7]), Level + " - LOS ");
        assertEquals(oldOverrideType, (decisionBarOutPutDataOvr.get(0)[8].toString()), Level + " - Old Override Type ");
        if (decisionBarOutPutDataOvr.get(0)[9] != null) {
            assertEquals(oldFloorRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[9]), Level + " - Old Floor Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[10] != null) {
            assertEquals(newOverrideType, (decisionBarOutPutDataOvr.get(0)[10]), Level + " - New Override Type ");
        }
        if (decisionBarOutPutDataOvr.get(0)[11] != null) {
            assertEquals(newRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[11]), Level + " - New Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[12] != null) {
            assertEquals(newFloorRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[12]), Level + " - New Floor Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[14] != null) {
            assertEquals(oldCeilingRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[14]), Level + " - Old Ceiling Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[15] != null) {
            assertEquals(newCeilingRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[15]), Level + " - New Ceiling Rate Unqualified Id ");
        }
    }


    private void assertDecisionBarOutOvrDetails(String Level, int decisionBarOutputOVRId, int accomTypeId,
                                                BigDecimal OldRateUnqualifiedValue, BigDecimal OldFloorRateUnqualifiedValue,
                                                BigDecimal newRateUnqualifiedValue,
                                                BigDecimal newFloorRateUnqualifiedValue, BigDecimal OldCeilingRateUnqualifiedValue, BigDecimal newCeilingRateUnqualifiedValue) {
        List<Object[]> decisionBarOutPutDataOvrDetails = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output_OVR_Details where Decision_Bar_Output_OVR_ID=" + decisionBarOutputOVRId + " and Accom_Type_ID=" + accomTypeId + "  ");
        assertEquals(decisionBarOutputOVRId, (decisionBarOutPutDataOvrDetails.get(0)[1]), Level + " - Decision Bar Output Override Id ");
        assertEquals(accomTypeId, (decisionBarOutPutDataOvrDetails.get(0)[2]), Level + " - Accom Type Id ");

        if (decisionBarOutPutDataOvrDetails.get(0)[3] != null) {
            assertEquals(OldRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[3]), Level + " - Old Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[4] != null) {
            assertEquals(OldFloorRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[4]), Level + " - Old Floor Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[5] != null) {
            assertEquals(newRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[5]), Level + " - New Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[6] != null) {
            assertEquals(newFloorRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[6]), Level + " - New Floor Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[8] != null) {
            assertEquals(OldCeilingRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[8]), Level + " - Old Ceiling Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[9] != null) {
            assertEquals(newCeilingRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[9]), Level + " - New Ceiling Rate Unqualified Value ");
        }
    }

    private int convertAndCompare(BigDecimal decimal, BigDecimal compareToDecimal) {
        if (decimal == null && compareToDecimal == null) {
            return 0;
        } else if (decimal == null || compareToDecimal == null) {
            //If you are getting a -2 back, you know that one value is null and not the other
            return -2;
        }
        decimal = decimal.setScale(2, BigDecimal.ROUND_HALF_DOWN);
        compareToDecimal = compareToDecimal.setScale(2, BigDecimal.ROUND_HALF_DOWN);
        //possible values are -1, 0, 1  - they are only equal if you are getting a zero back
        return decimal.compareTo(compareToDecimal);
    }

    private void assertDecisionBarOutOvrPace(String Level, int accomClassId, String arrivalDate, Integer RateUnqualifiedId, BigDecimal derivedRateUnqualifiedValue, int los,
                                             String OverrideType, Integer FloorRateUnqualifiedValue, String CeilingRateUnqualifiedValue) {
        List<Object[]> assertDecisionBarOutOvrPace = tenantCrudService().findByNativeQuery("select * from PACE_Bar_Output where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " and los=" + los + " order by Decision_ID desc ");
        assertEquals(accomClassId, (assertDecisionBarOutOvrPace.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (assertDecisionBarOutOvrPace.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(RateUnqualifiedId, (assertDecisionBarOutOvrPace.get(0)[5]), Level + " - Rate Unqualified Id ");
        assertEquals(derivedRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (assertDecisionBarOutOvrPace.get(0)[6]), Level + " - Derived Rate Unqualified Value ");
        assertEquals(los, (assertDecisionBarOutOvrPace.get(0)[7]), Level + " - LOS ");
        assertEquals(OverrideType, (assertDecisionBarOutOvrPace.get(0)[8].toString()), Level + " - Override Type ");
        if (assertDecisionBarOutOvrPace.get(0)[9] != null) {
            assertEquals(FloorRateUnqualifiedValue, (assertDecisionBarOutOvrPace.get(0)[9]), Level + " - Floor Rate Unqualified Value ");
        }
        if (assertDecisionBarOutOvrPace.get(0)[14] != null) {
            assertEquals(CeilingRateUnqualifiedValue, (assertDecisionBarOutOvrPace.get(0)[14].toString()), Level + " - Ceiling Rate Unqualified Id ");
        }
    }

    private void UpdateBarRateDetails() {

        StringBuilder insertQuery = new StringBuilder();

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2303.73438,Monday=2303.73438,Tuesday=2303.73438, ");
        insertQuery.append(" Wednesday=2303.73438,Thursday=2303.73438,Friday=2303.73438,Saturday=2303.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=12 and Accom_Type_ID in (").append(accomTypeIdFour).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4303.73438,Monday=4303.73438,Tuesday=4303.73438, ");
        insertQuery.append(" Wednesday=4303.73438,Thursday=4303.73438,Friday=4303.73438,Saturday=4303.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=12 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2450.73438,Monday=2450.73438,Tuesday=2450.73438, ");
        insertQuery.append(" Wednesday=2450.73438,Thursday=2450.73438,Friday=2450.73438,Saturday=2450.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=11 and Accom_Type_ID in (").append(accomTypeIdFour).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4450.73438,Monday=4450.73438,Tuesday=4450.73438, ");
        insertQuery.append(" Wednesday=4450.73438,Thursday=4450.73438,Friday=4450.73438,Saturday=4450.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=11 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2548.73438,Monday=2548.73438,Tuesday=2548.73438, ");
        insertQuery.append(" Wednesday=2548.73438,Thursday=2548.73438,Friday=2548.73438,Saturday=2548.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=10 and Accom_Type_ID in (").append(accomTypeIdFour).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4548.73438,Monday=4548.73438,Tuesday=4548.73438, ");
        insertQuery.append(" Wednesday=4548.73438,Thursday=4548.73438,Friday=4548.73438,Saturday=4548.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=10 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2744.73438,Monday=2744.73438,Tuesday=2744.73438, ");
        insertQuery.append(" Wednesday=2744.73438,Thursday=2744.73438,Friday=2744.73438,Saturday=2744.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=9 and Accom_Type_ID in (").append(accomTypeIdFour).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4744.73438,Monday=4744.73438,Tuesday=4744.73438, ");
        insertQuery.append(" Wednesday=4744.73438,Thursday=4744.73438,Friday=4744.73438,Saturday=4744.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=9 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2940.73438,Monday=2940.73438,Tuesday=2940.73438, ");
        insertQuery.append(" Wednesday=2940.73438,Thursday=2940.73438,Friday=2940.73438,Saturday=2940.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=8 and Accom_Type_ID in (").append(accomTypeIdFour).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4940.73438,Monday=4940.73438,Tuesday=4940.73438, ");
        insertQuery.append(" Wednesday=4940.73438,Thursday=4940.73438,Friday=4940.73438,Saturday=4940.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=8 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3136.73438,Monday=3136.73438,Tuesday=3136.73438, ");
        insertQuery.append(" Wednesday=3136.73438,Thursday=3136.73438,Friday=3136.73438,Saturday=3136.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=7 and Accom_Type_ID in (").append(accomTypeIdFour).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5236.73438,Monday=5236.73438,Tuesday=5236.73438, ");
        insertQuery.append(" Wednesday=5236.73438,Thursday=5236.73438,Friday=5236.73438,Saturday=5236.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=7 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3457.73438,Monday=3457.73438,Tuesday=3457.73438, ");
        insertQuery.append(" Wednesday=3457.73438,Thursday=3457.73438,Friday=3457.73438,Saturday=3457.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=6 and Accom_Type_ID in (").append(accomTypeIdFour).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5336.73438,Monday=5336.73438,Tuesday=5336.73438, ");
        insertQuery.append(" Wednesday=5336.73438,Thursday=5336.73438,Friday=5336.73438,Saturday=5336.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=6 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3795.73438,Monday=3795.73438,Tuesday=3795.73438, ");
        insertQuery.append(" Wednesday=3795.73438,Thursday=3795.73438,Friday=3795.73438,Saturday=3795.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=5 and Accom_Type_ID in (").append(accomTypeIdFour).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5476.73438,Monday=5476.73438,Tuesday=5476.73438, ");
        insertQuery.append(" Wednesday=5476.73438,Thursday=5476.73438,Friday=5476.73438,Saturday=5476.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=5 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3987.73438,Monday=3987.73438,Tuesday=3987.73438, ");
        insertQuery.append(" Wednesday=3987.73438,Thursday=3987.73438,Friday=3987.73438,Saturday=3987.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=4 and Accom_Type_ID in (").append(accomTypeIdFour).append(")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5586.73438,Monday=5586.73438,Tuesday=5586.73438, ");
        insertQuery.append(" Wednesday=5586.73438,Thursday=5586.73438,Friday=5586.73438,Saturday=5586.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=4 and Accom_Type_ID in (").append(accomTypeIdSix).append(",").append(accomTypeIdSeven).append(",").append(accomTypeIdEight).append(")  ");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

    }

    private int getDecisionBarOutputOVRId(int dateNumber, int accomClassId) {
        List decisionBarOutputOVRIdList = tenantCrudService().findByNativeQuery("select Decision_Bar_Output_OVR_ID from Decision_Bar_Output_OVR where " +
                " Arrival_DT='" + startDate.plusDays(dateNumber).toString() + "' and Accom_Class_ID=" + accomClassId + " " +
                " order by Decision_ID desc");
        int decisionBarOutputOVRId = Integer.valueOf(decisionBarOutputOVRIdList.get(0).toString());
        return decisionBarOutputOVRId;
    }

    private LocalDate getLocalDate(int propertyID) {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + ",3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    private int getMasterClassId() {
        List masterClassIdList = tenantCrudService().findByNativeQuery("select Accom_Class_ID from Accom_Class where Master_Class=1 ");
        String masterClassId = masterClassIdList.get(0).toString();
        return Integer.valueOf(masterClassId);
    }

    private int getMaxDecisionIdFromPaceBarOutPut(int accomClassId) {
        List maxDecisionIdFromPaceBarOutPutList = tenantCrudService().findByNativeQuery("select MAX(Decision_ID) from PACE_Bar_Output where Arrival_DT='" + startDate.plusDays(3).toString() + "' and Accom_Class_ID=" + accomClassId + "  ");
        String maxDecisionIdFromPaceBarOutPut = maxDecisionIdFromPaceBarOutPutList.get(0).toString();
        return Integer.valueOf(maxDecisionIdFromPaceBarOutPut);
    }

    private void assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(int expectedRowCount, int accomClassId, String arrivalDate) {

        List<Object[]> decisionBarOutOverrideData = tenantCrudService().findByNativeQuery("select count(*) from Decision_Bar_Output_OVR where Accom_Class_ID=" + accomClassId + " " +
                "and Arrival_DT='" + arrivalDate + "' ");
        assertEquals(expectedRowCount, decisionBarOutOverrideData.get(0), "Record Count in Decision Bar Out Override for accom class ID " + accomClassId + " - ");

    }


    private void setUpInitialDataWithSingleBarDecisionFalse() {
        startDate = getLocalDate(propertyIdForH1);
        createTestData();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn("false");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value())).thenReturn("true");
    }


    private void createTestData() {
        setWorkContextProperty(TestProperty.H1);
        UpdateBarRateDetails();
        int maxDecisionIdFromPaceBarOutPutOfMasterClass = getMaxDecisionIdFromPaceBarOutPut(masterClassId);
        int maxDecisionIdFromPaceBarOutPutOfNonMasterClass = getMaxDecisionIdFromPaceBarOutPut(ACCOM_CLASS_ID_FOR_STD);

        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(5).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(" update Rate_Unqualified_Details set End_Date_DT='2373-10-14' where Start_Date_DT='2015-01-01'");
        tenantCrudService().executeUpdateByNativeQuery("update Decision_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateLV5 + ",Floor_Rate_Unqualified_ID=NULL,Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(5).toString() + "' and Accom_Class_ID=" + ACCOM_CLASS_ID_FOR_STD + " ");
        tenantCrudService().executeUpdateByNativeQuery("update Decision_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateLV5 + ",Floor_Rate_Unqualified_ID=NULL,Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(5).toString() + "' and Accom_Class_ID=" + masterClassId + " ");

        tenantCrudService().executeUpdateByNativeQuery("update PACE_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateLV5 + ",Derived_Unqualified_Value=90.1234,Floor_Rate_Unqualified_ID=NULL," +
                " Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(5).toString() + "' and Accom_Class_ID=" + masterClassId + " " +
                " and Decision_ID=" + maxDecisionIdFromPaceBarOutPutOfMasterClass + " ");

        tenantCrudService().executeUpdateByNativeQuery("update PACE_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateLV5 + ",Derived_Unqualified_Value=95.4321,Floor_Rate_Unqualified_ID=NULL," +
                " Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(5).toString() + "' " +
                " and Accom_Class_ID=" + ACCOM_CLASS_ID_FOR_STD + " and Decision_ID=" + maxDecisionIdFromPaceBarOutPutOfNonMasterClass + " ");

        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR_Details");

    }

    private void setUpInitialDataWithSingleBarDecisionTrue() {
        startDate = getLocalDate(propertyIdForH1);

        createTestData();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value())).thenReturn("true");
        when(configParamsService.getParameterValue("pacman.feature.CeilingOverrideEnabled")).thenReturn("true");
    }
}
