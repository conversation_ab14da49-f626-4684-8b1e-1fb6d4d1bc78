package com.ideas.tetris.pacman.services.reports;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.auditreader.AuditReaderService;
import com.ideas.tetris.pacman.services.auditreader.AuditRevision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateTimeParameter;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reports.dto.ScheduleReportDTO;
import com.ideas.tetris.pacman.services.reports.entity.ScheduleReport;
import com.ideas.tetris.pacman.services.reports.enums.G3Report;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ReportType;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.sftp.SFTPDetails;
import com.ideas.tetris.pacman.services.sftp.SSHJSftpService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.reports.ReportSource;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.rmi.RemoteException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT;
import static com.ideas.tetris.pacman.services.reports.ReportService.DATA_EXTRACTION_REPORT;
import static com.ideas.tetris.pacman.services.reports.ReportService.PICKUP_AND_CHANGE_REPORT;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyListOf;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
class ReportServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    @Spy
    private ReportService mockReportService;
    @Mock
    private CrudService mockCrudService;

    @InjectMocks
    private ReportService reportService;
    private PropertyService mockPropertyService;
    private WorkContextType wc;
    @Mock
    private PacmanConfigParamsService mockPacmanConfigParamsService;
    private AuditReaderService auditReaderService = new AuditReaderService();

    @Mock
    private SSHJSftpService sshjSftpService;

    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;


    @BeforeEach
    public void setup() {
        reportService.setCrudService(tenantCrudService());
        reportService.setGlobalCrudService(globalCrudService());
        mockPropertyService = new PropertyService();
        mockPropertyService.setTenantCrudService(tenantCrudService());
        auditReaderService.setCrudService(tenantCrudService());
        setUpWorkContext();
    }


    public void setUpWorkContext() {
        wc = new WorkContextType();
        wc.setClientCode("BSTN");
        wc.setClientId(2);
        wc.setPropertyCode("H1");
        wc.setPropertyId(5);
        wc.setUserId("11403");
        PacmanThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, wc);
    }

    @Test
    void testScheduleAuditing() {
        ScheduleReport scheduledRpt = populateScheduleJobInPacman();
        assertEquals(true, scheduledRpt != null);
        commitTransaction(tenantCrudService());
        AuditRevision auditRevision = getAuditRevisions(scheduledRpt).get(0);
        String revisionType = auditRevision.getAuditRevisionOperations().get(0).getRevisionType();
        assertEquals(Constants.REVISION_TYPE_CREATE, revisionType);

        scheduledRpt.setName(scheduledRpt.getName() + "_Updated");
        reportService.saveSchedule(scheduledRpt);
        commitTransaction(tenantCrudService());
        auditRevision = getAuditRevisions(scheduledRpt).get(0);
        revisionType = auditRevision.getAuditRevisionOperations().get(0).getRevisionType();
        assertEquals(Constants.REVISION_TYPE_UPDATE, revisionType);

        reportService.deleteScheduleFromDB(scheduledRpt);
        commitTransaction(tenantCrudService());
        auditRevision = getAuditRevisions(scheduledRpt).get(0);
        revisionType = auditRevision.getAuditRevisionOperations().get(0).getRevisionType();
        assertEquals(Constants.REVISION_TYPE_DELETE, revisionType);

        tenantCrudService().getEntityManager().createNativeQuery("delete from ScheduledReports_AUD").executeUpdate();
        commitTransaction(tenantCrudService());
    }


    @Test
    void testGetAllSRPNames() {
        List<String> resultList = reportService.getAllSRPNames();
        assertEquals("SHHQO1", resultList.get(0));
    }

    @Test
    void testGetJNDINameString6() {
        assertEquals("000006", reportService.getJNDINameString(6));
    }

    @Test
    void testGetJNDINameString5() {
        assertEquals("000005", reportService.getJNDINameString(5));
    }

    @Test
    void testPopulateScheduleInPacman() {
        CrudService crudService = mock(CrudService.class);
        reportService.setCrudService(crudService);
        DateService mockDateServiceLocal = Mockito.mock(DateService.class);
        when(mockDateServiceLocal.getPropertyTimeZone()).thenReturn(TimeZone.getDefault());
        when(mockDateServiceLocal.getUserPreferredDateFormat()).thenReturn("MM/dd/yyyy");

        ScheduleReport scheduledRpt = new ScheduleReport();
        scheduledRpt.setId(1);
        ScheduleReport scheduleReport = mock(ScheduleReport.class);
        when(crudService.findByNamedQuerySingleResult(ScheduleReport.BY_SCHEDULE_ID, QueryParameter.with("id", 1).parameters())).thenReturn(scheduleReport);
        reportService.setDateServiceLocal(mockDateServiceLocal);
        reportService.getSchedules(null);
        reportService.deleteScheduleFromDB(scheduledRpt);
        List<ScheduleReportDTO> schedules = reportService.getSchedules(null);

        verify(crudService, times(schedules.size())).delete(scheduleReport);
    }

    @Test
    void shouldUseSSHJForTestSFTPConnection() {
        when(mockPacmanConfigParamsService.getBooleanParameterValue(USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT)).thenReturn(true);
        FileTransferDetails fileTransferDetails = new FileTransferDetails();
        fileTransferDetails.setServerName("abc.com");
        fileTransferDetails.setPort(22);
        fileTransferDetails.setUserName("user");
        fileTransferDetails.setPassword("pwd");
        fileTransferDetails.setFolderPath("path");

        reportService.testSFTPConnection(fileTransferDetails);

        verify(sshjSftpService).testSFTPConnectionWithSSHJ(eq("TestFileForIDeaS-SFTP.txt"), any(SFTPDetails.class));
    }

    @Test
    void shouldNotUseSSHJForTestSFTPConnection() {
        FileTransferDetails transferDetailsMock = new FileTransferDetails();
        when(mockPacmanConfigParamsService.getBooleanParameterValue(USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT)).thenReturn(false);
        doReturn(true).when(mockReportService).testSFTPConnectionWithJsch(transferDetailsMock);
        inject(mockReportService, "sshjSftpService", sshjSftpService);

        mockReportService.testSFTPConnection(transferDetailsMock);

        verify(sshjSftpService, never()).testSFTPConnectionWithSSHJ(eq("TestFileForIDeaS-SFTP.txt"), any(SFTPDetails.class));
    }

    @Test
    public void updateMailId_withPropertyId() {
        String oldEmail = "<EMAIL>";
        String newEmail = "<EMAIL>";

        reportService.updateScheduleReportMailId(5, oldEmail, newEmail);

        verify(multiPropertyCrudService).executeNativeDeleteOnSingleProperty(5, ScheduleReport.UPDATE_EMAIL, QueryParameter.with("oldEmailId", oldEmail)
                .and("newEmailId", newEmail).parameters());
    }

    private List<AuditRevision> getAuditRevisions(ScheduleReport scheduleReport) {
        return auditReaderService.getAuditRevisionsForEntity(ScheduleReport.class, scheduleReport.getId());
    }

    private ScheduleReport populateScheduleJobInPacman() {
        HashMap<Object, Object> paramObject = new HashMap<Object, Object>();
        paramObject.put("param_Rolling_End_Date", "07/23/2011");
        paramObject.put("JNDI_NAME", "000005");
        paramObject.put("param_Property_ID", 5);
        paramObject.put("param_StartDate", "2011071600000");
        paramObject.put("param_isRollingDate", 0);
        paramObject.put("param_Rolling_Start_Date", "07/16/2011");
        paramObject.put("param_EndDate", "2011072300000");

        ScheduleReportDTO schedule = new ScheduleReportDTO();
        schedule.setActualReportName("ScheduleSaved");
        schedule.setOutputFormat(new ArrayList<String>());
        schedule.setRecipientMail("<EMAIL>");
        schedule.setRecurrenceInterval(1);
        schedule.setRecurrenceIntervalUnit("Days");
        schedule.setReportJasperURI("DataExtractForFcstValidation_Report");
        schedule.setReportParamsObject(paramObject);
        schedule.setDescription("Report Description goes here ");
        schedule.setName("Forecast Validation Report");
        schedule.setStartScheduleForNow(true);
        schedule.setScheduleCalStartDateTime(new DateTimeParameter(Calendar.getInstance().getTime()));
        schedule.setReportParamsObject(paramObject);

        UserService mockUserService = mock(UserService.class);
        reportService.setUserService(mockUserService);
        LDAPUser user = getLDAPTestUser();
        Set<String> pages = new HashSet<String>();
        pages.add("-666");

        when(mockUserService.getByEmailFromDB("<EMAIL>")).thenReturn(user);
        when(mockUserService.getAuthorizedPagesForProperty(wc.getUserId(), wc.getPropertyId().toString())).thenReturn(pages);
        return reportService.populateScheduleDb(schedule, 1);
    }

    private LDAPUser getLDAPTestUser() {
        LDAPUser user = new LDAPUser();
        user.setGivenName("Test");
        user.setSn("User");
        user.setCn("Test User");
        user.setClient(PacmanWorkContextTestHelper.WC_CLIENT_CODE_TEST);
        user.setMail("<EMAIL>");
        user.setDefaultPropertyGroup("1");
        user.setIsCorporate(true);
        return user;
    }

    @Test
    void testValidateToScheduleExpectException() {
        assertThrows(TetrisException.class, () -> {
            CrudService mockCrudService = Mockito.mock(CrudService.class);
            ReportService reportService = new ReportService();
            reportService.setMultiPropertyCrudService(multiPropertyCrudService);
            inject(reportService, "crudService", mockCrudService);
            inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);
            String reportType = "DATA_EXTRACTION";
            when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
            when(mockPacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_DATA_EXTRACTION_REPORT.value())).thenReturn(2);
            when(mockCrudService.findByNamedQuerySingleResult(Mockito.anyString(), Mockito.anyMapOf(String.class, Object.class))).thenReturn(new Long(2));
            reportService.validateToSchedule(reportType, G3Report.DATA_EXTRACTION_AC.getReportUnitName());
        });
    }

    @Test
    void testValidateToScheduleVaadinSpecialEventsReportExpectException() {
        assertThrows(TetrisException.class, () -> {
            CrudService mockCrudService = Mockito.mock(CrudService.class);
            ReportService reportService = new ReportService();
            reportService.setMultiPropertyCrudService(multiPropertyCrudService);
            inject(reportService, "globalCrudService", mockCrudService);
            inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);
            String reportType = "SPECIAL_EVENTS";
            when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
            when(mockPacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_SPECIAL_EVENT_REPORT.value())).thenReturn(1);
            when(mockCrudService.findByNamedQuerySingleResult(Mockito.anyString(), Mockito.anyMapOf(String.class, Object.class))).thenReturn(new Long(1));
            reportService.validateToSchedule(reportType, "SPECIAL_EVENTS");
        });
    }

    @Test
    void testValidateToScheduleExpectExceptionForAll() {
        assertThrows(TetrisException.class, () -> {
            CrudService mockCrudService = Mockito.mock(CrudService.class);
            ReportService reportService = new ReportService();
            reportService.setMultiPropertyCrudService(multiPropertyCrudService);
            inject(reportService, "crudService", mockCrudService);
            inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);
            String reportType = "FORECAST_VALIDATION";
            when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
            when(mockPacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_FORECAST_VALIDATION_REPORT.value())).thenReturn(2);
            when(mockCrudService.findByNamedQuerySingleResult(Mockito.anyString(), Mockito.anyMapOf(String.class, Object.class))).thenReturn(new Long(2));
            reportService.validateToSchedule(reportType, G3Report.DATA_EXTRACTION_FCST_VALIDATION_FORECAST.getReportUnitName());
        });
    }

    @Test
    void updateEmailRecipientFromPacmanDBTest() {
        reportService.setMultiPropertyCrudService(multiPropertyCrudService());
        String orgMail1 = "<EMAIL>";
        String orgMail2 = "<EMAIL>";
        tenantCrudService().deleteAll(ScheduleReport.class);
        tenantCrudService().executeUpdateByNativeQuery("insert into ScheduledReports (propertyID,Name,ScheduleStartTime,GenerationFrequency,EmailRecipients,ReportParameters,JasperScheduleID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) " +
                "values(5,'Report1',GETDATE(),1,'" + orgMail1 + "','userLocale=en_US',1,11403,GETDATE(),11403,GETDATE()) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into ScheduledReports (propertyID,Name,ScheduleStartTime,GenerationFrequency,EmailRecipients,ReportParameters,JasperScheduleID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) " +
                "values(5,'Report2',GETDATE(),1,'" + orgMail2 + "','userLocale=en_US',1,11403,GETDATE(),11403,GETDATE())");

        List<ScheduleReport> list = tenantCrudService().findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters());
        assertEquals(orgMail1, list.get(0).getEmailRecipients());
        assertEquals(orgMail2, list.get(1).getEmailRecipients());

        String newRecepients1 = "<EMAIL>";
        reportService.updateEmailRecipientFromPacmanDB(list.get(0), newRecepients1);
        list = tenantCrudService().findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters());
        assertEquals(newRecepients1, list.get(0).getEmailRecipients());
        assertEquals(orgMail2, list.get(1).getEmailRecipients());

        String newRecepients2 = "<EMAIL>";
        reportService.updateEmailRecipientFromPacmanDB(list.get(1), newRecepients2);
        list = tenantCrudService().findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters());
        assertEquals(newRecepients1, list.get(0).getEmailRecipients());
        assertEquals(newRecepients2, list.get(1).getEmailRecipients());
    }


    private void setMocks(Date caughtUpDate) {
        MockitoAnnotations.initMocks(this);
        DateService mockDateServiceLocal = Mockito.mock(DateService.class);
        mockReportService.setDateServiceLocal(mockDateServiceLocal);
        mockReportService.setCrudService(mockCrudService);
        when(mockDateServiceLocal.getCaughtUpDate()).thenReturn(caughtUpDate);
    }

    @Test
    void deleteOldScheduledReports_HappyPath_Test() throws RemoteException {
        Date caughtUpDate = DateUtil.convertLocalDateToJavaUtilDate(LocalDate.of(2017, 9, 20));
        setMocks(caughtUpDate);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 19 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(3, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=0##param_EndDate=Thu Sep 15 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(4, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=0##param_EndDate=Thu Sep 19 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(5, "Test", "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters())).thenReturn(schedules);
        mockReportService.deleteOldScheduledReports();
        List<ScheduleReport> deleteTenantSchedulesList = new ArrayList<>();
        deleteTenantSchedulesList.add((ScheduleReport) schedules.get(0));
        verify(mockCrudService).delete(deleteTenantSchedulesList);
    }

    @Test
    void deleteOldScheduledReports_WithEndDateInFuture_Test() throws RemoteException {
        Date caughtUpDate = DateUtil.convertLocalDateToJavaUtilDate(LocalDate.of(2017, 9, 20));
        setMocks(caughtUpDate);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 25 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 29 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(3, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=0##param_EndDate=Thu Sep 25 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(4, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=0##param_EndDate=Thu Sep 29 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(5, "Test", "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters())).thenReturn(schedules);
        mockReportService.deleteOldScheduledReports();
        verify(mockCrudService, times(0)).delete(any());
    }

    @Test
    void deleteOldScheduledReports_WithFewRollingDates_Test() throws RemoteException {
        Date caughtUpDate = DateUtil.convertLocalDateToJavaUtilDate(LocalDate.of(2017, 9, 20));
        setMocks(caughtUpDate);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(3, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=1##param_EndDate=Thu Sep 15 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(4, PICKUP_AND_CHANGE_REPORT, "param_isRollingDate=1##param_EndDate=Thu Sep 19 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(5, "Test", "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters())).thenReturn(schedules);
        mockReportService.deleteOldScheduledReports();
        List<ScheduleReport> deleteTenantSchedulesList = new ArrayList<>();
        deleteTenantSchedulesList.add((ScheduleReport) schedules.get(0));
        verify(mockCrudService).delete(deleteTenantSchedulesList);
    }

    @Test
    void deleteOldScheduledReports_WithNoEntryInPacmanDB_Test() throws RemoteException {
        Date caughtUpDate = new Date();
        setMocks(caughtUpDate);
        List<Object> schedules = new ArrayList<>();
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters())).thenReturn(schedules);
        mockReportService.deleteOldScheduledReports();
        verify(mockCrudService, times(0)).delete(any());
    }

    @Test
    void shouldCreateParameterStringForURLConstruction() {
        String expected = "IS_IGNORE_PAGINATION=true&decorate=no&param_EndDate=20170220&param_StartDate=20170202";
        Map<String, String> inputMap = createParamsMap();

        String actualValue = reportService.convertParamMapToString(inputMap, "&");

        assertEquals(expected, actualValue);

    }

    private Map<String, String> createParamsMap() {
        Map<String, String> inputMap = new HashMap<>();
        inputMap.put("param_EndDate", "2017-02-20");
        inputMap.put("IS_IGNORE_PAGINATION", "true");
        inputMap.put("param_StartDate", "2017-02-02");
        inputMap.put("decorate", "no");
        return inputMap;
    }

    @Test
    void getReportParameters() {
        String reportsParam = "IS_IGNORE_PAGINATION=true##decorate=no##param_EndDate=2017-02-20##param_StartDate=2017-02-02";
        Map<String, String> expected = createParamsMap();

        Map<String, String> actual = reportService.getReportParameters(reportsParam);

        assertMap(expected, actual);
    }

    @Test
    void getReportParametersForNullValue() {
        String reportsParam = "IS_IGNORE_PAGINATION=##decorate=no##param_EndDate=2017-02-20##param_StartDate=2017-02-02";
        Map<String, String> expected = createParamsMapWithMissingValue();

        Map<String, String> actual = reportService.getReportParameters(reportsParam);

        assertMap(expected, actual);
    }

    private Map<String, String> createParamsMapWithMissingValue() {
        Map<String, String> inputMap = new HashMap<>();
        inputMap.put("param_EndDate", "2017-02-20");
        inputMap.put("IS_IGNORE_PAGINATION", "");
        inputMap.put("param_StartDate", "2017-02-02");
        inputMap.put("decorate", "no");
        return inputMap;
    }

    private void assertMap(final Map<String, String> expected, final Map<String, String> actual) {
        assertEquals(expected.size(), actual.size());
        expected.keySet()
                .forEach(key -> assertEquals(expected.get(key), actual.get(key)));
    }


    private ScheduleReport getScheduleReportObject(int id, String actualReportName, String params) {
        ScheduleReport sr = new ScheduleReport();
        sr.setId(id);
        sr.setName("SR" + id);
        sr.setJasperScheduleID(id + 10);
        sr.setRportParameters(params);
        sr.setActualReportName(actualReportName);
        return sr;
    }


    private ScheduleReport getScheduleReportObject(int id, String actualReportName, String params,
                                                   Date startDateTime, int generationFrequency) {
        ScheduleReport sr = getScheduleReportObject(id, actualReportName, params);
        sr.setScheduleStartTime(startDateTime);
        sr.setGenerationFrequency(generationFrequency);
        sr.setRecurrenceUnit("Days");
        return sr;
    }

    private ScheduleReport getWeeklyScheduleReportObject(int id, String actualReportName, String params,
                                                         Date startDateTime, int generationFrequency) {
        ScheduleReport sr = getScheduleReportObject(id, actualReportName, params);
        sr.setScheduleStartTime(startDateTime);
        sr.setGenerationFrequency(generationFrequency);
        sr.setRecurrenceUnit("Weeks");
        return sr;
    }

    private ScheduleReport getScheduleReportObject(int id, String actualReportName, String params, String executionSchedule) {
        ScheduleReport sr = getScheduleReportObject(id, actualReportName, params);
        sr.setExecutionSchedule(executionSchedule);

        return sr;
    }

    @Test
    void shouldThrowExceptionWhenMaxScheduleLimitIsReached() {
        assertThrows(TetrisException.class, () -> {
            ReportService reportService = givenInitialSetupFor(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_OPERATIONS_REPORT);
            reportService.validateToSchedule("OPERATIONS_REPORT", G3Report.OPERATIONS_REPORT.getReportUnitName());
        });
    }

    private ReportService givenInitialSetupFor(FeatureTogglesConfigParamName configParam) {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        ReportService reportService = new ReportService();
        reportService.setMultiPropertyCrudService(multiPropertyCrudService);
        inject(reportService, "crudService", mockCrudService);
        inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
        when(mockPacmanConfigParamsService.getIntegerParameterValue(configParam.value())).thenReturn(2);
        when(mockCrudService.findByNamedQuerySingleResult(Mockito.anyString(), Mockito.anyMapOf(String.class, Object.class))).thenReturn(new Long(2));
        return reportService;
    }

    @Test
    void getAllSchedulesForVerifyingDuplicates() {
        Date caughtUpDate = DateUtil.convertLocalDateToJavaUtilDate(LocalDate.of(2017, 9, 20));
        setMocks(caughtUpDate);
        mockReportService.getAllSchedulesForVerifyingDuplicates(null);
        verify(mockReportService).loadSchedulesByPropertyId(5);
        verify(mockReportService).convertToDTO(anyListOf(ScheduleReport.class), any());
    }

    @Test
    void testGetAllSchedulesByReportType() {
        Set<String> actualReportNames = new HashSet<>();
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017"));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_ACTUAL_REPORT_NAMES, QueryParameter.with("actualReportNames", actualReportNames).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllSchedulesByReportType(ReportType.DATA_EXTRACTION);
        assertNotNull(scheduleReportListActual);
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_isExecutionScheduleMatching_null() {
        ScheduleReport report = new ScheduleReport();
        report.setExecutionSchedule(null);
        assertTrue(reportService.isExecutionScheduleMatching(report, ReportSource.AFTER_BDE));
    }

    @Test
    void test_isExecutionScheduleMatching_bde() {
        ScheduleReport report = new ScheduleReport();
        report.setExecutionSchedule("BDE");
        assertTrue(reportService.isExecutionScheduleMatching(report, ReportSource.AFTER_BDE));
    }

    @Test
    void test_isExecutionScheduleMatching_cdp() {
        ScheduleReport report = new ScheduleReport();
        report.setExecutionSchedule("CDP");
        assertFalse(reportService.isExecutionScheduleMatching(report, ReportSource.AFTER_BDE));
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_1_day_cdp_valid() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);

        ((ScheduleReport) schedules.get(0)).setExecutionSchedule("CDP");
        ((ScheduleReport) schedules.get(1)).setExecutionSchedule("CDP");

        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_CDP);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_1_day_cdp_invalid() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);

        ((ScheduleReport) schedules.get(0)).setExecutionSchedule("BDE");
        ((ScheduleReport) schedules.get(1)).setExecutionSchedule("CDP,BDE");

        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_CDP);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_1_day_bde() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);

        ((ScheduleReport) schedules.get(0)).setExecutionSchedule("BDE");
        ((ScheduleReport) schedules.get(1)).setExecutionSchedule("CDP,BDE");

        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_1_day() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_2_days() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 2));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual =
                mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_start_date_in_future() {
        Set<String> actualReportNames = new HashSet<>();
        Date businessDate = DateUtil.getDate(15, 8, 2017);
        actualReportNames.add(ReportType.DATA_EXTRACTION.name());
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(16, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_start_time_after_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017, 23, 10, 10), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(16, 8, 2017, 23, 10, 10), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_start_time_same_as_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017, 22, 0, 0), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(16, 8, 2017, 22, 0, 0), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_daily_start_time_before_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017", DateUtil.getDate(14, 8, 2017, 21, 10, 10), 1));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017", DateUtil.getDate(16, 8, 2017, 21, 10, 10), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_1_week() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(21, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).
                        and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_2_weeks() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 2));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_start_date_in_future() {
        Date businessDate = DateUtil.getDate(14, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(21, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_start_time_after_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017, 23, 10, 10), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(21, 8, 2017, 23, 10, 10), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_start_time_before_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017, 21, 10, 10), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(21, 8, 2017, 21, 10, 10), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name()).and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService.getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_off_start_time_same_as_bde() {
        Date businessDate = DateUtil.getDate(14, 8, 2017, 22, 0, 0);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017, 22, 0, 0), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(21, 8, 2017, 22, 0, 0), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_invalid_day1() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(15, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_invalid_day2() {
        Date businessDate = DateUtil.getDate(22, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(15, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_invalid_day3() {
        Date businessDate = DateUtil.getDate(23, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(15, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(0, scheduleReportListActual.size());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_valid_day() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_freq_1() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_dow_value_not_set_freq_2() {
        Date businessDate = DateUtil.getDate(21, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 2));
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_invalid_day() {
        Date businessDate = DateUtil.getDate(22, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        addDoWValues(schedules);
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(0, scheduleReportListActual.size());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_valid_day() {
        Date businessDate = DateUtil.getDate(19, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        addDoWValues(schedules);
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_freq_2() {
        Date businessDate = DateUtil.getDate(19, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 2));
        addDoWValues(schedules);
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(1, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_freq_2_next_week() {
        Date businessDate = DateUtil.getDate(26, 8, 2017);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 1));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(14, 8, 2017), 2));
        addDoWValues(schedules);
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_freq_52_valid() {
        Date businessDate = DateUtil.getDate(31, 11, 2018);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(1, 0, 2018), 52));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(1, 0, 2018), 52));
        for (Object o : schedules) {
            ((ScheduleReport) o).setDayOfWeek("Mon");
        }
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(2, scheduleReportListActual.size());
        assertEquals("DATA_EXTRACTION", scheduleReportListActual.get(0).getActualReportName());
    }

    @Test
    void test_GetAllQualifiedSchedulesByReportType_weekly_dow_on_value_set_freq_52_invalid() {
        Date businessDate = DateUtil.getDate(7, 0, 2019);
        List<Object> schedules = new ArrayList<>();
        schedules.add(getWeeklyScheduleReportObject(1, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017",
                DateUtil.getDate(1, 0, 2018), 52));
        schedules.add(getWeeklyScheduleReportObject(2, DATA_EXTRACTION_REPORT,
                "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017",
                DateUtil.getDate(1, 0, 2018), 52));
        for (Object o : schedules) {
            ((ScheduleReport) o).setDayOfWeek("Mon");
        }
        when(mockCrudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", ReportType.DATA_EXTRACTION.name())
                        .and("businessDate", businessDate).parameters())).thenReturn(schedules);
        List<ScheduleReport> scheduleReportListActual = mockReportService
                .getAllQualifiedSchedulesByReportType(ReportType.DATA_EXTRACTION, businessDate, ReportSource.AFTER_BDE);
        assertNotNull(scheduleReportListActual);
        assertEquals(0, scheduleReportListActual.size());
    }

    @Test
    void verifyDuplicateSchedule_edit_schedule() {
        PacmanWorkContextHelper.setPropertyId(5);
        DateService mockDateServiceLocal = Mockito.mock(DateService.class);
        when(mockDateServiceLocal.getUserPreferredDateFormat()).thenReturn("MM/dd/yyyy");
        reportService.setDateServiceLocal(mockDateServiceLocal);
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        reportService.setCrudService(mockCrudService);
        String scheduleName = "Test_Schedule";
        ScheduleReport scheduleReport = mock(ScheduleReport.class);
        when(scheduleReport.getName()).thenReturn(scheduleName);
        when(scheduleReport.getId()).thenReturn(100);
        when(scheduleReport.getExecutionSchedule()).thenReturn("BDE");
        when(scheduleReport.getActualReportName()).thenReturn("BOOKING_SITUATION_REPORT");
        List<Object> scheduleReports = new ArrayList<>();
        scheduleReports.add(scheduleReport);
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters()))
                .thenReturn(scheduleReports);
        ScheduleReportDTO scheduleReportDTO = new ScheduleReportDTO();
        scheduleReportDTO.setName(scheduleName);
        scheduleReportDTO.setId(100);
        reportService.verifyDuplicateSchedule(scheduleReportDTO);
        verify(mockCrudService).findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters());
    }

    @Test
    void verifyDuplicateSchedule_new_schedule() {
        PacmanWorkContextHelper.setPropertyId(5);
        DateService mockDateServiceLocal = Mockito.mock(DateService.class);
        when(mockDateServiceLocal.getUserPreferredDateFormat()).thenReturn("MM/dd/yyyy");
        reportService.setDateServiceLocal(mockDateServiceLocal);
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        reportService.setCrudService(mockCrudService);
        String scheduleName = "Test_Schedule";
        ScheduleReport scheduleReport = mock(ScheduleReport.class);
        when(scheduleReport.getName()).thenReturn(scheduleName);
        when(scheduleReport.getId()).thenReturn(100);
        when(scheduleReport.getExecutionSchedule()).thenReturn("BDE");
        when(scheduleReport.getActualReportName()).thenReturn("BOOKING_SITUATION_REPORT");
        List<Object> scheduleReports = new ArrayList<>();
        scheduleReports.add(scheduleReport);
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with("propertyId", 5).parameters()))
                .thenReturn(scheduleReports);
        ScheduleReportDTO scheduleReportDTO = new ScheduleReportDTO();
        scheduleReportDTO.setName(scheduleName);
        TetrisException exception = assertThrows(TetrisException.class, () -> reportService.verifyDuplicateSchedule(scheduleReportDTO));
        assertEquals(ErrorCode.DUPLICATE_DATA, exception.getErrorCode());
    }

    @Test
    void getDeliverMode_Test() {
        ScheduleReport scheduleReport = mock(ScheduleReport.class);
        when(scheduleReport.getExecutionSchedule()).thenReturn("BDE");
        when(scheduleReport.getActualReportName()).thenReturn("BOOKING_SITUATION_REPORT");
        reportService.getDeliverMode(scheduleReport);
        assertEquals("BDE", scheduleReport.getExecutionSchedule());
        assertEquals("BOOKING_SITUATION_REPORT", scheduleReport.getActualReportName());
        when(scheduleReport.getExecutionSchedule()).thenReturn("CDP");
        reportService.getDeliverMode(scheduleReport);
        assertNotNull(scheduleReport.getExecutionSchedule());
        assertEquals("CDP", scheduleReport.getExecutionSchedule());
        when(scheduleReport.getExecutionSchedule()).thenReturn("- - ");
        reportService.getDeliverMode(scheduleReport);
        assertEquals("- - ", scheduleReport.getExecutionSchedule());
    }

    @Test
    void testGetTotalSchedulesBDE() {
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017", "BDE"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017", "BDE"));
        reportService.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(schedules);
        final long totalSchedulesBDE = reportService.getTotalSchedules(1, ReportSource.AFTER_BDE);
        assertEquals(2L, totalSchedulesBDE);
        final long totalSchedulesCDP = reportService.getTotalSchedules(1, ReportSource.AFTER_CDP);
        assertEquals(0L, totalSchedulesCDP);
    }

    @Test
    void testGetTotalSchedulesCDP() {
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017", "CDP"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017", "CDP"));
        reportService.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(schedules);
        final long totalSchedulesBDE = reportService.getTotalSchedules(1, ReportSource.AFTER_BDE);
        assertEquals(0L, totalSchedulesBDE);
        final long totalSchedulesCDP = reportService.getTotalSchedules(1, ReportSource.AFTER_CDP);
        assertEquals(2L, totalSchedulesCDP);
    }

    @Test
    void testGetTotalSchedulesMix() {
        List<Object> schedules = new ArrayList<>();
        schedules.add(getScheduleReportObject(1, DATA_EXTRACTION_REPORT, "param_isRollingDate=0##EndDate=Thu Sep 15 00:00:00 IST 2017", "BDE"));
        schedules.add(getScheduleReportObject(2, DATA_EXTRACTION_REPORT, "param_isRollingDate=1##EndDate=Thu Sep 19 00:00:00 IST 2017", "CDP"));
        reportService.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuery(ScheduleReport.ALL,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(schedules);
        final long totalSchedulesBDE = reportService.getTotalSchedules(1, ReportSource.AFTER_BDE);
        assertEquals(1L, totalSchedulesBDE);
        final long totalSchedulesCDP = reportService.getTotalSchedules(1, ReportSource.AFTER_CDP);
        assertEquals(1L, totalSchedulesCDP);
    }

    private void addDoWValues(List<Object> schedules) {
        for (Object o : schedules) {
            ((ScheduleReport) o).setDayOfWeek("Mon,Tue,Wed,Thu");
        }
    }

    @Test
    void updateScheduleAudit() {
        Integer scheduleReportId = 1;
        setUpWorkContext();
        reportService.setCrudService(mockCrudService);
        reportService.updateScheduleAudit(scheduleReportId);
        verify(mockCrudService, times(1)).executeUpdateByNativeQuery(anyString(), anyMap());
    }

    @Test
    void validateToScheduleTest_MinMaxLOSRestrictionReport_limitExhaustForBothMinMaxLOSandFPLOS() {
        assertThrows(TetrisException.class, () -> {
            List<Object> resultSet = getResultSetFor(1l, 1l);
            ReportService reportService = setupForRestrictionReport(2, resultSet);
            reportService.validateToSchedule(ReportType.RESTRICTION_REPORT.name(), G3Report.RESTRICTION_MINMAXLOS_REPORT.getReportUnitName());
        });
    }

    @Test
    void validateToScheduleTest_MinMaxLOSRestrictionReport_limitExhaustForMinMaxLOS() {
        assertThrows(TetrisException.class, () -> {
            List<Object> resultSet = getResultSetFor(5l, 0l);
            ReportService reportService = setupForRestrictionReport(9, resultSet);
            reportService.validateToSchedule(ReportType.RESTRICTION_REPORT.name(), G3Report.RESTRICTION_MINMAXLOS_REPORT.getReportUnitName());
        });
    }

    @Test
    void validateToScheduleTest_FPLOSRestrictionReport_limitExhaustForFpLOS() {
        assertThrows(TetrisException.class, () -> {
            List<Object> resultSet = getResultSetFor(0l, 4l);
            ReportService reportService = setupForRestrictionReport(9, resultSet);
            reportService.validateToSchedule(ReportType.RESTRICTION_REPORT.name(), G3Report.RESTRICTION_FPLOS_REPORT.getReportUnitName());
        });
    }

    @Test
    void validateToScheduleTest_MinMaxRestrictionReport_scheduleOnPriorityForMinMaxLos() {
        assertDoesNotThrow(() -> {
            List<Object> resultSet = getResultSetFor(4l, 4l);
            ReportService reportService = setupForRestrictionReport(9, resultSet);
            reportService.validateToSchedule(ReportType.RESTRICTION_REPORT.name(), G3Report.RESTRICTION_MINMAXLOS_REPORT.getReportUnitName());
        });
    }

    @Test
    void validateToScheduleTest_FpLosRestrictionReport_remainingPlaceReservedForMinMaxLos() {
        assertThrows(TetrisException.class, () -> {
            List<Object> resultSet = getResultSetFor(4l, 4l);
            ReportService reportService = setupForRestrictionReport(9, resultSet);
            reportService.validateToSchedule(ReportType.RESTRICTION_REPORT.name(), G3Report.RESTRICTION_FPLOS_REPORT.getReportUnitName());
        });
    }


    @Test
    void testIndividualGroupWashScheduledReport() {
        assertThrows(TetrisException.class, () -> {
            CrudService mockCrudService = Mockito.mock(CrudService.class);
            ReportService reportService = new ReportService();
            reportService.setMultiPropertyCrudService(multiPropertyCrudService);
            inject(reportService, "crudService", mockCrudService);
            inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);
            String reportType = "INDIVIDUAL_GROUP_WASH";
            when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
            when(mockPacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_INDIVIDUAL_GROUP_WASH_REPORT.value())).thenReturn(2);
            when(mockCrudService.findByNamedQuerySingleResult(Mockito.anyString(), Mockito.anyMapOf(String.class, Object.class))).thenReturn(new Long(2));
            reportService.validateToSchedule(reportType, G3Report.INDIVIDUAL_GROUP_WASH.getReportUnitName());
        });
    }

    private List<Object> getResultSetFor(Long minLosCount, Long fpLosCount) {
        List<Object> resultSet = new ArrayList<>();
        if (minLosCount > 0l) {
            Object[] restriction_minLOSReport = new Object[2];
            restriction_minLOSReport[0] = "restriction_minLOSReport";
            restriction_minLOSReport[1] = minLosCount;
            resultSet.add(restriction_minLOSReport);
        }
        if (fpLosCount > 0l) {
            Object[] restriction_FPLOSReport = new Object[2];
            restriction_FPLOSReport[0] = "restriction_FPLOSReport";
            restriction_FPLOSReport[1] = fpLosCount;
            resultSet.add(restriction_FPLOSReport);
        }
        return resultSet;
    }

    private ReportService setupForRestrictionReport(Integer numberOfScheduleConfigured, List<Object> resultSet) {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        ReportService reportService = new ReportService();
        reportService.setMultiPropertyCrudService(multiPropertyCrudService);
        inject(reportService, "crudService", mockCrudService);
        inject(reportService, "pacmanConfigParamsService", mockPacmanConfigParamsService);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value())).thenReturn(true);
        when(mockPacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_RESTRICTION_REPORT.value()))
                .thenReturn(numberOfScheduleConfigured);
        when(mockCrudService.findByNamedQuery(ScheduleReport.NUMBER_OF_SCHEDULES_FOR_RESTRICTION_REPORT,
                QueryParameter.with("actualReportName", ReportType.RESTRICTION_REPORT.name()).parameters()))
                .thenReturn(resultSet);
        return reportService;
    }
}