package com.ideas.tetris.pacman.services.grouppricing.evaluation.dto;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoomPriceTier;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpace;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.FunctionSpaceEvalWrapper;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class GroupEvaluationFunctionSpaceRequestBuilderTest {

    @Test
    public void buildGroupEvalFunctionSpacesForRequest_RoomRequestSpansMultipleDays() throws Exception {
        Set<GroupEvaluationFunctionSpace> inputSet = new LinkedHashSet<>();
        GroupEvaluationFunctionSpace gefs = new GroupEvaluationFunctionSpace();
        gefs.setStartTime(new LocalDateTime(2015, 1, 1, 10, 0));
        gefs.setEndTime(new LocalDateTime(2015, 1, 3, 12, 0));
        gefs.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        gefs.addGroupEvaluationFunctionSpaceFunctionRoom(new GroupEvaluationFunctionSpaceFunctionRoom());
        gefs.setSquareFeet(new BigDecimal(2000));
        inputSet.add(gefs);

        List<GroupEvaluationFunctionSpace> retList = GroupEvaluationFunctionSpaceRequestBuilder
                .buildGroupEvalFunctionSpacesForRequest(inputSet);

        assertEquals(3, retList.size());
        assertEquals(new LocalDate(2015, 1, 1), retList.get(0).getStartTime().toLocalDate());
        assertEquals(new LocalDate(2015, 1, 2), retList.get(1).getStartTime().toLocalDate());
        assertEquals(new LocalDate(2015, 1, 3), retList.get(2).getStartTime().toLocalDate());
        assertEquals(
                new LocalDateTime(gefs.getStartTime().toLocalDate().plusDays(1).toDateMidnight().toDate())
                        .minusMillis(1),
                retList.get(0).getEndTime());
        assertEquals(
                new LocalDateTime(gefs.getStartTime().toLocalDate().plusDays(2).toDateMidnight().toDate())
                        .minusMillis(1),
                retList.get(1).getEndTime());
        assertEquals(new LocalDateTime(gefs.getEndTime()), retList.get(2).getEndTime());
    }

    @Test
    public void buildGroupEvalFunctionSpacesForRequest_SameDay() throws Exception {
        Set<GroupEvaluationFunctionSpace> inputSet = new LinkedHashSet<>();
        GroupEvaluationFunctionSpace gefs = new GroupEvaluationFunctionSpace();
        gefs.setStartTime(new LocalDateTime(2015, 1, 1, 10, 0));
        gefs.setEndTime(new LocalDateTime(2015, 1, 1, 12, 0));
        gefs.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        gefs.addGroupEvaluationFunctionSpaceFunctionRoom(new GroupEvaluationFunctionSpaceFunctionRoom());
        gefs.setSquareFeet(new BigDecimal(2000));
        inputSet.add(gefs);

        List<GroupEvaluationFunctionSpace> retList = GroupEvaluationFunctionSpaceRequestBuilder.buildGroupEvalFunctionSpacesForRequest(inputSet);

        assertEquals(1, retList.size());
        assertEquals(new LocalDate(2015, 1, 1), retList.get(0).getStartTime().toLocalDate());
        assertEquals(new LocalDateTime(gefs.getEndTime()), retList.get(0).getEndTime());
    }

    @Test
    public void buildGroupEvalFunctionSpacesForRequest_TwoFunctionSpaceDays() throws Exception {
        Set<GroupEvaluationFunctionSpace> inputSet = new LinkedHashSet<>();
        GroupEvaluationFunctionSpace gefs1 = new GroupEvaluationFunctionSpace();
        gefs1.setStartTime(new LocalDateTime(2015, 1, 1, 10, 0));
        gefs1.setEndTime(new LocalDateTime(2015, 1, 1, 12, 0));
        gefs1.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        gefs1.addGroupEvaluationFunctionSpaceFunctionRoom(new GroupEvaluationFunctionSpaceFunctionRoom());
        gefs1.setSquareFeet(new BigDecimal(2000));
        inputSet.add(gefs1);

        GroupEvaluationFunctionSpace gefs2 = new GroupEvaluationFunctionSpace();
        gefs2.setStartTime(new LocalDateTime(2015, 1, 2, 8, 0));
        gefs2.setEndTime(new LocalDateTime(2015, 1, 2, 10, 0));
        gefs2.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        gefs2.addGroupEvaluationFunctionSpaceFunctionRoom(new GroupEvaluationFunctionSpaceFunctionRoom());
        gefs2.setSquareFeet(new BigDecimal(2000));
        inputSet.add(gefs2);

        List<GroupEvaluationFunctionSpace> retList = GroupEvaluationFunctionSpaceRequestBuilder.buildGroupEvalFunctionSpacesForRequest(inputSet);

        assertEquals(2, retList.size());
        assertEquals(new LocalDate(2015, 1, 1), retList.get(0).getStartTime().toLocalDate());
        assertEquals(new LocalDateTime(gefs1.getEndTime()), retList.get(0).getEndTime());
        assertEquals(new LocalDate(2015, 1, 2), retList.get(1).getStartTime().toLocalDate());
        assertEquals(new LocalDateTime(gefs2.getEndTime()), retList.get(1).getEndTime());
    }

    @Test
    public void getFunctionSpaceRequestedMinutes() throws Exception {
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart(new LocalTime(18, 0), LocalTime.MIDNIGHT.minusMillis(1));
        dayPart.setId(1);
        FSDayPartWrapper wrapper = new FSDayPartWrapper(1, dayPart.getBeginTime(), dayPart.getEndTime(), dayPart.getId());
        GroupEvaluationFunctionSpace gefs = new GroupEvaluationFunctionSpace();
        gefs.setStartTime(new LocalDateTime(2015, 1, 1, 18, 0));
        gefs.setEndTime(new LocalDateTime(2015, 1, 1, 18, 30));

        assertEquals(BigDecimal.valueOf(3).setScale(4, BigDecimal.ROUND_HALF_UP), GroupEvaluationFunctionSpaceRequestBuilder.getFunctionSpaceRequestedMinutes(wrapper, gefs, new BigDecimal(1000), BigDecimal.valueOf(100)));
    }

    @Test
    public void getSuggestMARAndUpperLimitForRequest() throws Exception {
        FunctionSpaceEvalWrapper wrapper = new FunctionSpaceEvalWrapper();
        wrapper.setAreaEvaluation(false);
        wrapper.setMARValue(new BigDecimal(250));
        wrapper.setUpperLimitValue(new BigDecimal(500));
        wrapper.setDate(new LocalDate(2015, 11, 1));

        Map<String, BigDecimal> retMap = GroupEvaluationFunctionSpaceRequestBuilder.getSuggestMARAndUpperLimitForRequest(singletonList(wrapper));

        assertNotNull(retMap);
        assertTrue(retMap.size() == 2);
        assertEquals(wrapper.getMARValue().setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_MAR));
        assertEquals(wrapper.getUpperLimitValue().setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_UPPER_LIMIT));
    }

    @Test
    public void getSuggestMARAndUpperLimitForRequest_MultipleDays() throws Exception {
        List<FunctionSpaceEvalWrapper> wrappers = new ArrayList<>();

        FunctionSpaceEvalWrapper wrapper1 = new FunctionSpaceEvalWrapper();
        wrapper1.setAreaEvaluation(true);
        wrapper1.setMARValue(new BigDecimal(250));
        wrapper1.setUpperLimitValue(new BigDecimal(500));
        wrapper1.setDate(new LocalDate(2015, 11, 1));

        FunctionSpaceEvalWrapper wrapper2 = new FunctionSpaceEvalWrapper();
        wrapper2.setAreaEvaluation(true);
        wrapper2.setMARValue(new BigDecimal(250));
        wrapper2.setUpperLimitValue(new BigDecimal(500));
        wrapper2.setDate(new LocalDate(2015, 11, 2));

        wrappers.add(wrapper1);
        wrappers.add(wrapper2);

        Map<String, BigDecimal> retMap = GroupEvaluationFunctionSpaceRequestBuilder.getSuggestMARAndUpperLimitForRequest(wrappers);

        assertNotNull(retMap);
        assertTrue(retMap.size() == 2);
        assertEquals(new BigDecimal(500).setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_MAR));
        assertEquals(new BigDecimal(1000).setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_UPPER_LIMIT));
    }

    @Test
    public void getSuggestMARAndUpperLimitForRequest_WithAreaAndRoomRequest() throws Exception {
        List<FunctionSpaceEvalWrapper> wrappers = new ArrayList<>();

        FunctionSpaceEvalWrapper wrapper1 = new FunctionSpaceEvalWrapper();
        wrapper1.setAreaEvaluation(true);
        wrapper1.setMARValue(new BigDecimal(500));
        wrapper1.setUpperLimitValue(new BigDecimal(1500));
        wrapper1.setDate(new LocalDate(2015, 11, 1));

        FunctionSpaceEvalWrapper wrapper2 = new FunctionSpaceEvalWrapper();
        wrapper2.setAreaEvaluation(false);
        wrapper2.setMARValue(new BigDecimal(200));
        wrapper2.setUpperLimitValue(new BigDecimal(500));
        wrapper2.setDate(new LocalDate(2015, 11, 1));

        wrappers.add(wrapper1);
        wrappers.add(wrapper2);

        Map<String, BigDecimal> retMap = GroupEvaluationFunctionSpaceRequestBuilder.getSuggestMARAndUpperLimitForRequest(wrappers);

        assertNotNull(retMap);
        assertTrue(retMap.size() == 2);
        // Should use area MAR and Upper Limit Values 500 and 1500
        assertEquals(new BigDecimal(500).setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_MAR));
        assertEquals(new BigDecimal(1500).setScale(2, BigDecimal.ROUND_HALF_UP), retMap.get(GroupEvaluationFunctionSpaceRequestBuilder.SUGGESTED_UPPER_LIMIT));
    }

    @Test
    public void getDayPartList_OnlyOneDayPart() throws Exception {
        GroupEvaluationFunctionSpace gefs = new GroupEvaluationFunctionSpace();
        gefs.setStartTime(new LocalDateTime(2015, 1, 1, 8, 0));
        gefs.setEndTime(new LocalDateTime(2015, 1, 1, 10, 0));

        List<FSDayPartWrapper> dayPartList = new ArrayList<>();

        FunctionSpaceDayPart dayPart1 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(8, 0), new LocalTime(11, 0));
        dayPart1.setId(1);
        FunctionSpaceDayPart dayPart2 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(11, 0), new LocalTime(13, 0));
        dayPart2.setId(2);
        dayPartList.add(new FSDayPartWrapper(1, dayPart1.getBeginTime(), dayPart1.getEndTime(), dayPart1.getId()));
        dayPartList.add(new FSDayPartWrapper(2, dayPart2.getBeginTime(), dayPart2.getEndTime(), dayPart2.getId()));

        List<FSDayPartWrapper> retDayParts = GroupEvaluationFunctionSpaceRequestBuilder.getDayPartList(gefs, dayPartList);

        assertEquals(1, retDayParts.size());
    }

    @Test
    public void getDayPartList_TwoDayParts() throws Exception {
        GroupEvaluationFunctionSpace gefs = new GroupEvaluationFunctionSpace();
        gefs.setStartTime(new LocalDateTime(2015, 1, 1, 8, 0));
        gefs.setEndTime(new LocalDateTime(2015, 1, 1, 13, 0));

        List<FSDayPartWrapper> dayPartList = new ArrayList<>();

        FunctionSpaceDayPart dayPart1 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(8, 0), new LocalTime(11, 0));
        dayPart1.setId(1);
        FunctionSpaceDayPart dayPart2 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(11, 0), new LocalTime(13, 0));
        dayPart2.setId(2);
        dayPartList.add(new FSDayPartWrapper(1, dayPart1.getBeginTime(), dayPart1.getEndTime(), dayPart1.getId()));
        dayPartList.add(new FSDayPartWrapper(2, dayPart2.getBeginTime(), dayPart2.getEndTime(), dayPart2.getId()));

        List<FSDayPartWrapper> retDayParts = GroupEvaluationFunctionSpaceRequestBuilder.getDayPartList(gefs, dayPartList);

        assertEquals(2, retDayParts.size());
    }

    @Test
    public void buildFSDayPartWrappers() throws Exception {
        List<FunctionSpaceDayPart> dayParts = setupDayParts();

        List<FSDayPartWrapper> dayPartWrappers = GroupEvaluationFunctionSpaceRequestBuilder.buildFSDayPartWrappers(dayParts);

        assertEquals(5, dayPartWrappers.size());
    }

    private List<FunctionSpaceDayPart> setupDayParts() {
        // build function space day parts
        List<FunctionSpaceDayPart> fsDayParts = new ArrayList();

        FunctionSpaceDayPart overnight = new FunctionSpaceDayPart();
        overnight.setId(1);
        overnight.setIncluded(false);
        overnight.setBeginTime(new LocalTime(2, 0, 0, 0));
        overnight.setEndTime(new LocalTime(6, 0, 0, 0));

        FunctionSpaceDayPart morning = new FunctionSpaceDayPart();
        morning.setId(2);
        morning.setIncluded(true);
        morning.setBeginTime(new LocalTime(6, 0, 0, 0));
        morning.setEndTime(new LocalTime(11, 0, 0, 0));

        FunctionSpaceDayPart noon = new FunctionSpaceDayPart();
        noon.setId(3);
        noon.setIncluded(true);
        noon.setBeginTime(new LocalTime(11, 0, 0, 0));
        noon.setEndTime(new LocalTime(14, 0, 0, 0));

        FunctionSpaceDayPart afternoon = new FunctionSpaceDayPart();
        afternoon.setId(4);
        afternoon.setIncluded(true);
        afternoon.setBeginTime(new LocalTime(14, 0, 0, 0));
        afternoon.setEndTime(new LocalTime(18, 0, 0, 0));

        FunctionSpaceDayPart evening = new FunctionSpaceDayPart();
        evening.setId(5);
        evening.setIncluded(true);
        evening.setBeginTime(new LocalTime(18, 0, 0, 0));
        evening.setEndTime(new LocalTime(1, 0, 0, 0));

        fsDayParts.add(overnight);
        fsDayParts.add(morning);
        fsDayParts.add(noon);
        fsDayParts.add(afternoon);
        fsDayParts.add(evening);

        return fsDayParts;
    }
}
