package com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal;

import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.PropertyHealthEvaluatorFactory;
import org.mockito.Mockito;

import java.math.BigDecimal;

public class HealthTestUtility {
    public static PropertyHealthEvaluationDAO getMockDAO() {
        PropertyHealthEvaluationDAO evaluationDAO = Mockito.mock(PropertyHealthEvaluationDAO.class);
        return evaluationDAO;
    }

    public static PropertyHealthEvaluatorFactory getMockFactory() {
        PropertyHealthEvaluatorFactory mockFactory = Mockito.mock(PropertyHealthEvaluatorFactory.class);
        return mockFactory;
    }

    public static Object[] createObject(int pId, String pCode, BigDecimal prcnt) {
        Object[] object = new Object[3];
        object[0] = pId;
        object[1] = pCode;
        object[2] = prcnt;
        return object;
    }
}