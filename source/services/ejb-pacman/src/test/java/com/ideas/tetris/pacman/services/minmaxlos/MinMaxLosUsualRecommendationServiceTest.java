package com.ideas.tetris.pacman.services.minmaxlos;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.minlos.entity.MinlosDecisions;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.services.minmaxlos.CalculateMinMaxLos.MAX_ALLOWED_STAY_LENGTH;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class MinMaxLosUsualRecommendationServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    MinMaxLosUsualRecommendationService minMaxLosUsualRecommendationService;
    @Mock
    DateService dateService;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    JpaJdbcUtil jpaJdbcUtil;
    @Mock
    DecisionService decisionService;

    private LocalDate date = new LocalDate(2014, 3, 14);

    @BeforeEach
    public void setUp() {
        tenantCrudService().getEntityManager().clear();
        setWorkContextProperty(TestProperty.H2);
        minMaxLosUsualRecommendationService.setCrudService(tenantCrudService());
        minMaxLosUsualRecommendationService.dateService = dateService;
        minMaxLosUsualRecommendationService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(Constants.TARS_DESTINATION))).thenReturn(Constants.DIFFERENTIAL);
        when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
    }

    @Test
    public void generatePaceAndNonPaceMinMaxLosDecisions() {
        createMockForFplosDecisionGeneration();
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 1, MAX_ALLOWED_STAY_LENGTH, 10, 3);
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-16", 22, MAX_ALLOWED_STAY_LENGTH, 10, 3);
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-18", 3, 5, 10, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, MAX_ALLOWED_STAY_LENGTH, 10);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-16", 22, MAX_ALLOWED_STAY_LENGTH, 10);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-18", 3, 5, 10);

    }

    @Test
    public void testGenerateMinMaxLosPace() {
        createMockForMinlosPaceDecisionGeneration();
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 1, 2, 11, 2);
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-16", 1, 3, 11, 3);
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-18", 2, 4, 11, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, 3, 10);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-16", 22, 22, 10);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, 2, 11);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-16", 1, 3, 11);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-18", 2, 4, 11);
    }

    @Test
    public void testGenerateMinMaxLosDecisionsForStaleDecisionMinLOSDeletion() {
        createMockForStaleDecisionMinLOSDeletion();
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertStaleDecisionsMinLOSDeleted("2014-03-17");
        assertStaleDecisionsNotPopulatedInPaceMinLOS("2014-03-17");
    }

    @Test
    public void testGenerateMinMaxLosPaceSetToNone() throws SQLException {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(Constants.TARS_DESTINATION))).thenReturn(Constants.NONE);
        createMockForFplosDecisionGeneration();
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertTrue(1 == getDecisionMinLosRecords("2014-03-16", 10).size());

    }

    @Test
    public void testChangeColumnIsThreeForFirstDecision() {
        setDecision_Minlos_DecQualifiedFPLOSData("YYYYYYYYYYNNNNNNNNNNN", 1, 1, 1, true);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 1, 10, 10, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, 10, 10);
    }

    @Test
    public void testChangeColumnTransitionFromThreetoTwo() {
        setDecision_Minlos_DecQualifiedFPLOSData("YYYYYYYYYYYNNNNNNNNNN", 1, 10, 3, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 1, 11, 10, 2);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, 11, 10);
    }

    @Test
    public void testChangeColumnTransitionFromTwotoThree() {
        setDecision_Minlos_DecQualifiedFPLOSData("YYYYYYYYYYYYNNNNNNNNY", 11, 11, 2, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 1, 12, 10, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 1, 12, 10);
    }

    @Test
    public void testChangeColumnTransitionFromThreetoOne() {
        setDecision_Minlos_DecQualifiedFPLOSData("NNNNNNNNYYYYNNNNNNNNN", 1, 12, 3, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 9, 12, 10, 1);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 9, 12, 10);
    }

    @Test
    public void testChangeColumnTransitionFromOnetoTwo() {
        setDecision_Minlos_DecQualifiedFPLOSData("NNNNNNNNYYYYYNNNNNNNN", 9, 12, 1, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 9, 13, 10, 2);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 9, 13, 10);
    }

    @Test
    public void testChangeColumnTransitionFromTwotoOne() {
        setDecision_Minlos_DecQualifiedFPLOSData("NNNNNNNYYYYYYNNYYNNNN", 9, 13, 2, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 8, 13, 10, 1);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 8, 13, 10);
    }

    @Test
    public void testChangeColumnTransitionFromOnetoThree() {
        setDecision_Minlos_DecQualifiedFPLOSData("NNNNNNNNYYYYNNNNNNNNN", 8, 13, 1, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 9, 12, 10, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 9, 12, 10);
    }

    @Test
    public void testMinlosPaceNotUpdatedForNoChange() {
        createMockForFplosDecisionGeneration_EmptyPaceScenario();
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertTrue(0 == getDecisionMinLosPaceRecords("2014-03-15", 10).size());
    }

    @Test
    public void testChangeColumnFromNulltoThree() {
        setDecision_Minlos_DecQualifiedFPLOSData("NNNNNNNNYYYYNNNNNNNNN", 8, false);
        minMaxLosUsualRecommendationService.createMinMaxlosDecisions();
        assertGeneratedMinMaxLosDecisionsNonPace("2014-03-15", 9, 12, 10, 3);
        assertGeneratedMinMaxLosDecisionsPace("2014-03-15", 9, 12, 10);
    }

    @Test
    public void testMinlosCreationAndDeletion() {
        int initialCount = tenantCrudService().findByNativeQuery("select * from Decision_Minlos").size();
        int accomTypeId = tenantCrudService().findByNativeQuerySingleResult("select max(Accom_Type_ID) from accom_type", null);
        int qualifiedRateId = tenantCrudService().findByNativeQuerySingleResult("select max(Rate_Qualified_ID) from Rate_Qualified", null);

        MinlosDecisions minlosDecisions = minMaxLosUsualRecommendationService.createMinlosDecisions(1, 1, accomTypeId, qualifiedRateId, new Date(), 1, 3);
        assertNotNull(minlosDecisions);
        int newCount = tenantCrudService().findByNativeQuery("select * from Decision_Minlos").size();
        assertEquals(initialCount + 1, newCount);

        int deletedRecordCount = minMaxLosUsualRecommendationService.removeMinlosDecisions();
        assertEquals(initialCount + 1, deletedRecordCount);
    }

    private void setDecision_Minlos_DecQualifiedFPLOSData(String strQualFPLOS, Integer minLOS, Boolean skipMinLOSUpdate) {
        Decision dec = new Decision();
        dec.setId(10);
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),	'2014-03-15', '" + strQualFPLOS + "',CURRENT_TIMESTAMP) ");
        if (!skipMinLOSUpdate) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS (Decision_Qualified_FPLOS_ID ,Decision_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,MINLOS,CreateDate_DTTM) " +
                    "values ( (select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, " +
                    "(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', '" + minLOS + "', CURRENT_TIMESTAMP)");
        }
        when(decisionService.createMinMaxLosDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
    }

    private void assertGeneratedMinMaxLosDecisionsNonPace(String arrivalDate, int minLos, int maxLos, int decisionId, int changeFlag) {
        List<Object[]> results = getDecisionMinLosRecords(arrivalDate, decisionId);

        assertFalse(results.isEmpty());
        for (Object[] row : results) {
            assertTrue((Integer) row[6] == minLos);
            assertTrue((Integer) row[8] == maxLos);
            assertTrue((Integer) row[9] == changeFlag);
        }
    }

    private List<Object[]> getDecisionMinLosPaceRecords(String arrivalDate, int decisionId) {
        return tenantCrudService().findByNativeQuery(new StringBuilder().append("select * from PACE_MINLOS where Arrival_DT= :date and ")
                .append("decision_Id =:decisionID").toString(), QueryParameter.with("date", arrivalDate).and("decisionID", decisionId).parameters());
    }

    private List<Object[]> getDecisionMinLosRecords(String arrivalDate, int decisionId) {
        return tenantCrudService().findByNativeQuery(new StringBuilder().append("select * from Decision_MINLOS where Arrival_DT= :date and ")
                .append("decision_Id =:decisionID").toString(), QueryParameter.with("date", arrivalDate).and("decisionID", decisionId).parameters());
    }

    private void assertGeneratedMinMaxLosDecisionsPace(String arrivalDate, int minLos, int maxLos, int decisionId) {
        List<Object[]> results = tenantCrudService().findByNativeQuery("select * from PACE_MINLOS where Arrival_DT= :date  and decision_Id =:decisionID",
                QueryParameter.with("date", arrivalDate).and("decisionID", decisionId).parameters());
        assertFalse(results.isEmpty());
        for (Object[] row : results) {
            assertTrue((Integer) row[6] == minLos);
            assertTrue((Integer) row[8] == maxLos);
        }
    }

    private void assertStaleDecisionsNotPopulatedInPaceMinLOS(String arrivalDate) {
        assertEquals(2, tenantCrudService().findByNativeQuery("Select * from PACE_MINLOS").size());
        assertEquals(0, tenantCrudService().findByNativeQuery("select * from PACE_MINLOS where Arrival_DT= :date",
                QueryParameter.with("date", arrivalDate).parameters()).size());
    }

    private void assertStaleDecisionsMinLOSDeleted(String arrivalDate) {
        assertEquals(2, tenantCrudService().findByNativeQuery("Select * from Decision_MINLOS").size());
        assertEquals(0, tenantCrudService().findByNativeQuery("select * from Decision_MINLOS where Arrival_DT= :date",
                QueryParameter.with("date", arrivalDate).parameters()).size());
    }

    private void createMockForFplosDecisionGeneration() {
        Decision decision = new Decision();
        decision.setId(10);
        //populate Decision_Qualified_FPLOS
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values(1,000006,(select max(Accom_Type_ID) from accom_type)," +
                "(select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-15','YYYYYYYYYYYYYYYYYYYYY',CURRENT_TIMESTAMP)");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values(1,000006,(select max(Accom_Type_ID) from accom_type)," +
                "(select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-16','NNNNNNNNNNNNNNNNNNNNN',CURRENT_TIMESTAMP)");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values(1,000006,(select max(Accom_Type_ID) from accom_type)," +
                "(select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-18','NNYYYNNNNNYNNNNNNNNNN',CURRENT_TIMESTAMP)");

        //populate Decision_Minlos
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS (Decision_Qualified_FPLOS_ID ,Decision_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,MINLOS,CreateDate_DTTM) " +
                "values ( (select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', 11, CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS (Decision_Qualified_FPLOS_ID ,Decision_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,MINLOS,CreateDate_DTTM) " +
                "values ( (select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-16', 2, CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS (Decision_Qualified_FPLOS_ID ,Decision_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,MINLOS,CreateDate_DTTM) " +
                "values ( (select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS)+1, 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-17', 3, CURRENT_TIMESTAMP) ");

        when(decisionService.createMinMaxLosDecision()).thenReturn(decision);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(date.plusDays(20).toDate());
    }

    private void createMockForMinlosPaceDecisionGeneration() {
        Decision dec = new Decision();
        dec.setId(11);

        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1,000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-15','YYNNNYYYYYYYYYYYYYYYY',CURRENT_TIMESTAMP)");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1,000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-16','YYYNNNNNNNNNNNNNNNNNN',CURRENT_TIMESTAMP)");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1,000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),'2014-03-18','NYYYNNNNYYNNNNNNNNNNN',CURRENT_TIMESTAMP)");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', 1, CURRENT_TIMESTAMP, 0, 1) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ((select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-16', 22, CURRENT_TIMESTAMP, 0, 3) ");

        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_MINLOS values ((select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 10, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', 1, CURRENT_TIMESTAMP, 3) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_MINLOS values ((select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 10, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-16', 22, CURRENT_TIMESTAMP, 22)");

        when(decisionService.createMinMaxLosDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
    }

    private void createMockForStaleDecisionMinLOSDeletion() {
        Decision dec = new Decision();
        dec.setId(11);
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),	'2014-03-15',	'YYNNNYYYYYYYYYYYYYYYY',CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),	'2014-03-16',	'YYYNNNNNNNNNNNNNNNNNN',CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ( (select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', 1, CURRENT_TIMESTAMP, 0, 1) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ( (select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-16', 22, CURRENT_TIMESTAMP, 0, 3) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ( (select max(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS)+1, 11, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-17', 3, CURRENT_TIMESTAMP, 3, 3) ");

        when(decisionService.createMinMaxLosDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
    }

    private void setDecision_Minlos_DecQualifiedFPLOSData(String strQualFPLOS, Integer minLOS, Integer maxLOS, Integer Change, Boolean skipMinLOSUpdate) {
        Decision dec = new Decision();
        dec.setId(10);
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (1, 000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),	'2014-03-15', '" + strQualFPLOS + "',CURRENT_TIMESTAMP) ");
        if (!skipMinLOSUpdate) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ( (select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', '" + minLOS + "', CURRENT_TIMESTAMP, '" + maxLOS + "', '" + Change + "') ");
        }
        when(decisionService.createMinMaxLosDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
    }

    private void createMockForFplosDecisionGeneration_EmptyPaceScenario() {
        Decision dec = new Decision();
        dec.setId(10);
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_Qualified_FPLOS values (	1, 000006 ,(select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified),	'2014-03-15',	'YYYYYYYYYYYYYYYYYYYYY',CURRENT_TIMESTAMP) ");
        tenantCrudService().executeUpdateByNativeQuery("insert into Decision_MINLOS values ( (select min(Decision_Qualified_FPLOS_ID) from Decision_Qualified_FPLOS), 1, (select max(Accom_Type_ID) from accom_type), (select max(Rate_Qualified_ID) from Rate_Qualified), '2014-03-15', 1, CURRENT_TIMESTAMP, " + MAX_ALLOWED_STAY_LENGTH + ", NULL) ");
        when(decisionService.createMinMaxLosDecision()).thenReturn(dec);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(date.toDate());
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn((date.plusDays(20)).toDate());
    }
}