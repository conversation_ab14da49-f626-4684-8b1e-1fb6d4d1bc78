package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.*;

class HotelMarketAccomActivityServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    private HotelMarketAccomActivityService hotelMarketAccomActivityService;

    @BeforeEach
    void setup() {
        hotelMarketAccomActivityService.tenantCrudService = tenantCrudService();
    }

    @Test
    void isPresent() {

        assertFalse(hotelMarketAccomActivityService.isHotelMarketActivityPresent());
        final LocalDate occupancyDt = LocalDate.now();
        final HotelMktSegAccomActivity hotelMktSegAccomActivity = buildHotelMktSegAccomActivity(occupancyDt);
        tenantCrudService().save(hotelMktSegAccomActivity);
        assertTrue(hotelMarketAccomActivityService.isHotelMarketActivityPresent());

    }

    @Test
    void nonLatestValuesSetToZero() {
        assertEquals(0, hotelMarketAccomActivityService.updateNonLatestValuesToZero(2));
        final LocalDate occupancyDt = LocalDate.now();
        final HotelMktSegAccomActivity inWindow = buildHotelMktSegAccomActivity(occupancyDt);
        final LocalDate occupancyDtMinus45 = LocalDate.now().minusDays(45);
        final HotelMktSegAccomActivity earlierThanWindow = buildHotelMktSegAccomActivity(occupancyDtMinus45);
        tenantCrudService().save(Arrays.asList(inWindow, earlierThanWindow));
        assertEquals(1, hotelMarketAccomActivityService.updateNonLatestValuesToZero(2));
    }

    private HotelMktSegAccomActivity buildHotelMktSegAccomActivity(final LocalDate occupancyDt) {
        HotelMktSegAccomActivity mktSegAccomActivity = new HotelMktSegAccomActivity();
        mktSegAccomActivity.setId(new Random().nextInt());
        mktSegAccomActivity.setPropertyId(5);
        mktSegAccomActivity.setAccomTypeCode("DELUXE");
        mktSegAccomActivity.setMktSegCode("BART");
        mktSegAccomActivity.setFileMetadataId(1);
        mktSegAccomActivity.setSnapShotDate(LocalDateTime.now().toDate());

        mktSegAccomActivity.setOccupancyDate(occupancyDt.toDate());
        mktSegAccomActivity.setArrivals(BigDecimal.ONE);
        mktSegAccomActivity.setCancellations(new BigDecimal(2));
        mktSegAccomActivity.setDepartures(new BigDecimal(3));
        mktSegAccomActivity.setFoodRevenue(new BigDecimal(4));
        mktSegAccomActivity.setNoShows(new BigDecimal(5));
        mktSegAccomActivity.setRoomRevenue(new BigDecimal(6));
        mktSegAccomActivity.setRoomsSold(new BigDecimal(9));
        mktSegAccomActivity.setTotalRevenue(new BigDecimal(11));
        return mktSegAccomActivity;
    }


}