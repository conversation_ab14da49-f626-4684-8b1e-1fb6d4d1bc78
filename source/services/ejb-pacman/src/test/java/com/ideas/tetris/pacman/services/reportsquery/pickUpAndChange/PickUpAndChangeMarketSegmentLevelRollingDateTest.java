package com.ideas.tetris.pacman.services.reportsquery.pickUpAndChange;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueFileMetadataCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.reports.ReportConstants;
import com.ideas.tetris.pacman.services.reports.pickUpAndChange.PickupAndChangeTestUtility;
import com.ideas.tetris.pacman.services.reports.pickUpAndChange.datasetup.ChangeReportMarketSegmentLevelDataSetup;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Created by idnsru on 12/16/2016.
 */
public class PickUpAndChangeMarketSegmentLevelRollingDateTest extends AbstractG3JupiterTest {

    private ChangeReportMarketSegmentLevelDataSetup changeReportDataSetup;
    private static final int TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS = 4;
    private final int propertyID = 5;
    private final int recordTypeId = 13;
    private final int processStatusId = 13;
    private static final int IDX_CHANGE_REPORT_FOR_ADR_CURRENT = 10;
    private static final int IDX_CHANGE_REPORT_FOR_ADR_DIFF = 11;
    private static final int IDX_CHANGE_REPORT_FOR_ROOM_SOLDS_CURRENT = 2;
    private static final int IDX_CHANGE_REPORT_FOR_ROOM_SOLDS_DIFF = 3;
    private static final int IDX_CHANGE_REPORT_FOR_ROOM_REVENUE_CURRENT = 6;
    private static final int IDX_CHANGE_REPORT_FOR_ROOM_REVNEUE_DIFF = 7;

    private static final int IDX_CHANGE_REPORT_FOR_FORECASTED_ADR_CURRENT = 12;
    private static final int IDX_CHANGE_REPORT_FOR_FORECASTED_ADR_DIFF = 13;
    private static final int IDX_FOR_FORECASTED_ROOM_SOLDS = 4;
    private static final int IDX_FOR_DIFF_FORECASTED_ROOM_SOLDS = 5;
    private static final int IDX_FOR_FORECASTED_ROOM_REVENUE = 8;
    private static final int IDX_FOR_DIFF_FORECASTED_ROOM_REVENUE = 9;
    private Date systemDate = null;
    private Object[] row = null;

    private static final int ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_1 = 10;
    private static final int ROOM_SOLDS_DIFF_SIDE_BY_SIDE_1 = 11;
    private static final int ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_2 = 12;
    private static final int ROOM_SOLDS_DIFF_SIDE_BY_SIDE_2 = 13;

    private static final int ADR_CURRENT_CURRENT_SIDE_BY_SIDE_1 = 74;
    private static final int ADR_CURRENT_DIFF_SIDE_BY_SIDE_1 = 75;
    private static final int ADR_CURRENT_CURRENT_SIDE_BY_SIDE_2 = 76;
    private static final int ADR_CURRENT_DIFF_SIDE_BY_SIDE_2 = 77;

    private static final int ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_1 = 42;
    private static final int ROOM_REVENUE_DIFF_SIDE_BY_SIDE_1 = 43;
    private static final int ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_2 = 44;
    private static final int ROOM_REVENUE_DIFF_SIDE_BY_SIDE_2 = 45;

    private static final int FORECASTED_ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_1 = 26;
    private static final int FORECASTED_ROOM_SOLDS_DIFF_SIDE_BY_SIDE_1 = 27;
    private static final int FORECASTED_ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_2 = 28;
    private static final int FORECASTED_ROOM_SOLDS_DIFF_SIDE_BY_SIDE_2 = 29;

    private static final int FORECASTED_ADR_CURRENT_CURRENT_SIDE_BY_SIDE_1 = 90;
    private static final int FORECASTED_ADR_CURRENT_DIFF_SIDE_BY_SIDE_1 = 91;
    private static final int FORECASTED_ADR_CURRENT_CURRENT_SIDE_BY_SIDE_2 = 92;
    private static final int FORECASTED_ADR_CURRENT_DIFF_SIDE_BY_SIDE_2 = 93;

    private static final int FORECASTED_ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_1 = 58;
    private static final int FORECASTED_ROOM_REVENUE_DIFF_SIDE_BY_SIDE_1 = 59;
    private static final int FORECASTED_ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_2 = 60;
    private static final int FORECASTED_ROOM_REVENUE_DIFF_SIDE_BY_SIDE_2 = 61;

    public PickUpAndChangeMarketSegmentLevelRollingDateTest() {
        this.changeReportDataSetup = new ChangeReportMarketSegmentLevelDataSetup(tenantCrudService());

    }

    private void populateMarketAccomActivityData(LocalDate occupancyDate, Integer marketSegId) {
        StringBuilder query = new StringBuilder();
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + occupancyDate + "','" + new LocalDate() + "'," + marketSegId + "," +
                "8,11,7,2,0,0,653.145,21.9870,633.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private MktSeg populateMarketSegment() {
        MktSeg mktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        return mktSeg;
    }

    @Test
    public void shouldValidatePickUpReportForRollingDateSystemDateMinusFiveToSystemDatePlusFive() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);
        Date systemDatePlusSix = DateUtil.addDaysToDate(systemDate, 6);
        Date systemDateMinusSix = DateUtil.addDaysToDate(systemDate, -6);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSix), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusSix), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-5','TODAY+5','TODAY-5','TODAY+5', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDateMinusOne, (Date) row[0]);
    }


    @Test
    public void shouldValidateChangeReportForRollingDateSystemDateMinusFiveToSystemDatePlusFive() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);
        Date systemDatePlusSix = DateUtil.addDaysToDate(systemDate, 6);
        Date systemDateMinusSix = DateUtil.addDaysToDate(systemDate, -6);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSix), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusSix), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-5','TODAY-5','TODAY+5', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDateMinusOne, (Date) row[0]);
    }


    @Test
    public void shouldValidatePickUpReportForRollingDateSystemDateToSystemDatePlusTen() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusOne = DateUtil.addDaysToDate(systemDate, 1);
        Date systemDatePlusTwelve = DateUtil.addDaysToDate(systemDate, 12);
        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDatePlusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusTwelve), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY','TODAY+10','TODAY','TODAY+10', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDatePlusOne, (Date) row[0]);
    }


    @Test
    public void shouldValidateChangeReportForRollingDateSystemDateToSystemDatePlusTen() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusOne = DateUtil.addDaysToDate(systemDate, 1);
        Date systemDatePlusTwelve = DateUtil.addDaysToDate(systemDate, 12);
        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDatePlusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusTwelve), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY','TODAY','TODAY+10', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDatePlusOne, (Date) row[0]);
    }


    @Test
    public void shouldValidatePickUpReportForRollingDateSystemDateToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY','TODAY','TODAY','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }

    @Test
    public void shouldValidateChangeReportForRollingDateSystemDateToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDateMinusOne = DateUtil.addDaysToDate(systemDate, -1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY','TODAY','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }

    @Test
    public void shouldValidatePickUpReportForRollingDateSystemDatePlusFiveToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusFive = DateUtil.addDaysToDate(systemDate, 5);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusFive), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+5','TODAY+5','TODAY+5','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDatePlusFive, (Date) row[0]);
    }

    @Test
    public void shouldValidateChangeReportForRollingDateSystemDatePlusFiveToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusFive = DateUtil.addDaysToDate(systemDate, 5);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusFive), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY+5','TODAY+5','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDatePlusFive, (Date) row[0]);
    }


    @Test
    public void shouldValidatePickUpReportForRollingDateStartOfMonthToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusSixty = DateUtil.addDaysToDate(systemDate, 60);
        Date systemDateMinusSixty = DateUtil.addDaysToDate(systemDate, -60);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSixty), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusSixty), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY','TODAY','START_OF_MONTH','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }

    @Test
    public void shouldValidateChangeReportForRollingDateStartOfMonthToEndOfMonth() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusSixty = DateUtil.addDaysToDate(systemDate, 60);
        Date systemDateMinusSixty = DateUtil.addDaysToDate(systemDate, -60);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSixty), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDateMinusSixty), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY','START_OF_MONTH','END_OF_MONTH', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }

    @Test
    public void shouldValidatePickUpReportForRollingDateStartOfMonthMinusOneToSystemDate() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusSixty = DateUtil.addDaysToDate(systemDate, 60);
        Date systemDatePlusOne = DateUtil.addDaysToDate(systemDate, 1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSixty), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY','TODAY','START_OF_MONTH-1','TODAY', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }

    @Test
    public void shouldValidateChangeReportForRollingDateStartOfMonthMinusOneToSystemDate() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date systemDatePlusSixty = DateUtil.addDaysToDate(systemDate, 60);
        Date systemDatePlusOne = DateUtil.addDaysToDate(systemDate, 1);

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusSixty), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(systemDatePlusOne), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY','START_OF_MONTH-1','TODAY', 1");
        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(systemDate, (Date) row[0]);
    }


    @Test
    public void shouldValidatePickUpReportForRollingDateStartOfMonthPlusOneToEndOfMonthPlusTwo() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date startOfMonthPlusOne = DateUtil.addMonthsToDate(systemDate, 1);
        Date startOfMonthPlusFour = DateUtil.addMonthsToDate(systemDate, 4);

        long daysBetweenSystemDateAndStartOfMonthPlusOne = DateUtil.daysBetween(systemDate, startOfMonthPlusOne);
        String activityStartAndEndDate = "TODAY+" + daysBetweenSystemDateAndStartOfMonthPlusOne;

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(startOfMonthPlusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(startOfMonthPlusFour), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_pickup_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.plusDays(1).toString() + "','" + systemLocalDate.plusDays(2).toString() + "','" + systemLocalDate.toString() + "','" + systemLocalDate.plusDays(3).toString() + "'," + isRolling + ",'" + activityStartAndEndDate + "','" + activityStartAndEndDate + "','START_OF_MONTH+1','END_OF_MONTH+2', 1");

        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(startOfMonthPlusOne, (Date) row[0]);
    }

    @Test
    public void shouldValidateChangeReportForRollingDateStartOfMonthPlusOneToEndOfMonthPlusTwo() {
        Date systemDate = PickupAndChangeTestUtility.getFutureDate(50, 15);
        LocalDate systemLocalDate = new LocalDate(systemDate);
        UniqueFileMetadataCreator.createFileMetadata(systemDate);

        Date startOfMonthPlusOne = DateUtil.addMonthsToDate(systemDate, 1);
        Date startOfMonthPlusFour = DateUtil.addMonthsToDate(systemDate, 4);

        long daysBetweenSystemDateAndStartOfMonthPlusOne = DateUtil.daysBetween(systemDate, startOfMonthPlusOne);
        String activityStartAndEndDate = "TODAY+" + daysBetweenSystemDateAndStartOfMonthPlusOne;

        MktSeg mktSeg = populateMarketSegment();
        int marketSegId = mktSeg.getId();
        populateMarketAccomActivityData(new LocalDate(systemDate), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(startOfMonthPlusOne), mktSeg.getId());
        populateMarketAccomActivityData(new LocalDate(startOfMonthPlusFour), mktSeg.getId());

        int isRolling = 1;

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",'" + marketSegId + "'," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "'," + isRolling + ",'" + activityStartAndEndDate + "','START_OF_MONTH+1','END_OF_MONTH+2', 1");

        assertTrue(reportData.size() == 1);
        Object[] row = reportData.get(0);
        assertDate(startOfMonthPlusOne, (Date) row[0]);
    }

    @Test
    public void ValidateChangeReportForRollingDateLastUpdatedDate() {
        setupTestData()
                .andFetchChangeReportWithRollingDate(ReportConstants.LAST_UPDATED)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksData()
                .andOccupancyDifferenceIs(new BigDecimal(3))
                .andRoomRevenueDifferenceIs(new BigDecimal(350))
                .andADRDifferenceIs(new BigDecimal(7.14))
                .verifyLatestOccupancyForecastDataIs()
                .andForecastRoomSoldDifferenceIs(new BigDecimal(5))
                .andForecastRevenueDifferenceIs(new BigDecimal(100))
                .andForecastADRDifferenceIS(new BigDecimal(0.4));
    }

    @Test
    public void ValidateChangeReportForRollingDateLastOptimization() {
        setupTestData()
                .andFetchChangeReportWithRollingDate(ReportConstants.LAST_OPTIMIZATION)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksData()
                .andOccupancyDifferenceIs(new BigDecimal(2))
                .andRoomRevenueDifferenceIs(new BigDecimal(210))
                .andADRDifferenceIs(new BigDecimal(1.25))
                .verifyLatestOccupancyForecastDataIs()
                .andForecastRoomSoldDifferenceIs(new BigDecimal(3))
                .andForecastRevenueDifferenceIs(new BigDecimal(50))
                .andForecastADRDifferenceIS(new BigDecimal(-0.15));
    }

    @Test
    public void ValidateChangeReportSideBySideViewForRollingDateLastUpdatedDate() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate(ReportConstants.LAST_UPDATED)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView()
                .andOccupancyDifferenceForSideBySideViewIs(3)
                .andRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(350))
                .andADRDifferenceForSideBySideViewIs(new BigDecimal(7.14))
                .verifyLatestOccupancyForecastDataForSideBySideIs()
                .andForecastOccupancyDifferenceForSideBySideViewIs(new BigDecimal(5))
                .andForecastRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(100))
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(0.4), new BigDecimal(0));
    }

    @Test
    void ValidateChangeReportSideBySideViewForRollingDateLastUpdatedDate_Include_DisconMS() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate_DisconMS(ReportConstants.LAST_UPDATED, 1)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView()
                .andOccupancyDifferenceForSideBySideViewIs(3)
                .andRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(350))
                .andADRDifferenceForSideBySideViewIs(new BigDecimal(7.14))
                .verifyLatestOccupancyForecastDataForSideBySideIs()
                .andForecastOccupancyDifferenceForSideBySideViewIs(new BigDecimal(5))
                .andForecastRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(100))
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(0.4), new BigDecimal(0));
    }

    @Test
    void ValidateChangeReportSideBySideViewForRollingDateLastUpdatedDate_Not_Include_DisconMS() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate_DisconMS(ReportConstants.LAST_UPDATED, 0)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView_DisconMS()
                .andOccupancyDifferenceForSideBySideViewIs_DisconMS(3)
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(0.4), new BigDecimal(0));
    }


    @Test
    public void ValidateChangeReportSideBySideViewForRollingDateLastOptimization() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate(ReportConstants.LAST_OPTIMIZATION)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView()
                .andOccupancyDifferenceForSideBySideViewIs(2)
                .andRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(210))
                .andADRDifferenceForSideBySideViewIs(new BigDecimal(1.25))
                .verifyLatestOccupancyForecastDataForSideBySideIs()
                .andForecastOccupancyDifferenceForSideBySideViewIs(new BigDecimal(3))
                .andForecastRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(50))
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(-0.15), new BigDecimal(-0.37));
    }

    @Test
    void ValidateChangeReportSideBySideViewForRollingDateLastOptimization_Include_DisconMS() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate_DisconMS(ReportConstants.LAST_OPTIMIZATION, 1)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView()
                .andOccupancyDifferenceForSideBySideViewIs(2)
                .andRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(210))
                .andADRDifferenceForSideBySideViewIs(new BigDecimal(1.25))
                .verifyLatestOccupancyForecastDataForSideBySideIs()
                .andForecastOccupancyDifferenceForSideBySideViewIs(new BigDecimal(3))
                .andForecastRoomRevenueDifferenceForSideBySideViewIs(new BigDecimal(50))
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(-0.15), new BigDecimal(-0.37));
    }

    @Test
    public void ValidateChangeReportSideBySideViewForRollingDateLastOptimization_Not_Include_DisconMS() {
        setupTestData()
                .andFetchChangeReportSideBySideViewWithRollingDate_DisconMS(ReportConstants.LAST_OPTIMIZATION, 0)
                .verifyOccupancyDateIs(DateUtil.addDaysToDate(systemDate, 1))
                .andLatestOnBooksDataForSideBySideView_DisconMS()
                .andOccupancyDifferenceForSideBySideViewIs_DisconMS(2)
                .andForecastADRDifferenceForSideBySideViewIs(new BigDecimal(-0.15), new BigDecimal(0));
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest verifyLatestOccupancyForecastDataIs() {
        BigDecimal currentForecastRoomRevenue = new BigDecimal(540);
        BigDecimal currentForecastRoomSolds = new BigDecimal(30);
        BigDecimal currentForecastADR = new BigDecimal(18);

        assertBigDecimal((BigDecimal) row[IDX_FOR_FORECASTED_ROOM_SOLDS], currentForecastRoomSolds, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[IDX_FOR_FORECASTED_ROOM_REVENUE], currentForecastRoomRevenue, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_FORECASTED_ADR_CURRENT], currentForecastADR, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastADRDifferenceIS(BigDecimal expectedADRDifference) {
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_FORECASTED_ADR_DIFF], expectedADRDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastRevenueDifferenceIs(BigDecimal expectedRevenueDifference) {
        assertBigDecimal((BigDecimal) row[IDX_FOR_DIFF_FORECASTED_ROOM_REVENUE], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastRoomSoldDifferenceIs(BigDecimal expectedRoomSoldDifference) {
        assertBigDecimal((BigDecimal) row[IDX_FOR_DIFF_FORECASTED_ROOM_SOLDS], expectedRoomSoldDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andFetchChangeReportSideBySideViewWithRollingDate(String rollingBusinessDate) {
        LocalDate systemLocalDate = new LocalDate(systemDate);
        Map<String, Object> queryParameters = changeReportDataSetup.createQueryParameters(rollingBusinessDate);
        String multipleMarketSegID = queryParameters.get("marketSegId") + "," + queryParameters.get("marketSegIdForSideBySide");
        queryParameters.remove("marketSegId");
        queryParameters.remove("marketSegIdForSideBySide");
        queryParameters.put("multipleMarketSegIDs", multipleMarketSegID);

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms " +
                "(" + propertyID + ",:multipleMarketSegIDs,'" + systemLocalDate.minusDays(11).toString() + "'," +
                "'" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "', :isRolling,:rollingBusinessDate,:rollingStartDate,:rollingEndDate, 1)", queryParameters);
        row = reportData.get(0);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andFetchChangeReportSideBySideViewWithRollingDate_DisconMS(String rollingBusinessDate, int includeDisconMS) {
        LocalDate systemLocalDate = new LocalDate(systemDate);

        Map<String, Object> queryParameters = changeReportDataSetup.createQueryParameters(rollingBusinessDate);
        setMktSegStatusDiscontinued(Collections.singletonList((Integer) queryParameters.get("marketSegIdForSideBySide")));

        String multipleMarketSegID = queryParameters.get("marketSegId") + "," + queryParameters.get("marketSegIdForSideBySide");
        queryParameters.remove("marketSegId");
        queryParameters.remove("marketSegIdForSideBySide");
        queryParameters.put("multipleMarketSegIDs", multipleMarketSegID);
        queryParameters.put("includeDisconMS", includeDisconMS);

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_change_report_comparative_view_ms " +
                "(" + propertyID + ",:multipleMarketSegIDs,'" + systemLocalDate.minusDays(11).toString() + "'," +
                "'" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "', :isRolling,:rollingBusinessDate,:rollingStartDate,:rollingEndDate, :includeDisconMS)", queryParameters);
        row = reportData.get(0);
        return this;
    }

    private void setMktSegStatusDiscontinued(List<Integer> mktSegIds) {
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (:mktSegIds)"
                , QueryParameter.with("mktSegIds", mktSegIds).parameters());
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest verifyOccupancyDateIs(Date expectedOccupancyDate) {
        assertDate(expectedOccupancyDate, (Date) row[0]);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andFetchChangeReportWithRollingDate(String rollingBusinessDate) {
        row = fetchReportData(rollingBusinessDate);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andADRDifferenceIs(BigDecimal expectedADRDifference) {
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ADR_DIFF], expectedADRDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andRoomRevenueDifferenceIs(BigDecimal expectedRevenueDifference) {
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ROOM_REVNEUE_DIFF], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andOccupancyDifferenceIs(BigDecimal expectedRoomSoldDifference) {
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ROOM_SOLDS_DIFF], expectedRoomSoldDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private Object[] fetchReportData(String rollingBusinessDate) {
        LocalDate systemLocalDate = new LocalDate(systemDate);
        Map<String, Object> queryParameters = changeReportDataSetup.createQueryParameters(rollingBusinessDate);
        queryParameters.remove("marketSegIdForSideBySide");
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_hotel_differential_control_report_ms " + propertyID + ",:marketSegId," + recordTypeId + "," + processStatusId + ",'" + systemLocalDate.minusDays(11).toString() + "','" + systemLocalDate.plusDays(6).toString() + "','" + systemLocalDate.plusDays(6).toString() + "', :isRolling,:rollingBusinessDate,:rollingStartDate,:rollingEndDate, 1", queryParameters);
        return reportData.get(0);
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andLatestOnBooksData() {
        BigDecimal currentRoomSolds = new BigDecimal(10);
        BigDecimal currentRoomRevenue = new BigDecimal(1000);
        BigDecimal currentADR = new BigDecimal(100);

        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ROOM_SOLDS_CURRENT], currentRoomSolds, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ROOM_REVENUE_CURRENT], currentRoomRevenue, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[IDX_CHANGE_REPORT_FOR_ADR_CURRENT], currentADR, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);

        return this;
    }

    private void assertBigDecimal(BigDecimal actual, BigDecimal expected, int numberOfDecimals) {
        assertThat(actual.setScale(numberOfDecimals, BigDecimal.ROUND_HALF_UP), is(expected.setScale(numberOfDecimals, BigDecimal.ROUND_HALF_UP)));
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest setupTestData() {
        systemDate = changeReportDataSetup.createFileMetadata();
        changeReportDataSetup.setupTestData(systemDate);
        return this;
    }

    private void assertDate(Date occupanyDate, Date resultDate) {
        Calendar occupanyDateCal = Calendar.getInstance();
        occupanyDateCal.setTime(occupanyDate);

        Calendar resultDateCal = Calendar.getInstance();
        resultDateCal.setTime(resultDate);

        assertTrue(occupanyDateCal.get(Calendar.DAY_OF_MONTH) == resultDateCal.get(Calendar.DAY_OF_MONTH));
        assertTrue(occupanyDateCal.get(Calendar.MONTH) == resultDateCal.get(Calendar.MONTH));
        assertTrue(occupanyDateCal.get(Calendar.YEAR) == resultDateCal.get(Calendar.YEAR));
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andADRDifferenceForSideBySideViewIs(BigDecimal expectedADRDifference) {
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_DIFF_SIDE_BY_SIDE_1], expectedADRDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_DIFF_SIDE_BY_SIDE_2], expectedADRDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andRoomRevenueDifferenceForSideBySideViewIs(BigDecimal expectedRevenueDifference) {
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_DIFF_SIDE_BY_SIDE_1], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_DIFF_SIDE_BY_SIDE_2], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andOccupancyDifferenceForSideBySideViewIs(Integer expectedRoomSoldDifference) {
        assertEquals(row[ROOM_SOLDS_DIFF_SIDE_BY_SIDE_1], expectedRoomSoldDifference);
        assertEquals(row[ROOM_SOLDS_DIFF_SIDE_BY_SIDE_2], expectedRoomSoldDifference);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andOccupancyDifferenceForSideBySideViewIs_DisconMS(Integer expectedRoomSoldDifference) {
        assertEquals(row[ROOM_SOLDS_DIFF_SIDE_BY_SIDE_1], expectedRoomSoldDifference);
        assertEquals(row[ROOM_SOLDS_DIFF_SIDE_BY_SIDE_2], 0);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andLatestOnBooksDataForSideBySideView() {
        Integer currentRoomSolds = 10;
        BigDecimal currentRoomRevenue = new BigDecimal(1000);
        BigDecimal currentADR = new BigDecimal(100);

        assertEquals(row[ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_1], currentRoomSolds);
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_1], currentRoomRevenue, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_CURRENT_SIDE_BY_SIDE_1], currentADR, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertEquals(row[ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_2], currentRoomSolds);
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_2], currentRoomRevenue, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_CURRENT_SIDE_BY_SIDE_2], currentADR, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);

        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andLatestOnBooksDataForSideBySideView_DisconMS() {
        Integer currentRoomSolds = 10;
        BigDecimal currentRoomRevenue = new BigDecimal(1000);
        BigDecimal currentADR = new BigDecimal(100);

        assertEquals(row[ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_1], currentRoomSolds);
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_1], currentRoomRevenue, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_CURRENT_SIDE_BY_SIDE_1], currentADR, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertEquals(row[ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_2], 0);
        assertBigDecimal((BigDecimal) row[ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_2], BigDecimal.ZERO, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[ADR_CURRENT_CURRENT_SIDE_BY_SIDE_2], BigDecimal.ZERO, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);

        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest verifyLatestOccupancyForecastDataForSideBySideIs() {
        BigDecimal currentRoomSolds = new BigDecimal(30.00);
        BigDecimal currentRoomRevenue1 = new BigDecimal(540);
        BigDecimal currentADR1 = new BigDecimal(18);
        BigDecimal currentRoomRevenue2 = new BigDecimal(600);
        BigDecimal currentADR2 = new BigDecimal(20);

        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_1], currentRoomSolds, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_1], currentRoomRevenue1, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ADR_CURRENT_CURRENT_SIDE_BY_SIDE_1], currentADR1, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_SOLDS_CURRENT_SIDE_BY_SIDE_2], currentRoomSolds, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_REVENUE_CURRENT_SIDE_BY_SIDE_2], currentRoomRevenue2, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ADR_CURRENT_CURRENT_SIDE_BY_SIDE_2], currentADR2, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastADRDifferenceForSideBySideViewIs(BigDecimal expectedADRDifference1, BigDecimal expectedADRDifference2) {
        assertBigDecimal((BigDecimal) row[FORECASTED_ADR_CURRENT_DIFF_SIDE_BY_SIDE_1], expectedADRDifference1, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ADR_CURRENT_DIFF_SIDE_BY_SIDE_2], expectedADRDifference2, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastRoomRevenueDifferenceForSideBySideViewIs(BigDecimal expectedRevenueDifference) {
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_REVENUE_DIFF_SIDE_BY_SIDE_1], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_REVENUE_DIFF_SIDE_BY_SIDE_2], expectedRevenueDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }

    private PickUpAndChangeMarketSegmentLevelRollingDateTest andForecastOccupancyDifferenceForSideBySideViewIs(BigDecimal expectedRoomSoldDifference) {
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_SOLDS_DIFF_SIDE_BY_SIDE_1], expectedRoomSoldDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        assertBigDecimal((BigDecimal) row[FORECASTED_ROOM_SOLDS_DIFF_SIDE_BY_SIDE_2], expectedRoomSoldDifference, TESTING_BIGDECIMALS_TILL_NUMBER_OF_DECIMALS);
        return this;
    }
}
