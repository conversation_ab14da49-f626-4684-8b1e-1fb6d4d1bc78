package com.ideas.tetris.pacman.services.reportsquery.bookingpace;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Created by idnrar on 07-10-2014.
 */
public class BookingPaceReportMarketSegmentLevelDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private String dow;
    private String dow1;
    private String dow2;
    private int propertyID = 6;
    private int mktSeg1 = 7;
    private int mktSeg2 = 8;
    private int mktSeg3 = 9;
    private int mktSeg4 = 10;
    private int paceDays = 30;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        createTestData();
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();
        retrieveDayOfWeekForDateEqualToSystemDateMinusEight();
        retrieveDayOfWeekForDateEqualToSystemDateMinusEleven();

        int firstDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven();
        int secondDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight();

        populateForecastPaceForMarketSegmentForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForMarketSegmentForSecondPacePoint(insertQuery, secondDecisionId);
        populateActivityPaceForMarketSegment(insertQuery);
        UpdateOccupancyForecastForGivenOccupancyDate(insertQuery);
        updateMktActivityData(insertQuery);
        updateMarketSegmentToExcludeCompRooms(insertQuery);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void updateMktActivityData(StringBuilder insertQuery) {
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 11,[Arrivals] = 7,[Departures] = 2,[Cancellations] = 1, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 653.14500,[Food_Revenue] = 21.98720,[Total_Revenue] = 633.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg1 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 13,[Arrivals] = 9,[Departures] = 3,[Cancellations] = 2, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 655.12500,[Food_Revenue] = 21.98720,[Total_Revenue] = 643.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg2 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 15,[Arrivals] = 11,[Departures] = 4,[Cancellations] = 3, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 677.67500,[Food_Revenue] = 23.98720,[Total_Revenue] = 653.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg3 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 17,[Arrivals] = 13,[Departures] = 5,[Cancellations] = 4, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 689.13700,[Food_Revenue] = 25.98720,[Total_Revenue] = 663.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg4 + " ");
    }

    private void UpdateOccupancyForecastForGivenOccupancyDate(StringBuilder insertQuery) {
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=11 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=9 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=13.00,Revenue=191.00000  where Accom_Type_ID=12 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSix() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(6) + "') as theDayName");
        dow = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDateMinusEight() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.minusDays(8) + "') as theDayName");
        dow1 = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDateMinusEleven() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.minusDays(11) + "') as theDayName");
        dow2 = dowList.get(0).toString();
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight() {
        List secondDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(8).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(secondDecisionIdList.get(0) + "");
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven() {
        List firstDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(firstDecisionIdList.get(0) + "");
    }

    private void populateActivityPaceForMarketSegment(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg1 + ",29,16,14,1");
        insertQuery.append(" ,1,1355.12567,325.76343");
        insertQuery.append(" ,2525.26893,1,1,1,GETDATE())");


        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg3 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg3 + ",31,13,17,3");
        insertQuery.append(" ,1,1425.15627,278.76343");
        insertQuery.append(" ,2358.28784,1,1,1,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForSecondPacePoint(StringBuilder insertQuery, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + secondDecisionId + ",6," + mktSeg1 + ",'" + startDate.plusDays(6).toString() + "',2.52,17.10556,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6," + mktSeg2 + ",'" + startDate.plusDays(6).toString() + "',3.35,27.30656,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6," + mktSeg3 + ",'" + startDate.plusDays(6).toString() + "',4.56,37.70456,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6," + mktSeg4 + ",'" + startDate.plusDays(6).toString() + "',5.45,17.40956,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForFirstPacePoint(StringBuilder insertQuery, int firstDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + firstDecisionId + ",6," + mktSeg1 + ",'" + startDate.plusDays(6).toString() + "',1.25405,47.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6," + mktSeg2 + ",'" + startDate.plusDays(6).toString() + "',2.25422,57.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6," + mktSeg3 + ",'" + startDate.plusDays(6).toString() + "',3.25000,67.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6," + mktSeg4 + ",'" + startDate.plusDays(6).toString() + "',4.25555,77.10456,GETDATE())");
    }

    private void updateMarketSegmentToExcludeCompRooms(StringBuilder insertQuery) {
        insertQuery.append(" Update Mkt_Seg set Exclude_CompHouse_Data_Display=1 where Mkt_Seg_ID in (" + mktSeg1 + ") ");
    }

    private LocalDate getLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(6,3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    @Test
    public void shouldValidateBookingPaceReportMarketSegmentLevelFunctionForStaticDate() {
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("select * from ufn_get_booking_pace_ms_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "'," + paceDays + "," + 0 + ")");
        assertBookingPaceReportDataAtMarketSegmentLevel("Booking Pace Report at Market Segment with Static Dates", reportDataByStaticDates);
    }

    @Test
    public void shouldValidateBookingPaceReportMarketSegmentLevelFunctionForStaticDateWithExcludeMktSeg() {
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("select * from ufn_get_booking_pace_ms_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "'," + paceDays + "," + 1 + ")");
        assertBookingPaceReportDataAtMarketSegmentLevelForExcludeCompRooms("Booking Pace Report at Market Segment with Static Dates", reportDataByStaticDates);
    }

    private void assertBookingPaceReportDataAtMarketSegmentLevel(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(120, (reportDataByStaticDates.size()), Level + " - Number of Records ");
        vaidateDataFor14thPacePoint(Level, reportDataByStaticDates);
        vaidateDataFor17thPacePoint(Level, reportDataByStaticDates);

    }

    private void assertBookingPaceReportDataAtMarketSegmentLevelForExcludeCompRooms(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals(90, (reportDataByStaticDates.size()), Level + " - Number of Records ");
        vaidateDataForPacePointWithExcludeCompRooms(Level, reportDataByStaticDates);

    }

    private void vaidateDataFor17thPacePoint(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals("25", (reportDataByStaticDates.get(76)[0].toString()), Level + " - Rom Sold for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(startDate.minusDays(11).toString(), (reportDataByStaticDates.get(76)[4].toString()), Level + " - Business date for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("17", (reportDataByStaticDates.get(76)[2].toString()), Level + " - Days to Arrival for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(76)[3].toString()), Level + " - DOW of Business date for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("RACK", (reportDataByStaticDates.get(76)[5].toString()), Level + " - Market Segment Name for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("1.25", (reportDataByStaticDates.get(76)[6].toString()), Level + " - Occupancy Forecast for 17th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(startDate.minusDays(11).toString(), (reportDataByStaticDates.get(77)[4].toString()), Level + " - Business date for 17th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("17", (reportDataByStaticDates.get(77)[2].toString()), Level + " - Days to Arrival for 17th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(77)[3].toString()), Level + " - DOW of Business date for 17th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("DISCOUNT", (reportDataByStaticDates.get(77)[5].toString()), Level + " - Market Segment Name for 17th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("2.25", (reportDataByStaticDates.get(77)[6].toString()), Level + " - Occupancy Forecast for 17th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("25", (reportDataByStaticDates.get(78)[0].toString()), Level + " - Rom Sold for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(startDate.minusDays(11).toString(), (reportDataByStaticDates.get(78)[4].toString()), Level + " - Business date for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("17", (reportDataByStaticDates.get(78)[2].toString()), Level + " - Days to Arrival for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(78)[3].toString()), Level + " - DOW of Business date for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("PACKAGES", (reportDataByStaticDates.get(78)[5].toString()), Level + " - Market Segment Name for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("3.25", (reportDataByStaticDates.get(78)[6].toString()), Level + " - Occupancy Forecast for 17th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(startDate.minusDays(11).toString(), (reportDataByStaticDates.get(79)[4].toString()), Level + " - Business date for 17th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("17", (reportDataByStaticDates.get(79)[2].toString()), Level + " - Days to Arrival for 17th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(79)[3].toString()), Level + " - DOW of Business date for 17th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("CORPORATE", (reportDataByStaticDates.get(79)[5].toString()), Level + " - Market Segment Name for 17th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("4.26", (reportDataByStaticDates.get(79)[6].toString()), Level + " - Occupancy Forecast for 17th Pace Point for Market Segment ID " + mktSeg4 + " ");
    }

    private void vaidateDataForPacePointWithExcludeCompRooms(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals("31", (reportDataByStaticDates.get(67)[0].toString()), Level + " - Rom Sold for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(67)[4].toString()), Level + " - Business date for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("14", (reportDataByStaticDates.get(67)[2].toString()), Level + " - Days to Arrival for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(67)[3].toString()), Level + " - DOW of Business date for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("PACKAGES", (reportDataByStaticDates.get(67)[5].toString()), Level + " - Market Segment Name for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("4.56", (reportDataByStaticDates.get(67)[6].toString()), Level + " - Occupancy Forecast for 67th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(68)[4].toString()), Level + " - Business date for 68th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("14", (reportDataByStaticDates.get(68)[2].toString()), Level + " - Days to Arrival for 68th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(68)[3].toString()), Level + " - DOW of Business date for 68th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("CORPORATE", (reportDataByStaticDates.get(68)[5].toString()), Level + " - Market Segment Name for 68th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("5.45", (reportDataByStaticDates.get(68)[6].toString()), Level + " - Occupancy Forecast for 68th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("25", (reportDataByStaticDates.get(58)[0].toString()), Level + " - Rom Sold for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(startDate.minusDays(11).toString(), (reportDataByStaticDates.get(58)[4].toString()), Level + " - Business date for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("17", (reportDataByStaticDates.get(58)[2].toString()), Level + " - Days to Arrival for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(58)[3].toString()), Level + " - DOW of Business date for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("PACKAGES", (reportDataByStaticDates.get(58)[5].toString()), Level + " - Market Segment Name for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("3.25", (reportDataByStaticDates.get(58)[6].toString()), Level + " - Occupancy Forecast for 58th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("10", (reportDataByStaticDates.get(79)[2].toString()), Level + " - Days to Arrival for 79th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals(dow2, (reportDataByStaticDates.get(79)[3].toString()), Level + " - DOW of Business date for 79th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("PACKAGES", (reportDataByStaticDates.get(79)[5].toString()), Level + " - Market Segment Name for 79th Pace Point for Market Segment ID " + mktSeg4 + " ");
    }

    private void vaidateDataFor14thPacePoint(String Level, List<Object[]> reportDataByStaticDates) {
        assertEquals("29", (reportDataByStaticDates.get(88)[0].toString()), Level + " - Rom Sold for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(88)[4].toString()), Level + " - Business date for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("14", (reportDataByStaticDates.get(88)[2].toString()), Level + " - Days to Arrival for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(88)[3].toString()), Level + " - DOW of Business date for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("RACK", (reportDataByStaticDates.get(88)[5].toString()), Level + " - Market Segment Name for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("2.52", (reportDataByStaticDates.get(88)[6].toString()), Level + " - Occupancy Forecast for 14th Pace Point for Market Segment ID " + mktSeg1 + " ");
        assertEquals("35", (reportDataByStaticDates.get(89)[0].toString()), Level + " - Rom Sold for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(89)[4].toString()), Level + " - Business date for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("14", (reportDataByStaticDates.get(89)[2].toString()), Level + " - Days to Arrival for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(89)[3].toString()), Level + " - DOW of Business date for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("DISCOUNT", (reportDataByStaticDates.get(89)[5].toString()), Level + " - Market Segment Name for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("3.35", (reportDataByStaticDates.get(89)[6].toString()), Level + " - Occupancy Forecast for 14th Pace Point for Market Segment ID " + mktSeg2 + " ");
        assertEquals("31", (reportDataByStaticDates.get(90)[0].toString()), Level + " - Rom Sold for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(90)[4].toString()), Level + " - Business date for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("14", (reportDataByStaticDates.get(90)[2].toString()), Level + " - Days to Arrival for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(90)[3].toString()), Level + " - DOW of Business date for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("PACKAGES", (reportDataByStaticDates.get(90)[5].toString()), Level + " - Market Segment Name for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("4.56", (reportDataByStaticDates.get(90)[6].toString()), Level + " - Occupancy Forecast for 14th Pace Point for Market Segment ID " + mktSeg3 + " ");
        assertEquals("39", (reportDataByStaticDates.get(91)[0].toString()), Level + " - Rom Sold for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals(startDate.minusDays(8).toString(), (reportDataByStaticDates.get(91)[4].toString()), Level + " - Business date for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("14", (reportDataByStaticDates.get(91)[2].toString()), Level + " - Days to Arrival for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals(dow1, (reportDataByStaticDates.get(91)[3].toString()), Level + " - DOW of Business date for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("CORPORATE", (reportDataByStaticDates.get(91)[5].toString()), Level + " - Market Segment Name for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
        assertEquals("5.45", (reportDataByStaticDates.get(91)[6].toString()), Level + " - Occupancy Forecast for 14th Pace Point for Market Segment ID " + mktSeg4 + " ");
    }


}
