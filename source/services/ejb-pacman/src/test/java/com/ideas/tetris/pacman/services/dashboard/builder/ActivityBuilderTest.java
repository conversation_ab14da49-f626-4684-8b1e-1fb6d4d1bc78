/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.ideas.tetris.pacman.services.dashboard.builder;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.MetricResponse;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.TotalActivityByOccupancyDateDtoRowMapper;
import com.ideas.tetris.pacman.services.dashboard.type.ActualEstimatedType;
import com.ideas.tetris.pacman.services.dashboard.type.GroupByType;
import com.ideas.tetris.pacman.services.dashboard.type.MetricType2;
import com.ideas.tetris.pacman.services.dashboard.type.YearType;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupCreator;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupDetailsCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@MockitoSettings(strictness = Strictness.LENIENT)
public class ActivityBuilderTest extends AbstractG3JupiterTest {

    public static final List<Integer> propertyIds = Arrays.asList(4, 5);

    private PropertyGroupService mockPropertyGroupServiceLocal = null;

    private PacmanConfigParamsService configParamsService;
    Date caughtUpDate;
    Date startDate;
    Date endDate;

    @Mock
    PropertyService propertyService;

    @Mock
    private Property property_4;

    @Mock
    private Property property_5;

    @Mock
    private Client client_BSTN;

    @InjectMocks
    ActivityBuilder instance;

    @BeforeEach
    public void setUp() {
        instance.setCrudService(tenantCrudService());
        mockPropertyGroupServiceLocal = mock(PropertyGroupService.class);
        configParamsService = mock(PacmanConfigParamsService.class);
        instance.setPropertyGroupService(mockPropertyGroupServiceLocal);
        instance.setMultiPropertyCrudService(multiPropertyCrudService());
        instance.setConfigParamsService(configParamsService);
        Calendar date = Calendar.getInstance();
        date.add(Calendar.MONTH, 1);
        date.set(Calendar.DAY_OF_MONTH, 1);
        caughtUpDate = new LocalDate(date.getTime()).toDate();
        date.set(Calendar.DAY_OF_MONTH, 2);
        startDate = new LocalDate(date.getTime()).toDate();
        date.set(Calendar.DAY_OF_MONTH, 3);
        endDate = new LocalDate(date.getTime()).toDate();
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(propertyIds);
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), "BSTN", "5")).thenReturn("PCRS");
        when(configParamsService.getParameterValue(IntegrationConfigParamName.YIELD_CURRENCY_CODE.value(Constants.RATCHET), "BSTN", "5")).thenReturn("USD");
        when(configParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        setupPropertyDependencies();
    }

    private void setupPropertyDependencies() {
        when(propertyService.getPropertyById(4)).thenReturn(property_4);
        when(propertyService.getPropertyById(5)).thenReturn(property_5);
        when(property_4.getClient()).thenReturn(client_BSTN);
        when(property_4.getCode()).thenReturn("4");
        when(property_5.getClient()).thenReturn(client_BSTN);
        when(property_5.getCode()).thenReturn("5");
        when(client_BSTN.getCode()).thenReturn("BSTN");
    }

    @Test
    public void testExecuteTotalActivityQuery() throws Exception {
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(propertyIds);
        List results = instance.executeTotalActivityQuery(new Date(), new Date(), new Date(), false);
        assertNotNull(results);
        verify(mockPropertyGroupServiceLocal, times(2)).getPropertyContextAsList();
    }

    @Test
    public void testExecuteTotalActivityQueryExcludeCompRooms() throws Exception {
        updateMarketSegmentToExcluded(1);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(propertyIds);
        List results = instance.executeTotalActivityQuery(new Date(), new Date(), new Date(), true);
        assertNotNull(results);
        verify(mockPropertyGroupServiceLocal, times(2)).getPropertyContextAsList();
        updateMarketSegmentToExcluded(0);
    }

    @Test
    public void buildMetricOccupancyForecastTest() throws Exception {
        MetricRequest mr = new MetricRequest(MetricType2.OCCUPANCY_FORECAST_PERCENT);
        validateResult(mr, 65.18, 68.53);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where accom_type_id = 7");
        validateResult(mr, 51.56, 54.02);
    }

    @Test
    public void buildMetricADRTest() throws Exception {
        MetricRequest mr = new MetricRequest(MetricType2.AVERAGE_DAILY_RATE);
        validateResult(mr, 28.431, 27.091);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where accom_type_id = 7");
        validateResult(mr, 28.741, 27.481);
    }

    @Test
    public void buildMetricRevPARTest() throws Exception {
        MetricRequest mr = new MetricRequest(MetricType2.REVENUE_PER_AVAIL_ROOM);
        validateResult(mr, 18.531, 18.561);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where accom_type_id = 7");
        validateResult(mr, 14.821, 14.841);
    }

    @Test
    public void buildHotelActivityDataMetricForInventoryGroup() {
        //GIVEN
        MetricRequest metricRequest = new MetricRequest(MetricType2.DEPARTURES, ActualEstimatedType.RELATIVE_TO_CAUGHT_UP_DATE, YearType.LAST_YEAR, GroupByType.INVENTORY_GROUP);
        metricRequest.setInventoryGroupId(1);
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        instance.setCrudService(mockCrudService);
        ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<TotalActivityByOccupancyDateDtoRowMapper> totalActivityByOccupancyDateDtoRowMapperArgumentCaptor = ArgumentCaptor.forClass(TotalActivityByOccupancyDateDtoRowMapper.class);
        //WHEN
        instance.buildHotelActivityDataMetric(startDate, endDate, caughtUpDate, metricRequest);
        //THEN
        verify(mockCrudService).findByNativeQuery(
                stringArgumentCaptor.capture(),
                mapArgumentCaptor.capture(),
                totalActivityByOccupancyDateDtoRowMapperArgumentCaptor.capture()
        );
        Map value = mapArgumentCaptor.getValue();
        assertEquals(1, ((Integer) value.get("inventoryGroupViewId")).intValue());
        final String inventoryGroupActivityQuery = String.format(instance.inventoryGroupActivityQuery, "from FN_Overbooking_InventoryGroupView_Level(:businessDayEndDate, :inventoryGroupViewId, :mktSegExcludeFlags)");
        assertEquals(inventoryGroupActivityQuery, stringArgumentCaptor.getValue());
    }

    @Test
    public void shouldGetOccupancyForecastPercentForInventoryGroupWhileBuildMetric() {
        //GIVEN
        LocalDate systemDate = LocalDate.now();
        LocalDate caughtUpDate = systemDate.minusDays(1);
        setOccupancyFcstBy(systemDate, "300.00", "15.000");
        setAccomActivityBy(systemDate, "150", "1", "1", "15", "100.00");
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("INV_GRP");
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, 2));
        tenantCrudService().save(inventoryGroupDetails);
        MetricRequest metricRequest = new MetricRequest(
                MetricType2.OCCUPANCY_FORECAST_PERCENT, ActualEstimatedType.RELATIVE_TO_CAUGHT_UP_DATE, YearType.CURRENT_YEAR, GroupByType.INVENTORY_GROUP);
        metricRequest.setInventoryGroupId(inventoryGroup.getId());
        //WHEN
        MetricResponse metricResponse = instance.buildOccupancyMetric(systemDate.toDate(), systemDate.toDate(), caughtUpDate.toDate(), metricRequest);
        assertEquals(new BigDecimal("91.220000"), metricResponse.getMetricValuesByGroupByType().get("INVENTORY_GROUP").get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForEstimated() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("3000"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForEstimatedByNotConsideringComponentRoom() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        markComponentRoomFor("6");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("2400"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForActual() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setMktAccomActivityBy(today, "100.00");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("3000"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForActualByNotConsideringComponentRoom() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setMktAccomActivityBy(today, "100.00");
        markComponentRoomFor("6");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("2400"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForEstimatedForInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createInventoryGroup(baseAccomClass);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(baseAccomClass, inventoryGroup);
        metricRequest.setInventoryGroupId(inventoryGroup.getId());
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("1800"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueByBusinessViewsMetricResponseForActualForInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE);
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createInventoryGroup(baseAccomClass);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(baseAccomClass, inventoryGroup);
        metricRequest.setInventoryGroupId(inventoryGroup.getId());
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setMktAccomActivityBy(today, "100.00");
        //WHEN
        MetricResponse response = instance.buildRevenueByBusinessViewMetric(today.toDate(), today.toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("1800"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForEstimated() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setOccupancyFcstBy(today.plusDays(1), "100.00", "10");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("6000"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForEstimatedByNotConsideringComponentRoom() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setOccupancyFcstBy(today.plusDays(1), "100.00", "10");
        markComponentRoomFor("6");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("4800"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForActual() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setMktAccomActivityBy(today, "100.00");
        setMktAccomActivityBy(today.plusDays(1), "100.00");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("6000"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForActualByNotConsideringComponentRoom() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));//1
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));//2
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));//3
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));//4
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));//5
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));//6
        setMktAccomActivityBy(today, "100.00");
        setMktAccomActivityBy(today.plusDays(1), "100.00");
        markComponentRoomFor("6");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("4800"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    public void markComponentRoomFor(final String roomTypeId) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID = " + roomTypeId);
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForEstimatedForInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ESTIMATED);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createInventoryGroup(baseAccomClass);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(baseAccomClass, inventoryGroup);
        metricRequest.setInventoryGroupId(inventoryGroup.getId());
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setOccupancyFcstBy(today, "100.00", "10");
        setOccupancyFcstBy(today.plusDays(1), "100.00", "10");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewForInventoryGroupMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("3600"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }

    @Test
    public void shouldGetRevenueTotalByBusinessViewsMetricResponseForActualForInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setActualEstimatedType(ActualEstimatedType.ACTUAL);
        metricRequest.setGroupByType(GroupByType.PROPERTY_BUSINESS_VIEW);
        metricRequest.setMetricType(MetricType2.REVENUE_TOTAL);
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createInventoryGroup(baseAccomClass);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(baseAccomClass, inventoryGroup);
        metricRequest.setInventoryGroupId(inventoryGroup.getId());
        when(mockPropertyGroupServiceLocal.getPropertyContextAsList()).thenReturn(Arrays.asList(5));
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        List<Integer> businessGroupIds = new ArrayList<>();
        businessGroupIds.add(BG1.getId());
        businessGroupIds.add(BG2.getId());
        metricRequest.setFilterIds(businessGroupIds);
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("BART"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("COMP"));
        addMarketSegmentsToBusinessGroup(BG1, findMarketSegment("CONS"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("CORP"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("DIST"));
        addMarketSegmentsToBusinessGroup(BG2, findMarketSegment("RACK"));
        setMktAccomActivityBy(today, "100.00");
        setMktAccomActivityBy(today.plusDays(1), "100.00");
        //WHEN
        MetricResponse response = instance.buildRevenueTotalByBusinessViewForInventoryGroupMetric(today.toDate(), today.plusDays(1).toDate(), today.toDate(), metricRequest);
        //THEN
        assertEquals(new BigDecimal("3600"), response.getMetricValuesByGroupByType().get(GroupByType.PROPERTY_BUSINESS_VIEW.name()).get(0));
    }


    private void validateResult(MetricRequest mr, double val1, double val2) {
        MetricResponse metricResponse = instance.buildOccupancyMetric(startDate, endDate, caughtUpDate, mr);
        assertNotNull(metricResponse);
        List<BigDecimal> list = (List<BigDecimal>) metricResponse.getMetricValuesByGroupByType().get("PROPERTY");
        assertEquals(2, list.size());
        assertEquals(new BigDecimal(val1).setScale(2, RoundingMode.DOWN), list.get(0).setScale(2, RoundingMode.DOWN));
        assertEquals(new BigDecimal(val2).setScale(2, RoundingMode.DOWN), list.get(1).setScale(2, RoundingMode.DOWN));
    }

    private void setOccupancyFcstBy(LocalDate occupancyDate, String revenue, String occupancyNbr) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_FCST set Occupancy_NBR = '" + occupancyNbr + "', Revenue = '" + revenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setAccomActivityBy(LocalDate occupancyDate, String capacity, String roomsNotAvailMaint, String roomsNotAvailOther, String roomsSolds, String roomRevenue) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Activity set Accom_Capacity = " + capacity + ", Rooms_Sold = " + roomsSolds +
                ", Rooms_Not_Avail_Maint = " + roomsNotAvailMaint + ", Rooms_Not_Avail_Other = " + roomsNotAvailOther + ", Room_Revenue = '" + roomRevenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setMktAccomActivityBy(LocalDate occupancyDate, String revenue) {
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Accom_Activity set Room_Revenue = '" + revenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private BusinessGroup addBusinessView(String name) {
        BusinessGroup businessGroup = new BusinessGroup();
        businessGroup.setName(name);
        businessGroup.setDescription(name);
        businessGroup.setRanking(1);
        businessGroup.setPropertyId(5);
        BusinessGroup savedBusinessGroup = tenantCrudService().save(businessGroup);
        return savedBusinessGroup;
    }

    private MktSegBusinessGroup addMarketSegmentsToBusinessGroup(BusinessGroup businessGroup, MktSeg mktSeg) {
        MktSegBusinessGroup mktSegBusinessGroup = new MktSegBusinessGroup();
        mktSegBusinessGroup.setBusinessGroup(businessGroup);
        mktSegBusinessGroup.setMktSeg(mktSeg);
        mktSegBusinessGroup.setRanking(1);
        return tenantCrudService().save(mktSegBusinessGroup);
    }

    private MktSeg findMarketSegment(String mktSegName) {
        return tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", mktSegName).parameters());
    }

    private void updateMarketSegmentToExcluded(int value) {
        tenantCrudService().executeUpdateByNativeQuery(" Update Mkt_Seg set Exclude_CompHouse_Data_Display=" + value + " where Mkt_Seg_ID in (" + 2 + ") ");
    }

}