package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.google.common.collect.Sets;
import com.ideas.cache.redis.configuration.IdeasRedisCacheManager;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.MultiPropertyCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.discontinuedmarketsegment.services.DiscontinuedMarketSegmentsService;
import com.ideas.tetris.pacman.services.fplos.constants.StatisticalOutlierAlertConstants;
import com.ideas.tetris.pacman.services.functionspace.service.FunctionSpaceService;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertStepType;
import com.ideas.tetris.pacman.services.informationmanager.cache.InfoMgrStatusEntityCache;
import com.ideas.tetris.pacman.services.informationmanager.cache.InformationManagerPropertyCountCache;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertHistory;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.Comment;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionConfigDTO;
import com.ideas.tetris.pacman.services.informationmanager.dto.ScoreRange;
import com.ideas.tetris.pacman.services.informationmanager.dto.SearchCriteriaDTO;
import com.ideas.tetris.pacman.services.informationmanager.dto.Step;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStepsEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionAlertService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobExecution;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.opera.CostOfWalkCalculator;
import com.ideas.tetris.pacman.services.pmsmigration.alert.PMSMigrationAlertActionHelper;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationDataCleanUpService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationNewVendorConfigurationService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.pacman.services.problem.ProblemService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.tars.TARSDecisionService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.configparams.entities.ParameterValue;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.hamcrest.core.Is;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FEATURE_LICENSING_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.ALERT_CATEGORY;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.NEW_STATUS;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper.createTetrisPrincipal;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_TEST;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CN_REGULAR_USER;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_DN_REGULAR_USER;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_PROPERTY_ID_PARIS;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_USER_ID_SSO;
import static java.util.List.of;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.internal.verification.VerificationModeFactory.times;

@MockitoSettings(strictness = Strictness.LENIENT)
public class AlertServiceTest extends AbstractG3JupiterTest {

    public static final String ALERT_METADATA_JSON = "{\"key1\":\"val1\", \"key2\":\"val2\"}";

    public static final String ALERT_METADATA_KEY = "AlertMetadata";

    private static final Integer PROPERTY_ID_PARIS = 6;

    private static final String PROPERTY_CODE_PARIS = "H2";

    private static final Integer PROPERTY_ID_PUNE = 5;

    private static final String PROPERTY_CODE_PUNE = "H1";

    private static final String DESCRIPTION = "test description";

    private static final String DETAILS = "test details";

    private static final AlertType TYPE = AlertType.UnassignedRoomType;

    private static final int SCORE = 50;

    private static final String COMMENT = "this is a test comment";

    private static final String DESCRIPTION_2 = "description for second alert";

    private static final String DETAILS_2 = "details for second alert";

    private static final int SCORE_INCREMENT = 5;

    private static final int SCORE_DECREMENT = 5;

    private static final int UNQUALIFIED_RATE_STEP_ID = 150;

    private static final String CLIENT = "unittest";

    private static final Integer CLIENT_ID_DUMMY = 1;

    private static final String UID_SSO_USER = "11403";

    private static final String CREATED_BY = "SSO User";

    private static final String STEP_TEXT_ASSIGN_ATTRIBUTES_FOR_NEW_MARKET_SEGMENT = "assign.attributes.for.new.market.segment";

    private static final String STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP = "create.a.new.forecast.group";

    private static final String STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION = "review.your.property.business.view.configuration";

    private static final String STEP_TEXT_ASSIGN_THE_APPROPRIATE_ATTRIBUTES_TO_ANY_MARKET_SEGMENTS = "assign.the.appropriate.attributes.to.any.unassigned.market.segments";

    private static final String MKT_SEGMENT_1 = "MKT_1";

    private static final String MKT_SEGMENT_2 = "MKT_2";

    private static final String ALRT_DETAIL_WITH_METADATA = "startDate:DT_05-Jul-2011_DT,endDate:DT_03-Jul-2012_DT,key1:val1,key2:val2";

    private static final String ALRT_DETAIL_WITH_JSON_METADATA = "startDate:DT_05-Jul-2011_DT,endDate:DT_03-Jul-2012_DT," + ALERT_METADATA_KEY + ":" + ALERT_METADATA_JSON;

    private final String FPLOS_ALERT_DETAIL_WITH_INVALID_JOB_EXE_ID = "startDate:DT_05-Jul-2011_DT,endDate:DT_03-Jul-2012_DT,decisionJobExecutionId:-1,test:test";

    private final String FPLOS_ALERT_DETAIL_WITH_VALID_JOB_EXE_ID = "startDate:DT_05-Jul-2011_DT,endDate:DT_03-Jul-2012_DT,decisionJobExecutionId:48,test:test";

    private PacmanConfigParamsService mockAlertConfigService = null;

    private DateService dateService;

    private UserService userService;

    private AuthorizationService authService;

    private PropertyGroupService propertyGroupService;

    @InjectMocks
    private AlertService service;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private FunctionSpaceService functionSpaceService;
    @Mock
    private VirtualPropertyMappingService virtualPropertyMappingService;

    private WorkContextType workContext;

    private WorkContextType workContextOtherProperty;

    private List<Property> properties;

    private boolean infoMgrPropertyAndPropertyGroupEnabledOriginal;

    private PropertyService mockPropertyService;

    @Mock
    private UserGlobalDBService mockUserGlobalDBService;

    @Mock
    private JobMonitorService mockJobMonitorService;

    @Mock
    private JobServiceLocal mockJobServiceLocal;

    @Mock
    private TARSDecisionService tarsDecisionService;

    @Mock
    private BookedDataService mockBookedDataService;

    @Mock
    private ProblemService problemService;

    @Mock
    private RoomTypeRecodingService mockRoomTypeRecodingService;

    @Mock
    private PMSMigrationService pmsMigrationService;

    @Mock
    private CostOfWalkCalculator costOfWalkCalculator;

    @Mock
    private MktSegRecodingService mktSegRecodingService;

    @Mock
    private PropertyStageChangeService propertyStageChangeService;

    @Mock
    private PMSMigrationNewVendorConfigurationService pmsMigrationNewVendorConfigurationService;

    @Mock
    private DiscontinuedMarketSegmentsService discontinuedMarketSegmentsService;

    private JobExecution lastExecution;

    private PMSMigrationAlertActionHelper pmsMigrationAlertActionHelper;

    @Mock
    @TenantCrudServiceBean.Qualifier
    CrudService tenantCrudService;

    @Mock
    TentativeGroupForecastAlertRepository repository;
    @Mock
    private PMSMigrationDataCleanUpService pmsMigrationDataCleanUpService;

    @Mock
    @Qualifier("multiPropertyCrudServiceBean")
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Mock
    private LicenseService licenseService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        setWorkContextProperty(TestProperty.H2);
        service.setMultiPropertyCrudService(multiPropertyCrudService());
        service.setGlobalCrudService(globalCrudService());
        AnalyticalMarketSegmentService analyticalMarketSegmentService = new AnalyticalMarketSegmentService();
        analyticalMarketSegmentService.setCrudService(tenantCrudService());
        service.setAnalyticalMarketSegmentService(analyticalMarketSegmentService);
        properties = new ArrayList<>();
        properties.add(globalCrudService().find(Property.class, 5));
        properties.add(globalCrudService().find(Property.class, 6));
        AuthorizationService authorizationService = mock(AuthorizationService.class);
        when(authorizationService.retrieveAuthorizedProperties()).thenReturn(properties);
        service.setAuthorizationService(authorizationService);
        mockAlertConfigService = mock(PacmanConfigParamsService.class);
        service.setConfigService(mockAlertConfigService);
        dateService = DateService.createTestInstance();
        service.dateService = dateService;
        userService = new UserService();
        userService.setTenantCrudService(tenantCrudService());
        userService.setGlobalCrudService(globalCrudService());
        userService.setPacmanConfigParamsService(pacmanConfigParamsService);
        mockPropertyService = mock(PropertyService.class);
        service.setPropertyService(mockPropertyService);
        // Initialize cache
        initCache();
        authService = new AuthorizationService();
        authService.setCrudService(tenantCrudService());
        authService.setGlobalCrudService(globalCrudService());
        userService.setAuthorizationService(authService);
        propertyGroupService = new PropertyGroupService();
        propertyGroupService.setGlobalCrudService(globalCrudService());
        service.propertyGroupService = propertyGroupService;
        pmsMigrationAlertActionHelper = new PMSMigrationAlertActionHelper();
        inject(pmsMigrationAlertActionHelper, "alertService", service);
        inject(pmsMigrationAlertActionHelper, "pmsMigrationService", pmsMigrationService);
        inject(pmsMigrationAlertActionHelper, "pmsMigrationDataCleanUpService", pmsMigrationDataCleanUpService);
        inject(pmsMigrationAlertActionHelper, "mktSegRecodingService", mktSegRecodingService);
        inject(pmsMigrationAlertActionHelper, "propertyStageChangeService", propertyStageChangeService);
        inject(pmsMigrationAlertActionHelper, "costOfWalkCalculator", costOfWalkCalculator);
        inject(pmsMigrationAlertActionHelper, "pmsMigrationNewVendorConfigurationService", pmsMigrationNewVendorConfigurationService);
        inject(service, "pmsMigrationAlertActionHelper", pmsMigrationAlertActionHelper);
        service.userService = userService;
        // Regular user did not have access to prop id 5 so we are using prop 6 (hilton paris)
        workContext = new WorkContextType();
        workContext.setUserId(UID_SSO_USER);
        workContext.setClientCode(CLIENT);
        workContext.setClientId(CLIENT_ID_DUMMY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode(PROPERTY_CODE_PARIS);
        workContextOtherProperty = new WorkContextType();
        workContextOtherProperty.setUserId(UID_SSO_USER);
        workContextOtherProperty.setClientCode(CLIENT);
        workContextOtherProperty.setClientId(CLIENT_ID_DUMMY);
        workContextOtherProperty.setPropertyId(PROPERTY_ID_PUNE);
        workContextOtherProperty.setPropertyCode(PROPERTY_CODE_PUNE);
        infoMgrPropertyAndPropertyGroupEnabledOriginal = SystemConfig.isInfoMgrPropertyAndPropertyGroupEnabled();
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
    }

    private void initCache() {
        InformationManagerPropertyCountCache informationManagerPropertyCountCache = new InformationManagerPropertyCountCache();
        informationManagerPropertyCountCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        informationManagerPropertyCountCache.clearCache();
        service.setInformationManagerPropertyCountCache(informationManagerPropertyCountCache);
    }

    @AfterEach
    public void tearDown() {
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", String.valueOf(infoMgrPropertyAndPropertyGroupEnabledOriginal));
        initCache();
    }

    private Alert buildAlert(String description, String details) {
        return buildAlert(description, details, workContext, SCORE);
    }

    private Alert buildAlert(String description, String details, WorkContextType workContextLocal, int score) {
        return buildAlert(description, details, workContextLocal, score, TYPE);
    }

    private Alert buildAlert(String description, String details, WorkContextType workContextLocal, int score, AlertType alertType) {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(description);
        alertTypeEntity.setBaseScore(score);
        return service.createAlert(workContextLocal, alertTypeEntity, description, details, alertType);
    }

    private void buildSystemException() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription("");
        alertTypeEntity.setBaseScore(SCORE);
        service.createAlert(workContext, alertTypeEntity, "", "", AlertType.LRVExceedingUserDefinedBAR);
    }

    @Test
    public void testGetAlertType() throws Exception {
        InfoMgrTypeEntity type = service.getAlertType(TYPE.toString());
        assertEquals(TYPE.toString(), type.getName());
    }

    @Test
    public void testGetAlertTypeForProperty() throws Exception {
        InfoMgrTypeEntity type = service.getAlertType(5, TYPE.toString());
        assertEquals(TYPE.toString(), type.getName());
    }

    @Test
    public void testGetOpenAlertCount() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Integer count = service.getOpenAlertCount();
        assertEquals(0, count.intValue());
        buildAlert(DESCRIPTION, DETAILS);
        count = service.getOpenAlertCount();
        assertEquals(1, count.intValue());
    }

    @Test
    public void testGetOpenAlerts() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(0, alerts.size());
        buildAlert(DESCRIPTION, DETAILS);
        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();
        alerts = service.getOpenAlerts();
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        Alert alert = alerts.get(0);
        assertEquals(PROPERTY_ID_PARIS, alert.getPropertyId());
        assertEquals(DESCRIPTION, alert.getDescription());
        assertEquals(DETAILS, alert.getDetails());
        assertEquals(TYPE, alert.getType());
        assertEquals(SCORE, alert.getScore());
        assertEquals(CREATED_BY, alert.getCreatedBy());
    }

    @Test
    public void testIncreaseScore() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("America/Chicago");
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(0, alerts.size());
        buildAlert(DESCRIPTION, DETAILS);
        alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        Alert alert = alerts.get(0);
        assertEquals(PROPERTY_ID_PARIS, alert.getPropertyId());
        assertEquals(DESCRIPTION, alert.getDescription());
        assertEquals(DETAILS, alert.getDetails());
        assertEquals(TYPE, alert.getType());
        assertEquals(SCORE, alert.getScore());
        assertEquals(CREATED_BY, alert.getCreatedBy());
        System.out.println("test to see if this is getting seen in new jenkins");
        // add second alert
        buildAlert(DESCRIPTION_2, DETAILS_2);
        alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        alert = alerts.get(0);
        assertEquals(PROPERTY_ID_PARIS, alert.getPropertyId());
        assertEquals(DESCRIPTION_2, alert.getDescription());
        assertEquals(DETAILS_2, alert.getDetails());
        assertEquals(TYPE, alert.getType());
        assertEquals(SCORE + SCORE_INCREMENT, alert.getScore());
        assertEquals(CREATED_BY, alert.getCreatedBy());
        List<AlertHistory> historyList = service.getHistory(alert.getAlertId(), alert.getPropertyId());
        assertEquals(2, historyList.size());
        // make sure the first history record in the list is the first one (timestamps could be the same)
        historyList.sort((h1, h2) -> h2.getHistoryId() - h1.getHistoryId());
        AlertHistory history = historyList.get(0);
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE + SCORE_INCREMENT, history.getScore());
        assertEquals("Score Increased", history.getType());
        Comment comment = history.getAssociatedComment();
        assertNull(comment);
        history = historyList.get(1);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals("Created", history.getType());
    }

    @Test
    public void shouldIncreaseScore_whenAlertRevokedFromSuspendedStatus() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        InfoMgrTypeEntity mockedAlertType = getMockedAlertType(TYPE);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("America/Chicago");
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        List<Alert> alerts = service.getOpenAlerts();
        buildAlert(DESCRIPTION, DETAILS);
        alerts = service.getOpenAlerts();
        Alert alert = alerts.get(0);
        //suspend alert
        service.suspendOrRevertRaisedAlert(alert.getAlertId(), alert.getPropertyId(), 1, true);
        // add second alert
        buildAlert(DESCRIPTION_2, DETAILS_2);
        alerts = service.getOpenAlerts();
        alert = alerts.get(0);
        assertEquals(mockedAlertType.getBaseScore(), alert.getScore());
    }


    @Test
    public void testGetAlertHistory() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("America/Chicago");
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        List<AlertHistory> historyList = service.getHistory(alert.getAlertId(), alert.getPropertyId());
        assertNotNull(historyList);
        assertEquals(1, historyList.size());
        AlertHistory history = historyList.get(0);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals("Created", history.getType());
    }

    @Test
    public void testAddComment() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("America/Chicago");
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        // some time has to pass before adding the comment in order for the history ordering to work properly
        try {
            Thread.sleep(250);
        } catch (Exception e) {
        }
        service.addComment(alert.getAlertId(), alert.getPropertyId(), COMMENT);
        List<AlertHistory> historyList = service.getHistory(alert.getAlertId(), alert.getPropertyId());
        assertNotNull(historyList);
        assertEquals(2, historyList.size());
        AlertHistory history = historyList.get(0);
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals("Comment Added", history.getType());
        Comment comment = history.getAssociatedComment();
        assertNotNull(comment);
        assertEquals(CREATED_BY, comment.getCreatedBy());
        assertEquals(COMMENT, comment.getText());
        history = historyList.get(1);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals("Created", history.getType());
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        alert = alerts.get(0);
        assertEquals("Viewed", alert.getStatus());
    }

    @Test
    @Disabled("Flaky")
    public void testAddCommentToSuspendedExceptionLeavesStateAsSuspended() {
        ExceptionAlertService service = new ExceptionAlertService();
        service.setGlobalCrudService(globalCrudService());
        service.setMultiPropertyCrudService(multiPropertyCrudService());
        service.setTenantCrudService(tenantCrudService());
        InfoMgrStatusEntityCache infoMgrStatusEntityCache = Mockito.mock(InfoMgrStatusEntityCache.class);
        Mockito.when(infoMgrStatusEntityCache.get(anyString())).thenReturn(tenantCrudService().find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID));
        service.setInfoMgrStatusEntityCache(infoMgrStatusEntityCache);
        InfoMgrExcepNotifEntity excepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, AlertType.DecisionChangeEx, service);
        ExceptionAlert alert = service.findByAlertId(excepNotifEntity.getId());
        service.suspendOrRevertRaisedException(alert.getAlertId(), alert.getPropertyId(), 1, true);
        service.addComment(alert.getAlertId(), alert.getPropertyId(), COMMENT);
        List<AlertHistory> historyList = service.getHistory(alert.getAlertId(), alert.getPropertyId());
        assertNotNull(historyList);
        assertTrue(historyList.stream().anyMatch(alertHistory -> "Comment Added".equals(alertHistory.getType())
                && "Suspended".equals(alertHistory.getStatus())));
        assertTrue(historyList.stream().anyMatch(alertHistory->"Suspended".equals(alertHistory.getStatus())));
    }

    private InfoMgrExcepNotifEntity buildExceptionAlert(String description, String details, AlertType alertType, ExceptionAlertService service) {
        ExceptionConfigService exceptionConfigService = new ExceptionConfigService();
        exceptionConfigService.crudService = tenantCrudService();
        exceptionConfigService.setGlobalCrudService(globalCrudService());
        exceptionConfigService.setMultiPropertyCrudService(multiPropertyCrudService());
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(CREATED_BY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode("");
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(description);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        Date occupancyDate = new Date();
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        list.get(0).setExceptionSubType(subType);
        InfoMgrExcepNotifEntity exceptionAlert = service.createExceptionAlert(alertTypeEntity, details, alertType, occupancyDate, list.get(0), null);
        exceptionAlert = tenantCrudService().save(exceptionAlert);
        service.createAndSaveExceptionHistory(new ArrayList<>(), null, Collections.singletonList(exceptionAlert));
        return exceptionAlert;
    }

    private ExceptionConfigDTO buildExceptionConfiguration(String subType, String levelType, String sublevelId, List<Integer> listPropertyIds, boolean subLevelKeywordUsed) {
        ExceptionConfigDTO objExceptionConfigDTO = new ExceptionConfigDTO();
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.DecisionChangeEx.toString()).parameters());
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", levelType).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", subType).parameters());
        objExceptionConfigDTO.setAlertTypeEntity(type);
        objExceptionConfigDTO.setDisabled(false);
        objExceptionConfigDTO.setEndDate("Today+7");
        objExceptionConfigDTO.setExceptionLevel(objExceptionLevelEntity);
        objExceptionConfigDTO.setExceptionSubLevel(sublevelId);
        objExceptionConfigDTO.setExceptionSubType(objExceptionSubTypeEntity);
        objExceptionConfigDTO.setFrequency("1");
        objExceptionConfigDTO.setPropertyIds(listPropertyIds);
        objExceptionConfigDTO.setStartDate("Today");
        objExceptionConfigDTO.setThresholdConstraint(">=");
        objExceptionConfigDTO.setThresholdValue(new BigDecimal(50));
        objExceptionConfigDTO.setStatusId(1);
        objExceptionConfigDTO.setMetricType(MetricType.CURRENCY);
        objExceptionConfigDTO.setSubLevelHasKeyword(subLevelKeywordUsed);
        return objExceptionConfigDTO;
    }

    @Test
    public void testResolveAlert() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        service.resolveAlert(alert.getAlertId(), alert.getPropertyId());
        alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
    }

    @Test
    public void testViewAlert() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        alert = alerts.get(0);
        assertEquals("New", alert.getStatus());
        service.viewAlert(alert.getAlertId(), alert.getPropertyId());
        alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        alert = alerts.get(0);
        assertEquals("Viewed", alert.getStatus());
    }

    @Test
    public void testResolveAllAlerts() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        buildAlert(DESCRIPTION, DETAILS);
        List<Alert> alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        service.resolveAllAlerts(TYPE, PROPERTY_ID_PARIS);
        alerts = service.getOpenAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
    }

    @Test
    public void testGetNewAlerts() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        buildAlert(DESCRIPTION, DETAILS);
        List<Alert> newAlertsList = service.getNewAlerts();
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(1, newAlertsList.size());
    }

    @Test
    public void testGetNewAlertCount() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        buildAlert(DESCRIPTION, DETAILS);
        Integer alertCount = service.getNewAlertCount();
        assertEquals(new Integer(1), alertCount);
    }

    @Test
    public void testActionAlert() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        service.actionAlert(alert.getAlertId(), alert.getPropertyId());
    }

    @Test
    public void testgetStep() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        @SuppressWarnings("unchecked")
        List<InfoMgrInstanceStepStateEntity> stepsEntity = tenantCrudService().findByNamedQuery(InfoMgrInstanceStepStateEntity.BY_ALERT_INSTANCE, QueryParameter.with("instanceId", objAlertEntity.getId()).parameters());
        assertEquals(6, stepsEntity.size());
    }

    @Test
    public void testDoAlertStep() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        service.doAlertStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
        List<Step> stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        assertTrue(stepList.get(0).isActioned());
        assertFalse(stepList.get(1).isActioned());
        Alert alert1 = service.getAlertById(alert.getAlertId(), alert.getPropertyId());
        assertNotNull(alert1);
        assertEquals("Actioned", alert1.getStatus());
    }

    @Test
    public void testDoAlertStepForSnoozedStatus() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        tenantCrudService().executeUpdateByNativeQuery("update Info_Mgr_Instance set info_mgr_status_id=" + Constants.ALERT_STATUS_SNOOZED_ID + " where Info_Mgr_Instance_id = " + alert.getAlertId());
        service.doAlertStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
        List<Step> stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        assertEquals(false, stepList.get(0).isActioned());
        assertEquals(false, stepList.get(1).isActioned());
        Alert alert1 = service.getAlertById(alert.getAlertId(), alert.getPropertyId());
        assertNotNull(alert1);
        assertEquals("Suspended/Monitor", alert1.getStatus());
    }

    // Test excluding specific steps for the UnassignedMarketSegment alert depending on whether AMS is enabled
    @Test
    public void testExcludeStep() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        // Enable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedMarketSegment);
        List<Step> stepList = alert.getSteps();
        // There should only be three steps
        assertEquals(3, stepList.size(), "stepList is wrong size");
        // When AMS is enabled, step with text of "assign.attributes.for.new.market.segment" should be present
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_ATTRIBUTES_FOR_NEW_MARKET_SEGMENT.equals(step.getText())), "assign.attributes.for.new.market.segment should be present");
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
        // Now test the same alert with AMS disabled
        // Disable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        // Resolve the current alert so a new one will be created
        service.resolveAlert(alert.getAlertId(), PROPERTY_ID_PARIS);
        alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedMarketSegment);
        stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        // There should only be three steps
        assertEquals(3, stepList.size(), "stepList is wrong size");
        // When AMS is disabled, step with text of "assign.attributes.for.new.market.segment" should be excluded
        assertFalse(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_ATTRIBUTES_FOR_NEW_MARKET_SEGMENT.equals(step.getText())), "assign.attributes.for.new.market.segment should not be present");
        // When AMS is disabled, step with text of "assign.the.appropriate.attributes.to.any.unassigned.market.segments" should be present
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_THE_APPROPRIATE_ATTRIBUTES_TO_ANY_MARKET_SEGMENTS.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
    }

    // Test excluding the assign market segment step when AMS is enabled and alert only has market segments which have
    // already been assigned in AMS
    @Test
    public void testExcludeStepWhenMktSegmentsAssigned() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        // Enable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        String alertDetail = MKT_SEGMENT_1;
        createAnalyticalMarketSegments(Collections.singletonList(MKT_SEGMENT_1));
        Alert alert = buildAlert(DESCRIPTION, alertDetail, workContext, SCORE, AlertType.UnassignedMarketSegment);
        List<Step> stepList = alert.getSteps();
        // There should only be two step
        assertEquals(2, stepList.size(), "stepList is wrong size");
        // When AMS is enabled, step with text of "assign.attributes.for.new.market.segment" should be excluded
        assertEquals(STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP, stepList.get(0).getText(), "text for step should be " + STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP);
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
        // Now test the same alert with AMS disabled
        // Disable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        // Resolve the current alert so a new one will be created
        service.resolveAlert(alert.getAlertId(), PROPERTY_ID_PARIS);
        alert = buildAlert(DESCRIPTION, alertDetail, workContext, SCORE, AlertType.UnassignedMarketSegment);
        stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        // There should now be three steps
        assertEquals(3, stepList.size(), "stepList is wrong size");
        // When AMS is disabled, step with text of "assign.the.appropriate.attributes.to.any.unassigned.market.segments" should be present
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_THE_APPROPRIATE_ATTRIBUTES_TO_ANY_MARKET_SEGMENTS.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
        // Re-enable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        // Now add one to the detail that doesn't exist in analytical_mkt_seg
        alertDetail = MKT_SEGMENT_1 + " , " + MKT_SEGMENT_2;
        // Resolve the current alert so a new one will be created
        service.resolveAlert(alert.getAlertId(), PROPERTY_ID_PARIS);
        alert = buildAlert(DESCRIPTION, alertDetail, workContext, SCORE, AlertType.UnassignedMarketSegment);
        stepList = alert.getSteps();
        // There should now be three steps
        assertEquals(3, stepList.size(), "stepList is wrong size");
        // When AMS is enabled, step with text of "assign.attributes.for.new.market.segment" should be present
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_ATTRIBUTES_FOR_NEW_MARKET_SEGMENT.equals(step.getText())), STEP_TEXT_ASSIGN_ATTRIBUTES_FOR_NEW_MARKET_SEGMENT + " should be present");
        // The create forecast group step should also exist
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP.equals(step.getText())), STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP + " should be present");
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
        // Now test the same alert with AMS disabled
        // Disable AMS
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        // Resolve the current alert so a new one will be created
        service.resolveAlert(alert.getAlertId(), PROPERTY_ID_PARIS);
        alert = buildAlert(DESCRIPTION, alertDetail, workContext, SCORE, AlertType.UnassignedMarketSegment);
        stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        // There should now be three steps
        assertEquals(3, stepList.size(), "stepList is wrong size");
        // When AMS is disabled, step with text of "assign.the.appropriate.attributes.to.any.unassigned.market.segments" should be present
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_ASSIGN_THE_APPROPRIATE_ATTRIBUTES_TO_ANY_MARKET_SEGMENTS.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_CREATE_A_NEW_FORECAST_GROUP.equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> STEP_TEXT_REVIEW_PROPERTY_BUSINESS_VIEW_CONFIGURATION.equals(step.getText())));
    }

    @Test
    public void testExcludeStepWhenComplimentaryCrossChargeIsDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(6, stepList.size());
        assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.type.to.a.room.class".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.the.cost.of.walk".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.overbooking.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.rate.plan.rate.details".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.vendor.integration.mapping.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "resolve.alert".equals(step.getText())));
    }

    @Test
    public void testIncludeStepWhenComplimentaryCrossChargeIsEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(7, stepList.size());
        assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.type.to.a.room.class".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.the.cost.of.walk".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.overbooking.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.rate.plan.rate.details".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.complimentary.cross.charge.values".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.vendor.integration.mapping.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "resolve.alert".equals(step.getText())));
    }

    @Test
    public void testDoAlertStep_ClearStepAction() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        service.doAlertStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
        service.clearStepActionedState(alert.getPropertyId(), alert.getAlertId());
        List<Step> stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertFalse(stepList.get(0).isActioned());
        assertEquals("ACTION", stepList.get(0).getLabel());
        assertFalse(stepList.get(1).isActioned());
        assertEquals("ACTION", stepList.get(1).getLabel());
    }

    @Test
    public void testIsAlertTypeEnabled() {
        InfoMgrTypeEntity alertTypeEntity = service.getAlertType(AlertType.DecisionChangeEx.toString());
        assertTrue(alertTypeEntity.isEnabled());
    }

    @Test
    public void testGetOpenAllCountForWorkContent() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
        buildAlert(DESCRIPTION, DETAILS);
        // should not appear in count
        buildAlert(DESCRIPTION, DETAILS, workContextOtherProperty, SCORE);
        buildSystemException();
        // buildException();
        List<Integer> alertCount = service.getOpenAllCountForWorkContext();
        assertNotNull(alertCount);
        assertTrue(alertCount.size() >= 3);
        assertEquals(new Integer(1), alertCount.get(0));
        assertEquals(new Integer(1), alertCount.get(1));
        assertEquals(new Integer(0), alertCount.get(2));
    }

    private Property getTestProperty() {
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        property.setId(6);
        property.setCode("test");
        property.setName("test");
        property.setDisplayLabelField("test");
        Client client = new Client();
        client.setCode("BSTN");
        property.setClient(client);
        return property;
    }

    @Test
    public void testGetOpenAllCountForWorkContextMultipleAlertsOneSystemExceptionAndNoExceptions() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
        buildAlert(DESCRIPTION, DETAILS);
        // should not appear in count
        buildAlert(DESCRIPTION, DETAILS, workContextOtherProperty, SCORE);
        buildSystemException();
        buildSystemException();
        List<Integer> alertCount = service.getOpenAllCountForWorkContext();
        assertNotNull(alertCount);
        assertTrue(alertCount.size() >= 3);
        assertEquals(new Integer(1), alertCount.get(0));
        assertEquals(new Integer(2), alertCount.get(1));
        assertEquals(new Integer(0), alertCount.get(2));
    }

    @Test
    public void testGetOpenAllCountForClient() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
        buildAlert(DESCRIPTION, DETAILS);
        buildAlert(DESCRIPTION, DETAILS, workContextOtherProperty, SCORE);
        buildSystemException();
        // buildException();
        List<Integer> alertCount = service.getOpenAllCountForClient();
        assertNotNull(alertCount);
        assertTrue(alertCount.size() >= 3);
        assertEquals(new Integer(2), alertCount.get(0));
        assertEquals(new Integer(1), alertCount.get(1));
        assertEquals(new Integer(0), alertCount.get(2));
    }

    @Test
    public void testGetOpenAllCountForClientMultipleAlertsOneSystemExceptionAndNoExceptions() throws Exception {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
        buildAlert(DESCRIPTION, DETAILS);
        buildAlert(DESCRIPTION, DETAILS, workContextOtherProperty, SCORE);
        buildSystemException();
        buildSystemException();
        List<Integer> alertCount = service.getOpenAllCountForClient();
        assertNotNull(alertCount);
        assertTrue(alertCount.size() >= 3);
        assertEquals(new Integer(2), alertCount.get(0));
        assertEquals(new Integer(2), alertCount.get(1));
        assertEquals(new Integer(0), alertCount.get(2));
    }

    @Test
    public void testGetAllAlertTypes() {
        List<InfoMgrTypeEntity> listAlertType = service.getAllAlertTypes();
        assertTrue(listAlertType.size() > 0);
    }

    @Test
    public void testSearchRaisedAlertsWithCriteria() {
        authService.setUserGlobalDBService(mockUserGlobalDBService);
        inject(userService, "userGlobalDBService", mockUserGlobalDBService);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);
        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(PROPERTY_ID_PARIS);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        // insertin in 45, 55, 50 order to test sorting
        buildAlert("Something", "details here", workContext, SCORE - 5, AlertType.UnassignedMarketSegment);
        buildAlert(DESCRIPTION_2, DETAILS_2, workContext, SCORE + 5, AlertType.NewRateCodeForMarketSegment);
        buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, TYPE);
        buildAlert("Something2", "details here2", workContext, 101, AlertType.LRVExceedingCeilingBAR);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();
        List<Integer> scores = new ArrayList<>();
        scores.add(ScoreRange.BLUE.getId());
        searchCriteria.setScores(scores);
        // set display label field to expected mocked properties
        for (Property target_property : this.properties) {
            target_property.setDisplayLabelField(target_property.getName());
        }
        List<Alert> foundAlerts = service.searchAlertsWithCriteria(searchCriteria);
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(3, foundAlerts.size());
        assertEquals(SCORE + 5, foundAlerts.get(0).getScore());
        assertEquals(SCORE, foundAlerts.get(1).getScore());
        assertEquals(SCORE - 5, foundAlerts.get(2).getScore());
        assertTrue(foundAlerts.get(0).getScore() <= 100);
        assertSame(foundAlerts.get(0).getType(), AlertType.NewRateCodeForMarketSegment);
        assertTrue(foundAlerts.get(1).getScore() <= 100);
        assertSame(foundAlerts.get(1).getType(), TYPE);
        assertTrue(foundAlerts.get(2).getScore() <= 100);
        assertSame(foundAlerts.get(2).getType(), AlertType.UnassignedMarketSegment);
        assertNotNull(foundAlerts.get(0).getPropertyName());
        assertNotNull(foundAlerts.get(0).getPropertyCode());
        assertNotNull(foundAlerts.get(0).getPropertyDisplayLabelField());
    }

    @Test
    public void testCRSAlertsOnTop() {
        UserService userService = Mockito.mock(UserService.class);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "userService", userService);
        inject(authService, "roleService", roleService);
        inject(service, "userService", userService);
        authService.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);
        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(PROPERTY_ID_PARIS);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        // insertin in 45, 55, 50 order to test sorting
        buildAlert("Something", "details here", workContext, SCORE - 5, AlertType.UnassignedMarketSegment);
        buildAlert(DESCRIPTION_2, DETAILS_2, workContext, SCORE + 5, AlertType.NewRateCodeForMarketSegment);
        buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, TYPE);
        buildAlert("Something2", "details here2", workContext, 2, AlertType.CRSDataDidNotArrive);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();
        List<Integer> scores = new ArrayList<>();
        scores.add(ScoreRange.BLUE.getId());
        searchCriteria.setScores(scores);
        List<Alert> foundAlerts = service.searchAlertsWithCriteria(searchCriteria);
        assertEquals(4, foundAlerts.size());
        assertEquals(2, foundAlerts.get(0).getScore());
        assertEquals(SCORE + 5, foundAlerts.get(1).getScore());
        assertEquals(SCORE, foundAlerts.get(2).getScore());
        assertEquals(SCORE - 5, foundAlerts.get(3).getScore());
        assertTrue(foundAlerts.get(0).getScore() <= 100);
        assertSame(foundAlerts.get(0).getType(), AlertType.CRSDataDidNotArrive);
        assertTrue(foundAlerts.get(1).getScore() <= 100);
        assertSame(foundAlerts.get(1).getType(), AlertType.NewRateCodeForMarketSegment);
        assertTrue(foundAlerts.get(2).getScore() <= 100);
        assertSame(foundAlerts.get(2).getType(), TYPE);
        assertTrue(foundAlerts.get(3).getScore() <= 100);
        assertSame(foundAlerts.get(3).getType(), AlertType.UnassignedMarketSegment);
    }

    @Test
    public void getAlertById() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        Alert alert1 = service.getAlertById(alert.getAlertId(), alert.getPropertyId());
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(alert.getPropertyId(), alert1.getPropertyId());
        assertEquals(alert.getAlertId(), alert1.getAlertId());
    }

    @Test
    public void testGetAlertByIdNoAlertFound() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        Alert fetchedAlert = service.getAlertById(-1, alert.getPropertyId());
        assertNull(fetchedAlert);
    }

    @Test
    public void testGetStepsOrder() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity entity = (InfoMgrInstanceEntity) multiPropertyCrudService().findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID_PARIS, InfoMgrInstanceEntity.FIND_BY_ID, QueryParameter.with("id", alert.getAlertId()).parameters());
        List<InfoMgrInstanceStepStateEntity> stepEntities = entity.getInfoMgrInstanceStepStateEntities();
        assertEquals(6, stepEntities.size());
        assertEquals(3, entity.getInfoMgrInstanceStepStateEntities().get(0).getStep().getId().intValue());
        assertEquals(1, entity.getInfoMgrInstanceStepStateEntities().get(0).getStep().getOrdinal().intValue());
        assertEquals(150, entity.getInfoMgrInstanceStepStateEntities().get(3).getStep().getId().intValue());
        assertEquals(5, entity.getInfoMgrInstanceStepStateEntities().get(3).getStep().getOrdinal().intValue());
    }

    private Property createPropertyForSorting(String name) {
        Property property = new Property();
        property.setDisplayLabelField(name);
        return property;
    }

    @Test
    public void getMasterClassForProperty() {
        AccomClass objAccomClass = service.getMasterClassForProperty(PROPERTY_ID_PARIS);
        assertNotNull(objAccomClass);
    }

    @Test
    public void testGetStepById() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrStepsEntity step = service.getStepById(PROPERTY_ID_PARIS, alert.getSteps().get(0).getStepId());
        assertNotNull(step);
        assertEquals(1, step.getStepType());
    }

    @Test
    public void testSearchRaisedAlertsWithCriteriaNew() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H1", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        buildAlert(DESCRIPTION, DETAILS);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();
        List<Integer> scores = new ArrayList<>();
        scores.add(ScoreRange.BLUE.getId());
        searchCriteria.setScores(scores);
        // set display label field to expected mocked properties
        for (Property target_property : this.properties) {
            target_property.setDisplayLabelField(target_property.getName());
        }
        List<Alert> foundAlerts = service.getAlertWithSearchCriteria(searchCriteria);
        assertEquals(1, foundAlerts.size());
        assertNotNull(foundAlerts.get(0).getPropertyName());
        assertNotNull(foundAlerts.get(0).getPropertyCode());
        assertNotNull(foundAlerts.get(0).getPropertyDisplayLabelField());
    }

    @Test
    public void testSortOnScoreDesc() {
        List<InfoMgrExcepNotifEntity> listOfExceptions = new ArrayList<>();
        listOfExceptions.add(createDummyInfoMgrExcepNotifData(Constants.ALERT_STATUS_NEW_ID, AlertType.ForecastChangeEx.toString(), DateUtil.getDate(1, 1, 2010), 30));
        listOfExceptions.add(createDummyInfoMgrExcepNotifData(Constants.ALERT_STATUS_NEW_ID, AlertType.ForecastChangeEx.toString(), DateUtil.getDate(1, 1, 2011), 20));
        listOfExceptions.add(createDummyInfoMgrExcepNotifData(Constants.ALERT_STATUS_NEW_ID, AlertType.ForecastChangeEx.toString(), DateUtil.getDate(1, 1, 2012), 40));
        service.sortOnScoreDesc(listOfExceptions);
        assertEquals(40, listOfExceptions.get(0).getScore());
        assertEquals(30, listOfExceptions.get(1).getScore());
        assertEquals(20, listOfExceptions.get(2).getScore());
    }

    @Test
    public void testGetInstancesSortedOnScore() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H1", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        buildAlert(DESCRIPTION, DETAILS);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();
        List<Integer> scores = new ArrayList<Integer>();
        scores.add(ScoreRange.BLUE.getId());
        searchCriteria.setScores(scores);
        List<InfoMgrExcepNotifEntity> foundAlerts = service.getInstancesSortedOnScore(searchCriteria, Constants.ALERT_CATEGORY);
        assertEquals(1, foundAlerts.size());
    }

    @Test
    public void testGetInstancesByCreatedDate() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H1", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();

        Date createdStartDate = DateUtil.addDaysToDate(alert.getCreatedTime().getTime(), -5);
        Date createdEndDate = DateUtil.addDaysToDate(alert.getCreatedTime().getTime(), 1);
        searchCriteria.setCreationStartDate(new DateParameter(createdStartDate));
        searchCriteria.setCreationEndDate(new DateParameter(createdEndDate));
        List<InfoMgrExcepNotifEntity> foundAlerts = service.getInstancesSortedOnScore(searchCriteria, Constants.ALERT_CATEGORY);
        assertEquals(1, foundAlerts.size());
    }

    @Test
    public void testGetNoInstancesByCreatedDate() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        ParameterValue paramValue = new ParameterValue();
        paramValue.setValue("America/Chicago");
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H1", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(paramValue);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();

        Date createdStartDate = DateUtil.addDaysToDate(alert.getCreatedTime().getTime(), 1);
        Date createdEndDate = DateUtil.addDaysToDate(alert.getCreatedTime().getTime(), 3);
        searchCriteria.setCreationStartDate(new DateParameter(createdStartDate));
        searchCriteria.setCreationEndDate(new DateParameter(createdEndDate));
        List<InfoMgrExcepNotifEntity> foundAlerts = service.getInstancesSortedOnScore(searchCriteria, Constants.ALERT_CATEGORY);
        assertEquals(0, foundAlerts.size());
    }

    private InfoMgrExcepNotifEntity createDummyInfoMgrExcepNotifData(Integer alertStatusId, String alertTypeName, Date occupancyDate, Integer score) {
        InfoMgrExcepNotifEntity excepNotifEntity = new InfoMgrExcepNotifEntity();
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        excepNotifEntity.setAlertStatus(tenantCrudService().find(InfoMgrStatusEntity.class, alertStatusId));
        excepNotifEntity.setSubType(objExceptionSubTypeEntity);
        excepNotifEntity.setAlertType(tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", alertTypeName).parameters()));
        excepNotifEntity.setStatusId(1);
        excepNotifEntity.setCreatedBy("testing");
        excepNotifEntity.setScore(score);
        excepNotifEntity.setDescription("A test user exception instance");
        excepNotifEntity.setDetails("with some but not many details");
        excepNotifEntity.setPropertyId(workContext.getPropertyId());
        excepNotifEntity.setOccupancyDate(occupancyDate);
        excepNotifEntity.setLastModificationDate(new Date());
        return excepNotifEntity;
    }

    @Test
    public void testReducedStepsWithRatePlanConfigurationTrue() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        List<InfoMgrStepsEntity> step = tenantCrudService().findByNamedQuery(InfoMgrStepsEntity.BY_TYPE_AND_ORDINAL, QueryParameter.with("typeId", 5).and("ordinal", 5).parameters());
        List<InfoMgrStepsEntity> reducedSteps = service.getReducedSteps(objAlertEntity, Collections.singletonList(step.get(0)));
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(1, reducedSteps.size());
    }

    @Test
    public void testReducedStepsWithRatePlanConfigurationFalse() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(false);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        List<InfoMgrStepsEntity> step = tenantCrudService().findByNamedQuery(InfoMgrStepsEntity.BY_TYPE_AND_ORDINAL, QueryParameter.with("typeId", 5).and("ordinal", 5).parameters());
        List<InfoMgrStepsEntity> reducedSteps = service.getReducedSteps(objAlertEntity, Collections.singletonList(step.get(0)));
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(0, reducedSteps.size());
    }

    private void excludeTest(int userSelection, int firstStepOrdinal) {
        List<InfoMgrStepsEntity> stepList = getFullStepList();
        InfoMgrInstanceEntity alertEntity = getAlertEntity();
        inject(service, "bookedDataService", mockBookedDataService);
        when(mockBookedDataService.getUserSelectedOptionToResolveAlert(AlertType.SufficientBookedRTDataAvailable)).thenReturn(userSelection);
        List<InfoMgrStepsEntity> reducedSteps = service.getReducedSteps(alertEntity, stepList);
        assertEquals(1, reducedSteps.size());
        assertEquals(firstStepOrdinal, reducedSteps.get(0).getOrdinal().intValue());
    }

    @Test
    public void shouldExcludeStepToReturnToTwoWayIfUserSelectedToStayInSameStageWhileResolvingSufficientBookedRTAlert() {
        excludeTest(1, 1);
    }

    @Test
    public void shouldExcludeStepToJustReviewIfUserSelectedToMoveToOneWayWhileResolvingSufficientBookedRTAlert() {
        excludeTest(2, 2);
    }

    private InfoMgrInstanceEntity getAlertEntity() {
        InfoMgrInstanceEntity alertEntity = new InfoMgrInstanceEntity();
        InfoMgrTypeEntity typeEntity = new InfoMgrTypeEntity();
        typeEntity.setName(AlertType.BookedDataUsageStarted.name());
        alertEntity.setAlertType(typeEntity);
        return alertEntity;
    }

    private List<InfoMgrStepsEntity> getFullStepList() {
        List<InfoMgrStepsEntity> stepList = new ArrayList<>();
        for (int i = 1; i < 3; i++) {
            InfoMgrStepsEntity entity = new InfoMgrStepsEntity();
            entity.setOrdinal(i);
            stepList.add(entity);
        }
        return stepList;
    }

    @Test
    public void testCreateSteps_UNASSIGNEDRoomType_RatePlanConfigurationEnabled_False() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(false);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        List<InfoMgrInstanceStepStateEntity> steps = service.createSteps(objAlertEntity);
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(5, steps.size());
    }

    @Test
    public void getAlertStepsTest() throws Exception {
        ExceptionConfigService exceptionConfigService = new ExceptionConfigService();
        exceptionConfigService.crudService = tenantCrudService();
        exceptionConfigService.setGlobalCrudService(globalCrudService());
        exceptionConfigService.setMultiPropertyCrudService(multiPropertyCrudService());
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(CREATED_BY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode("");
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        List<InfoMgrStepsEntity> alertStepsByConfig = service.getAlertSteps(resultList.get("SUCCESS").get(0));
        assertEquals(alertStepsByConfig.size(), 8);
    }

    @Test
    public void testCreateSteps_UNASSIGNEDRoomType_RatePlanConfigurationEnabled_True() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS);
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        List<InfoMgrInstanceStepStateEntity> steps = service.createSteps(objAlertEntity);
        verify(mockAlertConfigService, times(2)).getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());
        assertEquals(6, steps.size());
    }

    @Test
    public void testCreateMissingRoomTypesInHotelDataAlertInPopulation() {
        String description = "Desc";
        String details = "Details";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.MissingRoomTypesInHotelData);
        assertNotNull(alert);
        assertEquals(AlertType.MissingRoomTypesInHotelData, alert.getType());
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreatesNewRateCodeForMarketSegmentInPopulation() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.NewRateCodeForMarketSegment);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.NewRateCodeForMarketSegment);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreatesInvalidatedScheduledTwoWayDateAlertInDataCapture() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(10);
        Property property = new Property();
        property.setStage(Stage.DATA_CAPTURE);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, DESCRIPTION, DETAILS, AlertType.ScheduledDecisionDeliveryDateCancelled);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.ScheduledDecisionDeliveryDateCancelled);
        assertEquals(DESCRIPTION, alert.getDescription());
        assertEquals(DETAILS, alert.getDetails());
    }

    @Test
    public void testCreatePMSMigrationNewIntegrationDataValidationAlertInPopulation() {
        String description = "Desc";
        String details = "Details";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.PMSMigrationNewIntegrationDataValidation);
        assertNotNull(alert);
        assertEquals(AlertType.PMSMigrationNewIntegrationDataValidation, alert.getType());
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreatePMSMigrationSystemDataValidationAlertInPopulation() {
        String description = "Desc";
        String details = "Details";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.PMSMigrationSystemDataValidation);
        assertNotNull(alert);
        assertEquals(AlertType.PMSMigrationSystemDataValidation, alert.getType());
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreateRoomTypeOffsetConfigurationAlert() {
        // GIVEN
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity entity = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.RoomTypeOffsetConfigurationExpiring.name()).parameters());
        Property property = new Property();
        property.setStage(Stage.TWO_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        // WHEN
        Alert alert = service.createAlert(workContext, entity, description, details, AlertType.RoomTypeOffsetConfigurationExpiring);
        // THEN
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.RoomTypeOffsetConfigurationExpiring);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreatesUnassignedMarketSegmentInPopulationWhenAmsEnabled() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.UnassignedMarketSegment);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.UnassignedMarketSegment);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void testCreatesUnassignedMarketSegmentInPopulationWhenAmsDisabled() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.UnassignedMarketSegment);
        assertNull(alert);
    }

    @Test
    public void testPropertyTimeZone() {
        Property property = properties.get(0);
        ParameterValue parameterValue = new ParameterValue();
        String mountainTimeZone = "US/Mountain";
        parameterValue.setValue(mountainTimeZone);
        when(mockAlertConfigService.getParameterValue(service.getNodeName(property.getClient().getCode(), property.getCode()).toString(), IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("US/Mountain");
        TimeZone propertyTimeZone = service.getPropertyTimeZone(property);
        TimeZone expectedTimeZone = TimeZone.getTimeZone(mountainTimeZone);
        assertEquals(expectedTimeZone, propertyTimeZone);
    }

    @Test
    public void getExtendedDetailsForInstanceTest() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        inject(userService, "userGlobalDBService", mockUserGlobalDBService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        authService.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        String description = "Test";
        String details = "alert.occupancy.date 2017-01-01</B><BR/> alert.ooo.adjusted CR1 alert.from 0&nbsp; alert.to 1&nbsp;<BR/> alert.ooo.adjusted CR2 alert.from 2&nbsp; alert.to 4&nbsp;<BR/> alert.occupancy.date 2017-01-02</B><BR/> alert.ooo.adjusted CR2 alert.from 3&nbsp; alert.to 6&nbsp;<BR/> alert.ooo.adjusted CR3 alert.from 4&nbsp; alert.to 8&nbsp;<BR/>";
        Alert oldAlert = buildAlert(description, details, workContext, SCORE, AlertType.CROOONotUpdated);
        Alert newAlert = service.getExtendedDetailsForInstance(oldAlert);
        assertEquals(description, newAlert.getDescription());
        String newDetails = "alert.occupancy.date 01-Jan-2017</B><BR/> alert.ooo.adjusted CR1 alert.from 0&nbsp; alert.to 1&nbsp;<BR/> alert.ooo.adjusted CR2 alert.from 2&nbsp; alert.to 4&nbsp;<BR/> alert.occupancy.date 02-Jan-2017</B><BR/> alert.ooo.adjusted CR2 alert.from 3&nbsp; alert.to 6&nbsp;<BR/> alert.ooo.adjusted CR3 alert.from 4&nbsp; alert.to 8&nbsp;<BR/>";
        assertEquals(newDetails, newAlert.getDetails());
    }

    private Step createStepWithText(String text) {
        Step step = new Step();
        step.setText(text);
        return step;
    }

    private InfoMgrInstanceEntity createMockAlertEntity() {
        InfoMgrInstanceEntity mockAlertEntity = new InfoMgrInstanceEntity();
        InfoMgrTypeEntity mockAlertType = new InfoMgrTypeEntity();
        mockAlertType.setName(AlertType.LowestBarFplosCheckFail.name());
        mockAlertEntity.setAlertType(mockAlertType);
        mockAlertEntity.setDetails(StatisticalOutlierAlertConstants.DECISION_JOB_ID_KEY + ":" + 123);
        return mockAlertEntity;
    }

    private InfoMgrStepsEntity createMockStepEntity() {
        InfoMgrStepsEntity mockStepEntity = new InfoMgrStepsEntity();
        mockStepEntity.setText(StatisticalOutlierAlertConstants.IGNORE_ONCE_UPLOAD_DECISIONS);
        return mockStepEntity;
    }

    @Test
    public void testDoAlertStep_SufficientBookedDataAvailableAlertOneWay() {
        Alert alert = setMocks(Stage.ONE_WAY, AlertType.SufficientBookedRTDataAvailable);
        service.doAlertStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
        List<Step> stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        assertEquals(1, stepList.size());
        assertEquals(true, stepList.get(0).isActioned());
    }

    @Test
    public void testDoAlertStep_SufficientBookedDataAvailableAlertTwoWay() {
        Alert alert = setMocks(Stage.TWO_WAY, AlertType.SufficientBookedRTDataAvailable);
        service.doAlertStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
        List<Step> stepList = service.getSteps(alert.getAlertId(), alert.getPropertyId());
        assertEquals(2, stepList.size());
        assertEquals(true, stepList.get(0).isActioned());
        assertEquals(false, stepList.get(1).isActioned());
    }

    private Alert setMocks(Stage stage, AlertType alertType) {
        Property property = new Property();
        property.setStage(stage);
        property.setId(5);
        when(mockPropertyService.getPropertyById(getPropertyId())).thenReturn(property);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, 500, alertType);
        inject(service, "crudService", tenantCrudService());
        return alert;
    }

    @Test
    public void testCreatesNewRateCodeForMarketSegmentInPopulation_testCoverage() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlertWithNewTransaction(workContext, alertTypeEntity, description, details, AlertType.NewRateCodeForMarketSegment);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.NewRateCodeForMarketSegment);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void updateMigrationStateAndCreateAlertWithNewTransaction() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        mockAlertAndPropertyService();
        GlobalCrudServiceBean globalCrudServiceBean = mock(GlobalCrudServiceBean.class);
        service.setGlobalCrudService(globalCrudServiceBean);
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        Alert alert = service.updateMigrationStateAndCreateAlertWithNewTransaction(workContext, alertTypeEntity, description, details, AlertType.ConfigureOutbound, pmsMigrationConfig);
        verify(globalCrudServiceBean).save(pmsMigrationConfig);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.ConfigureOutbound);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    @Test
    public void updateRecodingStateAndCreateAlertWithNewTransaction() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        mockAlertAndPropertyService();
        GlobalCrudServiceBean globalCrudServiceBean = mock(GlobalCrudServiceBean.class);
        service.setGlobalCrudService(globalCrudServiceBean);
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        Alert alert = service.generateAlertAndUpdateRecodingState(workContext, alertTypeEntity, description, details, AlertType.PMSMigrationCCFGValidation, mktSegRecodingConfig);
        verify(globalCrudServiceBean).save(mktSegRecodingConfig);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.PMSMigrationCCFGValidation);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }


    @Test
    public void testGetOpenAlertsForSystemUser() {
        ArrayList<Integer> propertyIds = new ArrayList<>();
        AlertType alertType = AlertType.CRSDataDidNotArrive;
        List<Alert> openAlertsForSystemUser = service.getOpenAlertsForSystemUser(propertyIds, alertType);
        assertNotNull(openAlertsForSystemUser);
        assertEquals(0, openAlertsForSystemUser.size());
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        propertyIds.add(TestProperty.H1.getId());
        InfoMgrInstanceEntity mockedAlertInstanceEntity = getMockedAlertInstanceEntity(TestProperty.H1.getId(), alertType);
        when(mockedBean.findByNamedQueryUnionAcrossProperties(propertyIds, InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds).and("type", alertType.toString()).and("category", Constants.ALERT_CATEGORY).parameters())).thenReturn(Arrays.asList(mockedAlertInstanceEntity));
        openAlertsForSystemUser = service.getOpenAlertsForSystemUser(propertyIds, alertType);
        assertNotNull(openAlertsForSystemUser);
        assertEquals(1, openAlertsForSystemUser.size());
    }

    @Test
    public void testGetAlertById() {
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        GlobalCrudServiceBean globalCrudServiceBean = mock(GlobalCrudServiceBean.class);
        service.setGlobalCrudService(globalCrudServiceBean);
        setWorkContextProperty(TestProperty.H2);
        Integer propertyId = TestProperty.H2.getId();
        int alertId = 1;
        when(mockedBean.findByNamedQuerySingleResultForSingleProperty(propertyId, InfoMgrInstanceEntity.FIND_BY_ID, QueryParameter.with("id", alertId).parameters())).thenReturn(getMockedAlertInstanceEntity(propertyId, AlertType.CRSDataDidNotArrive));
        when(globalCrudServiceBean.find(Property.class, propertyId)).thenReturn(getMockedProperty());
        service.getAlertById(alertId);
    }

    @Test
    public void testSuspendOrRevertRaisedAlert_NotSuspended() {
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        Integer alertId = 1;
        Integer propertyId = 5;
        Alert alert = new Alert();
        alert.setAlertId(1);
        alert.setPropertyId(propertyId);
        Integer stepId = 2;
        Step step = new Step();
        step.setLabel(AlertStepType.SUSPEND.name());
        step.setStepId(stepId);
        InfoMgrTypeEntity mockedAlertType = getMockedAlertType(TYPE);
        InfoMgrStatusEntity alertSuspendedStatus = new InfoMgrStatusEntity();
        alertSuspendedStatus.setName(Constants.SUSPENDED_STATUS);
        InfoMgrInstanceEntity objAlert = new InfoMgrInstanceEntity();
        objAlert.setId(1);
        objAlert.setPropertyId(propertyId);
        objAlert.setDetails("test");
        objAlert.setAlertType(mockedAlertType);
        objAlert.setAlertStatus(alertSuspendedStatus);
        InfoMgrStatusEntity statusActioned = new InfoMgrStatusEntity();
        statusActioned.setId(1);
        statusActioned.setName("test");
        statusActioned.setDescription("test");
        Integer typeId = 9;
        InfoMgrHistoryTypeEntity infoMgrHistoryTypeEntity = new InfoMgrHistoryTypeEntity();
        infoMgrHistoryTypeEntity.setName("test");
        infoMgrHistoryTypeEntity.setDescription("test");
        infoMgrHistoryTypeEntity.setId(typeId);
        when(mockedBean.find(propertyId, InfoMgrInstanceEntity.class, alertId)).thenReturn(objAlert);
        when(mockedBean.find(objAlert.getPropertyId(), InfoMgrStatusEntity.class, Constants.ALERT_STATUS_VIEWED_ID)).thenReturn(statusActioned);
        when(mockedBean.save(objAlert)).thenReturn(objAlert);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        when(mockedBean.find(alert.getPropertyId(), InfoMgrHistoryTypeEntity.class, typeId)).thenReturn(infoMgrHistoryTypeEntity);
        service.suspendOrRevertRaisedAlert(alertId, propertyId, stepId, false);
        objAlert.setAlertStatus(statusActioned);
        objAlert.setLastModificationDate(new Date());
        verify(mockedBean, times(1)).save(objAlert);
        assertEquals(objAlert.getAlertType().getBaseScore(), objAlert.getScore());
    }

    @Test
    public void testSuspendOrRevertRaisedAlert_Suspended() {
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        Integer alertId = 1;
        Integer propertyId = 5;
        Alert alert = new Alert();
        alert.setAlertId(1);
        alert.setPropertyId(propertyId);
        Integer stepId = 2;
        Step step = new Step();
        step.setLabel(AlertStepType.SUSPEND.name());
        step.setStepId(stepId);
        InfoMgrInstanceEntity objAlert = new InfoMgrInstanceEntity();
        objAlert.setId(1);
        objAlert.setPropertyId(propertyId);
        objAlert.setDetails("test");
        InfoMgrStatusEntity statusSuspended = new InfoMgrStatusEntity();
        statusSuspended.setId(1);
        statusSuspended.setName("test");
        statusSuspended.setDescription("test");
        Integer typeId = 8;
        InfoMgrHistoryTypeEntity infoMgrHistoryTypeEntity = new InfoMgrHistoryTypeEntity();
        infoMgrHistoryTypeEntity.setName("test");
        infoMgrHistoryTypeEntity.setDescription("test");
        infoMgrHistoryTypeEntity.setId(typeId);
        when(mockedBean.find(propertyId, InfoMgrInstanceEntity.class, alertId)).thenReturn(objAlert);
        when(mockedBean.find(objAlert.getPropertyId(), InfoMgrStatusEntity.class, Constants.ALERT_STATUS_SUSPENDED_ID)).thenReturn(statusSuspended);
        when(mockedBean.save(objAlert)).thenReturn(objAlert);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        when(mockedBean.find(alert.getPropertyId(), InfoMgrHistoryTypeEntity.class, typeId)).thenReturn(infoMgrHistoryTypeEntity);
        service.suspendOrRevertRaisedAlert(alertId, propertyId, stepId, true);
        objAlert.setAlertStatus(statusSuspended);
        objAlert.setScore(0);
        objAlert.setLastModificationDate(new Date());
        verify(mockedBean, times(1)).save(objAlert);
    }

    @Test
    public void resolveUnassignedRoomTypeAlertsTest_ratePlanConfigEnabled_noUnassignedRoomTypes() {
        CrudService crudService = mock(TenantCrudServiceBean.class);
        PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
        inject(service, "crudService", crudService);
        inject(service, "configService", configParamsService);
        when(crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", TestProperty.H2.getId()).parameters())).thenReturn(new ArrayList());
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        service.resolveUnassignedRoomTypeAlerts();
        verify(crudService, never()).save(any(InfoMgrInstanceEntity.class));
    }

    @Test
    public void resolveUnassignedRoomTypeAlertsTest_ratePlanConfigEnabled_hasUnassignedRoomTypeWithCapacity() {
        CrudService crudService = mock(TenantCrudServiceBean.class);
        PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
        inject(service, "crudService", crudService);
        inject(service, "configService", configParamsService);
        AccomType unassignedAccomType = new AccomType();
        unassignedAccomType.setAccomTypeCapacity(100);
        when(crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", TestProperty.H2.getId()).parameters())).thenReturn(Arrays.asList(unassignedAccomType));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        InfoMgrInstanceEntity unresolvedAlert = new InfoMgrInstanceEntity();
        unresolvedAlert.setScore(500);
        InfoMgrStepsEntity step1 = new InfoMgrStepsEntity();
        step1.setId(100);
        InfoMgrStepsEntity step2 = new InfoMgrStepsEntity();
        step2.setId(UNQUALIFIED_RATE_STEP_ID);
        InfoMgrInstanceStepStateEntity stepState1 = new InfoMgrInstanceStepStateEntity();
        stepState1.setActioned(true);
        stepState1.setStep(step1);
        InfoMgrInstanceStepStateEntity stepState2 = new InfoMgrInstanceStepStateEntity();
        stepState2.setActioned(false);
        stepState2.setStep(step2);
        unresolvedAlert.setInfoMgrInstanceStepStateEntities(Arrays.asList(stepState1, stepState2));
        when(crudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_UNRESOLVED_BY_TYPE, QueryParameter.with("propertyId", getPropertyId()).and("type", AlertType.UnassignedRoomType.name()).and("category", Constants.ALERT_CATEGORY).and("alertStatus", "Resolved").parameters())).thenReturn(Arrays.asList(unresolvedAlert));
        InfoMgrStatusEntity resolvedStatus = new InfoMgrStatusEntity();
        when(crudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID)).thenReturn(resolvedStatus);
        service.resolveUnassignedRoomTypeAlerts();
        verify(crudService, never()).save(any(InfoMgrInstanceEntity.class));
    }

    @Test
    public void resolveUnassignedRoomTypeAlertsTest_ratePlanConfigEnabled_hasUnassignedRoomTypeWithZeroCapacity() {
        CrudService crudService = mock(TenantCrudServiceBean.class);
        PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
        inject(service, "crudService", crudService);
        inject(service, "configService", configParamsService);
        AccomType unassignedAccomType = new AccomType();
        unassignedAccomType.setAccomTypeCapacity(0);
        when(crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", TestProperty.H2.getId()).parameters())).thenReturn(Arrays.asList(unassignedAccomType));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        InfoMgrInstanceEntity unresolvedAlert = new InfoMgrInstanceEntity();
        unresolvedAlert.setScore(500);
        InfoMgrStepsEntity step1 = new InfoMgrStepsEntity();
        step1.setId(100);
        InfoMgrStepsEntity step2 = new InfoMgrStepsEntity();
        step2.setId(UNQUALIFIED_RATE_STEP_ID);
        InfoMgrInstanceStepStateEntity stepState1 = new InfoMgrInstanceStepStateEntity();
        stepState1.setActioned(true);
        stepState1.setStep(step1);
        InfoMgrInstanceStepStateEntity stepState2 = new InfoMgrInstanceStepStateEntity();
        stepState2.setActioned(false);
        stepState2.setStep(step2);
        unresolvedAlert.setInfoMgrInstanceStepStateEntities(Arrays.asList(stepState1, stepState2));
        when(crudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_UNRESOLVED_BY_TYPE, QueryParameter.with("propertyId", getPropertyId()).and("type", AlertType.UnassignedZeroCapacityRoomType.name()).and("category", Constants.ALERT_CATEGORY).and("alertStatus", "Resolved").parameters())).thenReturn(Collections.singletonList(unresolvedAlert));
        InfoMgrStatusEntity resolvedStatus = new InfoMgrStatusEntity();
        when(crudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID)).thenReturn(resolvedStatus);
        service.resolveUnassignedRoomTypeAlerts();
        verify(crudService, never()).save(any(InfoMgrInstanceEntity.class));
    }

    @Test
    public void resolveUnassignedRoomTypeAlertsTest_ratePlanConfigEnabled_hasUnassignedRoomTypeWithCapacity_allActioned() {
        CrudService crudService = mock(TenantCrudServiceBean.class);
        PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
        inject(service, "crudService", crudService);
        inject(service, "configService", configParamsService);
        AccomType unassignedAccomType = new AccomType();
        unassignedAccomType.setAccomTypeCapacity(100);
        when(crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", TestProperty.H2.getId()).parameters())).thenReturn(Arrays.asList(unassignedAccomType));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        InfoMgrInstanceEntity unresolvedAlert = new InfoMgrInstanceEntity();
        unresolvedAlert.setScore(500);
        InfoMgrStepsEntity step1 = new InfoMgrStepsEntity();
        step1.setId(100);
        InfoMgrStepsEntity step2 = new InfoMgrStepsEntity();
        step2.setId(UNQUALIFIED_RATE_STEP_ID);
        InfoMgrInstanceStepStateEntity stepState1 = new InfoMgrInstanceStepStateEntity();
        stepState1.setActioned(true);
        stepState1.setStep(step1);
        InfoMgrInstanceStepStateEntity stepState2 = new InfoMgrInstanceStepStateEntity();
        stepState2.setActioned(true);
        stepState2.setStep(step2);
        unresolvedAlert.setInfoMgrInstanceStepStateEntities(Arrays.asList(stepState1, stepState2));
        when(crudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_UNRESOLVED_BY_TYPE, QueryParameter.with("propertyId", getPropertyId()).and("type", AlertType.UnassignedRoomType.name()).and("category", Constants.ALERT_CATEGORY).and("alertStatus", "Resolved").parameters())).thenReturn(Collections.singletonList(unresolvedAlert));
        InfoMgrStatusEntity resolvedStatus = new InfoMgrStatusEntity();
        when(crudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID)).thenReturn(resolvedStatus);
        service.resolveUnassignedRoomTypeAlerts();
        ArgumentCaptor<InfoMgrInstanceEntity> alertCaptor = ArgumentCaptor.forClass(InfoMgrInstanceEntity.class);
        verify(crudService).save(alertCaptor.capture());
        assertEquals(0, alertCaptor.getValue().getScore());
        assertEquals(resolvedStatus, alertCaptor.getValue().getAlertStatus());
    }

    @Test
    public void resolveUnassignedRoomTypeAlertsTest_ratePlanConfigDisabled_hasUnassignedRoomTypeWithCapacity() {
        CrudService crudService = mock(TenantCrudServiceBean.class);
        PacmanConfigParamsService configParamsService = mock(PacmanConfigParamsService.class);
        inject(service, "crudService", crudService);
        inject(service, "configService", configParamsService);
        AccomType unassignedAccomType = new AccomType();
        unassignedAccomType.setAccomTypeCapacity(100);
        when(crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", TestProperty.H2.getId()).parameters())).thenReturn(Collections.singletonList(unassignedAccomType));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(false);
        InfoMgrInstanceEntity unresolvedAlert = new InfoMgrInstanceEntity();
        unresolvedAlert.setScore(500);
        InfoMgrStepsEntity step1 = new InfoMgrStepsEntity();
        step1.setId(100);
        InfoMgrStepsEntity step2 = new InfoMgrStepsEntity();
        step2.setId(UNQUALIFIED_RATE_STEP_ID);
        InfoMgrInstanceStepStateEntity stepState1 = new InfoMgrInstanceStepStateEntity();
        stepState1.setActioned(true);
        stepState1.setStep(step1);
        InfoMgrInstanceStepStateEntity stepState2 = new InfoMgrInstanceStepStateEntity();
        stepState2.setActioned(false);
        stepState2.setStep(step2);
        unresolvedAlert.setInfoMgrInstanceStepStateEntities(Arrays.asList(stepState1, stepState2));
        when(crudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_UNRESOLVED_BY_TYPE, QueryParameter.with("propertyId", getPropertyId()).and("type", AlertType.UnassignedRoomType.name()).and("category", Constants.ALERT_CATEGORY).and("alertStatus", "Resolved").parameters())).thenReturn(Arrays.asList(unresolvedAlert));
        InfoMgrStatusEntity resolvedStatus = new InfoMgrStatusEntity();
        when(crudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID)).thenReturn(resolvedStatus);
        service.resolveUnassignedRoomTypeAlerts();
        ArgumentCaptor<InfoMgrInstanceEntity> alertCaptor = ArgumentCaptor.forClass(InfoMgrInstanceEntity.class);
        verify(crudService).save(alertCaptor.capture());
        assertEquals(0, alertCaptor.getValue().getScore());
        assertEquals(resolvedStatus, alertCaptor.getValue().getAlertStatus());
    }

    @Test
    public void testCreateMissingMarketSegmentsInReservationsAlertInPopulation() {
        Property property = new Property();
        property.setStage(Stage.POPULATION);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        String description = "Desc";
        String details = "CN1, Active, 2019-01-01, 2019-02-01::CN2, Active, 2019-02-01, 2019-03-01";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.MissingMarketSegmentOnReservations);
        assertNotNull(alert);
        assertEquals(AlertType.MissingMarketSegmentOnReservations, alert.getType());
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
    }

    private void mockAlertAndPropertyService() {
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
    }

    private MultiPropertyCrudServiceBean mockServices() {
        MultiPropertyCrudServiceBean mockedBean = mock(MultiPropertyCrudServiceBean.class);
        service.setMultiPropertyCrudService(mockedBean);
        inject(service, "tarsDecisionService", tarsDecisionService);
        return mockedBean;
    }

    private Property getMockedProperty() {
        Property mockedProperty = new Property();
        mockedProperty.setCode(TestProperty.H2.name());
        Client mockedClient = new Client();
        mockedClient.setCode(TestProperty.H1.getTestClient().name());
        mockedProperty.setClient(mockedClient);
        mockedProperty.setStage(Stage.TWO_WAY);
        return mockedProperty;
    }

    private InfoMgrStepsEntity getMockedStepEntity(String stepText) {
        InfoMgrStepsEntity stepsEntity = new InfoMgrStepsEntity();
        stepsEntity.setStepType(Constants.IM_ALERT_STEP_TYPE);
        stepsEntity.setText(stepText);
        return stepsEntity;
    }

    private InfoMgrHistoryTypeEntity getMockedAlertHistoryTypeEntity() {
        InfoMgrHistoryTypeEntity mockedEntity = new InfoMgrHistoryTypeEntity();
        mockedEntity.setName("dummyName");
        mockedEntity.setId(1);
        return mockedEntity;
    }

    private List<InfoMgrInstanceStepStateEntity> getMockedStepStateEntityAsList() {
        InfoMgrInstanceStepStateEntity step = new InfoMgrInstanceStepStateEntity();
        step.setId(1);
        return new ArrayList<>(Collections.singletonList(step));
    }

    private InfoMgrInstanceEntity getMockedAlertInstanceEntity(Integer propertyId, AlertType alertType) {
        InfoMgrInstanceEntity mockedEntity = new InfoMgrInstanceEntity();
        mockedEntity.setId(1);
        mockedEntity.setAlertStatus(getMockedAlertStatus(Constants.ALERT_STATUS_NEW_ID));
        mockedEntity.setDetails("dummyDescription");
        mockedEntity.setPropertyId(propertyId);
        mockedEntity.setAlertType(getMockedAlertType(alertType));
        mockedEntity.setCreateDate(new Date());
        mockedEntity.setLastModificationDate(new Date());
        return mockedEntity;
    }

    private InfoMgrTypeEntity getMockedAlertType(AlertType alertType) {
        InfoMgrTypeEntity mockedEntity = new InfoMgrTypeEntity();
        mockedEntity.setName(alertType.name());
        mockedEntity.setBaseScore(100);
        mockedEntity.setScoreIncrement(5);
        mockedEntity.setScoreDecrement(5);
        return mockedEntity;
    }

    private InfoMgrStatusEntity getMockedAlertStatus(int alertStatus) {
        InfoMgrStatusEntity mockedStatus = new InfoMgrStatusEntity();
        mockedStatus.setId(alertStatus);
        return mockedStatus;
    }

    private void setUpJobRetriggerCaseData(Long jobExecutionId, ExecutionStatus executionStatus, boolean isStaleJobExecutionId) {
        inject(service, "configParamsService", mockAlertConfigService);
        inject(service, "jobMonitorService", mockJobMonitorService);
        inject(service, "jobService", mockJobServiceLocal);
        when(mockJobMonitorService.getJobInstanceId(jobExecutionId)).thenReturn(1L);
        JobView jobView = new JobView();
        lastExecution = new JobExecution();
        lastExecution.setJobExecutionId(isStaleJobExecutionId ? jobExecutionId + 1 : jobExecutionId);
        lastExecution.setStatus(executionStatus.name());
        jobView.setJobExecutions(new ArrayList<JobExecution>() {

            {
                add(lastExecution);
            }
        });
        when(mockJobMonitorService.getJobDetail(1L)).thenReturn(jobView);
    }

    private InfoMgrStepsEntity createFPLOSAlertStep(String ignoreAlwaysUploadDecisions) {
        InfoMgrStepsEntity stepEntity = new InfoMgrStepsEntity();
        stepEntity.setText(ignoreAlwaysUploadDecisions);
        return stepEntity;
    }

    private InfoMgrInstanceEntity createFPLOSAlertInstance(AlertType alertType, String alertDetail) {
        InfoMgrInstanceEntity alertEntity = new InfoMgrExcepNotifEntity();
        InfoMgrTypeEntity infoMgrTypeEntity = new InfoMgrTypeEntity();
        infoMgrTypeEntity.setName(alertType.name());
        alertEntity.setAlertType(infoMgrTypeEntity);
        alertEntity.setDetails(alertDetail);
        return alertEntity;
    }

    private GlobalUser getInternalUser(String wcUserIdSuperDuper) {
        GlobalUser user = new GlobalUser();
        user.setInternal(true);
        user.setId(Integer.parseInt(wcUserIdSuperDuper));
        user.setClientCode(Constants.CLIENT_INTERNAL);
        return user;
    }

    private void createAnalyticalMarketSegments(List<String> mappedMarketSegmentCodes) {
        for (String mappedMarketSegmentCode : mappedMarketSegmentCodes) {
            AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
            analyticalMarketSegment.setMarketCode("MKT_TST");
            analyticalMarketSegment.setMappedMarketCode(mappedMarketSegmentCode);
            Integer propertyId = getPropertyId();
            multiPropertyCrudService().save(propertyId, analyticalMarketSegment);
        }
    }

    @Test
    public void findActiveUnresolvedNotifications() {
        inject(service, "crudService", tenantCrudService());
        setupData();
        List<InfoMgrExcepNotifEntity> result = service.findActiveUnresolvedNotifications(AlertType.UnexpectedDemandEx.getName());
        assertEquals(4, result.size());
        assertEquals("2019-01-15", result.get(0).getOccupancyDate().toString());
        assertEquals("2019-01-18", result.get(1).getOccupancyDate().toString());
        assertEquals("2019-01-20", result.get(2).getOccupancyDate().toString());
        assertEquals("2019-01-22", result.get(3).getOccupancyDate().toString());
    }


    @Test
    public void findActiveUnresolvedNotificationsWhenNotificationInSuspendMonitor() {
        inject(service, "crudService", tenantCrudService());
        tenantCrudService().executeUpdateByNativeQuery("insert into Info_Mgr_Instance values " +
                " (69,'','',6,1,1,getdate(),50,getdate(),1,'Exception','2019-01-14',14,1,0,200,200)," +
                " (69,'','',6,1,1,getdate(),50,getdate(),1,'Exception','2019-01-15',14,1,0,200,200)," +
                " (68,'','',6,1,1,getdate(),50,getdate(),1,'Exception','2019-01-16',14,1,0,200,200)," +
                " (69,'','',6,1,1,getdate(),50,getdate(),7,'Exception','2019-01-16',14,1,0,200,200)," +
                " (69,'','',6,2,1,getdate(),50,getdate(),7,'Exception','2019-01-17',14,1,0,200,200)," +
                " (69,'','',6,1,1,getdate(),50,getdate(),2,'Exception','2019-01-18',14,1,0,200,200)," +
                " (69,'','',6,1,1,getdate(),50,getdate(),4,'Exception','2019-01-19',14,1,0,200,200)");
        List<InfoMgrExcepNotifEntity> result = service.findActiveUnresolvedNotifications(AlertType.UnexpectedDemandEx.getName());
        assertEquals(3, result.size());
        assertEquals("2019-01-14", result.get(0).getOccupancyDate().toString());
        assertEquals("2019-01-15", result.get(1).getOccupancyDate().toString());
        assertEquals("2019-01-18", result.get(2).getOccupancyDate().toString());

    }


    @Test
    public void testIsDataNotPopulatedTodayTrue() {
        service.setCrudService(tenantCrudService());
        assertTrue(service.isDataNotPopulatedToday());
    }

    @Test
    public void testIsDataNotPopulatedTodayFalse() {
        service.setCrudService(tenantCrudService());
        addRSSFileMetaData();
        addFileMetaData(1);
        assertFalse(service.isDataNotPopulatedToday());
    }

    @Test
    public void testIsDataNotPopulatedTodayFalseWithCDP() {
        service.setCrudService(tenantCrudService());
        addRSSFileMetaData();
        addFileMetaData(2);
        addFileMetaData(1);
        assertFalse(service.isDataNotPopulatedToday());
    }

    private void addFileMetaData(int isBde) {
        FileMetadata fm = new FileMetadata();
        fm.setRecordTypeId(3);
        fm.setProcessStatusId(13);
        fm.setBde(isBde);
        fm.setFileName("TestFile_" + isBde);
        fm.setFileLocation("TestFileLoc_" + isBde);
        fm.setTenantPropertyId(WC_PROPERTY_ID_PARIS);
        fm.setSnapshotDt(new Date());
        fm.setSnapshotTm(new Date());
        fm.setPreparedDt(new Date());
        fm.setPreparedTm(new Date());
        fm.setCreatedate(new Date());
        tenantCrudService().save(fm);
    }

    private void addRSSFileMetaData() {
        FileMetadata fm = new FileMetadata();
        fm.setRecordTypeId(10);
        fm.setProcessStatusId(13);
        fm.setBde(1);
        fm.setFileName("RssTestFile");
        fm.setFileLocation("RssTestFileLoc");
        fm.setTenantPropertyId(WC_PROPERTY_ID_PARIS);
        fm.setSnapshotDt(new Date());
        fm.setSnapshotTm(new Date());
        fm.setPreparedDt(new Date());
        fm.setPreparedTm(new Date());
        fm.setCreatedate(new Date());
        tenantCrudService().save(fm);
    }

    private void setupData() {
        tenantCrudService().executeUpdateByNativeQuery("insert into Info_Mgr_Instance values " + " (69,'','',6,1,1,getdate(),50,getdate(),1,'Exception','2019-01-15',14,1,0,200,200)," + " (68,'','',6,1,1,getdate(),50,getdate(),1,'Exception','2019-01-16',14,1,0,200,200)," + " (69,'','',6,2,1,getdate(),50,getdate(),1,'Exception','2019-01-17',14,1,0,200,200)," + " (69,'','',6,1,1,getdate(),50,getdate(),2,'Exception','2019-01-18',14,1,0,200,200)," + " (69,'','',6,1,1,getdate(),50,getdate(),4,'Exception','2019-01-19',14,1,0,200,200)," + " (69,'','',6,1,1,getdate(),50,getdate(),6,'Exception','2019-01-20',14,1,0,200,200)," + " (69,'','',6,1,1,getdate(),50,getdate(),5,'Exception','2019-01-21',14,1,0,200,200)," + " (69,'','',6,1,1,getdate(),50,getdate(),3,'Exception','2019-01-22',14,1,0,200,200)");
    }

    @Test
    public void testUnassignedRoomType_CPEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(12, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    public void testUnassignedRoomType_CPEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(7, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    public void testUnassignedRoomType_BARByLOSEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(11, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    public void testUnassignedRoomType_BARByLOSEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(6, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    public void testUnassignedRoomType_DailyBarNonCPEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION)).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(12, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    public void testUnassignedRoomType_DailyBarNonCPEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION)).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);


        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(7, stepList.size());
        int steps = verifyUnAssignedRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    private int verifyUnAssignedRoomTypeAlertSteps(List<Step> stepList) {

        int steps = 5;
        // common steps for all flavour
        assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.type.to.a.room.class".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.the.cost.of.walk".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.overbooking.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.vendor.integration.mapping.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "resolve.alert".equals(step.getText())));

        if (mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION) != null &&
                mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION).equals(BAR_DECISION_VALUE_RATEOFDAY) &&
                !mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.offsets".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.types.to.all.products.they.are.sold".equals(step.getText())));
            assertTrue(stepList.stream().anyMatch(step -> "enter.offset.values.and.if.needed.supplements".equals(step.getText())));
            steps += 2;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.rate.plan.rate.details".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.complimentary.cross.charge.values".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.group.pricing.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.function.space.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.channel.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.component.rooms.configuration".equals(step.getText())));
            steps++;
        }

        return steps;
    }

    public String getContext() {
        return mockAlertConfigService.propertyNode(PacmanWorkContextHelper.getWorkContext());
    }

    private ClientPropertyView mockClientPropertyView(String externalSystem) {
        ClientPropertyView clientPropertyView = new ClientPropertyView();
        clientPropertyView.setExternalSystem(externalSystem);
        return clientPropertyView;
    }

    private List<String> convert(List<TentativeGroupForecastDataMismatchAlertData> actual) {
        List<String> actualData = new ArrayList<>();
        for (TentativeGroupForecastDataMismatchAlertData result : actual) {
            actualData.add(result.getGroupArrivalDate().toString());
            actualData.add(result.getGroupName());
            actualData.add(result.getWithinPmsCrsWindow());
            actualData.add(result.getPmsCrsId());
            actualData.add(result.getSncId());
            actualData.add(result.getPmsCrsIdInSnc());
            actualData.add(result.getImportance());
        }
        return actualData;
    }

    private List<String> convertRawData(List<Object[]> alertMismatchData) {
        List<String> expectedData = new ArrayList<>();
        for (Object result : alertMismatchData) {
            Object[] result1 = (Object[]) result;
            for (Object value : result1) {
                expectedData.add(value.toString());
            }
        }
        return expectedData;
    }

    private VirtualPropertyMapping virtualPropertyMapping(String externalSystem, String propertyCode) {
        VirtualPropertyMapping physicalProperty = new VirtualPropertyMapping();
        physicalProperty.setExternalSystem(externalSystem);
        physicalProperty.setPhysicalPropertyCode(propertyCode);
        return physicalProperty;
    }

    private List<Object[]> alertMismatchData() {
        List<Object[]> resultSet1 = new ArrayList<>();
        Object[] object1 = new Object[7];
        object1[0] = "2018-07-28";
        object1[1] = "0718SUNRIS_Sunrise Tours";
        object1[2] = "Yes";
        object1[3] = "301950";
        object1[4] = "";
        object1[5] = "";
        object1[6] = "High";
        resultSet1.add(object1);

        return resultSet1;
    }

    @Test
    void testUnassignedZeroCapacityRoomType_CPEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(13, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void testUnassignedZeroCapacityRoomType_CPEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(8, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void testUnassignedZeroCapacityRoomType_BARByLOSEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(12, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void testUnassignedZeroCapacityRoomType_BARByLOSEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(7, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void testUnassignedZeroCapacityRoomType_DailyBarNonCPEnable_TogglesEnabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION)).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(true);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(13, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void testUnassignedZeroCapacityRoomType_DailyBarNonCPEnable_TogglesDisabled() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION)).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)).thenReturn(false);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)).thenReturn(false);

        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, SCORE, AlertType.UnassignedZeroCapacityRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(8, stepList.size());
        int steps = verifyUnAssignedZeroCapacityRoomTypeAlertSteps(stepList);
        assertEquals(stepList.size(), steps);
    }

    @Test
    void verifyInactiveRoomTypeAlertSteps() {
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = buildAlert(DESCRIPTION, DETAILS, workContext, 100, AlertType.InactivatedRoomType);
        List<Step> stepList = alert.getSteps();

        assertEquals(2, stepList.size());
        assertTrue(stepList.stream().anyMatch(step -> "reactivate.room.type".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "do.not.activate.room.type".equals(step.getText())));
    }


    private int verifyUnAssignedZeroCapacityRoomTypeAlertSteps(List<Step> stepList){

        int steps = 6;
        // common steps for all flavour
        assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.type.to.a.room.class".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "enter.the.cost.of.walk".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.overbooking.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "review.vendor.integration.mapping.configuration".equals(step.getText())));
        assertTrue(stepList.stream().anyMatch(step -> "suspend.the.unassigned.zero.capacity.rt.alert.on.this.date".equals(step.getText()) && step.getLabel().equalsIgnoreCase(AlertStepType.SUSPEND.toString())));
        assertTrue(stepList.stream().anyMatch(step -> "resolve.alert".equals(step.getText())));

        if (mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION) != null &&
                mockAlertConfigService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION).equals(BAR_DECISION_VALUE_RATEOFDAY) &&
                !mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.offsets".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "assign.the.new.room.types.to.all.products.they.are.sold".equals(step.getText())));
            assertTrue(stepList.stream().anyMatch(step -> "enter.offset.values.and.if.needed.supplements".equals(step.getText())));
            steps += 2;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.rate.plan.rate.details".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "enter.complimentary.cross.charge.values".equals(step.getText())));
            steps++;
        }


        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.group.pricing.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.function.space.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.channel.configuration".equals(step.getText())));
            steps++;
        }

        if (mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)) {
            assertTrue(stepList.stream().anyMatch(step -> "review.component.rooms.configuration".equals(step.getText())));
            steps++;
        }

        return steps;
    }

    @Test
    void shouldShowDSTPropertyTimezoneInAlertHistory() {
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        Integer propertyId = TestProperty.H2.getId();
        int alertId = 1;
        InfoMgrHistoryEntity entity = getInfoMgrHistoryEntity(DateUtil.getDate(4, 3, 2024, 9, 0, 0));
        when(mockedBean.findByNamedQueryForSingleProperty(propertyId, InfoMgrHistoryEntity.BY_ALERT_INSTANCE_ID, QueryParameter.with("alertInstanceId", alertId).parameters())).thenReturn(List.of(entity));
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("Pacific/Auckland");
        List<AlertHistory> history = service.getHistory(alertId, propertyId);
        assertEquals(1, history.size());
        assertEquals("NZDT", history.get(0).getCreatedDateTimeZone());

        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("America/New_York");
        List<AlertHistory> history1 = service.getHistory(alertId, propertyId);
        assertEquals(1, history1.size());
        assertEquals("EDT", history1.get(0).getCreatedDateTimeZone());
    }

    @Test
    void testGetOpenAlertInstances(){
        String description = "theAlert";
        String details = "theDetails";
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        inject(service, "crudService", tenantCrudService());
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(50);
        service.createAlert(workContext, alertTypeEntity, description, details, AlertType.UnassignedMarketSegment);
        InfoMgrTypeEntity alertTypeEntity1 = new InfoMgrTypeEntity();
        alertTypeEntity1.setBaseScore(50);
        service.createAlert(workContext, alertTypeEntity1, description, details, AlertType.NewRateCodeForMarketSegment);

        List<InfoMgrInstanceEntity> openAlertInstances = service.getOpenAlertInstances(
                of(AlertType.UnassignedMarketSegment.name(), AlertType.NewRateCodeForMarketSegment.name()),
                of(NEW_STATUS), ALERT_CATEGORY);
        assertThat(openAlertInstances.size(), Is.is(2));
    }

    @Test
    void testResolveOpenLDBAlerts(){
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        inject(service, "crudService", tenantCrudService());

        Alert incompleteConfigAlert = buildAlert(DESCRIPTION, DETAILS, workContext, 150, AlertType.IncompleteLDBConfiguration);
        Alert configUnlockedAlert = buildAlert(DESCRIPTION, DETAILS, workContext, 150, AlertType.LDBConfigurationUnlocked);
        Alert approachingNormDateAlert = buildAlert(DESCRIPTION, DETAILS, workContext, 30, AlertType.ApproachingSoftOpeningDate);

        assertEquals("New",incompleteConfigAlert.getStatus());
        assertEquals("New",configUnlockedAlert.getStatus());
        assertEquals("New",approachingNormDateAlert.getStatus());

        List<Alert> alerts = service.resolveOpenLDBAlerts();

        assertEquals(3,alerts.size());
        assertEquals("Resolved",alerts.get(0).getStatus());
        assertEquals("Resolved",alerts.get(1).getStatus());
        assertEquals("Resolved",alerts.get(2).getStatus());
    }

    @Test
    void shouldShowNonDSTPropertyTimezoneInAlertHistory() {
        MultiPropertyCrudServiceBean mockedBean = mockServices();
        Integer propertyId = TestProperty.H2.getId();
        int alertId = 1;
        InfoMgrHistoryEntity entity = getInfoMgrHistoryEntity(DateUtil.getDate(9, 3, 2024, 9, 0, 0));
        when(mockedBean.findByNamedQueryForSingleProperty(propertyId, InfoMgrHistoryEntity.BY_ALERT_INSTANCE_ID, QueryParameter.with("alertInstanceId", alertId).parameters())).thenReturn(List.of(entity));
        when(mockAlertConfigService.getParameterValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn("Pacific/Auckland");
        List<AlertHistory> history = service.getHistory(alertId, propertyId);
        assertEquals(1, history.size());
        assertEquals("NZST", history.get(0).getCreatedDateTimeZone());
    }

    private InfoMgrHistoryEntity getInfoMgrHistoryEntity(Date createdDate) {
        InfoMgrHistoryEntity entity = new InfoMgrHistoryEntity();
        entity.setCreateDate(createdDate);
        entity.setAlertInstanceId(1);
        entity.setAlertStatus(getMockedAlertStatus(Constants.ALERT_STATUS_NEW_ID));
        entity.setDetailsHistory("Test");
        entity.setDescription("Test");
        entity.setCreatedBy("1");
        return entity;
    }

    @Test
    public void testGetOpenAllCount_ReturnsZeroExceptionCountWhenG3EssentialProperty() {
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);
        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(PROPERTY_ID_PARIS);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        Property property = getTestProperty();
        when(mockPropertyService.getPropertiesWithDisplayLabelFieldByIds(Collections.singletonList(PROPERTY_ID_PARIS))).thenReturn(Collections.singletonList(property));
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        when(mockAlertConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FEATURE_LICENSING_ENABLED)).thenReturn(true);
        when(licenseService.isLicenseEnabledFor(PROPERTY_ID_PARIS, LicenseFeatureConstants.INFO_MGR_EXCEPTIONS)).thenReturn(false);
        System.setProperty("pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled", "true");
        buildAlert(DESCRIPTION, DETAILS);
        buildAlert(DESCRIPTION, DETAILS, workContextOtherProperty, SCORE);
        buildSystemException();
        List<Integer> alertCount = service.getOpenAllCountForWorkContext();
        assertNotNull(alertCount);
        assertTrue(alertCount.size() >= 3);
        assertEquals(new Integer(1), alertCount.get(0));
        assertEquals(new Integer(0), alertCount.get(1));
        assertEquals(new Integer(0), alertCount.get(2));
    }

    @Test
    public void testCreateSwitchFromHotToStandardBuildAlert() {
        String description = "theAlert";
        String details = "theDetails";
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setBaseScore(40);
        Property property = new Property();
        property.setStage(Stage.ONE_WAY);
        when(mockAlertConfigService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED)).thenReturn(true);
        when(mockPropertyService.getPropertyById(PROPERTY_ID_PARIS)).thenReturn(property);
        Alert alert = service.createAlert(workContext, alertTypeEntity, description, details, AlertType.SwitchFromHotStartToStandardBuild);
        assertNotNull(alert);
        assertEquals(alert.getType(), AlertType.SwitchFromHotStartToStandardBuild);
        assertEquals(description, alert.getDescription());
        assertEquals(details, alert.getDetails());
        assertEquals(40,alert.getScore());
    }

}
