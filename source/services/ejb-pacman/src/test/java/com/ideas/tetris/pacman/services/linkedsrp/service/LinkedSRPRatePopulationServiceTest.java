package com.ideas.tetris.pacman.services.linkedsrp.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.PostingRuleEnum;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.RateDefinition;
import com.ideas.tetris.pacman.common.xml.schema.rates.v1.TypeEnum;
import com.ideas.tetris.pacman.services.accommodation.repository.AccommodationRepository;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.linkedsrp.entity.LinkedSRPAdjustments;
import com.ideas.tetris.pacman.services.linkedsrp.entity.LinkedSRPDetailsOutput;
import com.ideas.tetris.pacman.services.linkedsrp.entity.LinkedSRPHeader;
import com.ideas.tetris.pacman.services.linkedsrp.repository.IntermediateTablesRepository;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType.T2SNAP_RECORD_TYPE_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LinkedSRPRatePopulationServiceTest {

    @InjectMocks
    LinkedSRPRatePopulationService linkedSRPRatePopulationService;

    @Mock
    IntermediateTablesRepository intermediateTablesRepository;

    @Mock
    FileMetadataService fileMetadataService;

    @Mock
    QualifiedRateService qualifiedRateService;

    @Mock
    AccommodationRepository accommodationRepository;

    private static final Integer PER_ROOM_PER_STAY = 1;
    @Mock
    DateService dateService;


    @Test
    public void shouldBuildRateHeaders() {
        mockDataForBuildingRateHeaders();

        List<RateDefinition.RateHeader> rateHeaders = linkedSRPRatePopulationService.buildRateHeadersUsingLinkedSRPs();
        assertNotNull(rateHeaders);
        assertEquals(2, rateHeaders.size());
        assertEquals("AT1", rateHeaders.get(0).getAccommodationType().get(0).getName());
        assertEquals(2, rateHeaders.get(0).getAccommodationType().get(0).getRateDetail().size());
        assertEquals(2, rateHeaders.get(1).getAccommodationType().size());

        assertEquals(rateHeaders.get(1).getRateAdjustment().size(), 1);
        assertEquals(rateHeaders.get(0).getRateAdjustment().size(), 1);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableValue().size(), 1);
        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().size(), 1);
        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableCost().size(), 2);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableCost().size(), 2);

        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableCost().get(0).getRule(), PostingRuleEnum.PER_ROOM_PER_STAY);
        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableCost().get(0).getValue(), BigDecimal.TEN);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableCost().get(0).getRule(), PostingRuleEnum.PER_ROOM_PER_STAY);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableCost().get(0).getValue(), BigDecimal.ONE);

        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().get(0).getRule(), PostingRuleEnum.PER_ROOM_PER_NIGHT);
        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().get(0).getType(), TypeEnum.AMOUNT);
        assertEquals(rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().get(0).getValue(), BigDecimal.TEN);

        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableValue().get(0).getRule(), PostingRuleEnum.PER_ROOM_PER_NIGHT);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableValue().get(0).getType(), TypeEnum.PERCENTAGE);
        assertEquals(rateHeaders.get(1).getRateAdjustment().get(0).getYieldableValue().get(0).getValue(), BigDecimal.TEN.add(BigDecimal.TEN));

    }

    @Test
    public void shouldBuildRateHeadersWithNoYieldableValue() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(30);
        LinkedSRPHeader header1 = getHeader(1, startDate, endDate, "SRP1", "LV0", BigDecimal.TEN, "A");

        LinkedSRPDetailsOutput details1 = getDetail(1, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN);
        LinkedSRPDetailsOutput details2 = getDetail(2, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN.add(BigDecimal.TEN));

        when(intermediateTablesRepository.getAllHeaders()).thenReturn(Arrays.asList(header1));
        Map<Integer, List<LinkedSRPDetailsOutput>> outputDetails = new HashMap<>();
        when(intermediateTablesRepository.getOutputDetailsForHeader(header1.getId())).thenReturn(Arrays.asList(details1, details2));
        when(intermediateTablesRepository.getAdjustmentsForAllHeaders()).thenReturn(new HashMap<>());
        List<RateDefinition.RateHeader> rateHeaders = linkedSRPRatePopulationService.buildRateHeadersUsingLinkedSRPs();
        assertEquals(1, rateHeaders.get(0).getRateAdjustment().size());
        assertEquals(1, rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().size());
        assertEquals(0, rateHeaders.get(0).getRateAdjustment().get(0).getYieldableCost().size());
    }

    @Test
    public void shouldBuildRateHeadersWithNoRateAdjustment() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(30);
        LinkedSRPHeader header1 = getHeader(1, startDate, endDate, "SRP1", "LV0", BigDecimal.TEN, "A");
        header1.setYieldableValue(null);
        LinkedSRPDetailsOutput details1 = getDetail(1, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN);
        LinkedSRPDetailsOutput details2 = getDetail(2, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN.add(BigDecimal.TEN));
        when(intermediateTablesRepository.getAllHeaders()).thenReturn(Arrays.asList(header1));
        Map<Integer, List<LinkedSRPDetailsOutput>> outputDetails = new HashMap<>();
        when(intermediateTablesRepository.getOutputDetailsForHeader(header1.getId())).thenReturn(Arrays.asList(details1, details2));
        when(intermediateTablesRepository.getAdjustmentsForAllHeaders()).thenReturn(new HashMap<>());
        List<RateDefinition.RateHeader> rateHeaders = linkedSRPRatePopulationService.buildRateHeadersUsingLinkedSRPs();
        assertEquals(1, rateHeaders.get(0).getRateAdjustment().size());
        assertEquals(0, rateHeaders.get(0).getRateAdjustment().get(0).getYieldableValue().size());
        assertEquals(0, rateHeaders.get(0).getRateAdjustment().get(0).getYieldableCost().size());
    }

    @Test
    public void shouldCreateFileMetadata() {
        Date snapshotDt = new LocalDate("2020-12-12").toDate();
        FileMetadata fileMetadata = buildFileMetaData(snapshotDt);
        when(fileMetadataService.getLastProcessingRecord(PacmanWorkContextHelper.getPropertyId(), T2SNAP_RECORD_TYPE_ID)).thenReturn(fileMetadata);
        FileMetadata fileMetaDataForLinkedSrpPopulation = linkedSRPRatePopulationService.createFileMetaDataForLinkedSrpPopulation();
        verify(fileMetadataService).createFileMetadata(any(), any(), any(), any(), any(), any(), any());
    }

    private FileMetadata buildFileMetaData(Date snapshotDt) {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setSnapshotDt(snapshotDt);
        fileMetadata.setSnapshotTm(snapshotDt);
        fileMetadata.setPreparedDt(snapshotDt);
        fileMetadata.setPreparedTm(snapshotDt);
        return fileMetadata;
    }

    @Test
    public void shouldPopulateLinkedSrpRates() {

        Date snapshotDt = new LocalDate("2020-12-12").toDate();
        FileMetadata fileMetadata = buildFileMetaData(snapshotDt);
        HashMap<String, Integer> accomTypeMap = new HashMap<>();
        accomTypeMap.put("AT1", 1);

        when(accommodationRepository.getAccomTypeCodeWithId(5)).thenReturn(accomTypeMap);
        when(dateService.getCaughtUpDate()).thenReturn(snapshotDt);
        when(fileMetadataService.getLastProcessingRecord(PacmanWorkContextHelper.getPropertyId(), T2SNAP_RECORD_TYPE_ID)).thenReturn(fileMetadata);
        when(fileMetadataService.createFileMetadata(any(), any(), any(), any(), any(), any(), any())).thenReturn(fileMetadata);
        HashMap<String, RateQualified> rateMap = new HashMap<>();
        rateMap.put("R1", new RateQualified());
        when(qualifiedRateService.getManagedRates()).thenReturn(rateMap);

        linkedSRPRatePopulationService.populateLinkedSrpRates();
        verify(qualifiedRateService).processRates(any(List.class), any(Date.class), nullable(Integer.class), anyMap(), anyMap());
        verify(intermediateTablesRepository).updateRateQualifiedTypes();
    }

    @Test
    public void shouldPopulateLinkedSrpRatesForGivenHeader() {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(1);
        HashMap<String, Integer> accomTypeMap = new HashMap<>();
        accomTypeMap.put("AT1", 1);

        when(accommodationRepository.getAccomTypeCodeWithId(5)).thenReturn(accomTypeMap);

        HashMap<String, RateQualified> rateMap = new HashMap<>();
        rateMap.put("R1", new RateQualified());
        when(qualifiedRateService.getManagedRates()).thenReturn(rateMap);

        RateDefinition.RateHeader header = new RateDefinition.RateHeader();
        List<RateDefinition.RateHeader> rateHeaders = Arrays.asList(header);
        linkedSRPRatePopulationService.populateLinkedSrpRates(rateHeaders, fileMetadata);
        verify(qualifiedRateService).processRates(any(List.class), nullable(Date.class), anyInt(), anyMap(), anyMap());
        verify(intermediateTablesRepository).updateRateQualifiedTypes();
    }

    private void mockDataForBuildingRateHeaders() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(30);
        LinkedSRPHeader header1 = getHeader(1, startDate, endDate, "SRP1", "LV0", BigDecimal.TEN, "V");
        LinkedSRPHeader header2 = getHeader(2, startDate, endDate, "SRP2", "LV1", BigDecimal.TEN.add(BigDecimal.TEN), "P");

        LinkedSRPDetailsOutput details1 = getDetail(1, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN);
        LinkedSRPDetailsOutput details2 = getDetail(2, header1.getId(), "AT1", startDate, endDate, BigDecimal.TEN.add(BigDecimal.TEN));

        LinkedSRPDetailsOutput details3 = getDetail(3, header2.getId(), "AT2", startDate, endDate, BigDecimal.TEN);
        LinkedSRPDetailsOutput details4 = getDetail(4, header2.getId(), "AT3", startDate, endDate, BigDecimal.TEN.add(BigDecimal.TEN));

        when(intermediateTablesRepository.getAllHeaders()).thenReturn(Arrays.asList(header1, header2));
        Map<Integer, List<LinkedSRPDetailsOutput>> outputDetails = new HashMap<>();
        when(intermediateTablesRepository.getOutputDetailsForHeader(header1.getId())).thenReturn(Arrays.asList(details1, details2));
        when(intermediateTablesRepository.getOutputDetailsForHeader(header2.getId())).thenReturn(Arrays.asList(details3, details4));

        LinkedSRPAdjustments adjustment1 = getAdjustments(header1.getId(), startDate, endDate, BigDecimal.TEN);
        LinkedSRPAdjustments adjustment2 = getAdjustments(header1.getId(), startDate, endDate, BigDecimal.TEN.add(BigDecimal.TEN));

        LinkedSRPAdjustments adjustment3 = getAdjustments(header2.getId(), startDate, endDate, BigDecimal.ONE);
        LinkedSRPAdjustments adjustment4 = getAdjustments(header2.getId(), startDate, endDate, BigDecimal.ONE.add(BigDecimal.TEN));

        Map<Integer, List<LinkedSRPAdjustments>> outputAdjustments = new HashMap<>();
        outputAdjustments.put(header1.getId(), Arrays.asList(adjustment1, adjustment2));
        outputAdjustments.put(header2.getId(), Arrays.asList(adjustment3, adjustment4));
        when(intermediateTablesRepository.getAdjustmentsForAllHeaders()).thenReturn(outputAdjustments);
    }

    private LinkedSRPDetailsOutput getDetail(Integer id, Integer headerId, String accomTypeCode, LocalDate startDate, LocalDate endDate, BigDecimal defaultValue) {
        LinkedSRPDetailsOutput detail = new LinkedSRPDetailsOutput();
        detail.setHeaderId(headerId);
        detail.setAccomTypeCode(accomTypeCode);
        detail.setCreatedDate(LocalDateTime.now());
        detail.setStartDate(startDate);
        detail.setEndDate(endDate);
        detail.setSunday(defaultValue);
        detail.setMonday(defaultValue);
        detail.setTuesday(defaultValue);
        detail.setWednesday(defaultValue);
        detail.setThursday(defaultValue);
        detail.setFriday(defaultValue);
        detail.setSaturday(defaultValue);
        detail.setId(id);
        return detail;
    }

    private LinkedSRPHeader getHeader(Integer id, LocalDate startDate, LocalDate endDate, String srp, String linkedSrp, BigDecimal yieldableValue, String valueType) {
        return LinkedSRPHeader.builder()
                .createdDate(LocalDateTime.now())
                .currency("INR")
                .priceRelative(1)
                .referenceRateCode(linkedSrp)
                .srpCode(srp)
                .startDate(startDate)
                .endDate(endDate)
                .yieldable(1)
                .controlType("N")
                .linked(0)
                .status("A")
                .yieldableValue(yieldableValue)
                .yieldableValueType(valueType)
                .id(id)
                .conversionFactor(BigDecimal.ONE)
                .build();
    }

    private LinkedSRPAdjustments getAdjustments(Integer headerId, LocalDate startDate, LocalDate endDate, BigDecimal value) {
        return LinkedSRPAdjustments.builder()
                .value(value)
                .type("yieldableCost")
                .srpHeaderId(headerId)
                .startDate(startDate)
                .endDate(endDate)
                .valueType(1)
                .createdDate(LocalDateTime.now())
                .rule(PER_ROOM_PER_STAY)
                .build();
    }

}