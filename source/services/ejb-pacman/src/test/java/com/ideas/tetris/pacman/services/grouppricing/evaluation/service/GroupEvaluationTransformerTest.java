package com.ideas.tetris.pacman.services.grouppricing.evaluation.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.utils.xmlutil.XmlUtil;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.Commission;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.Concessions.Concession;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.DayParts.Daypart;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.Nights.Night;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.RcMinMaxValues;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.RcNights.RcNight;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails.RcNights.RcNight.RoomClass.RoomType;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.GroupPriceRequestType;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ValueType;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup.OccupancyDate;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate.DayPart;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.OptimalRCRates;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.OptimalRCRates.RoomClass;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.StatusCodeParams;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.GroupPriceResponseType;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffsetPK;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.contextholder.BusinessContext;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.functionspace.demandcalendar.service.FunctionSpaceDemandCalendarService;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceLimitsDto;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationObjectMother;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackage;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackageElement;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackageElementMap;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationMinProfitService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.FloorCeilingDto;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationOnBooksGroupDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationOnBooksGroupRoomClassDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageDetail;
import com.ideas.tetris.pacman.services.groupwash.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.GroupPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingConfigurationObjectMother;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.sasoptimization.service.AbstractOptimizationService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.pacman.services.webrate.service.WebrateDataSchedulingService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.map.HashedMap;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.IS_GROUP_PRICING_PACKAGE_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.ONE_HUNDRED;
import static java.lang.Integer.valueOf;
import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Arrays.asList;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class GroupEvaluationTransformerTest extends AbstractG3JupiterTest {

    private static final int OPTIMIZATION_WINDOW_BUFFER = 30;
    public static final int EXTENDED_WINDOW_DAYS = 1094;
    @Mock
    DateService dateService;

    @Mock
    PacmanConfigParamsService configService;

    @Mock
    BusinessContextService businessContextService;

    @Mock
    WebrateDataSchedulingService webrateDataSchedulingService;

    @Mock
    CrudService tenantCrudService;

    @Mock
    FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Mock
    FunctionSpaceDemandCalendarService functionSpaceDemandCalendarService;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    GroupPricingConfigurationMinProfitService groupPricingConfigurationMinProfitService;

    @Mock
    TaxService taxService;

    @Mock
    GroupEvaluationService groupEvaluationService;

    @InjectMocks
    GroupEvaluationTransformer transformer;

    GroupEvaluation groupEvaluation;

    @BeforeEach
    public void setup() {
        transformer = new GroupEvaluationTransformer();
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithFunctionSpace(new LocalDateTime(2015, 1, 1, 8, 0), new LocalDateTime(2015, 1, 1, 10, 0));

        MockitoAnnotations.initMocks(this);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(false);

        Product barProduct = new Product();
        barProduct.setId(1);
        barProduct.setSystemDefault(true);
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(barProduct);
    }

    @Test
    public void testCreateCDPRequest() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        when(dateService.getWasLastLoadBDE()).thenReturn(false);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        transformer.populateGroupPricingRequestTypeParameters(groupEvaluation, groupPriceRequestType);

        assertEquals(Constants.CDP, groupPriceRequestType.getOperationType());

        verify(dateService).getWasLastLoadBDE();
        verify(businessContextService).getCurrentBusinessContext();
        verify(configService).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
    }

    @Test
    public void testCreateCPRequest() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        when(dateService.getWasLastLoadBDE()).thenReturn(false);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value()))
                .thenReturn(true);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        transformer.populateGroupPricingRequestTypeParameters(groupEvaluation, groupPriceRequestType);

        assertTrue(groupPriceRequestType.isContinuousPricing());
        assertEquals(valueOf(-1), groupPriceRequestType.getMasterAccomClassId());

        verify(dateService).getWasLastLoadBDE();
        verify(configService).getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        verify(configService).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
    }

    @Test
    public void populateGroupPricingRequestTypeParameters_withTax() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(7));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);

        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        when(dateService.getWasLastLoadBDE()).thenReturn(false);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(taxService.findTax()).thenReturn(tax);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.populateGroupPricingRequestTypeParameters(groupEvaluation, groupPriceRequestType);

        assertEquals(tax.getRoomTaxRate(), groupPriceRequestType.getTaxRate());
        assertTrue(groupPriceRequestType.isContinuousPricing());
        assertEquals(valueOf(-1), groupPriceRequestType.getMasterAccomClassId());

        verify(taxService).findTax();
        verify(dateService).getWasLastLoadBDE();
        verify(configService).getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        verify(configService).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
    }

    @Test
    public void testCreateRequest() {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupEvaluation(groupEvaluation);
        groupEvaluationAncillary.setRevenue(new BigDecimal(100));
        groupEvaluationAncillary.setProfitPercentage(new BigDecimal(5));
        groupEvaluation.addGroupEvaluationAncillary(groupEvaluationAncillary);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertNull(groupPriceRequestType.getGroupPriceParams().isReduceThread());
        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY,
                groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());
        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(), groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getNights().getNight().size());
        assertEquals(3, arrDateDetails.getNumNights());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates()
                .iterator().next();

        Night night1 = arrDateDetails.getNights().getNight().get(0);
        assertEquals(100, night1.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().toDate(),
                night1.getDate().toGregorianCalendar().getTime());

        Night night2 = arrDateDetails.getNights().getNight().get(1);
        assertEquals(100, night2.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(1).toDate(),
                night2.getDate().toGregorianCalendar().getTime());

        Night night3 = arrDateDetails.getNights().getNight().get(2);
        assertEquals(100, night3.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(2).toDate(),
                night3.getDate().toGregorianCalendar().getTime());

        assertNull(arrDateDetails.getMaxDailyRateChange());
        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqValue());
        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqRevenue());
        assertEquals(new BigDecimal(76).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqMargin());

        assertEquals(new BigDecimal(100), arrDateDetails.getAncillaryValue());
        assertEquals(new BigDecimal(5).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getAncillaryMargin());
    }

    @Test
    public void createRequest_multiProperty() {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        // Set multi property ID
        groupEvaluation.setGroupEvaluationMultiId(15);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertTrue(groupPriceRequestType.getGroupPriceParams().isReduceThread());
        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY,
                groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());

        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());
    }

    @Test
    public void testCreateRequest_IncludeFunctionSpace() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        GroupEvaluationCost functionSpaceCost = new GroupEvaluationCost();
        functionSpaceCost.setGroupEvaluationCostType(GroupEvaluationCostType.FUNCTION_SPACE);
        functionSpaceCost.setCost(new BigDecimal(200.00));
        groupEvaluation.addGroupEvaluationCost(functionSpaceCost);

        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupEvaluation(groupEvaluation);
        groupEvaluationAncillary.setRevenue(new BigDecimal(100));
        groupEvaluationAncillary.setProfitPercentage(new BigDecimal(5));
        groupEvaluation.addGroupEvaluationAncillary(groupEvaluationAncillary);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigs);

        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();

        GroupEvaluationFunctionSpace fs = groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs.getStartTime().toLocalTime(), fs.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime())).thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red", BigDecimal.valueOf(1000)));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, fs.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(14));
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);
        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY, groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());

        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getNights().getNight().size());
        assertEquals(3, arrDateDetails.getNumNights());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Night night1 = arrDateDetails.getNights().getNight().get(0);
        assertEquals(100, night1.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().toDate(), night1.getDate().toGregorianCalendar().getTime());

        Night night2 = arrDateDetails.getNights().getNight().get(1);
        assertEquals(100, night2.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(1).toDate(), night2.getDate().toGregorianCalendar().getTime());

        Night night3 = arrDateDetails.getNights().getNight().get(2);
        assertEquals(100, night3.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(2).toDate(), night3.getDate().toGregorianCalendar().getTime());

        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqValue());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqRevenue());
        assertEquals(new BigDecimal(8000).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqMargin());

        assertEquals(new BigDecimal(100), arrDateDetails.getAncillaryValue());
        assertEquals(new BigDecimal(5).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getAncillaryMargin());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());

        assertEquals(new BigDecimal(200), arrDateDetails.getFunctionSpaceCost());

        Daypart daypart = arrDateDetails.getDayParts().getDaypart().get(0);
        assertEquals(1, daypart.getDaypartId());
        assertEquals(groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next().getStartTime().toLocalDate()
                .toDate(), daypart.getDate().toGregorianCalendar().getTime());
        assertEquals(new BigDecimal(50).setScale(2, RoundingMode.HALF_UP), daypart.getPercentRequested());
    }

    public static Stream<Arguments> functionSpacePackagePricingSetProvider() {
        Map<String, FunctionSpacePackage> functionSpacePackageMap = FunctionSpaceObjectMother.buildFunctionSpacePackages();
        String SINGLE_OCCUPANCY = "single.occupancy";
        String DOUBLE_OCCUPANCY = "double.occupancy";
        String NON_GUEST_ROOM = "non.guest.room.type";

        Arguments functionSpaceEvalPackagePricingSet1 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50))), NON_GUEST_ROOM,
                                BigDecimal.valueOf(10))
                ),
                BigDecimal.valueOf(2610.00),
                BigDecimal.valueOf(942.75),
                "Full Day Meeting - Function Room Rental included in Package, Guest Room rate charged separately (1 day)"
        );

        Arguments functionSpaceEvalPackagePricingSet2 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 50))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(12))
                ),
                BigDecimal.valueOf(5104.00),
                BigDecimal.valueOf(1843.60),
                "Full Day Meeting - Function Room Rental included in Package, Guest Room rate charged separately (2 days)"
        );

        Arguments functionSpaceEvalPackagePricingSet3 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 100), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(12)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 100), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(12)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 100))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(12))
                ),
                BigDecimal.valueOf(8756.363),
                BigDecimal.valueOf(3181.382),
                "Full Day Meeting - Function Room Rental included in Package, Guest Room rate charged separately (2 days) - package added, without delegate count"
        );

        Arguments functionSpaceEvalPackagePricingSet4 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 50))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(4975.2065),
                BigDecimal.valueOf(1807.6035),
                "Two Day Meeting - Multiple Packages, Guest Room not included in Package (2 days)"
        );

        Arguments functionSpaceEvalPackagePricingSet5 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 75))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(5742.80975),
                BigDecimal.valueOf(2095.40525),
                "Two Day Meeting - Multiple Packages, Guest Room not included in Package (2 days) and delegate count differs for each day"
        );

        Arguments functionSpaceEvalPackagePricingSet6 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 40), Map.entry(3,
                                        0))), SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 0), Map.entry(3,
                                        75))), NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 90), Map.entry(2, 40), Map.entry(3,
                                        0))), NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(9322.80975),
                BigDecimal.valueOf(3363.90525),
                "Three Day Meeting - Multiple Packages, Guest Room not included in Package (3 days) and delegate count differs for each day for same package"
        );

        Arguments functionSpaceEvalPackagePricingSet7 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 40), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 20), Map.entry(2, 0))),
                                DOUBLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(5970.2478),
                BigDecimal.valueOf(2169.1242),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days)"
        );

        Arguments functionSpaceEvalPackagePricingSet8 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50))), NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(3132.50),
                BigDecimal.valueOf(1125.125),
                "Full Day Meeting - Function Room Rental included in Package, Guest Room rate charged separately (1 day) - when a package with package element with 0 tax"
        );

        Arguments functionSpaceEvalPackagePricingSet9 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 50))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 50))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                BigDecimal.valueOf(4411.413),
                BigDecimal.valueOf(1614.257),
                "Full Day Meeting - Function Room Rental included in Package, Guest Room rate charged separately (2 days) - package added, without delegate count - when package element with 0 tax"
        );

        Arguments functionSpaceEvalPackagePricingSet10 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(0.1)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 75))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.1)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.1)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Dinner - Basic 3 Course"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.1))
                ),
                BigDecimal.valueOf(8617.06725),
                BigDecimal.valueOf(3029.31),
                "Two Day Meeting - Multiple Packages, Guest Room not included in Package (2 days) and delegate count differs for each day - package having no package elements taxable"
        );

        Arguments functionSpaceEvalPackagePricingSet11 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Dinner - Basic 3 Course"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(11)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Lunch - Dinner - Combo"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 15), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(11))
                ),
                BigDecimal.valueOf(4093.95),
                BigDecimal.valueOf(1332.2025),
                "Package with some packages elements commissionable and some not"
        );

        Arguments functionSpaceEvalPackagePricingSet12 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99))
                ),
                BigDecimal.valueOf(5372.5279),
                BigDecimal.valueOf(1950.9183),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days)"
        );


        return Stream.of(functionSpaceEvalPackagePricingSet1, functionSpaceEvalPackagePricingSet2, functionSpaceEvalPackagePricingSet3, functionSpaceEvalPackagePricingSet4,
                functionSpaceEvalPackagePricingSet5, functionSpaceEvalPackagePricingSet6, functionSpaceEvalPackagePricingSet7, functionSpaceEvalPackagePricingSet8,
                functionSpaceEvalPackagePricingSet9, functionSpaceEvalPackagePricingSet10, functionSpaceEvalPackagePricingSet11,
                functionSpaceEvalPackagePricingSet12);
    }

    @ParameterizedTest
    @MethodSource("functionSpacePackagePricingSetProvider")
    public void testCreateRequestForFunctionSpacePackageAndFunctionSpaceWithoutConfBanq(Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricingSet,
                                                                                        BigDecimal expectedConfBanqValue, BigDecimal expectedConfBanqMargin,
                                                                                        String description) {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        Date caughtUpDate = new Date(2015, 7, 1);

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigs);

        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        GroupEvaluationFunctionSpace fs = groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs.getStartTime().toLocalTime(), fs.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime())).thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red", BigDecimal.valueOf(1000)));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, fs.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(14));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        int optWindowEndDateBDEDays = 365;
        int bufferDays = SystemConfig.getGroupPricingOptStartAndEndDateBufferDays();
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(bufferDays)
                .plusDays(groupEvaluation.getNumberOfNights()).plusDays(optWindowEndDateBDEDays);
        LocalDate lastFcstDate = LocalDate.fromDateFields(DateUtil.addDaysToDate(expectedOptEndDate.toDate(), 1));
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);
        when(dateService.getFunctionSpaceMaxFcstDate()).thenReturn(lastFcstDate);

        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(new HashSet<>());
        groupEvaluation.setFunctionSpaceEvalPackagePricings(functionSpaceEvalPackagePricingSet);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY, groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());
        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getNights().getNight().size());
        assertEquals(3, arrDateDetails.getNumNights());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        Night night1 = arrDateDetails.getNights().getNight().get(0);
        assertEquals(100, night1.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().toDate(), night1.getDate().toGregorianCalendar().getTime());

        Night night2 = arrDateDetails.getNights().getNight().get(1);
        assertEquals(100, night2.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(1).toDate(), night2.getDate().toGregorianCalendar().getTime());

        Night night3 = arrDateDetails.getNights().getNight().get(2);
        assertEquals(100, night3.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(2).toDate(), night3.getDate().toGregorianCalendar().getTime());

        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertTrue(BigDecimalUtil.equals(expectedConfBanqValue, arrDateDetails.getConfBanqValue()), description);
        assertTrue(BigDecimalUtil.equals(expectedConfBanqMargin, arrDateDetails.getConfBanqMargin()), description);
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());

        Daypart daypart = arrDateDetails.getDayParts().getDaypart().get(0);
        assertEquals(1, daypart.getDaypartId());
        assertEquals(groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next().getStartTime().toLocalDate()
                .toDate(), daypart.getDate().toGregorianCalendar().getTime());
        assertEquals(new BigDecimal(50).setScale(2, RoundingMode.HALF_UP), daypart.getPercentRequested());
    }

    public static Stream<Arguments> functionSpacePackagePricingAndFunctionSpaceConfAndBanqSetProvider() {
        Map<String, FunctionSpacePackage> functionSpacePackageMapForFSPkgAndFSConfBanq = FunctionSpaceObjectMother.buildFunctionSpacePackagesForGroupEvaluationWithFunctionSpaceConfBanq();
        Map<String, FunctionSpacePackage> functionSpacePackageMap = FunctionSpaceObjectMother.buildFunctionSpacePackages();
        String SINGLE_OCCUPANCY = "single.occupancy";
        String DOUBLE_OCCUPANCY = "double.occupancy";
        String NON_GUEST_ROOM = "non.guest.room.type";

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet1 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 40), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 20), Map.entry(2, 0))),
                                DOUBLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.40).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6580.2478),
                BigDecimal.valueOf(1909.9618),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet2 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 40), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 20), Map.entry(2, 0))),
                                DOUBLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6740.2478),
                BigDecimal.valueOf(1935.5618),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue with 0 commission value"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet3 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99))
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.40).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6177.0829),
                BigDecimal.valueOf(2158.4818),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet4 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(1.02)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(1.02)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(1.02))
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6140.9004),
                BigDecimal.valueOf(2117.3261),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue with 0 commission value"
        );

        return Stream.of(functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet1,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet2,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet3,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet4);
    }

    @ParameterizedTest
    @MethodSource("functionSpacePackagePricingAndFunctionSpaceConfAndBanqSetProvider")
    public void testAddConfBanqForFunctionSpacePackageAndFunctionSpaceConfAndBanq(Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricingSet,
                                                                                  Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqSet,
                                                                                  BigDecimal expectedConfBanqValue, BigDecimal expectedConfBanqMargin,
                                                                                  String description) {
        //GIVEN
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(groupEvaluationFunctionSpaceConfAndBanqSet);
        groupEvaluation.setFunctionSpaceEvalPackagePricings(functionSpaceEvalPackagePricingSet);

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);

        //WHEN
        transformer.addConferenceAndBanquet(groupEvaluation, arrDateDetails);

        //THEN
        assertTrue(BigDecimalUtil.equals(expectedConfBanqValue, arrDateDetails.getConfBanqValue()), description);
        assertTrue(BigDecimalUtil.equals(expectedConfBanqMargin, arrDateDetails.getConfBanqMargin()), description);
    }

    public static Stream<Arguments> fsPackagePricingAndFSConfAndBanqSetProviderWhenGroupPricingEnabledWithFSRevenueStreams() {
        Map<String, FunctionSpacePackage> functionSpacePackageMapForFSPkgAndFSConfBanq = FunctionSpaceObjectMother.buildFunctionSpacePackagesForGroupEvaluationWithFunctionSpaceConfBanq();
        Map<String, FunctionSpacePackage> functionSpacePackageMap = FunctionSpaceObjectMother.buildFunctionSpacePackages();
        String SINGLE_OCCUPANCY = "single.occupancy";
        String DOUBLE_OCCUPANCY = "double.occupancy";
        String NON_GUEST_ROOM = "non.guest.room.type";

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet1 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 40), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 20), Map.entry(2, 0))),
                                DOUBLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.40).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6870.2478),
                BigDecimal.valueOf(1909.9618),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet2 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 40), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Full Board Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 20), Map.entry(2, 0))),
                                DOUBLE_OCCUPANCY, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.ZERO),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMapForFSPkgAndFSConfBanq.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.ZERO)
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6870.2478),
                BigDecimal.valueOf(1935.5618),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue with 0 commission value"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet3 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(0.99))
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.40).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6467.0829),
                BigDecimal.valueOf(2158.4818),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue"
        );

        Arguments functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet4 = arguments(
                Set.of(
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Board Basic - Without Tax"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                                SINGLE_OCCUPANCY, BigDecimal.valueOf(1.02)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("AM Mtg Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 60))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(1.02)),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Breakfast - Basic"),
                                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                                NON_GUEST_ROOM, BigDecimal.valueOf(1.02))
                ),
                Set.of(
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.25))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(300).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0.30).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.30))),
                        GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(400).setScale(2, HALF_UP),
                                BigDecimal.valueOf(0).setScale(2, HALF_UP), FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI",
                                        BigDecimal.valueOf(0.16)))
                ),
                BigDecimal.valueOf(6270.9004),
                BigDecimal.valueOf(2117.3261),
                "Two Day Meeting - Multiple Packages (single & double occupancy), Guest Room not included in Package (2 days), also include function space revenue with 0 commission value"
        );

        return Stream.of(functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet1,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet2,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet3,
                functionSpaceEvalPackagePricingAndGroupEvaluationFunctionSpaceConfAndBanqSet4);
    }

    @ParameterizedTest
    @MethodSource("fsPackagePricingAndFSConfAndBanqSetProviderWhenGroupPricingEnabledWithFSRevenueStreams")
    public void shouldAddConfBanqForFunctionSpacePackageAndFunctionSpaceConfAndBanqWhenGroupPricingPackageWithUseRevenueStreamIsEnabled(Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricingSet,
                                                                                                                                        Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqSet,
                                                                                                                                        BigDecimal expectedConfBanqValue, BigDecimal expectedConfBanqMargin,
                                                                                                                                        String description) {
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(groupEvaluationFunctionSpaceConfAndBanqSet);
        groupEvaluation.setFunctionSpaceEvalPackagePricings(functionSpaceEvalPackagePricingSet);
        ArrDateDetails arrDateDetails = new ArrDateDetails();
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(false);

        transformer.addConferenceAndBanquet(groupEvaluation, arrDateDetails);

        assertTrue(BigDecimalUtil.equals(expectedConfBanqValue, arrDateDetails.getConfBanqValue()), description);
        assertTrue(BigDecimalUtil.equals(expectedConfBanqMargin, arrDateDetails.getConfBanqMargin()), description);
    }

    @Test
    void shouldAddFSConferenceBanquetToGroupPricingRequestWhenUseFSRevenueStreamsIsEnabled() {
        groupEvaluation.getGroupEvaluationConferenceAndBanquets().clear();
        groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().clear();
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(
                buildFunctionSpaceConferenceBanquets(100.00, 0.10, buildFunctionSpaceRevenueGroup("F&B", 0.30)),
                buildFunctionSpaceConferenceBanquets(100.00, 0.10, buildFunctionSpaceRevenueGroup("AV Services", 0.25))));
        ArrDateDetails arrDateDetails = new ArrDateDetails();
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        transformer.addConferenceAndBanquet(groupEvaluation, arrDateDetails);

        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(200.00), arrDateDetails.getConfBanqValue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(49.50), arrDateDetails.getConfBanqMargin()));
    }

    private GroupEvaluationFunctionSpaceConfAndBanq buildFunctionSpaceConferenceBanquets(
            double revenue, double commission, FunctionSpaceRevenueGroup functionSpaceRevenueGroup) {
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
        confAndBanq.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
        confAndBanq.setRevenue(BigDecimal.valueOf(revenue));
        confAndBanq.setCommissionPercentage(BigDecimal.valueOf(commission));
        return confAndBanq;
    }

    private FunctionSpaceRevenueGroup buildFunctionSpaceRevenueGroup(String name, double profit) {
        FunctionSpaceRevenueGroup revenueGroup = new FunctionSpaceRevenueGroup();
        revenueGroup.setName(name);
        revenueGroup.setProfitPercent(BigDecimal.valueOf(profit));
        return revenueGroup;
    }

    @Test
    public void shouldReturnNullForConfBanqValueWhenNoPackagesAreIncluded() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        Date caughtUpDate = new Date(2015, 7, 1);

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(DateUtil.addDaysToDate(caughtUpDate, 365));
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigs);

        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        GroupEvaluationFunctionSpace fs = groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_MILLIS_FORMAT);
        java.time.LocalDateTime startTime = DateUtil.convertJavaDateTimeFromUTC(DateUtil.getPreciseDate(fs.getStartTime()), dateTimeFormatter);
        java.time.LocalDateTime endTime = DateUtil.convertJavaDateTimeFromUTC(DateUtil.getPreciseDate(fs.getEndTime()), dateTimeFormatter);
        when(functionSpaceDemandCalendarService.isWithinTimeRange(DateUtil.convertJavaTimeToJodaTime(startTime.toLocalTime()), DateUtil.convertJavaTimeToJodaTime(endTime.toLocalTime()), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime())).thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red", BigDecimal.valueOf(1000)));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, DateUtil.convertJavaToJodaLocalDate(startTime.toLocalDate()))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(14));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        int optWindowEndDateBDEDays = 365;
        int bufferDays = SystemConfig.getGroupPricingOptStartAndEndDateBufferDays();
        java.time.LocalDate expectedOptEndDate = LocalDateUtils.toJavaTimeLocalDate(groupEvaluation.getLastArrivalDate()).plusDays(bufferDays)
                .plusDays(groupEvaluation.getNumberOfNights()).plusDays(optWindowEndDateBDEDays);
        java.time.LocalDate lastFcstDate = expectedOptEndDate.plusDays(1);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);
        when(dateService.getFunctionSpaceMaxFcstDate()).thenReturn(LocalDateUtils.toJodaLocalDate(lastFcstDate));
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        groupEvaluation.setGroupPricingEvalPackagePricings(null);
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY, groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());
        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getNights().getNight().size());
        assertEquals(3, arrDateDetails.getNumNights());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        Night night1 = arrDateDetails.getNights().getNight().get(0);
        assertEquals(100, night1.getValue());
        assertEquals(LocalDateUtils.toDate(LocalDateUtils.toJavaTimeLocalDate(groupEvaluationArrivalDate.getArrivalDate())), night1.getDate().toGregorianCalendar().getTime());

        Night night2 = arrDateDetails.getNights().getNight().get(1);
        assertEquals(100, night2.getValue());
        assertEquals(LocalDateUtils.toDate(LocalDateUtils.toJavaTimeLocalDate(groupEvaluationArrivalDate.getArrivalDate()).plusDays(1)), night2.getDate().toGregorianCalendar().getTime());

        Night night3 = arrDateDetails.getNights().getNight().get(2);
        assertEquals(100, night3.getValue());
        assertEquals(LocalDateUtils.toDate(LocalDateUtils.toJavaTimeLocalDate(groupEvaluationArrivalDate.getArrivalDate()).plusDays(2)), night3.getDate().toGregorianCalendar().getTime());

        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertNull(arrDateDetails.getConfBanqValue());
        assertNull(arrDateDetails.getConfBanqMargin());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());

        Daypart daypart = arrDateDetails.getDayParts().getDaypart().get(0);
        assertEquals(1, daypart.getDaypartId());
        java.time.LocalDateTime fsStartTime = DateUtil.convertJavaDateTimeFromUTC(DateUtil.getPreciseDate(groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next().getStartTime()), dateTimeFormatter);
        assertEquals(DateUtil.convertLocalDateToJavaUtilDate(fsStartTime.toLocalDate()), daypart.getDate().toGregorianCalendar().getTime());
        assertEquals(new BigDecimal(50).setScale(2, RoundingMode.HALF_UP), daypart.getPercentRequested());
    }

    @Test
    public void shouldReturnrConfBanqValueForGPConfBanqRevenueOnly() {
        setupGroupEvaluationInputExcludingGroupPricingPackageInput();
        GroupPricingConfigurationConferenceAndBanquet confAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(PacmanWorkContextHelper.getPropertyId());
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(confAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(confAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(Set.of(groupEvaluationConferenceAndBanquet));
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);
        BigDecimal expectedConfBanqValue = BigDecimal.valueOf(100);
        BigDecimal expectedConfBanqMargin = BigDecimal.valueOf(76);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);
        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());
        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(0, expectedConfBanqValue.compareTo(arrDateDetails.getConfBanqValue()));
        assertEquals(0, expectedConfBanqMargin.compareTo(arrDateDetails.getConfBanqMargin()));
    }

    @Test
    public void shouldReturnConfBanqValueWhenSinglePackageWithSingleDayDelegatesIsIncluded() {
        setupGroupEvaluationInputExcludingGroupPricingPackageInput();
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        BigDecimal expectedConfBanqValue = BigDecimal.valueOf(1665);
        BigDecimal expectedConfBanqMargin = BigDecimal.valueOf(546.75);

        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);
        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());
        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(0, expectedConfBanqValue.compareTo(arrDateDetails.getConfBanqValue()));
        assertEquals(0, expectedConfBanqMargin.compareTo(arrDateDetails.getConfBanqMargin()));
    }

    @Test
    public void shouldReturnConfBanqValueWhenSinglePackageWithMultiDayDelegatesIsIncluded() {
        setupGroupEvaluationInputExcludingGroupPricingPackageInput();
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 33))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(12)));
        BigDecimal expectedConfBanqValue = BigDecimal.valueOf(2702.48);
        BigDecimal expectedConfBanqMargin = BigDecimal.valueOf(887.436);
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());
        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(0, expectedConfBanqValue.compareTo(arrDateDetails.getConfBanqValue()));
        assertEquals(0, expectedConfBanqMargin.compareTo(arrDateDetails.getConfBanqMargin()));
    }

    @Test
    public void shouldReturnConfBanqValueWhenMultiplePackagesWithSingleDayDelegatesAreIncluded() {
        setupGroupEvaluationInputExcludingGroupPricingPackageInput();
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        String SINGLE_OCCUPANCY = "single.occupancy";
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        SINGLE_OCCUPANCY, BigDecimal.valueOf(0.99)),
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 0))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(0.99)),
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 60))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(0.99)));
        BigDecimal expectedConfBanqValue = BigDecimal.valueOf(4029.707);
        BigDecimal expectedConfBanqMargin = BigDecimal.valueOf(1323.26865);
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());
        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(0, expectedConfBanqValue.compareTo(arrDateDetails.getConfBanqValue()));
        assertEquals(0, expectedConfBanqMargin.compareTo(arrDateDetails.getConfBanqMargin()));
    }

    @Test
    public void shouldReturnConfBanqValueWhenMultiplePackagesWithMultiDayDelegatesAreIncluded() {
        setupGroupEvaluationInputExcludingGroupPricingPackageInput();
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        String SINGLE_OCCUPANCY = "single.occupancy";
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50), Map.entry(2, 0))),
                        SINGLE_OCCUPANCY, BigDecimal.valueOf(6.2)),
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 0), Map.entry(2, 75))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(6.2)),
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 0))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(6.2)),
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 60), Map.entry(2, 43))),
                        NON_GUEST_ROOM, BigDecimal.valueOf(6.2))
        );
        BigDecimal expectedConfBanqValue = BigDecimal.valueOf(9995.328);
        BigDecimal expectedConfBanqMargin = BigDecimal.valueOf(3282.2496);
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());
        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(0, expectedConfBanqValue.compareTo(arrDateDetails.getConfBanqValue()));
        assertEquals(0, expectedConfBanqMargin.compareTo(arrDateDetails.getConfBanqMargin()));
    }

    private void setupGroupEvaluationInputExcludingGroupPricingPackageInput() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        Date caughtUpDate = new Date(2015, 7, 1);

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(DateUtil.addDaysToDate(caughtUpDate, 365));
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigs);

        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        GroupEvaluationFunctionSpace fs = groupEvaluation.getGroupEvaluationFunctionSpaces().iterator().next();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_MILLIS_FORMAT);
        java.time.LocalDateTime startTime = DateUtil.convertJavaDateTimeFromUTC(DateUtil.getPreciseDate(fs.getStartTime()), dateTimeFormatter);
        java.time.LocalDateTime endTime = DateUtil.convertJavaDateTimeFromUTC(DateUtil.getPreciseDate(fs.getEndTime()), dateTimeFormatter);
        when(functionSpaceDemandCalendarService.isWithinTimeRange(DateUtil.convertJavaTimeToJodaTime(startTime.toLocalTime()), DateUtil.convertJavaTimeToJodaTime(endTime.toLocalTime()), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime())).thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red", BigDecimal.valueOf(1000)));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, LocalDateUtils.toJodaLocalDate(startTime.toLocalDate()))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(14));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        int optWindowEndDateBDEDays = 365;
        int bufferDays = SystemConfig.getGroupPricingOptStartAndEndDateBufferDays();
        java.time.LocalDate expectedOptEndDate = LocalDateUtils.toJavaTimeLocalDate(groupEvaluation.getLastArrivalDate()).plusDays(bufferDays)
                .plusDays(groupEvaluation.getNumberOfNights()).plusDays(optWindowEndDateBDEDays);
        java.time.LocalDate lastFcstDate = LocalDateUtils.toJavaLocalDate(DateUtil.addDaysToDate(DateUtil.convertLocalDateToJavaUtilDate(expectedOptEndDate), 1));
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);
        when(dateService.getFunctionSpaceMaxFcstDate()).thenReturn(LocalDateUtils.toJodaLocalDate(lastFcstDate));
    }

    private GroupPricingConfigurationPackage buildGroupPricingConfigurationPackage() {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationPackage("Full Day Basic", FunctionSpaceObjectMother.buildFunctionSpacePackageType("Full Day"));
        GroupPricingConfigurationPackageElement packageElement1 = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationPackageElement("Food Lunch",
                GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(1, "Food", BigDecimal.valueOf(30)), BigDecimal.valueOf(12));
        GroupPricingConfigurationPackageElement packageElement2 = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationPackageElement("Bev Lunch",
                GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(2, "Beverage", BigDecimal.valueOf(45)), BigDecimal.valueOf(21));
        Set<GroupPricingConfigurationPackageElementMap> packageElementMapSet = Set.of(
                GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationPackageElementMap(groupPricingConfigurationPackage, packageElement1, BigDecimal.valueOf(33.60)),
                GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationPackageElementMap(groupPricingConfigurationPackage, packageElement2, BigDecimal.valueOf(8.47)));
        groupPricingConfigurationPackage.setGroupPricingPackageElementMapSet(packageElementMapSet);

        return groupPricingConfigurationPackage;
    }

    @Test
    public void testCreateRequest_RoomClassEvaluation() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithRoomTypes();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupEvaluation(groupEvaluation);
        groupEvaluationAncillary.setRevenue(new BigDecimal(100));
        groupEvaluationAncillary.setProfitPercentage(new BigDecimal(5));
        groupEvaluation.addGroupEvaluationAncillary(groupEvaluationAncillary);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(tenantCrudService.find(AccomClass.class, 1)).thenReturn(
                groupEvaluation.getGroupEvaluationRoomTypes().iterator().next().getRoomType().getAccomClass());
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY,
                groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());

        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.RC.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getRcNights().getRcNight().size());

        assertEquals(3, arrDateDetails.getNumNights());
        assertNull(arrDateDetails.getMaxDailyRateChange());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates()
                .iterator().next();

        RcNight rcNight1 = arrDateDetails.getRcNights().getRcNight().get(0);
        assertEquals(2, rcNight1.getRoomClass().get(0).getRoomsRequested());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().toDate(),
                rcNight1.getDate().toGregorianCalendar().getTime());

        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqValue());
        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqRevenue());
        assertEquals(new BigDecimal(76).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqMargin());

        assertEquals(new BigDecimal(100), arrDateDetails.getAncillaryValue());
        assertEquals(new BigDecimal(5).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getAncillaryMargin());
    }

    @Test
    public void addRCNights_ComponentRoomEnabled() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationForRoomClass();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getPreferredGroupEvaluationArrivalDate();
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);

        transformer.addRCNights(groupEvaluation, arrivalDate, arrDateDetails);

        assertEquals(3, arrDateDetails.getRcNights().getRcNight().size());

        arrDateDetails.getRcNights().getRcNight().forEach(rcNight -> {
            assertEquals(100, rcNight.getRoomClass().get(0).getRoomsRequested());
            assertEquals(Integer.valueOf(1), rcNight.getRoomClass().get(0).getNumRTs());
            assertEquals(valueOf(1), rcNight.getRoomClass().get(0).getNumRTs());

            List<RoomType> roomTypes = rcNight.getRoomClass().get(0).getRoomType();
            assertEquals(1, roomTypes.get(0).getRtId());
            assertEquals(100, roomTypes.get(0).getRoomsRequested());
        });

        verify(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value());
    }

    // Testing that a RoomClass type evaluation with only function space has zero nights
    @Test
    public void testCreateRequest_RoomClassEvaluation_FunctionSpaceOnly() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationWithFunctionSpace(new LocalDateTime(2015, 1, 1, 8, 0), new LocalDateTime(2015, 1, 1, 10, 0));
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEvaluation.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(false);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.RC.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());

        assertEquals(0, arrDateDetails.getNumNights());
    }

    @Test
    public void testCreateRequest_RoomClassEvaluationWithMultipleArrivalDates() throws Exception {
        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluationForRoomClass();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);

        // Add another non-preferred arrival date
        GroupEvaluationArrivalDate nonPreferredGroupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        nonPreferredGroupEvaluationArrivalDate.setPreferredDate(false);
        nonPreferredGroupEvaluationArrivalDate.setArrivalDate(GroupEvaluationObjectMother.PREFERRED_DATE.plusWeeks(2));
        groupEvaluation.addGroupEvaluationArrivalDate(nonPreferredGroupEvaluationArrivalDate);

        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupEvaluation(groupEvaluation);
        groupEvaluationAncillary.setRevenue(new BigDecimal(100));
        groupEvaluationAncillary.setProfitPercentage(new BigDecimal(5));
        groupEvaluation.addGroupEvaluationAncillary(groupEvaluationAncillary);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY,
                groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());

        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(),
                groupPriceRequestType.getGroupPriceParams().getWindowSize());

        // Verify that two arrival dates were created with room class nights
        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(2, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.RC.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getRcNights().getRcNight().size());
        assertEquals(groupEvaluation.getPreferredDate(),
                new LocalDate(convertToDate(arrDateDetails.getArrivalDate().get(0))));

        assertEquals(3, arrDateDetails.getNumNights());

        arrDateDetails = arrivalDates.get(1);
        assertEquals(GroupPricingEvaluationMethod.RC.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getRcNights().getRcNight().size());
        assertEquals(nonPreferredGroupEvaluationArrivalDate.getArrivalDate(),
                new LocalDate(convertToDate(arrDateDetails.getArrivalDate().get(0))));

        assertEquals(3, arrDateDetails.getNumNights());
    }

    @Test
    public void addConcessionsAndDiscountsWithNone() {
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addCosts(groupEvaluation, arrDateDetails);

        assertEquals(0, arrDateDetails.getConcessions().getConcession().size());
    }

    @Test
    public void addConcessionsAndDiscountsWithComplimentaryRoomsFixed() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(2);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addCosts(groupEvaluation, arrDateDetails);

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(2, concession.getRooms().intValue());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());
        assertEquals(ValueType.PERCENT, concession.getType());
    }

    @Test
    public void addConcessionsAndDiscountsWithComplimentaryRoomsPer() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setDivisor(2);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addCosts(groupEvaluation, arrDateDetails);

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(120, concession.getRooms().intValue());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());
        assertEquals(ValueType.PERCENT, concession.getType());
    }

    @Test
    public void addConcessionsAndDiscountsWithComplimentaryRoomsPerUtilizingFloor() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setDivisor(2);
        groupEvaluationCost.setTotal(7);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addCosts(groupEvaluation, arrDateDetails);

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(85, concession.getRooms().intValue());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());
        assertEquals(ValueType.PERCENT, concession.getType());
    }

    @Test
    public void addRcMinMaxValues() throws Exception {
        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildRoomClassGroupEvaluation();

        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getPreferredGroupEvaluationArrivalDate();
        arrivalDate.setArrivalDate(new LocalDate(2016, 5, 1));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        CPConfigMergedOffsetPK cpConfigMergedOffsetPK = new CPConfigMergedOffsetPK(arrivalDate.getArrivalDate(), 1, 1, OccupancyType.SINGLE);
        CPConfigMergedOffset cpConfigMergedOffset = new CPConfigMergedOffset();
        cpConfigMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigMergedOffset.setOffsetValue(new BigDecimal(20));

        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> mergedOffsets = new HashMap<>();
        mergedOffsets.put(cpConfigMergedOffsetPK, cpConfigMergedOffset);

        FloorCeilingDto floorCeilingDto = new FloorCeilingDto();
        floorCeilingDto.setRoomClass(1);
        floorCeilingDto.setFloor(new BigDecimal(100));
        floorCeilingDto.setCeiling(new BigDecimal(300));

        when(pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(arrivalDate.getArrivalDate(),
                arrivalDate.getArrivalDate())).thenReturn(mergedOffsets);
        transformer.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNativeQuerySingleResult(anyString(), anyMap(), anyObject())).thenReturn(floorCeilingDto);

        transformer.addRcMinMaxValues(arrivalDate, arrDateDetails, groupEvaluation.getGroupEvaluationRoomTypes(), OccupancyType.SINGLE);

        assertNotNull(arrDateDetails.getRcMinMaxValues());

        RcMinMaxValues.RcMinMax rcMinMax = arrDateDetails.getRcMinMaxValues().getRcMinMax().get(0);
        assertEquals(new BigDecimal(11400), rcMinMax.getRcMin());
        assertEquals(new BigDecimal(32400), rcMinMax.getRcMax());

        verify(pricingConfigurationService).findOffsetsForDatesAndBaseOccupancyType(arrivalDate.getArrivalDate(), arrivalDate.getArrivalDate());
    }

    @Test
    public void determinePercentageOfNullInput() {
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), transformer.determinePercentage(null));
    }

    @Test
    public void determinePercentageWithBigDecimal() {
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), transformer.determinePercentage(new BigDecimal(100)));
        assertEquals(new BigDecimal(33).setScale(2, RoundingMode.HALF_UP), transformer.determinePercentage(new BigDecimal(33)));
        assertEquals(new BigDecimal(8).setScale(2, RoundingMode.HALF_UP), transformer.determinePercentage(new BigDecimal(8)));
    }

    @Test
    public void getAllOccupancyDateDates() throws Exception {
        LocalDate date = new LocalDate();

        List<OccupancyDate> occupancyDates = new ArrayList<>();
        OccupancyDate occupancyDate1 = new OccupancyDate();
        occupancyDate1.setDate(buildXMLGregorianCalendar(date));
        occupancyDates.add(occupancyDate1);

        OccupancyDate occupancyDate2 = new OccupancyDate();
        occupancyDate2.setDate(buildXMLGregorianCalendar(date.plusDays(1)));
        occupancyDates.add(occupancyDate2);

        List<Date> dates = transformer.getAllOccupancyDateDates(occupancyDates);
        assertTrue(dates.contains(date.toDate()));
        assertTrue(dates.contains(date.plusDays(1).toDate()));
    }

    @Test
    public void handleStatusCodeParamsWithNullParameters() throws Exception {
        transformer.handleStatusCodeParams(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next(),
                new ArrivalDateType());

        assertNull(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next().getResultCodeParameters());
    }

    @Test
    public void handleStatusCodeParamsWithEmptyParameters() throws Exception {
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        arrivalDateType.setStatusCodeParams(new StatusCodeParams());

        transformer.handleStatusCodeParams(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next(),
                arrivalDateType);

        assertNull(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next().getResultCodeParameters());
    }

    @Test
    public void handleStatusCodeParamsWithOneParameter() throws Exception {
        ArrivalDateType arrivalDateType = new ArrivalDateType();

        StatusCodeParams statusCodeParams = new StatusCodeParams();
        statusCodeParams.getParam().add("JEMS");
        arrivalDateType.setStatusCodeParams(statusCodeParams);

        transformer.handleStatusCodeParams(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next(),
                arrivalDateType);

        assertEquals("JEMS",
                groupEvaluation.getGroupEvaluationArrivalDates().iterator().next().getResultCodeParameters());
    }

    @Test
    public void handleStatusCodeParamsWithTwoParameters() throws Exception {
        ArrivalDateType arrivalDateType = new ArrivalDateType();

        StatusCodeParams statusCodeParams = new StatusCodeParams();
        statusCodeParams.getParam().add("JEMS");
        statusCodeParams.getParam().add("Vaadin");
        arrivalDateType.setStatusCodeParams(statusCodeParams);

        transformer.handleStatusCodeParams(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next(),
                arrivalDateType);

        assertEquals("JEMS,Vaadin",
                groupEvaluation.getGroupEvaluationArrivalDates().iterator().next().getResultCodeParameters());
    }

    @Test
    public void applyResultsWithNewResults_groupPricingOnly() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);

        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalRate(new BigDecimal(100));
        firstArrivalDateType.setBreakevenRate(new BigDecimal(100));
        firstArrivalDateType.setAvgWeightedMAR(new BigDecimal(100));
        firstArrivalDateType.setGrossRoomRev(new BigDecimal(99));
        firstArrivalDateType.setConcessions(new BigDecimal(98));
        firstArrivalDateType.setCommissions(new BigDecimal(97));
        firstArrivalDateType.setNetRoomRev(new BigDecimal(96));
        firstArrivalDateType.setNetIncRoomRev(new BigDecimal(95));
        firstArrivalDateType.setIncRooms(new BigDecimal(5));
        firstArrivalDateType.setRoomProfit(new BigDecimal(93));
        firstArrivalDateType.setIncRoomProfit(new BigDecimal(92));
        firstArrivalDateType.setAncillaryRev(new BigDecimal(91));
        firstArrivalDateType.setAncillaryProfit(new BigDecimal(90));
        firstArrivalDateType.setConfBanqRev(new BigDecimal(89));
        firstArrivalDateType.setConfBanqProfit(new BigDecimal(88));
        firstArrivalDateType.setNetIncRev(new BigDecimal(87));
        firstArrivalDateType.setNetIncProfit(new BigDecimal(86));
        firstArrivalDateType.setTransAncillaryRev(new BigDecimal(85));
        firstArrivalDateType.setTransAncillaryProfit(new BigDecimal(84));
        firstArrivalDateType.setGrossFSRev(new BigDecimal(100));
        firstArrivalDateType.setIncFSProfit(new BigDecimal(85));
        firstArrivalDateType.setNetFSRev(new BigDecimal(85));
        firstArrivalDateType.setBreakevenFSRate(new BigDecimal(50));
        firstArrivalDateType.setFsProfit(new BigDecimal(90));
        firstArrivalDateType.setWishRate(BigDecimal.TEN);
        firstArrivalDateType.setWalkRate(BigDecimal.ONE);

        firstArrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        firstArrivalDateType.setPostStay(new ArrivalDateType.PostStay());
        firstArrivalDateType.getPreStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPreStay().setOccFcstWithoutGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithoutGroup(BigDecimal.TEN);

        firstArrivalDateType.setMinProfitRate(new BigDecimal(100));

        ForecastGroup forecastGroup = new ForecastGroup();
        forecastGroup.setFgID(1);
        forecastGroup.setDisplacedRoomProfit(BigDecimal.TEN);
        forecastGroup.setDisplacedRoomRev(BigDecimal.TEN);
        firstArrivalDateType.getForecastGroup().add(forecastGroup);

        OccupancyDate occupancyDate = new OccupancyDate();
        occupancyDate.setDate(buildXMLGregorianCalendar(new LocalDate()));
        occupancyDate.setDisplacedRooms(BigDecimal.TEN);
        occupancyDate.setOccFcstWithGroup(new BigDecimal(20));
        occupancyDate.setOccFcstWithoutGroup(BigDecimal.TEN);
        forecastGroup.getOccupancyDate().add(occupancyDate);

        ForecastGroupSummary forecastGroupSummary = new ForecastGroupSummary();
        forecastGroupSummary.setId(1);

        when(tenantCrudService.find(ForecastGroupSummary.class, forecastGroup.getFgID())).thenReturn(forecastGroupSummary);
        when(dateService.getPropertyCurrentTime()).thenReturn(new LocalDateTime());
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(false);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertNotNull(groupEvaluation.getEvaluationDate());

        GroupEvaluationArrivalDate firstGroupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        assertEquals(firstArrivalDateType.getOptimalRate(), firstGroupEvaluationArrivalDate.getSuggestedRate());
        assertEquals(firstArrivalDateType.getBreakevenRate(), firstGroupEvaluationArrivalDate.getBreakEvenRate());
        assertEquals(firstArrivalDateType.getAvgWeightedMAR(), firstGroupEvaluationArrivalDate.getAverageWeightedMAR());

        assertEquals(firstArrivalDateType.getGrossRoomRev(), firstGroupEvaluationArrivalDate.getRoomGrossRevenue());
        assertEquals(firstArrivalDateType.getCommissions(), firstGroupEvaluationArrivalDate.getRoomCommissionRevenue());
        assertEquals(firstArrivalDateType.getConcessions(), firstGroupEvaluationArrivalDate.getRoomConcessionRevenue());
        assertEquals(firstArrivalDateType.getNetRoomRev(), firstGroupEvaluationArrivalDate.getRoomNetRevenue());
        assertEquals(firstArrivalDateType.getNetIncRoomRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRoomRevenue());
        assertEquals((groupEvaluation.getTotalNumberOfRooms() - occupancyDate.getDisplacedRooms().intValue()), firstGroupEvaluationArrivalDate.getIncrementalRooms().intValue());
        assertEquals(firstArrivalDateType.getRoomProfit(), firstGroupEvaluationArrivalDate.getRoomGrossProfit());
        assertEquals(firstArrivalDateType.getIncRoomProfit(), firstGroupEvaluationArrivalDate.getIncrementalRoomProfit());
        assertEquals(firstArrivalDateType.getAncillaryRev(), firstGroupEvaluationArrivalDate.getAncillaryGrossRevenue());
        assertEquals(firstArrivalDateType.getAncillaryProfit(), firstGroupEvaluationArrivalDate.getAncillaryGrossProfit());
        assertEquals(firstArrivalDateType.getConfBanqRev(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossRevenue());
        assertEquals(firstArrivalDateType.getConfBanqProfit(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossProfit());
        assertEquals(firstArrivalDateType.getNetIncRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRevenue());
        assertEquals(firstArrivalDateType.getNetIncProfit(), firstGroupEvaluationArrivalDate.getNetIncrementalProfit());
        assertEquals(firstArrivalDateType.getTransAncillaryRev(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryRevenue());
        assertEquals(firstArrivalDateType.getTransAncillaryProfit(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryProfit());
        assertEquals(firstArrivalDateType.getMinProfitRate(), firstGroupEvaluationArrivalDate.getMinProfitRate());

        assertEquals(firstArrivalDateType.getFsProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceProfit());
        assertEquals(firstArrivalDateType.getGrossFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceGrossRevenue());
        assertEquals(firstArrivalDateType.getIncFSProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceIncrementalProfit());
        assertEquals(firstArrivalDateType.getNetFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceNetRevenue());
        assertEquals(firstArrivalDateType.getBreakevenFSRate(), firstGroupEvaluationArrivalDate.getFunctionSpaceBreakEvenRate());
        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, firstGroupEvaluationArrivalDate.getWishRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.ONE, firstGroupEvaluationArrivalDate.getWalkRate()));

        GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup = firstGroupEvaluationArrivalDate.getGroupEvaluationArrivalDateForecastGroups().get(0);
        assertEquals(forecastGroup.getDisplacedRoomProfit(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomProfit());
        assertEquals(forecastGroup.getDisplacedRoomRev(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomRevenue());

        GroupEvaluationArrivalDateForecastGroupDateROH groupEvaluationArrivalDateForecastGroupDateROH = groupEvaluationArrivalDateForecastGroup.getGroupEvaluationArrivalDateForecastGroupDatesROH().get(0);
        assertEquals(occupancyDate.getDisplacedRooms(), groupEvaluationArrivalDateForecastGroupDateROH.getDisplacedRooms());
        assertEquals(occupancyDate.getOccFcstWithGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithGroup());
        assertEquals(occupancyDate.getOccFcstWithoutGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithoutGroup());
    }

    @Test
    public void applyWishAndWalkRateResultsWithNewResultsForGroupPricingOnly() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);

        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalRate(new BigDecimal(100));
        firstArrivalDateType.setBreakevenRate(new BigDecimal(100));
        firstArrivalDateType.setAvgWeightedMAR(new BigDecimal(100));
        firstArrivalDateType.setGrossRoomRev(new BigDecimal(99));
        firstArrivalDateType.setConcessions(new BigDecimal(98));
        firstArrivalDateType.setCommissions(new BigDecimal(97));
        firstArrivalDateType.setNetRoomRev(new BigDecimal(96));
        firstArrivalDateType.setNetIncRoomRev(new BigDecimal(95));
        firstArrivalDateType.setIncRooms(new BigDecimal(5));
        firstArrivalDateType.setRoomProfit(new BigDecimal(93));
        firstArrivalDateType.setIncRoomProfit(new BigDecimal(92));
        firstArrivalDateType.setAncillaryRev(new BigDecimal(91));
        firstArrivalDateType.setAncillaryProfit(new BigDecimal(90));
        firstArrivalDateType.setConfBanqRev(new BigDecimal(89));
        firstArrivalDateType.setConfBanqProfit(new BigDecimal(88));
        firstArrivalDateType.setNetIncRev(new BigDecimal(87));
        firstArrivalDateType.setNetIncProfit(new BigDecimal(86));
        firstArrivalDateType.setTransAncillaryRev(new BigDecimal(85));
        firstArrivalDateType.setTransAncillaryProfit(new BigDecimal(84));
        firstArrivalDateType.setGrossFSRev(new BigDecimal(100));
        firstArrivalDateType.setIncFSProfit(new BigDecimal(85));
        firstArrivalDateType.setNetFSRev(new BigDecimal(85));
        firstArrivalDateType.setBreakevenFSRate(new BigDecimal(50));
        firstArrivalDateType.setFsProfit(new BigDecimal(90));
        firstArrivalDateType.setWishGRRate(BigDecimal.TEN);
        firstArrivalDateType.setWalkGRRate(BigDecimal.ONE);

        firstArrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        firstArrivalDateType.setPostStay(new ArrivalDateType.PostStay());
        firstArrivalDateType.getPreStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPreStay().setOccFcstWithoutGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithoutGroup(BigDecimal.TEN);

        firstArrivalDateType.setMinProfitRate(new BigDecimal(100));

        ForecastGroup forecastGroup = new ForecastGroup();
        forecastGroup.setFgID(1);
        forecastGroup.setDisplacedRoomProfit(BigDecimal.TEN);
        forecastGroup.setDisplacedRoomRev(BigDecimal.TEN);
        firstArrivalDateType.getForecastGroup().add(forecastGroup);

        OccupancyDate occupancyDate = new OccupancyDate();
        occupancyDate.setDate(buildXMLGregorianCalendar(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.now())));
        occupancyDate.setDisplacedRooms(BigDecimal.TEN);
        occupancyDate.setOccFcstWithGroup(new BigDecimal(20));
        occupancyDate.setOccFcstWithoutGroup(BigDecimal.TEN);
        forecastGroup.getOccupancyDate().add(occupancyDate);

        ForecastGroupSummary forecastGroupSummary = new ForecastGroupSummary();
        forecastGroupSummary.setId(1);

        when(tenantCrudService.find(ForecastGroupSummary.class, forecastGroup.getFgID())).thenReturn(forecastGroupSummary);
        when(dateService.getPropertyCurrentTime()).thenReturn(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.now()));
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(false);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertNotNull(groupEvaluation.getEvaluationDate());

        GroupEvaluationArrivalDate firstGroupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        assertEquals(firstArrivalDateType.getOptimalRate(), firstGroupEvaluationArrivalDate.getSuggestedRate());
        assertEquals(firstArrivalDateType.getBreakevenRate(), firstGroupEvaluationArrivalDate.getBreakEvenRate());
        assertEquals(firstArrivalDateType.getAvgWeightedMAR(), firstGroupEvaluationArrivalDate.getAverageWeightedMAR());

        assertEquals(firstArrivalDateType.getGrossRoomRev(), firstGroupEvaluationArrivalDate.getRoomGrossRevenue());
        assertEquals(firstArrivalDateType.getCommissions(), firstGroupEvaluationArrivalDate.getRoomCommissionRevenue());
        assertEquals(firstArrivalDateType.getConcessions(), firstGroupEvaluationArrivalDate.getRoomConcessionRevenue());
        assertEquals(firstArrivalDateType.getNetRoomRev(), firstGroupEvaluationArrivalDate.getRoomNetRevenue());
        assertEquals(firstArrivalDateType.getNetIncRoomRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRoomRevenue());
        assertEquals((groupEvaluation.getTotalNumberOfRooms() - occupancyDate.getDisplacedRooms().intValue()), firstGroupEvaluationArrivalDate.getIncrementalRooms().intValue());
        assertEquals(firstArrivalDateType.getRoomProfit(), firstGroupEvaluationArrivalDate.getRoomGrossProfit());
        assertEquals(firstArrivalDateType.getIncRoomProfit(), firstGroupEvaluationArrivalDate.getIncrementalRoomProfit());
        assertEquals(firstArrivalDateType.getAncillaryRev(), firstGroupEvaluationArrivalDate.getAncillaryGrossRevenue());
        assertEquals(firstArrivalDateType.getAncillaryProfit(), firstGroupEvaluationArrivalDate.getAncillaryGrossProfit());
        assertEquals(firstArrivalDateType.getConfBanqRev(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossRevenue());
        assertEquals(firstArrivalDateType.getConfBanqProfit(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossProfit());
        assertEquals(firstArrivalDateType.getNetIncRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRevenue());
        assertEquals(firstArrivalDateType.getNetIncProfit(), firstGroupEvaluationArrivalDate.getNetIncrementalProfit());
        assertEquals(firstArrivalDateType.getTransAncillaryRev(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryRevenue());
        assertEquals(firstArrivalDateType.getTransAncillaryProfit(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryProfit());
        assertEquals(firstArrivalDateType.getMinProfitRate(), firstGroupEvaluationArrivalDate.getMinProfitRate());

        assertEquals(firstArrivalDateType.getFsProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceProfit());
        assertEquals(firstArrivalDateType.getGrossFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceGrossRevenue());
        assertEquals(firstArrivalDateType.getIncFSProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceIncrementalProfit());
        assertEquals(firstArrivalDateType.getNetFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceNetRevenue());
        assertEquals(firstArrivalDateType.getBreakevenFSRate(), firstGroupEvaluationArrivalDate.getFunctionSpaceBreakEvenRate());
        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, firstGroupEvaluationArrivalDate.getWishRate()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.ONE, firstGroupEvaluationArrivalDate.getWalkRate()));

        GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup = firstGroupEvaluationArrivalDate.getGroupEvaluationArrivalDateForecastGroups().get(0);
        assertEquals(forecastGroup.getDisplacedRoomProfit(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomProfit());
        assertEquals(forecastGroup.getDisplacedRoomRev(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomRevenue());

        GroupEvaluationArrivalDateForecastGroupDateROH groupEvaluationArrivalDateForecastGroupDateROH = groupEvaluationArrivalDateForecastGroup.getGroupEvaluationArrivalDateForecastGroupDatesROH().get(0);
        assertEquals(occupancyDate.getDisplacedRooms(), groupEvaluationArrivalDateForecastGroupDateROH.getDisplacedRooms());
        assertEquals(occupancyDate.getOccFcstWithGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithGroup());
        assertEquals(occupancyDate.getOccFcstWithoutGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithoutGroup());
    }

    @Test
    public void applyResultsWithNewResults_functionSpaceLicenseOn() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);

        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalRate(new BigDecimal(100));
        firstArrivalDateType.setBreakevenRate(new BigDecimal(100));
        firstArrivalDateType.setAvgWeightedMAR(new BigDecimal(100));
        firstArrivalDateType.setGrossRoomRev(new BigDecimal(99));
        firstArrivalDateType.setConcessions(new BigDecimal(98));
        firstArrivalDateType.setCommissions(new BigDecimal(97));
        firstArrivalDateType.setNetRoomRev(new BigDecimal(96));
        firstArrivalDateType.setNetIncRoomRev(new BigDecimal(95));
        firstArrivalDateType.setIncRooms(new BigDecimal(5));
        firstArrivalDateType.setRoomProfit(new BigDecimal(93));
        firstArrivalDateType.setIncRoomProfit(new BigDecimal(92));
        firstArrivalDateType.setAncillaryRev(new BigDecimal(91));
        firstArrivalDateType.setAncillaryProfit(new BigDecimal(90));
        firstArrivalDateType.setConfBanqRev(new BigDecimal(89));
        firstArrivalDateType.setConfBanqProfit(new BigDecimal(88));
        firstArrivalDateType.setNetIncRev(new BigDecimal(87));
        firstArrivalDateType.setNetIncProfit(new BigDecimal(86));
        firstArrivalDateType.setDisplacedAncillaryRev(new BigDecimal(85));
        firstArrivalDateType.setDisplacedAncillaryProfit(new BigDecimal(84));
        firstArrivalDateType.setGrossFSRev(new BigDecimal(100));
        firstArrivalDateType.setIncFSProfit(new BigDecimal(85));
        firstArrivalDateType.setNetFSRev(new BigDecimal(85));
        firstArrivalDateType.setBreakevenFSRate(new BigDecimal(50));
        firstArrivalDateType.setFsProfit(new BigDecimal(90));

        firstArrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        firstArrivalDateType.setPostStay(new ArrivalDateType.PostStay());
        firstArrivalDateType.getPreStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPreStay().setOccFcstWithoutGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithoutGroup(BigDecimal.TEN);

        firstArrivalDateType.setMinProfitRate(new BigDecimal(100));

        ForecastGroup forecastGroup = new ForecastGroup();
        forecastGroup.setFgID(1);
        forecastGroup.setDisplacedRoomProfit(BigDecimal.TEN);
        forecastGroup.setDisplacedRoomRev(BigDecimal.TEN);
        firstArrivalDateType.getForecastGroup().add(forecastGroup);

        OccupancyDate occupancyDate = new OccupancyDate();
        occupancyDate.setDate(buildXMLGregorianCalendar(new LocalDate()));
        occupancyDate.setDisplacedRooms(BigDecimal.TEN);
        occupancyDate.setOccFcstWithGroup(new BigDecimal(20));
        occupancyDate.setOccFcstWithoutGroup(BigDecimal.TEN);
        forecastGroup.getOccupancyDate().add(occupancyDate);

        ForecastGroupSummary forecastGroupSummary = new ForecastGroupSummary();
        forecastGroupSummary.setId(1);

        when(tenantCrudService.find(ForecastGroupSummary.class, forecastGroup.getFgID())).thenReturn(forecastGroupSummary);
        when(dateService.getPropertyCurrentTime()).thenReturn(new LocalDateTime());
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertNotNull(groupEvaluation.getEvaluationDate());

        GroupEvaluationArrivalDate firstGroupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        assertEquals(firstArrivalDateType.getOptimalRate(), firstGroupEvaluationArrivalDate.getSuggestedRate());
        assertEquals(firstArrivalDateType.getBreakevenRate(), firstGroupEvaluationArrivalDate.getBreakEvenRate());
        assertEquals(firstArrivalDateType.getAvgWeightedMAR(), firstGroupEvaluationArrivalDate.getAverageWeightedMAR());

        assertEquals(firstArrivalDateType.getGrossRoomRev(), firstGroupEvaluationArrivalDate.getRoomGrossRevenue());
        assertEquals(firstArrivalDateType.getCommissions(), firstGroupEvaluationArrivalDate.getRoomCommissionRevenue());
        assertEquals(firstArrivalDateType.getConcessions(), firstGroupEvaluationArrivalDate.getRoomConcessionRevenue());
        assertEquals(firstArrivalDateType.getNetRoomRev(), firstGroupEvaluationArrivalDate.getRoomNetRevenue());
        assertEquals(firstArrivalDateType.getNetIncRoomRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRoomRevenue());
        assertEquals((groupEvaluation.getTotalNumberOfRooms() - occupancyDate.getDisplacedRooms().intValue()), firstGroupEvaluationArrivalDate.getIncrementalRooms().intValue());
        assertEquals(firstArrivalDateType.getRoomProfit(), firstGroupEvaluationArrivalDate.getRoomGrossProfit());
        assertEquals(firstArrivalDateType.getIncRoomProfit(), firstGroupEvaluationArrivalDate.getIncrementalRoomProfit());
        assertEquals(firstArrivalDateType.getAncillaryRev(), firstGroupEvaluationArrivalDate.getAncillaryGrossRevenue());
        assertEquals(firstArrivalDateType.getAncillaryProfit(), firstGroupEvaluationArrivalDate.getAncillaryGrossProfit());
        assertEquals(firstArrivalDateType.getConfBanqRev(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossRevenue());
        assertEquals(firstArrivalDateType.getConfBanqProfit(), firstGroupEvaluationArrivalDate.getConferenceAndBanquetGrossProfit());
        assertEquals(firstArrivalDateType.getNetIncRev(), firstGroupEvaluationArrivalDate.getNetIncrementalRevenue());
        assertEquals(firstArrivalDateType.getNetIncProfit(), firstGroupEvaluationArrivalDate.getNetIncrementalProfit());
        assertEquals(firstArrivalDateType.getDisplacedAncillaryRev(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryRevenue());
        assertEquals(firstArrivalDateType.getDisplacedAncillaryProfit(), firstGroupEvaluationArrivalDate.getDisplacedAncillaryProfit());
        assertEquals(firstArrivalDateType.getMinProfitRate(), firstGroupEvaluationArrivalDate.getMinProfitRate());

        assertEquals(firstArrivalDateType.getFsProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceProfit());
        assertEquals(firstArrivalDateType.getGrossFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceGrossRevenue());
        assertEquals(firstArrivalDateType.getIncFSProfit(), firstGroupEvaluationArrivalDate.getFunctionSpaceIncrementalProfit());
        assertEquals(firstArrivalDateType.getNetFSRev(), firstGroupEvaluationArrivalDate.getFunctionSpaceNetRevenue());
        assertEquals(firstArrivalDateType.getBreakevenFSRate(), firstGroupEvaluationArrivalDate.getFunctionSpaceBreakEvenRate());

        GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup = firstGroupEvaluationArrivalDate.getGroupEvaluationArrivalDateForecastGroups().get(0);
        assertEquals(forecastGroup.getDisplacedRoomProfit(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomProfit());
        assertEquals(forecastGroup.getDisplacedRoomRev(), groupEvaluationArrivalDateForecastGroup.getDisplacedRoomRevenue());

        GroupEvaluationArrivalDateForecastGroupDateROH groupEvaluationArrivalDateForecastGroupDateROH = groupEvaluationArrivalDateForecastGroup.getGroupEvaluationArrivalDateForecastGroupDatesROH().get(0);
        assertEquals(occupancyDate.getDisplacedRooms(), groupEvaluationArrivalDateForecastGroupDateROH.getDisplacedRooms());
        assertEquals(occupancyDate.getOccFcstWithGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithGroup());
        assertEquals(occupancyDate.getOccFcstWithoutGroup(), groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyForecastWithoutGroup());
    }

    @Test
    public void addFunctionSpace_Equal() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        // create two identical function spaces
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);
        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);

        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);
        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, fs1.getStartTime().toLocalDate()))
                .thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(14));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());
        // should only have one day part
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(
                (fs1.getSquareFeet().add(fs2.getSquareFeet())
                        .divide(sqFtPerPriceTier.get(FunctionSpaceFunctionRoomPriceTier.TIER_1))
                        .setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)),
                arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void shouldCreateFSNotOverlappingTimeWithSingleFuncRoom() {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs1.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 8, 0, 0)));
        fs1.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 10, 0, 0)));

        GroupEvaluationFunctionSpace fs2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 9, 0, 0)));
        fs2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 11, 0, 0)));

        GroupEvaluationFunctionSpace fs3 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs3.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 16, 0, 0)));
        fs3.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 17, 0, 0)));

        GroupEvaluationFunctionSpace fs4 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs4.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 17, 0, 0)));
        fs4.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 18, 0, 0)));

        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs2);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs3);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs4);

        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaceSet = transformer.prepareGroupEvaluationFunctionSpaceForOverlappingTime(groupEval);
        assertEquals(3, groupEvaluationFunctionSpaceSet.size());

    }

    @Test
    public void shouldCreateGroupEvaluationFSForOverlappingTimeForMultipleFunctionRooms() {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs1.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 8, 0, 0)));
        fs1.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 12, 0, 0)));
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);


        GroupEvaluationFunctionSpace fs2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        java.time.LocalDateTime startDate = java.time.LocalDateTime.of(2020, 1, 31, 7, 0, 0);
        java.time.LocalDateTime endDate = java.time.LocalDateTime.of(2020, 1, 31, 9, 0, 0);
        fs2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(startDate));
        fs2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(endDate));
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs2);

        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces = transformer.prepareGroupEvaluationFunctionSpaceForOverlappingTime(groupEval);
        assertEquals(1, groupEvaluationFunctionSpaces.size());
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs2.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaces.iterator().next().getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs1.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaces.iterator().next().getEndTime()));

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        java.time.LocalDateTime startDateRoyalB = java.time.LocalDateTime.of(2020, 1, 31, 13, 0, 0);
        java.time.LocalDateTime endDateRoyalB = java.time.LocalDateTime.of(2020, 1, 31, 17, 0, 0);
        royalB.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(startDateRoyalB));
        royalB.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(endDateRoyalB));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        GroupEvaluationFunctionSpace royalB2 = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        java.time.LocalDateTime startDateRoyalB2 = java.time.LocalDateTime.of(2020, 1, 31, 8, 0, 0);
        java.time.LocalDateTime endDateRoyalB2 = java.time.LocalDateTime.of(2020, 1, 31, 17, 0, 0);
        royalB2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(startDateRoyalB2));
        royalB2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(endDateRoyalB2));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB2);

        groupEvaluationFunctionSpaces = transformer.prepareGroupEvaluationFunctionSpaceForOverlappingTime(groupEval);
        assertEquals(2, groupEvaluationFunctionSpaces.size());

        List<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaceList = new ArrayList<>(groupEvaluationFunctionSpaces);
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(royalB2.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(0).getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(royalB2.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(0).getEndTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs2.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(1).getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs1.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(1).getEndTime()));
    }

    @Test
    public void shouldCreateGroupEvaluationFSForOverlappingTimeForCombinationRooms() {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);
        GroupEvaluationFunctionSpace fs2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 8, 00, 00)));
        fs2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 9, 00, 00)));
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs2);

        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaceSet = transformer.prepareGroupEvaluationFunctionSpaceForOverlappingTime(groupEval);
        assertEquals(1, groupEvaluationFunctionSpaceSet.size());
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs2.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceSet.iterator().next().getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs1.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceSet.iterator().next().getEndTime()));

        GroupEvaluationFunctionSpace comboFuncSpace = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        comboFuncSpace.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 13, 0, 0)));
        comboFuncSpace.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 17, 0, 0)));
        comboFuncSpace.setGroupEvaluationFunctionSpaceFunctionRooms(new HashSet<>());
        FunctionSpaceCombinationFunctionRoom com = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Some Combo Room");
        com.setCombo(true);
        roomB.setFunctionSpaceFunctionRoom(com);
        comboFuncSpace.getGroupEvaluationFunctionSpaceFunctionRooms().add(roomB);
        groupEval.getGroupEvaluationFunctionSpaces().add(comboFuncSpace);

        groupEvaluationFunctionSpaceSet = transformer.prepareGroupEvaluationFunctionSpaceForOverlappingTime(groupEval);

        assertEquals(3, groupEvaluationFunctionSpaceSet.size());

        List<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaceList = new ArrayList<>(groupEvaluationFunctionSpaceSet);

        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs2.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(0).getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(fs1.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(0).getEndTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(comboFuncSpace.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(1).getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(comboFuncSpace.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(1).getEndTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(comboFuncSpace.getStartTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(2).getStartTime()));
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(comboFuncSpace.getEndTime()), JavaLocalDateUtils.jodaToJavaLocalDateTime(groupEvaluationFunctionSpaceList.get(2).getEndTime()));
    }


    @Test
    public void addFunctionSpace_TwoRoomsWithDifferentPriceTiers() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        // create two group eval function rooms with different price tiers
        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        GroupEvaluationFunctionSpaceFunctionRoom redRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        redRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        redRoom.getFunctionSpaceFunctionRoom()
                .setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_3);

        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(redRoom);

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_3, new BigDecimal(3000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);
        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(
                functionSpaceConfigurationService.getMARByPriceTierMap(rooms, groupEval.getContractedArrivalDate()))
                .thenReturn(priceTierMARMap);

        // mock out day part hours available
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(24));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(2, arrDateDetails.getDayParts().getDaypart().size());
    }

    @Test
    public void addFunctionSpace_TwoRoomsWithSamePriceTier() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        // create two group eval function rooms with same price tier
        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        GroupEvaluationFunctionSpaceFunctionRoom redRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        redRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));

        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(redRoom);

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms =
                asList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(
                functionSpaceConfigurationService.getMARByPriceTierMap(rooms, groupEval.getContractedArrivalDate()))
                .thenReturn(priceTierMARMap);
        // mock out day part hours available
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(24));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(
                blueRoom.getFunctionSpaceFunctionRoom().getAreaSqFeet()
                        .add(redRoom.getFunctionSpaceFunctionRoom().getAreaSqFeet()).divide(new BigDecimal(5000))
                        .setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP),
                arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_SingleRoom() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<FunctionSpaceFunctionRoomPriceTier, BigDecimal>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        blueRoom.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        List<FunctionSpaceFunctionRoom> allRooms = singletonList(blueRoom.getFunctionSpaceFunctionRoom());
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate()))
                .thenReturn(priceTierMARMap);
        // mock out day part hours available to 4 for this day part
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(4));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(
                blueRoom.getFunctionSpaceFunctionRoom().getAreaSqFeet().divide(new BigDecimal(5000))
                        .setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP),
                arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        // expected min = room mar (250 / (totals hours in day part (4)) * day part hours requested (1)
        assertEquals(new BigDecimal(62.50).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getFunctionSpaceMinRate());
        // expected max rate = upper room limit (1000) / (totals hours in day part (4)) * hours requested (1)
        assertEquals(new BigDecimal(250.00).setScale(2, RoundingMode.HALF_UP),
                arrDateDetails.getFunctionSpaceMaxRate());
    }

    @Test
    public void addFunctionSpace_OneRoom24Hours() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 10, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 23, 59, 59, 999));

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setStartTime(new LocalDateTime(2018, 10, 2, 0, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 2, 10, 0));

        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        blueRoom.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        List<FunctionSpaceFunctionRoom> allRooms = singletonList(blueRoom.getFunctionSpaceFunctionRoom());
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(6, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(0).getDate().getDay());
        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(1).getDate().getDay());
        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(2).getDate().getDay());
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(3).getDate().getDay());
        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(4).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(4).getDate().getDay());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(5).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(5).getDate().getDay());
    }

    @Test
    public void addFunctionSpace_Area24Hours() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(1000));
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 10, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 23, 59, 59, 999));

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.valueOf(1000));
        fs2.setStartTime(new LocalDateTime(2018, 10, 2, 0, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 2, 10, 0));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(Collections.singletonList(blueRoom));
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs2.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(6, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(0).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(1).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(2).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(3).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(3).getPercentRequested());
        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(4).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(4).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(4).getPercentRequested());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(5).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(5).getDate().getDay());
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(5).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_TwoRoomsAcrossDayParts() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 14, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 15, 0, 0));

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setStartTime(new LocalDateTime(2018, 10, 1, 14, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 1, 18, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(roomB);

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        // Weighted avg minutes calculation: (.1*60+.4*240)/(.1+.4) = 204 minutes sent in request xml
        assertEquals(204, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(50).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_OneRoomSingleDayPart() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 19, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 21, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(120, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(10).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_OneRoomSingleDayPartSpanMidnight() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 19, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(2, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(BigDecimal.valueOf(10).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(BigDecimal.valueOf(10).setScale(2), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_OneRoomSingleDayPartAreaRequest() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(100));
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 19, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 21, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(120, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(10).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_OneRoomSingleDayPartTillMidnightExclusive() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 19, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 23, 59, 59, 999));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(113)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(388)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(509)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1009));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(11.20).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_OneRoomSingleDayPartTillMidnightInclusive() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 19, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(113)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(388)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(509)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1009));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(11.20).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_TwoRoomsSpanningMidnight() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.ZERO);
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0, 0));

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.ZERO);
        fs2.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));
        roomA.getFunctionSpaceFunctionRoom().setId(1);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));
        roomB.getFunctionSpaceFunctionRoom().setId(2);
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(roomB);

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(2, arrDateDetails.getDayParts().getDaypart().size());
        // Weighted avg minutes calculation: (.1*60+.4*240)/(.1+.4) = 204 minutes sent in request xml
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(50).setScale(2), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(0).getDaypartId());

        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(BigDecimal.valueOf(50).setScale(2), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(1).getDaypartId());
    }

    @Test
    public void addFunctionSpace_AreaSpansMidnight() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(1000));
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 23, 59, 59, 999));

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.valueOf(1000));
        fs2.setStartTime(new LocalDateTime(2018, 10, 2, 0, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(Collections.singletonList(blueRoom));
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs2.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(20).setScale(2, HALF_UP), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(BigDecimal.valueOf(20).setScale(2, HALF_UP), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_AreaEvalSamePriceTier() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(1000));
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 19, 0, 0));

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.valueOf(1000));
        fs2.setStartTime(new LocalDateTime(2018, 10, 1, 20, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 1, 21, 0));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(Collections.singletonList(blueRoom));
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(Collections.singletonList(blueRoom), fs2.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(40).setScale(2, HALF_UP), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_TwoRoomsSameDayStartingAtMidnight() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 2, 0, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 19, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(9750.00)));
        roomA.getFunctionSpaceFunctionRoom().setId(1);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(7475.00)));
        roomB.getFunctionSpaceFunctionRoom().setId(2);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomB);

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(39348.83));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(4, arrDateDetails.getDayParts().getDaypart().size());

        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(43.78), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(0).getDaypartId());

        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(BigDecimal.valueOf(43.78), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(1).getDaypartId());

        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(BigDecimal.valueOf(43.78), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());
        assertEquals(4, arrDateDetails.getDayParts().getDaypart().get(2).getDaypartId());

        assertEquals(120, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(BigDecimal.valueOf(43.78), arrDateDetails.getDayParts().getDaypart().get(3).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(3).getDaypartId());
    }

    @Test
    public void addFunctionSpace_MixRoomWithAreaForSamePriceTier() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 10, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 1, 11, 0));

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.valueOf(150));
        fs2.setStartTime(new LocalDateTime(2018, 10, 1, 10, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 1, 11, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("B", BigDecimal.valueOf(400)));

        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("C", BigDecimal.valueOf(500)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(1000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());
        allRooms.add(roomB.getFunctionSpaceFunctionRoom());
        allRooms.add(roomC.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(BigDecimal.valueOf(25.00).setScale(2, ROUND_HALF_UP), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_Area2Days() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(10000.00));
        fs1.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 10, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 3, 23, 59, 59, 999));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(39348.83));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 12 day parts across two days
        assertEquals(12, arrDateDetails.getDayParts().getDaypart().size());

        // Verify minutes requested for each day part per day, as well as day of week. price tier and percent
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(0).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(0).getDate().getDay());
        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(1).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(1).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());

        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(2).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(2).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());

        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(3).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(3).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(3).getPercentRequested());

        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(4).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(4).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(4).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(4).getPercentRequested());

        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(5).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(5).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(5).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(5).getPercentRequested());

        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(6).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(6).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(6).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(6).getPercentRequested());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(7).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(7).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(7).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(7).getPercentRequested());

        assertEquals(300, arrDateDetails.getDayParts().getDaypart().get(8).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(8).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(8).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(8).getPercentRequested());

        assertEquals(180, arrDateDetails.getDayParts().getDaypart().get(9).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(9).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(9).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(9).getPercentRequested());

        assertEquals(240, arrDateDetails.getDayParts().getDaypart().get(10).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(10).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(10).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(10).getPercentRequested());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(11).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(11).getDate().getDay());
        assertEquals(FunctionSpaceFunctionRoomPriceTier.TIER_1.getId(), arrDateDetails.getDayParts().getDaypart().get(11).getPriceTier());
        assertEquals(BigDecimal.valueOf(25.41), arrDateDetails.getDayParts().getDaypart().get(11).getPercentRequested());
    }

    @Test
    public void addFunctionSpace_AreaSpansMidnightMultipleFunctionSpaces() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();

        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setSquareFeet(BigDecimal.valueOf(2860.00));
        fs1.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0, 0));

        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setSquareFeet(BigDecimal.valueOf(2860.00));
        fs2.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs2.setStartTime(new LocalDateTime(2018, 10, 2, 18, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 3, 1, 0, 0));

        GroupEvaluationFunctionSpace fs3 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs3.setSquareFeet(BigDecimal.valueOf(2860.00));
        fs3.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs3.setStartTime(new LocalDateTime(2018, 10, 3, 18, 0));
        fs3.setEndTime(new LocalDateTime(2018, 10, 4, 1, 0, 0));

        GroupEvaluationFunctionSpace fs4 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs4.setSquareFeet(BigDecimal.valueOf(2860.00));
        fs4.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs4.setStartTime(new LocalDateTime(2018, 10, 4, 18, 0));
        fs4.setEndTime(new LocalDateTime(2018, 10, 5, 1, 0, 0));

        GroupEvaluationFunctionSpace fs5 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs5.setSquareFeet(BigDecimal.valueOf(2860.00));
        fs5.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs5.setStartTime(new LocalDateTime(2018, 10, 5, 18, 0));
        fs5.setEndTime(new LocalDateTime(2018, 10, 6, 1, 0, 0));

        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(100)));

        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);
        spaces.add(fs3);
        spaces.add(fs4);
        spaces.add(fs5);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(39348.83));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs4.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs4.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs4.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs5.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs5.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs5.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(6, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(0).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(0).getDaypartId());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(1).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(1).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(1).getDaypartId());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(2).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(2).getDaypartId());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(4, arrDateDetails.getDayParts().getDaypart().get(3).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(3).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(3).getDaypartId());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(4).getMinutes());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(4).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(4).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(4).getDaypartId());

        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(5).getMinutes());
        assertEquals(6, arrDateDetails.getDayParts().getDaypart().get(5).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(5).getPercentRequested());
        assertEquals(5, arrDateDetails.getDayParts().getDaypart().get(5).getDaypartId());
    }

    @Test
    public void addFunctionSpace_RoomSpansMidnightMultipleFunctionSpaces() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("A", BigDecimal.valueOf(2860)));

        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        fs1.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs1.setStartTime(new LocalDateTime(2018, 10, 1, 18, 0));
        fs1.setEndTime(new LocalDateTime(2018, 10, 2, 1, 0, 0));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);


        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        fs2.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs2.setStartTime(new LocalDateTime(2018, 10, 2, 18, 0));
        fs2.setEndTime(new LocalDateTime(2018, 10, 3, 1, 0, 0));
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);


        GroupEvaluationFunctionSpace fs3 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        fs3.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        fs3.setStartTime(new LocalDateTime(2018, 10, 3, 18, 0));
        fs3.setEndTime(new LocalDateTime(2018, 10, 4, 1, 0, 0));
        fs3.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);


        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);
        spaces.add(fs2);
        spaces.add(fs3);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        setupDayParts();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(39348.83));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier()).thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomMARSeasons(null);
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();
        allRooms.add(roomA.getFunctionSpaceFunctionRoom());

        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(allRooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs1.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs2.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate().plusDays(1))).thenReturn(priceTierMARMap);
        when(functionSpaceConfigurationService.getMARByPriceTierMap(allRooms, fs3.getStartTime().toLocalDate().plusDays(2))).thenReturn(priceTierMARMap);

        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(18));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        // Should be 6 day parts across two days
        assertEquals(4, arrDateDetails.getDayParts().getDaypart().size());
        // Verify minutes requested for each day part per day, as well as day of week
        assertEquals(360, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().get(0).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(1).getMinutes());
        assertEquals(2, arrDateDetails.getDayParts().getDaypart().get(1).getDate().getDay());

        assertEquals(420, arrDateDetails.getDayParts().getDaypart().get(2).getMinutes());
        assertEquals(3, arrDateDetails.getDayParts().getDaypart().get(2).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());

        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(3).getMinutes());
        assertEquals(4, arrDateDetails.getDayParts().getDaypart().get(3).getDate().getDay());
        assertEquals(BigDecimal.valueOf(7.27), arrDateDetails.getDayParts().getDaypart().get(2).getPercentRequested());
    }

    private List<FunctionSpaceDayPart> setupDayParts() {
        // build function space day parts
        List<FunctionSpaceDayPart> fsDayParts = new ArrayList();

        FunctionSpaceDayPart overnight = new FunctionSpaceDayPart();
        overnight.setId(1);
        overnight.setIncluded(false);
        overnight.setBeginTime(new LocalTime(2, 0, 0, 0));
        overnight.setEndTime(new LocalTime(6, 0, 0, 0));

        FunctionSpaceDayPart morning = new FunctionSpaceDayPart();
        morning.setId(2);
        morning.setIncluded(true);
        morning.setBeginTime(new LocalTime(6, 0, 0, 0));
        morning.setEndTime(new LocalTime(11, 0, 0, 0));

        FunctionSpaceDayPart noon = new FunctionSpaceDayPart();
        noon.setId(3);
        noon.setIncluded(true);
        noon.setBeginTime(new LocalTime(11, 0, 0, 0));
        noon.setEndTime(new LocalTime(14, 0, 0, 0));

        FunctionSpaceDayPart afternoon = new FunctionSpaceDayPart();
        afternoon.setId(4);
        afternoon.setIncluded(true);
        afternoon.setBeginTime(new LocalTime(14, 0, 0, 0));
        afternoon.setEndTime(new LocalTime(18, 0, 0, 0));

        FunctionSpaceDayPart evening = new FunctionSpaceDayPart();
        evening.setId(5);
        evening.setIncluded(true);
        evening.setBeginTime(new LocalTime(18, 0, 0, 0));
        evening.setEndTime(new LocalTime(1, 0, 0, 0));

        fsDayParts.add(overnight);
        fsDayParts.add(morning);
        fsDayParts.add(noon);
        fsDayParts.add(afternoon);
        fsDayParts.add(evening);

        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(fsDayParts);
        return fsDayParts;
    }

    @Test
    public void addFunctionSpace_SingleRoomWithSeasonalMAR() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<FunctionSpaceFunctionRoomPriceTier, BigDecimal>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(functionSpaceConfigurationService.getMARByPriceTierMap(rooms, fs1.getStartTime().toLocalDate())).thenReturn(priceTierMARMap);
        // mock out day part hours available to 4 for this day part
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay()).thenReturn(new BigDecimal(4));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(blueRoom.getFunctionSpaceFunctionRoom().getAreaSqFeet().divide(new BigDecimal(5000)).setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP),
                arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
        // expected min = season room mar (175 / (totals hours in day part (4)) * hours requested (1)
        assertEquals(new BigDecimal(43.75).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getFunctionSpaceMinRate());
        // expected max rate = upper room limit (1000) / (totals hours in day part (4)) * hours requested (1)
        assertEquals(new BigDecimal(250.00).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getFunctionSpaceMaxRate());

    }

    @Test
    public void addFunctionSpace_ArrivalDateIsNotPreferredDate() throws Exception {
        GroupEvaluation groupEval = new GroupEvaluation();

        // add non-preferred arrival date
        GroupEvaluationArrivalDate alternateArrivalDate = new GroupEvaluationArrivalDate();
        alternateArrivalDate.setArrivalDate(new LocalDate(2015, 1, 15));
        alternateArrivalDate.setPreferredDate(false);
        // add preferred arrival date
        GroupEvaluationArrivalDate preferredArrivalDate = new GroupEvaluationArrivalDate();
        preferredArrivalDate.setArrivalDate(new LocalDate(2015, 1, 1));
        preferredArrivalDate.setPreferredDate(true);

        groupEval.addGroupEvaluationArrivalDate(alternateArrivalDate);
        groupEval.addGroupEvaluationArrivalDate(preferredArrivalDate);

        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother
                .buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        GroupEvaluationFunctionSpaceFunctionRoom blueRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        blueRoom.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue"));
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(blueRoom);
        Set<GroupEvaluationFunctionSpace> spaces = new LinkedHashSet<>();
        spaces.add(fs1);

        // add them to the group evaluation
        groupEval.setGroupEvaluationFunctionSpaces(spaces);
        List<FunctionSpaceDayPart> fsDayParts = mockDayParts();
        when(functionSpaceDemandCalendarService.isWithinTimeRange(fs1.getStartTime().toLocalTime(),
                fs1.getEndTime().toLocalTime(), fsDayParts.get(0).getBeginTime(), fsDayParts.get(0).getEndTime()))
                .thenReturn(true);
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(5000));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);

        // mock out call to get all active rooms
        List<FunctionSpaceFunctionRoom> rooms = singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red"));
        when(functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing()).thenReturn(rooms);
        // mock out price tier map
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = buildPriceTierMARMap();
        when(
                functionSpaceConfigurationService.getMARByPriceTierMap(rooms, groupEval.getContractedArrivalDate()))
                .thenReturn(priceTierMARMap);
        // mock out day part hours available
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(14));

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addFunctionSpace(groupEval, arrDateDetails, alternateArrivalDate);

        Date startDate = convertToDate(arrDateDetails.getDayParts().getDaypart().get(0).getDate());

        assertEquals(new LocalDate(2015, 1, 15), new LocalDate(startDate));
        assertEquals(1, arrDateDetails.getDayParts().getDaypart().size());
        assertEquals(
                blueRoom.getFunctionSpaceFunctionRoom().getAreaSqFeet().divide(new BigDecimal(5000))
                        .setScale(4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP),
                arrDateDetails.getDayParts().getDaypart().get(0).getPercentRequested());
        assertEquals(60, arrDateDetails.getDayParts().getDaypart().get(0).getMinutes());
    }

    @Test
    public void handleFunctionSpaceDateResults() throws Exception {
        LocalDate arrivalDate = new LocalDate(2015, 1, 1);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.addGroupEvaluationArrivalDate(groupEvaluationArrivalDate);

        ArrivalDateType arrivalDateType = new ArrivalDateType();
        FunctionSpace functionSpace = new FunctionSpace();
        com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate occupanceDate = new com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate();
        occupanceDate.setDate(buildXMLGregorianCalendar(arrivalDate));
        // Create day part
        DayPart dayPart = new DayPart();
        dayPart.setDayPartID(1);
        dayPart.setDisplacedFSProfit(new BigDecimal(122));
        dayPart.setDisplacedFSRev(new BigDecimal(150));
        dayPart.setFsUtilFcstWithGroup(new BigDecimal(300));
        dayPart.setFsUtilFcstWithoutGroup(new BigDecimal(250));
        // Add day part to occupancy date
        occupanceDate.getDayPart().add(dayPart);
        arrivalDateType.setFunctionSpace(functionSpace);
        arrivalDateType.getFunctionSpace().getOccupancyDate().add(occupanceDate);

        FunctionSpaceDayPart fsDayPart = FunctionSpaceObjectMother.buildDayPart();
        when(functionSpaceConfigurationService.findDayPartById(1)).thenReturn(fsDayPart);
        when(dateService.getCaughtUpLocalDate()).thenReturn(arrivalDate);

        transformer.handleFunctionSpaceDateResults(groupEvaluationArrivalDate, arrivalDateType);

        assertEquals(1, groupEvaluationArrivalDate.getGroupEvaluationFunctionSpaceArrivalDates().size());

        GroupEvaluationFunctionSpaceArrivalDate fsArrivalDate = groupEvaluationArrivalDate
                .getGroupEvaluationFunctionSpaceArrivalDates().get(0);

        assertTrue(fsArrivalDate.getGroupEvaluationFunctionSpaceArrivalDateDayParts().size() == 1);
        assertEquals(new BigDecimal(122),
                fsArrivalDate.getGroupEvaluationFunctionSpaceArrivalDateDayParts().get(0).getDisplacedProfit());
        assertEquals(new BigDecimal(300), fsArrivalDate.getGroupEvaluationFunctionSpaceArrivalDateDayParts().get(0)
                .getUtilizationFcstWithGroup());
        assertEquals(new BigDecimal(250), fsArrivalDate.getGroupEvaluationFunctionSpaceArrivalDateDayParts().get(0)
                .getUtilizationFcstWithoutGroup());

        verify(functionSpaceConfigurationService).findDayPartById(1);
        verify(dateService).getCaughtUpLocalDate();
    }

    @Test
    public void handleFunctionSpaceDateResults_WithOccupancyDatePriorToSystemDate() throws Exception {
        LocalDate systemDate = new LocalDate(2015, 7, 1);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setArrivalDate(systemDate);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.addGroupEvaluationArrivalDate(groupEvaluationArrivalDate);

        ArrivalDateType arrivalDateType = new ArrivalDateType();
        FunctionSpace functionSpace = new FunctionSpace();
        com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate occupanceDate = new com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate();
        occupanceDate.setDate(buildXMLGregorianCalendar(systemDate.minusDays(1)));

        arrivalDateType.setFunctionSpace(functionSpace);
        arrivalDateType.getFunctionSpace().getOccupancyDate().add(occupanceDate);

        when(dateService.getCaughtUpLocalDate()).thenReturn(systemDate);

        transformer.handleFunctionSpaceDateResults(groupEvaluationArrivalDate, arrivalDateType);

        assertEquals(0, groupEvaluationArrivalDate.getGroupEvaluationFunctionSpaceArrivalDates().size());

        verify(dateService).getCaughtUpLocalDate();
    }

    @Test
    public void setOptStartAndEndDates() throws Exception {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();

        int optWindowEndDateBDEDays = 365;
        LocalDate caughtUpDate = new LocalDate(2015, 7, 1);
        LocalDate expectedOptEndDate = caughtUpDate.plusDays(optWindowEndDateBDEDays);

        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(caughtUpDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));

        verify(dateService, Mockito.times(1)).getOptimizationWindowStartDate();
        verify(dateService, Mockito.times(1)).getForecastWindowEndDateBDE();
    }

    @Test
    public void shouldAdjustOptWindowUsingArrivalDateForFSEvaluation() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        int optWindowEndDateBDEDays = 365;
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfLastMonth()).plusYears(1);
        LocalDate expectedOptStartDate = groupEvaluation.getEarliestArrivalDate().minusDays(OPTIMIZATION_WINDOW_BUFFER);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(groupEvaluation.getNumberOfNights() - 1).plusDays(OPTIMIZATION_WINDOW_BUFFER);
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_FS_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(expectedOptStartDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDates_AdjustOptWindowUsingArrivalDate() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        int optWindowEndDateBDEDays = 365;
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfLastMonth()).plusYears(1);
        LocalDate expectedOptStartDate = groupEvaluation.getEarliestArrivalDate().minusDays(OPTIMIZATION_WINDOW_BUFFER);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(groupEvaluation.getNumberOfNights() - 1).plusDays(OPTIMIZATION_WINDOW_BUFFER);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(expectedOptStartDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    @Disabled
    public void setOptStartAndEndDates_AdjustOptWindowUsingMultipleArrivalDates() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        int optWindowEndDateBDEDays = 365;

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setPreferredDate(true);
        groupEvaluationArrivalDate.setArrivalDate(new LocalDate(DateUtil.getFirstDayOfCurrentMonth()).plusYears(1));
        groupEvaluationArrivalDate.setSuggestedRate(BigDecimal.valueOf(100));
        groupEvaluation.addGroupEvaluationArrivalDate(groupEvaluationArrivalDate);

        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth()).minusDays(30).plusYears(1);
        if (java.time.LocalDate.now().isLeapYear() && java.time.LocalDate.now().getMonth().ordinal() == 2) {
            // this joda to java.time and then again to joda is Just to avoid any new joda operation.
            caughtUpDate = LocalDateUtils.toJodaLocalDate(LocalDateUtils.toJavaTimeLocalDate(caughtUpDate)
                    .minusDays(1));
        }
        LocalDate expectedOptStartDate = groupEvaluation.getEarliestArrivalDate().minusDays(OPTIMIZATION_WINDOW_BUFFER);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(groupEvaluation.getNumberOfNights() - 1).plusDays(OPTIMIZATION_WINDOW_BUFFER);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(expectedOptStartDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDates_AdjustOptWindowUsingArrivalDateAndCaughtUpDate() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        int optWindowEndDateBDEDays = 365;
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth()).plusYears(1).plusDays(10);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(groupEvaluation.getNumberOfNights() - 1).plusDays(OPTIMIZATION_WINDOW_BUFFER);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(caughtUpDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDates_AdjustOptWindowUsingArrivalDateAndForecastWindow() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        int optWindowEndDateBDEDays = 365;
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate expectedOptStartDate = groupEvaluation.getEarliestArrivalDate().minusDays(OPTIMIZATION_WINDOW_BUFFER);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(optWindowEndDateBDEDays).toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(expectedOptStartDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(caughtUpDate.plusDays(optWindowEndDateBDEDays).toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDates_AdjustOptWindowUsingArrivalDateForExtendedWindow() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate expectedOptStartDate = groupEvaluation.getEarliestArrivalDate().minusDays(OPTIMIZATION_WINDOW_BUFFER);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(groupEvaluation.getNumberOfNights() - 1).plusDays(OPTIMIZATION_WINDOW_BUFFER);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(true);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(true);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(caughtUpDate));
        when(groupEvaluationService.getGroupPricingExtendedWindowDays()).thenReturn(EXTENDED_WINDOW_DAYS);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(expectedOptStartDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDatesForExtendedWindow() {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        LocalDate caughtUpDate = new LocalDate(DateUtil.getFirstDayOfCurrentMonth());
        LocalDate expectedOptEndDate = caughtUpDate.plusDays(EXTENDED_WINDOW_DAYS);

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADJUST_GRP_EVL_OPTIMIZATION_WINDOW_USING_ARRIVAL_DATE)).thenReturn(false);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate.toDate());
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(true);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(caughtUpDate));
        when(groupEvaluationService.getGroupPricingExtendedWindowDays()).thenReturn(EXTENDED_WINDOW_DAYS);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(caughtUpDate.toDate(), convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));
    }

    @Test
    public void setOptStartAndEndDates_FunctionSpaceEnabled() throws Exception {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        LocalDate earliestArrivalDate = groupEvaluation.getEarliestArrivalDate();

        int optWindowEndDateBDEDays = 365;
        int bufferDays = SystemConfig.getGroupPricingOptStartAndEndDateBufferDays();
        LocalDate caughtUpDate = new LocalDate(2015, 7, 1);
        LocalDate expectedOptEndDate = groupEvaluation.getLastArrivalDate().plusDays(bufferDays)
                .plusDays(groupEvaluation.getNumberOfNights()).plusDays(optWindowEndDateBDEDays);

        LocalDate lastFcstDate = LocalDate.fromDateFields(DateUtil.addDaysToDate(expectedOptEndDate.toDate(), 1));
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("propertyId", PacmanWorkContextHelper.getPropertyId());

        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);
        when(dateService.getFunctionSpaceMaxFcstDate()).thenReturn(lastFcstDate);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(earliestArrivalDate.minusDays(bufferDays).toDate(),
                convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(expectedOptEndDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));

        verify(dateService, Mockito.times(1)).getOptimizationWindowOffsetBDE();
        verify(dateService, Mockito.times(1)).getFunctionSpaceMaxFcstDate();
        verify(configService, Mockito.times(1))
                .getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value());
    }

    @Test
    public void setOptStartAndEndDates_FunctionSpaceEnabledWithOptEndDateLessThanMaxForecastDate() throws Exception {
        GroupPriceRequestType groupPriceRequestType = new GroupPriceRequestType();
        LocalDate earliestArrivalDate = groupEvaluation.getEarliestArrivalDate();

        int bufferDays = SystemConfig.getGroupPricingOptStartAndEndDateBufferDays();
        LocalDate caughtUpDate = new LocalDate(2015, 7, 1);
        LocalDate lastFcstDate = new LocalDate(2016, 1, 1);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("propertyId", PacmanWorkContextHelper.getPropertyId());

        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(dateService.getOptimizationWindowOffsetBDE()).thenReturn(365);
        when(dateService.getFunctionSpaceMaxFcstDate()).thenReturn(lastFcstDate);

        transformer.setOptStartAndEndDates(groupEvaluation, groupPriceRequestType, caughtUpDate.toDate());

        assertEquals(earliestArrivalDate.minusDays(bufferDays).toDate(),
                convertToDate(groupPriceRequestType.getOptStartDate()));
        assertEquals(lastFcstDate.toDate(), convertToDate(groupPriceRequestType.getOptEndDate()));

        verify(dateService, Mockito.times(1)).getOptimizationWindowOffsetBDE();
        verify(configService, Mockito.times(1))
                .getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value());
        verify(dateService, Mockito.times(1)).getFunctionSpaceMaxFcstDate();
    }

    @Test
    public void handleRoomTypeEvaluationResults() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        // Expected values are calculated by using the following formula:
        // 7000 = 10(x+10) + 5(x+20) --> x = 486.67 then apply the offset values +10 for rt1 and +20 for rt2
        // 7000 is total rooms requested (15) * optimal room class rate of 500.00
        // 10(x+10) is 10 rooms for room type 1, then add the +10 offset to x
        // 5(x+20) is 5 rooms for room type 2, then add the +20 offset to x
        BigDecimal expectedRateForRoomType1 = new BigDecimal(496.67).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(506.67).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        offset.setId(pk);
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk, offset);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    @Test
    public void handleRoomTypeEvaluationResults_WithNegativePercentOffset() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        BigDecimal expectedRateForRoomType1 = new BigDecimal(475.71).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(548.57).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        offset.setId(pk);
        offset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset.setOffsetValue(new BigDecimal(-10.00));

        offsets.put(pk, offset);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    @Test
    public void handleRoomTypeEvaluationResults_WithPositivePercentOffset() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        BigDecimal expectedRateForRoomType1 = new BigDecimal(508.75).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(482.50).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        offset.setId(pk);
        offset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk, offset);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    @Test
    public void handleRoomTypeEvaluationResults_WithTwoPositivePercentOffset() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        BigDecimal expectedRateForRoomType1 = new BigDecimal(485.30).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(529.42).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        offset.setId(pk);
        offset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk, offset);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    @Test
    public void handleRoomTypeEvaluationResults_withTwoPositivePercentOffsetTaxInclusive() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        BigDecimal expectedRateForRoomType1 = new BigDecimal(485.30).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(529.42).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        offset.setId(pk);
        offset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk, offset);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate.getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    @Test
    public void handleRoomTypeEvaluationResults_InvalidRoomClassIdInResponse() {
        int invalidRoomClassId = 0;

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(invalidRoomClassId);
        roomClass.setOptimalRate(BigDecimal.ZERO);

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        groupEvaluationArrivalDate.setArrivalDate(LocalDate.now());
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        assertThrows(TetrisException.class, () -> transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType));
    }

    @Test
    public void calculateRoomClassBlendedRate_OptimalRateIsZero() {
        BigDecimal optimalRate = BigDecimal.valueOf(0.00);
        BigDecimal blendedRate = transformer.calculateRoomClassBlendedRate(null, optimalRate, null, null, OccupancyType.SINGLE, 1);

        assertEquals(BigDecimal.ZERO, blendedRate);
    }

    private void validateRoomTypeRates(BigDecimal expectedRateForRoomType1, BigDecimal expectedRateForRoomType2, List<GroupEvaluationArrivalDateAccomClass> roomClassRates) {
        for (GroupEvaluationArrivalDateAccomType accomType : roomClassRates.get(0)
                .getGroupEvaluationArrivalDateAccomTypes()) {
            if (accomType.getAccomType().getId() == 1) {
                assertEquals(expectedRateForRoomType1, accomType.getRate());
            }

            if (accomType.getAccomType().getId() == 2) {
                assertEquals(expectedRateForRoomType2, accomType.getRate());
            }
        }
    }

    @Test
    public void handleRoomTypeEvaluationResults_BaseRoomTypeOnly() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();

        GroupEvaluationRoomType baseRoomType = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);

        groupEvaluation.addGroupEvaluationRoomType(baseRoomType);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00).setScale(2, RoundingMode.HALF_UP));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        GroupEvaluationArrivalDateAccomType accomType = roomClassRates.get(0).getGroupEvaluationArrivalDateAccomTypes()
                .get(0);

        assertEquals(roomClass.getOptimalRate(), accomType.getRate());
    }

    @Test
    public void handleRoomTypeEvaluationResults_DifferentRoomClasses() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        // Define expected room type rates here
        BigDecimal expectedRateForRoomType1 = new BigDecimal(496.67).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(506.67).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType3 = new BigDecimal(396.67).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType4 = new BigDecimal(406.67).setScale(2, RoundingMode.HALF_UP);
        // these two room types belong to room class
        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);
        // these two room types belong to room class 2
        GroupEvaluationRoomType roomType3 = GroupEvaluationObjectMother.buildGroupEvaluationType(3, 2, 3, 10);
        GroupEvaluationRoomType roomType4 = GroupEvaluationObjectMother.buildGroupEvaluationType(3, 2, 4, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);
        groupEvaluation.addGroupEvaluationRoomType(roomType3);
        groupEvaluation.addGroupEvaluationRoomType(roomType4);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        // Set up one room class response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass1 = new RoomClass();
        roomClass1.setRcID(1);
        roomClass1.setOptimalRate(new BigDecimal(500.00));

        // Set up second room class response with optimal rate of 400
        RoomClass roomClass2 = new RoomClass();
        roomClass2.setRcID(2);
        roomClass2.setOptimalRate(new BigDecimal(400.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass1);
        optimalRcRates.getRoomClass().add(roomClass2);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk1 = new CPConfigMergedOffsetPK(arrivalDate, 1, 1, OccupancyType.SINGLE);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(pk1);
        offset1.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset1.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk1, offset1);

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(new BigDecimal(20.00));

        offsets.put(pk2, offset2);

        CPConfigMergedOffsetPK pk3 = new CPConfigMergedOffsetPK(arrivalDate, 1, 3, OccupancyType.SINGLE);

        CPConfigMergedOffset offset3 = new CPConfigMergedOffset();
        offset3.setId(pk3);
        offset3.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset3.setOffsetValue(new BigDecimal(0));

        offsets.put(pk3, offset3);

        CPConfigMergedOffsetPK pk4 = new CPConfigMergedOffsetPK(arrivalDate, 1, 4, OccupancyType.SINGLE);

        CPConfigMergedOffset offset4 = new CPConfigMergedOffset();
        offset4.setId(pk4);
        offset4.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset4.setOffsetValue(new BigDecimal(10.00));

        offsets.put(pk4, offset4);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);

        transformer.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 2);

        List<GroupEvaluationArrivalDateAccomType> allAccomTypes = new ArrayList<>();
        allAccomTypes.addAll(roomClassRates.get(0).getGroupEvaluationArrivalDateAccomTypes());
        allAccomTypes.addAll(roomClassRates.get(1).getGroupEvaluationArrivalDateAccomTypes());

        // validate both room types
        for (GroupEvaluationArrivalDateAccomType accomType : allAccomTypes) {
            if (accomType.getAccomType().getId() == 1) {
                assertEquals(expectedRateForRoomType1, accomType.getRate());
            }

            if (accomType.getAccomType().getId() == 2) {
                assertEquals(expectedRateForRoomType2, accomType.getRate());
            }

            if (accomType.getAccomType().getId() == 3) {
                assertEquals(expectedRateForRoomType3, accomType.getRate());
            }

            if (accomType.getAccomType().getId() == 4) {
                assertEquals(expectedRateForRoomType4, accomType.getRate());
            }
        }
    }

    @Test
    public void applyResults_NullBreakEvenRateGetsSetToZero() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setBreakevenRate(null);
        firstArrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        firstArrivalDateType.setPostStay(new ArrivalDateType.PostStay());
        firstArrivalDateType.getPreStay().setOccFcstWithGroup(BigDecimal.ZERO);
        firstArrivalDateType.getPreStay().setOccFcstWithoutGroup(BigDecimal.ZERO);
        firstArrivalDateType.getPostStay().setOccFcstWithGroup(BigDecimal.ZERO);
        firstArrivalDateType.getPostStay().setOccFcstWithoutGroup(BigDecimal.ZERO);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertEquals(BigDecimal.ZERO, groupEvaluation.getGroupEvaluationArrivalDates()
                .iterator().next().getBreakEvenRate());
    }

    @Test
    public void handlePrePostStayAggregatedResults_ROH() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);
        // Set up response with aggregated pre/post stay occupancy forecast data
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        arrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        arrivalDateType.setPostStay(new ArrivalDateType.PostStay());


        BigDecimal preStayOccupancyFcstWithGroup = new BigDecimal((25.00));
        BigDecimal preStayOccupancyFcstWithoutGroup = new BigDecimal((35.00));
        BigDecimal preStayDisplacedRooms = new BigDecimal(2.00);
        BigDecimal preStayDisplacedRoomRev = new BigDecimal(10.00);
        BigDecimal preStayDisplacedRoomProfit = new BigDecimal(7.00);
        BigDecimal postStayOccupancyFcstWithGroup = new BigDecimal((45.00));
        BigDecimal postStayOccupancyFcstWithoutGroup = new BigDecimal((55.00));
        BigDecimal postStayDisplacedRoomRev = new BigDecimal(11.00);
        BigDecimal postStayDisplacedRoomProfit = new BigDecimal(8.00);
        BigDecimal postStayDisplacedRooms = new BigDecimal(3.00);

        arrivalDateType.getPreStay().setOccFcstWithGroup(preStayOccupancyFcstWithGroup);
        arrivalDateType.getPreStay().setOccFcstWithoutGroup(preStayOccupancyFcstWithoutGroup);
        arrivalDateType.getPreStay().setDisplacedRooms(preStayDisplacedRooms);
        arrivalDateType.getPreStay().setDisplacedRoomRev(preStayDisplacedRoomRev);
        arrivalDateType.getPreStay().setDisplacedRoomProfit(preStayDisplacedRoomProfit);
        arrivalDateType.getPostStay().setOccFcstWithGroup(postStayOccupancyFcstWithGroup);
        arrivalDateType.getPostStay().setOccFcstWithoutGroup(postStayOccupancyFcstWithoutGroup);
        arrivalDateType.getPostStay().setDisplacedRoomRev(postStayDisplacedRoomRev);
        arrivalDateType.getPostStay().setDisplacedRoomProfit(postStayDisplacedRoomProfit);
        arrivalDateType.getPostStay().setDisplacedRooms(postStayDisplacedRooms);

        transformer.handlePrePostStayAggregatedResults(arrivalDate, arrivalDateType);

        assertEquals(preStayOccupancyFcstWithGroup, arrivalDate.getPreOccupancyFcstWithGroup());
        assertEquals(preStayOccupancyFcstWithoutGroup, arrivalDate.getPreOccupancyFcstWithoutGroup());
        assertEquals(preStayDisplacedRooms, arrivalDate.getPreDisplacedRooms());
        assertEquals(preStayDisplacedRoomRev, arrivalDate.getPreDisplacedRoomRevenue());
        assertEquals(preStayDisplacedRoomProfit, arrivalDate.getPreDisplacedRoomProfit());
        assertEquals(postStayOccupancyFcstWithGroup, arrivalDate.getPostOccupancyFcstWithGroup());
        assertEquals(postStayOccupancyFcstWithoutGroup, arrivalDate.getPostOccupancyFcstWithoutGroup());
        assertEquals(postStayDisplacedRooms, arrivalDate.getPostDisplacedRooms());
        assertEquals(postStayDisplacedRoomRev, arrivalDate.getPostDisplacedRoomRevenue());
        assertEquals(postStayDisplacedRoomProfit, arrivalDate.getPostDisplacedRoomProfit());
    }

    @Test
    public void handlePrePostStayAggregatedResults_RC() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);
        // Set up response with aggregated pre/post stay occupancy forecast data
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        arrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        arrivalDateType.setPostStay(new ArrivalDateType.PostStay());

        Integer rcId = 1;
        Integer rcId2 = 2;

        BigDecimal preStayOccupancyFcstWithGroup = new BigDecimal((25.00));
        BigDecimal preStayOccupancyFcstWithoutGroup = new BigDecimal((35.00));
        BigDecimal preStayDisplacedRooms = new BigDecimal(2.00);
        BigDecimal preStayDisplacedRoomRev = new BigDecimal(10.00);
        BigDecimal preStayDisplacedRoomProfit = new BigDecimal(7.00);
        BigDecimal postStayOccupancyFcstWithGroup = new BigDecimal((45.00));
        BigDecimal postStayOccupancyFcstWithoutGroup = new BigDecimal((55.00));
        BigDecimal postStayDisplacedRoomRev = new BigDecimal(11.00);
        BigDecimal postStayDisplacedRoomProfit = new BigDecimal(8.00);
        BigDecimal postStayDisplacedRooms = new BigDecimal(3.00);

        ArrivalDateType.PreStay.RoomClass rc1Pre = new ArrivalDateType.PreStay.RoomClass();
        rc1Pre.setRcID(rcId);
        rc1Pre.setOccFcstWithGroup(preStayOccupancyFcstWithGroup);
        rc1Pre.setOccFcstWithoutGroup(preStayOccupancyFcstWithoutGroup);
        rc1Pre.setDisplacedRooms(preStayDisplacedRooms);

        ArrivalDateType.PreStay.RoomClass rc2Pre = new ArrivalDateType.PreStay.RoomClass();
        rc2Pre.setRcID(rcId2);
        rc2Pre.setOccFcstWithGroup(preStayOccupancyFcstWithGroup.add(BigDecimal.ONE));
        rc2Pre.setOccFcstWithoutGroup(preStayOccupancyFcstWithoutGroup.add(BigDecimal.ONE));
        rc2Pre.setDisplacedRooms(preStayDisplacedRooms.add(BigDecimal.ONE));

        ArrivalDateType.PostStay.RoomClass rc1Post = new ArrivalDateType.PostStay.RoomClass();
        rc1Post.setRcID(rcId);
        rc1Post.setOccFcstWithGroup(postStayOccupancyFcstWithGroup);
        rc1Post.setOccFcstWithoutGroup(postStayOccupancyFcstWithoutGroup);
        rc1Post.setDisplacedRooms(postStayDisplacedRooms);

        ArrivalDateType.PostStay.RoomClass rc2Post = new ArrivalDateType.PostStay.RoomClass();
        rc2Post.setRcID(rcId2);
        rc2Post.setOccFcstWithGroup(postStayOccupancyFcstWithGroup.add(BigDecimal.ONE));
        rc2Post.setOccFcstWithoutGroup(postStayOccupancyFcstWithoutGroup.add(BigDecimal.ONE));
        rc2Post.setDisplacedRooms(postStayDisplacedRooms.add(BigDecimal.ONE));

        arrivalDateType.getPreStay().getRoomClass().add(rc1Pre);
        arrivalDateType.getPreStay().getRoomClass().add(rc2Pre);
        arrivalDateType.getPreStay().setDisplacedRoomRev(preStayDisplacedRoomRev);
        arrivalDateType.getPreStay().setDisplacedRoomProfit(preStayDisplacedRoomProfit);
        arrivalDateType.getPostStay().getRoomClass().add(rc1Post);
        arrivalDateType.getPostStay().getRoomClass().add(rc2Post);
        arrivalDateType.getPostStay().setDisplacedRoomProfit(postStayDisplacedRoomProfit);
        arrivalDateType.getPostStay().setDisplacedRooms(postStayDisplacedRooms);

        arrivalDateType.getPreStay().setDisplacedRoomRev(preStayDisplacedRoomRev);
        arrivalDateType.getPreStay().setDisplacedRoomProfit(preStayDisplacedRoomProfit);
        arrivalDateType.getPostStay().setDisplacedRoomRev(postStayDisplacedRoomRev);
        arrivalDateType.getPostStay().setDisplacedRoomProfit(postStayDisplacedRoomProfit);

        transformer.handlePrePostStayAggregatedResults(arrivalDate, arrivalDateType);

        assertEquals(new BigDecimal(51), arrivalDate.getPreOccupancyFcstWithGroup());
        assertEquals(new BigDecimal(71), arrivalDate.getPreOccupancyFcstWithoutGroup());
        assertEquals(new BigDecimal(5), arrivalDate.getPreDisplacedRooms());
        assertEquals(new BigDecimal(91), arrivalDate.getPostOccupancyFcstWithGroup());
        assertEquals(new BigDecimal(111), arrivalDate.getPostOccupancyFcstWithoutGroup());
        assertEquals(new BigDecimal(7), arrivalDate.getPostDisplacedRooms());

        assertEquals(preStayDisplacedRoomRev, arrivalDate.getPreDisplacedRoomRevenue());
        assertEquals(preStayDisplacedRoomProfit, arrivalDate.getPreDisplacedRoomProfit());
        assertEquals(postStayDisplacedRoomRev, arrivalDate.getPostDisplacedRoomRevenue());
        assertEquals(postStayDisplacedRoomProfit, arrivalDate.getPostDisplacedRoomProfit());
    }

    @Test
    public void handleMinProfitPercent_enabled() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        BigDecimal minProfitPct = new BigDecimal(25.00);

        when(configService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value())).thenReturn(true);
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(arrivalDate)).thenReturn(minProfitPct);

        transformer.handleMinProfitPercent(arrivalDate, arrDateDetails);

        assertEquals(minProfitPct, arrDateDetails.getMinProfitPct());
        verify(configService).getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value());
        verify(groupPricingConfigurationMinProfitService).calculateMinProfitPercentThreshold(arrivalDate);
    }

    @Test
    public void handleMinProfitPercent_minProfitNotEnabledBecauseParameterNotSet() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        BigDecimal minProfitPct = new BigDecimal(25.00);

        when(configService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value())).thenReturn(false);
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(arrivalDate)).thenReturn(minProfitPct);

        transformer.handleMinProfitPercent(arrivalDate, arrDateDetails);

        assertEquals(BigDecimal.ZERO, arrDateDetails.getMinProfitPct());
        verify(configService).getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value());
        verifyZeroInteractions(groupPricingConfigurationMinProfitService);
    }

    @Test
    public void handleMinProfitPercent_minProfitNotEnabledForFunctionSpaceOnly() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        BigDecimal minProfitPct = new BigDecimal(25.00);

        when(configService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value())).thenReturn(true);
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(arrivalDate)).thenReturn(minProfitPct);

        transformer.handleMinProfitPercent(arrivalDate, arrDateDetails);

        assertEquals(BigDecimal.ZERO, arrDateDetails.getMinProfitPct());
        verify(configService).getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value());
        verifyZeroInteractions(groupPricingConfigurationMinProfitService);
    }

    @Test
    public void handleMinProfitPercent_roomClassEvaluation() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);

        ArrDateDetails arrDateDetails = new ArrDateDetails();

        BigDecimal minProfitPct = new BigDecimal(25.00);

        when(configService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value())).thenReturn(true);
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(arrivalDate)).thenReturn(minProfitPct);

        transformer.handleMinProfitPercent(arrivalDate, arrDateDetails);

        assertEquals(BigDecimal.ZERO, arrDateDetails.getMinProfitPct());
        verifyZeroInteractions(configService);
        verifyZeroInteractions(groupPricingConfigurationMinProfitService);
    }

    @Test
    public void testCreateRequest_resultsByOccupancyDateEnabled() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(true);
        when(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_MAX_DAILY_RATE_CHANGE.getParameterName())).thenReturn(3);

        groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
        groupEvaluationCost.setTotal(5);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        GroupEvaluationCost groupEvaluationCost1 = new GroupEvaluationCost();
        groupEvaluationCost1.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
        groupEvaluationCost1.setPercentage(BigDecimal.TEN);
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost1);

        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupEvaluation(groupEvaluation);
        groupEvaluationAncillary.setRevenue(new BigDecimal(100));
        groupEvaluationAncillary.setProfitPercentage(new BigDecimal(5));
        groupEvaluation.addGroupEvaluationAncillary(groupEvaluationAncillary);

        Date caughtUpDate = DateUtil.getFirstDayOfNextMonth();

        when(dateService.getWasLastLoadBDE()).thenReturn(true);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(caughtUpDate);
        when(dateService.getOptimizationWindowEndDateBDE())
                .thenReturn(new LocalDate(caughtUpDate).plusDays(365).toDate());
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("alias");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("1");
        when(groupPricingConfigurationMinProfitService.calculateMinProfitPercentThreshold(groupEvaluation.getGroupEvaluationArrivalDates().iterator().next())).thenReturn(new BigDecimal(30.00));
        when(groupEvaluationService.groupPricingUseExtendedWindowEnabled()).thenReturn(false);

        BusinessContext businessContext = new BusinessContext();
        businessContext.setMasterRoomClassId(5);
        businessContext.setSingleBARDecisionEnabled(true);
        when(businessContextService.getCurrentBusinessContext()).thenReturn(businessContext);

        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ADD_CONFBANQ_GROSS_REVENUE_IN_FS_EVALUATION)).thenReturn(true);

        List<WebrateShoppingConfig> webrateShoppingConfigs = new ArrayList<>();
        when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty())
                .thenReturn(webrateShoppingConfigs);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(0));
        when(taxService.findTax()).thenReturn(tax);

        GroupPriceRequestType groupPriceRequestType = transformer.createRequest(groupEvaluation);

        assertNull(groupPriceRequestType.getGroupPriceParams().isReduceThread());
        assertEquals(caughtUpDate, groupPriceRequestType.getCaughtUpDate().toGregorianCalendar().getTime());
        assertNull(groupPriceRequestType.getDecisionId());
        assertEquals(AbstractOptimizationService.DECISION_TYPE_RATE_OF_DAY,
                groupPriceRequestType.getDecisionRateType());
        assertEquals(businessContext.getMasterRoomClassId(), groupPriceRequestType.getMasterAccomClassId());
        assertEquals(Constants.ON_DEMAND, groupPriceRequestType.getOperationType());
        assertEquals(caughtUpDate, groupPriceRequestType.getOptStartDate().toGregorianCalendar().getTime());
        assertEquals(valueOf(1), groupPriceRequestType.getStaleness());
        assertEquals(0, groupPriceRequestType.getStalenessEntries().getStalenessEntry().size());
        assertFalse(groupPriceRequestType.isLRAFeatureToggle());
        assertEquals(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_WINDOW_DISPLACEMENT.value()).intValue(), groupPriceRequestType.getGroupPriceParams().getWindowSize());

        List<ArrDateDetails> arrivalDates = groupPriceRequestType.getGroupPriceParams().getArrival();
        assertEquals(1, arrivalDates.size());

        ArrDateDetails arrDateDetails = arrivalDates.get(0);
        assertEquals(GroupPricingEvaluationMethod.ROH.name(), arrDateDetails.getEvaluationMethod());
        assertEquals(groupEvaluation.getMarketSegment().getId().intValue(), arrDateDetails.getMarketSegment());
        assertEquals(3, arrDateDetails.getNights().getNight().size());
        assertEquals(3, arrDateDetails.getNumNights());

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates()
                .iterator().next();

        Night night1 = arrDateDetails.getNights().getNight().get(0);
        assertEquals(100, night1.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().toDate(),
                night1.getDate().toGregorianCalendar().getTime());

        Night night2 = arrDateDetails.getNights().getNight().get(1);
        assertEquals(100, night2.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(1).toDate(),
                night2.getDate().toGregorianCalendar().getTime());

        Night night3 = arrDateDetails.getNights().getNight().get(2);
        assertEquals(100, night3.getValue());
        assertEquals(groupEvaluationArrivalDate.getArrivalDate().plusDays(2).toDate(),
                night3.getDate().toGregorianCalendar().getTime());

        assertEquals(3, arrDateDetails.getMaxDailyRateChange());
        assertEquals(1, arrDateDetails.getConcessions().getConcession().size());

        Concession concession = arrDateDetails.getConcessions().getConcession().get(0);
        assertEquals(valueOf(5), concession.getRooms());
        assertEquals(ValueType.PERCENT, concession.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        Commission commission = arrDateDetails.getCommission();
        assertEquals(ValueType.PERCENT, commission.getType());
        assertEquals(new BigDecimal(100).setScale(2, RoundingMode.HALF_UP), concession.getValue());

        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqValue());
        assertEquals(new BigDecimal(100), arrDateDetails.getConfBanqRevenue());
        assertEquals(new BigDecimal(76).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getConfBanqMargin());

        assertEquals(new BigDecimal(100), arrDateDetails.getAncillaryValue());
        assertEquals(new BigDecimal(5).setScale(2, RoundingMode.HALF_UP), arrDateDetails.getAncillaryMargin());
    }

    @Test
    public void handleArrivalDateGuestRoomRates() throws Exception {
        LocalDate occupancyDate = new LocalDate(2020, 2, 3);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);

        ArrivalDateType arrivalDateType = new ArrivalDateType();

        arrivalDateType.setODRates(new ArrivalDateType.ODRates());
        ArrivalDateType.ODRates.OccupancyDate odRates1 = new ArrivalDateType.ODRates.OccupancyDate();
        odRates1.setDate(buildXMLGregorianCalendar(occupancyDate));
        odRates1.setBreakevenRate(BigDecimal.valueOf(100.00));
        odRates1.setOptimalRate(BigDecimal.valueOf(150.00));

        ArrivalDateType.ODRates.OccupancyDate odRates2 = new ArrivalDateType.ODRates.OccupancyDate();
        odRates2.setDate(buildXMLGregorianCalendar(occupancyDate));
        odRates2.setBreakevenRate(BigDecimal.valueOf(100.00));
        odRates2.setOptimalRate(BigDecimal.valueOf(150.00));

        arrivalDateType.getODRates().getOccupancyDate().add(odRates1);
        arrivalDateType.getODRates().getOccupancyDate().add(odRates2);

        transformer.handleArrivalDateGuestRoomRates(arrivalDate, arrivalDateType);

        assertEquals(2, arrivalDate.getGroupEvaluationArrivalDateGuestRoomRates().size());
        for (GroupEvaluationArrivalDateGuestRoomRates guestRoomRates : arrivalDate.getGroupEvaluationArrivalDateGuestRoomRates()) {
            assertEquals(occupancyDate, guestRoomRates.getOccupancyDate());
            assertEquals(BigDecimal.valueOf(100.00), guestRoomRates.getBreakEvenRate());
            assertEquals(BigDecimal.valueOf(150.00), guestRoomRates.getOptimalRate());
        }
    }

    @Test
    public void handleOnBooksGroupOptionalRates() {
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        arrivalDateType.setOptionalRates(new ArrivalDateType.OptionalRates());

        ArrivalDateType.OptionalRates.OptionList optionList1 = new ArrivalDateType.OptionalRates.OptionList();
        optionList1.setOptionId(1);
        optionList1.setStatusCode(0);
        optionList1.setExpIncProfit(ONE_HUNDRED);
        ArrivalDateType.OptionalRates.OptionList optionList2 = new ArrivalDateType.OptionalRates.OptionList();
        optionList2.setOptionId(2);
        optionList2.setStatusCode(0);
        optionList2.setExpIncProfit(ONE_HUNDRED);

        arrivalDateType.getOptionalRates().getOptionList().add(optionList1);
        arrivalDateType.getOptionalRates().getOptionList().add(optionList2);

        transformer.handleOnBooksGroupOptionalRates(groupEvaluation, arrivalDateType);

        assertEquals(2, groupEvaluation.getGroupEvaluationOnBooks().size());
        for (GroupEvaluationOnBooks groupEvaluationOnBooks : groupEvaluation.getGroupEvaluationOnBooks()) {
            assertEquals(GroupEvaluationArrivalDateResultCode.ACCEPTABLE, groupEvaluationOnBooks.getStatusCode());
            assertEquals(ONE_HUNDRED, groupEvaluationOnBooks.getExpectedProfit());
        }

    }

    @Test
    public void addMaxDailyRateChange() {
        ArrDateDetails arrDateDetails = new ArrDateDetails();
        when(configService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_MAX_DAILY_RATE_CHANGE.getParameterName())).thenReturn(3);

        transformer.addMaxDailyRateChange(arrDateDetails);
        assertEquals(3, arrDateDetails.getMaxDailyRateChange());

    }

    @Test
    public void addPreviousStayRate() {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_PREVIOUS_STAY_RATE_ENABLED)).thenReturn(true);
        when(configService.getBigDecimalParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_INFLATION_FACTOR_FOR_PREVIOUS_STAY.getParameterName())).thenReturn(BigDecimal.TEN);
        ArrDateDetails arrDateDetails = new ArrDateDetails();
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setExpectedBudgetRate(ONE_HUNDRED);

        transformer.addPreviousStayRates(groupEvaluation, arrDateDetails);
        assertEquals(ONE_HUNDRED, arrDateDetails.getPreviousStayRate());
    }

    @Test
    public void addOnBooksGroup() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomClass.setRankOrder(3);
        LocalDate arrivalDate = new LocalDate(DateUtil.getFirstDayOfNextMonth()).plusYears(1);
        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> {
            evaluationDayOfStay.setCurrentRate(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate1(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate2(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate3(ONE_HUNDRED);
        });
        GroupEvaluationOnBooksGroupRoomClassDetail roomClassDetail = new GroupEvaluationOnBooksGroupRoomClassDetail();
        roomClassDetail.setGroupBlockRate(ONE_HUNDRED);
        roomClassDetail.setRoomsSold(BigDecimal.valueOf(10));
        roomClassDetail.setRoomClassRatio(BigDecimal.valueOf(1));
        roomClassDetail.setPriceOffset(BigDecimal.valueOf(10));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail1 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail1.setOccupancyDate(arrivalDate);
        onBooksGroupDetail1.setCurrentRoomClass(accomClass);
        onBooksGroupDetail1.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail1.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail2 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail2.setOccupancyDate(arrivalDate.plusDays(1));
        onBooksGroupDetail2.setCurrentRoomClass(accomClass);
        onBooksGroupDetail2.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail2.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail3 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail3.setOccupancyDate(arrivalDate.plusDays(2));
        onBooksGroupDetail3.setCurrentRoomClass(accomClass);
        onBooksGroupDetail3.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail3.setRoomClassDetailList(singletonList(roomClassDetail));
        groupEvaluation.setOnBooksGroupDetails(Arrays.asList(onBooksGroupDetail1, onBooksGroupDetail2, onBooksGroupDetail3));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addOnBooksGroup(groupEvaluation, arrDateDetails);
        assertEquals(3, arrDateDetails.getCurrentRates().getCurrentRCId());
        assertEquals(ONE_HUNDRED, arrDateDetails.getCurrentRates().getOccupancyDate().get(0).getCurrentRate());
        assertEquals(3, arrDateDetails.getOptionalRates().getOptionList().size());
        assertEquals(ONE_HUNDRED, arrDateDetails.getOptionalRates().getOptionList().get(2).getOccupancyDate().get(0).getOptionalRate());
    }

    @Test
    public void addOnBooksGroupFor1OptionalRate() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomClass.setRankOrder(3);
        LocalDate arrivalDate = new LocalDate(DateUtil.getFirstDayOfNextMonth()).plusYears(1);
        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> {
            evaluationDayOfStay.setCurrentRate(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate1(ONE_HUNDRED);
        });
        GroupEvaluationOnBooksGroupRoomClassDetail roomClassDetail = new GroupEvaluationOnBooksGroupRoomClassDetail();
        roomClassDetail.setGroupBlockRate(ONE_HUNDRED);
        roomClassDetail.setRoomsSold(BigDecimal.valueOf(10));
        roomClassDetail.setRoomClassRatio(BigDecimal.valueOf(1));
        roomClassDetail.setPriceOffset(BigDecimal.valueOf(10));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail1 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail1.setOccupancyDate(arrivalDate);
        onBooksGroupDetail1.setCurrentRoomClass(accomClass);
        onBooksGroupDetail1.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail1.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail2 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail2.setOccupancyDate(arrivalDate.plusDays(1));
        onBooksGroupDetail2.setCurrentRoomClass(accomClass);
        onBooksGroupDetail2.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail2.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail3 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail3.setOccupancyDate(arrivalDate.plusDays(2));
        onBooksGroupDetail3.setCurrentRoomClass(accomClass);
        onBooksGroupDetail3.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail3.setRoomClassDetailList(singletonList(roomClassDetail));
        groupEvaluation.setOnBooksGroupDetails(Arrays.asList(onBooksGroupDetail1, onBooksGroupDetail2, onBooksGroupDetail3));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addOnBooksGroup(groupEvaluation, arrDateDetails);
        assertEquals(3, arrDateDetails.getCurrentRates().getCurrentRCId());
        assertEquals(1, arrDateDetails.getNumOptionalRates());
        assertEquals(ONE_HUNDRED, arrDateDetails.getCurrentRates().getOccupancyDate().get(0).getCurrentRate());
        assertEquals(1, arrDateDetails.getOptionalRates().getOptionList().size());
        assertEquals(ONE_HUNDRED, arrDateDetails.getOptionalRates().getOptionList().get(0).getOccupancyDate().get(0).getOptionalRate());
    }

    @Test
    public void addOnBooksGroupFor2OptionalRates() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomClass.setRankOrder(3);
        LocalDate arrivalDate = new LocalDate(DateUtil.getFirstDayOfNextMonth()).plusYears(1);
        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> {
            evaluationDayOfStay.setCurrentRate(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate1(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate2(ONE_HUNDRED);
        });
        GroupEvaluationOnBooksGroupRoomClassDetail roomClassDetail = new GroupEvaluationOnBooksGroupRoomClassDetail();
        roomClassDetail.setGroupBlockRate(ONE_HUNDRED);
        roomClassDetail.setRoomsSold(BigDecimal.valueOf(10));
        roomClassDetail.setRoomClassRatio(BigDecimal.valueOf(1));
        roomClassDetail.setPriceOffset(BigDecimal.valueOf(10));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail1 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail1.setOccupancyDate(arrivalDate);
        onBooksGroupDetail1.setCurrentRoomClass(accomClass);
        onBooksGroupDetail1.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail1.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail2 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail2.setOccupancyDate(arrivalDate.plusDays(1));
        onBooksGroupDetail2.setCurrentRoomClass(accomClass);
        onBooksGroupDetail2.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail2.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail3 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail3.setOccupancyDate(arrivalDate.plusDays(2));
        onBooksGroupDetail3.setCurrentRoomClass(accomClass);
        onBooksGroupDetail3.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail3.setRoomClassDetailList(singletonList(roomClassDetail));
        groupEvaluation.setOnBooksGroupDetails(Arrays.asList(onBooksGroupDetail1, onBooksGroupDetail2, onBooksGroupDetail3));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addOnBooksGroup(groupEvaluation, arrDateDetails);
        assertEquals(3, arrDateDetails.getCurrentRates().getCurrentRCId());
        assertEquals(2, arrDateDetails.getNumOptionalRates());
        assertEquals(ONE_HUNDRED, arrDateDetails.getCurrentRates().getOccupancyDate().get(0).getCurrentRate());
        assertEquals(2, arrDateDetails.getOptionalRates().getOptionList().size());
        assertEquals(ONE_HUNDRED, arrDateDetails.getOptionalRates().getOptionList().get(1).getOccupancyDate().get(0).getOptionalRate());
    }

    @Test
    public void addOnBooksGroupForMultipleCurrentAccomClass() {
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setRankOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setRankOrder(3);
        LocalDate arrivalDate = new LocalDate(DateUtil.getFirstDayOfNextMonth()).plusYears(1);
        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> {
            evaluationDayOfStay.setCurrentRate(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate1(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate2(ONE_HUNDRED);
            evaluationDayOfStay.setOptionalRate3(ONE_HUNDRED);
        });
        GroupEvaluationOnBooksGroupRoomClassDetail roomClassDetail = new GroupEvaluationOnBooksGroupRoomClassDetail();
        roomClassDetail.setGroupBlockRate(ONE_HUNDRED);
        roomClassDetail.setRoomsSold(BigDecimal.valueOf(10));
        roomClassDetail.setRoomClassRatio(BigDecimal.valueOf(1));
        roomClassDetail.setPriceOffset(BigDecimal.valueOf(10));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail1 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail1.setOccupancyDate(arrivalDate);
        onBooksGroupDetail1.setCurrentRoomClass(accomClass2);
        onBooksGroupDetail1.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail1.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail2 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail2.setOccupancyDate(arrivalDate.plusDays(1));
        onBooksGroupDetail2.setCurrentRoomClass(accomClass3);
        onBooksGroupDetail2.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail2.setRoomClassDetailList(singletonList(roomClassDetail));
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail3 = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail3.setOccupancyDate(arrivalDate.plusDays(2));
        onBooksGroupDetail3.setCurrentRoomClass(accomClass3);
        onBooksGroupDetail3.setWeightedAverageCurrentRate(ONE_HUNDRED);
        onBooksGroupDetail3.setRoomClassDetailList(singletonList(roomClassDetail));
        groupEvaluation.setOnBooksGroupDetails(Arrays.asList(onBooksGroupDetail1, onBooksGroupDetail2, onBooksGroupDetail3));
        ArrDateDetails arrDateDetails = new ArrDateDetails();

        transformer.addOnBooksGroup(groupEvaluation, arrDateDetails);
        assertEquals(2, arrDateDetails.getCurrentRates().getCurrentRCId());
        assertEquals(ONE_HUNDRED, arrDateDetails.getCurrentRates().getOccupancyDate().get(0).getCurrentRate());
        assertEquals(ONE_HUNDRED, arrDateDetails.getOptionalRates().getOptionList().get(0).getOccupancyDate().get(0).getOptionalRate());
    }

    @Test
    public void addOtherConcessions() {
        GroupEvaluationCost groupEvaluationCost = new GroupEvaluationCost();
        groupEvaluationCost.setGroupEvaluationCostType(GroupEvaluationCostType.OTHER_CONCESSIONS_FIXED);
        groupEvaluationCost.setTotal(10);
        groupEvaluationCost.setCost(BigDecimal.valueOf(20));
        groupEvaluation.addGroupEvaluationCost(groupEvaluationCost);

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addCosts(groupEvaluation, arrDateDetails);
        ArrDateDetails.OtherConcessions.OtherConcession otherConcession = arrDateDetails.getOtherConcessions().getOtherConcession().get(0);

        assertEquals(10, otherConcession.getRooms().intValue());
        assertEquals(new BigDecimal(20), otherConcession.getValue());
    }

    @Test
    public void applyResults_simulateResultCode4forROH() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setBreakevenRate(null);
        firstArrivalDateType.setPreStay(null);
        firstArrivalDateType.setPostStay(null);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        assertEquals(GroupPricingEvaluator.ANALYTICS, groupEvaluation.getEvaluator());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRoomProfit());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRoomRevenue());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRooms());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreOccupancyFcstWithGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreOccupancyFcstWithoutGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRoomProfit());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRoomRevenue());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRooms());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostOccupancyFcstWithGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostOccupancyFcstWithoutGroup());
    }

    @Test
    public void applyResults_simulateResultCode4forRC() throws Exception {
        GroupEvaluation rcGroupEvaluation = GroupEvaluationObjectMother.buildRoomClassGroupEvaluation();
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(rcGroupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setBreakevenRate(null);
        firstArrivalDateType.setPreStay(null);
        firstArrivalDateType.setPostStay(null);

        transformer.applyResults(rcGroupEvaluation, groupPriceResponse);

        GroupEvaluationArrivalDate arrivalDate = rcGroupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRoomProfit());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRoomRevenue());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreDisplacedRooms());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreOccupancyFcstWithGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPreOccupancyFcstWithoutGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRoomProfit());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRoomRevenue());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostDisplacedRooms());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostOccupancyFcstWithGroup());
        assertEquals(BigDecimal.ZERO, arrivalDate.getPostOccupancyFcstWithoutGroup());
    }

    @Test
    public void handleRoomTypeEvaluationResults_forPriceExcludedRoomClass() throws Exception {
        GroupEvaluationTransformer spy = Mockito.spy(transformer);
        GroupEvaluation groupEvaluation = new GroupEvaluation();

        BigDecimal expectedRateForRoomType1 = new BigDecimal(5000.00).setScale(2, RoundingMode.HALF_UP);
        BigDecimal expectedRateForRoomType2 = new BigDecimal(5100.00).setScale(2, RoundingMode.HALF_UP);

        GroupEvaluationRoomType roomType1 = GroupEvaluationObjectMother.buildGroupEvaluationType(1, 1, 1, 10);
        GroupEvaluationRoomType roomType2 = GroupEvaluationObjectMother.buildGroupEvaluationType(2, 1, 2, 5);

        groupEvaluation.addGroupEvaluationRoomType(roomType1);
        groupEvaluation.addGroupEvaluationRoomType(roomType2);

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();

        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);

        // Set up response with optimal rate of 500
        ArrivalDateType arrivalDateType = new ArrivalDateType();
        RoomClass roomClass = new RoomClass();
        roomClass.setRcID(1);
        roomClass.setOptimalRate(new BigDecimal(500.00));

        OptimalRCRates optimalRcRates = new OptimalRCRates();
        optimalRcRates.getRoomClass().add(roomClass);
        arrivalDateType.getOptimalRCRates().add(optimalRcRates);

        LocalDate arrivalDate = new LocalDate(2015, 11, 1);

        groupEvaluationArrivalDate.setArrivalDate(arrivalDate);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

        // Mock out pricing offsets
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = new HashMap<>();

        CPConfigMergedOffsetPK pk2 = new CPConfigMergedOffsetPK(arrivalDate, 1, 2, OccupancyType.SINGLE);

        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        offset2.setId(pk2);
        offset2.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        offset2.setOffsetValue(expectedRateForRoomType2);

        offsets.put(pk2, offset2);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.findOffsetsForDates(arrivalDate, arrivalDate)).thenReturn(offsets);
        when(configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        when(pricingConfigurationService.isAccomClassPriceExcluded(accomClass)).thenReturn(true);
        when(tenantCrudService.find(AccomClass.class, accomClass.getId())).thenReturn(accomClass);
        FloorCeilingDto floorCeilingDto = new FloorCeilingDto();
        floorCeilingDto.setRoomClass(1);
        floorCeilingDto.setFloor(expectedRateForRoomType1);
        floorCeilingDto.setCeiling(expectedRateForRoomType1);
        doReturn(floorCeilingDto).when(spy).getFloorCeilingDto(anyMap());

        spy.handleRoomTypeEvaluationResults(groupEvaluationArrivalDate, arrivalDateType);

        List<GroupEvaluationArrivalDateAccomClass> roomClassRates = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateAccomClasses();

        assertTrue(roomClassRates.size() == 1);
        // validate room type rates
        validateRoomTypeRates(expectedRateForRoomType1, expectedRateForRoomType2, roomClassRates);
    }

    private XMLGregorianCalendar buildXMLGregorianCalendar(LocalDate localDate) throws Exception {
        XMLGregorianCalendar cal = DatatypeFactory.newInstance().newXMLGregorianCalendar();
        cal.setDay(localDate.getDayOfMonth());
        cal.setMonth(localDate.getMonthOfYear());
        cal.setYear(localDate.getYear());
        cal.setHour(0);
        cal.setMinute(0);
        cal.setSecond(0);
        cal.setMillisecond(0);
        return cal;
    }

    private GroupEvaluation buildGroupEvaluation() {
        GroupEvaluation groupEval = new GroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setArrivalDate(new LocalDate(2015, 1, 1));
        arrivalDate.setPreferredDate(true);
        groupEval.addGroupEvaluationArrivalDate(arrivalDate);
        return groupEval;
    }

    private List<FunctionSpaceDayPart> mockDayParts() {
        // build function space day parts
        List<FunctionSpaceDayPart> fsDayParts = singletonList(FunctionSpaceObjectMother.buildDayPart());
        fsDayParts.get(0).setId(1);

        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(fsDayParts);
        return fsDayParts;
    }

    private Date convertToDate(XMLGregorianCalendar xmlDate) {
        return new XmlUtil().convertXMLGregorianToDate(xmlDate);
    }

    private Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> buildPriceTierMARMap() {
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> priceTierMARMap = new HashMap<>();
        Map<DayOfWeek, FunctionSpaceLimitsDto> limitsMap = new HashMap<DayOfWeek, FunctionSpaceLimitsDto>();

        for (DayOfWeek dow : DayOfWeek.values()) {
            limitsMap.put(dow, new FunctionSpaceLimitsDto(dow, new BigDecimal(250), new BigDecimal(2000)));
            priceTierMARMap.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, limitsMap);
        }

        return priceTierMARMap;
    }

    private GroupPriceResponseType buildGroupPriceResponseType(GroupEvaluation groupEvaluation) throws Exception {
        GroupPriceResponseType groupPriceResponse = new GroupPriceResponseType();

        for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluation.getGroupEvaluationArrivalDates()) {
            ArrivalDateType arrivalDateType = new ArrivalDateType();
            arrivalDateType.setDate(buildXMLGregorianCalendar(groupEvaluationArrivalDate.getArrivalDate()));
            groupPriceResponse.getArrivalDate().add(arrivalDateType);

            List<GroupEvaluationArrivalDateForecastGroup> groupEvaluationArrivalDateForecastGroups = groupEvaluationArrivalDate
                    .getGroupEvaluationArrivalDateForecastGroups();
            if (groupEvaluationArrivalDateForecastGroups != null) {

                for (GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup : groupEvaluationArrivalDateForecastGroups) {
                    ForecastGroup forecastGroup = new ForecastGroup();
                    forecastGroup.setFgID(groupEvaluationArrivalDateForecastGroup.getForecastGroup().getId());
                    arrivalDateType.getForecastGroup().add(forecastGroup);

                    List<GroupEvaluationArrivalDateForecastGroupDateROH> groupEvaluationArrivalDateForecastGroupDatesROH = groupEvaluationArrivalDateForecastGroup
                            .getGroupEvaluationArrivalDateForecastGroupDatesROH();
                    if (groupEvaluationArrivalDateForecastGroupDatesROH != null) {

                        for (GroupEvaluationArrivalDateForecastGroupDateROH groupEvaluationArrivalDateForecastGroupDateROH : groupEvaluationArrivalDateForecastGroupDatesROH) {
                            OccupancyDate occupancyDate = new OccupancyDate();
                            occupancyDate.setDate(buildXMLGregorianCalendar(
                                    groupEvaluationArrivalDateForecastGroupDateROH.getOccupancyDate()));
                            forecastGroup.getOccupancyDate().add(occupancyDate);
                        }
                    }
                }
            }
        }

        return groupPriceResponse;
    }

    @Test
    public void testCalculateFloor() throws Exception {
        FloorCeilingDto floorCeilingDto = Mockito.mock(FloorCeilingDto.class);
        CPConfigMergedOffset cpConfigMergedOffset = Mockito.mock(CPConfigMergedOffset.class);
        Mockito.when(cpConfigMergedOffset.getOffsetMethod()).thenReturn(OffsetMethod.PERCENTAGE);
        Mockito.when(cpConfigMergedOffset.getOffsetValue()).thenReturn(BigDecimal.valueOf(20));
        Mockito.when(floorCeilingDto.getFloor()).thenReturn(BigDecimal.valueOf(100));
        assertEquals(120, transformer.calculateFloor(floorCeilingDto, cpConfigMergedOffset).intValue());
    }

    @Test
    public void testCalculateCeiling() throws Exception {
        FloorCeilingDto floorCeilingDto = Mockito.mock(FloorCeilingDto.class);
        CPConfigMergedOffset cpConfigMergedOffset = Mockito.mock(CPConfigMergedOffset.class);
        Mockito.when(cpConfigMergedOffset.getOffsetMethod()).thenReturn(OffsetMethod.PERCENTAGE);
        Mockito.when(cpConfigMergedOffset.getOffsetValue()).thenReturn(BigDecimal.valueOf(20));
        Mockito.when(floorCeilingDto.getCeiling()).thenReturn(BigDecimal.valueOf(100));
        assertEquals(120, transformer.calculateCeiling(floorCeilingDto, cpConfigMergedOffset).intValue());
    }

    @Test
    public void getFloorCeilingDto_noSeasonValues() throws Exception {
        GroupPricingBaseAccomType accomType = (GroupPricingBaseAccomType) PricingConfigurationObjectMother.buildGroupPricingBaseAccomType();
        FloorCeilingDto floorCeilingDto = new FloorCeilingDto();
        floorCeilingDto.setOccupancyDate(new LocalDate(2017, 8, 29));
        floorCeilingDto.setPropertyId(5);
        floorCeilingDto.setRoomClass(1);
        floorCeilingDto.setFloor(null);
        floorCeilingDto.setCeiling(null);

        when(tenantCrudService.findByNativeQuerySingleResult(anyString(), anyMap(), anyObject())).thenReturn(floorCeilingDto);
        when(tenantCrudService.findByNamedQuerySingleResult(GroupPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS, QueryParameter.with("accomClassId", floorCeilingDto.getRoomClass()).and("propertyId", floorCeilingDto.getPropertyId()).parameters())).thenReturn(accomType);

        floorCeilingDto = transformer.getFloorCeilingDto(new HashedMap());

        assertEquals(accomType.getMondayCeilingRate(), floorCeilingDto.getCeiling());
        assertEquals(accomType.getMondayFloorRate(), floorCeilingDto.getFloor());
    }

    @Test
    public void calculateFloor_priceExcludedRoomClass() throws Exception {
        BigDecimal fixedPrice = new BigDecimal(5000.00);
        FloorCeilingDto floorCeilingDto = new FloorCeilingDto();
        CPConfigMergedOffset cpConfigMergedOffset = Mockito.mock(CPConfigMergedOffset.class);
        Mockito.when(cpConfigMergedOffset.getOffsetMethod()).thenReturn(OffsetMethod.FIXED_PRICE);
        Mockito.when(cpConfigMergedOffset.getOffsetValue()).thenReturn(fixedPrice);

        assertEquals(fixedPrice, transformer.calculateFloor(floorCeilingDto, cpConfigMergedOffset));
    }

    @Test
    public void calculateCeiling_priceExcludedRoomClass() throws Exception {
        BigDecimal fixedPrice = new BigDecimal(5000.00);
        FloorCeilingDto floorCeilingDto = new FloorCeilingDto();
        CPConfigMergedOffset cpConfigMergedOffset = Mockito.mock(CPConfigMergedOffset.class);
        Mockito.when(cpConfigMergedOffset.getOffsetMethod()).thenReturn(OffsetMethod.FIXED_PRICE);
        Mockito.when(cpConfigMergedOffset.getOffsetValue()).thenReturn(fixedPrice);

        assertEquals(fixedPrice, transformer.calculateCeiling(floorCeilingDto, cpConfigMergedOffset));
    }

    @Test
    public void addFunctionSpaces_nonPackageRentalMapForSingleNonPackageRoom1() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);

        setFunctionSpacePackageMap(groupEval);
        setFunctionSpaceFunctionRoomPriceTier();
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());


        assertEquals(new BigDecimal(1262.4125).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().size());
    }

    @Test
    public void calculateNonPackageRentalWithRoomRentalConfigurationUpperLimitTest() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();

        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace fs1 = buildFunctionSpaceWithConfigureRoomRentalRoomRoyalA();
        fs1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);

        GroupEvaluationFunctionSpace royalB = buildFunctionSpaceWithConfigureRoomRentalRoomRoyalB();
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);
        setFunctionSpaceFunctionRoomPriceTier();
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());


        assertEquals(new BigDecimal(833.333).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().size());

        assertEquals(new BigDecimal(8333.2500).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageUpperLimitRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageUpperLimitRoomMap().size());

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2015, 1, 1)));
        arrivalDate.setPreferredDate(true);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(12000));
        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(6000).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());

        // lower recommended rental than default and upper limit
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(2000));
        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(1000).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());

        // Higher recommended rental than default and upper limit
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(80000));
        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(8333.25).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental().setScale(2, ROUND_HALF_UP));
    }

    @Test
    public void calculateNonPackageRentalWithRoomRentalConfigurationNotPresent() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();

        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace fs1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        fs1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs1);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);
        setFunctionSpaceFunctionRoomPriceTier();
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());


        assertEquals(new BigDecimal(1262.41).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().size());

        assertEquals(new BigDecimal(0.00).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageUpperLimitRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageUpperLimitRoomMap().size());

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2015, 1, 1)));
        arrivalDate.setPreferredDate(true);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(12000));
        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(5640.00).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());

        // lower recommended rental than default and upper limit
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(2000));
        transformer.applyResults(groupEval,groupPriceResponse);
        assertEquals(new BigDecimal(1262.41).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental().setScale(2, ROUND_HALF_UP));

        // Higher recommended rental than default and upper limit
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(80000));
        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(37600.00).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental().setScale(2, ROUND_HALF_UP));
    }

    private GroupEvaluationFunctionSpace buildFunctionSpaceWithConfigureRoomRentalRoomRoyalB() {
        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();

        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Royal B", BigDecimal.valueOf(100)));
        roomB.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(200));
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomB.getFunctionSpaceFunctionRoom().setId(11);
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setThursdayDefaultMAR(new BigDecimal(12000));
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(roomB);
        return fs2;
    }

    private GroupEvaluationFunctionSpace buildGroupEvaluationFunctionSpaceRoomRoyalA() {
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs1.setStartTime(new LocalDateTime(2020, 1, 31, 17, 37));
        fs1.setEndTime(new LocalDateTime(2020, 2, 1, 17, 37, 0));
        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();

        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Royel A", BigDecimal.valueOf(1496.23)));
        roomA.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(139));
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons().forEach(marValue -> {
            marValue.setStartDate(new LocalDate(2020, 1, 29));
            marValue.setEndDate(new LocalDate(2020, 2, 10));
            marValue.setSaturdayMAR(new BigDecimal(1000));
            marValue.setFridayMAR(new BigDecimal(4000));
        });
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(12000));
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        roomA.getFunctionSpaceFunctionRoom().setId(12);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);
        return fs1;
    }

    private GroupEvaluationFunctionSpace buildFunctionSpaceWithConfigureRoomRentalRoomRoyalA() {
        GroupEvaluationFunctionSpace fs1 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        GroupEvaluationFunctionSpaceFunctionRoom roomA = new GroupEvaluationFunctionSpaceFunctionRoom();

        roomA.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Royal A", BigDecimal.valueOf(100)));
        roomA.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(200));
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons().forEach(marValue -> {
            marValue.setStartDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2020, 1, 29)));
            marValue.setEndDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2020, 2, 10)));
            marValue.setThursdayMAR(new BigDecimal(1000));
            marValue.setFridayMAR(new BigDecimal(4000));
            marValue.setFunctionSpaceFunctionRoomRentalMARSeasonDetails(buildFSRoomRentalMarSeasonDetails());
        });
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setThursdayDefaultMAR(new BigDecimal(12000));
        roomA.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        roomA.getFunctionSpaceFunctionRoom().setFunctionSpaceConfigureRoomRentalDetails(buildFSConfigureRoomRentalDetails());
        roomA.getFunctionSpaceFunctionRoom().setId(12);
        fs1.addGroupEvaluationFunctionSpaceFunctionRoom(roomA);
        return fs1;
    }

    private FunctionSpaceConfigureRoomRentalDetails buildFSConfigureRoomRentalDetails() {
        FunctionSpaceConfigureRoomRentalDetails details = new FunctionSpaceConfigureRoomRentalDetails();
        details.setConfigureRoomRental(true);
        details.setRoomRentalThursdayUpperLimit(new BigDecimal(99999));
        details.setRoomRentalFridayUpperLimit(new BigDecimal(99999));
        details.setRoomRentalThursdayDefaultMAR(new BigDecimal(10000));
        details.setRoomRentalFridayDefaultMAR(new BigDecimal(10000));
        return details;
    }

    private FunctionSpaceFunctionRoomRentalMARSeasonDetails buildFSRoomRentalMarSeasonDetails() {
        FunctionSpaceFunctionRoomRentalMARSeasonDetails details = new FunctionSpaceFunctionRoomRentalMARSeasonDetails();
        details.setConfigureRentalMAR(true);
        details.setThursdayRoomRentalMAR(new BigDecimal(10000));
        details.setFridayRoomRentalMAR(new BigDecimal(10000));
        return details;
    }

    @Test
    public void addFunctionSpaces_nonPackageRentalMapForSingleNonPackageRoom2() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        GroupEvaluationFunctionSpace fs = buildGroupEvaluationFunctionSpaceRoomFoyer();
        fs.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(fs);

        setFunctionSpacePackageMap(groupEval);
        setFunctionSpaceFunctionRoomPriceTier();
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(new BigDecimal(1115.246422).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().
                getFunctionSpacesNonRentalPackageRoomMap().values().iterator().next().setScale(2, ROUND_HALF_UP));
        assertEquals(1, groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpacesNonRentalPackageRoomMap().size());


    }

    private GroupEvaluationFunctionSpace buildGroupEvaluationFunctionSpaceRoomFoyer() {
        GroupEvaluationFunctionSpace fs3 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs3.setStartTime(new LocalDateTime(2020, 1, 31, 17, 37));
        fs3.setEndTime(new LocalDateTime(2020, 2, 1, 17, 37, 0));
        GroupEvaluationFunctionSpaceFunctionRoom roomC = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomC.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Foyer 1st", BigDecimal.valueOf(1345.53)));
        roomC.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(125));
        roomC.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomC.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons().forEach(marValue -> {
            marValue.setStartDate(new LocalDate(2020, 1, 29));
            marValue.setEndDate(new LocalDate(2020, 2, 10));
            marValue.setSaturdayMAR(new BigDecimal(1000));
            marValue.setFridayMAR(new BigDecimal(2000));
        });
        roomC.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(12000));
        roomC.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        roomC.getFunctionSpaceFunctionRoom().setId(10);
        fs3.addGroupEvaluationFunctionSpaceFunctionRoom(roomC);
        return fs3;
    }

    @Test
    public void addFunctionSpaces_nonPackageRentalMapForMultipleNonPackageRoom() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();

        GroupEvaluationFunctionSpace royalA = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA);

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);
        setFunctionSpaceFunctionRoomPriceTier();
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        ArrDateDetails arrDateDetails = new ArrDateDetails();
        transformer.addFunctionSpace(groupEval, arrDateDetails,
                groupEval.getGroupEvaluationArrivalDates().iterator().next());

        assertEquals(2, groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpacesNonRentalPackageRoomMap().size());


    }

    private GroupEvaluationFunctionSpace buildGroupEvaluationFunctionSpaceRoomRoyalB() {
        GroupEvaluationFunctionSpace fs2 = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceWithoutFunctionSpaceRooms();
        fs2.setStartTime(new LocalDateTime(2020, 1, 31, 17, 37));
        fs2.setEndTime(new LocalDateTime(2020, 2, 1, 17, 37, 0));
        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Royel B", BigDecimal.valueOf(1679.22)));
        roomB.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(156));
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons().forEach(marValue -> {
            marValue.setStartDate(new LocalDate(2020, 1, 29));
            marValue.setEndDate(new LocalDate(2020, 2, 10));
            marValue.setSaturdayMAR(new BigDecimal(1000));
            marValue.setFridayMAR(new BigDecimal(4000));
        });
        roomB.getFunctionSpaceFunctionRoom().setId(11);
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(12000));
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        fs2.addGroupEvaluationFunctionSpaceFunctionRoom(roomB);
        return fs2;
    }

    private void setFunctionSpaceFunctionRoomPriceTier() {
        // Set Tier Map
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> sqFtPerPriceTier = new HashMap<>();
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_1, new BigDecimal(4520.99031));
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_2, new BigDecimal(1571.58234));
        sqFtPerPriceTier.put(FunctionSpaceFunctionRoomPriceTier.TIER_3, new BigDecimal(6576.96450));
        when(functionSpaceConfigurationService.getTotalSqFeetForFunctionRoomsPerPriceTier())
                .thenReturn(sqFtPerPriceTier);

    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValueForSingleRoom() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalA());

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalB());
        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        10, FunctionSpaceFunctionRoomPriceTier.TIER_1);
                arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1115.246422));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(11776.99).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(3533.10).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testIfRecommendedFSRentalIsLessThanCalculatedNonRentalMARForSingleRoom() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalA());

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalB());
        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        10, FunctionSpaceFunctionRoomPriceTier.TIER_1);
                arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1115.246422));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(800).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(240).setScale(2, ROUND_HALF_UP), groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testIfRecommendedFSRentalIsLessThanCalculatedNonRentalMARForMultipleRooms() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA);

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalB());
        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 10)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1115.246422).setScale(2, ROUND_HALF_UP));
                else if (room.getFunctionSpaceFunctionRoom().getId() == 12)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(800).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(504).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValueForMultipleRoomsNotIncluded() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA);

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);

        groupEval.getGroupEvaluationFunctionSpaces().add(buildGroupEvaluationFunctionSpaceRoomRoyalB());
        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 10)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1115.246422).setScale(2, ROUND_HALF_UP));
                else if (room.getFunctionSpaceFunctionRoom().getId() == 12)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(11776.99).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(7419.51).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_ForSameRoomsOneNotIncluded() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setStartTime(new LocalDateTime(2020, 1, 31, 10, 23));
        royalA1.setEndTime(new LocalDateTime(2020, 2, 1, 10, 23));
        royalA1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalA2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA2.setStartTime(new LocalDateTime(2020, 2, 1, 10, 24));
        royalA2.setEndTime(new LocalDateTime(2020, 2, 2, 10, 24));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA2);

        GroupEvaluationFunctionSpace royalA3 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA3.setStartTime(new LocalDateTime(2020, 2, 2, 10, 25));
        royalA3.setEndTime(new LocalDateTime(2020, 2, 3, 10, 25));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA3);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 12)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(19732.23).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(6511.641).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_ForDiffRoomsOneNotIncluded() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setStartTime(new LocalDateTime(2020, 1, 31, 10, 56));
        royalA1.setEndTime(new LocalDateTime(2020, 2, 1, 10, 56));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalA2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA2.setStartTime(new LocalDateTime(2020, 2, 1, 10, 56));
        royalA2.setEndTime(new LocalDateTime(2020, 2, 2, 10, 56));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA2);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        royalB.setStartTime(new LocalDateTime(2020, 2, 3, 10, 57));
        royalB.setEndTime(new LocalDateTime(2020, 2, 3, 14, 57));
        royalB.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 11)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(17276.42).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(2591.463).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_AllRoomExcluded() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        royalB.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 11)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(6766.12).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(6766.12).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_AllRoomIncluded() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);

        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(6766.12).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(BigDecimal.ZERO,
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_ForCommaSeperatedRooms() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setStartTime(new LocalDateTime(2020, 1, 31, 15, 26));
        royalA1.setEndTime(new LocalDateTime(2020, 2, 1, 15, 26));
        royalA1.setRentalIncludedInPackage(false);
        GroupEvaluationFunctionSpaceFunctionRoom roomB = new GroupEvaluationFunctionSpaceFunctionRoom();
        roomB.setFunctionSpaceFunctionRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Royel B", BigDecimal.valueOf(1679.22)));
        roomB.getFunctionSpaceFunctionRoom().setAreaSqMeters(BigDecimal.valueOf(156));
        roomB.getFunctionSpaceFunctionRoom().setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons().forEach(marValue -> {
            marValue.setStartDate(new LocalDate(2020, 1, 29));
            marValue.setEndDate(new LocalDate(2020, 2, 10));
            marValue.setSaturdayMAR(new BigDecimal(1000));
            marValue.setFridayMAR(new BigDecimal(4000));
        });
        roomB.getFunctionSpaceFunctionRoom().setId(11);
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(12000));
        roomB.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(18000));
        royalA1.getGroupEvaluationFunctionSpaceFunctionRooms().add(roomB);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace foyer = buildGroupEvaluationFunctionSpaceRoomFoyer();
        foyer.setStartTime(new LocalDateTime(2020, 1, 31, 15, 26));
        foyer.setEndTime(new LocalDateTime(2020, 2, 1, 15, 26));
        groupEval.getGroupEvaluationFunctionSpaces().add(foyer);


        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);


        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(new LocalDate(2020, 1, 31));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                for (GroupEvaluationFunctionSpaceFunctionRoom room : grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms()) {
                    FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                            room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                    if (room.getFunctionSpaceFunctionRoom().getId() == 11)
                        arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
                    else if (room.getFunctionSpaceFunctionRoom().getId() == 12)
                        arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
                }
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(10490.72).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(7343.51).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void shouldBuildGroupPricingPackageResult() throws Exception {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalGRRate(BigDecimal.valueOf(15.50));
        GroupPricingConfigurationConferenceAndBanquet confAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(PacmanWorkContextHelper.getPropertyId());
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(confAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(confAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(Set.of(groupEvaluationConferenceAndBanquet));
        groupEvaluation.setGroupEvaluationDayOfStays(Set.of(getGroupEvaluationDayOfStay()));
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        when(groupEvaluationService.getPerRoomServicingCost(groupEvaluation)).thenReturn(BigDecimal.valueOf(34.70));

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        Set<GroupEvaluationGroupPricingPackageDetail> groupPricingPackageDetails = groupEvaluation.getGroupEvaluationGroupPricingPackageDetails();
        assertNotNull(groupPricingPackageDetails);
        assertEquals(1, groupPricingPackageDetails.size());
        assertEquals(1, groupPricingPackageDetails.iterator().next().getPackageRevenueByArrivalDates().size());
        assertEquals(2, groupPricingPackageDetails.iterator().next().getRevenueByRevenueGroups().size());
    }

    @Test
    public void shouldNotBuildGroupPricingPackageResultWhenGroupPricingIsDisabled() throws Exception {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalGRRate(BigDecimal.valueOf(15.50));
        GroupPricingConfigurationConferenceAndBanquet confAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(PacmanWorkContextHelper.getPropertyId());
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(confAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(confAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(Set.of(groupEvaluationConferenceAndBanquet));
        groupEvaluation.setGroupEvaluationDayOfStays(Set.of(getGroupEvaluationDayOfStay()));
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        when(groupEvaluationService.getPerRoomServicingCost(groupEvaluation)).thenReturn(BigDecimal.valueOf(34.70));

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertEquals(0, groupEvaluation.getGroupEvaluationGroupPricingPackageDetails().size());
    }

    @Test
    public void shouldNotBuildGroupPricingPackageResultWhenGroupPricingPackageToggleIsDisabled() throws Exception {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalGRRate(BigDecimal.valueOf(15.50));
        GroupPricingConfigurationConferenceAndBanquet confAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(PacmanWorkContextHelper.getPropertyId());
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(confAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(confAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(Set.of(groupEvaluationConferenceAndBanquet));
        groupEvaluation.setGroupEvaluationDayOfStays(Set.of(getGroupEvaluationDayOfStay()));
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(false);
        when(groupEvaluationService.getPerRoomServicingCost(groupEvaluation)).thenReturn(BigDecimal.valueOf(34.70));

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertEquals(0, groupEvaluation.getGroupEvaluationGroupPricingPackageDetails().size());
    }

    @Test
    public void shouldNotBuildGroupPricingPackageResultWhenFunctionSpacePackageToggleIsDisabled() throws Exception {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalGRRate(BigDecimal.valueOf(15.50));
        GroupPricingConfigurationConferenceAndBanquet confAndBanquet = GroupEvaluationObjectMother.buildGroupPricingConfigurationConferenceAndBanquet(PacmanWorkContextHelper.getPropertyId());
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(confAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(confAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(emptySet());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(Set.of(groupEvaluationConferenceAndBanquet));
        groupEvaluation.setGroupEvaluationDayOfStays(Set.of(getGroupEvaluationDayOfStay()));
        String NON_GUEST_ROOM = "non.guest.room.type";
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = Set.of(
                GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricing(buildGroupPricingConfigurationPackage(),
                        GroupEvaluationObjectMother.buildGroupPricingEvalPackagePricingDOSSet(Map.ofEntries(Map.entry(1, 50))),
                        NON_GUEST_ROOM,
                        BigDecimal.valueOf(10)));
        groupEvaluation.setGroupPricingEvalPackagePricings(groupPricingEvalPackagePricings);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(false);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        when(groupEvaluationService.getPerRoomServicingCost(groupEvaluation)).thenReturn(BigDecimal.valueOf(34.70));

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertEquals(0, groupEvaluation.getGroupEvaluationGroupPricingPackageDetails().size());
    }

    @Test
    public void shouldBuildResultUsingFunctionSpacePackagePricingWhenGroupPricingPackageIsEnabledWithUseFunctionSpacePackageData() throws Exception {
        GroupEvaluation groupEvaluation = buildGroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalGRRate(BigDecimal.valueOf(15.50));
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup(1, "AVI", BigDecimal.valueOf(0.25));
        GroupEvaluationFunctionSpaceConfAndBanq fsConfAndBanquet = GroupEvaluationObjectMother.buildGroupEvaluationFunctionSpaceConfAndBanq(BigDecimal.valueOf(200).setScale(2, HALF_UP),
                BigDecimal.valueOf(0.20).setScale(2, HALF_UP), functionSpaceRevenueGroup);
        groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(fsConfAndBanquet));
        groupEvaluation.setGroupEvaluationDayOfStays(Set.of(getGroupEvaluationDayOfStay()));
        groupEvaluation.setGroupPricingEvalPackagePricings(emptySet());
        setFunctionSpacePackageMap(groupEvaluation);
        when(configService.getBooleanParameterValue(GROUP_PRICING_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(IS_GROUP_PRICING_PACKAGE_ENABLED)).thenReturn(true);
        when(configService.getBooleanParameterValue(USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(groupEvaluationService.getPerRoomServicingCost(groupEvaluation)).thenReturn(BigDecimal.valueOf(34.70));

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        Set<GroupEvaluationFunctionSpacePackageDetail> functionSpacePackageDetails = groupEvaluation.getGroupEvaluationFunctionSpacePackageDetails();
        assertNotNull(functionSpacePackageDetails);
        assertEquals(2, functionSpacePackageDetails.size());
        GroupEvaluationFunctionSpacePackageDetail evaluationFunctionSpacePackageDetail1 = functionSpacePackageDetails.iterator().next();
        assertEquals(1, evaluationFunctionSpacePackageDetail1.getGroupEvaluationFunctionSpacePackageDayOfStays().size());
        assertEquals(1, evaluationFunctionSpacePackageDetail1.getGroupEvaluationFunctionSpaceArrivalDatePackages().size());
        assertEquals(3, evaluationFunctionSpacePackageDetail1.getGroupEvaluationFunctionSpacePackageRevenueGroups().size());
        GroupEvaluationFunctionSpacePackageDetail evaluationFunctionSpacePackageDetail2 = functionSpacePackageDetails.iterator().next();
        assertEquals(1, evaluationFunctionSpacePackageDetail2.getGroupEvaluationFunctionSpacePackageDayOfStays().size());
        assertEquals(1, evaluationFunctionSpacePackageDetail2.getGroupEvaluationFunctionSpaceArrivalDatePackages().size());
        assertEquals(3, evaluationFunctionSpacePackageDetail2.getGroupEvaluationFunctionSpacePackageRevenueGroups().size());
    }

    private GroupEvaluationDayOfStay getGroupEvaluationDayOfStay() {
        GroupEvaluationDayOfStay groupEvaluationDayOfStay = new GroupEvaluationDayOfStay();
        groupEvaluationDayOfStay.setDayOfStay(1);
        groupEvaluationDayOfStay.setNumberOfRooms(10);
        return groupEvaluationDayOfStay;
    }

    private List<FunctionSpaceDayPart> getDayPartsList() {
        FunctionSpaceDayPart dayPart1 = new FunctionSpaceDayPart();
        dayPart1.setId(1);
        dayPart1.setPropertyId(6);
        dayPart1.setName("Morning");
        dayPart1.setIncluded(true);
        dayPart1.setBeginTime(new LocalTime(6, 0, 0));
        dayPart1.setEndTime(new LocalTime(11, 0, 0));
        dayPart1.setAssociation(-1);
        dayPart1.setStatus(Status.ACTIVE);

        FunctionSpaceDayPart dayPart2 = new FunctionSpaceDayPart();
        dayPart2.setId(2);
        dayPart2.setPropertyId(6);
        dayPart2.setName("noon");
        dayPart2.setIncluded(true);
        dayPart2.setBeginTime(new LocalTime(11, 0, 0));
        dayPart2.setEndTime(new LocalTime(14, 0, 0));
        dayPart2.setAssociation(-1);
        dayPart2.setStatus(Status.ACTIVE);

        FunctionSpaceDayPart dayPart3 = new FunctionSpaceDayPart();
        dayPart3.setId(3);
        dayPart3.setPropertyId(6);
        dayPart3.setName("Evening");
        dayPart3.setIncluded(true);
        dayPart3.setBeginTime(new LocalTime(14, 0, 0));
        dayPart3.setEndTime(new LocalTime(18, 30, 0));
        dayPart3.setAssociation(0);
        dayPart3.setStatus(Status.ACTIVE);

        List<FunctionSpaceDayPart> fsDayPartsList = new ArrayList<>();
        fsDayPartsList.add(dayPart1);
        fsDayPartsList.add(dayPart2);
        fsDayPartsList.add(dayPart3);

        return fsDayPartsList;
    }

    private void setFunctionSpacePackageMap(GroupEvaluation groupEval) {
        Map<String, FunctionSpacePackage> functionSpacePackageMap = FunctionSpaceObjectMother.buildFunctionSpacePackages();
        String SINGLE_OCCUPANCY = "single.occupancy";
        String DOUBLE_OCCUPANCY = "double.occupancy";
        Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricings = new HashSet<>();
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing1 =
                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic"),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50))), SINGLE_OCCUPANCY,
                        BigDecimal.valueOf(10));
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing2 =
                FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricing(functionSpacePackageMap.get("Full Day Basic"),
                        FunctionSpaceObjectMother.buildFunctionSpaceEvalPackagePricingDOS(Map.ofEntries(Map.entry(1, 50))), DOUBLE_OCCUPANCY,
                        BigDecimal.valueOf(10));

        functionSpaceEvalPackagePricings.add(functionSpaceEvalPackagePricing1);
        functionSpaceEvalPackagePricings.add(functionSpaceEvalPackagePricing2);
        groupEval.setFunctionSpaceEvalPackagePricings(functionSpaceEvalPackagePricings);
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_ForSameRoomsOneNotIncludedWithOverlappingTime() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 10, 23)));
        royalA1.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 10, 23)));
        royalA1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalA2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 8, 23)));
        royalA2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 10, 23)));
        royalA2.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA2);

        GroupEvaluationFunctionSpace royalA3 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA3.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 10, 24)));
        royalA3.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 2, 10, 24)));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA3);

        GroupEvaluationFunctionSpace royalA4 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA4.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 2, 10, 25)));
        royalA4.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 3, 10, 25)));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA4);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);

        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2020, 1, 31)));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 12)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(19732.23).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(6511.641).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void testCalculateFunctionSpaceNonRentalValue_ForDiffRoomsOneNotIncludedWithOverlappingTime() throws Exception {
        GroupEvaluation groupEval = buildGroupEvaluation();
        groupEval.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupEval.setEvaluationType(GroupEvaluationType.FUNCTION_SPACE_ONLY);

        GroupEvaluationFunctionSpace royalA1 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA1.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 1, 31, 10, 56)));
        royalA1.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 10, 56)));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA1);

        GroupEvaluationFunctionSpace royalA2 = buildGroupEvaluationFunctionSpaceRoomRoyalA();
        royalA2.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 1, 10, 56)));
        royalA2.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 2, 10, 56)));
        groupEval.getGroupEvaluationFunctionSpaces().add(royalA2);

        GroupEvaluationFunctionSpace royalB = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        royalB.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 3, 10, 57)));
        royalB.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 3, 11, 57)));
        royalB.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB);

        GroupEvaluationFunctionSpace royalB1 = buildGroupEvaluationFunctionSpaceRoomRoyalB();
        royalB1.setStartTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 3, 10, 57)));
        royalB1.setEndTime(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.of(2020, 2, 3, 14, 57)));
        royalB1.setRentalIncludedInPackage(false);
        groupEval.getGroupEvaluationFunctionSpaces().add(royalB1);

        setFunctionSpacePackageMap(groupEval);

        // Set Toggle
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value()))
                .thenReturn(true);
        when(configService.getBooleanParameterValue(IS_FUNCTION_SPACE_PACKAGE_ENABLED)).thenReturn(true);

        // set Day part
        when(functionSpaceConfigurationService.getAllIncludedDayParts()).thenReturn(getDayPartsList());
        when(functionSpaceConfigurationService.getTotalDayPartHoursAvailablePerDay())
                .thenReturn(new BigDecimal(12));

        GroupEvaluationArrivalDate arrivalDate = groupEval.getGroupEvaluationArrivalDates().iterator().next();
        arrivalDate.setArrivalDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2020, 1, 31)));
        arrivalDate.setPreferredDate(true);
        groupEval.getGroupEvaluationFunctionSpaces().forEach(grpEvalFS -> {
            if (!grpEvalFS.getRentalIncludedInPackage()) {
                GroupEvaluationFunctionSpaceFunctionRoom room = grpEvalFS.getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next();
                FSNonRentalKey key = new FSNonRentalKey(grpEvalFS.getStartTime(), grpEvalFS.getEndTime(),
                        room.getFunctionSpaceFunctionRoom().getId(), FunctionSpaceFunctionRoomPriceTier.TIER_1);
                if (room.getFunctionSpaceFunctionRoom().getId() == 11)
                    arrivalDate.getFunctionSpacesNonRentalPackageRoomMap().put(key, new BigDecimal(1262.412500).setScale(2, ROUND_HALF_UP));
            }
        });
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEval);
        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalFSRate(new BigDecimal(17276.42).setScale(2, ROUND_HALF_UP));

        transformer.applyResults(groupEval, groupPriceResponse);
        assertEquals(new BigDecimal(2591.463).setScale(2, ROUND_HALF_UP),
                groupEval.getGroupEvaluationArrivalDates().iterator().next().getFunctionSpaceNonPackageRental());
    }

    @Test
    public void shouldPopulateTotalProfitContractualRevenueTotalProfitPercentageTotalRoomsAndNumberOfNights() throws Exception {
        GroupPriceResponseType groupPriceResponse = buildGroupPriceResponseType(groupEvaluation);

        ArrivalDateType firstArrivalDateType = groupPriceResponse.getArrivalDate().get(0);
        firstArrivalDateType.setOptimalRate(new BigDecimal(100));
        firstArrivalDateType.setBreakevenRate(new BigDecimal(100));
        firstArrivalDateType.setAvgWeightedMAR(new BigDecimal(100));
        firstArrivalDateType.setGrossRoomRev(new BigDecimal(99));
        firstArrivalDateType.setConcessions(new BigDecimal(98));
        firstArrivalDateType.setCommissions(new BigDecimal(97));
        firstArrivalDateType.setNetRoomRev(new BigDecimal(96));
        firstArrivalDateType.setNetIncRoomRev(new BigDecimal(95));
        firstArrivalDateType.setIncRooms(new BigDecimal(5));
        firstArrivalDateType.setRoomProfit(new BigDecimal(93));
        firstArrivalDateType.setIncRoomProfit(new BigDecimal(92));
        firstArrivalDateType.setAncillaryRev(new BigDecimal(91));
        firstArrivalDateType.setAncillaryProfit(new BigDecimal(90));
        firstArrivalDateType.setConfBanqRev(new BigDecimal(89));
        firstArrivalDateType.setConfBanqProfit(new BigDecimal(88));
        firstArrivalDateType.setNetIncRev(new BigDecimal(87));
        firstArrivalDateType.setNetIncProfit(new BigDecimal(86));
        firstArrivalDateType.setDisplacedAncillaryRev(new BigDecimal(85));
        firstArrivalDateType.setDisplacedAncillaryProfit(new BigDecimal(84));
        firstArrivalDateType.setGrossFSRev(new BigDecimal(100));
        firstArrivalDateType.setIncFSProfit(new BigDecimal(85));
        firstArrivalDateType.setNetFSRev(new BigDecimal(85));
        firstArrivalDateType.setBreakevenFSRate(new BigDecimal(50));
        firstArrivalDateType.setFsProfit(new BigDecimal(90));

        firstArrivalDateType.setPreStay(new ArrivalDateType.PreStay());
        firstArrivalDateType.setPostStay(new ArrivalDateType.PostStay());
        firstArrivalDateType.getPreStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPreStay().setOccFcstWithoutGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithGroup(BigDecimal.TEN);
        firstArrivalDateType.getPostStay().setOccFcstWithoutGroup(BigDecimal.TEN);

        firstArrivalDateType.setMinProfitRate(new BigDecimal(100));

        ForecastGroup forecastGroup = new ForecastGroup();
        forecastGroup.setFgID(1);
        forecastGroup.setDisplacedRoomProfit(BigDecimal.TEN);
        forecastGroup.setDisplacedRoomRev(BigDecimal.TEN);
        firstArrivalDateType.getForecastGroup().add(forecastGroup);

        OccupancyDate occupancyDate = new OccupancyDate();
        occupancyDate.setDate(buildXMLGregorianCalendar(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.now())));
        occupancyDate.setDisplacedRooms(BigDecimal.TEN);
        occupancyDate.setOccFcstWithGroup(new BigDecimal(20));
        occupancyDate.setOccFcstWithoutGroup(BigDecimal.TEN);
        forecastGroup.getOccupancyDate().add(occupancyDate);

        ForecastGroupSummary forecastGroupSummary = new ForecastGroupSummary();
        forecastGroupSummary.setId(1);

        when(tenantCrudService.find(ForecastGroupSummary.class, forecastGroup.getFgID())).thenReturn(forecastGroupSummary);
        when(dateService.getPropertyCurrentTime()).thenReturn(JavaLocalDateUtils.toJodaLocalDateTime(java.time.LocalDateTime.now()));
        when(configService.getBooleanParameterValue(FUNCTION_SPACE_ENABLED.value())).thenReturn(true);

        transformer.applyResults(groupEvaluation, groupPriceResponse);

        assertNotNull(groupEvaluation.getEvaluationDate());

        GroupEvaluationArrivalDate firstGroupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();


        assertEquals(firstGroupEvaluationArrivalDate.getSystemTotalNetProfit(),firstGroupEvaluationArrivalDate.getTotalNetProfit(true));
        assertEquals(firstGroupEvaluationArrivalDate.getTotalProfitPercentage(),firstGroupEvaluationArrivalDate.getTotalNetProfitPercentage(true));
        assertEquals(firstGroupEvaluationArrivalDate.getContractedRevenue(),firstGroupEvaluationArrivalDate.getContractualRevenueRequired());
        assertEquals(firstGroupEvaluationArrivalDate.getNumberOfNights(),firstGroupEvaluationArrivalDate.getGroupEvaluation().getNumberOfNights());
        assertEquals(firstGroupEvaluationArrivalDate.getTotalRooms(),firstGroupEvaluationArrivalDate.getGroupEvaluation().getTotalNumberOfRooms());
    }
}
