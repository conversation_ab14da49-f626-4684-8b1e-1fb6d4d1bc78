package com.ideas.tetris.pacman.services.pacebackfill;

import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockTestHelper;
import com.ideas.tetris.pacman.services.marketsegment.entity.*;
import com.ideas.tetris.pacman.services.pacebackfill.entity.PaceBackfillLog;
import com.ideas.tetris.pacman.testdatabuilder.AccomTypeBuilder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import static org.junit.jupiter.api.Assertions.*;

class BackFillServiceSQLDataTest  extends AbstractG3JupiterTest {

    public static final int MAX_PAST_DAYS = 50;
    public static final int FUTURE_DAYS = 40;
    public static final int PACE_NUMBER_DAYS = 365;
    private static int BOOKED_ACCOM_TYPE_ID;
    @InjectMocks
    BackFillService backFillService;
    protected static CrudService tenantCrudService;
    private static final Integer PROPERTY_ID = 5;
    private static MarketSegmentSummary GRP_MKT_SEGMENT;
    private static Integer ACCOM_TYPE_ID;
    private static AccomType PACE_RT;
    private static AccomType BOOKED_RT;
    private static Integer TRANS_MKT_SEGMENT_ID;
    private static LocalDate SNAPSHOT_DT;

    @BeforeEach
    public void setUp() {
        tenantCrudService = CrudServiceBeanExtension.getTenantCrudService();
        inject(backFillService, "tenantCrudService", tenantCrudService);
        clearAllReferredTables();
        cookDataForPacebackfill();
    }

    private static void cookDataForPacebackfill() {
        Date latestDate = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.GET_LATEST_SNAPSHOT_DT);
        SNAPSHOT_DT = LocalDateUtils.toJavaLocalDate(latestDate);
        // Create Mkt Segs
        GRP_MKT_SEGMENT = createMktSegment("GRP_MKT_SEG");
        createMktSegDetails(GRP_MKT_SEGMENT, 100, BusinessType.GROUP);
        MarketSegmentSummary transMktSeg = createMktSegment("TRANS_MKT_SEG");
        TRANS_MKT_SEGMENT_ID = transMktSeg.getId();
        createMktSegDetails(transMktSeg, 0, BusinessType.TRANSIENT);
        AccomClass accomClass = tenantCrudService.findOne(AccomClass.class);
        PACE_RT = createAccomType("PACE_RT", accomClass);
        ACCOM_TYPE_ID = PACE_RT.getId();
        BOOKED_RT = createAccomType("BOOKED_RT", accomClass);
        BOOKED_ACCOM_TYPE_ID = BOOKED_RT.getId();
        // Add metadata entries
        tenantCrudService.executeUpdateByNamedQuery(FileMetadata.MERGE_FILE_METADATA_FOR_BACKFILL, QueryParameter.with("propertyId", PROPERTY_ID)
                .and("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS)).and("endDate", SNAPSHOT_DT).parameters());
    }

    private static AccomType createAccomType(String name, AccomClass accomClass) {
        AccomType rt = new AccomTypeBuilder().withName(name).withAccomClass(accomClass)
                .withAccomTypeCode(name).withPropertyId(PROPERTY_ID).withSystemDefault(0)
                .withStatusId(1).buildData();
        tenantCrudService.save(rt);
        return rt;
    }

    private static void clearAllReferredTables() {
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Pace_Mkt_Activity");
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Pace_Accom_Activity");
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Pace_Total_Activity");
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Pace_Group_Block");
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Pace_Group_Master");
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Group_Block");
        tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE Reservation_Night");
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Wash_Ind_Group_Fcst_OVR");
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Wash_Ind_Group_Fcst");
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Group_Master");
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM pace_backfill_log");
    }

    @Test
    void shouldUpdatePaceBackFillLogs_AllValuesSet() {
        PaceBackfillLog log = createLog("ALL", SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(10);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(8);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "128", "CO", SNAPSHOT_DT.minusDays(15), ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "129", "CO", SNAPSHOT_DT.minusDays(35), BOOKED_ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt, GRP_MKT_SEGMENT.getId(), "130", "CO", SNAPSHOT_DT.minusDays(31), BOOKED_ACCOM_TYPE_ID, null, 100.00, 260.00);
        tenantCrudService.executeUpdateByNativeQuery("update reservation_night set inv_block_code = 'inv' where reservation_identifier = 130");
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());
        List<Object[]> pacebackfillLog = tenantCrudService.findByNativeQuery("select Group_Valid_percent, Use_Groups_As_Trans, Auto_Detect_Rate_Pace,Booked_Difference_Percent,Processed_Trans_Mkt_Seg , Processed_Group_Mkt_Seg  from pace_backfill_log");
        assertLogs(pacebackfillLog, true, 0.67);
    }

    private static void assertLogs(List<Object[]> pacebackfillLog, boolean toggle, double bookedDiffPercent) {
        assertEquals(0.0031416, pacebackfillLog.get(0)[0]);
        assertEquals(toggle, pacebackfillLog.get(0)[1]);
        assertEquals(toggle, pacebackfillLog.get(0)[2]);
        assertEquals(bookedDiffPercent, pacebackfillLog.get(0)[3]);
        assertNotNull(pacebackfillLog.get(0)[4]);
    }

    @Test
    void shouldUpdatePaceBackFillLogs() {
        PaceBackfillLog log = createLog("ALL", SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(10);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(8);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "128", "CO", SNAPSHOT_DT.minusDays(15), ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "129", "CO", SNAPSHOT_DT.minusDays(35), ACCOM_TYPE_ID, null, 100.00, 260.00);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());
        List<Object[]> pacebackfillLog = tenantCrudService.findByNativeQuery("select Group_Valid_percent, Use_Groups_As_Trans, Auto_Detect_Rate_Pace,Booked_Difference_Percent,Processed_Trans_Mkt_Seg , Processed_Group_Mkt_Seg  from pace_backfill_log");
        assertLogs(pacebackfillLog, false, 0.0);
        assertNotNull(pacebackfillLog.get(0)[5]);
    }

    private PaceBackfillLog createLog(String mkts, LocalDate snapshotDt, LocalDate endDate, LocalDate startDate) {
        PaceBackfillLog log = new PaceBackfillLog();
        log.setRequestedMarketSegmentIds(mkts);
        log.setStartDate(convertLocalDateToJavaUtilDate(startDate));
        log.setEndDate(convertLocalDateToJavaUtilDate(endDate));
        log.setFirstSnapshotDate(convertLocalDateToJavaUtilDate(snapshotDt));
        log.setPreserveHotelAccomPace(true);
        log.setCreatedDttm(new Date());
        log.setLastUpdatedDttm(new Date());
        log.setUseGroupsAsTrans(false);
        tenantCrudService.save(log);

        return log;
    }

    @Test
    void testNoShowsResverationsPace() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(10);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(8);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(15);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);

        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "125", "NS", bookingDate, ACCOM_TYPE_ID,null,  100.00, 260.00);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId," +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt and 1 departure date
        assertEquals(3, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points - 1 (No shows is calculated on Arrival_DT)
        assertEquals(5, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "260.00000", "260.00000");
        // Comparing occupancy date other than arrival date
        LocalDate occupancyDate = SNAPSHOT_DT.minusDays(9);
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), bookingDate, 1, 0, 0, 0, 0, "260.00000", "260.00000");
        // After Arrival_Date, sold goes to ZERO due to NS Transaction
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), arrivalDt, 0, 0, 0, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testPastCancelledResverationsPace() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(1);
        LocalDate departureDt = SNAPSHOT_DT.plusDays(1);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(10);
        LocalDate cancelDate = SNAPSHOT_DT.minusDays(2);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "124", "XX", bookingDate, ACCOM_TYPE_ID, "'" + cancelDate +"'", 100.00, 260.00);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt and 1 departure date
        assertEquals(3, paceByOccDt.size());

        assertEquals(6, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(1), cancelDate, 0, 0, 0, 1, 0, "0.00000", "0.00000");
        LocalDate occupancyDate = SNAPSHOT_DT;
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), bookingDate, 1, 0, 0, 0, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), cancelDate, 0, 0, 0, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(1), cancelDate, 0, 0, 0, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testPastCancelledReservationsPaceWithCancelDtBefore365() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(7);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(6);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(400);
        LocalDate cancelDate = SNAPSHOT_DT.minusDays(395);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        String missingExtractDate = "";

        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "127", "XX", bookingDate, ACCOM_TYPE_ID, "'" + cancelDate +"'", 100.00, 260.00);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", missingExtractDate)
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 1 occupancy_dt and no departure date
        assertEquals(1, paceByOccDt.size());

        assertEquals(1, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), arrivalDt.minusDays(PACE_NUMBER_DAYS), 0, 0, 0, 1, 0, "0.00000", "0.00000");
    }

    @Test
    void testFutureCancelledResverationsPace() {
        LocalDate arrivalDt = SNAPSHOT_DT.plusDays(6);
        LocalDate departureDt = SNAPSHOT_DT.plusDays(7);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(8);
        LocalDate cancelDate = SNAPSHOT_DT.minusDays(2);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "129", "XX", bookingDate, ACCOM_TYPE_ID, "'" + cancelDate +"'", 100.00, 260.00);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        String missingExtractDate = "";
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", missingExtractDate)
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 1 occupancy_dt and 1 departure date
        assertEquals(2, paceByOccDt.size());

        assertEquals(4, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(1), cancelDate, 0, 0, 0, 1, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(1), cancelDate, 0, 0, 0, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testCheckedOutResverationsPace() {
        createDummyReservationData(SNAPSHOT_DT.minusDays(10), SNAPSHOT_DT.minusDays(8), TRANS_MKT_SEGMENT_ID, "128", "CO", SNAPSHOT_DT.minusDays(15), ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(SNAPSHOT_DT.minusDays(10), SNAPSHOT_DT.minusDays(8), TRANS_MKT_SEGMENT_ID, "129", "CO", SNAPSHOT_DT.minusDays(35), ACCOM_TYPE_ID, null, 100.00, 260.00);

        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt and 1 departure date
        assertEquals(3, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points
        assertEquals(6, paceByOccDt.values().stream().mapToInt(List::size).sum());
        LocalDate occupancyDate = SNAPSHOT_DT.minusDays(10);
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), SNAPSHOT_DT.minusDays(35), 1, 1, 0, 0, 0, "100.00000", "240.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), SNAPSHOT_DT.minusDays(15), 2, 2, 0, 0, 0, "200.00000", "480.00000");
        // Comparing departures
        occupancyDate = SNAPSHOT_DT.minusDays(8);
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), SNAPSHOT_DT.minusDays(35), 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), SNAPSHOT_DT.minusDays(15), 0, 0, 2, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testCheckedOutResverationsPaceWithBookingDtBefore365() {
        createDummyReservationData(SNAPSHOT_DT.minusDays(10), SNAPSHOT_DT.minusDays(8), TRANS_MKT_SEGMENT_ID, "129", "CO", SNAPSHOT_DT.minusDays(395), ACCOM_TYPE_ID, null, 100.00, 260.00);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));

        assertEquals(3, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points
        assertEquals(3, paceByOccDt.values().stream().mapToInt(List::size).sum());
        LocalDate occupancyDate = SNAPSHOT_DT.minusDays(10);
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), occupancyDate.minusDays(PACE_NUMBER_DAYS), 1, 1, 0, 0, 0, "100.00000", "240.00000");
        // Comparing departures
        occupancyDate = SNAPSHOT_DT.minusDays(8);
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), occupancyDate.minusDays(PACE_NUMBER_DAYS), 0, 0, 1, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testDayUsePace() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(7);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(7);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(10);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        createDummyDayUseReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "127", "CO", bookingDate, ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt.plusDays(1), TRANS_MKT_SEGMENT_ID, "128", "CO", bookingDate, ACCOM_TYPE_ID, null, 100.00, 260.00);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 1)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt
        assertEquals(2, paceByOccDt.size());

        assertEquals(2, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "200.00000", "480.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt.plusDays(1))).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testIgnoreDayUsePaceWhenToggleOFF() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(7);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(7);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(10);
        createDummyDayUseReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "127", "CO", bookingDate, ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt.plusDays(1), TRANS_MKT_SEGMENT_ID, "128", "CO", bookingDate, ACCOM_TYPE_ID, null, 100.00, 260.00);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt
        assertEquals(2, paceByOccDt.size());

        assertEquals(2, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "100.00000", "240.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt.plusDays(1))).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testPostDepartureRevenuePace() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(7);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(6);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(10);
        LocalDate postDepartureDt = departureDt.plusDays(2);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "128", "CO", bookingDate, ACCOM_TYPE_ID, null, 100.00, 260.00);
        createPostDepartureReservationData(postDepartureDt, TRANS_MKT_SEGMENT_ID, "128", ACCOM_TYPE_ID, 100.00, 260.00);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId," +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 1)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt
        assertEquals(3, paceByOccDt.size());

        assertEquals(3, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "100.00000", "240.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(postDepartureDt)).get(0), arrivalDt, 0, 0, 0, 0, 0, "100.00000", "240.00000");
    }

    @Test
    void testIncludeXXNSRevenuePace() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(7);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(6);
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(12);
        LocalDate cancelDate = SNAPSHOT_DT.minusDays(9);

        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "124", "XX", bookingDate, ACCOM_TYPE_ID, "'" + cancelDate +"'", 100.00, 260.00);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 1)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 0)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt
        assertEquals(2, paceByOccDt.size());

        assertEquals(4, paceByOccDt.values().stream().mapToInt(List::size).sum());
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(0), bookingDate, 1, 1, 0, 0, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(arrivalDt)).get(1), cancelDate, 0, 0, 0, 1, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(0), bookingDate, 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(departureDt)).get(1), cancelDate, 0, 0, 0, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testCheckedOutResverationsPaceWithTotalRate() {
        LocalDate arrivalDt = SNAPSHOT_DT.minusDays(10);
        LocalDate departureDt = SNAPSHOT_DT.minusDays(8);

        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "128", "CO", SNAPSHOT_DT.minusDays(15), ACCOM_TYPE_ID, null, 100.00, 260.00);
        createDummyReservationData(arrivalDt, departureDt, TRANS_MKT_SEGMENT_ID, "129", "CO", SNAPSHOT_DT.minusDays(35), ACCOM_TYPE_ID, null, 100.00, 260.00);
        LocalDate startDate = SNAPSHOT_DT.minusDays(MAX_PAST_DAYS);
        LocalDate endDate = SNAPSHOT_DT.plusDays(FUTURE_DAYS);
        PaceBackfillLog log = createLog(TRANS_MKT_SEGMENT_ID.toString(), SNAPSHOT_DT, endDate, startDate);

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 2 occupancy_dt and 1 departure date
        assertEquals(3, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points
        assertEquals(6, paceByOccDt.values().stream().mapToInt(List::size).sum());
        LocalDate occupancyDate = arrivalDt;
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), SNAPSHOT_DT.minusDays(35), 1, 1, 0, 0, 0, "260.00000", "260.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), SNAPSHOT_DT.minusDays(15), 2, 2, 0, 0, 0, "520.00000", "520.00000");
        // Comparing departures
        occupancyDate = departureDt;
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(0), SNAPSHOT_DT.minusDays(35), 0, 0, 1, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(occupancyDate)).get(1), SNAPSHOT_DT.minusDays(15), 0, 0, 2, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testHappyPathGroupPace() {
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(20);
        LocalDate startDate = SNAPSHOT_DT.minusDays(2);
        LocalDate endDate = SNAPSHOT_DT.plusDays(2);
        int blocks = 10;
        int pickup = 8;
        int originalBlocks = 10;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(LocalDateUtils.toDate(bookingDate));
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate secondBdeDate = bookingDate.plusDays(10);
        LocalDate fourthBdeDate = endDate.minusDays(2);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(0, 0, bookingDate), new PaceGroupBlockHelper(5, 0, secondBdeDate),
                new PaceGroupBlockHelper(10, 6, startDate), new PaceGroupBlockHelper(10, 8, fourthBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points expect startDate, it will have 1 pace point as cutoffDate will be startDate; cutOffDate=StartDate
        assertEquals(9, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), secondBdeDate, 5, 5, 0, 0, 0, "500.00000", "500.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), secondBdeDate, 5, 5, 5, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(1), startDate, 6, 6, 6, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(0), secondBdeDate, 0, 0, 5, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(1), startDate, 0, 0, 8, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testGroupWithNoBookingDate() {
        LocalDate startDate = SNAPSHOT_DT.minusDays(2);
        LocalDate endDate = SNAPSHOT_DT.plusDays(2);
        int blocks = 10;
        int pickup = 8;
        int originalBlocks = 10;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(null);
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(20);
        LocalDate secondBdeDate = SNAPSHOT_DT.minusDays(10);
        LocalDate fourthBdeDate = endDate.minusDays(2);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(0, 0, bookingDate), new PaceGroupBlockHelper(5, 0, secondBdeDate),
                new PaceGroupBlockHelper(10, 6, startDate), new PaceGroupBlockHelper(10, 8, fourthBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());


        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        // Each occupancy date has 4 differential pace points expect startDate, it will have 1 pace point as cutoffDate will be startDate; cutOffDate=StartDate
        assertEquals(19, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), startDate.minusDays(60), 10, 10, 0, 0, 0, "1000.00000", "1000.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(1), startDate.minusDays(59), 0, 0, 0, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(2), secondBdeDate, 5, 5, 0, 0, 0, "500.00000", "500.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), startDate.minusDays(60), 10, 10, 10, 0, 0, "1000.00000", "1000.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(1), startDate.minusDays(59), 0, 0, 0, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(2), secondBdeDate, 5, 5, 5, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(3), startDate, 6, 6, 6, 0, 0, "600.00000", "600.00000");
    }

    @Test
    void testGroupWithCutOffDate() {
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(20);
        LocalDate startDate = SNAPSHOT_DT.minusDays(2);
        LocalDate endDate = SNAPSHOT_DT.plusDays(2);
        LocalDate cutOffDate = startDate.minusDays(2);
        int blocks = 10;
        int pickup = 8;
        int originalBlocks = 10;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(LocalDateUtils.toDate(bookingDate));
        oldDefGroup.setCutoffDate(LocalDateUtils.toDate(cutOffDate));
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate secondBdeDate = SNAPSHOT_DT.minusDays(10);
        LocalDate fourthBdeDate = endDate.minusDays(2);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(0, 0, bookingDate), new PaceGroupBlockHelper(5, 0, secondBdeDate),
                new PaceGroupBlockHelper(10, 6, startDate), new PaceGroupBlockHelper(10, 8, fourthBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());

        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points expect startDate
        assertEquals(10, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), secondBdeDate, 5, 5, 0, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(1), cutOffDate, 6, 6, 0, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), secondBdeDate, 5, 5, 5, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(1), cutOffDate, 6, 6, 6, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(0), secondBdeDate, 0, 0, 5, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(1), cutOffDate, 0, 0, 8, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testGroupWithUseBookDtAsCutOffDt() {
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(20);
        LocalDate startDate = SNAPSHOT_DT.minusDays(2);
        LocalDate endDate = SNAPSHOT_DT.plusDays(2);
        int blocks = 10;
        int pickup = 8;
        int originalBlocks = 10;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(LocalDateUtils.toDate(bookingDate));
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate secondBdeDate = SNAPSHOT_DT.minusDays(10);
        LocalDate fourthBdeDate = endDate.minusDays(2);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(0, 0, bookingDate), new PaceGroupBlockHelper(5, 0, secondBdeDate),
                new PaceGroupBlockHelper(10, 6, startDate), new PaceGroupBlockHelper(10, 8, fourthBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 1)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        // Each occupancy date has 2 differential pace points expect startDate
        assertEquals(5, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), bookingDate, 6, 6, 0, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), bookingDate, 6, 6, 6, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(0), bookingDate, 0, 0, 8, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testGroupWithCutOffDays() {
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(40);
        LocalDate startDate = SNAPSHOT_DT.minusDays(20);
        LocalDate endDate = SNAPSHOT_DT.minusDays(16);
        int blocks = 10;
        int pickup = 8;
        int originalBlocks = 10;
        int cutoffDays = 2;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(LocalDateUtils.toDate(bookingDate));
        oldDefGroup.setCutoffDays(cutoffDays);
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate secondBdeDate = SNAPSHOT_DT.minusDays(28);
        LocalDate fourthBdeDate = endDate.minusDays(2);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(0, 0, bookingDate), new PaceGroupBlockHelper(5, 0, secondBdeDate),
                new PaceGroupBlockHelper(10, 6, startDate), new PaceGroupBlockHelper(10, 8, fourthBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        assertEquals(14, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), secondBdeDate, 5, 5, 0, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(1), startDate.minusDays(cutoffDays), 6, 6, 0, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), secondBdeDate, 5, 5, 5, 0, 0, "500.00000", "500.00000");
        // This record is added because there has been change in departures
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(1), startDate.minusDays(cutoffDays), 5, 5, 6, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(2), startDate.plusDays(1).minusDays(cutoffDays), 6, 6, 6, 0, 0, "600.00000", "600.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(0), secondBdeDate, 0, 0, 5, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(1), startDate, 0, 0, 10, 0, 0, "0.00000", "0.00000");
        // change in departures is as per the change in solds in previous occupancy dt's pace point hence extra day is subtracted to reach the value.
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(2), endDate.minusDays(cutoffDays + 1), 0, 0, 8, 0, 0, "0.00000", "0.00000");
    }

    @Test
    void testGroupWithOnlyOneHistoryExtractPoint() {
        LocalDate bookingDate = SNAPSHOT_DT.minusDays(40);
        LocalDate startDate = SNAPSHOT_DT.minusDays(20);
        LocalDate endDate = SNAPSHOT_DT.minusDays(16);
        int blocks = 5;
        int pickup = 3;
        int originalBlocks = 10;
        GroupBlockMaster oldDefGroup = GroupBlockTestHelper.createGroupMaster("OLD_DEF_GROUP", GRP_MKT_SEGMENT, "DEFINITE", LocalDateUtils.toDate(startDate), LocalDateUtils.toDate(endDate));
        oldDefGroup.setBookingDate(LocalDateUtils.toDate(bookingDate));
        oldDefGroup = tenantCrudService.save(oldDefGroup);
        createGroupBlockDetails(startDate, endDate, oldDefGroup, blocks, pickup, originalBlocks, "100.00");
        LocalDate secondBdeDate = SNAPSHOT_DT.minusDays(28);
        List<PaceGroupBlockHelper> paceGroupBlocks = List.of(new PaceGroupBlockHelper(5, 3, secondBdeDate));
        createPaceGroupBlockDetails(startDate, endDate, oldDefGroup.getId(), paceGroupBlocks, originalBlocks, "100.00");

        PaceBackfillLog log = createLog(GRP_MKT_SEGMENT.getId().toString(), SNAPSHOT_DT, SNAPSHOT_DT.plusDays(FUTURE_DAYS), SNAPSHOT_DT.minusDays(MAX_PAST_DAYS));
        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_update_backfill_logs :logId",
                QueryParameter.with("logId", log.getId()).parameters());

        tenantCrudService.executeUpdateByNativeQuery("exec dbo.usp_backfill_from_pacman :startDate, :endDate, :logId, " +
                        " :includeCancelNsRevenue, :includeDayUseRev, :totalRateEnabled,:postDepEnabled, :useBookingDtAsCutoffDt, :usePaceGrpMaster, :missingExtractDate",
                QueryParameter.with("startDate", SNAPSHOT_DT.minusDays(MAX_PAST_DAYS))
                        .and("endDate", SNAPSHOT_DT.plusDays(FUTURE_DAYS))
                        .and("logId", log.getId())
                        .and("includeCancelNsRevenue", 0)
                        .and("includeDayUseRev", 0)
                        .and("totalRateEnabled", 1)
                        .and("postDepEnabled", 0)
                        .and("useBookingDtAsCutoffDt", 0)
                        .and("usePaceGrpMaster", 0)
                        .and("missingExtractDate", "")
                        .parameters());
        List<Object[]> paceMktActivities = tenantCrudService.findByNativeQuery("SELECT Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Total_Revenue FROM Pace_Mkt_Activity ORDER BY Mkt_Seg_ID, Occupancy_DT, Business_Day_End_DT");
        Map<Date, List<Object[]>> paceByOccDt = paceMktActivities.stream().collect(Collectors.groupingBy(row -> (Date) row[1]));
        // 4 occupancy_dt and 1 departure date
        assertEquals(5, paceByOccDt.size());
        assertEquals(14, paceByOccDt.values().stream().mapToInt(List::size).sum());

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(0), bookingDate, 10, 10, 0, 0, 0, "1000.00000", "1000.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate)).get(1), bookingDate.plusDays(1), 5, 5, 0, 0, 0, "500.00000", "500.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(0), bookingDate, 10, 10, 10, 0, 0, "1000.00000", "1000.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(1), bookingDate.plusDays(1), 5, 5, 5, 0, 0, "500.00000", "500.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(startDate.plusDays(1))).get(2), startDate, 3, 3, 3, 0, 0, "300.00000", "300.00000");

        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(0), bookingDate, 0, 0, 10, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(1), bookingDate.plusDays(1), 0, 0, 5, 0, 0, "0.00000", "0.00000");
        comparePaceActivityRecord(paceByOccDt.get(LocalDateUtils.toDate(endDate)).get(2), startDate, 0, 0, 3, 0, 0, "0.00000", "0.00000");
    }

    private void comparePaceActivityRecord(Object[] onePaceRecord, LocalDate businessDayEndDt, int roomsSold, int arrivals, int departures, int cancellations, int noShows, String roomRevenue, String totalRevenue) {
        assertAll(
                () -> assertEquals(businessDayEndDt, LocalDateUtils.toJavaLocalDate((Date) onePaceRecord[2])),
                () -> assertEquals(new BigDecimal(roomsSold), onePaceRecord[3]),
                () -> assertEquals(new BigDecimal(arrivals), onePaceRecord[4]),
                () -> assertEquals(new BigDecimal(departures), onePaceRecord[5]),
                () -> assertEquals(new BigDecimal(cancellations), onePaceRecord[6]),
                () -> assertEquals(new BigDecimal(noShows), onePaceRecord[7]),
                () -> assertEquals(new BigDecimal(roomRevenue), onePaceRecord[8]),
                () -> assertEquals(new BigDecimal(totalRevenue), onePaceRecord[9])
        );
    }

    private static void createDummyReservationData(LocalDate arrivalDt, LocalDate departureDt, int msId, String resId,
                                                   String indvStatus, LocalDate bookingDate, int rtId,
                                                   String cancelDt, double roomRev, double rateValue) {
        StringBuilder query = new StringBuilder();
        query.append("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], ")
                .append("       [Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], ")
                .append("       [Rate_Value],[Number_Children],[Number_Adults],[CreateDate_DTTM],[Occupancy_DT])  values");
        Stream.iterate(arrivalDt, occDt -> occDt.isBefore(departureDt), occDt -> occDt.plusDays(1))
                .forEach(occDt ->
                                query.append(" (1, 000005, '").append(resId).append("','").append(indvStatus).append("','").append(arrivalDt).append("','")
                                        .append(departureDt).append("','").append(bookingDate).append("',").append(cancelDt).append(",'PACE_RT', ").append(rtId).append(", ")
                                .append(msId).append("       , 1108, 'IN', ").append(roomRev).append(", 140.00, 240.00,'SHHQO1',").append(rateValue).append(",10,25,'2012-09-18 12:57:25.957', '")
                                .append(occDt).append("'),"));
        tenantCrudService.executeUpdateByNativeQuery(query.deleteCharAt(query.length() - 1).toString());
    }

    private static void createDummyDayUseReservationData(LocalDate arrivalDt, LocalDate departureDt, int msId, String resId,
                                                   String indvStatus, LocalDate bookingDate, int rtId,
                                                   String cancelDt, double roomRev, double rateValue) {
                        tenantCrudService.executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], " +
                                "       [Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], " +
                                "       [Rate_Value],[Number_Children],[Number_Adults],[CreateDate_DTTM],[Occupancy_DT]) " +
                                "       values(1, 000005, '" + resId + "','" + indvStatus + "','" + arrivalDt + "','" + departureDt + "','" + bookingDate + "'," + cancelDt + ",'D', " + rtId + ", " + msId +
                                "       , 1108, 'IN', " + roomRev + ", 140.00, 240.00,'SHHQO1'," + rateValue + ",10,25,'2012-09-18 12:57:25.957', '" + arrivalDt + "')");
    }

    private static void createPostDepartureReservationData(LocalDate postDepartureDt, int msId, String resId, int rtId,
                                                           double roomRev, double rateValue) {
                        tenantCrudService.executeUpdateByNativeQuery("insert into post_departure_revenue([Reservation_Identifier]," +
                                "       [Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], " +
                                "       [Rate_Value],[Occupancy_DT]) values('" + resId + "'," + rtId + ", " + msId +
                                "       , " + roomRev + ", 140.00, 240.00,'SHHQO1'," + rateValue + ", '" + postDepartureDt + "')");
    }

    private static MarketSegmentSummary createMktSegment(String name) {
        MarketSegmentSummary mktSeg = new MarketSegmentSummary();
        mktSeg.setCode(name);
        mktSeg.setName(name);
        mktSeg.setDescription(name);
        mktSeg.setStatusId(1);
        mktSeg.setPropertyId(PROPERTY_ID);
        tenantCrudService.save(mktSeg);
        return mktSeg;
    }

    private static void createMktSegDetails(MarketSegmentSummary mktSegSummary, int bookingBlockPc, BusinessType businessType) {
        MktSegDetails mktSegDetails = new MktSegDetails();
        MktSeg mktSeg = tenantCrudService.find(MktSeg.class, mktSegSummary.getId());
        mktSegDetails.setMktSeg(mktSeg);
        mktSegDetails.setStatusId(1);
        mktSegDetails.setBookingBlockPc(bookingBlockPc);
        mktSegDetails.setBusinessType(businessType);
        mktSegDetails.setFenced(1);
        ForecastActivityType forecastActivityType = tenantCrudService.find(ForecastActivityType.class, 1);
        mktSegDetails.setForecastActivityType(forecastActivityType);
        mktSegDetails.setLink(0);
        mktSegDetails.setPackageValue(0);
        ProcessStatus processStatus = tenantCrudService.find(ProcessStatus.class, 2);
        mktSegDetails.setProcessStatus(processStatus);
        mktSegDetails.setQualified(1);
        mktSegDetails.setTemplateDefault(0);
        YieldType yieldType = tenantCrudService.find(YieldType.class, 1);
        mktSegDetails.setYieldType(yieldType);
        tenantCrudService.save(mktSegDetails);
    }

    private static void createGroupBlockDetails(LocalDate startDate, LocalDate endDate,
                                                GroupBlockMaster groupMaster, int blocks, int pickup, int originalBlocks, String rate) {
        List<GroupBlockDetail> groupBlocks = new ArrayList<>();
        Stream.iterate(startDate, occDt -> occDt.isBefore(endDate), occDt -> occDt.plusDays(1))
                .forEach(occDt ->
                         groupBlocks.add(GroupBlockTestHelper.createGroupBlock(groupMaster, occDt, blocks, pickup, originalBlocks, PACE_RT, rate))
                );
        tenantCrudService.save(groupBlocks);
    }

    private void createPaceGroupBlockDetails(LocalDate startDate, LocalDate endDate, int groupId, List<PaceGroupBlockHelper> paceGroupBlocks, int originalBlocks, String rate) {
        StringBuilder query = new StringBuilder();
        query.append("insert into Pace_Group_Block values ");
        for (PaceGroupBlockHelper paceGroupBlock : paceGroupBlocks) {
            Stream.iterate(startDate, occDt -> occDt.isBefore(endDate), occDt -> occDt.plusDays(1))
                    .forEach(occDt ->
                            {
                                if (occDt.isAfter(paceGroupBlock.bdeDate) || occDt.isEqual(paceGroupBlock.bdeDate)) {
                                    query.append(" (").append(groupId).append(", '").append(occDt).append("',").append(ACCOM_TYPE_ID).append(",'").append(paceGroupBlock.bdeDate)
                                            .append("', ").append(paceGroupBlock.blocks).append(", ").append(paceGroupBlock.pickup).append(", ").append(originalBlocks)
                                            .append(", ").append(rate).append("),");
                                }
                            }
                    );
        }
        tenantCrudService.executeUpdateByNativeQuery(query.deleteCharAt(query.length() - 1).toString());
    }

    private static class PaceGroupBlockHelper {
        int blocks;
        int pickup;
        LocalDate bdeDate;

        public PaceGroupBlockHelper(int blocks, int pickup, LocalDate bdeDate) {
            this.blocks = blocks;
            this.pickup = pickup;
            this.bdeDate = bdeDate;
        }
    }
}