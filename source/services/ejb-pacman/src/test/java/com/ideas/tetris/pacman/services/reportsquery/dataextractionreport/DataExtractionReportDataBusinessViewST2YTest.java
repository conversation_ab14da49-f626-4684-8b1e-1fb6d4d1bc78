package com.ideas.tetris.pacman.services.reportsquery.dataextractionreport;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import java.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DataExtractionReportDataBusinessViewST2YTest extends AbstractG3JupiterTest {

    private final int propertyID = 6;
    private final int recordTypeId = 3;
    private final int processStatusId = 13;
    private final int mktSegIdSeven = 7;
    private final int mktSegIdEight = 8;
    private final int mktSegIdNine = 9;
    private final int mktSegIdTen = 10;

    private final String businessView1 = "BVTest1";
    private final String businessView2 = "BVTest2";

    private final BigDecimal ROOMS_SOLD_MKT = BigDecimal.valueOf(17);
    private final BigDecimal ROOMS_REVENUE_MKT = BigDecimal.valueOf(193.15647);
    private final BigDecimal ROOMS_SOLD_MKT_L2Y = ROOMS_SOLD_MKT.add(new BigDecimal(23));
    private final BigDecimal ROOMS_REVENUE_MKT_L2Y = ROOMS_REVENUE_MKT.add(new BigDecimal(147));
    private final BigDecimal ON_BOOKS_ADR_L2Y = ROOMS_REVENUE_MKT_L2Y.divide(ROOMS_SOLD_MKT_L2Y, RoundingMode.HALF_UP);

    private int isRolling = 0;

    private LocalDate snapShotDate;
    private LocalDate snapShotDateLast2YearsDOW;
    private LocalDate businessDate;
    private LocalDate businessDateLast2YearsDOW;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        createTestData();
    }

    private void createTestData() {
        final StringBuilder insertQuery = new StringBuilder();

        snapShotDate = getLocalDate();
        businessDate = snapShotDate.plusDays(-1);
        snapShotDateLast2YearsDOW = getDowAdjustedDateLast2Yrs(snapShotDate);
        businessDateLast2YearsDOW = getDowAdjustedDateLast2Yrs(businessDate);

        updateMainTableAtMkt(insertQuery);

        updatePaceMkt(insertQuery, snapShotDateLast2YearsDOW);
        updatePaceMkt(insertQuery, snapShotDateLast2YearsDOW.plusDays(1));

        populateBusinessViewData(insertQuery);

        updateMktSegDetails(insertQuery);
        getInventoryGroup(insertQuery);
        updateGroupBlockData(insertQuery);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private LocalDate getLocalDate() {
        List<Date> testDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + "," + recordTypeId + "," + processStatusId + ")");
        String testDate = testDates.get(0).toString();
        return LocalDate.parse(testDate);
    }

    private LocalDate getDowAdjustedDateLast2Yrs(LocalDate date) {
        List<Date> testDates = tenantCrudService().findByNativeQuery(" select DATEADD(WEEK, -104,'" + date + "') ");
        DateFormat Formatter = new SimpleDateFormat("yyyy-MM-dd");
        return LocalDate.parse(Formatter.format(testDates.get(0)));
    }

    private void updateMainTableAtMkt(StringBuilder insertQuery) {
        //noinspection StringConcatenationInsideStringBufferAppend
        insertQuery.append(" INSERT INTO [dbo].[Mkt_Accom_Activity]\n" +
                "([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Mkt_Seg_ID],[Accom_Type_ID],[Rooms_Sold],[Arrivals],[Departures]\n" +
                "              ,[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Last_Updated_DTTM]\n" +
                "              ,[CreateDate],[Pseudo_Room_Revenue],[Total_Profit],[No_Show_Revenue])\n" +
                "VALUES\n" +
                "    (6,'" + snapShotDateLast2YearsDOW.plusDays(-2) + "',GETDATE(),7,9," + ROOMS_SOLD_MKT.add(new BigDecimal(23)) + ",0,0," +
                "       0,0," + ROOMS_REVENUE_MKT.add(new BigDecimal(147)) + ",0.0,0.0,2,GETDATE()," +
                "       GETDATE(),0.0,0.0,0.0),\n" +
                "    (6,'" + snapShotDateLast2YearsDOW.plusDays(-1) + "',GETDATE(),7,9," + ROOMS_SOLD_MKT.add(new BigDecimal(23)) + ",0,0," +
                "       0,0," + ROOMS_REVENUE_MKT.add(new BigDecimal(147)) + ",0.0,0.0,2,GETDATE()," +
                "       GETDATE(),0.0,0.0,0.0),\n" +
                "    (6,'" + snapShotDateLast2YearsDOW + "',GETDATE(),7,9," + ROOMS_SOLD_MKT.add(new BigDecimal(23)) + ",0,0," +
                "       0,0," + ROOMS_REVENUE_MKT.add(new BigDecimal(147)) + ",0.0,0.0,2,GETDATE()," +
                "       GETDATE(),0.0,0.0,0.0),\n" +
                "    (6,'" + snapShotDateLast2YearsDOW.plusDays(1) + "',GETDATE(),7,9," + ROOMS_SOLD_MKT.add(new BigDecimal(23)) + ",0,0," +
                "       0,0," + ROOMS_REVENUE_MKT.add(new BigDecimal(147)) + ",0.0,0.0,2,GETDATE()," +
                "       GETDATE(),0.0,0.0,0.0),\n" +
                "    (6,'" + snapShotDateLast2YearsDOW.plusDays(2) + "',GETDATE(),7,9," + ROOMS_SOLD_MKT.add(new BigDecimal(23)) + ",0,0," +
                "       0,0," + ROOMS_REVENUE_MKT.add(new BigDecimal(147)) + ",0.0,0.0,2,GETDATE()," +
                "       GETDATE(),0.0,0.0,0.0);");
    }

    private void updatePaceMkt(StringBuilder insertQuery, LocalDate occupancyDate) {
        updatePaceTableAtMkt(insertQuery, occupancyDate, mktSegIdSeven);
        updatePaceTableAtMkt(insertQuery, occupancyDate, mktSegIdEight);
        updatePaceTableAtMkt(insertQuery, occupancyDate, mktSegIdNine);
        updatePaceTableAtMkt(insertQuery, occupancyDate, mktSegIdTen);
    }

    private void updatePaceTableAtMkt(StringBuilder insertQuery, LocalDate occupancyDate, int mktSegId) {
        insertQuery.append(" INSERT INTO PACE_Mkt_Activity ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID],[Rooms_Sold] ");
        insertQuery.append(" ,[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue],[Food_Revenue] ");
        insertQuery.append(" ,[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM]) ");
        insertQuery.append(" VALUES (" + propertyID + ",'").append(occupancyDate).append("','").append(businessDateLast2YearsDOW).append("' ");
        insertQuery.append(" ,'").append(businessDateLast2YearsDOW).append("',").append(mktSegId).append(",").append(ROOMS_SOLD_MKT).append(" ");
        insertQuery.append(" ,1,2,3,1,").append(ROOMS_REVENUE_MKT).append(",1678.9862 ");
        insertQuery.append(" ,1678.9862,2,3,9,GETDATE()) ");
    }

    private void populateBusinessViewData(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [Business_Group]([Business_Group_Name],[Business_Group_Description],[Ranking] ");
        insertQuery.append(" ,[Status_ID],[Created_by_User_ID],[Last_Updated_by_User_ID] ");
        insertQuery.append(" ,[Last_Updated_DTTM],[Created_DTTM],[Property_ID]) ");
        insertQuery.append(" VALUES ('" + businessView1 + "','For Unit Test',1,1,1,1,GETDATE(),GETDATE()," + propertyID + "), ");
        insertQuery.append(" ('" + businessView2 + "','For Unit Test',2,1,1,1,GETDATE(),GETDATE()," + propertyID + ") ");

        insertQuery.append(" INSERT INTO [Mkt_Seg_Business_Group]([Business_Group_ID],[Mkt_Seg_ID],[Ranking],[Created_By_User_ID], ");
        insertQuery.append(" [Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM]) ");
        insertQuery.append(" VALUES ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView1 + "' ), ");
        insertQuery.append(" " + mktSegIdSeven + ",1,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ), ");
        insertQuery.append(" " + mktSegIdEight + ",2,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ), ");
        insertQuery.append(" " + mktSegIdNine + ",2,1,GETDATE(),1,GETDATE()), ");
        insertQuery.append(" ((select Business_Group_ID from Business_Group where Business_Group_Name='" + businessView2 + "' ), ");
        insertQuery.append(" " + mktSegIdTen + ",2,1,GETDATE(),1,GETDATE()) ");
    }

    private void updateMktSegDetails(StringBuilder insertQuery) {
        insertQuery.append(" update Mkt_Seg_Details set Business_Type_ID=1 where Mkt_Seg_ID in (" + mktSegIdSeven + ") ");
    }

    private void excludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 1 WHERE Mkt_Seg_ID IN (" + mktSegIdEight + ", " + mktSegIdNine + ", " + mktSegIdTen + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    private void unExcludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 0 WHERE Mkt_Seg_ID  IN (" + mktSegIdEight + ", " + mktSegIdNine + ", " + mktSegIdTen + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithFutureDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDate.plusDays(1) + "'," + isRolling + ",null,null,0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate, snapShotDateLast2YearsDOW, 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate.plusDays(1), snapShotDateLast2YearsDOW.plusDays(1), 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW, 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(1), 2, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithFutureDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDate.plusDays(1) + "'," + isRolling + ",'TODAY','TODAY+1',0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate, snapShotDateLast2YearsDOW, 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate.plusDays(1), snapShotDateLast2YearsDOW.plusDays(1), 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW, 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(1), 2, reportData);
    }
    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithFutureDateRangeForStaticDateForInventoryGroup() {
        isRolling = 0;
        Object inventoryGroupId = tenantCrudService().findByNativeQuerySingleResult("select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'uiTest'", new HashMap<>());
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDate.plusDays(1) + "'," + isRolling + ",'TODAY','TODAY+1',0,1, " + inventoryGroupId + " ,0,0,0,0,1,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", BigDecimal.valueOf(3), new BigDecimal("225.00000"), snapShotDate, snapShotDateLast2YearsDOW, 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest2", BigDecimal.valueOf(5), new BigDecimal("375.00000"), snapShotDate, snapShotDateLast2YearsDOW, 1, reportData, businessView2);
        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", BigDecimal.valueOf(3), new BigDecimal("225.00000"), snapShotDate.plusDays(1), snapShotDateLast2YearsDOW.plusDays(1), 2, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest2", BigDecimal.valueOf(5), new BigDecimal("375.00000"), snapShotDate.plusDays(1), snapShotDateLast2YearsDOW.plusDays(1), 3, reportData, businessView2);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithPartiallyInPastAndPartiallyInFutureDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "'," + isRolling + ",null,null,0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-1), snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate, snapShotDateLast2YearsDOW, 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW, 2, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithPartiallyInPastAndPartiallyInFutureDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "'," + isRolling + ",'TODAY-1','TODAY',0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-1), snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate, snapShotDateLast2YearsDOW, 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW, 2, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithPastDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-2) + "','" + snapShotDate.plusDays(-1) + "'," + isRolling + ",null,null,0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-2), snapShotDateLast2YearsDOW.plusDays(-2), 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-1), snapShotDateLast2YearsDOW.plusDays(-1), 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-2), 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-1), 2, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportST2YAtBusinessViewLevelWithPastDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-2) + "','" + snapShotDate.plusDays(-1) + "'," + isRolling + ",'TODAY-2','TODAY-1',0,1, -1,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-2), snapShotDateLast2YearsDOW.plusDays(-2), 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-1), snapShotDateLast2YearsDOW.plusDays(-1), 2, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-2), 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-1), 2, reportData);
    }

    @Test
    public void shouldValidateDEReportST2YAtBVLevelWithPartiallyInPastAndPartiallyInFutureDateRangeAndExcludedCompRoomMarketSegments() {
        excludeMktSegments();

        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_bv_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "', 0, null, null, 0, 1, -1, 1, 0, 0,0,0,0");

        assertValueForGivenOccupancyDate("validate data for first date For Business Group BVTest1", ROOMS_SOLD_MKT.add(new BigDecimal(23)), ROOMS_REVENUE_MKT.add(new BigDecimal(147)), snapShotDate.plusDays(-1), snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData, businessView1);
        assertValueForGivenOccupancyDate("validate data for second date For Business Group BVTest1", ROOMS_SOLD_MKT, ROOMS_REVENUE_MKT, snapShotDate, snapShotDateLast2YearsDOW, 1, reportData, businessView1);

        assertL2YValuesForGivenOccupancyDate("validate data for first date For Business Group BVTest1", snapShotDateLast2YearsDOW.plusDays(-1), 0, reportData);
        assertL2YValuesForGivenOccupancyDate("validate data for second date For Business Group BVTest1", snapShotDateLast2YearsDOW, 1, reportData);

        unExcludeMktSegments();
    }

    private void assertValueForGivenOccupancyDate(String Level, BigDecimal roomSold, BigDecimal roomRevenue, LocalDate occupancyDt, LocalDate occupancySt2Y, int rowNumber, List<Object[]> reportData, String businessViewName) {
        assertEquals(roomSold, (reportData.get(rowNumber)[39]), Level);
        assertEquals(roomRevenue, (reportData.get(rowNumber)[40]), Level);
        assertEquals(occupancyDt, LocalDate.parse(reportData.get(rowNumber)[2].toString()), Level);
        assertEquals(businessViewName, (reportData.get(rowNumber)[3]), Level);
        assertEquals(occupancySt2Y, LocalDate.parse(reportData.get(rowNumber)[5].toString()), Level);
    }

    private void assertL2YValuesForGivenOccupancyDate(String level, LocalDate occupancyDtL2Y, int rowNumber, List<Object[]> reportData) {
        assertEquals(occupancyDtL2Y, LocalDate.parse(reportData.get(rowNumber)[5].toString()), level);
        assertEquals(ROOMS_SOLD_MKT_L2Y, new BigDecimal(reportData.get(rowNumber)[9].toString()), level);
        assertEquals(ROOMS_REVENUE_MKT_L2Y, (reportData.get(rowNumber)[13]), level);
        assertEquals(ON_BOOKS_ADR_L2Y, (reportData.get(rowNumber)[17]), level);
    }

    private void updateGroupBlockData(StringBuilder insertQuery) {
        insertIntoGroupMaster(insertQuery,"GRP1", snapShotDateLast2YearsDOW, businessDateLast2YearsDOW, mktSegIdSeven);
        insertIntoGroupMaster(insertQuery,"GRP2", snapShotDateLast2YearsDOW, businessDateLast2YearsDOW, mktSegIdEight);
        insertIntoGroupMaster(insertQuery,"GRP3",snapShotDateLast2YearsDOW.plusDays(1),businessDateLast2YearsDOW , mktSegIdSeven);
        insertIntoGroupMaster(insertQuery,"GRP4",snapShotDateLast2YearsDOW.plusDays(1),businessDateLast2YearsDOW , mktSegIdEight);

        insertIntoGroupBlock(insertQuery, snapShotDateLast2YearsDOW, "GRP1");
        insertIntoGroupBlock(insertQuery, snapShotDateLast2YearsDOW, "GRP2");
        insertIntoGroupBlock(insertQuery, snapShotDateLast2YearsDOW.plusDays(1), "GRP3");
        insertIntoGroupBlock(insertQuery, snapShotDateLast2YearsDOW.plusDays(1), "GRP4");

        insertIntoPaceGroupBlock(insertQuery,"GRP1",snapShotDateLast2YearsDOW,businessDateLast2YearsDOW.plusDays(-1), 8);
        insertIntoPaceGroupBlock(insertQuery,"GRP2",snapShotDateLast2YearsDOW,businessDateLast2YearsDOW.plusDays(-1), 6);
        insertIntoPaceGroupBlock(insertQuery,"GRP3",snapShotDateLast2YearsDOW.plusDays(1),businessDateLast2YearsDOW, 8);
        insertIntoPaceGroupBlock(insertQuery,"GRP4",snapShotDateLast2YearsDOW.plusDays(1),businessDateLast2YearsDOW, 6);
    }

    private void insertIntoGroupMaster(StringBuilder insertQuery, String groupCode, java.time.LocalDate startDate, java.time.LocalDate endDate, int mktSeg) {
        insertQuery.append("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) " + "values(000006, '")
                .append(groupCode).append("', 'Test Group Name', 'Test GM',").append(mktSeg).append(",'DEFINITE','TRANS','")
                .append(startDate).append("','").append(endDate).append("','2025-01-11');");
    }

    private void insertIntoGroupBlock(StringBuilder insertQuery, java.time.LocalDate occupancyDate, String groupCode) {
        insertQuery.append(" insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate) " + "values ((select Group_ID from Group_Master where Group_Code='")
                .append(groupCode).append("'),'").append(occupancyDate).append("',").append(9).append(",").append(11).append(",").append(8).append(",").append(11).append(",75.00);");
    }

    private void insertIntoPaceGroupBlock(StringBuilder insertQuery, String groupCode, java.time.LocalDate occupancyDate, java.time.LocalDate bdeDate, int pickup) {
        insertQuery.append(" insert into Pace_Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate) " + "values ((select Group_ID from Group_Master where Group_Code= '")
                .append(groupCode).append("'),'").append(occupancyDate).append("', " + 9 + ",'").append(bdeDate).append("',").append(11).append(",").append(pickup).append(",").append(11).append(",75.00);");
    }

    private void getInventoryGroup(StringBuilder insertQuery) {
        insertQuery.append("insert into Inventory_Group values ('uiTest', NULL, 6, 11403, getdate(), 11403, getdate(), 1)" +
                "insert into Inventory_Group_Details values ((select Inventory_Group_ID from Inventory_Group where Inventory_Group_Name = 'uiTest'), 6, 11403, getdate(), 11403, getdate())");
    }
}
