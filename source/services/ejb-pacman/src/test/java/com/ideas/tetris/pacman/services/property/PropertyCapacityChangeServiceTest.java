package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.event.MetaData;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.common.event.TetrisEventType;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PropertyCapacityChangeServiceTest {
    private static final int CLIENT_ID = 99;
    private static final Integer PROPERTY_ID = new Integer(0);
    private static final String PROPERTY_CODE = "TESTX";
    private static final BigDecimal OLD_CAPACITY = new BigDecimal(100);
    private static final BigDecimal CURRENT_CAPACITY_SAME = new BigDecimal(100);
    private static final BigDecimal CURRENT_CAPACITY_CHANGED = new BigDecimal(200);
    private static final Date CAUGHTUP_DATE = new Date();
    private static final String ROOM_TYPE_CONFIG_RECORD = "_RT_|TESTX|KXTD|KING STANDARD NON SMOKING|STANDARD|N|200";
    private static final String ROOM_TYPE_CONFIG_RECORD_ZERO_CAPACITY = "_RT_|TESTX|KXTD|KING STANDARD NON SMOKING|STANDARD|N|0";

    private PropertyCapacityChangeService propertyCapacityChangeService = new PropertyCapacityChangeService();

    TetrisEventManager tetrisEventManager;
    DateService dateService;
    CrudService globalCrudService;
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @BeforeEach
    public void setUp() {
        tetrisEventManager = mock(TetrisEventManager.class);
        propertyCapacityChangeService.tetrisEventManager = tetrisEventManager;
        dateService = mock(DateService.class);
        propertyCapacityChangeService.dateService = dateService;
        globalCrudService = mock(CrudService.class);
        propertyCapacityChangeService.globalCrudService = globalCrudService;
        multiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        propertyCapacityChangeService.multiPropertyCrudService = multiPropertyCrudService;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Test
    public void historyExtractCapacityChange() {
        when(dateService.getCaughtUpDate()).thenReturn(CAUGHTUP_DATE);
        TotalActivity totalActivity = new TotalActivity();
        totalActivity.setTotalAccomCapacity(CURRENT_CAPACITY_SAME);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(totalActivity);

        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, String.valueOf(CURRENT_CAPACITY_SAME.intValue())));
        TetrisEvent te = new TetrisEvent(TetrisEventType.CAPACITY_CHANGED, PROPERTY_ID, new Date(), mdList);
        when(tetrisEventManager.buildCapacityChangeEvent(PROPERTY_ID, String.valueOf(CURRENT_CAPACITY_SAME.intValue()))).thenReturn(te);
        tetrisEventManager.raiseEvent(te);
        //expectLastCall().times(1);

        propertyCapacityChangeService.executeFromJEMS(PROPERTY_ID, true);
        verify(dateService, times(1)).getCaughtUpDate();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Test
    public void dailyExtractNoCapacityChange() {
        when(dateService.getCaughtUpDate()).thenReturn(CAUGHTUP_DATE);
        TotalActivity oldTotalActivity = new TotalActivity();
        oldTotalActivity.setTotalAccomCapacity(OLD_CAPACITY);
        TotalActivity currentTotalActivity = new TotalActivity();
        currentTotalActivity.setTotalAccomCapacity(CURRENT_CAPACITY_SAME);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(oldTotalActivity);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(currentTotalActivity);

        propertyCapacityChangeService.executeFromJEMS(PROPERTY_ID, false);
        verify(dateService, times(1)).getCaughtUpDate();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Test
    public void dailyExtractCapacityChange() {
        when(dateService.getCaughtUpDate()).thenReturn(CAUGHTUP_DATE);
        TotalActivity oldTotalActivity = new TotalActivity();
        oldTotalActivity.setTotalAccomCapacity(OLD_CAPACITY);
        TotalActivity currentTotalActivity = new TotalActivity();
        currentTotalActivity.setTotalAccomCapacity(CURRENT_CAPACITY_CHANGED);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(currentTotalActivity);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(oldTotalActivity);

        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, String.valueOf(CURRENT_CAPACITY_CHANGED.intValue())));
        TetrisEvent te = new TetrisEvent(TetrisEventType.CAPACITY_CHANGED, PROPERTY_ID, new Date(), mdList);
        when(tetrisEventManager.buildCapacityChangeEvent(PROPERTY_ID, String.valueOf(CURRENT_CAPACITY_CHANGED.intValue()))).thenReturn(te);
        tetrisEventManager.raiseEvent(te);

        propertyCapacityChangeService.executeFromJEMS(PROPERTY_ID, false);
        verify(dateService, times(1)).getCaughtUpDate();
    }

    @Test
    public void raiseCapacityChangeForAddProperty() {
        List<ConfigurationFileRecord> records = new ArrayList<ConfigurationFileRecord>();
        ConfigurationFileRecord record = new ConfigurationFileRecord();
        record.setRecord(ROOM_TYPE_CONFIG_RECORD);
        records.add(record);
        when(globalCrudService.<ConfigurationFileRecord>findByNamedQuery(ConfigurationFileRecord.BY_TYPE_CLIENT_AND_PROPERTY,
                QueryParameter.with("propertyCode", PROPERTY_CODE).and("recordType", PropertyConfigurationRecordType.RT.name())
                        .and("clientId", CLIENT_ID).parameters())).thenReturn(records);

        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, String.valueOf(CURRENT_CAPACITY_CHANGED.intValue())));
        TetrisEvent te = new TetrisEvent(TetrisEventType.CAPACITY_CHANGED, PROPERTY_ID, new Date(), mdList);
        when(tetrisEventManager.buildCapacityChangeEvent(PROPERTY_ID, String.valueOf(CURRENT_CAPACITY_CHANGED.intValue()))).thenReturn(te);
        tetrisEventManager.raiseEvent(te);
        propertyCapacityChangeService.raiseCapacityChangeForAddProperty(PROPERTY_ID, PROPERTY_CODE, CLIENT_ID);

        verify(globalCrudService, times(1)).findByNamedQuery(Mockito.anyString(), Mockito.anyMap());
        verify(tetrisEventManager).buildCapacityChangeEvent(Mockito.anyInt(), Mockito.anyString());
    }

    @Test
    public void raiseCapacityChangeForAddPropertyZeroCapacity() {
        List<ConfigurationFileRecord> records = new ArrayList<ConfigurationFileRecord>();
        ConfigurationFileRecord record = new ConfigurationFileRecord();
        record.setRecord(ROOM_TYPE_CONFIG_RECORD_ZERO_CAPACITY);
        records.add(record);
        when(globalCrudService.<ConfigurationFileRecord>findByNamedQuery(ConfigurationFileRecord.BY_TYPE_CLIENT_AND_PROPERTY,
                QueryParameter.with("propertyCode", PROPERTY_CODE).and("recordType", PropertyConfigurationRecordType.RT.name())
                        .and("clientId", CLIENT_ID).parameters())).thenReturn(records);

        propertyCapacityChangeService.raiseCapacityChangeForAddProperty(PROPERTY_ID, PROPERTY_CODE, CLIENT_ID);
        verify(globalCrudService, times(1)).findByNamedQuery(Mockito.anyString(), Mockito.anyMap());
    }

    @Test
    public void raiseCapacityChangeForAddPropertyNoRoomTypeRecords() {
        List<ConfigurationFileRecord> records = new ArrayList<ConfigurationFileRecord>();
        when(globalCrudService.<ConfigurationFileRecord>findByNamedQuery(ConfigurationFileRecord.BY_TYPE_CLIENT_AND_PROPERTY,
                QueryParameter.with("propertyCode", PROPERTY_CODE).and("recordType", PropertyConfigurationRecordType.RT.name())
                        .and("clientId", CLIENT_ID).parameters())).thenReturn(records);

        propertyCapacityChangeService.raiseCapacityChangeForAddProperty(PROPERTY_ID, PROPERTY_CODE, CLIENT_ID);
        verify(globalCrudService, times(1)).findByNamedQuery(Mockito.anyString(), Mockito.anyMap());
    }

    @SuppressWarnings({"unchecked"})
    @Test
    public void executeFromJEMS() {
        Integer propertyId = PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE;
        PacmanWorkContextTestHelper.updateWorkContext(new WorkContextType(), PacmanWorkContextTestHelper.WC_USER_ID_REGULAR,
                PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE, PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE,
                propertyId, PacmanWorkContextTestHelper.WC_PROPERTY_CODE_PUNE);

        when(dateService.getCaughtUpDate()).thenReturn(CAUGHTUP_DATE);
        TotalActivity totalActivity = new TotalActivity();
        totalActivity.setTotalAccomCapacity(CURRENT_CAPACITY_SAME);

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.anyInt(),
                Mockito.anyString(), Mockito.anyMap())).thenReturn(totalActivity);

        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, String.valueOf(CURRENT_CAPACITY_SAME.intValue())));
        TetrisEvent te = new TetrisEvent(TetrisEventType.CAPACITY_CHANGED, propertyId, new Date(),
                mdList);

        when(tetrisEventManager.buildCapacityChangeEvent(propertyId,
                String.valueOf(CURRENT_CAPACITY_SAME.intValue()))).thenReturn(te);
        tetrisEventManager.raiseEvent(te);

        propertyCapacityChangeService.executeFromJEMS(propertyId, true);

        verify(tetrisEventManager).buildCapacityChangeEvent(Mockito.anyInt(), Mockito.anyString());
    }
}
