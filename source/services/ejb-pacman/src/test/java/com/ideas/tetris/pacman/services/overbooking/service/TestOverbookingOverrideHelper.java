package com.ideas.tetris.pacman.services.overbooking.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionCOWValueOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccomOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkProperty;
import com.ideas.tetris.pacman.services.overbooking.entity.OVROverbookingType;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomSeason;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingProperty;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingPropertySeason;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class TestOverbookingOverrideHelper extends AbstractG3JupiterTest {
    public OverbookingOverrideHelper overrideHelper;
    LocalDate testOccupancyDate;
    DecisionOvrbkProperty decisionOvrbkProperty;

    @BeforeEach
    public void setUp() {
        testOccupancyDate = new LocalDate(2010, 1, 1);

        overrideHelper = new OverbookingOverrideHelper();
        overrideHelper.setCrudService(tenantCrudService());
        decisionOvrbkProperty = new DecisionOvrbkProperty();
        decisionOvrbkProperty = new DecisionOvrbkProperty();
        decisionOvrbkProperty.setCreateDate(testOccupancyDate.toDate());
        decisionOvrbkProperty.setOccupancyDate(testOccupancyDate.toDate());
        decisionOvrbkProperty.setDecisionId(1);
        decisionOvrbkProperty.setMaxSolds(2);
        decisionOvrbkProperty.setOverbookingDecision(2);
        decisionOvrbkProperty.setPropertyId(5);
        decisionOvrbkProperty.setExpectedWalks(BigDecimal.ONE);
        tenantCrudService().save(decisionOvrbkProperty);
    }

    @Test
    public void testGetOverbookingLimitProperty() throws Exception {
        OverbookingProperty ovbkProperty = new OverbookingProperty();
        Integer sundayCeiling = 10;
        ovbkProperty.setSundayCeiling(sundayCeiling);
        Integer sundayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateSunday());
        assertEquals(sundayCeiling, sundayCeilingActual);

        Integer mondayCeiling = 20;
        ovbkProperty.setMondayCeiling(mondayCeiling);
        Integer mondayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateMonday());
        assertEquals(mondayCeiling, mondayCeilingActual);

        Integer tuesdayCeiling = 30;
        ovbkProperty.setTuesdayCeiling(tuesdayCeiling);
        Integer tuesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateTuesday());
        assertEquals(tuesdayCeiling, tuesdayCeilingActual);

        Integer wednesdayCeiling = 30;
        ovbkProperty.setWednesdayCeiling(wednesdayCeiling);
        Integer wednesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateWednesday());
        assertEquals(wednesdayCeiling, wednesdayCeilingActual);

        Integer thursdayCeiling = 40;
        ovbkProperty.setThursdayCeiling(thursdayCeiling);
        Integer thursdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateThursday());
        assertEquals(thursdayCeiling, thursdayCeilingActual);

        Integer fridayCeiling = 50;
        ovbkProperty.setFridayCeiling(fridayCeiling);
        Integer fridayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateFriday());
        assertEquals(fridayCeiling, fridayCeilingActual);

        Integer saturdayCeiling = 60;
        ovbkProperty.setSaturdayCeiling(saturdayCeiling);
        Integer saturdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkProperty, getDateSaturday());
        assertEquals(saturdayCeiling, saturdayCeilingActual);
    }

    @Test
    public void testGetOverbookingLimitPropertySeason() throws Exception {
        OverbookingPropertySeason ovbkPropertySeason = new OverbookingPropertySeason();
        Integer sundayCeiling = 10;
        ovbkPropertySeason.setSundayCeiling(sundayCeiling);
        Integer sundayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateSunday());
        assertEquals(sundayCeiling, sundayCeilingActual);

        Integer mondayCeiling = 20;
        ovbkPropertySeason.setMondayCeiling(mondayCeiling);
        Integer mondayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateMonday());
        assertEquals(mondayCeiling, mondayCeilingActual);

        Integer tuesdayCeiling = 30;
        ovbkPropertySeason.setTuesdayCeiling(tuesdayCeiling);
        Integer tuesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateTuesday());
        assertEquals(tuesdayCeiling, tuesdayCeilingActual);

        Integer wednesdayCeiling = 30;
        ovbkPropertySeason.setWednesdayCeiling(wednesdayCeiling);
        Integer wednesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateWednesday());
        assertEquals(wednesdayCeiling, wednesdayCeilingActual);

        Integer thursdayCeiling = 40;
        ovbkPropertySeason.setThursdayCeiling(thursdayCeiling);
        Integer thursdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateThursday());
        assertEquals(thursdayCeiling, thursdayCeilingActual);

        Integer fridayCeiling = 50;
        ovbkPropertySeason.setFridayCeiling(fridayCeiling);
        Integer fridayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateFriday());
        assertEquals(fridayCeiling, fridayCeilingActual);

        Integer saturdayCeiling = 60;
        ovbkPropertySeason.setSaturdayCeiling(saturdayCeiling);
        Integer saturdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkPropertySeason, getDateSaturday());
        assertEquals(saturdayCeiling, saturdayCeilingActual);
    }

    @Test
    public void testGetOverbookingLimitAccom() throws Exception {
        OverbookingAccom ovbkAccom = new OverbookingAccom();
        Integer sundayCeiling = 10;
        ovbkAccom.setSundayCeiling(sundayCeiling);
        Integer sundayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateSunday());
        assertEquals(sundayCeiling, sundayCeilingActual);

        Integer mondayCeiling = 20;
        ovbkAccom.setMondayCeiling(mondayCeiling);
        Integer mondayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateMonday());
        assertEquals(mondayCeiling, mondayCeilingActual);

        Integer tuesdayCeiling = 30;
        ovbkAccom.setTuesdayCeiling(tuesdayCeiling);
        Integer tuesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateTuesday());
        assertEquals(tuesdayCeiling, tuesdayCeilingActual);

        Integer wednesdayCeiling = 30;
        ovbkAccom.setWednesdayCeiling(wednesdayCeiling);
        Integer wednesdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateWednesday());
        assertEquals(wednesdayCeiling, wednesdayCeilingActual);

        Integer thursdayCeiling = 40;
        ovbkAccom.setThursdayCeiling(thursdayCeiling);
        Integer thursdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateThursday());
        assertEquals(thursdayCeiling, thursdayCeilingActual);

        Integer fridayCeiling = 50;
        ovbkAccom.setFridayCeiling(fridayCeiling);
        Integer fridayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateFriday());
        assertEquals(fridayCeiling, fridayCeilingActual);

        Integer saturdayCeiling = 60;
        ovbkAccom.setSaturdayCeiling(saturdayCeiling);
        Integer saturdayCeilingActual
                = overrideHelper.getOverbookingLimit(ovbkAccom, getDateSaturday());
        assertEquals(saturdayCeiling, saturdayCeilingActual);
    }

    @Test
    public void testGetOverbookingTypeAccom() throws Exception {
        String overbookingTypeNameAccomType = "Accommodation Type";
        String overbookingTypeNameHouse = "House";
        String overbookingTypeNameNone = "None";
        OverbookingAccom ovbkAccom = new OverbookingAccom();
        Integer sundayOverbookingTypeId = 1;
        ovbkAccom.setSundayOverbookingTypeId(sundayOverbookingTypeId);
        String overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateSunday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer mondayOverbookingTypeId = 2;
        ovbkAccom.setMondayOverbookingTypeId(mondayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateMonday());
        assertEquals(overbookingTypeNameHouse, overbookingTypeActual);

        Integer tuesdayOverbookingTypeId = 1;
        ovbkAccom.setTuesdayOverbookingTypeId(tuesdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateTuesday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer wednesdayOverbookingTypeId = 3;
        ovbkAccom.setWednesdayOverbookingTypeId(wednesdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateWednesday());
        assertEquals(overbookingTypeNameNone, overbookingTypeActual);

        Integer thursdayOverbookingTypeId = 1;
        ovbkAccom.setThursdayOverbookingTypeId(thursdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateThursday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer fridayOverbookingTypeId = 2;
        ovbkAccom.setFridayOverbookingTypeId(fridayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateFriday());
        assertEquals(overbookingTypeNameHouse, overbookingTypeActual);

        Integer satrudayOverbookingTypeId = 3;
        ovbkAccom.setSaturdayOverbookingTypeId(satrudayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(ovbkAccom, getDateSaturday());
        assertEquals(overbookingTypeNameNone, overbookingTypeActual);
    }

    @Test
    public void testGetOverbookingLimitAccomSeason() throws Exception {
        OverbookingAccomSeason accomSeason = new OverbookingAccomSeason();
        Integer sundayCeiling = 10;
        accomSeason.setSundayCeiling(sundayCeiling);
        Integer sundayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateSunday());
        assertEquals(sundayCeiling, sundayCeilingActual);

        Integer mondayCeiling = 20;
        accomSeason.setMondayCeiling(mondayCeiling);
        Integer mondayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateMonday());
        assertEquals(mondayCeiling, mondayCeilingActual);

        Integer tuesdayCeiling = 30;
        accomSeason.setTuesdayCeiling(tuesdayCeiling);
        Integer tuesdayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateTuesday());
        assertEquals(tuesdayCeiling, tuesdayCeilingActual);

        Integer wednesdayCeiling = 30;
        accomSeason.setWednesdayCeiling(wednesdayCeiling);
        Integer wednesdayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateWednesday());
        assertEquals(wednesdayCeiling, wednesdayCeilingActual);

        Integer thursdayCeiling = 40;
        accomSeason.setThursdayCeiling(thursdayCeiling);
        Integer thursdayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateThursday());
        assertEquals(thursdayCeiling, thursdayCeilingActual);

        Integer fridayCeiling = 50;
        accomSeason.setFridayCeiling(fridayCeiling);
        Integer fridayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateFriday());
        assertEquals(fridayCeiling, fridayCeilingActual);

        Integer saturdayCeiling = 60;
        accomSeason.setSaturdayCeiling(saturdayCeiling);
        Integer saturdayCeilingActual
                = overrideHelper.getOverbookingLimit(accomSeason, getDateSaturday());
        assertEquals(saturdayCeiling, saturdayCeilingActual);
    }

    @Test
    public void testGetOverbookingTypeAccomSeason() throws Exception {
        String overbookingTypeNameAccomType = "Accommodation Type";
        String overbookingTypeNameHouse = "House";
        String overbookingTypeNameNone = "None";
        OverbookingAccomSeason accomSeason = new OverbookingAccomSeason();
        Integer sundayOverbookingTypeId = 1;
        accomSeason.setSundayOverbookingTypeId(sundayOverbookingTypeId);
        String overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateSunday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer mondayOverbookingTypeId = 2;
        accomSeason.setMondayOverbookingTypeId(mondayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateMonday());
        assertEquals(overbookingTypeNameHouse, overbookingTypeActual);

        Integer tuesdayOverbookingTypeId = 1;
        accomSeason.setTuesdayOverbookingTypeId(tuesdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateTuesday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer wednesdayOverbookingTypeId = 3;
        accomSeason.setWednesdayOverbookingTypeId(wednesdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateWednesday());
        assertEquals(overbookingTypeNameNone, overbookingTypeActual);

        Integer thursdayOverbookingTypeId = 1;
        accomSeason.setThursdayOverbookingTypeId(thursdayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateThursday());
        assertEquals(overbookingTypeNameAccomType, overbookingTypeActual);

        Integer fridayOverbookingTypeId = 2;
        accomSeason.setFridayOverbookingTypeId(fridayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateFriday());
        assertEquals(overbookingTypeNameHouse, overbookingTypeActual);

        Integer satrudayOverbookingTypeId = 3;
        accomSeason.setSaturdayOverbookingTypeId(satrudayOverbookingTypeId);
        overbookingTypeActual
                = overrideHelper.getOverbookingType(accomSeason, getDateSaturday());
        assertEquals(overbookingTypeNameNone, overbookingTypeActual);
    }


    @Test
    public void getOverbookingDecisionProperty() {
        decisionOvrbkProperty = overrideHelper.getOverbookingDecisionProperty(testOccupancyDate.toDate());
        assertNotNull(decisionOvrbkProperty);
    }

    @Test
    public void shouldDeactivateOverbookingOverrides() {
        Integer accomTypeId = 6, decisionId = 1, propertyId = 5, statusId = 1;
        Date now = new Date();

        DecisionOvrbkAccomOVR accomOverride = createAccomOverrideDecision(accomTypeId, decisionId, propertyId, statusId, now);
        tenantCrudService().save(accomOverride);

        DecisionCOWValueOVR cowOverride = createCOWValueOverrideDecision(accomTypeId, decisionId, propertyId, statusId, now);
        tenantCrudService().save(cowOverride);

        overrideHelper.deactivateOverbookingOverrides(now, Collections.singletonList(accomTypeId));
        assertEquals(Integer.valueOf(2), accomOverride.getStatusId());
        assertEquals(Integer.valueOf(2), cowOverride.getStatusId());
    }

    private DecisionCOWValueOVR createCOWValueOverrideDecision(Integer accomTypeId, Integer decisionId, Integer propertyId, Integer statusId, Date now) {
        DecisionCOWValueOVR cowOverride = new DecisionCOWValueOVR();
        User user = tenantCrudService().find(User.class, 1);
        cowOverride.setDecisionId(decisionId);
        cowOverride.setPropertyId(propertyId);
        cowOverride.setOccupancyDate(now);
        cowOverride.setAccomTypeId(accomTypeId);
        cowOverride.setCostOfWalkValue(new BigDecimal("299.05"));
        cowOverride.setCostOfWalkValueOVR(new BigDecimal("300.00"));
        cowOverride.setStatusId(statusId);
        cowOverride.setCreateDate(now);
        cowOverride.setUser(user);
        return cowOverride;
    }

    private DecisionOvrbkAccomOVR createAccomOverrideDecision(Integer accomTypeId, Integer decisionId, Integer propertyId, Integer statusId, Date now) {
        DecisionOvrbkAccomOVR accomOverride = new DecisionOvrbkAccomOVR();
        OVROverbookingType type = tenantCrudService().find(OVROverbookingType.class, 1);
        accomOverride.setPropertyId(propertyId);
        accomOverride.setDecisionId(decisionId);
        accomOverride.setAccomTypeId(accomTypeId);
        accomOverride.setOverbookingDecision(24);
        accomOverride.setOverbookingOVR(25);
        accomOverride.setOvrOverbookingType(type);
        accomOverride.setStatusId(statusId);
        accomOverride.setOccupancyDate(now);
        return accomOverride;
    }

    /*
     * Get a date that represents Saturday.
     */
    private Date getDateSaturday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-11");
    }

    private Date getDateFriday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-10");
    }

    private Date getDateThursday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-09");
    }

    private Date getDateWednesday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-08");
    }

    private Date getDateTuesday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-07");
    }

    private Date getDateMonday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-06");
    }

    private Date getDateSunday() throws ParseException {
        return DateUtil.getDateFormatYYYYMMDD().parse("2011-06-05");
    }
}