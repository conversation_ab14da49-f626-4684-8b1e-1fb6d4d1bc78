package com.ideas.tetris.pacman.services.fols;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.services.fols.ngi.FolsUtilityService;
import com.ideas.tetris.pacman.services.ngi.dto.FolsCatchupData;
import com.ideas.tetris.pacman.services.ngi.dto.FolsFileNameMetadata;
import com.ideas.tetris.pacman.services.ngi.dto.IncomingFolderData;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class FolsCatchupDataServiceTest {

    @InjectMocks
    FolsCatchupDataService service;

    @InjectMocks
    FolsUtilityService folsUtilityService;

    @Mock
    RestClient restClient;

    private static final String CLIENT_CODE = "SandBox";
    private static final String PROPERTY_CODE = "5654";

    @BeforeEach
    public void doSetup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void shouldReturnEmptyIncomingFolderMetadata() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(new IncomingFolderData()));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        IncomingFolderData metadata = service.getIncomingFolderMetadata(CLIENT_CODE, PROPERTY_CODE);
        assertEmptyData(metadata);
    }

    @Test
    public void shouldReturnEmptyIncomingFolderMetadataWithOutPropertyCode() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(new IncomingFolderData()));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        IncomingFolderData metadata = service.getIncomingFolderMetadata(CLIENT_CODE, null);
        assertEmptyData(metadata);
    }

    private void assertEmptyData(IncomingFolderData metadata) {
        assertNotNull(metadata);
        assertNull(metadata.getMaxDate());
        assertNull(metadata.getMinDate());
        assertTrue(metadata.getExtractFiles().isEmpty());
    }

    @Test
    public void shouldReturnIncomingFolderMetadata() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(buildTestIncomingFolderData(buildExtractFilesData())));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        IncomingFolderData metadata = service.getIncomingFolderMetadata(CLIENT_CODE, PROPERTY_CODE);
        assertNotNull(metadata);
        assertEquals("2017-03-01", metadata.getMinDate());
        assertEquals("2017-09-12", metadata.getMaxDate());
        assertFalse(metadata.getExtractFiles().isEmpty());
        assertEquals(1, metadata.getExtractFiles().size());
        assertNotNull(metadata.getExtractFiles().get(0).getDate());
    }

    @Test
    public void shouldReturnEmptyObjectWithInvalidData() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        List<FolsFileNameMetadata> fileNameMetaData = buildExtractFilesData();
        fileNameMetaData.get(0).setDateTime("invalid");
        restResponse.add(buildTestJSONData(buildTestIncomingFolderData(fileNameMetaData)));

        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        IncomingFolderData metadata = service.getIncomingFolderMetadata(CLIENT_CODE, PROPERTY_CODE);
        assertNull(metadata);
    }

    @Test
    public void shouldReturnEmptyCatchUpData() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(new IncomingFolderData()));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<FolsCatchupData> catchupData = service.getCatchupData(CLIENT_CODE, PROPERTY_CODE);
        assertNotNull(catchupData);
        assertTrue(catchupData.isEmpty());
    }

    @Test
    public void shouldReturnParsedCatchUpData() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(buildTestIncomingFolderData(buildExtractFilesData())));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<FolsCatchupData> catchupData = service.getCatchupData(CLIENT_CODE, PROPERTY_CODE);
        assertNotNull(catchupData);
        assertFalse(catchupData.isEmpty());
        assertEquals(1, catchupData.size());
        assertEquals("FFLEASH565400000170603030825.zip", catchupData.get(0).getExtractFileName());
        assertEquals("2017-06-03", DateUtil.formatDate(catchupData.get(0).getBusinessDate(), DateUtil.DEFAULT_DATE_FORMAT));
    }

    @Test
    public void shouldReturnEmptyCatchUpDataWithinDateRange() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(new IncomingFolderData()));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<FolsCatchupData> catchupData = service.getCatchupData(DateUtil.getDate(1, 5, 2017), DateUtil.getDate(2, 6, 2017), CLIENT_CODE, PROPERTY_CODE);
        assertNotNull(catchupData);
        assertTrue(catchupData.isEmpty());
    }

    @Test
    public void shouldReturnParsedCatchUpDataWithinDateRange() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(buildTestIncomingFolderData(buildExtractFilesDataMultiple())));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<FolsCatchupData> catchupData = service.getCatchupData(DateUtil.parseDate("2017-05-01", DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.parseDate("2017-06-02", DateUtil.DEFAULT_DATE_FORMAT), CLIENT_CODE, PROPERTY_CODE);
        assertNotNull(catchupData);
        assertFalse(catchupData.isEmpty());
        assertEquals(1, catchupData.size());
        assertEquals("2017-05-01", DateUtil.formatDate(catchupData.get(0).getBusinessDate(), DateUtil.DEFAULT_DATE_FORMAT));
        assertEquals("FFLEASH565400000170501021557.zip", catchupData.get(0).getExtractFileName());
    }

    @Test
    public void shouldReturnNullWhenInvalidDataGiven() throws Exception {
        IncomingFolderData folderData = buildTestIncomingFolderData(buildExtractFilesDataMultiple());
        List<FolsCatchupData> catchupData = service.filterExtractCatchUpDataForDateRange(folderData, DateUtil.parseDate("2017-05-01", DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.parseDate("2017-06-02", DateUtil.DEFAULT_DATE_FORMAT));
        assertTrue(catchupData.isEmpty());
    }

    @Test
    public void shouldFilterCatchUpDataWithinDateRange() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(buildTestJSONData(buildTestIncomingFolderData(buildExtractFilesDataMultiple())));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        IncomingFolderData folderData = service.getIncomingFolderMetadata(CLIENT_CODE, PROPERTY_CODE);
        List<FolsCatchupData> catchupData = service.filterExtractCatchUpDataForDateRange(folderData, DateUtil.parseDate("2017-05-01", DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.parseDate("2017-06-02", DateUtil.DEFAULT_DATE_FORMAT));
        assertNotNull(catchupData);
        assertFalse(catchupData.isEmpty());
        assertEquals(1, catchupData.size());
        assertEquals("2017-05-01", DateUtil.formatDate(catchupData.get(0).getBusinessDate(), DateUtil.DEFAULT_DATE_FORMAT));
        assertEquals("FFLEASH565400000170501021557.zip", catchupData.get(0).getExtractFileName());
    }

    @Test
    public void shouldReturnPropertiesUnderGivenClient() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(new JSONObject("{\"propertyCodes\": [\"5654\", \"1445\"]}"));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<String> propertyCodesUnderClient = folsUtilityService.getNGIPropertyCodesUnderClient(CLIENT_CODE);
        assertNotNull(propertyCodesUnderClient);
        assertEquals(2, propertyCodesUnderClient.size());
        assertTrue(propertyCodesUnderClient.contains("5654"));
        assertTrue(propertyCodesUnderClient.contains("1445"));
    }

    @Test
    public void shouldReturnEmptyPropertiesListWithInvalidDate() throws Exception {
        ArrayList<JSONObject> restResponse = new ArrayList<>();
        restResponse.add(new JSONObject("{\"codes\": [\"1445\"]}"));
        Mockito.when(restClient.getDataFromEndpoint(Matchers.any(), Matchers.any())).thenReturn(restResponse);
        List<String> propertyCodesUnderClient = folsUtilityService.getNGIPropertyCodesUnderClient(CLIENT_CODE);
        assertNotNull(propertyCodesUnderClient);
        assertTrue(propertyCodesUnderClient.isEmpty());
    }

    private IncomingFolderData buildTestIncomingFolderData(List<FolsFileNameMetadata> metaDatas) {
        IncomingFolderData folderData = new IncomingFolderData();
        folderData.setMinDate("2017-03-01");
        folderData.setMaxDate("2017-09-12");
        folderData.setExtractFiles(metaDatas);
        return folderData;
    }

    private List<FolsFileNameMetadata> buildExtractFilesData() {
        FolsFileNameMetadata folsFileNameMetadata = new FolsFileNameMetadata();
        folsFileNameMetadata.setFileName("FFLEASH565400000170603030825.zip");
        folsFileNameMetadata.setHotelIdentifier("5654");
        folsFileNameMetadata.setYearMonthInfo("2017_06");
        folsFileNameMetadata.setDateTime("2017-06-03 04:28:33.000");
        return Arrays.asList(folsFileNameMetadata);
    }

    private List<FolsFileNameMetadata> buildExtractFilesDataMultiple() {
        FolsFileNameMetadata firstMetaData = new FolsFileNameMetadata();
        firstMetaData.setFileName("FFLEASH565400000170501021557.zip");
        firstMetaData.setHotelIdentifier("5654");
        firstMetaData.setYearMonthInfo("2017_05");
        firstMetaData.setDateTime("2017-05-01 02:15:57.000");

        FolsFileNameMetadata secondMetaData = new FolsFileNameMetadata();
        secondMetaData.setFileName("FFLEASH565400000170603030825.zip");
        secondMetaData.setHotelIdentifier("5654");
        secondMetaData.setYearMonthInfo("2017_06");
        secondMetaData.setDateTime("2017-06-03 04:28:33.000");

        return Arrays.asList(firstMetaData, secondMetaData);
    }

    private JSONObject buildTestJSONData(IncomingFolderData incomingFolderData) {
        try {
            JSONObject jsonObject = new JSONObject(new ObjectMapper().writeValueAsString(incomingFolderData));
            return jsonObject;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
