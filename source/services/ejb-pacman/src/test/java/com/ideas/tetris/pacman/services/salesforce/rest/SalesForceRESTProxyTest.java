package com.ideas.tetris.pacman.services.salesforce.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.salesforce.*;
import com.ideas.tetris.pacman.services.salesforce.AbstractSalesForceObject.Attributes;
import com.ideas.tetris.pacman.services.salesforce.entity.SfdcEmailContents;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.jboss.resteasy.client.ClientRequest;
import org.jboss.resteasy.client.ClientRequestFactory;
import org.jboss.resteasy.client.ClientResponse;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import javax.ws.rs.core.MultivaluedMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class SalesForceRESTProxyTest extends AbstractG3JupiterTest {

    private static final String ACCOUNT_ID = "id1";
    private static final String ACCOUNT_IDS[] = {"id1", "id2"};
    private static final String URL = "http://www.ideas.com";
    private static final String TOKEN = "authtoken";
    private static final String QUERY_PATH = "/services/data/v42.0/query";
    private static final String PATCH_PARAM = "?_HttpMethod=PATCH";
    private static final String UPDATE_CONTENT_TYPE = "application/json";
    private static final String EXPECTED_ERROR_CODE = "SOME_CODE";
    private static final String EXPECTED_ERROR_MSG = "Invalid query";
    private static final String ACCOUNT_BILLING_DETAILS_QUERY = String.format(AccountBillingDetails.FIND_ALL_ACCOUNT_BILLING_DETAILS);
    private static final String ACCOUNT_OWNER_DETAILS_QUERY = String.format(AccountOwnerDetails.FIND_ALL_ACCOUNT_OWNER_DETAILS);
    private static final String ACCOUNT_QUERY = String.format(Account.FIND_BY_ACCOUNT_NUMBER, ACCOUNT_ID);
    private static final String ACCOUNT_CONTACT_QUERY = String.format(AccountContactRole.FIND_PRIMARY_CONTACT_MULTI_ROLE_BY_ACCOUNT_NUMBER, ACCOUNT_ID);
    private static final String ORDER_OWNER_CONTACT_QUERY = String.format(Order.FIND_ORDER_OWNER_CONTACT_BY_ACCOUNT_NUMBER_FROM_ORDER, ACCOUNT_ID);
    private static final String MULTIPLE_ACCOUNT_QUERY = String.format(Account.FIND_BY_ACCOUNT_NUMBERS, "'id1','id2'");
    private static final String SOLUTION_PROFILE_QUERY = String.format(SolutionProfile.FIND_BY_ACCOUNT, "id1");
    private static final String ACCOUNT_BILLING_DETAILS_QUERY_RESULT = "{\n" +
            "    \"totalSize\": 12001,\n" +
            "    \"done\": true,\n" +
            "    \"nextRecordsUrl\": \"/services/data/v42.0/query/0r8xx3ygwwQXeKcAAL-2000\",\n" +
            "    \"records\": [\n" +
            "        {\n" +
            "            \"attributes\": {\n" +
            "                \"type\": \"Account_Billing_Contact__c\",\n" +
            "                \"url\": \"/services/data/v42.0/sobjects/Account_Billing_Contact__c/a7HPW0000001GPJ2A2\"\n" +
            "            },\n" +
            "            \"Account__r\": {\n" +
            "                \"attributes\": {\n" +
            "                    \"type\": \"Account\",\n" +
            "                    \"url\": \"/services/data/v42.0/sobjects/Account/001PW00000EZT79YAH\"\n" +
            "                },\n" +
            "                \"AccountNumber\": \"0000-0001\",\n" +
            "                \"G3_Client_Code__c\": null,\n" +
            "                \"Client_Property_Code__c\": \"5554\",\n" +
            "                \"Account_Segment_Tier__c\": \"Tier 1\"\n" +
            "            },\n" +
            "            \"Contact__r\": {\n" +
            "                \"attributes\": {\n" +
            "                    \"type\": \"Contact\",\n" +
            "                    \"url\": \"/services/data/v42.0/sobjects/Contact/003PW00000Ct59qYAB\"\n" +
            "                },\n" +
            "                \"FirstName\": \"Celina\",\n" +
            "                \"LastName\": \"Yan\"\n" +
            "            },\n" +
            "            \"Email__c\": null,\n" +
            "            \"Contact_Phone__c\": null,\n" +
            "            \"Billing_Entity_Name__c\": \"Hangzhou Westlake Hotel Company Limited\"\n" +
            "        }\n" +
            "\t]\t\n" +
            "}";
    private static final String ACCOUNT_OWNER_DETAILS_QUERY_RESULT = "{\n" +
            "    \"totalSize\": 3331,\n" +
            "    \"done\": true,\n" +
            "    \"nextRecordsUrl\": \"/services/data/v42.0/query/0r8xx3ygx3zND8RAAW-2000\",\n" +
            "    \"records\": [\n" +
            "        {\n" +
            "            \"attributes\": {\n" +
            "                \"type\": \"AccountContactRelation\",\n" +
            "                \"url\": \"/services/data/v42.0/sobjects/AccountContactRelation/07kPW00000EMxFtYAL\"\n" +
            "            },\n" +
            "            \"Account\": {\n" +
            "                \"attributes\": {\n" +
            "                    \"type\": \"Account\",\n" +
            "                    \"url\": \"/services/data/v42.0/sobjects/Account/0013y00001eleJJAAY\"\n" +
            "                },\n" +
            "                \"AccountNumber\": \"0000-0001\",\n" +
            "                \"G3_Client_Code__c\": null,\n" +
            "                \"Client_Property_Code__c\": null\n" +
            "            },\n" +
            "            \"Contact\": {\n" +
            "                \"attributes\": {\n" +
            "                    \"type\": \"Contact\",\n" +
            "                    \"url\": \"/services/data/v42.0/sobjects/Contact/0033y00002LGl2WAAT\"\n" +
            "                },\n" +
            "                \"Name\": \"Abbas ELShabasy\",\n" +
            "                \"Email\": \"<EMAIL>\",\n" +
            "                \"Phone\": \"+20 ************\"\n" +
            "            }\n" +
            "        }\n" +
            "\t]\n" +
            "}";

    @Mock
    private SalesforceAuthenticator authenticator;
    @Mock
    private ClientRequestFactory clientRequestFactory;

    @Mock
    private SalesforceAuthenticator salesforceAuthenticator;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    private SalesforceEndpoint endpoint;

    @Spy
    @InjectMocks
    private SalesForceRESTProxy proxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        endpoint = new SalesforceEndpoint();
        endpoint.setInstanceUrl(URL);
        endpoint.setAccessToken(TOKEN);
        System.setProperty("fetch.salesforce.email.address.disabled", "true");
    }

    @Test
    public void getAccountBillingDetails() throws Exception {
        ClientRequest request = setupAccountBillingDetailsFind(200, buildAccountBillingDetailsQueryResult(false, 1), false);
        AccountBillingDetails[] account = proxy.getAccountBillingDetails();
        assertEquals("0000-0001", account[0].getAccount().getAccountNumber());
        assertFindRequest(request, ACCOUNT_BILLING_DETAILS_QUERY);
    }
    @Test
    public void getAccountOwnerDetails() throws Exception {
        ClientRequest request = setupAccountOwnerDetailsFind(200, buildAccountOwnerDetailsQueryResult(false, 1), false);
        AccountOwnerDetails[] account = proxy.getAccountOwnerDetails();
        assertEquals("0000-0001", account[0].getAccount().getAccountNumber());
        assertFindRequest(request, ACCOUNT_OWNER_DETAILS_QUERY);
    }

    @Test
    public void testFindAccount() throws Exception {
        ClientRequest request = setupAccountFind(200, buildAccountQueryResult(false, 1), false);
        Account account = proxy.findAccount(ACCOUNT_ID);
        assertEquals("id1", account.getId());
        assertFindRequest(request, ACCOUNT_QUERY);
    }

    @Test
    public void testFindAccounts() throws Exception {
        ClientRequest request = setupAccountFind(200, buildAccountQueryResult(false, 2), false);
        Account[] accounts = proxy.findAccounts(ACCOUNT_IDS);
        assertEquals(2, accounts.length);
        assertEquals("id1", accounts[0].getId());
        assertEquals("id2", accounts[1].getId());
        assertFindRequest(request, MULTIPLE_ACCOUNT_QUERY);
    }

    @Test
    public void testFindAccountNoResults() throws Exception {
        setupAccountFind(200, buildAccountQueryResult(false, 0), false);
        try {
            proxy.findAccount(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains("no Account found"));
        }
    }

    @Test
    public void testFindAccountsNoResults() throws Exception {
        setupAccountFind(200, buildAccountQueryResult(false, 0), false);
        try {
            proxy.findAccounts(ACCOUNT_IDS);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains("no Account found"));
        }
    }

    @Test
    public void testFindAccountMultipleResults() throws Exception {
        setupAccountFind(200, buildAccountQueryResult(false, 2), false);
        try {
            proxy.findAccount(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains("multiple"));
        }
    }

    @Test
    public void testFindAccountErrorStatus() throws Exception {
        setupAccountFind(400, buildAccountQueryResult(true, 0), false);
        try {
            proxy.findAccount(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_CODE));
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_MSG));
        }
    }

    @Test
    public void testFindAccountsErrorStatus() throws Exception {
        setupAccountFind(400, buildAccountQueryResult(true, 0), false);
        try {
            proxy.findAccounts(ACCOUNT_IDS);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_CODE));
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_MSG));
        }
    }

    @Test
    public void testFindAccountExceptionThrown() throws Exception {
        setupAccountFind(400, buildAccountQueryResult(true, 0), true);
        try {
            proxy.findAccount(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
        }
    }

    @Test
    public void testFindAccountsExceptionThrown() throws Exception {
        setupAccountFind(400, buildAccountQueryResult(true, 0), true);
        try {
            proxy.findAccounts(ACCOUNT_IDS);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
        }
    }

    @Test
    public void testUpdateAccount() throws Exception {
        AccountQueryResult accountQueryResult = buildAccountQueryResult(false, 1);
        Account account = accountQueryResult.getAccounts()[0];
        setupAccountFind(200, accountQueryResult, false);
        ClientRequest request = setupAccountUpdate(204, account, buildUpdateResult(false), false);
        proxy.updateAccount(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
        assertUpdateRequest(request);
    }

    @Test
    public void testUpdateAccountErrorStatus() throws Exception {
        AccountQueryResult accountQueryResult = buildAccountQueryResult(false, 1);
        Account account = accountQueryResult.getAccounts()[0];
        setupAccountFind(200, accountQueryResult, false);
        setupAccountUpdate(400, account, buildUpdateResult(true), false);
        try {
            proxy.updateAccount(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_CODE));
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_MSG));
        }
    }

    @Test
    public void testUpdateAccountExceptionThrown() throws Exception {
        AccountQueryResult accountQueryResult = buildAccountQueryResult(false, 1);
        Account account = accountQueryResult.getAccounts()[0];
        setupAccountFind(200, accountQueryResult, false);
        setupAccountUpdate(400, account, buildUpdateResult(true), true);
        try {
            proxy.updateAccount(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
        }
    }

    @Test
    public void testFindSolutionProfile() throws Exception {
        ClientRequest solutionProfileRequest = setupSolutionProfileFind(200, buildSolutionProfileQueryResult(false, 1), false);
        SolutionProfile solutionProfile = proxy.findSolutionProfile(ACCOUNT_ID);
        assertEquals("id1", solutionProfile.getId());
        assertFindRequest(solutionProfileRequest, SOLUTION_PROFILE_QUERY);
    }

    @Test
    public void testFindSolutionProfileNoResults() throws Exception {
        setupSolutionProfileFind(200, buildSolutionProfileQueryResult(false, 0), false);
        try {
            proxy.findSolutionProfile(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains("no G3 Solution Profile found"));
        }
    }

    @Test
    public void testFindSolutionProfileMultipleResults() throws Exception {
        setupSolutionProfileFind(200, buildSolutionProfileQueryResult(false, 2), false);
        try {
            proxy.findSolutionProfile(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains("multiple"));
        }
    }

    @Test
    public void testFindSolutionProfileErrorStatus() throws Exception {
        setupSolutionProfileFind(400, buildSolutionProfileQueryResult(true, 0), false);
        try {
            proxy.findSolutionProfile(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_CODE));
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_MSG));
        }
    }

    @Test
    public void testFindSolutionProfileExceptionThrown() throws Exception {
        setupSolutionProfileFind(200, buildSolutionProfileQueryResult(false, 0), true);
        try {
            proxy.findSolutionProfile(ACCOUNT_ID);
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
        }
    }

    @Test
    public void testUpdateSolutionProfile() throws Exception {
        SolutionProfileQueryResult queryResult = buildSolutionProfileQueryResult(false, 1);
        SolutionProfile solutionProfile = queryResult.getSolutionProfiles()[0];
        setupSolutionProfileFind(200, queryResult, false);
        ClientRequest request = setupUpdate(204, solutionProfile, buildUpdateResult(false), false);
        proxy.updateSolutionProfile(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
        assertUpdateRequest(request);
    }

    @Test
    public void testFindAndUpdateOrder() throws Exception {
        OrderQueryResult queryResult = buildOrderQueryResult(false, 1);
        Order order = queryResult.getOrders()[0];
        setupOrderFind(200, queryResult, false);
        ClientRequest request = setupUpdate(204, order, buildUpdateResult(false), false);
        proxy.findAndUpdateOrder(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
        assertEquals(1, queryResult.getOrders().length);
        assertUpdateRequest(request);
    }

    @Test
    public void testUpdateSolutionProfileErrorStatus() throws Exception {
        SolutionProfileQueryResult queryResult = buildSolutionProfileQueryResult(false, 1);
        SolutionProfile solutionProfile = queryResult.getSolutionProfiles()[0];
        setupSolutionProfileFind(200, queryResult, false);
        setupUpdate(400, solutionProfile, buildUpdateResult(true), false);
        try {
            proxy.updateSolutionProfile(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_CODE));
            assertTrue(e.getMessage().contains(EXPECTED_ERROR_MSG));
        }
    }

    @Test
    public void testUpdateSolutionProfileExceptionThrown() throws Exception {
        SolutionProfileQueryResult queryResult = buildSolutionProfileQueryResult(false, 1);
        SolutionProfile solutionProfile = queryResult.getSolutionProfiles()[0];
        setupSolutionProfileFind(200, queryResult, false);
        setupUpdate(200, solutionProfile, buildUpdateResult(false), true);
        try {
            proxy.updateSolutionProfile(ACCOUNT_ID, buildUpdateMap("some_attribute", "some_value"));
            fail("should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.SALESFORCE_REQUEST_FAILED, e.getErrorCode());
        }
    }

    @Test
    public void getSfdcContacts() throws Exception {
        System.setProperty("fetch.salesforce.email.address.disabled", "false");
        ClientRequest requestAccountContact = setupAccountContactFind(200, buildAccountContactQueryResult(false, 1), false);

        when(pacmanConfigParamsService.getParameterValue(Constants.CONFIG_PARAMS_NODE_PREFIX, PreProductionConfigParamName.ENABLE_PROJECT_HEART_BEAT)).thenReturn(false);

        SfdcEmailContents sfdcEmailContents = proxy.getSfdcContacts(ACCOUNT_ID);

        assertThat(sfdcEmailContents.getToList()).isEqualTo("<EMAIL>; ");
        assertThat(sfdcEmailContents.getCcList()).contains("<EMAIL>; ");
        assertFindRequest(requestAccountContact, ACCOUNT_CONTACT_QUERY);
    }

    @Test
    public void getSfdcContactsFromOrder() throws Exception {
        System.setProperty("fetch.salesforce.email.address.disabled", "false");
        ClientRequest orderContact = setupOrderContactFind(200, buildAccountContactQueryResult(false, 1), buildOrderQueryResult(false, 1), false);

        when(pacmanConfigParamsService.getParameterValue(Constants.CONFIG_PARAMS_NODE_PREFIX, PreProductionConfigParamName.ENABLE_PROJECT_HEART_BEAT)).thenReturn(true);

        SfdcEmailContents sfdcEmailContents = proxy.getSfdcContacts(ACCOUNT_ID);

        assertThat(sfdcEmailContents.getToList()).isEqualTo("<EMAIL>; ");
        assertThat(sfdcEmailContents.getCcList()).contains("<EMAIL>; ");
        assertThat(sfdcEmailContents.getCcList()).contains("<EMAIL>; ");
        assertFindRequestForOrder(orderContact, ACCOUNT_CONTACT_QUERY, 0);
        assertFindRequestForOrder(orderContact, ORDER_OWNER_CONTACT_QUERY, 1);
    }

    @Test
    public void getSFDCLocalContacts() {
        SfdcEmailContents sfdcEmailContents = proxy.getSfdcContacts(ACCOUNT_ID);
        assertThat(sfdcEmailContents.getToList()).isEqualTo(SalesForceRESTProxy.LOCAL_TO_CC);
        assertThat(sfdcEmailContents.getCcList()).isEqualTo(SalesForceRESTProxy.LOCAL_TO_CC);
        assertThat(sfdcEmailContents.getFirstName()).isEqualTo(SalesForceRESTProxy.DUMMY);
        assertThat(sfdcEmailContents.getPropertyName()).isEqualTo(SalesForceRESTProxy.DUMMY);
        assertThat(sfdcEmailContents.getClientCode()).isEqualTo(SalesForceRESTProxy.DUMMY);
    }

    @SuppressWarnings("unchecked")
    private ClientRequest setupAccountFind(int status, AccountQueryResult result, boolean exception) throws Exception {
        when(authenticator.authenticate()).thenReturn(endpoint);
        ClientRequest request = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).get(String.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(request).get(String.class);
            when(response.getStatus()).thenReturn(status);
            String responseBody = "{\"records\":\"values\"}";
            when(response.getEntity()).thenReturn(responseBody);
            ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
            doReturn(mockObjectMapper).when(proxy).createObjectMapper();
            when(mockObjectMapper.readValue(responseBody, AccountQueryResult.class)).thenReturn(result);
        }
        return request;
    }

    @SuppressWarnings("unchecked")
    private ClientRequest setupAccountContactFind(int status, AccountContactQueryResult result, boolean exception) throws Exception {
        proxy.clientRequestFactory = clientRequestFactory;
        when(authenticator.authenticate()).thenReturn(endpoint);
        ClientRequest request = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).get(String.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(request).get(String.class);
            when(response.getStatus()).thenReturn(status);
            String responseBody = "{\"records\":\"values\"}";
            when(response.getEntity()).thenReturn(responseBody);
            ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
            doReturn(mockObjectMapper).when(proxy).createObjectMapper();
            when(mockObjectMapper.readValue(responseBody, AccountContactQueryResult.class)).thenReturn(result);
        }
        return request;
    }

    private ClientRequest setupAccountBillingDetailsFind(int status, AccountBillingDetailsQueryResult result, boolean exception) throws Exception {
        proxy.clientRequestFactory = clientRequestFactory;
        when(authenticator.authenticate()).thenReturn(endpoint);
        ClientRequest request = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).get(AccountBillingDetailsQueryResult.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(request).get(String.class);
            when(response.getStatus()).thenReturn(status);
            String responseBody = ACCOUNT_BILLING_DETAILS_QUERY_RESULT;
            when(response.getEntity()).thenReturn(responseBody);
        }
        return request;
    }
    private ClientRequest setupAccountOwnerDetailsFind(int status, AccountOwnerDetailsQueryResult result, boolean exception) throws Exception {
        proxy.clientRequestFactory = clientRequestFactory;
        when(authenticator.authenticate()).thenReturn(endpoint);
        ClientRequest request = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).get(AccountOwnerDetailsQueryResult.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(request).get(String.class);
            when(response.getStatus()).thenReturn(status);
            String responseBody = ACCOUNT_OWNER_DETAILS_QUERY_RESULT;
            when(response.getEntity()).thenReturn(responseBody);
        }
        return request;
    }

    private ClientRequest setupOrderContactFind(int status, AccountContactQueryResult result1, OrderQueryResult result2, boolean exception) throws Exception {
        proxy.clientRequestFactory = clientRequestFactory;
        when(authenticator.authenticate()).thenReturn(endpoint);
        ClientRequest request = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).get(String.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(request).get(String.class);
            when(response.getStatus()).thenReturn(status);
            String responseBody = "{\"records\":\"values\"}";
            when(response.getEntity()).thenReturn(responseBody);
            ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
            doReturn(mockObjectMapper).when(proxy).createObjectMapper();
            when(mockObjectMapper.readValue(responseBody, AccountContactQueryResult.class)).thenReturn(result1);
            when(mockObjectMapper.readValue(responseBody, OrderQueryResult.class)).thenReturn(result2);
        }
        return request;
    }

    @SuppressWarnings("unchecked")
    private ClientRequest setupSolutionProfileFind(int status, SolutionProfileQueryResult result, boolean exception) throws Exception {
        when(authenticator.authenticate()).thenReturn(endpoint);

        ClientRequest accountRequest = spy(new ClientRequest(URL + QUERY_PATH));
        ClientRequest solutionProfileRequest = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(accountRequest, solutionProfileRequest);

        AccountQueryResult accountQueryResult = buildAccountQueryResult(false, 1);
        ClientResponse<String> accountResponse = mock(ClientResponse.class);
        doReturn(accountResponse).when(accountRequest).get(String.class);
        when(accountResponse.getStatus()).thenReturn(200);
        String responseBody = "{\"records\":\"values\"}";
        when(accountResponse.getEntity()).thenReturn(responseBody);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        doReturn(mockObjectMapper).when(proxy).createObjectMapper();
        when(mockObjectMapper.readValue(responseBody, AccountQueryResult.class)).thenReturn(accountQueryResult);

        if (exception) {
            doThrow(new Exception("something bad happened")).when(solutionProfileRequest).get(SolutionProfileQueryResult.class);
        } else {
            ClientResponse<String> response = mock(ClientResponse.class);
            doReturn(response).when(solutionProfileRequest).get(String.class);
            when(response.getStatus()).thenReturn(status);
            when(response.getEntity()).thenReturn(responseBody);
            when(mockObjectMapper.readValue(responseBody, SolutionProfileQueryResult.class)).thenReturn(result);
        }
        return solutionProfileRequest;
    }

    private ClientRequest setupOrderFind(int status, OrderQueryResult result, boolean exception) throws Exception {
        when(authenticator.authenticate()).thenReturn(endpoint);

        ClientRequest accountRequest = spy(new ClientRequest(URL + QUERY_PATH));
        ClientRequest orderRequest = spy(new ClientRequest(URL + QUERY_PATH));
        when(clientRequestFactory.createRequest(URL + QUERY_PATH)).thenReturn(accountRequest, orderRequest);

        AccountQueryResult accountQueryResult = buildAccountQueryResult(false, 1);
        ClientResponse<String> accountResponse = mock(ClientResponse.class);
        doReturn(accountResponse).when(accountRequest).get(String.class);
        when(accountResponse.getStatus()).thenReturn(200);
        String responseBody = "{\"records\":\"values\"}";
        when(accountResponse.getEntity()).thenReturn(responseBody);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        doReturn(mockObjectMapper).when(proxy).createObjectMapper();
        when(mockObjectMapper.readValue(responseBody, AccountQueryResult.class)).thenReturn(accountQueryResult);

        if (exception) {
            doThrow(new Exception("something bad happened")).when(orderRequest).get(OrderQueryResult.class);
        } else {
            ClientResponse<String > response = mock(ClientResponse.class);
            doReturn(response).when(orderRequest).get(String.class);
            when(response.getStatus()).thenReturn(status);
            when(response.getEntity()).thenReturn(responseBody);
            when(mockObjectMapper.readValue(responseBody, OrderQueryResult.class)).thenReturn(result);
        }
        return orderRequest;
    }

    @SuppressWarnings("unchecked")
    private ClientRequest setupAccountUpdate(int status, Account account, SalesForceUpdateResult result, boolean exception) throws Exception {
        String url = URL + account.getAttributes().getUrl() + PATCH_PARAM;
        ClientRequest request = spy(new ClientRequest(url));
        when(clientRequestFactory.createRequest(url)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).post(List.class);
        } else {
            ClientResponse<List> response = mock(ClientResponse.class);
            doReturn(response).when(request).post(List.class);
            when(response.getStatus()).thenReturn(status);
            when(response.getEntity()).thenReturn(List.of(result));
        }
        return request;
    }

    @SuppressWarnings("unchecked")
    private ClientRequest setupUpdate(int status, AbstractSalesForceObject sObject, SalesForceUpdateResult result, boolean exception) throws Exception {
        String url = URL + sObject.getAttributes().getUrl() + PATCH_PARAM;
        ClientRequest request = spy(new ClientRequest(url));
        when(clientRequestFactory.createRequest(url)).thenReturn(request);
        if (exception) {
            doThrow(new Exception("something bad happened")).when(request).post(List.class);
        } else {
            ClientResponse<List> response = mock(ClientResponse.class);
            doReturn(response).when(request).post(List.class);
            when(response.getStatus()).thenReturn(status);
            when(response.getEntity()).thenReturn(List.of(result));
        }
        return request;
    }

    private Account buildAccount(int i) {
        Account account = new Account();
        account.setId("id" + i);
        Attributes attributes = new Attributes();
        attributes.setType("Account");
        attributes.setUrl("/some/path/Account/" + account.getId());
        account.setAttributes(attributes);
        return account;
    }
    private AccountBillingDetails buildAccountBillingDetails(int i) {
        AccountBillingDetails accountBillingDetails = new AccountBillingDetails();
        AccountBillingDetails.BillingAccount account = new AccountBillingDetails.BillingAccount();
        accountBillingDetails.setAccount(account);
        accountBillingDetails.getAccount().setAccountNumber("0000-000" + i);
        Attributes attributes = new Attributes();
        attributes.setType("Account");
        attributes.setUrl("/some/path/Account/" + accountBillingDetails.getId());
        accountBillingDetails.setAttributes(attributes);
        return accountBillingDetails;
    }
    private AccountOwnerDetails buildAccountOwnerDetails(int i) {
        AccountOwnerDetails accountOwnerDetails = new AccountOwnerDetails();
        AccountOwnerDetails.OwnerAccount account = new AccountOwnerDetails.OwnerAccount();
        accountOwnerDetails.setAccount(account);
        accountOwnerDetails.getAccount().setAccountNumber("0000-000" + i);
        Attributes attributes = new Attributes();
        attributes.setType("Account");
        attributes.setUrl("/some/path/Account/" + accountOwnerDetails.getId());
        accountOwnerDetails.setAttributes(attributes);
        return accountOwnerDetails;
    }

    private Order buildOrder(int i) {
        Order order = new Order();
        Order.PropertyManager projectManager = new Order.PropertyManager();
        projectManager.setEmail("<EMAIL>");
        order.setOrderOwner(projectManager);
        order.setId("id" + i);
        Attributes attributes = new Attributes();
        attributes.setType("Order");
        attributes.setUrl("/some/path/Order/" + order.getId());
        order.setAttributes(attributes);
        return order;
    }

    private AccountContactRole buildAccountContactRole(int i) {
        AccountContactRole accountContactRole = new AccountContactRole();
        AccountContactRole.Contact contact = new AccountContactRole.Contact();
        contact.setEmail("<EMAIL>");
        accountContactRole.setContact(contact);
        accountContactRole.setId("id" + i);
        accountContactRole.setContactRole("IT Contact;Primary User");
        Attributes attributes = new Attributes();
        attributes.setType("AccountContactRole");
        attributes.setUrl("/some/path/AccountContactRole/" + accountContactRole.getId());
        accountContactRole.setAttributes(attributes);
        return accountContactRole;
    }

    private AccountBillingDetailsQueryResult buildAccountBillingDetailsQueryResult(boolean error, int totalSize) {
        AccountBillingDetailsQueryResult queryResult = new AccountBillingDetailsQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            AccountBillingDetails[] accounts = new AccountBillingDetails[totalSize];
            for (int i = 0; i < totalSize; i++) {
                accounts[i] = buildAccountBillingDetails(i + 1);
            }
            queryResult.setAccountBillingDetails(accounts);
        }
        return queryResult;
    }
    private AccountOwnerDetailsQueryResult buildAccountOwnerDetailsQueryResult(boolean error, int totalSize) {
        AccountOwnerDetailsQueryResult queryResult = new AccountOwnerDetailsQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            AccountOwnerDetails[] accounts = new AccountOwnerDetails[totalSize];
            for (int i = 0; i < totalSize; i++) {
                accounts[i] = buildAccountOwnerDetails(i + 1);
            }
            queryResult.setAccountOwnerDetails(accounts);
        }
        return queryResult;
    }
    private AccountQueryResult buildAccountQueryResult(boolean error, int totalSize) {
        AccountQueryResult queryResult = new AccountQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            Account[] accounts = new Account[totalSize];
            for (int i = 0; i < totalSize; i++) {
                accounts[i] = buildAccount(i + 1);
            }
            queryResult.setAccounts(accounts);
        }
        return queryResult;
    }

    private AccountContactQueryResult buildAccountContactQueryResult(boolean error, int totalSize) {
        AccountContactQueryResult queryResult = new AccountContactQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            AccountContactRole[] accountContactRoles = new AccountContactRole[totalSize];
            for (int i = 0; i < totalSize; i++) {
                accountContactRoles[i] = buildAccountContactRole(i + 1);
            }
            queryResult.setAccountContactRoles(accountContactRoles);
        }
        return queryResult;
    }


    private OrderQueryResult buildOrderQueryResult(boolean error, int totalSize) {
        OrderQueryResult queryResult = new OrderQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            Order[] orders = new Order[totalSize];
            for (int i = 0; i < totalSize; i++) {
                orders[i] = buildOrder(i + 1);
            }
            queryResult.setOrders(orders);
        }
        return queryResult;
    }

    private SolutionProfileQueryResult buildSolutionProfileQueryResult(boolean error, int totalSize) {
        SolutionProfileQueryResult queryResult = new SolutionProfileQueryResult();
        if (error) {
            queryResult.setErrorCode(EXPECTED_ERROR_CODE);
            queryResult.setMessage(EXPECTED_ERROR_MSG);
        } else {
            queryResult.setTotalSize(totalSize);
            SolutionProfile[] solutionProfiles = new SolutionProfile[totalSize];
            for (int i = 0; i < totalSize; i++) {
                solutionProfiles[i] = buildSolutionProfile(i + 1);
            }
            queryResult.setSolutionProfiles(solutionProfiles);
        }
        return queryResult;
    }

    private SolutionProfile buildSolutionProfile(int i) {
        SolutionProfile solutionProfile = new SolutionProfile();
        solutionProfile.setId("id" + i);
        Attributes attributes = new Attributes();
        attributes.setType("SPType");
        attributes.setUrl("/some/path/SPType/" + solutionProfile.getId());
        solutionProfile.setAttributes(attributes);
        return solutionProfile;
    }

    private SalesForceUpdateResult buildUpdateResult(boolean error) {
        SalesForceUpdateResult updateResult = new SalesForceUpdateResult();
        if (error) {
            updateResult.setErrorCode(EXPECTED_ERROR_CODE);
            updateResult.setMessage(EXPECTED_ERROR_MSG);
        }
        return updateResult;
    }

    private Map<String, String> buildUpdateMap(String attribute, String value) {
        Map<String, String> updates = new HashMap<>();
        updates.put(attribute, value);
        return updates;
    }

    private void assertFindRequest(ClientRequest request, String expectedQuery) {
        assertHeaders(request);
        MultivaluedMap<String, String> queryParameters = request.getQueryParameters();
        assertEquals(expectedQuery, queryParameters.getFirst("q"));
    }

    private void assertFindRequestForOrder(ClientRequest request, String expectedQuery, int i) {
        assertHeaders(request);
        MultivaluedMap<String, String> queryParameters = request.getQueryParameters();
        assertEquals(expectedQuery, queryParameters.get("q").get(i));
    }

    private void assertUpdateRequest(ClientRequest request) {
        assertHeaders(request);
        assertEquals(UPDATE_CONTENT_TYPE, request.getBodyContentType().toString());
        JSONObject body = (JSONObject) request.getBody();
        assertEquals("some_value", body.get("some_attribute"));
    }

    private void assertHeaders(ClientRequest request) {
        MultivaluedMap<String, String> headers = request.getHeaders();
        assertEquals("OAuth " + TOKEN, headers.getFirst("Authorization"));
    }

}
