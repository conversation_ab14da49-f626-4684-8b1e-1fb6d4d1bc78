package com.ideas.tetris.pacman.services.propertygroup.service;

import com.ideas.tetris.pacman.services.customattributeservice.entity.CustomAttributeSearchCriteria;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchJoinCondition;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchOperator;

public class SearchCriteriaBuilder {
    public static CustomAttributeSearchCriteria buildSearchCriteria(
            Integer clientAttributeValueId, SearchOperator searchOperator, SearchJoinCondition joinCondition) {

        CustomAttributeSearchCriteria searchCriteria = new CustomAttributeSearchCriteria();

        searchCriteria.setClientAttributeValueId(clientAttributeValueId);
        searchCriteria.setOperator(searchOperator);
        searchCriteria.setJoinCondition(joinCondition);

        return searchCriteria;
    }

    public static CustomAttributeSearchCriteria buildSearchCriteria(
            Integer clientAttributeValueId, SearchOperator searchOperator) {
        return buildSearchCriteria(clientAttributeValueId, searchOperator, null);
    }
}