package com.ideas.tetris.pacman.services.specialevent.service;

import com.google.gson.Gson;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.repeatpattern.v1.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventCategoryDTO;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventUploadDTO;
import com.ideas.tetris.pacman.services.specialevent.entity.*;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.util.CustomizedDisplayName;
import com.ideas.tetris.pacman.util.LocalDateRange;
import com.ideas.tetris.pacman.util.jaxb.repeatpattern.RepeatPatternJAXBUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.mockito.Spy;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService.MAX_DEFAULT_FREQUENCY_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class SpecialEventServiceTest extends AbstractG3JupiterTest {

    private RepeatPatternJAXBUtil repeatPatternJaxbUtil;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    private SpecialEventService service;
    private SyncEventAggregatorService syncEventAggregatorMock = mock(SyncEventAggregatorService.class);
    private SyncDisplayNameService syncDisplayNameServiceMock = mock(SyncDisplayNameService.class);
    private CrudService tenantCrudService = mock(CrudService.class);
    private DateService dateService = mock(DateService.class);
    private Date caughtUpDate = new Date();
    private PacmanConfigParamsService pacmanConfigParamsService = mock(PacmanConfigParamsService.class);
    private RegulatorService regulatorService = mock(RegulatorService.class);
    private Date forecastWindowEndDate = DateUtil.addDaysToDate(caughtUpDate, 364);
    private static final Integer PROPERTY_ID_PUNE = 5;
    private static final String CLIENT = "BSTN";
    @Spy
    SpecialEventService specialEventServiceSpy = new SpecialEventService();

    @SuppressWarnings("static-access")
    @BeforeEach
    public void setUp() {
        service = new SpecialEventService();
        service.setTenantCrudService(this.tenantCrudService());
        repeatPatternJaxbUtil = new RepeatPatternJAXBUtil();
        repeatPatternJaxbUtil.init();
        service.setJaxbUtil(repeatPatternJaxbUtil);
        service.setSyncEventAggregatorService(syncEventAggregatorMock);
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);
        service.setDateService(dateService);
        inject(service, "pacmanConfigParamsService", pacmanConfigParamsService);
        specialEventServiceSpy.setTenantCrudService(this.tenantCrudService());
        inject(service, "syncDisplayNameService", syncDisplayNameServiceMock);
        lenient().when(regulatorService.isSpringTXEnableRegulatorService()).thenReturn(false);
    }

    @Test
    public void testGetAllSpecialEventTypes() {
        List<SpecialEventType> specialEvents = service.getAllSpecialEventTypes();
        assertNotNull(specialEvents);
        assertEquals(6, specialEvents.size());
        SpecialEventType first = specialEvents.iterator().next();
        assertEquals("Convention", first.getName());
        assertEquals("City Wide Convention (Transient Traffic)", first.getDescription());
    }

    @Test
    public void testCreateSpecialEventTypes() {
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("Some Crazy Category");
        specialEventType.setColorCode("444");
        specialEventType.setDescription("A crazy description of a crazy category");
        specialEventType.setStatusId(Constants.ACTIVE_STATUS_ID);
        service.saveSpecialEventType(specialEventType);

        tenantCrudService().flushAndClear();

        List<SpecialEventType> specialEvents = service.getAllSpecialEventTypes();
        assertNotNull(specialEvents);
        assertEquals(7, specialEvents.size());
    }

    @Test
    public void shouldNotMakeTheSyncFlagDirtyWhenSpecialEventIsInformationOnly() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, buildSpecialEventType(null, "Foo Festival"), start, end, 0,
                0, RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE,
                RepeatPatternEndEnum.OCCURENCES, 1);

        for (PropertySpecialEvent propertySpecialEvent : propertySpecialEvents) {
            propertySpecialEvent.setImpactOnForcast(Constants.SPECIAL_EVENT_INFORMATION_ONLY);
        }
        ;

        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.save(any(SpecialEventType.class))).thenReturn(any(SpecialEventType.class));
        service.saveSpecialEvents(propertySpecialEvents);

        verify(syncEventAggregatorMock, never()).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, never()).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void shouldMakeTheSyncFlagDirtyWhenSpecialEventIsNotInformationOnly() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, buildSpecialEventType(null, "Foo Festival"), start,
                end, 0, 0, RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE,
                RepeatPatternEndEnum.OCCURENCES, 1);
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.save(any(SpecialEventType.class))).thenReturn(any(SpecialEventType.class));
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        service.saveSpecialEvents(propertySpecialEvents);

        verify(syncEventAggregatorMock).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    public void shouldMakeDirtyFlagTrueWhenUpdateTheExistingEventTypeToNonInformationOnly() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";

        final SpecialEventType specialEventType = buildSpecialEventType(1, "Foo Festival");
        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                1);
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.find(SpecialEventType.class, specialEventType.getId())).thenReturn(specialEventType);
        when(tenantCrudService.save(propertySpecialEvents.get(0))).thenReturn(propertySpecialEvents.get(0));
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        service.saveSpecialEvents(propertySpecialEvents);

        verify(syncEventAggregatorMock).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    private SpecialEventType buildSpecialEventType(Integer id, String name) {
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setId(id);
        specialEventType.setName(name);
        return specialEventType;
    }

    @Test
    public void testCreatePropertySpecialEventInstanceNotes() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                1);

        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        assertNotNull(saved.iterator().next().getId());

        PropertySpecialEvent event = saved.get(0);
        PropertySpecialEventInstance instance = event.getPropertySpecialEventIntances().iterator().next();
        assertNull(instance.getPropertySpecialEventInstanceNotes());
        PropertySpecialEventInstanceNotes note = new PropertySpecialEventInstanceNotes();
        note.setPropertySpecialEventNotes("This is a note value");
        instance.setPropertySpecialEventInstanceNotes(note);
        instance.setIsEdited(1);

        service.saveSpecialEvents(saved);

        tenantCrudService().flushAndClear();

        List<PropertySpecialEvent> foundEvents = service.getAllSpecialEventsByDateRange(date(start), date(end))
                .values().iterator().next();
        assertEquals(1, foundEvents.size());
        PropertySpecialEvent foundEvent = foundEvents.iterator().next();
        assertEquals(1, foundEvent.getPropertySpecialEventIntances().size());
        PropertySpecialEventInstance foundInstance = foundEvent.getPropertySpecialEventIntances().iterator().next();
        assertNotNull(foundInstance.getPropertySpecialEventInstanceNotes());
        PropertySpecialEventInstanceNotes foundNote = foundEvent.getPropertySpecialEventIntances().iterator().next()
                .getPropertySpecialEventInstanceNotes();
        assertEquals("This is a note value", foundNote.getPropertySpecialEventNotes());
    }

    @Test
    public void testReadPropertySpecialEventInstanceNotes() throws Exception {
        Integer noteKey = new Integer(1);
        PropertySpecialEventInstanceNotes actualNote = service.getPropertySpecialEventInstanceNotes(noteKey);
        assertNotNull(actualNote);
        assertEquals("This is sample note - 1", actualNote.getPropertySpecialEventNotes());
        LocalDateTime created = LocalDateUtils.toJavaLocalDateTime(sdf.parse("2010-05-10 09:13:00.0"));
        Date ended = sdf.parse("2012-12-27 00:00:00.0");
        assertEquals(created, actualNote.getCreateDate());
        assertEquals(noteKey, actualNote.getId());
        assertEquals(created, actualNote.getPropertySpecialEventInstance().getCreateDate());
        assertEquals(ended, actualNote.getPropertySpecialEventInstance().getEndDate());
    }

    @Test
    public void testUpdatePropertySpecialEventInstanceNotes() throws Exception {
        Integer noteKey = new Integer(1);
        PropertySpecialEventInstanceNotes actualNote = service.getPropertySpecialEventInstanceNotes(noteKey);
        assertNotNull(actualNote);
        assertEquals("This is sample note - 1", actualNote.getPropertySpecialEventNotes());
        actualNote.setPropertySpecialEventNotes("This is a changed note");
        PropertySpecialEventInstance instance = actualNote.getPropertySpecialEventInstance();
        assertNotNull(instance);
        PropertySpecialEvent event = instance.getPropertySpecialEvent();
        assertNotNull(event);
        List<PropertySpecialEvent> events = new ArrayList<PropertySpecialEvent>();
        events.add(event);
        service.saveSpecialEvents(events);

        tenantCrudService().flushAndClear();

        PropertySpecialEventInstanceNotes updatedNote = service.getPropertySpecialEventInstanceNotes(noteKey);
        assertEquals("This is a changed note", updatedNote.getPropertySpecialEventNotes());
    }

    @Test
    public void testGetSpecialEventsSummary() {
        List<PropertySpecialEvent> propertySpecialEvents = new ArrayList<PropertySpecialEvent>();
        PropertySpecialEvent e = new PropertySpecialEvent();
        Set<PropertySpecialEventInstance> propertySpecialEventIntances = new HashSet<PropertySpecialEventInstance>();
        PropertySpecialEventInstance pseInstance = new PropertySpecialEventInstance();
        propertySpecialEventIntances.add(pseInstance);
        e.setPropertySpecialEventIntances(propertySpecialEventIntances);
        e.setId(1);
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setId(1);
        e.setSpecialEventType(specialEventType);
        e.setName("Foo Festival");
        propertySpecialEvents.add(e);
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setSnapshotTm(new Date());

        // service call
        List<SpecialEventCategoryDTO> actual = service.getSpecialEventsSummary();

        assertNotNull(actual);
        assertEquals(2, actual.size());
        SpecialEventCategoryDTO actualDTO = actual.get(1);
        assertEquals(2, actualDTO.getSpecialEventTypeId());
        assertEquals("St Patricks Day-Info Only", actualDTO.getSpecialEvents().iterator().next().getSpecialEventName());

        verify(dateService).getCaughtUpDate();
    }

    @Test
    public void testPropertySpecialEventsDeleteInstances() throws Exception {
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(
                date("2019-04-10 00:00:00.0"), date("2019-06-15 00:00:00.0"));
        assertEquals(0, stored.size()); // none in the database match this query

        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, "2019-05-10 00:00:00.0",
                "2019-05-11 00:00:00.0", 0, 0, RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE,
                RepeatPatternEndEnum.END_BY, 2020);

        // What we're testing...
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());
        assertNotNull(saved.iterator().next().getPropertySpecialEventIntances());
        assertEquals(2, saved.iterator().next().getPropertySpecialEventIntances().size());

        // Also what we're testing...
        stored = service.getAllSpecialEventsByDateRange(date("2019-03-10 00:00:00.0"), date("2020-05-15 00:00:00.0"));

        assertNotNull(stored);
        assertEquals(2, stored.size());
        List<PropertySpecialEvent> storedPropertySpecialEvents = stored.values().iterator().next();
        PropertySpecialEvent event = storedPropertySpecialEvents.iterator().next();
        assertEquals(saved.iterator().next().getId(), event.getId());
        assertEquals(2, event.getPropertySpecialEventIntances().size());
        event.getPropertySpecialEventIntances().iterator().next().setIsDeleted(1);

        service.saveSpecialEvents(storedPropertySpecialEvents);

        tenantCrudService().flushAndClear();

        stored = service.getAllSpecialEventsByDateRange(date("2019-03-10 00:00:00.0"), date("2020-05-15 00:00:00.0"));
        storedPropertySpecialEvents = stored.values().iterator().next();
        event = storedPropertySpecialEvents.iterator().next();
        assertEquals(saved.iterator().next().getId(), event.getId());
        assertEquals(1, event.getPropertySpecialEventIntances().size());
    }

    @Test
    public void testReadPropertySpecialEvents() throws Exception {
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(
                date("2019-04-04 00:00:00.0"), date("2019-04-11 00:00:00.0"));
        assertEquals(0, stored.size()); // none in the database match this query

        List<PropertySpecialEvent> saved = createSpecialEventsForProperty();

        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());
        assertNotNull(saved.iterator().next().getPropertySpecialEventIntances());
        assertEquals(1, saved.iterator().next().getPropertySpecialEventIntances().size());

        // Also what we're testing...
        stored = service.getAllSpecialEventsByDateRange(date("2019-04-04 00:00:00.0"), date("2019-04-11 00:00:00.0"));

        assertNotNull(stored);
        assertEquals(1, stored.size());
        assertTrue(stored.containsKey("2019-04-01"));
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        assertEquals(2, storedEvents.size());
        assertEquals(date("2019-04-10 00:00:00.0").getMonth(), storedEvents.get(0).getPropertySpecialEventIntances()
                .iterator().next().getInstanceStartDate().getMonth());
    }

    private List<PropertySpecialEvent> createSpecialEventsForProperty() throws Exception {
        List<SpecialEventType> types = service.getAllSpecialEventTypes();
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, types.get(0), "2019-04-10 00:00:00.0",
                "2019-04-10 00:00:00.0", 0, 0, RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE,
                RepeatPatternEndEnum.END_BY, 2019);
        PropertySpecialEvent propertySpecialEvent2 = createPropertySpecialEvent("Bar Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, types.get(1), "2019-04-05 00:00:00.0",
                "2019-04-05 00:00:00.0", 0, 0, RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE,
                RepeatPatternEndEnum.END_BY, 2019);
        List<PropertySpecialEvent> propertySpecialEvents = new ArrayList<PropertySpecialEvent>();
        propertySpecialEvents.add(propertySpecialEvent);
        propertySpecialEvents.add(propertySpecialEvent2);

        // What we're testing...
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flush();
        return saved;
    }

    @Test
    public void testReadPropertySpecialEventInstancesInSortedOrder() throws Exception {
        createSpecialEventsForProperty();
        List<PropertySpecialEventInstance> specialEventInstances = service.getSpecialEventInstancesForADateRange(date("2019-04-04 00:00:00.0").getTime(), date("2019-04-11 00:00:00.0").getTime(), false, "", "");
        assertNotNull(specialEventInstances);
        assertTrue(specialEventInstances.size() >= 2);
        assertSortedOrder(specialEventInstances);
    }

    private void assertSortedOrder(List<PropertySpecialEventInstance> specialEventInstances) {
        boolean allEventsSortedAscendingOnStartDate = IntStream.range(0, specialEventInstances.size() - 1)
                .allMatch(i -> specialEventInstances.get(i).getStartDate().before(specialEventInstances.get(i + 1).getStartDate()));
        assertTrue(allEventsSortedAscendingOnStartDate);
    }

    @Test
    public void testReadPropertySpecialEventsHandlePreAndPostDays1() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(0, stored.size()); // none in the database match this query

        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);
        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 100, 100,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        // What we're testing...
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flush();

        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());
        assertNotNull(saved.iterator().next().getPropertySpecialEventIntances());
        assertEquals(1, saved.iterator().next().getPropertySpecialEventIntances().size());

        // Also what we're testing...
        stored = service.getAllSpecialEventsByDateRange(date("2019-02-10 00:00:00.0"), date("2019-03-10 00:00:00.0"));

        assertNotNull(stored);
        assertEquals(2, stored.size());

        assertTrue(stored.containsKey("2019-02-01"));
        assertTrue(stored.containsKey("2019-03-01"));

        assertEquals(saved.iterator().next().getId(), stored.values().iterator().next().iterator().next().getId());

    }

    @Test
    public void testReadPropertySpecialEventsHandlePreAndPostDays2() throws Exception {
        String start = "2019-03-31 00:00:00.0";
        String end = "2019-04-05 00:00:00.0";
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(0, stored.size()); // none in the database match this query

        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);
        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 1, 1,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2024);
        Set<PropertySpecialEventInstance> propertySpecialEventInstances = propertySpecialEvents.iterator().next()
                .getPropertySpecialEventIntances();
        assertEquals(6, propertySpecialEventInstances.size());

        // What we're testing...
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flush();

        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());
        assertNotNull(saved.iterator().next().getPropertySpecialEventIntances());
        assertEquals(6, saved.iterator().next().getPropertySpecialEventIntances().size());

        // Also what we're testing...
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));

        assertNotNull(stored);
        assertEquals(2, stored.size());
        assertTrue(stored.containsKey("2019-03-01"));
        assertTrue(stored.containsKey("2019-04-01"));
        assertEquals(stored.get(2), stored.get(3));
        assertEquals(saved.iterator().next().getId(), stored.values().iterator().next().iterator().next().getId());
    }

    @Test
    public void testSavePropertySpecialEvents() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2024);

        // First time save
        List<PropertySpecialEvent> solution = service.saveSpecialEvents(propertySpecialEvents);

        assertNotNull(solution);
        assertTrue(0 < solution.iterator().next().getId());
        assertNotNull(solution.iterator().next().getPropertySpecialEventIntances());
        assertTrue(0 < solution.iterator().next().getPropertySpecialEventIntances().size());
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertNotNull(stored);
        assertEquals(1, stored.size());
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        assertEquals(solution.iterator().next().getId(), storedEvents.iterator().next().getId());
        assertEquals(6, storedEvents.iterator().next().getPropertySpecialEventIntances().size());

        // Save duplicates instances bug
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        tenantCrudService().flushAndClear();
        service.saveSpecialEvents(storedEvents);
        tenantCrudService().flushAndClear();
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());
        assertTrue(stored.containsKey("2019-04-01"));

        // check for forecasting flag...
        storedEvents = stored.values().iterator().next();
        PropertySpecialEvent storedEvent = storedEvents.iterator().next();
        assertEquals(new Integer(1), storedEvent.getImpactOnForcast());
        Set<PropertySpecialEventInstance> storedSpecialEventIntances = storedEvent.getPropertySpecialEventIntances();
        assertEquals(6, storedSpecialEventIntances.size());
        assertEquals(new Integer(0), storedSpecialEventIntances.iterator().next().getEnableForecast());

        // edit set for forecasting
        for (PropertySpecialEvent event : storedEvents) {
            event.setImpactOnForcast(3);
        }
        tenantCrudService().flush();
        service.saveSpecialEvents(storedEvents);
        PropertySpecialEvent pse = tenantCrudService().find(PropertySpecialEvent.class, storedEvent.getId());
        assertEquals(new Integer(3), pse.getImpactOnForcast());
    }

    @Test
    public void testSavePropertySpecialEventsNewCategory() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("New Category");
        specialEventType.setColorCode("555");
        specialEventType.setDescription("Category Description");
        specialEventType.setStatusId(Constants.ACTIVE_STATUS_ID);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2024);

        // First time save
        List<PropertySpecialEvent> solution = service.saveSpecialEvents(propertySpecialEvents);

        assertNotNull(solution);
        assertTrue(0 < solution.iterator().next().getId());
        assertNotNull(solution.iterator().next().getPropertySpecialEventIntances());
        assertTrue(0 < solution.iterator().next().getPropertySpecialEventIntances().size());
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertNotNull(stored);
        assertEquals(1, stored.size());
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        assertEquals(solution.iterator().next().getId(), storedEvents.iterator().next().getId());
        assertEquals(6, storedEvents.iterator().next().getPropertySpecialEventIntances().size());

        // Save duplicates instances bug
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        tenantCrudService().flushAndClear();
        service.saveSpecialEvents(storedEvents);
        tenantCrudService().flushAndClear();
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());
        assertTrue(stored.containsKey("2019-04-01"));

        // check for forecasting flag...
        storedEvents = stored.values().iterator().next();
        PropertySpecialEvent storedEvent = storedEvents.iterator().next();
        assertEquals(new Integer(1), storedEvent.getImpactOnForcast());
        Set<PropertySpecialEventInstance> storedSpecialEventIntances = storedEvent.getPropertySpecialEventIntances();
        assertEquals(6, storedSpecialEventIntances.size());
        assertEquals(new Integer(0), storedSpecialEventIntances.iterator().next().getEnableForecast());

        // edit set for forecasting
        for (PropertySpecialEvent event : storedEvents) {
            event.setImpactOnForcast(3);
        }
        tenantCrudService().flush();
        service.saveSpecialEvents(storedEvents);
        PropertySpecialEvent pse = tenantCrudService().find(PropertySpecialEvent.class, storedEvent.getId());
        assertEquals(new Integer(3), pse.getImpactOnForcast());
    }

    @Test
    public void testSavePropertySpecialEventsNoRepeat() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        // get rid of the frequency and set repeatable to null... TODO make it
        // so this can work with
        // the createPropertySpecialEvents method
        assertEquals(1, propertySpecialEvents.size());
        PropertySpecialEvent propertySpecialEvent = propertySpecialEvents.get(0);
        propertySpecialEvent.setFrequency(null);
        propertySpecialEvent.setRepeatable(null);
        assertEquals(1, propertySpecialEvent.getPropertySpecialEventIntances().size());
        PropertySpecialEventInstance theInstance = propertySpecialEvent.getPropertySpecialEventIntances().iterator()
                .next();
        theInstance.setFrequency(null);
        theInstance.setRepeatable(null);

        // First time save
        List<PropertySpecialEvent> solution = service.saveSpecialEvents(propertySpecialEvents);

        assertNotNull(solution);
        assertTrue(0 < solution.iterator().next().getId());
        assertNotNull(solution.iterator().next().getPropertySpecialEventIntances());
        assertTrue(0 < solution.iterator().next().getPropertySpecialEventIntances().size());
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertNotNull(stored);
        assertEquals(1, stored.size());
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        assertEquals(solution.iterator().next().getId(), storedEvents.iterator().next().getId());
        assertEquals(1, storedEvents.iterator().next().getPropertySpecialEventIntances().size());

        // Save duplicates instances bug
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        tenantCrudService().flushAndClear();
        service.saveSpecialEvents(storedEvents);
        tenantCrudService().flushAndClear();
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());
        assertTrue(stored.containsKey("2019-04-01"));

        // check for forecasting flag...
        storedEvents = stored.values().iterator().next();
        PropertySpecialEvent storedEvent = storedEvents.iterator().next();
        assertEquals(new Integer(1), storedEvent.getImpactOnForcast());
        Set<PropertySpecialEventInstance> storedSpecialEventIntances = storedEvent.getPropertySpecialEventIntances();
        assertEquals(1, storedSpecialEventIntances.size());
        assertEquals(new Integer(0), storedSpecialEventIntances.iterator().next().getEnableForecast());

        // edit set for forecasting
        for (PropertySpecialEvent event : storedEvents) {
            event.setImpactOnForcast(3);
        }
        tenantCrudService().flush();
        service.saveSpecialEvents(storedEvents);
        PropertySpecialEvent pse = tenantCrudService().find(PropertySpecialEvent.class, storedEvent.getId());
        assertEquals(new Integer(3), pse.getImpactOnForcast());
    }

    @Test
    public void testSaveExistingPastPropertySpecialEventsWithEventNameChange() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2024);
        propertySpecialEvents.get(0).setName("Changed Name");

        service.saveSpecialEvents(propertySpecialEvents);

        verify(syncEventAggregatorMock).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void testUpdatePropertySpecialEventNameDescription() throws Exception {
        String start = "2009-03-01 00:00:00.0";
        String end = "2009-03-31 00:00:00.0";

        // read
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));

        tenantCrudService().flush();

        assertNotNull(stored);
        assertEquals(1, stored.size());
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        PropertySpecialEvent storedPropertySpecialEvent = storedEvents.iterator().next();
        storedPropertySpecialEvent.setName("Quux Festival");
        storedPropertySpecialEvent.setDescription("Quux Festival Description");
        storedPropertySpecialEvent.getPropertySpecialEventIntances().iterator().next().setIsDeleted(new Integer(0));

        // save change
        storedEvents = service.saveSpecialEvents(storedEvents);

        tenantCrudService().flushAndClear();

        // read change
        PropertySpecialEvent storedSpecialEvent = tenantCrudService().find(PropertySpecialEvent.class,
                storedPropertySpecialEvent.getId());
        assertEquals("Quux Festival", storedSpecialEvent.getName());
        assertEquals("Quux Festival Description", storedSpecialEvent.getDescription());
        storedSpecialEvent.setDescription("Updated");
        storedEvents.clear();
        storedEvents.add(storedSpecialEvent);

        storedEvents = service.saveSpecialEvents(storedEvents);

        tenantCrudService().flushAndClear();

        storedSpecialEvent = tenantCrudService().find(PropertySpecialEvent.class, storedPropertySpecialEvent.getId());

        assertEquals("Updated", storedSpecialEvent.getDescription());
    }

    @Test
    public void testEditPropertySpecialEventInstances() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2024);

        // save it
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertNotNull(stored);
        assertEquals(1, stored.size());
        List<PropertySpecialEvent> storedEvents = stored.values().iterator().next();
        assertEquals(saved.iterator().next().getId(), storedEvents.iterator().next().getId());
        Set<PropertySpecialEventInstance> storedInstances = storedEvents.iterator().next()
                .getPropertySpecialEventIntances();
        assertEquals(6, storedInstances.size());

        // edit an instance
        PropertySpecialEventInstance editInstance = storedInstances.iterator().next();
        editInstance.setPostEventDays(5);

        // save it again
        service.saveSpecialEvents(storedEvents);

        tenantCrudService().flushAndClear();

        // It should not be duplicated
        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));

        tenantCrudService().flushAndClear();

        service.saveSpecialEvents(stored.values().iterator().next());

        tenantCrudService().flushAndClear();

        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());
        assertTrue(stored.containsKey("2019-04-01"));
        PropertySpecialEvent savedPropertySpecialEvent = stored.get("2019-04-01").iterator().next();
        assertEquals(6, savedPropertySpecialEvent.getPropertySpecialEventIntances().size());
        storedInstances = savedPropertySpecialEvent.getPropertySpecialEventIntances();
        // find our edited instance...
        boolean found = false;
        for (PropertySpecialEventInstance i : storedInstances) {
            if (i.getId().equals(editInstance.getId()) && i.getPostEventDays().equals(new Integer(5))) {
                found = true;
            }
        }
        assertTrue(found);
        assertEquals(6, storedInstances.size());
        assertEquals(new Integer(1), storedInstances.iterator().next().getEnableForecast());

        assertEquals(Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, savedPropertySpecialEvent.getImpactOnForcast());

        // change the impact on forecast settings
        savedPropertySpecialEvent.setImpactOnForcast(Constants.SPECIAL_EVENT_INFORMATION_ONLY);
        List<PropertySpecialEvent> informationOnlyList = new ArrayList<PropertySpecialEvent>();
        informationOnlyList.add(savedPropertySpecialEvent);

        service.saveSpecialEvents(informationOnlyList);

        tenantCrudService().flushAndClear();

        stored = service.getAllSpecialEventsByDateRange(date(start), date(end));

        assertEquals(1, stored.size());
        List<PropertySpecialEvent> events = stored.values().iterator().next();
        PropertySpecialEvent informationOnly = events.iterator().next();
        assertEquals(Constants.SPECIAL_EVENT_INFORMATION_ONLY, informationOnly.getImpactOnForcast());
    }

    @Test
    public void testEditPropertySpecialEventInstanceNotes() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                2);
        Set<PropertySpecialEventInstance> propertySpecialEventIntances = propertySpecialEvents.iterator().next()
                .getPropertySpecialEventIntances();

        Iterator<PropertySpecialEventInstance> iterator = propertySpecialEventIntances.iterator();
        PropertySpecialEventInstance firstInstance = iterator.next();
        PropertySpecialEventInstance secondInstance = iterator.next();

        // add a note to first instance...
        PropertySpecialEventInstanceNotes note1 = new PropertySpecialEventInstanceNotes();
        note1.setPropertySpecialEventInstance(firstInstance);
        note1.setPropertySpecialEventNotes("this is a note");
        note1.setCreatedByUserId(65535);
        firstInstance.setPropertySpecialEventInstanceNotes(note1);

        // add a note to second instance...
        PropertySpecialEventInstanceNotes note2 = new PropertySpecialEventInstanceNotes();
        note2.setPropertySpecialEventInstance(firstInstance);
        note2.setPropertySpecialEventNotes("this is another note");
        note2.setCreatedByUserId(65535);
        secondInstance.setPropertySpecialEventInstanceNotes(note2);

        // save the pse
        service.saveSpecialEvents(propertySpecialEvents);
        tenantCrudService().flushAndClear();

        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        List<PropertySpecialEvent> storedEvents = new ArrayList<PropertySpecialEvent>();
        storedEvents.addAll(stored.values().iterator().next());
        Iterator<PropertySpecialEventInstance> instancesIter = storedEvents.iterator().next()
                .getPropertySpecialEventIntances().iterator();
        PropertySpecialEventInstance firstStoredInstance = instancesIter.next();
        PropertySpecialEventInstanceNotes firstStoredNote = firstStoredInstance.getPropertySpecialEventInstanceNotes();
        assertNotNull(firstStoredNote);
        assertTrue("this is a note".equals(firstStoredNote.getPropertySpecialEventNotes())
                || "this is another note".equals(firstStoredNote.getPropertySpecialEventNotes()));
        PropertySpecialEventInstance secondStoredInstance = instancesIter.next();
        PropertySpecialEventInstanceNotes secondStoredNote = secondStoredInstance
                .getPropertySpecialEventInstanceNotes();
        assertNotNull(secondStoredNote);
        assertTrue("this is a note".equals(secondStoredNote.getPropertySpecialEventNotes())
                || "this is another note".equals(secondStoredNote.getPropertySpecialEventNotes()));

        // change the note
        secondStoredNote.setPropertySpecialEventNotes("this is a changed note");
        firstInstance.setPropertySpecialEventInstanceNotes(secondStoredNote);

        // save it again
        service.saveSpecialEvents(storedEvents);
        tenantCrudService().flushAndClear();

        // assert the note was updated...
        secondStoredNote = tenantCrudService().find(PropertySpecialEventInstanceNotes.class, secondStoredNote.getId());
        assertNotNull(secondStoredNote);
        assertEquals("this is a changed note", secondStoredNote.getPropertySpecialEventNotes());
    }

    @Test
    public void testSavePropertySpecialEventsNotLongerThan60Days() throws Exception {
        String start = "2019-11-01 00:00:00.0";
        String end = "2020-03-01 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                5);
        try {
            service.saveSpecialEvents(propertySpecialEvents);
            fail();
        } catch (TetrisException te) {
            // assertNotNull(te.getErrorCondition());
            assertEquals(ErrorCode.SPECIAL_EVENT_CANNOT_EXCEED_60_DAYS, te.getErrorCode());
        }
    }

    @Test
    public void testSaveDuplicatePropertySpecialEventsThrowsTetrisException() throws Exception {
        String start = "2019-11-01 00:00:00.0";
        String end = "2019-11-01 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);
        SpecialEventType differentSpecialEventType = specialEventTypes.get(1);
        assertNotNull(differentSpecialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        assertNull(propertySpecialEvents.iterator().next().getId());
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);
        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());

        // Create a new one with the same data
        propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        try {
            service.saveSpecialEvents(propertySpecialEvents);
            fail();
        } catch (TetrisException te) {
            assertNotNull(te.getErrorCode());
            assertEquals(ErrorCode.DUPLICATE_PROPERTY_SPECIAL_EVENT_NAME, te.getErrorCode());
        }

        // Create a new one with the same name, but different specialEventType
        propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, differentSpecialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        try {
            service.saveSpecialEvents(propertySpecialEvents);
            fail();
        } catch (TetrisException te) {
            assertNotNull(te.getErrorCode());
            assertEquals(ErrorCode.DUPLICATE_PROPERTY_SPECIAL_EVENT_NAME, te.getErrorCode());
        }
    }

    @Test
    public void testSaveDuplicateDeletedPropertySpecialEvents() throws Exception {
        // deleted property special events should have no effect on new ones
        String start = "2019-11-01 00:00:00.0";
        String end = "2019-11-01 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);
        SpecialEventType differentSpecialEventType = specialEventTypes.get(1);
        assertNotNull(differentSpecialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        assertNull(propertySpecialEvents.iterator().next().getId());
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);
        assertNotNull(saved);
        assertTrue(0 < saved.iterator().next().getId());

        tenantCrudService().flushAndClear();

        // Delete it
        assertTrue(service.deletePropertySpecialEvent(saved.iterator().next().getId()));

        tenantCrudService().flushAndClear();

        // Create a new one with the same data
        propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        propertySpecialEvents = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());
    }

    @Test
    public void testDeletePropertySpecialEvent() throws Exception {
        String start = "2019-11-01 00:00:00.0";
        String end = "2019-11-01 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);
        SpecialEventType differentSpecialEventType = specialEventTypes.get(1);
        assertNotNull(differentSpecialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        tenantCrudService().flush();

        propertySpecialEvents = service.saveSpecialEvents(propertySpecialEvents);
        PropertySpecialEvent specialEvent = tenantCrudService().find(PropertySpecialEvent.class,
                propertySpecialEvents.get(0).getId());
        assertEquals(new Integer(1), specialEvent.getStatusId());

        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());

        PropertySpecialEvent event = tenantCrudService().find(PropertySpecialEvent.class,
                stored.values().iterator().next().get(0).getId());
        assertNotNull(event);
        assertEquals(Constants.ACTIVE_STATUS_ID, event.getStatusId());
        assertEquals(0, event.getDeleteId());

        tenantCrudService().flushAndClear();

        assertTrue(service.deletePropertySpecialEvent(stored.values().iterator().next().get(0).getId()));

        tenantCrudService().flushAndClear();

        event = tenantCrudService().find(PropertySpecialEvent.class, stored.values().iterator().next().get(0).getId());
        assertEquals(Constants.INACTIVE_STATUS_ID, event.getStatusId());
        assertTrue(0 < event.getDeleteId());
    }

    @Test
    public void testDeleteAllInstancesAlsoDeletesPropertySpecialEvent() throws Exception {
        String start = "2019-11-01 00:00:00.0";
        String end = "2019-11-01 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);
        SpecialEventType differentSpecialEventType = specialEventTypes.get(1);
        assertNotNull(differentSpecialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2020);

        tenantCrudService().flush();

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date(start), date(end));
        assertEquals(1, stored.size());

        tenantCrudService().flushAndClear();

        // unfold all the instances into a set...
        Set<PropertySpecialEvent> storedEvents = new HashSet<PropertySpecialEvent>();
        Set<PropertySpecialEventInstance> storedInstances = new HashSet<PropertySpecialEventInstance>();
        for (List<PropertySpecialEvent> propertySpecialEventInstance : stored.values()) {
            for (PropertySpecialEvent propertySpecialEvent : propertySpecialEventInstance) {
                storedEvents.add(propertySpecialEvent);
                storedInstances.addAll(propertySpecialEvent.getPropertySpecialEventIntances());
            }
        }

        assertEquals(2, storedInstances.size());
        // mark instances as deleted...
        for (PropertySpecialEventInstance propertySpecialEventInstance : storedInstances) {
            propertySpecialEventInstance.setIsDeleted(1);
        }
        // put them into a list... :(
        List<PropertySpecialEvent> reSavedStoredEvents = new ArrayList<PropertySpecialEvent>(storedEvents);
        service.saveSpecialEvents(reSavedStoredEvents);

        tenantCrudService().flushAndClear();

        PropertySpecialEvent event = tenantCrudService().find(PropertySpecialEvent.class,
                stored.values().iterator().next().get(0).getId());
        assertEquals(Constants.INACTIVE_STATUS_ID, event.getStatusId());
        assertTrue(0 < event.getDeleteId());
    }

    @Test
    public void testSaveSpecialEventTypeNameCreate() throws Exception {
        Timestamp nowTs = new Timestamp(System.currentTimeMillis());
        SpecialEventType sevenType = new SpecialEventType();
        sevenType.setName("Foovention");
        sevenType.setColorCode("fff000");
        sevenType.setCreateDate(nowTs);
        sevenType.setDescription("A Great Gathering of Foo");
        sevenType.setStatusId(Constants.ACTIVE_STATUS_ID);
        service.saveSpecialEventType(sevenType);
        List<SpecialEventType> specialEvents = service.getAllSpecialEventTypes();
        assertNotNull(specialEvents);
        assertEquals(7, specialEvents.size());
    }

    @Test
    public void testSaveSpecialEventTypeNameUpdate() throws Exception {
        List<SpecialEventType> specialEvents = service.getAllSpecialEventTypes();
        assertEquals(6, specialEvents.size());
        SpecialEventType first = specialEvents.iterator().next();
        assertEquals("Convention", first.getName());

        tenantCrudService().flush();

        first.setName("abc");
        first.setStatusId(Constants.ACTIVE_STATUS_ID);

        service.saveSpecialEventType(first);

        List<SpecialEventType> specialEventsNew = service.getAllSpecialEventTypes();
        assertEquals(6, specialEventsNew.size());
        SpecialEventType firstNew = specialEventsNew.iterator().next();
        assertEquals("abc", firstNew.getName());
    }

    @Test
    public void hasUpdatedPropertySpecialEventImpactForecastImpactsForecastToFYI() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        String start = "2010-04-24 00:00:00.0";
        String end = "2010-04-24 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2020);

        tenantCrudService().flush();

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        // update the impact on forecast
        propertySpecialEvents.get(0).setImpactOnForcast(Constants.SPECIAL_EVENT_INFORMATION_ONLY);

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        PropertySpecialEvent foundPropertySpecialEvent = tenantCrudService().find(PropertySpecialEvent.class,
                propertySpecialEvents.get(0).getId());

        assertEquals(propertySpecialEvents.get(0).getImpactOnForcast(), foundPropertySpecialEvent.getImpactOnForcast());

        verify(syncEventAggregatorMock, times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(2)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void impactToFunctionSpaceForecastTriggersSync() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        String start = "2010-04-24 00:00:00.0";
        String end = "2010-04-24 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2020);
        propertySpecialEvents.get(0).setImpactOnFunctionSpaceForecast(false);

        tenantCrudService().flush();

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        // update function space impact
        propertySpecialEvents.get(0).setImpactOnFunctionSpaceForecast(true);

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        verify(syncEventAggregatorMock, times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(2)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void hasUpdatedPropertySpecialEventImpactForecastImpactsForecastToNoImpact() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        String start = "2010-04-24 00:00:00.0";
        String end = "2010-04-24 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2020);

        tenantCrudService().flush();

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        // update the impact on forecast
        propertySpecialEvents.get(0).setImpactOnForcast(Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING);

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        PropertySpecialEvent foundPropertySpecialEvent = tenantCrudService().find(PropertySpecialEvent.class,
                propertySpecialEvents.get(0).getId());

        assertEquals(propertySpecialEvents.get(0).getImpactOnForcast(), foundPropertySpecialEvent.getImpactOnForcast());

        verify(syncEventAggregatorMock, times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(2)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void testGeneratePropertySpecialEventInstances() {

        RepeatPattern repeatPattern = new RepeatPattern();
        repeatPattern.setPattern(RepeatPatternTypeEnum.YEAR);

        RepeatPatternStart repeatPatternStart = new RepeatPatternStart();
        repeatPatternStart.setDow("Thursday");
        repeatPatternStart.setDowPosition(6); //Last
        repeatPatternStart.setMonth("11");
        repeatPatternStart.setMode(RepeatPatternStartEnum.ON_DOW);
        repeatPattern.setPatterStart(repeatPatternStart);

        RepeatPatternEnd repeatPatternEnd = new RepeatPatternEnd();
        repeatPatternEnd.setMode(RepeatPatternEndEnum.OCCURENCES);
        repeatPatternEnd.setValue(10);
        repeatPattern.setPatterEnd(repeatPatternEnd);

        DateParameter startDateParameter = new DateParameter();
        startDateParameter.setDate(1);
        startDateParameter.setMonth(4);
        startDateParameter.setYear(2016);

        DateParameter endDateParameter = new DateParameter();
        startDateParameter.setDate(1);
        startDateParameter.setMonth(4);
        startDateParameter.setYear(2016);

        SpecialEventInstancesDTO specialEventInstancesDTO = service.generatePropertySpecialEventInstances(1, repeatPattern, startDateParameter, endDateParameter);
        boolean isInConsistent = specialEventInstancesDTO.getInconsistent();
        Set<PropertySpecialEventInstance> specialEventInstances = specialEventInstancesDTO.getInstances();

        assertFalse(isInConsistent);
        assertEquals(10, specialEventInstances.size());
    }

    @Test
    public void hasUpdatedPropertySpecialEventImpactForecastToInfoOnly() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        String start = "2010-04-24 00:00:00.0";
        String end = "2010-04-24 00:00:00.0";
        List<SpecialEventType> specialEventTypes = service.getAllSpecialEventTypes();
        assertTrue(0 < specialEventTypes.size());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2020);

        tenantCrudService().flush();

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        // update the impact on forecast
        propertySpecialEvents.get(0).setImpactOnForcast(Constants.SPECIAL_EVENT_INFORMATION_ONLY);

        service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flushAndClear();

        PropertySpecialEvent foundPropertySpecialEvent = tenantCrudService().find(PropertySpecialEvent.class,
                propertySpecialEvents.get(0).getId());

        assertEquals(propertySpecialEvents.get(0).getImpactOnForcast(), foundPropertySpecialEvent.getImpactOnForcast());

        verify(syncEventAggregatorMock, times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(2)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void updatePropertySpecialEventInstanceForEnableForecast() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();

        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                1).get(0);

        PropertySpecialEvent saved = service.saveSpecialEvents(Collections.singletonList(propertySpecialEvent)).get(0);
        tenantCrudService().flushAndClear();

        PropertySpecialEventInstance instance = saved.getPropertySpecialEventIntances().iterator().next();
        instance.setIsEdited(1);
        saved.setImpactOnForcast(Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING);

        PropertySpecialEvent retPropertySpecialEvent = service.saveSpecialEvents(Collections.singletonList(saved)).get(0);
        tenantCrudService().flushAndClear();

        PropertySpecialEventInstance foundInstance = retPropertySpecialEvent.getPropertySpecialEventIntances().iterator().next();
        assertEquals(Integer.valueOf(1), foundInstance.getEnableForecast());
    }

    private DateParameter date(String datePattern) throws Exception {
        return new DateParameter(sdf.parse(datePattern));
    }

    Frequency createFrequency(RepeatPattern repeatPattern) {
        Frequency frequency = new Frequency();
        frequency.setName("test_FrequencyName");
        frequency.setDescription("test_FrequencyDesc");
        frequency.setRepeatPattern(repeatPattern);
        frequency.setPattern(repeatPatternJaxbUtil.marshall(repeatPattern));
        return frequency;
    }

    Set<PropertySpecialEventInstance> createInstances(String start, String end, Integer impactOnForcast,
                                                      Frequency frequency) throws Exception {
        return new SpecialEventInstanceGenerator(impactOnForcast, frequency.getRepeatPattern(), date(start), date(end))
                .generateInstancesFromRepeatPattern();
    }

    List<PropertySpecialEvent> createPropertySpecialEvents(String name, int impactOnForcast,
                                                           SpecialEventType specialEventType, String start, String end, int numberOfPreDays, int numberOfPostDays,
                                                           RepeatPatternTypeEnum rptPatType, RepeatPatternStartEnum startEnum, RepeatPatternEndEnum endEnum,
                                                           int ocurrancesOrYears) throws Exception {
        List<PropertySpecialEvent> events = new ArrayList<PropertySpecialEvent>();
        events.add(createPropertySpecialEvent(name, impactOnForcast, specialEventType, start, end, numberOfPreDays,
                numberOfPostDays, rptPatType, startEnum, endEnum, ocurrancesOrYears));
        return events;
    }

    PropertySpecialEvent createPropertySpecialEvent(String name, int impactOnForcast,
                                                    SpecialEventType specialEventType, String start, String end, int numberOfPreDays, int numberOfPostDays,
                                                    RepeatPatternTypeEnum rptPatType, RepeatPatternStartEnum startEnum, RepeatPatternEndEnum endEnum,
                                                    int ocurrancesOrYears) throws Exception {
        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        propertySpecialEvent.setName(name);
        propertySpecialEvent.setSpecialEventType(specialEventType);
        propertySpecialEvent.setImpactOnForcast(impactOnForcast);
        propertySpecialEvent.setStatusId(Constants.ACTIVE_STATUS_ID);
        RepeatPattern repeatPattern = createRepeatPattern(start, end, numberOfPreDays, numberOfPostDays, rptPatType,
                startEnum, endEnum, ocurrancesOrYears);
        Frequency frequency = createFrequency(repeatPattern);
        Set<PropertySpecialEventInstance> pseiSet = createInstances(start, end, impactOnForcast, frequency);
        populatePropertySpecialEvent(propertySpecialEvent, frequency, pseiSet, start, end);
        return propertySpecialEvent;
    }

    RepeatPattern createRepeatPattern(String start, String end, int numberOfPreDays, int numberOfPostDays,
                                      RepeatPatternTypeEnum rptPatType, RepeatPatternStartEnum startEnum, RepeatPatternEndEnum endEnum,
                                      int occurancesOrYears) throws ParseException {
        RepeatPattern repeatPattern = new RepeatPattern();
        repeatPattern.setStartDate(sdf.parse(start).getTime());
        repeatPattern.setEndDate(sdf.parse(end).getTime());
        repeatPattern.setPattern(rptPatType);
        RepeatPatternStart startDate = new RepeatPatternStart();
        startDate.setMode(startEnum);
        repeatPattern.setPatterStart(startDate);
        RepeatPatternEnd rptPatternEnd = new RepeatPatternEnd();

        rptPatternEnd.setMode(endEnum);
        rptPatternEnd.setValue(occurancesOrYears);
        repeatPattern.setPatterEnd(rptPatternEnd);
        repeatPattern.setPostDays(numberOfPostDays);
        repeatPattern.setPreDays(numberOfPreDays);
        return repeatPattern;
    }

    PropertySpecialEvent populatePropertySpecialEvent(PropertySpecialEvent propertySpecialEvent, Frequency frequency,
                                                      Set<PropertySpecialEventInstance> pseiSet, String start, String end) throws ParseException {
        propertySpecialEvent.setStartDate(sdf.parse(start));
        propertySpecialEvent.setEndDate(sdf.parse(end));
        propertySpecialEvent.setImpactOnForcast(1);
        propertySpecialEvent.setFrequency(frequency);
        propertySpecialEvent.setPropertySpecialEventIntances(pseiSet);
        return propertySpecialEvent;
    }

    @Test
    public void shouldSaveNewSpecialEventCategory() {
        List<SpecialEventCategoryDTO> specialEventCategories = new ArrayList<SpecialEventCategoryDTO>();
        SpecialEventCategoryDTO specialEventCategory = new SpecialEventCategoryDTO();
        specialEventCategory.setSpecialEventTypeName("test special event name");
        specialEventCategory.setColor("000000");
        specialEventCategory.setStatusId(Constants.ACTIVE_STATUS_ID);
        specialEventCategories.add(specialEventCategory);
        List<SpecialEventCategoryDTO> savedSpecialEventCategories = service
                .saveSpecialEventCategory(specialEventCategories);
        assertNotNull(savedSpecialEventCategories.get(0).getSpecialEventTypeId());
        assertEquals(specialEventCategory.getColor(), savedSpecialEventCategories.get(0).getColor());
        assertEquals(specialEventCategory.getSpecialEventTypeName(), savedSpecialEventCategories.get(0)
                .getSpecialEventTypeName());
    }

    @Test
    public void shouldUpdateSpecialEventCategory() {
        SpecialEventType specialEventType = UniqueSpecialEventTypeCreator.createUniqueSpecialEventType();
        List<SpecialEventCategoryDTO> specialEventCategories = new ArrayList<SpecialEventCategoryDTO>();
        SpecialEventCategoryDTO specialEventCategory = new SpecialEventCategoryDTO();
        specialEventCategory.setSpecialEventTypeName("test special event name");
        specialEventCategory.setColor("0000012");
        specialEventCategory.setSpecialEventTypeId(specialEventType.getId());
        specialEventCategories.add(specialEventCategory);
        List<SpecialEventCategoryDTO> savedSpecialEventCategories = service
                .saveSpecialEventCategory(specialEventCategories);
        assertEquals(specialEventCategory.getSpecialEventTypeName(), savedSpecialEventCategories.get(0)
                .getSpecialEventTypeName());
        assertEquals(specialEventCategory.getColor(), savedSpecialEventCategories.get(0).getColor());
    }

    @Test
    public void shouldDeleteSpecialEventCategory() {
        SpecialEventType specialEventType = UniqueSpecialEventTypeCreator.createUniqueSpecialEventType();
        service.removeSpecialEventCategory(specialEventType.getId());

        SpecialEventType specialEventTypeAfterDelete = tenantCrudService().find(SpecialEventType.class,
                specialEventType.getId());
        assertNull(specialEventTypeAfterDelete);
    }

    @Test
    public void shouldFetchAllSpecialEventCategoriesAlongWithUsedStatus() {
        PropertySpecialEvent propertySpecialEvent = UniquePropertySpecialEventCreator
                .createUniquePropertySpecialEventCreator();
        List<SpecialEventCategoryDTO> specialEventCategories = service.extractSpecialEventCategoriesFromSpecialEventList(service.getPropertySpecialEvents(PROPERTY_ID_PUNE));
        assertTrue(propertySpecialEvent.getSpecialEventType().getId() == specialEventCategories.get(6)
                .getSpecialEventTypeId());
        assertEquals(propertySpecialEvent.getSpecialEventType().getName(), specialEventCategories.get(6)
                .getSpecialEventTypeName());
        assertTrue(specialEventCategories.get(6).getIsUsedInSpecialEvent());

        verify(dateService).getCaughtUpDate();
    }

    @Test
    public void shouldFetchAllSpecialEventCategoriesAlongWithUsedStatusByConvertingSpecialEventList() {
        PropertySpecialEvent propertySpecialEvent = UniquePropertySpecialEventCreator.createUniquePropertySpecialEventCreator();
        List<SpecialEventCategoryDTO> expectedSpecialEventCategories = createExpectedSpecialEventCategories(propertySpecialEvent);

        List<SpecialEventCategoryDTO> specialEventCategoryDTOS =
                service.extractSpecialEventCategoriesFromSpecialEventList(service.getPropertySpecialEvents(PROPERTY_ID_PUNE));


        verifySpecialEventsGroupedByCategories(expectedSpecialEventCategories, specialEventCategoryDTOS);
        verify(dateService).getCaughtUpDate();
    }

    private void verifySpecialEventsGroupedByCategories(List<SpecialEventCategoryDTO> expectedSpecialEventCategories, List<SpecialEventCategoryDTO> specialEventCategoryDTOS) {
        for (int i = 0; i < expectedSpecialEventCategories.size(); i++) {
            assertEquals(expectedSpecialEventCategories.get(i).getSpecialEventTypeName(), specialEventCategoryDTOS.get(i).getSpecialEventTypeName());
            assertEquals(expectedSpecialEventCategories.get(i).getColor(), specialEventCategoryDTOS.get(i).getColor());
            assertEquals(expectedSpecialEventCategories.get(i).getIsUsedInSpecialEvent(), specialEventCategoryDTOS.get(i).getIsUsedInSpecialEvent());
            assertEquals(expectedSpecialEventCategories.get(i).getSpecialEvents(), specialEventCategoryDTOS.get(i).getSpecialEvents());
        }
    }

    private List<SpecialEventCategoryDTO> createExpectedSpecialEventCategories(PropertySpecialEvent propertySpecialEvent) {
        String specialEventCategoryDataInJsonForm = new StringBuilder("[{\"specialEventTypeId\":1,\"specialEventTypeName\":\"Convention\",\"specialEvents\":[{\"specialEventId\":7,\"specialEventName\":\"Boy Scout Convention-Info Only\",\"soonestEventInstanceDate\":{\"date\":22,\"month\":9,\"year\":2015,\"millis\":0},\"isPastEvent\":true},{\"specialEventId\":3,\"specialEventName\":\"Hoteliers Convention\",\"soonestEventInstanceDate\":{\"date\":25,\"month\":11,\"year\":2009,\"millis\":0},\"isPastEvent\":true},{\"specialEventId\":6,\"specialEventName\":\"Hoteliers Convention-Info Only\",\"soonestEventInstanceDate\":{\"date\":21,\"month\":11,\"year\":2012,\"millis\":0},\"isPastEvent\":true}],\"statusId\":1,\"isUsedInSpecialEvent\":true},")
                .append("{\"specialEventTypeId\":2,\"specialEventTypeName\":\"Festival\",\"specialEvents\":[{\"specialEventId\":5,\"specialEventName\":\"St Patricks Day-Info Only\",\"soonestEventInstanceDate\":{\"date\":22,\"month\":2,\"year\":2013,\"millis\":0},\"isPastEvent\":true}],\"statusId\":1,\"isUsedInSpecialEvent\":true},")
                .append("{\"specialEventTypeId\":3,\"specialEventTypeName\":\"Holiday\",\"specialEvents\":[],\"statusId\":1,\"isUsedInSpecialEvent\":false},")
                .append("{\"specialEventTypeId\":4,\"specialEventTypeName\":\"LocHoliday\",\"specialEvents\":[],\"statusId\":1,\"isUsedInSpecialEvent\":false},")
                .append("{\"specialEventTypeId\":5,\"specialEventTypeName\":\"Other\",\"specialEvents\":[],\"statusId\":1,\"isUsedInSpecialEvent\":false},")
                .append("{\"specialEventTypeId\":6,\"specialEventTypeName\":\"Sports\",\"specialEvents\":[],\"statusId\":1,\"isUsedInSpecialEvent\":false},")
                .append("{\"specialEventTypeId\":" + propertySpecialEvent.getSpecialEventType().getId() + ",\"specialEventTypeName\":\"" + propertySpecialEvent.getSpecialEventType().getName() + "\",\"color\":\"000000\",\"specialEvents\":[{\"specialEventId\":" + propertySpecialEvent.getId() + ",\"specialEventName\":\"Property Special Event Name\",\"isPastEvent\":false}],\"statusId\":1,\"isUsedInSpecialEvent\":true}]")
                .toString();
        SpecialEventCategoryDTO[] specialEventCategoryDTOS = new Gson().fromJson(specialEventCategoryDataInJsonForm, SpecialEventCategoryDTO[].class);
        return Arrays.asList(specialEventCategoryDTOS);
    }


    @Test
    public void testCreatePropertySpecialEventInstanceOverlappingFail() throws Exception {
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        SpecialEventType specialEventType = service.getAllSpecialEventTypes().iterator().next();
        assertNotNull(specialEventType);

        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                1);

        List<PropertySpecialEvent> propertySpecialEvents1 = createPropertySpecialEvents("Foo Festival",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.YEAR, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.OCCURENCES,
                1);
        List<PropertySpecialEvent> saved = service.saveSpecialEvents(propertySpecialEvents);

        tenantCrudService().flush();
        assertEquals(1, saved.get(0).getPropertySpecialEventIntances().size());
        PropertySpecialEvent event = saved.get(0);
        tenantCrudService().flushAndClear();
        PropertySpecialEventInstance propertySpecialEventInstance = propertySpecialEvents1.get(0)
                .getPropertySpecialEventIntances().iterator().next();
        Set<PropertySpecialEventInstance> propertySpecialEventInstances = new HashSet<PropertySpecialEventInstance>();
        propertySpecialEventInstances.add(propertySpecialEventInstance);

        PropertySpecialEvent propertySpecialEvent = saved.get(0);
        propertySpecialEvent.setPropertySpecialEventIntances(propertySpecialEventInstances);
        assertTrue(propertySpecialEvent.getId() > 0);
        assertTrue(propertySpecialEvent.getPropertySpecialEventIntances().iterator().next().getId() == null);
        List<PropertySpecialEvent> storedEvent = null;
        try {
            storedEvent = service.saveSpecialEvents(saved);
        } catch (TetrisException t) {
            assertEquals(134, t.getErrorCode().getId());
        }
        tenantCrudService().flushAndClear();
        PropertySpecialEvent pse = tenantCrudService().find(PropertySpecialEvent.class, saved.get(0).getId());
        assertEquals(1, pse.getPropertySpecialEventIntances().size());
    }

    @Test
    public void testDeleteSpecialEvents_noSpecialEvents() throws Exception {
        service.setTenantCrudService(tenantCrudService);

        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEventInstanceNotes.BY_PROPERTY)).thenReturn(null);
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEventInstance.BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEvent.BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);

        service.deleteSpecialEvents();
        verify(tenantCrudService, times(0)).delete(Arrays.asList(any(PropertySpecialEventInstanceNotes.class)));
        verify(tenantCrudService, times(0)).delete(Arrays.asList(any(PropertySpecialEventInstance.class)));
        verify(tenantCrudService, times(0)).delete(Arrays.asList(any(PropertySpecialEvent.class)));
    }

    @Test
    public void testDeleteSpecialEvents_hasSpecialEvents() {

        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        List<PropertySpecialEvent> propertySpecialEvents = new ArrayList<>();
        propertySpecialEvents.add(propertySpecialEvent);
        PropertySpecialEventInstanceNotes propertySpecialEventInstanceNote = new PropertySpecialEventInstanceNotes();
        List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotes = new ArrayList<>();
        propertySpecialEventInstanceNotes.add(propertySpecialEventInstanceNote);
        PropertySpecialEventInstance propertySpecialEventInstance = new PropertySpecialEventInstance();
        List<PropertySpecialEventInstance> propertySpecialEventInstances = new ArrayList<>();
        propertySpecialEventInstances.add(propertySpecialEventInstance);

        service.setTenantCrudService(tenantCrudService);

        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEventInstanceNotes.BY_PROPERTY)).thenReturn(Arrays.asList(propertySpecialEventInstanceNotes));
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEventInstance.BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(propertySpecialEventInstances));
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEvent.BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(propertySpecialEvents));

        service.deleteSpecialEvents();
        verify(tenantCrudService, times(1)).delete(Arrays.asList(propertySpecialEvents));
        verify(tenantCrudService, times(1)).delete(Arrays.asList(propertySpecialEventInstanceNotes));
        verify(tenantCrudService, times(1)).delete(Arrays.asList(propertySpecialEventInstances));
    }

    @Test
    public void deleteAndCreateSpecialEventsShouldFailForSpecialEventHavingDurationMoreThan60Days() throws Exception {
        assertThrows(TetrisException.class, () -> {
            final Date startDate = LocalDate.parse("2019-04-10").toDate();
            final Date endDate = LocalDate.parse("2019-04-11").toDate();
            final List<SpecialEventUploadDTO> specialEventUploadDTOS = getSpecialEventUploadDTOs(startDate, endDate);
            try {
                doThrow(TetrisException.class).when(specialEventServiceSpy).persistSpecialEvents(eq(PROPERTY_ID_PUNE), eq(specialEventUploadDTOS));
                specialEventServiceSpy.deleteAndCreateSpecialEvents(PROPERTY_ID_PUNE, specialEventUploadDTOS);
                fail("Should not reach here");
            } catch (TetrisException e) {
                verify(syncEventAggregatorMock, never()).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
                verify(syncDisplayNameServiceMock, never()).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                        CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
                throw e;
            }
        });
    }

    @Test
    public void deleteAndCreateSpecialEventsHappyFlow() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        final Date startDate = LocalDate.parse("2019-04-10").toDate();
        final Date endDate = LocalDate.parse("2019-04-15").toDate();

        final List<SpecialEventUploadDTO> specialEventUploadDTOS = getSpecialEventUploadDTOs(startDate, endDate);

        final List<PropertySpecialEvent> propertySpecialEventsBefore
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(6, propertySpecialEventsBefore.size());
        final List<PropertySpecialEventInstance> propertySpecialEventInstancesBefore
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(21, propertySpecialEventInstancesBefore.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(4, propertySpecialEventInstanceNotesH1Before.size());

        final List<Frequency> frequenciesBefore = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        assertEquals(5, frequenciesBefore.size());

        service.deleteAndCreateSpecialEvents(PROPERTY_ID_PUNE, specialEventUploadDTOS);

        final List<PropertySpecialEvent> propertySpecialEventsAfter = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventsAfter.size());
        final List<PropertySpecialEventInstance> propertySpecialEventInstancesAfter
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventInstancesAfter.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesAfter
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(0, propertySpecialEventInstanceNotesAfter.size());

        final List<Frequency> frequenciesAfter = tenantCrudService().findAll(Frequency.class);
        assertEquals(5, frequenciesAfter.size());
        verify(syncEventAggregatorMock, times(1)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(1)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void testIsDisplayPropertyCodeTrue() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.getParameterName(), "BSTN")).thenReturn("Code");
        final boolean displayPropertyCode = service.isDisplayPropertyCode();
        assertTrue(displayPropertyCode);
        verify(pacmanConfigParamsService, times(1)).getParameterValueByClientLevel(
                eq(GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.getParameterName()), eq("BSTN"));
    }

    @Test
    public void testIsDisplayPropertyCodeFalse() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.getParameterName(), "BSTN")).thenReturn("Name");
        final boolean displayPropertyCode = service.isDisplayPropertyCode();
        assertFalse(displayPropertyCode);
        verify(pacmanConfigParamsService, times(1)).getParameterValueByClientLevel(
                eq(GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.getParameterName()), eq("BSTN"));
    }

    private List<SpecialEventUploadDTO> getSpecialEventUploadDTOs(Date startDate, Date endDate) throws Exception {
        final SpecialEventUploadDTO specialEventUploadDTO = new SpecialEventUploadDTO(CLIENT, "Hilton - Pune", PROPERTY_ID_PUNE, "H1", "Convention", "Christmas", startDate, endDate, 2, 3, 1, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
        return Collections.singletonList(specialEventUploadDTO);
    }

    @Test
    public void deletePropertySpecialEventInstance() throws Exception {
        List<PropertySpecialEvent> saved = createSpecialEventsForProperty();
        Set<PropertySpecialEventInstance> propertySpecialEventIntances = saved.iterator().next().getPropertySpecialEventIntances();
        PropertySpecialEventInstance firstTestEventInstance = propertySpecialEventIntances.iterator().next();
        assertTrue(isPropertyInstanceCreated(firstTestEventInstance));

        service.deletePropertySpecialEventInstance(firstTestEventInstance.getId());

        assertFalse(isPropertyInstanceCreated(firstTestEventInstance));
    }

    private boolean isPropertyInstanceCreated(PropertySpecialEventInstance firstTestEventInstance) throws Exception {
        Map<String, List<PropertySpecialEvent>> stored = service.getAllSpecialEventsByDateRange(date("2019-04-04 00:00:00.0"), date("2019-04-11 00:00:00.0"));

        Predicate<PropertySpecialEvent> predicate = event -> event.getPropertySpecialEventIntances().stream()
                .anyMatch(instance -> instance.getId().equals(firstTestEventInstance.getId()));
        return stored.values().stream().anyMatch(events -> events.stream().anyMatch(predicate));
    }

    @Test
    public void deleteAndCreatePropertySpecialEventsHappyFlow() throws Exception {
        when(syncEventAggregatorMock.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        final Date startDate = LocalDate.parse("2019-04-10").toDate();
        final Date endDate = LocalDate.parse("2019-04-15").toDate();

        final List<SpecialEventUploadDTO> specialEventUploadDTOS = getSpecialEventUploadDTOs(startDate, endDate);

        final List<PropertySpecialEvent> propertySpecialEventsBefore
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(6, propertySpecialEventsBefore.size());
        final List<PropertySpecialEventInstance> propertySpecialEventInstancesBefore
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(21, propertySpecialEventInstancesBefore.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(4, propertySpecialEventInstanceNotesH1Before.size());

        final List<Frequency> frequenciesBefore = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        assertEquals(5, frequenciesBefore.size());

        service.deleteAndCreatePropertySpecialEvents(specialEventUploadDTOS);

        final List<PropertySpecialEvent> propertySpecialEventsAfter = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventsAfter.size());
        final List<PropertySpecialEventInstance> propertySpecialEventInstancesAfter
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventInstancesAfter.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesAfter
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(0, propertySpecialEventInstanceNotesAfter.size());

        final List<Frequency> frequenciesAfter = tenantCrudService().findAll(Frequency.class);
        assertEquals(5, frequenciesAfter.size());
        verify(syncEventAggregatorMock, times(1)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameServiceMock, times(1)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
    }

    @Test
    public void deleteFrequencyData_FrequencyIdsGreaterThanMaxDefaultId() {
        final List<Frequency> frequenciesBefore = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        final Frequency newFrequency = UniqueFrequencyCreator.createUniqueFrequency();
        assertTrue(newFrequency.getId() > MAX_DEFAULT_FREQUENCY_ID);

        service.deleteFrequencyData();

        final List<Frequency> frequenciesAfter = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        assertEquals(frequenciesBefore.size(), frequenciesAfter.size());
    }

    @Test
    public void getSpecialEvents() {
        LocalDate date = new LocalDate("2013-03-22");
        Map<LocalDate, List<SpecialEventSummaryDto>> result = service.getSpecialEvents(new LocalDateRange(date, date));
        assertEquals(1, result.size());
        List<SpecialEventSummaryDto> eventList = result.get(date);
        assertEquals(1, eventList.size());
        assertEquals("St Patricks Day-Info Only", eventList.get(0).getEventName());
        assertEquals(date.toDate(), eventList.get(0).getOccupancyDate().getTime());
        assertEquals(date.toDate(), eventList.get(0).getStartDate().getTime());
        assertEquals(date.toDate(), eventList.get(0).getEndDate().getTime());
    }

    @Test
    public void testGetSpecialEventsForSpecialEventNameWithInstanceName() {
        // given
        LocalDate date = new LocalDate("2013-03-22");
        int specialEventId = 5;

        // when
        PropertySpecialEventInstance propertySpecialEventInstance = getSpecialEventInstance(specialEventId, date.toDate(), date.toDate());
        propertySpecialEventInstance.setEventInstanceName("Instance Name");
        tenantCrudService().save(propertySpecialEventInstance);
        Map<LocalDate, List<SpecialEventSummaryDto>> result = service.getSpecialEvents(new LocalDateRange(date, date));

        // then
        assertEquals(1, result.size());
        List<SpecialEventSummaryDto> eventList = result.get(date);
        assertEquals(1, eventList.size());
        assertEquals("St Patricks Day-Info Only - Instance Name", eventList.get(0).getEventName());
    }

    private PropertySpecialEventInstance getSpecialEventInstance(int specialEventId, Date eventStartDate, Date eventEndDate) {
        List<PropertySpecialEvent> storedPropertySpecialEvents = tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY,
                MapBuilder.with("propertyId", PROPERTY_ID_PUNE).and("specialEventId", specialEventId).get());

        PropertySpecialEvent storedPropertySpecialEvent = storedPropertySpecialEvents.get(0);
        PropertySpecialEventInstance propertySpecialEventInstance = storedPropertySpecialEvent.getPropertySpecialEventIntances().stream()
                .filter(storedPropertySpecialEventInstance -> storedPropertySpecialEventInstance.getStartDate().equals(eventStartDate) && storedPropertySpecialEventInstance.getEndDate().equals(eventEndDate))
                .findFirst()
                .orElse(null);

        return propertySpecialEventInstance;
    }

    @Test
    public void getDayWiseMapOfSpecialEventsWhenSpecialEventsEmpty() {
        LocalDate date = new LocalDate("2013-03-22");
        assertEquals(0, service.getDayWiseMapOfSpecialEvents(new LocalDateRange(date, date), null).size());
    }

    @Test
    public void testSaveSpecialEventsWithInstanceName() throws Exception {
        // given
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        String eventInstanceName = "Instance Name";

        List<SpecialEventType> specialEventTypes = tenantCrudService().findByNamedQuery(SpecialEventType.FIND_BY_NAME,
                MapBuilder.with("name", "Sports").get());
        SpecialEventType specialEventType = specialEventTypes.get(0);
        List<PropertySpecialEvent> propertySpecialEvents = createPropertySpecialEvents("Football",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        PropertySpecialEvent propertySpecialEvent = propertySpecialEvents.get(0);
        PropertySpecialEventInstance propertySpecialEventInstance = propertySpecialEvent.getPropertySpecialEventIntances().iterator().next();
        propertySpecialEventInstance.setEventInstanceName(eventInstanceName);

        // when
        // save the special event
        List<PropertySpecialEvent> savedPropertySpecialEvents = service.saveSpecialEvents(propertySpecialEvents);
        tenantCrudService().flushAndClear();

        // then
        assertEquals(1, savedPropertySpecialEvents.size());
        PropertySpecialEvent savedPropertySpecialEvent = savedPropertySpecialEvents.get(0);

        assertNotNull(savedPropertySpecialEvent.getId());
        Integer propertySpecialEventId = savedPropertySpecialEvent.getId();

        // when
        // get the special event
        List<PropertySpecialEvent> storedPropertySpecialEvents = tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY,
                MapBuilder.with("propertyId", PROPERTY_ID_PUNE).and("specialEventId", propertySpecialEventId).get());

        // then
        assertEquals(1, storedPropertySpecialEvents.size());
        PropertySpecialEvent storedPropertySpecialEvent = storedPropertySpecialEvents.get(0);

        assertNotNull(storedPropertySpecialEvent.getPropertySpecialEventIntances());
        assertEquals(1, storedPropertySpecialEvent.getPropertySpecialEventIntances().size());
        Set<PropertySpecialEventInstance> storedPropertySpecialEventInstances = storedPropertySpecialEvent.getPropertySpecialEventIntances();
        PropertySpecialEventInstance storedPropertySpecialEventInstance = storedPropertySpecialEventInstances.iterator().next();

        // verify the special event instance name
        assertEquals(eventInstanceName, storedPropertySpecialEventInstance.getEventInstanceName());
    }

    @ParameterizedTest
    @MethodSource("eventNameProvider")
    public void testFindSpecialEventSummaryDtosForSpecialEventName(String eventInstanceName, String expectedEventNameWithInstanceName) {
        // given
        LocalDate date = new LocalDate("2013-03-22");
        int specialEventId = 5;

        // when
        PropertySpecialEventInstance propertySpecialEventInstance = getSpecialEventInstance(specialEventId, date.toDate(), date.toDate());
        propertySpecialEventInstance.setEventInstanceName(eventInstanceName);
        tenantCrudService().save(propertySpecialEventInstance);
        List<SpecialEventSummaryDto> specialEventSummaryDtos = service.findSpecialEventSummaryDtos(date);

        // then
        assertEquals(1, specialEventSummaryDtos.size());
        SpecialEventSummaryDto specialEventSummaryDto = specialEventSummaryDtos.get(0);
        assertEquals(expectedEventNameWithInstanceName, specialEventSummaryDto.getEventName());
    }

    public static Stream<Arguments> eventNameProvider() {
        return Stream.of(
                arguments(null, "St Patricks Day-Info Only"),
                arguments("Instance Name", "St Patricks Day-Info Only - Instance Name")
        );
    }

    @Test
    public void deleteAndCreateNewHiltonSpecialEventTypes() {
        //Given
        setWorkContextProperty(TestProperty.H2);
        final List<SpecialEventType> existingEventTypes = tenantCrudService().findAll(SpecialEventType.class);
        assertFalse(existingEventTypes.isEmpty(), "Test Prerequisite: Test property should have existing special event types.");

        final List<PropertySpecialEvent> existingSpecialEvents = tenantCrudService().findAll(PropertySpecialEvent.class);
        assertTrue(existingSpecialEvents.isEmpty(), "Test Prerequisite: Test Property should not have existing special events.");

        //When
        service.deleteAndCreateNewHiltonSpecialEventTypes();

        tenantCrudService().flushAndClear();

        //Then
        final List<SpecialEventType> newHiltonSpecialEventTypes = tenantCrudService().findAll(SpecialEventType.class);

        final List<SpecialEventType> specialEventTypesSortedById = newHiltonSpecialEventTypes.stream()
                .sorted(Comparator.comparing(SpecialEventType::getId))
                .collect(Collectors.toList());

        assertFalse(specialEventTypesSortedById.isEmpty());

        assertSpecialEventType(specialEventTypesSortedById.get(0), 1, "Convention", "City Wide Convention (Transient Traffic)", Constants.ACTIVE_STATUS_ID, "ff9900");
        assertSpecialEventType(specialEventTypesSortedById.get(1), 2, "Festival", "Festival", Constants.ACTIVE_STATUS_ID, "00db57");
        assertSpecialEventType(specialEventTypesSortedById.get(2), 3, "National Holiday", "National Holiday", Constants.ACTIVE_STATUS_ID, "0066ff");
        assertSpecialEventType(specialEventTypesSortedById.get(3), 4, "Local Observance", "Local Observance", Constants.ACTIVE_STATUS_ID, "cc00ff");
        assertSpecialEventType(specialEventTypesSortedById.get(4), 5, "Direct Market Impact", "Direct Market Impact", Constants.ACTIVE_STATUS_ID, "ff0000");
        assertSpecialEventType(specialEventTypesSortedById.get(5), 6, "Sports", "Sporting Event", Constants.ACTIVE_STATUS_ID, "009292");
        assertSpecialEventType(specialEventTypesSortedById.get(6), 7, "Concert", "Concert", Constants.ACTIVE_STATUS_ID, "1a1a1a");
        assertSpecialEventType(specialEventTypesSortedById.get(7), 8, "Demand Disruption", "Demand Disruption", Constants.ACTIVE_STATUS_ID, "808080");
        assertSpecialEventType(specialEventTypesSortedById.get(8), 9, "Education", "Education", Constants.ACTIVE_STATUS_ID, "ccff00");
        assertSpecialEventType(specialEventTypesSortedById.get(9), 10, "On Site Event", "On Site Event", Constants.ACTIVE_STATUS_ID, "ff6dc5");

    }

    @Test
    public void testCreateSpecialEventCategory() {
        service.setTenantCrudService(tenantCrudService);
        service.setRegulatorService(regulatorService);
        Set<String> categories = Set.of("SP1", "SP2");
        when(tenantCrudService.findByNamedQuery(SpecialEventType.ALL)).thenReturn(new ArrayList<>());
        when(tenantCrudService.save(Mockito.any(SpecialEventType.class))).thenReturn(mockSpecialEventType("SP1"));
        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        service.createSpecialEventCategory(categories);
        verify(tenantCrudService).findByNamedQuery(SpecialEventType.ALL);
        verify(tenantCrudService, times(2)).save(Mockito.any(SpecialEventType.class));
        verify(regulatorService).isPropertyReadOnly();
    }

    @Test
    public void testCreateSpecialEventCategory_DuplicateCategories() {
        service.setTenantCrudService(tenantCrudService);
        service.setRegulatorService(regulatorService);
        Set<String> categories = Set.of("SP1", "SP2");
        when(tenantCrudService.findByNamedQuery(SpecialEventType.ALL)).thenReturn(List.of(mockSpecialEventType("SP1")));
        when(tenantCrudService.save(Mockito.any(SpecialEventType.class))).thenReturn(mockSpecialEventType("SP1"));
        when(regulatorService.isPropertyReadOnly()).thenReturn(false);
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> service.createSpecialEventCategory(categories));
        assertEquals("Special Event Category Request is not valid. Duplicate Categories : [SP1]", exception.getBaseMessage());
        verify(tenantCrudService).findByNamedQuery(SpecialEventType.ALL);
        verify(tenantCrudService, times(0)).save(Mockito.any(SpecialEventType.class));
        verify(regulatorService).isPropertyReadOnly();
    }

    @Test
    public void testDeleteSpecialEventCategory() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(SpecialEventType.FIND_BY_NAME,
                QueryParameter.with("name", "SP1").parameters())).thenReturn(mockSpecialEventType("SP1"));
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEvent.BY_PROPERTY_AND_SPECIAL_EVENT_TYPE,
                QueryParameter.with("specialEventTypeId", 1)
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(new ArrayList<>());
        when(tenantCrudService.executeUpdateByNamedQuery(SpecialEventType.DELETE_BY_CATEGORY_NAME,
                QueryParameter.with("name", "SP1").parameters())).thenReturn(1);
        service.deleteSpecialEventCategory("SP1");
        verify(tenantCrudService).executeUpdateByNamedQuery(SpecialEventType.DELETE_BY_CATEGORY_NAME,
                QueryParameter.with("name", "SP1").parameters());
    }

    @Test
    public void testDeleteSpecialEventCategory_ThrowsException() {
        service.setTenantCrudService(tenantCrudService);
        when(tenantCrudService.findByNamedQuerySingleResult(SpecialEventType.FIND_BY_NAME,
                QueryParameter.with("name", "SP1").parameters())).thenReturn(mockSpecialEventType("SP1"));
        when(tenantCrudService.findByNamedQuery(
                PropertySpecialEvent.BY_PROPERTY_AND_SPECIAL_EVENT_TYPE,
                QueryParameter.with("specialEventTypeId", 1)
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(mockPropertySpecialEvent(1, "name1")));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            service.deleteSpecialEventCategory("SP1");
        });
        assertEquals("Following special events must be re-assigned to another Category " +
                        "OR delete them using api. - /configautomation/deletePropertySpecialEventInstances/v1 [1]",
                exception.getBaseMessage());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(SpecialEventType.DELETE_BY_CATEGORY_NAME,
                QueryParameter.with("name", "SP1").parameters());
    }

    @Test
    public void testCreateSpecialEventCategory_PropertyIsReadOnly() {
        service.setRegulatorService(regulatorService);
        Set<String> categories = Set.of("SP1", "SP2");
        when(regulatorService.isPropertyReadOnly()).thenReturn(true);
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> service.createSpecialEventCategory(categories));
        assertEquals("Property is in read only state. Please wait for processes to complete.", exception.getBaseMessage());
        verify(regulatorService).isPropertyReadOnly();
    }


    @Test
    void testGetSpecialEventsWhenStartDateAndEndDateBetweenSpecialEventsDates() {

        final SpecialEventType specialEventType = buildSpecialEventType(1, "Foo Festival");

        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        propertySpecialEvent.setName("Foo Special Event");
        propertySpecialEvent.setSpecialEventType(specialEventType);
        propertySpecialEvent.setStartDate(DateUtil.getDateWithoutTime(30, 0, 2024));
        propertySpecialEvent.setEndDate(DateUtil.getDateWithoutTime(3, 1, 2024));
        propertySpecialEvent.setImpactOnForcast(1);
        propertySpecialEvent.setFrequency(null);
        propertySpecialEvent.setRepeatable(null);
        propertySpecialEvent.setStatusId(1);


        PropertySpecialEventInstance propertySpecialEventInstance = new PropertySpecialEventInstance();
        propertySpecialEventInstance.setStartDate(DateUtil.getDateWithoutTime(30, 0, 2024));
        propertySpecialEventInstance.setEndDate(DateUtil.getDateWithoutTime(3, 1, 2024));
        propertySpecialEventInstance.setEventInstanceName("Foo Property Special Event Instance");
        propertySpecialEventInstance.setPreEventDays(0);
        propertySpecialEventInstance.setPostEventDays(0);
        propertySpecialEventInstance.setEnableForecast(0);
        propertySpecialEventInstance.setPropertySpecialEvent(propertySpecialEvent);
        propertySpecialEvent.setStatusId(1);

        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(propertySpecialEventInstance));

        service.savePropertySpecialEvent(propertySpecialEvent, PROPERTY_ID_PUNE);


        Date startDate = DateUtil.getDateWithoutTime(1, 0, 2024);
        Date endDate = DateUtil.getDateWithoutTime(31, 0, 2024);

        Map<Date, String> dataMap = service.getSpecialEventsBetweenDates(startDate, endDate);
        assertEquals(2, dataMap.size());
    }

    @Test
    void shouldSaveSpecialEventsWithoutInstancesForNationalHoliday() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(null, service.NATIONAL_HOLIDAY_CATEGORY);
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        service.saveSpecialEvents(List.of(propertySpecialEvent));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);
        propertySpecialEvent.getPropertySpecialEventIntances().iterator().next().setIsDeleted(1);

        service.savePropertySpecialEvent(propertySpecialEvent, PROPERTY_ID_PUNE);

        List<PropertySpecialEvent> propertySpecialEvents = service.getPropertySpecialEvents(PROPERTY_ID_PUNE);
        assertTrue(propertySpecialEvents.stream().anyMatch(propertySpecialEvent1 -> propertySpecialEvent1.getName().equals(propertySpecialEvent.getName())));
        verify(syncEventAggregatorMock,times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    void shouldDeleteSpecialEventsWithoutInstancesForNationalHolidayWhenToggleFalse() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(null, service.NATIONAL_HOLIDAY_CATEGORY);
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        service.saveSpecialEvents(List.of(propertySpecialEvent));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        propertySpecialEvent.getPropertySpecialEventIntances().iterator().next().setIsDeleted(1);

        service.savePropertySpecialEvent(propertySpecialEvent, PROPERTY_ID_PUNE);

        List<PropertySpecialEvent> propertySpecialEvents = service.getPropertySpecialEvents(PROPERTY_ID_PUNE);
        assertFalse(propertySpecialEvents.stream().anyMatch(propertySpecialEvent1 -> propertySpecialEvent1.getName().equals(propertySpecialEvent.getName())));
        verify(syncEventAggregatorMock,times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    void shouldDeleteSpecialEventsWhenNoInstances() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(null, "Foo Festival");
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        service.saveSpecialEvents(List.of(propertySpecialEvent));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        propertySpecialEvent.getPropertySpecialEventIntances().iterator().next().setIsDeleted(1);

        service.savePropertySpecialEvent(propertySpecialEvent, PROPERTY_ID_PUNE);

        List<PropertySpecialEvent> propertySpecialEvents = service.getPropertySpecialEvents(PROPERTY_ID_PUNE);
        assertFalse(propertySpecialEvents.stream().anyMatch(propertySpecialEvent1 -> propertySpecialEvent1.getName().equals(propertySpecialEvent.getName())));
        verify(syncEventAggregatorMock,times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    void shouldDeleteSpecialEventsWhenNoInstancesAndToggleTrue() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(null, "Foo Festival");
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        service.saveSpecialEvents(List.of(propertySpecialEvent));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);
        propertySpecialEvent.getPropertySpecialEventIntances().iterator().next().setIsDeleted(1);

        service.savePropertySpecialEvent(propertySpecialEvent, PROPERTY_ID_PUNE);

        List<PropertySpecialEvent> propertySpecialEvents = service.getPropertySpecialEvents(PROPERTY_ID_PUNE);
        assertFalse(propertySpecialEvents.stream().anyMatch(propertySpecialEvent1 -> propertySpecialEvent1.getName().equals(propertySpecialEvent.getName())));
        verify(syncEventAggregatorMock,times(2)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    void testGetSpecialEventsWhenNoSpecialEventsPresent() {
        Date startDate = DateUtil.getDateWithoutTime(23, 11, 2050);
        Date endDate = DateUtil.getDateWithoutTime(25, 11, 2050);

        Map<Date, String> dataMap = service.getSpecialEventsBetweenDates(startDate, endDate);
        assertEquals(0, dataMap.size());
    }

    @Test
    void testShouldPreserveSpecialEventsWithoutInstancesForNationalHoliday(){
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("National Holiday");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);

        boolean shouldPreserveSpecialEventsWithoutInstances = service.shouldPreserveSpecialEventWithoutInstancesForCategory(specialEventType);

        assertTrue(shouldPreserveSpecialEventsWithoutInstances);
    }

    @Test
    void testShouldNotPreserveSpecialEventsWhenNotNationalHoliday(){
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("Festival");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);

        boolean shouldPreserveSpecialEventsWithoutInstances = service.shouldPreserveSpecialEventWithoutInstancesForCategory(specialEventType);

        assertFalse(shouldPreserveSpecialEventsWithoutInstances);
    }

    @Test
    void testShouldNotPreserveSpecialEventWithoutInstancesForNationalHoliday(){
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("National Holiday");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);

        boolean shouldPreserveSpecialEventsWithoutInstances = service.shouldPreserveSpecialEventWithoutInstancesForCategory(specialEventType);

        assertFalse(shouldPreserveSpecialEventsWithoutInstances);
    }

    @Test
    void testShouldNotPreserveSpecialEventWithoutInstances(){
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName("Festival");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);

        boolean shouldPreserveSpecialEventsWithoutInstances = service.shouldPreserveSpecialEventWithoutInstancesForCategory(specialEventType);

        assertFalse(shouldPreserveSpecialEventsWithoutInstances);
    }


    private PropertySpecialEvent mockPropertySpecialEvent(int id, String name) {
        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        propertySpecialEvent.setId(id);
        propertySpecialEvent.setName(name);
        return propertySpecialEvent;
    }

    private SpecialEventType mockSpecialEventType(String name) {
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setName(name);
        specialEventType.setId(1);
        return specialEventType;
    }

    private void assertSpecialEventType(SpecialEventType actualSpecialEventType, Integer specialEventTypeId, String specialEventName, String specialEventDescription, Integer statusId, String colorCode) {
        assertEquals(specialEventTypeId, actualSpecialEventType.getId());
        assertEquals(specialEventName, actualSpecialEventType.getName());
        assertEquals(specialEventDescription, actualSpecialEventType.getDescription());
        assertEquals(statusId, actualSpecialEventType.getStatusId());
        assertEquals(colorCode, actualSpecialEventType.getColorCode());
        assertNotNull(actualSpecialEventType.getCreateDate());
        assertNotNull(actualSpecialEventType.getLastUpdatedDate());
        assertEquals(Constants.SYSTEM_USER_ID, actualSpecialEventType.getCreatedByUserId());
        assertEquals(Constants.SYSTEM_USER_ID, actualSpecialEventType.getLastUpdatedByUserId());
    }

    @Test
    void shouldDeleteSpecialEventWhenAllInstancesDeletedAndToggleOff() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "Foo Festival");
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");

        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        instance1.setIsDeleted(1);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertTrue(result);
    }

    @Test
    void shouldNotDeleteSpecialEventWhenOneInstanceNotDeletedAndToggleOff() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "Foo Festival");
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");
        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertFalse(result);
    }

    @Test
    void shouldDeleteSpecialEventWhenAllInstancesDeletedAndToggleOffAndNonNationalHoliday() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");
        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);
        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");
        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        instance1.setIsDeleted(1);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertTrue(result);
    }

    @Test
    void shouldNotDeleteSpecialEventWhenAllInstancesDeletedAndToggleOn() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");

        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");
        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);
        instance1.setIsDeleted(1);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertFalse(result);
    }

    @Test
    void shouldNotDeleteSpecialEventWhenOneInstanceDeletedAndToggleOn() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");

        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");
        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertFalse(result);
    }

    @Test
    void shouldNotDeleteSpecialEventWhenOneInstanceDeletedAndToggleOff() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");

        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        PropertySpecialEventInstance instance1 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance1", "2024-01-30", "2024-01-03");
        PropertySpecialEventInstance instance2 = createEventInstance(propertySpecialEvent, "Foo Property Special Event Instance2", "2025-01-30", "2025-01-03");
        propertySpecialEvent.setPropertySpecialEventIntances(Set.of(instance1, instance2));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        instance2.setIsDeleted(1);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertFalse(result);
    }

    @Test
    void shouldDeleteSpecialEventWhenNoInstanceAndToggleOff() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");

        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        propertySpecialEvent.setPropertySpecialEventIntances(Set.of());
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(false);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertTrue(result);
    }

    @Test
    void shouldNotDeleteSpecialEventWhenNoInstanceAndToggleOn() throws Exception {
        final SpecialEventType specialEventType = buildSpecialEventType(1, "National Holiday");

        String start = "2019-04-10 00:00:00.0";
        String end = "2019-04-15 00:00:00.0";
        PropertySpecialEvent propertySpecialEvent = createPropertySpecialEvent("Independence Day",
                Constants.SPECIAL_EVENT_INFORMATION_ONLY, specialEventType, start, end, 0, 0,
                RepeatPatternTypeEnum.NONE, RepeatPatternStartEnum.ON_SAME_START_DATE, RepeatPatternEndEnum.END_BY,
                2019);

        propertySpecialEvent.setPropertySpecialEventIntances(Set.of());
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRESERVE_SPECIAL_EVENT_WITHOUT_INSTANCES)).thenReturn(true);
        boolean result = service.shouldDeletePropertySpecialEvent(propertySpecialEvent);

        assertFalse(result);
    }

    @Test
    void shouldBeAbleToGetSpecialEventsByPagination(){
        //GIVEN
        java.time.LocalDate startDateEvent1 = java.time.LocalDate.of(2025, 01, 01);
        java.time.LocalDate endDateEvent1 = java.time.LocalDate.of(2026, 12, 31);
        Integer specialEventId1 = addSpecialEvent(1, startDateEvent1, endDateEvent1, "0", "SpecialEvent1");
        addSpecialEventInstanceForDateRange(specialEventId1, java.time.LocalDate.of(2025, 01, 05), java.time.LocalDate.of(2025, 01, 10));
        addSpecialEventInstanceForDateRange(specialEventId1, java.time.LocalDate.of(2025, 03, 15), java.time.LocalDate.of(2025, 03, 20));
        java.time.LocalDate startDateEvent2 = java.time.LocalDate.of(2025, 06, 01);
        java.time.LocalDate endDateEvent2 = java.time.LocalDate.of(2026, 10, 31);
        Integer specialEventId2 = addSpecialEvent(1, startDateEvent2, endDateEvent2, "0", "SpecialEvent2");
        addSpecialEventInstanceForDateRange(specialEventId2, java.time.LocalDate.of(2025, 6, 25), java.time.LocalDate.of(2025, 6, 28));
        addSpecialEventInstanceForDateRange(specialEventId2, java.time.LocalDate.of(2025, 7, 15), java.time.LocalDate.of(2025, 7, 18));
        addSpecialEventInstanceForDateRange(specialEventId2, java.time.LocalDate.of(2025, 9, 20), java.time.LocalDate.of(2025, 9, 21));
        //WHEN
        java.time.LocalDate startDate = java.time.LocalDate.of(2025, 01, 01);
        java.time.LocalDate endDate = java.time.LocalDate.of(2025, 12, 31);
        List<PropertySpecialEventInstance> allSpecialEventInstanceByDateRangeByPaginationFirstPage = service.getAllSpecialEventInstanceByDateRangeByPagination(
                JavaLocalDateUtils.toDate(startDate),
                JavaLocalDateUtils.toDate(endDate),
                0,
                2);
        //THEN
        assertEquals(2, allSpecialEventInstanceByDateRangeByPaginationFirstPage.size());
        assertSpecialEventInstance(allSpecialEventInstanceByDateRangeByPaginationFirstPage.get(0), "SpecialEvent1", "2025-01-01", "2026-12-31", "2025-01-05", "2025-01-10");
        assertSpecialEventInstance(allSpecialEventInstanceByDateRangeByPaginationFirstPage.get(1), "SpecialEvent1", "2025-01-01", "2026-12-31", "2025-03-15", "2025-03-20");
        //WHEN
        List<PropertySpecialEventInstance> allSpecialEventInstanceByDateRangeByPaginationSecondPage = service.getAllSpecialEventInstanceByDateRangeByPagination(
                JavaLocalDateUtils.toDate(startDateEvent1),
                JavaLocalDateUtils.toDate(endDateEvent1),
                1,
                2);
        //THEN
        assertEquals(2, allSpecialEventInstanceByDateRangeByPaginationSecondPage.size());
        assertSpecialEventInstance(allSpecialEventInstanceByDateRangeByPaginationSecondPage.get(0), "SpecialEvent2", "2025-06-01", "2026-10-31", "2025-06-25", "2025-06-28");
        assertSpecialEventInstance(allSpecialEventInstanceByDateRangeByPaginationSecondPage.get(1), "SpecialEvent2", "2025-06-01", "2026-10-31", "2025-07-15", "2025-07-18");
        //WHEN
        List<PropertySpecialEventInstance> allSpecialEventInstanceByDateRangeByPaginationThirdPage = service.getAllSpecialEventInstanceByDateRangeByPagination(
                JavaLocalDateUtils.toDate(startDateEvent1),
                JavaLocalDateUtils.toDate(endDateEvent1),
                2,
                2);
        //THEN
        assertEquals(1, allSpecialEventInstanceByDateRangeByPaginationThirdPage.size());
        assertSpecialEventInstance(allSpecialEventInstanceByDateRangeByPaginationThirdPage.get(0), "SpecialEvent2", "2025-06-01", "2026-10-31", "2025-09-20", "2025-09-21");
    }

    private static void assertSpecialEventInstance(PropertySpecialEventInstance instance, String expectedSpecialEventName, String expectedSpecialEventStartDate, String expectedSpecialEventEndDate, String expectedInstanceStartDate, String expectedInstanceEndDate) {
        assertEquals(expectedSpecialEventName, instance.getPropertySpecialEvent().getName());
        assertEquals(expectedSpecialEventStartDate, instance.getPropertySpecialEvent().getStartDate().toString());
        assertEquals(expectedSpecialEventEndDate, instance.getPropertySpecialEvent().getEndDate().toString());
        assertEquals(expectedInstanceStartDate, instance.getStartDate().toString());
        assertEquals(expectedInstanceEndDate, instance.getEndDate().toString());
    }

    private void addSpecialEventInstanceForDateRange(Integer specialEventId, java.time.LocalDate instanceStartDate, java.time.LocalDate instanceEndDate) {
        addPropertySpecialEventInstance(instanceStartDate, instanceEndDate, specialEventId);
    }

    private void addPropertySpecialEventInstance(java.time.LocalDate startDate, java.time.LocalDate endDate, Integer specialEventId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Property_Special_Event_Instance values(" + specialEventId + ", '" + startDate + "', '" + endDate + "', 1, 1, 1, 0, 1, 1, GETDATE(), 1, GETDATE(), 1, 0, NULL)");
    }

    private Integer addSpecialEvent(Integer impactOnForecast, java.time.LocalDate startDate, java.time.LocalDate endDate, String repeatable, String eventName) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Property_Special_Event values " + "(5, 0, " + impactOnForecast + ", '" + startDate + "', '" + endDate + "', " + repeatable + ", 1, GETDATE(), 1, '" + eventName + "', '" + eventName + "', 1, 0, 1, GETDATE(), 1, 0) ");
        PropertySpecialEvent propertySpecialEvent = tenantCrudService().findByNamedQuerySingleResult(PropertySpecialEvent.BY_NAME_AND_PROPERTY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("name", eventName).parameters());
        return propertySpecialEvent.getId();
    }

    private PropertySpecialEventInstance createEventInstance(PropertySpecialEvent propertySpecialEvent, String instanceName, String startDate, String endDate) {
        PropertySpecialEventInstance instance = new PropertySpecialEventInstance();
        instance.setStartDate(DateUtil.getDateWithoutTime(Integer.parseInt(startDate.split("-")[0]), Integer.parseInt(startDate.split("-")[1]), Integer.parseInt(startDate.split("-")[2])));
        instance.setEndDate(DateUtil.getDateWithoutTime(Integer.parseInt(endDate.split("-")[0]), Integer.parseInt(endDate.split("-")[1]), Integer.parseInt(endDate.split("-")[2])));
        instance.setEventInstanceName(instanceName);
        instance.setPreEventDays(0);
        instance.setPostEventDays(0);
        instance.setEnableForecast(0);
        instance.setPropertySpecialEvent(propertySpecialEvent);
        return instance;
    }
}
