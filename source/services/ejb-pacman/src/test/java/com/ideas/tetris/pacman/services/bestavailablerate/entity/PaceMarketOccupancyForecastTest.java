package com.ideas.tetris.pacman.services.bestavailablerate.entity;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueBusinessGroupCreator;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.global.dao.UniquePropertyCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.forecastgroup.UniqueMktSegForecastGroupCreator;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.marketsegment.component.UniqueMktSegDetailsCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegForecastGroup;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PaceMarketOccupancyForecastTest extends AbstractG3JupiterTest {
    private static final int GROUP = 1;
    private static final int TRANSIENT = 2;
    private static final double DELTA = 0.01;
    private Random random = new Random();

    @Test
    public void shouldFetchDataForLastBusinessDateAtMarketSegmentLevelForRevenue() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }
    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtMarketSegmentLevelForOccupancy() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtMarketSegmentLevelForADR() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }


    @Test
    public void shouldFetchDataForLastBusinessDateAtMarketSegmentLevelForRevPar() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(5.66), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_MARKET_SEGMENT_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("mktSegId", uniqueMktSeg.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(5.66), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtPropertyLevelForRevenue() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.REVENUE.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.REVENUE.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }
    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtPropertyLevelForOccupancy() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtPropertyLevelForADR() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.ADR.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.ADR.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }


    @Test
    public void shouldFetchDataForLastBusinessDateAtPropertyLevelForRevPar() {
        //Given
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));

        MktSeg nextMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(100), BigDecimal.valueOf(400), nextMktSeg.getId(), decision.getId());
        tenantCrudService().flushAndClear();

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.REVPAR.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6.6), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());
        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_PROPERTY_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("propertyId", TestProperty.H1.getId())
                        .and("is_physical_capacity_enabled", "0")
                        .and("subType", ExceptionSubType.REVPAR.getCode()).parameters());
        //Then
        assertEquals(3, result.size());
        revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(5.66), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }


    @Test
    public void shouldFetchDataForLastBusinessDateAtForecastGroupLevelForRevenue() {
        //Given
        MktSegForecastGroup mktSegForecastGroup = createDummyDataForForecastGroupLevel();

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));

        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        revenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(300), BigDecimal.valueOf(90), BigDecimal.valueOf(110)));

        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revenue.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtForecastGroupLevelForOccupancy() {
        //Given
        MktSegForecastGroup mktSegForecastGroup = createDummyDataForForecastGroupLevel();

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        occupancy_NBR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(50), BigDecimal.valueOf(30), BigDecimal.valueOf(20)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == occupancy_NBR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtForecastGroupLevelForADR() {
        //Given
        MktSegForecastGroup mktSegForecastGroup = createDummyDataForForecastGroupLevel();

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());

        List<BigDecimal> ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());

        ADR = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(6), BigDecimal.valueOf(3), BigDecimal.valueOf(5.5)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == ADR.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    private MktSegForecastGroup createDummyDataForForecastGroupLevel() {
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDateWithoutTime(), 1);

        MktSeg uniqueMktSeg = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSegForecastGroup mktSegForecastGroup = UniqueMktSegForecastGroupCreator.createUniqueMktSegForecastGroupByMarketID(uniqueMktSeg.getPropertyId(), uniqueMktSeg);
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(50), BigDecimal.valueOf(300), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(30), BigDecimal.valueOf(90), uniqueMktSeg.getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotification((DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(20), BigDecimal.valueOf(110), uniqueMktSeg.getId(), decision.getId());

        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1)), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2)), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        createTotalActivityData(uniqueMktSeg, (DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3)), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        tenantCrudService().flushAndClear();
        return mktSegForecastGroup;
    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtForecastGroupLevelForRevPar() {
        //Given
        MktSegForecastGroup mktSegForecastGroup = createDummyDataForForecastGroupLevel();

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(5.66), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_FORECAST_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("forecastGroupId", mktSegForecastGroup.getForecastGroup().getId()).and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", TestProperty.H1.getId()).parameters());
        //Then
        assertEquals(3, result.size());
        revPar = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(5.66), BigDecimal.valueOf(0.67), BigDecimal.valueOf(3.67)));
        for (int i = 0; i < result.size(); i++) {
            assertTrue(0 == revPar.get(i).compareTo((BigDecimal) result.get(i)[4]));
        }

    }

    private void createTotalActivityData(MktSeg uniqueMktSeg, Date occupancyDate, BigDecimal roomsNotAvailableMaintenance, BigDecimal roomsNotAvailableOther, BigDecimal totalAccomCapacity) {
        TotalActivity totalActivity = tenantCrudService().findByNamedQuerySingleResult(TotalActivity.BY_OCCUPANCY_DATE_AND_PROPERTY_ID, QueryParameter.with("propertyId", 5).and("occupancyDate", occupancyDate).parameters());
        if (totalActivity == null) {
            UniqueTotalActivityCreator.createTotalActivity(uniqueMktSeg.getPropertyId(), occupancyDate, roomsNotAvailableMaintenance, roomsNotAvailableOther, totalAccomCapacity);
        } else {
            totalActivity.setRoomsNotAvailableMaintenance(roomsNotAvailableMaintenance);
            totalActivity.setRoomsNotAvailableOther(roomsNotAvailableOther);
            totalActivity.setTotalAccomCapacity(totalAccomCapacity);
            tenantCrudService().save(totalActivity);
        }
    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessTypeGroupForRevenue() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();
        createDummyDataForBusinessType(propertyId, GROUP);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedRevenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(100), BigDecimal.valueOf(1500), BigDecimal.valueOf(260)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualRevenue = (BigDecimal) result.get(i)[4];
            assertEquals(actualRevenue.setScale(2, RoundingMode.FLOOR), expectedRevenue.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedRevenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(100), BigDecimal.valueOf(1500), BigDecimal.valueOf(260)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualRevenue = (BigDecimal) result.get(i)[4];
            assertEquals(actualRevenue.setScale(2, RoundingMode.FLOOR), expectedRevenue.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessTypeGroupForOccupancy() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();
        createDummyDataForBusinessType(propertyId, GROUP);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(150), BigDecimal.valueOf(162), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(150), BigDecimal.valueOf(162), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessTypeGroupForADR() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();
        createDummyDataForBusinessType(propertyId, GROUP);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(0.67), BigDecimal.valueOf(9.26), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(0.67), BigDecimal.valueOf(9.26), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessTypeGroupForRevPar() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();
        createDummyDataForBusinessType(propertyId, GROUP);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(1.89), BigDecimal.valueOf(11.19), BigDecimal.valueOf(2.6)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_TYPE_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("businessTypeId", GROUP)
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(1.89), BigDecimal.valueOf(11.19), BigDecimal.valueOf(2.6)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    private void createDummyDataForBusinessType(int propertyId, int businessType) {
        Decision decision = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.getCurrentDate(), 1);
        MktSeg mktSeg = UniqueMktSegCreator.createUniqueMktSegCreatorForProperty(propertyId);
        MktSegDetails mktSegDetails = UniqueMktSegDetailsCreator.createUniqueMktSegDetailsFor(propertyId, mktSeg, businessType);
        createDummyMarketOccupancyData(propertyId, mktSegDetails.getMktSeg().getId(), decision.getId());
        createDummyTotalActivityData(propertyId);
    }

    private void createDummyMarketOccupancyData(int propertyId, int mktSegId, int decisionId) {
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastFor(propertyId, DateUtil.getCurrentDateWithoutTime(), mktSegId, decisionId, BigDecimal.valueOf(150), BigDecimal.valueOf(100));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), mktSegId, decisionId, BigDecimal.valueOf(162), BigDecimal.valueOf(1500));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3), mktSegId, decisionId, BigDecimal.valueOf(0), BigDecimal.valueOf(260));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 4), mktSegId, decisionId, BigDecimal.valueOf(195), BigDecimal.valueOf(346));

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotificationFor(propertyId, DateUtil.getCurrentDateWithoutTime(), mktSegId, decisionId, BigDecimal.valueOf(150), BigDecimal.valueOf(100));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotificationFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), mktSegId, decisionId, BigDecimal.valueOf(162), BigDecimal.valueOf(1500));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotificationFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3), mktSegId, decisionId, BigDecimal.valueOf(0), BigDecimal.valueOf(260));
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecastNotificationFor(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 4), mktSegId, decisionId, BigDecimal.valueOf(195), BigDecimal.valueOf(346));
    }

    private void createDummyTotalActivityData(int propertyId) {
        UniqueTotalActivityCreator.createTotalActivity(propertyId, DateUtil.getCurrentDateWithoutTime(), BigDecimal.valueOf(10), BigDecimal.valueOf(22), BigDecimal.valueOf(85));
        UniqueTotalActivityCreator.createTotalActivity(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), BigDecimal.valueOf(24), BigDecimal.valueOf(92), BigDecimal.valueOf(250));
        UniqueTotalActivityCreator.createTotalActivity(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 2), BigDecimal.valueOf(30), BigDecimal.valueOf(40), BigDecimal.valueOf(100));
        UniqueTotalActivityCreator.createTotalActivity(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3), BigDecimal.valueOf(0), BigDecimal.valueOf(0), BigDecimal.valueOf(100));
        UniqueTotalActivityCreator.createTotalActivity(propertyId, DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 4), BigDecimal.valueOf(15), BigDecimal.valueOf(15), BigDecimal.valueOf(100));
    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessGroupForRevenue() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();

        BusinessGroup businessGroup = createDummyDataForBusinessGroup(propertyId);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedRevenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(100), BigDecimal.valueOf(1500), BigDecimal.valueOf(260)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualRevenue = (BigDecimal) result.get(i)[4];
            assertEquals(actualRevenue.setScale(2, RoundingMode.FLOOR), expectedRevenue.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVENUE.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedRevenue = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(100), BigDecimal.valueOf(1500), BigDecimal.valueOf(260)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualRevenue = (BigDecimal) result.get(i)[4];
            assertEquals(actualRevenue.setScale(2, RoundingMode.FLOOR), expectedRevenue.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessGroupForOccupancy() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();

        BusinessGroup businessGroup = createDummyDataForBusinessGroup(propertyId);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(150), BigDecimal.valueOf(162), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.OCCUPANCY.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(150), BigDecimal.valueOf(162), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessGroupForADR() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();

        BusinessGroup businessGroup = createDummyDataForBusinessGroup(propertyId);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(0.67), BigDecimal.valueOf(9.26), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.ADR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(0.67), BigDecimal.valueOf(9.26), BigDecimal.valueOf(0)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldFetchDataForLastBusinessDateAtBusinessGroupForRevPar() {
        //Given
        TenantProperty tenantProperty = UniquePropertyCreator.createUniqueTenantProperty(CrudServiceBeanExtension.getTenantCrudService());
        Integer propertyId = tenantProperty.getId();

        BusinessGroup businessGroup = createDummyDataForBusinessGroup(propertyId);

        //When
        List<Object[]> result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecast.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        List<BigDecimal> expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(1.89), BigDecimal.valueOf(11.19), BigDecimal.valueOf(2.6)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

        //When
        result = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuery(PaceMarketOccupancyForecastNotifications.GET_BUSINESS_GROUP_LEVEL_FORECAST_AS_OF_LAST_BUSINESS_DATE,
                QueryParameter.with("inp_Business_Group_id", businessGroup.getId())
                        .and("startDate", DateUtil.getCurrentDateWithoutTime())
                        .and("endDate", DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3))
                        .and("subType", ExceptionSubType.REVPAR.getCode())
                        .and("is_physical_capacity_enabled", "0")
                        .and("propertyId", propertyId).parameters());
        //Then
        assertEquals(3, result.size());
        expectedOccupancy = new ArrayList<BigDecimal>(Arrays.asList(BigDecimal.valueOf(1.89), BigDecimal.valueOf(11.19), BigDecimal.valueOf(2.6)));
        for (int i = 0; i < result.size(); i++) {
            assertEquals(result.get(i)[0], propertyId);
            BigDecimal actualOccupancy = (BigDecimal) result.get(i)[4];
            assertEquals(actualOccupancy.setScale(2, RoundingMode.FLOOR), expectedOccupancy.get(i).setScale(2, RoundingMode.FLOOR));
        }

    }

    @Test
    public void shouldInsertMktPaceRecordsForOccupancyForecastRecordsForADateRange() throws Exception {
        Decision decision = UniqueDecisionCreator.createDecision(new Date(), 1);
        Integer decisionId = decision.getId();
        Date occupancyDate = getDate(LocalDate.now().plusYears(100));

        Integer marketSegId1 = 1;
        Integer marketSegId2 = 2;
        BigDecimal revenue1 = new BigDecimal(100);
        BigDecimal revenue2 = new BigDecimal(200);
        BigDecimal occupancy_nbr1 = new BigDecimal(10);
        BigDecimal occupancy_nbr2 = new BigDecimal(20);

        createAndPersistOccupancyForecast(decisionId, marketSegId1, 4, occupancyDate, revenue1, occupancy_nbr1);
        createAndPersistOccupancyForecast(decisionId, marketSegId1, 5, occupancyDate, revenue1, occupancy_nbr1);
        createAndPersistOccupancyForecast(decisionId, marketSegId1, 6, occupancyDate, revenue1, occupancy_nbr1);
        createAndPersistOccupancyForecast(decisionId, marketSegId1, 7, occupancyDate, revenue1, occupancy_nbr1);
        createAndPersistOccupancyForecast(decisionId, marketSegId2, 4, occupancyDate, revenue2, occupancy_nbr2);
        createAndPersistOccupancyForecast(decisionId, marketSegId2, 5, occupancyDate, revenue2, occupancy_nbr2);
        createAndPersistOccupancyForecast(decisionId, marketSegId2, 6, occupancyDate, revenue2, occupancy_nbr2);
        createAndPersistOccupancyForecast(decisionId, marketSegId2, 7, occupancyDate, revenue2, occupancy_nbr2);

        createAndPersistPaceMktOccupancyForecast(decisionId, marketSegId1, occupancyDate);
        createAndPersistPaceMktOccupancyForecast(decisionId, marketSegId2, occupancyDate);

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom ='Y' where Accom_Type_ID = 7");
        Map<String, Object> parameters = QueryParameter.with("decisionID", decisionId).and("propertyId", TestProperty.H1.getId()).parameters();
        int updatedRecords = tenantCrudService().executeUpdateByNamedQuery(PaceMarketOccupancyForecast.UPDATE_MKT_PACE_FORECAST, parameters);
        assertEquals(2, updatedRecords);

        List<Object[]> paceMktOccList = tenantCrudService().findByNativeQuery("select * from PACE_Mkt_Occupancy_FCST where decision_id=" + decisionId + "and occupancy_dt ='" + occupancyDate.toString() + "'");
        assertEquals(2, paceMktOccList.size());
        verifyResult(paceMktOccList.get(0), new BigDecimal(30.00000).setScale(5), new BigDecimal(300.00000).setScale(5), marketSegId1);
        verifyResult(paceMktOccList.get(1), new BigDecimal(60.00000).setScale(5), new BigDecimal(600.00000).setScale(5), marketSegId2);
    }

    private void verifyResult(Object[] row, BigDecimal occ_nbr, BigDecimal revenue, Integer marketSegId) {
        assertEquals(marketSegId, row[3]);
        assertEquals(occ_nbr, row[5]);
        assertEquals(revenue, row[6]);
    }

    private PaceMarketOccupancyForecast createAndPersistPaceMktOccupancyForecast(Integer decisionId, Integer marketSegmentID, Date occupancyDate) {
        PaceMarketOccupancyForecast pmof = new PaceMarketOccupancyForecast();
        pmof.setDecisionID(decisionId);
        pmof.setPropertyID(TestProperty.H1.getId());
        pmof.setMarketSegmentID(marketSegmentID);
        pmof.setOccupancyDate(occupancyDate);
        pmof.setOccupancyNumber(new BigDecimal(0));
        pmof.setRevenue(new BigDecimal(0));
        pmof.setCreateDate(new Date());
        tenantCrudService().getEntityManager().persist(pmof);
        return pmof;
    }

    private Date getDate(LocalDate date) {
        return DateUtil.toDate(date.toString());
    }

    private OccupancyForecast createAndPersistOccupancyForecast(Integer decisionId, Integer marketSegmentID, Integer accomTypeId, Date occupancyDate, BigDecimal revenue, BigDecimal occupancy) {
        OccupancyForecast occupancyForecast = new OccupancyForecast();
        occupancyForecast.setDecisionID(decisionId);
        occupancyForecast.setPropertyID(TestProperty.H1.getId());
        occupancyForecast.setMarketSegmentID(marketSegmentID);
        occupancyForecast.setAccomTypeID(accomTypeId);
        occupancyForecast.setOccupancyDate(occupancyDate);
        occupancyForecast.setOccupancyNumber(occupancy);
        occupancyForecast.setRevenue(revenue);
        occupancyForecast.setMonthID(5);
        occupancyForecast.setYearID(50);
        occupancyForecast.setCreateDate(new Date());
        tenantCrudService().getEntityManager().persist(occupancyForecast);
        return occupancyForecast;
    }

    private BusinessGroup createDummyDataForBusinessGroup(Integer propertyId) {
        Decision decision = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.getCurrentDate(), 1);
        MktSeg mktSeg = UniqueMktSegCreator.createUniqueMktSegCreatorForProperty(propertyId);
        BusinessGroup businessGroup = UniqueBusinessGroupCreator.createUniqueBusinessGroupFor(propertyId);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg, businessGroup);
        createDummyMarketOccupancyData(propertyId, mktSeg.getId(), decision.getId());
        createDummyTotalActivityData(propertyId);
        return businessGroup;
    }

    private void createDummyData() {
        Decision decision = UniqueDecisionCreator.createDecision(DateUtil.getCurrentDate(), 1);

        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.getCurrentDateWithoutTime(), BigDecimal.valueOf(10), BigDecimal.valueOf(200), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.getCurrentDateWithoutTime(), BigDecimal.valueOf(20), BigDecimal.valueOf(210), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), BigDecimal.valueOf(30), BigDecimal.valueOf(220), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), BigDecimal.valueOf(40), BigDecimal.valueOf(230), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 3), BigDecimal.valueOf(50), BigDecimal.valueOf(240), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
        UniquePaceMarketOccupancyForecastCreator.createPaceMarketOccupancyForecast(DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 4), BigDecimal.valueOf(60), BigDecimal.valueOf(250), UniqueMktSegCreator.createUniqueMktSegCreator().getId(), decision.getId());
    }
}