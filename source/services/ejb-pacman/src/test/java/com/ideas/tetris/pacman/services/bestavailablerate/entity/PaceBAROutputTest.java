package com.ideas.tetris.pacman.services.bestavailablerate.entity;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PaceBAROutputTest extends AbstractG3JupiterTest {

    RateUnqualified noRatePlan;

    @BeforeEach
    public void setUp() {
        noRatePlan = tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void shouldInvalidateBARDecisionsIfOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER, "LV1", null, null);
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals("Pending", paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsIfCeilingOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_CEILING, "LV1", null, "LV1");
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals("Pending", paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsIfFloorOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR, "LV1", "LV1", null);
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals("Pending", paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsCeilingNotEqualToRegularOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_CEILING, "LV1", null, "LV2");
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals(Constants.BARDECISIONOVERRIDE_CEILING, paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getCeilingRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsFloorNotEqualToRegularOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR, "LV1", "LV2", null);
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals("Floor", paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getFloorRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsFloorAndCeilingNotEqualToRegularOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, "LV1", "LV2", "LV4");
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getFloorRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getCeilingRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsFloorEqualToRegularCeilingNotOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, "LV1", "LV1", "LV4");
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals(Constants.BARDECISIONOVERRIDE_CEILING, paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertEquals(noRatePlan.getId(), paceBAROutput.getFloorRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getCeilingRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @Test
    public void shouldInvalidateBARDecisionsCeilingEqualToRegularFloorNotOverrideIsPresent() {
        //Given
        DecisionBAROutput decisionBarOutput = UniqueDecisionBarOutputCreator.createDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, "LV1", "LV2", "LV1");
        PaceBAROutput paceBAROutput = runTestAndLookupPaceRecord(decisionBarOutput);
        assertEquals("Floor", paceBAROutput.getOverride());
        assertEquals(noRatePlan.getId(), paceBAROutput.getRateUnqualified().getId());
        assertNotSame(noRatePlan.getId(), paceBAROutput.getFloorRateUnqualified().getId());
        assertEquals(noRatePlan.getId(), paceBAROutput.getCeilingRateUnqualified().getId());
        assertEquals(((Integer) DecisionReasonType.CONFIG_ISSUES.getId()), paceBAROutput.getReasonTypeId());
    }

    @SuppressWarnings("unchecked")
    private PaceBAROutput runTestAndLookupPaceRecord(DecisionBAROutput decisionBAROutput) {
        flushAndClear();
        String startDate = DateUtil.formatDate(DateUtil.getCurrentDate(), "yyyy-MM-dd");
        String endDate = DateUtil.formatDate(DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 20), "yyyy-MM-dd");

        Integer overrideDecisionId = tenantCrudService().findByNamedQuerySingleResult(Decision.GET_MAX_ID,
                QueryParameter.with("propertyId", 5).parameters());
        Map<String, Object> parameters = QueryParameter.with("ratePlanId", decisionBAROutput.getRateUnqualified().getId())
                .and("noRatePlanId", noRatePlan).and("decisionReasonTypeId", DecisionReasonType.CONFIG_ISSUES.getId())
                .and("overrideDecisionId", overrideDecisionId)
                .and("startDate", startDate)
                .and("endDate", endDate).parameters();
        //When
        int numberUpdated = tenantCrudService().executeUpdateByNamedQuery(PaceBAROutput.INSERT_REMOVED_OVERRIDES, parameters);
        assertTrue(numberUpdated > 0);

        parameters = QueryParameter.with("startDate", startDate).and("endDate", endDate).and("accomId", decisionBAROutput.getAccomClassId()).parameters();
        List<BigInteger> actualResult = tenantCrudService().findByNativeQuery("select pace_bar_output_id from pace_bar_output " +
                        " where Override = 'Pending' and arrival_dt between :startDate and :endDate and accom_class_id =:accomId ",
                parameters);
        parameters = QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters();
        tenantCrudService().executeUpdateByNamedQuery(PaceBAROutput.FIX_FLOOR_AND_CEILING_OVERRIDE_VALUE, parameters);
        tenantCrudService().executeUpdateByNamedQuery(PaceBAROutput.FIX_FLOOR_OVERRIDE_VALUE, parameters);
        tenantCrudService().executeUpdateByNamedQuery(PaceBAROutput.FIX_CEILING_OVERRIDE_VALUE, parameters);
        tenantCrudService().flush();
        //Then
        return tenantCrudService().find(PaceBAROutput.class, actualResult.get(0).intValue());
    }

    @Test
    public void shouldGetTheLatestRateForPriceByRankWhenBusinessDateHasDecisions() {
        setWorkContextProperty(TestProperty.H2);
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(TestProperty.H2.getId(), 0);
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = tenantCrudService().find(RateUnqualified.class, 13);
        //create decision
        Decision savedDecision = createDecisionFor(currentDate);
        //add entry to PACE_Bar_Output for this decision
        createPaceBarOutputFor(accomClass, currentDate.plusDays(1), savedDecision, rateUnqualified, 1, new BigDecimal("100.00000"));
        createPaceBarOutputNotificationFor(accomClass, currentDate.plusDays(1), savedDecision, rateUnqualified, 1, new BigDecimal("100.00000"));

        List<Object[]> results = tenantCrudService().findByNamedQuery(PaceBAROutput.DECISION_AS_OF_LAST_NIGHTLY_OPTIMIZATION_FOR_PRICING_BY_RANK,
                QueryParameter.with("startDate", currentDate.plusDays(1))
                        .and("endDate", currentDate.plusDays(1))
                        .and("businessDate", currentDate)
                        .and("los", 1)
                        .and("inp_accom_class_id", accomClass.getId())
                        .and("propertyId", TestProperty.H2.getId()).parameters());

        assertEquals(1, results.size());
        assertEquals(6, results.get(0)[0]);
        assertEquals(accomClass.getId(), results.get(0)[1]);
        assertEquals(currentDate.plusDays(1), new LocalDate(results.get(0)[2]));
        assertEquals(rateUnqualified.getName(), results.get(0)[4]);
        assertEquals(0, results.get(0)[5]);
        assertEquals(0, results.get(0)[6]);
        assertEquals(new BigDecimal("1.00"), results.get(0)[7]);

        results = tenantCrudService().findByNamedQuery(PaceBAROutputNotifications.DECISION_AS_OF_LAST_NIGHTLY_OPTIMIZATION_FOR_PRICING_BY_RANK,
                QueryParameter.with("startDate", currentDate.plusDays(1))
                        .and("endDate", currentDate.plusDays(1))
                        .and("businessDate", currentDate)
                        .and("los", 1)
                        .and("inp_accom_class_id", accomClass.getId())
                        .and("propertyId", TestProperty.H2.getId()).parameters());

        assertEquals(1, results.size());
        assertEquals(6, results.get(0)[0]);
        assertEquals(accomClass.getId(), results.get(0)[1]);
        assertEquals(currentDate.plusDays(1), new LocalDate(results.get(0)[2]));
        assertEquals(rateUnqualified.getName(), results.get(0)[4]);
        assertEquals(0, results.get(0)[5]);
        assertEquals(0, results.get(0)[6]);
        assertEquals(new BigDecimal("1.00"), results.get(0)[7]);

    }

    @Test
    public void shouldGetTheLatestRateForPriceByValueWhenBusinessDateHasDecisions() {
        setWorkContextProperty(TestProperty.H2);
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(TestProperty.H2.getId(), 0);
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = tenantCrudService().find(RateUnqualified.class, 13);
        //create decision
        Decision savedDecision = createDecisionFor(currentDate);
        //add entry to PACE_Bar_Output for this decision
        createPaceBarOutputFor(accomClass, currentDate.plusDays(1), savedDecision, rateUnqualified, -1, new BigDecimal("100.00000"));
        createPaceBarOutputNotificationFor(accomClass, currentDate.plusDays(1), savedDecision, rateUnqualified, -1, new BigDecimal("100.00000"));

        List<Object[]> results = tenantCrudService().findByNamedQuery(PaceBAROutput.DECISION_AS_OF_LAST_NIGHTLY_OPTIMIZATION_FOR_PRICING_BY_VALUE,
                QueryParameter.with("startDate", currentDate.plusDays(1))
                        .and("endDate", currentDate.plusDays(1))
                        .and("businessDate", currentDate)
                        .and("inp_accom_class_id", accomClass.getId())
                        .and("propertyId", TestProperty.H2.getId()).parameters());

        assertEquals(1, results.size());
        assertEquals(6, results.get(0)[0]);
        assertEquals(accomClass.getId(), results.get(0)[1]);
        assertEquals(currentDate.plusDays(1), new LocalDate(results.get(0)[2]));
        assertEquals(new BigDecimal("100.00"), results.get(0)[4]);
        assertEquals(0, results.get(0)[5]);
        assertEquals(0, results.get(0)[6]);
        assertEquals(new BigDecimal("100.00"), results.get(0)[7]);

        results = tenantCrudService().findByNamedQuery(PaceBAROutputNotifications.DECISION_AS_OF_LAST_NIGHTLY_OPTIMIZATION_FOR_PRICING_BY_VALUE,
                QueryParameter.with("startDate", currentDate.plusDays(1))
                        .and("endDate", currentDate.plusDays(1))
                        .and("businessDate", currentDate)
                        .and("inp_accom_class_id", accomClass.getId())
                        .and("propertyId", TestProperty.H2.getId()).parameters());

        assertEquals(1, results.size());
        assertEquals(6, results.get(0)[0]);
        assertEquals(accomClass.getId(), results.get(0)[1]);
        assertEquals(currentDate.plusDays(1), new LocalDate(results.get(0)[2]));
        assertEquals(new BigDecimal("100.00"), results.get(0)[4]);
        assertEquals(0, results.get(0)[5]);
        assertEquals(0, results.get(0)[6]);
        assertEquals(new BigDecimal("100.00"), results.get(0)[7]);

    }

    private PaceBAROutput createPaceBarOutputFor(AccomClass accomClass, LocalDate currentDate, Decision savedDecision, RateUnqualified rateUnqualified, int lengthOfStay, BigDecimal rate) {
        PaceBAROutput paceBAROutput = new PaceBAROutput();
        paceBAROutput.setDecision(savedDecision);
        paceBAROutput.setPropertyID(TestProperty.H2.getId());
        paceBAROutput.setAccomClassId(accomClass.getId());
        paceBAROutput.setArrivalDate(currentDate.toDate());
        paceBAROutput.setRateUnqualified(rateUnqualified);
        paceBAROutput.setDerivedUnqualifiedValue(rate);
        paceBAROutput.setLengthOfStay(lengthOfStay);
        paceBAROutput.setOverride("None");
        paceBAROutput.setReasonTypeId(1);
        paceBAROutput.setMonthId(1);
        paceBAROutput.setYearId(1);
        paceBAROutput.setCreateDate(currentDate.toDate());
        tenantCrudService().save(paceBAROutput);
        return paceBAROutput;
    }

    private PaceBAROutputNotifications createPaceBarOutputNotificationFor(AccomClass accomClass, LocalDate currentDate, Decision savedDecision, RateUnqualified rateUnqualified, int lengthOfStay, BigDecimal rate) {
        PaceBAROutputNotifications paceBAROutputNotifications = new PaceBAROutputNotifications();
        paceBAROutputNotifications.setDecision(savedDecision);
        paceBAROutputNotifications.setPropertyID(TestProperty.H2.getId());
        paceBAROutputNotifications.setAccomClassId(accomClass.getId());
        paceBAROutputNotifications.setArrivalDate(currentDate.toDate());
        paceBAROutputNotifications.setRateUnqualified(rateUnqualified);
        paceBAROutputNotifications.setDerivedUnqualifiedValue(rate);
        paceBAROutputNotifications.setLengthOfStay(lengthOfStay);
        paceBAROutputNotifications.setOverride("None");
        paceBAROutputNotifications.setReasonTypeId(1);
        paceBAROutputNotifications.setMonthId(1);
        paceBAROutputNotifications.setYearId(1);
        paceBAROutputNotifications.setCreateDate(currentDate.toDate());
        tenantCrudService().save(paceBAROutputNotifications);
        return paceBAROutputNotifications;
    }

    private Decision createDecisionFor(LocalDate currentDate) {
        Decision decision = new Decision();
        decision.setPropertyID(TestProperty.H2.getId());
        decision.setBusinessDate(currentDate.toDate());
        decision.setCaughtUpDate(currentDate.minusDays(1).toDate());
        decision.setDecisionTypeId(1);
        decision.setStartDate(currentDate.toDate());
        decision.setEndDate(currentDate.toDate());
        decision.setProcessStatusId(13);
        return tenantCrudService().save(decision);
    }


}