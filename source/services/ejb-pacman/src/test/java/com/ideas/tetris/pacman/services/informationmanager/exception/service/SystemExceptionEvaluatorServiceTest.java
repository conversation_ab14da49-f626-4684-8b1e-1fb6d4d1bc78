package com.ideas.tetris.pacman.services.informationmanager.exception.service;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.ServiceInitializer;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertEvaluationException;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.InsufficientCompetitorExceptionService;
import com.ideas.tetris.pacman.services.informationmanager.cache.InfoMgrStatusEntityCache;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertHistory;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.Comment;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionConfigDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrCostlyOOOExceptionSnoozeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.evaluation.InformationManagerEvaluationServicesExecsTracker;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.event.publisher.GlobalServicesEventPublisher;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.time.DateUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.Query;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.IM_LABEL_ESTIMATED_DISPLACED_REVENUE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class SystemExceptionEvaluatorServiceTest extends AbstractG3JupiterTest {

    public static final int PROPERTY_ID5 = 5;
    public static final String SUCCESS = "SUCCESS";
    public static final String TODAY = "Today";
    public static final String TODAY7 = "Today+7";
    public static final String FREQUENCY = "1";
    private static final String CREATED_BY = "SSO User";
    private static final int SCORE = 20;
    private static final String COMMENT = "this is a test comment";
    WorkContextType wc;
    SystemExceptionEvaluatorService exceptionEvaluator = null;
    DateService mockDateService;
    ExceptionAlertService exceptionAlertService;
    AuthorizationService authorizationService;
    ExceptionConfigService exceptionConfigService;
    AlertService alertService;
    PropertyGroupService propertyGroupService;
    InformationManagerEvaluationServicesExecsTracker util;
    @Mock
    PacmanConfigParamsService configParamService;
    @Mock
    DecisionService decisionService;
    @Mock
    PropertyService propertyService;
    AccomType accomType = null;
    @Mock
    private CrudService globalCrudService;
    @Mock
    private Property property_5;
    @Mock
    private Client client;
    @Mock
    private GlobalServicesEventPublisher globalServicesEventPublisher;

    @Mock
    private InsufficientCompetitorExceptionService inSufficientCompetitorExceptionService;

    @BeforeEach
    public void setUp() {
        setUpWorkContext();
        exceptionEvaluator = new SystemExceptionEvaluatorService();
        exceptionEvaluator.setGlobalCrudService(globalCrudService());
        exceptionEvaluator.setPropertyService(propertyService);
        propertyGroupService = new PropertyGroupService();
        mockDateService = DateService.createTestInstance();
        mockDateService.setCrudService(tenantCrudService());
        mockDateService.setPropertyGroupService(propertyGroupService);
        mockDateService.setMultiPropertyCrudService(multiPropertyCrudService());
        authorizationService = new AuthorizationService();
        authorizationService.setCrudService(tenantCrudService());
        authorizationService.setGlobalCrudService(globalCrudService());
        configParamService = mock(PacmanConfigParamsService.class);
        mockDateService.setConfigParamsService(configParamService);
        exceptionEvaluator.authorizationService = authorizationService;
        exceptionEvaluator.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionEvaluator.setTenantCrudService(tenantCrudService());
        exceptionEvaluator.dateService = mockDateService;
        exceptionEvaluator.configParamService = configParamService;
        exceptionEvaluator.inSufficientCompetitorExceptionService = inSufficientCompetitorExceptionService;
        alertService = new AlertService();
        alertService.setMultiPropertyCrudService(multiPropertyCrudService());
        alertService.setAuthorizationService(authorizationService);
        exceptionConfigService = new ExceptionConfigService();
        exceptionConfigService.crudService = tenantCrudService();
        exceptionConfigService.setAuthorizationService(authorizationService);
        exceptionConfigService.setGlobalCrudService(globalCrudService());
        exceptionConfigService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionEvaluator.exceptionConfigService = exceptionConfigService;
        exceptionEvaluator.setAlertService(alertService);
        exceptionAlertService = new ExceptionAlertService();
        exceptionAlertService.setGlobalCrudService(globalCrudService());
        exceptionAlertService.setAuthorizationService(authorizationService);
        exceptionAlertService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionAlertService.setTenantCrudService(tenantCrudService());
        exceptionAlertService.setConfigService(configParamService);
        exceptionEvaluator.exceptionAlertService = exceptionAlertService;
        accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        InfoMgrStatusEntityCache infoMgrStatusEntityCache = Mockito.mock(InfoMgrStatusEntityCache.class);
        Mockito.when(infoMgrStatusEntityCache.get(anyString())).thenReturn(tenantCrudService().find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID));
        exceptionAlertService.setInfoMgrStatusEntityCache(infoMgrStatusEntityCache);
        // Create a Property to be returned by the getPropertyById call
        Property property = new Property();
        property.setId(PROPERTY_ID5);
        property.setCode(TestProperty.H1.name());
        Client client = new Client();
        client.setCode(TestClient.BSTN.name());
        property.setClient(client);
        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT, QueryParameter.with(Property.PARAM_PROPERTY_ID, PROPERTY_ID5).parameters())).thenReturn(property);
        util = mock(InformationManagerEvaluationServicesExecsTracker.class);
        when(util.isOkToProceed(anyInt(), isA(String.class))).thenReturn(true);
        util.updateLastGoodExecutionDateWithCurrentDate(anyInt(), isA(String.class));
        exceptionEvaluator.evaluationServicesUtil = util;
        setupPropertyDependencies();
    }

    private void setupPropertyDependencies() {
        when(propertyService.getPropertyById(PROPERTY_ID5)).thenReturn(property_5);
        when(property_5.getClient()).thenReturn(client);
        when(property_5.getCode()).thenReturn("H1");
        when(client.getCode()).thenReturn("BSTN");
    }

    public void setUpWorkContext() {
        wc = new WorkContextType();
        wc.setClientCode("BSTN");
        wc.setClientId(2);
        wc.setPropertyCode("H1");
        wc.setPropertyId(5);
        wc.setUserId("11403");
        PacmanWorkContextHelper.setWorkContext(wc);
    }

    @Test
    public void testCreateAndEvaluateSystemExceptionConfigForUser() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testCreateAndEvaluateSystemExceptionConfigForUser_AlreadyExists() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testLosDetailsUpdated() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        Query qry = tenantCrudService().getEntityManager().createNativeQuery("update Decision_Bar_Output set Override='None' where Arrival_DT = '2012-09-06' and Accom_Class_ID = 7 and LOS=2");
        qry.executeUpdate();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testScoreDecrement() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        Query qry = tenantCrudService().getEntityManager().createNativeQuery("update Decision_Bar_Output set Override='None' where Arrival_DT = '2012-06-26' and Accom_Class_ID = 3");
        qry.executeUpdate();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testCreateLRVExceedingUserDefinedBARWithBaseScore() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(1.00, 15.00, Constants.BARDECISIONOVERRIDE_USER, 10, null, null, AlertType.LRVExceedingUserDefinedBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsUserDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - With Base Score", lrvExceedsUserDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.user.defined.bar.override", "LOS-1:LV6/1.00:15.00", "1", "20", "Exception");
        tenantCrudService().flushAndClear();
        // modifyUploadWindowToggle();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        lrvExceedsUserDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - With Base Score", lrvExceedsUserDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.user.defined.bar.override", "LOS-1:LV6/1.00:15.00", "1", "40", "Exception");
    }

    @Test
    public void testCreateLRVExceedingCeilingDefinedBARWithBaseScore() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_CEILING, 10, 10, null, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsCeilingDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling Defined BAR Exception - With Base Score", lrvExceedsCeilingDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV6/121.00:122.00", "1", "20", "Exception");
        tenantCrudService().flushAndClear();
        // modifyUploadWindowToggle();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        lrvExceedsCeilingDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling Defined BAR Exception - With Base Score", lrvExceedsCeilingDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV6/121.00:122.00", "1", "40", "Exception");
    }

    @Test
    public void testCreateLRVExceedingCeilingAndFloorDefinedBARWithBaseScore() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, 9, 9, 11, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsCeilingAndFloorDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling And Floor Defined BAR Exception - With Base Score", lrvExceedsCeilingAndFloorDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV5/121.00:122.00", "1", "20", "Exception");
        tenantCrudService().flushAndClear();
        // modifyUploadWindowToggle();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        lrvExceedsCeilingAndFloorDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling And Floor Defined BAR Exception - With Base Score", lrvExceedsCeilingAndFloorDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV5/121.00:122.00", "1", "40", "Exception");
    }

    @Test
    public void testCreateLRVExceedingUserDefinedBARWithScoreIncremented() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(1.00, 15.00, Constants.BARDECISIONOVERRIDE_USER, 10, null, null, AlertType.LRVExceedingUserDefinedBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsUserDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - With Score Incremented", lrvExceedsUserDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.user.defined.bar.override", "LOS-1:LV6/1.00:15.00", "1", "40", "Exception");
        List<Object[]> lrvExceedsUserDefinedBarExceptionHistoryWithScoreIncremented = getInfoMgrHistory();
        assertOnInfoMgrHistory("LRV Exceeds User Defined Bar Exception History for First Record- With Score Incremented ", lrvExceedsUserDefinedBarExceptionHistoryWithScoreIncremented, 0, "20", "Created");
        assertOnInfoMgrHistory("LRV Exceeds User Defined Bar Exception History for Second Record- With Score Incremented ", lrvExceedsUserDefinedBarExceptionHistoryWithScoreIncremented, 1, "40", "Score Increased los.details - LV6/1.00");
    }

    @Test
    public void testCreateLRVExceedingUserDefinedBARWhenInactive() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(2.00, 15.00, Constants.BARDECISIONOVERRIDE_USER, 10, null, null, AlertType.LRVExceedingUserDefinedBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        updateLRVData(1.00);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<Object[]> lrvExceedsUserDefinedBarExceptionWithInactive = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - Inactive", lrvExceedsUserDefinedBarExceptionWithInactive, "lrv.exceeding.the.user.defined.bar.override", "LOS-1:LV6/2.00:15.00", "2", "20", "Exception");
    }

    @Test
    public void testCreateLRVExceedingCeilingDefinedBARWithScoreIncremented() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_CEILING, 10, 10, null, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsCeilingDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling Defined BAR Exception - With Base Score", lrvExceedsCeilingDefinedBarExceptionWithBaseScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV6/121.00:122.00", "1", "40", "Exception");
        List<Object[]> lrvExceedsCeilingDefinedBarExceptionHistoryWithScoreIncremented = getInfoMgrHistory();
        assertOnInfoMgrHistory("LRV Exceeds Ceiling Defined Bar Exception History for First Record- With Score Incremented ", lrvExceedsCeilingDefinedBarExceptionHistoryWithScoreIncremented, 0, "20", "Created");
        assertOnInfoMgrHistory("LRV Exceeds Ceiling Defined Bar Exception History for Second Record- With Score Incremented ", lrvExceedsCeilingDefinedBarExceptionHistoryWithScoreIncremented, 1, "40", "Score Increased los.details - LV6/121.00");
    }

    @Test
    public void testCreateLRVExceedingCeilingDefinedBARWhenInactivated() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_CEILING, 10, 10, null, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        updateLRVData(1.00);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<Object[]> lrvExceedsCeilingDefinedBarExceptionWithInactive = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - Inactive", lrvExceedsCeilingDefinedBarExceptionWithInactive, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV6/121.00:122.00", "2", "20", "Exception");
    }

    @Test
    public void testCreateLRVExceedingCeilingAndFloorDefinedBARWithScoreIncremented() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, 9, 9, 11, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsCeilingAndFloorDefinedBarExceptionWithIncrementedScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds Ceiling And Floor Defined BAR Exception - With Score Incremented ", lrvExceedsCeilingAndFloorDefinedBarExceptionWithIncrementedScore, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV5/121.00:122.00", "1", "40", "Exception");
        List<Object[]> lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWithScoreIncremented = getInfoMgrHistory();
        assertOnInfoMgrHistory("LRV Exceeds Ceiling And Floor Defined Bar Exception History for First Record- With Score Incremented ", lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWithScoreIncremented, 0, "20", "Created");
        assertOnInfoMgrHistory("LRV Exceeds Ceiling And Floor Defined Bar Exception History for Second Record- With Score Incremented ", lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWithScoreIncremented, 1, "40", "Score Increased los.details - LV5/121.00");
    }

    @Test
    public void testCreateLRVExceedingCeilingAndFloorDefinedBARWhenInactivated() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        createTestDataForLRvExceedsDefinedBar(121.00, 122.00, Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING, 9, 9, 11, AlertType.LRVExceedingCeilingBAR, false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        updateLRVData(120.00);
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<Object[]> lrvExceedsCeilingDefinedBarExceptionWithInactive = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("LRV Exceeds User Defined BAR Exception - Inactive", lrvExceedsCeilingDefinedBarExceptionWithInactive, "lrv.exceeding.the.ceiling.bar.override", "LOS-1:LV5/121.00:122.00", "2", "20", "Exception");
        List<Object[]> lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWhenInactivated = getInfoMgrHistory();
        assertOnInfoMgrHistory("LRV Exceeds Ceiling And Floor Defined Bar Exception History for First Record - When InActivated ", lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWhenInactivated, 0, "20", "Created");
        assertOnInfoMgrHistory("LRV Exceeds Ceiling And Floor Defined Bar Exception History for Second Record - When InActivated ", lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWhenInactivated, 1, "40", "Score Increased los.details - LV5/121.00");
        assertOnInfoMgrHistory("LRV Exceeds Ceiling And Floor Defined Bar Exception History - When InActivated ", lrvExceedsCeilingAndFloorDefinedBarExceptionHistoryWhenInactivated, 2, "20", "Score Decreased");
    }

    @Test
    public void testCreateCPLRVExceedingUserDefinedBARWithBaseScore() {
        BigInteger decisionID = getDecisionID();
        when(decisionService.getMaxDecisionIdForDecisionTypes(Arrays.asList(1))).thenReturn(decisionID.intValue());
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("true");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        exceptionEvaluator.decisionService = decisionService;
        globalCrudService().flushAndClear();
        createTestDataForLRvExceedsDefinedBar(1.00, 15.00, Constants.BARDECISIONOVERRIDE_USER, 10, null, null, AlertType.CPUserOverrideBelowLRV, true);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsUserDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("Pricing User Specified Override below LRV", lrvExceedsUserDefinedBarExceptionWithBaseScore, "cp.user.override.below.lrv", "/10.00:15.00", "1", "20", "Exception");
    }

    @Test
    public void testCreateCPLRVExceedingCeilingBaseScore() {
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("true");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        globalCrudService().flushAndClear();
        createTestDataForLRvExceedsDefinedBar(1.00, 15.00, Constants.BARDECISIONOVERRIDE_CEILING, null, 10, null, AlertType.CPCeilingOverrideBelowLRV, true);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        tenantCrudService().flushAndClear();
        evaluateAndCreateExceptionForGivenListConfig(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        tenantCrudService().flushAndClear();
        List<Object[]> lrvExceedsUserDefinedBarExceptionWithBaseScore = getInfoMgrInstanceDetails();
        assertOnInfoMgrInstance("Pricing Ceiling Override below LRV", lrvExceedsUserDefinedBarExceptionWithBaseScore, "cp.ceiling.override.below.lrv", "/10.00:15.00", "1", "20", "Exception");
    }

    private void evaluateAndCreateExceptionForGivenListConfig(List<InformationMgrAlertConfigEntity> listConfigs) {
        try {
            exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        } catch (Exception e) {
            fail("receiving an exception when we shouldn't be " + e.toString());
        }
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getInfoMgrInstanceDetails() {
        return tenantCrudService().findByNativeQuery("select * from Info_Mgr_Instance");
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getInfoMgrHistory() {
        return tenantCrudService().findByNativeQuery("select * from Info_Mgr_History");
    }

    private void assertOnInfoMgrInstance(String Level, List<Object[]> alertData, String alertDescription, String alertDetails, String statusId, String score, String Discriminator) {
        assertEquals(alertDescription, (alertData.get(0)[2].toString()), Level + " - Alert Description ");
        assertTrue((alertData.get(0)[3].toString().contains(alertDetails)), Level + " - Alert Details ");
        assertEquals(statusId, (alertData.get(0)[5].toString()), Level + " - Status ID ");
        assertEquals(score, (alertData.get(0)[8].toString()), Level + " - Score ");
        assertEquals(Discriminator, (alertData.get(0)[11].toString()), Level + " - Discriminator ");
    }

    private void assertOnInfoMgrHistory(String Level, List<Object[]> alertData, int recordNumber, String score, String alertDescription) {
        assertEquals(score, (alertData.get(recordNumber)[5].toString()), Level + " - Score ");
        assertEquals(alertDescription, (alertData.get(recordNumber)[7].toString()), Level + " - Alert Description ");
    }

    private void createTestDataForLRvExceedsDefinedBar(double barRate, double lrv, String override, Integer userRateUnqualifiedId, Integer ceilingRateUnqualifiedId, Integer floorRateUnqualifiedId, AlertType expectedAlertType, boolean isContinuousPricing) {
        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String businessDateStr = sdf.format(businessDate).trim();
        BigInteger decisionid = getMaxDecisionIDforBusinessDate(businessDateStr);
        if (isContinuousPricing) {
            CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
            cpDecisionBAROutput.setPropertyId(PROPERTY_ID5);
            cpDecisionBAROutput.setDecisionId(decisionid.intValue());
            cpDecisionBAROutput.setProduct(tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
            cpDecisionBAROutput.setDecisionReasonTypeId(DecisionReasonType.ALL_IS_WELL.getId());
            cpDecisionBAROutput.setArrivalDate(LocalDate.fromDateFields(DateUtils.addDays(businessDate, 1)));
            cpDecisionBAROutput.setLengthOfStay(1);
            cpDecisionBAROutput.setAccomType(accomType);
            cpDecisionBAROutput.setOverrideType(DecisionOverrideType.valueOf(override.toUpperCase()));
            cpDecisionBAROutput.setOptimalBAR(BigDecimal.valueOf(barRate));
            cpDecisionBAROutput.setPrettyBAR(BigDecimal.valueOf(barRate));
            cpDecisionBAROutput.setRoomsOnlyBAR(BigDecimal.valueOf(barRate));
            cpDecisionBAROutput.setFinalBAR(BigDecimal.valueOf(barRate));
            cpDecisionBAROutput.setCeilingOverride(ceilingRateUnqualifiedId != null ? BigDecimal.valueOf(ceilingRateUnqualifiedId) : null);
            cpDecisionBAROutput.setFloorOverride(floorRateUnqualifiedId != null ? BigDecimal.valueOf(floorRateUnqualifiedId) : null);
            cpDecisionBAROutput.setSpecificOverride(userRateUnqualifiedId != null ? BigDecimal.valueOf(userRateUnqualifiedId) : null);
            tenantCrudService().save(cpDecisionBAROutput);
            tenantCrudService().flushAndClear();
        } else {
            Query qry = tenantCrudService().getEntityManager().createNativeQuery("Insert Into Decision_Bar_Output ([Decision_ID] ,[Property_ID] ,[Accom_Class_ID] ,[Arrival_DT] ,[Rate_Unqualified_ID] ,[LOS] ,[Override] ,[Floor_Rate_Unqualified_ID] ,[Decision_Reason_Type_ID] ,[Month_ID] ,[Year_ID] ,[CreateDate] ,[Ceil_Rate_Unqualified_ID])" + " VALUES (:decisionId, :propertyId, :accomTypeID, :arrivalDate, :userRateUnqualifiedId, -1, :override, :floorRateUnqualifiedId, 1, 1, 1, GETDATE(), :ceilingRateUnqualifiedId)");
            qry.setParameter("decisionId", decisionid);
            qry.setParameter("propertyId", PROPERTY_ID5);
            qry.setParameter("override", override);
            qry.setParameter("userRateUnqualifiedId", userRateUnqualifiedId);
            qry.setParameter("ceilingRateUnqualifiedId", ceilingRateUnqualifiedId);
            qry.setParameter("floorRateUnqualifiedId", floorRateUnqualifiedId);
            qry.setParameter("arrivalDate", DateUtils.addDays(businessDate, 1));
            qry.setParameter("accomTypeID", accomType.getAccomClass().getId());
            qry.executeUpdate();
        }
        // commented out, useful for debugging unit test
        // List<DecisionBAROutput> decisionBarOutputs =
        // tenantCrudService().findByNativeQuery("Select * from [dbo].[Decision_Bar_Output] where Decision_ID = " +
        // decisionid, new HashMap<String, Object>(), DecisionBAROutput.class);
        // Decision decision = tenantCrudService().find(Decision.class, decisionid.intValue());
        Query paceBarInsert = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO [dbo].[PACE_Bar_Output] " + " ([Decision_ID], [Property_ID], [Accom_Class_ID] ,[Arrival_DT] ,[Rate_Unqualified_ID] ,[Derived_Unqualified_Value] ,[LOS] ,[Override] ,[Floor_Rate_Unqualified_ID]," + " [Decision_Reason_Type_ID] ,[Month_ID] ,[Year_ID] ,[CreateDate] ,[Ceil_Rate_Unqualified_ID]) " + " VALUES (:decisionId, :propertyId, :accomId, :arrivalDate, :rateUnqualifiedID, :derivedValue, -1, :override, NULL, 1, 1, 1, '20130101', 10)");
        paceBarInsert.setParameter("decisionId", decisionid);
        paceBarInsert.setParameter("propertyId", PROPERTY_ID5);
        paceBarInsert.setParameter("accomId", accomType.getAccomClass().getId());
        paceBarInsert.setParameter("override", override);
        paceBarInsert.setParameter("arrivalDate", DateUtils.addDays(businessDate, 1));
        paceBarInsert.setParameter("rateUnqualifiedID", 12);
        paceBarInsert.setParameter("derivedValue", new BigDecimal(barRate));
        paceBarInsert.executeUpdate();
        Query paceBarNotificationInsert = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO [dbo].[PACE_Bar_Output_NOTIFICATION] " + " ([Decision_ID], [Property_ID], [Accom_Class_ID] ,[Arrival_DT] ,[Rate_Unqualified_ID] ,[Derived_Unqualified_Value] ,[LOS] ,[Override] ,[Floor_Rate_Unqualified_ID]," + " [Decision_Reason_Type_ID] ,[Month_ID] ,[Year_ID] ,[CreateDate] ,[Ceil_Rate_Unqualified_ID]) " + " VALUES (:decisionId, :propertyId, :accomId, :arrivalDate, :rateUnqualifiedID, :derivedValue, -1, :override, NULL, 1, 1, 1, '20130101', 10)");
        paceBarNotificationInsert.setParameter("decisionId", decisionid);
        paceBarNotificationInsert.setParameter("propertyId", PROPERTY_ID5);
        paceBarNotificationInsert.setParameter("accomId", accomType.getAccomClass().getId());
        paceBarNotificationInsert.setParameter("override", override);
        paceBarNotificationInsert.setParameter("arrivalDate", DateUtils.addDays(businessDate, 1));
        paceBarNotificationInsert.setParameter("rateUnqualifiedID", 12);
        paceBarNotificationInsert.setParameter("derivedValue", new BigDecimal(barRate));
        paceBarNotificationInsert.executeUpdate();
        // commented out, useful for debugging unit test
        // List<PaceBAROutput> paceBAROutputs =
        // tenantCrudService().findByNativeQuery("Select * from [dbo].[PACE_Bar_Output] where Decision_ID = " +
        // decisionid + " and Accom_Class_ID = " + accomType.getAccomClass().getId(), new HashMap<String, Object>(),
        // PaceBAROutput.class);
        Query decisionLRVInsert = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO [dbo].[Decision_LRV] " + " ([Decision_ID], [Property_ID], [Accom_Class_ID] ,[Occupancy_DT] ,[LRV] ,[CreateDate_DTTM] ,[Delta_Value] ,[Ceiling_Value]) " + " VALUES (:decisionId, :propertyId, :accomId, :arrivalDate, :lrv, '20130101', NULL, NULL)");
        decisionLRVInsert.setParameter("decisionId", decisionid);
        decisionLRVInsert.setParameter("propertyId", PROPERTY_ID5);
        decisionLRVInsert.setParameter("accomId", accomType.getAccomClass().getId());
        decisionLRVInsert.setParameter("arrivalDate", DateUtils.addDays(businessDate, 1));
        decisionLRVInsert.setParameter("lrv", new BigDecimal(lrv));
        decisionLRVInsert.executeUpdate();
        tenantCrudService().flushAndClear();
        Query fnOutput = tenantCrudService().getEntityManager().createNativeQuery("Select * from dbo.ufn_get_lrv_by_los_by_date_range_by_rc(:propertyId,:accomClassId,:startDate,:barWindowEndDate) where Decision_ID = :decisionId");
        fnOutput.setParameter("propertyId", PROPERTY_ID5);
        fnOutput.setParameter("accomClassId", accomType.getAccomClass().getId());
        fnOutput.setParameter("startDate", businessDate);
        fnOutput.setParameter("barWindowEndDate", DateUtils.addDays(businessDate, 2));
        fnOutput.setParameter("decisionId", decisionid);
        // commented out, useful for debugging unit test
        // List<Object[]> temp = fnOutput.getResultList();
        exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomType.getAccomClass().getCode(), MetricType.CURRENCY, DateUtils.addDays(businessDate, 1), false, tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", expectedAlertType.toString()).parameters())));
        tenantCrudService().flushAndClear();
    }

    private void updateLRVData(double lrv) {
        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String businessDateStr = sdf.format(businessDate).trim();
        Format formatter;
        formatter = new SimpleDateFormat("yyyy-MM-dd");
        BigInteger decisionid = getMaxDecisionIDforBusinessDate(businessDateStr);
        tenantCrudService().flushAndClear();
        Query decisionLRVUpdate = tenantCrudService().getEntityManager().createNativeQuery(" update Decision_LRV set LRV=:lrv where Decision_ID=:decisionId and Occupancy_DT=:arrivalDate and Accom_Class_ID=:accomId ");
        decisionLRVUpdate.setParameter("decisionId", decisionid);
        decisionLRVUpdate.setParameter("accomId", accomType.getAccomClass().getId());
        decisionLRVUpdate.setParameter("arrivalDate", formatter.format(DateUtils.addDays(businessDate, 1)));
        decisionLRVUpdate.setParameter("lrv", new BigDecimal(lrv));
        decisionLRVUpdate.executeUpdate();
        tenantCrudService().flushAndClear();
    }

    @Test
    public void testAutoConfigForSystemException_OOOAffectingDemand() {
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.OOORoomsAffectingDemand.toString()).parameters());
        exceptionEvaluator.getAccomClassForPropertyByExceptionType(PROPERTY_ID5, type);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testCreateAndUpdateException_OOOAffectingDemand() throws SecurityException, IllegalArgumentException {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.LRVExceedingUserDefinedBAR.toString()).parameters());
        type.setEnabled(false);
        tenantCrudService().save(type);
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertNotNull(listConfigs);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
    }

    @Test
    public void testExceptionConfiguration_Disabled_OOOAffectingDemand() throws SecurityException, NoSuchMethodException, IllegalArgumentException, IllegalAccessException, InvocationTargetException {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.LRVExceedingUserDefinedBAR.toString()).parameters());
        type.setEnabled(false);
        InfoMgrTypeEntity type2 = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.LRVExceedingCeilingBAR.toString()).parameters());
        type2.setEnabled(false);
        tenantCrudService().save(type2);
        InfoMgrTypeEntity type3 = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.OOORoomsAffectingDemand.toString()).parameters());
        type3.setEnabled(false);
        tenantCrudService().save(type3);
        InfoMgrTypeEntity straightLineEx = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.StraightLineAvailabilityEx.toString()).parameters());
        straightLineEx.setEnabled(false);
        InfoMgrTypeEntity type4 = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.CPCeilingOverrideBelowLRV.toString()).parameters());
        type4.setEnabled(false);
        InfoMgrTypeEntity type5 = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.CPUserOverrideBelowLRV.toString()).parameters());
        type5.setEnabled(false);
        InfoMgrTypeEntity cmpException = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.InsufficientCompetitorRatesForCompetitiveMarket.toString()).parameters());
        cmpException.setEnabled(false);
        tenantCrudService().save(cmpException);
        tenantCrudService().save(straightLineEx);
        List<InformationMgrAlertConfigEntity> systemExceptionConfigs = exceptionEvaluator.identifyExceptionConfigForProperty();
        exceptionEvaluator.processSystemExceptionConfiguration(systemExceptionConfigs);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(0, listConfigs.size());
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemand() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
    }

    @Test
    public void testOOOAffectingDemandExcWhenNoRateQualifiedData() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand(700,false);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertTrue(exceptionsRaised.get(0).getDetails().contains(IM_LABEL_ESTIMATED_DISPLACED_REVENUE + ":840000"));
    }

    @Test
    public void testOOOAffectingDemandExcWhenOOORoomsGreaterThanRemDemandWithToggleON() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand(700,true);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertTrue(exceptionsRaised.get(0).getDetails().contains(IM_LABEL_ESTIMATED_DISPLACED_REVENUE + ":840000"));
    }

    @Test
    public void testOOOAffectingDemandExcWhenOOORoomsLessThanRemDemandWithToggleON() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand(1200,true);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertTrue(exceptionsRaised.get(0).getDetails().contains(IM_LABEL_ESTIMATED_DISPLACED_REVENUE + ":1320000"));
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandWhenCostlyOOOThresholdLessThanLRV() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(1100d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandWhenCostlyOOOThresholdGreaterThanLRV() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(1300d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)).thenReturn(false);
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandWhenRateQualifiedTypeIsFixed() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(900d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        // set rate qualified type fixed
        setUpRateQualifiedType(3);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandForOutOfOrderOverride() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        mockConfigParamServiceForCostOOO();
        setupForOOOAffectingDemand();

        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dateInBetweenRange = DateUtil.addDaysToDate(businessDate, 3);
        String dateInBetweenRangeStr = sdf.format(dateInBetweenRange).trim();

        tenantCrudService().executeUpdateByNativeQuery("insert into Out_Of_Order_Override values ( " + accomType.getId() + ", '" + dateInBetweenRangeStr + "', 20, GETDATE(), 11403, 11403, GETDATE())");

        // set rate qualified type fixed
        setUpRateQualifiedType(3);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));

        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandForZeroCapacityRT() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        mockConfigParamServiceForCostOOO();
        setupForOOOAffectingDemand();
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Accom_Type_Capacity = 0 where accom_type_id = " + accomType.getId());
        // set rate qualified type fixed
        setUpRateQualifiedType(3);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
    }

    private void mockConfigParamServiceForCostOOO() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(900d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
    }

    private void setUpRateQualifiedType(int rateQualifiedTypeId) {
        Query query = tenantCrudService().getEntityManager().createNativeQuery("Update [dbo].[Rate_Qualified] SET Rate_Qualified_Type_Id=" + rateQualifiedTypeId + " where Rate_Code_Name In('Qualified Test rate','Qualified Test rate1')");
        query.executeUpdate();
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandWhenRateQualifiedTypeIsNotFixed() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        mockConfigParamServiceForCostOOO();
        setupForOOOAffectingDemand();
        // set rate qualified type 2 as not fixed
        setUpRateQualifiedType(2);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandWhenRateQualifiedTypeIsFixedAndValue() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(900d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)).thenReturn(false);

        setupForOOOAffectingDemand();
        // set rate qualified type 2 as not fixed
        setUpRateQualifiedType(3);
        // set Rate qualified type value
        tenantCrudService().executeUpdateByNativeQuery("Update [dbo].[Rate_Qualified] SET Rate_Qualified_Type_Id=" + 1 + " where Rate_Code_Name In('Qualified Test rate1')");
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
    }

    @Test
    public void testsetCommonDataForCreatingExceptionOOOAffectingDemand() throws SecurityException, NoSuchMethodException, IllegalArgumentException, IllegalAccessException, InvocationTargetException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getValue("pacman.BSTN.H1", "pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        Map<String, Object> parameters = new HashMap<String, Object>();
        Date businessDate = alertService.getBusinessDateForProperty(listConfigs.get(0).getPropertyId());
        String existingBarWindowParameterValue = "365";
        Date startDate = DateUtil.addDaysToDate(businessDate, 1);
        Date barWindowEndDate = DateUtil.addDaysToDate(startDate, Integer.parseInt(existingBarWindowParameterValue));
        exceptionEvaluator.setCommonDataForCreatingException(listConfigs.get(0), parameters, accomType.getAccomClass().getId(), false);
        assertEquals((new SimpleDateFormat("yyyy-MM-dd")).format(DateUtil.addDaysToDate(barWindowEndDate, -1)), parameters.get("barWindowEndDate"));
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandScoreIncreased() throws SecurityException, IllegalArgumentException, AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertEquals(40, exceptionsRaised.get(0).getScore());
        assertEquals(new Integer(1), exceptionsRaised.get(0).getStatusId());
    }

    @Test
    public void testCreateAndUpdateExceptionOOOAffectingDemandScoreDecreasedWhenExceptionIsSuspended() throws AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled", "BSTN", "H1")).thenReturn("false");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = infoMgrExcepNotifEntities.get(0);
        exceptionAlertService.suspendOrRevertRaisedException(infoMgrExcepNotifEntity.getId(), infoMgrExcepNotifEntity.getPropertyId(), 5, true);
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertEquals(0, exceptionsRaised.get(0).getScore());
        assertEquals(new Integer(1), exceptionsRaised.get(0).getStatusId());
    }

    @Test
    public void exceptionNotCreatedWhenThresholdsLessThanSnoozedThresholds() throws AlertEvaluationException {
        mockConfigParameters();
        setupForOOOAffectingDemand();
        setupSnoozeConfiguration(2000, 2000);
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurations = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, listConfigs.size());
        assertEquals(1, snoozeConfigurations.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(0, exceptionsRaised.size());
        assertEquals(1, snoozeConfigurationsAfterEvaluation.size());
    }

    @Test
    public void exceptionCreatedWhenOnlyOOOIncreased() throws AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)).thenReturn(true);
        setupSnoozeConfiguration(1000, 1200);
        setupForOOOAffectingDemand();
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertEquals(20, exceptionsRaised.get(0).getScore());
        assertEquals((Integer) 1, exceptionsRaised.get(0).getStatusId());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(0, snoozeConfigurationsAfterEvaluation.size());
    }

    @Test
    public void exceptionCreatedWhenOOOIncreasedLRVIncreased() throws AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)).thenReturn(true);
        setupSnoozeConfiguration(1000, 1000);
        setupForOOOAffectingDemand();
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        assertEquals(20, exceptionsRaised.get(0).getScore());
        assertEquals((Integer) 1, exceptionsRaised.get(0).getStatusId());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(0, snoozeConfigurationsAfterEvaluation.size());
    }

    @Test
    public void exceptionNotCreatedWhenOOOSameAndLRVIncreased() throws AlertEvaluationException {
        mockConfigParameters();
        setupForOOOAffectingDemand();
        setupSnoozeConfiguration(1100, 1100);
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, snoozeConfigurationsAfterEvaluation.size());
    }

    @Test
    public void snoozedExceptionNotSetInactiveIfConditionsNotWorsened() throws AlertEvaluationException {
        mockConfigParameters();
        Map<String, List<InformationMgrAlertConfigEntity>> testConfigs = setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> list = testConfigs.get("SUCCESS");
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO info_mgr_instance values (21, '','', 000005, 1, 'SSO User', GETDATE(), 100, GETDATE(), 7, 'Exception', '" + getTestExceptionDate() + "', 2, " + list.get(0).getId() + ", 0, NULL, NULL)");
        List<InfoMgrInstanceEntity> listExceptions = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        setupSnoozeConfiguration(1100, 1100, listExceptions.get(0).getId());
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, snoozeConfigurationsAfterEvaluation.size());
        assertEquals(Constants.ALERT_STATUS_SNOOZED_ID, exceptionsRaised.get(0).getAlertStatus().getId().intValue());
        assertEquals(1, exceptionsRaised.get(0).getStatusId().intValue());
    }

    @Test
    public void exceptionNotCreatedOOOSameAndLRVSame() throws AlertEvaluationException {
        mockConfigParameters();
        setupForOOOAffectingDemand();
        setupSnoozeConfiguration(1100, 1200);
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, snoozeConfigurationsAfterEvaluation.size());
    }

    @Test
    public void exceptionNotCreatedOOOIncreasedAndLRVDecreased() throws AlertEvaluationException {
        mockConfigParameters();
        setupForOOOAffectingDemand();
        setupSnoozeConfiguration(900, 1500);
        assertSingleRecordInSnoozeConfiguration();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(0, exceptionsRaised.size());
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurationsAfterEvaluation = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, snoozeConfigurationsAfterEvaluation.size());
    }

    private void assertSingleRecordInSnoozeConfiguration() {
        List<InfoMgrCostlyOOOExceptionSnoozeEntity> snoozeConfigurations = tenantCrudService().findAll(InfoMgrCostlyOOOExceptionSnoozeEntity.class);
        assertEquals(1, snoozeConfigurations.size());
    }

    private void mockConfigParameters() {
        when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("360");
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        when(configParamService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)).thenReturn(true);
    }

    private void setupSnoozeConfiguration(int ooo, int lrv) {
        String date = getTestExceptionDate();
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Info_mgr_Costly_ooo_Excep_Snooze]; INSERT INTO [dbo].[Info_mgr_Costly_ooo_Excep_Snooze] values ('" + date + "'," + accomType.getAccomClass().getId() + "," + ooo + "," + lrv + ", GETDATE(), 11403, 1 )");
    }

    private void setupSnoozeConfiguration(int ooo, int lrv, Integer exceptionId) {
        String date = getTestExceptionDate();
        tenantCrudService().executeUpdateByNativeQuery("delete from [dbo].[Info_mgr_Costly_ooo_Excep_Snooze]; INSERT INTO [dbo].[Info_mgr_Costly_ooo_Excep_Snooze] values ('" + date + "'," + accomType.getAccomClass().getId() + "," + ooo + "," + lrv + ", GETDATE(), 11403, " + exceptionId + " )");
    }

    private String getTestExceptionDate() {
        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dateInBetweenRange = DateUtil.addDaysToDate(businessDate, 3);
        return sdf.format(dateInBetweenRange);
    }

    @Test
    public void testHistoryForExceptionOOOAffectingDemand() throws AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), exceptionsRaised.get(0).getAlertType().getName());
        List<AlertHistory> historyList = exceptionAlertService.getHistory(exceptionsRaised.get(0).getId(), exceptionsRaised.get(0).getPropertyId());
        assertNotNull(historyList);
        assertEquals(1, historyList.size());
        AlertHistory history = historyList.get(0);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals(history.getType(), "Created");
    }

    @Test
    public void testHistoryForAddComment() throws AlertEvaluationException, InterruptedException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0.0D);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        AuthorizationService authorizationService = mock(AuthorizationService.class);
        when(authorizationService.retrieveAuthorizedProperties()).thenReturn(Arrays.asList(globalCrudService().find(Property.class, 5)));
        exceptionAlertService.setAuthorizationService(authorizationService);
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        // some time has to pass before adding the comment in order for the history ordering to work properly
        Thread.sleep(250);
        List<InfoMgrInstanceEntity> exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        exceptionAlertService.addComment(exceptionsRaised.get(0).getId(), exceptionsRaised.get(0).getPropertyId(), COMMENT);
        List<AlertHistory> historyList = exceptionAlertService.getHistory(exceptionsRaised.get(0).getId(), exceptionsRaised.get(0).getPropertyId());
        assertNotNull(historyList);
        assertEquals(2, historyList.size());
        AlertHistory history = historyList.get(0);
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals(history.getType(), "Comment Added");
        Comment comment = history.getAssociatedComment();
        assertNotNull(comment);
        assertEquals(CREATED_BY, comment.getCreatedBy());
        assertEquals(COMMENT, comment.getText());
        history = historyList.get(1);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals(history.getType(), "Created");
        exceptionsRaised = tenantCrudService().findAll(InfoMgrInstanceEntity.class);
        assertNotNull(exceptionsRaised);
        assertEquals(1, exceptionsRaised.size());
        assertEquals(exceptionsRaised.get(0).getAlertStatus().getName(), "Viewed");
    }

    @Test
    public void getAllExistingExceptionsTest() throws AlertEvaluationException {
        when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        when(configParamService.getParameterValue(AlertConfigParamName.COSTLY_OUT_OF_ORDER_THRESHOLD)).thenReturn(0d);
        when(configParamService.getParameterValue(AlertConfigParamName.OOO_THRESHOLD_FOR_EXCEPTION)).thenReturn(3);
        when(configParamService.getParameterValue("pacman.feature.isContinuousPricingEnabled","BSTN","H1")).thenReturn("false");
        when(configParamService.getValue("pacman.BSTN.H1","pacman.optimization.optimizationWindowBDE")).thenReturn("365");
        setupForOOOAffectingDemand();
        List<InformationMgrAlertConfigEntity> listConfigs = tenantCrudService().findAll(InformationMgrAlertConfigEntity.class);
        assertEquals(1, listConfigs.size());
        exceptionEvaluator.evaluateAndCreateForException(listConfigs.get(0));
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.OOORoomsAffectingDemand.toString()).parameters());
        Date exceptionDate = new LocalDate(mockDateService.getBusinessDate()).plusDays(3).toDate();
        Map<Date, InfoMgrExcepNotifEntity> allExistingExceptions = exceptionEvaluator.getAllExistingExceptions(type, listConfigs.get(0), Arrays.asList(exceptionDate));
        assertEquals(1, allExistingExceptions.size());
        assertNotNull(allExistingExceptions.get(exceptionDate).getId());
        assertEquals(AlertType.OOORoomsAffectingDemand.name(), allExistingExceptions.get(exceptionDate).getAlertType().getName());
    }

    private BigInteger getDecisionID() {
        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String businessDateStr = sdf.format(businessDate).trim();
        return getMaxDecisionIDforBusinessDate(businessDateStr);
    }

    private Map<String, List<InformationMgrAlertConfigEntity>> setupForOOOAffectingDemand() {
        return setupForOOOAffectingDemand(300,true);
    }

    private Map<String, List<InformationMgrAlertConfigEntity>> setupForOOOAffectingDemand(int remainingDemand,boolean withRateQualified) {
        StringBuilder queries = new StringBuilder();
        Date businessDate = mockDateService.getBusinessDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String businessDateStr = sdf.format(businessDate).trim();
        Date dateInBetweenRange = DateUtil.addDaysToDate(businessDate, 3);
        String dateInBetweenRangeStr = sdf.format(dateInBetweenRange).trim();
        BigInteger decisionid = getMaxDecisionIDforBusinessDate(businessDateStr);
        // insert the records into [Decision_LRV] table
        queries.append("INSERT INTO Decision_LRV(Decision_ID, Property_ID, Accom_Class_ID " + " ,Occupancy_DT, LRV,CreateDate_DTTM)VALUES(" + decisionid + ",5," + accomType.getAccomClass().getId() + ",'" + dateInBetweenRangeStr + "',1200,GETDATE())");
        queries.append("INSERT INTO Accom_Activity(Property_ID," + "Occupancy_DT,SnapShot_DTTM,Accom_type_id, Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint," + "Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue," + "File_Metadata_ID,Last_Updated_DTTM,createDate)VALUES(5,'" + dateInBetweenRangeStr + "',GETDATE()," + accomType.getId() + " ,2000,80,1100,0,50,40,0,0,10000,2000,12000,1,GETDATE(),GETDATE())");
        if(withRateQualified) {
            queries.append("INSERT INTO [dbo].[Rate_Qualified]" + " ([File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Remarks],[Rate_Code_Currency]" + "	,[Start_Date_DT],[End_Date_DT],[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" + "	,[Status_ID],[Created_DTTM])" + "  VALUES 	(1,5 ,'Qualified Test rate' ,'Test rate','Test rate','USD','2012-01-02','" + sdf.format(DateUtil.addDaysToDate(dateInBetweenRange, 20)).trim() + "',1 ,0,0 ,0,GETDATE(),1,GETDATE())");
            queries.append("INSERT INTO [dbo].[Rate_Qualified_Details]" + "  SELECT Rate_Qualified_ID," + accomType.getId() + ",'2012-01-02','" + sdf.format(DateUtil.addDaysToDate(dateInBetweenRange, 20)).trim() + "',1000 ,1000,1000,1000,1000,1000,1000, " + PacmanWorkContextHelper.getUserId() + ",GETDATE(), " + PacmanWorkContextHelper.getUserId() + ", GETDATE() from Rate_Qualified " + " where property_ID = 5 and Rate_Code_Name = 'Qualified Test rate'");
            queries.append("INSERT INTO [dbo].[Rate_Qualified]" + " ([File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Remarks],[Rate_Code_Currency]" + "	,[Start_Date_DT],[End_Date_DT],[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" + "	,[Status_ID],[Created_DTTM])" + "  VALUES 	(1,5 ,'Qualified Test rate1' ,'Test rate1','Test rate1','USD','2012-01-02','" + sdf.format(DateUtil.addDaysToDate(dateInBetweenRange, 20)).trim() + "',1 ,0,0 ,0,GETDATE(),1,GETDATE())");
            queries.append("INSERT INTO [dbo].[Rate_Qualified_Details]" + "  SELECT Rate_Qualified_ID," + accomType.getId() + ",'2012-01-02','" + sdf.format(DateUtil.addDaysToDate(dateInBetweenRange, 20)).trim() + "',20000 ,20000,20000,20000,20000,20000,20000, " + PacmanWorkContextHelper.getUserId() + ",GETDATE(), " + PacmanWorkContextHelper.getUserId() + ", GETDATE() from Rate_Qualified " + " where property_ID = 5 and Rate_Code_Name = 'Qualified Test rate1'");
        }
        tenantCrudService().executeUpdateByNativeQuery("insert into occupancy_Demand_FCST values (" + decisionid + ",5,1," + accomType.getAccomClass().getId() + ",'" + dateInBetweenRangeStr + "',3.0,1.00,0.40," + remainingDemand + ",'2023-06-12 10:16:58.583',null,null,1,null)");
        tenantCrudService().executeUpdateByNativeQuery("insert into occupancy_Demand_FCST values (" + decisionid + ",5,2," + 2 + ",'" + dateInBetweenRangeStr + "',3.0,1.00,0.40," + remainingDemand + ",'2023-06-12 10:16:58.583',null,null,1,null)");
        tenantCrudService().getEntityManager().createNativeQuery(queries.toString()).executeUpdate();
        return exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomType.getAccomClass().getCode(), MetricType.CURRENCY, dateInBetweenRange, false, tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.OOORoomsAffectingDemand.toString()).parameters())));
    }

    @SuppressWarnings("unchecked")
    private BigInteger getMaxDecisionIDforBusinessDate(String businessDateStr) {
        Query qry = tenantCrudService().getEntityManager().createNativeQuery("select dbo.ufn_get_decision_id_by_property(:propertyId,1,:businessDate)");
        qry.setParameter("propertyId", PROPERTY_ID5);
        qry.setParameter("businessDate", businessDateStr);
        List<BigInteger> currentresultList = qry.getResultList();
        return currentresultList.get(0);
    }

    /**
     * This Method will build the Exception configuration with input from different test cases.
     */
    private ExceptionConfigDTO buildExceptionConfiguration(String subType, String levelType, String sublevelType, MetricType objMetricType, Date dateInBetweenRange, boolean subLevelHasKeyword, InfoMgrTypeEntity type) {
        ExceptionConfigDTO objExceptionConfigDTO = new ExceptionConfigDTO();
        ArrayList<Integer> propertyIDList = new ArrayList<Integer>();
        propertyIDList.add(5);
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", levelType).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", subType).parameters());
        objExceptionConfigDTO.setAlertTypeEntity(type);
        objExceptionConfigDTO.setDisabled(false);
        objExceptionConfigDTO.setEndDate(new SimpleDateFormat("MM/dd/yyyy").format(dateInBetweenRange));
        objExceptionConfigDTO.setExceptionLevel(objExceptionLevelEntity);
        objExceptionConfigDTO.setExceptionSubLevel(sublevelType);
        objExceptionConfigDTO.setExceptionSubType(objExceptionSubTypeEntity);
        objExceptionConfigDTO.setFrequency(FREQUENCY);
        objExceptionConfigDTO.setPropertyIds(propertyIDList);
        objExceptionConfigDTO.setStartDate(new SimpleDateFormat("MM/dd/yyyy").format(dateInBetweenRange));
        objExceptionConfigDTO.setThresholdConstraint(">=");
        objExceptionConfigDTO.setThresholdValue(new BigDecimal(-1));
        objExceptionConfigDTO.setStatusId(1);
        objExceptionConfigDTO.setMetricType(objMetricType);
        objExceptionConfigDTO.setSubLevelHasKeyword(subLevelHasKeyword);
        return objExceptionConfigDTO;
    }

    @Test
    void testCreateAndEvaluateIndependentProductInsufficientCMPException() {
        when(configParamService.getValue(anyString(), eq(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value()))).thenReturn("1");
        when(configParamService.getParameterValue(eq(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value()), anyString(), anyString())).thenReturn("true");
        when(inSufficientCompetitorExceptionService.isInsufficientCompetitorCMPExceptionEnabled()).thenReturn(true);
        Product product1 = ProductBuilder.createIndependentProductProduct("ip1");
        Product product2 = ProductBuilder.createIndependentProductProduct("ip2");
        Product product3 = ProductBuilder.createIndependentProductProduct("ip3");
        CrudService crudService = tenantCrudService();
        crudService.save(List.of(product1, product2, product3));
        exceptionEvaluator.evaluate();
        List<InformationMgrAlertConfigEntity> listConfigs = crudService.findByNamedQuery(InformationMgrAlertConfigEntity.FIND_BY_NAME, Map.of("alertTypeName", "InsufficientCompetitorRatesForCompetitiveMarket"));
        assertNotNull(listConfigs);
        assertEquals(4, listConfigs.size());
        verify(inSufficientCompetitorExceptionService, times(4)).evaluate(any(), eq(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name()));
    }
}
