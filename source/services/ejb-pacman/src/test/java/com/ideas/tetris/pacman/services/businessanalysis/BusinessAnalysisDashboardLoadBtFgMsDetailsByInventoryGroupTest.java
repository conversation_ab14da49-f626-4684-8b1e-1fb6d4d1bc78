package com.ideas.tetris.pacman.services.businessanalysis;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BusinessAnalysisDashboardLoadBtFgMsDetailsByInventoryGroupTest extends AbstractG3JupiterTest {

    private final Integer COMPLEMENT_FG = 1;
    private final Integer WASH_FG = 7;
    private final Integer CORPORATE_FG = 2;
    private final Integer BUSINESS_TYPE_ID = 2;

    @Test
    public void shouldBeAbleToGetRoomsSold() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("45"), results.get(0)[2]);
    }

    @Test
    void shouldBeAbleToGetRoomsSold_Not_Include_DisconMS() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 0, 6, 5, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 0, 7, 5, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 5, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        setMktSegStatusDiscontinued(Collections.singletonList(4));
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + CORPORATE_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(1, results.size());
        assertEquals(new BigDecimal("15"), results.get(0)[2]);
    }

    @Test
    void shouldBeAbleToGetRoomsSold_Include_DisconMS() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 15, 6, 4, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 4, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 4, new BigDecimal("100.00"));

        setMktAccomActivityBy(startDate, 15, 6, 5, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 5, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 0, 8, 5, new BigDecimal("100.00"));

        InventoryGroup inventoryGroup = createInventoryGroup();
        setMktSegStatusDiscontinued(Arrays.asList(4, 5));
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + CORPORATE_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate + "','" + LocalDate.now() + "', '1,3'" + "," + 0);
        //THEN
        assertEquals(2, results.size());
        assertEquals(new BigDecimal("45"), results.get(0)[2]);
        assertEquals(new BigDecimal("30"), results.get(1)[2]);
    }

    @Test
    public void shouldBeAbleToGetRoomsSoldForMultiday() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 35, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 25, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("120"), results.get(0)[2]);
    }

    @Test
    public void shouldBeAbleToGetRevenue() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("300.00"), results.get(0)[6]);
    }

    @Test
    public void shouldBeAbleToGetRevenueForMultiday() {
        //GIVEN
        LocalDate startDate = LocalDate.now();
        setMktAccomActivityBy(startDate, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate, 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 15, 6, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 15, 7, 1, new BigDecimal("300.00"));
        setMktAccomActivityBy(startDate.plusDays(1), 15, 8, 1, new BigDecimal("300.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + startDate + "','" + startDate.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("1100.00"), results.get(0)[6]);
    }

    @Test
    public void shouldBeAbleToGetOccupancyForecastForPastDay() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(1) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("45"), results.get(0)[2]);
    }

    @Test
    public void shouldBeAbleToGetOccupancyForecastForPastDayForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(2), 20, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(2), 35, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(2), 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(2) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("115"), results.get(0)[2]);
    }

    @Test
    public void shouldBeAbleToGetOccupancyForecastForFutureDay() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("30.0"), results.get(0)[3]);
    }

    @Test
    public void shouldBeAbleToGetOccupancyForecastForFutureDayForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        setOccupancyFcstBy(today.plusDays(2), "200.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(2) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("60.0"), results.get(0)[3]);
    }

    @Test
    public void shouldBeAbleToGetRevenueForecastForFutureDay() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("600.00"), results.get(0)[7]);
    }

    @Test
    public void shouldBeAbleToGetRevenueForecastForFutureDayForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        setOccupancyFcstBy(today.plusDays(2), "200.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(2) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("1200.00"), results.get(0)[7]);
    }

    @Test
    public void shouldBeAbleToGetRevenueForecastForPastDay() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(1) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("300.00"), results.get(0)[6]);
    }

    @Test
    public void shouldBeAbleToGetRevenueForecastForPastDayForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(2), 15, 6, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.minusDays(2), 15, 7, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.minusDays(2), 15, 8, 1, new BigDecimal("200.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(2) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("900.00"), results.get(0)[6]);
    }

    @Test
    public void shouldBeAbleToGetADROnBooks() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today, 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today + "','" + today + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("6.67"), results.get(0)[4]);
    }

    @Test
    public void shouldBeAbleToGetADROnBooksForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today, 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today, 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today, 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.plusDays(1), 15, 6, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.plusDays(1), 15, 7, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.plusDays(1), 15, 8, 1, new BigDecimal("200.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today + "','" + today.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("10.00"), results.get(0)[4]);
    }

    @Test
    public void shouldBeAbleToGetADRForecastForFuture() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("20.00"), results.get(0)[5]);
    }

    @Test
    public void shouldBeAbleToGetADRForecastForFutureForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setOccupancyFcstBy(today.plusDays(1), "200.00", "10");
        setOccupancyFcstBy(today.plusDays(2), "300.00", "10");
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.plusDays(1) + "','" + today.plusDays(2) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("25.00"), results.get(0)[5]);
    }

    @Test
    public void shouldBeAbleToGetADRForecastForPast() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(1) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("6.67"), results.get(0)[4]);
    }

    @Test
    public void shouldGetADRForecastForPastForMultiday() {
        //GIVEN
        LocalDate today = LocalDate.now();
        setMktAccomActivityBy(today.minusDays(1), 15, 6, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 7, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(1), 15, 8, 1, new BigDecimal("100.00"));
        setMktAccomActivityBy(today.minusDays(2), 20, 6, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.minusDays(2), 20, 7, 1, new BigDecimal("200.00"));
        setMktAccomActivityBy(today.minusDays(2), 20, 8, 1, new BigDecimal("200.00"));
        InventoryGroup inventoryGroup = createInventoryGroup();
        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery("exec dbo.usp_bad_load_property_bt_fg_ms_details_for_inventorygroup 5," + inventoryGroup.getId() + ","
                + COMPLEMENT_FG + "," + BUSINESS_TYPE_ID + ",'" + today.minusDays(2) + "','" + today.minusDays(1) + "','" + LocalDate.now() + "', '1'" + "," + 0);
        //THEN
        assertEquals(new BigDecimal("8.57"), results.get(0)[4]);
    }


    private InventoryGroup createInventoryGroup() {
        InventoryGroup inventoryGroup = new InventoryGroup();
        inventoryGroup.setName("INV_GRP");
        AccomClass baseAccomClass = tenantCrudService().find(AccomClass.class, 2);
        inventoryGroup.setBaseAccomClass(baseAccomClass);
        tenantCrudService().save(inventoryGroup);
        InventoryGroupDetails inventoryGroupDetails = new InventoryGroupDetails();
        inventoryGroupDetails.setInventoryGroup(inventoryGroup);
        inventoryGroupDetails.setAccomClass(tenantCrudService().find(AccomClass.class, 2));
        tenantCrudService().save(inventoryGroupDetails);
        return inventoryGroup;
    }


    private void setOccupancyFcstBy(LocalDate occupancyDate, String revenue, String occupancyNbr) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_FCST set Occupancy_NBR = '" + occupancyNbr + "', Revenue = '" + revenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setMktAccomActivityBy(LocalDate startDate, Integer roomSold, Integer accomTypeId, int mktSegId, BigDecimal revenue) {
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Accom_Activity set Rooms_Sold = " + roomSold + ", Room_Revenue = " + revenue
                + " where Occupancy_DT = '" + startDate + "' and Accom_Type_ID = " + accomTypeId + " and Mkt_Seg_ID = " + mktSegId);
    }

    private void deleteFromMktAccomActivityBy(LocalDate occupancyDate, Integer accomTypeId, int mktSegId) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Mkt_Accom_Activity "
                + " where Occupancy_DT = '" + occupancyDate + "' and Accom_Type_ID = " + accomTypeId + " and Mkt_Seg_ID = " + mktSegId);
    }

    private void setMktSegStatusDiscontinued(List<Integer> mktSegIds) {
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set status_id = 3 where mkt_seg_id in (:mktSegIds)"
                , QueryParameter.with("mktSegIds", mktSegIds).parameters());
    }
}
