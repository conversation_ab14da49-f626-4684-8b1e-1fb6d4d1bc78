package com.ideas.tetris.pacman.services.specialevent.service;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.services.configautomation.dto.SpecialEventsUploadTemplate;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventImportDetails;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventUploadDTO;
import com.ideas.tetris.pacman.services.specialevent.entity.Frequency;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstanceNotes;
import com.ideas.tetris.pacman.services.specialevent.entity.SpecialEventType;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyListOf;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SpecialEventImportServiceDataTest extends AbstractG3JupiterTest {
    private static final String PROPERTY_NAME_PUNE = "Hilton - Pune";
    private static final String PROPERTY_NAME_PARIS = "Hilton - Paris";
    private static final Integer PROPERTY_ID_PARIS = 6;
    private static final String PROPERTY_CODE_PARIS = "H2";
    private static final Integer PROPERTY_ID_PUNE = 5;
    private static final String PROPERTY_CODE_PUNE = "H1";
    private static final String CLIENT = "BSTN";
    private static final String UID_SSO_USER = "11403";
    private WorkContextType workContext_Property_H1;
    private WorkContextType workContext_Property_H2;
    private SpecialEventService specialEventService;
    private SpecialEventImportService specialEventImportService = new SpecialEventImportService();

    private Date startDate;
    private Date endDate;

    @Mock
    SyncEventAggregatorService syncEventAggregator;
    @Mock
    private AuthorizationService authorizationService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private ClientPropertyCacheService clientPropertyCacheService;
    @Mock
    private SyncDisplayNameService syncDisplayNameService;
    @Mock
    private PropertyService propertyService;
    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Mock
    private RegulatorService regulatorService;
    @Mock
    private SpecialEventJobService specialEventJobService;

    @BeforeEach
    public void setUp() throws Exception {
        specialEventService = new SpecialEventService();
        inject(specialEventImportService, "specialEventService", specialEventService);
        inject(specialEventImportService, "pacmanConfigService", pacmanConfigParamsService);
        inject(specialEventService, "tenantCrudService", tenantCrudService());
        inject(specialEventService, "syncEventAggregatorService", syncEventAggregator);
        inject(specialEventService, "syncDisplayNameService", syncDisplayNameService);
        inject(specialEventImportService, "multiPropertyCrudService", multiPropertyCrudService);
        inject(specialEventImportService, "authorizationService", authorizationService);
        inject(specialEventImportService, "clientPropertyCacheService", clientPropertyCacheService);
        inject(specialEventImportService, "propertyService", propertyService);
        inject(specialEventImportService, "regulatorService", regulatorService);
        inject(specialEventImportService, "specialEventJobService", specialEventJobService);

        startDate = DateTime.parse("2018-12-25").toDate();
        endDate = DateTime.parse("2018-12-28").toDate();

        workContext_Property_H1 = new WorkContextType();
        workContext_Property_H1.setUserId(UID_SSO_USER);
        workContext_Property_H1.setClientCode(CLIENT);
        workContext_Property_H1.setClientId(TestClient.BSTN.getId());
        workContext_Property_H1.setPropertyId(PROPERTY_ID_PUNE);
        workContext_Property_H1.setPropertyCode(PROPERTY_CODE_PUNE);

        workContext_Property_H2 = new WorkContextType();
        workContext_Property_H2.setUserId(UID_SSO_USER);
        workContext_Property_H2.setClientCode(CLIENT);
        workContext_Property_H2.setClientId(TestClient.BSTN.getId());
        workContext_Property_H2.setPropertyId(PROPERTY_ID_PARIS);
        workContext_Property_H2.setPropertyCode(PROPERTY_CODE_PARIS);
        setWorkContext(workContext_Property_H1);

        lenient().when(authorizationService.retrieveAuthorizedProperties()).thenReturn(preparePropertyList());
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), CLIENT, PROPERTY_CODE_PUNE)).thenReturn(false);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), CLIENT, PROPERTY_CODE_PARIS)).thenReturn(true);
        lenient().doReturn(true).when(syncEventAggregator).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        lenient().when(regulatorService.isSpringTXEnableRegulatorService()).thenReturn(false);
    }

    private List<Property> preparePropertyList() {
        Property h1 = prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE);
        Property h2 = prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS);
        return Arrays.asList(h1, h2);
    }

    private Property prepareTestProperty(Integer propertyId, String propertyName, String propertyCode) {
        Client client = new Client();
        client.setCode(CLIENT);
        client.setId(TestClient.BSTN.getId());
        Property property = new Property();
        property.setClient(client);
        property.setId(propertyId);
        property.setStatus(Status.ACTIVE);
        property.setCode(propertyCode);
        property.setName(propertyName);
        return property;
    }

    @Test
    public void importSpecialEventsHappyFlow() {
        SpecialEventUploadDTO specialEventUploadDTO1 = new SpecialEventUploadDTO(CLIENT, PROPERTY_NAME_PUNE, PROPERTY_ID_PUNE, PROPERTY_CODE_PUNE, "Convention", "Christmas", startDate, endDate, 2, 3, 1, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);

        final List<PropertySpecialEvent> propertySpecialEventsH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(6, propertySpecialEventsH1Before.size());

        final List<PropertySpecialEventInstance> propertySpecialEventInstancesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(21, propertySpecialEventInstancesH1Before.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(4, propertySpecialEventInstanceNotesH1Before.size());

        final List<Frequency> frequenciesH1Before = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        assertEquals(5, frequenciesH1Before.size());

        final List<String> specialEventImportResult = specialEventImportService.importSpecialEvents(true, Collections.singletonList(specialEventUploadDTO1));
        assertNotNull(specialEventImportResult);
        assertEquals(0, specialEventImportResult.size());

        final List<PropertySpecialEvent> propertySpecialEventsH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.FIND_ALL, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventsH1After.size());

        final List<PropertySpecialEventInstance> propertySpecialEventInstancesH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventInstancesH1After.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(0, propertySpecialEventInstanceNotesH1After.size());

        final List<Frequency> frequenciesH1After = tenantCrudService().findAll(Frequency.class);
        assertEquals(5, frequenciesH1After.size());

    }

    @Test
    public void testImportSpecialEventsWhenExceptionIsEncounteredWhenDisplayPropertyCodeIsTrue() {
        SpecialEventService specialEventServiceMock = mock(SpecialEventService.class);
        inject(specialEventImportService, "specialEventService", specialEventServiceMock);
        endDate = DateTime.parse("2019-02-26").toDate();
        SpecialEventUploadDTO specialEventUploadDTO1 = new SpecialEventUploadDTO(CLIENT, PROPERTY_NAME_PUNE, PROPERTY_ID_PUNE, PROPERTY_CODE_PUNE, "Convention", "Christmas", startDate, endDate, 2, 3, 1, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
        doThrow(new TetrisException("TestException")).when(specialEventServiceMock).deleteAndCreateSpecialEvents(eq(PROPERTY_ID_PUNE), anyListOf(SpecialEventUploadDTO.class));
        final List<String> specialEventImportResult = specialEventImportService.importSpecialEvents(true, Collections.singletonList(specialEventUploadDTO1));
        assertNotNull(specialEventImportResult);
        assertEquals(1, specialEventImportResult.size());
        assertEquals(PROPERTY_CODE_PUNE, specialEventImportResult.get(0));
    }

    @Test
    public void testImportSpecialEventsWhenExceptionIsEncounteredWhenDisplayPropertyCodeIsFalse() {
        SpecialEventService specialEventServiceMock = mock(SpecialEventService.class);
        inject(specialEventImportService, "specialEventService", specialEventServiceMock);
        endDate = DateTime.parse("2019-02-26").toDate();
        SpecialEventUploadDTO specialEventUploadDTO1 = new SpecialEventUploadDTO(CLIENT, PROPERTY_NAME_PUNE, PROPERTY_ID_PUNE, PROPERTY_CODE_PUNE, "Convention", "Christmas", startDate, endDate, 2, 3, 1, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);

        doThrow(new TetrisException("TestException")).when(specialEventServiceMock).deleteAndCreateSpecialEvents(eq(PROPERTY_ID_PUNE), anyListOf(SpecialEventUploadDTO.class));
        final List<String> specialEventImportResult = specialEventImportService.importSpecialEvents(false, Collections.singletonList(specialEventUploadDTO1));
        assertNotNull(specialEventImportResult);
        assertEquals(1, specialEventImportResult.size());
        assertEquals(PROPERTY_NAME_PUNE, specialEventImportResult.get(0));
    }

    @Test
    public void initSpecialEventImportDetailsHappyFlow() {
        inject(specialEventImportService, "multiPropertyCrudService", multiPropertyCrudService());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE)).thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        final SpecialEventImportDetails specialEventImportDetails = specialEventImportService.initSpecialEventImportDetails(CLIENT, Arrays.asList(PROPERTY_CODE_PUNE, PROPERTY_CODE_PARIS));
        assertNotNull(specialEventImportDetails, "specialEventImportDetails should be initialized properly");
        assertSpecialEventImportDetailsForPropertyCode(specialEventImportDetails);
    }

    @Test
    public void importPropertySpecialEventsHappyFlow() {
        SpecialEventUploadDTO specialEventUploadDTO = new SpecialEventUploadDTO(CLIENT, PROPERTY_NAME_PUNE, PROPERTY_ID_PUNE, PROPERTY_CODE_PUNE, "Convention", "Christmas", startDate, endDate, 2, 3, 1, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);

        assertSpecialEventsForH1Before();

        specialEventImportService.importPropertySpecialEvents(Collections.singletonList(specialEventUploadDTO));

        final List<PropertySpecialEvent> propertySpecialEventsH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventsH1After.size());

        final List<PropertySpecialEventInstance> propertySpecialEventInstancesH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(1, propertySpecialEventInstancesH1After.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1After
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(0, propertySpecialEventInstanceNotesH1After.size());

        final List<Frequency> frequenciesH1After = tenantCrudService().findAll(Frequency.class);
        assertEquals(5, frequenciesH1After.size());
    }

    @Test
    public void importPropertySpecialEventsEmptyList() {
        specialEventImportService.importPropertySpecialEvents(new ArrayList<>());
        assertSpecialEventsForH1Before();
    }

    @Test
    public void getPropertiesInfoShouldUsePropertyCodesToFetchProperties() {
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE)).thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        final List<Property> properties = specialEventImportService.fetchPropertiesByClientCodeAndPropertyCode(CLIENT, Arrays.asList(PROPERTY_CODE_PUNE, PROPERTY_CODE_PARIS));
        assertEquals(2, properties.size());
        assertEquals(PROPERTY_CODE_PUNE, properties.get(0).getCode());
        assertEquals(PROPERTY_CODE_PARIS, properties.get(1).getCode());
    }

    @Test
    public void fetchPropertiesByClientCodeAndPropertyShouldNotReturnInvalidPropertiesInfo() {
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE)).thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(clientPropertyCacheService.getProperty(CLIENT, "InvalidPropertyCode")).thenReturn(null);

        final List<Property> properties = specialEventImportService.fetchPropertiesByClientCodeAndPropertyCode(CLIENT, Arrays.asList(PROPERTY_CODE_PUNE, PROPERTY_CODE_PARIS, "InvalidPropertyCode"));
        assertEquals(2, properties.size());
        assertEquals(PROPERTY_CODE_PUNE, properties.get(0).getCode());
        assertEquals(PROPERTY_CODE_PARIS, properties.get(1).getCode());

    }

    @Test
    void testUploadSpecialEventsUsingTemplate_HappyPath() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, null, null, "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Festival", "Diwali", "01-Oct-2023",
                "05-Oct-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        when(regulatorService.isPropertyReadOnly(CLIENT, PROPERTY_CODE_PUNE)).thenReturn(false);

        specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
        verify(regulatorService).isPropertyReadOnly(CLIENT, PROPERTY_CODE_PUNE);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_StartDateIsGreaterThanEndDateException() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2022", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("End date should be greater than or equal to Start date.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_InvalidClientCodeException() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT + "S", PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Client Code on the template must match the Client Code in the exported template. Correct the Client Code.",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_PreEventDaysMoreThan14() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 15, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates));
        assertEquals("Pre event days must be between 0 and 14", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_PreEventDaysLessThanZero() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", -1, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Pre event days must be between 0 and 14",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_PostEventDaysMoreThan14() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 16, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Post event days must be between 0 and 14",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_SpecialEventsCannotExceed60Days() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2024", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Special Events cannot exceed 60 days including Pre-Event and Post-Event Days. Modify the dates you selected.",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_PostEventDaysLessThanZero() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, -3, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Post event days must be between 0 and 14",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_InvalidEventName() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival~*",
                "01-Jul-2023", "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Invalid Event Name.Film Festival~*",
                exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidatePropertyName() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE + "S", "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Property Name on the template must match the Property Name in the exported template. " +
                "Correct the Property Name.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventCategoryName() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention1", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Invalid Event Category. The Event Category must match a configured Special Event Category in G3 RMS.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_EventsAreNotConfiguredInG3RMS() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(new ArrayList());

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("Invalid Event Category. The Event Category must match a configured Special Event Category in G3 RMS.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_GuestRoomAndFunctionalSpaceEventBothAreTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "Yes", "Yes", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        when(regulatorService.isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(false);

        specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
        verify(regulatorService).isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_ValidateEventForecastData_OnlyFunctionalSpaceEventIsTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "No", "Yes", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        when(regulatorService.isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(false);

        specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
        verify(regulatorService).isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_ValidateEventForecastData_OnlyGuestRoomEventIsTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "Yes", "No", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        when(regulatorService.isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(false);

        specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
        verify(regulatorService).isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_GuestRoomAndFunctionalSpaceEventBothAreFalse() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "No", "No", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Impact Forecast selection is missing or invalid.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_ImpactForecastIsOnlyInformational_OnlyGuestRoomEventIsTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "Yes", "No", "FYI"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Impact Forecast selection is missing or invalid.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_ImpactForecastIsOnlyInformational_OnlyFunctionalSpaceEventIsTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "No", "Yes", "FYI"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Impact Forecast selection is missing or invalid.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateEventForecastData_ImpactForecastIsOnlyInformational_BothGuestRoomAndFunctionalSpaceEventAreTrue() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "Yes", "Yes", "FYI"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("The Impact Forecast selection is missing or invalid.", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    public void testSpecialEventsUploadTemplate() {
        SpecialEventsUploadTemplate specialEventsUploadTemplate = new SpecialEventsUploadTemplate();
        specialEventsUploadTemplate.setClientCode(CLIENT);
        specialEventsUploadTemplate.setPropertyCode(PROPERTY_CODE_PARIS);
        specialEventsUploadTemplate.setPropertyName(PROPERTY_NAME_PARIS);
        specialEventsUploadTemplate.setEventCategory("Festival");
        specialEventsUploadTemplate.setEventName("Diwali");
        specialEventsUploadTemplate.setPreEventDays(5);
        specialEventsUploadTemplate.setPostEventDays(5);
        specialEventsUploadTemplate.setImpactForecast("FYI");
        specialEventsUploadTemplate.setGuestRoomEvent("Yes");
        specialEventsUploadTemplate.setFunctionSpaceEvent("Yes");
        Assertions.assertEquals(CLIENT, specialEventsUploadTemplate.getClientCode());
        Assertions.assertEquals(PROPERTY_CODE_PARIS, specialEventsUploadTemplate.getPropertyCode());
        Assertions.assertEquals(PROPERTY_NAME_PARIS, specialEventsUploadTemplate.getPropertyName());
        Assertions.assertEquals("Festival", specialEventsUploadTemplate.getEventCategory());
        Assertions.assertEquals("Diwali", specialEventsUploadTemplate.getEventName());
        Assertions.assertEquals(5, specialEventsUploadTemplate.getPreEventDays());
        Assertions.assertEquals(5, specialEventsUploadTemplate.getPostEventDays());
        Assertions.assertEquals("FYI", specialEventsUploadTemplate.getImpactForecast());
        Assertions.assertEquals("Yes", specialEventsUploadTemplate.getFunctionSpaceEvent());
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_SpecialEventsOverlapping() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, null, null, "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "03-Jul-2023",
                "05-Jul-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Instance is overlapping. Convention - Film Festival", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateCaseSensitiveDuplicateSpecialEventName() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, "No", "Yes", "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "10-Jul-2023",
                "15-Jul-2023", 0, 0, "No", "Yes", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Duplicate special event names provided. [Film Festival, film Festival]", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateGuestRoomForecastSelection() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, "No", "Yes", "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "10-Jul-2023",
                "15-Jul-2023", 0, 0, "Yes", "No", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Missing or invalid guest room forecast selection. Film Festival - [false, true]", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateFunctionSpaceForecastSelection() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, "Yes", "Yes", "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "10-Jul-2023",
                "15-Jul-2023", 0, 0, "Yes", "No", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Missing or invalid function space forecast selection. Film Festival - [false, true]", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_ValidateImpactForecastSelection() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, "No", "Yes", "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival", "10-Jul-2023",
                "15-Jul-2023", 0, 0, "No", "Yes", null));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Missing or invalid impact forecast selection. Film Festival - [null, 1]", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_PropertyIsReadOnly() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PARIS, PROPERTY_NAME_PARIS, "Convention", "Film Festival",
                "01-Jul-2023", "05-Jul-2023", 0, 0, "Yes", "No", "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PARIS))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PARIS, PROPERTY_NAME_PARIS, PROPERTY_CODE_PARIS));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        when(regulatorService.isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS)).thenReturn(true);

        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);
        });
        assertEquals("Some of the properties from request currently in read only mode. " +
                "Try enabling toggle - pacman.preProduction.UseAsyncSpecialEventUpload", exception.getBaseMessage());
        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PARIS), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PARIS);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PARIS, SpecialEventType.ALL, null);
        verify(regulatorService).isPropertyReadOnly(CLIENT, PROPERTY_CODE_PARIS);
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_UseAsyncSpecialEventUpload() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "05-Jul-2023", 0, 0, null, null, "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Festival", "Diwali", "01-Oct-2023",
                "05-Oct-2023", 0, 0, null, null, "Yes"));
        when(propertyService.getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2))
                .thenReturn(getMockPropertiesByVirtualPropertyDisplayCodesAndClientId());
        when(clientPropertyCacheService.getProperty(CLIENT, PROPERTY_CODE_PUNE))
                .thenReturn(prepareTestProperty(PROPERTY_ID_PUNE, PROPERTY_NAME_PUNE, PROPERTY_CODE_PUNE));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null))
                .thenReturn(mockSpecialEventTypes("Convention", "Festival"));
        lenient().when(pacmanConfigParamsService.getParameterValueByClientLevel(PreProductionConfigParamName.USE_ASYNC_SPECIAL_EVENT_UPLOAD.value())).thenReturn("true");
        doNothing().when(specialEventJobService).startMultiPropertySpecialEventUploadJob(Mockito.anyMap(), Mockito.anyString());

        specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        verify(propertyService).getPropertiesByVirtualPropertyDisplayCodesAndClientId(List.of(PROPERTY_CODE_PUNE), 2);
        verify(clientPropertyCacheService).getProperty(CLIENT, PROPERTY_CODE_PUNE);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID_PUNE, SpecialEventType.ALL, null);
        verify(specialEventJobService).startMultiPropertySpecialEventUploadJob(Mockito.anyMap(), Mockito.anyString());
    }

    @Test
    void testUploadSpecialEventsUsingTemplate_DateAreNotInProperFormat() {
        List<SpecialEventsUploadTemplate> specialEventsUploadTemplates = new ArrayList<>();
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Convention", "Film Festival", "01-Jul-2023",
                "2023-05-06", 0, 0, null, null, "Yes"));
        specialEventsUploadTemplates.add(mockSpecialEventsUploadTemplate(CLIENT, PROPERTY_CODE_PUNE, PROPERTY_NAME_PUNE, "Festival", "Diwali", "01-Oct-2023",
                "05-Oct-2023", 0, 0, null, null, "Yes"));
        TetrisException exception = Assertions.assertThrows(TetrisException.class, () -> {
            specialEventImportService.uploadSpecialEventsUsingTemplate(specialEventsUploadTemplates);

        });
        assertEquals("BAD Request. Date is not provided in proper format. Use dd-MMM-yyyy format. Unparseable date: \"2023-05-06\"",
                exception.getBaseMessage());
    }

    private List<Pair<String, String>> getMockPropertiesByVirtualPropertyDisplayCodesAndClientId() {
        String[] array = new String[2];
        array[0] = PROPERTY_CODE_PUNE;
        array[1] = "2";
        List<String[]> rows = new ArrayList<>();
        rows.add(array);
        return rows.stream().map(row -> new Pair<>(row[0], row[1])).collect(Collectors.toList());
    }

    private List<SpecialEventType> mockSpecialEventTypes(String name1, String name2) {
        List<SpecialEventType> specialEventTypes = new ArrayList<>();
        specialEventTypes.add(mockPropertySpecialEventType(1, name1));
        specialEventTypes.add(mockPropertySpecialEventType(2, name2));
        return specialEventTypes;
    }

    private SpecialEventType mockPropertySpecialEventType(int id, String name) {
        SpecialEventType specialEventType = new SpecialEventType();
        specialEventType.setId(id);
        specialEventType.setName(name);
        specialEventType.setStatusId(1);
        specialEventType.setDescription(name);
        specialEventType.setColorCode(null);
        return specialEventType;
    }

    private SpecialEventsUploadTemplate mockSpecialEventsUploadTemplate(String ClientCode, String propertyCode, String propertyName,
                                                                        String categoryName, String eventName, String startDate,
                                                                        String endDate, Integer preEventDays, Integer postEventDays,
                                                                        String isGuestRoomEvent, String isFunctionSpaceEvent, String impactForecast) {
        SpecialEventsUploadTemplate specialEventsUploadTemplate = new SpecialEventsUploadTemplate();
        specialEventsUploadTemplate.setClientCode(ClientCode);
        specialEventsUploadTemplate.setPropertyCode(propertyCode);
        specialEventsUploadTemplate.setPropertyName(propertyName);
        specialEventsUploadTemplate.setEventCategory(categoryName);
        specialEventsUploadTemplate.setEventName(eventName);
        specialEventsUploadTemplate.setStartDate(startDate);
        specialEventsUploadTemplate.setEndDate(endDate);
        specialEventsUploadTemplate.setPreEventDays(preEventDays);
        specialEventsUploadTemplate.setPostEventDays(postEventDays);
        specialEventsUploadTemplate.setImpactForecast(impactForecast);
        specialEventsUploadTemplate.setGuestRoomEvent(isGuestRoomEvent);
        specialEventsUploadTemplate.setFunctionSpaceEvent(isFunctionSpaceEvent);
        return specialEventsUploadTemplate;
    }

    private void assertSpecialEventsForH1Before() {
        final List<PropertySpecialEvent> propertySpecialEventsH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(4, propertySpecialEventsH1Before.size());

        final List<PropertySpecialEventInstance> propertySpecialEventInstancesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstance.BY_PROPERTY, MapBuilder.with("propertyId", PROPERTY_ID_PUNE).get());
        assertEquals(21, propertySpecialEventInstancesH1Before.size());

        final List<PropertySpecialEventInstanceNotes> propertySpecialEventInstanceNotesH1Before
                = tenantCrudService().findByNamedQuery(PropertySpecialEventInstanceNotes.BY_PROPERTY);
        assertEquals(4, propertySpecialEventInstanceNotesH1Before.size());

        final List<Frequency> frequenciesH1Before = tenantCrudService().findByNamedQuery(Frequency.BY_PROPERTY);
        assertEquals(5, frequenciesH1Before.size());
    }

    private void assertPropertyCodeFunctionSpaceEnabledToggleMap(SpecialEventImportDetails specialEventImportDetails) {
        final Map<String, Boolean> propertyCodeFunctionSpaceEnabledToggleMap = specialEventImportDetails.getPropertyCodeFunctionSpaceEnabledToggleMap();
        assertTrue(propertyCodeFunctionSpaceEnabledToggleMap.containsKey(PROPERTY_CODE_PUNE));
        assertFalse(propertyCodeFunctionSpaceEnabledToggleMap.get(PROPERTY_CODE_PUNE));
        assertTrue(propertyCodeFunctionSpaceEnabledToggleMap.containsKey(PROPERTY_CODE_PARIS));
        assertTrue(propertyCodeFunctionSpaceEnabledToggleMap.get(PROPERTY_CODE_PARIS));
    }

    private void assertPropertyCodeIdMap(SpecialEventImportDetails specialEventImportDetails) {
        final Map<String, Integer> propertyCodeIdMap = specialEventImportDetails.getPropertyCodeIdMap();
        assertEquals(2, propertyCodeIdMap.size());
        assertTrue(propertyCodeIdMap.containsKey(PROPERTY_CODE_PUNE));
        assertEquals(PROPERTY_ID_PUNE, propertyCodeIdMap.get(PROPERTY_CODE_PUNE));
        assertTrue(propertyCodeIdMap.containsKey(PROPERTY_CODE_PARIS));
        assertEquals(PROPERTY_ID_PARIS, propertyCodeIdMap.get(PROPERTY_CODE_PARIS));
    }

    private void assertPropertyCodeList(SpecialEventImportDetails specialEventImportDetails) {
        final List<String> propertyCodeList = specialEventImportDetails.getPropertyCodeList();
        assertEquals(2, propertyCodeList.size());
        assertTrue(propertyCodeList.contains(PROPERTY_CODE_PUNE));
        assertTrue(propertyCodeList.contains(PROPERTY_CODE_PARIS));
    }

    private void assertSpecialEventImportDetailsForPropertyCode(SpecialEventImportDetails specialEventImportDetails) {
        assertEquals(CLIENT, specialEventImportDetails.getClientCode());
        final Map<String, List<String>> propertyCodeEventTypeNamesMap = specialEventImportDetails.getPropertyCodeEventTypeNamesMap();
        assertEquals(2, propertyCodeEventTypeNamesMap.size());
        assertTrue(propertyCodeEventTypeNamesMap.containsKey(PROPERTY_CODE_PUNE));
        assertEquals(6, propertyCodeEventTypeNamesMap.get(PROPERTY_CODE_PUNE).size());
        assertTrue(propertyCodeEventTypeNamesMap.containsKey(PROPERTY_CODE_PARIS));
        assertEquals(6, propertyCodeEventTypeNamesMap.get(PROPERTY_CODE_PARIS).size());
        assertPropertyCodeList(specialEventImportDetails);
        assertPropertyCodeIdMap(specialEventImportDetails);
        assertPropertyCodeNameMap(specialEventImportDetails);
        assertPropertyCodeFunctionSpaceEnabledToggleMap(specialEventImportDetails);
    }

    private void assertPropertyCodeNameMap(SpecialEventImportDetails specialEventImportDetails) {
        final Map<String, String> propertyCodeNameMap = specialEventImportDetails.getPropertyCodeNameMap();
        assertEquals(2, propertyCodeNameMap.size());
        assertTrue(propertyCodeNameMap.containsKey(PROPERTY_CODE_PUNE));
        assertEquals(PROPERTY_NAME_PUNE, propertyCodeNameMap.get(PROPERTY_CODE_PUNE));
        assertTrue(propertyCodeNameMap.containsKey(PROPERTY_CODE_PARIS));
        assertEquals(PROPERTY_NAME_PARIS, propertyCodeNameMap.get(PROPERTY_CODE_PARIS));
    }
}