package com.ideas.tetris.pacman.services.reportsquery.dataextractionreport;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DataExtractionReportDatAccomTypeSTLYTest extends AbstractG3JupiterTest {

    public static final String dataValidtionForAccomTypeDouble = "validate data for Accom Type Double";
    public static final String dataValidationForAccTypeSUITE = "validate data for Accom Type SUITE";
    public static final String dataValidationForAccTypeQueen = "validate data for Accom Type Queen";
    public static final String validateDataForAccTypeKing = "validate data for Accom Type King";
    public static final Double LRVRC = 100.56123;
    public static final Double LRVRT = 33.56123;
    private LocalDate snapShotDate;
    private LocalDate snapShotDatePlus1;
    private LocalDate snapShotDateLastYearDOW;
    private LocalDate snapShotDateLastYearDOWPlus1;
    private LocalDate businessDateLastYearDOW;

    private static final int propertyID = 6;
    private static final int recordTypeId = 3;
    private static final int processStatusId = 13;
    private int isRolling = 0;
    private BigDecimal ROOMS_SOLD = BigDecimal.valueOf(17);
    private final BigDecimal ROOM_SOLD_PLUS_6 = ROOMS_SOLD.add(new BigDecimal(6));
    private final BigDecimal ROOM_SOLD_PLUS_2 = ROOMS_SOLD.add(new BigDecimal(2));
    private final BigDecimal ROOM_SOLD_PLUS_3 = ROOMS_SOLD.add(new BigDecimal(3));
    private final BigDecimal ROOM_SOLD_PLUS_4 = ROOMS_SOLD.add(new BigDecimal(4));
    private BigDecimal ROOMS_REVENUE = BigDecimal.valueOf(193.15647);
    private final BigDecimal ROOM_REVENUE_PLUS_119 = ROOMS_REVENUE.add(new BigDecimal(119));
    private final BigDecimal ROOM_REVENUE_PLUS_120 = ROOMS_REVENUE.add(new BigDecimal(120));
    private final BigDecimal ROOM_REVENUE_PLUS_121 = ROOMS_REVENUE.add(new BigDecimal(121));

    private static final int AT_NINE = 9;
    private static final int AT_TEN = 10;
    private static final int AT_ELEVEN = 11;
    private static final int AT_TWELVE = 12;

    private static final String AT_NINE_NAME = "King";
    private static final String AT_TEN_NAME = "SUITE";
    private static final String AT_ELEVEN_NAME = "Queen";
    private static final String AT_TWELVE_NAME = "Double";

    private final int accomClassIdSix = 6;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        snapShotDate = getLocalDate();
        snapShotDatePlus1 = snapShotDate.plusDays(1);
        LocalDate businessDate = snapShotDate.plusDays(-1);
        snapShotDateLastYearDOW = getdowAdjustedDate(snapShotDate);
        snapShotDateLastYearDOWPlus1 = snapShotDateLastYearDOW.plusDays(1);
        businessDateLastYearDOW = getdowAdjustedDate(businessDate);
        createTestData();
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithFutureDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDatePlus1.toString() + "'," + isRolling + ",null,null,1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDate, snapShotDateLastYearDOW, 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate, snapShotDateLastYearDOW, 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDate, snapShotDateLastYearDOW, 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate, snapShotDateLastYearDOW, 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 7, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithFutureDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDatePlus1.toString() + "'," + isRolling + ",'TODAY','TODAY+1',1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDate, snapShotDateLastYearDOW, 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate, snapShotDateLastYearDOW, 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDate, snapShotDateLastYearDOW, 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate, snapShotDateLastYearDOW, 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 7, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithPartiallyinPastAndPartiallyInFutureDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "'," + isRolling + ",null,null,1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDate, snapShotDateLastYearDOW, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate, snapShotDateLastYearDOW, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDate, snapShotDateLastYearDOW, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate, snapShotDateLastYearDOW, 7, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithPartiallyinPastAndPartiallyInFutureDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "'," + isRolling + ",'TODAY-1','TODAY',1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDate, snapShotDateLastYearDOW, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate, snapShotDateLastYearDOW, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDate, snapShotDateLastYearDOW, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate, snapShotDateLastYearDOW, 7, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithPastDateRangeForStaticDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-2) + "','" + snapShotDate.plusDays(-1) + "'," + isRolling + ",null,null,1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 5, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 7, reportData);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithPastDateRangeForRollingDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-2) + "','" + snapShotDate.plusDays(-1) + "'," + isRolling + ",'TODAY-2','TODAY-1',1,0,0,0,0,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-2), snapShotDateLastYearDOW.plusDays(-2), 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_6, ROOM_REVENUE_PLUS_121, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 5, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_120, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 7, reportData);
    }

    @Test
    public void lrvRCRtdataValidate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDate.plusDays(1) + "'," + isRolling + ",'','',1,0,0,0,0,0,0,0");
        int rowNumber = 2;
        assertEquals(LRVRC, ((BigDecimal) reportData.get(rowNumber)[56]).doubleValue(), 0.001, "STN room class");
        assertEquals(LRVRT, ((BigDecimal) reportData.get(rowNumber)[57]).doubleValue(), 0.001, AT_ELEVEN_NAME);
    }

    @Test
    public void shouldValidateDataExtractionReportSTLYWithPseudoRTs() {
        isRolling = 0;
        tenantCrudService().executeUpdateByNativeQuery("update accom_type set status_id = 6, display_status_id = 4");
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate + "','" + snapShotDatePlus1.toString() + "'," + isRolling + ",null,null,1,0,0,0,1,0,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDate, snapShotDateLastYearDOW, 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDate, snapShotDateLastYearDOW, 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDate, snapShotDateLastYearDOW, 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDate, snapShotDateLastYearDOW, 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, ROOMS_SOLD, ROOMS_REVENUE, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_ELEVEN_NAME, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119, snapShotDatePlus1, snapShotDateLastYearDOWPlus1, 7, reportData);
    }

    @Test
    @Disabled
    public void shouldValidateDataExtractionReportSTLYAtBusinessViewLevelWithPartiallyinPastAndPartiallyInFutureDateRangeForExcludedMS() {
        isRolling = 1;
        excludeMktSegments();
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_dataextraction_report_rt_thisyear_lastyear " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + snapShotDate.plusDays(-1) + "','" + snapShotDate + "'," + isRolling + ",'TODAY-1','TODAY',1,0,0,0,0,1,0,0");

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, BigDecimal.valueOf(9), BigDecimal.valueOf(-405.84353), snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 0, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, BigDecimal.valueOf(4), BigDecimal.valueOf(-526.84353), snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 1, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, BigDecimal.valueOf(7), BigDecimal.valueOf(-406.84353), snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 2, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, BigDecimal.valueOf(5), BigDecimal.valueOf(-407.84353), snapShotDate.plusDays(-1), snapShotDateLastYearDOW.plusDays(-1), 3, reportData);

        assertValueForGivenOccupancyDate(dataValidtionForAccomTypeDouble, AT_TWELVE_NAME, BigDecimal.valueOf(21), BigDecimal.valueOf(314.15647), snapShotDate, snapShotDateLastYearDOW, 4, reportData);
        assertValueForGivenOccupancyDate(validateDataForAccTypeKing, AT_NINE_NAME, BigDecimal.valueOf(17), BigDecimal.valueOf(193.15647), snapShotDate, snapShotDateLastYearDOW, 5, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeQueen, AT_ELEVEN_NAME, BigDecimal.valueOf(20), BigDecimal.valueOf(313.15647), snapShotDate, snapShotDateLastYearDOW, 6, reportData);
        assertValueForGivenOccupancyDate(dataValidationForAccTypeSUITE, AT_TEN_NAME, BigDecimal.valueOf(19), BigDecimal.valueOf(312.15647), snapShotDate, snapShotDateLastYearDOW, 7, reportData);

        unExcludeMktSegments();
    }

    private void excludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 1 WHERE Mkt_Seg_ID IN (" + 7 + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    private void unExcludeMktSegments() {
        final String updateQuery = "UPDATE [Mkt_Seg] SET [Exclude_CompHouse_Data_Display] = 0 WHERE Mkt_Seg_ID  IN (" + 7 + ");";
        tenantCrudService().executeUpdateByNativeQuery(updateQuery);
    }

    private void assertValueForGivenOccupancyDate(String Level, String accomTypeName, BigDecimal roomSold, BigDecimal roomRevenue, LocalDate date1, LocalDate date2, int rowNumber, List<Object[]> reportData) {
        assertEquals(roomSold, (reportData.get(rowNumber)[50]), Level);
        assertEquals(roomRevenue, (reportData.get(rowNumber)[51]), Level);
        assertEquals(date1, LocalDate.parse(reportData.get(rowNumber)[1].toString()), Level);
        assertEquals(accomTypeName, (reportData.get(rowNumber)[7]), Level);
        assertEquals(date2, LocalDate.parse(reportData.get(rowNumber)[3].toString()), Level);
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        UpdateMainTableAtMkt(insertQuery, AT_NINE, ROOMS_REVENUE, ROOMS_SOLD);
        UpdateMainTableAtMkt(insertQuery, AT_TEN, ROOM_REVENUE_PLUS_119, ROOM_SOLD_PLUS_2);
        UpdateMainTableAtMkt(insertQuery, AT_ELEVEN, ROOM_REVENUE_PLUS_120, ROOM_SOLD_PLUS_4);
        UpdateMainTableAtMkt(insertQuery, AT_TWELVE, ROOM_REVENUE_PLUS_121, ROOM_SOLD_PLUS_6);
        updatePaceMkt(insertQuery, snapShotDateLastYearDOW);
        updatePaceMkt(insertQuery, snapShotDateLastYearDOWPlus1);
        uppdateLRVdataRC(insertQuery, accomClassIdSix, LRVRC);
        uppdateLRVdataRT(insertQuery, AT_ELEVEN, LRVRT);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void uppdateLRVdataRC(StringBuilder insertQuery, int accomClassId, double lrv) {
        insertQuery.append(" update Decision_LRV set lrv = " + lrv + " where accom_class_id = " + accomClassId + " and Occupancy_DT between '" + snapShotDate.toString() + "' and '" + snapShotDate.plusDays(7).toString() + "'");
    }

    private void uppdateLRVdataRT(StringBuilder insertQuery, int accomTypeId, double lrv) {
        IntStream.range(0, 7).forEach(num -> {
            insertQuery.append(" INSERT INTO Decision_LRV_AT ( Decision_ID,Property_ID,Accom_Type_ID,Occupancy_DT,LRV,CreateDate_DTTM)VALUES (\n" +
                    "           1," + propertyID + "," + accomTypeId + ",'" + snapShotDate.plusDays(num).toString() + "'," + lrv + ",GETDATE());");

        });
    }

    private void updatePaceMkt(StringBuilder insertQuery, LocalDate occupancyDate) {
        UpdatePaceTableAtMkt(insertQuery, occupancyDate, AT_NINE, ROOMS_SOLD, ROOMS_REVENUE);
        UpdatePaceTableAtMkt(insertQuery, occupancyDate, AT_TEN, ROOM_SOLD_PLUS_2, ROOM_REVENUE_PLUS_119);
        UpdatePaceTableAtMkt(insertQuery, occupancyDate, AT_ELEVEN, ROOM_SOLD_PLUS_3, ROOM_REVENUE_PLUS_120);
        UpdatePaceTableAtMkt(insertQuery, occupancyDate, AT_TWELVE, ROOM_SOLD_PLUS_4, ROOM_REVENUE_PLUS_121);
    }


    private void UpdatePaceTableAtMkt(StringBuilder insertQuery, LocalDate occupancyDate, int accTypeID, BigDecimal roomSold, BigDecimal roomRevenue) {
        insertQuery.append(" update PACE_Accom_Activity set Rooms_Sold = " + roomSold + ",Room_Revenue = " + roomRevenue + "");
        insertQuery.append(" where Property_ID = " + propertyID + " and Occupancy_DT='" + occupancyDate + "' and Business_Day_End_DT = '");
        insertQuery.append(businessDateLastYearDOW + "' and Accom_Type_ID = " + accTypeID);
    }

    private void UpdateMainTableAtMkt(StringBuilder insertQuery, int mktSegId, BigDecimal roomRevenueMkt, BigDecimal roomSold) {
        insertQuery.append(" update Accom_Activity set Rooms_Sold=" + roomSold + ", " +
                "Room_Revenue=" + roomRevenueMkt + " " +
                "where Occupancy_DT between '" + snapShotDateLastYearDOW.plusDays(-2) + "' " +
                "and '" + snapShotDateLastYearDOW.plusDays(-1) + "' and Accom_Type_ID=" + mktSegId + " ");
    }

    private LocalDate getLocalDate() {
        List testDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + "," + recordTypeId + "," + processStatusId + ")");
        String testDate = testDates.get(0).toString();
        return LocalDate.parse(testDate);
    }

    private LocalDate getdowAdjustedDate(LocalDate date) {
        List testDates = tenantCrudService().findByNativeQuery("  select DATEADD(WEEK,-52,'" + date + "') ");
        DateFormat Formatter = new SimpleDateFormat("yyyy-MM-dd");
        return LocalDate.parse(Formatter.format(testDates.get(0)));
    }


}

