package com.ideas.tetris.pacman.services.linkedsrp.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.linkedsrp.dto.LinkedSrpMappingsDto;
import com.ideas.tetris.pacman.services.linkedsrp.entity.LinkedSRPMappings;
import com.ideas.tetris.pacman.services.linkedsrp.repository.LinkedSRPMappingsRepository;
import com.ideas.tetris.pacman.services.linkedsrp.repository.RateQualifiedFixedRepository;
import com.ideas.tetris.pacman.services.linkedsrp.repository.SemiYieldableRateDetailsRepository;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.SemiYieldableRateDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.repository.RateQualifiedFixedDetailsStageRepository;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.pacman.services.ratepopulation.RateDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixed;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetailsStg;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class LinkedSRPYieldAsSeasonsRateCalculationForStreamServiceTest extends AbstractG3JupiterTest {
    public static final BigDecimal SUNDAY = BigDecimal.valueOf(300.0);
    public static final BigDecimal SUNDAY_10 = BigDecimal.valueOf(330.0);
    public static final BigDecimal SUNDAY_20 = BigDecimal.valueOf(363.0);
    public static final BigDecimal MONDAY = BigDecimal.valueOf(200);
    static final Date DEFAULT_START_DATE = DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 2023);
    static final Date DEFAULT_END_DATE = DateUtil.getDateWithoutTime(31, Calendar.MARCH, 2023);
    static final Integer DELUXE_ACCOM_TYPE_ID = 4;
    static final Integer SUITE_ACCOM_TYPE_ID = 5;
    private static final Integer DEFAULT_FILE_METADATA_ID = 2;
    private static final Integer DEFAULT_PROPERTY_ID = TestProperty.H1.getId();
    private static final String DEFAULT_CURRENCY = "USD";
    private static final Integer ACTIVE_STATUS_ID = Constants.ACTIVE_STATUS_ID;
    private static final LocalDate START_DATE_LOCAL_DATE = DateUtil.convertJavaUtilDateToLocalDate(DEFAULT_START_DATE);
    private static final LocalDate END_DATE_LOCAL_DATE = DateUtil.convertJavaUtilDateToLocalDate(DEFAULT_END_DATE);
    private static final Integer DEFAULT_IS_YIELDABLE = 1;
    private static final Integer DEFAULT_IS_PRICE_RELATIVE = 1;
    private static final String LV0 = "LV0";
    private static final String NONE = "None";
    private static final String NONE_OFFSET_RESOLVER = "NONE";
    private static final Integer DEFAULT_INCLUDE_PACKAGE = 1;
    public static final String FORTYEIGHT = "FORTYEIGHT";
    public static final String FORTYSEVEN = "FORTYSEVEN";
    private final RateQualifiedFixedRepository repository = new RateQualifiedFixedRepository();

    @Mock
    DateService dateService;

    @InjectMocks
    private LinkedSRPRateCalculationForStreamService service;

    private CrudService tenantCrudService;

    @Mock
    private LinkedSRPLv0RateCalculationForStreamService lv0Service;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    LinkedSRPMappingsRepository linkedSRPMappingsRepository;

    private static RateQualifiedFixed getSrpRate(List<RateQualifiedFixed> fixedRates, String SRP1) {
        return fixedRates.stream().filter(rateQualifiedFixed -> rateQualifiedFixed.getName().equals(SRP1)).findFirst().get();
    }

    private static List<RateQualifiedFixedDetailsStg> getFixedRatesForSrp(RateQualifiedFixed srp1Rate, int accomType) {
        return srp1Rate.getDetailsStg().stream().filter(rateQualifiedFixedDetails -> rateQualifiedFixedDetails.getAccomTypeId().equals(accomType)).sorted(Comparator.comparing(RateQualifiedFixedDetailsStg::getStartDate)).collect(Collectors.toList());
    }

    public static RateQualified getRateQualifiedObj(String rateCodeName, String referenceRateCode, Integer rateQualifiedTypeId) {
        RateQualified qualified = new RateQualified();
        qualified.setFileMetadataId(DEFAULT_FILE_METADATA_ID);
        qualified.setPropertyId(DEFAULT_PROPERTY_ID);
        qualified.setCurrency(DEFAULT_CURRENCY);
        qualified.setStatusId(ACTIVE_STATUS_ID);
        qualified.setStartDate(DEFAULT_START_DATE);
        qualified.setEndDate(DEFAULT_END_DATE);
        qualified.setYieldable(DEFAULT_IS_YIELDABLE);
        qualified.setReferenceRateCode(referenceRateCode);
        qualified.setName(rateCodeName);
        qualified.setDescription(rateCodeName);
        qualified.setPriceRelative(DEFAULT_IS_PRICE_RELATIVE);
        qualified.setIncludesPackage(DEFAULT_INCLUDE_PACKAGE);
        qualified.setRateQualifiedTypeId(rateQualifiedTypeId);
        return qualified;
    }

    public static RateQualifiedFixed getRateQualifiedFixedObj(RateQualified rq) {
        RateQualifiedFixed qualified = new RateQualifiedFixed();
        qualified.setFileMetadataId(DEFAULT_FILE_METADATA_ID);
        qualified.setPropertyId(DEFAULT_PROPERTY_ID);
        qualified.setCurrency(DEFAULT_CURRENCY);
        qualified.setStatusId(rq.getStatusId());
        qualified.setStartDate(rq.getStartDate());
        qualified.setEndDate(rq.getEndDate());
        qualified.setYieldable(rq.getYieldable());
        qualified.setReferenceRateCode(rq.getReferenceRateCode());
        qualified.setName(rq.getName());
        qualified.setDescription(rq.getDescription());
        qualified.setRateQualifiedId(rq.getId());
        qualified.setPriceRelative(rq.getPriceRelative());
        qualified.setIncludesPackage(rq.getIncludesPackage());
        qualified.setRateQualifiedTypeId(Constants.QUALIFIED_RATE_PLAN_FIXED_TYPE);
        return qualified;
    }

    public static RateQualifiedDetails getRateQualifiedDetailsObj(Integer parentId, double sunday, Integer accomType, Date startDate, Date endDate) {

        RateQualifiedDetails details = new RateQualifiedDetails();
        details.setRateQualifiedId(parentId);
        details.setAccomTypeId(accomType);
        details.setStartDate(startDate);
        details.setEndDate(endDate);
        details.setSunday(BigDecimal.valueOf(sunday));
        details.setMonday(BigDecimal.valueOf(sunday));
        details.setTuesday(BigDecimal.valueOf(sunday));
        details.setWednesday(BigDecimal.valueOf(sunday));
        details.setThursday(BigDecimal.valueOf(sunday));
        details.setFriday(BigDecimal.valueOf(sunday));
        details.setSaturday(BigDecimal.valueOf(sunday));
        return details;
    }

    public static RateQualifiedDetails getRateQualifiedDetailsObj(Integer parentId, double sunday, double monday, double tuesday, double wednesday, double thursday, double friday,
                                                                  double saturday, Integer accomType, Date startDate, Date endDate) {

        RateQualifiedDetails details = new RateQualifiedDetails();
        details.setRateQualifiedId(parentId);
        details.setAccomTypeId(accomType);
        details.setStartDate(startDate);
        details.setEndDate(endDate);
        details.setSunday(BigDecimal.valueOf(sunday));
        details.setMonday(BigDecimal.valueOf(monday));
        details.setTuesday(BigDecimal.valueOf(tuesday));
        details.setWednesday(BigDecimal.valueOf(wednesday));
        details.setThursday(BigDecimal.valueOf(thursday));
        details.setFriday(BigDecimal.valueOf(friday));
        details.setSaturday(BigDecimal.valueOf(saturday));
        return details;
    }

    public static RateQualifiedFixedDetailsStg getRateQualifiedDetailsFixedStgObj(Integer parentId, double sunday, double monday, double tuesday, double wednesday, double thursday, double friday,
                                                                                  double saturday, Integer accomType, Date startDate, Date endDate, RateQualifiedFixed fixedParent) {

        RateQualifiedFixedDetailsStg details = new RateQualifiedFixedDetailsStg();
        details.setRateQualifiedId(parentId);
        details.setRateQualifiedFixed(fixedParent);
        details.setAccomTypeId(accomType);
        details.setStartDate(startDate);
        details.setEndDate(endDate);
        details.setSunday(BigDecimal.valueOf(sunday));
        details.setMonday(BigDecimal.valueOf(monday));
        details.setTuesday(BigDecimal.valueOf(tuesday));
        details.setWednesday(BigDecimal.valueOf(wednesday));
        details.setThursday(BigDecimal.valueOf(thursday));
        details.setFriday(BigDecimal.valueOf(friday));
        details.setSaturday(BigDecimal.valueOf(saturday));
        return details;
    }

    static RateDetails getRateDetailsObj(Integer accomType, Date startDate, Date endDate) {
        RateDetails details = new RateDetails(1, accomType);
        details.setStartDate(startDate);
        details.setEndDate(endDate);
        details.setSunday(SUNDAY);
        details.setMonday(MONDAY);
        details.setTuesday(BigDecimal.valueOf(150));
        details.setWednesday(BigDecimal.valueOf(150));
        details.setThursday(BigDecimal.valueOf(250));
        details.setFriday(SUNDAY);
        details.setSaturday(BigDecimal.valueOf(350));
        return details;
    }

    @BeforeEach
    void init() {
        tenantCrudService = tenantCrudService();
        RateQualifiedService rateQualifiedService = new RateQualifiedService();
        rateQualifiedService.setCrudService(tenantCrudService);
        rateQualifiedService.setDateService(dateService);
        inject(service, "rateQualifiedService", rateQualifiedService);
        repository.setCrudService(tenantCrudService);
        inject(service, "repository", repository);
        inject(service, "crudService", tenantCrudService);

        SemiYieldableRateDetailsRepository semiYieldableRateDetailsRepository = new SemiYieldableRateDetailsRepository();
        semiYieldableRateDetailsRepository.setCrudService(tenantCrudService);
        inject(service, "semiYieldableRateDetailsRepository", semiYieldableRateDetailsRepository);

        RateQualifiedFixedDetailsStageRepository rateQualifiedFixedDetailsStageRepository = new RateQualifiedFixedDetailsStageRepository();
        rateQualifiedFixedDetailsStageRepository.setTenantCrudService(tenantCrudService);
        inject(service, "rateQualifiedFixedDetailsStageRepository", rateQualifiedFixedDetailsStageRepository);

        RateQualified lv0 = getRateQualifiedObj(LV0, NONE, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(lv0);
        List<RateQualifiedDetails> rateQualifiedDetailsList = Arrays.asList(getRateQualifiedDetailsObj(lv0.getId(), 0, 0, 0, 0, 0, 0, 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE), getRateQualifiedDetailsObj(lv0.getId(), 0, 0, 0, 0, 0, 0, 0, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
        tenantCrudService.save(rateQualifiedDetailsList);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_YIELD_AS_REFINEMENT_ENABLED)).thenReturn(true);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(START_DATE_LOCAL_DATE);
    }

    /*
     * Parent :     ---  ---
     * Child :    ------------
     * */
    @Test
    void partialBreaksInParentSeason() {
        getMockLV0Rates();
        setStartDateAndEndDate();
        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(List.of(srp1, srp2));

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 10, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 20)),
                getRateQualifiedDetailsObj(srp1.getId(), 10, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_END_DATE, -21), DateUtil.addDaysToDate(DEFAULT_END_DATE, -11)),
                getRateQualifiedDetailsObj(srp1.getId(), 20, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE),
                getRateQualifiedDetailsObj(srp2.getId(), 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE),
                getRateQualifiedDetailsObj(srp2.getId(), 0, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(3, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(3, srp1Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp1.get(0), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 20), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp1.get(1), DateUtil.addDaysToDate(DEFAULT_END_DATE, -21), DateUtil.addDaysToDate(DEFAULT_END_DATE, -11), SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(6, srp2Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp2.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 9), SUNDAY);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 20), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp2.get(2), DateUtil.addDaysToDate(DEFAULT_START_DATE, 21), DateUtil.addDaysToDate(DEFAULT_END_DATE, -22), SUNDAY);
        assertFixedDetails(fixedRatesForSrp2.get(3), DateUtil.addDaysToDate(DEFAULT_END_DATE, -21), DateUtil.addDaysToDate(DEFAULT_END_DATE, -11), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp2.get(4), DateUtil.addDaysToDate(DEFAULT_END_DATE, -10), DEFAULT_END_DATE, SUNDAY);
    }

    //   Parent ----    ----
    //   Child    ----   ----
    @Test
    void partialBreakInParentSeason_ChildHasMultipleSeasons() {
        getMockLV0Rates();
        setStartDateAndEndDate();
        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", NONE, Constants.QUALIFIED_RATE_PLAN_FIXED_TYPE);
        tenantCrudService.save(List.of(srp1, srp2));

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 330, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 30)),
                getRateQualifiedDetailsObj(srp1.getId(), 330, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 45), DateUtil.addDaysToDate(DEFAULT_START_DATE, 60)),
                getRateQualifiedDetailsObj(srp2.getId(), 0, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), DateUtil.addDaysToDate(DEFAULT_START_DATE, 35)),
                getRateQualifiedDetailsObj(srp2.getId(), 0, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 50), DEFAULT_END_DATE));

        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(3, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(2, srp1Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp1.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 30), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp1.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 45), DateUtil.addDaysToDate(DEFAULT_START_DATE, 60), SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(4, srp2Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp2.get(0), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), DateUtil.addDaysToDate(DEFAULT_START_DATE, 30), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 31), DateUtil.addDaysToDate(DEFAULT_START_DATE, 35), SUNDAY);
        assertFixedDetails(fixedRatesForSrp2.get(2), DateUtil.addDaysToDate(DEFAULT_START_DATE, 50), DateUtil.addDaysToDate(DEFAULT_START_DATE, 60), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp2.get(3), DateUtil.addDaysToDate(DEFAULT_START_DATE, 61), DEFAULT_END_DATE, SUNDAY);

    }

    private void setUPFixedDetails(List<Integer> rateQualifiedIds) {
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of("SRP1", LV0, "SRP2", "SRP1")));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);
    }

    /*
     * Parent :  - -
     * Child :   ---
     * */
    @Test
    void singleDayOverlapBetweenChildAndParent() {
        getMockLV0Rates();
        setStartDateAndEndDate();
        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(List.of(srp1, srp2));

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 10, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_START_DATE),
                getRateQualifiedDetailsObj(srp2.getId(), 10, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10)));
        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(3, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(1, srp1Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp1.get(0), DEFAULT_START_DATE, DEFAULT_START_DATE, SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(2, srp2Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp2.get(0), DEFAULT_START_DATE, DEFAULT_START_DATE, SUNDAY_20);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), SUNDAY_10);
    }

    @Test
    void PartialBreaksInParent_MultiLevelHierarchy() {
        getMockLV0Rates();
        setStartDateAndEndDate();
        RateQualified srp3 = getRateQualifiedObj("SRP3", "SRP2", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(List.of(srp1, srp2, srp3));

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10)),
                getRateQualifiedDetailsObj(srp2.getId(), 10, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 20)),
                getRateQualifiedDetailsObj(srp3.getId(), 10, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 30)));
        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(4, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");
        RateQualifiedFixed srp3Rate = getSrpRate(fixedRates, "SRP3");

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(1, srp1Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp1.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), SUNDAY);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(2, srp2Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp2.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), SUNDAY_10);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 11), DateUtil.addDaysToDate(DEFAULT_START_DATE, 20), SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp3 = getFixedRatesForSrp(srp3Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(3, srp3Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp3.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), SUNDAY_20);
        assertFixedDetails(fixedRatesForSrp3.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 11), DateUtil.addDaysToDate(DEFAULT_START_DATE, 20), SUNDAY_20);
        assertFixedDetails(fixedRatesForSrp3.get(2), DateUtil.addDaysToDate(DEFAULT_START_DATE, 21), DateUtil.addDaysToDate(DEFAULT_START_DATE, 30), SUNDAY_10);

    }

    @Test
    void partialBreakInParentSeason_WithMissingAccomTypeInParent() {
        setStartDateAndEndDate();
        List<RateDetails> lv0Rates = List.of(
                getRateDetailsObj(DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7)),
                getRateDetailsObj(DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)),
                        getRateDetailsObj(SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7)),
                        getRateDetailsObj(SUITE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15))
        );
        when(lv0Service.buildLv0RatesFromCPDecisionBarOutput(START_DATE_LOCAL_DATE, END_DATE_LOCAL_DATE)).thenReturn(lv0Rates);

        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(List.of(srp1, srp2));

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 10, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)),
                getRateQualifiedDetailsObj(srp2.getId(), 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)),
                getRateQualifiedDetailsObj(srp2.getId(), 0, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)));

        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(3, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertEquals(1, srp1Rate.getDetailsStg().size());
        assertFixedDetails(fixedRatesForSrp1.get(0), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), SUNDAY_10);

        assertEquals(5, srp2Rate.getDetailsStg().size());
        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertFixedDetails(fixedRatesForSrp2.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7), SUNDAY);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 9), BigDecimal.valueOf(-1));
        assertFixedDetails(fixedRatesForSrp2.get(2), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp3 = getFixedRatesForSrp(srp2Rate, SUITE_ACCOM_TYPE_ID);
        assertFixedDetails(fixedRatesForSrp3.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7), SUNDAY);
        assertFixedDetails(fixedRatesForSrp3.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), SUNDAY);
    }

    private void setStartDateAndEndDate() {
        when(dateService.getOptimizationWindowStartDate()).thenReturn(DEFAULT_START_DATE);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(DEFAULT_END_DATE);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE)).thenReturn(11);
    }

    private void getMockLV0Rates() {
        List<RateDetails> lv0Rates = getLv0Rates();
        when(lv0Service.buildLv0RatesFromCPDecisionBarOutput(START_DATE_LOCAL_DATE, END_DATE_LOCAL_DATE)).thenReturn(lv0Rates);
    }

    private static List<RateDetails> getLv0Rates() {
        return List.of(
                getRateDetailsObj(DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE),
                getRateDetailsObj(SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE)
        );
    }

    @Test
    void partialBreakInParentSeason_LV0HasMultipleSeasonsForGapRange() {
        List<RateDetails> lv0Rates = List.of(
                getRateDetailsObj(DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7)),
                getRateDetailsObj(DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15))
        );
        setStartDateAndEndDate();
        when(lv0Service.buildLv0RatesFromCPDecisionBarOutput(START_DATE_LOCAL_DATE, END_DATE_LOCAL_DATE)).thenReturn(lv0Rates);

        RateQualified srp2 = getRateQualifiedObj("SRP2", "SRP1", Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(srp1);
        tenantCrudService.save(srp2);

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), 10, DELUXE_ACCOM_TYPE_ID, DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)),
                getRateQualifiedDetailsObj(srp2.getId(), 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 15)));

        tenantCrudService.save(srpRates);

        int insertedRecords = saveRatesAndDetails();
        assertEquals(3, insertedRecords);
        List<RateQualifiedFixed> fixedRates = repository.getAll();
        RateQualifiedFixed srp1Rate = getSrpRate(fixedRates, "SRP1");
        RateQualifiedFixed srp2Rate = getSrpRate(fixedRates, "SRP2");
        assertEquals(1, srp1Rate.getDetailsStg().size());
        assertEquals(3, srp2Rate.getDetailsStg().size());

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp1 = getFixedRatesForSrp(srp1Rate, DELUXE_ACCOM_TYPE_ID);
        assertFixedDetails(fixedRatesForSrp1.get(0), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), SUNDAY_10);

        List<RateQualifiedFixedDetailsStg> fixedRatesForSrp2 = getFixedRatesForSrp(srp2Rate, DELUXE_ACCOM_TYPE_ID);
        assertFixedDetails(fixedRatesForSrp2.get(0), DEFAULT_START_DATE, DateUtil.addDaysToDate(DEFAULT_START_DATE, 7), SUNDAY);
        assertFixedDetails(fixedRatesForSrp2.get(1), DateUtil.addDaysToDate(DEFAULT_START_DATE, 8), DateUtil.addDaysToDate(DEFAULT_START_DATE, 9), BigDecimal.valueOf(-1));
        assertFixedDetails(fixedRatesForSrp2.get(2), DateUtil.addDaysToDate(DEFAULT_START_DATE, 10), DateUtil.addDaysToDate(DEFAULT_START_DATE, 15), SUNDAY_10);


    }

    @Test
    void testShouldConsiderYieldAsSRPCodeAlongWithReferenceRateCode() {
        RateQualified srp2 = getRateQualifiedObj("SRP2", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        RateQualified srp1 = getRateQualifiedObj("SRP1", LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        tenantCrudService.save(srp1);
        tenantCrudService.save(srp2);

        List<RateQualifiedDetails> srpRates = List.of(
                getRateQualifiedDetailsObj(srp1.getId(), -5, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE),
                getRateQualifiedDetailsObj(srp2.getId(), -10, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
        RateQualifiedFixed srp1Fixed = getRateQualifiedFixedObj(srp1);
        RateQualifiedFixed srp2Fixed = getRateQualifiedFixedObj(srp2);
        tenantCrudService.save(List.of(srp1Fixed, srp2Fixed));
        RateQualifiedFixedDetailsStg srp2StageDetails = getRateQualifiedDetailsFixedStgObj(srp2.getId(),
                120, 120, 120, 120, 120, 130, 120, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, srp2Fixed);
        tenantCrudService.save(srp2StageDetails);
        tenantCrudService.save(srpRates);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of("SRP1", LV0, "SRP2", "SRP1")));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(List.of(srp1.getId(), srp2.getId()));

        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(List.of(srp2.getId()), Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE)));
        assertEquals(1, finalFixedDetails.size());
        assertEquals(finalFixedDetails.get(0).getRateQualifiedId(), srp2.getId());
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), -5, -5, -5, -5, -5, -5, -5)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpCodeHappyPath() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(2, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (child to be fixed and parent to be percent)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's original rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0,0, 0, 0, 0,0, 0),
                () -> assertRates(finalFixedDetails.get(1), 70.00000,70.00000, 70.00000, 70.00000, 70.00000,70.00000, 70.00000)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpCodeResolvableAsNone() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(NONE_OFFSET_RESOLVER);

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));

        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(2, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (both to be fixed)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's original rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 300,200, 150, 150, 250,300, 350),
                () -> assertRates(finalFixedDetails.get(1), 70.00000,70.00000, 70.00000, 70.00000, 70.00000,70.00000, 70.00000)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpCodeRefinementOff() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_YIELD_AS_REFINEMENT_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(2, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (both to be offset percent)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's stage rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0,0,0,0,0,0,0),
                () -> assertRates(finalFixedDetails.get(1), 10, 11, 12, 13, 14, 15, 16)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpInactiveRtParent() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, true, false, false, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(3, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (both to be fixed)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's original rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0,0,0,0,0,0,0),
                () -> assertRates(finalFixedDetails.get(1), 70.00000,70.00000, 70.00000, 70.00000, 70.00000,70.00000, 70.00000),
                () -> assertRates(finalFixedDetails.get(2), 120.00000,120.00000, 120.00000, 120.00000, 120.00000,130.00000, 120.00000)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpInactiveParent() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(INACTIVE_STATUS_ID, false, false, false, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(1, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (both to be fixed)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from self's stage rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 100.00000,100.00000, 100.00000, 100.00000, 100.00000,100.00000, 100.00000)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpChildMissingFromRateQualified() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(1, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent (offset)
        assertAll(
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(0)[1])
        );

        // Check Parent Rates is 0% offset of LV0, no rates for child are generated
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0.0,0.0, 0.0, 0.0, 0.0,0.0, 0.0)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpChildRQIsInactive() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);

        Integer fortyEightId = tenantCrudService.findByNativeQuerySingleResult("SELECT Rate_Qualified_ID FROM Rate_Qualified WHERE Rate_Code_Name='" + FORTYEIGHT + "'", Map.of());
        tenantCrudService.executeUpdateByNativeQuery("UPDATE Rate_Qualified SET Status_ID=2 WHERE Rate_Qualified_ID = " + fortyEightId + "; " +
                "DELETE FROM Rate_Qualified_Fixed WHERE Rate_Qualified_ID = " + fortyEightId + "; " +
                "DELETE FROM Rate_Qualified_Fixed_Details_Stg WHERE Rate_Qualified_ID = " + fortyEightId + "; ");
        rateQualifiedIds.remove(fortyEightId);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(1, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent (offset)
        assertAll(
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(0)[1])
        );

        // Check Parent Rates is 0% offset of LV0, no rates for child are generated
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0.0,0.0, 0.0, 0.0, 0.0,0.0, 0.0)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpChildWithoutSeason() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);

        Integer fortyEightId = tenantCrudService.findByNativeQuerySingleResult("SELECT Rate_Qualified_ID FROM Rate_Qualified WHERE Rate_Code_Name='" + FORTYEIGHT + "'", Map.of());
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Rate_Qualified_Details WHERE Rate_Qualified_ID = (SELECT Rate_Qualified_ID FROM Rate_Qualified WHERE Rate_Code_Name='" + FORTYEIGHT + "'); " +
                "DELETE FROM Rate_Qualified_Fixed_Details_Stg WHERE Rate_Qualified_ID = (SELECT Rate_Qualified_ID FROM Rate_Qualified WHERE Rate_Code_Name='" + FORTYEIGHT + "');" +
                "DELETE FROM Rate_Qualified_Fixed WHERE Rate_Qualified_ID = (SELECT Rate_Qualified_ID FROM Rate_Qualified WHERE Rate_Code_Name='" + FORTYEIGHT + "');");
        rateQualifiedIds.remove(fortyEightId);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(1, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent (offset)
        assertAll(
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(0)[1])
        );

        // Check Parent Rates is 0% offset of LV0, no rates for child are generated
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0.0,0.0, 0.0, 0.0, 0.0,0.0, 0.0)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpWithMissingSrpMapping() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, false, false, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(2, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child should be offset
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates is 0% offset of LV0, child rates are populated directly from details_stg
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0.0,0.0, 0.0, 0.0, 0.0,0.0, 0.0),
                () -> assertRates(finalFixedDetails.get(1), 100.0,100.0, 100.0, 100.0, 100.0,100.0, 100.0)
        );
    }

    @Test
    void testSemiYieldableLinkedSrpPartialSeasonParentInRateDetails() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, true, false, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(3, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (child to be fixed)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's original rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0,0,0,0,0,0,0),
                () -> assertEquals(finalFixedDetails.get(0).getStartDate(), DateUtil.getDateWithoutTime(10, Calendar.JANUARY, 2023)),
                () -> assertEquals(finalFixedDetails.get(0).getEndDate(), DateUtil.getDateWithoutTime(31, Calendar.JANUARY, 2023)),
                () -> assertRates(finalFixedDetails.get(1), 0,0,0,0,0,0,0),
                () -> assertEquals(finalFixedDetails.get(1).getStartDate(), DateUtil.getDateWithoutTime(1, Calendar.MARCH, 2023)),
                () -> assertEquals(finalFixedDetails.get(1).getEndDate(), DateUtil.getDateWithoutTime(20, Calendar.MARCH, 2023)),
                () -> assertRates(finalFixedDetails.get(2), 70.00000,70.00000, 70.00000, 70.00000, 70.00000,70.00000, 70.00000),
                () -> assertEquals(finalFixedDetails.get(2).getStartDate(), DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 2023)),
                () -> assertEquals(finalFixedDetails.get(2).getEndDate(), DateUtil.getDateWithoutTime(31, Calendar.MARCH, 2023))
        );
    }

    @Test
    void testSemiYieldableLinkedSrpPartialSeasonParentInOriginalRates() {
        List<Integer> rateQualifiedIds = setupSemiYieldableStageFixedDataAndGetRQIds(ACTIVE_STATUS_ID, false, true, true, true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT)).thenReturn(true);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR)).thenReturn(LV0);
        when(configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE))
                .thenReturn(Integer.parseInt(String.valueOf(DateUtil.daysBetween(DEFAULT_START_DATE, DEFAULT_END_DATE) + 1)));

        when(linkedSRPMappingsRepository.getMappings()).thenReturn(generateLinedSrpMappings(Map.of(FORTYSEVEN, LV0, FORTYEIGHT, FORTYSEVEN)));
        when(linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds()).thenReturn(rateQualifiedIds);


        List<RateQualifiedFixedDetails> finalFixedDetails = service.resolveRateQualifiedFixedDetails(rateQualifiedIds,
                Optional.of(LocalDateUtils.toJavaLocalDate(DEFAULT_START_DATE))).stream().sorted(Comparator.comparing(RateQualifiedFixedDetails::getRateQualifiedId)).collect(Collectors.toList());
        Assertions.assertEquals(7, finalFixedDetails.size());
        List<Object[]> rateQualifiedFixed = tenantCrudService.findByNativeQuery("SELECT Rate_Code_Name, Rate_Qualified_Type_ID FROM Rate_Qualified_Fixed ORDER BY Rate_Code_Name");

        // Check Rate_Qualified_Type_ID for the parent and child (child to be fixed)
        assertAll(
                () -> assertEquals(FORTYEIGHT, rateQualifiedFixed.get(0)[0]),
                () -> assertEquals(3, rateQualifiedFixed.get(0)[1]),
                () -> assertEquals(FORTYSEVEN, rateQualifiedFixed.get(1)[0]),
                () -> assertEquals(2, rateQualifiedFixed.get(1)[1])
        );

        // Check Parent Rates and child has derived rates from parent's original rates.
        assertAll(
                () -> assertRates(finalFixedDetails.get(0), 0,0,0,0,0,0,0),
                () -> assertRates(finalFixedDetails.get(2), 70.00000,70.00000, 70.00000, 70.00000, 70.00000,70.00000, 70.00000),
                () -> assertEquals(finalFixedDetails.get(2).getStartDate(), DateUtil.getDateWithoutTime(10, Calendar.JANUARY, 2023)),
                () -> assertEquals(finalFixedDetails.get(2).getEndDate(), DateUtil.getDateWithoutTime(31, Calendar.JANUARY, 2023)),
                () -> assertRates(finalFixedDetails.get(3), 80.00000,80.00000, 80.00000, 80.00000, 80.00000,80.00000, 80.00000),
                // If Parent season is absent yield at its own rate
                () -> assertRates(finalFixedDetails.get(4), 100.00000,100.00000, 100.00000, 100.00000, 100.00000,100.00000, 100.00000),
                () -> assertEquals(finalFixedDetails.get(4).getStartDate(), DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 2023)),
                () -> assertEquals(finalFixedDetails.get(4).getEndDate(), DateUtil.getDateWithoutTime(9, Calendar.JANUARY, 2023)),
                () -> assertRates(finalFixedDetails.get(5), 100.00000,100.00000, 100.00000, 100.00000, 100.00000,100.00000, 100.00000),
                () -> assertRates(finalFixedDetails.get(6), 100.00000,100.00000, 100.00000, 100.00000, 100.00000,100.00000, 100.00000)
        );
    }

    private List<Integer> setupSemiYieldableStageFixedDataAndGetRQIds(Integer statusIdOfParent, boolean parentHasInactiveRt, boolean partialParentSeason, boolean hasPartialBreaksInOriginalRates, boolean isChildRqPresent) {
        LV0SETUP();
        List<Integer> rateQualifiedId = new ArrayList<>();
        RateQualified fortySeven = getRateQualifiedObj(FORTYSEVEN, LV0, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE);
        fortySeven.setStatusId(statusIdOfParent);
        fortySeven = tenantCrudService.save(fortySeven);

        RateQualifiedFixed fixedParent = tenantCrudService.save(getRateQualifiedFixedObj(fortySeven));

        if (partialParentSeason) {
            tenantCrudService.save(getRateQualifiedDetailsObj(fortySeven.getId(), 10, 10, 10, 10, 10, 10, 10, DELUXE_ACCOM_TYPE_ID, DateUtil.getDateWithoutTime(10, Calendar.JANUARY, 2023), DateUtil.getDateWithoutTime(31, Calendar.JANUARY, 2023)));
            tenantCrudService.save(getRateQualifiedDetailsObj(fortySeven.getId(), 16,16,16,16,16,16,16, DELUXE_ACCOM_TYPE_ID, DateUtil.getDateWithoutTime(1, Calendar.MARCH, 2023), DateUtil.getDateWithoutTime(20, Calendar.MARCH, 2023)));
        } else {
            tenantCrudService.save(getRateQualifiedDetailsObj(fortySeven.getId(), 10, 11, 12, 13, 14, 15, 16, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
        }
        if (Objects.equals(statusIdOfParent, ACTIVE_STATUS_ID)) {
            tenantCrudService.save(getRateQualifiedDetailsFixedStgObj(fortySeven.getId(), 200, 200, 150, 120, 100, 200, 150, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, fixedParent));
            rateQualifiedId.add(fortySeven.getId());
        }

        if(isChildRqPresent) {
            RateQualified fortyEight = tenantCrudService.save(getRateQualifiedObj(FORTYEIGHT, FORTYSEVEN, Constants.QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE));
            RateQualifiedFixed fixedChild = tenantCrudService.save(getRateQualifiedFixedObj(fortyEight));
            rateQualifiedId.add(fortyEight.getId());

            tenantCrudService.save(getRateQualifiedDetailsObj(fortyEight.getId(), 10, 11, 12, 13, 14, 15, 16, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
            tenantCrudService.save(getRateQualifiedDetailsFixedStgObj(fortyEight.getId(), 100, 100, 100, 100, 100, 100, 100, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, fixedChild));

            if (parentHasInactiveRt) {
                tenantCrudService.save(getRateQualifiedDetailsObj(fortyEight.getId(), 10, 11, 12, 13, 14, 15, 16, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE));
                tenantCrudService.save(getRateQualifiedDetailsFixedStgObj(fortyEight.getId(), 120, 120, 120, 120, 120, 130, 120, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, fixedChild));
            }
        }

        if (!hasPartialBreaksInOriginalRates) {
            tenantCrudService.save(getSemiYieldableRateQualifiedDetails(FORTYSEVEN, 70, 70, 70, 70, 70, 70, 70, DELUXE_ACCOM_TYPE_ID));
        } else {
            SemiYieldableRateDetails season01 = getSemiYieldableRateQualifiedDetails(FORTYSEVEN, 70, 70, 70, 70, 70, 70, 70, DELUXE_ACCOM_TYPE_ID);
            season01.setStartDate(DateUtil.getDateWithoutTime(10, Calendar.JANUARY, 2023));
            season01.setEndDate(DateUtil.getDateWithoutTime(31, Calendar.JANUARY, 2023));
            tenantCrudService.save(season01);
            SemiYieldableRateDetails season02 = getSemiYieldableRateQualifiedDetails(FORTYSEVEN, 80, 80, 80, 80, 80, 80, 80, DELUXE_ACCOM_TYPE_ID);
            season02.setStartDate(DateUtil.getDateWithoutTime(1, Calendar.MARCH, 2023));
            season02.setEndDate(DateUtil.getDateWithoutTime(20, Calendar.MARCH, 2023));
            tenantCrudService.save(season02);
        }

        return rateQualifiedId;
    }

    private void LV0SETUP() {
        List<RateDetails> lv0Rates = getLv0Rates();
        when(lv0Service.buildLv0RatesFuture()).thenReturn(lv0Rates);
        addLv0ToStageDetails();
    }

    private void addLv0ToStageDetails() {
        RateQualified lv0 = new RateQualified();
        lv0.setName(LV0);
        lv0 = tenantCrudService.findByExampleSingleResult(lv0);

        RateQualifiedFixed fixedParent = tenantCrudService.save(getRateQualifiedFixedObj(lv0));
        tenantCrudService.save(getRateQualifiedDetailsFixedStgObj(lv0.getId(), 0, 0, 0, 0, 0, 0, 0, DELUXE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, fixedParent));
        tenantCrudService.save(getRateQualifiedDetailsFixedStgObj(lv0.getId(), 0, 0, 0, 0, 0, 0, 0, SUITE_ACCOM_TYPE_ID, DEFAULT_START_DATE, DEFAULT_END_DATE, fixedParent));
    }

    private SemiYieldableRateDetails getSemiYieldableRateQualifiedDetails(String rateCodeName, double sunday, double monday,
                                                                          double tuesday, double wednesday,
                                                                          double thursday, double friday, double saturday, Integer accomType) {

        SemiYieldableRateDetails details = new SemiYieldableRateDetails();
        details.setRateCodeName(rateCodeName);
        details.setAccomTypeId(accomType);
        details.setStartDate(DEFAULT_START_DATE);
        details.setEndDate(DEFAULT_END_DATE);
        details.setSunday(BigDecimal.valueOf(sunday));
        details.setMonday(BigDecimal.valueOf(monday));
        details.setTuesday(BigDecimal.valueOf(tuesday));
        details.setWednesday(BigDecimal.valueOf(wednesday));
        details.setThursday(BigDecimal.valueOf(thursday));
        details.setFriday(BigDecimal.valueOf(friday));
        details.setSaturday(BigDecimal.valueOf(saturday));
        return details;
    }

    private HashMap<String, LinkedSrpMappingsDto> generateLinedSrpMappings(Map<String, String> childToParentSrp) {
        HashMap<String, LinkedSrpMappingsDto> mappingDto = new HashMap<>();
        childToParentSrp.entrySet().forEach(relation -> {
            LinkedSRPMappings mapping = new LinkedSRPMappings();
            mapping.setSrpCode(relation.getKey());
            mapping.setLinkedSrpCode(relation.getValue());
            mapping.setYieldAs("Y");
            mappingDto.put(relation.getKey(), new LinkedSrpMappingsDto(mapping));
        });
        return mappingDto;
    }

    private void assertFixedDetails(RateQualifiedFixedDetailsStg rateQualifiedFixedDetailsStg, Date start, Date end, BigDecimal sundayValue) {
        assertEquals(start, rateQualifiedFixedDetailsStg.getStartDate());
        assertEquals(end, rateQualifiedFixedDetailsStg.getEndDate());
        assertEquals(0, sundayValue.compareTo(rateQualifiedFixedDetailsStg.getSunday()));
    }

    private int saveRatesAndDetails() {
        List<RateQualifiedFixedDetails> rateQualifiedFixedDetailsList = service.buildRatesAndSaveRateQualifiedFixed(service.loadRateQualifiedDataWithLv0Rates());
        service.saveRateDetailsAsBatch(rateQualifiedFixedDetailsList);
        flushAndClear();
        return rateQualifiedFixedDetailsList.stream().map(RateQualifiedFixedDetails::getRateQualifiedId).collect(Collectors.toSet()).size();
    }

    private void assertRates(RateQualifiedFixedDetails rateDetails, double sunday, double monday, double tuesday, double wednesday, double thursday, double friday, double saturday) {
        Assertions.assertEquals(sunday, rateDetails.getSunday().doubleValue());
        Assertions.assertEquals(monday, rateDetails.getMonday().doubleValue());
        Assertions.assertEquals(tuesday, rateDetails.getTuesday().doubleValue());
        Assertions.assertEquals(wednesday, rateDetails.getWednesday().doubleValue());
        Assertions.assertEquals(thursday, rateDetails.getThursday().doubleValue());
        Assertions.assertEquals(friday, rateDetails.getFriday().doubleValue());
        Assertions.assertEquals(saturday, rateDetails.getSaturday().doubleValue());
    }

}
