package com.ideas.tetris.pacman.services.groupwash;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.demandoverride.DemandOverrideService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockExtendedDTO;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateInterval;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class IndividualGroupForWashOverrideServicePerformanceFixesTest extends AbstractG3JupiterTest {

    private final IndividualGroupForWashOverrideService service = new IndividualGroupForWashOverrideService();
    private final int propertyID = 6;
    private final int mktSeg1 = 7;
    private final int mktSeg2 = 8;
    private final int mktSeg3 = 9;
    private final int accomTypeId1 = 9;
    private final int accomTypeId2 = 10;
    private final int accomTypeId3 = 11;
    private final String rateCode1 = "RATCOD1";
    private final String rateCode2 = "RATCOD2";
    private final String rateCode3 = "RATCOD3";
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    private DateService dateService;
    private LocalDate startDate;
    private DecisionService decisionService;
    private DemandOverrideService demandOverrideService;
    private GroupBlockDetail testGroupBlock;
    private WashOverrideByGroup testWashOverride;
    private Integer testWashOverrideId;


    @Test
    public void shouldNotReturnNullWhenGroupDataIsNotFound() {
        startDate = getLocalDate();
        int groupId = -1;
        List<GroupBlockExtendedDTO> groupData = service.getGroupWashData(Collections.singletonList(groupId), DateUtil.convertJavaToJodaLocalDate(startDate), DateUtil.convertJavaToJodaLocalDate(startDate), false);
        assertNotNull(groupData);
        assertTrue(groupData.isEmpty());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testGroupWashExportDataForCancellationsAndNoShowsPresent_Include_NonAdjust() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransData(insertQuery, 5, 9, 7, 3, "XX", mktSeg1, rateCode1, "1");
        populateTransData(insertQuery, 4, 10, 10, 2, "XX", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 0, 11, 13, 0, "SS", mktSeg1, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 3, 15, 13, 0, "NS", mktSeg1, rateCode1, "4");
        populateTransDataForNoShows(insertQuery, 6, 15, 13, 0, "NS", mktSeg1, rateCode1, "5");
        populateTransDataForNoShows(insertQuery, 7, 12, 4, 0, "SS", mktSeg1, rateCode1, "6");
        populateTransDataForNoShows(insertQuery, 2, 13, 11, 0, "SS", mktSeg1, rateCode1, "7");
        populateTransDataForNoShows(insertQuery, 1, 14, 2, 0, "SS", mktSeg1, rateCode1, "8");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 4, 12, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode1, "TENTATIVE", mktSeg2, 8, 12, 12);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId2, mktSeg1, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId3, mktSeg1, 15, 12, 151.99);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 0, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupId = getGroupCode(rateCode1, mktSeg1);
        int groupId2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(Arrays.asList(groupId, groupId2), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(0)), rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupId), 1, 0, 0, 150.94000, mktSeg1);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(1)), rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupId2), 1, 0, 0, 151.99000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testGroupWashExportDataForCancellationsAndNoShowsPresent() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransData(insertQuery, 5, 9, 7, 3, "XX", mktSeg1, rateCode1, "1");
        populateTransData(insertQuery, 4, 10, 10, 2, "XX", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 0, 11, 13, 0, "SS", mktSeg1, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 3, 15, 13, 0, "NS", mktSeg1, rateCode1, "4");
        populateTransDataForNoShows(insertQuery, 6, 15, 13, 0, "NS", mktSeg1, rateCode1, "5");
        populateTransDataForNoShows(insertQuery, 7, 12, 4, 0, "SS", mktSeg1, rateCode1, "6");
        populateTransDataForNoShows(insertQuery, 2, 13, 11, 0, "SS", mktSeg1, rateCode1, "7");
        populateTransDataForNoShows(insertQuery, 1, 14, 2, 0, "SS", mktSeg1, rateCode1, "8");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 4, 12, 14);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId2, mktSeg1, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId3, mktSeg1, 15, 12, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupId = getGroupCode(rateCode1, mktSeg1);
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(List.of(groupId), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), true);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", list, rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupId), 1, 0, 0, 150.94000, mktSeg1);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testGroupWashExportDataForUniqueMarketSegmentAndRateCodeCombination() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransDataForNoShows(insertQuery, 10, 11, 3, 0, "SS", mktSeg1, rateCode1, "1");
        populateTransDataForNoShows(insertQuery, 12, 13, 12, 0, "SS", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 7, 8, 7, 0, "SS", mktSeg2, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 9, 10, 13, 0, "SS", mktSeg2, rateCode1, "4");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 10, 15, 14);
        populateGroupMasterData(insertQuery, rateCode1, mktSeg2, 5, 10, 14);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber + 3, accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber + 4, accomTypeId2, mktSeg1, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 12, 151.99);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber + 1, accomTypeId2, mktSeg2, 7, 12, 143.76);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupIdForMktSeg2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list2 = service.getGroupWashData(List.of(groupIdForMktSeg2), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export For Market Segment 2 and rate Code 1- ", list2, rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupIdForMktSeg2), 1, 0, 0, 151.99000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testGroupWashExportDataForSplitGroups() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransDataForNoShows(insertQuery, 7, 8, 7, 0, "SS", mktSeg2, rateCode1, "3");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg2, 5, 10, 14);
        String splitRateCode = rateCode1 + "_2";
        insertQuery.append("UPDATE [Group_Master] SET Master_Group_Code = '" + rateCode1 + "', Group_Code = '").append(splitRateCode).append("';");
        populateGroupBlockData(insertQuery, splitRateCode, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 12, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupIdForMktSeg2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list2 = service.getGroupWashData(List.of(groupIdForMktSeg2), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export For Market Segment 2 and rate Code 1- ", list2, rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupIdForMktSeg2), 1, 0, 0, 151.99000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }


    @Test
    void testGroupWashExportDataForSplitGroupCode_WhenShowSplitGroupForGWReportOn_WithProcedure() {
        setWorkContextProperty(TestProperty.H2);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_SPLIT_GROUP_CODE_IN_GROUP_WASH_REPORT)).thenReturn(true);
        String splitRateCode = rateCode1 + "_2";
        int occupancyDateNumber = 7;
        final LocalDate startDate = getLocalDate();
        createTestDataForGroupWashExport(splitRateCode, occupancyDateNumber, startDate);
        int groupIdForMktSeg2 = getGroupCode(rateCode1, mktSeg2);
        final List<GroupBlockExtendedDTO> groupWashExportData = service.getGroupWashData(List.of(groupIdForMktSeg2), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);

        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export For Market Segment 2 and rate Code 1- ", groupWashExportData, splitRateCode, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupIdForMktSeg2), 1, 0, 0, 151.99000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.SHOW_SPLIT_GROUP_CODE_IN_GROUP_WASH_REPORT);
    }

    @Test
    void testGroupWashExportDataForSplitGroupCode_WhenShowSplitGroupForGWReportOff_WithProcedure() {
        setWorkContextProperty(TestProperty.H2);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_SPLIT_GROUP_CODE_IN_GROUP_WASH_REPORT)).thenReturn(false);
        String splitRateCode = rateCode1 + "_2";
        int occupancyDateNumber = 7;
        final LocalDate startDate = getLocalDate();
        createTestDataForGroupWashExport(splitRateCode, occupancyDateNumber, startDate);
        int groupIdForMktSeg2 = getGroupCode(rateCode1, mktSeg2);
        final List<GroupBlockExtendedDTO> groupWashExportData = service.getGroupWashData(List.of(groupIdForMktSeg2), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export For Market Segment 2 and rate Code 1- ", groupWashExportData, rateCode1, startDate.plusDays(occupancyDateNumber).toString(), String.valueOf(groupIdForMktSeg2), 1, 0, 0, 151.99000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.SHOW_SPLIT_GROUP_CODE_IN_GROUP_WASH_REPORT);
    }

    private void createTestDataForGroupWashExport(String splitRateCode, int occupancyDateNumber, LocalDate startDate) {
        StringBuilder insertQuery = new StringBuilder();
        populateTransDataForNoShows(insertQuery, 7, 8, 7, "SS", mktSeg2, rateCode1, "3", startDate);
        populateGroupMasterData(insertQuery, rateCode1, mktSeg2, 5, 10, 14, startDate);
        insertQuery.append("UPDATE [Group_Master] SET Master_Group_Code = '" + rateCode1 + "', Group_Code = '").append(splitRateCode).append("';");
        populateGroupBlockData(insertQuery, splitRateCode, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 12, 151.99, startDate);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void populateTransDataForNoShows(StringBuilder insertQuery, int startDateNumber, int endDateNumber, int bookingDateNumber, String individualStatus, int mktSegment, String rateCode, String reservationId, LocalDate startDate) {
        insertQuery.append(" INSERT INTO [individual_trans]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM]) ");
        insertQuery.append("  VALUES (2," + propertyID + ",").append(reservationId).append(",'").append(individualStatus).append("','").append(startDate.plusDays(startDateNumber)).append("' ");
        insertQuery.append(" ,'").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("',NULL,'QN',11,'").append(mktSegment).append("',500,0,0,0,0,500,NULL,NULL,'").append(rateCode).append("',400,NULL,'GT',0,1,GETDATE(),NULL,NULL,NULL)");
    }

    private void populateGroupMasterData(StringBuilder insertQuery, String rateCode, int mktSegment, int startDateNumber, int endDateNumber, int bookingDateNumber, LocalDate startDate) {
        insertQuery.append(" INSERT INTO [Group_Master]");
        insertQuery.append(" ([Property_ID],[Group_Code],[Group_Name],[Group_Description],[Master_Group_ID],[Master_Group_Code]");
        insertQuery.append(" ,[Group_Status_Code],[Group_Type_Code],[Mkt_Seg_ID],[Start_DT]");
        insertQuery.append(" ,[End_DT],[Booking_DT],[Pickup_Type_Code],[Cancel_DT],[Booking_type]");
        insertQuery.append(" ,[Sales_Person],[Cut_Off_date],[Cut_Off_days])");
        insertQuery.append("  VALUES (" + propertyID + ",'").append(rateCode).append("','").append(rateCode).append("','").append(rateCode).append("' ");
        insertQuery.append(" ,NULL,NULL,'DEFINITE','GROUP','").append(mktSegment).append("','").append(startDate.plusDays(startDateNumber)).append("','").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("',NULL,NULL,NULL,NULL,NULL,NULL)");
    }

    private void populateGroupBlockData(StringBuilder insertQuery, String rateCode, int occupancyDateNumber, int accomTypeId, int mktSegId, int blockValue, int pickUpValue, double rate, LocalDate startDate) {
        insertQuery.append(" INSERT INTO [Group_Block]");
        insertQuery.append(" ([Group_ID],[Occupancy_DT],[Accom_Type_ID],[Blocks],[Pickup],[Original_Blocks],[rate])");
        insertQuery.append("  VALUES ((select group_id from Group_Master where Group_Code='").append(rateCode).append("' and Mkt_Seg_ID=").append(mktSegId).append("),'").append(startDate.plusDays(occupancyDateNumber)).append("',").append(accomTypeId).append(",").append(blockValue).append(",").append(pickUpValue).append(",0,").append(rate).append(" )");
    }

    private void assertGroupWashExportDataForPickUpAndRate(String Level, List<GroupBlockExtendedDTO> reportData, String rateName, String occupancyDate, String groupId, int groupArrivals, int groupDepartures, int pickupVariance, double rateValue, int mktSegId) {
        assertEquals(rateName, (reportData.get(0).getGroupRateCode()), Level + " - Rate Name ");
        assertEquals(occupancyDate, (DateUtil.convertFromJodaLocalDateToJavaLocalDate(reportData.get(0).getOccupancyDate()).toString()), Level + " - Occupancy Date ");
        assertEquals(groupId, reportData.get(0).getGroupId().toString(), Level + " - Group Id ");
        assertEquals(groupArrivals, (reportData.get(0).getGroupArrivals()), Level + " - Group Arrivals ");
        assertEquals(groupDepartures, (reportData.get(0).getGroupDepartures()), Level + " - Group Departures ");
        assertEquals(pickupVariance, (reportData.get(0).getPickupVariance()), Level + " - Pick Up Variance ");
        assertEquals(new BigDecimal(rateValue).setScale(5, RoundingMode.HALF_DOWN), (reportData.get(0).getRateValue().setScale(5, RoundingMode.HALF_DOWN)), Level + " - Rate Value ");
        assertEquals(mktSegId, (reportData.get(0).getMktSegId()), Level + " - Market Segment Id ");
    }

    @Test
    public void testScenarioForLongIntervalToggleOFF_WithProcedure() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        testGroupWashForScenarioForLongIntervalToggleOFF(true);
    }

    public void testGroupWashForScenarioForLongIntervalToggleOFF(boolean excelToggleValue) {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED)).thenReturn(excelToggleValue);
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 365;
        populateAllDefiniteDataForTestScenarios(insertQuery, occupancyDateNumber);
        populateAllTentativeProspectDataForTestScenarios(insertQuery, occupancyDateNumber);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        List<Integer> groupIDs = service.getGroupMasterData(new LocalDateInterval(DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber))), false).
                stream().map(GroupBlockMaster::getId).
                collect(Collectors.toList());
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(groupIDs, DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertEquals(1, list.size());
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", list, rateCode1, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode1, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testScenarioForLongIntervalToggleON_WithProcedure() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        testGroupWashForScenarioForLongIntervalToggleON(true);
    }

    public void testGroupWashForScenarioForLongIntervalToggleON(boolean excelToggleValue) {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED)).thenReturn(excelToggleValue);
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 365;
        populateAllDefiniteDataForTestScenarios(insertQuery, occupancyDateNumber);
        populateAllTentativeProspectDataForTestScenarios(insertQuery, occupancyDateNumber);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        List<Integer> groupIDs = service.getGroupMasterData(new LocalDateInterval(DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber))), true).
                stream().map(GroupBlockMaster::getId).
                collect(Collectors.toList());
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(groupIDs, DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), true);
        assertEquals(3, list.size());
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(0)), rateCode1, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode1, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(1)), rateCode2, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode2, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(2)), rateCode3, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode3, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void testScenarioForLongIntervalOnlyTentativeProspectToggleON_WithProcedure() {
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM)).thenReturn("OXI");
        testGroupWashForScenarioForLongIntervalToggleON(true);
    }

    @Test
    public void testGroupWashForScenarioForLongIntervalOnlyTentativeProspectToggleON() {

        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 365;
        populateAllTentativeProspectDataForTestScenarios(insertQuery, occupancyDateNumber);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        List<Integer> groupIDs = service.getGroupMasterData(new LocalDateInterval(DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)), DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber))), true).
                stream().map(GroupBlockMaster::getId).
                collect(Collectors.toList());
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(groupIDs, DateUtil.convertJavaToJodaLocalDate(startDate.minusDays(occupancyDateNumber)),
                DateUtil.convertJavaToJodaLocalDate(startDate.plusDays(occupancyDateNumber)), true);
        assertEquals(2, list.size());
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(0)), rateCode2, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode2, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        assertGroupWashExportDataForPickUpAndRate("Validating Group Wash Export - ", List.of(list.get(1)), rateCode3, startDate.plusDays(0).toString(), String.valueOf(getGroupCode(rateCode3, mktSeg2)), 0, 0, 0, 199.49000, mktSeg2);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    public void populateAllDefiniteDataForTestScenarios(StringBuilder insertQuery, int occupancyDateNumber) {
        populateTransDataForNoShows(insertQuery, 10, 11, 3, 0, "SS", mktSeg1, rateCode1, "1");
        populateTransDataForNoShows(insertQuery, 12, 13, 12, 0, "SS", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 7, 8, 7, 0, "SS", mktSeg2, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 9, 10, 13, 0, "SS", mktSeg2, rateCode1, "4");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, (-10 - occupancyDateNumber), (-5 - occupancyDateNumber), (-14 - occupancyDateNumber));
        populateGroupMasterData(insertQuery, rateCode1, mktSeg2, 5, 10, 14);
        populateGroupMasterData(insertQuery, rateCode1, mktSeg3, 10 + occupancyDateNumber, 15 + occupancyDateNumber, 14 + occupancyDateNumber);
        populateGroupBlockData(insertQuery, rateCode1, (-10 - occupancyDateNumber), accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode1, 0, accomTypeId2, mktSeg2, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, 10 + occupancyDateNumber, accomTypeId3, mktSeg3, 15, 12, 151.99);
    }

    private void populateAllTentativeProspectDataForTestScenarios(StringBuilder insertQuery, int occupancyDateNumber) {
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode2, "TENTATIVE", mktSeg1, (-10 - occupancyDateNumber), (-5 - occupancyDateNumber), (-14 - occupancyDateNumber));
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode2, "TENTATIVE", mktSeg2, 5, 10, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode2, "TENTATIVE", mktSeg3, 10 + occupancyDateNumber, 15 + occupancyDateNumber, 14 + occupancyDateNumber);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode3, "PROSPECT", mktSeg1, (-10 - occupancyDateNumber), (-5 - occupancyDateNumber), (-14 - occupancyDateNumber));
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode3, "PROSPECT", mktSeg2, 5, 10, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode3, "PROSPECT", mktSeg3, 10 + occupancyDateNumber, 15 + occupancyDateNumber, 14 + occupancyDateNumber);
        populateGroupBlockData(insertQuery, rateCode2, (-10 - occupancyDateNumber), accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode2, 0, accomTypeId2, mktSeg2, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode2, 10 + occupancyDateNumber, accomTypeId3, mktSeg3, 15, 12, 151.99);
        populateGroupBlockData(insertQuery, rateCode3, (-10 - occupancyDateNumber), accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode3, 0, accomTypeId2, mktSeg2, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode3, 10 + occupancyDateNumber, accomTypeId3, mktSeg3, 15, 12, 151.99);
    }

    private void populateTransData(StringBuilder insertQuery, int startDateNumber, int endDateNumber, int bookingDateNumber, int cancellationDateNumber, String individualStatus, int mktSegment, String rateCode, String reservationId) {
        insertQuery.append(" INSERT INTO [individual_trans]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM]) ");
        insertQuery.append("  VALUES (2," + propertyID + ",").append(reservationId).append(",'").append(individualStatus).append("','").append(startDate.plusDays(startDateNumber)).append("' ");
        insertQuery.append(" ,'").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("','").append(startDate.minusDays(cancellationDateNumber)).append("','QN',11,'").append(mktSegment).append("',500,0,0,0,0,500,NULL,NULL,'").append(rateCode).append("',400,NULL,'GT',0,1,GETDATE(),NULL,NULL,NULL)");
    }

    private void populateTransDataForNoShows(StringBuilder insertQuery, int startDateNumber, int endDateNumber, int bookingDateNumber, int cancellationDateNumber, String individualStatus, int mktSegment, String rateCode, String reservationId) {
        insertQuery.append(" INSERT INTO [individual_trans]");
        insertQuery.append(" ([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT],[Booking_DT]");
        insertQuery.append(" ,[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue],[Other_Revenue],[Total_Revenue]");
        insertQuery.append(" ,[Source_Booking],[Nationality],[Rate_Code],[Rate_Value],[Room_Number],[Booking_type],[Number_Children]");
        insertQuery.append(" ,[Number_Adults],[CreateDate_DTTM],[Confirmation_No],[Channel],[Booking_TM]) ");
        insertQuery.append("  VALUES (2," + propertyID + ",").append(reservationId).append(",'").append(individualStatus).append("','").append(startDate.plusDays(startDateNumber)).append("' ");
        insertQuery.append(" ,'").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("',NULL,'QN',11,'").append(mktSegment).append("',500,0,0,0,0,500,NULL,NULL,'").append(rateCode).append("',400,NULL,'GT',0,1,GETDATE(),NULL,NULL,NULL)");
    }

    private void populateGroupMasterData(StringBuilder insertQuery, String rateCode, int mktSegment, int startDateNumber, int endDateNumber, int bookingDateNumber) {
        insertQuery.append(" INSERT INTO [Group_Master]");
        insertQuery.append(" ([Property_ID],[Group_Code],[Group_Name],[Group_Description],[Master_Group_ID],[Master_Group_Code]");
        insertQuery.append(" ,[Group_Status_Code],[Group_Type_Code],[Mkt_Seg_ID],[Start_DT]");
        insertQuery.append(" ,[End_DT],[Booking_DT],[Pickup_Type_Code],[Cancel_DT],[Booking_type]");
        insertQuery.append(" ,[Sales_Person],[Cut_Off_date],[Cut_Off_days])");
        insertQuery.append("  VALUES (" + propertyID + ",'").append(rateCode).append("','").append(rateCode).append("','").append(rateCode).append("' ");
        insertQuery.append(" ,NULL,NULL,'DEFINITE','GROUP','").append(mktSegment).append("','").append(startDate.plusDays(startDateNumber)).append("','").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("',NULL,NULL,NULL,NULL,NULL,NULL)");
    }

    private void populateGroupMasterIncludeNonAdjustData(StringBuilder insertQuery, String rateCode, String groupStatusCode, int mktSegment, int startDateNumber, int endDateNumber, int bookingDateNumber) {
        insertQuery.append(" INSERT INTO [Group_Master]");
        insertQuery.append(" ([Property_ID],[Group_Code],[Group_Name],[Group_Description],[Master_Group_ID],[Master_Group_Code]");
        insertQuery.append(" ,[Group_Status_Code],[Group_Type_Code],[Mkt_Seg_ID],[Start_DT]");
        insertQuery.append(" ,[End_DT],[Booking_DT],[Pickup_Type_Code],[Cancel_DT],[Booking_type]");
        insertQuery.append(" ,[Sales_Person],[Cut_Off_date],[Cut_Off_days])");
        insertQuery.append("  VALUES (" + propertyID + ",'").append(rateCode).append("','").append(rateCode).append("','").append(rateCode).append("' ");
        insertQuery.append(" ,NULL,NULL,'").append(groupStatusCode).append("','GROUP','").append(mktSegment).append("','").append(startDate.plusDays(startDateNumber)).append("','").append(startDate.plusDays(endDateNumber)).append("','").append(startDate.minusDays(bookingDateNumber)).append("',NULL,NULL,NULL,NULL,NULL,NULL)");
    }

    private void populateGroupBlockData(StringBuilder insertQuery, String rateCode, int occupancyDateNumber, int accomTypeId, int mktSegId, int blockValue, int pickUpValue, double rate) {
        insertQuery.append(" INSERT INTO [Group_Block]");
        insertQuery.append(" ([Group_ID],[Occupancy_DT],[Accom_Type_ID],[Blocks],[Pickup],[Original_Blocks],[rate])");
        insertQuery.append("  VALUES ((select group_id from Group_Master where Group_Code='").append(rateCode).append("' and Mkt_Seg_ID=").append(mktSegId).append("),'").append(startDate.plusDays(occupancyDateNumber)).append("',").append(accomTypeId).append(",").append(blockValue).append(",").append(pickUpValue).append(",0,").append(rate).append(" )");
    }

    private LocalDate getLocalDate() {
        List<Object> caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + ",3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

    private int getGroupCode(String groupName, int mktSegId) {
        List<Object> groupIds = tenantCrudService().findByNativeQuery("select Group_ID from Group_Master where Group_Name='" + groupName + "' and Mkt_Seg_ID=" + mktSegId + " ");
        return Integer.parseInt(String.valueOf(groupIds.get(0)));
    }

    @BeforeEach
    public void setUp() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_WASH_INDIVIDUAL_GROUPS_ALTERNATE_EXCEL_EXPORT)).thenReturn(true);
        dateService = new DateService() {
            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getBusinessDate() {
                return DateUtil.convertLocalDateToJavaUtilDate(getLocalDate().minusDays(1));
            }

            @Override
            public Date getWebRateShoppingDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getUnqualifiedRateCaughtUpDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getBARDisplayWindowEndDate() {
                return DateUtil.getLastDayOfCurrentMonth();
            }

            @Override
            public Date getBARDisplayWindowStartDate() {
                return getCaughtUpDate();
            }
        };
        service.setDateService(dateService);
        service.setCrudService(tenantCrudService());
        service.setPacmanConfigParamsService(pacmanConfigParamsService);
        demandOverrideService = new DemandOverrideService();
        demandOverrideService.setCrudService(tenantCrudService());
        service.setDemandOverrideService(demandOverrideService);

        decisionService = DecisionService.createTestInstance();

        try {
            decisionService.setDateServiceLocal(dateService);
            decisionService.setCrudService(tenantCrudService());
            service.setDecisionService(decisionService);

            LocalDate endDate = LocalDate.now().plusYears(1);
            List<GroupBlockDetail> list = service.getGroupBlockData(11, DateUtil.convertJavaToJodaLocalDate(LocalDate.of(2011, 2, 1)), DateUtil.convertJavaToJodaLocalDate(endDate));
            assertFalse(CollectionUtils.isEmpty(list), "Should be able to find at least one Group_Block record, but could not.");
            for (GroupBlockDetail groupBlock : list) {
                if (groupBlock.getSystemWashByGroupOrNull() != null && groupBlock.getSystemWashByGroupOrNull().getActiveWashOverrideByGroup() != null) {
                    testGroupBlock = groupBlock;
                    testWashOverride = groupBlock.getSystemWashByGroupOrNull().getActiveWashOverrideByGroup();
                    testWashOverrideId = testWashOverride.getId();
                    break;
                }
            }
            assertNotNull(testGroupBlock, "Tests cannot run as there does not appear to be any active wash overrides by group (Wash_Ind_Group_FCST_OVR) currently in the database.");
            // Setup test block related information.
        } catch (Exception e) {
            throw new RuntimeException("Error in setup.", e);
        }
    }

    @Test
    public void shouldGiveMinRateAsLowestRateOnPeakNightForIndividualGroupWhenRateAndBlockIsNonZEROForAllRoomTypesForOccupancyDateAndPerformanceOptimizationIsEnabled() {
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransData(insertQuery, 5, 9, 7, 3, "XX", mktSeg1, rateCode1, "1");
        populateTransData(insertQuery, 4, 10, 10, 2, "XX", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 0, 11, 13, 0, "SS", mktSeg1, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 3, 15, 13, 0, "NS", mktSeg1, rateCode1, "4");
        populateTransDataForNoShows(insertQuery, 6, 15, 13, 0, "NS", mktSeg1, rateCode1, "5");
        populateTransDataForNoShows(insertQuery, 7, 12, 4, 0, "SS", mktSeg1, rateCode1, "6");
        populateTransDataForNoShows(insertQuery, 2, 13, 11, 0, "SS", mktSeg1, rateCode1, "7");
        populateTransDataForNoShows(insertQuery, 1, 14, 2, 0, "SS", mktSeg1, rateCode1, "8");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 4, 12, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode1, "TENTATIVE", mktSeg2, 8, 12, 12);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg1, 21, 20, 120.13);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId2, mktSeg1, 13, 12, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId3, mktSeg1, 15, 12, 151.99);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 0, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupId = getGroupCode(rateCode1, mktSeg1);
        int groupId2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(Arrays.asList(groupId, groupId2), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertEquals(new BigDecimal("120.13").setScale(5, RoundingMode.HALF_DOWN), (list.get(0).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        assertEquals(new BigDecimal("151.99").setScale(5, RoundingMode.HALF_DOWN), (list.get(1).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void shouldGiveMinRateAsLowestRateOnPeakNightForIndividualGroupWhenRateIsZeroForAllRoomTypesForHighestBlockOccupancyDateAndPerformanceOptimizationInEnabled() {
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransData(insertQuery, 5, 9, 7, 3, "XX", mktSeg1, rateCode1, "1");
        populateTransData(insertQuery, 4, 10, 10, 2, "XX", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 0, 11, 13, 0, "SS", mktSeg1, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 3, 15, 13, 0, "NS", mktSeg1, rateCode1, "4");
        populateTransDataForNoShows(insertQuery, 6, 15, 13, 0, "NS", mktSeg1, rateCode1, "5");
        populateTransDataForNoShows(insertQuery, 7, 12, 4, 0, "SS", mktSeg1, rateCode1, "6");
        populateTransDataForNoShows(insertQuery, 2, 13, 11, 0, "SS", mktSeg1, rateCode1, "7");
        populateTransDataForNoShows(insertQuery, 1, 14, 2, 0, "SS", mktSeg1, rateCode1, "8");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 4, 12, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode1, "TENTATIVE", mktSeg2, 8, 12, 12);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg1, 22, 21, 0);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId2, mktSeg1, 13, 11, 199.49);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId3, mktSeg1, 21, 20, 151.99);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 0, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupId = getGroupCode(rateCode1, mktSeg1);
        int groupId2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(Arrays.asList(groupId, groupId2), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)), false);
        assertEquals(new BigDecimal("151.99").setScale(5, RoundingMode.HALF_DOWN), (list.get(0).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        assertEquals(new BigDecimal("151.99").setScale(5, RoundingMode.HALF_DOWN), (list.get(1).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }

    @Test
    public void shouldGiveMinRateAsLowestRateOnPeakNightForIndividualGroupWhenRateIsZeroForOccupancyDateWithHighestBlockAndPerformanceOptimizationIsEnabledThenItShouldGiveLowestRateWithNextHighestBlockWithDiffOccupancyDate() {
        setWorkContextProperty(TestProperty.H2);
        StringBuilder insertQuery = new StringBuilder();
        startDate = getLocalDate();
        int occupancyDateNumber = 7;
        populateTransData(insertQuery, 5, 9, 7, 3, "XX", mktSeg1, rateCode1, "1");
        populateTransData(insertQuery, 4, 10, 10, 2, "XX", mktSeg1, rateCode1, "2");
        populateTransDataForNoShows(insertQuery, 0, 11, 13, 0, "SS", mktSeg1, rateCode1, "3");
        populateTransDataForNoShows(insertQuery, 3, 15, 13, 0, "NS", mktSeg1, rateCode1, "4");
        populateTransDataForNoShows(insertQuery, 6, 15, 13, 0, "NS", mktSeg1, rateCode1, "5");
        populateTransDataForNoShows(insertQuery, 7, 12, 4, 0, "SS", mktSeg1, rateCode1, "6");
        populateTransDataForNoShows(insertQuery, 2, 13, 11, 0, "SS", mktSeg1, rateCode1, "7");
        populateTransDataForNoShows(insertQuery, 1, 14, 2, 0, "SS", mktSeg1, rateCode1, "8");
        populateGroupMasterData(insertQuery, rateCode1, mktSeg1, 4, 12, 14);
        populateGroupMasterIncludeNonAdjustData(insertQuery, rateCode1, "TENTATIVE", mktSeg2, 8, 12, 12);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg1, 22, 21, 0);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId2, mktSeg1, 13, 11, 199.99);
        populateGroupBlockData(insertQuery, rateCode1, 8, accomTypeId3, mktSeg1, 21, 20, 151.99);
        populateGroupBlockData(insertQuery, rateCode1, occupancyDateNumber, accomTypeId1, mktSeg2, 15, 0, 151.99);
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
        int groupId = getGroupCode(rateCode1, mktSeg1);
        int groupId2 = getGroupCode(rateCode1, mktSeg2);
        List<GroupBlockExtendedDTO> list = service.getGroupWashData(Arrays.asList(groupId, groupId2), JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(occupancyDateNumber)),
                JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(8)), false);
        if (Objects.isNull(list.get(0).getLowestRateOnPeakNightValue())) {
            assertEquals(new BigDecimal("151.99").setScale(5, RoundingMode.HALF_DOWN), (list.get(1).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        }
        assertEquals(new BigDecimal("151.99").setScale(5, RoundingMode.HALF_DOWN), (list.get(2).getLowestRateOnPeakNightValue().setScale(5, RoundingMode.HALF_DOWN)));
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDIVIDUAL_GROUP_FOR_WASH_OVERRIDE_PERF_IMPROVED_ENABLED);
    }
}
