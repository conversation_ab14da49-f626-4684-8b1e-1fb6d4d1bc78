package com.ideas.tetris.pacman.services.webrate.entity;


import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestWebrateType extends AbstractG3JupiterTest {

    @Test
    public void testPersistWebrateType() {
        WebrateType webrateType = new WebrateType();
        webrateType.setWebrateTypeName("Webrate Type Name");
        webrateType.setWebrateTypeDescription("Webrate Type Description");
        WebrateType persistedWebrateType = tenantCrudService().save(webrateType);
        assertNotNull(persistedWebrateType.hashCode());
        assertTrue(webrateType.equals(persistedWebrateType));
        assertFalse(webrateType.equals(null));
        assertFalse(webrateType.equals(new Webrate()));
        assertNotNull(persistedWebrateType.toString());

        assertEquals(persistedWebrateType.getWebrateTypeName(), webrateType.getWebrateTypeName());

    }

    @Test
    public void testUpdateWebrateType() {
        WebrateType webrateType = UniqueWebrateTypeCreator.createWebrateType();
        webrateType.setWebrateTypeName("Updated Name");
        tenantCrudService().save(webrateType);
        WebrateType dbWebrateType = tenantCrudService().find(WebrateType.class, webrateType.getId());
        assertEquals(dbWebrateType.getWebrateTypeName(), webrateType.getWebrateTypeName());

    }

    @Test
    public void testDeleteWebrateType() {
        WebrateType webrateType = UniqueWebrateTypeCreator.createWebrateType();
        tenantCrudService().delete(WebrateType.class, webrateType.getId());
        WebrateType dbWebrateType = tenantCrudService().find(WebrateType.class, webrateType.getId());
        assertNull(dbWebrateType);
    }


    @Test
    public void testLoadWebrateTypeByCode() {
        WebrateType webrateType = createQuickObj();
        WebrateType dbWebrateType = tenantCrudService().findByNamedQuerySingleResult(WebrateType.BY_TYPE_CODE, QueryParameter.with("code", webrateType.getWebrateTypeCode()).parameters());
        assertNotNull(dbWebrateType);
        assertEquals(webrateType.getWebrateTypeCode(), dbWebrateType.getWebrateTypeCode());
    }

    @Test
    public void testLoadWebrateTypeByName() {
        WebrateType webrateType = createQuickObj();
        WebrateType dbWebrateType = tenantCrudService().findByNamedQuerySingleResult(WebrateType.BY_TYPE_NAME, QueryParameter.with("name", webrateType.getWebrateTypeName()).parameters());
        assertNotNull(dbWebrateType);
        assertEquals(webrateType.getWebrateTypeCode(), dbWebrateType.getWebrateTypeCode());
    }


    protected WebrateType createQuickObj() {
        WebrateType webrateType = new WebrateType();
        webrateType.setWebrateTypeName("Webrate Type Name");
        webrateType.setWebrateTypeDescription("Webrate Type Description");
        webrateType.setWebrateTypeCode("Code");
        WebrateType persistedWebrateType = tenantCrudService().save(webrateType);
        return persistedWebrateType;

    }
}
