package com.ideas.tetris.pacman.services.reportsquery.pricingoverridehistoryreport;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Created by idnrar on 03-09-2014.
 */
public class PricingOverrideHistoryCPDataTest extends AbstractG3JupiterTest {

    private LocalDate startDate;
    private int propertyID = 5;
    private int recordTypeId = 3;
    private int processStatusId = 13;
    private int isRolling = 0;
    private int accomTypeIdFour = 4;
    private String accomTypeDLX = "DELUXE";
    private String accomClassDLX = "DLX";

    private String accomTypeDOUBLE = "DOUBLE";
    private String accomClassSTD = "STD";

    private int accomTypeIdSix = 6;
    private int accomTypeIdEight = 8;
    private int allRoomTypeSelected = -1;

    private int decisionId1 = 425;
    private int decisionId2 = 426;
    private int decisionId3 = 427;
    private int decisionId4 = 428;
    private int decisionId5 = 429;
    private int decisionId6 = 430;

    private static final String finalBarValue = "218.12665";
    private static final String userOverrideValue = "234.12665";
    private static final String floorOverrideValue = "156.12665";
    private static final String ceilingOverrideValue = "203.12665";
    private static final String userOverride = "USER";
    private static final String floorOverride = "FLOOR";
    private static final String ceilOverride = "CEIL";
    private static final String ceilFloorOverride = "FLOORANDCEIL";
    private static final String noOverride = "NONE";
    private static final String pending = "PENDING";
    private static final String someOldValue = "239.00000";

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H1);
        createTestData();
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForNoneToUserOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdEight, userOverrideValue, decisionId1);
        InsertOverrideData(decisionId1, accomTypeIdEight, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdEight + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForNoneToUserOverrideForRollingDate_UsingFn() {
        isRolling = 1;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdEight, userOverrideValue, decisionId1);
        InsertOverrideData(decisionId1, accomTypeIdEight, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdEight + "," + isRolling + ",'TODAY+6','TODAY+6','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Rolling Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToUserOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, userOverride, userOverrideValue, String.valueOf(Double.valueOf(userOverrideValue) + 8), null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> User Override with Static Dates ", reportData, 1,
                userOverride, userOverride, String.valueOf(Double.valueOf(userOverrideValue) + 8), userOverrideValue, null, null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, floorOverride, userOverrideValue, null, null, floorOverrideValue, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, userOverride, null, userOverrideValue, null, null, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToCeilingOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilOverride, userOverrideValue, null, null, null, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Ceiling Override with Static Dates ", reportData, 0,
                ceilOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorCeilingOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilFloorOverride, userOverrideValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> CeilingFloor Override with Static Dates ", reportData, 0,
                ceilFloorOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToUserOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, userOverride, someOldValue, userOverrideValue, floorOverrideValue, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> User Override with Static Dates ", reportData, 1,
                userOverride, floorOverride, userOverrideValue, someOldValue, null, null, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToFloorOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, floorOverride, someOldValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, floorOverride, null, someOldValue, null, null, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, ceilOverride, floorOverrideValue, null, floorOverrideValue, null, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> CEIL Override with Static Dates ", reportData, 0,
                ceilOverride, floorOverride, null, floorOverrideValue, ceilingOverrideValue, null, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToFloorCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, ceilFloorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, floorOverride, null, floorOverrideValue, ceilingOverrideValue, null, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToUserOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, userOverride, someOldValue, userOverrideValue, null, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> User Override with Static Dates ", reportData, 1,
                userOverride, ceilOverride, userOverrideValue, someOldValue, null, ceilingOverrideValue, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToFloorOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, floorOverride, someOldValue, null, null, floorOverrideValue, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, ceilOverride, null, someOldValue, null, ceilingOverrideValue, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, ceilOverride, someOldValue, null, null, null, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, ceilOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 1,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToCeilFloorOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, ceilOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToUserOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, userOverride, floorOverrideValue, userOverrideValue, floorOverrideValue, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> User Override with Static Dates ", reportData, 1,
                userOverride, ceilFloorOverride, userOverrideValue, floorOverrideValue, null, ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToFloorOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, ceilFloorOverride, null, floorOverrideValue, null, ceilingOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionWhenUserIsActive_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertEquals(2, reportData.size());
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionWhenUserIsInActive_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        tenantCrudService().executeUpdateByNativeQuery("update Users set Status_Id = 2 where User_ID = 11403 ");
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertEquals(2, reportData.size());
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, ceilOverride, floorOverrideValue, null, floorOverrideValue, null, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, ceilFloorOverride, null, floorOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToFloorCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, ceilFloorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, ceilFloorOverride, null, floorOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorCeilToRevertOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForPendingStatusInMainTable(accomTypeIdSix, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilFloorOverride, userOverrideValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 2,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Revert Override with Static Dates ", reportData, 1,
                pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToRevertToFloorCeilToRevertToCeilOverrideForStaticDate_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId3);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId3, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false') ");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 4,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 3,
                pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 2,
                pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToRevertToFloorCeilToRevertToCeilOverrideForStaticDateForAllRoomTypes_UsingFn() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId3);
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdFour, ceilingOverrideValue, finalBarValue, decisionId6);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId4, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId4, accomTypeIdFour, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId5, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId5, accomTypeIdFour, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId3, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        InsertOverrideData(decisionId6, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from ufn_get_pricing_override_history_cp_report (" + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + allRoomTypeSelected + "," + isRolling + ",'','','false') ");
        assertEquals(accomTypeDLX, (reportData.get(0)[4].toString()), "Pricing Override History Report For First Room Type");
        assertEquals(accomTypeDOUBLE, (reportData.get(5)[4].toString()), "Pricing Override History Report For Second Room Type");
        assertEquals(accomClassDLX, (reportData.get(0)[3].toString()), "Pricing Override History Report For First Accom Class");
        assertEquals(accomClassSTD, (reportData.get(5)[3].toString()), "Pricing Override History Report For Second Accom Class");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 9, userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 4, userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 8, pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 3, pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 6, ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 1, ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 2, pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 7, pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 5, ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 0, ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForNoneToUserOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdEight, userOverrideValue, decisionId1);
        InsertOverrideData(decisionId1, accomTypeIdEight, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdEight + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForNoneToUserOverrideForRollingDate_UsingSp() {
        isRolling = 1;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdEight, userOverrideValue, decisionId1);
        InsertOverrideData(decisionId1, accomTypeIdEight, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdEight + "," + isRolling + ",'TODAY+6','TODAY+6','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Rolling Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToUserOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, userOverride, userOverrideValue, String.valueOf(Double.valueOf(userOverrideValue) + 8), null, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 0,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> User Override with Static Dates ", reportData, 1,
                userOverride, userOverride, String.valueOf(Double.valueOf(userOverrideValue) + 8), userOverrideValue, null, null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, floorOverride, userOverrideValue, null, null, floorOverrideValue, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, userOverride, null, userOverrideValue, null, null, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToCeilingOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilOverride, userOverrideValue, null, null, null, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Ceiling Override with Static Dates ", reportData, 0,
                ceilOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorCeilingOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilFloorOverride, userOverrideValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 1,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> CeilingFloor Override with Static Dates ", reportData, 0,
                ceilFloorOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToUserOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, userOverride, someOldValue, userOverrideValue, floorOverrideValue, null, null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> User Override with Static Dates ", reportData, 1,
                userOverride, floorOverride, userOverrideValue, someOldValue, null, null, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToFloorOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, floorOverride, someOldValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), null, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, floorOverride, null, someOldValue, null, null, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, ceilOverride, floorOverrideValue, null, floorOverrideValue, null, null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> CEIL Override with Static Dates ", reportData, 0,
                ceilOverride, floorOverride, null, floorOverrideValue, ceilingOverrideValue, null, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorToFloorCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, floorOverride, someOldValue, null, null, floorOverrideValue, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), floorOverride, ceilFloorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), null, ceilingOverrideValue);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, noOverride, null, someOldValue, null, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Floor -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, floorOverride, null, floorOverrideValue, ceilingOverrideValue, null, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToUserOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, userOverride, someOldValue, userOverrideValue, null, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> User Override with Static Dates ", reportData, 1,
                userOverride, ceilOverride, userOverrideValue, someOldValue, null, ceilingOverrideValue, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToFloorOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, floorOverride, someOldValue, null, null, floorOverrideValue, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> Floor Override with Static Dates ", reportData, 1,
                floorOverride, ceilOverride, null, someOldValue, null, ceilingOverrideValue, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, ceilOverride, someOldValue, null, null, null, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, ceilOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 1,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForCeilToCeilFloorOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), floorOverrideValue, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Ceil -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, ceilOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, floorOverrideValue, null);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToUserOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForUserOverrideInMainTable(accomTypeIdSix, userOverrideValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, userOverride, floorOverrideValue, userOverrideValue, floorOverrideValue, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> User Override with Static Dates ", reportData, 1,
                userOverride, ceilFloorOverride, userOverrideValue, floorOverrideValue, null, ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToFloorOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Floor Override with Static Dates ", reportData, 0,
                floorOverride, ceilFloorOverride, null, floorOverrideValue, null, ceilingOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionWhenUserIsActive_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertEquals(2, reportData.size());
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionWhenUserIsInActive_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, floorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, null);
        tenantCrudService().executeUpdateByNativeQuery("update Users set Status_Id = 2 where User_ID = 11403 ");
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertEquals(2, reportData.size());
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, ceilOverride, floorOverrideValue, null, floorOverrideValue, null, ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, ceilFloorOverride, null, floorOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForFloorCeilToFloorCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(accomTypeIdSix, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), String.valueOf(Double.valueOf(floorOverrideValue) + 8), finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, ceilFloorOverride, floorOverrideValue, null, floorOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), ceilingOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> floorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, ceilFloorOverride, null, floorOverrideValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), ceilingOverrideValue, String.valueOf(Double.valueOf(floorOverrideValue) + 8), floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToFloorCeilToRevertOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForPendingStatusInMainTable(accomTypeIdSix, finalBarValue, decisionId2);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, ceilFloorOverride, userOverrideValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 2,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> floorCeil Override with Static Dates ", reportData, 0,
                ceilFloorOverride, userOverride, null, userOverrideValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For floorCeil -> Revert Override with Static Dates ", reportData, 1,
                pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToRevertToFloorCeilToRevertToCeilOverrideForStaticDate_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId3);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId3, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + accomTypeIdSix + "," + isRolling + ",'','','false'");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 4,
                userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 3,
                pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 1,
                ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 2,
                pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 0,
                ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);

    }

    @Test
    public void shouldValidateCPPricingOverrideReportFunctionForUserToRevertToFloorCeilToRevertToCeilOverrideForStaticDateForAllRoomTypes_UsingSp() {
        isRolling = 0;
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdSix, ceilingOverrideValue, finalBarValue, decisionId3);
        UpdateRoomTypeDataForCPForCeilOverrideInMainTable(accomTypeIdFour, ceilingOverrideValue, finalBarValue, decisionId6);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId4, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, userOverride, someOldValue, userOverrideValue, null, null, null, null);
        InsertOverrideData(decisionId1, accomTypeIdSix, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId4, accomTypeIdFour, startDate.plusDays(6).toString(), userOverride, pending, userOverrideValue, null, null, null, null, null);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId5, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, ceilFloorOverride, someOldValue, null, null, floorOverrideValue, null, ceilingOverrideValue);
        InsertOverrideData(decisionId2, accomTypeIdSix, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId5, accomTypeIdFour, startDate.plusDays(6).toString(), ceilFloorOverride, pending, null, null, floorOverrideValue, null, ceilingOverrideValue, null);
        InsertOverrideData(decisionId3, accomTypeIdSix, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        InsertOverrideData(decisionId6, accomTypeIdFour, startDate.plusDays(6).toString(), noOverride, ceilOverride, someOldValue, null, null, null, null, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8));
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("exec dbo.usp_get_pricing_override_history_cp_report " + propertyID + ",'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + allRoomTypeSelected + "," + isRolling + ",'','','false'");
        assertEquals(accomTypeDLX, (reportData.get(0)[4].toString()), "Pricing Override History Report For First Room Type");
        assertEquals(accomTypeDOUBLE, (reportData.get(5)[4].toString()), "Pricing Override History Report For Second Room Type");
        assertEquals(accomClassDLX, (reportData.get(0)[3].toString()), "Pricing Override History Report For First Accom Class");
        assertEquals(accomClassSTD, (reportData.get(5)[3].toString()), "Pricing Override History Report For Second Accom Class");
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 9, userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For None -> User Override with Static Dates ", reportData, 4, userOverride, noOverride, userOverrideValue, someOldValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 8, pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For User -> Revert Override with Static Dates ", reportData, 3, pending, userOverride, null, userOverrideValue, null, null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 6, ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> FloorCeil Override with Static Dates ", reportData, 1, ceilFloorOverride, noOverride, null, someOldValue, ceilingOverrideValue, null, floorOverrideValue, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 2, pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For FloorCeil -> Revert Override with Static Dates ", reportData, 7, pending, ceilFloorOverride, null, null, null, ceilingOverrideValue, null, floorOverrideValue);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 5, ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);
        assertPricingOverrideHistoryReportCPFunction("Pricing Override History Report For Revert -> Ceil Override with Static Dates ", reportData, 0, ceilOverride, noOverride, null, someOldValue, String.valueOf(Double.valueOf(ceilingOverrideValue) + 8), null, null, null);
    }

    private void assertPricingOverrideHistoryReportCPFunction(String Level, List<Object[]> reportData, int rowNumber, String newOverride, String oldOverride, String newBarRate, String oldBarRate, String newCeilRate, String oldCeilRate, String newFloorRate, String oldFloorRate) {
        assertEquals(newOverride, (reportData.get(rowNumber)[6].toString()), Level);
        assertEquals(oldOverride, (reportData.get(rowNumber)[7].toString()), Level);
        if (newBarRate != null) {
            assertEquals(newBarRate, (reportData.get(rowNumber)[8].toString()), Level);
        }
        if (oldBarRate != null) {
            assertEquals(oldBarRate, (reportData.get(rowNumber)[9].toString()), Level);
        }
        if (newCeilRate != null) {
            assertEquals(newCeilRate, (reportData.get(rowNumber)[10].toString()), Level);
        }
        if (oldCeilRate != null) {
            assertEquals(oldCeilRate, (reportData.get(rowNumber)[11].toString()), Level);
        }
        if (newFloorRate != null) {
            assertEquals(newFloorRate, (reportData.get(rowNumber)[12].toString()), Level);
        }
        if (oldFloorRate != null) {
            assertEquals(oldFloorRate, (reportData.get(rowNumber)[13].toString()), Level);
        }


    }

    private void createTestData() {
        startDate = getLocalDate();
        createTestDecisionIds();
    }

    private void InsertOverrideData(int decisionId, int accomTypeId, String arrivalDate, String oldOverride, String newOverride,
                                    String oldBar, String newBar, String oldFloorRate, String newFloorRate,
                                    String oldCeilRate, String newCeilRate) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" INSERT INTO [CP_Decision_Bar_Output_OVR] ");
        insertQuery.append(" ([Property_ID],[Decision_ID],[Product_ID],[Accom_Type_ID],[Arrival_DT], ");
        insertQuery.append(" [LOS],[User_ID],[Old_Override],[New_Override],[Old_BAR],[New_BAR], ");
        insertQuery.append(" [Old_Floor_Rate],[New_Floor_Rate],[Old_Ceil_Rate],[New_Ceil_Rate],[CreateDate]) ");
        insertQuery.append(" VALUES (" + propertyID + "," + decisionId + ",1," + accomTypeId + ",'" + arrivalDate + "' ");
        insertQuery.append(" ,-1,11403,'" + oldOverride + "','" + newOverride + "' ");
        insertQuery.append(" ," + oldBar + "," + newBar + "," + oldFloorRate + " ");
        insertQuery.append(" ," + newFloorRate + "," + oldCeilRate + ", ");
        insertQuery.append(" " + newCeilRate + ",'" + startDate.toString() + "') ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void createTestDecisionIds() {
        StringBuilder insertQuery = new StringBuilder();

        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(1) + "') as theDayName");

        // Inserting test data in Decision
        insertQuery.append(" SET IDENTITY_INSERT [dbo].[Decision] ON ");
        insertQuery.append("INSERT [dbo].[Decision] ");
        insertQuery.append(" ([Decision_ID], [Property_ID], [Business_DT], [Caught_up_DTTM], [Rate_Unqualified_DTTM] ");
        insertQuery.append(",[WebRate_DTTM], [Decision_Type_ID], [Start_DTTM], [End_DTTM], [Process_Status_ID], [CreateDate_DTTM] )");


        insertQuery.append(" VALUES (" + decisionId1 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', CAST(0x0000A3990012F390 AS DateTime),CAST(0x00009E0C0089B6FD AS DateTime) ");
        insertQuery.append(",NULL, 4,CAST(0x0000A39B00477CC9 AS DateTime), CAST(0x0000A39B00477CC9 AS DateTime), 2, CAST(0x0000A39B00477CCC AS DateTime)) ");

        insertQuery.append(",(" + decisionId2 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', CAST(0x0000A3990012F390 AS DateTime),CAST(0x00009E0C0089B6FD AS DateTime) ");
        insertQuery.append(",NULL, 4,CAST(0x0000A39B00477CC9 AS DateTime), CAST(0x0000A39B00477CC9 AS DateTime), 2, CAST(0x0000A39B00477CCC AS DateTime)) ");

        insertQuery.append(" ,(" + decisionId3 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', CAST(0x0000A3990012F390 AS DateTime),CAST(0x00009E0C0089B6FD AS DateTime) ");
        insertQuery.append(",NULL, 4,CAST(0x0000A39B00477CC9 AS DateTime), CAST(0x0000A39B00477CC9 AS DateTime), 2, CAST(0x0000A39B00477CCC AS DateTime)) ");


        insertQuery.append(" ,(" + decisionId4 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', GETDATE(),GETDATE()  ");
        insertQuery.append(",NULL, 4,GETDATE(), GETDATE(), 2, GETDATE() ) ");

        insertQuery.append(" ,(" + decisionId5 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', GETDATE(),GETDATE()  ");
        insertQuery.append(",NULL, 4,GETDATE(), GETDATE(), 2, GETDATE() ) ");

        insertQuery.append(" ,(" + decisionId6 + ", " + propertyID + ", '" + startDate.minusDays(1).toString() + "', GETDATE(),GETDATE()  ");
        insertQuery.append(",NULL, 4,GETDATE(), GETDATE(), 2, GETDATE() ) ");

        insertQuery.append(" SET IDENTITY_INSERT [dbo].[Decision] OFF");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }


    private void UpdateRoomTypeDataForCPForPendingStatusInMainTable(int accomTypeId, String rateValue, int decisionId) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" update CP_Decision_Bar_Output set Decision_ID=" + decisionId + ",Final_BAR=" + rateValue + ",Override='NONE' where Accom_Type_ID=" + accomTypeId + " and Arrival_DT='" + startDate.plusDays(6).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void UpdateRoomTypeDataForCPForUserOverrideInMainTable(int accomTypeId, String rateValue, int decisionId) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" update CP_Decision_Bar_Output set Decision_ID=" + decisionId + ",Final_BAR=" + rateValue + ",Override='USER',User_Specified_Rate=" + rateValue + " where Accom_Type_ID=" + accomTypeId + " and Arrival_DT='" + startDate.plusDays(6).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void UpdateRoomTypeDataForCPForCeilOverrideInMainTable(int accomTypeId, String ceilingRateValue, String finalBarValue, int decisionId) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" update CP_Decision_Bar_Output set Decision_ID=" + decisionId + ",Final_BAR=" + finalBarValue + ",Override='CEIL',Ceil_Rate=" + ceilingRateValue + " where Accom_Type_ID=" + accomTypeId + " and Arrival_DT='" + startDate.plusDays(6).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void UpdateRoomTypeDataForCPForFloorOverrideInMainTable(int accomTypeId, String floorRateValue, String finalBarValue, int decisionId) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" update CP_Decision_Bar_Output set Decision_ID=" + decisionId + ",Final_BAR=" + finalBarValue + ",Override='FLOOR',Floor_Rate=" + floorRateValue + " where Accom_Type_ID=" + accomTypeId + " and Arrival_DT='" + startDate.plusDays(6).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void UpdateRoomTypeDataForCPForFloorAndCeilOverrideInMainTable(int accomTypeId, String ceilingRateValue, String floorRateValue, String finalBarValue, int decisionId) {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append(" update CP_Decision_Bar_Output set Decision_ID=" + decisionId + ",Pretty_BAR=" + finalBarValue + ",Override='FLOORANDCEIL',Floor_Rate=" + floorRateValue + ",Ceil_Rate=" + ceilingRateValue + " where Accom_Type_ID=" + accomTypeId + " and Arrival_DT='" + startDate.plusDays(6).toString() + "' ");
        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }


    private LocalDate getLocalDate() {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + "," + recordTypeId + "," + processStatusId + ")");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }

}
