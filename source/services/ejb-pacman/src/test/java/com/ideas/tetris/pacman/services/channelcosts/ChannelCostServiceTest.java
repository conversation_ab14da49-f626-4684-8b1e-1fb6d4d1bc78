package com.ideas.tetris.pacman.services.channelcosts;


import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelCost;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSourceSelectionView;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSourceView;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSrcDailyFcst;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSrcFcst;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSrcFcstDateEntry;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSrcFcstDto;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelSrcFcstKPIDTO;
import com.ideas.tetris.pacman.services.channelcosts.entity.DOWType;
import com.ideas.tetris.pacman.services.channelcosts.entity.ExcludedRate;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.ChannelCostSource;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetric2;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.MetricResponse;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_FORMAT_DD_MMM_YYYY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ChannelCostServiceTest {
    private SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT_DD_MMM_YYYY);
    private DateFormat format = new SimpleDateFormat(DATE_FORMAT_DD_MMM_YYYY);

    private static final int PROPERTY_ID = 913;
    private static final int CLIENT_ID = 526;
    private static final String PROPERTY_CODE = "BOSCO";
    private static final String CLIENT_CODE = "Hilton";
    private static final String CLIENT_NAME = "Hilton Hotels";
    private static final String PROPERTY_NAME = "Boston Hotel";

    @Mock
    @TenantCrudServiceBean.Qualifier
    private CrudService tenantCrudService;
    @InjectMocks
    private ChannelCostService service;
    @Mock
    private DashboardService dashboardService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(PROPERTY_ID);
        workContext.setPropertyCode(PROPERTY_CODE);
        workContext.setClientId(CLIENT_ID);
        workContext.setClientCode(CLIENT_CODE);
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    @Test
    public void test_createCostsForNewSources() {
        List<String> newSources = Arrays.asList("SOURCE1", "SOURCE2");
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.save(Mockito.anyListOf(ChannelCost.class))).thenReturn(entities);
        List<ChannelCost> results = service.createCostsForNewSources(newSources);
        assertEquals(entities, results);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<ChannelCost> savedEntities = saveCaptor.getValue();
        assertEquals(2, savedEntities.size());
        ChannelCost savedEntity = savedEntities.get(0);
        assertEquals("SOURCE1", savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
        savedEntity = savedEntities.get(1);
        assertEquals("SOURCE2", savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
    }

    @Test
    public void test_getChannelCosts() {
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.findAll(ChannelCost.class)).thenReturn(entities);
        List<ChannelCost> results = service.getChannelCosts();
        assertEquals(entities, results);
    }

    @Test
    public void test_updateChannelCostSource_sourceBooking() {
        ChannelCostSource SOURCE = ChannelCostSource.SOURCE_BOOKING;
        TenantProperty property = new TenantProperty();
        property.setChannelCostSource(ChannelCostSource.CHANNEL);
        when(tenantCrudService.find(TenantProperty.class, PROPERTY_ID)).thenReturn(property);
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.<ChannelCost>save(Mockito.anyList())).thenReturn(entities);
        String SOURCE_BOOKING = "mySourceBooking";
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_SOURCE_BOOKINGS)).thenReturn(Collections.singletonList(SOURCE_BOOKING));
        List<ChannelCost> results = service.updateChannelCostSource(SOURCE);
        assertEquals(entities, results);
        assertEquals(SOURCE, property.getChannelCostSource());
        assertFalse(property.isApplicationManagesIndexRebuild());
        verify(tenantCrudService).save(property);
        verify(tenantCrudService).deleteAll(ExcludedRate.class);
        verify(tenantCrudService).deleteAll(ChannelCost.class);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<ChannelCost> savedEntities = saveCaptor.getValue();
        assertEquals(1, savedEntities.size());
        ChannelCost savedEntity = savedEntities.get(0);
        assertEquals(SOURCE_BOOKING, savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
    }

    @Test
    public void test_updateChannelCostSource_channel() {
        ChannelCostSource SOURCE = ChannelCostSource.CHANNEL;
        TenantProperty property = new TenantProperty();
        property.setChannelCostSource(ChannelCostSource.SOURCE_BOOKING);
        when(tenantCrudService.find(TenantProperty.class, PROPERTY_ID)).thenReturn(property);
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.<ChannelCost>save(Mockito.anyList())).thenReturn(entities);
        String CHANNEL = "myChannel";
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_CHANNELS)).thenReturn(Collections.singletonList(CHANNEL));
        List<ChannelCost> results = service.updateChannelCostSource(SOURCE);
        assertEquals(entities, results);
        assertEquals(SOURCE, property.getChannelCostSource());
        verify(tenantCrudService).save(property);
        verify(tenantCrudService).deleteAll(ExcludedRate.class);
        verify(tenantCrudService).deleteAll(ChannelCost.class);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<ChannelCost> savedEntities = saveCaptor.getValue();
        assertEquals(1, savedEntities.size());
        ChannelCost savedEntity = savedEntities.get(0);
        assertEquals(CHANNEL, savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
    }

    @Test
    public void test_updateChannelCostSource_rateCode() {
        ChannelCostSource SOURCE = ChannelCostSource.RATE_CODE;
        TenantProperty property = new TenantProperty();
        property.setChannelCostSource(ChannelCostSource.SOURCE_BOOKING);
        when(tenantCrudService.find(TenantProperty.class, PROPERTY_ID)).thenReturn(property);
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.<ChannelCost>save(Mockito.anyList())).thenReturn(entities);
        String RATE_CODE = "myRateCode";
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_RATE_CODES)).thenReturn(Collections.singletonList(RATE_CODE));
        List<ChannelCost> results = service.updateChannelCostSource(SOURCE);
        assertEquals(entities, results);
        assertEquals(SOURCE, property.getChannelCostSource());
        verify(tenantCrudService).save(property);
        verify(tenantCrudService).deleteAll(ExcludedRate.class);
        verify(tenantCrudService).deleteAll(ChannelCost.class);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<ChannelCost> savedEntities = saveCaptor.getValue();
        assertEquals(1, savedEntities.size());
        ChannelCost savedEntity = savedEntities.get(0);
        assertEquals(RATE_CODE, savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
    }

    @Test
    public void test_updateChannelCostSource_channelAndSourceBooking() {
        ChannelCostSource SOURCE = ChannelCostSource.CHANNEL_AND_SOURCE_BOOKING;
        TenantProperty property = new TenantProperty();
        property.setChannelCostSource(ChannelCostSource.SOURCE_BOOKING);
        when(tenantCrudService.find(TenantProperty.class, PROPERTY_ID)).thenReturn(property);
        List<ChannelCost> entities = new ArrayList<>();
        when(tenantCrudService.<ChannelCost>save(Mockito.anyList())).thenReturn(entities);
        String CHANNEL = "myChannel";
        String SOURCE_BOOKING = "mySourceBooking";
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_CHANNELS)).thenReturn(Collections.singletonList(CHANNEL));
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_SOURCE_BOOKINGS)).thenReturn(Collections.singletonList(SOURCE_BOOKING));
        List<ChannelCost> results = service.updateChannelCostSource(SOURCE);
        assertEquals(entities, results);
        assertEquals(SOURCE, property.getChannelCostSource());
        verify(tenantCrudService).save(property);
        verify(tenantCrudService).deleteAll(ExcludedRate.class);
        verify(tenantCrudService).deleteAll(ChannelCost.class);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<ChannelCost> savedEntities = saveCaptor.getValue();
        assertEquals(1, savedEntities.size());
        ChannelCost savedEntity = savedEntities.get(0);
        assertEquals(CHANNEL + "/" + SOURCE_BOOKING, savedEntity.getCostSourceName());
        assertNull(savedEntity.getDescription());
        assertEquals(BigDecimal.ZERO, savedEntity.getFixedCost());
        assertEquals(BigDecimal.ZERO, savedEntity.getPercentageCost());
    }

    @Test
    public void test_saveChannelCosts() {
        List<ChannelCost> entities = new ArrayList<>();
        List<ChannelCost> results = service.saveChannelCosts(entities);
        assertEquals(entities, results);
        verify(tenantCrudService).deleteAll(ExcludedRate.class);
        verify(tenantCrudService).save(entities);
    }

    @Test
    public void test_getChannelCostSource() {
        ChannelCostSource SOURCE = ChannelCostSource.SOURCE_BOOKING;
        TenantProperty property = new TenantProperty();
        property.setChannelCostSource(SOURCE);
        when(tenantCrudService.find(TenantProperty.class, PROPERTY_ID)).thenReturn(property);
        ChannelCostSource result = service.getChannelCostSource();
        assertEquals(SOURCE, result);
    }

    @Test
    public void test_getAllRateCodesFromTransactions() {
        String RATE_CODE = "myRateCode";
        when(tenantCrudService.findByNamedQuery(IndividualTransactions.GET_RATE_CODES)).thenReturn(Collections.singletonList(RATE_CODE));
        List<String> results = service.getAllRateCodesFromTransactions();
        assertEquals(1, results.size());
        assertEquals(RATE_CODE, results.get(0));
    }

    @Test
    public void testGetChannelSrcFcstInRange() {
        Date date = new Date();
        ChannelSrcFcstDto mockedDto = Mockito.mock(ChannelSrcFcstDto.class);
        List<Integer> channelIds = Arrays.asList(1);
        List<ChannelSrcFcstDto> expected = Arrays.asList(mockedDto);
        ArgumentCaptor<RowMapper<ChannelSrcFcstDto>> channelSrcFcstDtoRowMapper = ArgumentCaptor.forClass(RowMapper.class);
        when(tenantCrudService.findByNativeQuery(Mockito.anyString(),
                Mockito.anyMap(),
                channelSrcFcstDtoRowMapper.capture()))
                .thenReturn(expected);

        List<ChannelSrcFcstDto> result = service.getChannelSrcFcstInRange(date, date, true, true, channelIds);
        assertEquals(expected, result);
        verify(tenantCrudService).findByNativeQuery(Mockito.anyString(),
                Mockito.anyMap(),
                channelSrcFcstDtoRowMapper.capture());
    }

    @Test
    public void shouldGetDailyChannelSrcFcstInRange() throws ParseException {
        Date date = new Date();
        List<Integer> channelIds = Arrays.asList(1);
        ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<RowMapper<ChannelSrcFcstDto>> channelSrcFcstDtoRowMapper = ArgumentCaptor.forClass(RowMapper.class);

        service.getDailyChannelSrcFcstInRange(date, date, true, true, channelIds, true, true);
        verify(tenantCrudService).findByNativeQuery(eq(ChannelSrcDailyFcst.GET_CHANNEL_SRC_FCST_IN_RANGE),
                queryParameterCaptor.capture(),
                channelSrcFcstDtoRowMapper.capture());
        assertEquals(date, queryParameterCaptor.getValue().get("endDate"));
        assertEquals(date, queryParameterCaptor.getValue().get("startDate"));
        assertEquals(true, queryParameterCaptor.getValue().get("isWeekday"));
        assertEquals(true, queryParameterCaptor.getValue().get("isWeekend"));
        assertEquals(channelIds, queryParameterCaptor.getValue().get("selectedMappingIds"));
        assertEquals(true, queryParameterCaptor.getValue().get("isChannel"));
    }

    @Test
    public void shouldGetMonthlyFromChannelSrcDailyFcstInRange() throws ParseException {
        Date date = new Date();
        List<Integer> channelIds = Arrays.asList(1);
        ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<RowMapper<ChannelSrcFcstDto>> channelSrcFcstDtoRowMapper = ArgumentCaptor.forClass(RowMapper.class);

        service.getDailyChannelSrcFcstInRange(date, date, true, true, channelIds, true, false);
        verify(tenantCrudService).findByNativeQuery(eq(ChannelSrcDailyFcst.GET_CHANNEL_SRC_FCST_IN_RANGE_FOR_MONTH),
                queryParameterCaptor.capture(),
                channelSrcFcstDtoRowMapper.capture());
        assertEquals(date, queryParameterCaptor.getValue().get("endDate"));
        assertEquals(date, queryParameterCaptor.getValue().get("startDate"));
        assertEquals(true, queryParameterCaptor.getValue().get("isWeekday"));
        assertEquals(true, queryParameterCaptor.getValue().get("isWeekend"));
        assertEquals(channelIds, queryParameterCaptor.getValue().get("selectedMappingIds"));
        assertEquals(true, queryParameterCaptor.getValue().get("isChannel"));
    }

    @Test
    public void testTransformChannelSrcFcstToFusionChartDto() {
        Date monthStartDate = new Date();
        String item1Name = "item1";
        String item2Name = "item2";

        ChannelSrcFcstDto item1 = new ChannelSrcFcstDto();
        item1.setChannelName(item1Name);
        item1.setMonthStartDate(monthStartDate);
        item1.setWeekend(true);
        item1.setRoomsSoldFcst(BigDecimal.valueOf(10));
        item1.setRoomRevenueFcst(BigDecimal.valueOf(100));
        item1.setChannelCostFcst(BigDecimal.valueOf(200));
        item1.setRoomsSoldOnbooks(20);
        item1.setRoomRevenueOnbooks(BigDecimal.valueOf(300));
        item1.setChannelCostOnbooks(BigDecimal.valueOf(400));
        ChannelSrcFcstDto item2 = new ChannelSrcFcstDto();
        item2.setChannelName(item2Name);
        item2.setMonthStartDate(monthStartDate);
        item2.setWeekend(false);
        item2.setRoomsSoldFcst(BigDecimal.valueOf(30));
        item2.setRoomRevenueFcst(BigDecimal.valueOf(500));
        item2.setChannelCostFcst(BigDecimal.valueOf(600));
        item2.setRoomsSoldOnbooks(40);
        item2.setRoomRevenueOnbooks(BigDecimal.valueOf(700));
        item2.setChannelCostOnbooks(BigDecimal.valueOf(800));

        Map<String, Map<String, List<ChannelSrcFcstDateEntry>>> result = service.transformChannelSrcFcstToFusionChartDto(Arrays.asList(item1, item2), DateUtil.DATE_FORMAT_MMM_YYYY_SPACED, false);
        assertEquals(2, result.size());
        assertEquals(1, result.get(item1Name).size());
        assertEquals(1, result.get(item2Name).size());
        List<ChannelSrcFcstDateEntry> item1Results = result.get(item1Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED));
        assertEquals(1, item1Results.size());
        List<ChannelSrcFcstDateEntry> item2Results = result.get(item2Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED));
        assertEquals(1, item2Results.size());

        ChannelSrcFcstDateEntry firstItem = item1Results.get(0);
        assertTrue(firstItem.isWeekend());
        assertEquals(BigDecimal.valueOf(10), firstItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(100), firstItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(200), firstItem.getChannelCostFcst());
        assertEquals(20, firstItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(300), firstItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(400), firstItem.getChannelCostOnbooks());

        ChannelSrcFcstDateEntry secondItem = item2Results.get(0);
        assertFalse(secondItem.isWeekend());
        assertEquals(BigDecimal.valueOf(30), secondItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(500), secondItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(600), secondItem.getChannelCostFcst());
        assertEquals(40, secondItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(700), secondItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(800), secondItem.getChannelCostOnbooks());

        ChannelSrcFcstDto item3 = new ChannelSrcFcstDto();
        item3.setChannelName(item2Name);
        item3.setMonthStartDate(monthStartDate);
        item3.setWeekend(false);
        item3.setRoomsSoldFcst(BigDecimal.valueOf(30));
        item3.setRoomRevenueFcst(BigDecimal.valueOf(500));
        item3.setChannelCostFcst(BigDecimal.valueOf(600));
        item3.setRoomsSoldOnbooks(40);
        item3.setRoomRevenueOnbooks(BigDecimal.valueOf(700));
        item3.setChannelCostOnbooks(BigDecimal.valueOf(800));
        ChannelSrcFcstDto item4 = new ChannelSrcFcstDto();
        item4.setChannelName(item2Name);
        item4.setMonthStartDate(DateUtils.addMonths(new Date(), 1));
        item4.setWeekend(false);
        item4.setRoomsSoldFcst(BigDecimal.valueOf(30));
        item4.setRoomRevenueFcst(BigDecimal.valueOf(500));
        item4.setChannelCostFcst(BigDecimal.valueOf(600));
        item4.setRoomsSoldOnbooks(40);
        item4.setRoomRevenueOnbooks(BigDecimal.valueOf(700));
        item4.setChannelCostOnbooks(BigDecimal.valueOf(800));
        result = service.transformChannelSrcFcstToFusionChartDto(Arrays.asList(item1, item2, item3, item4), DateUtil.DATE_FORMAT_MMM_YYYY_SPACED, false);

        assertEquals(2, result.size());
        assertEquals(1, result.get(item1Name).size());
        assertEquals(2, result.get(item2Name).size());
        item1Results = result.get(item1Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED));
        assertEquals(1, item1Results.size());
        item2Results = result.get(item2Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED));
        assertEquals(2, item2Results.size());
    }

    @Test
    public void shouldTransformChannelSrcFcstToFusionChartDtoForDailyForecastWhenThereAreMultipleSourcesForChannel() {
        Date monthStartDate = new Date();
        String item1Name = "item1";
        String item2Name = "item2";

        ChannelSrcFcstDto item1 = new ChannelSrcFcstDto();
        item1.setChannelName(item1Name);
        item1.setMonthStartDate(monthStartDate);
        item1.setWeekend(true);
        item1.setRoomsSoldFcst(BigDecimal.valueOf(10));
        item1.setRoomRevenueFcst(BigDecimal.valueOf(100));
        item1.setChannelCostFcst(BigDecimal.valueOf(200));
        item1.setRoomsSoldOnbooks(20);
        item1.setRoomRevenueOnbooks(BigDecimal.valueOf(300));
        item1.setChannelCostOnbooks(BigDecimal.valueOf(400));

        ChannelSrcFcstDto item2 = new ChannelSrcFcstDto();
        item2.setChannelName(item2Name);
        item2.setMonthStartDate(monthStartDate);
        item2.setWeekend(false);
        item2.setRoomsSoldFcst(BigDecimal.valueOf(30));
        item2.setRoomRevenueFcst(BigDecimal.valueOf(500));
        item2.setChannelCostFcst(BigDecimal.valueOf(600));
        item2.setRoomsSoldOnbooks(40);
        item2.setRoomRevenueOnbooks(BigDecimal.valueOf(700));
        item2.setChannelCostOnbooks(BigDecimal.valueOf(800));

        ChannelSrcFcstDto item3 = new ChannelSrcFcstDto();
        item3.setChannelName(item1Name);
        item3.setMonthStartDate(monthStartDate);
        item3.setWeekend(true);
        item3.setRoomsSoldFcst(BigDecimal.valueOf(10));
        item3.setRoomRevenueFcst(BigDecimal.valueOf(100));
        item3.setChannelCostFcst(BigDecimal.valueOf(200));
        item3.setRoomsSoldOnbooks(20);
        item3.setRoomRevenueOnbooks(BigDecimal.valueOf(300));
        item3.setChannelCostOnbooks(BigDecimal.valueOf(400));

        Map<String, Map<String, List<ChannelSrcFcstDateEntry>>> result = service.transformChannelSrcFcstToFusionChartDto(Arrays.asList(item1, item2, item3), DateUtil.DATE_FORMAT_DD_MMM_YYYY, true);
        assertEquals(2, result.size());
        assertEquals(1, result.get(item1Name).size());
        assertEquals(1, result.get(item2Name).size());
        List<ChannelSrcFcstDateEntry> item1Results = result.get(item1Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_DD_MMM_YYYY));
        assertEquals(1, item1Results.size());
        List<ChannelSrcFcstDateEntry> item2Results = result.get(item2Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_DD_MMM_YYYY));
        assertEquals(1, item2Results.size());

        ChannelSrcFcstDateEntry firstItem = item1Results.get(0);
        assertTrue(firstItem.isWeekend());
        assertEquals(BigDecimal.valueOf(20), firstItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(200), firstItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(400), firstItem.getChannelCostFcst());
        assertEquals(40, firstItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(600), firstItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(800), firstItem.getChannelCostOnbooks());

        ChannelSrcFcstDateEntry secondItem = item2Results.get(0);
        assertFalse(secondItem.isWeekend());
        assertEquals(BigDecimal.valueOf(30), secondItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(500), secondItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(600), secondItem.getChannelCostFcst());
        assertEquals(40, secondItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(700), secondItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(800), secondItem.getChannelCostOnbooks());
    }

    @Test
    public void shouldTransformChannelSrcFcstToFusionChartDtoForDailyForecastWhenThereIsOnlyOneSourceForChannel() {
        Date monthStartDate = new Date();
        String item1Name = "item1";
        String item2Name = "item2";

        ChannelSrcFcstDto item1 = new ChannelSrcFcstDto();
        item1.setChannelName(item1Name);
        item1.setMonthStartDate(monthStartDate);
        item1.setWeekend(true);
        item1.setRoomsSoldFcst(BigDecimal.valueOf(10));
        item1.setRoomRevenueFcst(BigDecimal.valueOf(100));
        item1.setChannelCostFcst(BigDecimal.valueOf(200));
        item1.setRoomsSoldOnbooks(20);
        item1.setRoomRevenueOnbooks(BigDecimal.valueOf(300));
        item1.setChannelCostOnbooks(BigDecimal.valueOf(400));

        ChannelSrcFcstDto item2 = new ChannelSrcFcstDto();
        item2.setChannelName(item2Name);
        item2.setMonthStartDate(monthStartDate);
        item2.setWeekend(false);
        item2.setRoomsSoldFcst(BigDecimal.valueOf(30));
        item2.setRoomRevenueFcst(BigDecimal.valueOf(500));
        item2.setChannelCostFcst(BigDecimal.valueOf(600));
        item2.setRoomsSoldOnbooks(40);
        item2.setRoomRevenueOnbooks(BigDecimal.valueOf(700));
        item2.setChannelCostOnbooks(BigDecimal.valueOf(800));

        Map<String, Map<String, List<ChannelSrcFcstDateEntry>>> result = service.transformChannelSrcFcstToFusionChartDto(Arrays.asList(item1, item2), DateUtil.DATE_FORMAT_DD_MMM_YYYY, true);
        assertEquals(2, result.size());
        assertEquals(1, result.get(item1Name).size());
        assertEquals(1, result.get(item2Name).size());
        List<ChannelSrcFcstDateEntry> item1Results = result.get(item1Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_DD_MMM_YYYY));
        assertEquals(1, item1Results.size());
        List<ChannelSrcFcstDateEntry> item2Results = result.get(item2Name).get(DateUtil.formatDate(monthStartDate, DateUtil.DATE_FORMAT_DD_MMM_YYYY));
        assertEquals(1, item2Results.size());

        ChannelSrcFcstDateEntry firstItem = item1Results.get(0);
        assertTrue(firstItem.isWeekend());
        assertEquals(BigDecimal.valueOf(10), firstItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(100), firstItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(200), firstItem.getChannelCostFcst());
        assertEquals(20, firstItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(300), firstItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(400), firstItem.getChannelCostOnbooks());

        ChannelSrcFcstDateEntry secondItem = item2Results.get(0);
        assertFalse(secondItem.isWeekend());
        assertEquals(BigDecimal.valueOf(30), secondItem.getRoomsSoldFcst());
        assertEquals(BigDecimal.valueOf(500), secondItem.getRoomRevenueFcst());
        assertEquals(BigDecimal.valueOf(600), secondItem.getChannelCostFcst());
        assertEquals(40, secondItem.getRoomsSoldOnbooks());
        assertEquals(BigDecimal.valueOf(700), secondItem.getRoomRevenueOnbooks());
        assertEquals(BigDecimal.valueOf(800), secondItem.getChannelCostOnbooks());
    }


    @Test
    public void testGetChannelSrcFCSTWithFilter() {
        Date startDate = new Date();
        Date endDate = DateUtils.addMonths(new Date(), 1);
        boolean isChannel = true;
        boolean isWeekend = true;
        boolean isWeekday = true;

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("startDate", startDate);
        queryParam.put("endDate", endDate);
        queryParam.put("dowTypes", Arrays.asList(0, 1));

        service.getChannelSrcFCSTWithFilterOptions(startDate, endDate, isWeekday, isWeekend, isChannel);
        verify(service.tenantCrudService).findByNamedQuery(ChannelSrcFcst.CHANNEL_SRC_FCST_BY_CHANNEL, queryParam);
    }

    @Test
    public void testGetReservationNightLYWithFilterOptions() {
        Date startDate = new Date();
        Date endDate = DateUtils.addMonths(new Date(), 1);
        Date startDateLY = DateUtil.getFirstDayOfMonth(DateUtil.addYearsToDate(startDate, -1));
        Date endDateLY = DateUtil.getLastDayOfMonth(DateUtil.addYearsToDate(endDate, -1));
        boolean isWeekend = true;
        boolean isWeekday = true;
        List<Integer> weekdays = Arrays.asList(1, 2, 3, 4, 5, 6);
        List<Integer> weekends = Arrays.asList(0, 7);

        ReservationNight resNight = new ReservationNight();
        resNight.setOccupancyDate(startDate);

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("startDate", startDateLY);
        queryParam.put("endDate", endDateLY);

        when(service.getReservationNightLYWithFilterOptions(startDateLY, endDateLY, isWeekday, isWeekend, weekdays, weekends)).thenReturn(new ArrayList<>(Arrays.asList(resNight)));
        List<ReservationNight> results = service.getReservationNightLYWithFilterOptions(startDateLY, endDateLY, isWeekday, isWeekend, weekdays, weekends);
        verify(service.tenantCrudService).findByNamedQuery(ReservationNight.GET_BY_OCCUPANCY_DT_RANGE, queryParam);
        assertEquals(results.size(), 1);
    }

    @Test
    public void testGetTotalActivityLYWithFilterOptions() {
        Date startDate = new Date();
        Date endDate = DateUtils.addMonths(new Date(), 1);
        boolean isWeekend = true;
        boolean isWeekday = true;
        List<Integer> weekdays = Arrays.asList(1, 2, 3, 4, 5, 6);
        List<Integer> weekends = Arrays.asList(0, 7);

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("startDate", startDate);
        queryParam.put("endDate", endDate);
        queryParam.put("propertyId", 913);

        TotalActivity totalActivity = new TotalActivity();
        totalActivity.setOccupancyDate(DateUtil.getFirstDayOfMonth(DateUtil.addYearsToDate(startDate, -1)));

        when(service.getTotalActivityWithFilterOptions(startDate, endDate, isWeekday, isWeekend, weekdays, weekends)).thenReturn(new ArrayList<>(Arrays.asList(totalActivity)));
        List<TotalActivity> results = service.getTotalActivityWithFilterOptions(startDate, endDate, isWeekday, isWeekend, weekdays, weekends);
        verify(service.tenantCrudService).findByNamedQuery(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID, queryParam);
        assertEquals(results.size(), 1);
    }

    @Test
    public void testFilterReservationNightByChannelOrSource() {
        ReservationNight resNight1 = new ReservationNight();
        resNight1.setChannel("channel1");
        resNight1.setSourceBooking("unknown");
        ReservationNight resNight2 = new ReservationNight();
        resNight2.setChannel("unknown");
        resNight2.setSourceBooking("source1");

        List<ReservationNight> reservationNights = new ArrayList<>(Arrays.asList(resNight1, resNight2));

        List<ReservationNight> results = service.filterReservationNightByChannelOrSource(reservationNights, true, false);
        assertEquals(results.size(), 2);

        results = service.filterReservationNightByChannelOrSource(reservationNights, false, false);
        assertEquals(results.size(), 2);
    }

    @Test
    public void testFilterReservationNightByChannelOrSourceWithUnassignedChannelAndFromDailyKPI() {
        ReservationNight resNight1 = new ReservationNight();
        resNight1.setChannel("");
        resNight1.setSourceBooking("");
        ReservationNight resNight2 = new ReservationNight();
        resNight2.setChannel("unknown");
        resNight2.setSourceBooking("source1");

        List<ReservationNight> reservationNights = new ArrayList<>(Arrays.asList(resNight1, resNight2));

        List<ReservationNight> results = service.filterReservationNightByChannelOrSource(reservationNights, true, true);
        assertEquals(results.size(), 2);

        results = service.filterReservationNightByChannelOrSource(reservationNights, false, true);
        assertEquals(results.size(), 1);
    }

    @Test
    public void testFilterReservationNightByChannelOrSourceWithUnassignedChannelAndFromMonthlyKPI() {
        ReservationNight resNight1 = new ReservationNight();
        resNight1.setChannel("");
        resNight1.setSourceBooking("");
        ReservationNight resNight2 = new ReservationNight();
        resNight2.setChannel("unknown");
        resNight2.setSourceBooking("source1");

        List<ReservationNight> reservationNights = new ArrayList<>(Arrays.asList(resNight1, resNight2));

        List<ReservationNight> results = service.filterReservationNightByChannelOrSource(reservationNights, true, false);
        assertEquals(results.size(), 1);

        results = service.filterReservationNightByChannelOrSource(reservationNights, false, false);
        assertEquals(results.size(), 1);
    }

    @Test
    public void testFormatKPIPercentChange() {
        BigDecimal twoDecimalPlaceOne = new BigDecimal("256.15");
        BigDecimal twoDecimalPlaceTwo = new BigDecimal("251.34");

        assertEquals(new BigDecimal("1.91"), service.formatKPIPercentChange(twoDecimalPlaceOne, twoDecimalPlaceTwo));

        BigDecimal twoDecimalPlaceThree = new BigDecimal("256.16");
        BigDecimal twoDecimalPlaceFour = new BigDecimal("251.34");

        assertEquals(new BigDecimal("1.92"), service.formatKPIPercentChange(twoDecimalPlaceThree, twoDecimalPlaceFour));

        //Base cases with various scales for ZERO being input
        assertEquals(BigDecimal.ZERO, service.formatKPIPercentChange(twoDecimalPlaceFour, BigDecimal.ZERO));
        assertEquals(BigDecimal.ZERO, service.formatKPIPercentChange(twoDecimalPlaceFour, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)));
        assertEquals(BigDecimal.ZERO, service.formatKPIPercentChange(twoDecimalPlaceFour, BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP)));
        assertEquals(BigDecimal.ZERO, service.formatKPIPercentChange(twoDecimalPlaceFour, BigDecimal.ZERO.setScale(6, RoundingMode.HALF_UP)));

        assertEquals(BigDecimal.valueOf(-100).setScale(2, RoundingMode.HALF_UP), service.formatKPIPercentChange(BigDecimal.ZERO, twoDecimalPlaceThree));
        assertEquals(BigDecimal.valueOf(-100).setScale(2, RoundingMode.HALF_UP), service.formatKPIPercentChange(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP), twoDecimalPlaceThree));
        assertEquals(BigDecimal.valueOf(-100).setScale(2, RoundingMode.HALF_UP), service.formatKPIPercentChange(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), twoDecimalPlaceThree));
        assertEquals(BigDecimal.valueOf(-100).setScale(2, RoundingMode.HALF_UP), service.formatKPIPercentChange(BigDecimal.ZERO.setScale(6, RoundingMode.HALF_UP), twoDecimalPlaceThree));
    }

    @Test
    public void testNetRevFcstAggregate() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setRoomRevenueFcst(new BigDecimal("10.115"));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setRoomRevenueFcst(new BigDecimal("10.516"));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getNetRevForecastAggregate(channelSrcFcsts, "CENTER");
        assertEquals(new BigDecimal("20.63"), results.get("CENTER"));
    }

    @Test
    public void testNetRevFcstByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setRoomRevenueFcst(new BigDecimal("10.115"));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setRoomRevenueFcst(new BigDecimal("10.516"));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getNetRevForecastByMonth(channelSrcFcsts);
        assertEquals(new BigDecimal("10.12"), results.get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("10.52"), results.get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Test
    public void testAvgChannelCostRevFcstAggregate() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setChannelCostFcst(new BigDecimal("10.115"));
        fcst1.setRoomsSoldFcst(new BigDecimal(5));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setChannelCostFcst(new BigDecimal("10.516"));
        fcst2.setRoomsSoldFcst(new BigDecimal(5));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getAvgChannelCostRevenueForecastAggregate(channelSrcFcsts, "CENTER");
        assertEquals(new BigDecimal("2.06"), results.get("CENTER"));

        // test divide by 0 flow
        fcst1.setRoomsSoldFcst(BigDecimal.ZERO);
        fcst2.setRoomsSoldFcst(BigDecimal.ZERO);
        results = service.getAvgChannelCostRevenueForecastAggregate(channelSrcFcsts, "CENTER");
        assertEquals(BigDecimal.ZERO, results.get("CENTER"));
    }

    @Test
    public void testAvgChannelRevFcstByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setChannelCostFcst(new BigDecimal("10.115"));
        fcst1.setRoomsSoldFcst(new BigDecimal(5));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setChannelCostFcst(new BigDecimal("10.516"));
        fcst2.setRoomsSoldFcst(new BigDecimal(5));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getAvgChannelCostRevenueForecastByMonth(channelSrcFcsts);
        assertEquals(new BigDecimal("2.02"), results.get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("2.10"), results.get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test divide by 0 flow
        fcst2.setRoomsSoldFcst(BigDecimal.ZERO);
        results = service.getAvgChannelCostRevenueForecastByMonth(channelSrcFcsts);
        assertEquals(new BigDecimal("2.02"), results.get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(BigDecimal.ZERO, results.get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Test
    public void testAvgChannelCostLY() {
        List<ReservationNight> reservationNights = new ArrayList<>();
        ReservationNight res1 = new ReservationNight();
        res1.setTotalAcquisitionCost(new BigDecimal("10.115"));

        ReservationNight res2 = new ReservationNight();
        res2.setTotalAcquisitionCost(new BigDecimal("10.516"));

        // test empty list flow
        BigDecimal result = service.getAverageChannelCostLY(reservationNights);
        assertEquals(BigDecimal.ZERO, result);

        // test non empty list flow
        reservationNights.add(res1);
        reservationNights.add(res2);

        result = service.getAverageChannelCostLY(reservationNights);
        assertEquals(new BigDecimal("10.32"), result);
    }

    @Test
    public void testChannelCostFcstAggregate() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setRoomsSoldFcst(new BigDecimal(5));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setRoomsSoldFcst(new BigDecimal(7));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getChannelOccForecastAggregate(channelSrcFcsts, new BigDecimal(15), "CENTER");
        assertEquals(new BigDecimal("80.00"), results.get("CENTER"));

        // test divide by 0 flow
        results = service.getChannelOccForecastAggregate(channelSrcFcsts, BigDecimal.ZERO, "CENTER");
        assertEquals(BigDecimal.ZERO, results.get("CENTER"));
    }

    @Test
    public void testChannelOccupancyFcstByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setChannelCostFcst(new BigDecimal("10.115"));
        fcst1.setRoomsSoldFcst(new BigDecimal(5));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setChannelCostFcst(new BigDecimal("10.516"));
        fcst2.setRoomsSoldFcst(new BigDecimal(5));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        List<TotalActivity> totalActivities = new ArrayList<>();
        TotalActivity ta1 = new TotalActivity();
        ta1.setOccupancyDate(date1);
        ta1.setTotalAccomCapacity(new BigDecimal(10));

        TotalActivity ta2 = new TotalActivity();
        ta2.setOccupancyDate(date2);
        ta2.setTotalAccomCapacity(new BigDecimal(7));

        totalActivities.add(ta1);
        totalActivities.add(ta2);

        Map<String, BigDecimal> results = service.getChannelOccupancyForecastByMonth(channelSrcFcsts, totalActivities);
        assertEquals(new BigDecimal("50.00"), results.get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("71.43"), results.get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test divide by 0 flow
        ta2.setTotalAccomCapacity(BigDecimal.ZERO);
        results = service.getChannelOccupancyForecastByMonth(channelSrcFcsts, totalActivities);
        assertEquals(new BigDecimal("50.00"), results.get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(BigDecimal.ZERO, results.get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Test
    public void testChannelOCcupancyLY() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ReservationNight> reservationNights = new ArrayList<>();
        ReservationNight res1 = new ReservationNight();
        res1.setOccupancyDate(date1);

        ReservationNight res2 = new ReservationNight();
        res2.setOccupancyDate(date2);

        reservationNights.add(res1);
        reservationNights.add(res2);

        List<TotalActivity> totalActivities = new ArrayList<>();
        TotalActivity ta1 = new TotalActivity();
        ta1.setTotalAccomCapacity(new BigDecimal(5));
        ta1.setOccupancyDate(date1);

        TotalActivity ta2 = new TotalActivity();
        ta2.setTotalAccomCapacity(new BigDecimal(7));
        ta2.setOccupancyDate(date2);

        totalActivities.add(ta1);
        totalActivities.add(ta2);

        BigDecimal result = service.getChannelOccupancyLY(reservationNights, totalActivities);
        assertEquals(new BigDecimal("16.67"), result);

        // test divide by 0 flow
        ta1.setTotalAccomCapacity(BigDecimal.ZERO);
        ta2.setTotalAccomCapacity(BigDecimal.ZERO);

        result = service.getChannelOccupancyLY(reservationNights, totalActivities);
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    public void testNetADRFcst() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setRoomRevenueFcst(new BigDecimal("10.115"));
        fcst1.setRoomsSoldFcst(new BigDecimal(5));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setRoomRevenueFcst(new BigDecimal("10.516"));
        fcst2.setRoomsSoldFcst(new BigDecimal(7));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getNetADRForecast(channelSrcFcsts, "CENTER");
        assertEquals(new BigDecimal("1.72"), results.get("CENTER"));

        // test divide by 0 flow
        fcst1.setRoomsSoldFcst(BigDecimal.ZERO);
        fcst2.setRoomsSoldFcst(BigDecimal.ZERO);

        results = service.getNetADRForecast(channelSrcFcsts, "CENTER");
        assertEquals(BigDecimal.ZERO, results.get("CENTER"));
    }

    @Test
    public void testADRFcst() {
        BigDecimal result = service.getADRForecast(new BigDecimal("10.548"), new BigDecimal("2.879"));
        assertEquals(new BigDecimal("3.66"), result);

        // test divide by 0 flow
        result = service.getADRForecast(new BigDecimal("10.548"), BigDecimal.ZERO);
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    public void testNetRevPARFcst() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setRoomRevenueFcst(new BigDecimal("10.115"));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setRoomRevenueFcst(new BigDecimal("10.516"));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        BigDecimal totalCapacity = new BigDecimal(5);

        Map<String, BigDecimal> results = service.getNetRevParForecast(channelSrcFcsts, "CENTER", totalCapacity);
        assertEquals(new BigDecimal("4.13"), results.get("CENTER"));

        // test divide by 0 flow
        results = service.getNetRevParForecast(channelSrcFcsts, "CENTER", BigDecimal.ZERO);
        assertEquals(BigDecimal.ZERO, results.get("CENTER"));
    }

    @Test
    public void testChannelCostFcst() {
        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setChannelCostFcst(new BigDecimal("10.115"));

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setChannelCostFcst(new BigDecimal("10.516"));

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        Map<String, BigDecimal> results = service.getChannelCostForecast(channelSrcFcsts, "CENTER");
        assertEquals(new BigDecimal("20.63"), results.get("CENTER"));
    }

    @Test
    public void testChannelCostLY() {
        List<ReservationNight> reservationNights = new ArrayList<>();
        ReservationNight res1 = new ReservationNight();
        res1.setTotalAcquisitionCost(new BigDecimal("10.115"));

        ReservationNight res2 = new ReservationNight();
        res2.setTotalAcquisitionCost(new BigDecimal("10.516"));

        reservationNights.add(res1);
        reservationNights.add(res2);

        BigDecimal result = service.getChannelCostLY(reservationNights);
        assertEquals(new BigDecimal("20.63"), result);
    }

    @Test
    public void testMetrisResponsesForChannelFcstKPIs_MonthlyData() throws ParseException {
        Date date1 = dateFormat.parse("05-Jun-2024");
        Date date2 = dateFormat.parse("07-Jun-2024");

        // setup response
        DashboardMetric2 metric = new DashboardMetric2();

        // setup revFcstResponse
        MetricResponse revFcstResponse = new MetricResponse();
        Map<String, List<?>> revFcstValuesMap = new HashMap<>();
        List<BigDecimal> revFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        revFcstValuesMap.put("PROPERTY", revFcstList);
        revFcstResponse.setMetricValuesByGroupByType(revFcstValuesMap);

        // setup occupancyResponse
        MetricResponse occupancyFcstResponse = new MetricResponse();
        Map<String, List<?>> occupancyFcstValuesMap = new HashMap<>();
        List<BigDecimal> occupancyFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        occupancyFcstValuesMap.put("PROPERTY", occupancyFcstList);
        occupancyFcstResponse.setMetricValuesByGroupByType(occupancyFcstValuesMap);

        // setup capacityResponse
        MetricResponse capacityResponse = new MetricResponse();
        Map<String, List<?>> capacityValuesMap = new HashMap<>();
        List<BigDecimal> capacityList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        capacityValuesMap.put("PROPERTY", capacityList);
        capacityResponse.setMetricValuesByGroupByType(capacityValuesMap);

        // setup revParFcstResponse
        MetricResponse revParFcstResponse = new MetricResponse();
        Map<String, List<?>> revParFcstValuesMap = new HashMap<>();
        List<BigDecimal> revParFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        revParFcstValuesMap.put("PROPERTY", revParFcstList);
        revParFcstResponse.setMetricValuesByGroupByType(revParFcstValuesMap);

        // attach response metrics to response
        metric.setMetricResponses(Arrays.asList(revFcstResponse, occupancyFcstResponse, capacityResponse, revParFcstResponse));

        // setup occupancy dates for response
        Date date3 = dateFormat.parse("06-Jun-2024");
        Set<Date> occupancyDates = new HashSet<>();
        occupancyDates.add(date1);
        occupancyDates.add(date2);
        occupancyDates.add(date3);
        metric.setOccupancyDates(occupancyDates);

        when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(metric);
        Map<String, BigDecimal> result = service.getMetricResponsesForChannelForecastKPIs(date1, date2, true, true, Arrays.asList(1, 2, 3, 4, 5), Arrays.asList(6, 7), false);

        verify(dashboardService, times(1)).getMetrics2(anyList(), eq(dateFormat.parse("01-Jun-2024")), eq(dateFormat.parse("30-Jun-2024")));
        assertEquals(new BigDecimal("20.631"), result.get("revenueFcst"));
        assertEquals(new BigDecimal("20.63"), result.get("occupancyFcst"));
        assertEquals(new BigDecimal("20.63"), result.get("totalCapacity"));
        assertEquals(new BigDecimal("6.88"), result.get("revParFcst"));
    }

    @Test
    public void testMetrisResponsesForChannelFcstKPIs_DailyData() throws ParseException {
        Date date1 = dateFormat.parse("05-Jun-2024");
        Date date2 = dateFormat.parse("07-Jun-2024");

        // setup response
        DashboardMetric2 metric = new DashboardMetric2();

        // setup revFcstResponse
        MetricResponse revFcstResponse = new MetricResponse();
        Map<String, List<?>> revFcstValuesMap = new HashMap<>();
        List<BigDecimal> revFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        revFcstValuesMap.put("PROPERTY", revFcstList);
        revFcstResponse.setMetricValuesByGroupByType(revFcstValuesMap);

        // setup occupancyResponse
        MetricResponse occupancyFcstResponse = new MetricResponse();
        Map<String, List<?>> occupancyFcstValuesMap = new HashMap<>();
        List<BigDecimal> occupancyFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        occupancyFcstValuesMap.put("PROPERTY", occupancyFcstList);
        occupancyFcstResponse.setMetricValuesByGroupByType(occupancyFcstValuesMap);

        // setup capacityResponse
        MetricResponse capacityResponse = new MetricResponse();
        Map<String, List<?>> capacityValuesMap = new HashMap<>();
        List<BigDecimal> capacityList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        capacityValuesMap.put("PROPERTY", capacityList);
        capacityResponse.setMetricValuesByGroupByType(capacityValuesMap);

        // setup revParFcstResponse
        MetricResponse revParFcstResponse = new MetricResponse();
        Map<String, List<?>> revParFcstValuesMap = new HashMap<>();
        List<BigDecimal> revParFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
        revParFcstValuesMap.put("PROPERTY", revParFcstList);
        revParFcstResponse.setMetricValuesByGroupByType(revParFcstValuesMap);

        // attach response metrics to response
        metric.setMetricResponses(Arrays.asList(revFcstResponse, occupancyFcstResponse, capacityResponse, revParFcstResponse));

        // setup occupancy dates for response
        Date date3 = dateFormat.parse("06-Jun-2024");
        Set<Date> occupancyDates = new HashSet<>();
        occupancyDates.add(date1);
        occupancyDates.add(date2);
        occupancyDates.add(date3);
        metric.setOccupancyDates(occupancyDates);

        when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(metric);

        Map<String, BigDecimal> result = service.getMetricResponsesForChannelForecastKPIs(date1, date2, true, true, Arrays.asList(1, 2, 3, 4, 5), Arrays.asList(6, 7), true);

        verify(dashboardService, times(1)).getMetrics2(anyList(), eq(date1), eq(date2));
        assertEquals(new BigDecimal("20.631"), result.get("revenueFcst"));
        assertEquals(new BigDecimal("20.63"), result.get("occupancyFcst"));
        assertEquals(new BigDecimal("20.63"), result.get("totalCapacity"));
        assertEquals(new BigDecimal("6.88"), result.get("revParFcst"));
    }

    @Test
    public void testNetChannelRevFcstByChannelByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setRoomRevenueFcst(new BigDecimal("10.115"));
        fcst1.setChannelId(1);
        fcst1.setSourceId(1);

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setRoomRevenueFcst(new BigDecimal("10.516"));
        fcst2.setChannelId(2);
        fcst2.setSourceId(2);

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        List<ChannelSourceView> channelSourceViews = new ArrayList<>();
        ChannelSourceView c1 = new ChannelSourceView();
        c1.setName("CS1");
        c1.setId(1);

        ChannelSourceView c2 = new ChannelSourceView();
        c2.setName("CS2");
        c2.setId(2);

        channelSourceViews.add(c1);
        channelSourceViews.add(c2);

        // test channel flow
        Map<String, Map<String, BigDecimal>> results = service.getNetRevenueForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, true);
        assertEquals(new BigDecimal("10.12"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("10.52"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test source flow
        results = service.getNetRevenueForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, false);
        assertEquals(new BigDecimal("10.12"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("10.52"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Test
    public void testChannelOccupancyFcstByChannelByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setRoomsSoldFcst(new BigDecimal("10.115"));
        fcst1.setChannelId(1);
        fcst1.setSourceId(1);

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setRoomsSoldFcst(new BigDecimal("10.516"));
        fcst2.setChannelId(2);
        fcst2.setSourceId(2);

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        List<ChannelSourceView> channelSourceViews = new ArrayList<>();
        ChannelSourceView c1 = new ChannelSourceView();
        c1.setName("CS1");
        c1.setId(1);

        ChannelSourceView c2 = new ChannelSourceView();
        c2.setName("CS2");
        c2.setId(2);

        channelSourceViews.add(c1);
        channelSourceViews.add(c2);

        List<TotalActivity> totalActivities = new ArrayList<>();
        TotalActivity ta1 = new TotalActivity();
        ta1.setTotalAccomCapacity(new BigDecimal(15));
        ta1.setOccupancyDate(date1);
        totalActivities.add(ta1);

        TotalActivity ta2 = new TotalActivity();
        ta2.setTotalAccomCapacity(new BigDecimal(15));
        ta2.setOccupancyDate(date2);
        totalActivities.add(ta2);

        // test channel flow
        Map<String, Map<String, BigDecimal>> results = service.getChannelOccupancyForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, true, totalActivities);
        assertEquals(new BigDecimal("67.43"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("70.11"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test source flow
        results = service.getChannelOccupancyForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, false, totalActivities);
        assertEquals(new BigDecimal("67.43"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("70.11"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test divide by 0 flow
        ta1.setTotalAccomCapacity(BigDecimal.ZERO);
        results = service.getChannelOccupancyForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, true, totalActivities);
        assertEquals(BigDecimal.ZERO, results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("70.11"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Test
    public void testAvgChannelCostRevFcstByChannelByMonth() {
        Date date1 = LocalDateUtils.toDate(LocalDate.now());
        Date date2 = LocalDateUtils.toDate(LocalDate.now().plusMonths(1));

        List<ChannelSrcFcst> channelSrcFcsts = new ArrayList<>();
        ChannelSrcFcst fcst1 = new ChannelSrcFcst();
        fcst1.setMonthStartDate(date1);
        fcst1.setChannelCostFcst(new BigDecimal("10.115"));
        fcst1.setRoomsSoldFcst(new BigDecimal(5));
        fcst1.setChannelId(1);
        fcst1.setSourceId(1);

        ChannelSrcFcst fcst2 = new ChannelSrcFcst();
        fcst2.setMonthStartDate(date2);
        fcst2.setChannelCostFcst(new BigDecimal("10.516"));
        fcst2.setRoomsSoldFcst(new BigDecimal(5));
        fcst2.setChannelId(2);
        fcst2.setSourceId(2);

        channelSrcFcsts.add(fcst1);
        channelSrcFcsts.add(fcst2);

        List<ChannelSourceView> channelSourceViews = new ArrayList<>();
        ChannelSourceView c1 = new ChannelSourceView();
        c1.setName("CS1");
        c1.setId(1);

        ChannelSourceView c2 = new ChannelSourceView();
        c2.setName("CS2");
        c2.setId(2);

        channelSourceViews.add(c1);
        channelSourceViews.add(c2);

        // test channel flow
        Map<String, Map<String, BigDecimal>> results = service.getAvgChannelCostRevenueForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, true);
        assertEquals(new BigDecimal("2.02"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("2.10"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test source flow
        results = service.getAvgChannelCostRevenueForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, false);
        assertEquals(new BigDecimal("2.02"), results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("2.10"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));

        // test divide by 0 flow
        fcst1.setRoomsSoldFcst(BigDecimal.ZERO);
        results = service.getAvgChannelCostRevenueForecastByChannelByMonth(channelSrcFcsts, channelSourceViews, true);
        assertEquals(BigDecimal.ZERO, results.get("CS1").get(DateUtil.formatDate(date1, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
        assertEquals(new BigDecimal("2.10"), results.get("CS2").get(DateUtil.formatDate(date2, DateUtil.DATE_FORMAT_MMM_YYYY_SPACED)));
    }

    @Nested
    class DailyKPITests {

        @Nested
        class TotalAccomCapacityForDay {
            @Test
            void shouldReturnZero_NoActivities_NoDateFilter() {
                BigDecimal totalAccomCapacity = service.getTotalAccomCapacityForDay(List.of(), null);

                assertEquals(BigDecimal.ZERO, totalAccomCapacity);
            }

            @Test
            void shouldReturnZero_NoActivities_WithDateFilter() throws ParseException {
                BigDecimal totalAccomCapacity = service.getTotalAccomCapacityForDay(List.of(), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.ZERO, totalAccomCapacity);
            }

            @Test
            void shouldReturnTotalAccomCapacityForDay_WithActivities_NoDateFilter() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.TEN);
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(5));

                BigDecimal totalAccomCapacity = service.getTotalAccomCapacityForDay(List.of(activity1, activity2), null);

                assertEquals(BigDecimal.valueOf(15), totalAccomCapacity);
            }

            @Test
            void shouldReturnTotalAccomCapacityForDay_WithActivities_WithDateFilter() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.TEN);
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(5));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(7));

                BigDecimal totalAccomCapacity = service.getTotalAccomCapacityForDay(List.of(activity1, activity2, activity3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.valueOf(10), totalAccomCapacity);
            }

            @Test
            void shouldReturnTotalAccomCapacityForDay_WithActivities_WithMissingCapacityInActivity() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.TEN);
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(5));
                TotalActivity activity3 = new TotalActivity();
                activity3.setOccupancyDate(dateFormat.parse("03-Jan-2024"));

                BigDecimal totalAccomCapacity = service.getTotalAccomCapacityForDay(List.of(activity1, activity2, activity3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.valueOf(10), totalAccomCapacity);
            }
        }

        @Nested
        class TotalRoomRevenueForecast {
            @Test
            void shouldReturnZero_NoDailyForecasts() {
                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of());

                assertEquals(BigDecimal.ZERO, totalRoomRevenueForecast);
            }

            @Test
            void shouldReturnTotalRoomRevenueForecast_DailyForecastsAreProvided() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.valueOf(19), totalRoomRevenueForecast);
            }

            @Test
            void shouldReturnTotalRoomRevenueForecast_DailyForecastsAreProvidedWithMissingData() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();

                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.TEN, totalRoomRevenueForecast);
            }

            @Test
            void shouldReturnZero_NoDailyForecasts_WithDateFilter() throws ParseException {
                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of(), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.ZERO, totalRoomRevenueForecast);
            }

            @Test
            void shouldReturnTotalRoomRevenueForecast_DailyForecastsAreProvided_WithDateFilter() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));

                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.valueOf(17), totalRoomRevenueForecast);
            }

            @Test
            void shouldReturnTotalRoomRevenueForecast_DailyForecastsAreProvidedWithMissingData_WithDateFilter() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));

                BigDecimal totalRoomRevenueForecast = service.getTotalRoomRevenueForecast(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.TEN, totalRoomRevenueForecast);
            }
        }

        @Nested
        class TotalRoomSoldFcst {
            @Test
            void shouldReturnZero_NoDailyForecasts() {
                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of());

                assertEquals(BigDecimal.ZERO, totalRoomSoldFcst);
            }

            @Test
            void shouldReturnTotalRoomSoldFcst_DailyForecastsProvided() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.valueOf(19), totalRoomSoldFcst);
            }

            @Test
            void shouldReturnTotalRoomSoldFcst_DailyForecastsProvidedWithMissingData() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();

                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.TEN, totalRoomSoldFcst);
            }

            @Test
            void shouldReturnZero_NoDailyForecasts_WithDateFilter() throws ParseException {
                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of(), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.ZERO, totalRoomSoldFcst);
            }

            @Test
            void shouldReturnTotalRoomSoldFcst_DailyForecastsProvided_WithDateFilter() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.valueOf(17), totalRoomSoldFcst);
            }

            @Test
            void shouldReturnTotalRoomSoldFcst_DailyForecastsProvidedWithMissingData_WithDateFilter() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));

                BigDecimal totalRoomSoldFcst = service.getTotalRoomSoldFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.TEN, totalRoomSoldFcst);
            }
        }

        @Nested
        class TotalChannelCostFcst {
            @Test
            void shouldReturnZero_NoDailyForecasts() {
                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of());

                assertEquals(BigDecimal.ZERO, totalChannelCostFcst);
            }

            @Test
            void shouldReturnTotalChannelCostFcst_DailyForecastsProvided() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));

                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.valueOf(19), totalChannelCostFcst);
            }

            @Test
            void shouldReturnTotalChannelCostFcst_DailyForecastsProvidedWithMissingData() {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();

                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2));

                assertEquals(BigDecimal.TEN, totalChannelCostFcst);
            }

            @Test
            void shouldReturnZero_NoDailyForecasts_WithFilterDate() throws ParseException {
                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of(), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.ZERO, totalChannelCostFcst);
            }

            @Test
            void shouldReturnTotalChannelCostFcst_DailyForecastsProvided_WithFilterDate() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));

                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.valueOf(17), totalChannelCostFcst);
            }

            @Test
            void shouldReturnTotalChannelCostFcst_DailyForecastsProvidedWithMissingData_WithFilterDate() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));

                BigDecimal totalChannelCostFcst = service.getTotalChannelCostFcst(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), dateFormat.parse("01-Jan-2024"));

                assertEquals(BigDecimal.TEN, totalChannelCostFcst);
            }
        }

        @Nested
        class DistinctDays {
            @Test
            void shouldReturnEmptyListWhenNoDailyForecastsAreProvided() {
                List<Date> distinctDates = service.getDistinctDays(List.of());
                assertEquals(0, distinctDates.size());
            }

            @Test
            void shouldReturnDistinctDates_DailyForecastsProvided() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));

                List<Date> distinctDates = service.getDistinctDays(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3));

                assertEquals(2, distinctDates.size());
                assertEquals(List.of(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024")), distinctDates);
            }
        }

        @Nested
        class ChannelSrcDailyFCSTWithFilterOptions {
            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_CHANNEL_Weekday_Weekend() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(0, 1)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), true, true, true);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }

            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_CHANNEL_OnlyWeekday() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(0)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), true, false, true);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }

            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_CHANNEL_OnlyWeekend() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(1)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), false, true, true);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }

            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_SOURCE_Weekday_Weekend() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(0, 1)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), true, true, false);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }

            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_SOURCE_OnlyWeekday() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(0)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), true, false, false);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }

            @Test
            void shouldReturnChannelSrcDailyFCSTListWhenFilterAreProvided_SOURCE_OnlyWeekend() throws ParseException {
                ArgumentCaptor<Map> queryParameterCaptor = ArgumentCaptor.forClass(Map.class);
                Map<String, Object> expectedQueryParameters = Map.of(
                        "startDate", dateFormat.parse("01-Jan-2024"),
                        "endDate", dateFormat.parse("02-Jan-2024"),
                        "dowTypes", List.of(1)
                );
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                when(tenantCrudService.findByNamedQuery(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE, expectedQueryParameters)).thenReturn(List.of(channelSrcDailyFcst1));

                List<ChannelSrcDailyFcst> result = service.getChannelSrcDailyFCSTWithFilterOptions(dateFormat.parse("01-Jan-2024"), dateFormat.parse("02-Jan-2024"), false, true, false);

                verify(tenantCrudService).findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE),
                        queryParameterCaptor.capture());
                assertEquals(expectedQueryParameters, queryParameterCaptor.getValue());
                assertEquals(List.of(channelSrcDailyFcst1), result);
            }
        }

        @Nested
        class NetADRDailyForecastKPI {
            @Test
            void shouldReturnZeroWhenNoDailyForecastsAreProvided() throws ParseException {
                BigDecimal result = service.getNetADRForecastForDailyKPI(List.of());

                assertEquals(BigDecimal.ZERO, result);
            }

            @Test
            void shouldReturnNetADRForecastWhenDailyForecastsAreProvided() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.valueOf(70));

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(60));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(40));

                BigDecimal result = service.getNetADRForecastForDailyKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3));

                assertEquals(BigDecimal.valueOf(6.54), result);
            }

            @Test
            void shouldReturnNetADRDailyForecastKPI_NoDailyForecastProvided() {
                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20), "occupancyFcst", BigDecimal.valueOf(15));
                ChannelSrcFcstKPIDTO expectedResponse = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.ZERO,
                        "leftValue", BigDecimal.valueOf(1.33).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-100).setScale(2, RoundingMode.HALF_UP)
                ));

                ChannelSrcFcstKPIDTO result = service.getNetADRDailyForecastKPI(List.of(), metricResponseMap);

                assertEquals(expectedResponse.getKpiCardMap(), result.getKpiCardMap());
            }

            @Test
            void shouldReturnNetADRDailyForecastKPI_DailyForecastProvided() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.valueOf(70));

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(60));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(40));

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20), "occupancyFcst", BigDecimal.valueOf(5));
                ChannelSrcFcstKPIDTO expectedResponse = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(6.54),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(63.5).setScale(2, RoundingMode.HALF_UP)
                ));

                ChannelSrcFcstKPIDTO result = service.getNetADRDailyForecastKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), metricResponseMap);

                assertEquals(expectedResponse.getKpiCardMap(), result.getKpiCardMap());
            }
        }

        @Nested
        class NetRevParForecastForDailyKPI {
            @Test
            void shouldReturnZeroWhenZeroCapacity() {
                BigDecimal result = service.getNetRevParForecastForDailyKPI(List.of(), BigDecimal.ZERO);

                assertEquals(BigDecimal.ZERO, result);
            }

            @Test
            void shouldReturnZeroWhenNoDailyForecastsAreProvided_WithCapacity() {
                BigDecimal result = service.getNetRevParForecastForDailyKPI(List.of(), BigDecimal.TEN);

                assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP), result);
            }

            @Test
            void shouldReturnNetRevParForecastWhenDailyForecastsAreProvided_WithCapacity() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));

                BigDecimal result = service.getNetRevParForecastForDailyKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), BigDecimal.TEN);

                assertEquals(BigDecimal.valueOf(2.6).setScale(2, RoundingMode.HALF_UP), result);
            }

            @Test
            void shouldReturnNetRevParDailyForecastKPI_DailyForecastsAreProvided_WithCapacity() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));

                Map<String, BigDecimal> metricResponseMap = Map.of("revParFcst", BigDecimal.valueOf(20), "totalCapacity", BigDecimal.valueOf(5));
                ChannelSrcFcstKPIDTO expectedResponse = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(5.20).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(-74).setScale(2, RoundingMode.HALF_UP)
                ));

                ChannelSrcFcstKPIDTO result = service.getNetRevParDailyForecastKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), metricResponseMap);

                assertEquals(expectedResponse.getKpiCardMap(), result.getKpiCardMap());
            }
        }

        @Nested
        class ChannelCostForecastForDailyKPI {
            @Test
            void shouldReturnNetRevParDailyForecastKPI_DailyForecastsAreProvided_WithCapacity() throws ParseException {
                ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);

                ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));

                ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));

                ChannelSrcFcstKPIDTO expectedResponse = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(26).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(148).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-82.43).setScale(2, RoundingMode.HALF_UP)
                ));

                ChannelSrcFcstKPIDTO result = service.getChannelCostDailyForecastKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), getReservationNightList());

                assertEquals(expectedResponse.getKpiCardMap(), result.getKpiCardMap());
            }
        }

        @Nested
        class NetRevenueFcst {
            @Nested
            class NetRevForecastByDay {
                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnZeroWhenNoDailyForecastsAreProvided(boolean isDaly) {
                    Map<String, BigDecimal> result = service.getNetRevForecastByDay(List.of(), isDaly);
                    assertEquals(Map.of(), result);
                }


                @Test
                void shouldReturnNetRevForecastByDay_DailyForecastsAreProvided() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));

                    Map<String, BigDecimal> result = service.getNetRevForecastByDay(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), true);

                    assertEquals(
                            Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(17).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(9).setScale(2, RoundingMode.HALF_UP)
                            ),
                            result
                    );
                }

                @Test
                void shouldReturnNetRevForecastByMonth_WhenDailyForecastsAreProvided() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));

                    Map<String, BigDecimal> result = service.getNetRevForecastByDay(List.of(channelSrcDailyFcst1,
                            channelSrcDailyFcst2,
                            channelSrcDailyFcst3), false);

                    assertEquals(
                            Map.of(
                                    "Jan 2024", BigDecimal.valueOf(26).setScale(2, RoundingMode.HALF_UP)
                            ),
                            result
                    );
                }
            }

            @Nested
            class NetRevenueForecastByChannelByDay {
                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnNetRevenueForecastByChannelByDay_NoSourcesOrChannels(boolean isDaily) {
                    Map<String, Map<String, BigDecimal>> result = service.getNetRevenueForecastByChannelByDay(List.of(), List.of(), true, isDaily);

                    assertEquals(Map.of(), result);
                }

                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnNetRevenueForecastByChannelByDay_NoDailyForecasts(boolean isDaily) {
                    ChannelSourceSelectionView emailChannel = getChannelSourceSelection(1, 1, "EMAIL", 1, "SRC1", "SOURCE", true);

                    Map<String, Map<String, BigDecimal>> result = service.getNetRevenueForecastByChannelByDay(List.of(), List.of(emailChannel), true, isDaily);

                    assertEquals(Map.of("EMAIL", Map.of()), result);
                }

                @Test
                void shouldReturnNetRevenueForecastByChannelByDay_DailyForecastsProvided_ChannelsProvided() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(9).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(7).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getNetRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            true
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnNetRevenueForecastByChannelByMonth_DailyForecastsProvided_ChannelsProvided() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(19).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(7).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getNetRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            false
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnNetRevenueForecastByChannelByDay_DailyForecastsProvided_SourcesProvided() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(9).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW - MYF", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(7).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getNetRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            false,
                            true
                    );

                    assertEquals(expectedResult, result);
                }
            }

            @Test
            void shouldReturnNetRevenueForecast_Weekend_Weekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(210).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnNetRevenueForecastByMonth_Weekend_Weekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(160).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(28.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(7.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(35.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnNetRevenueForecast_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(210).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnNetRevenueForecastByMonth_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(160).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(7.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(35.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnNetRevenueForecast_OnlyWeekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(
                        Map.of(
                        "centerValue", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(160).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(16.00).setScale(2, RoundingMode.HALF_UP),
                                "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP),
                                "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnNetRevenueForecastByMonth_OnlyWeekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                Map<String, BigDecimal> metricResponseMap = Map.of("revenueFcst", BigDecimal.valueOf(20));

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(20),
                        "rightValue", BigDecimal.valueOf(160).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(52.00).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(28.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(35.00).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getNetRevenueDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

        }

        @Nested
        class ChannelOccupancyFcst {
            @Nested
            class ChannelOccForecastAggregateForDailyKPI {
                @Test
                void shouldReturnZeroWhenZeroCapacity() {
                    BigDecimal result = service.getChannelOccForecastAggregateForDailyKPI(List.of(), BigDecimal.ZERO);

                    assertEquals(BigDecimal.ZERO, result);
                }

                @Test
                void shouldReturnChannelOccForecastAggregateForDailyKPI_WithCapacity_EmptyForecasts() {
                    BigDecimal result = service.getChannelOccForecastAggregateForDailyKPI(List.of(), BigDecimal.TEN);

                    assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP), result);
                }

                @Test
                void shouldReturnChannelOccForecastAggregateForDailyKPI_WithCapacity_WithForecasts() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                    BigDecimal result = service.getChannelOccForecastAggregateForDailyKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), BigDecimal.TEN);

                    assertEquals(BigDecimal.valueOf(260).setScale(2, RoundingMode.HALF_UP), result);
                }
            }

            @Nested
            class ChannelOccupancyForecastByDay {
                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnEmptyChannelOccupancyForecastByDay_NoForecasts(boolean isDaily) {
                    Map<String, BigDecimal> result = service.getChannelOccupancyForecastByDay(List.of(), List.of(), isDaily);

                    assertEquals(Map.of(), result);
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByDay_NoActivities() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                    Map<String, BigDecimal> result = service.getChannelOccupancyForecastByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            List.of(),
                            true
                    );

                    assertEquals(
                            Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO,
                                    "02-Jan-2024", BigDecimal.ZERO
                            ),
                            result
                    );
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByMonth_NoActivities() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                    Map<String, BigDecimal> result = service.getChannelOccupancyForecastByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            List.of(),
                            false
                    );

                    assertEquals(
                            Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            ),
                            result
                    );
                }

                @Test
                void shouldReturnChannelOccupancyForecastByDay_WithActivities() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, BigDecimal> result = service.getChannelOccupancyForecastByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            List.of(activity1, activity2, activity3),
                            true
                    );

                    assertEquals(
                            Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(18).setScale(2, RoundingMode.HALF_UP)
                            ),
                            result
                    );
                }

                @Test
                void shouldReturnChannelOccupancyForecastByMonth_WithActivities() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, BigDecimal> result = service.getChannelOccupancyForecastByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            List.of(activity1, activity2, activity3),
                            false
                    );

                    assertEquals(
                            Map.of(
                                    "Jan 2024", BigDecimal.valueOf(17.33).setScale(2, RoundingMode.HALF_UP)),
                            result
                    );
                }
            }

            @Nested
            class ChannelOccupancyForecastByChannelByDay {

                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnEmptyChannelOccupancyForecastByChannelByDay_NoChannelsOrSources(boolean isDaily) {
                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(List.of(), List.of(), true, List.of(), isDaily);

                    assertEquals(Map.of(), result);
                }

                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnEmptyDateForEachChannelOccupancyForecastByChannelByDay_NoForecasts(boolean isDaily) {
                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(List.of(), getChannelSourceSelections(), true, List.of(), isDaily);

                    assertEquals(Map.of("EMAIL", Map.of(), "SW", Map.of()), result);
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByChannelByDay_NoActivities_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO,
                                    "02-Jan-2024", BigDecimal.ZERO
                            ),
                            "SW", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(), true, List.of(), true);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByChannelByMonth_NoActivities_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "Jan 2024", BigDecimal.ZERO

                            ),
                            "SW", Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(), true, List.of(), false);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnChannelOccupancyForecastByChannelByDay_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(18).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(7).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            List.of(activity1, activity2, activity1), true);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnChannelOccupancyForecastByChannelByMonth_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(19).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(14).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            List.of(activity1, activity2, activity3), false);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByChannelByDay_NoActivities_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomRevenueFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomRevenueFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomRevenueFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO,
                                    "02-Jan-2024", BigDecimal.ZERO
                            ),
                            "SW - MYF", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(), false, List.of(), true);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnZeroChannelOccupancyForecastByChannelByMonth_NoActivities_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            ),
                            "SW - MYF", Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(), false, List.of(), false);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnChannelOccupancyForecastByChannelByDay_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(18).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW - MYF", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(7).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            false,
                            List.of(activity1, activity2, activity1), true);

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnChannelOccupancyForecastByChannelByMonth_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                    TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(19).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW - MYF", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(14).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getChannelOccupancyForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            false,
                            List.of(activity1, activity2, activity3), false);

                    assertEquals(expectedResult, result);
                }
            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPI_Weekday_Weekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "01-Jan-2024", BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP),
                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of(
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                ),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPIByMonth_Weekday_Weekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "Jan 2024", BigDecimal.valueOf(52).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of(
                                        "Jan 2024", BigDecimal.valueOf(36).setScale(2, RoundingMode.HALF_UP)
                                ),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(30.67).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPI_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "01-Jan-2024", BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP),
                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                        ),
                       Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());

            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPIByMonth_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "Jan 2024", BigDecimal.valueOf(52).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(30.67).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());

            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPI_OnlyWeekdays() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "01-Jan-2024", BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP),
                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of(
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                ),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());

            }

            @Test
            void shouldReturnChannelOccupancyDailyForecastKPIByMonth_OnlyWeekdays() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                Map<String, BigDecimal> metricResponseMap = Map.of("totalCapacity", BigDecimal.valueOf(50));
                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(156).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(3800).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "Jan 2024", BigDecimal.valueOf(52).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of(
                                        "Jan 2024", BigDecimal.valueOf(36).setScale(2, RoundingMode.HALF_UP)
                                ),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of(
                                        "Jan 2024", BigDecimal.valueOf(30.67).setScale(2, RoundingMode.HALF_UP)
                                ),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getChannelOccupancyDailyForecastKPI(
                        allForecasts,
                        metricResponseMap,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        List.of(activityLastYear1, activityLastYear2, activityLastYear3),
                        List.of(activity1, activity2, activity3),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());

            }
        }

        @Nested
        class AvgChannelCostFcst {

            @Nested
            class AvgChannelCostRevenueForecastAggregateForDailyKPI {
                @Test
                void shouldReturnZeroAvgChannelCostRevenueForecastAggregate_NoForecasts() {
                    BigDecimal result = service.getAvgChannelCostRevenueForecastAggregateForDailyKPI(List.of());

                    assertEquals(BigDecimal.ZERO, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastAggregateForDailyKPI_DailyForecastsPresent() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.valueOf(7));

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(8));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(9));

                    BigDecimal result = service.getAvgChannelCostRevenueForecastAggregateForDailyKPI(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3));

                    assertEquals(BigDecimal.valueOf(0.92), result);
                }
            }

            @Nested
            class AvgChannelCostRevenueForecastByDay {
                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnZeroAvgChannelCostRevenueForecast_NoDailyForecasts(boolean isDaily) {
                    Map<String, BigDecimal> result = service.getAvgChannelCostRevenueForecastByDay(List.of(), isDaily);

                    assertEquals(Map.of(), result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByDay_DailyForecastsPresent() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.valueOf(7));

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(8));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(9));

                    Map<String, BigDecimal> result = service.getAvgChannelCostRevenueForecastByDay(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), true);

                    assertEquals(Map.of("01-Jan-2024", BigDecimal.valueOf(0.94), "02-Jan-2024", BigDecimal.valueOf(0.89)), result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByMonth_DailyForecastsPresent() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.valueOf(7));

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(8));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(9));

                    Map<String, BigDecimal> result = service.getAvgChannelCostRevenueForecastByDay(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), false);

                    assertEquals(Map.of("Jan 2024", BigDecimal.valueOf(0.92)), result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByDay_ZeroRoomsSoldFcst() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.valueOf(7));

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(8));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(9));

                    Map<String, BigDecimal> result = service.getAvgChannelCostRevenueForecastByDay(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), true);

                    assertEquals(Map.of("01-Jan-2024", BigDecimal.ZERO, "02-Jan-2024", BigDecimal.ZERO), result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByMonth_ZeroRoomsSoldFcst() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.valueOf(7));

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(8));

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(9));

                    Map<String, BigDecimal> result = service.getAvgChannelCostRevenueForecastByDay(List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3), false);

                    assertEquals(Map.of("Jan 2024", BigDecimal.ZERO), result);
                }
            }

            @Nested
            class AvgChannelCostRevenueForecastByChannelByDay {
                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnEmptyAvgChannelCostRevenueForecastByChannelByDay_NoSourcesOrChannels(boolean isDaily) {
                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(List.of(), List.of(), true, isDaily);

                    assertEquals(Map.of(), result);
                }

                @ParameterizedTest
                @ValueSource(booleans = {true, false})
                void shouldReturnAvgChannelCostRevenueForecastByChannelByDay_NoDailyForecasts(boolean isDaily) {
                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(List.of(), List.of(getChannelSourceSelections().get(0)), true, isDaily);

                    assertEquals(Map.of("EMAIL", Map.of()), result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByDay_WithChannelSrcDailyFcstList_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(1.43).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(1.13).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(0.88).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            true
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByMonth_WithChannelSrcDailyFcstList_Channel() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(1.27).setScale(2, RoundingMode.HALF_UP)

                            ),
                            "SW", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(0.88).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            true,
                            false
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByDay_WithChannelSrcDailyFcstList_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(1.43).setScale(2, RoundingMode.HALF_UP),
                                    "02-Jan-2024", BigDecimal.valueOf(1.13).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW - MYF", Map.of(
                                    "01-Jan-2024", BigDecimal.valueOf(0.88).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            false,
                            true
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByMonthly_WithChannelSrcDailyFcstList_Source() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("02-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(9));
                    channelSrcDailyFcst2.setChannelId(1);
                    channelSrcDailyFcst2.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst3 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst3.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst3.setRoomsSoldFcst(BigDecimal.valueOf(8));
                    channelSrcDailyFcst3.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst3.setChannelId(2);
                    channelSrcDailyFcst3.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(1.27).setScale(2, RoundingMode.HALF_UP)
                            ),
                            "SW - MYF", Map.of(
                                    "Jan 2024", BigDecimal.valueOf(0.88).setScale(2, RoundingMode.HALF_UP)
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2, channelSrcDailyFcst3),
                            getChannelSourceSelections(),
                            false,
                            false
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByDay_WithChannelSrcDailyFcstList_ZeroRoomSoldFcst() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst2.setChannelId(2);
                    channelSrcDailyFcst2.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO
                            ),
                            "SW - MYF", Map.of(
                                    "01-Jan-2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2),
                            getChannelSourceSelections(),
                            false,
                            true
                    );

                    assertEquals(expectedResult, result);
                }

                @Test
                void shouldReturnAvgChannelCostRevenueForecastByChannelByMonthly_WithChannelSrcDailyFcstList_ZeroRoomSoldFcst() throws ParseException {
                    ChannelSrcDailyFcst channelSrcDailyFcst1 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst1.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst1.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst1.setChannelCostFcst(BigDecimal.TEN);
                    channelSrcDailyFcst1.setChannelId(1);
                    channelSrcDailyFcst1.setSourceId(1);

                    ChannelSrcDailyFcst channelSrcDailyFcst2 = new ChannelSrcDailyFcst();
                    channelSrcDailyFcst2.setOccupancyDate(dateFormat.parse("01-Jan-2024"));
                    channelSrcDailyFcst2.setRoomsSoldFcst(BigDecimal.ZERO);
                    channelSrcDailyFcst2.setChannelCostFcst(BigDecimal.valueOf(7));
                    channelSrcDailyFcst2.setChannelId(2);
                    channelSrcDailyFcst2.setSourceId(2);

                    Map<String, Map<String, BigDecimal>> expectedResult = Map.of(
                            "EMAIL - TS", Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            ),
                            "SW - MYF", Map.of(
                                    "Jan 2024", BigDecimal.ZERO
                            )
                    );

                    Map<String, Map<String, BigDecimal>> result = service.getAvgChannelCostRevenueForecastByChannelByDay(
                            List.of(channelSrcDailyFcst1, channelSrcDailyFcst2),
                            getChannelSourceSelections(),
                            false,
                            false
                    );

                    assertEquals(expectedResult, result);
                }
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPI_Weekend_Weekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP),
                                "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.90).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPIByMonth_Weekend_Weekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.78).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.90).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.8043).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.625).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        true,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPI_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.90).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPIByMonth_OnlyWeekend() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.90).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.8043).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.625).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        false,
                        true,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPI_OnlyWeekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("01-Jan-2024", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of()
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        true
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }

            @Test
            void shouldReturnAverageChannelCostDailyForecastKPIByMonth_OnlyWeekday() throws ParseException {
                List<ChannelSrcDailyFcst> channelSrcFcstListWeekday = List.of(
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                List<ChannelSrcDailyFcst> channelSrcFcstListWeekend = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true)
                );

                List<ChannelSrcDailyFcst> allForecasts = new ArrayList<>();
                allForecasts.addAll(channelSrcFcstListWeekday);
                allForecasts.addAll(channelSrcFcstListWeekend);

                ChannelSrcFcstKPIDTO expectedResult = new ChannelSrcFcstKPIDTO(Map.of(
                        "centerValue", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP),
                        "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                        "rightValue", BigDecimal.valueOf(-97.04).setScale(2, RoundingMode.HALF_UP)),
                        Map.of("Jan 2024", BigDecimal.valueOf(0.73).setScale(2, RoundingMode.HALF_UP)),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.78).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                        ),
                        Map.of(),
                        Map.of(
                                "EMAIL", Map.of("Jan 2024", BigDecimal.valueOf(0.8043).setScale(2, RoundingMode.HALF_UP)),
                                "SW", Map.of("Jan 2024", BigDecimal.valueOf(0.625).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                ChannelSrcFcstKPIDTO result = service.getAverageChannelCostDailyForecastKPI(
                        allForecasts,
                        true,
                        false,
                        channelSrcFcstListWeekday,
                        channelSrcFcstListWeekend,
                        getChannelSourceSelections(),
                        true,
                        getReservationNightList(),
                        false
                );

                assertEquals(expectedResult.getKpiCardMap(), result.getKpiCardMap());
                assertEquals(expectedResult.getKpiBarGraphMap(), result.getKpiBarGraphMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekdayMap(), result.getKpiLineGraphAndTableWeekdayMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableWeekendMap(), result.getKpiLineGraphAndTableWeekendMap());
                assertEquals(expectedResult.getKpiLineGraphAndTableMonthMap(), result.getKpiLineGraphAndTableMonthMap());
            }
        }

        @Nested
        class DailyKPICardData {
            @Test
            void verifyDailyKPICardData_Channel_Weekday_Weekend() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24.66667).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.92).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(20.631),
                                "rightValue", BigDecimal.valueOf(200.52).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(148).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-54.05).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(436.26).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(10806.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        ),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(3.01).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.32).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-70.83).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        true,
                        List.of(1, 2),
                        true,
                         true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_Channel_OnlyWeekdays() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(28).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-97.29).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.115),
                                "rightValue", BigDecimal.valueOf(512.95).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(28).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(142.86).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(889.33).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(2).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(44366.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.ZERO,
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        ),
                                        "SW", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(6.13).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5.06).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(21.15).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        false,
                        List.of(1, 2),
                        true,
                         true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_Channel_OnlyWeekends() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.83).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.516),
                                "rightValue", BigDecimal.valueOf(489.58).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(120.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-43.33).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(855.51).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(17010.20).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.ZERO,
                                        "02-Jan-2024", BigDecimal.ZERO),
                                Map.of(),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(5.89).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5.26).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(11.98).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        false,
                        true,
                        List.of(1, 2),
                        true,
                         true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_Source_Weekday_Weekend() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.92).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(20.631),
                                "rightValue", BigDecimal.valueOf(200.52).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(148).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-54.05).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(436.26).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(10806.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        ),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(3.01).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.32).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-70.83).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        true,
                        List.of(1, 2),
                        false,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_Source_OnlyWeekdays() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(28).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-97.29).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(0.59).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.115),
                                "rightValue", BigDecimal.valueOf(512.95).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(8.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(28).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(142.86).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(889.33).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(2).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(44366.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.ZERO,
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        ),
                                        "SW - MYF", Map.of("02-Jan-2024", BigDecimal.valueOf(34).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of()
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(6.13).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5.06).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(21.15).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        false,
                        List.of(1, 2),
                        false,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_Source_OnlyWeekends() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.83).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.516),
                                "rightValue", BigDecimal.valueOf(489.58).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(120).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-43.33).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(855.51).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(17010.20).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.ZERO,
                                        "02-Jan-2024", BigDecimal.ZERO),
                                Map.of(),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP)),
                                        "SW - MYF", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(5.89).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(5.26).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(11.98).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        false,
                        true,
                        List.of(1, 2),
                        false,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_SingleDate() throws ParseException {
                TotalActivity activity = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activityLastYear = createActivity("01-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(getChannelSourceSelections());
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("01-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(List.of(
                        createReservationNight("01-Jan-2023", BigDecimal.TEN, "EMAIL", "TS"),
                        createReservationNight("01-Jan-2023", BigDecimal.valueOf(25), "SW", "MYF"),
                        createReservationNight("01-Jan-2023", BigDecimal.valueOf(15), "", "TS")
                )));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("01-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("01-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(16.66667).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-95.14).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("EMAIL", Map.of(), "SW", Map.of()),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(0.67).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(20.631),
                                "rightValue", BigDecimal.valueOf(26.02).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("EMAIL", Map.of(), "SW", Map.of()),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(9.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.70).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-30.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-40.0).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(179.35).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(6).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(2889.17).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("EMAIL", Map.of(), "SW", Map.of()),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP)),
                                        "SW", Map.of("01-Jan-2024", BigDecimal.valueOf(30).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(1.26).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.32).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-87.79).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("01-Jan-2024"),
                        true,
                        true,
                        List.of(1, 2),
                        true,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_SingleChannel() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_CHANNEL), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(List.of(getChannelSourceSelections().get(0)));
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24.66667).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.92).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(20.631),
                                "rightValue", BigDecimal.valueOf(200.52).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(148).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-54.05).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(436.26).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(10806.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        )
                                ),
                                Map.of(
                                        "EMAIL", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(3.01).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.32).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-70.83).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        true,
                        List.of(1),
                        true,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

            @Test
            void verifyDailyKPICardData_SingleSource() throws ParseException {
                TotalActivity activity1 = createActivity("01-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity2 = createActivity("02-Jan-2024", BigDecimal.valueOf(50));
                TotalActivity activity3 = createActivity("03-Jan-2024", BigDecimal.valueOf(50));

                TotalActivity activityLastYear1 = createActivity("01-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear2 = createActivity("02-Jan-2023", BigDecimal.valueOf(50));
                TotalActivity activityLastYear3 = createActivity("03-Jan-2023", BigDecimal.valueOf(50));

                List<ChannelSrcDailyFcst> forecasts = List.of(
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.TEN, BigDecimal.valueOf(7), BigDecimal.valueOf(9), 1, 1, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(15), BigDecimal.valueOf(9), BigDecimal.TEN, 2, 2, true),
                        getChannelSrcDailyFcst("01-Jan-2024", BigDecimal.valueOf(12), BigDecimal.TEN, BigDecimal.valueOf(11), 1, 1, true),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(20), BigDecimal.valueOf(17), BigDecimal.valueOf(15), 1, 1, false),
                        getChannelSrcDailyFcst("02-Jan-2024", BigDecimal.valueOf(17), BigDecimal.valueOf(8), BigDecimal.TEN, 2, 2, false),
                        getChannelSrcDailyFcst("03-Jan-2024", BigDecimal.valueOf(16), BigDecimal.valueOf(11), BigDecimal.valueOf(13), 1, 1, false)
                );

                when(tenantCrudService.findByNamedQuery(eq(ChannelSrcDailyFcst.CHANNEL_SRC_DAILY_FCST_BY_SOURCE), any())).thenReturn(List.copyOf(forecasts));
                when(tenantCrudService.findAll(DOWType.class)).thenReturn(getDOWTypes());
                when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(List.of(getChannelSourceSelections().get(0)));
                when(tenantCrudService.findByNamedQuery(
                        ReservationNight.GET_BY_OCCUPANCY_DT_RANGE,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023")
                        )
                )).thenReturn(new ArrayList<>(getReservationNightList()));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2024"),
                                "endDate", dateFormat.parse("03-Jan-2024"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activity1, activity2, activity3)));
                when(tenantCrudService.findByNamedQuery(
                        TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        Map.of(
                                "startDate", dateFormat.parse("01-Jan-2023"),
                                "endDate", dateFormat.parse("03-Jan-2023"),
                                "propertyId", PROPERTY_ID
                        )
                )).thenReturn(new ArrayList<>(List.of(activityLastYear1, activityLastYear2, activityLastYear3)));
                when(dashboardService.getMetrics2((List<MetricRequest>) any(), (Date) any(), (Date) any())).thenReturn(createMetricResponse());

                Map<String, ChannelSrcFcstKPIDTO> expectedResult = Map.of(
                        "AvgChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.76).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(24.67).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-96.92).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.68).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(0.81).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(0.75).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(0.91).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevenueFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(62.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(20.631),
                                "rightValue", BigDecimal.valueOf(200.52).setScale(2, RoundingMode.HALF_UP)),
                                Map.of("01-Jan-2024", BigDecimal.valueOf(26.00).setScale(2, RoundingMode.HALF_UP), "03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of("03-Jan-2024", BigDecimal.valueOf(11.00).setScale(2, RoundingMode.HALF_UP), "02-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(17.00).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetADRFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(0.69).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(1.00).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-31.00).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelCostFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(68.00).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(148).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-54.05).setScale(2, RoundingMode.HALF_UP))
                        ),
                        "ChannelOccupancyFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(436.26).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(4).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(10806.50).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "01-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP),
                                        "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                        "02-Jan-2024", BigDecimal.valueOf(74).setScale(2, RoundingMode.HALF_UP)),
                                Map.of(
                                        "EMAIL - TS", Map.of(
                                                "03-Jan-2024", BigDecimal.valueOf(32).setScale(2, RoundingMode.HALF_UP),
                                                "02-Jan-2024", BigDecimal.valueOf(40).setScale(2, RoundingMode.HALF_UP)
                                        )
                                ),
                                Map.of(
                                        "EMAIL - TS", Map.of("01-Jan-2024", BigDecimal.valueOf(44).setScale(2, RoundingMode.HALF_UP))
                                )
                        ),
                        "NetRevPARFcst", new ChannelSrcFcstKPIDTO(Map.of(
                                "centerValue", BigDecimal.valueOf(3.01).setScale(2, RoundingMode.HALF_UP),
                                "leftValue", BigDecimal.valueOf(10.32).setScale(2, RoundingMode.HALF_UP),
                                "rightValue", BigDecimal.valueOf(-70.83).setScale(2, RoundingMode.HALF_UP))
                        )
                );

                Map<String, ChannelSrcFcstKPIDTO> result = service.getDailyKPICardData(
                        dateFormat.parse("01-Jan-2024"),
                        dateFormat.parse("03-Jan-2024"),
                        true,
                        true,
                        List.of(1),
                        false,
                        true
                );

                result.keySet().forEach(key -> {
                    assertEquals(expectedResult.get(key).getKpiCardMap(), result.get(key).getKpiCardMap());
                    assertEquals(expectedResult.get(key).getKpiBarGraphMap(), result.get(key).getKpiBarGraphMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekdayMap(), result.get(key).getKpiLineGraphAndTableWeekdayMap());
                    assertEquals(expectedResult.get(key).getKpiLineGraphAndTableWeekendMap(), result.get(key).getKpiLineGraphAndTableWeekendMap());
                });
            }

        }

        private List<DOWType> getDOWTypes() {
            return List.of(
                    createDOWType(1, 1),
                    createDOWType(2, 1),
                    createDOWType(3, 0),
                    createDOWType(4, 0),
                    createDOWType(5, 0),
                    createDOWType(6, 0),
                    createDOWType(7, 0)
            );
        }

        private DOWType createDOWType(int id, int type) {
            DOWType dowType = new DOWType();
            dowType.setDowType(type);
            dowType.setId(id);
            return dowType;
        }

        private ChannelSrcDailyFcst getChannelSrcDailyFcst(
                String date,
                BigDecimal roomSoldFcst,
                BigDecimal roomRevenueFcst,
                BigDecimal channelCostFcst,
                int channelId,
                int sourceId,
                boolean isWeekend
        ) throws ParseException {
            ChannelSrcDailyFcst channelSrcDailyFcst = new ChannelSrcDailyFcst();
            channelSrcDailyFcst.setOccupancyDate(dateFormat.parse(date));
            channelSrcDailyFcst.setRoomsSoldFcst(roomSoldFcst);
            channelSrcDailyFcst.setRoomRevenueFcst(roomRevenueFcst);
            channelSrcDailyFcst.setChannelCostFcst(channelCostFcst);
            channelSrcDailyFcst.setChannelId(channelId);
            channelSrcDailyFcst.setSourceId(sourceId);
            channelSrcDailyFcst.setDowType(isWeekend ? 1 : 0);
            return channelSrcDailyFcst;
        }

        private List<ChannelSourceSelectionView> getChannelSourceSelections() {
            ChannelSourceSelectionView tsSource = getChannelSourceSelection(1, 1, "EMAIL", 1, "TS", "SOURCE", true);
            ChannelSourceSelectionView myfSource = getChannelSourceSelection(2, 2, "SW", 2, "MYF", "SOURCE", false);
            return List.of(tsSource, myfSource);
        }

        private List<ReservationNight> getReservationNightList() throws ParseException {
            return List.of(
                    createReservationNight("01-Jan-2023", BigDecimal.TEN, "EMAIL", "TS"),
                    createReservationNight("01-Jan-2023", BigDecimal.valueOf(25), "SW", "MYF"),
                    createReservationNight("01-Jan-2023", BigDecimal.valueOf(15), "", "TS"),
                    createReservationNight("02-Jan-2023", BigDecimal.valueOf(30), "EMAIL", "TS"),
                    createReservationNight("02-Jan-2023", BigDecimal.valueOf(40), "SW", "MYF"),
                    createReservationNight("03-Jan-2023", BigDecimal.valueOf(28), "EMAIL", "TS")
            );
        }

        private ReservationNight createReservationNight(String date, BigDecimal cost, String channel, String source) throws ParseException {
            ReservationNight reservationNight = new ReservationNight();
            reservationNight.setOccupancyDate(dateFormat.parse(date));
            reservationNight.setTotalAcquisitionCost(cost);
            reservationNight.setSourceBooking(source);
            reservationNight.setChannel(channel);
            return reservationNight;
        }

        private TotalActivity createActivity(String date, BigDecimal capacity) throws ParseException {
            TotalActivity activity = new TotalActivity();
            activity.setOccupancyDate(dateFormat.parse(date));
            activity.setTotalAccomCapacity(capacity);
            return activity;
        }

        private DashboardMetric2 createMetricResponse() throws ParseException {
            Date date1 = dateFormat.parse("01-Jan-2024");
            Date date2 = dateFormat.parse("03-Jan-2024");

            // setup response
            DashboardMetric2 metric = new DashboardMetric2();

            // setup revFcstResponse
            MetricResponse revFcstResponse = new MetricResponse();
            Map<String, List<?>> revFcstValuesMap = new HashMap<>();
            List<BigDecimal> revFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
            revFcstValuesMap.put("PROPERTY", revFcstList);
            revFcstResponse.setMetricValuesByGroupByType(revFcstValuesMap);

            // setup occupancyResponse
            MetricResponse occupancyFcstResponse = new MetricResponse();
            Map<String, List<?>> occupancyFcstValuesMap = new HashMap<>();
            List<BigDecimal> occupancyFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
            occupancyFcstValuesMap.put("PROPERTY", occupancyFcstList);
            occupancyFcstResponse.setMetricValuesByGroupByType(occupancyFcstValuesMap);

            // setup capacityResponse
            MetricResponse capacityResponse = new MetricResponse();
            Map<String, List<?>> capacityValuesMap = new HashMap<>();
            List<BigDecimal> capacityList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
            capacityValuesMap.put("PROPERTY", capacityList);
            capacityResponse.setMetricValuesByGroupByType(capacityValuesMap);

            // setup revParFcstResponse
            MetricResponse revParFcstResponse = new MetricResponse();
            Map<String, List<?>> revParFcstValuesMap = new HashMap<>();
            List<BigDecimal> revParFcstList = Arrays.asList(new BigDecimal("10.115"), new BigDecimal("10.516"));
            revParFcstValuesMap.put("PROPERTY", revParFcstList);
            revParFcstResponse.setMetricValuesByGroupByType(revParFcstValuesMap);

            // attach response metrics to response
            metric.setMetricResponses(Arrays.asList(revFcstResponse, occupancyFcstResponse, capacityResponse, revParFcstResponse));

            // setup occupancy dates for response
            Date date3 = dateFormat.parse("03-Jan-2024");
            Set<Date> occupancyDates = new HashSet<>();
            occupancyDates.add(date1);
            occupancyDates.add(date2);
            occupancyDates.add(date3);
            metric.setOccupancyDates(occupancyDates);

            return metric;
        }
    }

    @Nested
    class ChannelSourceSelections {
        @Test
        public void shouldQueryDatabaseForChannelSourceSelections() {
            ChannelSourceSelectionView firstSelection = getChannelSourceSelection(1, 1, "CH1", 1, "SRC1", "SOURCE", true);
            ChannelSourceSelectionView secondSelection = getChannelSourceSelection(2, 1, "CH1", 2, "SRC2", "SOURCE", false);
            List<ChannelSourceSelectionView> expectedSelections = List.of(firstSelection, secondSelection);
            when(tenantCrudService.findAll(ChannelSourceSelectionView.class)).thenReturn(expectedSelections);

            List<ChannelSourceSelectionView> actualSelections = service.getAllChannelSourceSelections();

            verify(tenantCrudService, times(1)).findAll(ChannelSourceSelectionView.class);
            assertEquals(expectedSelections, actualSelections);
        }
    }

    private ChannelSourceSelectionView getChannelSourceSelection(
            Integer channelSourceMappingId,
            Integer channelId,
            String channelName,
            Integer sourceId,
            String sourceName,
            String forecastSource,
            boolean isDefault) {
        ChannelSourceSelectionView channelSourceSelectionView = new ChannelSourceSelectionView();
        channelSourceSelectionView.setId(channelSourceMappingId);
        channelSourceSelectionView.setChannelId(channelId);
        channelSourceSelectionView.setChannelName(channelName);
        channelSourceSelectionView.setSourceId(sourceId);
        channelSourceSelectionView.setSourceName(sourceName);
        channelSourceSelectionView.setForecastSource(forecastSource);
        channelSourceSelectionView.setIsDefault(isDefault);
        return channelSourceSelectionView;
    }
}
