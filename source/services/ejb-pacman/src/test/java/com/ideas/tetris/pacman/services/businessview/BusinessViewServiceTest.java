package com.ideas.tetris.pacman.services.businessview;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.businessview.entity.BusinessGroupMarketSegment;
import com.ideas.tetris.pacman.services.businessview.entity.ClientBusinessGroup;
import com.ideas.tetris.pacman.services.businessview.entity.ClientBusinessView;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyGroupCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyPropertyGroupCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.ClientMarketSegment;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

// Reverting to Previous version
public class BusinessViewServiceTest extends AbstractG3JupiterTest {

    private BusinessViewService bvService;

    @Mock
    VirtualPropertyMappingService virtualPropertyMappingService;

    @Mock
    CorporateBusinessViewMappingRulesService corporateBusinessViewMappingRulesService;

    @BeforeEach
    public void setUp() {
        bvService = new BusinessViewService();
        bvService.setGlobalCrudService(globalCrudService());
        inject(bvService, "virtualPropertyMappingService", virtualPropertyMappingService);
        PropertyService propertyService = new PropertyService();
        propertyService.setGlobalCrudService(globalCrudService());
        bvService.setPropertyService(propertyService);
        bvService.setCorporateBusinessViewMappingRulesService(corporateBusinessViewMappingRulesService);
        deleteAllBusinessViews();
    }

    @AfterEach
    public void tearDown() {
        deleteAllBusinessViews();
    }

    @Test
    public void testCreateUpdateRetrieveBusinessView() {
        List<ClientBusinessView> businessViews;
        // shouldn't be any to start with
        businessViews = retrieveBusinessViews();
        assertNotNull(businessViews, "Should've returned an empty list");
        assertEquals(0, businessViews.size(), "Should've returned an empty list");
        // add one
        ClientBusinessView byGroupTransient = new ClientBusinessView("NoGroup", "No Business Groups");
        businessViews = new ArrayList<ClientBusinessView>();
        businessViews.add(byGroupTransient);
        businessViews = updateBusinessViews(businessViews);
        // verify the add
        assertEquals(1, businessViews.size(), "Wrong number of views");
        assertNotNull(businessViews.get(0).getId(), "id should've been set on call to update");
        assertEquals("NoGroup", businessViews.get(0).getName(), "name incorrect");
        assertEquals("No Business Groups", businessViews.get(0).getDescription(), "description incorrect");
        // retrieve and verify
        businessViews = retrieveBusinessViews();
        assertEquals(1, businessViews.size(), "Wrong number of views");
        assertNotNull(businessViews.get(0).getId(), "id should've been set on call to update");
        assertEquals("NoGroup", businessViews.get(0).getName(), "name incorrect");
        assertEquals("No Business Groups", businessViews.get(0).getDescription(), "description incorrect");
        // add a second one
        ClientBusinessView byQualifiedUnqualified = new ClientBusinessView("NoGroup2", "No Business Groups");
        businessViews.add(byQualifiedUnqualified);
        businessViews = updateBusinessViews(businessViews);
        // verify second record
        businessViews = retrieveBusinessViews();
        assertEquals(2, businessViews.size(), "Wrong number of views");
        assertNotNull(businessViews.get(1).getId(), "id should've been set on call to update");
        assertEquals("NoGroup2", businessViews.get(1).getName(), "name incorrect");
        assertEquals("No Business Groups", businessViews.get(1).getDescription(), "description incorrect");
        // retrieve and verify
        businessViews = retrieveBusinessViews();
        assertEquals(2, businessViews.size(), "Wrong number of views");
        assertNotNull(businessViews.get(1).getId(), "id should've been set on call to update");
        assertEquals("NoGroup2", businessViews.get(1).getName(), "name incorrect");
        assertEquals("No Business Groups", businessViews.get(1).getDescription(), "description incorrect");
        // update second record
        businessViews.get(1).setName("NoGroup2Update");
        // Set this as Default View
        businessViews.get(1).setDefaultCorpBusinessView(1);
        businessViews = updateBusinessViews(businessViews);
        // verify update
        assertEquals("NoGroup2Update", businessViews.get(1).getName(), "name incorrect");
        assertEquals(new Integer(1), businessViews.get(1).getDefaultCorpBusinessView(), "Default View is set");
        // retrieve and verify
        businessViews = retrieveBusinessViews();
        ;
        assertEquals(2, businessViews.size(), "Wrong number of views");
        assertEquals("NoGroup2Update", businessViews.get(1).getName(), "name incorrect");
        assertEquals("No Business Groups", businessViews.get(1).getDescription(), "description incorrect");
    }

    @Test
    public void testCreateRetrieveBusinessViewWithBusinessGroups() {
        List<ClientBusinessView> businessViews = generateByCorporateBusinessView();
        updateBusinessViews(businessViews);
        tenantCrudService().clear();
        businessViews = retrieveBusinessViews();
        // verify the business view
        assertBusinessView(businessViews);
        // verify the business groups
        assertBusinessGroups(businessViews);
    }

    @Test
    public void testCreateRetrieveBusinessViewWithBusinessGroupsAndMarketSegments() {
        List<ClientBusinessView> businessViews = generateByCorporateBusinessView();
        updateBusinessViews(businessViews);
        tenantCrudService().clear();
        businessViews = retrieveBusinessViews();
        assertBusinessView(businessViews);
        assertBusinessGroups(businessViews);
        assertMarketSegments(businessViews);
    }

    @Test
    public void testUnassignMarketSegment() {
        List<ClientBusinessView> businessViews = generateByCorporateBusinessView();
        updateBusinessViews(businessViews);
        businessViews = retrieveBusinessViews();
        assertBusinessView(businessViews);
        assertBusinessGroups(businessViews);
        assertMarketSegments(businessViews);
        flushAndClear();
        // now unassign one of the market segments for the first business groups
        // for the first business view
        ClientBusinessView businessView = businessViews.get(0);
        BusinessGroupMarketSegment ms2 = null;
        for (ClientBusinessGroup bg : businessView.getBusinessGroups()) {
            // only change the mappings for the Non-corporate business group
            if (bg.getName().equals("Non-corporate")) {
                List<BusinessGroupMarketSegment> mappings = bg.getMarketSegments();
                int elementIndexToRemove = -1;
                int i = 0;
                // remove MS2
                for (BusinessGroupMarketSegment mapping : mappings) {
                    if (mapping.getMarketSegmentCode().equals("RACK")) {
                        elementIndexToRemove = i;
                    }
                    i++;
                }
                // remove the first mapping so that it gets unassigned
                ms2 = mappings.remove(elementIndexToRemove);
            }
        }
        // save the move to unassign
        updateBusinessViews(businessViews);
        // retrieve for verification
        businessViews = retrieveBusinessViews();
        businessView = businessViews.get(0);
        Integer afterMarketSegmentMappingCount = 99;
        for (ClientBusinessGroup bg : businessView.getBusinessGroups()) {
            if (bg.getName().equals("Non-corporate")) {
                // get the mappings for the first business group
                List<BusinessGroupMarketSegment> mappings = bg.getMarketSegments();
                // get the after count for later assertion
                afterMarketSegmentMappingCount = mappings.size();
                // make sure the unassigned one isn't there
                assertFalse(bg.getMarketSegments().contains(ms2));
            }
        }
        assertEquals(4, afterMarketSegmentMappingCount.intValue(), "Market segment count incorrect");
    }

    @Test
    public void testRetrieveUniqueMarketSegmentCodes() {
        bvService.setMultiPropertyCrudService(multiPropertyCrudService());
        Set<String> codes = bvService.retrieveUniqueMarketSegmentCodes();
        assertNotNull(codes, "returned set of codes should not be null");
        assertTrue(codes.size() > 0, "should be at least one market segment code returned, actual returned: " + codes.size());
        assertTrue(codes.contains("RACK"), "set should contain RACK");
    }

    @Test
    void shouldNotRetrieveUniqueMarketSegmentCodesForInActiveProperty() {
        globalCrudService().executeUpdateByNativeQuery("update Property set Status_ID=2 where Property_ID=6");
        bvService.setMultiPropertyCrudService(multiPropertyCrudService());
        Set<String> codes = bvService.retrieveUniqueMarketSegmentCodes();
        assertNotNull(codes, "returned set of codes should not be null");
        assertTrue(codes.size() > 0, "should be at least one market segment code returned, actual returned: " + codes.size());
        assertTrue(codes.contains("RACK"), "set should contain RACK");
        assertFalse(codes.contains("MS1"), "set should not contain MS1");
    }

    @Test
    void shouldRetrieveUniqueMarketSegmentCodesForActiveProperty() {
        bvService.setMultiPropertyCrudService(multiPropertyCrudService());
        Set<String> codes = bvService.retrieveUniqueMarketSegmentCodes();
        assertNotNull(codes, "returned set of codes should not be null");
        assertTrue(codes.size() > 0, "should be at least one market segment code returned, actual returned: " + codes.size());
        assertTrue(codes.contains("MS1"), "set should contain MS1");
    }


    @Test
    public void testDeleteBusinessGroup() {
        List<ClientBusinessView> businessViews = generateByCorporateBusinessView();
        businessViews = updateBusinessViews(businessViews);
        flushAndClear();
        bvService.deleteBusinessGroup(businessViews.get(0).getBusinessGroups().iterator().next().getId());
        ClientBusinessView retrievedBusinessView = globalCrudService().find(ClientBusinessView.class, businessViews.get(0).getId());
        assertEquals(1, retrievedBusinessView.getBusinessGroups().size());
    }

    @Test
    void testSyncPropertyMSIntoGlobalDBClientUniqueMS() {
        List<ClientMarketSegment> ms = globalCrudService().findAll(ClientMarketSegment.class);
        assertEquals(0, ms.size());
        bvService.tenantCrudService = tenantCrudService();
        bvService.syncPropertyMSIntoGlobalDBClientUniqueMS();
        List<ClientMarketSegment> msList = globalCrudService().findAll(ClientMarketSegment.class);
        assertEquals(11, msList.size());
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg values(5, 'Mkt', 'Mkt', 'Mkt', 1, getdate(), 1, 1, getdate(), 1, 0, 0)");
        bvService.syncPropertyMSIntoGlobalDBClientUniqueMS();
        msList = globalCrudService().findAll(ClientMarketSegment.class);
        assertEquals(12, msList.size());
    }

    private List<ClientBusinessView> updateBusinessViews(List<ClientBusinessView> businessViews) {
        businessViews = bvService.updateBusinessViews(businessViews);
        return businessViews;
    }

    private void assertBusinessGroups(List<ClientBusinessView> businessViews) {
        int i = 0;
        assertEquals(2, businessViews.get(0).getBusinessGroups().size(), "Wrong number of groups");
        for (ClientBusinessGroup businessGroup : businessViews.get(0).getBusinessGroups()) {
            assertNotNull(businessGroup.getId(), "id should've been set on call to update");
            // ensure sort order
            if (businessGroup.getRanking() == 1) {
                assertEquals("Corporate", businessGroup.getName(), "name incorrect");
                assertEquals("Grouped by Corporate Business", businessGroup.getDescription(), "description incorrect");
            } else if (businessGroup.getRanking() == 2) {
                assertEquals("Non-corporate", businessGroup.getName(), "name incorrect");
                assertEquals("Grouped by Non-corporate Business", businessGroup.getDescription(), "description incorrect");
            } else {
                fail("Should only be two business groups");
            }
            i++;
        }
        if (i != 2) {
            fail("Should only be two business groups");
        }
    }

    private void assertBusinessView(List<ClientBusinessView> businessViews) {
        assertEquals(1, businessViews.size(), "Wrong number of views");
        assertNotNull(businessViews.get(0).getId(), "id should've been set on call to update");
        assertEquals("By Corporate / Non-corporate", businessViews.get(0).getName(), "name incorrect");
        assertEquals("Broken down by Corporate / Non-corporate", businessViews.get(0).getDescription(), "description incorrect");
    }

    private void assertMarketSegments(List<ClientBusinessView> businessViews) {
        // Ugly, could use some cleanup. Ideally using comparators
        for (ClientBusinessView bv : businessViews) {
            for (ClientBusinessGroup bg : bv.getBusinessGroups()) {
                if (bg.getName().equals("Corporate")) {
                    assertEquals(1, bg.getMarketSegments().size(), "Wrong number of market segments");
                    for (BusinessGroupMarketSegment ms : bg.getMarketSegments()) {
                        if (ms.getMarketSegmentCode().equals("CORP")) {
                            // OK
                        } else {
                            fail("Wrong market segment code. Expected CORP. Was: " + ms.getMarketSegmentCode());
                        }
                    }
                } else if (bg.getName().equals("Non-corporate")) {
                    for (BusinessGroupMarketSegment ms : bg.getMarketSegments()) {
                        if (ms.getMarketSegmentCode().equals("BART") || ms.getMarketSegmentCode().equals("CONS") || ms.getMarketSegmentCode().equals("DIST") || ms.getMarketSegmentCode().equals("RACK") || ms.getMarketSegmentCode().equals("COMP")) {
                            // OK
                        } else {
                            fail("Wrong market segment code. Was: " + ms.getMarketSegmentCode());
                        }
                    }
                } else {
                    fail("Expected only Corporate and Non-corporate Business Groups");
                }
            }
        }
    }

    private List<ClientBusinessView> retrieveBusinessViews() {
        List<ClientBusinessView> businessViews;
        businessViews = bvService.retrieveBusinessViews();
        return businessViews;
    }

    /**
     * Generates a single Business View
     * with two Business Groups (Corporate, Non-corporate)
     * with the following Market Segment Mappings
     * Corporate = CORP
     * Transient = BART, COMP, CONS, DIST, RACK
     */
    private List<ClientBusinessView> generateByCorporateBusinessView() {
        List<ClientBusinessView> businessViews = new ArrayList<ClientBusinessView>();
        // add a business view with two groups
        ClientBusinessView byGroupTransient = new ClientBusinessView("By Corporate / Non-corporate", "Broken down by Corporate / Non-corporate");
        businessViews.add(byGroupTransient);
        // create the corporate
        Integer RANKING_ONE = 1;
        ClientBusinessGroup group = generateBusinessGroup("Corporate", new String[]{"CORP"}, RANKING_ONE);
        // create the non-corporate business group
        Integer RANKING_TWO = 2;
        ClientBusinessGroup transients = generateBusinessGroup("Non-corporate", new String[]{"BART", "COMP", "CONS", "DIST", "RACK"}, RANKING_TWO);
        // setup the relationships
        byGroupTransient.setBusinessGroups(new HashSet<ClientBusinessGroup>());
        byGroupTransient.getBusinessGroups().add(group);
        byGroupTransient.getBusinessGroups().add(transients);
        group.setClientBusinessView(byGroupTransient);
        transients.setClientBusinessView(byGroupTransient);
        return businessViews;
    }

    private ClientBusinessGroup generateBusinessGroup(String groupName, String[] marketSegmentCodes, Integer ranking) {
        ClientBusinessGroup group = new ClientBusinessGroup(groupName, "Grouped by " + groupName + " Business", ranking);
        List<BusinessGroupMarketSegment> groupMarketSegments = new ArrayList<BusinessGroupMarketSegment>();
        for (String code : marketSegmentCodes) {
            addMarketSegmentToBusinessGroup(code, group, groupMarketSegments);
        }
        group.setMarketSegments(groupMarketSegments);
        return group;
    }

    private void addMarketSegmentToBusinessGroup(String marketSegmentCode, ClientBusinessGroup group, List<BusinessGroupMarketSegment> groupMarketSegments) {
        BusinessGroupMarketSegment ms3 = new BusinessGroupMarketSegment(marketSegmentCode);
        ms3.setCreateDate(LocalDateTime.now());
        ms3.setClientBusinessGroup(group);
        groupMarketSegments.add(ms3);
    }

    private void deleteAllBusinessViews() {
        List<ClientBusinessView> businessViews;
        businessViews = bvService.retrieveBusinessViews();
        for (ClientBusinessView bv : businessViews) {
            bvService.deleteBusinessView(bv.getId());
        }
    }

    @Test
    public void testSetDefaultCorporateBusinessView() {
        List<ClientBusinessView> listClientBusinessViews = new ArrayList<ClientBusinessView>();
        ClientBusinessView cbv = new ClientBusinessView();
        cbv.setDescription("test");
        cbv.setName("test");
        cbv.setStatusId(Integer.valueOf(1));
        cbv.setCreateDate(new java.sql.Timestamp(new java.util.Date().getTime()));
        cbv.setLastUpdatedByUserId(Integer.valueOf(11403));
        cbv.setCreatedByUserId(Integer.valueOf(11403));
        listClientBusinessViews.add(cbv);
        List<ClientBusinessView> clientBusinessView = bvService.updateBusinessViews(listClientBusinessViews);
        bvService.setDefaultCorpBusinessView(clientBusinessView.get(0).getId());
        ClientBusinessView cbv1 = new ClientBusinessView();
        cbv1.setDescription("test1");
        cbv1.setName("test1");
        cbv1.setStatusId(Integer.valueOf(1));
        cbv1.setCreateDate(new java.sql.Timestamp(new java.util.Date().getTime()));
        cbv1.setLastUpdatedByUserId(Integer.valueOf(11403));
        cbv1.setCreatedByUserId(Integer.valueOf(11403));
        listClientBusinessViews.add(cbv1);
        List<ClientBusinessView> clientBusinessViewOne = bvService.updateBusinessViews(listClientBusinessViews);
        bvService.setDefaultCorpBusinessView(clientBusinessViewOne.get(0).getId());
    }

    @Test
    public void testCreateCorporateBusinessView() {
        ClientBusinessView businessViews;
        // Create new CBV and persist it.
        businessViews = new ClientBusinessView("CBV", "CBV Desc");
        businessViews = bvService.persistCorporateBusinessViews(businessViews);
        // verify the add
        assertNotNull(businessViews.getId(), "id should've been set on call to update");
        assertEquals("CBV", businessViews.getName(), "name incorrect");
        assertEquals("CBV Desc", businessViews.getDescription(), "description incorrect");
    }

    @Test
    public void testUpdateCorporateBusinessView() {
        ClientBusinessView businessViews;
        // Create new CBV and persist it.
        businessViews = new ClientBusinessView("CBV_Name", "CBV Update Desc");
        businessViews = bvService.persistCorporateBusinessViews(businessViews);
        // verify the add
        assertNotNull(businessViews.getId(), "id should've been set on call to update");
        assertEquals("CBV_Name", businessViews.getName(), "name incorrect");
        assertEquals("CBV Update Desc", businessViews.getDescription(), "description incorrect");
        // update second record
        businessViews.setName("CBV_Name_Updated");
        // Set this as Default View
        businessViews.setDefaultCorpBusinessView(1);
        businessViews = bvService.updateCorporateBusinessViews(businessViews);
        // verify update
        assertEquals("CBV_Name_Updated", businessViews.getName(), "name incorrect");
        assertEquals(new Integer(1), businessViews.getDefaultCorpBusinessView(), "Default View is set");
    }

    @Test
    public void testRetrieveActiveCorporateBusinessView() {
        ClientBusinessView businessViews1;
        ClientBusinessView businessViews2;
        List<ClientBusinessView> listClientBusinessViews = new ArrayList<ClientBusinessView>();
        // Create new CBV and persist it.
        businessViews1 = new ClientBusinessView("CBV_Act", "CBV_Act Desc");
        businessViews1 = bvService.persistCorporateBusinessViews(businessViews1);
        // verify the add
        assertNotNull(businessViews1.getId(), "id should've been set on call to update");
        assertEquals("CBV_Act", businessViews1.getName(), "name incorrect");
        assertEquals("CBV_Act Desc", businessViews1.getDescription(), "description incorrect");
        listClientBusinessViews.add(businessViews1);
        businessViews2 = new ClientBusinessView("CBV_Soft_Del", "CBV_Soft_Del Desc");
        businessViews2.setStatusId(2);
        businessViews2 = bvService.persistCorporateBusinessViews(businessViews2);
        // verify the add
        assertNotNull(businessViews2.getId(), "id should've been set on call to update");
        assertEquals("CBV_Soft_Del", businessViews2.getName(), "name incorrect");
        assertEquals("CBV_Soft_Del Desc", businessViews2.getDescription(), "description incorrect");
        listClientBusinessViews.add(businessViews2);
        List<ClientBusinessView> businessViews;
        // shouldn't be any to start with
        businessViews = retrieveBusinessViews();
        assertEquals(1, businessViews.size(), "Should return an list with ONE/Active Corporate Business view");
        businessViews2.setStatusId(1);
        businessViews = updateBusinessViews(listClientBusinessViews);
        deleteAllBusinessViews();
    }

    @Test
    public void shouldGetBusinessViewIdByName() {
        BusinessGroup BG1 = addBusinessView("BG1");
        BusinessGroup BG2 = addBusinessView("BG2");
        bvService.tenantCrudService = tenantCrudService();
        Map<String, Integer> businessViewIdByName = bvService.getBusinessViewIdByName();
        assertEquals(BG1.getId(), businessViewIdByName.get("BG1"));
        assertEquals(BG2.getId(), businessViewIdByName.get("BG2"));
        assertEquals(-1, businessViewIdByName.get("Unassigned").intValue());
    }

    @Test
    public void shouldGetBusinessViewIdByNameForPropertyGroup() {
        PropertyGroup propertyGroup = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), 2);
        UniquePropertyPropertyGroupCreator.createPropertyPropertyGroupByPropertyGroup(globalCrudService(), propertyGroup, 5);
        UniquePropertyPropertyGroupCreator.createPropertyPropertyGroupByPropertyGroup(globalCrudService(), propertyGroup, 6);
        PacmanWorkContextHelper.setPropertyGroupId(propertyGroup.getId());
        ClientBusinessView cbv = createClientBusinessViewBy("CBV_Test");
        ClientBusinessGroup cbg = createClientBusinessGroupBy(cbv, "CBG");
        Map<String, Integer> businessViewIdByName = bvService.getBusinessViewIdByName();
        assertEquals(cbg.getId(), businessViewIdByName.get("CBG"));
    }

    @Test
    void shouldGetUnMappedMktSegForBusinessView() {
        List<ClientBusinessView> clientBusinessViews = generateByCorporateBusinessView();
        Set<String> allMktSeg = Set.of("BART", "COMP", "CONS", "DIST", "RACK", "CORP", "CORS", "BAR");
        Set<String> unMappedMarketSegmentCode = bvService.getUnMappedMarketSegmentCode(clientBusinessViews.get(0), allMktSeg);
        assertEquals(2, unMappedMarketSegmentCode.size());
        assertTrue(List.of("CORS", "BAR").containsAll(unMappedMarketSegmentCode));
    }

    private BusinessGroup addBusinessView(String name) {
        BusinessGroup businessGroup = new BusinessGroup();
        businessGroup.setName(name);
        businessGroup.setDescription(name);
        businessGroup.setRanking(1);
        businessGroup.setPropertyId(5);
        BusinessGroup savedBusinessGroup = tenantCrudService().save(businessGroup);
        return savedBusinessGroup;
    }

    private ClientBusinessView createClientBusinessViewBy(String name) {
        ClientBusinessView clientBusinessView = new ClientBusinessView();
        clientBusinessView.setName(name);
        clientBusinessView.setDescription(name);
        clientBusinessView.setStatusId(1);
        clientBusinessView.setDefaultCorpBusinessView(1);
        clientBusinessView.setClientId(2);
        return globalCrudService().save(clientBusinessView);
    }

    private ClientBusinessGroup createClientBusinessGroupBy(ClientBusinessView cbv, String name) {
        ClientBusinessGroup clientBusinessGroup = new ClientBusinessGroup();
        clientBusinessGroup.setName(name);
        clientBusinessGroup.setDescription(name);
        clientBusinessGroup.setClientBusinessView(cbv);
        clientBusinessGroup.setRanking(1);
        clientBusinessGroup.setStatusId(1);
        return globalCrudService().save(clientBusinessGroup);
    }
}
