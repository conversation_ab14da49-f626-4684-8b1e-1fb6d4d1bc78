package com.ideas.tetris.pacman.services.lastgooddecisions;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByRankDecisionService;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsMinMaxLOSServiceTest.COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsMinMaxLOSServiceTest.ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsMinMaxLOSServiceTest.QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE;
import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsServiceTest.QUERY_TO_FETCH_FUNCTION_DEFINITION;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_TIME_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LastGoodDecisionsFPLOSByRankServiceTest extends AbstractG3JupiterTest {

    public static final String MAX_DATE_RANGE = "2173-10-14";

    public static final LocalDate NOW = new LocalDate("2018-12-15");

    public static final String YYYY_MM_DD = "YYYY-MM-dd";

    public static final String START_DATE = DateUtil.formatDate(NOW.toDate(), YYYY_MM_DD);

    public static final String END_DATE = DateUtil.formatDate(NOW.plusDays(5).toDate(), YYYY_MM_DD);

    private static final String FIFTH_DAY = DateUtil.formatDate(NOW.plusDays(4).toDate(), DATE_TIME_FORMAT);

    private static final String FOURTH_DAY = DateUtil.formatDate(NOW.plusDays(3).toDate(), DATE_TIME_FORMAT);

    private static final String THIRD_DAY = DateUtil.formatDate(NOW.plusDays(2).toDate(), DATE_TIME_FORMAT);

    private static final String SECOND_DAY = DateUtil.formatDate(NOW.plusDays(1).toDate(), DATE_TIME_FORMAT);

    private static final String FIRST_DAY = DateUtil.formatDate(NOW.toDate(), DATE_TIME_FORMAT);

    private static final String DECISION_FPLOS_BY_RANK_COLUMNS = "Decision_FPLOS_By_Rank_ID,Decision_ID,Property_ID,Accom_Type_ID,Rate_Unqualified_ID,Arrival_DT,FPLOS,CreateDate_DTTM";

    private static final String PACE_FPLOS_BY_RANK_COLUMNS = "PACE_FPLOS_By_Rank_ID,Decision_ID,Property_ID,Accom_Type_ID,Rate_Unqualified_ID,Arrival_DT,FPLOS,CreateDate_DTTM";

    private int propertyId;

    @Mock
    DateService dateService;

    @Mock
    DecisionService decisionService;

    @InjectMocks
    LastGoodDecisionsFPLOSByRankService lastGoodDecisionsFPLOSByRankService;

    FPLOSByRankDecisionService fplosByRankDecisionService;

    final String NonPaceTable = "Decision_FPLOS_By_Rank";

    final String PaceTable = "PACE_FPLOS_By_Rank";

    public static final String FPLOS_BY_RANK_FUNCTION_FOR_DIFFERENTIAL = "\n" +
            "/*************************************************************************************\n" +
            "\n" +
            "Function Name: ufn_get_barbyrank_fplos_decisions_masterclassonly_differential\n" +
            "\n" +
            "Input Parameters : \n" +
            "\t@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)\n" +
            "\t@start_date --> occupancy_start_date ('2011-07-01')\n" +
            "\t@end_date --> occupancy_end_date ('2011-07-31')\n" +
            "\t@targetTime --> mainly used when isDiiferencial is 1. pass the date with which you want to compare the decisions with.\n" +
            "\t\n" +
            "Ouput Parameter : NA\n" +
            "\n" +
            "Execution: this is just an example\n" +
            "\tselect * from ufn_get_barbyrank_fplos_decisions_masterclassonly_differential(10016,'2011-12-01','2011-12-10','2011-12-01')\n" +
            "\n" +
            "\n" +
            "Purpose: The purpose of this function is to get the differential bar by rank FPLOS decisions only for master class.\n" +
            "\n" +
            "Assumptions : NA\n" +
            "\t\t \n" +
            "Author: Atul \n" +
            "Release Update:\n" +
            "Release_Dt\t\tFirst_Name\t\t\tLast_Name\t\t\t\tRelease Comments\n" +
            "----------\t----------------\t-------------------\t\t-------------------------------\n" +
            "04/23/2012\tAtul\t\t\t\tShendye\t\t\t\t\tInitial Version\n" +
            "***************************************************************************************/\n" +
            "create function [dbo].[ufn_get_barbyrank_fplos_decisions_masterclassonly_differential]\n" +
            "(\n" +
            "\t\t@property_id int,\n" +
            "\t\t@start_date date,\n" +
            "\t\t@end_date date,\n" +
            "\t\t@targetTime datetime\n" +
            ")\t\t\n" +
            "returns  @barbyrank_fplos_decisions_masterclassonly table\n" +
            "(\t\n" +
            "\tproperty_id\tint,\n" +
            "\tarrival_dt date,\n" +
            "\trate_qualified_name nvarchar(50),\n" +
            "\tfplos nvarchar(50)\n" +
            ")\n" +
            "as\n" +
            "begin\n" +
            "\t\tdeclare @barbyrank_fplos_decisions_1 table\n" +
            "\t\t(\t\n" +
            "\t\t\tproperty_id\tint,\n" +
            "\t\t\tarrival_dt date,\n" +
            "\t\t\trate_unqualified_id int,\n" +
            "\t\t\tdecision_id int,\n" +
            "\t\t\tUNIQUE CLUSTERED (property_id,arrival_dt,rate_unqualified_id,decision_id)\n" +
            "\t\t)\n" +
            "\n" +
            "\t\tinsert into @barbyrank_fplos_decisions_1\n" +
            "\t\tselect PFBR.Property_ID,Arrival_DT,Rate_Unqualified_ID,MAX(D.Decision_id)as Decision_id \n" +
            "\t\tfrom PACE_FPLOS_By_Rank PFBR inner join Decision D on PFBR.Decision_ID=D.Decision_ID \n" +
            "\t\t\tand PFBR.Property_ID=@property_id and Arrival_DT >= @start_date and Arrival_DT <= @end_date\n" +
            "\t\t\tand D.End_DTTM <=@targetTime\n" +
            "\t\tgroup by PFBR.Property_ID,Arrival_DT,Rate_Unqualified_ID\n" +
            "\t\t\t\n" +
            "\t\t\t\n" +
            "\t\tdeclare @barbyrank_fplos_decisions_2 table\n" +
            "\t\t(\t\n" +
            "\t\t\tproperty_id\tint,\n" +
            "\t\t\tarrival_dt date,\n" +
            "\t\t\trate_unqualified_id int,\n" +
            "\t\t\tfplos nvarchar(50),\n" +
            "\t\t\tUNIQUE CLUSTERED (property_id,arrival_dt,rate_unqualified_id)\n" +
            "\t\t)\n" +
            "\t\t\t\n" +
            "\t\tinsert into @barbyrank_fplos_decisions_2\n" +
            "\t\tselect distinct Property_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS \n" +
            "\t\t\tfrom Decision_FPLOS_By_Rank \n" +
            "\t\t\twhere Property_ID=@property_id and Arrival_DT >= @start_date and Arrival_DT <= @end_date\n" +
            "\n" +
            "\t\t\n" +
            "\t\tdeclare @barbyrank_fplos_decisions_3 table\n" +
            "\t\t(\t\n" +
            "\t\t\tproperty_id\tint,\n" +
            "\t\t\tarrival_dt date,\n" +
            "\t\t\trate_unqualified_id int,\n" +
            "\t\t\tcur_fplos nvarchar(50),\n" +
            "\t\t\tprev_fplos nvarchar(50),\n" +
            "\t\t\tUNIQUE CLUSTERED (property_id,arrival_dt,rate_unqualified_id)\n" +
            "\t\t)\n" +
            "\t\t\n" +
            "\t\tinsert into @barbyrank_fplos_decisions_3\n" +
            "\t\tselect D.Property_ID,D.Arrival_DT,D.Rate_Unqualified_ID,D.FPLOS as cur_fplos,PACE.FPLOS as prev_fplos \n" +
            "\t\tfrom @barbyrank_fplos_decisions_2 D ,\n" +
            "\t\t(\n" +
            "\t\t\tselect distinct PFBR.Property_ID,PFBR.Arrival_DT,PFBR.Rate_Unqualified_ID,FPLOS \n" +
            "\t\t\tfrom PACE_FPLOS_By_Rank PFBR \n" +
            "\t\t\tinner join\n" +
            "\t\t\t(\n" +
            "\t\t\t\tselect * from @barbyrank_fplos_decisions_1\n" +
            "\t\t\t) DE on PFBR.Property_ID=DE.Property_ID\n" +
            "\t\t\t\tand PFBR.Arrival_DT=DE.Arrival_DT\n" +
            "\t\t\t\tand PFBR.Rate_Unqualified_ID=DE.Rate_Unqualified_ID\n" +
            "\t\t\t\tand PFBR.Decision_ID=DE.Decision_ID\n" +
            "\t\t\twhere PFBR.Property_ID=@property_id and PFBR.Arrival_DT >= @start_date and PFBR.Arrival_DT <= @end_date\n" +
            "\t\t) PACE\n" +
            "\t\t\twhere D.Property_ID=PACE.Property_ID\n" +
            "\t\t\t\tand D.Arrival_DT=PACE.Arrival_DT\n" +
            "\t\t\t\tand D.Rate_Unqualified_ID=PACE.Rate_Unqualified_ID\n" +
            "\t\t\t\n" +
            "\n" +
            "\t\t-- insert new records which were not present as of target time.\n" +
            "\t\tinsert into @barbyrank_fplos_decisions_3\n" +
            "\t\tselect D.Property_ID,D.Arrival_DT,D.rate_unqualified_id,D.FPLOS as cur_fplos,'' as prev_fplos \n" +
            "\t\tfrom \n" +
            "\t\t@barbyrank_fplos_decisions_2 D \n" +
            "\t\tleft join \n" +
            "\t\t@barbyrank_fplos_decisions_1 pace\n" +
            "\t\ton \tD.Property_ID=PACE.Property_ID\n" +
            "\t\t\tand D.Arrival_DT=PACE.Arrival_DT\n" +
            "\t\t\tand D.rate_unqualified_id=PACE.rate_unqualified_id\n" +
            "\t\twhere pace.decision_id is null \t\n" +
            "\t\t\n" +
            "\t\tinsert into @barbyrank_fplos_decisions_masterclassonly\n" +
            "\t\t\tselect d.Property_ID,Arrival_DT,Rate_Code_Name,cur_fplos from @barbyrank_fplos_decisions_3 d\n" +
            "\t\t\tinner join Rate_Unqualified RUQ on RUQ.Rate_Unqualified_ID=d.Rate_Unqualified_ID and RUQ.Status_ID=1 and Yieldable=1 and RUQ.System_Default!=1\n" +
            "\t\t\twhere cur_fplos <> prev_fplos\t\t\n" +
            "\t\t\torder by Arrival_DT,Rate_Code_Name\n" +
            "\treturn\n" +
            "end\n";

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        propertyId = TestProperty.H2.getId();
        lastGoodDecisionsFPLOSByRankService.crudService = tenantCrudService();
        fplosByRankDecisionService = new FPLOSByRankDecisionService();
        fplosByRankDecisionService.setCrudService(tenantCrudService());
    }

    @Test
    public void shouldSendAsOfDecisions() {
        List<Integer> roomTypeIds = Arrays.asList(9, 10);
        final Date businessDate = new Date();
        cleanUpTables(roomTypeIds, true);
        // 1st time decision created
        final LocalDateTime firstDecisionCreationTime = LocalDateTime.now();
        final Decision decision1st = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE, firstDecisionCreationTime.toDate());
        RateUnqualified rateUnqualified_1 = UniqueRateUnqualified.createRateUnqualifiedBy(6, "RU_1");
        createDecision(businessDate, decision1st.getId(), rateUnqualified_1.getId(), "YYYYYYY", roomTypeIds);
        final Date startDate = DateUtil.addDaysToDate(new Date(), -1);
        final Date endDate = DateUtil.addDaysToDate(startDate, 1);
        // 2nd time decision created n uploaded
        final LocalDateTime firstDecisionUploadTime = firstDecisionCreationTime.plusMinutes(2);
        final LocalDateTime secondDecisionCreationTime = firstDecisionUploadTime.plusHours(2);
        final Decision decision2nd = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE, secondDecisionCreationTime.toDate());
        roomTypeIds = Arrays.asList(10);
        cleanUpTables(roomTypeIds, false);
        createDecision(businessDate, decision2nd.getId(), rateUnqualified_1.getId(), "NNNNNNN", roomTypeIds);
        // check first differential
        List<FPLOSDecisions> differentialsFPLOSByRankDecisions = fplosByRankDecisionService.getFPLOSDecisionData(startDate, endDate, firstDecisionUploadTime.toDate());
        assertEquals(1, differentialsFPLOSByRankDecisions.size());
        assertEquals("NNNNNNN", differentialsFPLOSByRankDecisions.get(0).getFplos());
        final LocalDateTime secondDecisionUploadTime = secondDecisionCreationTime.plusMinutes(2);
        // Give call for asOFDate decisions and verify differential data after that
        final LocalDateTime newDecisionCreationTime = secondDecisionUploadTime.plusHours(2);
        final Decision newDecisionIDForAsOF = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE, newDecisionCreationTime.toDate());
        when(decisionService.createBarFplosByRoomTypeDecision()).thenReturn(newDecisionIDForAsOF);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(endDate);
        lastGoodDecisionsFPLOSByRankService.createLastGoodDecisions(decision1st.getId());
        differentialsFPLOSByRankDecisions = fplosByRankDecisionService.getFPLOSDecisionData(startDate, endDate, secondDecisionUploadTime.toDate());
        assertEquals(1, differentialsFPLOSByRankDecisions.size());
        assertEquals("YYYYYYY", differentialsFPLOSByRankDecisions.get(0).getFplos());
        final LocalDateTime newDecisionUploadTime = newDecisionCreationTime.plusMinutes(2);
        // 3rd Day - regular differential call after AsOfDate
        final LocalDateTime nextDayDecisionCreationTime = newDecisionUploadTime.plusHours(4);
        final Decision decision3rd = UniqueDecisionCreator.createDecisionFor(6, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE, nextDayDecisionCreationTime.toDate());
        cleanUpTables(roomTypeIds, false);
        createDecision(businessDate, decision3rd.getId(), rateUnqualified_1.getId(), "NYYYYYN", roomTypeIds);
        differentialsFPLOSByRankDecisions = fplosByRankDecisionService.getFPLOSDecisionData(startDate, endDate, newDecisionUploadTime.toDate());
        assertEquals(1, differentialsFPLOSByRankDecisions.size());
        assertEquals("NYYYYYN", differentialsFPLOSByRankDecisions.get(0).getFplos());
    }

    @Test
    public void testIfAsOfDecisionsGeneratedAsPerRequiredDates() throws Exception {
        cleanPaceAndNonPaceTables();
        Date arrivalStartDate = LocalDate.parse(START_DATE).toDate();
        Date arrivalEndDate = LocalDate.parse(END_DATE).toDate();
        final Date businessDate = new Date();
        final Decision decisionId4 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -4), DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        final Decision decisionId3 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -3), DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        final Decision decisionId2 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -2), DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        final Decision decisionId1 = UniqueDecisionCreator.createDecisionFor(propertyId, DateUtil.addDaysToDate(businessDate, -1), DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        RateUnqualified rateUnqualified_1 = UniqueRateUnqualified.createRateUnqualifiedByDate(businessDate, LocalDate.parse(MAX_DATE_RANGE).toDate(), propertyId);
        RateUnqualified rateUnqualified_2 = UniqueRateUnqualified.createRateUnqualifiedByDate(businessDate, LocalDate.parse(MAX_DATE_RANGE).toDate(), propertyId);
        RateUnqualified rateUnqualified_3 = UniqueRateUnqualified.createRateUnqualifiedByDate(businessDate, LocalDate.parse(MAX_DATE_RANGE).toDate(), propertyId);
        RateUnqualified rateUnqualified_4 = UniqueRateUnqualified.createRateUnqualifiedByDate(businessDate, LocalDate.parse(MAX_DATE_RANGE).toDate(), propertyId);
        RateUnqualified rateUnqualified_5 = UniqueRateUnqualified.createRateUnqualifiedByDate(businessDate, LocalDate.parse(MAX_DATE_RANGE).toDate(), propertyId);
        final Decision businessDateDecision = UniqueDecisionCreator.createDecisionFor(propertyId, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        prepareDataForValidatingFPLOSByRank(arrivalStartDate, arrivalEndDate, businessDate, decisionId1, decisionId2, decisionId3, decisionId4, businessDateDecision, Arrays.asList(rateUnqualified_1, rateUnqualified_2, rateUnqualified_3, rateUnqualified_4, rateUnqualified_5));
        Decision newDecisionId = UniqueDecisionCreator.createDecisionFor(propertyId, businessDate, DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        when(decisionService.createBarFplosByRoomTypeDecision()).thenReturn(newDecisionId);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(arrivalStartDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(arrivalEndDate);
        lastGoodDecisionsFPLOSByRankService.createLastGoodDecisions(decisionId2.getId());
        List<FPLOSDecisions> fplosDecisionData = fplosByRankDecisionService.getFPLOSDecisionData(arrivalStartDate, arrivalEndDate, businessDateDecision.getCreateDate());
        assertEquals(4, fplosDecisionData.size());
        assertEquals(FIRST_DAY.split(" ")[0], fplosDecisionData.get(0).getArrivalDate().toString());
        assertEquals(SECOND_DAY.split(" ")[0], fplosDecisionData.get(1).getArrivalDate().toString());
        assertEquals(THIRD_DAY.split(" ")[0], fplosDecisionData.get(2).getArrivalDate().toString());
        assertEquals(FIFTH_DAY.split(" ")[0], fplosDecisionData.get(3).getArrivalDate().toString());
        assertEquals("YNNNNNN", fplosDecisionData.get(0).getFplos());
        assertEquals("YNNNNNN", fplosDecisionData.get(1).getFplos());
        assertEquals("NNYYYYY", fplosDecisionData.get(2).getFplos());
        assertEquals("YYYYYYN", fplosDecisionData.get(3).getFplos());
    }

    private void prepareDataForValidatingFPLOSByRank(Date arrivalStartDate, Date arrivalEndDate, Date businessDate, Decision decisionId1, Decision decisionId2, Decision decisionId3, Decision decisionId4, Decision businessDateDecision, List<RateUnqualified> rateUnqualifiedList) {
        populatePaceData(decisionId1, decisionId2, decisionId3, decisionId4, businessDateDecision, rateUnqualifiedList);
        populatePaceAndNonPaceData(decisionId1, decisionId2, decisionId3, decisionId4, businessDateDecision, rateUnqualifiedList);
    }

    private void populatePaceAndNonPaceData(Decision decisionId1, Decision decisionId2, Decision decisionId3, Decision decisionId4, Decision businessDateDecision, List<RateUnqualified> rateUnqualifieds) {
        List<String> tableList = Arrays.asList(PaceTable, NonPaceTable);
        tableList.stream().forEach(value -> {
            insertRecordForTable(decisionId1, decisionId3, businessDateDecision, rateUnqualifieds, value);
        });
    }

    private void insertRecordForTable(Decision decisionId1, Decision decisionId3, Decision businessDateDecision, List<RateUnqualified> rateUnqualifieds, String nonPaceTable) {
        StringBuilder sixthDayQuery = new StringBuilder("insert into " + nonPaceTable + "(Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        sixthDayQuery.append(decisionId3.getId() + "," + propertyId + ",10,'" + END_DATE + "'," + rateUnqualifieds.get(1).getId() + ",'YYYYNNN','" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(sixthDayQuery.toString());
        StringBuilder fifthDayQuery = new StringBuilder("insert into " + nonPaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        fifthDayQuery.append(decisionId1.getId() + "," + propertyId + ",9,'" + FIFTH_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YYYYYYY','" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fifthDayQuery.toString());
        StringBuilder fourthDayQuery = new StringBuilder("insert into " + nonPaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        fourthDayQuery.append(decisionId3.getId() + "," + propertyId + ",10,'" + FOURTH_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YYNNNNN','" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fourthDayQuery.toString());
        StringBuilder thirdDayQuery = new StringBuilder("insert into " + nonPaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        thirdDayQuery.append(decisionId1.getId() + "," + propertyId + ",9,'" + THIRD_DAY + "'," + rateUnqualifieds.get(0).getId() + ",'NYYYYYY','" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery.toString());
        StringBuilder secondDayQuery = new StringBuilder("insert into " + nonPaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        secondDayQuery.append(decisionId1.getId() + "," + propertyId + ",10,'" + SECOND_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YYNNNNN','" + DateUtil.formatDate(decisionId1.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(secondDayQuery.toString());
        StringBuilder firstDayQuery = new StringBuilder("insert into " + nonPaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        firstDayQuery.append(businessDateDecision.getId() + "," + propertyId + ",9,'" + FIRST_DAY + "'," + rateUnqualifieds.get(0).getId() + ",'YYNNNNN','" + DateUtil.formatDate(businessDateDecision.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(firstDayQuery.toString());
    }

    private void populatePaceData(Decision decisionId1, Decision decisionId2, Decision decisionId3, Decision decisionId4, Decision businessDateDecision, List<RateUnqualified> rateUnqualifieds) {
        StringBuilder sixthDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        sixthDayQuery1.append(decisionId4.getId() + "," + propertyId + ",10,'" + END_DATE + "'," + rateUnqualifieds.get(1).getId() + ",'YYYNNNN','" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(sixthDayQuery1.toString());
        StringBuilder fifthDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        fifthDayQuery1.append(decisionId2.getId() + "," + propertyId + ",9,'" + FIFTH_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YYYYYYN','" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fifthDayQuery1.toString());
        StringBuilder fourthDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        fourthDayQuery1.append(decisionId4.getId() + "," + propertyId + ",10,'" + FOURTH_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YNNNNNN','" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(fourthDayQuery1.toString());
        StringBuilder thirdDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        thirdDayQuery1.append(decisionId3.getId() + "," + propertyId + ",9,'" + THIRD_DAY + "'," + rateUnqualifieds.get(0).getId() + ",'NNYYYYY','" + DateUtil.formatDate(decisionId3.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery1.toString());
        StringBuilder thirdDayQuery2 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        thirdDayQuery2.append(decisionId4.getId() + "," + propertyId + ",9,'" + THIRD_DAY + "'," + rateUnqualifieds.get(0).getId() + ",'NNNYYYY','" + DateUtil.formatDate(decisionId4.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(thirdDayQuery2.toString());
        StringBuilder secondDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        secondDayQuery1.append(decisionId2.getId() + "," + propertyId + ",10,'" + SECOND_DAY + "'," + rateUnqualifieds.get(1).getId() + ",'YNNNNNN','" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(secondDayQuery1.toString());
        StringBuilder firstDayQuery1 = new StringBuilder("insert into " + PaceTable + " (Decision_ID,Property_ID,Accom_Type_ID,Arrival_DT,Rate_Unqualified_ID,FPLOS,CreateDate_DTTM) values (");
        firstDayQuery1.append(decisionId2.getId() + "," + propertyId + ",9,'" + FIRST_DAY + "'," + rateUnqualifieds.get(0).getId() + ",'YNNNNNN','" + DateUtil.formatDate(decisionId2.getBusinessDate(), YYYY_MM_DD) + "')");
        tenantCrudService().executeUpdateByNativeQuery(firstDayQuery1.toString());
    }

    private void cleanPaceAndNonPaceTables() {
        tenantCrudService().executeUpdateByNativeQuery("delete from " + NonPaceTable);
        tenantCrudService().executeUpdateByNativeQuery("delete from " + PaceTable);
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Upload_Date_To_External_System ");
    }

    private void createDecision(Date businessDate, Integer decisionId, Integer rateUnqualifiedId, String fpLOS, List<Integer> roomTypeIds) {
        final String arrivalDateStr = "'" + DateUtil.formatDate(businessDate, "yyyy-MM-dd") + "'";
        roomTypeIds.forEach(rtId -> {
            tenantCrudService().executeUpdateByNativeQuery("insert into Decision_FPLOS_By_Rank values(" + decisionId + "," + propertyId + "," + rtId + "," + rateUnqualifiedId + "," + arrivalDateStr + ",'" + fpLOS + "',getDate())");
            tenantCrudService().executeUpdateByNativeQuery("insert into PACE_FPLOS_By_Rank values(" + decisionId + "," + propertyId + "," + rtId + "," + rateUnqualifiedId + "," + arrivalDateStr + ",'" + fpLOS + "',getDate())");
        });
    }

    private void cleanUpTables(List<Integer> roomTypeIds, boolean deletePaceData) {
        final CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery("delete from Decision_FPLOS_By_Rank where Accom_Type_ID in (:roomTypeIds)", QueryParameter.with("roomTypeIds", roomTypeIds).parameters());
        if (deletePaceData) {
            crudService.executeUpdateByNativeQuery("delete from PACE_FPLOS_By_Rank where Accom_Type_ID in (:roomTypeIds)", QueryParameter.with("roomTypeIds", roomTypeIds).parameters());
        }
    }

    @Test
    public void test_If_FPLOSByRank_Differential_Query_Is_In_Sync_With_Last_Good_Decision_Delivery_Job_For_HilstarOrPCRS() {
        final Object functionDefinition = tenantCrudService().findByNativeQuery(String.format(QUERY_TO_FETCH_FUNCTION_DEFINITION, "ufn_get_barbyrank_fplos_decisions_masterclassonly_differential")).get(0);
        assertEquals(FPLOS_BY_RANK_FUNCTION_FOR_DIFFERENTIAL.replaceAll("\\s", ""), String.valueOf(functionDefinition).replaceAll("\\s", ""), String.format(ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED, "BARFPLOSBYRANK", "FPLOSByRankDecisionService"));
    }

    @Test
    public void test_If_Decision_FPLOSByRank_Table_Columns_Are_In_Sync_With_Last_Good_Decision_Delivery_Job() {
        final Object columns = tenantCrudService().findByNativeQuery(String.format(QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE, "Decision_FPLOS_By_Rank")).get(0);
        assertEquals(DECISION_FPLOS_BY_RANK_COLUMNS, String.valueOf(columns), String.format(COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB, "Decision_FPLOS_By_Rank", "FPLOSByRankDecisionService"));
    }

    @Test
    public void test_If_Pace_FPLOSByRank_Table_Columns_Are_In_Sync_With_Last_Good_Decision_Delivery_Job() {
        final Object columns = tenantCrudService().findByNativeQuery(String.format(QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE, "PACE_FPLOS_By_Rank")).get(0);
        assertEquals(PACE_FPLOS_BY_RANK_COLUMNS, String.valueOf(columns), String.format(COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB, "PACE_FPLOS_By_Rank", "FPLOSByRankDecisionService"));
    }
}
