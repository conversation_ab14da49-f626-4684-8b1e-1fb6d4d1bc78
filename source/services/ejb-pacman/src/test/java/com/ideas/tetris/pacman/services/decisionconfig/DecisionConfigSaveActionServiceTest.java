package com.ideas.tetris.pacman.services.decisionconfig;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.cdp.ConfigurationService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBHybridService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValueType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.job.JobService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.*;
import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.decisionconfig.ClientQuestionnaireElement.FORECASTING_WINDOW;
import static com.ideas.tetris.pacman.services.decisionconfig.ClientQuestionnaireElement.PROPERTY_BASE_OCCUPANCY;
import static com.ideas.tetris.pacman.services.decisionconfig.CoreSettingsElement.*;
import static com.ideas.tetris.pacman.services.decisionconfig.DecisionConfigurationTestSetUp.TEST_CLIENT;
import static com.ideas.tetris.pacman.services.decisionconfig.DecisionConfigurationTestSetUp.TEST_PROPERTY;
import static com.ideas.tetris.pacman.services.decisionconfig.OutboundElement.*;
import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DecisionConfigSaveActionServiceTest extends AbstractG3JupiterTest {

    private static final String USD = "USD";
    private static final String INR = "INR";
    private static final String EUR = "EUR";
    private static final String SAS_PARAMETER_ENABLED = "1";
    private static final String SAS_PARAMETER_DISABLED = "0";
    @InjectMocks
    private DecisionConfigSaveActionService service;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private InformationManagerCleanupService informationManagerCleanupService;
    @Mock
    private BarDecisionService barDecisionService;
    @Mock
    private JobService jobService;
    @Mock
    private PropertyService propertyService;
    @Mock
    private ConfigurationService configurationService;
    @Mock
    private LDBHybridService ldbHybridService;
    @Mock
    private LDBService ldbService;
    @Mock
    private DecisionConfigCoreSettingService decisionConfigCoreSettingService;
    @Mock
    @GlobalCrudServiceBean.Qualifier
    CrudService globalCrudService;
    @Spy
    @InjectMocks
    private ROAPropertyAttributeService roaPropertyAttributeService;

    private final ExternalSystem EXTERNAL_SYSTEM = ExternalSystem.PCRS;
    private final String SINGLE = "Single";
    private final String DOUBLE = "Double";
    private final DecisionConfigurationTestSetUp testSetUp = new DecisionConfigurationTestSetUp();
    private final static List<String> SAS_PARAMETERS = new ArrayList<>(List.of(SKIP_EVENT_BKCRV.getAttributeName(), OCC_TO_ARR_FUTURE.getAttributeName(),
            BOOKING_CURVE_HIER_METHOD.getAttributeName(), INHOUSE_EXT.getAttributeName(), USE_BOOK_DATA.getAttributeName(), DISCARD_NEGATIVE_DTA.getAttributeName()));

    @BeforeEach
    public void setUp() {
        PacmanWorkContextHelper.setPropertyId(5);
        PacmanWorkContextHelper.setClientCode(TEST_CLIENT);
        PacmanWorkContextHelper.setPropertyCode(TEST_PROPERTY);
        when(propertyService.getPropertyById(DecisionConfigurationTestSetUp.PROPERTY_ID)).thenReturn(testSetUp.createProperty());
        when(configParamsService.propertyNode(any(WorkContextType.class))).thenReturn(getContext());
    }

    @Test
    public void handleChangeRelatedToMaxCdps() {
        when(configParamsService.getValue(anyString(),
                eq(IntegrationConfigParamName.CORE_PROPERTY_CDP_DAILYMAX.value(EXTERNAL_SYSTEM.getExternalSystemName()))))
                .thenReturn("0");
        service.saveElement(MAXIMUM_IDPS, "1", EXTERNAL_SYSTEM);
        verify(configurationService).saveOnlyMaxConfiguredCdpsSchedules(1);
    }

    @Test
    public void saveLimitedDataBuildByRoomTypeData() {
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, 5);
        Mockito.when(jobService.startGuaranteedNewInstance(JobName.TlukDailyReportsJob, jobParams)).thenReturn(new Long(4321));

        DecisionConfigSaveActionService.SaveActionParameter saveActionParameter = Mockito.mock(DecisionConfigSaveActionService.SaveActionParameter.class);
        doReturn("true").when(saveActionParameter).getValue();
        service.saveLimitedDataBuildByRoomTypeData(saveActionParameter);
        verify(ldbHybridService).configurePropertyToHybrid();

        doReturn("false").when(saveActionParameter).getValue();
        service.saveLimitedDataBuildByRoomTypeData(saveActionParameter);
        verify(ldbService).startRevertToStandardJob(anyInt());
    }

    @Test
    public void shouldSaveLDBChangeWhenTurnedOff() {
        DecisionConfigSaveActionService.SaveActionParameter saveActionParameter = Mockito.mock(DecisionConfigSaveActionService.SaveActionParameter.class);
        doReturn("false").when(saveActionParameter).getValue();
        service.saveLimitedDataBuild(saveActionParameter);
        verify(ldbService).startRevertToStandardJob(anyInt());
    }

    @Test
    public void shouldNotSaveWhenLDBToggleTurnedOn() {
        DecisionConfigSaveActionService.SaveActionParameter saveActionParameter = Mockito.mock(DecisionConfigSaveActionService.SaveActionParameter.class);
        doReturn("true").when(saveActionParameter).getValue();
        service.saveLimitedDataBuild(saveActionParameter);
        verify(ldbService, times(0)).revertLDBPropertyToStandard();
    }


    @Test
    public void handleForContinuousPricingTRUEAndExternalSystemIsREZVIEW() {
        doReturn(Boolean.FALSE.toString()).when(configParamsService).getValue(anyString(), eq(DAILY_BAR_CONFIGURATION_ENABLED.getParameterName()));
        doReturn(Boolean.TRUE.toString()).when(configParamsService).getValue(anyString(), eq(MASS_BAR_CONFIGURATION_ENABLED.getParameterName()));
        doReturn(Boolean.FALSE.toString()).when(configParamsService).getValue(anyString(), eq(IS_CONTINUOUS_PRICING_ENABLED.getParameterName()));

        service.saveElement(CONTINUOUS_PRICING, "TRUE", ExternalSystem.REZVIEW);

        verify(configParamsService).addParameterValue(anyString(), eq(DAILY_BAR_CONFIGURATION_ENABLED.getParameterName()), eq(Boolean.TRUE.toString()));
        verify(configParamsService).addParameterValue(anyString(), eq(MASS_BAR_CONFIGURATION_ENABLED.getParameterName()), eq(Boolean.FALSE.toString()));
        verify(configParamsService).addParameterValue(anyString(), eq(IS_CONTINUOUS_PRICING_ENABLED.getParameterName()), eq("TRUE"));

    }


    @Test
    public void verifyFutureBarDecisionsAreDeleted() {
        service.deleteFutureBarDecisions();
        verify(informationManagerCleanupService, times(4)).deleteExceptionConfiguration(anyInt(), anyString());
        verify(informationManagerCleanupService, times(2)).deleteExceptionConfiguration(anyInt(), anyString(), anyString());
        verify(barDecisionService).triggerFutureBarDecisionsCleanupJob();
    }

    @Test
    public void verifySingleBarDecisionEnabledElementSaved() {
        when(configParamsService.getValue(anyString(), eq(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))).thenReturn(FALSE);
        service.saveElement(PRICING_BY_MASTER_CLASS, TRUE, EXTERNAL_SYSTEM);
        verify(jobService, never()).startGuaranteedNewInstance(any(), any());
        verify(configParamsService).addParameterValue(getContext(), IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), TRUE);
    }

    @Test
    public void verifyContinousPricingEnabledElementSavedAndCPMigrationJobIsTriggeredForSpecificProperty() {
        ArgumentCaptor<Map> map = ArgumentCaptor.forClass(Map.class);
        setUpMockDataForCP(Boolean.FALSE);
        service.saveElement(CONTINUOUS_PRICING, TRUE, EXTERNAL_SYSTEM);
        verify(jobService).startGuaranteedNewInstance(eq(JobName.ContinuousPricingMigrationJob), map.capture());
        assertEquals(TEST_CLIENT, map.getValue().get(JobParameterKey.CLIENT_CODE));
        assertEquals(DecisionConfigurationTestSetUp.TEST_PROPERTY, map.getValue().get(JobParameterKey.PROPERTY_CODES));
        assertEquals(Stage.ONE_WAY.getCode(), map.getValue().get(JobParameterKey.PROPERTY_STAGE));
        verify(configParamsService, times(3)).addParameterValue(anyString(), any(), anyString());
    }


    @Test
    public void verifySaveDtoForBaseOccupancy() {
        String element = FeatureTogglesConfigParamName.RATES_BASE_OCCUPANCY_NUMBER.value();
        String oldValue = SINGLE;
        String newValue = DOUBLE;
        when(configParamsService.getValue(anyString(), eq(element))).thenReturn(oldValue);
        service.saveElement(PROPERTY_BASE_OCCUPANCY, newValue, ExternalSystem.OXI);
        verify(configParamsService).addParameterValue(getContext(), element, newValue);
    }

    @Test
    public void shouldDeleteParamValueIfSameValueExistsAtHigherContextLevel() {
        when(configParamsService.getValue(anyString(), eq(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))).thenReturn(TRUE);
        service.saveElement(PRICING_BY_MASTER_CLASS, TRUE, EXTERNAL_SYSTEM);
        verify(jobService, never()).startGuaranteedNewInstance(any(), any());
    }


    @Test
    public void shouldSaveParamValueWithExternalSystemPlaceHolder() {
        when(configParamsService.getValue(anyString(), eq(IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(EXTERNAL_SYSTEM.getExternalSystemName()))))
                .thenReturn(FULL);
        service.saveElement(MIN_MAX_LOS_BY_RATE_CODE, DIFFERENTIAL, EXTERNAL_SYSTEM);
        verify(jobService, never()).startGuaranteedNewInstance(any(), any());
        verify(configParamsService).addParameterValue(getContext(), IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(EXTERNAL_SYSTEM.getExternalSystemName()), DIFFERENTIAL);
    }

    @Test
    public void verifySaveDtoForDailyBarPMSRateCodeForORS() {
        String element = IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(ExternalSystem.OPERA_AGENT.getExternalSystemName());
        String oldValue = "RATE_A";
        String newValue = "RATE_B";
        when(configParamsService.getValue(anyString(), eq(element))).thenReturn(oldValue);
        service.saveElement(DAILY_BAR_PMS_RATE_CODE, newValue, ExternalSystem.ORS);
        verify(configParamsService).addParameterValue(getContext(), element, newValue);
    }

    @Test
    public void verifySaveDtoForOperaParams() {
        String element = IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(ExternalSystem.OXI.getExternalSystemName());
        String oldValue = "RATE_A";
        String newValue = "RATE_B";
        when(configParamsService.getValue(anyString(), eq(element))).thenReturn(oldValue);
        service.saveElement(DAILY_BAR_PMS_RATE_CODE, newValue, ExternalSystem.OXI);
        verify(configParamsService).addParameterValue(getContext(), element, newValue);
    }

    @Test
    public void verifySaveDtoForOxiParams() {
        String element = IntegrationConfigParamName.OUTBOUND_USERNAME.value(ExternalSystem.OXI.getCode());
        String oldValue = "User_A";
        String newValue = "User_B";
        when(configParamsService.getValue(anyString(), eq(element))).thenReturn(oldValue);
        service.saveElement(OXI_USERNAME, newValue, ExternalSystem.OXI);
        verify(configParamsService).addParameterValue(getContext(), element, newValue);
    }

    @Test
    public void verifyApplyTaxSetToTrue() {
        String taxParam = IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(ExternalSystem.OPERA_AGENT.getExternalSystemName());
        String oldValue = "10";
        String newValue = "20";
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(TAX, newValue, ExternalSystem.OPERA_AGENT);
        verify(configParamsService).addParameterValue(getContext(), IntegrationConfigParamName.APPLY_TAX.value(ExternalSystem.OPERA_AGENT.getExternalSystemName()), TRUE);
        verify(configParamsService).addParameterValue(getContext(), taxParam, newValue);
    }

    @Test
    public void verifyMinMaxLosByRateCodeForTARSDependentElementsAreSaved() {
        String minMaxLosByRateCode = IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(ExternalSystem.TARS.getExternalSystemName());
        String oldValue = DIFFERENTIAL;
        String newValue = FULL;
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(MIN_MAX_LOS_BY_RATE_CODE, newValue, ExternalSystem.TARS);
        verify(configParamsService).addParameterValue(getContext(), IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE);
        verify(configParamsService).addParameterValue(getContext(), MIN_MAX_LOS_DECISION_ENABLED.value(), TRUE);
        verify(configParamsService).addParameterValue(getContext(), minMaxLosByRateCode, newValue);
    }

    @Test
    public void verifyMinMaxLosByRateCodeDependentElementsAreNotSavedWhenExternalSystemIsNotTars() {
        String minMaxLosByRatecode = IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(ExternalSystem.OPERA_AGENT.getExternalSystemName());
        String oldValue = DIFFERENTIAL;
        String newValue = FULL;
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(MIN_MAX_LOS_BY_RATE_CODE, newValue, ExternalSystem.OPERA_AGENT);
        verify(configParamsService, never()).addParameterValue(getContext(), IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE);
        verify(configParamsService, never()).addParameterValue(getContext(), MIN_MAX_LOS_DECISION_ENABLED.value(), TRUE);
        verify(configParamsService).addParameterValue(getContext(), minMaxLosByRatecode, newValue);
    }

    @Test
    public void verifyMinMaxLosByRateCodeDependentElementsAreNotSavedWhenDecisonTypeIsTurnedOff() {
        String minMaxLosByRatecode = IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(ExternalSystem.TARS.getExternalSystemName());
        String oldValue = DIFFERENTIAL;
        String newValue = NONE;
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(MIN_MAX_LOS_BY_RATE_CODE, newValue, ExternalSystem.TARS);
        verify(configParamsService, never()).addParameterValue(getContext(), IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE);
        verify(configParamsService, never()).addParameterValue(getContext(), MIN_MAX_LOS_DECISION_ENABLED.value(), TRUE);
        verify(configParamsService).addParameterValue(getContext(), minMaxLosByRatecode, newValue);
    }

    @Test
    public void verifyApplyTaxSetToFalse() {
        String taxParam = IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(ExternalSystem.OPERA_AGENT.getExternalSystemName());
        String oldValue = "10";
        String newValue = "";
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(TAX, newValue, ExternalSystem.OPERA_AGENT);
        verify(configParamsService).addParameterValue(getContext(), IntegrationConfigParamName.APPLY_TAX.value(ExternalSystem.OPERA_AGENT.getExternalSystemName()), FALSE);
        verify(configParamsService).deleteParameterValue(getContext(), taxParam, true);
    }

    @Test
    public void demand360SubscriberIDisDeletedWhenDemand360IsOff() {
        String d360Enabled = FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED.value();
        String oldValue = TRUE;
        String newValue = FALSE;
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(DEMAND_360, newValue, ExternalSystem.OPERA_AGENT);
        verify(configParamsService).addParameterValue(getContext(), d360Enabled, newValue);
        verify(configParamsService).deleteParameterValue(getContext(), FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID.value(), true);
    }

    @Test
    public void optimizationWindowShouldBeSameAsForecastWindow() {
        String bdeForecastWindow = IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value();
        String oldValue = "365";
        String newValue = "760";
        when(configParamsService.getValue(anyString(), anyString())).thenReturn(oldValue);
        service.saveElement(FORECASTING_WINDOW, newValue, ExternalSystem.OPERA_AGENT);
        verify(configParamsService).addParameterValue(getContext(), bdeForecastWindow, newValue);
        verify(configParamsService).addParameterValue(getContext(), IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value(), newValue);
    }

    @Test
    public void verifyContinuousPricingDisabledElementSaved() {
        setUpMockDataForCP(Boolean.TRUE);
        service.saveElement(CONTINUOUS_PRICING, FALSE, EXTERNAL_SYSTEM);
        verify(jobService, never()).startGuaranteedNewInstance(any(), any());
        verify(configParamsService, times(3)).addParameterValue(anyString(), any(), anyString());
    }

    private void setUpMockDataForCP(Boolean cpValue) {
        Boolean dependentParamsValue = !cpValue;
        when(configParamsService.getValue(anyString(), eq(IS_CONTINUOUS_PRICING_ENABLED.value()))).thenReturn(cpValue.toString());
        when(configParamsService.getValue(anyString(), eq(DAILY_BAR_CONFIGURATION_ENABLED.value()))).thenReturn(dependentParamsValue.toString());
        when(configParamsService.getValue(anyString(), eq(MASS_BAR_CONFIGURATION_ENABLED.value()))).thenReturn(dependentParamsValue.toString());
    }


    private String getContext() {
        return getPropertyConfigContext(TEST_CLIENT, DecisionConfigurationTestSetUp.TEST_PROPERTY);
    }

    @Test
    void shouldReturnTrue_WhenBaseAndYieldCurrencies_areDifferent() {
        Assertions.assertTrue(service.isDifferent(USD, INR));
    }

    @Test
    void shouldReturnFalse_WhenBaseAndYieldCurrencies_areSame() {
        Assertions.assertFalse(service.isDifferent(USD, USD));
    }

    @Test
    void shouldReturnFalse_WhenBaseAndYieldCurrenciesAreSame_ignoringCase() {
        Assertions.assertFalse(service.isDifferent(USD.toLowerCase(), USD));
    }

    @Test
    void shouldReturnFalse_WhenBaseCurrencyIsBlank() {
        Assertions.assertFalse(service.isDifferent(EMPTY_STRING, USD));
    }

    @Test
    void testIsDifferent_WhenYieldCurrencyIsBlank() {
        Assertions.assertFalse(service.isDifferent(USD, EMPTY_STRING));
    }

    @Test
    void shouldReturnFalse_WhenBaseAndYieldCurrenciesAreBlank() {
        Assertions.assertFalse(service.isDifferent(EMPTY_STRING, EMPTY_STRING));
    }

    @Test
    void shouldReturnFalse_WhenBaseCurrencyIsNull() {
        Assertions.assertFalse(service.isDifferent(null, INR));
    }

    @Test
    void shouldReturnFalse_WhenYieldCurrencyIsNull() {
        Assertions.assertFalse(service.isDifferent(USD, null));
    }

    @Test
    void shouldReturnFalse_WhenBothCurrenciesAreNull() {
        Assertions.assertFalse(service.isDifferent(null, null));
    }

    @Test
    public void shouldFetchPropertyLevelContext() {
        String context = service.getContext();

        assertEquals("pacman.TEST_CLIENT.TEST_PROPERTY", context);
        verify(configParamsService).propertyNode(PacmanWorkContextHelper.getWorkContext());
    }

    @Test
    public void shouldNotUpdateDailyBarUrlWhenRegionIsUpdated() {

        service.saveElement(HTNG_REGION_WISE_URL, "https://abc/api/ideas/avail_submit", ExternalSystem.RMS_PMS);

        verify(configParamsService).addParameterValue(getContext(), "pacman.integration.RMSPMS.url", "https://abc/api/ideas/avail_submit");
        verify(configParamsService, never()).addParameterValue(getContext(), "pacman.integration.RMSPMS.DailyBAR.alternateURL", "https://abc/api/ideas/rate_submit_async");
    }

    @Test
    public void shouldUpdateDailyBarUrlWhenRegionIsUpdated() {
        String url = "https://abc/api/ideas/avail_submit";
        String dailyBarUrl = "https://abc/api/ideas/rate_submit_async";
        when(globalCrudService.findByNamedQuerySingleResult(ConfigParameterPredefinedValueType.BY_CODE, QueryParameter.with("code", "RMSPMSRegionalURLs").parameters()))
                .thenReturn(getConfigParameterPredefinedValueType(url));
        when(globalCrudService.findByNamedQuerySingleResult(ConfigParameterPredefinedValueType.BY_CODE, QueryParameter.with("code", "RMSPMSDailyBARRegionalURLs").parameters()))
                .thenReturn(getConfigParameterPredefinedValueType(dailyBarUrl));

        service.saveElement(HTNG_REGION_WISE_URL, url, ExternalSystem.RMS_PMS);

        verify(configParamsService).addParameterValue(getContext(), "pacman.integration.RMSPMS.url", url);
        verify(configParamsService).addParameterValue(getContext(), "pacman.integration.RMSPMS.DailyBAR.alternateURL", dailyBarUrl);
    }

    private ConfigParameterPredefinedValueType getConfigParameterPredefinedValueType(String s) {
        ConfigParameterPredefinedValueType configParameterPredefinedValueType = new ConfigParameterPredefinedValueType();
        Set<ConfigParameterPredefinedValue> configParameterPredefinedValues = new HashSet<>();
        ConfigParameterPredefinedValue configParameterPredefinedValue = new ConfigParameterPredefinedValue();
        configParameterPredefinedValue.setValue(s);
        configParameterPredefinedValue.setDisplayName("India");
        configParameterPredefinedValues.add(configParameterPredefinedValue);
        configParameterPredefinedValueType.setConfigParameterPredefinedValues(configParameterPredefinedValues);
        return configParameterPredefinedValueType;
    }

    @Test
    public void testAddSasParamsWithValues() {
        Map<String, String> result = service.addSasParamsWithValues();

        assertEquals(SAS_PARAMETER_ENABLED, result.get(SKIP_EVENT_BKCRV.getAttributeName()));
        assertEquals(SAS_PARAMETER_ENABLED, result.get(OCC_TO_ARR_FUTURE.getAttributeName()));
        assertEquals(SAS_PARAMETER_ENABLED, result.get(BOOKING_CURVE_HIER_METHOD.getAttributeName()));
        assertEquals(SAS_PARAMETER_ENABLED, result.get(INHOUSE_EXT.getAttributeName()));
        assertEquals(SAS_PARAMETER_DISABLED, result.get(USE_BOOK_DATA.getAttributeName()));
    }

    @Test
    public void testUpdateSasParameterDiscardNegativeDTAWithCoreLimitedDataBuildEnabledTrue() {
        when(configParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);

        service.updateSasParameterDiscardNegativeDTA();

        verify(roaPropertyAttributeService).saveOverrideByAttributeName(DISCARD_NEGATIVE_DTA.getAttributeName(), SAS_PARAMETER_DISABLED);
    }

    @Test
    public void testUpdateSasParameterDiscardNegativeDTAWithCoreLimitedDataBuildEnabledFalse() {
        when(configParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);

        service.updateSasParameterDiscardNegativeDTA();

        verify(roaPropertyAttributeService).saveOverrideByAttributeName(DISCARD_NEGATIVE_DTA.getAttributeName(), SAS_PARAMETER_ENABLED);
    }

    @Test
    void shouldUpdateParameterValuesToTrueWhenIndepedentToggleMadeEnabled() {
        DecisionConfigSaveActionService.SaveActionParameter action = new DecisionConfigSaveActionService.SaveActionParameter("true", null, getContext());
        service.updateParametersIndependentProduct(action);

        verify(configParamsService).addParameterValue(getContext(), INDEPENDENT_PRODUCTS_ENABLED.value(), "true");
    }

    @Test
    void shouldUpdateParameterValuesToFalseWhenIndepedentToggleMadeDisabled() {
        DecisionConfigSaveActionService.SaveActionParameter action = new DecisionConfigSaveActionService.SaveActionParameter("false", null, getContext());
        service.updateParametersIndependentProduct(action);
        verify(configParamsService).addParameterValue(getContext(), PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED.value(), "false");
    }
}